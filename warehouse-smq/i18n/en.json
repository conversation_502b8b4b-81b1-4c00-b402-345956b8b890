{"content": {"Login": "LogIn", "Account": "Account", "Password": "Password", "EnterAccount": "Please enter account number", "EnterPassword": "Please enter the password", "Language": "Language", "EnvironmentSettings": "EnvironmentSettings", "Current": "Current", "EnterRequestedUrl": "Please enter the requested address", "Chinese": "Chinese", "English": "English", "EnterAccountPassword": "Please enter the account password", "Error": "Error", "Baling": "<PERSON><PERSON>", "ScanPickingCarCode": "ScanPickingCarCode", "ScanPickingCarOrBoxCode": "ScanPickingCarCode", "Finish": "Finish", "NextStep": "NextStep", "ScanCodeQuery": "Query", "PScanPickingCarCode": "Please scan the picking car code", "Tips": "Tips", "EndAllPackagingJobs": "Are you sure you want to force all packaging jobs to end?", "InRequest": "InRequest", "PickingCarode": "PickingCarode", "PickingCarsNumber": "PickingCarsNumber", "OutboundOrderNumber": "OutboundOrderNumber", "ScanPalletID": "ScanPalletID", "Return": "Return", "Confirm": "Confirm", "PScanPalletID": "Please scan the palletId", "PickingCarCode": "PickingCarCode", "BalingOrderNumber": "BalingOrderNumber", "ScanSKUBarcode": "ScanSKUBarcode", "PScanSKUBarcode": "Please ScanSKUBarcode", "ConfirmExit": "Confirm Exit", "PrintSuccessfully": "Print Successfully", "PrintingFailed": "Printing Failed", "BaleSucceeded": "BaleSucceeded", "BalePartitionCode": "BalePartitionCode", "PBalePartitionCode": "Please Scan Bale Partition Code", "BalingStation": "BalingStation", "BalingPartitionCodingError": "Baling Partition Coding Error", "Loading": "Loading", "Inventory": "Inventory", "ScanInventoryOrderNo": "Scan Inventory OrderNo", "PScanInventoryOrderNo": "Please Scan the inventoryOrderNo", "ICountingOperationsEnd": "Are you sure you want to force all counting operations to end?", "InventoryOrderNo": "InventoryOrderNo", "Location": "Location", "ScanLocations": "Scan Locations", "PScanLocations": "Please Scan Locations", "InconsistentWithTargetLoc": "The scanned location is inconsistent with the target location", "ScanSkuNo": "ScanSkuNo", "ScanSku": "Scan S<PERSON>", "ScanTurnoverBox": "Scan Turnover Box", "ScanOrEnterSKUNO": "Scan or enter SKU NO.", "ScannedSKUInconsistentTargetSKU": "Scanned SKU is inconsistent with target SKU", "BookQuantity": "BookQuantity", "EnterCurrentSKUQuantity": "Enter the current SKU count quantity", "PEnterCurrentSKUQuantity": "Please enter the current SKU count quantity", "PEnterCountQuantity": "Please enter count quantity", "CountQuantityCannotThanZero": "Count quantity cannot be less than 0", "InventorySucceeded": "Inventory Succeeded", "Discharge": "Discharge", "TruckFBAOut": "Truck FBA Discharge Out", "TruckNoFBAOut": "Truck No FBA Discharge Out", "ScanInBoundOrderNo": "Scan InBound OrderNo", "ScanOrderNum": "Scan <PERSON>o", "PScanInBoundOrderNo": "Please Scan InBound OrderNo", "PScanExpressOrderNo": "Please Scan Express OrderNo", "UnloadingOperationsEnd": "Are you sure you want to force all unloading operations to end?", "InBoundOrderNo": "InBoundOrderNo", "OutBoundOrderNo": "OutBoundOrderNo", "RefundOrderNo": "RefundOrderNo", "TrackingNo": "TrackingNo", "EnterNumberOfBoxes": "Enter the number of boxes", "PEnterNumberOfBoxes": "Please Enter the number of boxes", "ScanBoxRecord": "Scan Box Record", "BoxQuantity": "Box Quantity", "DischargeSucceeded": "Discharge Succeeded", "HomePage": "HomePage", "PrintFaceSheet": "PrintSheet", "Receipt": "Receipt", "Shelves": "<PERSON><PERSON>", "Refunds": "Refunds", "Picking": "Picking", "TransferOff": "TransferOff", "TransferOn": "TransferOn", "NoTransferDeatils": "TransDeatils", "RefundOrderOn": "Refund Shelves", "Search": "Search", "Sorting": "Sorting", "LogOut": "LogOut", "OrderNo": "OrderNo", "Status": "Status", "Type": "Type", "Total": "Total", "FbaTakeStock": "FBA Take Stock", "ScanExpressOrderNo": "Scan Box No.", "FbaTakeStockDetail": "FBA Take Stock Detail", "Operation": "Operation", "Completed": "Completed", "Ongoing": "Ongoing", "CheckDetails": "Details", "NoInformation": "NoInformation", "NoTransferDeatilList": "NoTransferDeatilList", "Warehouse": "Warehouse", "StartTime": "StartTime", "FinishTime": "FinishTime", "ProductName": "ProductName", "GoodsInfo": "GoodsInfo", "TargetLocation": "TargetLocation", "Quantity": "Quantity", "Operator": "Operator", "PrintPartition": "PrintPartition", "ScanOutboundOrder": "<PERSON>an outbound order", "Printer": "Printer", "PrinterName": "PrinterName", "NoData": "NoData", "PScanOutboundOrder": "Please <PERSON><PERSON> outbound order", "ScanPickingOrderNo": "<PERSON>an <PERSON>ing<PERSON>rderNo", "PScanPickingOrderNo": "Please <PERSON><PERSON><PERSON>", "PickingOrderNo": "PickingNo", "NumberOfPiecesToBePicked": "ToBePicked", "MCode": "MCode", "NumberOfPickedPieces": "Number Of Picked Pieces", "Length": "L", "Width": "W", "Height": "H", "ReplacePickingCar": "Replace PickingCar", "Skip": "<PERSON><PERSON>", "ShortPicking": "Short<PERSON>ick", "SkipCurrentSku": "Are you sure to skip the current sku?", "SureReplaceCar": "Are you sure to replace picking car?", "PickingSucceeded": "Picking Succeeded", "ScanPickingCar": "<PERSON><PERSON>", "PScanPickingCar": "Please <PERSON><PERSON>", "EndAllReceivingJob": "Are you sure you want to force all receiving operations to end?", "CurrentSKUInfor": "Current SKU Infor", "LengthCM": "Length(CM)", "WidthCM": "Width(CM)", "HeightCM": "Height(CM)", "GrossWeight": "GrossWeight(KG)", "SameQuantityCurrentSKUs": "Same quantity of current SKUs", "PleaseEnter": "PleaseEnter", "PleaseLWHGWQ": "Please enter length, width, height, gross weight and quantity", "PleaseRLWHGWQ": "Please enter rigth length, width, height, gross weight and quantity", "ReceiptSucceeded": "Receipt Succeeded", "WushiPercent": "Deviation exceeds plus or minus 50%. Are you sure to continue the current operation?", "ScanOrEnterCode": "Scan or enter code", "PScanOrEnterCode": "Please Scan or enter code", "QueryResult": "QueryResult", "GridList": "Gird List", "EndAllShelvesJob": "Are you sure you want to force all shelves to end?", "InboundOrderPCS": "Inbound Order PCS", "EnterCurrentSKUShelfQuantity": "Enter the current SKU shelf quantity", "PEnterShelfQuantity": "Please enter the shelf quantity", "PEnterCorrectShelfQuantity": "Please enter the correct shelf quantity", "QuantityOnShelves": "Quantity on shelves", "NumberShelvesThisTime": "Number of shelves this time", "PEnterLocation": "Please enter a location", "ShelvingSuccess": "Shelving Success", "QuerySkuInventory": "Query Sku Inventory", "QueryLocationInventory": "Query Location Inventory", "LocationInformation": "Location Information", "EndAllTransferOff": "Are you sure you want to force the end of all off-shelf operations?", "RecordOffShelfLst": "Record of off-shelf list", "Stock": "Stock", "EnterTransferOffQuantity": "Enter TransferOff Quantity", "PEnterQuantityShelves": "Please enter the quantity of shelves", "XiaoYuLing": "TransferOff Quantity must be greater than 0", "TransferOffSucceeded": "TransferOff Succeeded", "EnterTransferOnQuantity": "Enter TransferOn Quantity", "OnXiaoYuLing": "TransferOn Quantity must be greater than 0", "ScanEnterLocationInventory": "Scan or enter location inventory"}}