<template>
  <view class="datetime-picker-container">
    <text v-if="label" :style="{ fontSize: labelSize }">{{ label }}</text>
    <view class="picker-input-wrapper">
      <input
        class="picker-input"
        type="text"
        v-model="selectedDateTime"
        :placeholder="placeholder"
        @click="openPicker"
      />
      <view class="picker-icon" @click="openPicker">
        <text class="iconfont icon-calendar"></text>
      </view>
    </view>
    <uni-datetime-picker
      v-if="showPicker"
      ref="picker"
      v-model="selectedDateTime"
      type="datetime"
      :clear-icon="true"
      @change="handleDateTimeChange"
      @maskClick="handleMaskClick"
    />
  </view>
</template>

<script>
export default {
  name: "DatetimePicker",
  model: {
    prop: "value",
    event: "input",
  },
  props: {
    // 值
    value: {
      type: String,
      default: "",
    },
    // 标签文本
    label: {
      type: String,
      default: "",
    },
    // 标签文字大小
    labelSize: {
      type: String,
      default: "160%",
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: "请选择日期时间",
    },
  },
  data() {
    return {
      showPicker: false,
      selectedDateTime: this.value || "",
    };
  },
  watch: {
    value(newVal) {
      this.selectedDateTime = newVal || "";
    }
  },
  methods: {
    openPicker() {
      this.showPicker = true;
      this.$nextTick(() => {
        this.$refs.picker.show();
      });
      console.log("openPicker-->", this.showPicker);
    },
    handleMaskClick() {
      this.showPicker = false;
      console.log("handleMaskClick-->", this.showPicker);
    },
    handleDateTimeChange(value) {
      this.selectedDateTime = value;
      this.$emit("input", value);
      this.showPicker = false;
    }
    // handleBlur() {
    //   // 添加一个小延时，以便用户能够点击选择器
    //   setTimeout(() => {
    //     this.showPicker = false;
    //   }, 200);
    // }
  },
};
</script>

<style lang="scss">
.datetime-picker-container {
  .picker-label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 10rpx;
  }

  .picker-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    flex: 1;

    .picker-input {
      flex: 1;
      height: 80rpx;
      padding: 0 20rpx;
      border: 1px solid #dcdfe6;
      border-radius: 4rpx;
      font-size: 28rpx;
      color: #333;

      &:focus {
        border-color: #409eff;
      }
    }

    .picker-icon {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      color: #909399;
      font-size: 32rpx;
      cursor: pointer;
    }
  }
}
</style>
