<template>
  <view class="modal" v-if="dialogModalVisible">
    <view class="modal-overlay" @click="closeDialog()"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">{{ title }}</text>
        <button class="close-button" @click="closeDialog()">×</button>
      </view>
      <view class="modal-body">
        <view v-for="(data, index) in dialogContentData.slice()" :key="index">
            <view class="info-container">
              <view class="info-row">
                <view class="info-column">
                  <view class="info-item" :style="{ 'font-size': index === 0 ? '20px' : '20px' }">
                    <text>{{ data }}</text>
                  </view>
                </view>
              </view>
            </view>
        </view>
      </view>
        <view class="modal-footer">
            <button class="left bottom-btn-cancel" type="" @click="closeDialog()" :loading="false">取消</button>
            <button class="right bottom-btn-confirm" type="primary" @click="dialogConfirm()" :loading="false">确认</button>
        </view>
    </view>
  </view>
</template>

<script>
export default {
    name: 'showListDialog',
  props: {
    dialogModalVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    dialogContentData: {
      type: Array,
      default: () => [] // 默认值为一个空数组
    }
  },
  // data () {
  //     return {
  //         dialogContentData: []
  //     }
  // },
  methods: {
    dialogConfirm() {
      this.$emit('dialogConfirm');
      this.closeDialog();
    },
    closeDialog() {
      this.$emit('update:dialogModalVisible', false);
    }
  }
};
</script>

<style scoped>
.right{
    right: 0;
}
.left{
    left: 0;
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
}
.modal-content {
  background: white;
  border-radius: 8px;
  position: relative;
  padding: 20px;
  width: 80%;
  max-width: 500px;
  height: 90%;
}
.modal-header {
    height: 5%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.modal-title {
    display: inline-block;
    font-size: 25px;
}
.modal-body {
  /* margin: 16px 0; */
  /* padding: 16px 0; */
  border-radius: 5px;
  /* width: 460px; */
  height: 89%;
  position: relative;
  background: #fff;
  overflow-y: auto;
}
.modal-footer {
  display: flex;
  justify-content: flex-end;
}
.modal-footer{
  /* position: absolute; */
  bottom: 20px;
  width: 500px;
  height: 6%;
}
.bottom-btn-cancel, .bottom-btn-confirm{
    /* position: absolute; */
    display: inline-block;
    width: 50%;
}
.close-button {
 position: absolute;
  top: 0;
  right: 10px;
  /* height: 34px; */
  background: none;
  /* border: none !important; */
  font-size: 25px;
  cursor: pointer;
}
uni-button /deep/ {
    margin-left: 0 !important;
}
/deep/ uni-button:after {
    border: none;
}
button {
  margin-left: 10px;
}

/* 设置滚动条样式 */
 /*滚动条宽高 */
  ::-webkit-scrollbar {
    width: 9px;
    height: 8px;
  }

  /* 滚动条上的滚动滑块。样式 */
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.2);
  }

  /* 鼠标悬停时，设置滑块的背景颜色为深灰色 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #999999;
  }

  /* 鼠标按下时，设置滑块的背景颜色为灰色 */
  ::-webkit-scrollbar-thumb:active {
    background-color: #666666;
  }


</style>