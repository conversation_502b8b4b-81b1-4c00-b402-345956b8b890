/* #ifdef H5 */
uni-page {
	opacity: 0;
}

uni-page.animation-before {
	/* 在页面上使用 transform 会导致页面内的 fixed 定位渲染为 absolute，需要在动画完成后移除 */
	transform: translateY(20px);
}

uni-page.animation-leave {
	transition: all .3s ease;
}

uni-page.animation-enter {
	transition: all .3s ease;
}

uni-page.animation-show {
	opacity: 1;
}

uni-page.animation-after {
	/* 在页面上使用 transform 会导致页面内的 fixed 定位渲染为 absolute，需要在动画完成后移除 */
	transform: translateY(0);
}

/* #endif */
