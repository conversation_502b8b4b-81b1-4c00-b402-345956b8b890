export default {
	'contact': '\ue100',
	'person': '\ue101',
	'personadd': '\ue102',
	'contact-filled': '\ue130',
	'person-filled': '\ue131',
	'personadd-filled': '\ue132',
	'phone': '\ue200',
	'email': '\ue201',
	'chatbubble': '\ue202',
	'chatboxes': '\ue203',
	'phone-filled': '\ue230',
	'email-filled': '\ue231',
	'chatbubble-filled': '\ue232',
	'chatboxes-filled': '\ue233',
	'weibo': '\ue260',
	'weixin': '\ue261',
	'pengyouquan': '\ue262',
	'chat': '\ue263',
	'qq': '\ue264',
	'videocam': '\ue300',
	'camera': '\ue301',
	'mic': '\ue302',
	'location': '\ue303',
	'mic-filled': '\ue332',
	'speech': '\ue332',
	'location-filled': '\ue333',
	'micoff': '\ue360',
	'image': '\ue363',
	'map': '\ue364',
	'compose': '\ue400',
	'trash': '\ue401',
	'upload': '\ue402',
	'download': '\ue403',
	'close': '\ue404',
	'redo': '\ue405',
	'undo': '\ue406',
	'refresh': '\ue407',
	'star': '\ue408',
	'plus': '\ue409',
	'minus': '\ue410',
	'circle': '\ue411',
	'checkbox': '\ue411',
	'close-filled': '\ue434',
	'clear': '\ue434',
	'refresh-filled': '\ue437',
	'star-filled': '\ue438',
	'plus-filled': '\ue439',
	'minus-filled': '\ue440',
	'circle-filled': '\ue441',
	'checkbox-filled': '\ue442',
	'closeempty': '\ue460',
	'refreshempty': '\ue461',
	'reload': '\ue462',
	'starhalf': '\ue463',
	'spinner': '\ue464',
	'spinner-cycle': '\ue465',
	'search': '\ue466',
	'plusempty': '\ue468',
	'forward': '\ue470',
	'back': '\ue471',
	'left-nav': '\ue471',
	'checkmarkempty': '\ue472',
	'home': '\ue500',
	'navigate': '\ue501',
	'gear': '\ue502',
	'paperplane': '\ue503',
	'info': '\ue504',
	'help': '\ue505',
	'locked': '\ue506',
	'more': '\ue507',
	'flag': '\ue508',
	'home-filled': '\ue530',
	'gear-filled': '\ue532',
	'info-filled': '\ue534',
	'help-filled': '\ue535',
	'more-filled': '\ue537',
	'settings': '\ue560',
	'list': '\ue562',
	'bars': '\ue563',
	'loop': '\ue565',
	'paperclip': '\ue567',
	'eye': '\ue568',
	'arrowup': '\ue580',
	'arrowdown': '\ue581',
	'arrowleft': '\ue582',
	'arrowright': '\ue583',
	'arrowthinup': '\ue584',
	'arrowthindown': '\ue585',
	'arrowthinleft': '\ue586',
	'arrowthinright': '\ue587',
	'pulldown': '\ue588',
	'closefill': '\ue589',
	'sound': '\ue590',
	'scan': '\ue612'
}
