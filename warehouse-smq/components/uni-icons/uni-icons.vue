<template>
	<text :style="{ color: color, 'font-size': size + 'px' }" class="uni-icons" @click="onClick">{{ iconMap[type] || '' }}</text>
</template>

<script>
import icons from './icons.js'

export default {
	name: 'UniIcons',
	props: {
		type: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: '#333333'
		},
		size: {
			type: [Number, String],
			default: 16
		}
	},
	computed: {
		iconMap() {
			return icons
		}
	},
	methods: {
		onClick() {
			this.$emit('click')
		}
	}
}
</script>

<style>
.uni-icons {
	font-family: uniicons;
	text-decoration: none;
	text-align: center;
}
</style>
