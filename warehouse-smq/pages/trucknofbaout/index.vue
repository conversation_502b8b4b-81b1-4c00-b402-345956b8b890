<template>
  <view>
  <!-- 顶部栏 -->
  <view class="step">
    <uni-nav-bar :shadow='false' :title="$t('content.TruckNoFBAOut')" @clickLeft="backToIndex()">
      <view slot="left">
        <i class="iconfont iconfanhui1 icon-left"/>
      </view>
      <view slot="right"> <text>{{$username}}</text> </view>
    </uni-nav-bar>
  </view>

  <!-- 操作栏 -->
  <view class="operation">

    <view class="scanner">
		<text style="font-size: 160%;">扫描中性面单：</text>
		<input class="scanner-input uni-input"
			readonly
			cursor-spacing="10"
			ref='parcel_num'
			type="text"
			:placeholder="$t('content.PScanInBoundOrderNo')"
			:focus="master_order_num_focus"
			@confirm="nextStep()"
			v-model="formData.master_order_num"
		/>
    </view>
    <view>
		<uni-fab
			:pattern="pattern"
			:content="content"
			:horizontal="horizontal"
			:vertical="vertical"
			:direction="direction"
			@trigger="trigger"
			@fabClick="fabClick">
		</uni-fab>
    </view>
	
    <view class="info-container" v-if="master_order_info.master_order_num" v-model="master_order_info">
		<view class="info-row">
			<view class="info-column">
        <view class="info-item" style="color: #ff0000; cursor: pointer;" @click="viewNotOutWarehouseOrders">
					<text>提单号： {{ master_order_info.master_order_num }}</text>
				</view>
				<!-- <view class="info-item">
					<text>预计出仓数：</text>
					<text>{{ master_order_info.pre_carton }}</text>
				</view> -->
				<view class="info-item">
					<text>配载票数： {{ master_order_info.set_ocean }}</text>
				</view>
				<view class="info-item">
					<text>配载件数： {{ master_order_info.set_warehouse }}</text>
				</view>
			</view>
			<view class="info-column">
				<!-- <view class="info-item" style="color: red;" @click="to_not_scanned"> -->
              <view class="info-item" style="color: #0000ff;" @click="viewNotInWarehouse">
                <text>未入仓包裹： {{ master_order_info.not_in_warehouse }}</text>
              </view>
				<!-- <view class="info-item">
					<text>客户姓名：</text>
					<text>{{ master_order_info.client_name }}</text>
				</view> -->
              <!-- <view class="info-item">
                <text>入仓数量：</text>
                <text>{{ master_order_info.in_warehouse }}</text>
              </view>
              <view class="info-item">
                <text>出仓数量：</text>
                <text>{{ master_order_info.out_warehouse }}</text>
              </view> -->
              <view class="info-item">
                <text>出仓票数： {{ master_order_info.out_warehouse_qty }}</text>
              </view>
              <view class="info-item">
                <text>出仓件数： {{ master_order_info.out_warehouse_num }}</text>
              </view>
              <view class="info-item" style="color: #ff0000; cursor: pointer;" @click="viewNotOutWarehouse">
                <text>未出仓包裹: {{ master_order_info.not_out_warehouse }}</text>
              </view>
            </view>
		</view>
    </view>
	
    <view v-else class="no-data">
      <!-- 如果 master_order_info 不存在，则显示其他内容或者留空 -->
      <text>暂无数据</text>
    </view>
	
	<view v-if="responseDataList.length > 0">
	  <text style="font-size: 150%;">已扫描包裹：</text>
	  <view v-for="(data, index) in responseDataList.slice().reverse()" :key="index">
		<view class="info-container">
		  <view class="info-row">
			<view class="info-column">
			  <view class="info-item" :style="{ 'font-size': index === 0 ? '30px' : '20px' }">
				<text>{{ data }}</text>
			  </view>
			</view>
		  </view>
		</view>
	  </view>
	</view>

	<!-- 使用v-if进行条件渲染 -->
	<button v-if="isButtonVisible" @click="handleButtonClick">出仓完成</button>
	
    <!-- 模态框: 选择海运提单 -->
    <view class="oceanModal" v-if="showSelectOceanOrder">
        <view class="modal-content">
            <div class="custom-select">
                <text style="font-size: 160%;">请选择海运提单：</text>
            </div>
              <uni-data-select
                v-model="ocean_num_id"
                :localdata="oceanOrderOptions"
                @change=""
              ></uni-data-select>
            <view class="model-bottom-btn">
                <button class="left bottom-btn-cancel" type="" @click="handleCancel()" :loading="false">取消</button>
                <button class="right bottom-btn-confirm" type="primary" @click="handleConfirm()" :loading="false">确认</button>
            </view>
        </view>
    </view>
    
    <showListDialog :dialog-modal-visible.sync="notInWarehouseVisible" :dialog-content-data="notInWarehouseData" title="当前订单未入仓包裹" @dialog-confirm="dialogConfirm">
    </showListDialog>
    
    <showListDialog :dialog-modal-visible.sync="notOutWarehouseVisible" :dialog-content-data="notOutWarehouseData" title="当前订单未出仓包裹" @dialog-confirm="dialogConfirm">
    </showListDialog>

    <showListDialog :dialog-modal-visible.sync="notOutWarehouseOrdersVisible" :dialog-content-data="notOutWarehouseOrders" title="当前海运单未出仓订单" @dialog-confirm="dialogConfirm">
    </showListDialog>
    
    <!-- 底栏操作按钮 -->
    <view class="bottom-btn">
      <button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Finish')}}</button>
      <button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
    </view>

  </view>
  </view>
</template>

<script>
import {
	getStep, nextStep, truck_not_fba_parcel_out, out_warehouse_over, pda_save_img, parcel_get_ocean_order
} from '../../commom/js/api.js'
import UniDataSelect from '@/components/uni-data-select/uni-data-select'
import showListDialog from '@/components/detail/dialog.vue'


export default {
    components: {
    'uni-data-select': UniDataSelect,
    showListDialog
    },
	data() {
		return {
			isButtonVisible: false,
			master_order_num_focus: true,
			express_order_num_focus: false,
			master_order_info: {
				master_order_num: '',
				express_order_num: ''
			}, // 主单信息
			responseDataList: [], // 保存返回的数据列表
			subOrderNumber: '',
			orderInfo: {
				masterOrder: '',
				errorSubOrders: 0,
				loadedTickets: 0,
				deliveredTickets: 0,
				loadedItems: 0,
				deliveredItems: 0,
			},
			pattern: {
				color: '',
				selectedColor: '',
				backgroundColor: '',
				buttonColor: ''
			},
			horizontal: 'left',
			vertical: 'bottom',
			direction: 'vertical',
			content: [
				{
					iconPath: '/static/search.png',
					selectedIconPath: '/static/search.png',
					text: this.$t('content.ScanCodeQuery'),
					active: true
				},
			],
			formData: {
				master_order_num: '',
				express_order_num: ''
			},
			option: {},
			loading: false,
			ocean_num_id: null,
            ocean_nums: [],
            showSelectOceanOrder: false,
            oceanOrderOptions: [],
            notInWarehouseVisible: false,
            notOutWarehouseVisible: false,
            notOutWarehouseOrdersVisible: false,
            notInWarehouseData: [],
            notOutWarehouseData: [],
            notOutWarehouseOrders: []
		}
	},
	onLoad: function(option) { //option为object类型，会序列化上个页面传递的参数
		// this.getStep(option.type)
	},
	created() {
	},
	methods: {
		// 扫描快递号
		to_scan_express() {
			const params = {
				parcel_num: this.formData.master_order_num,
				tracking_num: this.formData.master_order_num,
				// order_num: this.master_order_info.master_order_num !== "" ? this.master_order_info.master_order_num : ""
			}
			uni.navigateTo({
				url: `../nottransfer/index?parcel_num=${this.formData.master_order_num}`,
				query: params
			})
		},
		// 下一步
		async nextStep() {
			// 等待500毫秒，可以根据实际情况调整时间
			await new Promise(resolve => setTimeout(resolve, 500));
            if(this.showSelectOceanOrder) {
                uni.showToast({
                	title: '未选择海运提单',
                	icon: 'none'
                })
                return
            }
            if(this.ocean_num_id===null){
                if(this.ocean_nums.length === 1){
                    this.ocean_num_id = this.ocean_nums[0]['id']
                } else {
                    this.getOceanOrderNum()
                }
            }
			const params = {
				mid_num: this.formData.master_order_num,
                ocean_num_id: this.ocean_num_id
			}
			truck_not_fba_parcel_out(params).then(res => {
				if (res.code === 200) {
					this.formData.master_order_num = res.parcel_num;
					this.master_order_info = res.data.master_order;
					this.responseDataList.push(res.data.sum_order_num);
					this.cleanFocus()
					
					// 每次出库成功后判断 是否要显示出库完成按钮
					console.log(this.master_order_info.set_warehouse)
					console.log(this.master_order_info.out_warehouse_num)
					if (this.master_order_info.set_warehouse === this.master_order_info.out_warehouse_num) {
						this.isButtonVisible = true
					}
                    // 全部出仓, 重置ocean_num_id
                    if (res.data.all_out) {
                        this.ocean_num_id = null
                    } else {
                        this.ocean_num_id = res.data.ocean_num_id
                    }
				}else if (res.code === 402) {
                    uni.showToast({
                        title: '失败: 请先选择海运提单',
                        icon: 'none'
                    })
                    // this.getOceanOrderNum()
                    this.ocean_num_id = null
                } else {
                    this.failAudio()
					console.log(res.msg)
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
                    this.cleanFocus()
				}
			}).catch(err => {
                this.failAudio()
                uni.showToast({
                    title: '失败: ' + err,
                    icon: 'none'
                })
				console.error('POST请求失败', err);
				this.cleanFocus()
			})
		},
		handleButtonClick(){
			const params = {
				parcel_num: this.master_order_info.master_order_num
			}
			console.log(params)
			console.log("发送请求，出仓完成")
			
			out_warehouse_over(params).then(res => {
				if (res.code === 200) {
					this.formData.master_order_num = "";
					this.master_order_info = "";
					this.responseDataList = [];
					
					// 清空输入框 并 重新聚焦
					this.$set(this.formData, 'master_order_num', '')
					this.$set(this.formData, 'express_order_num', '')
					this.master_order_num_focus=false
					this.$nextTick(() => {
						this.master_order_num_focus=true
					});
				} else {
                    this.failAudio()
					console.log(res.msg)
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			}).catch(err => {
                this.failAudio()
				console.error('请求失败', err);
			})
		},
		getStep(type){
			uni.showLoading({
				title: this.$t('content.Loading'),
				mask: true
			});
			getStep({type}).then(res=>{
				console.log(res);
				if(res.code===200){
					getApp().globalData.request = {...getApp().globalData.request,...res.data.params}
					this.getStepRouter(res.data.step)
				}
				uni.hideLoading();
			}).catch(err=>{uni.hideLoading();})
		},
		// 清空再聚焦
		cleanFocus(){
			// 清空输入框
			this.$set(this.formData, 'master_order_num', '')
			this.focusStatus=false
			// 重新聚焦
			this.$nextTick(() => {
				this.focusStatus=true
			});
		},
		// 监听回车
		handelEnter(str) {
			if (str === 'post') {
				this.getOrder()
			} else if (str) {
				this.$nextTick(() => {
					this.$refs[str].focus=true;
				});
			}
		},
		// 跳转 查看未处理包裹
		to_not_scanned() {
			const not_scan_params = {
				order_num: this.master_order_info.master_order_num,
			}
			uni.navigateTo({
				url: `../nottransfer/index?order_num=${this.master_order_info.master_order_num}`,
				query: not_scan_params
			})
		},
		// 核重
		getOrder() {
			const weight = localStorage.getItem('fba_update_weight')
			const size = localStorage.getItem('fba_update_size')

			// 包裹号
			if (!this.formData.parcel_num) {
				this.$nextTick(function() {
			})
				this.$notify.error({
				title: 'error',
				message: '请确保填写了包裹号'
			})
			  return
			}
	
			his.loading = true
		
			const api = '/api/fbaBigParcels/fba_parcel_size/'
			actionPost({ api, data: this.formData }).then(res => {
				if (res.code === 200) {
					this.$notify.success({
					title: 'success',
					message: res.msg
				})
				this.handelEnter('parcel_num')
				// this.frontOrderNum = Object.assign({}, this.formData).order_num
				this.frontWeight = res.data.weight
				this.diffWeight = res.data.diffWeight
				this.scanResult = res.msg
				this.parcelOrderNums = (this.parcelOrderNums + 1)
				const weightingData = {
					result: 'success',
					time: res.data.time,
					order_num: res.data.order_num,
					parcel_num: res.data.parcel_num,
					product: res.data.product,
					buyer_country_code: res.data.buyer_country_code,
					label_weight: res.data.label_weight,
					label_length: res.data.label_length,
					label_width: res.data.label_width,
					label_height: res.data.label_height,
					actual_weight: res.data.actual_weight,
					actual_length: res.data.actual_length,
					actual_width: res.data.actual_width,
					actual_height: res.data.actual_height,
					country_code: res.data.buyer_country_code
				}
				this.parcelWeighingList.push(weightingData)
				this.parcelWeighingListReverse = [...this.parcelWeighingList].reverse()
				this.formData.parcel_num = ''
				this.formData.label_weight = weight ? localStorage.getItem('fba_update_weight') : ''
				this.formData.label_length = size ? size.split(',')[0] : ''
				this.formData.label_width = size ? size.split(',')[1] : ''
				this.formData.label_height = size ? size.split(',')[2] : ''
				this.pre_carton = res.data.pre_carton
				this.frontOrderNum = res.data.parcel_num
				this.formData.mojia_img = res.data.mojia_img
				this.$nextTick(function() {
					this.success_count += 1
				})
			} else {
				this.$nextTick(function() {
				  this.fail_count += 1
				})
				this.$notify.error({
				  title: 'error',
				  message: res.msg || res.detail
				})
			}
			  this.loading = false
			}).catch(() => { this.loading = false })
		  },
		// 校验
		validateForm() {
			return new Promise((resolve, reject) => {
				if (!this.formData.master_order_num) {
					this.failAudio()
					uni.showToast({
						title: this.$t('content.PScanInventoryOrderNo'),
						icon: 'none',
						mask: true,
						duration: 2000
					});
					resolve(false)
				}
				resolve(true)
			})
		},
		onInputConfirm() {
			// 在这里处理扫描子单号的逻辑，更新 orderInfo 数据
			// 为了示例，这里简单地模拟了一些数据
			this.orderInfo = {
				masterOrder: '123456',
				errorSubOrders: 5,
				loadedTickets: 10,
				deliveredTickets: 8,
				loadedItems: 20,
				deliveredItems: 15,
			};
		},
        setSelectOption(resData, err=0) {
            console.log('resData-->', resData)
            if (resData.length===0) {
                uni.showToast({
                	title: '未查询到海运提单',
                	icon: 'none'
                })
                return
            } else if (resData.length===1) {
                this.ocean_num_id = resData[0]['id']
            } else {
                this.ocean_nums = resData
                this.showSelectOceanOrder = true
                // this.$nextTick(() => {
                //     // 获取 select 元素
                //     const selectElement = document.getElementById('select-ocean');
                //     selectElement.options.length = 0;
                //     // 动态生成选项并添加到 select 中
                //     resData.forEach((option, index) => {
                //         const optionElement = document.createElement('option');
                //         optionElement.value = option.id;
                //         optionElement.textContent = option.order_num;
                //         selectElement.appendChild(optionElement);
                //     })
                // });
                this.oceanOrderOptions = resData
            }
        },
		getOceanOrderNum() {
		    const params = {
		    	parcel_num: this.formData.master_order_num
		    }
		    parcel_get_ocean_order(params).then(res => {
		    	if (res.code === 200) {
		            this.setSelectOption(res.data.ocean_order)
		    	} else {
		    		console.log(res.msg)
		    		uni.showToast({
		    			title: res.msg,
		    			icon: 'none'
		    		})
		            this.ocean_num_id = null
		            this.failAudio()
		    	}
		    }).catch(err => {
		        console.log(err)
                uni.showToast({
                	title: err,
                	icon: 'none'
                })
		    	this.$set(this.formData, 'fba_num', '')
		        this.ocean_num_id = null
		        this.failAudio()
		    })
		},
        handleCancel(){
            this.showSelectOceanOrder = false
        },
        async handleConfirm(){
            // this.$nextTick(() => {
            //     const selectElement = document.getElementById('select-ocean');
            //     this.ocean_num_id = selectElement.value
            // })
            this.showSelectOceanOrder = false
            console.log('this.ocean_num_id-->', this.ocean_num_id)
        },
        viewNotInWarehouse() {
            this.notInWarehouseData = this.master_order_info['not_in_warehouse_parcel']
            this.notInWarehouseVisible = true
        },
        viewNotOutWarehouse() {
            this.notOutWarehouseData = this.master_order_info['not_out_warehouse_parcel']
            this.notOutWarehouseVisible = true
        },
        viewNotOutWarehouseOrders() {
            this.notOutWarehouseOrders = this.master_order_info['not_out_warehouse_orders']
            this.notOutWarehouseOrdersVisible = true
        },
        async dialogConfirm(){
            this.notInWarehouseVisible = false
            this.notOutWarehouseVisible = false
            this.notOutWarehouseOrdersVisible = false
        }
	}
}
</script>

<style scoped>
.container {
  padding: 10px;
}

.input-container {
  margin-bottom: 20px;
}

.input-box {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-container {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-row {
  display: flex;
  justify-content: space-between;
}

.info-column {
  display: flex;
  flex-direction: column;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh; /* 100%视窗高度，根据实际需要调整 */
}

/* 海运提单模态框样式 */
.form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
}
.form-control {
    min-width: 100px;
    display: block;
    width: 100%;
    height: 50px;
    padding: 6px 12px;
    font-size: 20px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
}
.oceanModal{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-content {
  position: relative;
  background: #fff;
  padding: 20px;
  border-radius: 5px;
  width: 500px;
  height: 300px;
}
.model-bottom-btn{
  position: absolute;
  bottom: 20px;
  width: 500px;
  height: 46px;
}
.bottom-btn-cancel, .bottom-btn-confirm{
    position: absolute;
    display: inline-block;
    width: 50%;
}
.right{
    right: 0;
}
.left{
    left: 0;
}
/* 海运提单模态框样式结束 */
</style>
