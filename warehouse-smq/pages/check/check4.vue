<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.Inventory')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left" />
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>

		<!-- 操作栏 -->
		<view class="operation">
			<!-- 顶部显示栏 -->
			<view class="top-info">
				<view>{{$t('content.InventoryOrderNo')}}：{{requestData.master_order_num}}</view>
				<view>{{$t('content.Location')}}：{{requestData.target_location}}</view>
				<view>SKU NO.：{{requestData.sku_code}}</view>
				<view v-if="requestData.book_inventory">{{$t('content.BookQuantity')}}：{{requestData.book_inventory}}</view>
			</view>
			<view class="scanner">
				<view class="scanner-title">{{$t('content.EnterCurrentSKUQuantity')}}</view>
				<input class="scanner-input uni-input" cursor-spacing="10" :placeholder="$t('content.PEnterCurrentSKUQuantity')" focus @confirm="nextStep()" v-model="formData.check_inventory" />
			</view>
			<view>
				<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
				 @trigger="trigger" @fabClick="fabClick"></uni-fab>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Finish')}}</button>
				<button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
			</view>
		</view>

	</view>

</template>

<script>
	const Qs = require('qs')
	import {
		nextStep,
		get_intranet
	} from '../../commom/js/api.js'
	import uniFab from '@/components/uni-fab/uni-fab.vue';
	import { successAudio,failAudio } from '../../audio.js'
	export default {
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [
					{
						iconPath: '/static/search.png',
						selectedIconPath: '/static/search.png',
						text: this.$t('content.ScanCodeQuery'),
						active: true
					},
				],
				formData: {
					check_inventory: null
				},
				// 初始反写信息
				initData: {},
				comfirm: false,
				option: {},
				loading: false
			}
		},
		computed: {
			// 获取当前步骤需要提交的表单信息
			requestData() {
				return {
					...getApp().globalData.request,
					step: this.getRoutePath().step,
					...this.formData,
				}
			}
		},
		onLoad: function(option) {
			this.option = option
		},
		created() {
			const t = getApp().globalData.request
			Object.keys(t).forEach(i => {
				if (i.startsWith('$')) {
					this.initData[i.slice(1)] = t[i]
					this.formData[i.slice(1)] = t[i]
					this.comfirm = true
				}
			})
		},
		methods: {
			trigger(data) {
				switch (data.index) {
					case 0:
						uni.navigateTo({
							url: '../search/index'
						})
						break;
					default:
						break;
				}			
			},
			fabClick() {},
			// 校验
			validateForm() {
				return new Promise((resolve, reject) => {
					console.log(this.formData.width);
					if (!this.formData.check_inventory) {
						this.failAudio()
						uni.showToast({
							title: this.$t('content.PEnterCountQuantity'),
							icon: 'none',
							mask: true,
							duration: 2000
						});
						resolve(false)
					} else if (Number(this.formData.check_inventory) < 0) {
						this.failAudio()
						uni.showToast({
							title: this.$t('content.CountQuantityCannotThanZero'),
							icon: 'none',
							mask: true,
							duration: 2000
						});
						resolve(false)
					}
					resolve(true)

				})
			},
			// 返回
			back() {
				uni.redirectTo({
					url: this.getRoutePath().lastPath
				})
			},
			// 下一步
			async nextStep() {
				if (this.loading) return
				this.loading = true
				uni.showLoading({
					title: this.$t('content.InRequest'),
					mask: true
				});
				let flag = await this.validateForm()
				uni.hideLoading();
				this.loading = false
				if (!flag) return
				nextStep(this.filterRequest(this.requestData)).then(res => {
					console.log(res);
					if (res.code === 200) {
						this.successAudio()
						if (res.data.step != 1) {
							getApp().globalData.request = { ...getApp().globalData.request,
								...res.data
							}
						} else {
							const type = getApp().globalData.request.type
							getApp().globalData.request = {
								type,
								master_order_num: res.data.master_order_num
							}
						}
						uni.showToast({
							title: this.$t('content.InventorySucceeded'),
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
						uni.redirectTo({
							url: this.getRoutePath().basicPath + res.data.step
						})
					} else {
						this.failAudio()
						uni.showToast({
							title: res.detail || res.message || 'fail request! please check!',
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
						this.formData.check_inventory = ''
					}
				})
			},
		}
	}
</script>

<style>
</style>
