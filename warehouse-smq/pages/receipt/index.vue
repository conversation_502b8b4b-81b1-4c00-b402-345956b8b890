<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
      <uni-nav-bar :shadow='false' :title="$t('content.Receipt')" @clickLeft="backToIndex()">
        <view slot="left">
          <i class="iconfont iconfanhui1 icon-left"/>
        </view>
        <view slot="right"> <text>{{$username}}</text> </view>
      </uni-nav-bar>
    </view>

    <!-- 操作栏 -->
    <view class="operation">
      <view class="scanner">
		<text style="font-size: 200%;">扫描箱号：</text>
        <input class="scanner-input uni-input" 
			ref='parcel_num' 
			cursor-spacing="10"
			type="text"
			:placeholder="$t('content.PScanInBoundOrderNo')"
			:focus="focusStatus"
			@confirm="nextStep()"
			v-model="formData.master_order_num"
		/>
      </view>
      <view>
        <uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
                 @trigger="trigger" @fabClick="fabClick"></uni-fab>
      </view>

      <view class="table-container" v-if="responseDataList.length>0">
        <view class="table-header">
          <text style="text-align: center;">包裹号</text>
          <text style="text-align: center;">扫描时间</text>
        </view>
        <view v-for="item in responseDataList" :key="item.parcel_num" class="table-row">
          <text class="table-item">{{ item.parcel_num }}</text>
          <text class="table-item">{{ item.scanned_time }}</text>
        </view>
      </view>

      <view v-else class="no-data">
        <!-- 如果 master_order_info 不存在，则显示其他内容或者留空 -->
        <text>暂无数据</text>
      </view>

      <!-- 底栏操作按钮 -->
      <view class="bottom-btn">
        <button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Finish')}}</button>
        <button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
      </view>
    </view>
  </view>
</template>

<script>
	import {
		getStep, fba_parcel_out_cancel
	} from '../../commom/js/api.js'
	export default {
		data() {
			return {
				focusStatus: true,
				master_order_info: {
					master_order_num: ''
				}, // 主单信息
				responseDataList: [], // 保存返回的数据列表
				tableData:[],
				subOrderNumber: '',
				orderInfo: {
					masterOrder: '',
					errorSubOrders: 0,
					loadedTickets: 0,
					deliveredTickets: 0,
					loadedItems: 0,
					deliveredItems: 0,
				},
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [
					{
						iconPath: '/static/search.png',
						selectedIconPath: '/static/search.png',
						text: this.$t('content.ScanCodeQuery'),
						active: true
					},
				],
				formData: {
					master_order_num: '',
					sku_code: ''
				},
				option: {},
				loading: false
			}
		},
		onLoad: function(option) {
			// option为object类型，会序列化上个页面传递的参数
			// this.getStep(option.type)
		},
		created() {
		},
		watch: {
		    // 监听父组件传递的 focus prop 的变化
		    focus(newValue) {
				this.inputFocus = newValue;
		    }
		  },
		methods: {
			getStep(type){
				uni.showLoading({
					title: this.$t('content.Loading'),
					mask: true
				});
				getStep({type}).then(res=>{
					console.log(res);
					if(res.code===200){
						getApp().globalData.request = {...getApp().globalData.request,...res.data.params}
						this.getStepRouter(res.data.step)
					}
					uni.hideLoading();
				}).catch(err=>{uni.hideLoading();})
			},
			// 监听回车
			handelEnter(str) {
				if (str === 'post') {
					this.getOrder()
				} else if (str) {
					this.$nextTick(() => {
						this.$refs[str].focus=true;
					})
				}
			},
			async nextStep() {
				// 等待500毫秒，可以根据实际情况调整时间
				await new Promise(resolve => setTimeout(resolve, 500));
				const params = {
					parcel_num: this.formData.master_order_num
				}
				fba_parcel_out_cancel(params).then(res => {
					console.log('POST请求成功', res);
					if (res.code === 200) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						// 清空输入框
						this.$set(this.formData, 'master_order_num', '')
						this.focusStatus=false
						// 重新聚焦
						this.$nextTick(() => {
							this.focusStatus=true
						});
					} else {
                        this.failAudio()
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						this.responseDataList.push(res);
						// 清空输入框
						this.$set(this.formData, 'master_order_num', '')
						this.focusStatus=false
						// 重新聚焦
						this.$nextTick(() => {
							this.focusStatus=true
						});
					}
				}).catch(err => {
                    this.failAudio()
                    uni.showToast({
                        title: `请求失败: ${err}`,
                        icon: 'none'
                    })
					console.error('POST请求失败', err);
					// 清空输入框
					this.$set(this.formData, 'master_order_num', '')
					this.focusStatus=false
					// 重新聚焦
					this.$nextTick(() => {
						this.focusStatus=true
					});
				})
			},
		}
	}
</script>

<style>
.container {
	padding: 20px;
}

.input-container {
	margin-bottom: 20px;
}

.input-box {
	border: 1px solid #ccc;
	padding: 10px;
}

.table-container {
	border: 1px solid #ccc;
}

.table-header {
	display: flex;
	justify-content: space-between;
	background-color: #f0f0f0;
	padding: 10px;
}

.table-row {
	display: flex;
	justify-content: space-between;
	padding: 10px;
	border-top: 1px solid #ccc;
}

.table-item {
	text-align: left;
}

.no-data {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 60vh; /* 100%视窗高度，根据实际需要调整 */
}
</style>
