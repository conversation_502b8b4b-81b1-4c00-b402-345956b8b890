<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.Refunds')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left"/>
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>

		<!-- 操作栏 -->
		<view class="operation">
			<!-- 顶部显示栏 -->
			<view class="top-info">
				<view>{{$t('content.RefundOrderNo')}}：{{requestData.master_order_num}}</view>
				<view>{{$t('content.OutBoundOrderNo')}}：{{requestData.outbound_order_num}}</view>
				<view>{{$t('content.TrackingNo')}}：{{requestData.tracking_no}}</view>
			</view>
			
			<view class="bottom-info">
				<view class="list-table">
					<table style='word-wrap: break-word; word-break: break-all;'>
						<th>
						<td>{{$t('content.GoodsInfo')}}</td>
						</th>
						<template v-if="requestData.refund_details.length===0">
							<tr>
								{{$t('content.NoData')}}
							</tr>
						</template>
						<template v-else>
							<tr v-for="item in requestData.refund_details" :key="item.id">
								<td class='td_size' rowspan="2">{{item.name}}</td>
								<td class='td_size' rowspan="2">{{item.value}}</td>
							</tr>
						</template>
						
					</table>
				</view>
			</view>
			
			<view>
				<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
				 @trigger="trigger" @fabClick="fabClick"></uni-fab>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Return')}}</button>
				<button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.Confirm')}}</button>
			</view>
		</view>

	</view>

</template>

<script>
	import {
		nextStep
	} from '../../commom/js/api.js'
	import uniFab from '@/components/uni-fab/uni-fab.vue';
	import { successAudio,failAudio } from '../../audio.js'
	export default {
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [
					{
						iconPath: '/static/search.png',
						selectedIconPath: '/static/search.png',
						text: this.$t('content.ScanCodeQuery'),
						active: true
					},
				],
				formData: {
					refund_order_num: ''
				},
				option: {},
				loading: false
			}
		},
		computed: {
			// 获取当前步骤需要提交的表单信息
			requestData() {
				return {
					...getApp().globalData.request,
					step: this.getRoutePath().step,
					...this.formData,
				}
			}
		},
		onLoad: function(option) {
			this.option = option
		},
		methods: {
			trigger(data) {
				switch (data.index) {
					case 0:
						uni.navigateTo({
							url: '../search/index'
						})
						break;
					default:
						break;
				}			
			},
			fabClick() {},
			// 校验
			validateForm() {
				return new Promise((resolve, reject) => {
					// if (!requestData.data.master_order_num) {
					// 	this.failAudio()
					// 	uni.showToast({
					// 		title: this.$t('content.ScanOrEnterSKUNO'),
					// 		icon: 'none',
					// 		mask: true,
					// 		duration: 2000
					// 	});
					// 	resolve(false)
					// }
					resolve(true)

				})
			},
			// 返回
			back() {
				uni.redirectTo({
					url: this.getRoutePath().lastPath
				})
			},
			// 下一步
			async nextStep() {
				if (this.loading) return
				this.loading = true
				uni.showLoading({
					title: this.$t('content.InRequest'),
					mask: true
				});
				let flag = await this.validateForm()
				uni.hideLoading();
				this.loading = false
				if (!flag) return
				nextStep(this.filterRequest(this.requestData)).then(res => {
					if (res.code === 200) {
						this.successAudio()
						if (res.data.step != 1) {
							getApp().globalData.request = { ...getApp().globalData.request,
								...res.data
							}
						}
	                    this.formData.refund_order_num = res.data.master_order_num
						uni.redirectTo({
							url: this.getRoutePath().basicPath + res.data.step
						})
					} else {
						this.failAudio()
						uni.showToast({
							title: res.detail || res.message || 'fail request! please check!',
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
						
					}
				})
			},
		}
	}
</script>

<style>
</style>
