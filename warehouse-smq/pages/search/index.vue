<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.ScanCodeQuery')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left" />
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>
		<!-- 操作栏 -->
		<view class="operation">
			<view class="scanner">
				<view class="scanner-label">{{$t('content.ScanOrEnterCode')}}：</view>
				<input class="scanner-input" type="text"  @confirm="nextStep()" v-model="formData.scan_code" focus :placeholder="$t('content.PScanOrEnterCode')" />
			</view>
			<!-- 底部显示栏 -->
			<view class="bottom-info">
				<view>{{formData.result_code}}</view>
				<view class="list-table">
					<table style='word-wrap: break-word; word-break: break-all;'>
						<th>
						<td>{{$t('content.QueryResult')}}</td>
						</th>
						<template v-if="formData.record.length===0">
							<tr>
								{{$t('content.NoData')}}
							</tr>
						</template>
						<template v-else>
							<tr v-for="item in formData.record" :key="item.id">
								<td class='td_size' rowspan="2">{{item.name}}</td>
								<td class='td_size' rowspan="2">{{item.value}}</td>
							</tr>
						</template>
						
					</table>
				</view>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Return')}}</button>
				<button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.Confirm')}}</button>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		code_search
	} from '../../commom/js/api.js'
	export default {
		data() {
			return {
				formData: {
					scan_code: '',
					result_code: '',
					record: []
				},
				option: {},
				loading: false
			}
		},
		computed: {
			// 获取当前步骤需要提交的表单信息
			requestData() {
				return {
					...getApp().globalData.request,
					step: this.getRoutePath().step,
					...this.formData,
				}
			}
		},
		onLoad: function(option) {
			console.log(option);
			this.option = option
		},
		methods: {
			// 校验
			validateForm() {
				return new Promise((resolve, reject) => {
					if (!this.formData.scan_code) {
						this.failAudio()
						uni.showToast({
							title: this.$t('content.PScanOrEnterCode'),
							icon: 'none',
							mask: true,
							duration: 2000
						});
						resolve(false)
					}
					resolve(true)

				})
			},
			// 返回
			back() {
				// uni.redirectTo({
				// 	url: '../index/index'
				// })
				uni.navigateBack({
					delta: 1 // 返回上一级页面。
				})
			},
			// 下一步
			async nextStep() {
				if (this.loading) return
				this.loading = true
				uni.showLoading({
					title: this.$t('content.InRequest'),
					mask: true
				});
				let flag = await this.validateForm()
				uni.hideLoading();
				this.loading = false
				if (!flag) return
				code_search(this.filterRequest(this.requestData)).then(res => {
					if (res.code === 200) {
						this.successAudio()
						getApp().globalData.request = { ...getApp().globalData.request,
							...res.data
						}
						this.formData.sku_name = res.data.sku_name
						this.formData.result_code = res.scan_code
						this.formData.record = res.data
						this.formData.scan_code = ''
						
					} else {
						this.failAudio()
						uni.showToast({
							title: res.detail || res.message || 'fail request! please check!',
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
					}
				})
			},
		}
	}
</script>

<style scoped>
	.td_size{
		text-align: left;
	}
</style>
