<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
		  <uni-nav-bar :shadow='false' title="盘点单详情" @clickLeft="backToTaskStock()">
		    <view slot="left">
		      <i class="iconfont iconfanhui1 icon-left"/>
		    </view>
		    <view slot="right"> <text>{{$username}}</text> </view>
		  </uni-nav-bar>
		</view>
		
		<!-- 操作栏 -->
		<view class="operation">
			<view class="order-info">
			  <view class="info-item">
				<text class="info-title">作业单号：</text>
				<text class="info-value">{{ orderInfo.job_num }}</text>
			  </view>
			  <view class="info-item">
				<text class="info-title">开始时间：</text>
				<text class="info-value">{{ formatDate(orderInfo.start_time) }}</text>
			  </view>
			  <view class="info-item">
				<text class="info-title">结束时间：</text>
				<text class="info-value">{{ formatDate(orderInfo.end_time) }}</text>
			  </view>
			  <!-- <view class="info-item">
				<text class="info-title">订单号：</text>
				<text class="info-value">{{ orderInfo.order_num }}</text>
			  </view>
			  <view class="info-item">
				<text class="info-title">客户名：</text>
				<text class="info-value">{{ orderInfo.customer_name }}</text>
			  </view>
			  <view class="info-item">
				<text class="info-title">产品名称：</text>
				<text class="info-value">{{ orderInfo.product_name }}</text>
			  </view> -->
			  <view class="info-item">
				<text class="info-title">状态：</text>
				<text class="info-value">{{ getStatusText(orderInfo.status) }}</text>
			  </view>
			  <view class="info-item">
				<text class="info-title">件数：</text>
				<text class="info-value">{{ orderInfo.parcel_qty }}</text>
			  </view>
			</view>
				
			<!-- 包裹详情表格 -->
			<view class="parcel-table">
			  <!-- 表头 -->
			  <view class="table-row table-header">
				<view class="table-cell">箱号</view>
				<view class="table-cell">订单号</view>
			  </view>
			  <view class="table-row" v-for="(parcel, index) in orderInfo.details" :key="index">
				<view class="table-cell">{{ parcel.parcel_num }}</view>
				<view class="table-cell">{{ parcel.order_num }}</view>
			  </view>
			</view>
		</view>
		
        <view class="fotter">
        	<button class="floating-button" @click="continueJob">
        	  继续盘点
        	</button>
        </view>
        
	</view>
</template>

<script>
	import {
		fbaTakeLoadDetail,
        fbaTakeStockStartJob
	} from '../../commom/js/api.js'
	
	export default {
		data() {
			return {
				id: null,
				orderInfo: {}
			}
		},
		onLoad(options) {
			this.id = options.id;
			console.log(`id_is:${this.id}`)
			this.start_load(this.id)
		},
		methods: {
			backToTaskStock() {
				uni.navigateTo({
					url: `./index`,
				})
			},
			formatDate(time) {
			  if (time == null) {
				  return ''
			  }
			  const date = new Date(time);
			  return date.toLocaleString();
			},
			getStatusText(status) {
			  const statusMap = {
				C: '已完结',
				I: '进行中',
				W: '未开始'
			  };
			  return statusMap[status] || '未知状态';
			},

			start_load(id) {
				fbaTakeLoadDetail(id).then(res => {
					if (res.code === 200) {
						this.orderInfo = res.data
						// console.log(`${JSON.stringify(this.orderInfo)}`)
					} 
				}).catch(err => {

				})
							
			},
            continueJob() {
                const params = {
                    'is_create': false,
                    'job_id': this.id
                }
                this.execute(params)
            },
            execute(params) {
                fbaTakeStockStartJob(params).then(res => {
                	if (res.code === 200) {
                		uni.showToast({
                			title: res.msg,
                			icon: 'none'
                		})
                		// 处理扫描结果
                		const jobData = res.data
                		this.toDetail(jobData)
                	} else {
                        this.failAudio()
                		uni.showToast({
                			title: res.msg || res.detail,
                			icon: 'none'
                		})
                		// this.responseDataList.push(res);
                	}
                }).catch(err => {
                    this.failAudio()
                    uni.showToast({
                        title: `请求失败: ${err}`,
                        icon: 'none'
                    })
                	console.log('POST请求失败', err);
                })
            },
            toDetail(data) {
            	  const params = JSON.stringify(data);
            	  uni.navigateTo({
            	    url: `./detail?data=${encodeURIComponent(params)}`
            	  });
            }

		}
	}
</script>

<style>
.order-info {
  /* 订单信息列表的样式 */
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-title {
  color: #888;
  font-size: 28rpx;
}

.info-value {
  color: #000;
  font-size: 32rpx;
  font-weight: bold;
}
	
.parcel-table {
  margin-top: 20rpx;
}
.table {
  width: 100%;
  border-collapse: collapse;
}

.table-row {
  display: flex;
  width: 100%;
}

.table-header {
  background-color: #f0f0f0;
  font-weight: bold;
}
.table-cell {
  flex: 1;
  border: 1px solid #dcdcdc;
  text-align: center;
  padding: 10px;
}

.table-header .table-cell {
  font-weight: bold;
}
/* 浮动底部按钮样式 */
.floating-button {
  position: fixed; /* 固定定位 */
  bottom: 20rpx; /* 距离底部20rpx */
  left: 50%; /* 距离左侧50%，实现水平居中 */
  transform: translateX(-50%); /* 向左移动50%，实现精确居中 */
  background-color: #55aaff; /* 按钮背景颜色 */
  color: white; /* 按钮文字颜色 */
  padding: 2rpx; /* 按钮内边距 */
  border-radius: 10rpx; /* 按钮边框圆角，非圆形 */
  width: 90%; /* 按钮宽度 */
  height: 80rpx; /* 按钮高度 */
  font-size: 30rpx; /* 文字大小 */
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2); /* 按钮阴影 */
  text-align: center; /* 文字居中 */
  line-height: 80rpx; /* 行高与按钮高度一致，实现文字垂直居中 */
}
</style>
