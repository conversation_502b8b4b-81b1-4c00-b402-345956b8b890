<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
      <uni-nav-bar
        :shadow="false"
        :title="$t('content.FbaTakeStock')"
        @clickLeft="backToIndex()"
      >
        <view slot="left">
          <i class="iconfont iconfanhui1 icon-left" />
        </view>
        <view slot="right">
          <text>{{ $username }}</text>
        </view>
      </uni-nav-bar>
    </view>

    <!-- 操作栏 -->
    <view class="operation">
      <view class="scanner">
        <text style="font-size: 200%">扫描箱号：</text>
        <input
          class="scanner-input uni-input"
          ref="parcel_num"
          cursor-spacing="10"
          type="text"
          placeholder="扫描已盘点的箱号, 进入对应盘点任务继续盘点"
          :focus="focusStatus"
          @confirm="doScan()"
          v-model="formData.scanNo"
        />
      </view>
      <!-- 扫描结果列表 -->
      <view class="scan-results" v-if="scanResults.length > 1">
        <view class="title-style">请选择一个单号</view>
        <view
          v-for="(item, index) in scanResults"
          :key="index"
          @click="goToDetail(item)"
        >
          <text class="link-style"
            >{{ item.order_num }} - {{ item.customer_name }}</text
          >
        </view>
      </view>
      <view class="info-item" style="font-size: 24px">
        <text
          >当前仓盘单： {{ currentOrder.take_stock_order_num || "无" }}</text
        >
      </view>
      <view class="info-item" style="font-size: 24px">
        <text>仓盘单件数： {{ currentOrder.parcel_qty || 0 }}</text>
      </view>
      <view class="list-container">
        <text class="title-style">历史盘点任务（最新10单）</text>
        <view
          class="list-item"
          v-for="(item, index) in historyOrders"
          :key="index"
        >
          <!-- <text :style="getStatusStyle(item.status)" @click="itemClick(item)"> -->
          <text :style="getStatusStyle(item.status)">
            {{ item.job_num }}（{{ item.parcel_qty }}件）
          </text>
        </view>
      </view>

      <!-- 模态框: 选择仓盘任务 -->
      <view class="dialogModal" v-if="showSelectOptions">
        <view class="modal-content">
          <view class="info-item" style="font-size: 24px">
            <text
              >当前仓盘单：
              {{ currentOrder.take_stock_order_num || "无" }}</text
            >
          </view>
          <UniDataSelect
            v-model="job_id"
            :localdata="existsJob"
            title="请选择盘点任务"
            @change=""
          ></UniDataSelect>
          <view class="model-bottom-btn">
            <button
              class="left bottom-btn-cancel"
              type=""
              @click="handleCancel()"
              :loading="false"
            >
              取消
            </button>
            <button
              class="right bottom-btn-confirm"
              type="primary"
              @click="handleConfirm()"
              :loading="false"
            >
              确认
            </button>
          </view>
        </view>
      </view>

      <!-- 底栏操作按钮 -->
      <view class="bottom-btn">
        <button
          class="left"
          type="primary"
          @click="createNew()"
          :loading="false"
        >
          创建新的盘点任务
        </button>
        <button
          class="right"
          type="primary"
          @click="selectOld()"
          :loading="false"
        >
          选择已存在的盘点任务
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import {
  fbaTakeStockScanNo,
  fbaTakeStockListHistoryJob,
  fbaTakeStockStartJob,
  fbaTakeStockGetExistsOrder,
  fbaTakeStockGetExistsJob,
} from "../../commom/js/api.js";
import UniDataSelect from "@/components/uni-data-select/uni-data-select";

export default {
  components: {
    UniDataSelect,
  },
  data() {
    return {
      focusStatus: true,
      formData: {
        focusStatus: true,
        scanNo: "",
      },
      scanResults: [],
      historyOrders: [],
      responseDataList: [],
      showSelectOptions: false,
      job_id: null,
      currentOrder: {
        // take_stock_order_id: null,
        // take_stock_order_num: null,
        // parcel_qty: 0
      },
      existsJob: [],
      currentJobId: null,
    };
  },
  onLoad(options) {
    // 加载历史单据
    this.fetchHistoryJobs();
    this.fbaTakeStockGetCurrentOrder();
  },
  methods: {
    async doScan() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));
      const params = {
        no: this.formData.scanNo,
      };

      this.scanResults = [];

      fbaTakeStockScanNo(params)
        .then((res) => {
          if (res.code === 200) {
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            console.log("fbaTakeStockScanNo res.data-->", res.data);
            // 处理扫描结果
            if (res.data && res.data.length > 1) {
              this.scanResults = res.data;
            } else if (res.data && res.data.length == 1) {
              // 直接跳转到详情
              this.goToDetail(res.data[0]);
            }
            // 清空输入框
            this.$set(this.formData, "scanNo", "");
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
          } else {
            this.failAudio();
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            this.responseDataList.push(res);
            // 清空输入框
            this.$set(this.formData, "scanNo", "");
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
          }
        })
        .catch((err) => {
          this.failAudio();
          uni.showToast({
            title: `请求失败: ${err}`,
            icon: "none",
          });
          console.error("POST请求失败", err);
          // 清空输入框
          this.$set(this.formData, "scanNo", "");
          this.focusStatus = false;
          // 重新聚焦
          this.$nextTick(() => {
            this.focusStatus = true;
          });
        });
    },
    async goToDetail(item) {
      // console.log(`goToDetail item_is:${JSON.stringify(item)}`)
      this.toDetail(item);
    },
    toDetail(data) {
      const params = JSON.stringify(data);
      uni.navigateTo({
        url: `./detail?data=${encodeURIComponent(params)}`,
      });
    },
    async fetchHistoryJobs() {
      await new Promise((resolve) => setTimeout(resolve, 500));
      fbaTakeStockListHistoryJob()
        .then((res) => {
          if (res.code === 200) {
            this.historyOrders = res.data;
          }
        })
        .catch((err) => {});
    },
    async fbaTakeStockGetCurrentOrder() {
      await new Promise((resolve) => setTimeout(resolve, 500));
      fbaTakeStockGetExistsOrder()
        .then((res) => {
          if (res.code === 200) {
            this.currentOrder = res.data;
            console.log("this.currentOrder-->", this.currentOrder);
          }
        })
        .catch((err) => {});
    },
    getStatusStyle(status) {
      switch (status) {
        case "C":
          return { color: "#4CAF50" }; // 已完成
        case "I":
          return { color: "#FF9800" }; // 进行中
        case "W":
          return { color: "#2196F3" }; // 未开始
        default:
          return { color: "#000" }; // 默认颜色
      }
    },
    itemClick(item) {
      uni.navigateTo({
        url: `./box?id=${item.id}`,
      });
    },
    createNew() {
      const that = this;
      uni.showModal({
        title: this.$t("content.Tips"),
        content: "确定创建新的仓盘任务？",
        success: function (res) {
          if (res.confirm) {
            const params = {
              is_create: true,
              currentOrder: that.currentOrder.take_stock_order_id,
            };
            that.execute(params);
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
    selectOld() {
      const params = {
        currentOrder: this.currentOrder.take_stock_order_id,
      };
      fbaTakeStockGetExistsJob(params)
        .then((res) => {
          if (res.code === 200) {
            this.existsJob = res.data.data.map((item) => ({
              value: item.id,
              text: `${item.job_num}（${item.parcel_qty}件）`,
            }));
            console.log("this.existsJob-->", this.existsJob);
          }
          if (res.data.length === 0) {
            uni.showToast({
              title: "目前没有未完成的仓盘任务",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: res.msg,
            icon: "none",
          });
        });
      this.showSelectOptions = true;
    },
    execute(params) {
      fbaTakeStockStartJob(params)
        .then((res) => {
          if (res.code === 200) {
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            // 处理扫描结果
            const jobData = res.data;
            this.toDetail(jobData);
          } else {
            this.failAudio();
            uni.showToast({
              title: res.msg || res.detail,
              icon: "none",
            });
            this.responseDataList.push(res);
            // 清空输入框
            this.$set(this.formData, "scanNo", "");
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
          }
        })
        .catch((err) => {
          this.failAudio();
          uni.showToast({
            title: `请求失败: ${err}`,
            icon: "none",
          });
          console.error("POST请求失败", err);
          // 清空输入框
          this.$set(this.formData, "scanNo", "");
          this.focusStatus = false;
          // 重新聚焦
          this.$nextTick(() => {
            this.focusStatus = true;
          });
        });
    },
    async handleConfirm() {
      const params = {
        is_create: false,
        job_id: this.job_id,
        currentOrder: this.currentOrder.take_stock_order_id,
      };
      this.execute(params);
      this.showSelectOptions = false;
      console.log("this.ocean_num_id-->", this.ocean_num_id);
      this.refocus();
    },
    handleCancel() {
      this.showSelectOptions = false;
    },
    refocus() {
      this.focusStatus = false;
      this.$nextTick(() => {
        this.focusStatus = true;
      });
    },
  },
};
</script>

<style>
.scan-results {
  margin-top: 20px;
  text-align: center;
}
.scan-results view {
  padding: 2px;
  border-bottom: 1px solid #ccc;
}
.link-style {
  color: #1aad19; /* 设置为超链接颜色，这里使用绿色作为示例 */
  text-decoration: underline; /* 添加下划线 */
  cursor: pointer; /* 鼠标悬停时显示指针形状 */
}
.title-style {
  text-align: center; /* 文本居中 */
  font-size: 12px; /* 设置字体大小 */
  color: #666; /* 设置字体颜色 */
  padding: 2px 0; /* 上下添加一些内边距 */
  text-decoration: none !important;
}
.list-container {
  padding: 20rpx;
  text-align: center;
}

.list-item {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee; /* 列表项分隔线 */
}

.list-item text {
  font-size: 15rpx; /* 文字大小 */
  line-height: 2rpx; /* 行高 */
  color: #333; /* 文字颜色 */
}

/* 状态颜色 */
.status-c {
  color: #4caf50; /* 已完成 */
}
.status-i {
  color: #ff9800; /* 进行中 */
}
.status-w {
  color: #2196f3; /* 未开始 */
}
.list-item text {
  font-size: 28rpx; /* 文字大小 */
  line-height: 5rpx; /* 行高 */
}

/* 对话框组件样式 */
.form-inline .form-control {
  display: inline-block;
  width: auto;
  vertical-align: middle;
}
.form-control {
  min-width: 100px;
  display: block;
  width: 100%;
  height: 50px;
  padding: 6px 12px;
  font-size: 20px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.dialogModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-content {
  position: relative;
  background: #fff;
  padding: 20px;
  border-radius: 5px;
  width: 500px;
  height: 300px;
}
.model-bottom-btn {
  position: absolute;
  bottom: 20px;
  width: 500px;
  height: 46px;
}
.bottom-btn-cancel,
.bottom-btn-confirm {
  position: absolute;
  display: inline-block;
  width: 50%;
}
.right {
  right: 0;
}
.left {
  left: 0;
}
</style>
