<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
      <uni-nav-bar
        :shadow="false"
        :title="$t('content.FbaTakeStockDetail')"
        @clickLeft="backToTaskStock()"
      >
        <view slot="left">
          <i class="iconfont iconfanhui1 icon-left" />
        </view>
        <view slot="right">
          <text>{{ $username }}</text>
        </view>
      </uni-nav-bar>
    </view>

    <!-- 操作栏 -->
    <view class="operation">
      <view class="scanner">
        <text style="font-size: 200%">开始盘点：</text>
        <input
          class="scanner-input uni-input"
          ref="parcel_num"
          cursor-spacing="10"
          type="text"
          :placeholder="$t('content.ScanExpressOrderNo')"
          :focus="focusStatus"
          @confirm="handleInput1()"
          v-model="formData.scanNo"
        />
        <text style="font-size: 200%">盘点件数：</text>
        <input
          class="scanner-input uni-input"
          ref="parcel_qty"
          cursor-spacing="10"
          type="number"
          min="0"
          placeholder="请输入盘点件数"
          :focus="pacel_qty_focus_status"
          @confirm="doScan()"
          v-model.number="formData.parcel_qty"
        />
        <view class="base-margin-top">
          <text class="base-h6 base-secondary-text">盘点详情</text>
        </view>
        <view style="height: 28rpx"></view>
        <view class="detail-view">
          <view class="detail-item">
            <text class="detail-label">盘点作业单：</text>
            <text class="detail-value">{{ data.job_num }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">订单号：</text>
            <text class="detail-value">{{ data.order_num }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">箱号：</text>
            <text class="detail-value">{{ data.parcel_num }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">客户编码：</text>
            <text class="detail-value">{{ data.customer_short_name }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">订单盘点件数/订单件数：</text>
            <text class="detail-value"
              >{{ data.current_order_ensure_qty }}/{{
                data.current_order_total_qty
              }}</text
            >
          </view>
          <view class="detail-item">
            <text class="detail-label">总盘点件数：</text>
            <text class="detail-value">{{ data.job_total_ensure_qty }}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="fotter">
      <button class="floating-button" @click="finishOrder">完结盘点任务</button>
    </view>
  </view>
</template>

<script>
import {
  fbaTakeStockAddDetail,
  fbaTakeStockListHistoryJob,
  fbaTakeStockfinishJob,
} from "../../commom/js/api.js";

export default {
  data() {
    return {
      focusStatus: true,
      pacel_qty_focus_status: false,
      formData: {
        scanNo: "",
        parcel_qty: null,
      },
      scanResults: [],
      data: {},
    };
  },
  onLoad(options) {
    const params = options.data;
    if (params) {
      // 将字符串转换回对象
      this.data = JSON.parse(decodeURIComponent(params));
    }
    console.log(`options:${JSON.stringify(this.data)}`);
    this.focusStatus = false;
    // 重新聚焦
    this.$nextTick(() => {
      this.focusStatus = true;
    });
  },
  methods: {
    async handleInput1() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));
      this.pacel_qty_focus_status = false;
      this.$nextTick(() => {
        this.pacel_qty_focus_status = true;
      });
    },
    backToTaskStock() {
      uni.navigateTo({
        url: `./index`,
      });
    },
    async doScan() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));
      if (
        !this.formData.scanNo ||
        this.formData.parcel_qty == null ||
        this.formData.parcel_qty === 0
      ) {
        uni.showToast({
          title: "请输入正确的盘点箱号/数量",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      const params = {
        job_id: this.data.job_id,
        parcel_num: this.formData.scanNo,
        parcel_qty: this.formData.parcel_qty,
      };

      console.log("param-----------------------0", params);
      this.submit(params);
    },

    submit(params) {
      fbaTakeStockAddDetail(params)
        .then((res) => {
          if (res.code === 200) {
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            // 处理扫描结果
            if (res.data) {
              // 更新页面 箱号 + 数量
              console.log(`fbaTakeStockAddDetail:${res.data}`);
              this.data["parcel_num"] = res.data["parcel_num"];
              this.data["order_num"] = res.data["order_num"];
              this.data["customer_short_name"] =
                res.data["customer_short_name"];
              this.data["job_total_ensure_qty"] =
                res.data["job_total_ensure_qty"];
              this.data["current_order_ensure_qty"] =
                res.data["current_order_ensure_qty"];
              this.data["current_order_total_qty"] =
                res.data["current_order_total_qty"];
            }
            // 清空输入框
            this.$set(this.formData, "scanNo", "");
            this.$set(this.formData, "parcel_qty", null);
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
          } else {
            this.failAudio();
            uni.showToast({
              title: res.msg || res.detail,
              icon: "none",
              duration: 2000,
            });
            //this.responseDataList.push(res);
            // 清空输入框
            this.$set(this.formData, "scanNo", "");
            this.$set(this.formData, "parcel_qty", null);
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
          }
        })
        .catch((err) => {
          this.failAudio();
          uni.showToast({
            title: `请求失败: ${err}`,
            icon: "none",
            duration: 2000,
          });
          console.error("POST请求失败", err);
          // 清空输入框
          this.$set(this.formData, "scanNo", "");
          this.$set(this.formData, "parcel_qty", null);
          this.focusStatus = false;
          // 重新聚焦
          this.$nextTick(() => {
            this.focusStatus = true;
          });
        });
    },
    async finishOrder() {
      await new Promise((resolve) => setTimeout(resolve, 500));
      const params = {
        job_id: this.data.job_id,
      };
      uni.showModal({
        title: "盘点单完结", // 弹窗标题
        content: "请确认是否操作完结?", // 弹窗内容
        success: (res) => {
          if (res.confirm) {
            this.doFinishOrder(params);
          } else if (res.cancel) {
            // 用户点击了取消按钮
            console.log("用户点击了取消按钮");
          }
        },
      });
    },
    doFinishOrder(params) {
      fbaTakeStockfinishJob(params)
        .then((res) => {
          if (res.code === 200) {
            uni.showToast({
              title: res.msg, // 提示的内容
              icon: "success", // 图标类型
              duration: 2000, // 提示框显示的时长为2秒
            });

            // 清空输入框
            this.$set(this.formData, "scanNo", "");
            this.$set(this.formData, "parcel_qty", null);

            // 延迟2秒跳转到扫描首页
            setTimeout(function () {
              uni.navigateTo({
                url: "./index",
              });
            }, 2000); // 2000毫秒后执行跳转
          } else {
            this.failAudio();
            uni.showToast({
              title: res.msg || res.detail,
              icon: "none",
              duration: 2000,
            });
            //this.responseDataList.push(res);
            // 清空输入框
            this.$set(this.formData, "scanNo", "");
            this.$set(this.formData, "parcel_qty", null);
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
          }
        })
        .catch((err) => {
          this.failAudio();
          uni.showToast({
            title: `请求失败: ${err}`,
            icon: "none",
            duration: 2000,
          });
          console.error("POST请求失败", err);
          // 清空输入框
          this.$set(this.formData, "scanNo", "");
          this.$set(this.formData, "parcel_qty", null);
          this.focusStatus = false;
          // 重新聚焦
          this.$nextTick(() => {
            this.focusStatus = true;
          });
        });
    },
  },
};
</script>

<style>
.scan-results {
  margin-top: 20px;
}
.scan-results view {
  padding: 10px;
  border-bottom: 1px solid #ccc;
}
.base-margin-top {
  margin-top: 10rpx;
}
.base-padding-x {
  padding: 0 25rpx;
}
.base-h6 {
  font-size: 25rpx;
}
.base-secondary-text {
   {
    color: rgba(68, 68, 68, 1);
  }
}
/* 盘点详情视图样式 */
.detail-view {
  display: flex;
  flex-direction: column;
  padding: 10rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  margin-top: 2rpx;
}

/* 盘点详情项样式 */
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1px solid #eaeaea;
}

/* 最后一个详情项无底部边框 */
.detail-item:last-child {
  border-bottom: none;
}

/* 详情标签样式 */
.detail-label {
  color: #666;
  font-size: 28rpx;
}

/* 详情值样式 */
.detail-value {
  color: #000;
  font-size: 30rpx;
  font-weight: bold;
}
/* 浮动底部按钮样式 */
.floating-button {
  position: fixed; /* 固定定位 */
  bottom: 20rpx; /* 距离底部20rpx */
  left: 50%; /* 距离左侧50%，实现水平居中 */
  transform: translateX(-50%); /* 向左移动50%，实现精确居中 */
  background-color: #55aaff; /* 按钮背景颜色 */
  color: white; /* 按钮文字颜色 */
  padding: 2rpx; /* 按钮内边距 */
  border-radius: 10rpx; /* 按钮边框圆角，非圆形 */
  width: 90%; /* 按钮宽度 */
  height: 80rpx; /* 按钮高度 */
  font-size: 30rpx; /* 文字大小 */
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2); /* 按钮阴影 */
  text-align: center; /* 文字居中 */
  line-height: 80rpx; /* 行高与按钮高度一致，实现文字垂直居中 */
}
</style>
