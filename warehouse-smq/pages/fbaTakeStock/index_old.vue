<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
		  <uni-nav-bar :shadow='false' :title="$t('content.FbaTakeStock')" @clickLeft="backToIndex()">
		    <view slot="left">
		      <i class="iconfont iconfanhui1 icon-left"/>
		    </view>
		    <view slot="right"> <text>{{$username}}</text> </view>
		  </uni-nav-bar>
		</view>
		
		<!-- 操作栏 -->
		<view class="operation">
			<view class="scanner">
				<text style="font-size: 200%;">扫描箱号：</text>
				<input class="scanner-input uni-input" 
						ref='parcel_num' 
						cursor-spacing="10"
						type="text"
						:placeholder="$t('content.ScanExpressOrderNo')"
						:focus="focusStatus"
						@confirm="doScan()"
						v-model="formData.scanNo"
				/>
			</view>
			<!-- 扫描结果列表 -->
			<view class="scan-results" v-if="scanResults.length > 1">
				<view class="title-style">请选择一个单号</view>
			  <view v-for="(item, index) in scanResults" :key="index" @click="goToDetail(item)">
				<text class="link-style">{{ item.order_num }} - {{ item.customer_name }}</text>
			  </view>
			</view>
			
			  <view class="list-container">
				  <text class="title-style">历史盘点任务</text>
				<view class="list-item" v-for="(item, index) in historyOrders" :key="index">
				  <text :style="getStatusStyle(item.status)" @click="itemClick(item)">
					{{ item.job_num }} - {{ item.order_num }} （{{ item.parcel_qty }}件）
				  </text>
				</view>
			  </view>
              
              <!-- 底栏操作按钮 -->
              <view class="bottom-btn">
                <button class="left" type="primary" @click="createNew()" :loading="false">创建新的仓盘单</button>
                <button class="right" type="primary" @click="selectOld()" :loading="false">选择已存在的仓盘单</button>
              </view>
              
		</view>
		
	</view>
</template>

<script>
	import {
		fbaTakeStockScanNo,
		fbaTakeStockListHistoryJob,
		fbaTakeStockStartJob
	} from '../../commom/js/api.js'
	
	export default {
		data() {
			return {
				focusStatus: true,
				formData: {
					focusStatus: true,
					scanNo: "",
				},
				scanResults: [],
				historyOrders: [],
                responseDataList: []
			}
		},
		onLoad(options) {
			
			// 加载历史单据
			this.fetchHistoryJobs()
		},
		methods: {
			async doScan() {
				// 等待500毫秒，可以根据实际情况调整时间
				await new Promise(resolve => setTimeout(resolve, 500));
				const params = {
					no: this.formData.scanNo
				}
				
				this.scanResults = []
				
				fbaTakeStockScanNo(params).then(res => {
					if (res.code === 200) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						// 处理扫描结果
						if (res.data && res.data.length > 1) {
							this.scanResults = res.data;
						} else if (res.data && res.data.length == 1) {
							// 直接跳转到详情
							this.goToDetail(res.data[0])
						}
						// 清空输入框
						this.$set(this.formData, 'scanNo', '')
						this.focusStatus=false
						// 重新聚焦
						this.$nextTick(() => {
							this.focusStatus=true
						});
					} else {
				        this.failAudio()
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						this.responseDataList.push(res);
						// 清空输入框
						this.$set(this.formData, 'scanNo', '')
						this.focusStatus=false
						// 重新聚焦
						this.$nextTick(() => {
							this.focusStatus=true
						});
					}
				}).catch(err => {
				    this.failAudio()
				    uni.showToast({
				        title: `请求失败: ${err}`,
				        icon: 'none'
				    })
					console.error('POST请求失败', err);
					// 清空输入框
					this.$set(this.formData, 'scanNo', '')
					this.focusStatus=false
					// 重新聚焦
					this.$nextTick(() => {
						this.focusStatus=true
					});
				})
			},
			async goToDetail(item) {
				console.log(`item_is:${JSON.stringify(item)}`)
				// 开始作业
				fbaTakeStockStartJob(item).then(res => {
					if (res.code === 200) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						// 处理扫描结果
						const jobData = res.data
						this.toDetail(jobData)
					} else {
				        this.failAudio()
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						this.responseDataList.push(res);
						// 清空输入框
						this.$set(this.formData, 'scanNo', '')
						this.focusStatus=false
						// 重新聚焦
						this.$nextTick(() => {
							this.focusStatus=true
						});
					}
				}).catch(err => {
				    this.failAudio()
				    uni.showToast({
				        title: `请求失败: ${err}`,
				        icon: 'none'
				    })
					console.error('POST请求失败', err);
					// 清空输入框
					this.$set(this.formData, 'scanNo', '')
					this.focusStatus=false
					// 重新聚焦
					this.$nextTick(() => {
						this.focusStatus=true
					});
				})
				
				

			},
			toDetail(data) {
				  const params = JSON.stringify(data);
				  uni.navigateTo({
				    url: `./detail?data=${encodeURIComponent(params)}`
				  });
			},
			async fetchHistoryJobs() {
				await new Promise(resolve => setTimeout(resolve, 500));
				fbaTakeStockListHistoryJob().then(res => {
					if (res.code === 200) {
						this.historyOrders = res.data
					}
				}).catch(err => {

				})
			},
			getStatusStyle(status) {
			  switch (status) {
				case 'C':
				  return { color: '#4CAF50' }; // 已完成
				case 'I':
				  return { color: '#FF9800' }; // 进行中
				case 'W':
				  return { color: '#2196F3' }; // 未开始
				default:
				  return { color: '#000' }; // 默认颜色
			  }
			},
			itemClick(item) {
			  uni.navigateTo({
			    url: `./box?id=${item.id}`
			  });
			}
		}
	}
</script>

<style>
.scan-results {
  margin-top: 20px;
  text-align: center;
  
}
.scan-results view {
  padding: 2px;
  border-bottom: 1px solid #ccc;
}
.link-style {
  color: #1aad19; /* 设置为超链接颜色，这里使用绿色作为示例 */
  text-decoration: underline; /* 添加下划线 */
  cursor: pointer; /* 鼠标悬停时显示指针形状 */
}
.title-style {
  text-align: center; /* 文本居中 */
  font-size: 12px; /* 设置字体大小 */
  color: #666; /* 设置字体颜色 */
  padding: 2px 0; /* 上下添加一些内边距 */
  text-decoration: none !important;
}
.list-container {
  padding: 20rpx;
  text-align: center;
}

.list-item {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee; /* 列表项分隔线 */
}

.list-item text {
  font-size: 15rpx; /* 文字大小 */
  line-height: 2rpx; /* 行高 */
  color: #333; /* 文字颜色 */
}

/* 状态颜色 */
.status-c {
  color: #4CAF50; /* 已完成 */
}
.status-i {
  color: #FF9800; /* 进行中 */
}
.status-w {
  color: #2196F3; /* 未开始 */
}
.list-item text {
  font-size: 28rpx; /* 文字大小 */
  line-height: 5rpx; /* 行高 */
}
</style>
