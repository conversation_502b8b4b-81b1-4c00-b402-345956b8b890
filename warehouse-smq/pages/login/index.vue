<template>
	<view class="login">
		<view class="login-box">
			<view class="title">仓库扫描</view>
			<view class="login-input">
				<text class="lebel">{{$t('content.Account')}}:</text><input style="margin-left: 10px;" v-model="username" @confirm="login()" class="uni-input" focus @fu :placeholder="$t('content.EnterAccount')" />
			</view>
			<view class="login-input">
				<text class="lebel">{{$t('content.Password')}}:</text><input style="margin-left: 10px;" v-model="password" @confirm="login()" class="uni-input" :password="true"
				 :placeholder="$t('content.EnterPassword')" />
			</view>
			<view class="submit">
				<button type="primary" :loading="submiting" :disabled="submiting" size="mini" @click="login()">{{$t('content.Login')}}</button>
			</view>
			<view class="lngview">
			          <picker @change="lngType" :range="lngTypeArray">
			              <label>{{$t('content.Language')}}：</label>
			              <label class="">{{ lngArrayType }}</label>
			          </picker>
			  </view>
			<view class="setting">
				<button @click="cancelShow=true" size="mini" type="default"><i class="iconfont iconziyuan"></i>{{$t('content.EnvironmentSettings')}}</button>
			</view>
		</view>
		<hFormAlert v-if="cancelShow" :title="base.BASE_URL+ '(' + $t('content.Current') + ')'" name="host" :placeholder="$t('content.EnterRequestedUrl')" @confirm="confirm"
		 @cancel="cancel"></hFormAlert>
	</view>
</template>

<script>
	import hFormAlert from '@/components/h-form-alert/h-form-alert.vue';
	import base from '../../commom/js/config.js'
	import {
		login,
		getInfo
	} from '../../commom/js/api.js'
	export default {
		data() {
			return {
				lngTypeArray:['Chinese','English'],
				lngTypeIndex:0,
				lngArrayType:'Chinese',
				username: '',
				password: '',
				submiting: false,
				firstEnter: true,
				environment: '',
				cancelShow: false,
				base: {}
			}
		},
		components: {
			hFormAlert
		},
		onShow() {
			this.checkLogin()
		},
		onLoad: function(option) { //option为object类型，会序列化上个页面传递的参数
			this.checkLogin()
		},
		created() {
			console.log('on create--------------');
			this.environment = process.env.NODE_ENV
			console.log('base-->', base);
			this.base = base
		},
		methods: {
			lngType(e){
				this.lngTypeIndex = e.target.value;
				this.lngArrayType=this.lngTypeArray[this.lngTypeIndex]
				var command;
				if(this.lngTypeIndex == 0){
					command = 'zh'
				}
				if(this.lngTypeIndex == 1){
					command = 'en'
				}
				uni.setStorageSync('lng', command);
				this.$i18n.locale = command;
			},
			// 修改host确认
			confirm(data) {
				let host = data.host.trim()
				if (!host) {
					return
				}
				if (!host.startsWith('http')) {
					host = 'http://' + host
				}
				if (!host.endsWith('/')) {
					host += '/'
				}
				console.log('host-->', host);
				this.base.BASE_URL = host
				uni.setStorageSync('BASE_URL', host);
				this.cancelShow = false
			},
			// 取消修改
			cancel() {
				this.cancelShow = false
			},
			// 检查是否已经登陆过
			checkLogin() {
				if (getApp().globalData.token) {
					this.getInfo()
				} else if (uni.getStorageSync('token')) {
					getApp().globalData.token = uni.getStorageSync('token')
					this.getInfo()
				} else {
					return
				}
			},
			// 检验账号密码
			checkForm() {
				return new Promise((resolve, reject) => {
					if (!this.username || !this.password) {
						resolve(false)
					} else {
						resolve(true)
					}
				})
			},
			// 登录
			async login() {
				let n = await this.checkForm()
				if (!n) {
					uni.showToast({
						title: this.$t('content.EnterAccountPassword'),
						mask: true,
						duration: 2000,
						icon: 'none',
						position: 'top'
					});
					return
				}
				const loginParams = {
					username: this.username,
					password: this.password
				}
				this.submiting = true
				login(loginParams).then(res => {
					uni.setStorageSync('token', res.token);
					getApp().globalData.token = res.token
					this.getInfo()
				}).catch(err => {
					console.log('错误');
					this.submiting = false
				})
			},
			// 获取个人信息
			getInfo() {
				getInfo({}).then(res => {
					if (res.id) {
						// 跳转首页
						getApp().globalData.userInfo = res
						console.log(res)
						uni.setStorageSync('userInfo', JSON.stringify(res));
						this.$setUsername(res.username)
						uni.navigateTo({
							url: '../index/index'
						});
					} else {
						uni.showToast({
							title: res.message,
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
					}
					this.submiting = false
				}).catch(err => {
					this.submiting = false
				})
			}
		}
	}
</script>

<style lang="scss">
	.lngview {
		text-align: center;
		border: 1px solid black;
		border-radius: 5px;
		height: 37px;
		line-height: 37px;
		color: white;
		background-color: #007aff;
		font-size: 12px;
	}
	.login {
		width: 100vw;
		height: 100vh;
		background-color: #2A2A36;

		.login-box {
			width: 80vw;
			height: 650rpx;
			background-color: #fff;
			position: fixed;
			padding: 0 30rpx;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -70%);
			border-radius: 5px;

			.title {
				text-align: center;
				padding: 30rpx 0 60rpx 0;
				font-size: 40rpx;
				font-weight: bold;
			}

			.login-input {
				display: flex;

				.lebel {
					font-size: 30rpx;
					line-height: 65rpx;
					width: 120rpx;
					height: 65rpx;
				}

				input {
					border-bottom: 1px solid #ccc;
					border-radius: 2px;
					width: 500rpx;
					height: 65rpx;
					padding: 0 16rpx;
					font-size: 30rpx;
				}

				&:nth-last-child(2) {
					margin-top: 40rpx;
				}
			}

			.submit {
				text-align: center;
				margin-top: 40rpx;

				button {
					width: 100%;
					padding: 10rpx 0;
				}
			}

			.setting {
				margin-top: 10rpx;
				text-align: center;
				font-size: 30rpx;
			}
		}
	}
</style>
