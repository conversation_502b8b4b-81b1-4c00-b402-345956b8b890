<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.Discharge')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left"/>
				</view>
				<view slot="right">
					<text>{{$username}}</text>
				</view>
			</uni-nav-bar>
		</view>

		<!-- 操作栏 -->
		<view class="operation">
			<text style="font-size: 200%;">扫描单号：</text>
			<view class="scanner">
				<input class="scanner-input uni-input"
					cursor-spacing="10"
					ref='parcel_num'
					type="text"
					:placeholder="$t('content.PScanInBoundOrderNo')"
					:focus="master_order_num_focus"
					@confirm="handleInput1()"
					v-model="formData.parcel_num"
				/>
				
				<!--
				<text>主单号：{{ formData.order_num }}</text>
				<text v-if="formData.tracking_num">快递号：{{ formData.tracking_num }}</text>
				-->
				<text style="font-size: 200%;">扫描快递单号：</text>
				<input class="scanner-input uni-input"
					cursor-spacing="10"
					ref='express_num'
					type="text"
					:placeholder="$t('content.PScanExpressOrderNo')"
					:focus="express_order_num_focus"
					@confirm="handleInput2()"
					v-model="formData.express_num"
				/>
			</view>
			<view>
				<uni-fab
					:pattern="pattern"
					:content="content"
					:horizontal="horizontal"
					:vertical="vertical"
					:direction="direction"
					@trigger="trigger"
					@fabClick="fabClick">
				</uni-fab>
			</view>
			<view class="info-container" v-if="master_order_info.master_order_num" v-model="master_order_info">
				<view class="info-row">
					<view class="info-column">
						<view class="info-item">
							<text>主单号：</text>
							<text>{{ master_order_info.master_order_num }}</text>
						</view>
						<view class="info-item">
							<text>预计出仓数：</text>
							<text>{{ master_order_info.pre_carton }}</text>
						</view>
						<view class="info-item">
							<text>入仓数量：</text>
							<text>{{ master_order_info.in_warehouse }}</text>
						</view>
					</view>
					<view class="info-column">
						<view class="info-item" style="color: red;" @click="to_not_scanned">
						  <text>未扫描：</text>
						  <text>{{ master_order_info.not_scanned }}</text>
						</view>
						<view class="info-item">
							<text>客户姓名：</text>
							<text>{{ master_order_info.client_name }}</text>
						</view>
						<view class="info-item">
							<text>出仓数量：</text>
							<text>{{ master_order_info.out_warehouse }}</text>
						</view>
					</view>
				</view>
			</view>
			<view v-else class="no-data">
				<!-- 如果 master_order_info 不存在，则显示其他内容或者留空 -->
				<text>暂无数据</text>
			</view>
			
	<view>
	  <view v-for="(data, index) in this.responseDataList.slice().reverse()" :key="index">
	    <view class="info-container">
	      <view class="info-row">
	        <view class="info-column">
	          <view class="info-item" :style="{ 'font-size': index === 0 ? '30px' : '20px' }">
	            <text>包裹号：</text>
	            <text>{{ data }}</text>
	          </view>
	        </view>
	      </view>
	    </view>
	  </view>
	</view>
<!--    <view>
      <view v-for="(data, index) in this.responseDataList" :key="index">
        <view class="info-container">
          <view class="info-row">
            <view class="info-column">
              <view class="info-item">
                <text>包裹号：</text>
                <text>{{ data }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view> -->

			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Finish')}}</button>
				<button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
			</view>

		</view>
	</view>
</template>

<script>
import { getStep, nextStep, big_parcel_out, pda_save_img } from '../../commom/js/api.js'
export default {
	data() {
		return {
			master_order_num_focus: false,
			express_order_num_focus: true,
			master_order_info: {
				order_num: '',
				parcel_num: '',
				tracking_num: ''
			}, // 主单信息
			responseDataList: [], // 保存返回的数据列表
			subOrderNumber: '',
			orderInfo: {
				masterOrder: '',
				errorSubOrders: 0,
				loadedTickets: 0,
				deliveredTickets: 0,
				loadedItems: 0,
				deliveredItems: 0,
			},
			pattern: {
				color: '',
				selectedColor: '',
				backgroundColor: '',
				buttonColor: ''
			},
			horizontal: 'left',
			vertical: 'bottom',
			direction: 'vertical',
			content: [
				{
					iconPath: '/static/search.png',
					selectedIconPath: '/static/search.png',
					text: this.$t('content.ScanCodeQuery'),
					active: true
				},
			],
			formData: {
				order_num: '',
				parcel_num: '',
				tracking_num: '',
				express_num: ''
			},
			option: {},
			loading: false
		}
	},
	onLoad: function(options) {
		const order_num = options.order_num ? decodeURIComponent(options.order_num) : '';
		const parcel_num = options.parcel_num ? decodeURIComponent(options.parcel_num) : '';
		const tracking_num = options.tracking_num ? decodeURIComponent(options.tracking_num) : '';
		
		console.log(order_num, parcel_num, tracking_num);
		
		this.formData.order_num = order_num;
		this.formData.parcel_num = parcel_num;
		this.formData.tracking_num = tracking_num;
	},
	created() {
		if (this.formData.order_num == ""){
			this.express_order_num_focus=false
			this.$nextTick(() => {
				this.master_order_num_focus=true
			});
		}
	},
	methods: {
		async handleInput1() {
			// 清空输入框
			// this.$set(this.formData, 'order_num', '')
			// this.$set(this.formData, 'parcel_num', '')
			// this.$set(this.formData, 'express_num', '')
			// this.$set(this.formData, 'tracking_num', '')
			this.master_order_num_focus=false
			this.express_order_num_focus=false
			// 重新聚焦
			this.$nextTick(() => {
				this.express_order_num_focus=true
			});
		},
		async handleInput2() {
			const params = {
				parcel_num: this.formData.parcel_num,
				express_num: this.formData.express_num
			}
			// 这里随便写个接口测试下
			big_parcel_out(params).then(res => {
				if (res.code === 200) {
					this.master_order_info = res.data.master_order;
					this.responseDataList.push(res.data.sum_order_num);
					// 清空输入框
					this.$set(this.formData, 'order_num', '')
					this.$set(this.formData, 'parcel_num', '')
					this.$set(this.formData, 'express_num', '')
					this.$set(this.formData, 'tracking_num', '')
					this.master_order_num_focus=false
					// 重新聚焦
					this.$nextTick(() => {
						this.master_order_num_focus=true
					});
				} else if (res.code === 201){
					// if (res.tracking_num) {
						
					// }
					console.log(res.msg)
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
					// 清空输入框
					this.$set(this.formData, 'express_num', '')
					this.express_order_num_focus=false
					// 重新聚焦
					this.$nextTick(() => {
						this.express_order_num_focus=true
					});
				} else {
					console.log(res.msg)
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
					// 清空输入框
					this.$set(this.formData, 'express_num', '')
					this.express_order_num_focus=false
					// 重新聚焦
					this.$nextTick(() => {
						this.express_order_num_focus=true
					});
				}
			}).catch(err => {
				console.error('请求失败', err);
				// 清空输入框
				// this.$set(this.formData, 'master_order_num', '')
				// this.$set(this.formData, 'express_order_num', '')
				// this.master_order_num_focus=false
				// 重新聚焦
				this.$nextTick(() => {
					this.master_order_num_focus=true
				});
			})
		},
		// 页面跳转
		// 跳转 查看未处理包裹
		to_not_scanned() {
			const not_scan_params = {
				order_num: this.master_order_info.master_order_num,
			}
			uni.navigateTo({
				url: `../nottransfer/index?order_num=${this.master_order_info.master_order_num}`,
				query: not_scan_params
			})
		},
		getStep(type){
			uni.showLoading({
				title: this.$t('content.Loading'),
				mask: true
			});
			getStep({type}).then(res=>{
				console.log(res);
				if(res.code===200){
					getApp().globalData.request = {...getApp().globalData.request,...res.data.params}
					this.getStepRouter(res.data.step)
				}
				uni.hideLoading();
			}).catch(err=>{uni.hideLoading();})
		},
		// 监听回车
		handelEnter(str) {
			if (str === 'post') {
				this.getOrder()
			} else if (str) {
				this.$nextTick(() => {
					this.$refs[str].focus=true;
				});
			}
		},
		// 核重
		getOrder() {
			const weight = localStorage.getItem('fba_update_weight')
			const size = localStorage.getItem('fba_update_size')

			// 包裹号
			if (!this.formData.parcel_num) {
				this.$nextTick(function() {
				this.$refs.audio_error.play()
			})
				this.$notify.error({
				title: 'error',
				message: '请确保填写了包裹号'
			})
			  return
			}
	
			his.loading = true
		
			const api = '/api/fbaBigParcels/fba_parcel_size/'
			actionPost({ api, data: this.formData }).then(res => {
				if (res.code === 200) {
					this.$notify.success({
					title: 'success',
					message: res.msg
				})
				this.handelEnter('parcel_num')
				// this.frontOrderNum = Object.assign({}, this.formData).order_num
				this.frontWeight = res.data.weight
				this.diffWeight = res.data.diffWeight
				this.scanResult = res.msg
				this.parcelOrderNums = (this.parcelOrderNums + 1)
				const weightingData = {
					result: 'success',
					time: res.data.time,
					order_num: res.data.order_num,
					parcel_num: res.data.parcel_num,
					product: res.data.product,
					buyer_country_code: res.data.buyer_country_code,
					label_weight: res.data.label_weight,
					label_length: res.data.label_length,
					label_width: res.data.label_width,
					label_height: res.data.label_height,
					actual_weight: res.data.actual_weight,
					actual_length: res.data.actual_length,
					actual_width: res.data.actual_width,
					actual_height: res.data.actual_height,
					country_code: res.data.buyer_country_code
				}
				this.parcelWeighingList.push(weightingData)
				this.parcelWeighingListReverse = [...this.parcelWeighingList].reverse()
				this.formData.parcel_num = ''
				this.formData.label_weight = weight ? localStorage.getItem('fba_update_weight') : ''
				this.formData.label_length = size ? size.split(',')[0] : ''
				this.formData.label_width = size ? size.split(',')[1] : ''
				this.formData.label_height = size ? size.split(',')[2] : ''
				this.pre_carton = res.data.pre_carton
				this.frontOrderNum = res.data.parcel_num
				this.formData.mojia_img = res.data.mojia_img
				this.$nextTick(function() {
					this.$refs.audio_success.play()
					this.success_count += 1
				})
			} else {
				this.$nextTick(function() {
				  this.$refs.audio_error.play()
				  this.fail_count += 1
				})
				this.$notify.error({
				  title: 'error',
				  message: res.msg || res.detail
				})
			}
			  this.loading = false
			}).catch(() => { this.loading = false })
		  },
		// 校验
		validateForm() {
			return new Promise((resolve, reject) => {
				if (!this.formData.master_order_num) {
					this.failAudio()
					uni.showToast({
						title: this.$t('content.PScanInventoryOrderNo'),
						icon: 'none',
						mask: true,
						duration: 2000
					});
					resolve(false)
				}
				resolve(true)
			})
		},
		onInputConfirm() {
			// 在这里处理扫描子单号的逻辑，更新 orderInfo 数据
			// 为了示例，这里简单地模拟了一些数据
			this.orderInfo = {
				masterOrder: '123456',
				errorSubOrders: 5,
				loadedTickets: 10,
				deliveredTickets: 8,
				loadedItems: 20,
				deliveredItems: 15,
			};
		}
	}
}
</script>

<style scoped>
.container {
  padding: 20px;
}

.input-container {
  margin-bottom: 20px;
}

.input-box {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-container {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-row {
  display: flex;
  justify-content: space-between;
}

.info-column {
  display: flex;
  flex-direction: column;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh; /* 100%视窗高度，根据实际需要调整 */
}
</style>
