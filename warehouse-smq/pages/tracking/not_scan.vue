<template>
  <view>
  <!-- 顶部栏 -->
  <view class="step">
    <uni-nav-bar :shadow='false' :title="$t('content.Discharge')" @clickLeft="backToIndex()">
      <view slot="left">
        <i class="iconfont iconfanhui1 icon-left"/>
      </view>
      <view slot="right"> <text>{{$username}}</text> </view>
    </uni-nav-bar>
  </view>

  <!-- 操作栏 -->
  <view class="operation">

    <view class="scanner">
      <input class="scanner-input uni-input" cursor-spacing="10" type="text" :placeholder="$t('content.PScanInBoundOrderNo')" focus @confirm="nextStep()"
             v-model="formData.master_order_num" />
    </view>
    <view>
      <uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
               @trigger="trigger" @fabClick="fabClick"></uni-fab>
    </view>
    <view class="table-container" v-if="responseDataList.length>0">
      <view class="table-header">
        <text style="text-align: center;">包裹号</text>
      </view>
      <view v-for="item in responseDataList" :key="item.parcel_num" class="table-row">
        <text class="table-item">{{ item.parcel_num }}</text>
      </view>
    </view>
    
    <view v-else class="no-data">
      <!-- 如果 master_order_info 不存在，则显示其他内容或者留空 -->
      <text>暂无数据</text>
    </view>

    <!-- 底栏操作按钮 -->
    <view class="bottom-btn">
      <button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Finish')}}</button>
      <button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
    </view>

  </view>
  </view>
</template>

<script>
import {
  getStep, nextStep, get_not_scanned
} from '../../commom/js/api.js'
	export default {
		data() {
			return {
        master_order_info: {
          master_order_num: ''
        }, // 主单信息
        responseDataList: [], // 保存返回的数据列表
        subOrderNumber: '',
        orderInfo: {
          masterOrder: '',
          errorSubOrders: 0,
          loadedTickets: 0,
          deliveredTickets: 0,
          loadedItems: 0,
          deliveredItems: 0,
        },
        pattern: {
          color: '',
          selectedColor: '',
          backgroundColor: '',
          buttonColor: ''
        },
        horizontal: 'left',
        vertical: 'bottom',
        direction: 'vertical',
        content: [
          {
            iconPath: '/static/search.png',
            selectedIconPath: '/static/search.png',
            text: this.$t('content.ScanCodeQuery'),
            active: true
          },
        ],
        formData: {
          master_order_num: ''
        },
        option: {},
        loading: false
      }
		},
		onLoad: function(option) { //option为object类型，会序列化上个页面传递的参数
			// this.getStep(option.type)
		},
		created() {
		},
		methods: {
      async nextStep() {
		const params = {
			parcel_num: this.formData.master_order_num
		}
		  
		get_not_scanned(params).then(res => {
			console.log(res)
			if (res.code === 200) {
			  console.log('POST请求成功', res);
			  this.master_order_info = res.data.master_order;
			  this.responseDataList.push(res.data.sum_order_num);
			} else {
				console.log(res.msg)
			  uni.showToast({
			    title: res.msg,
			    icon: 'none'
			  })
			}
		}).catch(err => {
			console.error('POST请求失败', err);
		})
      },
			getStep(type){
				uni.showLoading({
					title: this.$t('content.Loading'),
					mask: true
				});
				getStep({type}).then(res=>{
					console.log(res);
					if(res.code===200){
						getApp().globalData.request = {...getApp().globalData.request,...res.data.params}
						this.getStepRouter(res.data.step)
					}
					uni.hideLoading();
				}).catch(err=>{uni.hideLoading();})
			},
      // 监听回车
      handelEnter(str) {
        if (str === 'post') {
          this.getOrder()
        } else if (str) {
          this.$nextTick(() => {
            this.$refs[str].focus()
          })
        }
      },
      // 核重
      getOrder() {
        const weight = localStorage.getItem('fba_update_weight')
        const size = localStorage.getItem('fba_update_size')

        // 包裹号
        if (!this.formData.parcel_num) {
          this.$nextTick(function() {
            this.$refs.audio_error.play()
          })
          this.$notify.error({
            title: 'error',
            message: '请确保填写了包裹号'
          })
          return
        }

        this.loading = true
        const api = '/api/fbaBigParcels/fba_parcel_size/'
        actionPost({ api, data: this.formData }).then(res => {
          if (res.code === 200) {
            this.$notify.success({
              title: 'success',
              message: res.msg
            })
            this.handelEnter('parcel_num')
            // this.frontOrderNum = Object.assign({}, this.formData).order_num
            this.frontWeight = res.data.weight
            this.diffWeight = res.data.diffWeight
            this.scanResult = res.msg
            this.parcelOrderNums = (this.parcelOrderNums + 1)
            const weightingData = {
              result: 'success',
              time: res.data.time,
              order_num: res.data.order_num,
              parcel_num: res.data.parcel_num,
              product: res.data.product,
              buyer_country_code: res.data.buyer_country_code,
              label_weight: res.data.label_weight,
              label_length: res.data.label_length,
              label_width: res.data.label_width,
              label_height: res.data.label_height,
              actual_weight: res.data.actual_weight,
              actual_length: res.data.actual_length,
              actual_width: res.data.actual_width,
              actual_height: res.data.actual_height,
              country_code: res.data.buyer_country_code
            }
            this.parcelWeighingList.push(weightingData)
            this.parcelWeighingListReverse = [...this.parcelWeighingList].reverse()
            this.formData.parcel_num = ''
            this.formData.label_weight = weight ? localStorage.getItem('fba_update_weight') : ''
            this.formData.label_length = size ? size.split(',')[0] : ''
            this.formData.label_width = size ? size.split(',')[1] : ''
            this.formData.label_height = size ? size.split(',')[2] : ''
            this.pre_carton = res.data.pre_carton
            this.frontOrderNum = res.data.parcel_num
            this.formData.mojia_img = res.data.mojia_img
            this.$nextTick(function() {
              this.$refs.audio_success.play()
              this.success_count += 1
            })
          } else {
            this.$nextTick(function() {
              this.$refs.audio_error.play()
              this.fail_count += 1
            })
            this.$notify.error({
              title: 'error',
              message: res.msg || res.detail
            })
          }
          this.loading = false
        }).catch(() => { this.loading = false })
      },
      // 校验
      validateForm() {
        return new Promise((resolve, reject) => {
          if (!this.formData.master_order_num) {
            this.failAudio()
            uni.showToast({
              title: this.$t('content.PScanInventoryOrderNo'),
              icon: 'none',
              mask: true,
              duration: 2000
            });
            resolve(false)
          }
          resolve(true)
        })
      },
      onInputConfirm() {
        // 在这里处理扫描子单号的逻辑，更新 orderInfo 数据
        // 为了示例，这里简单地模拟了一些数据
        this.orderInfo = {
          masterOrder: '123456',
          errorSubOrders: 5,
          loadedTickets: 10,
          deliveredTickets: 8,
          loadedItems: 20,
          deliveredItems: 15,
        };
      }
		}
	}
</script>

<style scoped>
.container {
  padding: 20px;
}

.input-container {
  margin-bottom: 20px;
}

.input-box {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-container {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-row {
  display: flex;
  justify-content: space-between;
}

.info-column {
  display: flex;
  flex-direction: column;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh; /* 100%视窗高度，根据实际需要调整 */
}
</style>
