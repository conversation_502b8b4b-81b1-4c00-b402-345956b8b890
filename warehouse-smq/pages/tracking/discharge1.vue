<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.Discharge')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left" />
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>
		<!-- 操作栏 -->
		<view class="operation">
			<view class="scanner">
				<view class="scanner-label">{{$t('content.ScanInBoundOrderNo')}}：</view>
				<input class="scanner-input uni-input" cursor-spacing="10" type="text" :placeholder="$t('content.PScanInBoundOrderNo')" focus @confirm="nextStep()"
				 v-model="formData.master_order_num" />
			</view>
			<view>
				<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
				 @trigger="trigger" @fabClick="fabClick"></uni-fab>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Finish')}}</button>
				<button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		nextStep,
		finish_task
	} from '../../commom/js/api.js'
	import uniFab from '@/components/uni-fab/uni-fab.vue';
	import { successAudio,failAudio } from '../../audio.js'
	export default {
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [
					{
						iconPath: '/static/search.png',
						selectedIconPath: '/static/search.png',
						text: this.$t('content.ScanCodeQuery'),
						active: true
					},
				],
				formData: {
					master_order_num: ''
				},
				option: {},
				loading: false
			}
		},
		computed: {
			// 获取当前步骤需要提交的表单信息
			requestData() {
				return {
					...getApp().globalData.request,
					step: this.getRoutePath().step,
					...this.formData,
				}
			}
		},
		onLoad: function(option) {
			console.log(option);
			this.option = option
		},
		methods: {
			// 校验
			validateForm() {
				return new Promise((resolve, reject) => {
					if (!this.formData.master_order_num) {
						this.failAudio()
						uni.showToast({
							title: this.$t('content.PScanInBoundOrderNo'),
							icon: 'none',
							mask: true,
							duration: 2000
						});
						resolve(false)
					}
					resolve(true)
				})
			},
			// 返回
			back() {
				const _this = this
				uni.showModal({
					title: this.$t('content.Tips'),
					content: this.$t('content.UnloadingOperationsEnd'),
					success: function(res) {
						if (res.confirm) {
							// 调用接口
							finish_task(_this.filterRequest(_this.requestData)).then(res => {
								console.log(res);
								if (res.code === 200) {
									this.successAudio()
									uni.showToast({
										title: res.msg,
										duration: 2000,
										icon: 'none',
										mask: true,
										position: 'top',
										success: function() {
											setTimeout(() => {
												uni.redirectTo({
													url: '../index/index'
												});
											}, 1900)
										}
									});
								} else {
									this.failAudio()
									uni.showToast({
										title: res.msg || res.message || 'fail request! please check!',
										mask: true,
										duration: 2000,
										icon: 'none',
										position: 'top'
									});
								}
							})

						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 下一步
			async nextStep() {
				if (this.loading) return
				this.loading = true
				uni.showLoading({
					title: this.$t('content.InRequest'),
					mask: true
				});
				let flag = await this.validateForm()

				uni.hideLoading();
				this.loading = false
				if (!flag) return
				nextStep(this.filterRequest(this.requestData)).then(res => {
					if (res.code === 200) {
						this.successAudio()
						getApp().globalData.request = { ...getApp().globalData.request,
							...res.data
						}
						uni.navigateTo({
							url: this.getRoutePath().basicPath + res.data.step
						})
					} else {
						this.failAudio()
						uni.showToast({
							title: res.detail || res.message || 'fail request! please check!',
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
						this.formData.master_order_num = ''
					}
					uni.hideLoading()
				})
			},
			trigger(data) {
				switch (data.index) {
					case 0:
						uni.navigateTo({
							url: '../search/index'
						})
						break;
					default:
						break;
				}			
			},
			fabClick() {},
		}
	}
</script>

<style scoped>
	
</style>
