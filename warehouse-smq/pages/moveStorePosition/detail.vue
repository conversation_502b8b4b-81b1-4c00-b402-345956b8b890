<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
      <uni-nav-bar
        :shadow="false"
        :title="$t('content.MoveStorePositionDetail')"
        @clickLeft="backToTaskStock()"
      >
        <view slot="left">
          <i class="iconfont iconfanhui1 icon-left" />
        </view>
        <view slot="right">
          <text>{{ $username }}</text>
        </view>
      </uni-nav-bar>
    </view>

    <!-- 操作栏 -->
    <view class="operation">
      <view class="scanner">
        <view class="detail-view">
          <view class="detail-item">
            <text class="detail-label">订单号：</text>
            <!-- 后5位字体放大处理 -->
            <text class="detail-value">
              <span v-if="data.master_num && data.master_num.length > 5">
                {{ data.master_num.slice(0, -5) }}
                <span style="font-size: 150%">
                  {{ data.master_num.slice(-5) }}
                </span>
              </span>
              <span v-else>{{ data.master_num }}</span>
            </text>
          </view>
        </view>
        <!-- <text style="font-size: 160%">入库数量：</text>
        <input
          class="scanner-input uni-input"
          cursor-spacing="10"
          ref="scan_carton"
          type="text"
          placeholder="请输入数量"
          :focus="scan_carton_focus"
          @confirm="handleInput1()"
          v-model="formData.scan_carton"
        /> -->

        <view v-if="data.position_num && data.position_num.length">
          <!-- 表头 -->
          <view
            style="display: flex; justify-content: space-between; padding: 10px"
          >
            <text>库位</text>
            <text>数量</text>
            <text>移库数量</text>
          </view>

          <!-- 数据行 -->
          <view
            v-for="(position, index) in data.position_num"
            :key="index"
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 10px;
              border-bottom: 1px solid #eee;
            "
          >
            <text>{{ position.position_num }}</text>
            <text>{{ position.scan_carton }}</text>
            <input
              class="scanner-input"
              type="number"
              placeholder="请输入移库数量"
              min="0"
              :max="position.scan_carton"
              v-model.number="position.move_qty"
              :focus="moveQtyInputRefs[index]"
              @input="handleQtyInput(position)"
              @confirm="handleMoveQtyConfirm(index)"
              style="
                width: 120px;
                border: 1px solid #ccc;
                padding: 4rpx;
                text-align: center;
              "
            />
          </view>
        </view>

        <text style="font-size: 160%">扫描库位号：</text>
        <input
          class="scanner-input uni-input"
          cursor-spacing="10"
          ref="store_position"
          type="text"
          placeholder="请扫描库位号"
          :focus="store_position_focus"
          @confirm="handleInput2()"
          v-model="formData.store_position"
        />
      </view>
    </view>

    <view class="fotter">
      <button class="floating-button" @click="back()">
        {{ $t("content.Finish") }}
      </button>
    </view>
  </view>
</template>

<script>
import {
  move_store_position,
  fbaTakeStockAddDetail,
  fbaTakeStockListHistoryJob,
  fbaTakeStockfinishJob,
  bind_store_position,
} from "../../commom/js/api.js";

export default {
  data() {
    return {
      scan_carton_focus: true,
      store_position_focus: false,
      formData: {
        // scan_carton: "",
        store_position: "",
      },
      scanResults: [],
      data: {},
      moveQtyInputRefs: [], // 存储每个移库数量输入框的 ref
    };
  },
  onLoad(options) {
    const params = options.data;
    if (params) {
      // 将字符串转换回对象
      this.data = JSON.parse(decodeURIComponent(params));
      // 初始化 move_qty 字段
      if (this.data.position_num && Array.isArray(this.data.position_num)) {
        this.data.position_num.forEach((item) => {
          if (!item.hasOwnProperty("move_qty")) {
            this.$set(item, "move_qty", null); // 使用 Vue.set 确保响应式
          }
        });
      }
      console.log("this.data---------", this.data);
    }
    console.log(`options:${JSON.stringify(this.data)}`);
    // 初始化焦点数组（确保长度正确）
    this.moveQtyInputRefs = new Array(this.data.position_num.length).fill(
      false
    );
    // 使用 $nextTick 确保DOM更新后执行
    this.$nextTick(() => {
      // 使用Vue.set触发响应式更新
      this.$set(this.moveQtyInputRefs, 0, true);
      // 或使用 splice
      // this.moveQtyInputRefs.splice(0, 1, true);
    });
  },
  methods: {
    // 校验输入值是否合法，并处理 placeholder 显示
    handleQtyInput(position) {
      const { move_qty, total_scan_carton } = position;

      if (move_qty === null || move_qty === "") {
        position.move_qty = null; // 保持 placeholder 显示
      } else {
        const value = parseInt(move_qty, 10);

        if (isNaN(value)) {
          uni.showToast({
            title: "请输入有效数字",
            icon: "none",
          });
          position.move_qty = null;
        } else if (value < 0) {
          uni.showToast({
            title: "移库数量不能为负数",
            icon: "none",
          });
          position.move_qty = null;
        } else if (value > total_scan_carton) {
          uni.showToast({
            title: "不能超过库存数量",
            icon: "none",
          });
          position.move_qty = null;
        } else {
          position.move_qty = value;
        }
      }
    },

    async handleInput1() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));
      this.store_position_focus = false;
      this.$nextTick(() => {
        this.store_position_focus = true;
      });
    },
    async handleInput2() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));
      // 校验：是否选择了至少一个有效的移库数量
      const validMoveList = this.data.position_num.filter(
        (item) => typeof item.move_qty === "number" && item.move_qty > 0
      );

      if (!validMoveList.length) {
        uni.showToast({
          title: "请填写有效的移库数量（> 0）",
          icon: "none",
        });
        return;
      }

      const params = {
        order_num: this.data.master_num,
        store_position: this.formData.store_position,
        move_store_info: validMoveList,
      };
      console.log("------params: ", params);

      move_store_position(params)
        .then((res) => {
          if (res.code === 200) {
            this.successAudio();
            uni.showToast({
              title: res.msg,
              duration: 2000,
              icon: "none",
              mask: true,
              position: "top",
              success: function () {
                setTimeout(() => {
                  uni.redirectTo({
                    url: "./index",
                  });
                });
              },
            });
          } else {
            console.log(res.msg);
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            // 清空输入框
            this.data.position_num.forEach((item) => {
              this.$set(item, "move_qty", null); // 直接重置为 null
            });
            this.formData.store_position = "";

            this.failAudio();
          }
        })
        .catch((err) => {
          console.error("请求失败", err);
          // 清空输入框
          // 清空输入框
          this.data.position_num.forEach((item) => {
            this.$set(item, "move_qty", null); // 直接重置为 null
          });
          this.formData.store_position = "";
        });
      // this.parcel_num_focus = false;
      // this.$nextTick(() => {
      //   this.parcel_num_focus = true;
      // });
    },
    focusFirstMoveQtyInput() {
      if (this.moveQtyInputRefs.length > 0) {
        this.moveQtyInputRefs.splice(0, 1, true);
        this.$nextTick(() => {
          this.moveQtyInputRefs[0] = true;
        });
      }
    },
    // 收集 input 引用
    setMoveQtyInputRef(el, index) {
      this.moveQtyInputRefs[index] = el;
    },

    // 回车后聚焦下一个或底部输入框
    // 在 methods 中
    handleMoveQtyConfirm(currentIndex) {
      const nextIndex = currentIndex + 1;

      if (this.moveQtyInputRefs[nextIndex] !== undefined) {
        // 聚焦下一个移库数量输入框
        this.moveQtyInputRefs.splice(nextIndex, 1, true);
        this.$nextTick(() => {
          this.moveQtyInputRefs[nextIndex] = true;
        });
      } else {
        // 聚焦到底部“扫描库位号”输入框
        this.store_position_focus = true;
        this.$nextTick(() => {
          this.store_position_focus = false;
          this.store_position_focus = true;
        });
      }
    },
    backToTaskStock() {
      uni.navigateTo({
        url: `./index`,
      });
    },
  },
};
</script>

<style>
.scan-results {
  margin-top: 20px;
}
.scan-results view {
  padding: 10px;
  border-bottom: 1px solid #ccc;
}
.base-margin-top {
  margin-top: 10rpx;
}
.base-padding-x {
  padding: 0 25rpx;
}
.base-h6 {
  font-size: 25rpx;
}
.base-secondary-text {
   {
    color: rgba(68, 68, 68, 1);
  }
}
/* 盘点详情视图样式 */
.detail-view {
  display: flex;
  flex-direction: column;
  padding: 10rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  margin-top: 2rpx;
}

/* 盘点详情项样式 */
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1px solid #eaeaea;
}

/* 最后一个详情项无底部边框 */
.detail-item:last-child {
  border-bottom: none;
}

/* 详情标签样式 */
.detail-label {
  color: #666;
  font-size: 28rpx;
}

/* 详情值样式 */
.detail-value {
  color: #000;
  font-size: 30rpx;
  font-weight: bold;
}
/* 浮动底部按钮样式 */
.floating-button {
  position: fixed; /* 固定定位 */
  bottom: 20rpx; /* 距离底部20rpx */
  left: 50%; /* 距离左侧50%，实现水平居中 */
  transform: translateX(-50%); /* 向左移动50%，实现精确居中 */
  background-color: #55aaff; /* 按钮背景颜色 */
  color: white; /* 按钮文字颜色 */
  padding: 2rpx; /* 按钮内边距 */
  border-radius: 10rpx; /* 按钮边框圆角，非圆形 */
  width: 90%; /* 按钮宽度 */
  height: 80rpx; /* 按钮高度 */
  font-size: 30rpx; /* 文字大小 */
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2); /* 按钮阴影 */
  text-align: center; /* 文字居中 */
  line-height: 80rpx; /* 行高与按钮高度一致，实现文字垂直居中 */
}
</style>
