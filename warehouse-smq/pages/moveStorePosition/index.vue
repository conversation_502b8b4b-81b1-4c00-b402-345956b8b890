<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
      <uni-nav-bar
        :shadow="false"
        :title="$t('content.MoveStorePosition')"
        @clickLeft="backToIndex()"
      >
        <view slot="left">
          <i class="iconfont iconfanhui1 icon-left" />
        </view>
        <view slot="right">
          <text>{{ $username }}</text>
        </view>
      </uni-nav-bar>
    </view>

    <!-- 操作栏 -->
    <view class="operation">
      <view>
        <!-- <checkbox :checked="forcedBinding" @click='checkboxChange1' style='margin-right: 24px'>强制绑定</checkbox> -->
        <!-- <checkbox :checked="spendMore" @click='checkboxChange2'>一单多位</checkbox> -->
      </view>
      <view class="scanner">
        <text style="font-size: 160%">扫描FBA箱号：</text>
        <input
          class="scanner-input uni-input"
          cursor-spacing="10"
          ref="parcel_num"
          type="text"
          placeholder="请扫描FBA箱号"
          :focus="parcel_num_focus"
          @confirm="doScan()"
          v-model="formData.parcel_num"
        />
      </view>

      <!-- 底栏操作按钮 -->
      <view class="bottom-btn">
        <button class="left" type="primary" @click="back()" :loading="false">
          {{ $t("content.Finish") }}
        </button>
        <button
          class="right"
          type="primary"
          @click="nextStep()"
          :loading="false"
        >
          {{ $t("content.NextStep") }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import {
  parcel_get_customer_order,
  get_parcel_info,
  getStep,
  nextStep,
  truck_fba_parcel_out,
  out_warehouse_over,
  pda_save_img,
  bind_store_position,
} from "../../commom/js/api.js";
import UniDataSelect from "@/components/uni-data-select/uni-data-select";
export default {
  components: {
    "uni-data-select": UniDataSelect,
  },
  data() {
    return {
      isButtonVisible: false,
      parcel_num_focus: true,
      store_position_focus: false,
      order_info: {
        parcel_num: "",
        store_position: "",
        customer_order_num: "",
      }, // 主单信息
      responseDataList: [], // 保存返回的数据列表
      subOrderNumber: "",
      orderInfo: {
        masterOrder: "",
        errorSubOrders: 0,
        loadedTickets: 0,
        deliveredTickets: 0,
        loadedItems: 0,
        deliveredItems: 0,
      },
      pattern: {
        color: "",
        selectedColor: "",
        backgroundColor: "",
        buttonColor: "",
      },
      horizontal: "left",
      vertical: "bottom",
      direction: "vertical",
      content: [
        {
          iconPath: "/static/search.png",
          selectedIconPath: "/static/search.png",
          text: this.$t("content.ScanCodeQuery"),
          active: true,
        },
      ],
      formData: {
        parcel_num: "",
        // store_position: "",
      },
      option: {},
      loading: false,
      ocean_num_id: null,
      ocean_nums: [],
      showSelectOceanOrder: false,
      oceanOrderOptions: [],
      forcedBinding: false,
      spendMore: false,
    };
  },
  onLoad: function (option) {
    // option为object类型，会序列化上个页面传递的参数
    // this.getStep(option.type)
  },
  created() {},
  methods: {
    async doScan() {
      // 模拟等待时间以避免频繁请求
      await new Promise((resolve) => setTimeout(resolve, 500));
      const params = {
        parcel_num: this.formData.parcel_num, // 构造请求参数
      };
      // 发起请求查询当前箱号对应的订单信息, 如果查到了,跳转另一个页面
      get_parcel_info(params)
        .then((res) => {
          if (res.code === 200) {
            uni.showToast({
              // 显示成功提示
              title: res.msg,
              icon: "none",
            });

            console.log("res-->", res);

            // 直接跳转详情页
            // const detail_params = {
            //   parcel_num: res.parcel_num, // 构造请求参数
            //   order_num: res.data.order_num, // 构造请求参数
            // };
            this.goToDetail(res);

            // 清空输入框并重新聚焦
            this.$set(this.formData, "parcel_num", "");
            this.parcel_num_focus = false;
            this.$nextTick(() => {
              this.parcel_num_focus = true;
            });
          } else {
            this.failAudio(); // 播放失败音效
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            this.responseDataList.push(res);

            // 清空输入框并重新聚焦
            this.$set(this.formData, "parcel_num", "");
            this.parcel_num_focus = false;
            this.$nextTick(() => {
              this.parcel_num_focus = true;
            });
          }
        })
        .catch((err) => {
          this.failAudio();
          uni.showToast({
            title: `请求失败: ${err}`,
            icon: "none",
          });
          console.error("POST请求失败", err);

          // 清空输入框并重新聚焦
          this.$set(this.formData, "parcel_num", "");
          this.parcel_num_focus = false;
          this.$nextTick(() => {
            this.parcel_num_focus = true;
          });
        });
    },
    async goToDetail(item) {
      // 跳转至详情页面
      console.log("item-->", item);
      this.toDetail(item);
    },
    toDetail(data) {
      const params = JSON.stringify(data); // 将数据转换为字符串传递
      uni.navigateTo({
        url: `./detail?data=${encodeURIComponent(params)}`,
      });
    },
    // async checkboxChange1(event) {
    //   this.forcedBinding = !this.forcedBinding;
    // },
    // async checkboxChange2(event) {
    //   this.spendMore = !this.spendMore;
    // },
    async handleInput1() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));
      this.store_position_focus = false;
      this.$nextTick(() => {
        this.store_position_focus = true;
      });
    },
    handleCancel() {},
    async handleConfirm() {
      // this.$nextTick(() => {
      //     const selectElement = document.getElementById('select-ocean');
      //     this.ocean_num_id = selectElement.value
      // })
      this.store_position_focus = false;
      this.$nextTick(() => {
        this.store_position_focus = true;
      });
    },
    async handleInput2() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));
      const params = {
        parcel_num: this.formData.parcel_num,
        store_position: this.formData.store_position,
        forcedBinding: this.forcedBinding,
        // spendMore: this.spendMore
      };
      console.log("truckfbaorder-params: ", params);

      bind_store_position(params)
        .then((res) => {
          if (res.code === 200) {
            // this.formData.parcel_num = res.parcel_num;
            this.order_info.parcel_num = res.parcel_num;
            this.order_info.customer_order_num = res.customer_order_num;
            this.order_info.store_position = res.store_position;
            console.log("this.order_info-->", this.order_info);
            this.responseDataList.push(res.parcel_num);

            // 清空输入框 重新聚焦
            this.$set(this.formData, "parcel_num", "");
            this.$set(this.formData, "store_position", "");
            this.successAudio();
          } else {
            console.log(res.msg);
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            // 这里的异常有两种情况：第一个输入框不对、第二个输入框不对
            // 清空输入框
            this.$set(this.formData, "parcel_num", "");
            this.$set(this.formData, "store_position", "");
            // this.ocean_num_id = null
            this.failAudio();
          }
        })
        .catch((err) => {
          console.error("请求失败", err);
          // 清空输入框
          this.$set(this.formData, "parcel_num", "");
          this.$set(this.formData, "store_position", "");
          // this.ocean_num_id = null
          this.failAudio();
        });
      this.parcel_num_focus = false;
      this.$nextTick(() => {
        this.parcel_num_focus = true;
      });
    },
    to_scan_express() {
      const params = {
        parcel_num: this.formData.parcel_num,
        tracking_num: this.formData.parcel_num,
        // order_num: this.order_info.parcel_num !== "" ? this.order_info.parcel_num : ""
      };
      uni.navigateTo({
        url: `../nottransfer/index?parcel_num=${this.formData.parcel_num}`,
        query: params,
      });
    },
    getStep(type) {
      uni.showLoading({
        title: this.$t("content.Loading"),
        mask: true,
      });
      getStep({ type })
        .then((res) => {
          console.log(res);
          if (res.code === 200) {
            getApp().globalData.request = {
              ...getApp().globalData.request,
              ...res.data.params,
            };
            this.getStepRouter(res.data.step);
          }
          uni.hideLoading();
        })
        .catch((err) => {
          uni.hideLoading();
        });
    },
    // 清空再聚焦
    cleanFocus() {
      // 清空输入框
      this.$set(this.formData, "parcel_num", "");
      this.focusStatus = false;
      // 重新聚焦
      this.$nextTick(() => {
        this.focusStatus = true;
      });
    },
    // 监听回车
    handelEnter(str) {
      if (str === "post") {
      } else if (str) {
        this.$nextTick(() => {
          this.$refs[str].focus = true;
        });
      }
    },
    // 跳转 查看未处理包裹
    to_not_scanned() {
      const not_scan_params = {
        order_num: this.order_info.parcel_num,
      };
      uni.navigateTo({
        url: `../nottransfer/index?order_num=${this.order_info.parcel_num}`,
        query: not_scan_params,
      });
    },
    // 校验
    validateForm() {
      return new Promise((resolve, reject) => {
        if (!this.formData.parcel_num) {
          this.failAudio();
          uni.showToast({
            title: this.$t("content.PScanInventoryOrderNo"),
            icon: "none",
            mask: true,
            duration: 2000,
          });
          resolve(false);
        }
        resolve(true);
      });
    },
    onInputConfirm() {
      // 在这里处理扫描子单号的逻辑，更新 orderInfo 数据
      // 为了示例，这里简单地模拟了一些数据
      this.orderInfo = {
        masterOrder: "123456",
        errorSubOrders: 5,
        loadedTickets: 10,
        deliveredTickets: 8,
        loadedItems: 20,
        deliveredItems: 15,
      };
    },
  },
};
</script>

<style scoped>
.container {
  padding: 20px;
}

.input-container {
  margin-bottom: 20px;
}

.input-box {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-container {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-row {
  display: flex;
  justify-content: space-between;
}

.info-column {
  display: flex;
  flex-direction: column;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh; /* 100%视窗高度，根据实际需要调整 */
}

/* 海运提单模态框样式 */
.form-inline .form-control {
  display: inline-block;
  width: auto;
  vertical-align: middle;
}
.form-control {
  min-width: 100px;
  display: block;
  width: 100%;
  height: 50px;
  padding: 6px 12px;
  font-size: 20px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.oceanModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-content {
  position: relative;
  background: #fff;
  padding: 20px;
  border-radius: 5px;
  width: 500px;
  height: 300px;
}
.model-bottom-btn {
  position: absolute;
  bottom: 20px;
  width: 500px;
  height: 46px;
}
.bottom-btn-cancel,
.bottom-btn-confirm {
  position: absolute;
  display: inline-block;
  width: 50%;
}
.right {
  right: 0;
}
.left {
  left: 0;
}
</style>
