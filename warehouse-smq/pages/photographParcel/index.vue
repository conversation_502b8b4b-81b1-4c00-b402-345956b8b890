<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
		<uni-nav-bar :shadow='false' :title="$t('content.PhotographParcel')" @clickLeft="backToIndex()">
		<view slot="left">
			<i class="iconfont iconfanhui1 icon-left" />
        </view>
        <view slot="right"> <text>{{$username}}</text> </view>
      </uni-nav-bar>
    </view>
	
    <!-- 操作栏 -->
    <view class="operation">
		<view class="scanner">
			<text style="font-size: 200%;">扫描箱号：</text>
			<input class="scanner-input uni-input" 
				cursor-spacing="10" 
				type="text"
				ref="parcel_num"
				:focus="focusStatus" 
				:placeholder="$t('content.PScanPickingOrderNo')" 
				@confirm="nextStep()" 
				v-model="formData.master_order_num"
			/>
		</view>
		<view>
			<uni-fab :pattern="pattern" 
				:content="content" 
				:horizontal="horizontal" 
				:vertical="vertical" 
				:direction="direction"
				@trigger="trigger"
				@fabClick="fabClick">
			</uni-fab>
		</view>


	<view class="image-container">
		<!-- 判断 img_data 是否有数据 -->
		<view v-if="img_data.length > 0">
			<img v-for="(image, index) in img_data" :key="index" class="arrival-image" alt="图片" :src="getImageUrl(image.url)" mode="aspectFit"></img>
		</view>
		<view v-else>
			<text style="text-align: center; margin-top: 10px; margin-bottom: 10px;">暂无图片</text>
		</view>
	</view>

	  
	<!-- 拍照上传按钮 -->
	<button v-if="uploadImageShow" @click="uploadPhoto">上传包裹图片</button>
    
    <!-- 底栏操作按钮 -->
    <view class="bottom-btn">
		<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Finish')}}</button>
        <button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
    </view>
	  
    </view>
</view>

</template>

<script>
import {get_parcel_img} from '../../commom/js/api.js'
import base from '@/commom/js/config.js'
import UniDataSelect from '@/components/uni-data-select/uni-data-select'

export default {
    components: {
    'uni-data-select': UniDataSelect
    },
	data() {
		return {
			focusStatus: true,
			img_data: [],
			pattern: {
				color: '',
				selectedColor: '',
				backgroundColor: '',
				buttonColor: ''
			},
			horizontal: 'left',
			vertical: 'bottom',
			direction: 'vertical',
			content: [
				{
					iconPath: '/static/search.png',
					selectedIconPath: '/static/search.png',
					text: this.$t('content.ScanCodeQuery'),
					active: true
				},
			],
			formData: {
				master_order_num: ''
			},
			option: {},
			loading: false,
      ocean_nums: [],
      uploadImageShow: false,
      oceanOrderOptions: [],
      parcel: null
		}
	},
	computed: {
		// 获取当前步骤需要提交的表单信息
		requestData() {
			return {
				...getApp().globalData.request,
				step: this.getRoutePath().step,
				...this.formData,
			}
		}
	},
	onLoad: function(option) {
		console.log(option);
		this.option = option
	},
	watch: {
		// 监听父组件传递的 focus prop 的变化
		focus(newValue) {
			this.inputFocus = newValue;
		}
    },
	methods: {
		trigger(data) {
			switch (data.index) {
				case 0:
				uni.navigateTo({
					url: '../search/index'
				})
				break;
				default:
				break;
			}
		},
		fabClick() {},

		// 校验
		validateForm() {
			return new Promise((resolve, reject) => {
				if (!this.formData.master_order_num) {
					this.failAudio()
					uni.showToast({
						title: this.$t('content.PScanPickingOrderNo'),
						icon: 'none',
						mask: true,
						duration: 2000
					});
					resolve(false)
				}
				resolve(true)
			})
		},

		// 返回
		back() {
			uni.redirectTo({
				url: '../index/index'
			})
		},

		// 监听回车
		handelEnter(str) {
			if (str === 'post') {
				this.getOrder()
			} else if (str) {
				this.$nextTick(() => {
					this.$refs[str].focus=true;
				});
			}
		},
        // 拼接图片url
        getImageUrl(url) {
      console.log('null????????', url)
          // return base.BASE_URL + '/media' + url.split('media')[1]
          let mediaPath = url.match(/\/media\/(.*)/)
          if (mediaPath) {
            mediaPath = url.match(/\/media\/(.*)/)[1]
          } else {
            mediaPath = url
          }
          // return mediaPath
          return `${base.BASE_URL}/media/${mediaPath}`
        },
		// 下一步
		async nextStep() {
			// 等待500毫秒，可以根据实际情况调整时间
			await new Promise(resolve => setTimeout(resolve, 500));
			const params = {
				// parcel_num: this.formData.master_order_num
				// parcel__parcel_num: this.formData.master_order_num
				parcel_num: this.formData.master_order_num
			}
			get_parcel_img(params).then(res => {
        console.log('post res-->', res)
				if (res.code === 200) {
					// this.failAudio()
					uni.showToast({
						title: res.msg,
						icon: 'none',
						mask: true,
						duration: 500
					});
          this.img_data = res.data
          this.parcel = res.parcel
          console.log('图片-->', this.img_data)
					// // 清空输入框
					// this.$set(this.formData, 'master_order_num', '')
					this.focusStatus=false
					// 重新聚焦
					this.$nextTick(() => {
						this.focusStatus=true
					});
          this.uploadImageShow=false
          this.$nextTick(() => {
            this.uploadImageShow=true
          })
				} else {
					this.failAudio()
					uni.showToast({
						title: res.msg,
						icon: 'none',
						mask: true,
						duration: 500
					});
					this.search_data = res;

					// // 清空输入框
					// this.$set(this.formData, 'master_order_num', '')
					this.focusStatus=false
					// 重新聚焦
					this.$nextTick(() => {
						this.focusStatus=true
					})
          this.uploadImageShow=true
          this.$nextTick(() => {
            this.uploadImageShow=false
          })
          this.img_data = []
				}
			}).catch(err => {console.error('POST请求失败', err);
                this.failAudio()
                uni.showToast({
                    title: err,
                    icon: 'none',
                    mask: true,
                    duration: 500
                });
				// 清空输入框
				// this.$set(this.formData, 'master_order_num', '')
				this.focusStatus=false
				this.$nextTick(() => {
					this.focusStatus=true
				})
                this.uploadImageShow=true
                this.$nextTick(() => {
                	this.uploadImageShow=false
                })
                this.img_data = []
			})
		},
        handleCancel(){
            this.showSelectOceanOrder = false
        },
        async handleConfirm(){
            // this.$nextTick(() => {
            //     const selectElement = document.getElementById('select-ocean');
            // })
            this.showSelectOceanOrder = false
            this.nextStep()
        },
		// 点击按钮上传图片
		async uploadPhoto() {
      const params = {
        // parcel_num: this.formData.master_order_num
        parcel: this.parcel
      }

			// 调用PDA拍照方法
			try {
				const imageInfo = await uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['camera'],
				});
				// 获取选中图片的本地路径
				const tempFilePath = imageInfo[1].tempFilePaths[0];
        console.log('tempFilePath-->', tempFilePath)
         // 文件名
        // params['name'] = `${tempFilePath.split('/').pop()}.jpg`
				// 执行拍照上传逻辑
				await this.uploadImage(tempFilePath, params);

			} catch (error) {
				console.error('拍照失败或取消', error);
			}
		},

	// 	async uploadImage(filePath, params) {
	// 		const uploadParams = {
	// 		    filePath,
	// 		    name: 'file',
	// 		    formData: params,
	// 		};
  //         upload_parcel_img(uploadParams).then(res => {
  //               const resData = JSON.parse(res[1].data)
  //               if (resData&&resData.code === 200) {
  //                   // 处理上传成功的逻辑
  //                   console.log('上传成功', res[1]);
  //                   uni.showToast({
  //                   	title: resData.msg,
  //                   	icon: 'none'
  //                   })
  //               } else {
  //                   uni.showToast({
  //                   	title: '上传失败: ' + resData.msg || resData.detail,
  //                   	icon: 'none'
  //                   })
  //               }
	// 		  }).catch (error => {
	// 		    // 处理上传失败的逻辑
	// 		    console.error('上传失败', error);
  //               uni.showToast({
  //               	title: '上传失败: ' + error,
  //               	icon: 'none'
  //               })
	// 		    throw error; // 将错误抛出，让调用者知道上传失败
	// 		  })
	// 	// }
	// }

      async uploadImage(filePath, params) {

			try {
			    const res = await uni.uploadFile({
					url: `${base.BASE_URL}/api/parcelAttachments/pda_upload/`, // 替换成你的实际上传图片的接口地址
					filePath: filePath,
					name: 'file',
					formData: params,
			    });
                const resData = JSON.parse(res[1].data)
                if (resData&&resData.code === 200) {
                    // 处理上传成功的逻辑
                    console.log('上传成功', res[1]);
                    uni.showToast({
                    	title: resData.msg,
                    	icon: 'none'
                    })
                  await this.nextStep()
                } else if(resData&&resData.code === 201) {
                    console.log('上传成功', res[1]);
                    uni.showToast({
                    	title: resData.msg,
                    	icon: 'none'
                    })
                  await this.nextStep()
                } else {
                    uni.showToast({
                    	title: '上传失败: ' + resData.msg || resData.detail,
                    	icon: 'none'
                    })
                }
			  } catch (error) {
			    // 处理上传失败的逻辑
			    console.error('上传失败', error);
                uni.showToast({
                	title: '上传失败: ' + error,
                	icon: 'none'
                })
			    throw error; // 将错误抛出，让调用者知道上传失败
			  }
		}

  }
}
</script>

<style>
/* 海运提单模态框样式 */
.form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
}
.form-control {
    min-width: 100px;
    display: block;
    width: 100%;
    height: 50px;
    padding: 6px 12px;
    font-size: 20px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
}
.oceanModal{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-content {
  position: relative;
  background: #fff;
  padding: 20px;
  border-radius: 5px;
  width: 500px;
  height: 300px;
}
.model-bottom-btn{
  position: absolute;
  bottom: 20px;
  width: 500px;
  height: 46px;
}
.bottom-btn-cancel, .bottom-btn-confirm{
    position: absolute;
    display: inline-block;
    width: 50%;
}
.right{
    right: 0;
}
.left{
    left: 0;
}
/* 海运提单模态框样式结束 */
    
</style>
