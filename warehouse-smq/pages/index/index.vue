<template>
	<view class="container">
		<uni-nav-bar :shadow='false' :title="$t('content.HomePage')" @clickRight="logOut()">
			<view slot="right">
				<text>{{$username}}</text>
				<i class="iconfont icontuichu logout" />
			</view>
		</uni-nav-bar>
		<view class="grid-box">
			<uni-grid @change="navigatorTo($event)" :column="3" :show-border="true" :square="true">
				<uni-grid-item v-for="(item,index) in grid_array" :index='index' :key="item.icon" class="grid-item">
					<i :class="['grid-icon','iconfont', item.icon]" :style="{color:item.color}" />
					<text>{{item.text}}</text>
				</uni-grid-item>
			</uni-grid>
		</view>
		<!--
		 <view>
			<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
			 @trigger="trigger" @fabClick="fabClick"></uni-fab>
		</view>
		 -->
	</view>
</template>

<script>
	import uniGrid from "@/components/uni-grid/uni-grid.vue"
	import uniGridItem from "@/components/uni-grid-item/uni-grid-item.vue"
	import uniNavBar from "@/components/uni-nav-bar/uni-nav-bar.vue"
	import uniFab from '@/components/uni-fab/uni-fab.vue'
	// const Qs = require('qs')
	export default {
		components: {
			uniGrid,
			uniGridItem,
			uniNavBar,
			uniFab
		},
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				content: [
					// {
					// 	iconPath: '/static/sku.png',
					// 	selectedIconPath: '/static/sku.png',
					// 	text: this.$t('content.ScanCodeQuery'),
					// 	active: true
					// },
					// {
					// 		iconPath: '/static/sku.png',
					// 		selectedIconPath: '/static/sku.png',
					// 		text: 'sku库存',
					// 		active: true
					// },
					// {
					// 	iconPath: '/static/kc.png',
					// 	selectedIconPath: '/static/kc.png',
					// 	text: '库位库存',
					// 	active: true
					// },
					{
						iconPath: '/static/sku.png',
						selectedIconPath: '/static/sku.png',
						text: this.$t('content.PrintFaceSheet'),
						active: false
					},
				],
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				grid_array: [
					{
						icon: 'iconxiezai',
						text: this.$t('content.Discharge'),
						path: 'discharge',
						color: '#87ceeb'
					},
					{
						icon: 'iconiconfontzhizuobiaozhunbduan2',
						text: this.$t('content.TruckFBAOut'),
						path: 'truckfbaout',
						color: '#1f43c5'
					},
					{
						icon: 'iconiconfontzhizuobiaozhunbduan31',
						text: this.$t('content.TruckNoFBAOut'),
						path: 'trucknofbaout',
						color: 'teal'
					},
					{
						icon: 'iconshouhuo',
						text: this.$t('content.Receipt'),
						path: 'receipt',
						color: '#ad2c56'
					},
					{
						icon: 'iconpack',
						text: '绑定库位',
						path: 'bindStorePosition',
						color: '#5555ff'
					},
					{
						icon: 'iconpack2',
						text: '移动库位',
						path: 'moveStorePosition',
						color: '#ffcc00'
					},
					{
						icon: 'iconchaxun',
						text: this.$t('content.Shelves'),
						path: 'shelf',
						color: '#0fd454'
					},
					{
						icon: 'iconjianhuoguihuan',
						text: this.$t('content.Picking'),
						path: 'pick',
						color: '#e087eb'
					},
					{
						icon: 'iconjianhuoguihuan2',
						text: this.$t('content.PhotographParcel'),
						path: 'photographParcel',
						color: '#d95454'
					},
          {
						icon: 'iconyiku',
						text: this.$t('content.FbaTakeStock'),
						path: 'fbaTakeStock',
						color: '#0f5ed4'
					},
					// {
					// 	icon: 'icondabaoxiazai',
					// 	text: this.$t('content.Baling'),
					// 	path: 'bale',
					// 	color: '#1f43c5'
					// },
					// {
					// 	icon: 'iconpack',
					// 	text: this.$t('content.Sorting'),
					// 	path: 'sorting',
					// 	color: 'teal'
					// },
					// {
					// 	icon: 'iconyiku',
					// 	text: this.$t('content.TransferOff'),
					// 	path: 'transferoff',
					// 	color: '#000'
					// },
					// {
					// 	icon: 'iconyiku1',
					// 	text: this.$t('content.TransferOn'),
					// 	path: 'transferon',
					// 	color: 'teal'
					// },
					// {
					// 	icon: 'iconnotyiku',
					// 	text: this.$t('content.NoTransferDeatils'),
					// 	path: 'nottransfer',
					// 	color: 'teal'
					// },
					// {
					// 	icon: 'iconsearch',
					// 	text: this.$t('content.Search'),
					// 	path: 'search',
					// 	color: 'teal'
					// },
					// {
					// 	icon: 'iconnavicon-kcpdd',
					// 	text: this.$t('content.Inventory'),
					// 	path: 'check',
					// 	color: '#f90909'
					// },
					// {
					// 	icon: 'iconrefund',
					// 	text: this.$t('content.RefundOrderOn'),
					// 	path: 'refund',
					// 	color: 'teal'
					// },

				]
			}
		},
		onShow() {
			getApp().globalData.request = {}
		},
		computed: {
			username() {
				return getApp().globalData.userInfo.username
			}
		},
		created() {
			// this.$axios.post('http://127.0.0.1:8001', Qs.stringify({
			// 	action: 'getPrinterList'
			// })).then((res) => {
			// 	if (res.status === 200) {
			// 		this.printer_list = res.data.data
			// 	} else {
			// 		this.$message.error('获取打印机失败')
			// 	}
			// }).catch(() => {
			// 	this.$message.error('获取打印机失败')
			// })
		},
		methods: {
			trigger(data) {
				switch (data.index) {
					// case 0:
					// 	uni.navigateTo({
					// 		url: '../search/index'
					// 	})
					// 	break;
					// case 1:
					// 	uni.navigateTo({
					// 		url: '../warehouse/index'
					// 	})
					// 	break;
					case 0:
						uni.navigateTo({
							url: '../order/printer'
						})
						break;
					default:
						break;
				}

			},
			fabClick() {},
			// 跳转
			navigatorTo(e) {
				const type = this.grid_array[e.detail.index]['path']
				const url = `../${this.grid_array[e.detail.index]['path']}/index?type=${type}`

				console.log(type);
				getApp().globalData.request.type = type
				console.log(url);
				uni.navigateTo({
					url: url
				});
			},
			// 退出登录
			logOut() {
				uni.showModal({
					title: this.$t('content.Tips'),
					content: this.$t('content.LogOut'),
					success: function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
							getApp().globalData.token = ''
							getApp().globalData.userInfo = {}
							uni.removeStorageSync('token');
							uni.removeStorageSync('userInfo');
							uni.navigateTo({
								url: '../login/index'
							});
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.container {
		font-size: 14px;
		line-height: 24px;

		.logout {
			color: red;
			font-weight: bold;
			margin-left: 10rpx;
		}

		.grid-box {
			padding: $Gpadding;

			.grid-item {
				text-align: center;

				.grid-icon {
					margin: auto auto;
					font-size: 80rpx;
				}
			}
		}
	}
</style>
