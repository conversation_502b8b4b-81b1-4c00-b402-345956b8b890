<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.Baling')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left" />
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>

		<!-- 操作栏 -->
		<view class="operation">
			<!-- 顶部显示栏 -->
			<view class="top-info">
				<view>{{$t('content.PickingCarCode')}}：{{requestData.master_order_num}}</view>
				<view>Pallet ID：{{requestData.pallet_id}}</view>
				<view>{{$t('content.PickingCarsNumber')}}：{{requestData.pick_car_num}}</view>
				<view>{{$t('content.BalingOrderNumber')}}：{{requestData.expected_pick}} | {{requestData.actual_pick}}</view>
			</view>
			<view class="scanner">
				<view class="scanner-title">{{$t('content.ScanSKUBarcode')}}</view>
				<input class="scanner-input uni-input" cursor-spacing="10" :placeholder="$t('content.PScanSKUBarcode')" focus @confirm="nextStep()" v-model="formData.sku_code" />
			</view>
			
			<view>
				<view class="info">{{requestData.actual_pick}}</view>
			</view>
			<view>
				<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
				 @trigger="trigger" @fabClick="fabClick"></uni-fab>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Finish')}}</button>
				<button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
			</view>
		</view>

	</view>

</template>

<script>
	const Qs = require('qs')
	import {
		nextStep,
		printer,
		get_intranet,
		get_print_name,
		getStep
	} from '../../commom/js/api.js'
	import uniFab from '@/components/uni-fab/uni-fab.vue';
	import { successAudio,failAudio } from '../../audio.js'
	export default {
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [
					{
						iconPath: '/static/search.png',
						selectedIconPath: '/static/search.png',
						text: this.$t('content.ScanCodeQuery'),
						active: true
					},
				],
				formData: {
					sku_code: null,
					piece: 1
				},
				// 初始反写信息
				initData: {},
				comfirm: false,
				option: {},
				loading: false,
				// 内网ip
				intranet: ''
			}
		},
		computed: {
			// 获取当前步骤需要提交的表单信息
			requestData() {
				return {
					...getApp().globalData.request,
					step: this.getRoutePath().step,
					...this.formData,
				}
			}
		},
		onLoad: function(option) {
			this.option = option
		},
		created() {

			// get_intranet().then(res => {
			// 	console.log(res);
			// 	if (res.code === 200) {
			// 		this.intranet = res.data[0].value
			// 		console.log('无线打印ip ='+res.data[0].value);
			// 		// this.$axios.post('http://'+this.intranet+':8001', Qs.stringify({
			// 		// 	action: 'getPrinterList'
			// 		// })).then((res) => {
			// 		// 	if (res.status === 200) {
			// 		// 		console.log(res.data.data[0][2])
			// 		// 		this.printName = res.data.data[0][2]
			// 		// 		if (this.printName === ''){
			// 		// 			uni.showToast({
			// 		// 			    title: '获取打印机失败',
			// 		// 			    duration: 2000
			// 		// 			});
			// 		// 		}

			// 		// 	} else {
			// 		// 		uni.showToast({
			// 		// 		    title: '获取打印机失败',
			// 		// 		    duration: 2000
			// 		// 		});
			// 		// 	}
			// 		// }).catch(() => {
			// 		// 	uni.showToast({
			// 		// 	    title: '获取打印机失败',
			// 		// 	    duration: 2000
			// 		// 	});
			// 		// })
			// 	} else {
			// 		console.log('获取不到内网ip，请重试！');
			// 		uni.showToast({
			// 		    title: '获取不到内网ip，请重试',
			// 		    duration: 2000
			// 		});
			// 	}
			// })

			//先配置默认打印机
			// get_print_name().then(res => {
			// 	console.log(res);
			// 	if (res.code === 200) {
			// 		this.printName = res.data[0].value
			// 		console.log('打印机名称 ='+res.data[0].value);
			// 	} else {
			// 		console.log('获取不到打印机名称，请重试！');
			// 	}
			// })

			const t = getApp().globalData.request
			Object.keys(t).forEach(i => {
				if (i.startsWith('$')) {
					this.initData[i.slice(1)] = t[i]
					this.formData[i.slice(1)] = t[i]
					this.comfirm = true
				}
			})
		},
		methods: {
			trigger(data) {
				switch (data.index) {
					case 0:
						uni.navigateTo({
							url: '../search/index'
						})
						break;
					default:
						break;
				}			
			},
			fabClick() {},
			// 校验
			validateForm() {
				return new Promise((resolve, reject) => {
					console.log(this.formData.width);
					if (!this.formData.sku_code) {
						this.failAudio()
						uni.showToast({
							title: this.$t('content.PScanSKUBarcode'),
							icon: 'none',
							mask: true,
							duration: 2000
						});
						resolve(false)
					}
					resolve(true)

				})
			},
			// 返回
			back() {
				uni.showModal({
					title: this.$t('content.Tips'),
					content: `${this.$t('content.ConfirmExit')}(${getApp().globalData.request.master_order_num})${this.$t('content.Baling')}？`,
					success: function(res) {
						if (res.confirm) {
							const {
								type
							} = getApp().globalData.request
							getApp().globalData.request = {
								type
							}
							uni.redirectTo({
								url: './bale'
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 获取数字得正负百分之50数组
			getNum50Range(num, percentage = 50) {
				const diff = Number((num * 0.5).toFixed(3))
				return [num - diff, num + diff]
			},
			// 下一步
			async nextStep() {
				if (this.loading) return
				this.loading = true
				uni.showLoading({
					title: this.$t('content.InRequest'),
					mask: true
				});
				let flag = await this.validateForm()
				uni.hideLoading();
				this.loading = false
				if (!flag) return
				// const PrinterName = this.printName
				// const _axios_post = this.$axios
				// const intranet = this.intranet
				// console.log("---->"+PrinterName)
				nextStep(this.filterRequest(this.requestData)).then(res => {
					console.log(res);
					if (res.code === 200) {
						this.successAudio()
						// 获取面单base64数据
						const labelContent = res.data.label_base64
						if (labelContent === 'success'){
							uni.showToast({
								title: this.$t('content.PrintSuccessfully'),
								duration: 500
							});
						}else{
							uni.showToast({
								title: this.$t('content.PrintingFailed'),
								duration: 1000
							});
						}

						// window打印
						// _axios_post.post('http://'+intranet+':8001', Qs.stringify({
						// 	action: 'doPrint',
						// 	printerName: PrinterName,
						// 	print_too: 'sumatrapdf',
						// 	printQty: 1,
						// 	label: labelContent,
						// 	type: 'pdf'
						// })).then((res) => {
						// 	if (res.data.status === 1) {
						// 		uni.showToast({
						// 		    title: this.$t('content.PrintSuccessfully'),
						// 		    duration: 500
						// 		});
						// 	} else {
						// 		uni.showToast({
						// 		    title: this.$t('content.PrintingFailed'),
						// 		    duration: 2000
						// 		});
						// 	}
						// }).catch(() => {
						// 	uni.showToast({
						// 	    title: this.$t('content.PrintingFailed'),
						// 	    duration: 2000
						// 	});
						// })

						if (res.data.step != 1) {
							getApp().globalData.request = { ...getApp().globalData.request,
								...res.data
							}
						} else {
							const type = getApp().globalData.request.type
							getApp().globalData.request = {
								type,
								master_order_num: res.data.master_order_num,
								
							}
						}
						if (res.data.msg){
							uni.showToast({
								title: res.data.msg,
								mask: true,
								duration: 500,
								icon: 'none',
								position: 'top'
							});
							uni.redirectTo({
								url: this.getRoutePath().basicPath + res.data.step
							})
						} else{
							uni.showToast({
								title: this.$t('content.BaleSucceeded'),
								mask: true,
								duration: 500,
								icon: 'none',
								position: 'top'
							});
							uni.redirectTo({
								url: this.getRoutePath().basicPath + res.data.step
							})
							
						}
						
						
					} else if(res.code === 996){
							this.failAudio()
							console.log(res.message)
							getStep({
								type:'bale',
								'flag':'1',
								'master_order_num':this.requestData.master_order_num
							}).then(res => {
								console.log(res);
								if (res.code === 200) {
									getApp().globalData.request = { ...getApp().globalData.request,
										...res.data.params
									}
									this.getStepRouter(3)
								}
								uni.hideLoading();
							}).catch(err => {
								uni.hideLoading();
							})
						
					} else {
						this.failAudio()
						uni.showToast({
							title: res.detail || res.message || 'fail request! please check!',
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
						this.formData.sku_code = ''
					}
				})
			},
		}
	}
</script>

<style>
</style>
