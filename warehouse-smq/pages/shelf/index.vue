<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
      <uni-nav-bar
        :shadow="false"
        :title="$t('content.Shelves')"
        @clickLeft="backToIndex()"
      >
        <view slot="left">
          <i class="iconfont iconfanhui1 icon-left" />
        </view>
        <view slot="right">
          <text>{{ $username }}</text>
        </view>
      </uni-nav-bar>
    </view>

    <!--    <view v-model="search_data" :key="index">
        <view class="info-container">
          <view class="info-row">
            <view class="info-column">
              <view class="info-item">
                <text>包裹号：</text>
                <text>{{ search_data.master_order }}</text>
              </view>
            </view>
          </view>
        </view>
      </view> -->
    <!-- 信息展示 -->

    <!-- 操作栏 -->
    <view class="operation">
      <view class="scanner">
        <text style="font-size: 200%">扫描单号：</text>
        <input
          class="scanner-input uni-input"
          cursor-spacing="10"
          type="text"
          ref="parcel_num"
          :focus="focusStatus"
          :placeholder="$t('content.PScanInBoundOrderNo')"
          @confirm="nextStep()"
          v-model="formData.master_order_num"
        />
      </view>
      <view>
        <uni-fab
          :pattern="pattern"
          :content="content"
          :horizontal="horizontal"
          :vertical="vertical"
          :direction="direction"
          @trigger="trigger"
          @fabClick="fabClick"
        ></uni-fab>
      </view>

      <view v-if="search_data.master_num">
        <view>
          <view class="item">
            <text>主单号: {{ search_data.master_num }}</text>
          </view>
          <view class="item">
            <view
              v-if="search_data.position_num && search_data.position_num.length"
            >
              <view
                v-for="(position, index) in search_data.position_num"
                :key="index"
                style="display: flex; justify-content: space-between"
              >
                <text>库位: {{ position.position_num }}</text>
                <text>数量: {{ position.scan_carton }}</text>
              </view>
            </view>
          </view>
          <view class="item">
            <text>收件人编码: {{ search_data.receiver_name }}</text>
          </view>
          <view class="item">
            <text>产品名称: {{ search_data.product_name }}</text>
          </view>
          <view class="item">
            <text>预计出仓: {{ search_data.pre_carton }}</text>
          </view>
          <view class="item">
            <text>已入仓: {{ search_data.in_warehouse }}</text>
          </view>
          <view
            class="item"
            style="color: #0000ff; cursor: pointer"
            @click="viewNotInWarehouse"
          >
            <text>未入仓: {{ search_data.not_in_warehouse }}</text>
          </view>
          <view class="item">
            <text>已出仓: {{ search_data.out_warehouse }}</text>
          </view>
          <view
            class="item"
            style="color: #ff0000; cursor: pointer"
            @click="viewNotOutWarehouse"
          >
            <text>未出仓: {{ search_data.not_out_warehouse }}</text>
          </view>
          <view class="item">
            <text>客户名: {{ search_data.customer_short_name }}</text>
          </view>
          <view class="item">
            <text>收件人国家: {{ search_data.buyer_country_code }}</text>
          </view>
          <view class="item">
            <text>收件人邮编: {{ search_data.buyer_postcode }}</text>
          </view>
          <!--
		  <view class="item">
		    <text>地址简称: {{ search_data.address_short_name }}</text>
		  </view>
		  <view class="item">
		    <text>实际到岗时间: {{ search_data.actual_arrivals_date }}</text>
		  </view>
		  <view class="item">
		    <text>预计到岗时间: {{ search_data.expected_arrivals_date }}</text>
		  </view>
		  -->
          <view class="image-container">
            <img
              v-for="(image, index) in search_data.img_data"
              :key="index"
              class="arrival-image"
              :src="image.img_url"
              mode="aspectFit"
            />
          </view>
        </view>
      </view>
      <view v-else class="no-data">
        <!-- 如果 master_order_info 不存在，则显示其他内容或者留空 -->
        <text>请输入单号查询</text>
      </view>

      <!-- 模态框: 未入仓包裹 -->
      <!-- <view class="oceanModal" v-if="showNotScannerParcel">
      <view v-if="dialogContentData.length > 0" class="modal-content">
        <text style="font-size: 150%;">未入仓包裹：</text>
        <view class="content-container">
            <view v-for="(data, index) in dialogContentData.slice().reverse()" :key="index">
                <view class="info-container">
                  <view class="info-row">
                    <view class="info-column">
                      <view class="info-item" :style="{ 'font-size': index === 0 ? '20px' : '20px' }">
                        <text>{{ data }}</text>
                      </view>
                    </view>
                  </view>
                </view>
            </view>
        </view>
        <view class="model-bottom-btn">
            <button class="left bottom-btn-cancel" type="" @click="handleCancel()" :loading="false">取消</button>
            <button class="right bottom-btn-confirm" type="primary" @click="dialogConfirm()" :loading="false">确认</button>
        </view>
      </view>
    </view> -->

      <showListDialog
        :dialog-modal-visible.sync="notInWarehouseVisible"
        :dialog-content-data="notInWarehouseData"
        title="当前订单未入仓包裹"
        @dialog-confirm="dialogConfirm"
      >
      </showListDialog>

      <showListDialog
        :dialog-modal-visible.sync="notOutWarehouseVisible"
        :dialog-content-data="notOutWarehouseData"
        title="当前订单未出仓包裹"
        @dialog-confirm="dialogConfirm"
      >
      </showListDialog>

      <!-- 底栏操作按钮 -->
      <view class="bottom-btn">
        <button class="left" type="primary" @click="back()" :loading="false">
          {{ $t("content.Finish") }}
        </button>
        <button
          class="right"
          type="primary"
          @click="continuousSweep()"
          :loading="false"
        >
          {{ $t("content.NextStep") }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import {
  nextStep,
  finish_task,
  get_parcel_info,
  parcel_get_ocean_order,
} from "../../commom/js/api.js";
import uniFab from "@/components/uni-fab/uni-fab.vue";
import { successAudio, failAudio } from "../../audio.js";
import UniDataSelect from "@/components/uni-data-select/uni-data-select";
import { eventBus } from "@/components/utils/eventBus.js";
import showListDialog from "@/components/detail/dialog.vue";

export default {
  components: {
    "uni-data-select": UniDataSelect,
    showListDialog,
  },
  data() {
    return {
      focusStatus: true,
      search_data: {
        master_num: "",
      },
      pattern: {
        color: "",
        selectedColor: "",
        backgroundColor: "",
        buttonColor: "",
      },
      horizontal: "left",
      vertical: "bottom",
      direction: "vertical",
      content: [
        {
          iconPath: "/static/search.png",
          selectedIconPath: "/static/search.png",
          text: this.$t("content.ScanCodeQuery"),
          active: true,
        },
      ],
      formData: {
        master_order_num: "",
      },
      option: {},
      loading: false,
      ocean_num_id: null,
      ocean_nums: [],
      showNotScannerParcel: false,
      oceanOrderOptions: [],
      notInWarehouseData: [],
      notOutWarehouseData: [],
      notInWarehouseVisible: false,
      notOutWarehouseVisible: false,
    };
  },
  computed: {
    // 获取当前步骤需要提交的表单信息
    requestData() {
      return {
        ...getApp().globalData.request,
        step: this.getRoutePath().step,
        ...this.formData,
      };
    },
  },
  onLoad: function (option) {
    console.log(option);
    this.option = option;
  },
  watch: {
    // 监听父组件传递的 focus prop 的变化
    focus(newValue) {
      this.inputFocus = newValue;
    },
  },
  methods: {
    trigger(data) {
      switch (data.index) {
        case 0:
          uni.navigateTo({
            url: "../search/index",
          });
          break;
        default:
          break;
      }
    },
    fabClick() {},
    // 校验
    validateForm() {
      return new Promise((resolve, reject) => {
        if (!this.formData.master_order_num) {
          this.failAudio();
          uni.showToast({
            title: this.$t("content.PScanInBoundOrderNo"),
            icon: "none",
            mask: true,
            duration: 1000,
          });
          resolve(false);
        }
        resolve(true);
      });
    },
    // 返回
    back() {
      const _this = this;
      uni.showModal({
        title: this.$t("content.Tips"),
        content: this.$t("content.EndAllShelvesJob"),
        success: function (res) {
          if (res.confirm) {
            // 调用接口
            finish_task(_this.filterRequest(_this.requestData))
              .then((res) => {
                console.log(res);
                if (res.code === 200) {
                  this.successAudio();
                  uni.showToast({
                    title: res.msg,
                    duration: 2000,
                    icon: "none",
                    mask: true,
                    position: "top",
                    success: function () {
                      setTimeout(() => {
                        uni.redirectTo({
                          url: "../index/index",
                        });
                      }, 1900);
                    },
                  });
                } else {
                  this.failAudio();
                  uni.showToast({
                    title:
                      res.msg || res.message || "fail request! please check!",
                    mask: true,
                    duration: 2000,
                    icon: "none",
                    position: "top",
                  });
                }
              })
              .catch((err) => {
                this.failAudio();
                console.error("请求失败", err);
              });
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
    async continuousSweep() {
      uni.pageScrollTo({
        scrollTop: 0, // 设置滚动的垂直位置
        duration: 300, // 滚动的动画时间（毫秒）
      });
      // 清空输入框
      // this.$set(this.formData.master_order_num, '')
      this.$set(this.formData, "master_order_num", "");
      this.focusStatus = false;
      // 重新聚焦
      this.$nextTick(() => {
        this.focusStatus = true;
      });
    },
    // 下一步
    async nextStep() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));
      // if(this.showNotScannerParcel) {
      //     uni.showToast({
      //     	title: '未选择海运提单',
      //     	icon: 'none'
      //     })
      //     return
      // }
      // if(this.ocean_num_id===null){
      //     if(this.ocean_nums.length > 0){
      //         this.ocean_num_id = this.ocean_nums[0]['id']
      //     } else {
      //         this.getOceanOrderNum()
      //     }
      // }

      const params = {
        parcel_num: this.formData.master_order_num,
      };
      console.log(this.formData.master_order_num);
      get_parcel_info(params)
        .then((res) => {
          console.log("POST请求成功", res, res.code);
          if (res.code === 200) {
            this.search_data = res;
            // 清空输入框
            this.$set(this.formData, "master_order_num", "");
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
          } else if (res.code === 405) {
            uni.showToast({
              title: "失败: " + res.msg || res.detail,
              icon: "none",
            });
            // this.getOceanOrderNum()
          } else {
            this.failAudio();
            uni.showToast({
              title: "失败: " + res.msg || res.detail,
              icon: "none",
            });
            // 清空输入框
            this.$set(this.formData, "master_order_num", "");
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
          }
        })
        .catch((err) => {
          console.error("POST请求失败", err);
          uni.showToast({
            title: "失败: " + err,
            icon: "none",
          });
          // 清空输入框
          // this.$set(this.formData.master_order_num, '')
          this.$set(this.formData, "master_order_num", "");
          this.focusStatus = false;
          // 重新聚焦
          this.$nextTick(() => {
            this.focusStatus = true;
          });
        });
    },

    // 监听回车
    handelEnter(str) {
      if (str === "post") {
        this.getOrder();
      } else if (str) {
        this.$nextTick(() => {
          this.$refs[str].focus = true;
        });
      }
    },

    setSelectOption(resData, err = 0) {
      console.log("resData-->", resData);
      if (resData.length === 0) {
        uni.showToast({
          title: "未查询到海运提单",
          icon: "none",
        });
        return;
      } else if (resData.length === 1) {
        this.ocean_num_id = resData[0]["id"];
      } else {
        this.ocean_nums = resData;
        this.showNotScannerParcel = true;
        // this.$nextTick(() => {
        //     // 获取 select 元素
        //     const selectElement = document.getElementById('select-ocean');
        //     selectElement.options.length = 0;
        //     // 动态生成选项并添加到 select 中
        //     resData.forEach((option, index) => {
        //         const optionElement = document.createElement('option');
        //         optionElement.value = option.id;
        //         optionElement.textContent = option.order_num;
        //         selectElement.appendChild(optionElement);
        //     })
        // });
        this.oceanOrderOptions = resData;
      }
    },
    getOceanOrderNum() {
      const params = {
        parcel_num: this.formData.master_order_num,
      };
      parcel_get_ocean_order(params)
        .then((res) => {
          if (res.code === 200) {
            this.setSelectOption(res.data.ocean_order);
          } else {
            console.log(res.msg);
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            this.ocean_num_id = null;
            this.failAudio();
          }
        })
        .catch((err) => {
          console.log(err);
          uni.showToast({
            title: err,
            icon: "none",
          });
          this.$set(this.formData, "fba_num", "");
          this.ocean_num_id = null;
          this.failAudio();
        });
    },
    handleCancel() {
      this.showNotScannerParcel = false;
    },
    async dialogConfirm() {
      // this.$nextTick(() => {
      //     const selectElement = document.getElementById('select-ocean');
      //     this.ocean_num_id = selectElement.value
      // })
      // this.showNotScannerParcel = false
      this.notInWarehouseVisible = false;
      this.notOutWarehouseVisible = false;
      // console.log('this.ocean_num_id-->', this.ocean_num_id)
      // this.nextStep()
    },
    viewNotInWarehouse() {
      // eventBus.$emit('sendData', { orderNum: '123456', dialogContentData: ['122', '233'] })
      // eventBus.$on('sendData', (data) => {
      //   this.orderNum = data.orderNum;
      //   this.dialogContentData = data.dialogContentData;
      // });
      // uni.navigateTo({
      //     url: `./notScanner`
      // })
      this.notInWarehouseData = this.search_data["not_in_warehouse_parcel"];
      // this.showNotScannerParcel = true
      this.notInWarehouseVisible = true;
    },
    viewNotOutWarehouse() {
      this.notOutWarehouseData = this.search_data["not_out_warehouse_parcel"];
      this.notOutWarehouseVisible = true;
    },
  },
};
</script>

<style scoped>
.item {
  margin-bottom: 10px; /* 调整每个字段之间的间距 */
}
.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh; /* 100%视窗高度，根据实际需要调整 */
}
/* 海运提单模态框样式 */
.form-inline .form-control {
  display: inline-block;
  width: auto;
  vertical-align: middle;
}
.form-control {
  min-width: 100px;
  display: block;
  width: 100%;
  height: 50px;
  padding: 6px 12px;
  font-size: 20px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.oceanModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-content {
  position: relative;
  background: #fff;
  padding: 20px;
  border-radius: 5px;
  width: 500px;
  height: 80%;
}
.content-container {
  height: 80%;
  overflow-y: auto;
}
.model-bottom-btn {
  position: absolute;
  bottom: 20px;
  width: 500px;
  height: 46px;
}
.bottom-btn-cancel,
.bottom-btn-confirm {
  position: absolute;
  display: inline-block;
  width: 50%;
}
.right {
  right: 0;
}
.left {
  left: 0;
}
/* 海运提单模态框样式结束 */
</style>
