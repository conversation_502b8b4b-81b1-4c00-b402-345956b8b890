<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
      <uni-nav-bar :shadow='false' :title="$t('content.Shelves')" @clickLeft="backToIndex()">
        <view slot="left">
          <i class="iconfont iconfanhui1 icon-left"/>
        </view>
        <view slot="right"> <text>{{$username}}</text> </view>
      </uni-nav-bar>
    </view>
    <!-- 操作栏 -->
    <view class="operation">
      <text style="font-size: 150%;">订单号：</text>
      <!-- view>
        <uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
                 @trigger="trigger" @fabClick="fabClick"></uni-fab>
      </view> -->

      <view v-if="notScannerParcel.length > 0">
        <text style="font-size: 150%;">未入仓包裹：</text>
        <view v-for="(data, index) in notScannerParcel.slice().reverse()" :key="index">
      	<view class="info-container">
      	  <view class="info-row">
      		<view class="info-column">
      		  <view class="info-item" :style="{ 'font-size': index === 0 ? '30px' : '20px' }">
      			<text>{{ data }}</text>
      		  </view>
      		</view>
      	  </view>
      	</view>
        </view>
      </view>
      
    <!-- 底栏操作按钮 -->
    <view class="bottom-btn">
      <button class="left" type="primary" @click="back()" :loading="false">返回上一页</button>
      <button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
    </view>
    
    </view>
  </view>
</template>

<script>
import { eventBus } from '@/components/utils/eventBus.js'
    
export default {
    data() {
        return {
            orderNum: null,
            notScannerParcel: [] // 保存返回的数据列表
        }
    },
    onLoad: function(option) { //option为object类型，会序列化上个页面传递的参数
        // this.getStep(option.type)
    },
    created() {
        eventBus.$on('sendData', (data) => {
          this.orderNum = data.orderNum;
          this.notScannerParcel = data.notScannerParcel;
        });
    },
    beforeDestroy() {
        eventBus.$off('sendData');  // 解除监听
    },
    methods: {
        back() {
            uni.navigateBack()
        }
    }
}
</script>

<style>
</style>