<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.QueryLocationInventory')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left" />
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>
		<!-- 操作栏 -->
		<view class="operation">
			<!-- 			<view class="scanner">
				<view class="scanner-label">扫描或输入SKU：</view>
				<input class="scanner-input" type="text"  @confirm="nextStep()" v-model="formData.sku_code" focus placeholder="请扫描或输入SKU" />
			</view> -->
			<!-- 底部显示栏 -->
			<view class="bottom-info">
				<!-- 				<view>{{type_name}}：{{formData.sku_code}}</view> -->
				<view>{{$t('content.LocationInformation')}}：{{locationinfo}}</view>
				<view class="list-table">
					<table>
						<th>
						<td>sku</td>
						<td>{{$t('content.Quantity')}}</td>
						</th>
						<template v-if="formData.record.length===0">
							<tr>
								{{$t('content.NoData')}}
							</tr>
						</template>
						<template v-else>
							<tr v-for="item in formData.record" :key="item.id">
								<td>{{item.sku_code}}</td>
								<td>{{item.available_stock}}</td>
							</tr>
						</template>

					</table>
				</view>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false" style="width: 100%;">{{$t('content.Return')}}</button>
				<!-- <button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.Confirm')}}</button> -->
			</view>
		</view>
	</view>

</template>

<script>
	import {
		position_search
	} from '../../commom/js/api.js'
	import { successAudio,failAudio } from '../../audio.js'
	export default {
		data() {
			return {
				formData: {
					sku_code: '',
					sku_name: '',
					record: [],
					type_name: '',
					locationinfo: ''
				},
				option: {},
				loading: false
			}
		},
		onLoad: function(option) {
			console.log(option);
			this.option = option;
			this.locationinfo = this.option.loc
		},
		created() {
			this.loading = true
			uni.showLoading({
				title: this.$t('content.InRequest'),
				mask: true
			});
			//const location = this.$route.query.loc;
			const p = {
				position_num: this.option.loc
			}
			position_search(p).then(res => {
				console.log(res)
				if (res.code === 200) {
					this.successAudio()
					this.formData.sku_name = res.data.sku_name
					this.formData.record = res.data.record
				} else {
					this.failAudio()
					uni.showToast({
						title: res.detail || res.message || 'fail request! please check!',
						mask: true,
						duration: 2000,
						icon: 'none',
						position: 'top'
					});
				}
			})
			uni.hideLoading();
			this.loading = false
		},
		methods: {
			back() {
				uni.navigateBack({
					delta: 1 // 返回上一级页面。
				})				
			}
		}
	}
</script>

<style>
</style>
