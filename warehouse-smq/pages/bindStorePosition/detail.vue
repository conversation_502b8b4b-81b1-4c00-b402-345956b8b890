<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
      <uni-nav-bar
        :shadow="false"
        :title="$t('content.BindStorePositionDetail')"
        @clickLeft="backToTaskStock()"
      >
        <view slot="left">
          <i class="iconfont iconfanhui1 icon-left" />
        </view>
        <view slot="right">
          <text>{{ $username }}</text>
        </view>
      </uni-nav-bar>
    </view>

    <!-- 操作栏 -->
    <view class="operation">
      <view class="scanner">
        <view class="detail-view">
          <view class="detail-item">
            <text class="detail-label">箱号：</text>
            <!-- 后5位字体放大处理 -->
            <text class="detail-value">
              <span v-if="data.parcel_num && data.parcel_num.length > 5">
                {{ data.parcel_num.slice(0, -5) }}
                <span style="font-size: 150%">
                  {{ data.parcel_num.slice(-5) }}
                </span>
              </span>
              <span v-else>{{ data.parcel_num }}</span>
            </text>
          </view>
          <view class="detail-item">
            <text class="detail-label">订单号：</text>
            <!-- 后5位字体放大处理 -->
            <text class="detail-value">
              <span v-if="data.order_num && data.order_num.length > 5">
                {{ data.order_num.slice(0, -5) }}
                <span style="font-size: 150%">
                  {{ data.order_num.slice(-5) }}
                </span>
              </span>
              <span v-else>{{ data.order_num }}</span>
            </text>
          </view>
        </view>
        <text style="font-size: 160%">入库数量：</text>
        <input
          class="scanner-input uni-input"
          cursor-spacing="10"
          ref="scan_carton"
          type="number"
          placeholder="请输入数量"
          :focus="scan_carton_focus"
          @confirm="handleInput1()"
          v-model.number="formData.scan_carton"
        />
        <text style="font-size: 160%">扫描库位号：</text>
        <input
          class="scanner-input uni-input"
          cursor-spacing="10"
          ref="store_position"
          type="text"
          placeholder="请扫描库位号"
          :focus="store_position_focus"
          @confirm="handleInput2()"
          v-model="formData.store_position"
        />
        <view class="detail-value" style="margin-top: 20upx">
          <view v-if="data.order_num">
            <view
              v-for="(position, index) in data.position_num"
              :key="index"
              style="display: flex; justify-content: space-between"
            >
              <text>库位: {{ position.position_num }} </text>
              <text>数量: {{ position.scan_carton }}</text>
            </view>
          </view>
        </view>
        <!-- <view class="base-margin-top">
          <text class="base-h6 base-secondary-text">盘点详情</text>
        </view>
        <view style="height: 28rpx"></view> -->
      </view>
    </view>

    <view class="fotter">
      <button class="floating-button" @click="back()">
        {{ $t("content.Finish") }}
      </button>
    </view>
  </view>
</template>

<script>
import {
  fbaTakeStockAddDetail,
  fbaTakeStockListHistoryJob,
  fbaTakeStockfinishJob,
  bind_store_position,
} from "../../commom/js/api.js";

export default {
  data() {
    return {
      scan_carton_focus: true,
      store_position_focus: false,
      formData: {
        scan_carton: null,
        store_position: "",
      },
      scanResults: [],
      data: {},
    };
  },
  onLoad(options) {
    const params = options.data;
    if (params) {
      // 将字符串转换回对象
      this.data = JSON.parse(decodeURIComponent(params));
      console.log("this.data---------", this.data);
    }
    console.log(`options:${JSON.stringify(this.data)}`);
    this.scan_carton_focus = false;
    // 重新聚焦
    this.$nextTick(() => {
      this.scan_carton_focus = true;
    });
  },
  methods: {
    async handleInput1() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));
      this.store_position_focus = false;
      this.$nextTick(() => {
        this.store_position_focus = true;
      });
    },
    async handleInput2() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 500));

      if (
        !this.formData.store_position ||
        this.formData.scan_carton == null ||
        this.formData.scan_carton === 0
      ) {
        uni.showToast({
          title: "请输入正确的库位号/数量",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      const params = {
        scan_carton: parseInt(this.formData.scan_carton, 10) || 0,
        store_position: this.formData.store_position,
        order_num: this.data.order_num,
      };
      console.log("truckfbaorder-params: ", params);

      bind_store_position(params)
        .then((res) => {
          if (res.code === 200) {
            this.$set(this.formData, "scan_carton", null);
            this.$set(this.formData, "store_position", "");
            this.successAudio();
            uni.showToast({
              title: res.msg,
              duration: 2000,
              icon: "none",
              mask: true,
              position: "top",
              success: function () {
                setTimeout(() => {
                  uni.redirectTo({
                    url: "./index",
                  });
                });
              },
            });
          } else {
            console.log(res.msg);
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            // 这里的异常有两种情况：第一个输入框不对、第二个输入框不对
            // 清空输入框
            this.$set(this.formData, "scan_carton", null);
            this.$set(this.formData, "store_position", "");
            // this.ocean_num_id = null
            this.failAudio();
          }
        })
        .catch((err) => {
          console.error("请求失败", err);
          // 清空输入框
          this.$set(this.formData, "scan_carton", null);
          this.$set(this.formData, "store_position", "");
          // this.ocean_num_id = null
          this.failAudio();
        });
      this.scan_carton_focus = false;
      // 重新聚焦
      this.$nextTick(() => {
        this.scan_carton_focus = true;
      });
    },
    backToTaskStock() {
      uni.navigateTo({
        url: `./index`,
      });
    },
  },
};
</script>

<style>
.scan-results {
  margin-top: 20px;
}
.scan-results view {
  padding: 10px;
  border-bottom: 1px solid #ccc;
}
.base-margin-top {
  margin-top: 10rpx;
}
.base-padding-x {
  padding: 0 25rpx;
}
.base-h6 {
  font-size: 25rpx;
}
.base-secondary-text {
   {
    color: rgba(68, 68, 68, 1);
  }
}
/* 盘点详情视图样式 */
.detail-view {
  display: flex;
  flex-direction: column;
  padding: 10rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  margin-top: 2rpx;
}

/* 盘点详情项样式 */
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1px solid #eaeaea;
}

/* 最后一个详情项无底部边框 */
.detail-item:last-child {
  border-bottom: none;
}

/* 详情标签样式 */
.detail-label {
  color: #666;
  font-size: 28rpx;
}

/* 详情值样式 */
.detail-value {
  color: #000;
  font-size: 30rpx;
  font-weight: bold;
}
/* 浮动底部按钮样式 */
.floating-button {
  position: fixed; /* 固定定位 */
  bottom: 20rpx; /* 距离底部20rpx */
  left: 50%; /* 距离左侧50%，实现水平居中 */
  transform: translateX(-50%); /* 向左移动50%，实现精确居中 */
  background-color: #55aaff; /* 按钮背景颜色 */
  color: white; /* 按钮文字颜色 */
  padding: 2rpx; /* 按钮内边距 */
  border-radius: 10rpx; /* 按钮边框圆角，非圆形 */
  width: 90%; /* 按钮宽度 */
  height: 80rpx; /* 按钮高度 */
  font-size: 30rpx; /* 文字大小 */
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2); /* 按钮阴影 */
  text-align: center; /* 文字居中 */
  line-height: 80rpx; /* 行高与按钮高度一致，实现文字垂直居中 */
}
</style>
