<template>
  <view>
    <!-- 顶部栏 -->
    <view class="step">
      <uni-nav-bar
        :shadow="false"
        :title="$t('content.Picking')"
        @clickLeft="backToIndex()"
      >
        <view slot="left">
          <i class="iconfont iconfanhui1 icon-left" />
        </view>
        <view slot="right">
          <text>{{ $username }}</text>
        </view>
      </uni-nav-bar>
    </view>

    <!-- 操作栏 -->
    <view class="operation">
      <view class="scanner">
        <text style="font-size: 200%">扫描箱号/系统箱号：</text>
        <input
          class="scanner-input uni-input"
          cursor-spacing="10"
          type="text"
          ref="parcel_num"
          :focus="focusStatus"
          :placeholder="$t('content.PScanPickingOrderNo')"
          @confirm="handleInput1()"
          v-model="formData.master_order_num"
        />
        <div style="font-size: 150%">或</div>
        <text style="font-size: 200%">输入提单号/柜号：</text>
        <input
          class="scanner-input uni-input"
          cursor-spacing="10"
          type="text"
          ref="cabinet_no"
          :focus="cabinet_no_focus"
          :placeholder="$t('content.PInputCabinetNo')"
          @confirm="nextStep()"
          v-model="formData.cabinet_no"
        />
      </view>
      <view>
        <uni-fab
          :pattern="pattern"
          :content="content"
          :horizontal="horizontal"
          :vertical="vertical"
          :direction="direction"
          @trigger="trigger"
          @fabClick="fabClick"
        >
        </uni-fab>
      </view>

      <!-- 时间日期选择器 -->
      <datetime-picker
        v-if="uploadImageShow"
        v-model="actual_loading_time"
        label="实际装柜时间"
        label-size="160%"
        placeholder="请选择装柜时间"
      />

      <view class="image-container">
        <!-- 判断 img_data 是否有数据 -->
        <view v-if="img_data.length > 0">
          <img
            v-for="(image, index) in img_data"
            :key="index"
            class="arrival-image"
            :src="getImageUrl(image.img_url)"
            mode="aspectFit"
          />
        </view>
        <view v-else>
          <text
            style="text-align: center; margin-top: 10px; margin-bottom: 10px"
            >暂无图片</text
          >
        </view>
      </view>

      <!-- 拍照上传按钮 -->
      <button v-if="uploadImageShow" @click="uploadPhoto">
        上传海运单图片
      </button>

      <!-- 模态框: 选择海运提单 -->
      <view class="oceanModal" v-if="showSelectOceanOrder">
        <view class="modal-content">
          <!-- <div class="custom-select">
                <text style="font-size: 160%;">请选择海运提单：</text>
                <select id='select-ocean' class="form-control" name="name">
                </select>
            </div> -->
          <uni-data-select
            v-model="ocean_num_id"
            :localdata="oceanOrderOptions"
            placeholder="请选择海运提单"
            @change=""
          ></uni-data-select>
          <view class="model-bottom-btn">
            <button
              class="left bottom-btn-cancel"
              type=""
              @click="handleCancel()"
              :loading="false"
            >
              取消
            </button>
            <button
              class="right bottom-btn-confirm"
              type="primary"
              @click="handleConfirm()"
              :loading="false"
            >
              确认
            </button>
          </view>
        </view>
      </view>

      <!-- 底栏操作按钮 -->
      <view class="bottom-btn">
        <button class="left" type="primary" @click="back()" :loading="false">
          {{ $t("content.Finish") }}
        </button>
        <button
          class="right"
          type="primary"
          @click="nextStep()"
          :loading="false"
        >
          {{ $t("content.NextStep") }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import {
  nextStep,
  finish_task,
  pda_get_img,
  pda_save_img,
  parcel_get_ocean_order,
} from "../../commom/js/api.js";
import uniFab from "@/components/uni-fab/uni-fab.vue";
import { successAudio, failAudio } from "../../audio.js";
import base from "@/commom/js/config.js";
import UniDataSelect from "@/components/uni-data-select/uni-data-select";
import DatetimePicker from "@/components/datetime-picker/datetime-picker.vue";
export default {
  components: {
    "uni-data-select": UniDataSelect,
    DatetimePicker,
  },
  data() {
    return {
      focusStatus: true,
      cabinet_no_focus: false,
      img_data: [],
      actual_loading_time: "",
      pattern: {
        color: "",
        selectedColor: "",
        backgroundColor: "",
        buttonColor: "",
      },
      horizontal: "left",
      vertical: "bottom",
      direction: "vertical",
      content: [
        {
          iconPath: "/static/search.png",
          selectedIconPath: "/static/search.png",
          text: this.$t("content.ScanCodeQuery"),
          active: true,
        },
      ],
      formData: {
        master_order_num: "",
        cabinet_no: "",
      },
      option: {},
      loading: false,
      ocean_num_id: null,
      ocean_nums: [],
      showSelectOceanOrder: false,
      uploadImageShow: false,
      oceanOrderOptions: [],
    };
  },
  computed: {
    // 获取当前步骤需要提交的表单信息
    requestData() {
      return {
        ...getApp().globalData.request,
        step: this.getRoutePath().step,
        ...this.formData,
      };
    },
  },
  onLoad: function (option) {
    console.log(option);
    this.option = option;
  },
  watch: {
    // 监听父组件传递的 focus prop 的变化
    focus(newValue) {
      this.inputFocus = newValue;
    },
  },
  methods: {
    handleDateTimeChange(value) {
      console.log("选择的日期时间：", value);
      this.actual_loading_time = value;
    },
    trigger(data) {
      switch (data.index) {
        case 0:
          uni.navigateTo({
            url: "../search/index",
          });
          break;
        default:
          break;
      }
    },
    fabClick() {},

    // 校验
    validateForm() {
      return new Promise((resolve, reject) => {
        if (!this.formData.master_order_num) {
          this.failAudio();
          uni.showToast({
            title: this.$t("content.PScanPickingOrderNo"),
            icon: "none",
            mask: true,
            duration: 2000,
          });
          resolve(false);
        }
        resolve(true);
      });
    },

    // 返回
    back() {
      uni.redirectTo({
        url: "../index/index",
      });
    },

    // 监听回车
    handelEnter(str) {
      if (str === "post") {
        this.getOrder();
      } else if (str) {
        this.$nextTick(() => {
          this.$refs[str].focus = true;
        });
      }
    },
    // 拼接图片url
    getImageUrl(url) {
      return base.BASE_URL + "/media" + url.split("media")[1];
    },
    async handleInput1() {
      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 200));
      this.cabinet_no_focus = false;
      this.$nextTick(() => {
        this.cabinet_no_focus = true;
      });
    },
    // 获取当前时间格式化方法
    getCurrentTime() {
      const now = new Date();
      // 格式化为 YYYY-MM-DD HH:mm:ss
      return `${now.getFullYear()}-${(now.getMonth() + 1)
        .toString()
        .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")} ${now
        .getHours()
        .toString()
        .padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now
        .getSeconds()
        .toString()
        .padStart(2, "0")}`;
    },
    // 下一步
    async nextStep() {
      if (
        this.formData.cabinet_no === "" &&
        this.formData.master_order_num === ""
      ) {
        uni.showToast({
          title: "请扫描箱号或输入柜号",
          icon: "none",
        });
        this.focusStatus = false; // 重置焦点状态
        this.$nextTick(() => {
          this.focusStatus = true; // 下一帧重新聚焦
        });
        return;
      }
      if (!this.actual_loading_time) {
          // 先设置装柜时间
          this.actual_loading_time = this.getCurrentTime();
      }

      // 等待500毫秒，可以根据实际情况调整时间
      await new Promise((resolve) => setTimeout(resolve, 200));
      if (this.showSelectOceanOrder) {
        uni.showToast({
          title: "未选择海运提单",
          icon: "none",
        });
        return;
      }
      if (this.ocean_num_id === null) {
        if (this.ocean_nums.length === 1) {
          this.ocean_num_id = this.ocean_nums[0]["value"];
          console.log("这是个啥0-->", this.ocean_num_id);
          this.pda_get_img_handle();
        } else {
          this.getOceanOrderNum();
        }
      } else {
        this.pda_get_img_handle();
      }
    },
    pda_get_img_handle() {
      const params = {
        parcel_num: this.formData.master_order_num,
        ocean_num_id: this.ocean_num_id,
        cabinet_no: this.formData.cabinet_no,
      };
      console.log("这是个啥1-->", params);
      console.log("这是个啥2-->", this.ocean_num_id);
      pda_get_img(params)
        .then((res) => {
          // console.log('smres-->', res)
          if (res.code === 200) {
            // this.failAudio()
            uni.showToast({
              title: res.msg,
              icon: "none",
              mask: true,
              duration: 500,
            });
            this.img_data = res.images;
            this.actual_loading_time = res.actual_loading_time;
            // console.log('图片-->', this.img_data)
            // // 清空输入框
            // this.$set(this.formData, 'master_order_num', '')
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
            this.uploadImageShow = false;
            this.$nextTick(() => {
              this.uploadImageShow = true;
            });
          } else if (res && res.code === 405) {
            uni.showToast({
              title: "获取提单图片失败: " + "请先选择海运提单",
              icon: "none",
            });
            // this.getOceanOrderNum();
            this.uploadImageShow = true;
            this.$nextTick(() => {
              this.uploadImageShow = false;
            });
            this.img_data = [];
          } else {
            this.failAudio();
            uni.showToast({
              title: res.msg,
              icon: "none",
              mask: true,
              duration: 500,
            });
            this.search_data = res;

            // // 清空输入框
            // this.$set(this.formData, 'master_order_num', '')
            this.focusStatus = false;
            // 重新聚焦
            this.$nextTick(() => {
              this.focusStatus = true;
            });
            this.uploadImageShow = true;
            this.$nextTick(() => {
              this.uploadImageShow = false;
            });
            this.img_data = [];
          }
        })
        .catch((err) => {
          console.error("POST请求失败", err);
          this.failAudio();
          uni.showToast({
            title: err,
            icon: "none",
            mask: true,
            duration: 500,
          });
          // 清空输入框
          // this.$set(this.formData, 'master_order_num', '')
          this.focusStatus = false;
          this.$nextTick(() => {
            this.focusStatus = true;
          });
          this.uploadImageShow = true;
          this.$nextTick(() => {
            this.uploadImageShow = false;
          });
          this.img_data = [];
        });
    },

    setSelectOption(resData, err = 0) {
      console.log("resData-->", resData);
      if (resData.length === 0) {
        uni.showToast({
          title: "未查询到海运提单",
          icon: "none",
        });
        this.uploadImageShow = true;
        this.$nextTick(() => {
          this.uploadImageShow = false;
        });
        return;
      } else if (resData.length === 1) {
        console.log("resData001-->", resData);
        this.ocean_num_id = resData[0]["value"];
        console.log("this.ocean_num_id-->", this.ocean_num_id);
        // this.$set(this, 'ocean_num_id', resData[0]["value"])
        this.pda_get_img_handle();
      } else {
        this.ocean_nums = resData;
        this.showSelectOceanOrder = true;
        // this.$nextTick(() => {
        //     // 获取 select 元素
        //     const selectElement = document.getElementById('select-ocean');
        //     selectElement.options.length = 0;
        //     // 动态生成选项并添加到 select 中
        //     resData.forEach((option, index) => {
        //         const optionElement = document.createElement('option');
        //         optionElement.value = option.id;
        //         optionElement.textContent = option.order_num;
        //         selectElement.appendChild(optionElement);
        //     })
        // });
        this.oceanOrderOptions = resData;
      }
    },

    getOceanOrderNum() {
      const params = {
        parcel_num: this.formData.master_order_num,
        cabinet_no: this.formData.cabinet_no,
      };
      parcel_get_ocean_order(params)
        .then((res) => {
          if (res.code === 200) {
            this.setSelectOption(res.data.ocean_order);
          } else {
            console.log(res.msg);
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
            this.ocean_num_id = null;
            this.failAudio();
            this.uploadImageShow = true;
            this.$nextTick(() => {
              this.uploadImageShow = false;
            });
          }
        })
        .catch((err) => {
          console.log(err);
          uni.showToast({
            title: err,
            icon: "none",
          });
          this.$set(this.formData, "fba_num", "");
          this.ocean_num_id = null;
          this.failAudio();
          this.uploadImageShow = true;
          this.$nextTick(() => {
            this.uploadImageShow = false;
          });
        });
    },
    handleCancel() {
      this.showSelectOceanOrder = false;
    },
    async handleConfirm() {
      // this.$nextTick(() => {
      //     const selectElement = document.getElementById('select-ocean');
      //     this.ocean_num_id = selectElement.value
      // })
      this.showSelectOceanOrder = false;
      this.nextStep();
    },
    // 点击按钮上传图片
    async uploadPhoto() {
      if (this.ocean_num_id === null) {
        if (this.ocean_nums.length === 1) {
          this.ocean_num_id = this.ocean_nums[0]["value"];
        } else {
          this.getOceanOrderNum();
        }
      }
      const params = {
        parcel_num: this.formData.master_order_num,
        cabinet_no: this.formData.cabinet_no,
        ocean_num_id: this.ocean_num_id,
        actual_loading_time: this.actual_loading_time,
      };

      // 调用PDA拍照方法
      try {
        const imageInfo = await uni.chooseImage({
          count: 1,
          sizeType: ["original", "compressed"],
          sourceType: ["camera"],
        });
        console.log("imageInfo2-->", imageInfo[1].tempFilePaths);
        // 获取选中图片的本地路径
        const tempFilePath = imageInfo[1].tempFilePaths[0];

        // 执行拍照上传逻辑
        await this.uploadImage(tempFilePath, params);
      } catch (error) {
        console.error("拍照失败或取消", error);
      }
    },

    async uploadImage(filePath, params) {
      const uploadParams = {
        filePath,
        name: "file",
        formData: params,
      };

      try {
        const res = await uni.uploadFile({
          url: `${base.BASE_URL}/api/fbaBigParcels/pda_save_img/`, // 替换成你的实际上传图片的接口地址
          filePath: uploadParams.filePath,
          name: uploadParams.name,
          formData: uploadParams.formData,
        });
        const resData = JSON.parse(res[1].data);
        if (resData && resData.code === 200) {
          // 处理上传成功的逻辑
          console.log("上传成功", res[1]);
          uni.showToast({
            title: resData.msg,
            icon: "none",
          });
          await this.nextStep();
        } else if (resData && resData.code === 405) {
          uni.showToast({
            title: "上传失败: " + "请先选择海运提单",
            icon: "none",
          });
          this.getOceanOrderNum();
        } else {
          uni.showToast({
            title: "上传失败: " + resData.msg || resData.detail,
            icon: "none",
          });
        }
      } catch (error) {
        // 处理上传失败的逻辑
        console.error("上传失败", error);
        uni.showToast({
          title: "上传失败: " + error,
          icon: "none",
        });
        throw error; // 将错误抛出，让调用者知道上传失败
      }
    },
  },
};
// uni.request({
// 	url: 'http://cshm.mz56.com/api/fbaBigParcels/pda_get_img/',
// 	// url: 'http://localhost:8000/api/fbaBigParcels/pda_get_img/',
// 	method: 'GET',
// 	data: {
// 		parcel_num: this.formData.master_order_num,
// 	},
// 	success: (res) => {
// 		console.log('POST请求成功', res);
// 		if (res.data.code === 400) {
// 			this.failAudio()
// 			uni.showToast({
// 				title: res.data.msg,
// 				icon: 'none',
// 				mask: true,
// 				duration: 500
// 			});

// 			// 清空输入框
// 			// this.$set(this.formData, 'master_order_num', '' )
// 			this.formData.master_order_num=''
// 			this.focusStatus=false
// 			// 重新聚焦
// 			this.$nextTick(() => {
// 				this.focusStatus=true
// 			});
// 		} else {
// 			this.img_data = res.data.images;

// 			// 清空输入框
// 			// this.$set(this.formData, 'master_order_num', '' )
// 			this.$set(this.formData, 'master_order_num', '')
// 			this.focusStatus=false
// 			// 重新聚焦
// 			this.$nextTick(() => {
// 				this.focusStatus=true
// 			});
// 		}
// 	},
// 	fail: (err) => {
// 		console.error('POST请求失败', err);
// 		// 清空输入框
// 		this.$set(this.formData, 'master_order_num', '' )
// 		// this.formData.master_order_num=''
// 		this.focusStatus=false
// 		// 重新聚焦
// 		this.$nextTick(() => {
// 			this.focusStatus=true
// 		});
// 	},
// })
// 		},
// 	}
// }
</script>

<style>
/* 海运提单模态框样式 */
.form-inline .form-control {
  display: inline-block;
  width: auto;
  vertical-align: middle;
}
.form-control {
  min-width: 100px;
  display: block;
  width: 100%;
  height: 50px;
  padding: 6px 12px;
  font-size: 20px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.oceanModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-content {
  position: relative;
  background: #fff;
  padding: 20px;
  border-radius: 5px;
  width: 500px;
  height: 300px;
}
.model-bottom-btn {
  position: absolute;
  bottom: 20px;
  width: 500px;
  height: 46px;
}
.bottom-btn-cancel,
.bottom-btn-confirm {
  position: absolute;
  display: inline-block;
  width: 50%;
}
.right {
  right: 0;
}
.left {
  left: 0;
}
/* 海运提单模态框样式结束 */
</style>
