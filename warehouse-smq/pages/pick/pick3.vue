<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.Picking')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left" />
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>

		<!-- 操作栏 -->
		<view class="operation relative">
			<!-- 顶部显示栏 -->
			<view class="top-info">
				<view class="spacing">{{$t('content.PickingOrderNo')}}：{{requestData.master_order_num}}</view>
				<view class="spacing" @click="checkSkuInventory(requestData.target_location)">{{$t('content.Location')}}：{{requestData.target_location}}</view>
				<view class="spacing" @click="checkInventory(requestData.code)">SKUNO：{{requestData.code}}</view>
				<view class="spacing" @click="checkInventory(requestData.customer_code)">{{$t('content.MCode')}}：<br> <text style="font-size: 38rpx;">{{requestData.customer_code}}</text></view>
				<view class="spacing" style='margin-top:30px'>{{$t('content.NumberOfPiecesToBePicked')}}：{{requestData.expected_pick}}</view>
			</view>
			<prompt ref="prompt" @onConfirm="onConfirm" @onCancel="onCancel" title="确定短拣?"  placeholder="请输入短拣密码"></prompt>
			<view class="scanner">
				<text class="scanner-label">{{$t('content.ScanSKUBarcode')}}：</text>
				<input @click="touch" @blur="blur_input" class="scanner-input uni-input" cursor-spacing="10" type="text" focus
				 :placeholder="$t('content.PScanSKUBarcode')" @confirm="nextStep()" v-model="formData.sku_code" />
			</view>
			<!-- 底部信息 -->
			<view class="bottom-info">
				<!-- <view>实际已拣选件数: {{requestData.actual_pick}}</view> -->
				<view>{{$t('content.NumberOfPickedPieces')}}: {{actual_pick}}</view>
				<!-- <input class="pick_num" type="number" placeholder="请输入已拣选数量" @confirm="nextStep(true)" v-model.number="formData.actual_pick" /> -->
				<view class="sku_img">
					<img style="width: 200rpx; height: 200rpx; background-color: #eeeeee;" :mode="'aspectFill'" :src="requestData.img" @error="imageError"> </img>
					<view>{{$t('content.Length')}}: {{requestData.length}} {{$t('content.Width')}}: {{requestData.width}}
						{{$t('content.Height')}}: {{requestData.height}}</view>
				</view>
			</view>
			<view>
				<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
				 @trigger="trigger" @fabClick="fabClick"></uni-fab>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="change_car()" :loading="false">{{$t('content.ReplacePickingCar')}}</button>
				<button class="left" type="primary" @click="skip()" :loading="false">{{$t('content.Skip')}}</button>
				<button class="right" type="primary" @click="short_pick()" :loading="false">{{$t('content.ShortPicking')}}</button>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		nextStep,
		pick_ship_pick_order,
		pick_short_pick_order,
		getStep
	} from '../../commom/js/api.js'
	import uniFab from '@/components/uni-fab/uni-fab.vue';
	import {
		successAudio,
		failAudio
	} from '../../audio.js'
	import prompt from './prompt/prompt.vue';
	export default {
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [{
					iconPath: '/static/search.png',
					selectedIconPath: '/static/search.png',
					text: this.$t('content.ScanCodeQuery'),
					active: true
				}, ],
				formData: {
					sku_code: null,
					actual_pick: 0
				},
				actual_pick: 0,
				option: {},
				loading: false,
				height: 0
			}
		},
		computed: {
			// 获取当前步骤需要提交的表单信息
			requestData() {
				return {
					...getApp().globalData.request,
					step: this.getRoutePath().step,
					...this.formData,
				}
			}
		},
		created() {

		},
		components: {
			prompt,
		},
		onLoad: function(option) {
			this.option = option;
			let screenHeight = uni.getSystemInfoSync().windowHeight;
			let statusHeight = uni.getSystemInfoSync().statusBarHeight;
			this.height = screenHeight;
		},
		watch: {

		},
		mounted() {
			this.formData.actual_pick = getApp().globalData.request.actual_pick
			this.actual_pick = getApp().globalData.request.actual_pick
		},
		methods: {
			trigger(data) {
				switch (data.index) {
					case 0:
						uni.navigateTo({
							url: '../search/index'
						})
						break;
					default:
						break;
				}
			},
			imageError(e) {
				// console.log("hi")
			},
			fabClick() {},
			touch(e) {
				let y = e.detail.y;
				if (y <= this.height) {
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 0,
					});
				}
			},
			// 失去焦点的时候让页面回正
			blur_input() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0,
				});
			},
			// 校验
			validateForm() {
				return new Promise((resolve, reject) => {
					if (!this.formData.sku_code) {
						this.failAudio()
						uni.showToast({
							title: this.$t('content.PScanSKUBarcode'),
							icon: 'none',
							mask: true,
							duration: 2000
						});
						resolve(false)
					}
					resolve(true)
				})
			},
			// 跳过
			skip() {
				const _this = this
				uni.showModal({
					title: this.$t('content.Tips'),
					content: this.$t('content.SkipCurrentSku'),
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: 'InRequest',
								mask: true
							});
							pick_ship_pick_order(_this.filterRequest(_this.requestData)).then(res => {
								if (res.code && res.data.step) {
									// this.successAudio()
									getApp().globalData.request = { ...getApp().globalData.request,
										...res.data
									}
									uni.redirectTo({
										url: _this.getRoutePath().basicPath + res.data.step
									})

								} else {
									// this.failAudio()
									uni.showToast({
										title: res.detail || res.message || 'fail request! please check!',
										mask: true,
										duration: 2000,
										icon: 'none',
										position: 'top'
									});
								}
								uni.hideLoading();
							})
						}
					}
				});
			},
			change_car() {
				const _this = this
				uni.showModal({
					title: this.$t('content.Tips'),
					content: this.$t('content.SureReplaceCar'),
					success: function(res) {

						getApp().globalData.request = { ...getApp().globalData.request,
							...res.data
						}
						uni.redirectTo({
							url: _this.getRoutePath().basicPath + 4
						})

					}
				});
			},
			// 短拣
			short_pick() {
				this.$refs.prompt.show();
				// uni.showModal({
				// 	title: this.$t('content.Tips'),
				// 	content: '确定短拣',
				// 	success: function(res) {
				// 		console.log(res.confirm)
				// 		if (res.confirm) {
							
				// 		}
				// 	}
				// });
			},
			prompt: function() {
				this.$refs.prompt.show();
			},
			onConfirm: function(e) {
				// console.log(e);
				const _this = this
				let _cost = e;
				if (_cost == '123456') {
					
					uni.showLoading({
						title: '请求中',
						mask: true
					});
					pick_short_pick_order(_this.filterRequest(_this.requestData)).then(res => {
						if (res.code && res.data.step) {
							uni.showToast({
								title: '已将该SKU已拣选的数量放回库位库存中，并已生成该SKU调整单',
								mask: true,
								duration: 2000,
								icon: 'none',
								position: 'top',
								success: function() {
									getApp().globalData.request = { ...getApp().globalData.request,
										...res.data
									}
									uni.redirectTo({
										url: _this.getRoutePath().basicPath + res.data.step
									})
								}
							});
						} else if (res.step && res.step === '1') {
							uni.showToast({
								title: '已将该SKU已拣选的数量放回库位库存中，并已生成该SKU调整单',
								mask: true,
								duration: 2000,
								icon: 'none',
								position: 'top',
								success: function() {
									const type = getApp().globalData.request.type
									getApp().globalData.request = {
										type
									}
									uni.redirectTo({
										url: _this.getRoutePath().basicPath + '1'
									})
								}
							});
						} else {
							uni.showToast({
								title: res.detail || res.message || 'fail request! please check!',
								mask: true,
								duration: 2000,
								icon: 'none',
								position: 'top'
							});
						}
						uni.hideLoading();
					})
					
				} else {
					this.$refs.prompt.hide();
					uni.showToast({
						title: '你输入的密码不正确'+_cost,
						mask: true,
						duration: 2000,
						icon: 'none',
						position: 'top'
					});
				}
			},
			onCancel: function() {
				this.$refs.prompt.hide();
				this.$refs.prompt.cost = '';
			},
			// 返回
			back() {
				uni.redirectTo({
					url: this.getRoutePath().lastPath
				})
			},
			// 下一步
			async nextStep(fill) {
				if (fill) {
					if (!this.formData.sku_code) {
						this.formData.sku_code = this.requestData.code
					}
				}
				if (this.loading) return
				this.loading = true
				uni.showLoading({
					title: this.$t('content.InRequest'),
					mask: true
				});
				let flag = await this.validateForm()
				uni.hideLoading();
				this.loading = false
				if (!flag) return
				nextStep(this.filterRequest(this.requestData)).then(res => {
					console.log(res);
					if (res.code === 200) {
						this.successAudio()
						if (res.data.step != 1) {
							getApp().globalData.request = { ...getApp().globalData.request,
								...res.data
							}
						} else {
							const type = getApp().globalData.request.type
							getApp().globalData.request = {
								type
							}
						}
						uni.showToast({
							title: this.$t('content.PickingSucceeded'),
							mask: true,
							duration: 500,
							icon: 'none',
							position: 'top'
						});
						uni.redirectTo({
							url: this.getRoutePath().basicPath + res.data.step
						})
					} else if (res.code === 996) {
						this.failAudio()
						console.log(res.message)
						// getStep({
						// 	type: 'pick'
						// }).then(res => {
						// 	console.log(res);
						// 	if (res.code === 200) {
						// 		getApp().globalData.request = { ...getApp().globalData.request,
						// 			...res.data.params
						// 		}
						// 		this.getStepRouter(res.data.step)
						// 	}
						// 	uni.hideLoading();
						// }).catch(err => {
						// 	uni.hideLoading();
						// })

					} else {
						this.failAudio()
						uni.showToast({
							title: res.detail || res.message || 'fail request! please check!',
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
						this.formData.sku_code = ''
					}
				})
			},
			checkInventory(sku) {
				uni.navigateTo({
					url: '../stock/index?sku=' + sku + ''
				})
			},
			checkSkuInventory(location) {
				uni.navigateTo({
					url: '../targetlocation/index?loc=' + location + ''
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.pick_num {
		border: 1rpx solid #ccc;
		padding: 15rpx 20rpx;
		width: 40vw;
		font-size: 30rpx;
	}

	.relative {
		position: relative;
	}

	.sku_img {
		padding-top: 30rpx;
		position: absolute;
		top: 80rpx;
		right: 20rpx;
	}

	.top-info {
		padding-right: 230rpx;
	}

	.spacing {
		justify-content: left;
		align-items: center;
		//display: fixed;
		height: 101rpx;
	}
</style>
