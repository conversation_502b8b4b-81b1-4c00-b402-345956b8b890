<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.Picking')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left" />
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>
		<!-- 操作栏 -->
		<view class="operation">
			<view class="scanner">
				<view class="scanner-label">{{$t('content.ScanPickingCar')}}：</view>
				<input class="scanner-input uni-input" cursor-spacing="10" type="text" focus :placeholder="$t('content.PScanPickingCar')" @confirm="nextStep()" v-model="formData.picking_car" />
			</view>
			<view>
				<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
				 @trigger="trigger" @fabClick="fabClick"></uni-fab>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Return')}}</button>
				<button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		nextStep,
		finish_task
	} from '../../commom/js/api.js'
	import uniFab from '@/components/uni-fab/uni-fab.vue';
	import { successAudio,failAudio } from '../../audio.js'
	export default {
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [
					{
						iconPath: '/static/search.png',
						selectedIconPath: '/static/search.png',
						text: this.$t('content.ScanCodeQuery'),
						active: true
					},
				],
				formData: {
					picking_car: ''
				},
				option: {},
				loading: false
			}
		},
		computed: {
			// 获取当前步骤需要提交的表单信息
			requestData() {
				return {
					...getApp().globalData.request,
					step: this.getRoutePath().step,
					...this.formData,
				}
			}
		},
		onLoad: function(option) {
			console.log(option);
			this.option = option
		},
		methods: {
			trigger(data) {
				switch (data.index) {
					case 0:
						uni.navigateTo({
							url: '../search/index'
						})
						break;
					default:
						break;
				}			
			},
			fabClick() {},
			// 校验
			validateForm() {
				return new Promise((resolve, reject) => {
					if (!this.formData.picking_car) {
						this.failAudio()
						uni.showToast({
							title: this.$t('content.PScanPickingCar'),
							icon: 'none',
							mask: true,
							duration: 2000
						});
						resolve(false)
					}
					resolve(true)

				})
			},
			// 返回
			back() {
				uni.navigateBack({
					delta: 1 // 返回上一级页面
				})
			},
			// 下一步
			async nextStep() {
				let flag = await this.validateForm()
				if (!flag) return
				
				nextStep(this.filterRequest(this.requestData)).then(res => {
					console.log(res);
					if (res.code === 200) {
						this.successAudio()
						getApp().globalData.request = { ...getApp().globalData.request,
							...res.data
						}
						
						if (res.data.msg){
							uni.showToast({
								title: res.data.msg,
								mask: true,
								duration: 1000,
								icon: 'none',
								position: 'top',
								success:function(){
									uni.navigateTo({
										url: this.getRoutePath().basicPath + res.data.step
									})
								}
							});
							
						}else{
							uni.navigateTo({
								url: this.getRoutePath().basicPath + res.data.step
							})
						}
						   
					} else {
						this.failAudio()
						uni.showToast({
							title: res.detail || res.message || 'fail request! please check!',
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
						this.formData.master_order_num = ''
					}
				})
				
			},
		}
	}
</script>

<style>
</style>
