<template>
	<view>
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.NoTransferDeatils')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left" />
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>
		<view>
			<unilist>
				<view class="baseInfo_header">
					<view class="baseInfo_child">{{$t('content.OrderNo')}}:{{ordernum}}</view>
					<view class="baseInfo_child">{{$t('content.Status')}}:{{status == 'OI' ? this.$t('content.Ongoing') : ''}}</view>
				</view>
				<view class="baseInfo">
					<view class="baseInfo_child">{{$t('content.Warehouse')}}:{{warehouse}}</view>
					<view class="baseInfo_child">{{$t('content.Total')}}:{{pieces}}</view>
				</view>
				<view class="baseInfo">
					<view class="baseInfo_child">{{$t('content.StartTime')}}:{{parseTime(starttime)}}</view>
					<view class="baseInfo_child">{{$t('content.FinishTime')}}:{{parseTime(endtime)}}</view>
				</view>
			</unilist>
		</view>
		<view>
			<unilist style="margin-top: 10%;">
				<hr>
				<view class="baseInfo">
					<view class="header_child">SKUNO</view>
					<view class="header_child">{{$t('content.ProductName')}}</view>
					<view class="header_child">{{$t('content.TargetLocation')}}</view>
					<view class="header_child">{{$t('content.Quantity')}}</view>
					<view class="header_child">{{$t('content.Operator')}}</view>
				</view>
				<view v-for="(item, index) in transferOffDetails" :key="item.id">
					<view class="header_child_cell">{{item.order_num}}</view>
					<view class="header_child_cell">{{item.name}}</view>
					<view class="header_child_cell">{{item.target_location}}</view>
					<view class="header_child_cell">{{item.pieces}}</view>
					<view class="header_child_cell">{{item.operator_name}}</view>
				</view>
			</unilist>
		</view>
		<view>
			<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
			 @trigger="trigger" @fabClick="fabClick"></uni-fab>
		</view>
	</view>
</template>

<script>
	import {
		get_nottransferoffdetails,
		get_nottransferondetails
	} from '../../commom/js/api.js'
	import unilist from '../../components/uni-list/uni-list.vue'
	import uniFab from '@/components/uni-fab/uni-fab.vue';
	import { successAudio,failAudio } from '../../audio.js'
	export default {
		components: {
			unilist
		},
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [
					{
						iconPath: '/static/search.png',
						selectedIconPath: '/static/search.png',
						text: this.$t('content.ScanCodeQuery'),
						active: true
					},
				],
				option: {},
				ordernum: "",
				status: "",
				warehouse: "",
				pieces: 0,
				starttime: "",
				endtime: "",
				transferOffDetails: []
			}
		},
		onLoad: function(option) {
			this.option = option
		},
		created() {

		},
		mounted() {
			if (this.option.type == 'off') {
				get_nottransferoffdetails(this.option.id).then(res => {
					if (res.code === 200) {
						this.successAudio()
						this.ordernum = res.data.order_num;
						this.status = res.data.status;
						this.warehouse = res.data.warehouse,
							this.pieces = res.data.pieces;
						this.starttime = res.data.start_date;
						this.endtime = res.data.finish_date;
						this.transferOffDetails = res.data.transfer_off_detail
					} else {
						this.failAudio()
						console.log('获取明细详情失败');
					}
				})
			} else {
				get_nottransferondetails(this.option.id).then(res => {
					if (res.code === 200) {
						this.successAudio()
						this.ordernum = res.data.order_num;
						this.status = res.data.status;
						this.warehouse = res.data.warehouse,
						this.pieces = res.data.pieces;
						this.starttime = res.data.start_date;
						this.endtime = res.data.finish_date;
						this.transferOffDetails = res.data.transfer_off_detail
					} else {
						this.failAudio()
						console.log('获取明细详情失败');
					}
				})
			}
		},
		methods: {
			trigger(data) {
				switch (data.index) {
					case 0:
						uni.navigateTo({
							url: '../search/index'
						})
						break;
					default:
						break;
				}			
			},
			fabClick() {}
		}
	}
</script>

<style scoped>
	.baseInfo {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.baseInfo_header {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 10%;
	}

	.baseInfo_child {
		width: 50%;
		text-align: center;
		line-height: 40px;
		font-size: 10px;
		height: 40px;
		border: 0px;
		border-radius: 10px;
		font-weight: 900;
		float: left;
		text-align: left;
		margin-left: 5%;
	}

	.header_child {
		width: 20%;
		text-align: center;
		line-height: 40px;
		font-size: 15px;
		height: 40px;
		border: 0px;
		border-radius: 10px;
		font-weight: 900;
		float: left;
	}

	.header_child_cell {
		width: 20%;
		text-align: center;
		line-height: 40px;
		font-size: 15px;
		height: 40px;
		border: 0px;
		border-radius: 10px;
		font-weight: 400;
		float: left;
		margin-bottom: 2px;
	}
</style>
