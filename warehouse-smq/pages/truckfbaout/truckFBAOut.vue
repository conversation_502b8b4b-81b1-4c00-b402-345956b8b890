<template>
  <view>
  <!-- 顶部栏 -->
  <view class="step">
    <uni-nav-bar :shadow='false' title="②卡派FBA出仓" @clickLeft="backToIndex()">
      <view slot="left">
        <i class="iconfont iconfanhui1 icon-left"/>
      </view>
      <view slot="right"> <text>{{$username}}</text> </view>
    </uni-nav-bar>
  </view>

  <!-- 操作栏 -->
  <view class="operation">

    <view class="scanner">
		<text style="font-size: 160%;">扫描FBA箱号：</text>
		<input class="scanner-input uni-input"
			cursor-spacing="10"
			ref='parcel_num'
			readonly
			type="text"
			:placeholder="$t('content.PScanInBoundOrderNo')"
			:focus="master_order_num_focus"
			@confirm="handleInput1()"
			v-model="formData.fba_num"
		/>
		<text style="font-size: 160%;">扫描中性面单：</text>
		<input class="scanner-input uni-input"
			cursor-spacing="10"
			ref='express_num'
			type="text"
			:placeholder="$t('content.PScanExpressOrderNo')"
			:focus="express_order_num_focus"
			@confirm="handleInput2()"
			v-model="formData.mid_num"
		/>
    </view>
    <view>
		<uni-fab
			:pattern="pattern"
			:content="content"
			:horizontal="horizontal"
			:vertical="vertical"
			:direction="direction"
			@trigger="trigger"
			@fabClick="fabClick">
		</uni-fab>
    </view>
    <view class="info-container" v-if="master_order_info.master_order_num" v-model="master_order_info">
      <view class="info-row">
        <view class="info-column">
          <view class="info-item">
            <text>提单号： {{ master_order_info.master_order_num }}</text>
          </view>
          <!-- <view class="info-item">
            <text>预计出仓数：</text>
            <text>{{ master_order_info.pre_carton }}</text>
          </view> -->
		  <view class="info-item">
			<text>配载票数： {{ master_order_info.set_ocean }}</text>
		  </view>
		  <view class="info-item">
			<text>配载件数： {{ master_order_info.set_warehouse }}</text>
		  </view>
        </view>
        <view class="info-column">
          <view class="info-item" style="color: red;" @click="to_not_scanned">
            <text>未扫描： {{ master_order_info.not_scanned }}</text>
          </view>
          <!-- <view class="info-item">
            <text>客户姓名：</text>
            <text>{{ master_order_info.client_name }}</text>
          </view> -->
          <!-- <view class="info-item">
            <text>入仓数量：</text>
            <text>{{ master_order_info.in_warehouse }}</text>
          </view>
          <view class="info-item">
            <text>出仓数量：</text>
            <text>{{ master_order_info.out_warehouse }}</text>
          </view> -->
		  <view class="info-item">
		    <text>出仓票数： {{ master_order_info.out_warehouse_qty }}</text>
		  </view>
		  <view class="info-item">
		    <text>出仓件数： {{ master_order_info.out_warehouse_num }}</text>
		  </view>
        </view>
      </view>
    </view>
    <view v-else class="no-data">
      <!-- 如果 master_order_info 不存在，则显示其他内容或者留空 -->
      <text>暂无数据</text>
    </view>
	
	<view v-if="responseDataList.length > 0">
	  <text style="font-size: 150%;">已扫描包裹：</text>
	  <view v-for="(data, index) in responseDataList.slice().reverse()" :key="index">
		<view class="info-container">
		  <view class="info-row">
			<view class="info-column">
			  <view class="info-item" :style="{ 'font-size': index === 0 ? '30px' : '20px' }">
				<text>{{ data }}</text>
			  </view>
			</view>
		  </view>
		</view>
	  </view>
	</view>
	<!-- 使用v-if进行条件渲染 -->
	<button v-if="isButtonVisible" @click="handleButtonClick">出仓完成</button>
    <!-- 底栏操作按钮 -->
    <view class="bottom-btn">
      <button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Finish')}}</button>
      <button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
    </view>

  </view>
  </view>
</template>

<script>
import {
	getStep, nextStep, truck_fba_parcel_out, out_warehouse_over, pda_save_img
} from '../../commom/js/api.js'
export default {
	data() {
		return {
			isButtonVisible: false,
			master_order_num_focus: true,
			express_order_num_focus: false,
			master_order_info: {
				master_order_num: '',
				express_order_num: ''
			}, // 主单信息
			responseDataList: [], // 保存返回的数据列表
			subOrderNumber: '',
			orderInfo: {
				masterOrder: '',
				errorSubOrders: 0,
				loadedTickets: 0,
				deliveredTickets: 0,
				loadedItems: 0,
				deliveredItems: 0,
			},
			pattern: {
				color: '',
				selectedColor: '',
				backgroundColor: '',
				buttonColor: ''
			},
			horizontal: 'left',
			vertical: 'bottom',
			direction: 'vertical',
			content: [
				{
					iconPath: '/static/search.png',
					selectedIconPath: '/static/search.png',
					text: this.$t('content.ScanCodeQuery'),
					active: true
				},
			],
			formData: {
				fba_num: '',
				mid_num: ''
			},
			option: {},
			loading: false
		}
	},
	onLoad: function(option) {
		// option为object类型，会序列化上个页面传递的参数
		// this.getStep(option.type)
        console.log('什么-->', option.type)
	},
	created() {
		
	},
	methods: {
		async handleInput1() {
			// 等待500毫秒，可以根据实际情况调整时间
			await new Promise(resolve => setTimeout(resolve, 500));
			const params = {
				fba_num: this.formData.fba_num,
				mid_num: this.formData.mid_num
			}
			truck_fba_parcel_out(params).then(res => {
				if (res.code === 200) {
					this.formData.master_order_num = res.parcel_num;
					this.master_order_info = res.data.master_order;
					this.responseDataList.push(res.data.sum_order_num);
					
					// 清空输入框 并 重新聚焦
					this.$set(this.formData, 'master_order_num', '')
					this.$set(this.formData, 'express_order_num', '')
					this.master_order_num_focus=false
					this.$nextTick(() => {
						this.master_order_num_focus=true
					});
				} else {
                    this.failAudio()
					console.log(res.msg)
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
					this.$set(this.formData, 'express_order_num', '')
					this.express_order_num_focus=false
					this.$nextTick(() => {
						this.$refs.audio_error.play()
						this.express_order_num_focus=true
					});
				}
			}).catch(err => {
                this.failAudio()
				console.error('请求失败', err);
				this.$set(this.formData, 'master_order_num', '')
				this.$set(this.formData, 'express_order_num', '')
				this.express_order_num_focus=false
				this.$nextTick(() => {
					this.$refs.audio_error.play()
					this.express_order_num_focus=true
				});
			})
		},
		async handleInput2() {
			// 等待500毫秒，可以根据实际情况调整时间
			await new Promise(resolve => setTimeout(resolve, 500));
            const oceanNum = this.$route.query.oceanNum;
            console.log('oceanNum-->', oceanNum)
            if (!oceanNum) {
                this.failAudio()
                uni.showToast({
                	title: '未选择海运提单',
                	icon: 'none'
                })
                return
            }
			const params = {
                oceanNum: oceanNum,
				fba_num: this.formData.fba_num,
				mid_num: this.formData.mid_num,
			}
			console.log(params)
			
			truck_fba_parcel_out(params).then(res => {
				if (res.code === 200) {
					this.formData.master_order_num = res.parcel_num;
					this.master_order_info = res.data.master_order;
					this.responseDataList.push(res.data.sum_order_num);
					
					// 清空输入框 重新聚焦
					this.$set(this.formData, 'fba_num', '')
					this.$set(this.formData, 'mid_num', '')
					this.master_order_num_focus=false
					this.$nextTick(() => {
						this.master_order_num_focus=true
					});
					// 每次出库成功后判断 是否要显示出库完成按钮
					console.log(this.master_order_info.set_warehouse)
					console.log(this.master_order_info.out_warehouse_num)
					if (this.master_order_info.set_warehouse === this.master_order_info.out_warehouse_num) {
						this.isButtonVisible = true
					}
				} else {
                    this.failAudio()
					console.log(res.msg)
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
					// 这里的异常有两种情况：第一个输入框不对、第二个输入框不对
					// 清空输入框
					this.$set(this.formData, 'fba_num', '')
					this.$set(this.formData, 'mid_num', '')
					this.master_order_num_focus=false
					this.$nextTick(() => {
						this.master_order_num_focus=true
					});
				}
			}).catch(err => {
                this.failAudio()
				console.log('请求失败', err);
				// 清空输入框
				this.$set(this.formData, 'master_order_num', '')
				this.$set(this.formData, 'express_order_num', '')
				this.master_order_num_focus=false
				// 重新聚焦
				this.$nextTick(() => {
					this.master_order_num_focus=true
				});
			})
		},
		to_scan_express() {
			const params = {
				parcel_num: this.formData.master_order_num,
				tracking_num: this.formData.master_order_num,
				// order_num: this.master_order_info.master_order_num !== "" ? this.master_order_info.master_order_num : ""
			}
			uni.navigateTo({
				url: `../nottransfer/index?parcel_num=${this.formData.master_order_num}`,
				query: params
			})
		},
		handleButtonClick(){
			const params = {
				parcel_num: this.master_order_info.master_order_num
			}
			console.log(params)
			console.log("发送请求，出仓完成")
			
			out_warehouse_over(params).then(res => {
				if (res.code === 200) {
					this.formData.master_order_num = "";
					this.master_order_info = "";
					this.responseDataList = [];
					
					// 清空输入框 并 重新聚焦
					this.$set(this.formData, 'master_order_num', '')
					this.$set(this.formData, 'express_order_num', '')
					this.master_order_num_focus=false
					this.$nextTick(() => {
						this.master_order_num_focus=true
					});
				} else {
                    this.failAudio()
					console.log(res.msg)
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			}).catch(err => {
                this.failAudio()
				console.error('请求失败', err);
			})
		},
		getStep(type){
			uni.showLoading({
				title: this.$t('content.Loading'),
				mask: true
			});
			getStep({type}).then(res=>{
				console.log(res);
				if(res.code===200){
					getApp().globalData.request = {...getApp().globalData.request,...res.data.params}
					this.getStepRouter(res.data.step)
				}
				uni.hideLoading();
			}).catch(err=>{uni.hideLoading();})
		},
		// 清空再聚焦
		cleanFocus(){
			// 清空输入框
			this.$set(this.formData, 'master_order_num', '')
			this.focusStatus=false
			// 重新聚焦
			this.$nextTick(() => {
				this.focusStatus=true
			});
		},
		// 监听回车
		handelEnter(str) {
			if (str === 'post') {
				this.getOrder()
			} else if (str) {
				this.$nextTick(() => {
					this.$refs[str].focus=true;
				});
			}
		},
		// 跳转 查看未处理包裹
		to_not_scanned() {
			const not_scan_params = {
				order_num: this.master_order_info.master_order_num,
			}
			uni.navigateTo({
				url: `../nottransfer/index?order_num=${this.master_order_info.master_order_num}`,
				query: not_scan_params
			})
		},
		// 核重
		getOrder() {
			const weight = localStorage.getItem('fba_update_weight')
			const size = localStorage.getItem('fba_update_size')

			// 包裹号
			if (!this.formData.parcel_num) {
				this.$nextTick(function() {
				this.$refs.audio_error.play()
			})
				this.$notify.error({
				title: 'error',
				message: '请确保填写了包裹号'
			})
			  return
			}
	
			his.loading = true
		
			const api = '/api/fbaBigParcels/fba_parcel_size/'
			actionPost({ api, data: this.formData }).then(res => {
				if (res.code === 200) {
					this.$notify.success({
					title: 'success',
					message: res.msg
				})
				this.handelEnter('parcel_num')
				// this.frontOrderNum = Object.assign({}, this.formData).order_num
				this.frontWeight = res.data.weight
				this.diffWeight = res.data.diffWeight
				this.scanResult = res.msg
				this.parcelOrderNums = (this.parcelOrderNums + 1)
				const weightingData = {
					result: 'success',
					time: res.data.time,
					order_num: res.data.order_num,
					parcel_num: res.data.parcel_num,
					product: res.data.product,
					buyer_country_code: res.data.buyer_country_code,
					label_weight: res.data.label_weight,
					label_length: res.data.label_length,
					label_width: res.data.label_width,
					label_height: res.data.label_height,
					actual_weight: res.data.actual_weight,
					actual_length: res.data.actual_length,
					actual_width: res.data.actual_width,
					actual_height: res.data.actual_height,
					country_code: res.data.buyer_country_code
				}
				this.parcelWeighingList.push(weightingData)
				this.parcelWeighingListReverse = [...this.parcelWeighingList].reverse()
				this.formData.parcel_num = ''
				this.formData.label_weight = weight ? localStorage.getItem('fba_update_weight') : ''
				this.formData.label_length = size ? size.split(',')[0] : ''
				this.formData.label_width = size ? size.split(',')[1] : ''
				this.formData.label_height = size ? size.split(',')[2] : ''
				this.pre_carton = res.data.pre_carton
				this.frontOrderNum = res.data.parcel_num
				this.formData.mojia_img = res.data.mojia_img
				this.$nextTick(function() {
					this.$refs.audio_success.play()
					this.success_count += 1
				})
			} else {
				this.$nextTick(function() {
				  this.$refs.audio_error.play()
				  this.fail_count += 1
				})
				this.$notify.error({
				  title: 'error',
				  message: res.msg || res.detail
				})
			}
			  this.loading = false
			}).catch(() => { this.loading = false })
		  },
		// 校验
		validateForm() {
			return new Promise((resolve, reject) => {
				if (!this.formData.master_order_num) {
					this.failAudio()
					uni.showToast({
						title: this.$t('content.PScanInventoryOrderNo'),
						icon: 'none',
						mask: true,
						duration: 2000
					});
					resolve(false)
				}
				resolve(true)
			})
		},
		onInputConfirm() {
			// 在这里处理扫描子单号的逻辑，更新 orderInfo 数据
			// 为了示例，这里简单地模拟了一些数据
			this.orderInfo = {
				masterOrder: '123456',
				errorSubOrders: 5,
				loadedTickets: 10,
				deliveredTickets: 8,
				loadedItems: 20,
				deliveredItems: 15,
			};
		}
	}
}
</script>

<style scoped>
.container {
  padding: 20px;
}

.input-container {
  margin-bottom: 20px;
}

.input-box {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-container {
  border: 1px solid #ccc;
  padding: 10px;
}

.info-row {
  display: flex;
  justify-content: space-between;
}

.info-column {
  display: flex;
  flex-direction: column;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh; /* 100%视窗高度，根据实际需要调整 */
}
</style>
