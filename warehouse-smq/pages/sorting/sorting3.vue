<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.ScanTurnoverBox')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left"/>
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>
		<!-- 操作栏 -->
		<view class="operation">
			<view class="scanner">
				<view class="scanner-label">请将{{formData.old_sku_code}}放入格口号:{{formData.gird_num}}</view>
				<view class="scanner-label">{{$t('content.ScanTurnoverBox')}}：</view>
				<input class="scanner-input uni-input" cursor-spacing="10" type="text" :placeholder="$t('content.ScanTurnoverBox')" @confirm="nextStep()" focus v-model="formData.turnover_box_code" />
			</view>
			<view>
				<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
				 @trigger="trigger" @fabClick="fabClick"></uni-fab>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Return')}}</button>
				<button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.NextStep')}}</button>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		nextStep,
		finish_task
	} from '../../commom/js/api.js'
	import uniFab from '@/components/uni-fab/uni-fab.vue';
	import { successAudio,failAudio } from '../../audio.js'
	export default {
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [
					{
						iconPath: '/static/search.png',
						sesku_codelectedIconPath: '/static/search.png',
						text: this.$t('content.ScanCodeQuery'),
						active: true
					},
				],
				formData: {
					turnover_box_code: '',
					gird_num: '',
					old_sku_code:''
				},
				option: {},
				loading: false
			}
		},
		computed: {
			// 获取当前步骤需要提交的表单信息
			requestData() {
				return {
					...getApp().globalData.request,
					step: this.getRoutePath().step,
					...this.formData,
				}
			}
		},
		created(){
			const t = getApp().globalData.request
			this.formData.record = t.gird_num_list
			this.formData.old_sku_code = t.sku_code
			this.formData.gird_num = t.gird_num
		},
		onLoad: function(option) {
			this.option = option
		},
		methods: {
			trigger(data) {
				switch (data.index) {
					case 0:
						uni.navigateTo({
							url: '../search/index'
						})
						break;
					default:
						break;
				}			
			},
			fabClick() {},
			// 校验
			validateForm() {
				return new Promise((resolve, reject) => {
					if (!this.formData.turnover_box_code) {
						this.failAudio()
						uni.showToast({
							title: this.$t('content.PBalePartitionCode'),
							icon: 'none',
							mask: true,
							duration: 2000
						});
						resolve(false)
					}
				    
					resolve(true)
					// uni.navigateTo({
					// 	url: '../bale/bale1'
					// })
				})
			},
			// 返回
			back() {
				uni.navigateBack({
					delta: 1 // 返回上一级页面
				})
			},
			// 下一步
			async nextStep() {
				if (this.loading) return
				this.loading = true
				uni.showLoading({
					title: this.$t('content.InRequest'),
					mask: true
				});
				let flag = await this.validateForm()
				uni.hideLoading();
				this.loading = false
				if (!flag) return
				nextStep(this.filterRequest(this.requestData)).then(res => {
					console.log(res);
					if (res.code === 200) {
						this.successAudio()
						getApp().globalData.request = { ...getApp().globalData.request,
							...res.data
						}
						
						uni.showToast({
							title: '绑定周转箱成功',
							duration: 2000,
							icon: 'none',
							mask: true,
							position: 'top'
						});
						
						uni.navigateTo({
							url: this.getRoutePath().basicPath + res.data.step
						})
						
						
					} else {
						this.failAudio()
						uni.showToast({
							title: res.detail || res.message || 'fail request! please check!',
							mask: true,
							duration: 2000,
							icon: 'none',
							position: 'top'
						});
						this.formData.master_order_num = ''
					}
				})
			},
		}
	}
</script>

<style>
</style>
