<template>
	<view>
		<!-- 顶部栏 -->
		<view class="step">
			<uni-nav-bar :shadow='false' :title="$t('content.PrintFaceSheet')" @clickLeft="backToIndex()">
				<view slot="left">
					<i class="iconfont iconfanhui1 icon-left" />
				</view>
				<view slot="right"> <text>{{$username}}</text> </view>
			</uni-nav-bar>
		</view>
		<!-- 操作栏 -->
		<view class="operation">
			<view class="scanner">
				<view class="scanner-label">{{$t('content.ScanOutboundOrder')}}：</view>
				<input :focus="autofocus" class="scanner-input" type="text" :placeholder="$t('content.ScanOutboundOrder')" @confirm="nextStep()" v-model="formData.order_num" />
			</view>
			<!-- 底部显示栏 -->
			<view class="bottom-info">
				<view>{{$t('content.Printer')}}</view>
				<view class="list-table">
					<table>
						<th>
						<td>{{$t('content.PrinterName')}}</td>
						</th>
						<template v-if="formData.record.length===0">
							<tr>
								{{$t('content.NoData')}}
							</tr>
						</template>
						<template v-else>
							<tr v-for="item in formData.record" :key="item.id">
								<td>{{item.printName}}</td>
							</tr>
						</template>
						
					</table>
				</view>
			</view>
			<view>
				<uni-fab :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical" :direction="direction"
				 @trigger="trigger" @fabClick="fabClick"></uni-fab>
			</view>
			<!-- 底栏操作按钮 -->
			<view class="bottom-btn">
				<button class="left" type="primary" @click="back()" :loading="false">{{$t('content.Return')}}</button>
				<button class="right" type="primary" @click="nextStep()" :loading="false">{{$t('content.Confirm')}}</button>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		get_intranet,
		get_print_name,
		get_outbound_order_label,
		printer,
		api_print_label
	} from '../../commom/js/api.js'
	import uniFab from '@/components/uni-fab/uni-fab.vue';
	import { successAudio,failAudio } from '../../audio.js'
	const Qs = require('qs')
	export default {
		data() {
			return {
				pattern: {
					color: '',
					selectedColor: '',
					backgroundColor: '',
					buttonColor: ''
				},
				horizontal: 'left',
				vertical: 'bottom',
				direction: 'vertical',
				content: [
					{
						iconPath: '/static/search.png',
						selectedIconPath: '/static/search.png',
						text: this.$t('content.ScanCodeQuery'),
						active: true
					},
				],
				formData: {
					order_num: '',
					record: []
				},
				option: {},
				loading: false,
				autofocus: false
			}
		},
		mounted() {
			setTimeout(() => {
				this.autofocus = true
			}, 1000)
		},
		created() {
			// 用于测试获取内网ip和默认打印机(用于windows)
			// get_intranet().then(res => {
			// 	console.log(res);
			// 	if (res.code === 200) {
			// 		this.intranet = res.data[0].value
			// 		console.log('无线打印ip ='+res.data[0].value);
			// 		this.$axios.post('http://'+this.intranet+':8001', Qs.stringify({
			// 			action: 'getPrinterList'
			// 		})).then((res) => {
			// 			console.log(res)
			// 			if (res.status === 200) {
			// 				let prints = []
			// 				res.data.data.forEach(function(element) {
			// 				  console.log(element[2])
			// 				  prints.push({"printName":element[2]})
			// 				});
			// 			    this.formData.record = prints
			// 				// this.printName = prints[0]['printName']
			// 			} else {
			// 				uni.showToast({
			// 				    title: '获取打印机失败',
			// 				    duration: 2000
			// 				});
			// 			}
			// 		}).catch(() => {
			// 			uni.showToast({
			// 			    title: '获取打印机失败',
			// 			    duration: 2000
			// 			});
			// 		})
			// 	} else {
			// 		console.log('获取不到内网ip，请重试！');
			// 	}
			// })
			
			//先配置默认打印机
			// get_print_name().then(res => {
			// 	console.log(res);
			// 	if (res.code === 200) {
			// 		this.printName = res.data[0].value
			// 		console.log('打印机名称 ='+res.data[0].value);
			// 	} else {
			// 		console.log('获取不到打印机名称，请重试！');
			// 	}
			// })
			
			//获取打印机
			printer().then(res => {
				if (res.code === 200) {
					this.successAudio()
					this.printName = res.data[0].value
					let prints = []
					res.data.forEach(function(element) {
					  console.log(element)
					  prints.push({"printName":element})
					});
					this.formData.record = prints
					
				} else {
					this.failAudio()
					console.log('获取不到打印机名称，请重试！');
				}
			})
			
		},
		computed: {
			// 获取当前步骤需要提交的表单信息
			requestData() {
				return {
					...getApp().globalData.request,
					step: this.getRoutePath().step,
					...this.formData,
				}
			}
		},
		onLoad: function(option) {
			console.log(option);
			this.option = option
		},
		// watch: {
		// 	'formData.order_num': {
		// 		handler(n, o) {
		// 			this.formData.record = []
		// 		},
		// 		deep: true
		// 	}
		// },
		methods: {
			trigger(data) {
				switch (data.index) {
					case 0:
						uni.navigateTo({
							url: '../search/index'
						})
						break;
					default:
						break;
				}			
			},
			fabClick() {},
			// 校验
			validateForm() {
				return new Promise((resolve, reject) => {
					if (!this.formData.order_num) {
						this.failAudio()
						uni.showToast({
							title: this.$t('content.PScanOutboundOrder'),
							icon: 'none',
							mask: true,
							duration: 2000
						});
						resolve(false)
					}
					resolve(true)

				})
			},
			reset_order_num() {
				this.formData.order_num = ''
			},
			// 返回
			back() {
				uni.redirectTo({
					url: '../index/index'
				})
			},
			// 下一步
			async nextStep() {
				if (this.loading) return
				this.loading = true
				uni.showLoading({
					title: this.$t('content.InRequest'),
					mask: true
				});
				let flag = await this.validateForm()
				uni.hideLoading();
				this.loading = false
				if (!flag) return
				console.log(this.intranet)
				const PrinterName = this.printName
				// const _axios_post = this.$axios
				const intranet = this.intranet
				const _this = this
				
				api_print_label(this.filterRequest(this.requestData)).then(res => {
					if (res.code === 200) {
						this.successAudio()
						uni.showToast({
							title: this.$t('content.PrintSuccessfully'),
							duration: 500
						});
					}else{
						this.failAudio()
						uni.showToast({
							title: this.$t('content.PrintingFailed'),
							duration: 2000
						});
					}
				})
				
			// 用于	
			// 	get_outbound_order_label(this.filterRequest(this.requestData)).then(res => {
			// 		if (res.code === 200) {
			// 			getApp().globalData.request = { ...getApp().globalData.request,
			// 				...res.data
			// 			}
   
			// 			res.data.label_base64s.forEach(function(element) {
							
			// 				const labelContent = element
							
			// 				_axios_post.post('http://'+intranet+':8001', Qs.stringify({
			// 					action: 'doPrint',
			// 					printerName: PrinterName,
			// 					print_too: 'sumatrapdf',
			// 					printQty: 1,
			// 					label: labelContent,
			// 					type: 'pdf'
			// 				})).then((res) => {
			// 					if (res.data.status === 1) {
			// 						uni.showToast({
			// 						    title: this.$t('content.PrintSuccessfully'),
			// 						    duration: 500
			// 						});
			// 					} else {
			// 						uni.showToast({
			// 						    title: this.$t('content.PrintingFailed'),
			// 						    duration: 2000
			// 						});
			// 					}
			// 				}).catch(() => {
			// 					uni.showToast({
			// 					    title: this.$t('content.PrintingFailed'),
			// 					    duration: 2000
			// 					});
			// 				})
							
			// 			})
			// 			_this.reset_order_num()
						
			// 		} else {
			// 			uni.showToast({
			// 				title: res.detail || res.msg || 'fail request! please check!',
			// 				mask: true,
			// 				duration: 2000,
			// 				icon: 'none',
			// 				position: 'top'
			// 			});
			// 			_this.reset_order_num()
			// 		}
			// 	})
			
			    
			},
			
		}
	}
</script>

<style>
</style>
