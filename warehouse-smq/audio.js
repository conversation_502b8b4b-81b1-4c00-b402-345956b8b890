export function successAudio(uni){
	// const innerAudioContext = uni.createInnerAudioContext();
	// innerAudioContext.autoplay = true;
	// innerAudioContext.src = '/static/AudioFile/success.mp3';
	// innerAudioContext.onPlay(() => {

	// });
	console.log('successAudio')
}

export function failAudio(uni){
	// const innerAudioContext = uni.createInnerAudioContext();
	// innerAudioContext.autoplay = true;
	// innerAudioContext.src = '/static/AudioFile/fail.mp3';
	// innerAudioContext.onPlay(() => {

	// });
	console.log('failAudio')
}