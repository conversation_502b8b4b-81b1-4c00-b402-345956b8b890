{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
  },
  "pages": [
    {
      "path": "pages/login/index",
      "style": {
        "navigationBarTitleText": "登录"
      }
    },
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页"
      }
    },
    {
      "path": "pages/discharge/index",
      "style": {
        "navigationBarTitleText": "快递出仓"
      }
    },
    {
      "path": "pages/truckfbaout/index",
      "style": {
        "navigationBarTitleText": "卡派FBA出仓(选择海运提单)"
      }
    },
    {
      "path": "pages/truckfbaout/truckFBAOut",
      "style": {
        "navigationBarTitleText": "卡派FBA出仓"
      }
    },
    {
      "path": "pages/trucknofbaout/index",
      "style": {
        "navigationBarTitleText": "卡派私人地址出仓"
      }
    },
    {
      "path": "pages/bindStorePosition/index",
      "style": {
        "navigationBarTitleText": "绑定库位"
      }
    },
    {
      "path": "pages/bindStorePosition/detail",
      "style": {
        "navigationBarTitleText": "绑定库位详情"
      }
    },
    {
      "path": "pages/moveStorePosition/index",
      "style": {
        "navigationBarTitleText": "移动库位"
      }
    },
    {
      "path": "pages/moveStorePosition/detail",
      "style": {
        "navigationBarTitleText": "移动库位详情"
      }
    },
    {
      "path": "pages/discharge/discharge1",
      "style": {
        "navigationBarTitleText": "扫描入库单号"
      }
    },
    {
      "path": "pages/discharge/discharge2",
      "style": {
        "navigationBarTitleText": "输入箱数"
      }
    },
    {
      "path": "pages/tracking/index",
      "style": {
        "navigationBarTitleText": "扫描快递号"
      }
    },
    // 收货
    {
      "path": "pages/receipt/index",
      "style": {
        "navigationBarTitleText": "收货"
      }
    },
    {
      "path": "pages/receipt/receipt1",
      "style": {
        "navigationBarTitleText": "扫描入库单号"
      }
    },
    {
      "path": "pages/receipt/receipt2",
      "style": {
        "navigationBarTitleText": "扫描SKU"
      }
    },
    {
      "path": "pages/receipt/receipt3",
      "style": {
        "navigationBarTitleText": "输入SKU包装信息"
      }
    },
    // 订单查询
    {
      "path": "pages/shelf/index",
      "style": {
        "navigationBarTitleText": "订单查询"
      }
    },
    {
      "path": "pages/shelf/notScanner",
      "style": {
        "navigationBarTitleText": "未入仓包裹"
      }
    },
    {
      "path": "pages/shelf/shelf1",
      "style": {
        "navigationBarTitleText": "扫描入库单号"
      }
    },
    {
      "path": "pages/shelf/shelf2",
      "style": {
        "navigationBarTitleText": "扫描SKU"
      }
    },
    {
      "path": "pages/shelf/shelf3",
      "style": {
        "navigationBarTitleText": "输入SKU上架数量"
      }
    },
    {
      "path": "pages/shelf/shelf4",
      "style": {
        "navigationBarTitleText": "扫描库位"
      }
    },
    // 拣选
    {
      "path": "pages/pick/index",
      "style": {
        "navigationBarTitleText": "拣选"
      }
    },
    {
      "path": "pages/pick/pick1",
      "style": {
        "navigationBarTitleText": "扫描入拣选单号"
      }
    },
    {
      "path": "pages/pick/pick2",
      "style": {
        "navigationBarTitleText": "扫描库位"
      }
    },
    {
      "path": "pages/pick/pick3",
      "style": {
        "navigationBarTitleText": "扫描SKU条码"
      }
    },
    {
      "path": "pages/pick/pick4",
      "style": {
        "navigationBarTitleText": "扫描拣选车"
      }
    },
    {
      "path": "pages/photographParcel/index",
      "style": {
        "navigationBarTitleText": "上传包裹图片"
      }
    },
    // 打包
    {
      "path": "pages/bale/index",
      "style": {
        "navigationBarTitleText": "打包"
      }
    },
    {
      "path": "pages/bale/bale1",
      "style": {
        "navigationBarTitleText": "扫描拣选队列号"
      }
    },
    {
      "path": "pages/bale/bale2",
      "style": {
        "navigationBarTitleText": "扫描Pallet ID"
      }
    },
    {
      "path": "pages/bale/bale3",
      "style": {
        "navigationBarTitleText": "输入SKU或分拣箱条码"
      }
    },
    // 盘点
    {
      "path": "pages/check/index",
      "style": {
        "navigationBarTitleText": "扫描"
      }
    },
    {
      "path": "pages/check/check1",
      "style": {
        "navigationBarTitleText": "扫描盘点单号"
      }
    },
    {
      "path": "pages/check/check2",
      "style": {
        "navigationBarTitleText": "扫描库位"
      }
    },
    {
      "path": "pages/check/check3",
      "style": {
        "navigationBarTitleText": "输入SKU"
      }
    },
    {
      "path": "pages/check/check4",
      "style": {
        "navigationBarTitleText": "输入数量"
      }
    },
    // 移库下架
    {
      "path": "pages/transferoff/index",
      "style": {
        "navigationBarTitleText": "移库下架"
      }
    },
    {
      "path": "pages/transferoff/transferoff1",
      "style": {
        "navigationBarTitleText": "扫描库位"
      }
    },
    {
      "path": "pages/transferoff/transferoff2",
      "style": {
        "navigationBarTitleText": "扫描SKU NO."
      }
    },
    {
      "path": "pages/transferoff/transferoff3",
      "style": {
        "navigationBarTitleText": "输入移库下架数量"
      }
    },
    // 移库上架
    {
      "path": "pages/transferon/index",
      "style": {
        "navigationBarTitleText": "移库上架"
      }
    },
    {
      "path": "pages/transferon/transferon1",
      "style": {
        "navigationBarTitleText": "扫描SKU NO."
      }
    },
    {
      "path": "pages/transferon/transferon2",
      "style": {
        "navigationBarTitleText": "扫描库位"
      }
    },
    {
      "path": "pages/transferon/transferon3",
      "style": {
        "navigationBarTitleText": "输入移库上架数量"
      }
    },
    //扫码查询
    {
      "path": "pages/search/index",
      "style": {
        "navigationBarTitleText": "扫码查询"
      }
    },
    // 查询sku库存
    // {
    // 	"path": "pages/sku/index",
    // 	"style": {
    // 		"navigationBarTitleText": "查询SKU库存"
    // 	}
    // },
    // 查询库位库存
    // {
    // 	"path": "pages/warehouse/index",
    // 	"style": {
    // 		"navigationBarTitleText": "查询库位库存"
    // 	}
    // },
    // 打印面单
    {
      "path": "pages/order/printer",
      "style": {
        "navigationBarTitleText": "打印面单"
      }
    },
    {
      "path": "pages/order/printer2",
      "style": {
        "navigationBarTitleText": "打印面单2"
      }
    },
    // 未移库明细
    {
      "path": "pages/nottransfer/index",
      "style": {
        "navigationBarTitleText": "未移库明细"
      }
    },
    // 未移库单据明细
    {
      "path": "pages/nottransfer/nottransferdetails",
      "style": {
        "navigationBarTitleText": "未移库单据明细"
      }
    },
    // sku库存查询列表
    {
      "path": "pages/stock/index",
      "style": {
        "navigationBarTitleText": "查询SKU库存"
      }
    },
    // 库位库存查询列表
    {
      "path": "pages/targetlocation/index",
      "style": {
        "navigationBarTitleText": "查询库位库存"
      }
    },
    // 打包台
    {
      "path": "pages/bale/bale4",
      "style": {
        "navigationBarTitleText": "打包台"
      }
    },
    //分拣
    {
      "path": "pages/sorting/index",
      "style": {
        "navigationBarTitleText": "分拣"
      }
    },
    {
      "path": "pages/sorting/sorting1",
      "style": {
        "navigationBarTitleText": "扫描拣选车"
      }
    },
    {
      "path": "pages/sorting/sorting2",
      "style": {
        "navigationBarTitleText": "扫描SKU"
      }
    },
    {
      "path": "pages/sorting/sorting3",
      "style": {
        "navigationBarTitleText": "扫描周转箱"
      }
    },
    // 退货上架
    {
      "path": "pages/refund/index",
      "style": {
        "navigationBarTitleText": "退货上架"
      }
    },
    {
      "path": "pages/refund/refund1",
      "style": {
        "navigationBarTitleText": "扫描单号"
      }
    },
    {
      "path": "pages/refund/refund2",
      "style": {
        "navigationBarTitleText": "扫描单号"
      }
    },
    {
      "path": "pages/refund/refund3",
      "style": {
        "navigationBarTitleText": "扫描SKU"
      }
    },
    {
      "path": "pages/refund/refund4",
      "style": {
        "navigationBarTitleText": "扫描库位"
      }
    },
    {
      "path": "pages/pick/prompt/prompt",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/fbaTakeStock/index",
      "style": {
        "navigationBarTitleText": "集货仓盘点"
      }
    },
    {
      "path": "pages/fbaTakeStock/detail",
      "style": {
        "navigationBarTitleText": "集货仓盘点详情"
      }
    },
    {
      "path": "pages/fbaTakeStock/box",
      "style": {
        "navigationBarTitleText": "集货仓盘点箱信息"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "white",
    "navigationBarTitleText": "扫描枪-SMQ",
    "navigationBarBackgroundColor": "#007AFF",
    "backgroundColor": "#FFFFFF",
    "app-plus": {
      "titleNView": false, // 去掉APP、H5的顶部导航
      "animationType": "fade-in",
      "animationDuration": 300
    }
  }
}
