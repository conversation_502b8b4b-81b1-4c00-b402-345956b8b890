//api.js

import http from "./request.js";
// 登录
export const login = (params) => http.post(`auth/login/`, params || {});
export const getInfo = (params) => http.get(`auth/info/`, params || {});

// 统一获取当前用户当前操作所在步骤
export const getStep = (params) => http.post(`api/scanner/get_step/`, params);

// 统一进行回车下一步操作
export const nextStep = (params) =>
  http.post(`api/scanner/next_step/`, params || {});

// 拣选跳过
export const pick_ship_pick_order = (params) =>
  http.post(`api/scanner/pick_ship_pick_order/`, params || {});

// 拣选短拣
export const pick_short_pick_order = (params) =>
  http.post(`api/scanner/pick_short_pick_order/`, params || {});

// 查询sku库存
export const sku_search = (params) =>
  http.post(`api/scanner/sku_search/`, params || {});

// 查询库位库存
export const position_search = (params) =>
  http.post(`api/scanner/position_search/`, params || {});

// 步骤操作点击完成
export const finish_task = (params) =>
  http.post(`api/scanner/finish_task/`, params || {});

// 获取打印机和调用打印机
// export const printer = (url, params) => http.post('http://127.0.0.1' + `:8001`, params || {})
export const printer = (url, params) =>
  http.get(`api/printer/get_printer/`, params || {});

// 通过字典获取内网ip
export const get_intranet = () =>
  http.get(`api/dicts/`, { page: 1, size: 100000, key: "intranet" } || {});

//获取打印机名称
export const get_print_name = () =>
  http.get(`api/dicts/`, { page: 1, size: 100000, key: "print_name" } || {});

// 查询订单和面单
export const get_outbound_order_label = (params) =>
  http.post(`api/outbounds/api_get_label/`, params || {});

// api打印面单
export const api_print_label = (params) =>
  http.post(`api/outbounds/api_print_label/`, params || {});

// 获取未移库明细列表
export const get_nottransferlist = () =>
  http.get(`api/wmsTransfer/get_data`, { page: 1, size: 100000 } || {});

// 获取未移库下架明细详情
export const get_nottransferoffdetails = (params) =>
  http.get("api/wmsTransferOffs/" + params, params || {});

// 获取未移库上架明细详情
export const get_nottransferondetails = (params) =>
  http.get("api/wmsTransferOns/" + params, params || {});

// 扫码查询
export const code_search = (params) =>
  http.post(`api/scanner/code_search/`, params || {});

// 扫码查询海运提单
export const parcel_get_ocean_order = (params) =>
  http.post(`api/fbaBigParcels/parcel_get_ocean_order/`, params || {});

// 绑定库位--扫包裹号获取订单
export const parcel_get_customer_order = (params) =>
  http.post(`api/fbaBigParcels/parcel_get_customer_order/`, params || {});

// 快递出仓
export const big_parcel_out = (params) =>
  http.post(`api/fbaBigParcels/big_parcel_out/`, params || {});

// 卡派FBA出仓
export const truck_fba_parcel_out = (params) =>
  http.post(`api/fbaBigParcels/truck_fba_parcel_out/`, params || {});

// 卡派私人地址出仓
export const truck_not_fba_parcel_out = (params) =>
  http.post(`api/fbaBigParcels/truck_not_fba_parcel_out/`, params || {});

// 出仓完成
export const out_warehouse_over = (params) =>
  http.post(`api/fbaBigParcels/out_warehouse_over/`, params || {});

// 查询当前出仓
export const get_not_scanned = (params) =>
  http.post(`api/fbaBigParcels/get_not_scanned/`, params || {});

// 取消出仓
export const fba_parcel_out_cancel = (params) =>
  http.post(`api/fbaBigParcels/fba_parcel_out_cancel/`, params || {});

// 扫描查询
export const get_parcel_info = (params) =>
  http.post(`api/fbaBigParcels/get_parcel_info/`, params || {});

// 查看海运单图片
export const pda_get_img = (params) =>
  http.post(`api/fbaBigParcels/pda_get_img/`, params || {});

// 查看包裹图片
export const get_parcel_img = (params) =>
  http.get(`api/parcelAttachments/get_parcel/`, params || {});

// 拍照上传海运单图片
export const pda_save_img = (params) =>
  http.post(`api/fbaBigParcels/pda_save_img/`, params || {});

// 拍照上传包裹图片
export const upload_parcel_img = (params) =>
  http.post(`api/parcelAttachments/`, params || {});

// 绑定库位
export const bind_store_position = (params) =>
  http.post(`api/fbaBigParcels/bind_store_position/`, params || {});

// 查询库位
export const query_store_position = (params) =>
  http.post(`api/fbaBigParcels/query_store_position/`, params || {});

// 移动库位
export const move_store_position = (params) =>
  http.post(`api/fbaBigParcels/move_store_position/`, params || {});

// 集货仓盘点 - 扫描箱号
export const fbaTakeStockScanNo = (params) =>
  http.get(`api/takeStockJobs/do_scan/`, params || {});

// 集货仓盘点 - 获取历史扫描作业单
export const fbaTakeStockListHistoryJob = (params) =>
  http.get(`api/takeStockJobs/list_history_jobs/`, params || {});

// 集货仓盘点 - 开始作业
export const fbaTakeStockStartJob = (params) =>
  http.post(`api/takeStockJobs/start_job/`, params || {});
// 集货仓盘点 - 查询未完成的盘点单
export const fbaTakeStockGetExistsJob = (params) =>
  http.post(`api/takeStockJobs/get_exists_job/`, params || {});
export const fbaTakeStockGetExistsOrder = (params) =>
  http.post(`api/takeStockOrders/get_take_stock_order/`, params || {});

// 集货仓盘点 - 新增详情
export const fbaTakeStockAddDetail = (params) =>
  http.post(`api/takeStockJobs/add_detail/`, params || {});
// 集货仓盘点 - 完结作业单
export const fbaTakeStockfinishJob = (params) =>
  http.post(`api/takeStockJobs/complete_job/`, params || {});

export const fbaTakeLoadDetail = (id) =>
  http.get(`api/takeStockJobs/${id}/`, id || {});
