#!/usr/bin/env bash
git pull

chmod +x ./deploy_git_log.sh
./deploy_git_log.sh log-only

APP_NAME=$(basename $(dirname "$PWD"))
#APP_NAME=$1

echo "开始发布时间: $(date +'%Y-%m-%d %H:%M:%S')"

echo "当前运行环境:$APP_NAME"

if [ -z "$APP_NAME" ]; then
    echo "Usage: $PROG_NAME {test|zj}"
    exit 2
fi

APP_HOME=/data/$APP_NAME

source /home/<USER>/alita/bin/activate
# 退出venv deactivate
#pip3 install -r requirements.txt

#设置环境
export PROJECT_SETTINGS=${APP_NAME}

python3 ${APP_HOME}/alita/manage.py migrate --settings=alita.settings.${APP_NAME}

# 编译中英文
python3 manage.py compilemessages -l zh_Hans -l en

#启动命令
#/usr/local/python3/bin/uwsgi -d --ini ${APP_HOME}/alita/${APP_NAME}_uwsgi.ini

/usr/local/bin/uwsgi -d --ini ${APP_HOME}/alita/${APP_NAME}_uwsgi.ini --reload ${APP_HOME}/pid/${APP_NAME}.pid

#sudo /usr/local/bin/uwsgi -d --ini data/dfjs/alita/dfjs_uwsgi.ini --reload /data/dfjs/pid/dfjs.pid

echo "结束发布时间: $(date +'%Y-%m-%d %H:%M:%S')"
