[uwsgi]
file-charset = utf-8
socket = 0.0.0.0:3038

http-timeout = 86400  # 设置较长的超时时间，单位为秒，根据实际需要调整
http-timeout-line = 86400  # 同样设置较长的超时时间

chdir = /data/zxt/alita
wsgi-file = alita/wsgi.py
processes = 4
threads = 2
stats = 127.0.0.1:9198
pythonpath = /usr/local/bin/python3.8
pidfile = /data/zxt/pid/mz.pid
# daemonize = /data/zxt/log/mz_uwsgi.log
env = DJANGO_SETTINGS_MODULE=alita.settings.mz
# env LANG=en_US.utf8
# env LC_ALL=en_US.UTF-8
# env LC_LANG=en_US.UTF-8
reload-mercy = 1
worker-reload-mercy = 1