#!/bin/bash

APP_NAME=$1
echo "当前运行环境:$APP_NAME"

PHONE='13316990258'
DD_ACCESS_TOKEN='73a65b43ab9d0aa381798927e7b8819c06cbe16bee50109d1fb4a2921beb1bf3'

# 拼接 JSON 数据
generate_json_data() {
  local content=$1
  local phone=$2
  cat <<EOF
{
  "msgtype": "text",
  "text": {
    "content": "$content"
  },
  "at": {"atMobiles": ["$phone", ]}
}
EOF
}


# 获取磁盘使用情况
df -h | awk 'NR>1 {print $5 " " $1}' | while read output;
do
  # 提取使用率和磁盘名称
  usep=$(echo $output | awk '{print $1}' | sed 's/%//')
  partition=$(echo $output | awk '{print $2}')

  # 检查使用率是否超过95%
  if [ $usep -ge 95 ]; then
    content="alita:${APP_NAME} 警告：磁盘分区 $partition 使用率已达到 $usep%"
    JSON_DATA=$(generate_json_data "$content" "$PHONE")
    curl -X POST "https://oapi.dingtalk.com/robot/send?access_token=$DD_ACCESS_TOKEN" -H 'Content-Type: application/json' -d "$JSON_DATA"
  fi
done

# 获取总内存和已使用内存
total_mem=$(free -m | awk '/^Mem:/{print $2}')
used_mem=$(free -m | awk '/^Mem:/{print $3}')

# 计算内存使用率
mem_usage=$(echo "scale=2; $used_mem / $total_mem * 100" | bc)

echo "内存使用率: $mem_usage"

# 检查内存使用率是否达到 100%
if (( $(echo "$mem_usage >= 100" | bc -l) )); then
  echo "警告：内存使用率已达到 100%!"
  content="alita:${APP_NAME} 警告：内存使用率已达到 $mem_usage%!"
  JSON_DATA=$(generate_json_data "$content" "$PHONE")
  curl -X POST "https://oapi.dingtalk.com/robot/send?access_token=$DD_ACCESS_TOKEN" -H 'Content-Type: application/json' -d "$JSON_DATA"
fi

# CPU设置负载阈值
# 设置负载阈值（每个核心的阈值）
THRESHOLD_PER_CORE=3
# 获取系统的 CPU 核心数
CPU_CORES=$(nproc)
# 计算总负载阈值
THRESHOLD=$(echo "$THRESHOLD_PER_CORE * $CPU_CORES" | bc)
# 获取系统的 1 分钟平均负载
LOAD=$(uptime | awk -F'load average:' '{ print $2 }' | cut -d, -f1 | xargs)
echo "总负载阈值:$THRESHOLD, 系统的 1 分钟平均负载:$LOAD"
# 比较负载是否超过阈值
if (( $(echo "$LOAD > $THRESHOLD" | bc -l) )); then
  echo "警告：CPU 1 分钟平均负载已达到 $LOAD，超过阈值 $THRESHOLD"
  content="alita:${APP_NAME} 警告：CPU 1 分钟平均负载已达到 $LOAD，超过阈值 $THRESHOLD"
  JSON_DATA=$(generate_json_data "$content" "$PHONE")
  curl -X POST "https://oapi.dingtalk.com/robot/send?access_token=$DD_ACCESS_TOKEN" -H 'Content-Type: application/json' -d "$JSON_DATA"
fi