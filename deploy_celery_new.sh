#!/usr/bin/env bash
git pull

APP_NAME=$(basename $(dirname "$PWD"))
#APP_NAME=$1
echo "当前运行环境:$APP_NAME"

if [ -z "$APP_NAME" ]; then
    echo "Usage: $PROG_NAME {test|yqf|mz}"
    exit 2
fi

case "$APP_NAME" in
    "zj2"|"zj3")
        echo "$APP_NAME 环境不能运行celery"
        exit 2
        ;;
esac

APP_HOME=/data/$APP_NAME

source /home/<USER>/alita/bin/activate
# 退出venv deactivate
#pip3 install -r requirements.txt

#设置环境
export PROJECT_SETTINGS=${APP_NAME}


# Supervisord control functions
check_supervisord() {
    if pgrep -f "supervisord" > /dev/null; then
        return 0  # Running
    else
        return 1  # Not running
    fi
}

# 检查celery进程是否还在运行
check_celery_processes() {
    # 检查celery worker进程
    if pgrep -f "celery.*worker" > /dev/null; then
        echo "发现celery worker进程还在运行"
        return 0  # 有celery进程
    fi
    
    # 检查celery beat进程
    if pgrep -f "celery.*beat" > /dev/null; then
        echo "发现celery beat进程还在运行"
        return 0  # 有celery进程
    fi
    
    # 检查celery flower进程
    if pgrep -f "celery.*flower" > /dev/null; then
        echo "发现celery flower进程还在运行"
        return 0  # 有celery进程
    fi
    
    echo "没有发现celery进程"
    return 1  # 没有celery进程
}

# 等待celery进程完全停止
wait_for_celery_stop() {
    echo "等待celery进程完全停止..."
    local max_wait=120  # 最大等待120秒
    local wait_count=0
    
    while [ $wait_count -lt $max_wait ]; do
        if check_celery_processes; then
            echo "等待中... ($((wait_count + 1))/$max_wait)"
            sleep 1
            wait_count=$((wait_count + 1))
        else
            echo "celery进程已完全停止"
            return 0
        fi
    done
    
    echo "警告: 等待超时，celery进程可能未完全停止"
    return 1
}

start_supervisord() {
    echo "Starting supervisord..."

    python3 ${APP_HOME}/alita/manage.py migrate --settings=alita.settings.${APP_NAME}

    export LOG_ROOT=${APP_HOME}/log

    # 生成supervisor配置，支持自定义主机名
    python3 generate_supervisor_config.py ${APP_NAME} --settings=alita.settings.${APP_NAME} --db

    # 同步celery配置,如果存在就忽略，不存在就新增
    python3 sync_beat_config.py

    supervisord -c supervisord_celery.conf
}

stop_supervisord() {
    echo "Stopping supervisord..."
    supervisorctl -c supervisord_celery.conf shutdown
    sleep 5  # Wait for complete shutdown
    sh remove_redis_keys.sh ${APP_NAME}
}

restart_supervisord() {
    echo "Restarting supervisord..."
    
    # 先停止supervisord
    if check_supervisord; then
        echo "停止supervisord..."
        stop_supervisord
    else
        echo "Supervisord未运行，直接启动"
    fi
    
    # 等待celery进程完全停止
    wait_for_celery_stop
    
    # 检查celery进程状态，如果没有进程则启动
    if check_celery_processes; then
        echo "警告: celery进程仍在运行，尝试强制停止..."
        # 强制杀死celery进程
        pkill -f "celery.*worker"
        pkill -f "celery.*beat"
        pkill -f "celery.*flower"
        sleep 3
        wait_for_celery_stop
    fi
    
    # 启动supervisord
    echo "启动supervisord..."
    start_supervisord
}

# Handle supervisord based on command
case "${1:-restart}" in
    start)
        if check_supervisord; then
            echo "Supervisord is already running"
        else
            start_supervisord
        fi
        ;;
    stop)
        if check_supervisord; then
            stop_supervisord
        else
            echo "Supervisord is not running"
        fi
        ;;
    restart)
        restart_supervisord
        ;;
    status)
        echo "=== Supervisord状态 ==="
        if check_supervisord; then
            echo "Supervisord: 运行中"
            supervisorctl -c supervisord_celery.conf status
        else
            echo "Supervisord: 未运行"
        fi
        
        echo ""
        echo "=== Celery进程状态 ==="
        if check_celery_processes; then
            echo "Celery进程: 运行中"
            echo "正在运行的celery进程:"
            pgrep -f "celery" | xargs ps -p
        else
            echo "Celery进程: 未运行"
        fi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac

# 查看状态
supervisorctl -c supervisord_celery.conf status

echo "发布完成!"


# 使用方法:
# 重启celery (默认): ./deploy_celery_new.sh 或 ./deploy_celery_new.sh restart
# 启动celery: ./deploy_celery_new.sh start
# 停止celery: ./deploy_celery_new.sh stop  
# 查看状态: ./deploy_celery_new.sh status (查看supervisord和celery进程状态)
