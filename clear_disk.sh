#!/bin/bash

APP_NAME=$(basename $(dirname "$PWD"))

echo "当前运行环境:$APP_NAME"
APP_HOME=/data/$APP_NAME

MYSQL_USER="alita_${APP_NAME}"
MYSQL_PASS="AhW@a^DjU9AgSV89_2025ASqwedfwehszh"
MYSQL_HOST="127.0.0.1"  # 如果是远程MySQL，改为IP或域名
MYSQL_PORT="13369"
DATABASE_NAME="alita_${APP_NAME}"

# 执行清理命令

echo "开始清理数据库中的django_celery_results_taskresult表"

mysql -u"$MYSQL_USER" -p"$MYSQL_PASS" -h"$MYSQL_HOST" -P"$MYSQL_PORT" <<EOF
USE $DATABASE_NAME;
TRUNCATE TABLE django_celery_results_taskresult;
EOF

echo "清理数据库中的django_celery_results_taskresult表完成"

# 清空 Nginx 访问日志文件
echo "开始清理nginx access.log日志"
> /usr/local/openresty/nginx/logs/access.log
echo "清理nginx access.log日志完成"

# 查询MySQL binlog日志的位置
echo "查询MySQL binlog日志位置..."
BINLOG_BASENAME=$(mysql -u"$MYSQL_USER" -p"$MYSQL_PASS" -h"$MYSQL_HOST" -P"$MYSQL_PORT" -sN -e "SHOW VARIABLES LIKE 'log_bin_basename';" | awk '{print $2}')

if [ -n "$BINLOG_BASENAME" ]; then
    echo "MySQL binlog基础路径: $BINLOG_BASENAME"
    # 提取binlog目录路径和文件前缀，添加通配符
    BINLOG_DIR=$(dirname "$BINLOG_BASENAME")
    BINLOG_PREFIX=$(basename "$BINLOG_BASENAME")
    BINLOG_PATTERN="${BINLOG_DIR}/${BINLOG_PREFIX}.*"
    echo "MySQL binlog匹配模式: $BINLOG_PATTERN"
else
    echo "警告: 无法获取MySQL binlog路径，可能binlog未启用"
    BINLOG_PATTERN=""
fi

# 定义要操作的文件路径列表
target_paths=(
    "/data/${APP_NAME}/log/celery_beat.log.*"
    "/data/${APP_NAME}/log/${APP_NAME}_uwsgi.log.*"
)

# 如果binlog路径有效，则添加到target_paths数组中
if [ -n "$BINLOG_PATTERN" ]; then
    target_paths+=("$BINLOG_PATTERN")
    echo "已将MySQL binlog路径添加到清理列表: $BINLOG_PATTERN"
fi

echo "开始清理2天前的mysql binlog日志和celery_beat.log日志"

# 计算2天前的时间戳
two_days_ago=$(date -d "2 days ago" +%s)

# 遍历路径列表
for path_pattern in "${target_paths[@]}"; do
    # 提取目录部分
    target_dir=$(dirname "$path_pattern")
    # 检查目录是否存在
    if [ -d "$target_dir" ]; then
        echo "正在清理目录: $target_dir"
        # 使用 find 命令查找符合条件的文件
        find "$target_dir" -name "$(basename "$path_pattern")" -type f -ctime +1 | while read -r file; do
            rm "$file"
            echo "已删除文件: $file"
        done
    else
        echo "警告: $target_dir 目录不存在，跳过清理"
    fi
done

echo "清理2天前的mysql binlog日志和celery_beat.log日志完成" 