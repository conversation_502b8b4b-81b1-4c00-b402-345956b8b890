#!/usr/bin/env bash

#APP_NAME=$(basename $(dirname "$PWD"))

APP_NAME=$1

echo "当前运行环境:$APP_NAME"


#source /home/<USER>/alita/bin/activate

#设置环境
export PROJECT_SETTINGS=${APP_NAME}

python3 /data/alita/manage.py migrate --settings=alita.settings.${APP_NAME}

export LOG_ROOT=/data/log

# 默认取配置文件
python3 generate_supervisor_config.py ${APP_NAME} --db

# 同步celery配置,如果存在就忽略，不存在就新增
python3 sync_beat_config.py

supervisord -c  supervisord_celery.conf