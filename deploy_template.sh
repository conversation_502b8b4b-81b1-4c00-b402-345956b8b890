#!/usr/bin/env bash
# 用于更新模版文件

git pull

APP_NAME=$(basename $(dirname "$PWD"))

# bills_details
mv  /data/${APP_NAME}/data/bill/bills_details.xlsx /data/${APP_NAME}/data/bill/bills_details.xlsxbak
cp /data/${APP_NAME}/alita/media/data/${APP_NAME}_bills_details.xlsx /data/${APP_NAME}/data/bill/bills_details.xlsx

# 清关账单模版
mv  /data/${APP_NAME}/data/bill/clearance_bills_details.xlsx /data/${APP_NAME}/data/bill/clearance_bills_details.xlsxbak
cp /data/${APP_NAME}/alita/media/data/${APP_NAME}_clearance_bills_details.xlsx /data/${APP_NAME}/data/bill/clearance_bills_details.xlsx

mv  /data/${APP_NAME}/data/bill/clearance_vasorder_bills_details.xlsx /data/${APP_NAME}/data/bill/clearance_vasorder_bills_details.xlsxbak
cp /data/${APP_NAME}/alita/media/data/${APP_NAME}_clearance_vasorder_bills_details.xlsx /data/${APP_NAME}/data/bill/clearance_vasorder_bills_details.xlsx


mv  /data/${APP_NAME}/data/bill/bills_details_transport.xlsx /data/${APP_NAME}/data/bill/bills_details_transport.xlsxbak
cp /data/${APP_NAME}/alita/media/data/${APP_NAME}_bills_details_transport.xlsx /data/${APP_NAME}/data/bill/bills_details_transport.xlsx

mv  /data/${APP_NAME}/data/bill/bills_details_packet.xlsx /data/${APP_NAME}/data/bill/bills_details_packet.xlsxbak
cp /data/${APP_NAME}/alita/media/data/${APP_NAME}_bills_details_packet.xlsx /data/${APP_NAME}/data/bill/bills_details_packet.xlsx

# air_debit
mv  /data/${APP_NAME}/data/debit/air_debit.xlsx /data/${APP_NAME}/data/debit/air_debit.xlsxbak
cp /data/${APP_NAME}/alita/media/data/${APP_NAME}_air_debit.xlsx /data/${APP_NAME}/data/debit/air_debit.xlsx

# 区分环境复制fba导入模板
mv  /data/${APP_NAME}/alita/media/templates/template_customer_fba_order.xlsx /data/${APP_NAME}/alita/media/templates/template_customer_fba_order.xlsxbak
cp /data/${APP_NAME}/alita/media/data/${APP_NAME}_template_customer_fba_order.xlsx /data/${APP_NAME}/alita/media/templates/template_customer_fba_order.xlsx

mv  /data/${APP_NAME}/alita/media/templates/template_customer_fba_order_single.xlsx /data/${APP_NAME}/alita/media/templates/template_customer_fba_order_single.xlsxbak
cp /data/${APP_NAME}/alita/media/data/${APP_NAME}_template_customer_fba_order_single.xlsx /data/${APP_NAME}/alita/media/templates/template_customer_fba_order_single.xlsx

# 构建文件夹路径
folder_path="/data/${APP_NAME}/static/alita/media/templates/"

# 判断文件夹是否存在
if [ ! -d "$folder_path" ]; then
    # 如果不存在，创建文件夹
    mkdir -p "$folder_path"
    if [ $? -eq 0 ]; then
        echo "文件夹 $folder_path 创建成功。"
    else
        echo "创建文件夹 $folder_path 失败。"
    fi
else
    echo "文件夹 $folder_path 已经存在。"
fi

cp -rf /data/${APP_NAME}/alita/media/templates/* /data/${APP_NAME}/static/alita/media/templates/