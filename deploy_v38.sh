#!/usr/bin/env bash
git pull

APP_NAME=$(basename $(dirname "$PWD"))
#APP_NAME=$1

echo "当前时间: $(date)"

echo "当前运行环境:$APP_NAME"

# 默认
#python3 generate_supervisor_config.py ${APP_NAME} --legacy

export alita_end_url=registry.cn-shenzhen.aliyuncs.com/alita_px/alita_backend:v-py-38
export deploy_version=$APP_NAME
export alita_front_url=registry.cn-shenzhen.aliyuncs.com/alita_px/alita_vue:v1.1


docker build -f Dockerfile38 --build-arg deploy_version=${APP_NAME} -t registry.cn-shenzhen.aliyuncs.com/alita_px/alita_backend:v-py-38 .


docker-compose -f docker-compose38.yml stop alita-task
docker-compose -f docker-compose38.yml rm -f alita-task
docker-compose -f docker-compose38.yml up alita-task -d

sleep 10

docker-compose -f docker-compose38.yml stop alita-end
docker-compose -f docker-compose38.yml rm -f alita-end
docker-compose -f docker-compose38.yml up alita-end -d

# 清理缓存
docker image prune -f
#docker system prune -f
#docker system prune -a
