#!/usr/bin/env bash
git pull

APP_NAME=$(basename $(dirname "$PWD"))
#APP_NAME=$1
echo "当前运行环境:$APP_NAME"

if [ -z "$APP_NAME" ]; then
    echo "Usage: $PROG_NAME {test|yqf|mz}"
    exit 2
fi

case "$APP_NAME" in
    "zj2"|"zj3")
        echo "$APP_NAME 环境不能运行celery"
        exit 2
        ;;
esac

APP_HOME=/data/$APP_NAME

source /home/<USER>/alita/bin/activate
# 退出venv deactivate
#pip3 install -r requirements.txt

#设置环境
export PROJECT_SETTINGS=${APP_NAME}

python3 ${APP_HOME}/alita/manage.py migrate --settings=alita.settings.${APP_NAME}

stop_celery_worker() {
    echo "stopping the ${APP_NAME} celery worker"
    PID=`ps ax | grep "celery -A alita --workdir=${APP_HOME}" | grep -v grep | awk '{print $1}'`
#    PID=`ps ax | grep 'celery worker -A alita' | grep -v grep | awk '{print $1}'`
    echo ${PID}
    if [[ ! -z "$PID" ]]; then
       kill -9 $PID
    else
     echo "${APP_NAME} celery worker  is not running"
    fi
}

stop_celery_beat() {
    echo "stopping the ${APP_NAME} celery beat "
    PID=`ps ax | grep "celery -A alita beat --detach -l info -S django -f ${APP_HOME}" | grep -v grep | awk '{print $1}'`
    echo ${PID}
    if [[ ! -z "$PID" ]]; then
      echo "kill -9 $PID"
      kill -9 $PID
      sh remove_redis_keys.sh ${APP_NAME}
    else
      echo "${APP_NAME} celery beat is not running"
    fi
}

echo $(date)
stop_celery_beat
sleep 5s
#echo $(date)
stop_celery_worker

start_celery_worker(){
   echo "start ${APP_NAME}  celery worker"
#   nohup celery worker --workdir=${APP_HOME}/alita -A alita -l debug -P gevent -c 20  -f ${APP_HOME}/log/celery_worker.log &
   GEVENT_NUM=30
   if [ $APP_NAME == "zj" ] || [ "$APP_NAME" = "md" ]; then
      GEVENT_NUM=40
   elif [ $APP_NAME == "hanjin" ]; then
      GEVENT_NUM=40
   else
      GEVENT_NUM=30
   fi

#   celery multi stop fp_w1 --workdir=/data/fp/alita -A alita
   #celery multi restart zhs_w1 --workdir=/data/zhs/alita -A alita -l DEBUG -P threads -c 20 -f /data/zhs/log/celery_worker.log
   celery multi restart ${APP_NAME}_w1 --workdir=${APP_HOME}/alita -A alita -l ERROR -P threads -c ${GEVENT_NUM} -f ${APP_HOME}/log/celery_worker.log
#   celery multi restart ${APP_NAME}_w1 --workdir=${APP_HOME}/alita -A alita -l ERROR -P gevent -c ${GEVENT_NUM} -f ${APP_HOME}/log/celery_worker.log

   PID=`ps ax | grep "celery -A alita --workdir=${APP_HOME}" | grep -v grep | awk '{print $1}'`
   echo ${PID}
   if [[ ! -z "$PID" ]]; then
      echo "${APP_NAME} celery worker is  running"
   else
      echo "${APP_NAME} celery worker is not running"
   fi

}

start_celery_beat(){
   echo "start ${APP_NAME} celery beat"
#   nohup celery beat --workdir=${APP_HOME}/alita -A alita -l info -S django  -f ${APP_HOME}/log/celery_beat.out &
   celery -A alita beat --detach -l info -S django -f ${APP_HOME}/log/celery_beat.out
   PID=`ps ax | grep "celery -A alita beat --detach -l info -S django -f ${APP_HOME}" | grep -v grep | awk '{print $1}'`
   echo ${PID}
   if [[ ! -z "$PID" ]]; then
      echo "${APP_NAME} celery beat is  running"
   else
      echo "${APP_NAME} celery beat is not running"
   fi

}

echo $(date)
start_celery_worker

sleep 5s
echo $(date)
start_celery_beat

echo "发布完成!"