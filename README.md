## 介绍
- 数字化产业
- 前后端分离项目

## 软件架构

## 安装教程
### 环境 
- 系统
  - Local： Windows
  - Test：Linux
  - Product：Linux
- 后端 
  - Python 3.8
  - MySQL 8.0 
  - Redis 5.7
- 前端 
  - Vue 3.0
  - nvm 1.1.12 
  - npm 6.14.16 
  - node 12.22.12

## 前端
- 环境准备 
  - 清理 node
    - 直接在控制面板中卸载 Node
    - [ 不同系统清理 ](https://www.python100.com/html/3WU2V9G1B38I.html)
  - 下载并安装 nvm
    - [点击下载](https://github.com/coreybutler/nvm-windows/releases/download/1.1.12/nvm-setup.exe)
    - [查看版本](https://github.com/coreybutler/nvm-windows/releases)
    - 安装的时候会安装 npm，但是不会直接安装 node，会预设 node 的安装位置，后面再手动安装 
  - 安装 node
    - nvm install 12.22.12 
  - 查看版本
    - nvm -v 
    - node -v
    - npm -v
  - 安装运行
    - npm install
      - [报错处理](https://www.cnblogs.com/zealousness/p/10452861.html)
      - npm install  --save  file-saver@1.3.8
      - npm install
    - npm run dev 
  - 后端
    - Python
      - [ 点击下载 ](https://www.python.org/ftp/python/3.8.10/python-3.8.10-amd64.exe)
      - 安装的时候选择添加环境变量
      - pip3 install -r requirements.txt
    - MySQL
    - Redis
    - PyCharm
      - 配置 Django 支持
      - 指定 test 配置文件
    - 报错：
      - 出现报错的包，直接 pip install pageage_name
      - SimSun.tff 的异常，找到字体包，右键点击，安装给所有用户

## 使用说明

----

## 参与开发
1. [进入云效](https://codeup.aliyun.com/642bfff63fb296b5679482b7/pyer/alita/tree/mk?appId=5e730cf2201d20d717988e46)
2. 拉起指定分支代码（mk）
3. 完成需求开发，完成本地测试
4. 提交代码，新建 Pull Request

pip3 install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

## zbar
sudo apt-get install libzbar0
yum install zbar
条形码识别依赖
pip install zbar
pip3 install pyzbar
pip3 install PyMuPDF
pip3 install zbarlight

升级django3.2
pip3 install -i https://mirrors.aliyun.com/pypi/simple -r requirements.txt
pip3 install https://codeload.github.com/sshwsfc/xadmin/zip/django2
pip3 install crispy-bootstrap3


DJANGO_SETTINGS_MODULE=alita.settings.test
