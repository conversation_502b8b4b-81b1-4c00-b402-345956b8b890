#!/usr/bin/env python
"""
🔄 Celery Beat配置同步脚本
从 celery_beat_config.py 同步任务到 django_celery_beat 数据库

使用方法:
python sync_beat_config.py                 # 同步所有任务
python sync_beat_config.py --dry-run       # 预览将要同步的任务
python sync_beat_config.py --force         # 强制更新已存在的任务
"""

import os
import sys
import json
import argparse
from datetime import timedelta

# Django环境初始化
profile = os.environ.get('PROJECT_SETTINGS', 'test')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'alita.settings.%s' % profile)
import django
django.setup()

from django_celery_beat.models import PeriodicTask, CrontabSchedule, IntervalSchedule
from celery.schedules import crontab, schedule


def print_info(message):
    """打印信息"""
    print(f"ℹ️  {message}")


def print_success(message):
    """打印成功"""
    print(f"✅ {message}")


def print_warning(message):
    """打印警告"""
    print(f"⚠️  {message}")


def print_error(message):
    """打印错误"""
    print(f"❌ {message}")


def load_beat_schedule():
    """加载 celery_beat_config.py 中的 beat_schedule"""
    try:
        # 首先尝试使用新的动态配置方式
        try:
            from celery_beat_config import get_beat_schedule
            beat_schedule = get_beat_schedule()
            print_success(f"加载动态配置文件成功，找到 {len(beat_schedule)} 个任务")
            
            # 统计静态和动态任务数量
            from celery_beat_config import static_beat_schedule, get_dynamic_beat_schedule
            static_count = len(static_beat_schedule)
            dynamic_count = len(get_dynamic_beat_schedule())
            print_info(f"  - 静态任务: {static_count} 个")
            print_info(f"  - 动态任务: {dynamic_count} 个")
            
            return beat_schedule
        except (ImportError, AttributeError):
            # fallback到旧的静态配置方式
            from celery_beat_config import beat_schedule
            print_success(f"加载静态配置文件成功，找到 {len(beat_schedule)} 个任务")
            return beat_schedule
            
    except ImportError as e:
        print_error(f"无法导入 celery_beat_config.py: {str(e)}")
        print_error("请确保 celery_beat_config.py 文件存在且包含 beat_schedule 变量或 get_beat_schedule 函数")
        return None
    except Exception as e:
        print_error(f"加载配置文件失败: {str(e)}")
        return None


def create_schedule_from_celery_config(schedule_config):
    """将 Celery 调度配置转换为 Django Celery Beat 调度对象"""
    
    if isinstance(schedule_config, crontab):
        # Crontab 调度
        schedule_obj, created = CrontabSchedule.objects.get_or_create(
            minute=schedule_config.minute,
            hour=schedule_config.hour,
            day_of_week=schedule_config.day_of_week,
            day_of_month=schedule_config.day_of_month,
            month_of_year=schedule_config.month_of_year,
            timezone='Asia/Shanghai'
        )
        return schedule_obj, 'crontab'
    
    elif isinstance(schedule_config, timedelta):
        # timedelta 调度
        total_seconds = int(schedule_config.total_seconds())
        schedule_obj, created = IntervalSchedule.objects.get_or_create(
            every=total_seconds,
            period=IntervalSchedule.SECONDS
        )
        return schedule_obj, 'interval'
    
    elif isinstance(schedule_config, schedule):
        # schedule 调度（通常是 run_every）
        if hasattr(schedule_config, 'run_every'):
            if isinstance(schedule_config.run_every, (int, float)):
                # 数字表示秒数
                schedule_obj, created = IntervalSchedule.objects.get_or_create(
                    every=int(schedule_config.run_every),
                    period=IntervalSchedule.SECONDS
                )
                return schedule_obj, 'interval'
            elif isinstance(schedule_config.run_every, timedelta):
                total_seconds = int(schedule_config.run_every.total_seconds())
                schedule_obj, created = IntervalSchedule.objects.get_or_create(
                    every=total_seconds,
                    period=IntervalSchedule.SECONDS
                )
                return schedule_obj, 'interval'
    
    elif isinstance(schedule_config, (int, float)):
        # 直接的数字，表示秒数
        schedule_obj, created = IntervalSchedule.objects.get_or_create(
            every=int(schedule_config),
            period=IntervalSchedule.SECONDS
        )
        return schedule_obj, 'interval'
    
    else:
        raise ValueError(f"不支持的调度类型: {type(schedule_config)}")


def sync_task_to_database(task_name, task_config, dry_run=False, force_update=False):
    """同步单个任务到数据库"""
    
    # 检查任务是否已存在
    existing_task = PeriodicTask.objects.filter(name=task_name).first()
    
    if existing_task and not force_update:
        print_info(f"任务 '{task_name}' 已存在，跳过")
        return False
    
    if dry_run:
        print_info(f"[DRY RUN] 将{'更新' if existing_task else '创建'}任务: {task_name}")
        print_info(f"  - 任务: {task_config['task']}")
        print_info(f"  - 调度: {task_config['schedule']}")
        if task_config.get('args'):
            print_info(f"  - 参数: {task_config['args']}")
        return True
    
    try:
        # 创建调度对象
        schedule_obj, schedule_type = create_schedule_from_celery_config(task_config['schedule'])
        
        # 准备任务数据
        task_data = {
            'name': task_name,
            'task': task_config['task'],
            'args': json.dumps(task_config.get('args', [])),
            'kwargs': json.dumps(task_config.get('kwargs', {})),
            'queue': task_config.get('queue', None),
            'exchange': task_config.get('exchange', None),
            'routing_key': task_config.get('routing_key', None),
            'priority': task_config.get('priority', None),
            'enabled': True,
            'description': f'从 celery_beat_config.py 同步: {task_name}'
        }
        
        # 添加调度信息
        if schedule_type == 'crontab':
            task_data['crontab'] = schedule_obj
        else:
            task_data['interval'] = schedule_obj
        
        # 创建或更新任务
        if existing_task:
            # 更新现有任务
            for key, value in task_data.items():
                setattr(existing_task, key, value)
            existing_task.save()
            print_success(f"更新任务: {task_name}")
        else:
            # 创建新任务
            PeriodicTask.objects.create(**task_data)
            print_success(f"创建任务: {task_name}")
        
        return True
        
    except Exception as e:
        print_error(f"同步任务 '{task_name}' 失败: {str(e)}")
        return False


def sync_all_tasks(beat_schedule, dry_run=False, force_update=False):
    """同步所有任务"""
    
    print_info(f"开始同步 {len(beat_schedule)} 个任务...")
    
    success_count = 0
    skip_count = 0
    error_count = 0
    
    # 按类别分组统计
    categories = {
        'handler_': '业务处理任务',
        'beat_': 'Beat调度任务', 
        'create_': '创建任务',
        'get_': '获取任务',
        'sync_': '同步任务',
        'push_': '推送任务',
        'auto_': '自动任务',
        'common_': '通用任务',
        'execute_': '执行任务',
        'update_': '更新任务',
        'confirm_': '确认任务',
        'generate_': '生成任务'
    }
    
    category_stats = {cat: {'success': 0, 'skip': 0, 'error': 0} for cat in categories.values()}
    category_stats['其他任务'] = {'success': 0, 'skip': 0, 'error': 0}
    
    for task_name, task_config in beat_schedule.items():
        try:
            result = sync_task_to_database(task_name, task_config, dry_run, force_update)
            
            # 确定任务类别
            task_category = '其他任务'
            for prefix, category in categories.items():
                if task_name.startswith(prefix):
                    task_category = category
                    break
            
            if result:
                success_count += 1
                category_stats[task_category]['success'] += 1
            else:
                skip_count += 1
                category_stats[task_category]['skip'] += 1
                
        except Exception as e:
            print_error(f"处理任务 '{task_name}' 时发生错误: {str(e)}")
            error_count += 1
            
            # 确定任务类别
            task_category = '其他任务'
            for prefix, category in categories.items():
                if task_name.startswith(prefix):
                    task_category = category
                    break
            category_stats[task_category]['error'] += 1
    
    # 打印统计信息
    print("\n" + "="*60)
    if dry_run:
        print_info(f"[DRY RUN] 同步预览完成")
    else:
        print_success(f"同步完成！")
    
    print_info(f"总计: 成功 {success_count} 个, 跳过 {skip_count} 个, 失败 {error_count} 个")
    
    # 按类别显示统计
    print("\n📊 按类别统计:")
    for category, stats in category_stats.items():
        total = stats['success'] + stats['skip'] + stats['error']
        if total > 0:
            print_info(f"  {category}: 成功 {stats['success']}, 跳过 {stats['skip']}, 失败 {stats['error']}")
    
    print("="*60)


def show_task_details(beat_schedule):
    """显示任务详情"""
    print("\n📋 任务配置详情:")
    print("-" * 80)
    
    for task_name, task_config in beat_schedule.items():
        print(f"任务名称: {task_name}")
        print(f"  - 任务: {task_config['task']}")
        print(f"  - 调度: {task_config['schedule']}")
        if task_config.get('args'):
            print(f"  - 参数: {task_config['args']}")
        if task_config.get('kwargs'):
            print(f"  - 关键字参数: {task_config['kwargs']}")
        print("-" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='同步 Celery Beat 配置到数据库 (支持静态配置和动态配置)')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际创建任务')
    parser.add_argument('--force', action='store_true', help='强制更新已存在的任务')
    parser.add_argument('--show-details', action='store_true', help='显示任务详情')
    parser.add_argument('--task', type=str, help='只同步指定的任务')
    
    args = parser.parse_args()
    
    print("🔄 Celery Beat 配置同步工具")
    print("支持静态配置和基于Django settings的动态配置")
    print("="*60)
    
    # 加载配置
    beat_schedule = load_beat_schedule()
    if not beat_schedule:
        sys.exit(1)
    
    # 显示任务详情
    if args.show_details:
        show_task_details(beat_schedule)
        return
    
    # 如果指定了特定任务
    if args.task:
        if args.task not in beat_schedule:
            print_error(f"任务 '{args.task}' 不存在于配置文件中")
            sys.exit(1)
        
        task_config = {args.task: beat_schedule[args.task]}
        print_info(f"只同步任务: {args.task}")
        sync_all_tasks(task_config, args.dry_run, args.force)
    else:
        # 同步所有任务
        sync_all_tasks(beat_schedule, args.dry_run, args.force)


if __name__ == '__main__':
    main()