#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Celery输出解析器

专门用于解析celery inspect命令的非标准输出格式
"""

import json
import re
from typing import Dict, List, Any, Optional

class CeleryOutputParser:
    """Celery输出解析器"""
    
    @staticmethod
    def parse_inspect_output(output: str) -> Dict[str, Any]:
        """
        解析celery inspect命令的输出
        
        输出格式示例:
        ->  worker_name@hostname: OK
            {json_data}
        或
        ->  worker_name@hostname: OK
            - empty -
        """
        if not output.strip():
            return {}
        
        workers = {}
        lines = output.split('\n')
        current_worker = None
        json_lines = []
        
        for line in lines:
            # 检查是否是worker行
            worker_match = re.match(r'^->\s+([^:]+):\s+OK\s*$', line.strip())
            if worker_match:
                # 保存之前worker的数据
                if current_worker and json_lines:
                    workers[current_worker] = CeleryOutputParser._parse_worker_data(json_lines)
                
                # 开始新worker
                current_worker = worker_match.group(1)
                json_lines = []
            elif current_worker:
                # 收集worker的数据行
                stripped_line = line.strip()
                if stripped_line and stripped_line != '- empty -':
                    json_lines.append(line)
        
        # 处理最后一个worker
        if current_worker and json_lines:
            workers[current_worker] = CeleryOutputParser._parse_worker_data(json_lines)
        elif current_worker:
            workers[current_worker] = []  # empty worker
        
        return workers
    
    @staticmethod
    def _parse_worker_data(json_lines: List[str]) -> Any:
        """解析单个worker的JSON数据"""
        try:
            # 合并所有行并尝试解析JSON
            json_text = '\n'.join(json_lines)
            # 去掉开头的空格缩进
            json_text = re.sub(r'^    ', '', json_text, flags=re.MULTILINE)
            
            if json_text.strip():
                return json.loads(json_text)
            else:
                return {}
        except json.JSONDecodeError:
            # 如果不是JSON，可能是任务列表格式
            return CeleryOutputParser._parse_task_list(json_lines)
    
    @staticmethod
    def _parse_task_list(lines: List[str]) -> List[Dict]:
        """解析任务列表格式"""
        tasks = []
        current_task = {}
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue
                
            # 尝试解析任务信息
            if stripped.startswith('['):
                # 任务开始标记
                if current_task:
                    tasks.append(current_task)
                current_task = {}
            elif ':' in stripped and current_task is not None:
                # 任务属性
                key, value = stripped.split(':', 1)
                current_task[key.strip()] = value.strip()
        
        if current_task:
            tasks.append(current_task)
        
        return tasks
    
    @staticmethod
    def count_total_tasks(workers_data: Dict[str, Any]) -> int:
        """统计总任务数"""
        total = 0
        for worker_name, data in workers_data.items():
            if isinstance(data, list):
                total += len(data)
            elif isinstance(data, dict) and 'total' in str(data):
                # 尝试从stats中提取任务数
                pass
        return total
    
    @staticmethod
    def extract_worker_stats(workers_data: Dict[str, Any]) -> Dict[str, Dict]:
        """从worker数据中提取统计信息"""
        stats = {}
        
        for worker_name, data in workers_data.items():
            if isinstance(data, dict):
                worker_stats = {
                    'task_succeeded': data.get('total', {}).get('task-succeeded', 0),
                    'task_failed': data.get('total', {}).get('task-failed', 0),
                    'task_received': data.get('total', {}).get('task-received', 0),
                    'task_rejected': data.get('total', {}).get('task-rejected', 0),
                }
                stats[worker_name] = worker_stats
        
        return stats
    
    @staticmethod
    def parse_ping_output(output: str) -> int:
        """解析ping命令输出，返回在线worker数量"""
        if not output.strip():
            return 0
        
        # 计算包含"OK"的行数
        ok_count = 0
        for line in output.split('\n'):
            if ': OK' in line and '->' in line:
                ok_count += 1
        
        return ok_count 