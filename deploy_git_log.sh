#!/bin/bash
# 记录发版git日志

set -e  # 遇到错误立即退出

# 配置
PROJECT_NAME="${PROJECT_NAME:-alita}"
DEPLOY_LOG_FILE="${DEPLOY_LOG_FILE:-deploy_log.txt}"
GIT_LOG_FILE="${GIT_LOG_FILE:-git_log.txt}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 记录部署开始
record_deploy_start() {
    local deploy_time=$(date '+%Y-%m-%d %H:%M:%S')
    local deploy_user="${DEPLOY_USER:-$(whoami)}"
    local current_branch=$(git branch --show-current)
    local current_commit=$(git rev-parse HEAD)
    local commit_short=$(git rev-parse --short HEAD)
    
    log_step "记录部署开始..."
    
    cat >> "$DEPLOY_LOG_FILE" << EOF

==========================================
部署开始时间: $deploy_time
部署用户: $deploy_user
项目名称: $PROJECT_NAME
当前分支: $current_branch
当前提交: $commit_short ($current_commit)
==========================================
EOF
    
    log_info "部署开始记录已保存到 $DEPLOY_LOG_FILE"
}

# 记录Git状态
record_git_status() {
    log_step "记录Git状态..."
    
    cat >> "$DEPLOY_LOG_FILE" << EOF

--- Git状态信息 ---
EOF
    
    # 记录当前分支
    echo "当前分支: $(git branch --show-current)" >> "$DEPLOY_LOG_FILE"
    
    # 记录远程仓库
    echo "远程仓库: $(git config --get remote.origin.url)" >> "$DEPLOY_LOG_FILE"
    
    # 记录是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        echo "状态: 有未提交的更改" >> "$DEPLOY_LOG_FILE"
        git status --short >> "$DEPLOY_LOG_FILE"
    else
        echo "状态: 工作区干净" >> "$DEPLOY_LOG_FILE"
    fi
    
    # 记录与远程的差异
    local ahead_behind=$(git rev-list --count --left-right @{u}...HEAD 2>/dev/null || echo "0 0")
    local behind=$(echo $ahead_behind | cut -d' ' -f1)
    local ahead=$(echo $ahead_behind | cut -d' ' -f2)
    echo "落后远程: $behind 个commit" >> "$DEPLOY_LOG_FILE"
    echo "领先远程: $ahead 个commit" >> "$DEPLOY_LOG_FILE"
}

# 拉取最新代码
pull_latest_code() {
    local target_branch="${1:-$(git branch --show-current)}"
    
    log_step "拉取最新代码..."
    
    log_info "目标分支: $target_branch"
    
    # 记录拉取前的状态
    local before_commit=$(git rev-parse HEAD)
    local before_commit_short=$(git rev-parse --short HEAD)
    
    echo "拉取前提交: $before_commit_short ($before_commit)" >> "$DEPLOY_LOG_FILE"
    
    # 执行拉取
    if git pull origin "$target_branch"; then
        log_info "代码拉取成功"
        
        # 记录拉取后的状态
        local after_commit=$(git rev-parse HEAD)
        local after_commit_short=$(git rev-parse --short HEAD)
        
        echo "拉取后提交: $after_commit_short ($after_commit)" >> "$DEPLOY_LOG_FILE"
        
        # 检查是否有更新
        if [ "$before_commit" != "$after_commit" ]; then
            log_info "检测到代码更新: $before_commit_short -> $after_commit_short"
            echo "代码已更新" >> "$DEPLOY_LOG_FILE"
        else
            log_warn "代码无更新"
            echo "代码无更新" >> "$DEPLOY_LOG_FILE"
        fi
    else
        log_error "代码拉取失败"
        echo "代码拉取失败" >> "$DEPLOY_LOG_FILE"
        return 1
    fi
}

# 记录最新提交信息
record_latest_commits() {
    log_step "记录最新提交信息..."
    
    cat >> "$DEPLOY_LOG_FILE" << EOF

--- 最新提交信息 ---
EOF
    
    # 获取最新的5个提交
    git log -5 --pretty=format:"%h - %an, %ar : %s" >> "$DEPLOY_LOG_FILE"
    
    # 获取最新提交的详细信息
    cat >> "$DEPLOY_LOG_FILE" << EOF

--- 最新提交详细信息 ---
EOF
    
    git log -1 --pretty=format:"提交Hash: %H%n简短Hash: %h%n作者: %an <%ae>%n提交时间: %ai%n提交信息: %s%n%n详细描述:%n%b" >> "$DEPLOY_LOG_FILE"
}

# 记录部署完成
record_deploy_end() {
    local deploy_time=$(date '+%Y-%m-%d %H:%M:%S')
    local deploy_note="${DEPLOY_NOTE:-}"
    
    log_step "记录部署完成..."
    
    cat >> "$DEPLOY_LOG_FILE" << EOF

==========================================
部署完成时间: $deploy_time
EOF

    if [ -n "$deploy_note" ]; then
        echo "部署备注: $deploy_note" >> "$DEPLOY_LOG_FILE"
    fi

    cat >> "$DEPLOY_LOG_FILE" << EOF
==========================================
EOF
    
    log_info "部署完成记录已保存到 $DEPLOY_LOG_FILE"
}

# 显示部署摘要
show_deploy_summary() {
    log_step "部署摘要:"
    echo
    echo "=========================================="
    echo "部署完成"
    echo "=========================================="
    echo "项目: $PROJECT_NAME"
    echo "分支: $(git branch --show-current)"
    echo "最新提交: $(git rev-parse --short HEAD)"
    echo "日志文件: $DEPLOY_LOG_FILE"
    echo "=========================================="
    echo
}

# 检查Git仓库
check_git_repo() {
    if [ ! -d ".git" ]; then
        log_error "当前目录不是Git仓库"
        return 1
    fi
}

# 完整部署流程
full_deploy() {
    local target_branch="${1:-}"
    
    log_info "开始完整部署流程..."
    
    # 检查Git仓库
    check_git_repo || return 1
    
    # 记录部署开始
    record_deploy_start
    
    # 记录Git状态
    record_git_status
    
    # 拉取最新代码
    pull_latest_code "$target_branch"
    
    # 记录最新提交信息
    record_latest_commits
    
    # 记录部署完成
    record_deploy_end
    
    # 显示部署摘要
    show_deploy_summary
    
    log_info "完整部署流程完成！"
}

# 只记录日志（不拉取代码）
log_only() {
    log_info "开始记录日志..."
    
    # 检查Git仓库
    check_git_repo || return 1
    
    # 记录部署开始
    record_deploy_start
    
    # 记录Git状态
    record_git_status
    
    # 记录最新提交信息
    record_latest_commits
    
    # 记录部署完成
    record_deploy_end
    
    # 显示部署摘要
    show_deploy_summary
    
    log_info "日志记录完成！"
}

# 只拉取代码（不记录日志）
pull_only() {
    local target_branch="${1:-}"
    
    log_info "开始拉取代码..."
    
    # 检查Git仓库
    check_git_repo || return 1
    
    # 拉取最新代码
    pull_latest_code "$target_branch"
    
    log_info "代码拉取完成！"
}

# 主函数
main() {
    local action="${1:-full}"
    local target_branch="${2:-}"
    
    case "$action" in
        "full")
            full_deploy "$target_branch"
            ;;
        "log-only")
            log_only
            ;;
        "pull-only")
            pull_only "$target_branch"
            ;;
        "help"|"-h"|"--help")
            echo "使用方法: $0 [动作] [分支]"
            echo "动作:"
            echo "  full      - 完整部署（拉取代码+记录日志）"
            echo "  log-only  - 只记录日志"
            echo "  pull-only - 只拉取代码"
            echo "  help      - 显示帮助"
            echo "示例:"
            echo "  $0 full"
            echo "  $0 full main"
            echo "  $0 log-only"
            echo "  $0 pull-only develop"
            ;;
        *)
            log_error "未知的动作: $action"
            echo "使用 '$0 help' 查看帮助"
            return 1
            ;;
    esac
}

# 如果直接执行此脚本，则运行main函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 