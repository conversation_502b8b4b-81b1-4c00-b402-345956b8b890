#!/usr/bin/env python3
"""
Worker配置初始化脚本

用于创建示例的Worker节点配置，演示分布式Worker管理功能。

使用方法：
  # 使用指定设置和节点名称
  python init_worker_config.py --settings=alita.settings.test server_a
  
  # 只指定设置模块，节点名称自动生成
  python init_worker_config.py --settings=alita.settings.test
  
  # 使用环境变量设置，指定节点名称
  export DJANGO_SETTINGS_MODULE=alita.settings.test
  python init_worker_config.py server_a
  
  # 完全使用默认值
  python init_worker_config.py
"""

import os
import sys
import django

# 解析命令行参数获取设置模块和节点名称
settings_module = None
node_name_arg = None

# 解析命令行参数
for i, arg in enumerate(sys.argv[1:], 1):
    if arg.startswith('--settings='):
        settings_module = arg.split('=', 1)[1]
    elif not arg.startswith('--'):
        # 位置参数作为节点名称
        node_name_arg = arg

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 优先级：命令行参数 > 环境变量 > 默认值
if settings_module:
    os.environ['DJANGO_SETTINGS_MODULE'] = settings_module
    print(f"🔧 使用命令行指定的设置: {settings_module}")
elif 'DJANGO_SETTINGS_MODULE' in os.environ:
    settings_module = os.environ['DJANGO_SETTINGS_MODULE']
    print(f"🔧 使用环境变量设置: {settings_module}")
else:
    settings_module = 'alita.settings.develop'
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', settings_module)
    print(f"🔧 使用默认设置: {settings_module}")

# 确定节点名称：命令行参数 > settings_module生成
if node_name_arg:
    node_name = node_name_arg
    print(f"🏷️  使用命令行指定的节点名称: {node_name}")
else:
    node_name = settings_module.replace('.', '_')
    print(f"🏷️  使用设置模块生成的节点名称: {node_name}")

django.setup()

from task.models import WorkerNode

def init_worker_configurations():
    """初始化Worker配置"""
    
    global node_name  # 声明使用全局变量
    
    print("🚀 开始初始化Worker配置...")
    
    # 清理已有配置
    WorkerNode.objects.all().delete()
    print("✅ 清理已有配置完成")
    
    # 基于 Legacy 模式的 Worker 配置
    server_a_workers = [
        {
            'name': 'celery_default_worker',
            'worker_type': 'default',
            'description': '主要默认Worker，处理一般任务',
            'node_name': node_name,
            'node_type': 'server',
            'queues': 'default',
            'concurrency': 40,  # 匹配 legacy 的并发数
            'pool_type': 'threads',
            'log_level': 'ERROR',
            'status': 'active'
        },
        {
            'name': 'beat_scheduler',
            'worker_type': 'beat',
            'description': 'Beat调度器，管理定时任务',
            'node_name': node_name,
            'node_type': 'server',
            'queues': '',  # Beat不需要队列
            'concurrency': 1,
            'pool_type': 'threads',
            'log_level': 'ERROR',
            'status': 'active'
        },
        {
            'name': 'celery_log_worker',
            'worker_type': 'log',
            'description': '日志Worker，处理日志队列',
            'node_name': node_name,
            'node_type': 'server',
            'queues': 'logqueue',
            'concurrency': 10,
            'pool_type': 'threads',
            'log_level': 'INFO',
            'status': 'active'
        },
        {
            'name': 'celery_track_worker',
            'worker_type': 'track',
            'description': '跟踪Worker，处理跟踪队列',
            'node_name': node_name,
            'node_type': 'server',
            'queues': 'trackqueue',
            'concurrency': 15,
            'pool_type': 'threads',
            'log_level': 'ERROR',
            'status': 'active'
        },
        {
            'name': 'sync_wms_worker',
            'worker_type': 'track',
            'description': '同步wms Worker，处理跟踪队列',
            'node_name': node_name,
            'node_type': 'server',
            'queues': 'sync_wms_queue',
            'concurrency': 15,
            'pool_type': 'threads',
            'log_level': 'ERROR',
            'status': 'active'
        },
    ]
    
    # 添加8个标签Worker，每个处理5个队列（匹配 legacy 模式）
    for i in range(1, 9):  # 1到8
        start_queue = (i - 1) * 5 + 1
        end_queue = i * 5
        queues = ','.join([f'label_queue_{j}' for j in range(start_queue, end_queue + 1)])
        
        label_worker = {
            'name': f'celery_label_worker_{i}',
            'worker_type': 'label',
            'description': f'标签Worker组{i}，处理标签队列{start_queue}-{end_queue}',
            'node_name': node_name,
            'node_type': 'server',
            'queues': queues,
            'concurrency': 5,  # 匹配 legacy 的并发数
            'pool_type': 'threads',
            'log_level': 'ERROR',
            'status': 'active'
        }
        server_a_workers.append(label_worker)

    
    # 创建所有Worker配置
    all_workers = server_a_workers
    
    created_count = 0
    for worker_config in all_workers:
        try:
            worker = WorkerNode.objects.create(**worker_config)
            print(f"✅ 创建Worker: {worker.name} ({worker.node_name})")
            created_count += 1
        except Exception as e:
            print(f"❌ 创建Worker失败: {worker_config['name']}, 错误: {str(e)}")
    
    print(f"\n🎉 Worker配置初始化完成！")
    print(f"   成功创建: {created_count} 个Worker配置")
    print(f"   涵盖节点: {len(set(w['node_name'] for w in all_workers))} 个")
    print(f"   节点名称: {node_name}")
    
    # 按节点统计
    print(f"\n📊 节点分布统计:")
    node_stats = {}
    for worker in WorkerNode.objects.all():
        if worker.node_name not in node_stats:
            node_stats[worker.node_name] = {
                'count': 0,
                'types': set(),
                'concurrency': 0,
                'node_type': worker.node_type
            }
        node_stats[worker.node_name]['count'] += 1
        node_stats[worker.node_name]['types'].add(worker.worker_type)
        node_stats[worker.node_name]['concurrency'] += worker.concurrency
    
    for node_name, stats in node_stats.items():
        types_str = ', '.join(stats['types'])
        icon = '🖥️' if stats['node_type'] == 'server' else '🐳' if stats['node_type'] == 'docker' else '☸️'
        print(f"   {icon} {node_name}: {stats['count']}个Worker, 总并发{stats['concurrency']}, 类型[{types_str}]")
    
    # 按类型统计
    print(f"\n🔧 Worker类型统计:")
    type_stats = {}
    for worker in WorkerNode.objects.all():
        if worker.worker_type not in type_stats:
            type_stats[worker.worker_type] = {'count': 0, 'concurrency': 0, 'queues': set()}
        type_stats[worker.worker_type]['count'] += 1
        type_stats[worker.worker_type]['concurrency'] += worker.concurrency
        if worker.queues:
            type_stats[worker.worker_type]['queues'].update(worker.queues.split(','))
    
    for worker_type, stats in type_stats.items():
        queues_info = f"队列{len(stats['queues'])}个" if stats['queues'] else "无队列"
        if worker_type == 'label':
            print(f"   📊 {worker_type}: {stats['count']}个Worker, 总并发{stats['concurrency']}, {queues_info}")
        elif worker_type == 'default':
            print(f"   ⚙️  {worker_type}: {stats['count']}个Worker, 总并发{stats['concurrency']}, {queues_info}")
        elif worker_type == 'beat':
            print(f"   ⏰ {worker_type}: {stats['count']}个Worker, {queues_info}")
        elif worker_type == 'log':
            print(f"   📝 {worker_type}: {stats['count']}个Worker, 总并发{stats['concurrency']}, {queues_info}")
        elif worker_type == 'track':
            print(f"   🔍 {worker_type}: {stats['count']}个Worker, 总并发{stats['concurrency']}, {queues_info}")
    
    print(f"\n💡 使用说明:")
    print(f"   1. 访问管理界面: http://localhost:8000/api/task/dashboard/")
    print(f"   2. 切换到'Worker管理'标签页查看配置")
    print(f"   3. 生成supervisor配置: 点击节点操作中的'📄'按钮")
    print(f"   4. 使用数据库模式生成配置:")
    print(f"      python generate_supervisor_config.py {node_name} --db")
    print(f"   5. 使用传统模式生成配置:")
    print(f"      python generate_supervisor_config.py {node_name} --legacy")
    print(f"\n🔧 命令行参数:")
    print(f"   python init_worker_config.py [--settings=模块] [节点名称]")
    print(f"   --settings=模块: 指定Django设置模块 (如: --settings=alita.settings.test)")
    print(f"   节点名称: 指定Worker节点名称 (如: server_a)")
    print(f"   💡 设置模块优先级: 命令行参数 > 环境变量 > 默认值")
    print(f"   💡 节点名称优先级: 命令行参数 > 设置模块生成")
    print(f"   💡 当前使用的节点名称: {node_name}")
    print(f"\n📚 使用示例:")
    print(f"   # 指定设置和节点名称")
    print(f"   python init_worker_config.py --settings=alita.settings.test server_a")
    print(f"   # 只指定设置，节点名称自动生成")
    print(f"   python init_worker_config.py --settings=alita.settings.test")
    print(f"   # 只指定节点名称，使用默认设置")
    print(f"   python init_worker_config.py server_a")
    print(f"   # 完全默认")
    print(f"   python init_worker_config.py")
    
    return created_count

if __name__ == '__main__':
    try:
        count = init_worker_configurations()
        print(f"\n✨ 初始化成功完成，共创建 {count} 个Worker配置")
    except Exception as e:
        print(f"\n❌ 初始化失败: {str(e)}")
        sys.exit(1) 