#!/usr/bin/env python3
"""
Supervisor配置生成脚本

用于生成Celery Worker的Supervisor配置文件，支持数据库模式和传统模式。

数据库模式特性：
  - 如果指定节点没有Worker配置，会自动调用init_worker_config.py脚本初始化
  - 保持代码单一职责，避免重复实现初始化逻辑
  - 自动传递当前设置模块和节点名称给初始化脚本

使用方法：
  python generate_supervisor_config.py --settings=alita.settings.test --db
  或者设置环境变量：
  export DJANGO_SETTINGS_MODULE=alita.settings.test
  python generate_supervisor_config.py --db
"""

import os
import socket
import sys
import django
import subprocess

# 解析命令行参数获取设置模块
settings_module = None
for arg in sys.argv[1:]:
    if arg.startswith('--settings='):
        settings_module = arg.split('=', 1)[1]
        break

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 优先级：命令行参数 > 环境变量 > 默认值
if settings_module:
    os.environ['DJANGO_SETTINGS_MODULE'] = settings_module
    print(f"🔧 使用命令行指定的设置: {settings_module}")
elif 'DJANGO_SETTINGS_MODULE' in os.environ:
    print(f"🔧 使用环境变量设置: {os.environ['DJANGO_SETTINGS_MODULE']}")
else:
    profile = os.environ.get('PROJECT_SETTINGS', 'develop')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'alita.settings.%s' % profile)
    print(f"🔧 使用默认设置: alita.settings.%s" % profile)

django.setup()

SUPERVISOR_TEMPLATE = '''
[unix_http_server]
file=/tmp/supervisor_celery.sock
chmod=0700

[supervisorctl]
serverurl=unix:///tmp/supervisor_celery.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisord]
daemonize=true
logfile={log_dir}/supervisord_celery.log
logfile_maxbytes=1024MB
logfile_backups=10
pidfile={log_dir}/supervisord_celery.pid

{workers_config}
'''

def init_worker_configurations_for_node(node_name):
    """通过调用init_worker_config.py脚本为指定节点初始化Worker配置"""
    try:
        print(f"🚀 调用init_worker_config.py为节点 {node_name} 初始化Worker配置...")
        
        # 构建调用init_worker_config.py的命令
        current_dir = os.path.dirname(os.path.abspath(__file__))
        init_script = os.path.join(current_dir, 'init_worker_config.py')
        
        # 准备命令参数
        cmd_args = [sys.executable, init_script]
        
        # 传递当前的设置模块
        if 'DJANGO_SETTINGS_MODULE' in os.environ:
            cmd_args.extend([f"--settings={os.environ['DJANGO_SETTINGS_MODULE']}"])
        
        # 传递节点名称
        cmd_args.append(node_name)
        
        print(f"🔧 执行命令: {' '.join(cmd_args)}")
        
        # 调用初始化脚本
        result = subprocess.run(
            cmd_args,
            capture_output=True,
            text=True,
            cwd=current_dir
        )
        
        # 输出脚本的标准输出
        if result.stdout:
            print("📄 初始化脚本输出:")
            for line in result.stdout.strip().split('\n'):
                print(f"   {line}")
        
        # 检查执行结果
        if result.returncode == 0:
            # 从数据库验证配置是否创建成功
            from task.models import WorkerNode
            worker_count = WorkerNode.objects.filter(node_name=node_name, status='active').count()
            print(f"✅ 初始化成功完成，节点 {node_name} 现有 {worker_count} 个活跃Worker配置")
            return worker_count
        else:
            print(f"❌ 初始化脚本执行失败，返回码: {result.returncode}")
            if result.stderr:
                print("错误信息:")
                for line in result.stderr.strip().split('\n'):
                    print(f"   {line}")
            return 0
            
    except FileNotFoundError:
        print(f"❌ 找不到初始化脚本: {init_script}")
        return 0
    except ImportError:
        print("❌ 无法导入Django模型，请确保数据库配置正确")
        return 0
    except Exception as e:
        print(f"❌ 调用初始化脚本失败: {str(e)}")
        return 0

def generate_config_from_database(node_name):
    """从数据库生成指定节点的Worker配置"""
    try:
        from task.models import WorkerNode
        
        # 获取该节点的所有活跃Worker配置
        workers = WorkerNode.objects.filter(
            node_name=node_name,
            status='active'
        ).order_by('worker_type', 'name')
        
        if not workers.exists():
            print(f"⚠️  节点 {node_name} 没有找到任何活跃的Worker配置，开始初始化...")
            # 调用初始化函数创建默认配置
            init_count = init_worker_configurations_for_node(node_name)
            if init_count > 0:
                print(f"✅ 初始化完成，创建了 {init_count} 个Worker配置")
                # 重新查询Worker配置
                workers = WorkerNode.objects.filter(
                    node_name=node_name,
                    status='active'
                ).order_by('worker_type', 'name')
                
                if not workers.exists():
                    print(f"❌ 初始化后仍然没有找到Worker配置")
                    return None
            else:
                print(f"❌ 初始化失败，没有创建任何Worker配置")
                return None
        
        # 生成workers配置段
        workers_config = ""
        for worker in workers:
            program_name = worker.generate_supervisor_program_name()
            
            if worker.worker_type == 'beat':
                command = worker.generate_beat_command()
            else:
                command = worker.generate_worker_command()
            
            workers_config += f'''
[program:{program_name}]
command={command}
autostart=true
autorestart={str(worker.autorestart).lower()}
stopwaitsecs={worker.stop_wait_secs}
stopsignal=TERM
stderr_logfile={log_dir}/{worker.name}.log
stdout_logfile={log_dir}/{worker.name}_out.log
stderr_logfile_maxbytes={worker.log_file_max_bytes}
stdout_logfile_maxbytes={worker.log_file_max_bytes}
stderr_logfile_backups={worker.log_file_backups}
stdout_logfile_backups={worker.log_file_backups}
'''
        
        print(f"✅ 从数据库为节点 {node_name} 生成了 {workers.count()} 个Worker配置")
        return workers_config
        
    except ImportError:
        print("❌ 无法导入Django模型，请确保数据库配置正确")
        return None
    except Exception as e:
        print(f"❌ 从数据库生成配置失败: {str(e)}")
        return None

def generate_legacy_config():
    """生成传统的静态配置（兼容原有逻辑）"""
    
    LABEL_WORKER_TEMPLATE = '''[program:celery_label_worker_{worker_num}_{hostname}]
command=celery -A alita worker --loglevel=ERROR -P threads -c 5 -Q {queues} -n label_worker_{worker_num}@{hostname}
autostart=true
autorestart=true
stopwaitsecs=8
stopsignal=TERM
stderr_logfile={log_dir}/celery_label_worker_{worker_num}.log
stdout_logfile={log_dir}/celery_label_worker_{worker_num}.out.log
stderr_logfile_maxbytes=1024MB
stdout_logfile_maxbytes=1024MB
stderr_logfile_backups=10
stdout_logfile_backups=10
'''

    def generate_label_workers(hostname, num_workers=8, queues_per_worker=5):
        label_workers = []
        for i in range(1, num_workers + 1):
            start_queue = (i - 1) * queues_per_worker + 1
            end_queue = i * queues_per_worker
            queues = ','.join([f'label_queue_{j}' for j in range(start_queue, end_queue + 1)])
            worker_config = LABEL_WORKER_TEMPLATE.format(
                worker_num=i,
                queues=queues,
                hostname=hostname,
                log_dir=log_dir
            )
            label_workers.append(worker_config)
        return '\n'.join(label_workers)

    # 先生成label_workers
    label_workers = generate_label_workers(hostname)

    # 生成默认Worker配置
    workers_config = f'''
[program:celery_default_worker_{hostname}]
command=celery -A alita worker --loglevel=ERROR -P threads -c 40 -Q default -n default_worker@{hostname}
autostart=true
autorestart=true
stopwaitsecs=8
stopsignal=TERM
stderr_logfile={log_dir}/celery_default_worker.log
stdout_logfile={log_dir}/celery_default_worker.out.log
stderr_logfile_maxbytes=1024MB
stdout_logfile_maxbytes=1024MB
stderr_logfile_backups=10
stdout_logfile_backups=10

[program:celery_beat_{hostname}]
command=celery -A alita beat --loglevel=ERROR -S django
autostart=true
autorestart=true
stderr_logfile={log_dir}/celery_beat.log
stdout_logfile={log_dir}/celery_beat.out.log

[program:celery_logworker_{hostname}]
command=celery -A alita worker --loglevel=ERROR -P threads -c 10 -Q logqueue -n logworker@{hostname}
autostart=true
autorestart=true
stopwaitsecs=5
stopsignal=TERM
stderr_logfile={log_dir}/celery_logworker.log
stdout_logfile={log_dir}/celery_logworker.out.log
stderr_logfile_maxbytes=1024MB
stdout_logfile_maxbytes=1024MB
stderr_logfile_backups=10
stdout_logfile_backups=10

[program:celery_trackworker_{hostname}]
command=celery -A alita worker --loglevel=ERROR -P threads -c 10 -Q trackqueue -n trackworker@{hostname}
autostart=true
autorestart=true
stopwaitsecs=5
stopsignal=TERM
stderr_logfile={log_dir}/celery_trackworker.log
stdout_logfile={log_dir}/celery_trackworker.out.log
stderr_logfile_maxbytes=1024MB
stdout_logfile_maxbytes=1024MB
stderr_logfile_backups=10
stdout_logfile_backups=10

{label_workers}
'''
    
    return workers_config.format(
        hostname=hostname,
        log_dir=log_dir,
        label_workers=label_workers
    )

def main():
    global hostname, log_dir
    
    # 解析命令行参数
    use_database = '--db' in sys.argv or '--database' in sys.argv
    legacy_mode = '--legacy' in sys.argv
    
    # 获取主机名
    if len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
        hostname = sys.argv[1]
        print(f"✅ 使用指定主机名: {hostname}")
    else:
        hostname = socket.gethostname()
        print(f"📋 使用系统主机名: {hostname}")
    
    # 设置日志目录
    log_dir = os.environ.get('LOG_ROOT', '/tmp')
    print(f"📁 日志目录: {log_dir}")
    
    # 选择配置生成模式
    if use_database and not legacy_mode:
        print("🔄 使用数据库模式生成配置...")
        workers_config = generate_config_from_database(hostname)
        if workers_config is None:
            print("⚠️  数据库模式失败，回退到传统模式...")
            workers_config = generate_legacy_config()
    else:
        print("🔄 使用传统模式生成配置...")
        workers_config = generate_legacy_config()
    
    # 生成完整配置
    config = SUPERVISOR_TEMPLATE.format(
        hostname=hostname,
        log_dir=log_dir,
        workers_config=workers_config
    )
    
    # 写入配置文件
    config_file = f'supervisord_celery.conf'
    with open(config_file, 'w') as f:
        f.write(config)
    
    print(f"🎯 配置文件已生成: {config_file}")
    print(f"🏷️  主机名标识: {hostname}")
    print(f"💾 配置模式: {'数据库模式' if use_database and not legacy_mode else '传统模式'}")
    
    # 显示使用帮助
    print("\n📖 使用方法:")
    print(f"   启动: supervisord -c {config_file}")
    print(f"   控制: supervisorctl -c {config_file} status")
    print(f"   重载: supervisorctl -c {config_file} reread && supervisorctl -c {config_file} update")
    print(f"   停止: supervisorctl -c {config_file} shutdown")
    
    print("\n🔧 命令行参数:")
    print("   python generate_supervisor_config.py [hostname] [选项]")
    print("   --db/--database: 使用数据库配置模式")
    print("   --legacy: 强制使用传统配置模式")
    print("   --settings=模块: 指定Django设置模块 (如: --settings=alita.settings.test)")
    print("\n💡 数据库模式特性:")
    print("   - 如果指定节点没有Worker配置，会自动调用init_worker_config.py脚本初始化")
    print("   - 自动初始化包含: 默认Worker、Beat调度器、日志Worker、跟踪Worker、8个标签Worker")
    print("   - 初始化完成后会自动使用新配置生成supervisor配置文件")
    print("   - 保持代码单一职责，避免重复实现初始化逻辑")
    print("\n💡 设置模块优先级: 命令行参数 > 环境变量 > 默认值")

if __name__ == '__main__':
    main() 