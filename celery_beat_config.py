from celery.schedules import crontab
from datetime import timedelta
from celery.schedules import schedule


def get_dynamic_beat_schedule():
    """
    动态生成Beat任务配置
    根据Django设置自动生成循环任务配置
    """
    try:
        from django.conf import settings
        
        # 读取配置参数
        mode_key_default_num = getattr(settings, 'MODE_KEY_DEFAULT_NUM', 5)
        parcel_mode_key_default_num = getattr(settings, 'PARCEL_MODE_KEY_DEFAULT_NUM', 5)
        customer_order_times = getattr(settings, 'CUSTOMER_ORDER_TIMES', 30.0)
        parcel_customer_order_times = getattr(settings, 'PARCEL_CUSTOMER_ORDER_TIMES', 30.0)
        system_order_mark = getattr(settings, 'SYSTEM_ORDER_MARK', 'FX')
        mode_key_sync_order_num = getattr(settings, 'MODE_KEY_SYNC_ORDER_NUM', 15)
        customer_order_sync_times = getattr(settings, 'CUSTOMER_ORDER_SYNC_TIMES', 25)
        # 添加出库面单任务配置参数
        outbound_label_thread_num = getattr(settings, 'OUTBOUND_LABEL_THREAD_NUM', 5)
        outbound_label_interval = getattr(settings, 'OUTBOUND_LABEL_INTERVAL', 30.0)
        
        # 添加轨迹任务配置参数
        system_version = getattr(settings, 'SYSTEM_VERSION', 'V1')
        
        dynamic_tasks = {}
        
        # 1. 创建面单相关任务 - 根据 MODE_KEY_DEFAULT_NUM 循环生成
        for i in range(mode_key_default_num):
            thread_id = str(i + 1)
            
            # 创建面单任务
            dynamic_tasks[f'创建面单线程{thread_id}'] = {
                'task': 'apps.order.tasks.createLabelTask',
                'schedule': customer_order_times,
                'args': (thread_id,),
            }
            
            # 获取面单任务
            dynamic_tasks[f'获取面单线程{thread_id}'] = {
                'task': 'apps.order.tasks.getLabelTask',
                'schedule': customer_order_times,
                'args': (thread_id,),
            }
            
            # 同步面单任务
            dynamic_tasks[f'同步面单线程{thread_id}'] = {
                'task': 'apps.order.tasks.sync_label_task',
                'schedule': customer_order_times,
                'args': (thread_id,),
            }
        
        # 2. 小包面单相关任务 - 根据 PARCEL_MODE_KEY_DEFAULT_NUM 循环生成
        for i in range(parcel_mode_key_default_num):
            thread_id = str(i + 1)
            
            # 创建小包面单任务
            dynamic_tasks[f'创建小包面单线程{thread_id}'] = {
                'task': 'apps.order.tasks.create_parcel_label_task',
                'schedule': parcel_customer_order_times,
                'args': (thread_id,),
            }
            
            # 获取小包面单任务
            dynamic_tasks[f'获取小包面单线程{thread_id}'] = {
                'task': 'apps.order.tasks.get_parcel_label_task',
                'schedule': parcel_customer_order_times,
                'args': (thread_id,),
            }
        
        # 3. 其他单线程任务
        dynamic_tasks.update({
            '创建小包确认定时任务1': {
                'task': 'apps.order.tasks.confirm_parcel_label_task',
                'schedule': customer_order_times,
                'args': ('1',),
            },
            '更新小包重量线程1': {
                'task': 'apps.order.tasks.update_parcel_task',
                'schedule': customer_order_times,
                'args': ('1',),
            },
            '创建大包面单线程1': {
                'task': 'apps.order.tasks.create_big_parcel_label_task',
                'schedule': customer_order_times,
                'args': ('1',),
            },
            '获取大包面单线程1': {
                'task': 'apps.order.tasks.get_big_parcel_label_task',
                'schedule': customer_order_times,
                'args': ('1',),
            },
            '创建asendia大包面单线程1': {
                'task': 'apps.order.tasks.create_big_parcel_label_task_for_asendia',
                'schedule': customer_order_times,
                'args': ('1',),
            },
            '获取asendia大包面单线程1': {
                'task': 'apps.order.tasks.get_big_parcel_label_task_for_asendia',
                'schedule': customer_order_times,
                'args': ('1',),
            },
            '创建 rd_post 大包单线程': {
                'task': 'apps.order.tasks.create_big_parcel_label_task_for_rd_post',
                'schedule': customer_order_times,
                'args': ('1',),
            },
        })
        
        # 3.1. 出库面单相关任务 - 根据 OUTBOUND_LABEL_THREAD_NUM 循环生成
        for i in range(outbound_label_thread_num):
            thread_id = str(i + 1)
            
            # 创建出库面单任务
            dynamic_tasks[f'创建出库面单线程{thread_id}'] = {
                'task': 'apps.oms.tasks.create_outbound_order_label_task',
                'schedule': outbound_label_interval,
                'args': (thread_id,),
            }
            
            # 获取出库面单任务
            dynamic_tasks[f'获取出库面单线程{thread_id}'] = {
                'task': 'apps.oms.tasks.get_outbound_order_label_task',
                'schedule': outbound_label_interval,
                'args': (thread_id,),
            }
        
        # 3.2. 轨迹相关任务 - 根据 SYSTEM_VERSION 配置不同的执行策略
        if system_version == 'V2':
            # V2版本：每2小时重置拉单次数
            dynamic_tasks['重置轨迹拉单次数V2'] = {
                'task': 'apps.track.tasks.handler_tracking_pull_times',
                'schedule': crontab(minute=0, hour='*/2'),  # 每2小时
                'args': ('1',),
            }
            
            # V2版本：启动15个获取轨迹任务，每1秒执行一次
            for i in range(15):
                thread_id = str(i + 1)
                dynamic_tasks[f'获取轨迹线程{thread_id}'] = {
                    'task': 'apps.track.tasks.get_track_info',
                    'schedule': 1.0,  # 每1秒
                    'args': (thread_id,),
                }
        else:
            # 其他版本：固定时间点重置拉单次数
            dynamic_tasks['重置轨迹拉单次数'] = {
                'task': 'apps.track.tasks.handler_tracking_pull_times',
                'schedule': crontab(hour='3,9,15,21', minute=0),  # 每6小时的固定时间点
                'args': ('1',),
            }
            
            # 其他版本：启动3个获取轨迹任务，每5秒执行一次
            for i in range(3):
                thread_id = str(i + 1)
                dynamic_tasks[f'获取轨迹线程{thread_id}'] = {
                    'task': 'apps.track.tasks.get_track_info',
                    'schedule': 5.0,  # 每5秒
                    'args': (thread_id,),
                }
        
        # 4. 51tracking轨迹任务 - 固定2个线程
        for i in range(2):
            thread_id = str(i + 1)
            dynamic_tasks[f'获取51轨迹线程{thread_id}'] = {
                'task': 'apps.order.tasks.handler_get_51tracking_info',
                'schedule': parcel_customer_order_times,
                'args': (thread_id,),
            }
        
        # 5. 17track相关任务
        dynamic_tasks.update({
            '推送17转单号线程': {
                'task': 'apps.order.tasks.handler_push_parcel_17_tracking_num',
                'schedule': 5.0,
                'args': ('1',),
            },
            '获取17轨迹线程': {
                'task': 'apps.order.tasks.handler_get_17track_info',
                'schedule': 5.0,
                'args': (),
            },
        })
        
        # 6. ScanForm任务 - 固定5个线程
        for i in range(5):
            thread_id = str(i + 1)
            dynamic_tasks[f'创建ScanForm_beat任务{thread_id}'] = {
                'task': 'apps.order.tasks.create_beat_scan_form_task',
                'schedule': parcel_customer_order_times,
                'args': (thread_id,),
            }
        
        # 7. 定时取消面单任务
        dynamic_tasks.update({
            '生成二次取消小包单面单任务': {
                'task': 'apps.order.tasks.generate_cancel_parcel_order_label_tasks',
                'schedule': crontab(hour='6,12,0', minute=0),
                'args': (),
            },
            '执行二次取消小包单面单任务': {
                'task': 'apps.order.tasks.cancel_label_with_cancelled_order',
                'schedule': 300.0,  # 5分钟
                'args': (),
            },
            '自动作废小包单': {
                'task': 'apps.order.tasks.auto_cancel_parcel_customer_order',
                'schedule': crontab(hour='12,3', minute=20),
                'args': (),
            },
            '发布小包单导出任务': {
                'task': 'apps.order.tasks.push_parcel_customer_order_export_task',
                'schedule': crontab(minute=20),
                'args': (),
            },
        })
        
        # 8. 根据系统标识添加特定任务
        if system_order_mark in ['MZ', 'YQF']:
            # 同步订单相关任务 - 根据 MODE_KEY_SYNC_ORDER_NUM 循环生成
            for i in range(mode_key_sync_order_num):
                thread_id = str(i + 1)
                
                # 同步订单任务
                dynamic_tasks[f'创建同步订单线程{thread_id}'] = {
                    'task': 'apps.order.tasks.sync_customer_order_task',
                    'schedule': customer_order_sync_times,
                    'args': (thread_id,),
                }
                
                # 同步供应商订单任务
                dynamic_tasks[f'创建同步供应商订单件重体和轨迹线程{thread_id}'] = {
                    'task': 'apps.order.tasks.sync_order_data_to_supplier',
                    'schedule': customer_order_sync_times,
                    'args': (thread_id,),
                }
                
                # 同步出口报关单任务
                dynamic_tasks[f'创建同步出口报关单线程{thread_id}'] = {
                    'task': 'apps.order.tasks.sync_clearance_out_data_to_supplier',
                    'schedule': customer_order_sync_times,
                    'args': (thread_id,),
                }
        
        # 9. 麦点系统保险订单任务
        if system_order_mark in ['MM']:
            for i in range(mode_key_default_num):
                thread_id = str(i + 1)
                dynamic_tasks[f'创建保险抓单线程{thread_id}'] = {
                    'task': 'apps.order.tasks.create_insurance_order_task',
                    'schedule': customer_order_times,
                    'args': (thread_id,),
                }
        
        # 10. 中辉系统清关订单任务
        if system_order_mark in ['ZH']:
            dynamic_tasks['创建清关订单1'] = {
                'task': 'apps.order.tasks.create_customs_clearance_order',
                'schedule': 5.0,
                'args': ('1',),
            }
        
        # 11. 产品相关任务
        dynamic_tasks['检查产品价格版本任务'] = {
            'task': 'apps.pms.tasks.check_mul_product_price_version_task',
            'schedule': 60.0,
            'args': (),
        }
        
        return dynamic_tasks
        
    except Exception as e:
        print(f"警告：生成动态任务配置失败: {str(e)}")
        return {}


# 静态配置的任务 - 直接写死的配置
static_beat_schedule = {
    'handler_parcel_order_cost_task': {
        'task': 'apps.task.tasks.handler_parcel_order_cost_task',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_parcel_order_revenue_task': {
        'task': 'apps.task.tasks.handler_parcel_order_revenue_task',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_export_task': {
        'task': 'apps.task.tasks.handler_export_task',
        'schedule': crontab(minute='*/5'),
        'args': (),
    },
    'handler_payment_detail': {
        'task': 'apps.task.tasks.handler_payment_detail',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_invoice_detail': {
        'task': 'apps.task.tasks.handler_invoice_detail',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_summary_report': {
        'task': 'apps.task.tasks.handler_summary_report',
        'schedule': crontab(minute=0, hour='*/1'),
        'args': (),
    },
    'handler_reconciliation_task': {
        'task': 'apps.task.tasks.handler_reconciliation_task',
        'schedule': crontab(minute='*/1'),
        'args': (1,),
    },
    'handler_reconciliation_task2': {
        'task': 'apps.task.tasks.handler_reconciliation_task',
        'schedule': crontab(minute='*/1'),
        'args': (2,),
    },
    'handler_statement_compared_task': {
        'task': 'apps.task.tasks.handler_statement_compared_task',
        'schedule': crontab(minute='*/1'),
        'args': (1,),
    },
    'handler_statement_compared_task2': {
        'task': 'apps.task.tasks.handler_statement_compared_task',
        'schedule': crontab(minute='*/1'),
        'args': (2,),
    },
    'handler_summary_day_report': {
        'task': 'apps.task.tasks.handler_summary_day_report',
        'schedule': crontab(minute=0, hour=0),
        'args': (),
    },
    'handler_parcel_profit_report': {
        'task': 'apps.task.tasks.handler_parcel_profit_report',
        'schedule': crontab(minute=0, hour='*/1'),
        'args': (),
    },
    'handler_customer_profit_report': {
        'task': 'apps.task.tasks.handler_customer_profit_report',
        'schedule': crontab(minute=0, hour='*/1'),
        'args': (),
    },
    'handler_clearance_profit_report': {
        'task': 'apps.task.tasks.handler_clearance_profit_report',
        'schedule': crontab(minute=0, hour='*/1'),
        'args': (),
    },
    'handler_adjust_profit_report': {
        'task': 'apps.task.tasks.handler_adjust_profit_report',
        'schedule': crontab(minute=0, hour='*/1'),
        'args': (),
    },
    'handler_generate_debit_detail_file': {
        'task': 'apps.task.tasks.handler_generate_debit_detail_file',
        'schedule': crontab(minute='*/2'),
        'args': (),
    },
    'handler_push_wechat_message': {
        'task': 'apps.wechat.tasks.handler_push_wechat_message',
        'schedule': crontab(minute='*/2'),
        'args': (),
    },
    'handler_clearance_aging': {
        'task': 'apps.task.tasks.handler_clearance_aging',
        'schedule': crontab(minute=0, hour=0),
        'args': (),
    },
    'common_upload_order_handle': {
        'task': 'apps.task.tasks.common_upload_order_handle',
        'schedule': schedule(run_every=1),
        'args': (),
    },
    'common_order_async_task_handle': {
        'task': 'apps.task.tasks.common_order_async_task_handle',
        'schedule': timedelta(seconds=30),
        'args': (),
    },
    'handler_push_label': {
        'task': 'apps.order.tasks.handler_push_label',
        'schedule': crontab(minute='*/3'),
        'args': (),
    },
    'handler_push_error_label_info': {
        'task': 'apps.order.tasks.handler_push_error_label_info',
        'schedule': crontab(minute='*/3'),
        'args': (),
    },
    'handler_ems_order': {
        'task': 'apps.order.tasks.handler_ems_order',
        'schedule': crontab(minute='*/10'),
        'args': (),
    },
    'handler_mabang_order': {
        'task': 'apps.order.tasks.handler_mabang_order',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_tongtool_order': {
        'task': 'apps.order.tasks.handler_tongtool_order',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_yicang_order': {
        'task': 'apps.order.tasks.handler_yicang_order',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_accept_mabang_order': {
        'task': 'apps.order.tasks.handler_accept_mabang_order',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_accept_tong_tool_order': {
        'task': 'apps.order.tasks.handler_accept_tong_tool_order',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_push_tracking_num_yicang': {
        'task': 'apps.order.tasks.handler_push_tracking_num_yicang',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    # 'handler_51tracking_pull_times': {
    #     'task': 'apps.order.tasks.handler_51tracking_pull_times',
    #     'schedule': crontab(hour='*/6', minute=30), # 每6个小时
    #     'args': (),
    # },
    'handler_push_ems_receiver_lading': {
        'task': 'apps.order.tasks.handler_push_ems_receiver_lading',
        'schedule': crontab(hour='*/1'),  # 每小时
        'args': (),
    },
    'handler_17track_pull_times': {
        'task': 'apps.order.tasks.handler_17track_pull_times',
        'schedule': crontab(minute=0, hour='*/2'),
        'args': (),
    },
    'handler_sync_order_data_handle_times': {
        'task': 'apps.order.tasks.handler_sync_order_data_handle_times',
        'schedule': crontab(minute=0, hour='*/1'),
        # 'schedule': crontab(minute='*/30'),
        'args': (),
    },
    'handler_get_secondary_label': {
        'task': 'apps.order.tasks.handler_get_secondary_label',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_push_parcel_track_info': {
        'task': 'apps.order.tasks.handler_push_parcel_track_info',
        'schedule': timedelta(seconds=30),
        'args': (1,),
    },
    'handler_push_parcel_track_info2': {
        'task': 'apps.order.tasks.handler_push_parcel_track_info',
        'schedule': timedelta(seconds=30),
        'args': (2,),
    },
    'handler_get_alita_order': {
        'task': 'apps.order.tasks.handler_get_alita_order',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_push_to_ems_order': {
        'task': 'apps.order.tasks.handler_push_to_ems_order',
        'schedule': crontab(hour='12'), # 12点整
        'args': (),
    },
    'handle_shopify_order': {
        'task': 'apps.order.tasks.handle_shopify_order',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    # 'sync_create_waybills_parcels': {
    #     'task': 'apps.order.tasks.sync_create_waybills_parcels',
    #     'schedule': crontab(minute=0, hour='*/8'),
    #     'args': (),
    # },
    'handle_inventory_age_task': {
        'task': 'apps.task.tasks.handle_inventory_age_task',
        'schedule': crontab(minute=0, hour=1),  # 每天1点执行计算库龄
        'args': (),
    },
    'handle_rent_order_task': {
        'task': 'apps.task.tasks.handle_rent_order_task',
        'schedule': crontab(minute=0, hour=3),  # 每天3点执行生成仓租单
        'args': (),
    },
    # 暂时不需要这些dmas消息推送
    # 'fba_order_statistics_dmas': {
    #     'task': 'apps.order.tasks.fba_order_statistics_dmas',
    #     'schedule': crontab(minute=0, hour=10),
    #     'args': (),
    # },
    # 'settle_statistics_dmas': {
    #     'task': 'apps.order.tasks.settle_statistics_dmas',
    #     'schedule': crontab(minute=3, hour=10),
    #     'args': (),
    # },
    # 'place_order_count_dmas': {
    #     'task': 'apps.order.tasks.place_order_count_dmas',
    #     'schedule': crontab(minute=6, hour=10),
    #     'args': (),
    # },
    'ocean_order_aging_dmas': {
        'task': 'apps.order.tasks.ocean_order_aging_dmas',
        'schedule': crontab(minute=9, hour=10),
        'args': (),
    },
    # 添加缺失的dmas异步任务处理
    'dmas_async_task_handle': {
        'task': 'apps.task.tasks.dmas_async_task_handle',
        'schedule': crontab(minute='*/20'),
        'args': (),
    },
    # 海外仓同步入库计划
    'beat_sync_in_plan_task': {
        'task': 'apps.oms.tasks.beat_sync_in_plan_task',
        'schedule': crontab(minute='*/3'),
        'args': (),
    },
    # 海外仓同步入库计划发运操作
    'beat_sync_ship_in_plan_task': {
        'task': 'apps.oms.tasks.beat_sync_ship_in_plan_task',
        'schedule': crontab(minute='*/3'),
        'args': (),
    },
    # 海外仓同步入库计划详情
    'beat_sync_in_plan_info_task': {
        'task': 'apps.oms.tasks.beat_sync_in_plan_info_task',
        'schedule': crontab(minute='*/3'),
        'args': (),
    },
    # 海外仓同步入库取消结果
    'beat_sync_cancel_task': {
        'task': 'apps.oms.tasks.beat_sync_cancel_in_plan_task',
        'schedule': crontab(minute='*/5'),
        'args': (),
    },
    # 海外仓同步出库取消结果
    'beat_sync_cancel_out_plan_task': {
        'task': 'apps.oms.tasks.beat_sync_cancel_out_plan_task',
        'schedule': crontab(minute='*/5'),
        'args': (),
    },
    # 海外仓同步出库单
    'beat_sync_out_order_task': {
        'task': 'apps.oms.tasks.beat_sync_out_order_task',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    # 海外仓同步出库单详情
    'beat_sync_out_order_info_task': {
        'task': 'apps.oms.tasks.beat_sync_out_order_info_task',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    # 海外仓库存对账
    'beat_check_all_diff_inventory_task': {
        'task': 'apps.oms.tasks.beat_check_all_diff_inventory_task',
        'schedule': crontab(minute=0, hour=3),
        'args': (),
    },
    # 出库单自动审单
    'bate_oc_shipping_rules_order_task': {
        'task': 'apps.oms.tasks.bate_oc_shipping_rules_order_task',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    # 海外仓入库单自动成本、收入确认
    'beat_oms_inbound_order_cost_revenue_task': {
        'task': 'apps.oms.tasks.beat_oms_inbound_order_cost_revenue_task',
        'schedule': crontab(minute='*/15'),
        'args': (),
    },
    # 海外仓出库单自动成本、收入确认
    'beat_oms_outbound_order_cost_revenue_task': {
        'task': 'apps.oms.tasks.beat_oms_outbound_order_cost_revenue_task',
        'schedule': crontab(minute='*/15'),
        'args': (),
    },
    # 海外仓计算库龄
    'beat_oms_handle_inventory_age_task': {
        'task': 'apps.oms.tasks.beat_oms_handle_inventory_age_task',
        'schedule': crontab(minute=0, hour=1),  # 每天1点执行计算库龄
        'args': (),
    },
    # 生成仓租单任务
    'beat_oms_handle_rent_order_task': {
        'task': 'apps.oms.tasks.beat_oms_handle_rent_order_task',
        'schedule': crontab(minute=0, hour=3),  # 每天3点执行生成仓租单
        'args': (),
    },
    # 汇算订单提成导出
    'sync_generate_royalty_derivation': {
        'task': 'apps.crm.tasks.sync_generate_royalty_derivation',
        'schedule': crontab(minute=0, hour=1),
        'args': (),
    },
    'common_product_async_task_handle': {
        'task': 'apps.task.tasks.common_product_async_task_handle',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'handler_product_sales_price_strategy_task': {
        'task': 'apps.pms.tasks.handler_product_sales_price_strategy_task',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    # oms同步数据到wms
    'beat_sync_wms_tasks': {
        'task': 'apps.order.sync_wms_tasks.beat_sync_wms_tasks',
        # 'schedule': crontab(minute='*/1'),
        'schedule': 10.0,
        'args': (),
    },
    'statistical_sla_data': {
        'task': 'apps.track.tasks.statistical_sla_data',
        'schedule': crontab(minute=0, hour=5),  # 每天凌晨5点执行
        'args': (1,),
    },
    'bate_sync_transit_order': {
        'task': 'apps.oms.tasks.bate_sync_transit_order',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    # 检查已生效的临时信用额度是否过期
    'beat_subtract_credit_limit_task': {
        'task': 'apps.account.tasks.beat_subtract_credit_limit_task',
        'schedule': crontab(hour='*/12', minute=30),
        'args': (),
    },
    'call_rm_manifest_interface': {
        'task': 'apps.task.tasks.call_rm_manifest_interface',
        # 'schedule': crontab(minute=0, hour='0,12'),  # 中午十二点和凌晨十二点执行
        'schedule': crontab(minute=30, hour='21,12'),  # 中午十二点和凌晨十二点执行
        'args': (),
    },
    'amazon_ship_track_task': {
        'task': 'apps.order.tasks.amazon_ship_track_task',
        'schedule': crontab(minute=0, hour='*/2'),  # 每两小时执行
        'args': (),
    },
    'handler_cancel_parcel_order_task': {
        'task': 'apps.salesorder.tasks.handler_cancel_parcel_order_task',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'upload_tracking_num_to_shopify': {
        'task': 'apps.salesorder.tasks.upload_tracking_num_to_shopify',
        'schedule': crontab(minute='*/1'),
        'args': (),
    },
    'return_order_statistics_dmas': {
        'task': 'apps.order.tasks.return_order_statistics_dmas',
        'schedule': crontab(minute=0, hour=16),  # 每天16点执行
        'args': (),
    },
    # 添加缺失的退货账户余额告警任务
    'return_account_balance_alert_dmas': {
        'task': 'apps.order.tasks.return_account_balance_alert_dmas',
        'schedule': crontab(minute=0, hour=16),  # 每天16点执行
        'args': (),
    },
    # 'wishpost_msg_send_carrier': {
    #     'task': 'apps.order.tasks.wishpost_msg_send_carrier',
    #     'schedule': crontab(minute='*/1'),
    #     'args': (),
    # },
    # 'wishpost_push_tracking_info': {
    #     'task': 'apps.track.tasks.wishpost_push_tracking_info',
    #     'schedule': crontab(minute='*/1'), # todo 测试时候一分钟一次。调试
    #     # 'schedule': crontab(minute=0, hour='*/3'),  # 每三时执行
    #     'args': (),
    # },
    # 添加缺失的轨迹相关任务
    'trace_insert': {
        'task': 'apps.order.tasks.trace_insert',
        'schedule': crontab(minute=0, hour='*/4'),  # 每4小时执行一次
        'args': (),
    },
    'trace_push': {
        'task': 'apps.order.tasks.trace_push',
        'schedule': crontab(minute=0, hour='*/4'),  # 每4小时执行一次
        'args': (),
    },
}


def get_beat_schedule():
    """
    获取完整的Beat任务配置
    合并静态配置和动态配置
    """
    # 合并静态配置和动态配置
    complete_schedule = static_beat_schedule.copy()
    
    # 获取动态配置并合并
    dynamic_schedule = get_dynamic_beat_schedule()
    complete_schedule.update(dynamic_schedule)
    
    return complete_schedule


# 为了向后兼容，保留原来的变量名
beat_schedule = get_beat_schedule()