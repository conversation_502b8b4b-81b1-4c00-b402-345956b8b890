#version: '3'

services:

#  # 前端服务
  alita-front:
    image: ${alita_front_url}
    container_name: ${deploy_version}-front
    hostname: ${deploy_version}-front
    ports:
      - "28080:28080"
      - "38080:38080"
      - "80:80"
      - "443:443"
    volumes:
      - /data/dockers/nginx/logs:/usr/local/openresty/nginx/logs
      # SSL 证书目录（只读，安全）
      - /data/dockers/nginx/ssl:/data/ssl:ro
      # acme 验证工作目录（读写，用于验证）
      - /data/dockers/nginx/webroot:/data/webroot
      - /data/dockers/nginx/conf/nginx-front.conf:/etc/nginx/conf.d/default.conf
      - /data/${deploy_version}/static:/data/static
    # 确保容器以 root 用户运行以访问 SSL 证书
    user: "0:0"
    networks:
      - shared_network  # 让前端（Nginx）加入共享网络

networks:
  shared_network:
    external: true


