# HTTP 服务 - 用于 Let's Encrypt 验证
server {
    listen 80;
    listen [::]:80;
    server_name mzhk56.com www.mzhk56.com cshm.mzhk56.com;

    # Let's Encrypt 验证路径
    location /.well-known/acme-challenge/ {
        root /data/webroot;
        try_files $uri =404;
    }

    # 增加文件上传大小
    client_max_body_size 50m;

    # 静态文件配置
    location /static {
        alias /data/static/alita;
    }

    location /media {
        alias /data/static/alita/media;
    }

    # 主应用代理
    location / {
        include uwsgi_params;
        uwsgi_pass 127.0.0.1:3038;
    }
}

# 原有的内部服务端口配置保持不变
server {
    listen 28080;
    listen [::]:28080;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
    }

    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

server {
    listen 38080;
    listen [::]:38080;
    server_name localhost;

    # 增加文件上传大小
    client_max_body_size 50m;

    # 这里的两个文件是指向nginx容器中的
    location /static {
        alias /data/static/alita;
    }

    location /media {
        alias /data/static/alita/media;
    }

    location / {
        include uwsgi_params;
        uwsgi_pass 127.0.0.1:3038;
    }
} 