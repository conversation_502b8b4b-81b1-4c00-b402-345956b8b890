#!/bin/bash

# SSL 证书续期脚本 - 增强版
# 用于自动续期 Let's Encrypt 证书，包含备份、验证和通知功能

set -e

# 加载配置文件
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/ssl_config.sh"

if [ -f "$CONFIG_FILE" ]; then
    source "$CONFIG_FILE"
else
    echo "警告: 配置文件 $CONFIG_FILE 不存在，使用默认配置"
    # 默认配置
    CERT_PATH="/data/dockers/nginx/ssl"
    COMPOSE_FILE="docker-compose-front.yml"
    LOG_FILE="/var/log/ssl_renew.log"
    BACKUP_DIR="/data/dockers/nginx/ssl/backup"
    DOMAIN="mzhk56.com"
    RENEWAL_DAYS_THRESHOLD=30
    KEEP_BACKUP_DAYS=7
    NOTIFICATION_EMAIL=""
    CONTAINER_NAME="mzhk-front"
fi

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
handle_error() {
    log "错误: $1"
    exit 1
}

# 发送通知函数
send_notification() {
    local status="$1"
    local message="$2"
    local subject="SSL证书续期 - $status"
    
    # 优先使用 DMAS 企业微信通知
    if [ "${DMAS_ENABLED:-false}" = "true" ] && [ -n "$DMAS_URL" ]; then
        local app_name="${APP_NAME:-ssl-monitor}"
        local full_message="【$app_name】$subject: $message"
        
        # 生成 JSON 数据（简化版本）
        local json_data="{\"list\": [{\"groupNameList\": [\"\\u666e\\u4fe1IT\\u652f\\u6301\\u7fa4\"], \"atList\": [\"${DMAS_AT_USER:-donnie}\"], \"receivedContent\": \"$full_message\"}]}"
        
        if curl -s -X POST "$DMAS_URL" \
            -H 'Content-Type: application/json' \
            -d "$json_data" >/dev/null 2>&1; then
            log "✓ DMAS 企业微信通知发送成功"
        else
            log "✗ DMAS 企业微信通知发送失败"
        fi
    fi
    
    # 尝试使用专门的通知脚本（备用）
    if [ -f "$SCRIPT_DIR/send_notification.sh" ]; then
        "$SCRIPT_DIR/send_notification.sh" "$subject" "$message" 2>/dev/null || true
    fi
    
    # 如果配置了邮箱，作为备用通知方式
    if [ -n "$NOTIFICATION_EMAIL" ] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "$subject" "$NOTIFICATION_EMAIL" 2>/dev/null || true
    fi
}

# 备份现有证书
backup_certificates() {
    log "开始备份现有证书..."
    
    # 创建备份目录
    sudo mkdir -p "$BACKUP_DIR"
    
    # 生成备份文件名（带时间戳）
    local backup_timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/ssl_backup_$backup_timestamp.tar.gz"
    
    # 备份证书文件
    if [ -f "$CERT_PATH/cert.pem" ] && [ -f "$CERT_PATH/key.pem" ]; then
        sudo tar -czf "$backup_file" -C "$CERT_PATH" cert.pem key.pem fullchain.pem 2>/dev/null || true
        log "证书已备份到: $backup_file"
        
        # 保留最近7天的备份，删除旧备份
        find "$BACKUP_DIR" -name "ssl_backup_*.tar.gz" -mtime +7 -delete 2>/dev/null || true
    else
        log "警告: 未找到现有证书文件，跳过备份"
    fi
}

# 检查证书过期时间
check_certificate_expiry() {
    log "检查证书过期时间..."
    
    if [ -f "$CERT_PATH/cert.pem" ]; then
        local expiry_date=$(openssl x509 -in "$CERT_PATH/cert.pem" -noout -enddate | cut -d= -f2)
        local expiry_timestamp=$(date -d "$expiry_date" +%s)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        log "当前证书将在 $days_until_expiry 天后过期 ($expiry_date)"
        
        if [ $days_until_expiry -lt 30 ]; then
            log "证书将在30天内过期，需要续期"
            return 0
        else
            log "证书还有效期充足，无需续期"
            return 1
        fi
    else
        log "警告: 未找到证书文件，强制续期"
        return 0
    fi
}

# 验证证书有效性
validate_certificate() {
    log "验证新证书..."
    
    # 检查证书文件是否存在且不为空
    for file in "cert.pem" "key.pem" "fullchain.pem"; do
        if [ ! -s "$CERT_PATH/$file" ]; then
            handle_error "证书文件 $file 不存在或为空"
        fi
    done
    
    # 验证证书和私钥匹配
    local cert_modulus=$(openssl x509 -noout -modulus -in "$CERT_PATH/cert.pem" | openssl md5)
    local key_modulus=$(openssl rsa -noout -modulus -in "$CERT_PATH/key.pem" | openssl md5)
    
    if [ "$cert_modulus" != "$key_modulus" ]; then
        handle_error "证书和私钥不匹配"
    fi
    
    # 验证证书域名
    local cert_domains=$(openssl x509 -in "$CERT_PATH/cert.pem" -noout -text | grep -A1 "Subject Alternative Name" | tail -1 | tr ',' '\n' | grep "DNS:" | sed 's/.*DNS://' | tr '\n' ' ')
    log "证书包含域名: $cert_domains"
    
    log "✓ 证书验证通过"
}

# 重启服务并验证
restart_and_verify() {
    log "重启 nginx 服务..."
    
    cd "$(dirname "$0")"
    
    # 测试 nginx 配置
    if ! docker exec -it ${deploy_version}-front nginx -t 2>/dev/null; then
        handle_error "nginx 配置测试失败"
    fi
    
    # 重启容器
    if docker-compose -f "$COMPOSE_FILE" restart alita-front; then
        log "✓ 容器重启成功"
        
        # 等待服务启动
        sleep 5
        
        # 验证 HTTPS 服务
        if curl -I https://$DOMAIN --connect-timeout 10 >/dev/null 2>&1; then
            log "✓ HTTPS 服务验证成功"
        else
            log "警告: HTTPS 服务验证失败，请手动检查"
        fi
    else
        handle_error "容器重启失败"
    fi
}

# 清理函数
cleanup() {
    log "执行清理..."
    # 可以在这里添加清理临时文件等操作
}

# 主函数
main() {
    log "=================================="
    log "SSL 证书续期脚本 - 开始执行"
    log "=================================="
    
    # 检查依赖
    if [ ! -f "$HOME/.acme.sh/acme.sh" ]; then
        handle_error "acme.sh 未安装"
    fi
    
    # 创建日志目录
    sudo mkdir -p "$(dirname "$LOG_FILE")" 2>/dev/null || true
    
    # 检查是否需要续期（可选，如果想强制续期可以注释掉这个检查）
    if ! check_certificate_expiry; then
        log "证书无需续期，脚本退出"
        exit 0
    fi
    
    # 备份现有证书
    backup_certificates
    
    log "开始续期证书..."
    
    # 续期所有证书
    if ~/.acme.sh/acme.sh --cron --home "$HOME/.acme.sh"; then
        log "✓ 证书续期检查完成"
        
        # 重新安装证书（如果有更新）
        if ~/.acme.sh/acme.sh --install-cert \
          -d $DOMAIN \
          --cert-file "$CERT_PATH/cert.pem" \
          --key-file "$CERT_PATH/key.pem" \
          --fullchain-file "$CERT_PATH/fullchain.pem" \
          --reloadcmd "echo 'Certificates updated'"; then
            
            log "✓ 证书安装成功"
            
            # 设置文件权限
            sudo chmod 644 "$CERT_PATH/cert.pem" "$CERT_PATH/fullchain.pem"
            sudo chmod 600 "$CERT_PATH/key.pem"
            sudo chown root:root "$CERT_PATH"/*.pem
            
            # 验证证书
            validate_certificate
            
            # 重启服务
            restart_and_verify
            
            log "✓ 证书续期完成"
            send_notification "成功" "SSL证书续期成功完成"
            
        else
            handle_error "证书安装失败"
        fi
    else
        handle_error "证书续期失败"
    fi
    
    cleanup
    log "=================================="
    log "SSL 证书续期脚本 - 执行完成"
    log "=================================="
}

# 设置陷阱处理函数
trap cleanup EXIT
trap 'handle_error "脚本被中断"' INT TERM

# 执行主函数
main "$@" 