# 完整的 Nginx 配置文件
# 包含 HTTP 和 HTTPS 服务，以及 Let's Encrypt 验证路径
# 支持 HTTP/2 和 SSL 配置
# 适用于多个域名和端口


# HTTP 服务 - 用于 Let's Encrypt 验证和重定向
server {
    listen 80;
    listen [::]:80;
    server_name mzhk56.com www.mzhk56.com cshm.mzhk56.com;

    # Let's Encrypt 验证路径
    location /.well-known/acme-challenge/ {
        root /data/webroot;
        try_files $uri =404;
    }

    # 其他请求重定向到 HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS 服务
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    http2 on;
    server_name mzhk56.com www.mzhk56.com;

    # SSL 证书配置
    ssl_certificate /data/ssl/fullchain.pem;
    ssl_certificate_key /data/ssl/key.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 增加文件上传大小
    client_max_body_size 50m;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /apibackend/ {
            proxy_pass http://127.0.0.1:38080/;  # 后端 API 服务
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
     }

    error_page   500 502 503 504  /50x.html;

    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}

# 原有的内部服务端口配置保持不变
server {
    listen 28080;
    listen [::]:28080;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
    }

    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

server {
    listen 38080;
    listen [::]:38080;
    server_name localhost;

    # 增加文件上传大小
    client_max_body_size 50m;

    # 这里的两个文件是指向nginx容器中的
    location /static {
        alias /data/static/alita;
    }

    location /media {
        alias /data/static/alita/media;
    }

    #location / {
    #    include uwsgi_params;
    #    uwsgi_pass 127.0.0.1:3038;

    location / {
            proxy_pass http://mzhk-end:3038;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
       }

}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    http2 on;
    server_name  cshm.mzhk56.com;

    # SSL 证书配置
    ssl_certificate /data/ssl/fullchain.pem;
    ssl_certificate_key /data/ssl/key.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 增加文件上传大小
    client_max_body_size 50m;


    location / {
            proxy_pass http://127.0.0.1:38080/;  # 后端 API 服务
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
     }

    error_page   500 502 503 504  /50x.html;

    location = /50x.html {
        root   /usr/share/nginx/html;
    }

}