#!/bin/bash

# SSL 证书 Crontab 一键配置脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "=================================="
echo "SSL 证书 Crontab 配置脚本"
echo "=================================="

# 检查文件是否存在
if [ ! -f "$SCRIPT_DIR/crontab_ssl.txt" ]; then
    echo "错误: crontab_ssl.txt 文件不存在"
    exit 1
fi

echo "1. 备份现有 crontab..."
crontab -l > "$SCRIPT_DIR/crontab_backup_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || echo "当前没有 crontab 配置"

echo "2. 显示即将添加的任务:"
echo "-------------------"
cat "$SCRIPT_DIR/crontab_ssl.txt"
echo "-------------------"

echo ""
read -p "确认添加这些定时任务吗? (y/N): " confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    echo "3. 添加 SSL 相关定时任务..."
    
    # 追加到现有 crontab
    (crontab -l 2>/dev/null; echo ""; cat "$SCRIPT_DIR/crontab_ssl.txt") | crontab -
    
    echo "✓ 定时任务添加成功"
    
    echo "4. 当前 crontab 配置:"
    echo "-------------------"
    crontab -l
    echo "-------------------"
    
    echo "5. 创建日志目录..."
    sudo mkdir -p /var/log
    sudo touch /var/log/ssl_renew.log /var/log/ssl_status.log
    sudo chmod 644 /var/log/ssl_*.log
    
    echo "✓ 配置完成!"
    echo ""
    echo "下次执行时间:"
    echo "- 证书续期检查: 每天凌晨 2:00"
    echo "- 状态报告: 每周一上午 8:00"
    echo "- 月度检查: 每月 1 号上午 9:00"
    echo ""
    echo "日志文件:"
    echo "- 续期日志: /var/log/ssl_renew.log"
    echo "- 状态日志: /var/log/ssl_status.log"
    
else
    echo "取消配置"
fi

echo "==================================" 