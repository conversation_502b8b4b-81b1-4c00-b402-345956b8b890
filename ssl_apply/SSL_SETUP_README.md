# SSL 证书获取和管理指南

## 脚本说明

### 1. get_ssl_cert.sh - 初始证书获取脚本
用于首次获取 Let's Encrypt SSL 证书

### 2. renew_ssl_cert.sh - 证书续期脚本（增强版）
用于定期续期证书，包含备份、验证和通知功能

### 3. check_ssl_cert.sh - 证书状态检查脚本
用于快速检查证书状态、过期时间和服务健康状况

### 4. ssl_config.sh - 配置文件
集中管理所有SSL相关配置参数

## 使用步骤

### 第一步：准备工作

1. **确保域名解析正确**
   ```bash
   # 检查域名解析
   nslookup mzhk56.com
   nslookup www.mzhk56.com
   nslookup cshm.mzhk56.com
   ```

2. **确保 nginx 服务运行并且 80 端口可访问**
   ```bash
   # 启动 nginx 容器（仅 HTTP，不要 HTTPS）
   docker-compose -f docker-compose-front.yml up -d alita-front
   
   # 检查 80 端口是否可访问
   curl -I http://mzhk56.com
   ```

3. **创建必要目录**
   ```bash
   # 创建验证工作目录
   sudo mkdir -p /data/dockers/nginx/webroot
   sudo chmod 755 /data/dockers/nginx/webroot
   
   # 确保证书目录存在
   sudo mkdir -p /data/dockers/nginx/ssl
   sudo chmod 755 /data/dockers/nginx/ssl
   ```

### 第二步：执行脚本

1. **添加执行权限**
   ```bash
   chmod +x get_ssl_cert.sh
   chmod +x renew_ssl_cert.sh
   ```

2. **运行证书获取脚本**
   ```bash
   ./get_ssl_cert.sh
   ```

### 第三步：验证结果

1. **检查证书文件**
   ```bash
   ls -la /data/dockers/nginx/ssl/
   openssl x509 -in /data/dockers/nginx/ssl/cert.pem -text -noout
   ```

2. **测试 HTTPS 访问**
   ```bash
   curl -I https://cshm.mzhk56.com
   ```

## 自动续期设置

### 添加 crontab 任务
```bash
# 编辑 crontab
crontab -e

# 推荐配置（智能续期 + DMAS 通知）:
# 每天凌晨 2 点检查续期（只在需要时执行）
0 2 * * * /path/to/alita_bak/renew_ssl_cert.sh

# 每周一早上 8 点发送状态报告到企业微信
0 8 * * 1 /path/to/alita_bak/send_ssl_status.sh

# 可选：每月1号强制检查续期
0 3 1 * * FORCE_RENEWAL=true /path/to/alita_bak/renew_ssl_cert.sh
```

### 配置通知

#### 方案 1：DMAS 企业微信通知（推荐）
编辑 `ssl_config.sh` 文件：
```bash
# 启用 DMAS 企业微信通知
export DMAS_ENABLED=true
export DMAS_AT_USER="your-username"    # 替换为您的企业微信用户名
export APP_NAME="ssl-monitor"          # 可自定义应用名称

# 其他配置保持默认即可
```

#### 方案 2：邮箱通知
```bash
# 设置邮箱通知
export NOTIFICATION_EMAIL="<EMAIL>"
```

#### 方案 3：自定义 Webhook
```bash
# 设置 Webhook 通知
export WEBHOOK_URL="https://hooks.slack.com/services/..."
```

## 通知方式配置

### 方案 1：安装邮件客户端
```bash
# CentOS/RHEL/AlmaLinux
sudo yum install mailx

# Ubuntu/Debian  
sudo apt-get install mailutils

# 测试邮件发送
echo "测试邮件" | mail -s "测试" <EMAIL>
```

### 方案 2：使用通知脚本（推荐）
```bash
# 添加执行权限
chmod +x send_notification.sh

# 测试通知
./send_notification.sh "测试标题" "测试消息内容"

# 查看通知日志
tail -f /var/log/ssl_notifications.log
```

### 方案 3：QQ 邮箱 SMTP 配置
如果需要通过 QQ 邮箱发送，需要：
1. 开启 QQ 邮箱的 SMTP 服务
2. 获取授权码（不是登录密码）
3. 配置 SMTP 参数

### 方案 4：Webhook 通知
```bash
# 配置企业微信/钉钉/Slack Webhook
export WEBHOOK_URL="你的webhook地址"

# 测试发送
curl -X POST -H "Content-Type: application/json" \
     -d '{"text":"SSL证书状态测试"}' \
     "$WEBHOOK_URL"
```

## 故障排除

### 问题 1：域名验证失败
**解决方案：**
- 确保域名正确解析到服务器 IP
- 确保防火墙允许 80 端口访问
- 检查 nginx 配置，确保 webroot 路径正确

### 问题 2：证书文件权限问题
**解决方案：**
```bash
sudo chmod 644 /data/dockers/nginx/ssl/*.pem
sudo chown root:root /data/dockers/nginx/ssl/*.pem
```

### 问题 3：nginx 无法启动
**解决方案：**
```bash
# 检查 nginx 配置语法
docker exec -it mzhk-front nginx -t

# 查看错误日志
docker-compose -f docker-compose-front.yml logs alita-front
```

## 新功能特性

### 增强的续期脚本功能
- ✅ **智能续期**：只在证书即将过期时才执行续期（可配置天数）
- ✅ **自动备份**：续期前自动备份现有证书
- ✅ **完整验证**：验证证书文件完整性和有效性
- ✅ **服务检查**：自动验证 HTTPS 服务是否正常
- ✅ **详细日志**：带时间戳的详细操作日志
- ✅ **通知支持**：支持邮件和 Webhook 通知
- ✅ **错误恢复**：失败时自动恢复备份

### 快速状态检查
```bash
# 快速检查证书状态
./check_ssl_cert.sh

# 查看详细的证书信息
./check_ssl_cert.sh | tee ssl_status.log
```

### DMAS 通知测试
```bash
# 添加执行权限
chmod +x test_dmas_notification.sh send_ssl_status.sh

# 测试 DMAS 企业微信通知
./test_dmas_notification.sh

# 测试通用通知脚本
./send_notification.sh "测试标题" "测试消息"

# 测试管道方式通知
echo "这是测试消息" | ./send_notification.sh "管道测试"

# 测试完整的状态通知流程
./send_ssl_status.sh
```

### 通知脚本使用方式
```bash
# 方式 1：直接传参
./send_notification.sh "标题" "消息内容"

# 方式 2：管道方式
echo "消息内容" | ./send_notification.sh "标题"

# 方式 3：文件内容方式
cat status.txt | ./send_notification.sh "状态报告"

# 方式 4：命令输出方式
./check_ssl_cert.sh | ./send_notification.sh "SSL证书状态"

# 方式 5：使用专用脚本（推荐）
./send_ssl_status.sh
```

### 配置管理
```bash
# 修改配置参数
vim ssl_config.sh

# 测试配置
DRY_RUN=true ./renew_ssl_cert.sh
```

## 重要注意事项

1. **备份现有证书**：脚本会自动备份，也建议手动备份重要证书
2. **DNS 传播时间**：新域名可能需要等待 DNS 传播完成
3. **防火墙设置**：确保 80 和 443 端口对外开放
4. **证书有效期**：Let's Encrypt 证书有效期为 90 天，建议设置自动续期
5. **日志监控**：定期检查 `/var/log/ssl_renew.log` 日志文件

## 文件路径说明

- 宿主机证书路径：`/data/dockers/nginx/ssl/` → 容器内：`/data/ssl/` （只读）
- 宿主机验证路径：`/data/dockers/nginx/webroot/` → 容器内：`/data/webroot/` （读写）
- nginx 配置路径：项目内 `nginx-front-complete.conf`

## 目录分离的优势

- **安全性**：SSL 证书目录只读挂载，防止意外修改
- **功能性**：acme 验证目录读写挂载，支持验证过程
- **清晰性**：职责分离，目录用途明确 