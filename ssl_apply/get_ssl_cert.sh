#!/bin/bash

# SSL 证书获取和安装脚本
# 使用 acme.sh 获取 Let's Encrypt 证书

set -e  # 遇到错误时退出

# 配置变量
DOMAIN_LIST="mzhk56.com www.mzhk56.com cshm.mzhk56.com"
WEBROOT_PATH="/data/dockers/nginx/webroot/"
CERT_PATH="/data/dockers/nginx/ssl"
COMPOSE_FILE="docker-compose-front.yml"

echo "=================================="
echo "SSL 证书获取和安装脚本"
echo "域名: $DOMAIN_LIST"
echo "=================================="

# 检查 acme.sh 是否安装
if [ ! -f "$HOME/.acme.sh/acme.sh" ]; then
    echo "错误: acme.sh 未安装"
    echo "请先安装 acme.sh: curl https://get.acme.sh | sh"
    exit 1
fi

# 检查 webroot 目录是否存在
if [ ! -d "$WEBROOT_PATH" ]; then
    echo "创建 webroot 目录: $WEBROOT_PATH"
    sudo mkdir -p "$WEBROOT_PATH"
    sudo chmod 755 "$WEBROOT_PATH"
fi

# 检查证书目录是否存在
if [ ! -d "$CERT_PATH" ]; then
    echo "创建证书目录: $CERT_PATH"
    sudo mkdir -p "$CERT_PATH"
fi

echo "步骤 1: 获取证书..."
# 使用 acme.sh 获取证书
~/.acme.sh/acme.sh --issue \
  -d mzhk56.com \
  -d www.mzhk56.com \
  -d cshm.mzhk56.com \
  --webroot "$WEBROOT_PATH" \
  --debug

if [ $? -eq 0 ]; then
    echo "✓ 证书获取成功"
else
    echo "✗ 证书获取失败"
    exit 1
fi

echo "步骤 2: 安装证书到 nginx 目录..."
# 安装证书到指定目录
~/.acme.sh/acme.sh --install-cert \
  -d mzhk56.com \
  --cert-file "$CERT_PATH/cert.pem" \
  --key-file "$CERT_PATH/key.pem" \
  --fullchain-file "$CERT_PATH/fullchain.pem" \
  --reloadcmd "cd $(pwd) && docker-compose -f $COMPOSE_FILE restart alita-front"

if [ $? -eq 0 ]; then
    echo "✓ 证书安装成功"
else
    echo "✗ 证书安装失败"
    exit 1
fi

echo "步骤 3: 设置文件权限..."
# 设置正确的权限
sudo chmod 644 "$CERT_PATH/cert.pem"
sudo chmod 644 "$CERT_PATH/key.pem"
sudo chmod 644 "$CERT_PATH/fullchain.pem"
sudo chown root:root "$CERT_PATH"/*.pem

echo "步骤 4: 验证证书文件..."
# 验证证书文件
echo "证书文件列表:"
ls -la "$CERT_PATH"

echo "证书详细信息:"
openssl x509 -in "$CERT_PATH/cert.pem" -text -noout | grep -E "(Subject|Issuer|Not Before|Not After)"

echo "步骤 5: 重启 nginx 容器..."
# 重启容器以加载新证书
cd "$(dirname "$0")"
docker-compose -f "$COMPOSE_FILE" restart alita-front

echo "=================================="
echo "✓ SSL 证书获取和安装完成!"
echo "证书位置: $CERT_PATH"
echo "容器已重启，请检查服务状态"
echo "=================================="

# 检查容器状态
echo "容器状态:"
docker-compose -f "$COMPOSE_FILE" ps alita-front

echo "如果需要查看日志，请运行:"
echo "docker-compose -f $COMPOSE_FILE logs alita-front" 