#!/bin/bash

# SSL 证书续期配置文件
# 在这里修改配置参数，无需编辑主脚本

# 基础配置
export CERT_PATH="/data/dockers/nginx/ssl"
export COMPOSE_FILE="docker-compose-front.yml"
export LOG_FILE="/var/log/ssl_renew.log"
export BACKUP_DIR="/data/dockers/nginx/ssl/backup"
export DOMAIN="mzhk56.com"

# 续期设置
export RENEWAL_DAYS_THRESHOLD=30  # 提前多少天续期
export KEEP_BACKUP_DAYS=30        # 备份保留天数
export MAX_LOG_SIZE_MB=50         # 日志文件最大大小（MB）

# 通知设置
export NOTIFICATION_EMAIL=""              # 邮箱通知（可选）
export WEBHOOK_URL=""                     # Webhook 通知 URL（可选）
export NOTIFICATION_ON_SUCCESS=true      # 成功时是否发送通知
export NOTIFICATION_ON_ERROR=true        # 错误时是否发送通知

# DMAS 企业微信通知设置
export DMAS_ENABLED=true                  # 是否启用 DMAS 通知
export DMAS_URL="http://dmas.puxinc.com/api/v2/wecom/sendMessage?robotId=dmas_local_00001&sendType=async"
export DMAS_GROUP_NAME="普信IT支持群"      # 企业微信群名称
export DMAS_AT_USER="donnie"              # @的用户名
export APP_NAME="ssl-monitor"             # 应用名称标识

# 服务验证设置
export VERIFY_HTTPS_SERVICE=true         # 是否验证 HTTPS 服务
export SERVICE_CHECK_TIMEOUT=10          # 服务检查超时时间（秒）
export SERVICE_CHECK_RETRY=3             # 服务检查重试次数

# 高级设置
export FORCE_RENEWAL=false               # 是否强制续期（忽略过期时间检查）
export SKIP_BACKUP=false                 # 是否跳过备份
export DRY_RUN=false                     # 干运行模式（仅检查，不实际操作）

# 容器设置
export CONTAINER_NAME="${deploy_version:-mzhk}-front"  # 容器名称
export NGINX_CONFIG_TEST=true            # 是否测试 nginx 配置

# 日志级别 (DEBUG, INFO, WARN, ERROR)
export LOG_LEVEL="INFO" 