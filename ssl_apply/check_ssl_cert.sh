#!/bin/bash

# SSL 证书状态检查脚本
# 用于快速检查证书状态和过期时间

# 基础配置
CERT_PATH="/data/dockers/nginx/ssl"
DOMAIN="mzhk56.com"
BACKUP_DIR="/data/dockers/nginx/ssl/backup"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
print_status() {
    local status="$1"
    local message="$2"
    case $status in
        "OK")    echo -e "${GREEN}✓${NC} $message" ;;
        "WARN")  echo -e "${YELLOW}⚠${NC} $message" ;;
        "ERROR") echo -e "${RED}✗${NC} $message" ;;
        "INFO")  echo -e "${BLUE}ℹ${NC} $message" ;;
    esac
}

# 检查证书文件
check_certificate_files() {
    echo "=== 证书文件检查 ==="
    
    for file in "cert.pem" "key.pem" "fullchain.pem"; do
        local file_path="$CERT_PATH/$file"
        if [ -f "$file_path" ]; then
            local file_size=$(ls -la "$file_path" | awk '{print $5}')
            if [ "$file_size" -gt 0 ]; then
                print_status "OK" "$file 存在且有内容 (${file_size} bytes)"
            else
                print_status "ERROR" "$file 存在但为空"
            fi
        else
            print_status "ERROR" "$file 不存在"
        fi
    done
}

# 检查证书过期时间
check_certificate_expiry() {
    echo -e "\n=== 证书有效期检查 ==="
    
    if [ -f "$CERT_PATH/cert.pem" ]; then
        local expiry_date=$(openssl x509 -in "$CERT_PATH/cert.pem" -noout -enddate | cut -d= -f2)
        local expiry_timestamp=$(date -d "$expiry_date" +%s 2>/dev/null)
        local current_timestamp=$(date +%s)
        local days_remaining=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        print_status "INFO" "过期时间: $expiry_date"
        
        if [ $days_remaining -lt 0 ]; then
            print_status "ERROR" "证书已过期 $((-days_remaining)) 天"
        elif [ $days_remaining -lt 7 ]; then
            print_status "ERROR" "证书将在 $days_remaining 天后过期（紧急）"
        elif [ $days_remaining -lt 30 ]; then
            print_status "WARN" "证书将在 $days_remaining 天后过期（需要续期）"
        else
            print_status "OK" "证书还有 $days_remaining 天有效期"
        fi
    else
        print_status "ERROR" "证书文件不存在"
    fi
}

# 检查在线服务
check_online_service() {
    echo -e "\n=== 在线服务检查 ==="
    
    if curl -I https://$DOMAIN --connect-timeout 5 >/dev/null 2>&1; then
        print_status "OK" "HTTPS 服务正常响应"
    else
        print_status "ERROR" "HTTPS 服务无法访问"
    fi
}

# 检查备份
check_backups() {
    echo -e "\n=== 备份检查 ==="
    
    if [ -d "$BACKUP_DIR" ]; then
        local backup_count=$(find "$BACKUP_DIR" -name "ssl_backup_*.tar.gz" 2>/dev/null | wc -l)
        print_status "INFO" "找到 $backup_count 个备份文件"
    else
        print_status "WARN" "备份目录不存在"
    fi
}

# 主函数
main() {
    echo "SSL 证书状态检查报告"
    echo "域名: $DOMAIN"
    echo "证书路径: $CERT_PATH"
    echo "检查时间: $(date)"
    echo "========================================"
    
    check_certificate_files
    check_certificate_expiry
    check_online_service
    check_backups
    
    echo -e "\n========================================"
    echo "检查完成"
}

# 执行主函数
main "$@" 