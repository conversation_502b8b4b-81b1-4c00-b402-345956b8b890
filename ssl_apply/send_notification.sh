#!/bin/bash

# 通用通知发送脚本
# 支持 DMAS 企业微信、邮件等多种通知方式

SUBJECT="$1"
MESSAGE="$2"

# 如果没有提供消息内容，或消息内容是特殊占位符，尝试从 stdin 读取
if ([ -z "$MESSAGE" ] || [ "$MESSAGE" = "-" ] || [ "$MESSAGE" = "STDIN" ]) && [ ! -t 0 ]; then
    MESSAGE=$(cat)
fi

# 处理长消息内容
process_message() {
    local msg="$1"
    
    # 移除控制字符和非打印字符
    msg=$(echo "$msg" | tr -d '\r' | sed 's/\x1b\[[0-9;]*m//g')
    
    # 检查是否是SSL证书状态报告，如果是则精简处理
    if echo "$msg" | grep -q "SSL 证书状态检查报告"; then
        # 提取关键信息
        local days=$(echo "$msg" | grep -o "还有 [0-9]* 天有效期" | grep -o "[0-9]*")
        local https_status=$(echo "$msg" | grep "HTTPS 服务" | head -1)
        local backup_status=$(echo "$msg" | grep "备份" | head -1)
        
        # 生成精简消息
        if [ -n "$days" ]; then
            msg="SSL证书状态: ✅ 证书有效期还有${days}天"
            if echo "$https_status" | grep -q "✓"; then
                msg="$msg, HTTPS服务正常"
            fi
            if echo "$backup_status" | grep -q "⚠"; then
                msg="$msg (备份目录不存在)"
            fi
        else
            # 如果无法提取天数，使用备用简化
            msg="SSL证书状态: 检查完成，详情请查看服务器日志"
        fi
    else
        # 非SSL报告的其他消息，进行常规处理
        # 移除多余的空行和特殊符号
        msg=$(echo "$msg" | sed '/^$/N;/^\n$/d' | tr -d '✓✗⚠ℹ' | sed 's/=*//g')
        
        # 限制消息长度
        if [ ${#msg} -gt 200 ]; then
            msg="${msg:0:180}...[已截断]"
        fi
    fi
    
    echo "$msg"
}

MESSAGE=$(process_message "$MESSAGE")

# 如果还是没有消息内容，显示使用帮助
if [ -z "$SUBJECT" ] || [ -z "$MESSAGE" ]; then
    echo "使用方法: $0 <标题> <消息内容>"
    echo "或者: echo '消息内容' | $0 <标题>"
    exit 1
fi

# 加载配置文件
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/ssl_config.sh"
[ -f "$CONFIG_FILE" ] && source "$CONFIG_FILE"

# 生成 DMAS 企业微信 JSON 数据
generate_dmas_json() {
    local content="$1"
    local at_user="${DMAS_AT_USER:-donnie}"
    local group_name="${DMAS_GROUP_NAME:-普信IT支持群}"
    
    # 转换中文字符为Unicode（如果需要）
    local encoded_group=$(echo "$group_name" | sed 's/普/\\u666e/g;s/信/\\u4fe1/g;s/IT/IT/g;s/支/\\u652f/g;s/持/\\u6301/g;s/群/\\u7fa4/g')
    
    # JSON 转义函数
    json_escape() {
        local text="$1"
        # 转义特殊字符
        text=$(echo "$text" | sed 's/\\/\\\\/g')    # 反斜杠
        text=$(echo "$text" | sed 's/"/\\"/g')      # 双引号
        text=$(echo "$text" | sed 's/\n/\\n/g')     # 换行符
        text=$(echo "$text" | sed 's/\r/\\r/g')     # 回车符
        text=$(echo "$text" | sed 's/\t/\\t/g')     # 制表符
        echo "$text"
    }
    
    # 转义内容
    local escaped_content=$(json_escape "$content")
    
    cat <<EOF
{"list": [{"groupNameList": ["$encoded_group"], "atList": ["$at_user"], "receivedContent": "$escaped_content"}]}
EOF
}

# 发送 DMAS 企业微信通知
send_dmas() {
    local subject="$1"
    local message="$2"
    local app_name="${APP_NAME:-ssl-monitor}"
    
    # 组合完整消息
    local full_message="【$app_name】$subject: $message"
    
    # 生成 JSON 数据
    local json_data=$(generate_dmas_json "$full_message")
    
    # 调试输出（可选）
    if [ "${DEBUG:-false}" = "true" ]; then
        echo "DEBUG: 发送到 DMAS 的消息长度: ${#full_message} 字符"
        echo "DEBUG: JSON 数据: $json_data"
    fi
    
    # 发送请求
    if command -v curl >/dev/null 2>&1; then
        local response=$(curl -s -w "HTTP_CODE:%{http_code}" -X POST "$DMAS_URL" \
            -H 'Content-Type: application/json' \
            -d "$json_data" 2>/dev/null)
        
        local http_code=$(echo "$response" | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
        local response_body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')
        
        if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
            echo "✓ DMAS 企业微信通知发送成功 (HTTP $http_code)"
            return 0
        else
            echo "✗ DMAS 企业微信通知发送失败 (HTTP $http_code)"
            echo "响应内容: $response_body"
            return 1
        fi
    else
        echo "✗ curl 命令不可用，无法发送 DMAS 通知"
        return 1
    fi
}

# 主通知函数
send_notification() {
    local log_file="/var/log/ssl_notifications.log"
    local success=false
    
    # 确保日志目录存在
    sudo mkdir -p "$(dirname "$log_file")" 2>/dev/null || true
    
    # 记录到日志文件
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $SUBJECT" | sudo tee -a "$log_file" >/dev/null
    echo "$MESSAGE" | sudo tee -a "$log_file" >/dev/null
    echo "----------------------------------------" | sudo tee -a "$log_file" >/dev/null
    
    # 显示到控制台
    echo "=== $SUBJECT ==="
    echo "$MESSAGE"
    
    # 发送 DMAS 企业微信通知
    if [ "${DMAS_ENABLED:-false}" = "true" ] && [ -n "$DMAS_URL" ]; then
        if send_dmas "$SUBJECT" "$MESSAGE"; then
            success=true
        fi
    fi
    
    # 尝试发送邮件（如果配置了）
    if [ -n "$NOTIFICATION_EMAIL" ] && command -v mail >/dev/null 2>&1; then
        if echo "$MESSAGE" | mail -s "$SUBJECT" "$NOTIFICATION_EMAIL" 2>/dev/null; then
            echo "✓ 邮件通知发送成功"
            success=true
        fi
    fi
    
    echo "通知已记录到: $log_file"
    
    if $success; then
        return 0
    else
        return 1
    fi
}

# 执行通知
send_notification 