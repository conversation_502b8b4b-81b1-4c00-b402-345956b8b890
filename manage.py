#!/usr/bin/env python
import os
import sys

if __name__ == '__main__':
    profile = os.environ.get('PROJECT_SETTINGS', 'test')
    # profile = os.environ.get('PROJECT_SETTINGS', 'hanjinTest')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'alita.settings.%s' % profile)
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)
