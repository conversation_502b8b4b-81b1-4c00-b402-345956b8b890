version: '3'

# ports: 绑定容器的端口到主机的端口，这样就可以在外网访问docker容器的服务
# expose: 将当前容器的端口3暴露给link到本容器的容器，expose不会将端口暴露给主机

# 开启共享docker网络
#docker network create shared_network
#查看容器是否在相同的网络中
#docker network inspect shared_network

networks:
  shared_network:
    external: true

services:

  # 后端服务
  alita-end:
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - DJANGO_SETTINGS_MODULE=alita.settings.${deploy_version}
      - deploy_version=${deploy_version}
    image: ${alita_end_url}
    container_name: ${deploy_version}-end
    hostname: ${deploy_version}-end
    command: >
      /bin/sh -c "supervisord -c supervisord38.conf"
    volumes:
      - /data/${deploy_version}/log:/data/log
      - /data/${deploy_version}/static:/data/static
    ports:
      - "3038:3038"
    networks:
      - shared_network

  # task服务
  alita-task:
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - DJANGO_SETTINGS_MODULE=alita.settings.${deploy_version}
      - deploy_version=${deploy_version}
    image: ${alita_end_url}
    container_name: ${deploy_version}-task
    hostname: ${deploy_version}-task
    command: >
      /bin/sh -c "./deploy_celery_docker.sh ${deploy_version} & tail -f /dev/null"
    volumes:
      - /data/${deploy_version}/log:/data/log
      - /data/${deploy_version}/static:/data/static
    ports:
      - "30388:3038"
    networks:
      - shared_network

  alita-front:
    image: ${alita_front_url}
    container_name: ${deploy_version}-front
    hostname: ${deploy_version}-front
    ports:
      - "28080:28080"
      - "38080:38080"
      - "80:80"
      - "443:443"
    volumes:
      - /data/dockers/nginx/logs:/usr/local/openresty/nginx/logs
      - /data/dockers/nginx/conf/nginx-front.conf:/etc/nginx/conf.d/default.conf
      - /data/${deploy_version}/static:/data/static
    networks:
      - shared_network  # 让前端（Nginx）加入共享网络

#  # MySQL数据库
  mysql:
    image: registry.cn-shenzhen.aliyuncs.com/alita_px/mysql8:8.0.20
    container_name: mysql
    volumes:
      - /data/dockers/mysql/conf:/etc/mysql/conf.d
      - /data/dockers/mysql/data:/var/lib/mysql
      - /data/dockers/mysql/logs:/var/log
      - /data/dockers/mysql/files:/files
    ports:
      - "13369:3306"
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=IXWKf@lFJQIb8sJJ_A4CD7NIOb^OeTlTQ
      - MYSQL_DATABASE=alita_${deploy_version}
      - MYSQL_USER=alita_${deploy_version}
      - MYSQL_PASSWORD=IXWKf@lFJQIb8sJJ_A4CD7NIOb^OeTlTQ
    networks:
      - shared_network
#
#  # Redis数据库
  redis:
    image: registry.openanolis.cn/openanolis/redis:5.0.3-8.6
    container_name: redis
    volumes:
      - /data/dockers/redis/redis.conf:/etc/redis/redis.conf
      - /data/dockers/redis/data:/data
    ports:
      - "15388:6379"
    restart: always
    command: redis-server /etc/redis/redis.conf
    networks:
      - shared_network

