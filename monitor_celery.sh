#!/bin/bash

APP_NAME=$1
echo "当前运行环境:$APP_NAME"

# 日志文件配置
LOG_DIR="/data/logs/celery_monitor"
LOG_FILE="$LOG_DIR/celery_monitor.log"
STATS_FILE="$LOG_DIR/celery_stats.log"
mkdir -p $LOG_DIR

log_message() {
    local level=$1
    local message=$2
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $message" | tee -a $LOG_FILE
}

get_public_ip() {
    # Try multiple IP lookup services in case some are down
    public_ip=""
    
    # Try ipinfo.io first
    public_ip=$(curl -s ipinfo.io/ip)
    if [ -n "$public_ip" ]; then
        echo "$public_ip"
        return
    fi
    
    # Try ifconfig.me as backup
    public_ip=$(curl -s ifconfig.me)
    if [ -n "$public_ip" ]; then
        echo "$public_ip" 
        return
    fi
    
    # Try icanhazip.com as another backup
    public_ip=$(curl -s icanhazip.com)
    if [ -n "$public_ip" ]; then
        echo "$public_ip"
        return
    fi
    
    echo "Could not determine public IP"
    return 1
}

# Get the public IP
PUBLIC_IP=$(get_public_ip)
log_message "INFO" "Server IP: $PUBLIC_IP"

# 读取配置文件函数
read_ini() {
    local file=$1
    local section=$2
    local key=$3
    awk -F '=' -v section="$section" -v key="$key" '
        $0 ~ /^\[.*\]$/ {gsub(/\[|\]/, "", $0); current=$0; next}
        current==section && $1~"^"key"$" {gsub(/^ +| +$/, "", $2); print $2; exit}
    ' "$file"
}

CELERY_APP="alita"

config_file="./redis_config.ini"

db_db=$(read_ini "$config_file" "$APP_NAME" "REDIS_DB")
db_password=$(read_ini "$config_file" "$APP_NAME" "REDIS_PASSWD")
db_host=$(read_ini "$config_file" "$APP_NAME" "REDIS_HOST")
db_port=$(read_ini "$config_file" "$APP_NAME" "REDIS_PORT")

export CELERY_BROKER_URL="redis://:$db_password@$db_host:$db_port/$db_db"

log_message "INFO" "Using broker URL: $CELERY_BROKER_URL"

if [ -d "/home/<USER>/" ]; then
    source /home/<USER>/alita/bin/activate
fi

case $APP_NAME in
    "mz" | "yqf")
        PHONE='18727931171'
        ;;
    *)
        PHONE='13316990258'
        ;;
esac

# 告警级别定义
WARN_QUEUE_THRESHOLD=10    # 警告级别队列长度
ERROR_QUEUE_THRESHOLD=50   # 错误级别队列长度
CRITICAL_QUEUE_THRESHOLD=100 # 严重级别队列长度

# 新增：假死检测阈值
MAX_TASK_DURATION=300      # 任务最长执行时间（秒）
MIN_PROCESSING_RATE=0.1    # 最小任务处理速率（任务/秒）
STATS_WINDOW=300           # 统计窗口时间（秒）

# 告警函数
send_alert() {
    local level=$1
    local content=$2
    local phone=$3
    
    local json_data=$(cat <<EOF
{
    "msgtype": "markdown",
    "markdown": {
        "title": "alita:Celery监控告警",
        "text": "alita:Celery监控告警\n**级别**: ${level}\n**环境**: ${APP_NAME}\n**服务器**: ${PUBLIC_IP}\n**详情**: ${content}\n**时间**: $(date '+%Y-%m-%d %H:%M:%S')"
    },
    "at": {
        "atMobiles": ["$phone"],
        "isAtAll": false
    }
}
EOF
)
    
    curl -X POST \
        'https://oapi.dingtalk.com/robot/send?access_token=73a65b43ab9d0aa381798927e7b8819c06cbe16bee50109d1fb4a2921beb1bf3' \
        -H 'Content-Type: application/json' \
        -d "$json_data"
}

# 检查Celery进程
check_celery_process() {
    if ! ps aux | grep -E "celery.*-A[ =]$CELERY_APP" | grep -v grep > /dev/null; then
        log_message "ERROR" "Celery进程未运行"
        send_alert "严重" "Celery进程未运行，请立即检查" "$PHONE"
        return 1
    fi
    log_message "INFO" "Celery进程正常运行"
    return 0
}

# 检查Worker状态
check_worker_status() {
    local temp_file=$(mktemp)
    celery -A $CELERY_APP inspect ping > "$temp_file" 2>&1
    local exit_code=$?
    local worker_status=$(cat "$temp_file")
    
    log_message "DEBUG" "命令退出码: $exit_code"
    
    # 清理临时文件
    rm -f "$temp_file"
    
    if [ $exit_code -ne 0 ] || [ -z "$worker_status" ]; then
        log_message "ERROR" "无法连接到Celery Workers"
        send_alert "严重" "无法连接到Celery Workers，可能所有worker都已离线" "$PHONE"
        return 1
    fi
    
    local online_workers=$(echo "$worker_status" | grep -E "^->.*: OK$" | wc -l)
    log_message "INFO" "当前在线worker数量: $online_workers"
    
    if [ "$online_workers" -eq 0 ]; then
        log_message "ERROR" "未检测到在线Worker"
        send_alert "严重" "没有在线的Worker" "$PHONE"
        return 1
    fi
    
    return 0
}

# 新增：检查任务处理效率
check_task_processing_efficiency() {
    log_message "INFO" "开始检查任务处理效率..."
    
    # 获取当前统计信息
    local current_stats=$(celery -A $CELERY_APP inspect stats 2>/dev/null)
    local current_time=$(date +%s)
    
    if [ -z "$current_stats" ]; then
        log_message "WARN" "无法获取worker统计信息"
        return 1
    fi
    
    # 创建临时文件来处理worker统计
    local temp_stats_file=$(mktemp)
    local dead_workers=()
    local total_processed=0
    local total_failed=0
    
    # 解析每个worker的统计信息
    echo "$current_stats" | awk '
    BEGIN { in_worker = 0; worker_name = ""; }
    /^->[[:space:]]+[^:]+:[[:space:]]+OK$/ {
        if (worker_name != "") {
            print current_time "," worker_name "," succeeded "," failed
        }
        match($0, /^->[[:space:]]+([^:]+):[[:space:]]+OK$/, arr)
        worker_name = arr[1]
        succeeded = 0
        failed = 0
        in_worker = 1
        next
    }
    in_worker && /"task-succeeded":[[:space:]]*[0-9]+/ {
        match($0, /"task-succeeded":[[:space:]]*([0-9]+)/, arr)
        succeeded = arr[1]
    }
    in_worker && /"task-failed":[[:space:]]*[0-9]+/ {
        match($0, /"task-failed":[[:space:]]*([0-9]+)/, arr)
        failed = arr[1]
    }
    END {
        if (worker_name != "") {
            print current_time "," worker_name "," succeeded "," failed
        }
    }' current_time="$current_time" > "$temp_stats_file"
    
    # 处理每个worker的统计
    while IFS=',' read -r timestamp worker_name succeeded failed; do
        if [ -n "$worker_name" ]; then
            local worker_stats_file="${STATS_FILE%.log}_${worker_name}.log"
            
            # 记录当前worker统计
            echo "$timestamp,$succeeded,$failed" >> "$worker_stats_file"
            
            # 保持文件大小
            tail -n 50 "$worker_stats_file" > "${worker_stats_file}.tmp" && mv "${worker_stats_file}.tmp" "$worker_stats_file"
            
            # 检查worker处理效率
            if [ -f "$worker_stats_file" ] && [ $(wc -l < "$worker_stats_file") -ge 2 ]; then
                local prev_line=$(tail -n 2 "$worker_stats_file" | head -n 1)
                local prev_time=$(echo "$prev_line" | cut -d',' -f1)
                local prev_processed=$(echo "$prev_line" | cut -d',' -f2)
                
                local time_diff=$((current_time - prev_time))
                local processed_diff=$((succeeded - prev_processed))
                
                if [ "$time_diff" -gt 0 ]; then
                    local processing_rate=$(echo "scale=3; $processed_diff / $time_diff" | bc -l 2>/dev/null || echo "0")
                    log_message "INFO" "Worker $worker_name 处理速率: $processing_rate 任务/秒 (处理: $processed_diff, 时间: ${time_diff}秒)"
                    
                    # 改进的假死检测（30分钟 = 1800秒，并增加额外检查）
                    if [ "$processed_diff" -eq 0 ] && [ "$time_diff" -gt 1800 ]; then
                        log_message "INFO" "Worker $worker_name 在过去${time_diff}秒内没有处理任务，进行进一步检查..."
                        
                        # 检查1: Worker是否真的在线
                        local ping_result=$(celery -A $CELERY_APP inspect ping --destination="$worker_name" 2>/dev/null)
                        if [ $? -ne 0 ] || [ -z "$ping_result" ]; then
                            log_message "WARN" "Worker $worker_name ping失败，真的可能有问题"
                            dead_workers+=("$worker_name")
                            continue
                        fi
                        
                        # 检查2: 是否有任务在队列中等待
                        local reserved_tasks=$(celery -A $CELERY_APP inspect reserved 2>/dev/null)
                        local task_count=$(echo "$reserved_tasks" | grep -c '"id":' || echo "0")
                        if [ "$task_count" -gt 0 ]; then
                            log_message "WARN" "Worker $worker_name 长时间未处理任务且队列中有${task_count}个待处理任务"
                            dead_workers+=("$worker_name")
                        else
                            log_message "INFO" "Worker $worker_name 长时间未处理任务，但队列中无待处理任务，属于正常空闲状态"
                        fi
                        
                        # 检查3: 是否有活跃任务
                        local active_tasks=$(celery -A $CELERY_APP inspect active --destination="$worker_name" 2>/dev/null)
                        local active_count=$(echo "$active_tasks" | grep -c '"id":' || echo "0")
                        if [ "$active_count" -gt 0 ]; then
                            log_message "INFO" "Worker $worker_name 当前有${active_count}个活跃任务，移除假死标记"
                            # 从假死列表中移除
                            dead_workers=($(printf '%s\n' "${dead_workers[@]}" | grep -v "^$worker_name$"))
                        fi
                    fi
                fi
            else
                log_message "INFO" "Worker $worker_name - 首次记录 (成功: $succeeded, 失败: $failed)"
            fi
            
            # 累计总数
            total_processed=$((total_processed + succeeded))
            total_failed=$((total_failed + failed))
        fi
    done < "$temp_stats_file"
    
    # 清理临时文件
    rm -f "$temp_stats_file"
    
    # 记录总体统计
    echo "$current_time,$total_processed,$total_failed" >> "$STATS_FILE"
    tail -n 100 "$STATS_FILE" > "$STATS_FILE.tmp" && mv "$STATS_FILE.tmp" "$STATS_FILE"
    
    # 发送具体的假死告警
    if [ ${#dead_workers[@]} -gt 0 ]; then
        local dead_worker_list=$(IFS=', '; echo "${dead_workers[*]}")
        send_alert "警告" "检测到假死Worker: $dead_worker_list - 这些worker长时间未处理任务，可能存在假死问题" "$PHONE"
    fi
    
    log_message "INFO" "总处理统计 - 成功: $total_processed, 失败: $total_failed"
    
    return 0
}

# 新增：检查长时间运行的任务
check_long_running_tasks() {
    log_message "INFO" "检查长时间运行任务..."
    
    local active_tasks=$(celery -A $CELERY_APP inspect active 2>/dev/null)
    
    if [ -z "$active_tasks" ]; then
        log_message "DEBUG" "无活跃任务或无法获取活跃任务信息"
        return 0
    fi
    
    local current_time=$(date +%s)
    local long_running_count=0
    
    # 查找活跃任务并检查运行时间
    echo "$active_tasks" | grep -E '"time_start".*[0-9]+\.[0-9]+' | while read -r line; do
        local start_time=$(echo "$line" | grep -o '[0-9]\+\.[0-9]\+' | head -1)
        if [ -n "$start_time" ]; then
            local task_duration=$((current_time - ${start_time%.*}))
            
            if [ "$task_duration" -gt "$MAX_TASK_DURATION" ]; then
                long_running_count=$((long_running_count + 1))
                log_message "WARN" "发现长时间运行任务，运行时间: ${task_duration}秒"
            fi
        fi
    done
    
    # 统计长时间运行任务数量的另一种方法
    local long_tasks=$(echo "$active_tasks" | grep -c "time_start")
    if [ "$long_tasks" -gt 0 ]; then
        log_message "INFO" "当前有 $long_tasks 个活跃任务"
    fi
    
    return 0
}

# 新增：检查worker负载状态和处理能力
check_worker_processing_capacity() {
    log_message "INFO" "检查worker处理能力..."
    
    local stats=$(celery -A $CELERY_APP inspect stats 2>/dev/null)
    
    if [ -z "$stats" ]; then
        log_message "WARN" "无法获取worker统计信息"
        return 1
    fi
    
    # 检查每个worker的统计信息
    echo "$stats" | grep -E '"[^"]*": {' | while read -r worker_line; do
        local worker_name=$(echo "$worker_line" | cut -d'"' -f2)
        if [ -n "$worker_name" ]; then
            log_message "INFO" "检查Worker: $worker_name"
            
            # 获取worker的任务统计
            local worker_stats=$(echo "$stats" | sed -n "/$worker_name/,/}/p")
            local succeeded=$(echo "$worker_stats" | grep -o '"task-succeeded": [0-9]*' | cut -d' ' -f2)
            local failed=$(echo "$worker_stats" | grep -o '"task-failed": [0-9]*' | cut -d' ' -f2)
            local received=$(echo "$worker_stats" | grep -o '"task-received": [0-9]*' | cut -d' ' -f2)
            
            log_message "INFO" "Worker $worker_name - 成功: ${succeeded:-0}, 失败: ${failed:-0}, 接收: ${received:-0}"
        fi
    done
    
    return 0
}

# 新增：队列积压趋势分析
check_queue_trend() {
    log_message "INFO" "分析队列积压趋势..."
    
    local reserved_tasks=$(celery -A $CELERY_APP inspect reserved 2>/dev/null)
    local current_time=$(date +%s)
    local queue_length=0
    
    if [ -n "$reserved_tasks" ]; then
        # 更准确地计算队列长度
        queue_length=$(echo "$reserved_tasks" | grep -o '"id":' | wc -l)
    fi
    
    # 记录队列长度历史
    local queue_history_file="$LOG_DIR/queue_history.log"
    echo "$current_time,$queue_length" >> "$queue_history_file"
    
    # 只保留最近的记录
    tail -n 50 "$queue_history_file" > "$queue_history_file.tmp" && mv "$queue_history_file.tmp" "$queue_history_file"
    
    log_message "INFO" "当前队列长度: $queue_length"
    
    # 分析队列趋势（如果有足够的历史数据）
    if [ -f "$queue_history_file" ] && [ $(wc -l < "$queue_history_file") -ge 3 ]; then
        local prev_queue=$(tail -n 2 "$queue_history_file" | head -n 1 | cut -d',' -f2)
        local trend_diff=$((queue_length - prev_queue))
        
        if [ "$trend_diff" -gt 0 ]; then
            log_message "INFO" "队列长度增加趋势: +$trend_diff"
            if [ "$trend_diff" -gt 20 ]; then
                send_alert "警告" "队列长度快速增长，当前: $queue_length, 增长: +$trend_diff，可能存在处理问题" "$PHONE"
            fi
        else
            log_message "INFO" "队列长度变化: $trend_diff"
        fi
    fi
    
    return 0
}

# 原有的队列状态检查（保持不变）
check_queue_status() {
    local active_tasks=$(celery -A $CELERY_APP inspect active 2>/dev/null)
    if [ $? -eq 0 ]; then
        local active_count=$(echo "$active_tasks" | grep -c '"id":')
        log_message "INFO" "当前活跃任务数: $active_count"
    fi
    
    local reserved_tasks=$(celery -A $CELERY_APP inspect reserved 2>/dev/null)
    local queue_length=$(echo "$reserved_tasks" | grep -c '"id":')
    
    if [ -z "$queue_length" ]; then
        queue_length=0
    fi
    
    log_message "INFO" "当前队列长度: $queue_length"
    
    if [ "$queue_length" -ge "$CRITICAL_QUEUE_THRESHOLD" ]; then
        send_alert "严重" "任务队列严重积压，当前队列长度: $queue_length" "$PHONE"
        return 2
    elif [ "$queue_length" -ge "$ERROR_QUEUE_THRESHOLD" ]; then
        send_alert "错误" "任务队列积压，当前队列长度: $queue_length" "$PHONE"
        return 1
    elif [ "$queue_length" -ge "$WARN_QUEUE_THRESHOLD" ]; then
        send_alert "警告" "任务队列轻微积压，当前队列长度: $queue_length" "$PHONE"
        return 0
    fi
    
    return 0
}

# 主函数
main() {
    log_message "INFO" "开始增强版Celery监控检查..."
    
    # 检查Celery进程
    check_celery_process || exit 1
    
    # 检查Worker状态
    check_worker_status
    
    # 检查队列状态
    check_queue_status
    
    # 新增：检查任务处理效率
    check_task_processing_efficiency
    
    # 新增：检查长时间运行任务
    check_long_running_tasks
    
    # 新增：检查worker处理能力
    check_worker_processing_capacity
    
    # 新增：队列积压趋势分析
    check_queue_trend
    
    log_message "INFO" "增强版Celery监控检查完成"
}

# 执行主函数
main 