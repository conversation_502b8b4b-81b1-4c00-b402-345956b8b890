<!DOCTYPE HTML>
<html>
<head>
    <title>图表demo</title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" charset="utf-8" src="../ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="editor_api.js"></script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="../lang/zh-cn/zh-cn.js"></script>

    <style type="text/css">
        .clear {
            clear: both;
        }
    </style>
</head>
<body>
<div>
    <script id="editor" type="text/plain" style="width:1024px;height:500px;">
        <h1>测试图表请点击表格， 然后点击工具栏上的“图表”按钮</h1>
        <table data-chart="title:2012北京房价趋势图;subTitle:;xTitle:月份;yTitle:金额（元）;suffix:元;tip:;dataFormat:1;chartType:0">
            <tbody>
            <tr>
                <th width="90"><br></th>
                <th width="90">1月</th>
                <th width="90">2月</th>
                <th width="90">3月</th>
                <th width="90">4月</th>
                <th width="90">5月</th>
                <th width="90">6月</th>
                <th width="90">7月</th>
                <th width="90">8月</th>
                <th width="90">9月</th>
                <th width="91">10月</th>
                <th>11月</th>
                <th>12月</th>
            </tr>
            <tr>
                <th valign="null" width="90">2012</th>
                <td valign="top" width="55">24593</td>
                <td valign="top" width="55">24308</td>
                <td valign="top" width="55">24932</td>
                <td valign="top" width="55">25413</td>
                <td valign="top" width="55">25588</td>
                <td valign="top" width="55">25948</td>
                <td valign="top" width="55">26579</td>
                <td valign="top" width="55">27199</td>
                <td valign="top" width="55">28392</td>
                <td valign="top" width="55">29071</td>
                <td valign="top" width="55">29522</td>
                <td valign="top" width="55">30158</td>
            </tr>
            </tbody>
        </table>
    </script>
</div>
<script type="text/javascript">

    //实例化编辑器
    var ue = UE.getEditor('editor', {
        toolbars: [
            [
                'charts', 'preview'
            ]
        ]
    });

</script>
</body>
</html>