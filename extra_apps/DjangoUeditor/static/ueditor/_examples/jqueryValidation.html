<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <title>Ueditor在jquery validation下的验证</title>
    <script src="http://ajax.aspnetcdn.com/ajax/jQuery/jquery-1.8.3.min.js"></script>
    <script type="text/javascript" src="http://ajax.aspnetcdn.com/ajax/jquery.validate/1.10.0/jquery.validate.min.js"></script>

    <script type="text/javascript" charset="utf-8" src="../ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="editor_api.js"></script>
    <script>
        $(function(){
            UE.getEditor('content');

            var validator = $("#myform").submit(function() {
                UE.getEditor('content').sync();
            }).validate({
                        ignore: "",
                        rules: {
                            title: "required",
                            content: "required"
                        },
                        errorPlacement: function(label, element) {
                            label.insertAfter(element.is("textarea") ? element.next() : element);
                        }
                    });
            validator.focusInvalid = function() {
                if( this.settings.focusInvalid ) {
                    try {
                        var toFocus = $(this.findLastActive() || this.errorList.length && this.errorList[0].element || []);
                        if (toFocus.is("textarea")) {
                            UE.getEditor('content').focus()
                        } else {
                            toFocus.filter(":visible").focus();
                        }
                    } catch(e) {
                    }
                }
            }
        })
    </script>

</head>
<body>


<form id="myform" action="">
    <h3>Ueditor在jquery validation下的验证</h3>

    <label>其他内容</label>
    <input name="title" />

    <br/>

    <label>编辑器</label>
    <textarea id="content" name="content" rows="15" cols="80" style="width: 80%"></textarea>

    <br />
    <input type="submit" name="save" value="Submit" />
</form>
</body>
</html>