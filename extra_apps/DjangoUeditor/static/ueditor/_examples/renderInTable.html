<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <script type="text/javascript" charset="utf-8" src="../ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="editor_api.js"></script>
    <title>表格内实例化编辑器实例</title>
</head>

<body>
<div style="height: 100px"></div>
<div id="div" style="border: 1px solid #fff">
    <table border="1">
        <caption>表格标题</caption>
        <tr><th>标题</th><th>内容</th></tr>
        <!--编辑器实例化到表格内部时，请在对应的单元格上明确标注宽度值（百分数或者直接数均可），否则有可能在工具栏浮动等功能状态下出现移位-->
        <tr>
            <td>中国</td><td width="100%"><textarea id="editor"></textarea></td>
        </tr>
    </table>
</div>
<script type="text/javascript">
    UE.getEditor('editor');
</script>
</body>
</html>