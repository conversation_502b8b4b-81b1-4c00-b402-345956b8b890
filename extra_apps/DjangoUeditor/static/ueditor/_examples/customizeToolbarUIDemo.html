<!DOCTYPE HTML>
<html>
<head>
    <title>完整demo</title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" charset="utf-8" src="../ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="editor_api.js"> </script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="../lang/zh-cn/zh-cn.js"></script>
    <!--添加按钮-->
    <script type="text/javascript" charset="utf-8" src="addCustomizeButton.js"></script>
    <!--添加下拉菜单-->
    <script type="text/javascript" charset="utf-8" src="addCustomizeCombox.js"></script>
    <!--添加弹出层-->
    <script type="text/javascript" charset="utf-8" src="addCustomizeDialog.js"></script>

    <style type="text/css">
        .clear {
            clear: both;
        }
        div{
            width:100%;
        }
    </style>
</head>
<body>
<div>
    <h1>二次开发demo</h1>
    <script id="editor" type="text/plain" style="width:1024px;height:500px;"></script>
</div>
</body>
<script type="text/javascript">

    //实例化编辑器
    //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
    UE.getEditor('editor',{
        //清空了工具栏
        toolbars:[[]]
    })

</script>
</html>