<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <style type="text/css">
        h3{
            color:#630000;
        }
        ul li{
            margin: 10px;
        }
        a:link,a:active,a:visited{
            color: #0000EE;
        }
        a{
            text-decoration: none;
        }
    </style>
</head>
<body>
<h2>UEditor各种实例演示</h2>
<h3>基础示例</h3>
<ul>
    <li>
        <a href="simpleDemo.html" target="_self">简单示例</a><br/>
        使用基础的按钮实现简单的功能
    </li>
</ul>
<h3>应用展示</h3>
<ul>
    <li>
        <a href="submitFormDemo.html" target="_self">表单应用</a><br/>
        编辑器的内容通过表单提交到后台
    </li>
    <li>
        <a href="resetDemo.html" target="_self">重置编辑器</a><br/>
        将编辑器的内部变量清空，重置。
    </li>
    <li>
        <a href="textareaDemo.html" target="_self">文本域渲染编辑器</a><br/>
        将编辑器渲染到文本域，并且将文本域的内容放到编辑器的初始化内容里
    </li>
</ul>
<h3>二次开发</h3>
<ul>
    <li>
        <a style="color:red;" href="customizeToolbarUIDemo.html" target="_self">二次开发例子</a><br/>
        添加自定义的普通按钮、下拉菜单按钮、对话框按钮
    </li>
    <li>
        <a href="customToolbarDemo.html" target="_self">自定义Toolbar</a><br/>
        用自己的皮肤，设计自己的编辑器
    </li>
    <li>
        <a href="customPluginDemo.html" target="_self">自定义插件</a><br/>
        在编辑器的基础上开发自己的插件
    </li>
</ul>
<h3>高级案例</h3>
<ul>
    <li>
        <a href="completeDemo.html" target="_self">完整示例</a><br/>
        编辑器的完整功能
    </li>
    <li>
        <a href="charts.html" target="_self"  >图表示例</a><br/>
        图表功能
    </li>
    <li>
        <a href="sortableDemo.html" target="_self" >表格排序示例</a><br/>
        编辑表格,并设置排序后可在展示区域点击排序
    </li>
    <li>
        <a href="sectionDemo.html" target="_self" >目录大纲示例</a><br/>
        获取编辑内容的目录大纲，并通过操作目录，更新编辑器内容
    </li>
    <li>
        <a href="multiDemo.html" target="_self">多编辑器实例</a><br/>
        一个页面实例化多个编辑器，互不影响
    </li>
    <li>
        <a href="renderInTable.html" target="_self">在表格中渲染编辑器</a><br/>
        表格中渲染编辑器
    </li>
    <li>
        <a href="jqueryCompleteDemo.html" target="_self">jquery</a><br/>
        jquery中使用编辑器
    </li>
    <li>
        <a href="jqueryValidation.html" target="_self">jqueryValidation</a><br/>
        编辑器在jqueryValidation中验证
    </li>
    <li>
        <a href="uparsedemo.html" target="_self">展示页面uparse.js解析</a><br/>
        通过调用uparse.js在展示页面中自动解析编辑内容
    </li>
    <li>
        <a href="filterRuleDemo.html" target="_self">过滤规则定制化</a><br/>
        通过配置filterRules可以定制黑白名单，过滤和转换你要的标签和属性
    </li>
    <li>
        <a href="setWidthHeightDemo.html" target="_self">设置宽高</a><br/>
        设置宽高的demo页面
    </li>
    <li>
        <a href="multiEditorWithOneInstance.html" target="_self">多个编辑区使用一个编辑器实例</a><br/>
        多个编辑区使用一个编辑器实例
    </li>
</ul>
<script type="text/javascript">
    var href = location.href;
    if(href.indexOf("http") != 0 ){
        alert("您当前尚未将UEditor部署到服务器环境，部分浏览器下所有弹出框功能的使用会受到限制！");
    }
</script>
</body>
</html>