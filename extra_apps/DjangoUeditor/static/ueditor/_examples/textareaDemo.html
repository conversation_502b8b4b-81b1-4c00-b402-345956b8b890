<!DOCTYPE HTML>
<html>
<head>

    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <title></title>
   
    <script type="text/javascript" charset="utf-8" src="../ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="editor_api.js"></script>
    <style type="text/css">
        #myEditor{
            width: 500px;
            height: 300px;
        }
    </style>
</head>
<body>
    <h1>文本域渲染编辑器</h1>

    <!--style给定宽度可以影响编辑器的最终宽度-->
    <textarea id="myEditor">这里是原始的textarea中的内容，可以从数据中读取</textarea>
    <br/>
    <input type="button" onclick="render()" value="渲染编辑器">
    <script type="text/javascript">
        //渲染编辑器
        function render(){
            UE.getEditor('myEditor')
        }
    </script>

</body>


</html>
