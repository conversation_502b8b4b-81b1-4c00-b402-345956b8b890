<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>

    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <title></title>
    <script type="text/javascript" charset="utf-8" src="../ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="editor_api.js"></script>
    <!--加入高亮的js和css文件，如果你的编辑器和展示也是一个页面那么高亮的js可以不加载-->
    <script type="text/javascript" charset="utf-8" src="../third-party/SyntaxHighlighter/shCore.js"></script>
    <link rel="stylesheet" type="text/css" href="../third-party/SyntaxHighlighter/shCoreDefault.css"/>
</head>
<body>
<h1>代码高亮演示</h1>
<h2>获得编辑器实例</h2>
<div style="width:200px">
    <pre class="brush:js;toolbar:false;">
        UE.getEditor('myEditor');
    </pre>
</div>

<!--style给定宽度可以影响编辑器的最终宽度-->
<script type="text/plain" id="myEditor" style="width:500px">
    <h3>实例化编辑器</h3>
     <pre class="brush:js;toolbar:false;">
         UE.getEditor('myEditor');
</pre>
</script>
<script type="text/javascript">
    //为了在编辑器之外能展示高亮代码
    SyntaxHighlighter.highlight();
   UE.getEditor('myEditor');
</script>

</body>


</html>
