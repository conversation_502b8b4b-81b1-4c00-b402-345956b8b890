<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>

    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script src="../ueditor.parse.js"></script>
    <title></title>
</head>
<body>
    <h1>解析编辑的内容</h1>
    <div class="content" style="width:200px">
        <ol class="custom_cn2 list-paddingleft-1"><li class="list-cn-3-1 list-cn2-paddingleft-1"><p>这里可以书写，编辑器的初始内容</p></li></ol><ul class="custom_dash list-paddingleft-1"><li class="list-dash list-dash-paddingleft"><p>sdfas</p></li></ul><ol class="custom_cn2 list-paddingleft-1"><ol style="list-style-type: decimal; " class=" list-paddingleft-3"><li><p>dfas</p></li></ol><li class="list-cn-3-1 list-cn2-paddingleft-1"><p>dfa</p></li><ol style="list-style-type: decimal; " class=" list-paddingleft-3"><li><p>sdfadf</p></li></ol></ol>
        <p>
            这里可以书写，编辑器的初始内容
        </p>
        <p>
            <video class="video-js vjs-default-skin" data-setup="{}" controls preload="none" width="640" height="264"
                   src="http://video-js.zencoder.com/oceans-clip.mp4"
                   poster="http://video-js.zencoder.com/oceans-clip.png">
                <source src="http://video-js.zencoder.com/oceans-clip.mp4" type='video/mp4' />
            </video>
        </p>
        <pre class="brush:js;toolbar:false;">
               moveToBookmark:function (bookmark) {
            var start = bookmark.id ? this.document.getElementById(bookmark.start) : bookmark.start,
                end = bookmark.end && bookmark.id ? this.document.getElementById(bookmark.end) : bookmark.end;
            this.setStartBefore(start);
            domUtils.remove(start);
            if (end) {
                this.setEndBefore(end);
                domUtils.remove(end);
            } else {
                this.collapse(true);
            }
            return this;
        },
        </pre>
        <ol class="custom_cn2 list-paddingleft-1">
            <li class="list-cn-3-1 list-cn2-paddingleft-1">
                <p>
                    dfasdf
                </p>
            </li>
            <li class="list-cn-3-2 list-cn2-paddingleft-1">
                <p>
                    asd
                </p>
            </li>
            <li class="list-cn-3-3 list-cn2-paddingleft-1">
                <p>
                    fa
                </p>
            </li>
            <li class="list-cn-3-4 list-cn2-paddingleft-1">
                <p>
                    sdfa
                </p>
            </li>
            <li class="list-cn-3-5 list-cn2-paddingleft-1">
                <p>
                    sdfa
                </p>
            </li>
        </ol>
    </div>
    <div id="content" class="content">
        <table width="960">
            <caption>
                sdf<br />
            </caption>
            <tbody>
            <tr>
                <th valign="null">
                    sdf<br />
                </th>
                <th valign="null">
                    sdf<br />
                </th>
                <th valign="null">
                    <br />
                </th>
                <th valign="null">
                    <br />
                </th>
                <th valign="null">
                    <br />
                </th>
                <th valign="null">
                    <br />
                </th>
                <th valign="null">
                    <br />
                </th>
            </tr>
            <tr>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
            </tr>
            <tr>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
                <td width="116" valign="top">
                    <br />
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="content_background">
        <h1>
            UEditor
        </h1>
        <h2>
            简介
        </h2>
        <p>
            UEditor是由百度WEB前端研发部开发的所见即所得的开源富文本编辑器，具有轻量、可定制、用户体验优秀等特点。开源基于BSD协议，所有源代码在协议允许范围内可自由修改和使用。百度UEditor的推出，可以帮助不少网站开者在开发富文本编辑器所遇到的难题，节约开发者因开发富文本编辑器所需要的大量时间，有效降低了企业的开发成本。
        </p>
        <h2>
            特点<br/>
        </h2>
        <p>
            1、核心层提供了编辑器底层的一些方法和概念，如DOM树操作、Selection、Range等。
        </p>
        <p>
            2、在核心层之上覆盖的是命令插件层。之所以叫命令插件层，是因为UEditor中所有的功能型实现都是通过这一层中的命令和插件来完成的，并且各个命令和插件之间基本互不耦合——使用者需要使用哪个功能就导入哪个功能对应的命令或者插件文件，完全不用考虑另外那些杂七杂八的JS文件（极少数插件除外，关于这些插件下文会整理出一个依赖列表来供同学们参考）。
        </p>
        <p>
            理论上来讲，所有的命令都是可以用插件来代替的，但是依然将两者分开的主要原因是命令都是一些静态的方法，无需随editor实例初始化，从而优化了编辑器的性能。而插件随编辑器的初始化而初始化，性能上会有少许的影响，但相比命令而言，插件能够完成更加复杂的功能。其中最主要的一个特点是在插件内部既可以为编辑器注册命令，也可以为编辑器绑定监听事件。这个特点使得为编辑器添加任何功能都可以在插件中独立完成。
        </p>
        <p>
            3、在命令插件层之上则是UI层。UEditor的UI设计与核心层和命令插件层几乎完全解耦，简单的几个配置就可以为编辑器在界面上添加额外的UI元素和功能，具体的配置下面将会深入阐述。
        </p>
        <p>
            <br/>
        </p>
        <p style="display:none;" data-background="background-repeat:no-repeat;background-position:center center;background-color:#C3D69B;background-image:url(http://www.baidu.com/img/bdlogo.gif);">
            <br/>
        </p>
    </div>

    <script>
        // 语法
        // uParse(selector,[option])
        /*
         selector支持
         id,class,tagName
         */
        /*
         目前支持的参数
         option:
         highlightJsUrl 代码高亮相关js的路径 如果展示有代码高亮，必须给定该属性
         highlightCssUrl 代码高亮相关css的路径 如果展示有代码高亮，必须给定该属性
         liiconpath 自定义列表样式的图标路径，可以不给定，默认'http://bs.baidu.com/listicon/',
         listDefaultPaddingLeft : 自定义列表样式的左边宽度 默认'20',
         customRule 可以传入你自己的处理规则函数，函数第一个参数是容器节点
         */

        uParse('.content',{
            rootPath : '../'
        })
        uParse('.content_background',{
            rootPath : '../'
        })
    </script>

</body>
</html>
