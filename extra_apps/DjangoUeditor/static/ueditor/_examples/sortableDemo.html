<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>

    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script src="../ueditor.parse.js" type="text/javascript"></script>
    <title></title>
</head>
<body>
<h1>表格排序演示</h1>
<p>
<p>
    <strong>默认排序方法有五种:</strong><br/>
    reversecurrent : 逆序当前<br/>
    orderbyasc : 按ASCII字符升序<br/>
    reversebyasc : 按ASCII字符降序<br/>
    orderbynum : 按数值大小升序<br/>
    reversebynum : 按数值大小降序
</p>
<p>
    <span style="font-size: 14px; color: rgb(127, 127, 127);">表格data-sort-type属性值为reversebynum，<span style="color:#ff511a; font-weight: bold;">按照数值大小降序排序</span>，点击第一行的单元格进行排序。</span>
</p>
<div id="content1" class="content">
    <table data-sort="sortEnabled" width="992" class="sortEnabled" data-sort-type="reversebynum">
        <tbody>
        <tr class="firstRow"> <td width="165">343</td> <td width="165">352</td> <td width="165">323</td> <td width="165">234</td> <td width="165">379</td> <td width="166">782</td> </tr>
        <tr> <td width="165">341</td> <td width="165">163</td> <td width="165">422</td> <td width="165">234</td> <td width="165">725</td> <td width="166">833</td> </tr>
        <tr> <td width="165">221</td> <td width="165">456</td> <td width="165">335</td> <td width="165">423</td> <td width="165">445</td> <td width="166">793</td> </tr>
        <tr> <td width="165">112</td> <td width="165">277</td> <td width="165">563</td> <td width="165">423</td> <td width="165">932</td> <td width="166">425</td> </tr>
        <tr> <td width="165">587</td> <td width="165">175</td> <td width="165">159</td> <td width="165">734</td> <td width="165">582</td> <td width="166">458</td> </tr>
        </tbody>
    </table>
</div>

<p>
    <br />
</p>
<p>
    <span style="font-size: 14px; color: rgb(127, 127, 127);">自定义排序，<span style="color:#ff511a; font-weight: bold;">按照个位数排序</span>，点击第一行的单元格进行排序。</span>
</p>
<div id="content2" class="content">
    <table data-sort="sortEnabled" width="992" class="sortEnabled">
        <tbody>
        <tr class="firstRow"> <td width="165">343</td> <td width="165">352</td> <td width="165">323</td> <td width="165">234</td> <td width="165">379</td> <td width="166">782</td> </tr>
        <tr> <td width="165">341</td> <td width="165">163</td> <td width="165">422</td> <td width="165">234</td> <td width="165">725</td> <td width="166">833</td> </tr>
        <tr> <td width="165">221</td> <td width="165">456</td> <td width="165">335</td> <td width="165">423</td> <td width="165">445</td> <td width="166">793</td> </tr>
        <tr> <td width="165">112</td> <td width="165">277</td> <td width="165">563</td> <td width="165">423</td> <td width="165">932</td> <td width="166">425</td> </tr>
        <tr> <td width="165">587</td> <td width="165">175</td> <td width="165">159</td> <td width="165">734</td> <td width="165">582</td> <td width="166">458</td> </tr>
        </tbody>
    </table>
</div>


<script>
    // 语法
    // uParse(selector,[option])
    /*
     selector支持
     id,class,tagName
     */
    /*
     目前支持的参数
     option:
     highlightJsUrl 代码高亮相关js的路径 如果展示有代码高亮，必须给定该属性
     highlightCssUrl 代码高亮相关css的路径 如果展示有代码高亮，必须给定该属性
     liiconpath 自定义列表样式的图标路径，可以不给定，默认'http://bs.baidu.com/listicon/',
     listDefaultPaddingLeft : 自定义列表样式的左边宽度 默认'20',
     customRule 可以传入你自己的处理规则函数，函数第一个参数是容器节点
     */
    uParse('#content1',{
        rootPath : '../'
    })
    uParse('#content2',{
        rootPath : '../',
        tableSortCompareFn: function(td1, td2){
            var value1 = td1.innerText||td1.textContent,
                    value2 = td2.innerText||td2.textContent;
            return parseInt(value1) % 10 > parseInt(value2) % 10;
        }
    })

</script>

</body>
</html>