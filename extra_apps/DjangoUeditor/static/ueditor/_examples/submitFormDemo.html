<!DOCTYPE HTML>
<html>
<head>

    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <title></title>
    <script type="text/javascript" charset="utf-8" src="../ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="editor_api.js"></script>
    <style type="text/css">
        body{
            font-size:14px;
        }
    </style>
</head>
<body>
    <h2>UEditor提交示例</h2>
    <form id="form" method="post" target="_blank">
        <script type="text/plain" id="myEditor" name="myEditor">
            <p>欢迎使用UEditor！</p>
        </script>
        <input type="submit" value="通过input的submit提交">
    </form>
   <p>
       从1.2.6开始，会自动同步数据无需再手动调用sync方法
       <button onclick="document.getElementById('form').submit()">通过js调用submit提交</button>

   </p>

    <script type="text/javascript">
        var editor_a = UE.getEditor('myEditor',{initialFrameHeight:500});

        //--自动切换提交地址----
        var doc=document,
                version = editor_a.options.serverUrl || editor_a.options.imageUrl || "php",
                form=doc.getElementById("form");

        if(version.match(/php/)){
            form.action="./server/getContent.php";
        }else if(version.match(/net/)){
            form.action="./server/getContent.ashx";
        }else if(version.match(/jsp/)){
            form.action="./server/getContent.jsp";
        }else if(version.match(/asp/)){
            form.action="./server/getContent.asp";
        }
        //-----


    </script>

</body>


</html>
