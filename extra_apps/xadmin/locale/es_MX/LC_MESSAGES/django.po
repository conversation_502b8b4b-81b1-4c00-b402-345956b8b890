# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2013
# <AUTHOR> <EMAIL>, 2013
# <AUTHOR> <EMAIL>, 2013
# <AUTHOR> <EMAIL>, 2013
# <AUTHOR> <EMAIL>, 2013
# <AUTHOR> <EMAIL>, 2013
# <AUTHOR> <EMAIL>, 2013
msgid ""
msgstr ""
"Project-Id-Version: xadmin-core\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-07-20 13:28+0800\n"
"PO-Revision-Date: 2013-05-12 18:45+0000\n"
"Last-Translator: sacrac <<EMAIL>>\n"
"Language-Team: Spanish (Mexico) (http://www.transifex.com/projects/p/xadmin/"
"language/es_MX/)\n"
"Language: es_MX\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: adminx.py:19
msgid "Admin Object"
msgstr ""

#: apps.py:11
msgid "Administration"
msgstr ""

#: filters.py:159 filters.py:191 filters.py:407 filters.py:493 filters.py:531
msgid "All"
msgstr "Todo"

#: filters.py:160 plugins/export.py:165
msgid "Yes"
msgstr "Sí"

#: filters.py:161 plugins/export.py:165
msgid "No"
msgstr "No"

#: filters.py:175
msgid "Unknown"
msgstr "Desconocido"

#: filters.py:267
msgid "Any date"
msgstr "Cualquier fecha"

#: filters.py:268
#, fuzzy
msgid "Has date"
msgstr "Cualquier fecha"

#: filters.py:271
#, fuzzy
msgid "Has no date"
msgstr "Cualquier fecha"

#: filters.py:274 widgets.py:30
msgid "Today"
msgstr "Hoy"

#: filters.py:278
msgid "Past 7 days"
msgstr "Pasados ​​7 días"

#: filters.py:282
msgid "This month"
msgstr "Este mes"

#: filters.py:286
msgid "This year"
msgstr "Este año"

#: forms.py:10
msgid ""
"Please enter the correct username and password for a staff account. Note "
"that both fields are case-sensitive."
msgstr ""
"Por favor, introduzca el nombre de usuario y contraseña correctos de su "
"cuenta. Note que ambos campos son sensibles a mayúsculas y minúsculas."

#: forms.py:21
msgid "Please log in again, because your session has expired."
msgstr "Por favor, ingrese de nuevo, debido a que su sesión ha caducado."

#: forms.py:41
#, python-format
msgid "Your e-mail address is not your username. Try '%s' instead."
msgstr ""
"Tu dirección de correo no es tu nombre de usuario. Prueba '%s'  nuevamente."

#: models.py:48
msgid "Title"
msgstr "Titulo"

#: models.py:49 models.py:88 models.py:107 models.py:149
msgid "user"
msgstr ""

#: models.py:50
msgid "Url Name"
msgstr "Nombre de Url"

#: models.py:52
msgid "Query String"
msgstr "Cadena de consulta"

#: models.py:53
msgid "Is Shared"
msgstr "Es compartido"

#: models.py:66 plugins/bookmark.py:50 plugins/bookmark.py:180
msgid "Bookmark"
msgstr "Marcador"

#: models.py:67
msgid "Bookmarks"
msgstr "Marcadores"

#: models.py:89
msgid "Settings Key"
msgstr "Configuración llave"

#: models.py:90
msgid "Settings Content"
msgstr "Configuración de contenidos"

#: models.py:102
msgid "User Setting"
msgstr "Configuración del usuario"

#: models.py:103
msgid "User Settings"
msgstr "Configuraciones de los usuarios"

#: models.py:108
msgid "Page"
msgstr "Página"

#: models.py:109 views/dashboard.py:82 views/dashboard.py:92
msgid "Widget Type"
msgstr "Tipo de Widget"

#: models.py:110 views/dashboard.py:83
msgid "Widget Params"
msgstr "Parametros del Widget"

#: models.py:137
msgid "User Widget"
msgstr "Widget del Usuario"

#: models.py:138
msgid "User Widgets"
msgstr "Widgets del Usuario"

#: models.py:142
#, fuzzy
#| msgid "Date/time"
msgid "action time"
msgstr "Fecha/hora"

#: models.py:151
msgid "action ip"
msgstr ""

#: models.py:155
msgid "content type"
msgstr ""

#: models.py:158
msgid "object id"
msgstr ""

#: models.py:159
msgid "object repr"
msgstr ""

#: models.py:160
msgid "action flag"
msgstr ""

#: models.py:161
#, fuzzy
#| msgid "Change %s"
msgid "change message"
msgstr "Cambiar %s"

#: models.py:164
#, fuzzy
#| msgid "log in"
msgid "log entry"
msgstr "Iniciar sesión"

#: models.py:165
msgid "log entries"
msgstr ""

#: models.py:173
#, python-format
msgid "Added \"%(object)s\"."
msgstr ""

#: models.py:175
#, fuzzy, python-format
msgid "Changed \"%(object)s\" - %(changes)s"
msgstr "Cambiar multiples objetos"

#: models.py:180
#, fuzzy, python-format
#| msgid "Related Objects"
msgid "Deleted \"%(object)s.\""
msgstr "Objetos relacionados"

#: plugins/actions.py:57
#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "Borrar selección %(verbose_name_plural)s"

#: plugins/actions.py:72
#, fuzzy, python-format
#| msgid "Successfully deleted %(count)d %(items)s."
msgid "Batch delete %(count)d %(items)s."
msgstr "Correctamente eliminado %(count)d %(items)s."

#: plugins/actions.py:78
#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr "Correctamente eliminado %(count)d %(items)s."

#: plugins/actions.py:110 views/delete.py:70
#, python-format
msgid "Cannot delete %(name)s"
msgstr "No se puede eliminar %(name)s"

#: plugins/actions.py:112 views/delete.py:73
msgid "Are you sure?"
msgstr "Esta seguro?"

#: plugins/actions.py:158
#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "%(total_count)s seleccionados"
msgstr[1] "Todos los  %(total_count)s seleccionados"

#: plugins/actions.py:162
#, python-format
msgid "0 of %(cnt)s selected"
msgstr "0 de %(cnt)s seleccionado"

#: plugins/actions.py:179 plugins/actions.py:189
msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""
"Los articulos deben de ser seleccionados en orden para realizar la acción. "
"No existen artículos que han cambiado"

#: plugins/aggregation.py:14
msgid "Min"
msgstr "Minimo"

#: plugins/aggregation.py:14
msgid "Max"
msgstr "Maximo"

#: plugins/aggregation.py:14
msgid "Avg"
msgstr "Promedio"

#: plugins/aggregation.py:14
msgid "Sum"
msgstr "Suma"

#: plugins/aggregation.py:14
msgid "Count"
msgstr "Conteo"

#: plugins/auth.py:21
#, fuzzy, python-format
msgid "Can add %s"
msgstr "Añadir %s"

#: plugins/auth.py:22
#, fuzzy, python-format
msgid "Can change %s"
msgstr "Cambiar %s"

#: plugins/auth.py:23
#, python-format
msgid "Can edit %s"
msgstr ""

#: plugins/auth.py:24
#, fuzzy, python-format
msgid "Can delete %s"
msgstr "No se puede eliminar %(name)s"

#: plugins/auth.py:25
#, python-format
msgid "Can view %s"
msgstr ""

#: plugins/auth.py:87
msgid "Personal info"
msgstr "Información personal"

#: plugins/auth.py:91
msgid "Permissions"
msgstr "Permisos"

#: plugins/auth.py:94
msgid "Important dates"
msgstr "Fechas importantes"

#: plugins/auth.py:99
msgid "Status"
msgstr "Estados"

#: plugins/auth.py:111
#, fuzzy
msgid "Permission Name"
msgstr "Permisos"

#: plugins/auth.py:167
#, fuzzy
msgid "Change Password"
msgstr "Cambiar Contraseña"

#: plugins/auth.py:198
#, python-format
msgid "Change password: %s"
msgstr "Cambiar contraseña: %s"

#: plugins/auth.py:223 plugins/auth.py:255
msgid "Password changed successfully."
msgstr "Contraseña cambiada correctamente"

#: plugins/auth.py:242 templates/xadmin/auth/user/change_password.html:11
#: templates/xadmin/auth/user/change_password.html:22
#: templates/xadmin/auth/user/change_password.html:55
msgid "Change password"
msgstr "Cambiar contraseña"

#: plugins/batch.py:44
#, fuzzy
msgid "Change this field"
msgstr "Historial de cambios: %s"

#: plugins/batch.py:65
#, python-format
msgid "Batch Change selected %(verbose_name_plural)s"
msgstr "Cambio de grupo seleccionado %(verbose_name_plural)s"

#: plugins/batch.py:89
#, fuzzy, python-format
msgid "Successfully change %(count)d %(items)s."
msgstr "Correctamente eliminado %(count)d %(items)s."

#: plugins/batch.py:138
#, fuzzy, python-format
msgid "Batch change %s"
msgstr "Cambiar %s"

#: plugins/bookmark.py:173
#, fuzzy
msgid "bookmark"
msgstr "Marcador"

#: plugins/bookmark.py:176
msgid "Bookmark Widget, can show user's bookmark list data in widget."
msgstr ""
"Widget de marcador, puede mostrar datos de la lista de favoritos del usuario "
"en el widget."

#: plugins/chart.py:25
msgid "Show models simple chart."
msgstr "Mostrar simple grafos para los modelos"

#: plugins/chart.py:51
#, python-format
msgid "%s Charts"
msgstr "%s Graficos"

#: plugins/comments.py:33
msgid "Metadata"
msgstr ""

#: plugins/comments.py:60
msgid "flagged"
msgid_plural "flagged"
msgstr[0] ""
msgstr[1] ""

#: plugins/comments.py:61
msgid "Flag selected comments"
msgstr ""

#: plugins/comments.py:66
msgid "approved"
msgid_plural "approved"
msgstr[0] ""
msgstr[1] ""

#: plugins/comments.py:67
msgid "Approve selected comments"
msgstr ""

#: plugins/comments.py:72
#, fuzzy
msgid "removed"
msgid_plural "removed"
msgstr[0] "Eliminar"
msgstr[1] "Eliminar"

#: plugins/comments.py:73
#, fuzzy
msgid "Remove selected comments"
msgstr "Recuperar borrado %(name)s"

#: plugins/comments.py:86
#, python-format
msgid "1 comment was successfully %(action)s."
msgid_plural "%(count)s comments were successfully %(action)s."
msgstr[0] ""
msgstr[1] ""

#: plugins/details.py:52 views/list.py:578
#, python-format
msgid "Details of %s"
msgstr "Detalles de %s"

#: plugins/editable.py:46
#, python-format
msgid "Enter %s"
msgstr "Entre %s"

#: plugins/editable.py:73 views/dashboard.py:649 views/delete.py:27
#: views/detail.py:145 views/edit.py:454
#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "%(name)s el objeto con la llave primaria %(key)r no existe."

#: plugins/export.py:98 plugins/export.py:135
msgid "Sheet"
msgstr "Hoja"

#: plugins/filters.py:133 plugins/quickfilter.py:141
#, python-format
msgid "<b>Filtering error:</b> %s"
msgstr ""

#: plugins/images.py:29
msgid "Previous"
msgstr "Previo"

#: plugins/images.py:29
msgid "Next"
msgstr "Próximo "

#: plugins/images.py:29
msgid "Slideshow"
msgstr "presentación"

#: plugins/images.py:29
msgid "Download"
msgstr "Descargar"

#: plugins/images.py:50
msgid "Change:"
msgstr "Cambiar:"

#: plugins/layout.py:16
msgid "Table"
msgstr ""

#: plugins/layout.py:22
msgid "Thumbnails"
msgstr ""

#: plugins/passwords.py:64
msgid "Forgotten your password or username?"
msgstr "¿Olvidó su contraseña o nombre de usuario?"

#: plugins/quickform.py:79
#, python-format
msgid "Create New %s"
msgstr ""

#: plugins/relate.py:104
msgid "Related Objects"
msgstr "Objetos relacionados"

#: plugins/relfield.py:29 plugins/topnav.py:38
#, python-format
msgid "Search %s"
msgstr "Buscar %s"

#: plugins/relfield.py:67
#, fuzzy, python-format
#| msgid "Select Date"
msgid "Select %s"
msgstr "Seleccionar Fecha"

#: plugins/themes.py:47
#, fuzzy
msgid "Default"
msgstr "Tema por defecto"

#: plugins/themes.py:48
#, fuzzy
msgid "Default bootstrap theme"
msgstr "Tema bootstrap por defecto"

#: plugins/themes.py:49
msgid "Bootstrap2"
msgstr ""

#: plugins/themes.py:49
#, fuzzy
msgid "Bootstrap 2.x theme"
msgstr "Tema bootstrap por defecto"

#: plugins/topnav.py:62 views/dashboard.py:465 views/edit.py:387
#: views/edit.py:396
#, python-format
msgid "Add %s"
msgstr "Añadir %s"

#: plugins/xversion.py:106
msgid "Initial version."
msgstr "Versión inicial."

#: plugins/xversion.py:108
msgid "Change version."
msgstr "Cambiar la versión."

#: plugins/xversion.py:110
msgid "Revert version."
msgstr "Revertir la versión."

#: plugins/xversion.py:112
msgid "Rercover version."
msgstr "Recuperar versión"

#: plugins/xversion.py:114
#, python-format
msgid "Deleted %(verbose_name)s."
msgstr "Borrado %(verbose_name)s."

#: plugins/xversion.py:127 templates/xadmin/views/recover_form.html:26
msgid "Recover"
msgstr "Recuperar"

#: plugins/xversion.py:143 templates/xadmin/views/model_history.html:11
#: templates/xadmin/views/revision_diff.html:11
#: templates/xadmin/views/revision_form.html:15
msgid "History"
msgstr "Historico"

#: plugins/xversion.py:194 templates/xadmin/views/recover_form.html:14
#: templates/xadmin/views/recover_list.html:10
#, python-format
msgid "Recover deleted %(name)s"
msgstr "Recuperar borrado %(name)s"

#: plugins/xversion.py:238
#, python-format
msgid "Change history: %s"
msgstr "Historial de cambios: %s"

#: plugins/xversion.py:288
msgid "Must select two versions."
msgstr "Debe seleccionar dos versiones."

#: plugins/xversion.py:296
msgid "Please select two different versions."
msgstr "Por favor seleccione dos versiones diferente."

#: plugins/xversion.py:383 plugins/xversion.py:500
#, python-format
msgid "Current: %s"
msgstr "Actual: %s"

#: plugins/xversion.py:424
#, python-format
msgid "Revert %s"
msgstr "Revertir %s"

#: plugins/xversion.py:440
#, python-format
msgid ""
"The %(model)s \"%(name)s\" was reverted successfully. You may edit it again "
"below."
msgstr ""
"El %(model)s \"%(name)s\" se revirtió con éxito. Puede editarlo de nuevo a "
"continuación."

#: plugins/xversion.py:461
#, python-format
msgid "Recover %s"
msgstr "Recuperar %s"

#: plugins/xversion.py:477
#, python-format
msgid ""
"The %(model)s \"%(name)s\" was recovered successfully. You may edit it again "
"below."
msgstr ""
"El %(model)s \"%(name) s\" se recuperó con éxito. Puede editarlo de nuevo a "
"continuación."

#: templates/xadmin/404.html:4 templates/xadmin/404.html:8
msgid "Page not found"
msgstr "Pagina no encontrada"

#: templates/xadmin/404.html:10
msgid "We're sorry, but the requested page could not be found."
msgstr "Lo sentimos pero la pagina que usted solicita no se encuentra."

#: templates/xadmin/500.html:7
#: templates/xadmin/auth/user/change_password.html:10
#: templates/xadmin/auth/user/change_password.html:15
#: templates/xadmin/base_site.html:53
#: templates/xadmin/includes/sitemenu_default.html:7
#: templates/xadmin/views/app_index.html:9
#: templates/xadmin/views/batch_change_form.html:9
#: templates/xadmin/views/invalid_setup.html:7
#: templates/xadmin/views/model_dashboard.html:7
#: templates/xadmin/views/model_delete_selected_confirm.html:8
#: templates/xadmin/views/model_history.html:8
#: templates/xadmin/views/recover_form.html:8
#: templates/xadmin/views/recover_list.html:8
#: templates/xadmin/views/revision_diff.html:8
#: templates/xadmin/views/revision_form.html:8 views/base.py:473
msgid "Home"
msgstr "Inicio"

#: templates/xadmin/500.html:8
msgid "Server error"
msgstr "Error en el servidor"

#: templates/xadmin/500.html:12
msgid "Server error (500)"
msgstr "Error en el servidor (500)"

#: templates/xadmin/500.html:15
msgid "Server Error <em>(500)</em>"
msgstr "Error en el servidor <em>(500)</em>"

#: templates/xadmin/500.html:16
msgid ""
"There's been an error. It's been reported to the site administrators via e-"
"mail and should be fixed shortly. Thanks for your patience."
msgstr ""
"Ha habido un error. Se nos ha informado a los administradores del sitio a "
"través de un correo electrónico y debe repararse en la breve. Gracias por su "
"paciencia."

#: templates/xadmin/auth/password_reset/complete.html:11
#: templates/xadmin/auth/password_reset/done.html:11
msgid "Password reset successful"
msgstr "Restablecimiento de contraseña con éxito"

#: templates/xadmin/auth/password_reset/complete.html:14
msgid "Your password has been set.  You may go ahead and log in now."
msgstr "Se ha establecido su contraseña. Usted puede acceder ahora mismo."

#: templates/xadmin/auth/password_reset/complete.html:15
msgid "Log in"
msgstr "Entrar"

#: templates/xadmin/auth/password_reset/confirm.html:12
msgid "Enter new password"
msgstr "Escriba la nueva contraseña"

#: templates/xadmin/auth/password_reset/confirm.html:17
msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""
"Introduzca su nueva contraseña dos veces para que podamos verificar que la "
"ha escrito correctamente."

#: templates/xadmin/auth/password_reset/confirm.html:19
msgid "Change my password"
msgstr "Cambiar mi contraseña"

#: templates/xadmin/auth/password_reset/confirm.html:24
msgid "Password reset unsuccessful"
msgstr "Restablecimiento de contraseña sin éxito"

#: templates/xadmin/auth/password_reset/confirm.html:27
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""
"El enlace de restablecimiento de contraseña no es válida, posiblemente "
"debido a que ya se ha utilizado. Por favor, solicite un nuevo "
"restablecimiento de contraseña."

#: templates/xadmin/auth/password_reset/done.html:14
msgid ""
"We've e-mailed you instructions for setting your password to the e-mail "
"address you submitted. You should be receiving it shortly."
msgstr ""
"Nosotro te hemos enviado por correo electrónico las instrucciones para "
"configurar la contraseña a la dirección de correo electrónico que ha "
"enviado. Usted debe recibirlo en breve momento."

#: templates/xadmin/auth/password_reset/email.html:2
#, python-format
msgid ""
"You're receiving this e-mail because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""
"Tu has recibido este correo debido a que has solicitado restablecimiento de "
"contraseña para tu cuenta de usuario en %(site_name)s."

#: templates/xadmin/auth/password_reset/email.html:4
msgid "Please go to the following page and choose a new password:"
msgstr "Por favor ir a la siguiente pagina y selecciona una nueva contraseña"

#: templates/xadmin/auth/password_reset/email.html:8
msgid "Your username, in case you've forgotten:"
msgstr "Tu usuario, en caso de que lo hayas olvidado"

#: templates/xadmin/auth/password_reset/email.html:10
msgid "Thanks for using our site!"
msgstr "Gracias por usar nuestro sitio web!"

#: templates/xadmin/auth/password_reset/email.html:12
#, python-format
msgid "The %(site_name)s team"
msgstr "El equipo de %(site_name)s"

#: templates/xadmin/auth/password_reset/form.html:13
msgid "Password reset"
msgstr "restablecimiento de contraseña"

#: templates/xadmin/auth/password_reset/form.html:17
msgid ""
"Forgotten your password? Enter your e-mail address below, and we'll e-mail "
"instructions for setting a new one."
msgstr ""
"¿Olvidaste tu contraseña? Escribe tu dirección de e-mail debajo, y le "
"enviaremos un e-mail para el establecimiento de una nueva."

#: templates/xadmin/auth/password_reset/form.html:25
msgid "E-mail address:"
msgstr "Dirección de correo:"

#: templates/xadmin/auth/password_reset/form.html:33
msgid "Reset my password"
msgstr "Cambiar contraseña"

#: templates/xadmin/auth/user/add_form.html:6
msgid ""
"First, enter a username and password. Then, you'll be able to edit more user "
"options."
msgstr ""
"Primero, ingrese usuario y contraseña. Luego, sera capaz de editar mas "
"opciones de usuario."

#: templates/xadmin/auth/user/add_form.html:8
msgid "Enter a username and password."
msgstr "Ingresa nombre de usuario y contraseña"

#: templates/xadmin/auth/user/change_password.html:31
#: templates/xadmin/views/batch_change_form.html:24
#: templates/xadmin/views/form.html:18
#: templates/xadmin/views/model_form.html:20
msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "Por favor corrija el error abajo."
msgstr[1] "Por favor corrija los  errores abajo."

#: templates/xadmin/auth/user/change_password.html:38
msgid "Enter your new password."
msgstr "Ingresa una nueva contraseña"

#: templates/xadmin/auth/user/change_password.html:40
#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""
"Introduzca una nueva contraseña para el usuario <strong>%(username)s</strong>"

#: templates/xadmin/base_site.html:18
msgid "Welcome,"
msgstr "Bienvenidos,"

#: templates/xadmin/base_site.html:24
msgid "Log out"
msgstr "Salir"

#: templates/xadmin/base_site.html:36
msgid "You don't have permission to edit anything."
msgstr "No tiene permiso para editar nada. "

#: templates/xadmin/blocks/comm.top.theme.html:4
msgid "Themes"
msgstr "Temas"

#: templates/xadmin/blocks/comm.top.topnav.html:9
#: templates/xadmin/blocks/model_list.nav_form.search_form.html:8
#: templates/xadmin/filters/char.html:7
#: templates/xadmin/filters/fk_search.html:7
#: templates/xadmin/filters/fk_search.html:16
#: templates/xadmin/filters/number.html:7
msgid "Search"
msgstr "Buscar"

#: templates/xadmin/blocks/comm.top.topnav.html:23
msgid "Add"
msgstr "Agregar"

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:9
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:26
msgid "Prev step"
msgstr "Paso previo"

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:13
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:29
msgid "Next step"
msgstr "Próximo paso"

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:15
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:31
#: templates/xadmin/includes/submit_line.html:10
#: templates/xadmin/includes/submit_line.html:13
#: templates/xadmin/views/form.html:30 templates/xadmin/views/form.html:31
msgid "Save"
msgstr "Guardar"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:7
msgid "Clean Bookmarks"
msgstr "Limpiar marcadores"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:18
msgid "No Bookmarks"
msgstr "No hay marcadores"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:22
msgid "New Bookmark"
msgstr "Nuevo marcador"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:26
msgid "Save current page as Bookmark"
msgstr "Guardar actual pagino como Marcador"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:32
msgid "Enter bookmark title"
msgstr "Ingrese titulo de marcador"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:33
msgid "Waiting"
msgstr "Espera un momento"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:33
msgid "Save Bookmark"
msgstr "Guardar marcador"

#: templates/xadmin/blocks/model_list.nav_menu.filters.html:4
msgid "Filters"
msgstr "Filtros"

#: templates/xadmin/blocks/model_list.nav_menu.filters.html:8
msgid "Clean Filters"
msgstr "Limpiar Filtros"

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:19
msgid "Click here to select the objects across all pages"
msgstr ""
"Haz clic aquí para seleccionar los objetos a través de todas las páginas"

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:19
#, python-format
msgid "Select all %(total_count)s %(model_name)s"
msgstr "Seleccionar todo %(total_count)s %(model_name)s"

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:20
msgid "Clear selection"
msgstr "Limpiar seleccion"

#: templates/xadmin/blocks/model_list.results_top.charts.html:4
msgid "Charts"
msgstr "Graficos"

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:4
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:8
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:19
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:47
msgid "Export"
msgstr "Exportar"

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:26
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:29
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:32
msgid "Export with table header."
msgstr "Exportar con la cabecera de la tabla."

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:35
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:38
msgid "Export with format."
msgstr "Exportar con formato"

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:42
msgid "Export all data."
msgstr "Exportar todos los datos."

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:46
#: templates/xadmin/widgets/base.html:41
msgid "Close"
msgstr "Cerrar"

#: templates/xadmin/blocks/model_list.top_toolbar.layouts.html:4
msgid "Layout"
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.refresh.html:8
msgid "Clean Refresh"
msgstr "Limpiar Refrescar"

#: templates/xadmin/blocks/model_list.top_toolbar.refresh.html:14
#, fuzzy, python-format
msgid "Every %(t)s seconds"
msgstr "Por %(t)s segundos"

#: templates/xadmin/blocks/model_list.top_toolbar.saveorder.html:4
msgid "Save Order"
msgstr ""

#: templates/xadmin/edit_inline/blank.html:5 views/detail.py:23
#: views/edit.py:102 views/list.py:29
msgid "Null"
msgstr "Nulo"

#: templates/xadmin/filters/char.html:13
msgid "Enter"
msgstr "Introducir"

#: templates/xadmin/filters/date.html:10 templates/xadmin/filters/date.html:13
msgid "Choice Date"
msgstr "Elija Fecha"

#: templates/xadmin/filters/date.html:18
msgid "YY"
msgstr ""

#: templates/xadmin/filters/date.html:19
msgid "year"
msgstr "año"

#: templates/xadmin/filters/date.html:22
msgid "MM"
msgstr ""

#: templates/xadmin/filters/date.html:23
msgid "month"
msgstr "Mes"

#: templates/xadmin/filters/date.html:26
msgid "DD"
msgstr ""

#: templates/xadmin/filters/date.html:27
msgid "day"
msgstr "día"

#: templates/xadmin/filters/date.html:29 templates/xadmin/filters/date.html:46
#: templates/xadmin/filters/date.html:54
#: templates/xadmin/filters/fk_search.html:24
#: templates/xadmin/filters/number.html:37
msgid "Apply"
msgstr "Aplicar"

#: templates/xadmin/filters/date.html:34
msgid "Date Range"
msgstr "Rangos de fechas"

#: templates/xadmin/filters/date.html:41
msgid "Select Date"
msgstr "Seleccionar Fecha"

#: templates/xadmin/filters/date.html:42
msgid "From"
msgstr "De"

#: templates/xadmin/filters/date.html:44
msgid "To"
msgstr "Para"

#: templates/xadmin/filters/fk_search.html:14
#, fuzzy
#| msgid "Select Date"
msgid "Select"
msgstr "Seleccionar Fecha"

#: templates/xadmin/filters/fk_search.html:26
#: templates/xadmin/filters/number.html:39
msgid "Clean"
msgstr "Limpiar"

#: templates/xadmin/filters/number.html:17
#: templates/xadmin/filters/number.html:25
#: templates/xadmin/filters/number.html:33
msgid "Enter Number"
msgstr "Ingrese el numero"

#: templates/xadmin/filters/rel.html:3
#, python-format
msgid " By %(filter_title)s "
msgstr "Por %(filter_title)s"

#: templates/xadmin/forms/transfer.html:4
msgid "Available"
msgstr "Disponible"

#: templates/xadmin/forms/transfer.html:12
msgid "Click to choose all at once."
msgstr "Click para elegir todos"

#: templates/xadmin/forms/transfer.html:12
msgid "Choose all"
msgstr "Seleccionar todos"

#: templates/xadmin/forms/transfer.html:16
msgid "Choose"
msgstr "Elegir"

#: templates/xadmin/forms/transfer.html:19
#: templates/xadmin/widgets/base.html:40
msgid "Remove"
msgstr "Eliminar"

#: templates/xadmin/forms/transfer.html:23
msgid "Chosen"
msgstr "Preferido"

#: templates/xadmin/forms/transfer.html:27
msgid "Click to remove all chosen at once."
msgstr "Click para remover todos los preferidos"

#: templates/xadmin/forms/transfer.html:27
msgid "Remove all"
msgstr "Remover todos"

#: templates/xadmin/includes/pagination.html:9
msgid "Show all"
msgstr "Mostrar todos"

#: templates/xadmin/includes/submit_line.html:10
#: templates/xadmin/includes/submit_line.html:13
#: templates/xadmin/views/form.html:30 templates/xadmin/views/form.html:31
msgid "Saving.."
msgstr ""

#: templates/xadmin/includes/submit_line.html:17
msgid "Save as new"
msgstr "Guardar como nuevo"

#: templates/xadmin/includes/submit_line.html:18
msgid "Save and add another"
msgstr "Guardar y añadir otro"

#: templates/xadmin/includes/submit_line.html:19
msgid "Save and continue editing"
msgstr "Guardar y continuar editando"

#: templates/xadmin/includes/submit_line.html:24
#: templates/xadmin/views/model_detail.html:28 views/delete.py:93
msgid "Delete"
msgstr "Borrar"

#: templates/xadmin/views/app_index.html:13
#, python-format
msgid "%(name)s"
msgstr "%(name)s"

#: templates/xadmin/views/batch_change_form.html:11
msgid "Change multiple objects"
msgstr "Cambiar multiples objetos"

#: templates/xadmin/views/batch_change_form.html:16
#, fuzzy, python-format
msgid "Change one %(objects_name)s"
msgid_plural "Batch change %(counter)s %(objects_name)s"
msgstr[0] "Cambiar multiples objetos"
msgstr[1] "Cambiar multiples objetos"

#: templates/xadmin/views/batch_change_form.html:38
msgid "Change Multiple"
msgstr "Cambios multiples"

#: templates/xadmin/views/dashboard.html:15
#: templates/xadmin/views/dashboard.html:22
#: templates/xadmin/views/dashboard.html:23
msgid "Add Widget"
msgstr "Añadir Widget"

#: templates/xadmin/views/invalid_setup.html:13
msgid ""
"Something's wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""
"Algo está mal con tu instalación de base de datos. Asegúrese de que las "
"tablas de la bases de datos se han creado apropiadamente, y asegúrese de que "
"la base de datos puede ser leído por el usuario apropiado"

#: templates/xadmin/views/logged_out.html:16
msgid "Logout Success"
msgstr "Salio con éxito "

#: templates/xadmin/views/logged_out.html:17
msgid "Thanks for spending some quality time with the Web site today."
msgstr "Gracias por pasar tiempo de calidad con el sitio web hoy."

#: templates/xadmin/views/logged_out.html:19
msgid "Close Window"
msgstr "Cerrar ventana"

#: templates/xadmin/views/logged_out.html:20
msgid "Log in again"
msgstr "Entrar nuevamente"

#: templates/xadmin/views/login.html:39 views/website.py:38
msgid "Please Login"
msgstr "Por favor, inicia sesión"

#: templates/xadmin/views/login.html:52
#, fuzzy
msgid "Username"
msgstr "Usuario:"

#: templates/xadmin/views/login.html:64
#, fuzzy
msgid "Password"
msgstr "Contraseña:"

#: templates/xadmin/views/login.html:75
msgid "log in"
msgstr "Iniciar sesión"

#: templates/xadmin/views/model_dashboard.html:26
#: templates/xadmin/views/model_detail.html:25
msgid "Edit"
msgstr "Editar."

#: templates/xadmin/views/model_delete_confirm.html:11
#, python-format
msgid ""
"Deleting the %(verbose_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""
"Eliminando los %(verbose_name)s '%(escaped_object)s terminaría eliminando "
"objetos relacionados, pero su cuenta no tiene permisos para eliminar los "
"siguientes tipo de objetos:"

#: templates/xadmin/views/model_delete_confirm.html:19
#, python-format
msgid ""
"Deleting the %(verbose_name)s '%(escaped_object)s' would require deleting "
"the following protected related objects:"
msgstr ""
"Eliminando los %(verbose_name)s '%(escaped_object)s requeriría eliminar los "
"siguientes objetos relacionas protegidos:"

#: templates/xadmin/views/model_delete_confirm.html:27
#, python-format
msgid ""
"Are you sure you want to delete the %(verbose_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""
"¿Estas seguro de querer eliminar los %(verbose_name)s \"%(escaped_object)s"
"\" ? Todos los siguientes artículos relacionados serán eliminados:"

#: templates/xadmin/views/model_delete_confirm.html:34
#: templates/xadmin/views/model_delete_selected_confirm.html:49
msgid "Yes, I'm sure"
msgstr "Si, estoy seguro"

#: templates/xadmin/views/model_delete_confirm.html:35
#: templates/xadmin/views/model_delete_selected_confirm.html:50
#, fuzzy
msgid "Cancel"
msgstr "Cancelar ordenación"

#: templates/xadmin/views/model_delete_selected_confirm.html:10
msgid "Delete multiple objects"
msgstr "Eliminar múltiples objetos."

#: templates/xadmin/views/model_delete_selected_confirm.html:18
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""
"Eliminando los %(objects_name)s terminaría eliminando objetos relacionados, "
"pero su cuenta no tiene permisos para eliminar los siguientes tipo de "
"objetos:"

#: templates/xadmin/views/model_delete_selected_confirm.html:26
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""
"Eliminando  los %(objects_name)s seleccionados terminaria emilinando los "
"siguientes objetos relacionados protegidos:"

#: templates/xadmin/views/model_delete_selected_confirm.html:34
#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""
"¿Estas seguro de querer borrar los %(objects_name)s seleccionados? Todos los "
"siguientes objetos y sus artículos relacionados serán eliminados:"

#: templates/xadmin/views/model_history.html:26
msgid "Diff"
msgstr "Differencia"

#: templates/xadmin/views/model_history.html:27
#: templates/xadmin/views/recover_list.html:25
msgid "Date/time"
msgstr "Fecha/hora"

#: templates/xadmin/views/model_history.html:28
msgid "User"
msgstr "Usuario"

#: templates/xadmin/views/model_history.html:29
msgid "Comment"
msgstr "Comentario"

#: templates/xadmin/views/model_history.html:54
msgid "Diff Select Versions"
msgstr "Seleccionar diferentes versiones"

#: templates/xadmin/views/model_history.html:58
msgid ""
"This object doesn't have a change history. It probably wasn't added via this "
"admin site."
msgstr ""
"Este objeto no tiene un historial de cambios, Es probable  que no se haya "
"añadido a través de este sitio de admin.|"

#: templates/xadmin/views/model_list.html:29
#, python-format
msgid "Add %(name)s"
msgstr "Agregar %(name)s"

#: templates/xadmin/views/model_list.html:39
#, fuzzy
msgid "Columns"
msgstr "Seleccionar columns."

#: templates/xadmin/views/model_list.html:42
msgid "Restore Selected"
msgstr "Restaurar seleccionados."

#: templates/xadmin/views/model_list.html:147
#: templates/xadmin/widgets/list.html:33
msgid "Empty list"
msgstr "Lista vacia"

#: templates/xadmin/views/recover_form.html:20
msgid "Press the recover button below to recover this version of the object."
msgstr ""
"Presione el botón de recuperación abajo para recuperar esta versión del "
"objeto."

#: templates/xadmin/views/recover_list.html:19
msgid ""
"Choose a date from the list below to recover a deleted version of an object."
msgstr ""
"Selecciona una fecha de la lista de abajo para recuperar una versión "
"eliminada de un objeto."

#: templates/xadmin/views/recover_list.html:39
msgid "There are no deleted objects to recover."
msgstr "No hay objetos eliminados para recuperar."

#: templates/xadmin/views/revision_diff.html:12
#: templates/xadmin/views/revision_diff.html:17
#, python-format
msgid "Diff %(verbose_name)s"
msgstr "Diferentes %(verbose_name)s"

#: templates/xadmin/views/revision_diff.html:25
msgid "Field"
msgstr "Campo"

#: templates/xadmin/views/revision_diff.html:26
msgid "Version A"
msgstr "Versión A"

#: templates/xadmin/views/revision_diff.html:27
msgid "Version B"
msgstr "Versión B"

#: templates/xadmin/views/revision_diff.html:39
msgid "Revert to"
msgstr "Revertir a"

#: templates/xadmin/views/revision_diff.html:40
#: templates/xadmin/views/revision_diff.html:41
msgid "Revert"
msgstr "Revertir"

#: templates/xadmin/views/revision_form.html:16
#, python-format
msgid "Revert %(verbose_name)s"
msgstr "Revertir  %(verbose_name)s"

#: templates/xadmin/views/revision_form.html:21
msgid "Press the revert button below to revert to this version of the object."
msgstr ""
"Presiona el botón revertir de abajo para revertir a esta versión del objeto."

#: templates/xadmin/views/revision_form.html:27
msgid "Revert this revision"
msgstr "Revertir esta revisión"

#: templates/xadmin/widgets/addform.html:14
msgid "Success"
msgstr "Éxito"

#: templates/xadmin/widgets/addform.html:14
msgid "Add success, click <a id='change-link'>edit</a> to edit."
msgstr ""
"Agregado exitosa mente, presiona  <a id='change-link'>edit</a> para editar."

#: templates/xadmin/widgets/addform.html:17
msgid "Quick Add"
msgstr "Agregar rápidamente."

#: templates/xadmin/widgets/base.html:31
msgid "Widget Options"
msgstr "Opciones del Widget"

#: templates/xadmin/widgets/base.html:42
msgid "Save changes"
msgstr "Guardar cambios"

#: views/base.py:315
msgid "Django Xadmin"
msgstr "Django Xadmin"

#: views/base.py:316
msgid "my-company.inc"
msgstr ""

#: views/dashboard.py:186
msgid "Widget ID"
msgstr "ID del Widget"

#: views/dashboard.py:187
msgid "Widget Title"
msgstr "Titulo del Widget"

#: views/dashboard.py:252
msgid "Html Content Widget, can write any html content in widget."
msgstr ""
"Contenido html del Widget, puede escribir cualquier contenido del widget."

#: views/dashboard.py:255
msgid "Html Content"
msgstr "Contenido HTML"

#: views/dashboard.py:318
msgid "Target Model"
msgstr "Modelo objeto"

#: views/dashboard.py:369
msgid "Quick button Widget, quickly open any page."
msgstr "botón rapido del widget, abrir rápidamente cualquier página."

#: views/dashboard.py:371
msgid "Quick Buttons"
msgstr "Botones rapidos"

#: views/dashboard.py:416
msgid "Any Objects list Widget."
msgstr "Cualquier objeto listado en el widget"

#: views/dashboard.py:456
msgid "Add any model object Widget."
msgstr "Añadir cualquier modelo de objeto al Widget."

#: views/dashboard.py:492
msgid "Dashboard"
msgstr "Panel de control"

#: views/dashboard.py:633
#, fuzzy, python-format
msgid "%s Dashboard"
msgstr "Panel de control"

#: views/delete.py:103
#, python-format
msgid "The %(name)s \"%(obj)s\" was deleted successfully."
msgstr "El   %(name)s \"%(obj)s\" fue eliminado exitosa mente."

#: views/detail.py:173 views/edit.py:211 views/form.py:72
msgid "Other Fields"
msgstr "Otros campos"

#: views/detail.py:235
#, python-format
msgid "%s Detail"
msgstr "%s Detalles"

#: views/edit.py:253
msgid "Added."
msgstr ""

#: views/edit.py:255
#, fuzzy, python-format
#| msgid "Change %s"
msgid "Changed %s."
msgstr "Cambiar %s"

#: views/edit.py:255
msgid "and"
msgstr ""

#: views/edit.py:258
msgid "No fields changed."
msgstr ""

#: views/edit.py:420
#, python-format
msgid "The %(name)s \"%(obj)s\" was added successfully."
msgstr "El   %(name)s \"%(obj)s\" fue agregado exitosa mente."

#: views/edit.py:425 views/edit.py:520
msgid "You may edit it again below."
msgstr "Puedes editarlo de nuevo abajo."

#: views/edit.py:429 views/edit.py:523
#, python-format
msgid "You may add another %s below."
msgstr "Puedes agregar otro %s debajo."

#: views/edit.py:471
#, python-format
msgid "Change %s"
msgstr "Cambiar %s"

#: views/edit.py:516
#, python-format
msgid "The %(name)s \"%(obj)s\" was changed successfully."
msgstr "El %(name)s \"%(obj)s\" fue editado exitosa mente."

#: views/form.py:165
#, fuzzy, python-format
msgid "The %s was changed successfully."
msgstr "El %(name)s \"%(obj)s\" fue editado exitosa mente."

#: views/list.py:199
msgid "Database error"
msgstr "Error de base de datos"

#: views/list.py:373
#, python-format
msgid "%s List"
msgstr "%s Lista"

#: views/list.py:499
msgid "Sort ASC"
msgstr "Ordenar ascendente mente."

#: views/list.py:500
msgid "Sort DESC"
msgstr "Ordenar descendente mente"

#: views/list.py:504
msgid "Cancel Sort"
msgstr "Cancelar ordenación"

#: views/website.py:16
msgid "Main Dashboard"
msgstr "Dashboard principal."

#: widgets.py:48
msgid "Now"
msgstr "Ahora"

#~ msgid "Add Other %s"
#~ msgstr "Añadir otro %s"

#~ msgid "Recover deleted"
#~ msgstr "Recuperar borrado"

#~ msgid "Documentation"
#~ msgstr "Documentación"

#~ msgid "Password change"
#~ msgstr "Cambiar contraseña"

#~ msgid "Password change successful"
#~ msgstr "Contraseña cambio con exito "

#~ msgid "Your password was changed."
#~ msgstr "Tu contraseña fue cambiada."

#~ msgid ""
#~ "Please enter your old password, for security's sake, and then enter your "
#~ "new password twice so we can verify you typed it in correctly."
#~ msgstr ""
#~ "Introduzca su antigua contraseña, por el bien de la seguridad, y luego "
#~ "ingrese su nueva contraseña dos veces para que podamos verificar que la "
#~ "ha escrito correctamente."

#~ msgid "Old password"
#~ msgstr "Vieja contraseña"

#~ msgid "New password"
#~ msgstr "Nueva contraseña"

#~ msgid "Password (again)"
#~ msgstr "Contraseña (nuevamente)"

#~ msgid "Password reset complete"
#~ msgstr "restablecimiento de contraseña completo"

#~ msgid "Password reset confirmation"
#~ msgstr "Confirmación de restablecimiento de contraseña"

#~ msgid "New password:"
#~ msgstr "Nueva contraseña:"

#~ msgid "Confirm password:"
#~ msgstr "Confirmar contraseña:"

#~ msgid "Search By"
#~ msgstr "Buscar por"

#~ msgid "None"
#~ msgstr "Ninguno"

#~ msgid "Cacnel"
#~ msgstr "Cancelar"
