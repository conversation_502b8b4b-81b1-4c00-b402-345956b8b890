# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-27 21:17+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=1;\n"

msgid "Admin Object"
msgstr ""

msgid "Administration"
msgstr ""

msgid "All"
msgstr ""

msgid "Yes"
msgstr ""

msgid "No"
msgstr ""

msgid "Unknown"
msgstr ""

msgid "Any date"
msgstr ""

msgid "Has date"
msgstr ""

msgid "Has no date"
msgstr ""

msgid "Today"
msgstr ""

msgid "Past 7 days"
msgstr ""

msgid "This month"
msgstr ""

msgid "This year"
msgstr ""

msgid "Please enter the correct username and password for a staff account. Note that both fields are case-sensitive."
msgstr ""

msgid "Please log in again, because your session has expired."
msgstr ""

#, python-format
msgid "Your e-mail address is not your username. Try '%s' instead."
msgstr ""

msgid "Title"
msgstr ""

msgid "user"
msgstr ""

msgid "Url Name"
msgstr ""

msgid "Query String"
msgstr ""

msgid "Is Shared"
msgstr ""

msgid "Bookmark"
msgstr ""

msgid "Bookmarks"
msgstr ""

msgid "Settings Key"
msgstr ""

msgid "Settings Content"
msgstr ""

msgid "User Setting"
msgstr ""

msgid "User Settings"
msgstr ""

msgid "Page"
msgstr ""

msgid "Widget Type"
msgstr ""

msgid "Widget Params"
msgstr ""

msgid "User Widget"
msgstr ""

msgid "User Widgets"
msgstr ""

msgid "action time"
msgstr ""

msgid "action ip"
msgstr ""

msgid "content type"
msgstr ""

msgid "object id"
msgstr ""

msgid "object repr"
msgstr ""

msgid "action flag"
msgstr ""

msgid "change message"
msgstr ""

msgid "log entry"
msgstr ""

msgid "log entries"
msgstr ""

#, python-format
msgid "Added \"%(object)s\"."
msgstr ""

#, python-format
msgid "Changed \"%(object)s\" - %(changes)s"
msgstr ""

#, python-format
msgid "Deleted \"%(object)s.\""
msgstr ""

#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr ""

#, python-format
msgid "Batch delete %(count)d %(items)s."
msgstr ""

#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr ""

#, python-format
msgid "Cannot delete %(name)s"
msgstr ""

msgid "Are you sure?"
msgstr ""

#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "0 of %(cnt)s selected"
msgstr ""

msgid "Items must be selected in order to perform actions on them. No items have been changed."
msgstr ""

msgid "Min"
msgstr ""

msgid "Max"
msgstr ""

msgid "Avg"
msgstr ""

msgid "Sum"
msgstr ""

msgid "Count"
msgstr ""

#, python-format
msgid "Can add %s"
msgstr ""

#, python-format
msgid "Can change %s"
msgstr ""

#, python-format
msgid "Can edit %s"
msgstr ""

#, python-format
msgid "Can delete %s"
msgstr ""

#, python-format
msgid "Can view %s"
msgstr ""

msgid "Personal info"
msgstr ""

msgid "Permissions"
msgstr ""

msgid "Important dates"
msgstr ""

msgid "Status"
msgstr ""

msgid "Permission Name"
msgstr ""

#, fuzzy
msgid "Change Password"
msgstr "Changed Password"

#, python-format
msgid "Change password: %s"
msgstr ""

msgid "Password changed successfully."
msgstr ""

msgid "Change password"
msgstr ""

msgid "Change this field"
msgstr ""

#, python-format
msgid "Batch Change selected %(verbose_name_plural)s"
msgstr ""

#, python-format
msgid "Successfully change %(count)d %(items)s."
msgstr ""

#, python-format
msgid "Batch change %s"
msgstr ""

msgid "bookmark"
msgstr ""

msgid "Bookmark Widget, can show user's bookmark list data in widget."
msgstr ""

msgid "Show models simple chart."
msgstr ""

#, python-format
msgid "%s Charts"
msgstr ""

msgid "Metadata"
msgstr ""

msgid "flagged"
msgid_plural "flagged"
msgstr[0] ""
msgstr[1] ""

msgid "Flag selected comments"
msgstr ""

msgid "approved"
msgid_plural "approved"
msgstr[0] ""
msgstr[1] ""

msgid "Approve selected comments"
msgstr ""

msgid "removed"
msgid_plural "removed"
msgstr[0] ""
msgstr[1] ""

msgid "Remove selected comments"
msgstr ""

#, python-format
msgid "1 comment was successfully %(action)s."
msgid_plural "%(count)s comments were successfully %(action)s."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Details of %s"
msgstr ""

#, python-format
msgid "Enter %s"
msgstr ""

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr ""

msgid "Sheet"
msgstr ""

#, python-format
msgid "<b>Filtering error:</b> %s"
msgstr ""

msgid "Previous"
msgstr ""

msgid "Next"
msgstr ""

msgid "Slideshow"
msgstr ""

msgid "Download"
msgstr ""

msgid "Change:"
msgstr ""

msgid "Import"
msgstr ""

#, python-format
msgid "<h1>Imported file has a wrong encoding: %s</h1>"
msgstr ""

#, python-format
msgid "<h1>%s encountered while trying to read file: %s</h1>"
msgstr ""

msgid "Import finished"
msgstr ""

msgid "Add"
msgstr ""

msgid "Update"
msgstr ""

msgid "You must select an export format."
msgstr ""

msgid "Table"
msgstr ""

msgid "Thumbnails"
msgstr ""

msgid "Forgotten your password or username?"
msgstr ""

#, python-format
msgid "Create New %s"
msgstr ""

msgid "Related Objects"
msgstr ""

#, python-format
msgid "Search %s"
msgstr ""

#, python-format
msgid "Select %s"
msgstr ""

msgid "Default"
msgstr ""

msgid "Default bootstrap theme"
msgstr ""

msgid "Bootstrap2"
msgstr ""

msgid "Bootstrap 2.x theme"
msgstr ""

#, python-format
msgid "Add %s"
msgstr ""

msgid "Initial version."
msgstr ""

msgid "Change version."
msgstr ""

msgid "Revert version."
msgstr ""

msgid "Rercover version."
msgstr ""

#, python-format
msgid "Deleted %(verbose_name)s."
msgstr ""

msgid "Recover"
msgstr ""

msgid "History"
msgstr ""

#, python-format
msgid "Recover deleted %(name)s"
msgstr ""

#, python-format
msgid "Change history: %s"
msgstr ""

msgid "Must select two versions."
msgstr ""

msgid "Please select two different versions."
msgstr ""

#, python-format
msgid "Current: %s"
msgstr ""

#, python-format
msgid "Revert %s"
msgstr ""

#, python-format
msgid "The %(model)s \"%(name)s\" was reverted successfully. You may edit it again below."
msgstr ""

#, python-format
msgid "Recover %s"
msgstr ""

#, python-format
msgid "The %(model)s \"%(name)s\" was recovered successfully. You may edit it again below."
msgstr ""

msgid "Page not found"
msgstr ""

msgid "We're sorry, but the requested page could not be found."
msgstr ""

msgid "Home"
msgstr ""

msgid "Server error"
msgstr ""

msgid "Server error (500)"
msgstr ""

msgid "Server Error <em>(500)</em>"
msgstr ""

msgid "There's been an error. It's been reported to the site administrators via e-mail and should be fixed shortly. Thanks for your patience."
msgstr ""

msgid "Password reset successful"
msgstr ""

msgid "Your password has been set.  You may go ahead and log in now."
msgstr ""

msgid "Log in"
msgstr ""

msgid "Enter new password"
msgstr ""

msgid "Please enter your new password twice so we can verify you typed it in correctly."
msgstr ""

msgid "Change my password"
msgstr ""

msgid "Password reset unsuccessful"
msgstr ""

msgid "The password reset link was invalid, possibly because it has already been used.  Please request a new password reset."
msgstr ""

msgid "We've e-mailed you instructions for setting your password to the e-mail address you submitted. You should be receiving it shortly."
msgstr ""

#, python-format
msgid "You're receiving this e-mail because you requested a password reset for your user account at %(site_name)s."
msgstr ""

msgid "Please go to the following page and choose a new password:"
msgstr ""

msgid "Your username, in case you've forgotten:"
msgstr ""

msgid "Thanks for using our site!"
msgstr ""

#, python-format
msgid "The %(site_name)s team"
msgstr ""

msgid "Password reset"
msgstr ""

msgid "Forgotten your password? Enter your e-mail address below, and we'll e-mail instructions for setting a new one."
msgstr ""

msgid "E-mail address:"
msgstr ""

msgid "Reset my password"
msgstr ""

msgid "First, enter a username and password. Then, you'll be able to edit more user options."
msgstr ""

msgid "Enter a username and password."
msgstr ""

msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] ""
msgstr[1] ""

msgid "Enter your new password."
msgstr ""

#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""

msgid "Welcome,"
msgstr ""

msgid "Log out"
msgstr ""

msgid "You don't have permission to edit anything."
msgstr ""

msgid "Themes"
msgstr ""

msgid "Search"
msgstr ""

msgid "Prev step"
msgstr ""

msgid "Next step"
msgstr ""

msgid "Save"
msgstr ""

msgid "Clean Bookmarks"
msgstr ""

msgid "No Bookmarks"
msgstr ""

msgid "New Bookmark"
msgstr ""

msgid "Save current page as Bookmark"
msgstr ""

msgid "Enter bookmark title"
msgstr ""

msgid "Waiting"
msgstr ""

msgid "Save Bookmark"
msgstr ""

msgid "Filters"
msgstr ""

msgid "Clean Filters"
msgstr ""

msgid "Click here to select the objects across all pages"
msgstr ""

#, python-format
msgid "Select all %(total_count)s %(model_name)s"
msgstr ""

msgid "Clear selection"
msgstr ""

msgid "Charts"
msgstr ""

msgid "Export"
msgstr ""

msgid "Export with table header."
msgstr ""

msgid "Export with format."
msgstr ""

msgid "Export all data."
msgstr ""

msgid "Close"
msgstr ""

msgid "Export current page data."
msgstr ""

msgid "Export selected data."
msgstr ""

msgid "Export header only."
msgstr ""

msgid "Layout"
msgstr ""

msgid "Clean Refresh"
msgstr ""

#, python-format
msgid "Every %(t)s seconds"
msgstr ""

msgid "Save Order"
msgstr ""

msgid "Null"
msgstr ""

msgid "Enter"
msgstr ""

msgid "Choice Date"
msgstr ""

msgid "YY"
msgstr ""

msgid "year"
msgstr ""

msgid "MM"
msgstr ""

msgid "month"
msgstr ""

msgid "DD"
msgstr ""

msgid "day"
msgstr ""

msgid "Apply"
msgstr ""

msgid "Date Range"
msgstr ""

msgid "Select Date"
msgstr ""

msgid "From"
msgstr ""

msgid "To"
msgstr ""

msgid "Select"
msgstr ""

msgid "Clean"
msgstr ""

msgid "Enter Number"
msgstr ""

#, python-format
msgid " By %(filter_title)s "
msgstr ""

msgid "Available"
msgstr ""

msgid "Click to choose all at once."
msgstr ""

msgid "Choose all"
msgstr ""

msgid "Choose"
msgstr ""

msgid "Remove"
msgstr ""

msgid "Chosen"
msgstr ""

msgid "Click to remove all chosen at once."
msgstr ""

msgid "Remove all"
msgstr ""

msgid "Submit"
msgstr ""

msgid "Below is a preview of data to be imported. If you are satisfied with the results, click 'Confirm import'"
msgstr ""

msgid "Confirm import"
msgstr ""

msgid "This importer will import the following fields: "
msgstr ""

msgid "Errors"
msgstr ""

msgid "Line number"
msgstr ""

msgid "Preview"
msgstr ""

msgid "New"
msgstr ""

msgid "Skipped"
msgstr ""

msgid "Delete"
msgstr ""

msgid "Show all"
msgstr ""

msgid "Saving.."
msgstr ""

msgid "Save as new"
msgstr ""

msgid "Save and add another"
msgstr ""

msgid "Save and continue editing"
msgstr ""

#, python-format
msgid "%(name)s"
msgstr ""

msgid "Change multiple objects"
msgstr ""

#, python-format
msgid "Change one %(objects_name)s"
msgid_plural "Batch change %(counter)s %(objects_name)s"
msgstr[0] ""
msgstr[1] ""

msgid "Change Multiple"
msgstr ""

msgid "Add Widget"
msgstr ""

msgid "Something's wrong with your database installation. Make sure the appropriate database tables have been created, and make sure the database is readable by the appropriate user."
msgstr ""

msgid "Logout Success"
msgstr ""

msgid "Thanks for spending some quality time with the Web site today."
msgstr ""

msgid "Close Window"
msgstr ""

msgid "Log in again"
msgstr ""

msgid "Please Login"
msgstr ""

msgid "Username"
msgstr ""

msgid "Password"
msgstr ""

msgid "log in"
msgstr ""

msgid "Edit"
msgstr ""

#, python-format
msgid "Deleting the %(verbose_name)s '%(escaped_object)s' would result in deleting related objects, but your account doesn't have permission to delete the following types of objects:"
msgstr ""

#, python-format
msgid "Deleting the %(verbose_name)s '%(escaped_object)s' would require deleting the following protected related objects:"
msgstr ""

#, python-format
msgid "Are you sure you want to delete the %(verbose_name)s \"%(escaped_object)s\"? All of the following related items will be deleted:"
msgstr ""

msgid "Yes, I'm sure"
msgstr ""

msgid "Cancel"
msgstr ""

msgid "Delete multiple objects"
msgstr ""

#, python-format
msgid "Deleting the selected %(objects_name)s would result in deleting related objects, but your account doesn't have permission to delete the following types of objects:"
msgstr ""

#, python-format
msgid "Deleting the selected %(objects_name)s would require deleting the following protected related objects:"
msgstr ""

#, python-format
msgid "Are you sure you want to delete the selected %(objects_name)s? All of the following objects and their related items will be deleted:"
msgstr ""

msgid "Diff"
msgstr ""

msgid "Date/time"
msgstr ""

msgid "User"
msgstr ""

msgid "Comment"
msgstr ""

msgid "Diff Select Versions"
msgstr ""

msgid "This object doesn't have a change history. It probably wasn't added via this admin site."
msgstr ""

#, python-format
msgid "Add %(name)s"
msgstr ""

msgid "Columns"
msgstr ""

msgid "Restore Selected"
msgstr ""

msgid "Empty list"
msgstr ""

msgid "Press the recover button below to recover this version of the object."
msgstr ""

msgid "Choose a date from the list below to recover a deleted version of an object."
msgstr ""

msgid "There are no deleted objects to recover."
msgstr ""

#, python-format
msgid "Diff %(verbose_name)s"
msgstr ""

msgid "Field"
msgstr ""

msgid "Version A"
msgstr ""

msgid "Version B"
msgstr ""

msgid "Revert to"
msgstr ""

msgid "Revert"
msgstr ""

#, python-format
msgid "Revert %(verbose_name)s"
msgstr ""

msgid "Press the revert button below to revert to this version of the object."
msgstr ""

msgid "Revert this revision"
msgstr ""

msgid "Success"
msgstr ""

msgid "Add success, click <a id='change-link'>edit</a> to edit."
msgstr ""

msgid "Quick Add"
msgstr ""

msgid "Widget Options"
msgstr ""

msgid "Save changes"
msgstr ""

msgid "Django Xadmin"
msgstr ""

msgid "my-company.inc"
msgstr ""

msgid "Widget ID"
msgstr ""

msgid "Widget Title"
msgstr ""

msgid "Html Content Widget, can write any html content in widget."
msgstr ""

msgid "Html Content"
msgstr ""

msgid "Target Model"
msgstr ""

msgid "Quick button Widget, quickly open any page."
msgstr ""

msgid "Quick Buttons"
msgstr ""

msgid "Any Objects list Widget."
msgstr ""

msgid "Add any model object Widget."
msgstr ""

msgid "Dashboard"
msgstr ""

#, python-format
msgid "%s Dashboard"
msgstr ""

#, python-format
msgid "The %(name)s \"%(obj)s\" was deleted successfully."
msgstr ""

msgid "Other Fields"
msgstr ""

#, python-format
msgid "%s Detail"
msgstr ""

msgid "Added."
msgstr ""

#, fuzzy, python-format
msgid "Changed %s."
msgstr "Changed Password"

msgid "and"
msgstr ""

msgid "No fields changed."
msgstr ""

#, python-format
msgid "The %(name)s \"%(obj)s\" was added successfully."
msgstr ""

msgid "You may edit it again below."
msgstr ""

#, python-format
msgid "You may add another %s below."
msgstr ""

#, python-format
msgid "Change %s"
msgstr ""

#, python-format
msgid "The %(name)s \"%(obj)s\" was changed successfully."
msgstr ""

#, python-format
msgid "The %s was changed successfully."
msgstr ""

msgid "Database error"
msgstr ""

#, python-format
msgid "%s List"
msgstr ""

msgid "Sort ASC"
msgstr ""

msgid "Sort DESC"
msgstr ""

msgid "Cancel Sort"
msgstr ""

msgid "Main Dashboard"
msgstr ""

msgid "Now"
msgstr ""
