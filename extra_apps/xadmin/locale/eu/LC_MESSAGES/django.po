# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2013
msgid ""
msgstr ""
"Project-Id-Version: xadmin-core\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-07-20 13:28+0800\n"
"PO-Revision-Date: 2013-11-20 10:21+0000\n"
"Last-Translator: sshwsfc <<EMAIL>>\n"
"Language-Team: Basque (http://www.transifex.com/projects/p/xadmin/language/"
"eu/)\n"
"Language: eu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: adminx.py:19
msgid "Admin Object"
msgstr ""

#: apps.py:11
msgid "Administration"
msgstr ""

#: filters.py:159 filters.py:191 filters.py:407 filters.py:493 filters.py:531
msgid "All"
msgstr "Guztia"

#: filters.py:160 plugins/export.py:165
msgid "Yes"
msgstr "Bai"

#: filters.py:161 plugins/export.py:165
msgid "No"
msgstr "Ez"

#: filters.py:175
msgid "Unknown"
msgstr "Ezezaguna"

#: filters.py:267
msgid "Any date"
msgstr "Edozein data"

#: filters.py:268
msgid "Has date"
msgstr ""

#: filters.py:271
msgid "Has no date"
msgstr ""

#: filters.py:274 widgets.py:30
msgid "Today"
msgstr "Gaur"

#: filters.py:278
msgid "Past 7 days"
msgstr "Duela 7 egun"

#: filters.py:282
msgid "This month"
msgstr "Hilabete hau"

#: filters.py:286
msgid "This year"
msgstr "Urte hau"

#: forms.py:10
msgid ""
"Please enter the correct username and password for a staff account. Note "
"that both fields are case-sensitive."
msgstr ""
"Mesedez sartu langile kontu baten erabiltzaile eta pasahitz egokiak. Kontuan "
"hartu bi eremuek maiuskula eta minuskulak bereizten dituztela."

#: forms.py:21
msgid "Please log in again, because your session has expired."
msgstr "Mesedez hasi berriro saioa, zure uneko saioa iraungi da eta."

#: forms.py:41
#, python-format
msgid "Your e-mail address is not your username. Try '%s' instead."
msgstr ""
"Zure e-posta helbidea ez da zure erabiltzaile izena. Horren ordez saiatu "
"'%s'."

#: models.py:48
msgid "Title"
msgstr "Izenburua"

#: models.py:49 models.py:88 models.py:107 models.py:149
msgid "user"
msgstr ""

#: models.py:50
msgid "Url Name"
msgstr "Url Izena"

#: models.py:52
msgid "Query String"
msgstr "Kontsulta Katea"

#: models.py:53
msgid "Is Shared"
msgstr "Partekatua da."

#: models.py:66 plugins/bookmark.py:50 plugins/bookmark.py:180
msgid "Bookmark"
msgstr "Laster-marketara gehitu"

#: models.py:67
msgid "Bookmarks"
msgstr "Laster-markak"

#: models.py:89
msgid "Settings Key"
msgstr "Ezarpenen Gakoa"

#: models.py:90
msgid "Settings Content"
msgstr "Ezarpenen Edukia"

#: models.py:102
msgid "User Setting"
msgstr "Erabiltzaile Ezarpenak"

#: models.py:103
msgid "User Settings"
msgstr "Erabiltzaile Ezarpenak"

#: models.py:108
msgid "Page"
msgstr "Orrialdea"

#: models.py:109 views/dashboard.py:82 views/dashboard.py:92
msgid "Widget Type"
msgstr "Widget Mota"

#: models.py:110 views/dashboard.py:83
msgid "Widget Params"
msgstr "Widget Parametroak"

#: models.py:137
msgid "User Widget"
msgstr "Erabiltzaile Widgeta"

#: models.py:138
msgid "User Widgets"
msgstr "Erabiltzaile Widgetak"

#: models.py:142
#, fuzzy
#| msgid "Date/time"
msgid "action time"
msgstr "Data/ordua"

#: models.py:151
msgid "action ip"
msgstr ""

#: models.py:155
msgid "content type"
msgstr ""

#: models.py:158
msgid "object id"
msgstr ""

#: models.py:159
msgid "object repr"
msgstr ""

#: models.py:160
msgid "action flag"
msgstr ""

#: models.py:161
#, fuzzy
#| msgid "Change %s"
msgid "change message"
msgstr "Aldaketa %s"

#: models.py:164
#, fuzzy
#| msgid "log in"
msgid "log entry"
msgstr "hasi saioa"

#: models.py:165
msgid "log entries"
msgstr ""

#: models.py:173
#, python-format
msgid "Added \"%(object)s\"."
msgstr ""

#: models.py:175
#, python-format
msgid "Changed \"%(object)s\" - %(changes)s"
msgstr ""

#: models.py:180
#, fuzzy, python-format
#| msgid "Related Objects"
msgid "Deleted \"%(object)s.\""
msgstr "Erlazionatutako Objetuak"

#: plugins/actions.py:57
#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "Aukeratutako %(verbose_name_plural)s ezabatu"

#: plugins/actions.py:72
#, fuzzy, python-format
#| msgid "Successfully deleted %(count)d %(items)s."
msgid "Batch delete %(count)d %(items)s."
msgstr "Aukeratutako %(count)d %(items)s arrakastaz ezabatuak."

#: plugins/actions.py:78
#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr "Aukeratutako %(count)d %(items)s arrakastaz ezabatuak."

#: plugins/actions.py:110 views/delete.py:70
#, python-format
msgid "Cannot delete %(name)s"
msgstr "Ezin izan da %(name)s ezabatu"

#: plugins/actions.py:112 views/delete.py:73
msgid "Are you sure?"
msgstr "Ziur al zaude?"

#: plugins/actions.py:158
#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "%(total_count)s aukeratua"
msgstr[1] "%(total_count)s guztiak aukeratuak"

#: plugins/actions.py:162
#, python-format
msgid "0 of %(cnt)s selected"
msgstr "%(cnt)stik 0 aukeratuak"

#: plugins/actions.py:179 plugins/actions.py:189
msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""
"Elementuren bat aukeratzea beharrezkoa da ekintza bat burutzeko. Ez da "
"elementurik aldatu."

#: plugins/aggregation.py:14
msgid "Min"
msgstr "Min"

#: plugins/aggregation.py:14
msgid "Max"
msgstr "Max"

#: plugins/aggregation.py:14
msgid "Avg"
msgstr "Btz-bst"

#: plugins/aggregation.py:14
msgid "Sum"
msgstr "Batura"

#: plugins/aggregation.py:14
msgid "Count"
msgstr "Kontaketa"

#: plugins/auth.py:21
#, fuzzy, python-format
msgid "Can add %s"
msgstr "%s gehitu"

#: plugins/auth.py:22
#, fuzzy, python-format
msgid "Can change %s"
msgstr "Aldaketa %s"

#: plugins/auth.py:23
#, python-format
msgid "Can edit %s"
msgstr ""

#: plugins/auth.py:24
#, fuzzy, python-format
msgid "Can delete %s"
msgstr "Ezin izan da %(name)s ezabatu"

#: plugins/auth.py:25
#, python-format
msgid "Can view %s"
msgstr ""

#: plugins/auth.py:87
msgid "Personal info"
msgstr "Info pertsonala"

#: plugins/auth.py:91
msgid "Permissions"
msgstr "Baimenak"

#: plugins/auth.py:94
msgid "Important dates"
msgstr "Data garrantzitsuak"

#: plugins/auth.py:99
msgid "Status"
msgstr "Egoera"

#: plugins/auth.py:111
#, fuzzy
msgid "Permission Name"
msgstr "Baimenak"

#: plugins/auth.py:167
msgid "Change Password"
msgstr "Pasahitza Aldatu"

#: plugins/auth.py:198
#, python-format
msgid "Change password: %s"
msgstr "Pasahitza aldatu: %s"

#: plugins/auth.py:223 plugins/auth.py:255
msgid "Password changed successfully."
msgstr "Pasahitza arrakastaz aldatua."

#: plugins/auth.py:242 templates/xadmin/auth/user/change_password.html:11
#: templates/xadmin/auth/user/change_password.html:22
#: templates/xadmin/auth/user/change_password.html:55
msgid "Change password"
msgstr "Pasahitza aldatu"

#: plugins/batch.py:44
msgid "Change this field"
msgstr ""

#: plugins/batch.py:65
#, python-format
msgid "Batch Change selected %(verbose_name_plural)s"
msgstr "Sorta moduko Aldaketa aukeratua %(verbose_name_plural)s"

#: plugins/batch.py:89
#, python-format
msgid "Successfully change %(count)d %(items)s."
msgstr ""

#: plugins/batch.py:138
#, python-format
msgid "Batch change %s"
msgstr ""

#: plugins/bookmark.py:173
msgid "bookmark"
msgstr ""

#: plugins/bookmark.py:176
msgid "Bookmark Widget, can show user's bookmark list data in widget."
msgstr ""
"Laster-marken Widgeta, erabiltzailearen laster-marka zerrenda widgetean "
"erakutsi dezake."

#: plugins/chart.py:25
msgid "Show models simple chart."
msgstr "Erabili modeluen diagrama sinplea."

#: plugins/chart.py:51
#, python-format
msgid "%s Charts"
msgstr "%s Diagrama"

#: plugins/comments.py:33
msgid "Metadata"
msgstr ""

#: plugins/comments.py:60
msgid "flagged"
msgid_plural "flagged"
msgstr[0] ""
msgstr[1] ""

#: plugins/comments.py:61
msgid "Flag selected comments"
msgstr ""

#: plugins/comments.py:66
msgid "approved"
msgid_plural "approved"
msgstr[0] ""
msgstr[1] ""

#: plugins/comments.py:67
msgid "Approve selected comments"
msgstr ""

#: plugins/comments.py:72
#, fuzzy
msgid "removed"
msgid_plural "removed"
msgstr[0] "Ezabatu"
msgstr[1] "Ezabatu"

#: plugins/comments.py:73
#, fuzzy
msgid "Remove selected comments"
msgstr "Ezabatutako %(name)sa berreskuratu"

#: plugins/comments.py:86
#, python-format
msgid "1 comment was successfully %(action)s."
msgid_plural "%(count)s comments were successfully %(action)s."
msgstr[0] ""
msgstr[1] ""

#: plugins/details.py:52 views/list.py:578
#, python-format
msgid "Details of %s"
msgstr "%sren xehetasunak"

#: plugins/editable.py:46
#, python-format
msgid "Enter %s"
msgstr "Sartu %s"

#: plugins/editable.py:73 views/dashboard.py:649 views/delete.py:27
#: views/detail.py:145 views/edit.py:454
#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "Ez da %(key)r eremua nagusitzat duen %(name)srik."

#: plugins/export.py:98 plugins/export.py:135
msgid "Sheet"
msgstr "Xafla"

#: plugins/filters.py:133 plugins/quickfilter.py:141
#, python-format
msgid "<b>Filtering error:</b> %s"
msgstr ""

#: plugins/images.py:29
msgid "Previous"
msgstr "Aurrekoa"

#: plugins/images.py:29
msgid "Next"
msgstr "Hurrengoa"

#: plugins/images.py:29
msgid "Slideshow"
msgstr "Aurkezpena"

#: plugins/images.py:29
msgid "Download"
msgstr "Deskargatu"

#: plugins/images.py:50
msgid "Change:"
msgstr "Aldatu:"

#: plugins/layout.py:16
msgid "Table"
msgstr ""

#: plugins/layout.py:22
msgid "Thumbnails"
msgstr ""

#: plugins/passwords.py:64
msgid "Forgotten your password or username?"
msgstr "Zure pasahitza edo erabiltzailea ahaztu duzu?"

#: plugins/quickform.py:79
#, python-format
msgid "Create New %s"
msgstr ""

#: plugins/relate.py:104
msgid "Related Objects"
msgstr "Erlazionatutako Objetuak"

#: plugins/relfield.py:29 plugins/topnav.py:38
#, python-format
msgid "Search %s"
msgstr "Bilatu %s"

#: plugins/relfield.py:67
#, fuzzy, python-format
#| msgid "Select Date"
msgid "Select %s"
msgstr "Data Aukeratu"

#: plugins/themes.py:47
msgid "Default"
msgstr ""

#: plugins/themes.py:48
msgid "Default bootstrap theme"
msgstr ""

#: plugins/themes.py:49
msgid "Bootstrap2"
msgstr ""

#: plugins/themes.py:49
msgid "Bootstrap 2.x theme"
msgstr ""

#: plugins/topnav.py:62 views/dashboard.py:465 views/edit.py:387
#: views/edit.py:396
#, python-format
msgid "Add %s"
msgstr "%s gehitu"

#: plugins/xversion.py:106
msgid "Initial version."
msgstr "Hasierako bertsioa."

#: plugins/xversion.py:108
msgid "Change version."
msgstr "Bertsioa aldatu."

#: plugins/xversion.py:110
msgid "Revert version."
msgstr "Aurreko bertsiora itzuli."

#: plugins/xversion.py:112
msgid "Rercover version."
msgstr "Bertsioa berreskuratu."

#: plugins/xversion.py:114
#, python-format
msgid "Deleted %(verbose_name)s."
msgstr "%(verbose_name)s ezabatua."

#: plugins/xversion.py:127 templates/xadmin/views/recover_form.html:26
msgid "Recover"
msgstr "Berreskuratu"

#: plugins/xversion.py:143 templates/xadmin/views/model_history.html:11
#: templates/xadmin/views/revision_diff.html:11
#: templates/xadmin/views/revision_form.html:15
msgid "History"
msgstr "Historiala"

#: plugins/xversion.py:194 templates/xadmin/views/recover_form.html:14
#: templates/xadmin/views/recover_list.html:10
#, python-format
msgid "Recover deleted %(name)s"
msgstr "Ezabatutako %(name)sa berreskuratu"

#: plugins/xversion.py:238
#, python-format
msgid "Change history: %s"
msgstr "Historiala aldatu: %s"

#: plugins/xversion.py:288
msgid "Must select two versions."
msgstr "Bi bertsio aukeratu behar dira."

#: plugins/xversion.py:296
msgid "Please select two different versions."
msgstr "Mesedez aukeratu bi bertsio ezberdin."

#: plugins/xversion.py:383 plugins/xversion.py:500
#, python-format
msgid "Current: %s"
msgstr "Unekoa: %s"

#: plugins/xversion.py:424
#, python-format
msgid "Revert %s"
msgstr "%s aurreko bertsiora itzuli"

#: plugins/xversion.py:440
#, python-format
msgid ""
"The %(model)s \"%(name)s\" was reverted successfully. You may edit it again "
"below."
msgstr ""
"\"%(name)s\" %(model)sa aurreko bertsiora arrakastaz itzularazi da. Azpian "
"berriro editatu dezakezu."

#: plugins/xversion.py:461
#, python-format
msgid "Recover %s"
msgstr "%s berreskuratu"

#: plugins/xversion.py:477
#, python-format
msgid ""
"The %(model)s \"%(name)s\" was recovered successfully. You may edit it again "
"below."
msgstr ""
"\"%(name)s\" %(model)sa arrakastaz berreskuratu da. Azpian berriro editatu "
"dezakezu."

#: templates/xadmin/404.html:4 templates/xadmin/404.html:8
msgid "Page not found"
msgstr "Orria ez da aurkitu"

#: templates/xadmin/404.html:10
msgid "We're sorry, but the requested page could not be found."
msgstr "Sentitzen dugu baina eskatutako orria ezin izan da aurkitu."

#: templates/xadmin/500.html:7
#: templates/xadmin/auth/user/change_password.html:10
#: templates/xadmin/auth/user/change_password.html:15
#: templates/xadmin/base_site.html:53
#: templates/xadmin/includes/sitemenu_default.html:7
#: templates/xadmin/views/app_index.html:9
#: templates/xadmin/views/batch_change_form.html:9
#: templates/xadmin/views/invalid_setup.html:7
#: templates/xadmin/views/model_dashboard.html:7
#: templates/xadmin/views/model_delete_selected_confirm.html:8
#: templates/xadmin/views/model_history.html:8
#: templates/xadmin/views/recover_form.html:8
#: templates/xadmin/views/recover_list.html:8
#: templates/xadmin/views/revision_diff.html:8
#: templates/xadmin/views/revision_form.html:8 views/base.py:473
msgid "Home"
msgstr "Hasiera"

#: templates/xadmin/500.html:8
msgid "Server error"
msgstr "Zerbitzariaren errorea"

#: templates/xadmin/500.html:12
msgid "Server error (500)"
msgstr "Zerbitzariaren errorea (500)"

#: templates/xadmin/500.html:15
msgid "Server Error <em>(500)</em>"
msgstr "Zerbitzariaren errorea "

#: templates/xadmin/500.html:16
msgid ""
"There's been an error. It's been reported to the site administrators via e-"
"mail and should be fixed shortly. Thanks for your patience."
msgstr ""
"Errore bat egon da. Eposta bidez bidali zaio webgunearen kudeatzaileari eta "
"laister konpondu beharko litzake. Milesker zure pazientziagatik."

#: templates/xadmin/auth/password_reset/complete.html:11
#: templates/xadmin/auth/password_reset/done.html:11
msgid "Password reset successful"
msgstr "Pasahitzaren berrezartze arrakastatsua"

#: templates/xadmin/auth/password_reset/complete.html:14
msgid "Your password has been set.  You may go ahead and log in now."
msgstr "Zure pasahitza ezarria izan da. Orain saioa hasi dezakezu."

#: templates/xadmin/auth/password_reset/complete.html:15
msgid "Log in"
msgstr "Sarioa hasi"

#: templates/xadmin/auth/password_reset/confirm.html:12
msgid "Enter new password"
msgstr "Pasahitz berria idatzi"

#: templates/xadmin/auth/password_reset/confirm.html:17
msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""
"Mesedez idatzi zure pasahitz berria bi aldiz ongi idatzi duzun egiaztatzeko."

#: templates/xadmin/auth/password_reset/confirm.html:19
msgid "Change my password"
msgstr "Nire pasahitza aldatu"

#: templates/xadmin/auth/password_reset/confirm.html:24
msgid "Password reset unsuccessful"
msgstr "Pasahitzaren berrezartzea gaizki joan da"

#: templates/xadmin/auth/password_reset/confirm.html:27
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""
"Pasahitzaren berrezartze esteka baliogabea zen, ziur aski iadanik erabilia "
"izan delak. Mesedez, eskatu pasahitzaren berrezartze berri bat."

#: templates/xadmin/auth/password_reset/done.html:14
msgid ""
"We've e-mailed you instructions for setting your password to the e-mail "
"address you submitted. You should be receiving it shortly."
msgstr ""
"Eposta biez zure pasahitza ezartzeko argibideak bidali dizkizugu eman "
"diguzun eposta helbidera. Laister jaso beharko zenituzke."

#: templates/xadmin/auth/password_reset/email.html:2
#, python-format
msgid ""
"You're receiving this e-mail because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""
"%(site_name)sen zure erabiltzailearen pasahitzaren berrezarpen bat eskatu "
"duzulako ari zera eposta hau jasotzen."

#: templates/xadmin/auth/password_reset/email.html:4
msgid "Please go to the following page and choose a new password:"
msgstr "Mesedez, joan ondorengo orrira eta pasahitz berri bat aukeratu:"

#: templates/xadmin/auth/password_reset/email.html:8
msgid "Your username, in case you've forgotten:"
msgstr "Zure erabiltzailea, ahaztu baduzu:"

#: templates/xadmin/auth/password_reset/email.html:10
msgid "Thanks for using our site!"
msgstr "Eskerrik asko gunea erabiltzaileagatik!"

#: templates/xadmin/auth/password_reset/email.html:12
#, python-format
msgid "The %(site_name)s team"
msgstr "%(site_name)sko taldea"

#: templates/xadmin/auth/password_reset/form.html:13
msgid "Password reset"
msgstr "Pasahitza berrezarri"

#: templates/xadmin/auth/password_reset/form.html:17
msgid ""
"Forgotten your password? Enter your e-mail address below, and we'll e-mail "
"instructions for setting a new one."
msgstr ""
"Pasahitza ahaztu duzu? Idatzi zure eposta helbidea behean eta berri bat "
"ezartzeko argibideak bidaliko dizkizugu."

#: templates/xadmin/auth/password_reset/form.html:25
msgid "E-mail address:"
msgstr "Eposta helbidea:"

#: templates/xadmin/auth/password_reset/form.html:33
msgid "Reset my password"
msgstr "Nire pasahitza berrezarri"

#: templates/xadmin/auth/user/add_form.html:6
msgid ""
"First, enter a username and password. Then, you'll be able to edit more user "
"options."
msgstr ""
"Lehendabizi, erabiltzaile eta pasahitz bat sartu. Ondoren, erabiltzaile "
"aukera gehiago editatzeko gai izango zara."

#: templates/xadmin/auth/user/add_form.html:8
msgid "Enter a username and password."
msgstr "Erabiltzaile eta pasahitz bat sartu."

#: templates/xadmin/auth/user/change_password.html:31
#: templates/xadmin/views/batch_change_form.html:24
#: templates/xadmin/views/form.html:18
#: templates/xadmin/views/model_form.html:20
msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "Beheko errorea zuzendu mesedez."
msgstr[1] "Beheko erroreak zuzendu mesedez."

#: templates/xadmin/auth/user/change_password.html:38
msgid "Enter your new password."
msgstr "Zure pasahitz berria sartu."

#: templates/xadmin/auth/user/change_password.html:40
#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""
"<strong>%(username)s</strong> erabiltzailearentzat pasahitz berri bat sartu."

#: templates/xadmin/base_site.html:18
msgid "Welcome,"
msgstr "Ongietorri,"

#: templates/xadmin/base_site.html:24
msgid "Log out"
msgstr "Saioa amaitu"

#: templates/xadmin/base_site.html:36
msgid "You don't have permission to edit anything."
msgstr "Ez daukazu ezer editatzeko baimenik."

#: templates/xadmin/blocks/comm.top.theme.html:4
msgid "Themes"
msgstr "Itsura-gaiak"

#: templates/xadmin/blocks/comm.top.topnav.html:9
#: templates/xadmin/blocks/model_list.nav_form.search_form.html:8
#: templates/xadmin/filters/char.html:7
#: templates/xadmin/filters/fk_search.html:7
#: templates/xadmin/filters/fk_search.html:16
#: templates/xadmin/filters/number.html:7
msgid "Search"
msgstr "Bilatu"

#: templates/xadmin/blocks/comm.top.topnav.html:23
msgid "Add"
msgstr "Gehitu"

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:9
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:26
msgid "Prev step"
msgstr "Aurr pausua"

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:13
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:29
msgid "Next step"
msgstr "Ondo pausua"

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:15
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:31
#: templates/xadmin/includes/submit_line.html:10
#: templates/xadmin/includes/submit_line.html:13
#: templates/xadmin/views/form.html:30 templates/xadmin/views/form.html:31
msgid "Save"
msgstr "Gorde"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:7
msgid "Clean Bookmarks"
msgstr "Laster-markak Garbitu"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:18
msgid "No Bookmarks"
msgstr "Ez dago Laster-markarik"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:22
msgid "New Bookmark"
msgstr "Laster-marka Berria"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:26
msgid "Save current page as Bookmark"
msgstr "Uneko orria Laster-marka bezala gorde"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:32
msgid "Enter bookmark title"
msgstr "Laster-markaren izenburua idatzi"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:33
msgid "Waiting"
msgstr "Itxoiten"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:33
msgid "Save Bookmark"
msgstr "Laster-marka gorde"

#: templates/xadmin/blocks/model_list.nav_menu.filters.html:4
msgid "Filters"
msgstr "Filtroak"

#: templates/xadmin/blocks/model_list.nav_menu.filters.html:8
msgid "Clean Filters"
msgstr "Filtroak garbitu"

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:19
msgid "Click here to select the objects across all pages"
msgstr "Klikatu hemen orrialde guztiko objetuak aukeratzeko"

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:19
#, python-format
msgid "Select all %(total_count)s %(model_name)s"
msgstr "Aukeratu %(total_count)s %(model_name)sak"

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:20
msgid "Clear selection"
msgstr "Aukeraketa garbitu"

#: templates/xadmin/blocks/model_list.results_top.charts.html:4
msgid "Charts"
msgstr "Diagramak"

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:4
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:8
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:19
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:47
msgid "Export"
msgstr "Esportatu"

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:26
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:29
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:32
msgid "Export with table header."
msgstr "Esportatu taularen goiburuarekin."

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:35
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:38
msgid "Export with format."
msgstr "Formatuarekin esportatu."

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:42
msgid "Export all data."
msgstr "Data guztiak esportatu."

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:46
#: templates/xadmin/widgets/base.html:41
msgid "Close"
msgstr "Itxi"

#: templates/xadmin/blocks/model_list.top_toolbar.layouts.html:4
msgid "Layout"
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.refresh.html:8
msgid "Clean Refresh"
msgstr "Garbitu Freskatzea"

#: templates/xadmin/blocks/model_list.top_toolbar.refresh.html:14
#, python-format
msgid "Every %(t)s seconds"
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.saveorder.html:4
msgid "Save Order"
msgstr ""

#: templates/xadmin/edit_inline/blank.html:5 views/detail.py:23
#: views/edit.py:102 views/list.py:29
msgid "Null"
msgstr "Null"

#: templates/xadmin/filters/char.html:13
msgid "Enter"
msgstr "Sartu"

#: templates/xadmin/filters/date.html:10 templates/xadmin/filters/date.html:13
msgid "Choice Date"
msgstr "Aukeraketa Data"

#: templates/xadmin/filters/date.html:18
msgid "YY"
msgstr ""

#: templates/xadmin/filters/date.html:19
msgid "year"
msgstr "urtea"

#: templates/xadmin/filters/date.html:22
msgid "MM"
msgstr ""

#: templates/xadmin/filters/date.html:23
msgid "month"
msgstr "hilabetea"

#: templates/xadmin/filters/date.html:26
msgid "DD"
msgstr ""

#: templates/xadmin/filters/date.html:27
msgid "day"
msgstr "eguna"

#: templates/xadmin/filters/date.html:29 templates/xadmin/filters/date.html:46
#: templates/xadmin/filters/date.html:54
#: templates/xadmin/filters/fk_search.html:24
#: templates/xadmin/filters/number.html:37
msgid "Apply"
msgstr "Aplikatu"

#: templates/xadmin/filters/date.html:34
msgid "Date Range"
msgstr "Data Tartea"

#: templates/xadmin/filters/date.html:41
msgid "Select Date"
msgstr "Data Aukeratu"

#: templates/xadmin/filters/date.html:42
msgid "From"
msgstr "Nork"

#: templates/xadmin/filters/date.html:44
msgid "To"
msgstr "Nori"

#: templates/xadmin/filters/fk_search.html:14
#, fuzzy
#| msgid "Select Date"
msgid "Select"
msgstr "Data Aukeratu"

#: templates/xadmin/filters/fk_search.html:26
#: templates/xadmin/filters/number.html:39
msgid "Clean"
msgstr "Garbitu"

#: templates/xadmin/filters/number.html:17
#: templates/xadmin/filters/number.html:25
#: templates/xadmin/filters/number.html:33
msgid "Enter Number"
msgstr "Zenbakia Sartu"

#: templates/xadmin/filters/rel.html:3
#, python-format
msgid " By %(filter_title)s "
msgstr "%(filter_title)sgatik"

#: templates/xadmin/forms/transfer.html:4
msgid "Available"
msgstr "Eskuragarri"

#: templates/xadmin/forms/transfer.html:12
msgid "Click to choose all at once."
msgstr "Klikatu guztia aukeratzeko."

#: templates/xadmin/forms/transfer.html:12
msgid "Choose all"
msgstr "Guztia aukeratu"

#: templates/xadmin/forms/transfer.html:16
msgid "Choose"
msgstr "Aukeratu"

#: templates/xadmin/forms/transfer.html:19
#: templates/xadmin/widgets/base.html:40
msgid "Remove"
msgstr "Ezabatu"

#: templates/xadmin/forms/transfer.html:23
msgid "Chosen"
msgstr "Aukeratua"

#: templates/xadmin/forms/transfer.html:27
msgid "Click to remove all chosen at once."
msgstr "Klikatu aukeraketa guztia ezabatzeko"

#: templates/xadmin/forms/transfer.html:27
msgid "Remove all"
msgstr "Guztia ezabatu"

#: templates/xadmin/includes/pagination.html:9
msgid "Show all"
msgstr "Guztia erakutsi"

#: templates/xadmin/includes/submit_line.html:10
#: templates/xadmin/includes/submit_line.html:13
#: templates/xadmin/views/form.html:30 templates/xadmin/views/form.html:31
msgid "Saving.."
msgstr ""

#: templates/xadmin/includes/submit_line.html:17
msgid "Save as new"
msgstr "Berri bezala gorde"

#: templates/xadmin/includes/submit_line.html:18
msgid "Save and add another"
msgstr "Gorde eta beste bat gehitu"

#: templates/xadmin/includes/submit_line.html:19
msgid "Save and continue editing"
msgstr "Gorde eta editatzen jarraitu"

#: templates/xadmin/includes/submit_line.html:24
#: templates/xadmin/views/model_detail.html:28 views/delete.py:93
msgid "Delete"
msgstr "Ezabatu"

#: templates/xadmin/views/app_index.html:13
#, python-format
msgid "%(name)s"
msgstr "%(name)s"

#: templates/xadmin/views/batch_change_form.html:11
msgid "Change multiple objects"
msgstr "Objetu anitz aldatu"

#: templates/xadmin/views/batch_change_form.html:16
#, python-format
msgid "Change one %(objects_name)s"
msgid_plural "Batch change %(counter)s %(objects_name)s"
msgstr[0] ""
msgstr[1] ""

#: templates/xadmin/views/batch_change_form.html:38
msgid "Change Multiple"
msgstr "Bizpahiru Aldatu"

#: templates/xadmin/views/dashboard.html:15
#: templates/xadmin/views/dashboard.html:22
#: templates/xadmin/views/dashboard.html:23
msgid "Add Widget"
msgstr "Widgeta Gehitu"

#: templates/xadmin/views/invalid_setup.html:13
msgid ""
"Something's wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""
"Zerbait txarto dago zure datubasearen instalazioarekin. Ziurtatu beharrezko "
"datubase taulak sortuak izan direla eta datubasea erabiltzaile egokiak "
"irakurri dezakela."

#: templates/xadmin/views/logged_out.html:16
msgid "Logout Success"
msgstr "Saioa Arrakastaz Amaitua"

#: templates/xadmin/views/logged_out.html:17
msgid "Thanks for spending some quality time with the Web site today."
msgstr "Eskerrikasko Web gunean zure denbora sartzeagatik."

#: templates/xadmin/views/logged_out.html:19
msgid "Close Window"
msgstr "Lehioa Itxi"

#: templates/xadmin/views/logged_out.html:20
msgid "Log in again"
msgstr "Saioa hasi berriro"

#: templates/xadmin/views/login.html:39 views/website.py:38
msgid "Please Login"
msgstr "Hasi Saioa mesedez"

#: templates/xadmin/views/login.html:52
msgid "Username"
msgstr ""

#: templates/xadmin/views/login.html:64
msgid "Password"
msgstr ""

#: templates/xadmin/views/login.html:75
msgid "log in"
msgstr "hasi saioa"

#: templates/xadmin/views/model_dashboard.html:26
#: templates/xadmin/views/model_detail.html:25
msgid "Edit"
msgstr "Editatu"

#: templates/xadmin/views/model_delete_confirm.html:11
#, python-format
msgid ""
"Deleting the %(verbose_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""
"'%(escaped_object)s' %(verbose_name)sa ezabatzeak erlazionatutako beste "
"objetu batzuk ezabatuzko lituzke baina zure kontuak ez dauzka ondorengo "
"objetu motak ezabatzeko baimenik:"

#: templates/xadmin/views/model_delete_confirm.html:19
#, python-format
msgid ""
"Deleting the %(verbose_name)s '%(escaped_object)s' would require deleting "
"the following protected related objects:"
msgstr ""
"'%(escaped_object)s' %(verbose_name)sa ezabatzeak ondorengo babestutako "
"erlazionatutako objetuak ezabatuko lituzke:"

#: templates/xadmin/views/model_delete_confirm.html:27
#, python-format
msgid ""
"Are you sure you want to delete the %(verbose_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""
"Ziur al zaude \"%(escaped_object)s\" %(verbose_name)sa ezabatu nahi duzula? "
"Ondorengo erlazionatutako objetuak ezabatuko lirateke:"

#: templates/xadmin/views/model_delete_confirm.html:34
#: templates/xadmin/views/model_delete_selected_confirm.html:49
msgid "Yes, I'm sure"
msgstr "Bai, ziur nago"

#: templates/xadmin/views/model_delete_confirm.html:35
#: templates/xadmin/views/model_delete_selected_confirm.html:50
msgid "Cancel"
msgstr ""

#: templates/xadmin/views/model_delete_selected_confirm.html:10
msgid "Delete multiple objects"
msgstr "Bizpahiru objetu ezabatu"

#: templates/xadmin/views/model_delete_selected_confirm.html:18
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""
"Aukeratutako %(objects_name)s objetua ezabatzeak erlazionatutako objetuak "
"ezabatuko lituzke, baina zure kontuak ez dauzka ondorengo objetu motak "
"ezabatzeko baimenik:"

#: templates/xadmin/views/model_delete_selected_confirm.html:26
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""
"Aukeratutako %(objects_name)s objetua ezabatzeak ondorengo objetuak "
"ezabatuko lituzke:"

#: templates/xadmin/views/model_delete_selected_confirm.html:34
#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""
"Ziur zaude aukeratutako %(objects_name)s ezabatu nahi duzula? Ondorengo "
"objetuak eta haiekin erlazionatuta daudenak ezabatuak izango dira:"

#: templates/xadmin/views/model_history.html:26
msgid "Diff"
msgstr "Diferentzia"

#: templates/xadmin/views/model_history.html:27
#: templates/xadmin/views/recover_list.html:25
msgid "Date/time"
msgstr "Data/ordua"

#: templates/xadmin/views/model_history.html:28
msgid "User"
msgstr "Erabiltzailea"

#: templates/xadmin/views/model_history.html:29
msgid "Comment"
msgstr "Iruzkina"

#: templates/xadmin/views/model_history.html:54
msgid "Diff Select Versions"
msgstr "Aukeratutako Bertsioen Ezberdintasunak"

#: templates/xadmin/views/model_history.html:58
msgid ""
"This object doesn't have a change history. It probably wasn't added via this "
"admin site."
msgstr ""
"Objetu honek ez dauka aldaketa historialik. Ziurrenik ez zen kudeaketa gune "
"honen bitartez gehitua izango."

#: templates/xadmin/views/model_list.html:29
#, python-format
msgid "Add %(name)s"
msgstr "%(name)s gehitu"

#: templates/xadmin/views/model_list.html:39
msgid "Columns"
msgstr ""

#: templates/xadmin/views/model_list.html:42
msgid "Restore Selected"
msgstr "Aukeratutakoa berrezarri"

#: templates/xadmin/views/model_list.html:147
#: templates/xadmin/widgets/list.html:33
msgid "Empty list"
msgstr "Zerrenda hustu"

#: templates/xadmin/views/recover_form.html:20
msgid "Press the recover button below to recover this version of the object."
msgstr ""
"Sakatu beheko berreskuratze botoia objetuaren bertsio hau berreskuratzeko."

#: templates/xadmin/views/recover_list.html:19
msgid ""
"Choose a date from the list below to recover a deleted version of an object."
msgstr ""
"Aukeratu beheko zerrendatik data bat objetu baten ezabatutako bertsioa "
"berreskuratzeko."

#: templates/xadmin/views/recover_list.html:39
msgid "There are no deleted objects to recover."
msgstr "Ez dago berreskuratu daitekeen ezabatutako objeturik."

#: templates/xadmin/views/revision_diff.html:12
#: templates/xadmin/views/revision_diff.html:17
#, python-format
msgid "Diff %(verbose_name)s"
msgstr "%(verbose_name)s ezberdintasunak"

#: templates/xadmin/views/revision_diff.html:25
msgid "Field"
msgstr "Eremua"

#: templates/xadmin/views/revision_diff.html:26
msgid "Version A"
msgstr "A  bertsioa"

#: templates/xadmin/views/revision_diff.html:27
msgid "Version B"
msgstr "B bertsioa"

#: templates/xadmin/views/revision_diff.html:39
msgid "Revert to"
msgstr "Hona itzularazi"

#: templates/xadmin/views/revision_diff.html:40
#: templates/xadmin/views/revision_diff.html:41
msgid "Revert"
msgstr "Itzularazi"

#: templates/xadmin/views/revision_form.html:16
#, python-format
msgid "Revert %(verbose_name)s"
msgstr "%(verbose_name)s itzularazi"

#: templates/xadmin/views/revision_form.html:21
msgid "Press the revert button below to revert to this version of the object."
msgstr "Beheko itzularatze botoia sakatu objetua bertsio hontara itzultzeko."

#: templates/xadmin/views/revision_form.html:27
msgid "Revert this revision"
msgstr "Bertsio hau itzularazi"

#: templates/xadmin/widgets/addform.html:14
msgid "Success"
msgstr "Arrakasta"

#: templates/xadmin/widgets/addform.html:14
msgid "Add success, click <a id='change-link'>edit</a> to edit."
msgstr ""
"Gehitze arrakastatsua, klikatu <a id='change-link'>editatu</a> editatzeko."

#: templates/xadmin/widgets/addform.html:17
msgid "Quick Add"
msgstr "Gehitze Azkarra"

#: templates/xadmin/widgets/base.html:31
msgid "Widget Options"
msgstr "Widget Aukerak"

#: templates/xadmin/widgets/base.html:42
msgid "Save changes"
msgstr "Aldaketak gorde"

#: views/base.py:315
msgid "Django Xadmin"
msgstr "Django Xadmin"

#: views/base.py:316
msgid "my-company.inc"
msgstr ""

#: views/dashboard.py:186
msgid "Widget ID"
msgstr "Widget IDa"

#: views/dashboard.py:187
msgid "Widget Title"
msgstr "Widget Izenburua"

#: views/dashboard.py:252
msgid "Html Content Widget, can write any html content in widget."
msgstr "Html Eduki Widgeta, edozein html eduki idatzi daiteke widgetan."

#: views/dashboard.py:255
msgid "Html Content"
msgstr "Html Edukia"

#: views/dashboard.py:318
msgid "Target Model"
msgstr "Helburu den Modelua"

#: views/dashboard.py:369
msgid "Quick button Widget, quickly open any page."
msgstr "Botoi azkarra Widgeta, edozein orri azkar ireki."

#: views/dashboard.py:371
msgid "Quick Buttons"
msgstr "Botoi Azkarrak"

#: views/dashboard.py:416
msgid "Any Objects list Widget."
msgstr "Edozein Objetu zerrenda Widgeta."

#: views/dashboard.py:456
msgid "Add any model object Widget."
msgstr "Edozein objetu gehitu Widgeta."

#: views/dashboard.py:492
msgid "Dashboard"
msgstr "Arbela"

#: views/dashboard.py:633
#, python-format
msgid "%s Dashboard"
msgstr ""

#: views/delete.py:103
#, python-format
msgid "The %(name)s \"%(obj)s\" was deleted successfully."
msgstr "\"%(obj)s\" %(name)sa arrakastaz ezabatua izan da."

#: views/detail.py:173 views/edit.py:211 views/form.py:72
msgid "Other Fields"
msgstr "Beste Eremuak"

#: views/detail.py:235
#, python-format
msgid "%s Detail"
msgstr "%s Xehetasunak"

#: views/edit.py:253
msgid "Added."
msgstr ""

#: views/edit.py:255
#, fuzzy, python-format
#| msgid "Change %s"
msgid "Changed %s."
msgstr "Aldaketa %s"

#: views/edit.py:255
msgid "and"
msgstr ""

#: views/edit.py:258
msgid "No fields changed."
msgstr ""

#: views/edit.py:420
#, python-format
msgid "The %(name)s \"%(obj)s\" was added successfully."
msgstr "\"%(obj)s\" %(name)sa arrakastaz gehitua izan da."

#: views/edit.py:425 views/edit.py:520
msgid "You may edit it again below."
msgstr "Behean editatu dezakezu berriro."

#: views/edit.py:429 views/edit.py:523
#, python-format
msgid "You may add another %s below."
msgstr "Behean beste %s bat gehitu dezakezu."

#: views/edit.py:471
#, python-format
msgid "Change %s"
msgstr "Aldaketa %s"

#: views/edit.py:516
#, python-format
msgid "The %(name)s \"%(obj)s\" was changed successfully."
msgstr "\"%(obj)s\" %(name)sa arrakastaz gehitua izan da."

#: views/form.py:165
#, fuzzy, python-format
msgid "The %s was changed successfully."
msgstr "\"%(obj)s\" %(name)sa arrakastaz gehitua izan da."

#: views/list.py:199
msgid "Database error"
msgstr "Datubase errorea"

#: views/list.py:373
#, python-format
msgid "%s List"
msgstr "%s Zerrenda"

#: views/list.py:499
msgid "Sort ASC"
msgstr "GOR Ordenatu"

#: views/list.py:500
msgid "Sort DESC"
msgstr "BEH Ordenatu"

#: views/list.py:504
msgid "Cancel Sort"
msgstr "Ezeztatu Ordenaketa"

#: views/website.py:16
msgid "Main Dashboard"
msgstr "Arbela Nagusia"

#: widgets.py:48
msgid "Now"
msgstr "Orain"
