# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2013
msgid ""
msgstr ""
"Project-Id-Version: xadmin-core\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-07-20 13:28+0800\n"
"PO-Revision-Date: 2013-12-28 19:36+0000\n"
"Last-Translator: crazyzubr <<EMAIL>>\n"
"Language-Team: Russian (Russia) (http://www.transifex.com/projects/p/xadmin/"
"language/ru_RU/)\n"
"Language: ru_RU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: adminx.py:19
msgid "Admin Object"
msgstr ""

#: apps.py:11
msgid "Administration"
msgstr ""

#: filters.py:159 filters.py:191 filters.py:407 filters.py:493 filters.py:531
msgid "All"
msgstr "Все"

#: filters.py:160 plugins/export.py:165
msgid "Yes"
msgstr "Да"

#: filters.py:161 plugins/export.py:165
msgid "No"
msgstr "Нет"

#: filters.py:175
msgid "Unknown"
msgstr "Неизвестно"

#: filters.py:267
msgid "Any date"
msgstr "Любая дата"

#: filters.py:268
msgid "Has date"
msgstr ""

#: filters.py:271
msgid "Has no date"
msgstr ""

#: filters.py:274 widgets.py:30
msgid "Today"
msgstr "Сегодня"

#: filters.py:278
msgid "Past 7 days"
msgstr "За последние 7 дней"

#: filters.py:282
msgid "This month"
msgstr "В этом месяце"

#: filters.py:286
msgid "This year"
msgstr "В этом году"

#: forms.py:10
msgid ""
"Please enter the correct username and password for a staff account. Note "
"that both fields are case-sensitive."
msgstr ""
"Пожалуйста, введите корректные имя пользователя и пароль для аккаунта. Оба "
"поля могут быть чувствительны к регистру."

#: forms.py:21
msgid "Please log in again, because your session has expired."
msgstr "Пожалуйста, войдите снова, поскольку ваша сессия устарела."

#: forms.py:41
#, python-format
msgid "Your e-mail address is not your username. Try '%s' instead."
msgstr ""

#: models.py:48
msgid "Title"
msgstr "Заголовок"

#: models.py:49 models.py:88 models.py:107 models.py:149
msgid "user"
msgstr ""

#: models.py:50
msgid "Url Name"
msgstr ""

#: models.py:52
msgid "Query String"
msgstr "Строка запроса"

#: models.py:53
msgid "Is Shared"
msgstr ""

#: models.py:66 plugins/bookmark.py:50 plugins/bookmark.py:180
msgid "Bookmark"
msgstr "Закладка"

#: models.py:67
msgid "Bookmarks"
msgstr "Закладки"

#: models.py:89
msgid "Settings Key"
msgstr ""

#: models.py:90
msgid "Settings Content"
msgstr ""

#: models.py:102
msgid "User Setting"
msgstr ""

#: models.py:103
msgid "User Settings"
msgstr ""

#: models.py:108
msgid "Page"
msgstr "Страница"

#: models.py:109 views/dashboard.py:82 views/dashboard.py:92
msgid "Widget Type"
msgstr "Тип виджета"

#: models.py:110 views/dashboard.py:83
msgid "Widget Params"
msgstr "Параметры виджета"

#: models.py:137
msgid "User Widget"
msgstr ""

#: models.py:138
msgid "User Widgets"
msgstr ""

#: models.py:142
msgid "action time"
msgstr ""

#: models.py:151
msgid "action ip"
msgstr ""

#: models.py:155
msgid "content type"
msgstr ""

#: models.py:158
msgid "object id"
msgstr ""

#: models.py:159
msgid "object repr"
msgstr ""

#: models.py:160
msgid "action flag"
msgstr ""

#: models.py:161
#, fuzzy
#| msgid "Change %s"
msgid "change message"
msgstr "Изменить %s"

#: models.py:164
msgid "log entry"
msgstr ""

#: models.py:165
msgid "log entries"
msgstr ""

#: models.py:173
#, python-format
msgid "Added \"%(object)s\"."
msgstr ""

#: models.py:175
#, python-format
msgid "Changed \"%(object)s\" - %(changes)s"
msgstr ""

#: models.py:180
#, fuzzy, python-format
#| msgid "Related Objects"
msgid "Deleted \"%(object)s.\""
msgstr "Связанные объекты"

#: plugins/actions.py:57
#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "Удалить выбранные %(verbose_name_plural)s"

#: plugins/actions.py:72
#, fuzzy, python-format
#| msgid "Successfully deleted %(count)d %(items)s."
msgid "Batch delete %(count)d %(items)s."
msgstr "Успешно удалены %(count)d %(items)s."

#: plugins/actions.py:78
#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr "Успешно удалены %(count)d %(items)s."

#: plugins/actions.py:110 views/delete.py:70
#, python-format
msgid "Cannot delete %(name)s"
msgstr "Не удается удалить %(name)s"

#: plugins/actions.py:112 views/delete.py:73
msgid "Are you sure?"
msgstr "Вы уверены?"

#: plugins/actions.py:158
#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "Выбран %(total_count)s"
msgstr[1] "Выбраны все %(total_count)s"
msgstr[2] "Выбраны все %(total_count)s"

#: plugins/actions.py:162
#, python-format
msgid "0 of %(cnt)s selected"
msgstr "Выбрано 0 объектов из %(cnt)s "

#: plugins/actions.py:179 plugins/actions.py:189
msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""
"Чтобы произвести действия над объектами, необходимо их выбрать. Объекты не "
"были изменены."

#: plugins/aggregation.py:14
msgid "Min"
msgstr ""

#: plugins/aggregation.py:14
msgid "Max"
msgstr ""

#: plugins/aggregation.py:14
msgid "Avg"
msgstr ""

#: plugins/aggregation.py:14
msgid "Sum"
msgstr ""

#: plugins/aggregation.py:14
msgid "Count"
msgstr ""

#: plugins/auth.py:21
#, fuzzy, python-format
msgid "Can add %s"
msgstr "Добавить %s"

#: plugins/auth.py:22
#, fuzzy, python-format
msgid "Can change %s"
msgstr "Изменить %s"

#: plugins/auth.py:23
#, python-format
msgid "Can edit %s"
msgstr ""

#: plugins/auth.py:24
#, fuzzy, python-format
msgid "Can delete %s"
msgstr "Не удается удалить %(name)s"

#: plugins/auth.py:25
#, python-format
msgid "Can view %s"
msgstr ""

#: plugins/auth.py:87
msgid "Personal info"
msgstr "Персональная информация"

#: plugins/auth.py:91
msgid "Permissions"
msgstr "Права"

#: plugins/auth.py:94
msgid "Important dates"
msgstr "Важные даты"

#: plugins/auth.py:99
msgid "Status"
msgstr "Статус"

#: plugins/auth.py:111
#, fuzzy
msgid "Permission Name"
msgstr "Права"

#: plugins/auth.py:167
msgid "Change Password"
msgstr "Изменить пароль"

#: plugins/auth.py:198
#, python-format
msgid "Change password: %s"
msgstr "Изменить пароль: %s"

#: plugins/auth.py:223 plugins/auth.py:255
msgid "Password changed successfully."
msgstr "Пароль успешно изменен"

#: plugins/auth.py:242 templates/xadmin/auth/user/change_password.html:11
#: templates/xadmin/auth/user/change_password.html:22
#: templates/xadmin/auth/user/change_password.html:55
msgid "Change password"
msgstr "Изменить пароль"

#: plugins/batch.py:44
msgid "Change this field"
msgstr ""

#: plugins/batch.py:65
#, python-format
msgid "Batch Change selected %(verbose_name_plural)s"
msgstr ""

#: plugins/batch.py:89
#, python-format
msgid "Successfully change %(count)d %(items)s."
msgstr ""

#: plugins/batch.py:138
#, python-format
msgid "Batch change %s"
msgstr ""

#: plugins/bookmark.py:173
msgid "bookmark"
msgstr ""

#: plugins/bookmark.py:176
msgid "Bookmark Widget, can show user's bookmark list data in widget."
msgstr ""

#: plugins/chart.py:25
msgid "Show models simple chart."
msgstr ""

#: plugins/chart.py:51
#, python-format
msgid "%s Charts"
msgstr ""

#: plugins/comments.py:33
msgid "Metadata"
msgstr ""

#: plugins/comments.py:60
msgid "flagged"
msgid_plural "flagged"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: plugins/comments.py:61
msgid "Flag selected comments"
msgstr ""

#: plugins/comments.py:66
msgid "approved"
msgid_plural "approved"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: plugins/comments.py:67
msgid "Approve selected comments"
msgstr ""

#: plugins/comments.py:72
msgid "removed"
msgid_plural "removed"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: plugins/comments.py:73
msgid "Remove selected comments"
msgstr ""

#: plugins/comments.py:86
#, python-format
msgid "1 comment was successfully %(action)s."
msgid_plural "%(count)s comments were successfully %(action)s."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: plugins/details.py:52 views/list.py:578
#, python-format
msgid "Details of %s"
msgstr "Детали %s"

#: plugins/editable.py:46
#, python-format
msgid "Enter %s"
msgstr ""

#: plugins/editable.py:73 views/dashboard.py:649 views/delete.py:27
#: views/detail.py:145 views/edit.py:454
#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr ""

#: plugins/export.py:98 plugins/export.py:135
msgid "Sheet"
msgstr "Лист"

#: plugins/filters.py:133 plugins/quickfilter.py:141
#, python-format
msgid "<b>Filtering error:</b> %s"
msgstr ""

#: plugins/images.py:29
msgid "Previous"
msgstr ""

#: plugins/images.py:29
msgid "Next"
msgstr ""

#: plugins/images.py:29
msgid "Slideshow"
msgstr "Слайдшоу"

#: plugins/images.py:29
msgid "Download"
msgstr "Загрузить"

#: plugins/images.py:50
msgid "Change:"
msgstr "Изменить:"

#: plugins/layout.py:16
msgid "Table"
msgstr ""

#: plugins/layout.py:22
msgid "Thumbnails"
msgstr ""

#: plugins/passwords.py:64
msgid "Forgotten your password or username?"
msgstr ""

#: plugins/quickform.py:79
#, python-format
msgid "Create New %s"
msgstr ""

#: plugins/relate.py:104
msgid "Related Objects"
msgstr "Связанные объекты"

#: plugins/relfield.py:29 plugins/topnav.py:38
#, python-format
msgid "Search %s"
msgstr "Поиск %s"

#: plugins/relfield.py:67
#, python-format
msgid "Select %s"
msgstr ""

#: plugins/themes.py:47
msgid "Default"
msgstr ""

#: plugins/themes.py:48
msgid "Default bootstrap theme"
msgstr ""

#: plugins/themes.py:49
msgid "Bootstrap2"
msgstr ""

#: plugins/themes.py:49
msgid "Bootstrap 2.x theme"
msgstr ""

#: plugins/topnav.py:62 views/dashboard.py:465 views/edit.py:387
#: views/edit.py:396
#, python-format
msgid "Add %s"
msgstr "Добавить %s"

#: plugins/xversion.py:106
msgid "Initial version."
msgstr ""

#: plugins/xversion.py:108
msgid "Change version."
msgstr ""

#: plugins/xversion.py:110
msgid "Revert version."
msgstr ""

#: plugins/xversion.py:112
msgid "Rercover version."
msgstr ""

#: plugins/xversion.py:114
#, python-format
msgid "Deleted %(verbose_name)s."
msgstr ""

#: plugins/xversion.py:127 templates/xadmin/views/recover_form.html:26
msgid "Recover"
msgstr ""

#: plugins/xversion.py:143 templates/xadmin/views/model_history.html:11
#: templates/xadmin/views/revision_diff.html:11
#: templates/xadmin/views/revision_form.html:15
msgid "History"
msgstr "История"

#: plugins/xversion.py:194 templates/xadmin/views/recover_form.html:14
#: templates/xadmin/views/recover_list.html:10
#, python-format
msgid "Recover deleted %(name)s"
msgstr ""

#: plugins/xversion.py:238
#, python-format
msgid "Change history: %s"
msgstr ""

#: plugins/xversion.py:288
msgid "Must select two versions."
msgstr "Необходимо выбрать две версии."

#: plugins/xversion.py:296
msgid "Please select two different versions."
msgstr "Пожалуйста, выберите две различные версии."

#: plugins/xversion.py:383 plugins/xversion.py:500
#, python-format
msgid "Current: %s"
msgstr ""

#: plugins/xversion.py:424
#, python-format
msgid "Revert %s"
msgstr ""

#: plugins/xversion.py:440
#, python-format
msgid ""
"The %(model)s \"%(name)s\" was reverted successfully. You may edit it again "
"below."
msgstr ""

#: plugins/xversion.py:461
#, python-format
msgid "Recover %s"
msgstr ""

#: plugins/xversion.py:477
#, python-format
msgid ""
"The %(model)s \"%(name)s\" was recovered successfully. You may edit it again "
"below."
msgstr ""

#: templates/xadmin/404.html:4 templates/xadmin/404.html:8
msgid "Page not found"
msgstr "Страница не найдена"

#: templates/xadmin/404.html:10
msgid "We're sorry, but the requested page could not be found."
msgstr ""

#: templates/xadmin/500.html:7
#: templates/xadmin/auth/user/change_password.html:10
#: templates/xadmin/auth/user/change_password.html:15
#: templates/xadmin/base_site.html:53
#: templates/xadmin/includes/sitemenu_default.html:7
#: templates/xadmin/views/app_index.html:9
#: templates/xadmin/views/batch_change_form.html:9
#: templates/xadmin/views/invalid_setup.html:7
#: templates/xadmin/views/model_dashboard.html:7
#: templates/xadmin/views/model_delete_selected_confirm.html:8
#: templates/xadmin/views/model_history.html:8
#: templates/xadmin/views/recover_form.html:8
#: templates/xadmin/views/recover_list.html:8
#: templates/xadmin/views/revision_diff.html:8
#: templates/xadmin/views/revision_form.html:8 views/base.py:473
msgid "Home"
msgstr "Главная"

#: templates/xadmin/500.html:8
msgid "Server error"
msgstr ""

#: templates/xadmin/500.html:12
msgid "Server error (500)"
msgstr ""

#: templates/xadmin/500.html:15
msgid "Server Error <em>(500)</em>"
msgstr ""

#: templates/xadmin/500.html:16
msgid ""
"There's been an error. It's been reported to the site administrators via e-"
"mail and should be fixed shortly. Thanks for your patience."
msgstr ""

#: templates/xadmin/auth/password_reset/complete.html:11
#: templates/xadmin/auth/password_reset/done.html:11
msgid "Password reset successful"
msgstr "Пароль успешно восстановлен"

#: templates/xadmin/auth/password_reset/complete.html:14
msgid "Your password has been set.  You may go ahead and log in now."
msgstr ""

#: templates/xadmin/auth/password_reset/complete.html:15
msgid "Log in"
msgstr "Войти"

#: templates/xadmin/auth/password_reset/confirm.html:12
msgid "Enter new password"
msgstr "Введите новый пароль:"

#: templates/xadmin/auth/password_reset/confirm.html:17
msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""
"Пожалуйста, введите новый пароль дважды, чтобы мы могли убедиться в "
"правильности написания."

#: templates/xadmin/auth/password_reset/confirm.html:19
msgid "Change my password"
msgstr ""

#: templates/xadmin/auth/password_reset/confirm.html:24
msgid "Password reset unsuccessful"
msgstr "Ошибка восстановления пароля"

#: templates/xadmin/auth/password_reset/confirm.html:27
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""
"Неверная ссылка для восстановления пароля. Возможно, ей уже воспользовались. "
"Пожалуйста, попробуйте восстановить пароль еще раз."

#: templates/xadmin/auth/password_reset/done.html:14
msgid ""
"We've e-mailed you instructions for setting your password to the e-mail "
"address you submitted. You should be receiving it shortly."
msgstr ""
"Мы отправили инструкцию по восстановлению пароля на указанный вами адрес "
"электронной почты. Вы должны её вскоре получить."

#: templates/xadmin/auth/password_reset/email.html:2
#, python-format
msgid ""
"You're receiving this e-mail because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""
"Вы получили это письмо, потому что вы (или кто-то другой) запросили "
"восстановление пароля от учётной записи на сайте %(site_name)s, которая "
"связана с этим адресом электронной почты."

#: templates/xadmin/auth/password_reset/email.html:4
msgid "Please go to the following page and choose a new password:"
msgstr "Пожалуйста, перейдите на эту страницу и введите новый пароль:"

#: templates/xadmin/auth/password_reset/email.html:8
msgid "Your username, in case you've forgotten:"
msgstr ""

#: templates/xadmin/auth/password_reset/email.html:10
msgid "Thanks for using our site!"
msgstr ""

#: templates/xadmin/auth/password_reset/email.html:12
#, python-format
msgid "The %(site_name)s team"
msgstr ""

#: templates/xadmin/auth/password_reset/form.html:13
msgid "Password reset"
msgstr "Восстановление пароля"

#: templates/xadmin/auth/password_reset/form.html:17
msgid ""
"Forgotten your password? Enter your e-mail address below, and we'll e-mail "
"instructions for setting a new one."
msgstr ""

#: templates/xadmin/auth/password_reset/form.html:25
msgid "E-mail address:"
msgstr ""

#: templates/xadmin/auth/password_reset/form.html:33
msgid "Reset my password"
msgstr ""

#: templates/xadmin/auth/user/add_form.html:6
msgid ""
"First, enter a username and password. Then, you'll be able to edit more user "
"options."
msgstr ""

#: templates/xadmin/auth/user/add_form.html:8
msgid "Enter a username and password."
msgstr ""

#: templates/xadmin/auth/user/change_password.html:31
#: templates/xadmin/views/batch_change_form.html:24
#: templates/xadmin/views/form.html:18
#: templates/xadmin/views/model_form.html:20
msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "Пожалуйста, исправьте ошибку ниже."
msgstr[1] "Пожалуйста, исправьте ошибки ниже."
msgstr[2] "Пожалуйста, исправьте ошибки ниже."

#: templates/xadmin/auth/user/change_password.html:38
msgid "Enter your new password."
msgstr ""

#: templates/xadmin/auth/user/change_password.html:40
#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""

#: templates/xadmin/base_site.html:18
msgid "Welcome,"
msgstr ""

#: templates/xadmin/base_site.html:24
msgid "Log out"
msgstr "Выйти"

#: templates/xadmin/base_site.html:36
msgid "You don't have permission to edit anything."
msgstr ""

#: templates/xadmin/blocks/comm.top.theme.html:4
msgid "Themes"
msgstr ""

#: templates/xadmin/blocks/comm.top.topnav.html:9
#: templates/xadmin/blocks/model_list.nav_form.search_form.html:8
#: templates/xadmin/filters/char.html:7
#: templates/xadmin/filters/fk_search.html:7
#: templates/xadmin/filters/fk_search.html:16
#: templates/xadmin/filters/number.html:7
msgid "Search"
msgstr ""

#: templates/xadmin/blocks/comm.top.topnav.html:23
msgid "Add"
msgstr ""

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:9
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:26
msgid "Prev step"
msgstr ""

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:13
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:29
msgid "Next step"
msgstr ""

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:15
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:31
#: templates/xadmin/includes/submit_line.html:10
#: templates/xadmin/includes/submit_line.html:13
#: templates/xadmin/views/form.html:30 templates/xadmin/views/form.html:31
msgid "Save"
msgstr ""

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:7
msgid "Clean Bookmarks"
msgstr ""

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:18
msgid "No Bookmarks"
msgstr ""

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:22
msgid "New Bookmark"
msgstr ""

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:26
msgid "Save current page as Bookmark"
msgstr ""

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:32
msgid "Enter bookmark title"
msgstr ""

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:33
msgid "Waiting"
msgstr ""

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:33
msgid "Save Bookmark"
msgstr ""

#: templates/xadmin/blocks/model_list.nav_menu.filters.html:4
msgid "Filters"
msgstr ""

#: templates/xadmin/blocks/model_list.nav_menu.filters.html:8
msgid "Clean Filters"
msgstr ""

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:19
msgid "Click here to select the objects across all pages"
msgstr ""

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:19
#, python-format
msgid "Select all %(total_count)s %(model_name)s"
msgstr ""

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:20
msgid "Clear selection"
msgstr ""

#: templates/xadmin/blocks/model_list.results_top.charts.html:4
msgid "Charts"
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:4
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:8
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:19
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:47
msgid "Export"
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:26
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:29
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:32
msgid "Export with table header."
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:35
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:38
msgid "Export with format."
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:42
msgid "Export all data."
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:46
#: templates/xadmin/widgets/base.html:41
msgid "Close"
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.layouts.html:4
msgid "Layout"
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.refresh.html:8
msgid "Clean Refresh"
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.refresh.html:14
#, python-format
msgid "Every %(t)s seconds"
msgstr ""

#: templates/xadmin/blocks/model_list.top_toolbar.saveorder.html:4
msgid "Save Order"
msgstr ""

#: templates/xadmin/edit_inline/blank.html:5 views/detail.py:23
#: views/edit.py:102 views/list.py:29
msgid "Null"
msgstr ""

#: templates/xadmin/filters/char.html:13
msgid "Enter"
msgstr ""

#: templates/xadmin/filters/date.html:10 templates/xadmin/filters/date.html:13
msgid "Choice Date"
msgstr ""

#: templates/xadmin/filters/date.html:18
msgid "YY"
msgstr ""

#: templates/xadmin/filters/date.html:19
msgid "year"
msgstr ""

#: templates/xadmin/filters/date.html:22
msgid "MM"
msgstr ""

#: templates/xadmin/filters/date.html:23
msgid "month"
msgstr ""

#: templates/xadmin/filters/date.html:26
msgid "DD"
msgstr ""

#: templates/xadmin/filters/date.html:27
msgid "day"
msgstr ""

#: templates/xadmin/filters/date.html:29 templates/xadmin/filters/date.html:46
#: templates/xadmin/filters/date.html:54
#: templates/xadmin/filters/fk_search.html:24
#: templates/xadmin/filters/number.html:37
msgid "Apply"
msgstr ""

#: templates/xadmin/filters/date.html:34
msgid "Date Range"
msgstr ""

#: templates/xadmin/filters/date.html:41
msgid "Select Date"
msgstr ""

#: templates/xadmin/filters/date.html:42
msgid "From"
msgstr ""

#: templates/xadmin/filters/date.html:44
msgid "To"
msgstr ""

#: templates/xadmin/filters/fk_search.html:14
msgid "Select"
msgstr ""

#: templates/xadmin/filters/fk_search.html:26
#: templates/xadmin/filters/number.html:39
msgid "Clean"
msgstr ""

#: templates/xadmin/filters/number.html:17
#: templates/xadmin/filters/number.html:25
#: templates/xadmin/filters/number.html:33
msgid "Enter Number"
msgstr ""

#: templates/xadmin/filters/rel.html:3
#, python-format
msgid " By %(filter_title)s "
msgstr ""

#: templates/xadmin/forms/transfer.html:4
msgid "Available"
msgstr ""

#: templates/xadmin/forms/transfer.html:12
msgid "Click to choose all at once."
msgstr ""

#: templates/xadmin/forms/transfer.html:12
msgid "Choose all"
msgstr ""

#: templates/xadmin/forms/transfer.html:16
msgid "Choose"
msgstr ""

#: templates/xadmin/forms/transfer.html:19
#: templates/xadmin/widgets/base.html:40
msgid "Remove"
msgstr ""

#: templates/xadmin/forms/transfer.html:23
msgid "Chosen"
msgstr ""

#: templates/xadmin/forms/transfer.html:27
msgid "Click to remove all chosen at once."
msgstr ""

#: templates/xadmin/forms/transfer.html:27
msgid "Remove all"
msgstr ""

#: templates/xadmin/includes/pagination.html:9
msgid "Show all"
msgstr ""

#: templates/xadmin/includes/submit_line.html:10
#: templates/xadmin/includes/submit_line.html:13
#: templates/xadmin/views/form.html:30 templates/xadmin/views/form.html:31
msgid "Saving.."
msgstr ""

#: templates/xadmin/includes/submit_line.html:17
msgid "Save as new"
msgstr ""

#: templates/xadmin/includes/submit_line.html:18
msgid "Save and add another"
msgstr ""

#: templates/xadmin/includes/submit_line.html:19
msgid "Save and continue editing"
msgstr ""

#: templates/xadmin/includes/submit_line.html:24
#: templates/xadmin/views/model_detail.html:28 views/delete.py:93
msgid "Delete"
msgstr ""

#: templates/xadmin/views/app_index.html:13
#, python-format
msgid "%(name)s"
msgstr ""

#: templates/xadmin/views/batch_change_form.html:11
msgid "Change multiple objects"
msgstr ""

#: templates/xadmin/views/batch_change_form.html:16
#, python-format
msgid "Change one %(objects_name)s"
msgid_plural "Batch change %(counter)s %(objects_name)s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: templates/xadmin/views/batch_change_form.html:38
msgid "Change Multiple"
msgstr ""

#: templates/xadmin/views/dashboard.html:15
#: templates/xadmin/views/dashboard.html:22
#: templates/xadmin/views/dashboard.html:23
msgid "Add Widget"
msgstr ""

#: templates/xadmin/views/invalid_setup.html:13
msgid ""
"Something's wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""

#: templates/xadmin/views/logged_out.html:16
msgid "Logout Success"
msgstr ""

#: templates/xadmin/views/logged_out.html:17
msgid "Thanks for spending some quality time with the Web site today."
msgstr ""

#: templates/xadmin/views/logged_out.html:19
msgid "Close Window"
msgstr "Закрыть окно"

#: templates/xadmin/views/logged_out.html:20
msgid "Log in again"
msgstr "Войти заново"

#: templates/xadmin/views/login.html:39 views/website.py:38
msgid "Please Login"
msgstr ""

#: templates/xadmin/views/login.html:52
msgid "Username"
msgstr ""

#: templates/xadmin/views/login.html:64
msgid "Password"
msgstr ""

#: templates/xadmin/views/login.html:75
msgid "log in"
msgstr ""

#: templates/xadmin/views/model_dashboard.html:26
#: templates/xadmin/views/model_detail.html:25
msgid "Edit"
msgstr ""

#: templates/xadmin/views/model_delete_confirm.html:11
#, python-format
msgid ""
"Deleting the %(verbose_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""

#: templates/xadmin/views/model_delete_confirm.html:19
#, python-format
msgid ""
"Deleting the %(verbose_name)s '%(escaped_object)s' would require deleting "
"the following protected related objects:"
msgstr ""

#: templates/xadmin/views/model_delete_confirm.html:27
#, python-format
msgid ""
"Are you sure you want to delete the %(verbose_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""

#: templates/xadmin/views/model_delete_confirm.html:34
#: templates/xadmin/views/model_delete_selected_confirm.html:49
msgid "Yes, I'm sure"
msgstr ""

#: templates/xadmin/views/model_delete_confirm.html:35
#: templates/xadmin/views/model_delete_selected_confirm.html:50
msgid "Cancel"
msgstr ""

#: templates/xadmin/views/model_delete_selected_confirm.html:10
msgid "Delete multiple objects"
msgstr ""

#: templates/xadmin/views/model_delete_selected_confirm.html:18
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""

#: templates/xadmin/views/model_delete_selected_confirm.html:26
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""

#: templates/xadmin/views/model_delete_selected_confirm.html:34
#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""

#: templates/xadmin/views/model_history.html:26
msgid "Diff"
msgstr ""

#: templates/xadmin/views/model_history.html:27
#: templates/xadmin/views/recover_list.html:25
msgid "Date/time"
msgstr ""

#: templates/xadmin/views/model_history.html:28
msgid "User"
msgstr ""

#: templates/xadmin/views/model_history.html:29
msgid "Comment"
msgstr ""

#: templates/xadmin/views/model_history.html:54
msgid "Diff Select Versions"
msgstr ""

#: templates/xadmin/views/model_history.html:58
msgid ""
"This object doesn't have a change history. It probably wasn't added via this "
"admin site."
msgstr ""

#: templates/xadmin/views/model_list.html:29
#, python-format
msgid "Add %(name)s"
msgstr ""

#: templates/xadmin/views/model_list.html:39
msgid "Columns"
msgstr ""

#: templates/xadmin/views/model_list.html:42
msgid "Restore Selected"
msgstr ""

#: templates/xadmin/views/model_list.html:147
#: templates/xadmin/widgets/list.html:33
msgid "Empty list"
msgstr ""

#: templates/xadmin/views/recover_form.html:20
msgid "Press the recover button below to recover this version of the object."
msgstr ""

#: templates/xadmin/views/recover_list.html:19
msgid ""
"Choose a date from the list below to recover a deleted version of an object."
msgstr ""

#: templates/xadmin/views/recover_list.html:39
msgid "There are no deleted objects to recover."
msgstr ""

#: templates/xadmin/views/revision_diff.html:12
#: templates/xadmin/views/revision_diff.html:17
#, python-format
msgid "Diff %(verbose_name)s"
msgstr ""

#: templates/xadmin/views/revision_diff.html:25
msgid "Field"
msgstr ""

#: templates/xadmin/views/revision_diff.html:26
msgid "Version A"
msgstr ""

#: templates/xadmin/views/revision_diff.html:27
msgid "Version B"
msgstr ""

#: templates/xadmin/views/revision_diff.html:39
msgid "Revert to"
msgstr ""

#: templates/xadmin/views/revision_diff.html:40
#: templates/xadmin/views/revision_diff.html:41
msgid "Revert"
msgstr ""

#: templates/xadmin/views/revision_form.html:16
#, python-format
msgid "Revert %(verbose_name)s"
msgstr ""

#: templates/xadmin/views/revision_form.html:21
msgid "Press the revert button below to revert to this version of the object."
msgstr ""

#: templates/xadmin/views/revision_form.html:27
msgid "Revert this revision"
msgstr ""

#: templates/xadmin/widgets/addform.html:14
msgid "Success"
msgstr ""

#: templates/xadmin/widgets/addform.html:14
msgid "Add success, click <a id='change-link'>edit</a> to edit."
msgstr ""

#: templates/xadmin/widgets/addform.html:17
msgid "Quick Add"
msgstr ""

#: templates/xadmin/widgets/base.html:31
msgid "Widget Options"
msgstr ""

#: templates/xadmin/widgets/base.html:42
msgid "Save changes"
msgstr ""

#: views/base.py:315
msgid "Django Xadmin"
msgstr ""

#: views/base.py:316
msgid "my-company.inc"
msgstr ""

#: views/dashboard.py:186
msgid "Widget ID"
msgstr ""

#: views/dashboard.py:187
msgid "Widget Title"
msgstr ""

#: views/dashboard.py:252
msgid "Html Content Widget, can write any html content in widget."
msgstr ""

#: views/dashboard.py:255
msgid "Html Content"
msgstr ""

#: views/dashboard.py:318
msgid "Target Model"
msgstr ""

#: views/dashboard.py:369
msgid "Quick button Widget, quickly open any page."
msgstr ""

#: views/dashboard.py:371
msgid "Quick Buttons"
msgstr ""

#: views/dashboard.py:416
msgid "Any Objects list Widget."
msgstr ""

#: views/dashboard.py:456
msgid "Add any model object Widget."
msgstr ""

#: views/dashboard.py:492
msgid "Dashboard"
msgstr ""

#: views/dashboard.py:633
#, python-format
msgid "%s Dashboard"
msgstr ""

#: views/delete.py:103
#, python-format
msgid "The %(name)s \"%(obj)s\" was deleted successfully."
msgstr ""

#: views/detail.py:173 views/edit.py:211 views/form.py:72
msgid "Other Fields"
msgstr ""

#: views/detail.py:235
#, python-format
msgid "%s Detail"
msgstr ""

#: views/edit.py:253
msgid "Added."
msgstr ""

#: views/edit.py:255
#, fuzzy, python-format
#| msgid "Change %s"
msgid "Changed %s."
msgstr "Изменить %s"

#: views/edit.py:255
msgid "and"
msgstr ""

#: views/edit.py:258
msgid "No fields changed."
msgstr ""

#: views/edit.py:420
#, python-format
msgid "The %(name)s \"%(obj)s\" was added successfully."
msgstr ""

#: views/edit.py:425 views/edit.py:520
msgid "You may edit it again below."
msgstr ""

#: views/edit.py:429 views/edit.py:523
#, python-format
msgid "You may add another %s below."
msgstr ""

#: views/edit.py:471
#, python-format
msgid "Change %s"
msgstr "Изменить %s"

#: views/edit.py:516
#, python-format
msgid "The %(name)s \"%(obj)s\" was changed successfully."
msgstr ""

#: views/form.py:165
#, fuzzy, python-format
msgid "The %s was changed successfully."
msgstr "Пароль успешно изменен"

#: views/list.py:199
msgid "Database error"
msgstr ""

#: views/list.py:373
#, python-format
msgid "%s List"
msgstr ""

#: views/list.py:499
msgid "Sort ASC"
msgstr ""

#: views/list.py:500
msgid "Sort DESC"
msgstr ""

#: views/list.py:504
msgid "Cancel Sort"
msgstr ""

#: views/website.py:16
msgid "Main Dashboard"
msgstr ""

#: widgets.py:48
msgid "Now"
msgstr "Сейчас"
