# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2013
# <PERSON> <<EMAIL>>, 2013
# <AUTHOR> <EMAIL>, 2013
msgid ""
msgstr ""
"Project-Id-Version: xadmin-core\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-07-20 13:28+0800\n"
"PO-Revision-Date: 2013-12-19 11:06+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German (Germany) (http://www.transifex.com/projects/p/xadmin/"
"language/de_DE/)\n"
"Language: de_DE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: adminx.py:19
msgid "Admin Object"
msgstr ""

#: apps.py:11
msgid "Administration"
msgstr ""

#: filters.py:159 filters.py:191 filters.py:407 filters.py:493 filters.py:531
msgid "All"
msgstr "Alle"

#: filters.py:160 plugins/export.py:165
msgid "Yes"
msgstr "Ja"

#: filters.py:161 plugins/export.py:165
msgid "No"
msgstr "Nein"

#: filters.py:175
msgid "Unknown"
msgstr "Unbekannt"

#: filters.py:267
msgid "Any date"
msgstr "Beliebiges Datum"

#: filters.py:268
msgid "Has date"
msgstr "Hat ein Datum"

#: filters.py:271
msgid "Has no date"
msgstr "Hat kein Datum"

#: filters.py:274 widgets.py:30
msgid "Today"
msgstr "Heute"

#: filters.py:278
msgid "Past 7 days"
msgstr "Letzten 7 Tage"

#: filters.py:282
msgid "This month"
msgstr "Diesen Monat"

#: filters.py:286
msgid "This year"
msgstr "Dieses Jahr"

#: forms.py:10
msgid ""
"Please enter the correct username and password for a staff account. Note "
"that both fields are case-sensitive."
msgstr ""
"Bitte geben Sie den richtigen Benutzernamen und das Kennwort für ein "
"Mitarbeiter Konto an. Beachten Sie die Groß-und Kleinschreibung in den "
"beiden Feldern."

#: forms.py:21
msgid "Please log in again, because your session has expired."
msgstr "Ihre Sitzung ist abgelaufen - bitte melden Sie sich erneut an."

#: forms.py:41
#, python-format
msgid "Your e-mail address is not your username. Try '%s' instead."
msgstr "Ihre Email ist nicht ihr Benutzername. Probieren sie anstelle %s."

#: models.py:48
msgid "Title"
msgstr "Titel"

#: models.py:49 models.py:88 models.py:107 models.py:149
msgid "user"
msgstr "Benutzer"

#: models.py:50
msgid "Url Name"
msgstr "URL Name"

#: models.py:52
msgid "Query String"
msgstr "Abfrage String"

#: models.py:53
msgid "Is Shared"
msgstr "Wird geteilt"

#: models.py:66 plugins/bookmark.py:50 plugins/bookmark.py:180
msgid "Bookmark"
msgstr "Lesezeichen"

#: models.py:67
msgid "Bookmarks"
msgstr "Lesezeichen"

#: models.py:89
msgid "Settings Key"
msgstr "Einstellungsschlüssel"

#: models.py:90
msgid "Settings Content"
msgstr "Einstellungsinhalt"

#: models.py:102
msgid "User Setting"
msgstr "Benutzereinstellung"

#: models.py:103
msgid "User Settings"
msgstr "Benutzereinstelllungen"

#: models.py:108
msgid "Page"
msgstr "Seite"

#: models.py:109 views/dashboard.py:82 views/dashboard.py:92
msgid "Widget Type"
msgstr "Widget Typ"

#: models.py:110 views/dashboard.py:83
msgid "Widget Params"
msgstr "Widget Parameter"

#: models.py:137
msgid "User Widget"
msgstr "Benutzer Widget"

#: models.py:138
msgid "User Widgets"
msgstr "Benutzer Widgets"

#: models.py:142
#, fuzzy
#| msgid "Date/time"
msgid "action time"
msgstr "Datum/Uhrzeit"

#: models.py:151
msgid "action ip"
msgstr ""

#: models.py:155
msgid "content type"
msgstr ""

#: models.py:158
msgid "object id"
msgstr ""

#: models.py:159
msgid "object repr"
msgstr ""

#: models.py:160
msgid "action flag"
msgstr ""

#: models.py:161
#, fuzzy
#| msgid "Change %s"
msgid "change message"
msgstr "Ändern %s"

#: models.py:164
#, fuzzy
#| msgid "log in"
msgid "log entry"
msgstr "einloggen"

#: models.py:165
msgid "log entries"
msgstr ""

#: models.py:173
#, python-format
msgid "Added \"%(object)s\"."
msgstr ""

#: models.py:175
#, fuzzy, python-format
#| msgid "Change one %(objects_name)s"
#| msgid_plural "Batch change %(counter)s %(objects_name)s"
msgid "Changed \"%(object)s\" - %(changes)s"
msgstr "Anpassung von %(objects_name)s"

#: models.py:180
#, fuzzy, python-format
#| msgid "Related Objects"
msgid "Deleted \"%(object)s.\""
msgstr "Abhängige Objekte"

#: plugins/actions.py:57
#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "Lösche ausgewählte %(verbose_name_plural)s"

#: plugins/actions.py:72
#, fuzzy, python-format
#| msgid "Successfully deleted %(count)d %(items)s."
msgid "Batch delete %(count)d %(items)s."
msgstr "Erfolgreich gelöscht %(count)d %(items)s."

#: plugins/actions.py:78
#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr "Erfolgreich gelöscht %(count)d %(items)s."

#: plugins/actions.py:110 views/delete.py:70
#, python-format
msgid "Cannot delete %(name)s"
msgstr "Kann nicht gelöscht werden %(name)s"

#: plugins/actions.py:112 views/delete.py:73
msgid "Are you sure?"
msgstr "Sind Sie sicher?"

#: plugins/actions.py:158
#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "%(total_count)s ausgewählt"
msgstr[1] "Alle %(total_count)s ausgewählt"

#: plugins/actions.py:162
#, python-format
msgid "0 of %(cnt)s selected"
msgstr "0 von %(cnt)s ausgewählt"

#: plugins/actions.py:179 plugins/actions.py:189
msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""

#: plugins/aggregation.py:14
msgid "Min"
msgstr "Min"

#: plugins/aggregation.py:14
msgid "Max"
msgstr "Max"

#: plugins/aggregation.py:14
msgid "Avg"
msgstr "Durchschnitt"

#: plugins/aggregation.py:14
msgid "Sum"
msgstr "Summe"

#: plugins/aggregation.py:14
msgid "Count"
msgstr "Anzahl"

#: plugins/auth.py:21
#, fuzzy, python-format
msgid "Can add %s"
msgstr "Kann %s betrachten"

#: plugins/auth.py:22
#, fuzzy, python-format
msgid "Can change %s"
msgstr "Ändern %s"

#: plugins/auth.py:23
#, fuzzy, python-format
msgid "Can edit %s"
msgstr "Kann %s betrachten"

#: plugins/auth.py:24
#, fuzzy, python-format
msgid "Can delete %s"
msgstr "Kann nicht gelöscht werden %(name)s"

#: plugins/auth.py:25
#, python-format
msgid "Can view %s"
msgstr "Kann %s betrachten"

#: plugins/auth.py:87
msgid "Personal info"
msgstr "Persönliche Informationen"

#: plugins/auth.py:91
msgid "Permissions"
msgstr "Berechtigungen"

#: plugins/auth.py:94
msgid "Important dates"
msgstr "Wichtige Termine"

#: plugins/auth.py:99
msgid "Status"
msgstr "Status"

#: plugins/auth.py:111
#, fuzzy
msgid "Permission Name"
msgstr "Berechtigungen"

#: plugins/auth.py:167
msgid "Change Password"
msgstr "Passwort ändern"

#: plugins/auth.py:198
#, python-format
msgid "Change password: %s"
msgstr "Passwort ändern: %s"

#: plugins/auth.py:223 plugins/auth.py:255
msgid "Password changed successfully."
msgstr "Passwort erfolgreich geändert."

#: plugins/auth.py:242 templates/xadmin/auth/user/change_password.html:11
#: templates/xadmin/auth/user/change_password.html:22
#: templates/xadmin/auth/user/change_password.html:55
msgid "Change password"
msgstr "Passwort ändern"

#: plugins/batch.py:44
msgid "Change this field"
msgstr "Änderung des Feldes"

#: plugins/batch.py:65
#, python-format
msgid "Batch Change selected %(verbose_name_plural)s"
msgstr "Anpassung aller gewählten %(verbose_name_plural)s"

#: plugins/batch.py:89
#, python-format
msgid "Successfully change %(count)d %(items)s."
msgstr "Es wurden %(count)d %(items)s erfolgreich geändert."

#: plugins/batch.py:138
#, python-format
msgid "Batch change %s"
msgstr ""

#: plugins/bookmark.py:173
msgid "bookmark"
msgstr "Als Lesezeichen abspeichern"

#: plugins/bookmark.py:176
msgid "Bookmark Widget, can show user's bookmark list data in widget."
msgstr ""

#: plugins/chart.py:25
msgid "Show models simple chart."
msgstr "Einfaches Diagramm der Modelle anzeigen."

#: plugins/chart.py:51
#, python-format
msgid "%s Charts"
msgstr "%s Diagramme"

#: plugins/comments.py:33
msgid "Metadata"
msgstr ""

#: plugins/comments.py:60
msgid "flagged"
msgid_plural "flagged"
msgstr[0] ""
msgstr[1] ""

#: plugins/comments.py:61
msgid "Flag selected comments"
msgstr ""

#: plugins/comments.py:66
msgid "approved"
msgid_plural "approved"
msgstr[0] ""
msgstr[1] ""

#: plugins/comments.py:67
msgid "Approve selected comments"
msgstr ""

#: plugins/comments.py:72
#, fuzzy
msgid "removed"
msgid_plural "removed"
msgstr[0] "Entferne"
msgstr[1] "Entferne"

#: plugins/comments.py:73
#, fuzzy
msgid "Remove selected comments"
msgstr "Gelöschte %(name)s wiederherstellen"

#: plugins/comments.py:86
#, python-format
msgid "1 comment was successfully %(action)s."
msgid_plural "%(count)s comments were successfully %(action)s."
msgstr[0] ""
msgstr[1] ""

#: plugins/details.py:52 views/list.py:578
#, python-format
msgid "Details of %s"
msgstr "Einzelheiten von %s"

#: plugins/editable.py:46
#, python-format
msgid "Enter %s"
msgstr "Eingabe %s"

#: plugins/editable.py:73 views/dashboard.py:649 views/delete.py:27
#: views/detail.py:145 views/edit.py:454
#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr ""
"%(name)s bezeichnetes Objekt mit dem Primärschlüssel %(key)r existiert nicht."

#: plugins/export.py:98 plugins/export.py:135
msgid "Sheet"
msgstr "Seite"

#: plugins/filters.py:133 plugins/quickfilter.py:141
#, python-format
msgid "<b>Filtering error:</b> %s"
msgstr "<b>Fehlerhaftes Filtern:</b> %s"

#: plugins/images.py:29
msgid "Previous"
msgstr "Vorherige"

#: plugins/images.py:29
msgid "Next"
msgstr "Nächste"

#: plugins/images.py:29
msgid "Slideshow"
msgstr "Slideshow"

#: plugins/images.py:29
msgid "Download"
msgstr "Download"

#: plugins/images.py:50
msgid "Change:"
msgstr "Änderung:"

#: plugins/layout.py:16
msgid "Table"
msgstr "Tabelle"

#: plugins/layout.py:22
msgid "Thumbnails"
msgstr "Thumbnails"

#: plugins/passwords.py:64
msgid "Forgotten your password or username?"
msgstr "Haben Sie Ihr Passwort oder den Benutzernamen vergessen?"

#: plugins/quickform.py:79
#, python-format
msgid "Create New %s"
msgstr "Erstelle %s neu"

#: plugins/relate.py:104
msgid "Related Objects"
msgstr "Abhängige Objekte"

#: plugins/relfield.py:29 plugins/topnav.py:38
#, python-format
msgid "Search %s"
msgstr "Suche %s"

#: plugins/relfield.py:67
#, fuzzy, python-format
#| msgid "Select Date"
msgid "Select %s"
msgstr "Datum wählen"

#: plugins/themes.py:47
msgid "Default"
msgstr "Standard"

#: plugins/themes.py:48
msgid "Default bootstrap theme"
msgstr "Standard Bootstrap Thema"

#: plugins/themes.py:49
msgid "Bootstrap2"
msgstr "Bootstrap2"

#: plugins/themes.py:49
msgid "Bootstrap 2.x theme"
msgstr "Bootstrap 2.x Thema"

#: plugins/topnav.py:62 views/dashboard.py:465 views/edit.py:387
#: views/edit.py:396
#, python-format
msgid "Add %s"
msgstr "Hinzufügen %s"

#: plugins/xversion.py:106
msgid "Initial version."
msgstr "Erste Version."

#: plugins/xversion.py:108
msgid "Change version."
msgstr "Version ändern."

#: plugins/xversion.py:110
msgid "Revert version."
msgstr "Version zurückfallen."

#: plugins/xversion.py:112
msgid "Rercover version."
msgstr "Version wiederherstellen."

#: plugins/xversion.py:114
#, python-format
msgid "Deleted %(verbose_name)s."
msgstr "Gelöschte %(verbose_name)s."

#: plugins/xversion.py:127 templates/xadmin/views/recover_form.html:26
msgid "Recover"
msgstr ""

#: plugins/xversion.py:143 templates/xadmin/views/model_history.html:11
#: templates/xadmin/views/revision_diff.html:11
#: templates/xadmin/views/revision_form.html:15
msgid "History"
msgstr "Verlauf"

#: plugins/xversion.py:194 templates/xadmin/views/recover_form.html:14
#: templates/xadmin/views/recover_list.html:10
#, python-format
msgid "Recover deleted %(name)s"
msgstr "Gelöschte %(name)s wiederherstellen"

#: plugins/xversion.py:238
#, python-format
msgid "Change history: %s"
msgstr "Änderungen: %s"

#: plugins/xversion.py:288
msgid "Must select two versions."
msgstr "Es müssen zwei Versionen ausgewählt sein."

#: plugins/xversion.py:296
msgid "Please select two different versions."
msgstr "Bitte wählen Sie zwei unterschiedliche Versionen aus."

#: plugins/xversion.py:383 plugins/xversion.py:500
#, python-format
msgid "Current: %s"
msgstr "Aktuell: %s"

#: plugins/xversion.py:424
#, python-format
msgid "Revert %s"
msgstr "Auf %s zurückfallen"

#: plugins/xversion.py:440
#, python-format
msgid ""
"The %(model)s \"%(name)s\" was reverted successfully. You may edit it again "
"below."
msgstr ""

#: plugins/xversion.py:461
#, python-format
msgid "Recover %s"
msgstr "Stelle %s wieder her"

#: plugins/xversion.py:477
#, python-format
msgid ""
"The %(model)s \"%(name)s\" was recovered successfully. You may edit it again "
"below."
msgstr ""

#: templates/xadmin/404.html:4 templates/xadmin/404.html:8
msgid "Page not found"
msgstr "Seite konnte nicht gefunden werden"

#: templates/xadmin/404.html:10
msgid "We're sorry, but the requested page could not be found."
msgstr ""
"Es tut uns leid, aber die angeforderte Seite konnte nicht gefunden werden."

#: templates/xadmin/500.html:7
#: templates/xadmin/auth/user/change_password.html:10
#: templates/xadmin/auth/user/change_password.html:15
#: templates/xadmin/base_site.html:53
#: templates/xadmin/includes/sitemenu_default.html:7
#: templates/xadmin/views/app_index.html:9
#: templates/xadmin/views/batch_change_form.html:9
#: templates/xadmin/views/invalid_setup.html:7
#: templates/xadmin/views/model_dashboard.html:7
#: templates/xadmin/views/model_delete_selected_confirm.html:8
#: templates/xadmin/views/model_history.html:8
#: templates/xadmin/views/recover_form.html:8
#: templates/xadmin/views/recover_list.html:8
#: templates/xadmin/views/revision_diff.html:8
#: templates/xadmin/views/revision_form.html:8 views/base.py:473
msgid "Home"
msgstr "Startseite"

#: templates/xadmin/500.html:8
msgid "Server error"
msgstr "Serverfehler"

#: templates/xadmin/500.html:12
msgid "Server error (500)"
msgstr "Serverfehler (500)"

#: templates/xadmin/500.html:15
msgid "Server Error <em>(500)</em>"
msgstr "Server Fehler <em>(500)</em>"

#: templates/xadmin/500.html:16
msgid ""
"There's been an error. It's been reported to the site administrators via e-"
"mail and should be fixed shortly. Thanks for your patience."
msgstr ""
"Es ist ein Fehler aufgetreten. Der Siteadmin wurde via E-Mail informiert und "
"wird sich in Kürze um die Behebung des Fehlers kümmern. Wir danken für Ihre "
"Geduld."

#: templates/xadmin/auth/password_reset/complete.html:11
#: templates/xadmin/auth/password_reset/done.html:11
msgid "Password reset successful"
msgstr "Passwort erfolgreich zurückgesetzt"

#: templates/xadmin/auth/password_reset/complete.html:14
msgid "Your password has been set.  You may go ahead and log in now."
msgstr "Ihr Passwort wurde gesetzt. Sie können fortfahren und sich einloggen."

#: templates/xadmin/auth/password_reset/complete.html:15
msgid "Log in"
msgstr "Einloggen"

#: templates/xadmin/auth/password_reset/confirm.html:12
msgid "Enter new password"
msgstr "Geben Sie Ihr neues Passwort ein."

#: templates/xadmin/auth/password_reset/confirm.html:17
msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""
"Bitte geben Sie Ihr neues Passwort zweimal ein, damit die Identität "
"festgestellt werden kann."

#: templates/xadmin/auth/password_reset/confirm.html:19
msgid "Change my password"
msgstr "Passwort ändern"

#: templates/xadmin/auth/password_reset/confirm.html:24
msgid "Password reset unsuccessful"
msgstr "Passwort zurücksetzen fehlgeschlagen"

#: templates/xadmin/auth/password_reset/confirm.html:27
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""
"Der Passwort-zurücksetzen Link war ungültig, weil dieser möglicherweise "
"schon verwendet wurde. Bitte fordern Sie einen neuen Link an."

#: templates/xadmin/auth/password_reset/done.html:14
msgid ""
"We've e-mailed you instructions for setting your password to the e-mail "
"address you submitted. You should be receiving it shortly."
msgstr ""
"Wir haben Ihnen eine E-Mail mit Anweisungen zum Zurücksetzen Ihres "
"Passwortes an die Adresse geschickt, die Sie uns übermittelt haben. Sie "
"sollten sie in Kürze erhalten."

#: templates/xadmin/auth/password_reset/email.html:2
#, python-format
msgid ""
"You're receiving this e-mail because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""

#: templates/xadmin/auth/password_reset/email.html:4
msgid "Please go to the following page and choose a new password:"
msgstr "Bitte gehen Sie auf folgende Seite und erstellen ein neues Passwort."

#: templates/xadmin/auth/password_reset/email.html:8
msgid "Your username, in case you've forgotten:"
msgstr "Ihr Benutzername, falls Sie ihn vergessen sollten:"

#: templates/xadmin/auth/password_reset/email.html:10
msgid "Thanks for using our site!"
msgstr "Danke, dass Sie uns besucht haben!"

#: templates/xadmin/auth/password_reset/email.html:12
#, python-format
msgid "The %(site_name)s team"
msgstr "Das %(site_name)s Team"

#: templates/xadmin/auth/password_reset/form.html:13
msgid "Password reset"
msgstr "Passwort zurücksetzen"

#: templates/xadmin/auth/password_reset/form.html:17
msgid ""
"Forgotten your password? Enter your e-mail address below, and we'll e-mail "
"instructions for setting a new one."
msgstr ""
"Haben Sie Ihr Passwort vergessen? Geben Sie unten Ihre  E-Mail Adresse ein "
"und wir werden Ihnen eine Nachricht mit Anweisungen schicken, wie ein Neues "
"erstellt wird."

#: templates/xadmin/auth/password_reset/form.html:25
msgid "E-mail address:"
msgstr "E-Mail Adresse:"

#: templates/xadmin/auth/password_reset/form.html:33
msgid "Reset my password"
msgstr "Passwort zurücksetzen"

#: templates/xadmin/auth/user/add_form.html:6
msgid ""
"First, enter a username and password. Then, you'll be able to edit more user "
"options."
msgstr ""
"Bitte tragen Sie einen Benutzernamen und Passwort ein. Anschließend können "
"Sie weitere Benutzerinformationen bearbeiten."

#: templates/xadmin/auth/user/add_form.html:8
msgid "Enter a username and password."
msgstr "Bitte geben Sie Benutzernamen und Passwort ein."

#: templates/xadmin/auth/user/change_password.html:31
#: templates/xadmin/views/batch_change_form.html:24
#: templates/xadmin/views/form.html:18
#: templates/xadmin/views/model_form.html:20
msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "Bitte korrigieren Sie den folgenden Fehler."
msgstr[1] "Bitte korrigieren Sie die folgenden Fehler."

#: templates/xadmin/auth/user/change_password.html:38
msgid "Enter your new password."
msgstr "Geben Sie Ihr neues Passwort ein."

#: templates/xadmin/auth/user/change_password.html:40
#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""
"Vergeben Sie ein neues Passwort für den Benutzer <strong>%(username)s</"
"strong>."

#: templates/xadmin/base_site.html:18
msgid "Welcome,"
msgstr "Willkommen,"

#: templates/xadmin/base_site.html:24
msgid "Log out"
msgstr "Abmelden"

#: templates/xadmin/base_site.html:36
msgid "You don't have permission to edit anything."
msgstr "Sie haben nicht die notwendige Berechtigung etwas zu ändern."

#: templates/xadmin/blocks/comm.top.theme.html:4
msgid "Themes"
msgstr "Themeneinstellungen"

#: templates/xadmin/blocks/comm.top.topnav.html:9
#: templates/xadmin/blocks/model_list.nav_form.search_form.html:8
#: templates/xadmin/filters/char.html:7
#: templates/xadmin/filters/fk_search.html:7
#: templates/xadmin/filters/fk_search.html:16
#: templates/xadmin/filters/number.html:7
msgid "Search"
msgstr "Suchen"

#: templates/xadmin/blocks/comm.top.topnav.html:23
msgid "Add"
msgstr "Hinzufügen"

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:9
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:26
msgid "Prev step"
msgstr "Zurück"

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:13
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:29
msgid "Next step"
msgstr "Nächstes"

#: templates/xadmin/blocks/model_form.submit_line.wizard.html:15
#: templates/xadmin/blocks/model_form.submit_line.wizard.html:31
#: templates/xadmin/includes/submit_line.html:10
#: templates/xadmin/includes/submit_line.html:13
#: templates/xadmin/views/form.html:30 templates/xadmin/views/form.html:31
msgid "Save"
msgstr "Speichern"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:7
msgid "Clean Bookmarks"
msgstr "Bereinige Lesezeichen"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:18
msgid "No Bookmarks"
msgstr "Keine Lesezeichen"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:22
msgid "New Bookmark"
msgstr "Neues Lesezeichen"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:26
msgid "Save current page as Bookmark"
msgstr "Lesezeichen hinzufügen"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:32
msgid "Enter bookmark title"
msgstr "Lesezeichenname"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:33
msgid "Waiting"
msgstr "Warte"

#: templates/xadmin/blocks/model_list.nav_menu.bookmarks.html:33
msgid "Save Bookmark"
msgstr "Speichere Lesezeichen"

#: templates/xadmin/blocks/model_list.nav_menu.filters.html:4
msgid "Filters"
msgstr "Filter"

#: templates/xadmin/blocks/model_list.nav_menu.filters.html:8
msgid "Clean Filters"
msgstr "Bereinige Filter"

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:19
msgid "Click here to select the objects across all pages"
msgstr "Klicken Sie hier, um alle Objekte über alle Seiten hinweg auszuwählen."

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:19
#, python-format
msgid "Select all %(total_count)s %(model_name)s"
msgstr "Alles auswählen %(total_count)s %(model_name)s"

#: templates/xadmin/blocks/model_list.results_bottom.actions.html:20
msgid "Clear selection"
msgstr "Auswahl abwählen"

#: templates/xadmin/blocks/model_list.results_top.charts.html:4
msgid "Charts"
msgstr "Diagramme"

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:4
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:8
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:19
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:47
msgid "Export"
msgstr "Exportieren"

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:26
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:29
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:32
msgid "Export with table header."
msgstr "Export mit Tabellenkopf."

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:35
#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:38
msgid "Export with format."
msgstr "Exportieren mit Formatierung."

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:42
msgid "Export all data."
msgstr "Komplette Daten exportieren."

#: templates/xadmin/blocks/model_list.top_toolbar.exports.html:46
#: templates/xadmin/widgets/base.html:41
msgid "Close"
msgstr "Schließen"

#: templates/xadmin/blocks/model_list.top_toolbar.layouts.html:4
msgid "Layout"
msgstr "Layout"

#: templates/xadmin/blocks/model_list.top_toolbar.refresh.html:8
msgid "Clean Refresh"
msgstr "Säubern"

#: templates/xadmin/blocks/model_list.top_toolbar.refresh.html:14
#, python-format
msgid "Every %(t)s seconds"
msgstr "Alle %(t)s Sekunden"

#: templates/xadmin/blocks/model_list.top_toolbar.saveorder.html:4
msgid "Save Order"
msgstr ""

#: templates/xadmin/edit_inline/blank.html:5 views/detail.py:23
#: views/edit.py:102 views/list.py:29
msgid "Null"
msgstr "Null"

#: templates/xadmin/filters/char.html:13
msgid "Enter"
msgstr "Eingabe"

#: templates/xadmin/filters/date.html:10 templates/xadmin/filters/date.html:13
msgid "Choice Date"
msgstr "Auswahldatum"

#: templates/xadmin/filters/date.html:18
msgid "YY"
msgstr "YY"

#: templates/xadmin/filters/date.html:19
msgid "year"
msgstr "Jahr"

#: templates/xadmin/filters/date.html:22
msgid "MM"
msgstr "MM"

#: templates/xadmin/filters/date.html:23
msgid "month"
msgstr "Monat"

#: templates/xadmin/filters/date.html:26
msgid "DD"
msgstr "DD"

#: templates/xadmin/filters/date.html:27
msgid "day"
msgstr "Tag"

#: templates/xadmin/filters/date.html:29 templates/xadmin/filters/date.html:46
#: templates/xadmin/filters/date.html:54
#: templates/xadmin/filters/fk_search.html:24
#: templates/xadmin/filters/number.html:37
msgid "Apply"
msgstr "Anwenden"

#: templates/xadmin/filters/date.html:34
msgid "Date Range"
msgstr "Datumsintervall"

#: templates/xadmin/filters/date.html:41
msgid "Select Date"
msgstr "Datum wählen"

#: templates/xadmin/filters/date.html:42
msgid "From"
msgstr "Von"

#: templates/xadmin/filters/date.html:44
msgid "To"
msgstr "Bis"

#: templates/xadmin/filters/fk_search.html:14
#, fuzzy
#| msgid "Select Date"
msgid "Select"
msgstr "Datum wählen"

#: templates/xadmin/filters/fk_search.html:26
#: templates/xadmin/filters/number.html:39
msgid "Clean"
msgstr "Entfernen"

#: templates/xadmin/filters/number.html:17
#: templates/xadmin/filters/number.html:25
#: templates/xadmin/filters/number.html:33
msgid "Enter Number"
msgstr "Zahl eingeben"

#: templates/xadmin/filters/rel.html:3
#, python-format
msgid " By %(filter_title)s "
msgstr "Mittels %(filter_title)s"

#: templates/xadmin/forms/transfer.html:4
msgid "Available"
msgstr "Verfügbar"

#: templates/xadmin/forms/transfer.html:12
msgid "Click to choose all at once."
msgstr "Klicken um alles auf einmal auszuwählen."

#: templates/xadmin/forms/transfer.html:12
msgid "Choose all"
msgstr "Alles auswählen"

#: templates/xadmin/forms/transfer.html:16
msgid "Choose"
msgstr "Wähle"

#: templates/xadmin/forms/transfer.html:19
#: templates/xadmin/widgets/base.html:40
msgid "Remove"
msgstr "Entferne"

#: templates/xadmin/forms/transfer.html:23
msgid "Chosen"
msgstr "Gewählt"

#: templates/xadmin/forms/transfer.html:27
msgid "Click to remove all chosen at once."
msgstr "Klicken um alles Ausgewählte sofort zu löschen."

#: templates/xadmin/forms/transfer.html:27
msgid "Remove all"
msgstr "Alles entfernen"

#: templates/xadmin/includes/pagination.html:9
msgid "Show all"
msgstr "Alles anzeigen"

#: templates/xadmin/includes/submit_line.html:10
#: templates/xadmin/includes/submit_line.html:13
#: templates/xadmin/views/form.html:30 templates/xadmin/views/form.html:31
msgid "Saving.."
msgstr "Speichern..."

#: templates/xadmin/includes/submit_line.html:17
msgid "Save as new"
msgstr "Als neu abspeichern"

#: templates/xadmin/includes/submit_line.html:18
msgid "Save and add another"
msgstr "Speichern und neu hinzufügen"

#: templates/xadmin/includes/submit_line.html:19
msgid "Save and continue editing"
msgstr "Speichern und weiter bearbeiten"

#: templates/xadmin/includes/submit_line.html:24
#: templates/xadmin/views/model_detail.html:28 views/delete.py:93
msgid "Delete"
msgstr "Löschen"

#: templates/xadmin/views/app_index.html:13
#, python-format
msgid "%(name)s"
msgstr "%(name)s"

#: templates/xadmin/views/batch_change_form.html:11
msgid "Change multiple objects"
msgstr "Mehrere Objekte ändern"

#: templates/xadmin/views/batch_change_form.html:16
#, python-format
msgid "Change one %(objects_name)s"
msgid_plural "Batch change %(counter)s %(objects_name)s"
msgstr[0] "Anpassung von %(objects_name)s"
msgstr[1] "Anpassung aller %(counter)s %(objects_name)s"

#: templates/xadmin/views/batch_change_form.html:38
msgid "Change Multiple"
msgstr "Mehrere andern"

#: templates/xadmin/views/dashboard.html:15
#: templates/xadmin/views/dashboard.html:22
#: templates/xadmin/views/dashboard.html:23
msgid "Add Widget"
msgstr "Widget hinzufügen"

#: templates/xadmin/views/invalid_setup.html:13
msgid ""
"Something's wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""

#: templates/xadmin/views/logged_out.html:16
msgid "Logout Success"
msgstr "Logout erfolgreich"

#: templates/xadmin/views/logged_out.html:17
msgid "Thanks for spending some quality time with the Web site today."
msgstr "Danke, dass Sie ein paar nette Minuten hier verbracht haben."

#: templates/xadmin/views/logged_out.html:19
msgid "Close Window"
msgstr "Fenster schließen"

#: templates/xadmin/views/logged_out.html:20
msgid "Log in again"
msgstr "Wieder einloggen"

#: templates/xadmin/views/login.html:39 views/website.py:38
msgid "Please Login"
msgstr "Bitte loggen Sie sich ein"

#: templates/xadmin/views/login.html:52
msgid "Username"
msgstr "Benutzername"

#: templates/xadmin/views/login.html:64
msgid "Password"
msgstr "Passwort"

#: templates/xadmin/views/login.html:75
msgid "log in"
msgstr "einloggen"

#: templates/xadmin/views/model_dashboard.html:26
#: templates/xadmin/views/model_detail.html:25
msgid "Edit"
msgstr "Bearbeiten"

#: templates/xadmin/views/model_delete_confirm.html:11
#, python-format
msgid ""
"Deleting the %(verbose_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""

#: templates/xadmin/views/model_delete_confirm.html:19
#, python-format
msgid ""
"Deleting the %(verbose_name)s '%(escaped_object)s' would require deleting "
"the following protected related objects:"
msgstr ""

#: templates/xadmin/views/model_delete_confirm.html:27
#, python-format
msgid ""
"Are you sure you want to delete the %(verbose_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""

#: templates/xadmin/views/model_delete_confirm.html:34
#: templates/xadmin/views/model_delete_selected_confirm.html:49
msgid "Yes, I'm sure"
msgstr "Ja, na klar!"

#: templates/xadmin/views/model_delete_confirm.html:35
#: templates/xadmin/views/model_delete_selected_confirm.html:50
msgid "Cancel"
msgstr "Abbruch"

#: templates/xadmin/views/model_delete_selected_confirm.html:10
msgid "Delete multiple objects"
msgstr "Mehrere Objekte löschen"

#: templates/xadmin/views/model_delete_selected_confirm.html:18
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""

#: templates/xadmin/views/model_delete_selected_confirm.html:26
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""

#: templates/xadmin/views/model_delete_selected_confirm.html:34
#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""

#: templates/xadmin/views/model_history.html:26
msgid "Diff"
msgstr "Delta"

#: templates/xadmin/views/model_history.html:27
#: templates/xadmin/views/recover_list.html:25
msgid "Date/time"
msgstr "Datum/Uhrzeit"

#: templates/xadmin/views/model_history.html:28
msgid "User"
msgstr "Benutzer"

#: templates/xadmin/views/model_history.html:29
msgid "Comment"
msgstr "Kommentar"

#: templates/xadmin/views/model_history.html:54
msgid "Diff Select Versions"
msgstr ""

#: templates/xadmin/views/model_history.html:58
msgid ""
"This object doesn't have a change history. It probably wasn't added via this "
"admin site."
msgstr ""

#: templates/xadmin/views/model_list.html:29
#, python-format
msgid "Add %(name)s"
msgstr "%(name)s hinzufügen"

#: templates/xadmin/views/model_list.html:39
#, fuzzy
msgid "Columns"
msgstr "Spalten"

#: templates/xadmin/views/model_list.html:42
msgid "Restore Selected"
msgstr "Gewähltes wiederherstellen"

#: templates/xadmin/views/model_list.html:147
#: templates/xadmin/widgets/list.html:33
msgid "Empty list"
msgstr "Leere Liste"

#: templates/xadmin/views/recover_form.html:20
msgid "Press the recover button below to recover this version of the object."
msgstr ""

#: templates/xadmin/views/recover_list.html:19
msgid ""
"Choose a date from the list below to recover a deleted version of an object."
msgstr ""

#: templates/xadmin/views/recover_list.html:39
msgid "There are no deleted objects to recover."
msgstr "Es gibt keine gelöschten Objekte wiederherzustellen."

#: templates/xadmin/views/revision_diff.html:12
#: templates/xadmin/views/revision_diff.html:17
#, python-format
msgid "Diff %(verbose_name)s"
msgstr "Delta zwischen %(verbose_name)s"

#: templates/xadmin/views/revision_diff.html:25
msgid "Field"
msgstr "Feld"

#: templates/xadmin/views/revision_diff.html:26
msgid "Version A"
msgstr "Version A"

#: templates/xadmin/views/revision_diff.html:27
msgid "Version B"
msgstr "Version B"

#: templates/xadmin/views/revision_diff.html:39
msgid "Revert to"
msgstr ""

#: templates/xadmin/views/revision_diff.html:40
#: templates/xadmin/views/revision_diff.html:41
msgid "Revert"
msgstr ""

#: templates/xadmin/views/revision_form.html:16
#, python-format
msgid "Revert %(verbose_name)s"
msgstr ""

#: templates/xadmin/views/revision_form.html:21
msgid "Press the revert button below to revert to this version of the object."
msgstr ""

#: templates/xadmin/views/revision_form.html:27
msgid "Revert this revision"
msgstr "Diese Überarbeitung umkehren."

#: templates/xadmin/widgets/addform.html:14
msgid "Success"
msgstr "Erfolg"

#: templates/xadmin/widgets/addform.html:14
msgid "Add success, click <a id='change-link'>edit</a> to edit."
msgstr ""

#: templates/xadmin/widgets/addform.html:17
msgid "Quick Add"
msgstr "Schnelles Hinzufügen"

#: templates/xadmin/widgets/base.html:31
msgid "Widget Options"
msgstr "Widget Optionen"

#: templates/xadmin/widgets/base.html:42
msgid "Save changes"
msgstr "Änderungen speichern"

#: views/base.py:315
msgid "Django Xadmin"
msgstr "Django Xadmin"

#: views/base.py:316
msgid "my-company.inc"
msgstr ""

#: views/dashboard.py:186
msgid "Widget ID"
msgstr "Widget ID"

#: views/dashboard.py:187
msgid "Widget Title"
msgstr "Widget Titel"

#: views/dashboard.py:252
msgid "Html Content Widget, can write any html content in widget."
msgstr ""

#: views/dashboard.py:255
msgid "Html Content"
msgstr "Html Inhalt"

#: views/dashboard.py:318
msgid "Target Model"
msgstr "Zielmodell"

#: views/dashboard.py:369
msgid "Quick button Widget, quickly open any page."
msgstr ""
"Schnellauswahlbutton Widget, zum schnellen Öffnen einer beliebigen Seite."

#: views/dashboard.py:371
msgid "Quick Buttons"
msgstr "Schnellauswahl Buttons"

#: views/dashboard.py:416
msgid "Any Objects list Widget."
msgstr "Beliebige Objektliste Widget."

#: views/dashboard.py:456
msgid "Add any model object Widget."
msgstr "Fügen Sie ein beliebiges Modell Objekt Widget hinzu."

#: views/dashboard.py:492
msgid "Dashboard"
msgstr "Armaturentafel"

#: views/dashboard.py:633
#, python-format
msgid "%s Dashboard"
msgstr "%s Armaturentafel"

#: views/delete.py:103
#, python-format
msgid "The %(name)s \"%(obj)s\" was deleted successfully."
msgstr ""

#: views/detail.py:173 views/edit.py:211 views/form.py:72
msgid "Other Fields"
msgstr "Andere Felder"

#: views/detail.py:235
#, python-format
msgid "%s Detail"
msgstr "%s Einzelheiten"

#: views/edit.py:253
msgid "Added."
msgstr ""

#: views/edit.py:255
#, fuzzy, python-format
#| msgid "Change %s"
msgid "Changed %s."
msgstr "Ändern %s"

#: views/edit.py:255
msgid "and"
msgstr ""

#: views/edit.py:258
msgid "No fields changed."
msgstr ""

#: views/edit.py:420
#, python-format
msgid "The %(name)s \"%(obj)s\" was added successfully."
msgstr "%(name)s \"%(obj)s\" wurde erfolgreich hinzugefügt."

#: views/edit.py:425 views/edit.py:520
msgid "You may edit it again below."
msgstr "Sie können es trotzdem nochmals bearbeiten."

#: views/edit.py:429 views/edit.py:523
#, python-format
msgid "You may add another %s below."
msgstr "Sie können unten ein weiteres %s hinzufügen."

#: views/edit.py:471
#, python-format
msgid "Change %s"
msgstr "Ändern %s"

#: views/edit.py:516
#, python-format
msgid "The %(name)s \"%(obj)s\" was changed successfully."
msgstr ""

#: views/form.py:165
#, fuzzy, python-format
msgid "The %s was changed successfully."
msgstr "Passwort erfolgreich geändert."

#: views/list.py:199
msgid "Database error"
msgstr "Datenbankfehler"

#: views/list.py:373
#, python-format
msgid "%s List"
msgstr "%s Liste"

#: views/list.py:499
msgid "Sort ASC"
msgstr "Aufsteigend sortieren"

#: views/list.py:500
msgid "Sort DESC"
msgstr "Absteigend sortieren"

#: views/list.py:504
msgid "Cancel Sort"
msgstr "Sortierung abbrechen"

#: views/website.py:16
msgid "Main Dashboard"
msgstr "Hauptübersicht"

#: widgets.py:48
msgid "Now"
msgstr "Jetzt"
