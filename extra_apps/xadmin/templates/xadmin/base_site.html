{% extends 'xadmin/base.html' %}
{% load i18n xadmin_tags %}
{% block title %}{{ title }} | {{ site_title }}{% endblock %}

{% block body %}
  <!-- Header -->
  {% block top-nav %}
  <div id="top-nav" class="navbar navbar-xs navbar-inverse navbar-fixed-top">
    <div class="navbar-header">
      <a class="navbar-brand" href="#">{% block branding %}{{ site_title }}{% endblock %}</a>
    </div>
    <div class="navbar-collapse collapse">
      <ul class="nav navbar-nav pull-right">
        {% view_block 'top_navmenu' %}
        {% if user.is_active and user.is_staff %}
          <li class="dropdown">
            <a class="dropdown-toggle" role="button" data-toggle="dropdown" href="#">
            <strong>{% trans 'Welcome,' %} {% firstof user.first_name user.username %}</strong> <span class="caret"></span></a>
            <ul id="g-account-menu" class="dropdown-menu" role="menu">
              {% view_block 'top_account_menu' %}
            </ul>
          </li>
        {% endif %}
        <li><a href="{% url 'xadmin:logout' %}"><i class="show-sm fa fa-sign-out"></i><span class="hide-sm">{% trans 'Log out' %}</span></a></li>
      </ul>
      {% view_block 'top_navbar' %}
    </div>
  </div>
  {% endblock %}
  <div id="body-content" class="clearfix row">
    <div id="left-side" class="col-sm-1 col-md-2">
        {% block navbar %}
        {% if nav_menu %}
          {% include menu_template %}
        {% else %}
          <p>{% trans "You don't have permission to edit anything." %}</p>
        {% endif %}
      {% endblock %}
      {% view_block 'left_navbar' %}
    </div>

    <div id="content-block" class="col-sm-11 col-md-10">
      {% block breadcrumbs %}
      <ul class="breadcrumb">
      {% if breadcrumbs %}
      {% for bc in breadcrumbs %}
      <li>
        {% if forloop.last or not bc.url %}{{bc.title}}
        {% else %}<a href="{{bc.url}}">{{bc.title}}</a>{% endif %}
      </li>
      {% endfor %}
      {% else %}
      <li><a href="{% url 'xadmin:index' %}">{% trans 'Home' %}</a></li>
      {% if title %}{{ title }}{% endif %}
      {% endif %}
      </ul>
      {% endblock %}

      {% block content-nav %}
      <div class="navbar content-navbar navbar-default navbar-xs" data-toggle="breakpoint"
        data-class-xs="navbar content-navbar navbar-inverse navbar-xs"
        data-class-sm="navbar content-navbar navbar-default navbar-xs">
        <div class="navbar-header">
          {% view_block 'nav_toggles' %}
          {% block nav_toggles %}
          {% include "xadmin/includes/toggle_back.html" %}
          {% endblock %}

          <a class="navbar-brand" data-toggle="collapse" data-target="#top-nav .navbar-collapse">
            {% block nav_title %}{% endblock %}
          </a>
        </div>
        <div class="navbar-collapse collapse">
          <ul class="nav navbar-nav">
            {% view_block 'nav_menu' %}
          </ul>
          {% view_block 'nav_form' %}
          {% block nav_form %}{% endblock %}
          <div class="navbar-btn pull-right hide-xs">
            {% view_block 'nav_btns' %}
            {% block nav_btns %}{% endblock %}
          </div>
        </div>
      </div>
      {% endblock %}

      {% block messages %}
      {% if messages %}
        {% for message in messages %}
        <div class="alert alert-dismissable{% if message.tags %} {% if message.tags == 'error' %}alert-danger{% else %}alert-{{ message.tags }}{% endif %}{% endif %}">
          <button type="button" class="close" data-dismiss="alert">&times;</button>
          {{ message|safe }}
        </div>
        {% endfor %}
      {% endif %}
      {% endblock messages %}

      {% block content %}
      {{ content }}
      {% endblock %}
    </div>
  </div>

  {% block footer %}
  <div id="footer">
    <hr/>
    <footer class="text-center">
        <p>&copy; {% block site_footer %}{{ site_footer }}{% endblock %}</p>
    </footer>
  </div>
  {% endblock %}

{% endblock body %}
