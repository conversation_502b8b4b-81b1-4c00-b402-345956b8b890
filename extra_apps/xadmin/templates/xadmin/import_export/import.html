{% extends 'xadmin/base_site.html' %}
{% load i18n %}
{% load admin_urls %}
{% load import_export_tags %}

{% trans "Import" %}
{% load xadmin_tags %}

{% block breadcrumbs %}
    <ul class="breadcrumb">
        <li><a href="{% url 'xadmin:index' %}">{% trans 'Home' %}</a></li>
        <li><a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a></li>
        <li>{% trans 'Import' %}</li>
    </ul>
{% endblock %}
{% block nav_title %}
    <i class="glyphicon glyphicon-import"></i> {{title}}
{% endblock %}

{% block content %}
{% if confirm_form %}
  <form action="{% url opts|admin_urlname:"process_import" %}" method="POST">
    {% csrf_token %}
    {{ confirm_form.as_p }}
    <p>
      {% trans "Below is a preview of data to be imported. If you are satisfied with the results, click 'Confirm import'" %}
    </p>
    <div class="submit-row">
      <input type="submit" class="default btn btn-primary" name="confirm" value="{% trans "Confirm import" %}">
    </div>
  </form>

{% else %}
  <form action="" method="post" enctype="multipart/form-data">
    {% csrf_token %}

    <p>
      {% trans "This importer will import the following fields: " %}
      <code>{{ fields|join:", " }}</code>
    </p>

    <fieldset class="module aligned">
      {% for field in form %}
        <div class="form-row">
          {{ field.errors }}

          {{ field.label_tag }}

          {{ field }}

          {% if field.field.help_text %}
          <p class="help">{{ field.field.help_text|safe }}</p>
          {% endif %}
        </div>
      {% endfor %}
    </fieldset>

    <div class="submit-row">
      <input type="submit" class="default btn btn-primary" value="{% trans "Submit" %}">
    </div>
  </form>
{% endif %}

{% if result %}

  {% if result.has_errors %}
    <h2>{% trans "Errors" %}</h2>
    <ul>
      {% for error in result.base_errors  %}
      <li>
        {{ error.error }}
        <div class="traceback">{{ error.traceback|linebreaks }}</div>
      </li>
      {% endfor %}
      {% for line, errors in result.row_errors %}
        {% for error in errors %}
          <li>
            {% trans "Line number" %}: {{ line }} - {{ error.error }}
            <div><code>{{ error.row.values|join:", " }}</code></div>
            <div class="traceback">{{ error.traceback|linebreaks }}</div>
          </li>
        {% endfor %}
      {% endfor %}
    </ul>
  {% else %}

  <h2>
    {% trans "Preview" %}
  </h2>
  <table class="table table-bordered table-striped table-hover">
    <thead>
      <tr>
        <th></th>
        {% for field in result.diff_headers %}
          <th>{{ field }}</th>
        {% endfor %}
      </tr>
    </thead>
    {% for row in result.rows %}
    <tr>
      <td>
        {% if row.import_type == 'new' %}
          {% trans "New" %}
        {% elif row.import_type == 'skip' %}
          {% trans "Skipped" %}
        {% elif row.import_type == 'delete' %}
          {% trans "Delete" %}
        {% elif row.import_type == 'update' %}
          {% trans "Update" %}
        {% endif %}
      </td>
      {% for field in row.diff %}
      <td>
        {{ field }}
      </td>
      {% endfor %}
    </tr>
    {% endfor %}
  </table>
  {% endif %}

  {% endif %}
{% endblock %}
