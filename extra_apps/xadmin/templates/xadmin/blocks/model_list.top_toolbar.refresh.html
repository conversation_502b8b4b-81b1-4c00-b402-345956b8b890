{% load i18n %}
<div class="btn-group refresh">
  <a class="dropdown-toggle btn btn-default btn-sm" data-toggle="dropdown" href="#">
    <i class="fa fa-refresh"></i>{% if has_refresh %} <span id="refresh_time">{{current_refresh}}</span>{% endif %} <span class="caret"></span>
  </a>
  <ul class="dropdown-menu" role="menu" aria-labelledby="dLabel">
    {% if has_refresh %}
      <li><a href="{{clean_refresh_url}}"><i class="fa fa-blank"></i> {% trans "Clean Refresh" %}</a></li>
    <li class="divider"></li>
    {% endif %}
    {% for r in refresh_times %}
      <li{% if r.selected %} class="active"{% endif %}>
      <a href="{{r.url}}"><i class="fa fa-time"></i> 
        {% blocktrans with r.time as t %}Every {{ t }} seconds{% endblocktrans %}</a></li>
    {% endfor %}
  </ul>
</div>