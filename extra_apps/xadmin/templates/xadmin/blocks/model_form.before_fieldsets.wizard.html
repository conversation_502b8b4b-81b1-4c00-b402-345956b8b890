{% load i18n %}
{{ wizard.management_form }}
<ul class="steps-nav nav nav-pills">
  {% for step in wizard.steps.all %}
  <li{% if step == wizard.steps.current %} class="active"{% endif %}>
      <a
      {% if wizard.steps.step0 > forloop.counter0 %} onclick="javascript:$('#wizard_goto_step').val('{{forloop.counter0}}').click();"{% endif %}
      >
      {% if wizard.steps.step0 < forloop.counter0 %}<span class="text-muted">{% endif %}
          {{forloop.counter}}. {{step}}{% if step != wizard.steps.last %} <i class="fa fa-caret-right"></i>{% endif %}
      {% if wizard.steps.step0 < forloop.counter0 %}</span>{% endif %}
      </a>
  </li>
  {% endfor %}
</ul>