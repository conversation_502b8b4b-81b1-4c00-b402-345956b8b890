{% load i18n %}
<div class="show-xs">
<button id="wizard_goto_step" name="wizard_goto_step" 
{% if wizard.steps.prev %}
class="btn btn-default col-xs-2"
{% else %}
class="btn btn-default disabled col-xs-2" disabled="disabled" 
{% endif %}
type="submit" value="{{ wizard.steps.step0|add:"-1" }}">&larr;<span> {% trans "Prev step" %}</span></button>

<div class="btn-group col-xs-10">
{% if wizard.steps.last != wizard.steps.current %}
<button name="_save" type="submit" class="btn btn-primary col-xs-12">{% trans "Next step" %} &rarr;</button>
{% else %}
<button name="_save" type="submit" class="btn btn-primary col-xs-12">{% trans "Save" %}</button>
{% endif %}
</div>
</div>
<div class="hide-xs" style="display:inline-block;">
<button id="wizard_goto_step" name="wizard_goto_step" 
{% if wizard.steps.prev %}
class="btn btn-default"
{% else %}
class="btn btn-default disabled" disabled="disabled" 
{% endif %}
type="submit" value="{{ wizard.steps.step0|add:"-1" }}">&larr;<span> {% trans "Prev step" %}</span></button>

{% if wizard.steps.last != wizard.steps.current %}
<button name="_save" type="submit" class="btn btn-primary">{% trans "Next step" %} &rarr;</button>
{% else %}
<button name="_save" type="submit" class="btn btn-primary">{% trans "Save" %}</button>
{% endif %}
</div>