{% load i18n xadmin_tags %}
{% autoescape off %}
{% if search_models %}
<form method="get" class="top-search-form navbar-form navbar-left" id="g-search">
  <div class="input-group">
    <input name="{{ search_name }}" class="form-control" type="text">
    <span class="input-group-btn">
      <button class="btn btn-default dropdown-toggle" data-toggle="dropdown">
        <i class="fa fa-search"></i> {% trans "Search" %} <span class="caret"></span>
      </button>
      <ul class="dropdown-menu">
      {% for m in search_models %}
          <li><a data-action="{{m.url}}"><i class="fa fa-search"></i> {{m.title}}</a></li>
      {% endfor %}
      </ul>
    </span>
  </div>
</form>
{% endif %}
{% if add_models %}
<li class="dropdown g-add">
  <a class="dropdown-toggle" role="button" data-toggle="dropdown" href="#">
  <i class="fa fa-plus"></i> <span class="hide-sm">{% trans "Add" %} <i class="caret"></i></span></a>
  <ul id="g-add-menu" class="dropdown-menu" role="menu">
    {% for m in add_models %}
        <li><a href="{{m.url}}"><i class="fa fa-plus"></i> {{m.title}}</a></li>
    {% endfor %}
  </ul>
</li>
{% endif %}
{% endautoescape %}
