{% extends "xadmin/views/model_form.html" %}
{% load i18n %}

{% load xadmin_tags %}
{% load crispy_forms_tags %}

{% block breadcrumbs %}
{% if account_view %}
<ul class="breadcrumb">
  <li><a href="{% url 'xadmin:index' %}">{% trans 'Home' %}</a></li>
  <li class="active">{% trans 'Change password' %}</li>
</ul>
{% else %}
<ul class="breadcrumb">
  <li><a href="{% url 'xadmin:index' %}">{% trans 'Home' %}</a></li>
  <li>
    {% if has_view_permission %}
    <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
    {% else %}{{ opts.verbose_name_plural|capfirst }}{% endif %}
  </li>
  <li><a href="{% url opts|admin_urlname:'change' original.pk %}">{{ original|truncatewords:"18" }}</a></li>
  <li class="active">{% trans 'Change password' %}</li>
</ul>
{% endif %}
{% endblock %}

{% block content %}
<form action="{{ form_url }}" class="exform" method="post" id="{{ opts.model_name }}_form">{% csrf_token %}{% block form_top %}{% endblock %}
  {% if errors %}
    <div class="alert alert-danger">
    {% blocktrans count counter=errors|length %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktrans %}
    </div>
    {{ form.non_field_errors }}
  {% endif %}

  <div class="alert alert-info">
    {% if account_view %}
    {% trans "Enter your new password." %}
    {% else %}
    {% blocktrans with username=original.username %}Enter a new password for the user <strong>{{ username }}</strong>.{% endblocktrans %}
    {% endif %}
  </div>

  <div class="form-container row" >
    <div class="col-sm-12 form-horizontal">
      <div class="panel panel-default fieldset">
        <div class="panel-body">
        {% crispy form %}
        </div>
      </div>
    </div>
  </div>

  <div class="form-actions well well-sm clearfix">
  <input class="btn btn-primary" type="submit" value="{% trans 'Change password' %}" class="default" />
  </div>
</form>
<script type="text/javascript">document.getElementById("id_old_password").focus();</script>
{% endblock %}
