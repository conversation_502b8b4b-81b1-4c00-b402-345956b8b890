{% extends "xadmin/views/model_form.html" %}
{% load i18n %}

{% load xadmin_tags %}

{% block breadcrumbs %}
<ul class="breadcrumb">
  <li><a href="{% url 'xadmin:index' %}">{% trans 'Home' %}</a></li>
  <li>
    {% if has_view_permission %}
    <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
    {% else %}{{ opts.verbose_name_plural|capfirst }}{% endif %}
  </li>
  <li><a href="{% url opts|admin_urlname:'recoverlist' %}">{% blocktrans with opts.verbose_name_plural as name %}Recover deleted {{name}}{% endblocktrans %}</a> </li>
  <li class="active">{{ title }}</li>
</ul>
{% endblock %}

{% block content %}
    <div class="alert alert-info">{% blocktrans %}Press the recover button below to recover this version of the object.{% endblocktrans %}</div>
    {{block.super}}
{% endblock %}

{% block submit_buttons_bottom %}
<div class="form-actions well well-sm">
    <button type="submit" class="default btn btn-primary"><i class="fa fa-undo"></i> {% trans 'Recover' %}</button>
</div>
{% endblock %}