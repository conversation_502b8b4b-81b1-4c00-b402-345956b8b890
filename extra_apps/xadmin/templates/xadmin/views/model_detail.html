{% extends base_template %}
{% load i18n %}

{% load xadmin_tags %}
{% load crispy_forms_tags %}

{% block bodyclass %}{{ opts.app_label }}-{{ opts.object_name.lower }} detail{% endblock %}

{% block nav_title %}
  {% if model_icon %}<i class="{{model_icon}}"></i> {% endif %}{{ object|truncatewords:"18" }}
{% endblock %}

{% block nav_toggles %}
{% include "xadmin/includes/toggle_back.html" %}
{% if has_change_permission %}
<a href="{% url opts|admin_urlname:'change' object.pk %}" class="navbar-toggle pull-right"><i class="fa fa-pencil"></i></a>
{% endif %}
{% if has_delete_permission %}
<a href="{% url opts|admin_urlname:'delete' object.pk %}" class="navbar-toggle pull-right"><i class="fa fa-trash-o"></i></a>
{% endif %}
{% endblock %}

{% block nav_btns %}
  {% if has_change_permission %}
  <a href="{% url opts|admin_urlname:'change' object.pk %}" class="btn btn-primary"><i class="fa fa-pencil"></i> <span>{% trans "Edit" %}</span></a>
  {% endif %}
  {% if has_delete_permission %}
  <a href="{% url opts|admin_urlname:'delete' object.pk %}" class="btn btn-danger"><i class="fa fa-trash-o"></i> <span>{% trans "Delete" %}</span></a>
  {% endif %}
{% endblock %}

{% block content %}
  {% view_block 'before_fieldsets' %}
  {% crispy form %}
  {% view_block 'after_fieldsets' %}
{% endblock %}
