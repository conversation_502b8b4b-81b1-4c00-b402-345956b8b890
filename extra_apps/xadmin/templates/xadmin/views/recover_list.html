{% extends base_template %}
{% load i18n %}

{% load xadmin_tags %}

{% block breadcrumbs %}
<ul class="breadcrumb">
  <li><a href="{% url 'xadmin:index' %}">{% trans 'Home' %}</a></li>
  <li><a href="{{changelist_url}}">{{opts.verbose_name_plural|capfirst}}</a></li>
  <li class="active">{% blocktrans with opts.verbose_name_plural|escape as name %}Recover deleted {{name}}{% endblocktrans %}</li>
</ul>
{% endblock %}

{% block nav_title %}
  <i class="fa fa-trash-o"></i> {{title}}
{% endblock %}

{% block content %}
  <div class="alert alert-info">{% blocktrans %}Choose a date from the list below to recover a deleted version of an object.{% endblocktrans %}</div>
  <div class="module">
    {% if deleted %}
      <table id="change-history" class="table table-bordered table-hover">
        <thead>
        <tr>
          <th scope="col">{% trans 'Date/time' %}</th>
          <th scope="col">{{opts.verbose_name|capfirst}}</th>
        </tr>
        </thead>
        <tbody>
          {% for deletion in deleted %}
            <tr>
              <th scope="row"><a href="{{deletion.pk}}/">{{deletion.revision.date_created}}</a></th>
              <td>{{deletion.object_repr}}</td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% else %}
      <p class="well">{% trans "There are no deleted objects to recover." %}</p>
    {% endif %}
  </div>
{% endblock %}

