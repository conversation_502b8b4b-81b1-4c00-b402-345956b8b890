{% extends base_template %}
{% load i18n %}

{% load xadmin_tags %}

{% block nav_title %}{{title}}{% endblock %}

{% block content %}
{% if perms_lacking or protected %}
    {% if perms_lacking %}
        <div class="alert alert-danger">{% blocktrans with escaped_object=object %}Deleting the {{ verbose_name }} '{{ escaped_object }}' would result in deleting related objects, but your account doesn't have permission to delete the following types of objects:{% endblocktrans %}</div>
        <ul class="model_ul">
        {% for obj in perms_lacking %}
            <li>{{ obj }}</li>
        {% endfor %}
        </ul>
    {% endif %}
    {% if protected %}
        <div class="alert alert-danger">{% blocktrans with escaped_object=object %}Deleting the {{ verbose_name }} '{{ escaped_object }}' would require deleting the following protected related objects:{% endblocktrans %}</div>
        <ul class="model_ul">
        {% for obj in protected %}
            <li>{{ obj }}</li>
        {% endfor %}
        </ul>
    {% endif %}
{% else %}
    <div class="alert alert-warning">{% blocktrans with escaped_object=object %}Are you sure you want to delete the {{ verbose_name }} "{{ escaped_object }}"? All of the following related items will be deleted:{% endblocktrans %}</div>
    <ul class="model_ul">{{ deleted_objects|unordered_list }}</ul>
    <form action="" method="post">{% csrf_token %}
    <div>
    <input type="hidden" name="post" value="yes" />
    {% view_block 'form_fields' %}
    <div class="form-actions well well-sm">
        <input class="btn btn-danger btn-lg" type="submit" value="{% trans "Yes, I'm sure" %}" />
        <a class="btn btn-default pull-right" onclick="javascript:history.back();" >{% trans 'Cancel' %}</a>
    </div>
    </div>
    </form>
{% endif %}
{% endblock %}
