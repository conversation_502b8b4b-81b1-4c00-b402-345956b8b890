{% load i18n %}
<li class="nav-header ">{{title}} <i class="icon-chevron-right pull-right"></i></li>
{% for choice in choices %}
    <li class="filter-multiselect">
    	<a class="small filter-item" {% if choice.selected %} href="{{ choice.remove_query_string|iriencode }}" {% else %} href="{{ choice.query_string|iriencode }}" {% endif %} data-toggle="tooltip" data-placement="right" title="{{ choice.display }}">
    		<input class="filter-col-1" type="checkbox" {% if choice.selected %} checked="checked"{% endif %}>
    		<span class="filter-col-2">{{ choice.display }}</span>
    	</a>
    </li>
{% endfor %}
