{% load i18n %}
<li class="dropdown-submenu filter-date">
  <a><i class="fa fa-filter {% if spec.is_used %}text-success{%else%}text-muted{% endif %}"></i> {{ title }}</a>
  <ul class="dropdown-menu">
    {% for choice in choices %}
      <li{% if choice.selected %} class="active"{% endif %}>
      <a href="{{ choice.query_string|iriencode }}">{{ choice.display }}</a></li>
    {% endfor %}
    <li class="dropdown-submenu menu-choice-date{% if choice_selected %} active{% endif %}">
      <a>{% trans "Choice Date" %}</a>
      <div class="popover right">
        <div class="arrow"></div>
        <h3 class="popover-title">{% trans "Choice Date" %}</h3>
        <div class="popover-content">
          <form method="get" action="" class="form-inline">
            {{ form_params|safe }}
            <div class="input-group">
            <span class="input-group-addon">{% trans "YY" %}</span>
            <input name="{{year_name}}" class="choice-year form-control" type="text" placeholder="{% trans "year" %}" size="4" value="{{year_val}}">
            </div>
            <div class="input-group">
            <span class="input-group-addon">{% trans "MM" %}</span>
            <input name="{{month_name}}" class="choice-month form-control" type="text" placeholder="{% trans "month" %}" size="2" value="{{month_val}}">
            </div>
            <div class="input-group">
            <span class="input-group-addon">{% trans "DD" %}</span>
            <input name="{{day_name}}" class="choice-day form-control" type="text" placeholder="{% trans "day" %}" size="2" value="{{day_val}}">
            </div>
            <button class="btn btn-success btn-block" type="submit">{% trans "Apply" %}</button>
          </form>
        </div>
      </div>
    </li>
    <li class="dropdown-submenu menu-date-range"><a>{% trans "Date Range" %}</a>
      <div class="popover bottom">
        <div class="arrow"></div>
        <div class="popover-content row">
        <form method="get" action="" class="clearfix">
          <div class="ranges col-sm-3 hide-xs">
            <fieldset class="range_inputs">
              <h4>{% trans "Select Date" %}</h4>
              <label for="{{since_name}}">{% trans "From" %}</label>
              <input class="form-control start_input" type="text" name="{{since_name}}" value="{{since_val}}" />
              <label for="{{until_name}}">{% trans "To" %}</label>
              <input class="form-control end_input" type="text" name="{{until_name}}" value="{{until_val}}" />
              <button type="submit" class="btn btn-success btn-block">{% trans "Apply" %}</button>
            </fieldset>
          </div>
          <div class="col-sm-9">
            <div class="calendar date-start col-sm-6" data-date="{{since_val}}"></div>
            <div class="calendar date-end col-sm-6" data-date="{{until_val}}"></div>
          </div>
          <div class="col-xs-12">
            <button type="submit" class="btn btn-success btn-block show-xs">{% trans "Apply" %}</button>
          </div>
          {{ form_params|safe }}
        </form>
        </div>
      </div>
    </li>
  </ul>
</li>