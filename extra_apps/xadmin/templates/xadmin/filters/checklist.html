{% load i18n %}
<li class="dropdown-submenu filter-multiselect">
  <a><i class="icon-filter {% if spec.is_used %}text-success{%else%}text-muted{% endif %}"></i> {{ title }}</a>
  <ul class="dropdown-menu">
    {% for choice in choices %}
        <li{% if choice.selected %} class="active"{% endif %}>
        	<a href="{% if choice.selected %}{{ choice.remove_query_string|iriencode }}{% else %}{{ choice.query_string|iriencode }}{% endif %}">
        		<input type="checkbox" {% if choice.selected %} checked="checked"{% endif %}>
        		{{ choice.display }}
        	</a>
        </li>
    {% endfor %}
  </ul>
</li>