{% load i18n %}
<li class="dropdown-submenu filter-number">
  <a><i class="fa fa-filter {% if spec.is_used %}text-success{%else%}text-muted{% endif %}"></i> {{ title }}</a>
  <div class="popover right">
    <div class="arrow"></div>
    <h3 class="popover-title">
      {% trans "Search" %} {{title}}
    </h3>
    <div class="popover-content">
      <form method="get" action="">
        {{ form_params|safe }}
          <div class="input-group">
            <span class="input-group-addon">=</span>
            <span class="input-group-btn">
              <a class="btn btn-default toggle{%if ne_val%} active{%endif%}" data-toggle="button" data-off-name="{{equal_name}}" data-on-name="{{ne_name}}">!</a>
            </span>
            <input name="{%if ne_val %}{{ne_name}}{% else %}{{equal_name}}{%endif%}" class="form-control" type="number" value="{%if ne_val %}{{ne_val}}{% else %}{{equal_val}}{%endif%}" placeholder="{% trans "Enter Number" %}"/>
            <span class="input-group-btn"><a class="btn btn-default remove">x</a></span>
          </div>
          <div class="input-group">
            <span class="input-group-addon">></span>
            <span class="input-group-btn">
              <a class="btn btn-default toggle{%if gte_val%} active{%endif%}" data-toggle="button" data-off-name="{{gt_name}}" data-on-name="{{gte_name}}">=</a>
            </span>
            <input name="{%if gte_val %}{{gte_name}}{% else %}{{gt_name}}{%endif%}" class="form-control" type="number" value="{%if gte_val %}{{gte_val}}{% else %}{{gt_val}}{%endif%}" placeholder="{% trans "Enter Number" %}"/>
            <span class="input-group-btn"><a class="btn btn-default remove">x</a></span>
          </div>
          <div class="input-group">
            <span class="input-group-addon"><</span>
            <span class="input-group-btn">
              <a class="btn btn-default toggle{%if lte_val%} active{%endif%}" data-toggle="button" data-off-name="{{lt_name}}" data-on-name="{{lte_name}}">=</a>
            </span>
            <input name="{%if lte_val %}{{lte_name}}{% else %}{{lt_name}}{%endif%}" class="form-control" type="number" value="{%if lte_val %}{{lte_val}}{% else %}{{lt_val}}{%endif%}" placeholder="{% trans "Enter Number" %}"/>
            <span class="input-group-btn"><a class="btn btn-default remove">x</a></span>
          </div>

          <button type="submit" class="btn btn-success">{% trans "Apply" %}</button>
        {% if spec.used_params %}
          <a class="btn btn-default" href="{{remove_url}}">{% trans "Clean" %}</a>
        {% endif %}
      </form>
    </div>
  </div>
</li>