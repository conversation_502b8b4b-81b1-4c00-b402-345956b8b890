{% load i18n %}
<li class="dropdown-submenu filter-fk-search">
  <a><i class="fa fa-filter {% if spec.is_used %}text-success{%else%}text-muted{% endif %}"></i> {{ title }}</a>
  <div class="popover right">
    <div class="arrow"></div>
    <h3 class="popover-title">
      {% trans "Search" %} {{title}}
    </h3>
    <div class="popover-content">
      <form class="exform" method="get" action="">
        {{ form_params|safe }}
          <select name="{{exact_name}}" class="select-search {% if relfield_style == 'fk-select' %}select-preload{% endif %}" data-search-url="{{search_url}}" data-choices="{{ choices }}"
          {% if relfield_style == 'fk-select' %}
          data-placeholder="{% trans "Select" %} {{title}}…"
          {% else %}
          data-placeholder="{% trans "Search" %} {{title}}…"
          {% endif %}
          >
          {% if exact_val %}
            <option value="{{exact_val}}" selected="selected">{{label}}</option>
          {% endif %}
          </select>

          <button type="submit" class="btn btn-success">{% trans "Apply" %}</button>
        {% if spec.used_params %}
          <a class="btn btn-default" href="{{remove_url}}">{% trans "Clean" %}</a>
        {% endif %}
      </form>
    </div>
  </div>
</li>
