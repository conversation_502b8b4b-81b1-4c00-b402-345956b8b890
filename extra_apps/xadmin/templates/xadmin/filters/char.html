{% load i18n %}
<li class="dropdown-submenu filter-char">
  <a><i class="fa fa-filter {% if spec.is_used %}text-success{%else%}text-muted{% endif %}"></i> {{ title }}</a>
  <div class="popover right">
    <div class="arrow"></div>
    <h3 class="popover-title">
      {% trans "Search" %} {{title}}
    </h3>
    <div class="popover-content">
      <form method="get" action="">
        {{ form_params|safe }}
        <div class="input-group">
          <input name="{{search_name}}" class="input-char form-control" type="text" value="{{search_val}}" placeholder="{% trans "Enter" %} {{title}}…"/>
          <span class="input-group-btn">
          {% if search_val %}
            <a class="btn btn-default" href="{{remove_url}}">x</a>
          {% endif %}
            <button class="btn btn-success" type="submit"><i class="fa fa-search"></i></button>
          </span>
        </div>
      </form>
    </div>
  </div>
</li>