{% extends "xadmin/includes/box.html" %}
{% load i18n xadmin_tags %}
{% load crispy_forms_tags %}

{% block box_attrs %}id="{{ widget_id }}"{% endblock box_attrs %}
{% block box_class %}widget {{widget_type}}{% endblock box_class %}

{% block box_title %}
  {% if widget.dashboard.widget_customiz %}
    <i class='icon fa fa-wrench pull-right' data-toggle="modal" data-target="#{{ widget_id }}-opts-form"></i>
  {% endif %}
  {% block title %}
    <i class='{{widget_icon}}'></i>
    {{ widget_title }}
  {% endblock title %}
{% endblock box_title %}

{% block box_content %}
  {% block content %}
  {{ content|safe }}
  {% endblock content %}
{% endblock box_content %}

{% block box_extra %}
<div class="modal fade widget_options" tabindex="-1" role="dialog" id="{{ widget_id }}-opts-form">
  <div class="modal-content">
  <form method="post" class="box-form form-horizontal short_label widget-form exform">
  {% csrf_token %}
  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h4 class="modal-title">{% trans "Widget Options" %}</h4>
  </div>
  <div class="modal-body">
    {% block form %}
      {% crispy form %}
    {% endblock form %}
    <input type="hidden" name="_delete"/>
  </div>
  <div class="modal-footer">
    <a class="btn btn-danger pull-left btn-remove">{% trans "Remove" %}</a>
    <a class="btn btn-default" data-dismiss="modal">{% trans "Close" %}</a>
    <input class="btn btn-primary" type="submit" value="{% trans 'Save changes' %}"/>
  </div>
  </form>
  </div>
  </div>
{% endblock box_extra %}
