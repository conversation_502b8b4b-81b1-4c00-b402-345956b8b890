{% load crispy_forms_field %}

{% if field.is_hidden %}
  {{ field }}
{% else %}
  <td class="control-group {% if field.errors %}error{% endif %} field {% if field.field.name %} field-{{ field.field.name }}{% endif %}">
  {% if field.is_readonly %}
      <p>{{ field.contents }}</p>
  {% else %}
      {% crispy_field field %}
      {% if field.errors %}
          {% for error in field.errors %}
              <span class='help-inline'><strong>{{ error }}</strong></span>
          {% endfor %}
      {% endif %}
  {% endif %}
  </td>
{% endif %}
