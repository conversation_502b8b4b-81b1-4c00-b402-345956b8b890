{% load crispy_forms_field %}

{% if field.is_hidden %}
    {{ field }}
{% else %}
    <div id="div_{{ field.auto_id }}" class="form-group{% if wrapper_class %} {{ wrapper_class }}{% endif %}{% if form_show_errors and field.errors %} has-error{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}">

        {% if field.label and form_show_labels %}
            <label for="{{ field.id_for_label }}" class="control-label {{ label_class }}{% if field.field.required %} requiredField{% endif %}">
                {{ field.label|safe }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
            </label>
        {% endif %}

        <div class="controls {{ field_class }}">
            <div class="input-group {{classes}}">
            {% for input in inputs %}
              {% if input == '@@' %}
                {% crispy_field field %}
              {% else %}
                 <span class="input-group-addon">{{input|safe}}</span>
              {% endif %}
            {% endfor %}
            </div>

            {% include 'bootstrap3/layout/help_text_and_errors.html' %}
        </div>
    </div>
{% endif %}

