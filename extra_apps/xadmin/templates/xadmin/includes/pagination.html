{% load i18n %}
  <li><span><span class="text-success">{{ cl.result_count }}</span> {% ifequal cl.result_count 1 %}{{ cl.opts.verbose_name }}{% else %}{{ cl.opts.verbose_name_plural }}{% endifequal %}</span></li>
  {% if pagination_required %}
    {% for num in page_range %}
        <li>{{ num }}</li>
    {% endfor %}
  {% endif %}
  {% if show_all_url %}
    <li><a href="{{ show_all_url }}" class="showall">{% trans 'Show all' %}</a></li>
  {% endif %}
