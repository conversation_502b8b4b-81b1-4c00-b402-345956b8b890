{% extends "xadmin/edit_inline/base.html" %}
{% load i18n xadmin_tags crispy_forms_tags %}

{% block box_content_class %}{{ block.super }} panel-group{% endblock box_content_class %}

{% block formset_form %}
  <div class="panel formset-row row fieldset">
    <div class="panel-heading">
      {% if formset.formset.can_delete %}
        <a class="delete-row pull-right"><i class="fa fa-times"></i></a>
      {% endif %}
      <a class="panel-toggle" data-toggle="collapse" data-parent="#{{ formset.css_id }}-{{inline_style}}" href="#{{ prefix }}-{{ forloop.counter0 }}-body"><b>{{ formset.opts.verbose_name|title }}:</b>&nbsp;{% if fs.instance.pk %}{{ fs.instance }}{% else %}#<span class="formset-num">{{ forloop.counter }}</span>{% endif %}
      </a>
    </div>
    <div class="panel-collapse collapse" id="{{ prefix }}-{{ forloop.counter0 }}-body">
      <div class="panel-body formset-form">
        {% crispy fs formset.formset.helper %}
      </div>
    </div>
  </div>
{% endblock formset_form %}
{% block formset_empty %}
  <div class="panel formset-row row fieldset">
    <div class="panel-heading">
      {% if formset.formset.can_delete %}
          <a class="delete-row pull-right"><i class="fa fa-times"></i></a>
      {% endif %}
      <a class="panel-toggle" data-toggle="collapse" data-parent="#{{ formset.css_id }}-{{inline_style}}" href="#{{ prefix }}-__prefix__-body">
        <b>{{ formset.opts.verbose_name|title }}:</b>&nbsp;{% if fs.instance.pk %}{{ fs.instance }}{% else %}#<span class="formset-num">{{ forloop.counter }}</span>{% endif %}
      </a>
    </div>
    <div class="panel-collapse collapse" id="{{ prefix }}-__prefix__-body">
      <div class="panel-body formset-form">
        {% crispy formset.formset.empty_form formset.formset.helper %}
      </div>
    </div>
  </div>
{% endblock formset_empty %}