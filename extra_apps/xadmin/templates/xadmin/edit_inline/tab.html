{% extends "xadmin/edit_inline/base.html" %}
{% load i18n xadmin_tags crispy_forms_tags %}

{% block box_content_class %}tabs{% endblock box_content_class %}
{% block box_content_attrs %}id="{{ formset.css_id }}-tabs"{% endblock box_content_attrs %}

{% block formset_content %}
  <ul class="nav nav-tabs">
    {% for fs in formset.formset %}
    <li{% if forloop.first%} class="active"{% endif %}>
      <a href="#{{ prefix }}-{{ forloop.counter0 }}-body" data-toggle="tab"{% if fs.instance.pk %} title="{{ fs.instance }}"{% endif %}>
      {% if fs.instance.pk %}{{ fs.instance|truncatechars:12 }}{% else %}#<span class="formset-num">{{ forloop.counter }}</span>{% endif %}
    </a></li>
    {% endfor %}
  </ul>
  <div class="tab-content formset-content" data-prefix="{{ prefix }}" data-style="tab">
  {% for fs in formset.formset %}
    <div class="formset-row tab-pane col-xs-12{% if forloop.first%} active{% endif %}" id="{{ prefix }}-{{ forloop.counter0 }}-body">
      {% if formset.formset.can_delete %}
        <a class="delete-row pull-right"><i class="fa fa-times"></i></a>
      {% endif %}
      {% crispy fs formset.formset.helper %}
    </div>
  {% endfor %}
  </div>
{% endblock formset_content %}

{% block formset_empty %}
  <div class="formset-row tab-pane col-xs-12" data-replace-id="{{ prefix }}-__prefix__-body">
      {% if formset.formset.can_delete %}
        <a class="delete-row pull-right"><i class="fa fa-times"></i></a>
      {% endif %}
    {% crispy formset.formset.empty_form formset.formset.helper %}
  </div>
{% endblock formset_empty %}
