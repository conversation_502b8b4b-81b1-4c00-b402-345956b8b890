

@media (min-width: 768px) and (max-width: 991px) {
  ul.nav-sitemenu{
    padding: 0px;
  }
  ul.nav-sitemenu > li.app_menu>a{
    padding: 3px 0px;
    margin: 0px;
    text-align: center;
  }
  ul.nav-sitemenu .dropdown-menu{
    z-index: 1100;
  }
  ul.nav-sitemenu > li.app_menu>a:after{
    display: none;
  }
  ul.nav-sitemenu > li.app_menu>hr{ margin: 0px;}
  ul.nav-sitemenu > li.app_menu>a>i.icon{
    font-size: 20px;
    line-height: 1.7em;
  }
  ul.nav-sitemenu > li.app_menu li {

  }
}

@media (min-width: 768px) {
  #content-block:not(.full-content) {
    padding-left: 5px;
    min-height: 450px;
  }
  .form-actions .more-btns{
    display: inline-block !important;
  }
  .form-actions .hidden-sm{
    display: inline-block !important;
  }
  .form-actions .col-1,
  .form-actions .col-2,
  .form-actions .col-3,
  .form-actions .col-4,
  .form-actions .col-5,
  .form-actions .col-6,
  .form-actions .col-7,
  .form-actions .col-8,
  .form-actions .col-9,
  .form-actions .col-10,
  .form-actions .col-11,
  .form-actions .col-12
  {
    width: auto !important;
  }

  .quick-form .modal-dialog {
    width: auto;
  }
  .detail-modal .modal-dialog {
    width: 80%;
  }
}

@media (max-width: 767px) {
#body-content {
  margin-top: 50px; 
  padding-top: 10px;
}
#body-content.show_menu{
  width: 100%;
  overflow-x: hidden;
}
#footer, .breadcrumb {
  display: none;
}
#top-nav .navbar-nav {
  float: none;
}
#left-side {
  min-height: 0px;
  position: fixed;
  top: 65px;
  left: 0px;
  bottom: 0px;
  background: #FFF;
  z-index: 999;
  overflow: auto;
  display: none;
  width: 70%;
}
#body-content.show_menu #left-side {
  display: block;
}
#body-content.show_menu #content-block{
  margin-left: 70%;
  width: 100%;
}
#left-side .well {
  border: 0px;
}
#changelist-form {
  clear: both;
}
.popover.dropdown-menu form > .btn {
  width: 100%;
}
.navbar .navbar-brand {
  max-width: 260px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.panel-content.nopadding {
  margin: -15px;
}

.panel-content.nopadding .table {
  margin-bottom: 0;
}

.form-actions {
  margin-bottom: 0px;
  padding: 10px;
  position:fixed;
  bottom:0px;
  z-index:295;
  width: 100%;
  margin-right: -15px;
  margin-left: -15px;
  border: 0px;
  border-radius: 0px;
}
.form-actions .btn-group {
  padding-left: 10px;
  padding-right: 0px;
}
.form-actions .more-btns{
  clear: both;
  overflow: hidden;
}
.form-actions .more-btns .btn:first-child{
}
.form-actions .more-btns .btn, .form-actions .more-btns .btn-group {
  display: block;
  width: 100%;
  padding-right: 0;
  padding-left: 0;
}
.form-actions .more-btns .btn,
.form-actions .more-btns .btn-group {
  margin-top: 5px;
}

#body-content {
  padding-bottom: 60px;
}
.model_ul {
  margin-left: 0px;
  margin-right: 0px;
}
.navbar.content-navbar {
  position: fixed;
  right: 0;
  left: 0;
  top: 0px;
  z-index: 1030;
  border-radius: 0;
}
.content-navbar .navbar-toggle{
  padding: 3px 10px;
  font-size: 20px;
  color: white;
  width: auto;
  height: auto;
}
.navbar-toggle.pull-left{
  margin-left: 15px;
  margin-right: 0px;
}
.btn span{
  display: none;
}
.navbar-btn {
  position: absolute;
  top: 0px;
  right: 10px;
}
.navbar-nav > .dropdown > .dropdown-menu{
  position: relative;
  float: none;
  max-height: 400px;
  overflow: auto;
}
.navbar-nav > .dropdown > .dropdown-menu li>a{
  padding: 8px 20px;
}
.navbar-nav > .dropdown .dropdown-submenu > .dropdown-menu,
.navbar-nav > .dropdown .dropdown-submenu > .popover{
  position: relative;
  top: 0px;
  left: 0px;
  margin: 10px;
  float: none;
}
.navbar-nav .dropdown-submenu .popover {
  max-width: none;
}
.navbar-nav .dropdown-submenu .popover .arrow {
  display: none;
}

.layout-btns {
  display: none;
}
.pagination.pagination-small{
  display: none;
}

/* search form */
.input-group.search-group{
  max-width: none;
  width: auto;
}

}

@media (max-width: 767px) {
  .show-sm, .show-md, .show-lg, .hide-xs{
    display: none !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .show-xs, .show-md, .show-lg, .hide-sm{
    display: none !important;
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .show-xs, .show-sm, .show-lg, .hide-md{
    display: none !important;
  }
}

@media (min-width: 1200px) {
  .show-xs, .show-sm, .show-md, .hide-lg{
    display: none !important;
  }
}
