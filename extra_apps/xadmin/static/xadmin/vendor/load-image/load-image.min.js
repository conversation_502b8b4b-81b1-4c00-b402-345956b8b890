(function(e){"use strict";var t=function(e,a,n){var r,i,c=document.createElement("img");if(c.onerror=a,c.onload=function(){!i||n&&n.noRevoke||t.revokeObjectURL(i),a&&a(t.scale(c,n))},t.isInstanceOf("Blob",e)||t.isInstanceOf("File",e))r=i=t.createObjectURL(e),c._type=e.type;else{if("string"!=typeof e)return!1;r=e,n&&n.crossOrigin&&(c.crossOrigin=n.crossOrigin)}return r?(c.src=r,c):t.readFile(e,function(e){var t=e.target;t&&t.result?c.src=t.result:a&&a(e)})},a=window.createObjectURL&&window||window.URL&&URL.revokeObjectURL&&URL||window.webkitURL&&webkitURL;t.isInstanceOf=function(e,t){return Object.prototype.toString.call(t)==="[object "+e+"]"},t.detectSubsampling=function(e){var t,a,n=e.width,r=e.height;return n*r>1048576?(t=document.createElement("canvas"),t.width=t.height=1,a=t.getContext("2d"),a.drawImage(e,-n+1,0),0===a.getImageData(0,0,1,1).data[3]):!1},t.detectVerticalSquash=function(e,t){var a,n,r,i,c,o=document.createElement("canvas"),d=o.getContext("2d");for(o.width=1,o.height=t,d.drawImage(e,0,0),a=d.getImageData(0,0,1,t).data,n=0,r=t,i=t;i>n;)c=a[4*(i-1)+3],0===c?r=i:n=i,i=r+n>>1;return i/t||1},t.renderImageToCanvas=function(e,a,n,r){var i,c,o,d,g,h,s,u,l=e.width,m=e.height,f=a.getContext("2d"),w=1024,v=document.createElement("canvas");for(f.save(),t.detectSubsampling(e)&&(l/=2,m/=2),i=t.detectVerticalSquash(e,m),v.width=v.height=w,c=v.getContext("2d"),o=Math.ceil(w*n/l),d=Math.ceil(w*r/m/i),h=0,u=0;m>u;){for(g=0,s=0;l>s;)c.clearRect(0,0,w,w),c.drawImage(e,-s,-u),f.drawImage(v,0,0,w,w,g,h,o,d),s+=w,g+=o;u+=w,h+=d}f.restore(),v=c=null},t.scale=function(e,a){a=a||{};var n=document.createElement("canvas"),r=e.width,i=e.height,c=Math.max((a.minWidth||r)/r,(a.minHeight||i)/i);return c>1&&(r=Math.ceil(r*c),i=Math.ceil(i*c)),c=Math.min((a.maxWidth||r)/r,(a.maxHeight||i)/i),1>c&&(r=Math.ceil(r*c),i=Math.ceil(i*c)),e.getContext||a.canvas&&n.getContext?(n.width=r,n.height=i,"image/jpeg"===e._type?t.renderImageToCanvas(e,n,r,i):n.getContext("2d").drawImage(e,0,0,r,i),n):(e.width=r,e.height=i,e)},t.createObjectURL=function(e){return a?a.createObjectURL(e):!1},t.revokeObjectURL=function(e){return a?a.revokeObjectURL(e):!1},t.readFile=function(e,t){if(window.FileReader&&FileReader.prototype.readAsDataURL){var a=new FileReader;return a.onload=a.onerror=t,a.readAsDataURL(e),a}return!1},"function"==typeof define&&define.amd?define(function(){return t}):e.loadImage=t})(this);