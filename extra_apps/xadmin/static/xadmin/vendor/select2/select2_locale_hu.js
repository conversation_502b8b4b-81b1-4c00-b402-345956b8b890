/**
 * Select2 Hungarian translation
 */
(function ($) {
    "use strict";

    $.extend($.fn.select2.defaults, {
        formatNoMatches: function () { return "<PERSON>nc<PERSON> találat."; },
        formatInputTooShort: function (input, min) { var n = min - input.length; return "T<PERSON> rövid. Még " + n + " karakter <PERSON>."; },
        formatInputTooLong: function (input, max) { var n = input.length - max; return "T<PERSON> hossz<PERSON>. " + n + " kerekterrel több mint kellene."; },
        formatSelectionTooBig: function (limit) { return "Csak " + limit + " elemet lehet kiv<PERSON>las<PERSON>tani."; },
        formatLoadMore: function (pageNumber) { return "Töltés..."; },
        formatSearching: function () { return "Keresés..."; }
    });
})(jQuery);
