# eladmin-qt

eladmin 前端源码

[//]: # (sudo n 10.24.0)
sudo n 12.16.0

#### 后端源码
- 码云: [https://gitee.com/elunez/eladmin](https://gitee.com/elunez/eladmin)
- github: [https://github.com/elunez/eladmin](https://github.com/elunez/eladmin)

#### eladmin开发文档
[http://docs.auauz.net/#/](http://docs.auauz.net/#/)

## Build Setup
``` bash
# 安装依赖
npm install

#
npm config set registry https://registry.npmmirror.com

# 启动服务 localhost:8013
npm run dev

# 构建生产环境
npm run build
```

#### 反馈交流

- QQ交流群：<a target="_blank" href="//shang.qq.com/wpa/qunwpa?idkey=90830191a40600e3a07acdcc4864890fca50c8e3ca1772e7e288a561d576f6c4"><img border="0" src="//pub.idqqimg.com/wpa/images/group.png" alt="Quella/el-admin" title="Quella/el-admin"></a>

- 作者邮箱：<EMAIL>