'use strict'
let HOST = process.argv.splice(2)[0] || '';
let CONTENT_INFO = '';
let TITLE_NAME = '';
let SYS_FLAG = '';
switch(HOST) {
  case 'zh':
    HOST = 'http://manage.tangus.cn'
    CONTENT_INFO = '粤ICP备2023091264号-1'
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'fp':
    HOST = 'http://test.puxinc.com:38080'
    CONTENT_INFO = ''
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'js':
    HOST = 'http://************:38080'
    CONTENT_INFO = ''
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'zhs':
    HOST = 'http://**************:38080'
    CONTENT_INFO = ''
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'zj':
    HOST = 'http://*************:38080'
    CONTENT_INFO = ''
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'zj2':
    HOST = 'http://************:38080'
    CONTENT_INFO = ''
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'test':
    HOST = 'http://**************:38080';
    CONTENT_INFO = '';
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'test2':
    HOST = 'http://**************:38080';
    CONTENT_INFO = '';
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'mz':
    HOST = 'http://cshm.mz56.com';
    CONTENT_INFO = '';
    TITLE_NAME = '铭志国际'
    SYS_FLAG = 'mz'
    break;
  case 'yqf':
    HOST = 'http://cshm.yiqifei56.com';
    CONTENT_INFO = '';
    TITLE_NAME = '壹起飞物流'
    SYS_FLAG = 'yqf'
    break;
  case 'md':
    HOST = 'http://*************:38080'
    CONTENT_INFO = '';
    TITLE_NAME = '麥點小包'
    SYS_FLAG = 'zfx'
    break;
  case 'clt':
    HOST = 'http://www.carreton-express.com:38080'
    CONTENT_INFO = '';
    TITLE_NAME = '凯乐通'
    SYS_FLAG = 'clt'
    break;
  case 'dfjs':
    HOST = 'https://omsm.easttopintl.com'
    CONTENT_INFO = '';
    TITLE_NAME = '东方嘉盛'
    SYS_FLAG = 'dfjs'
    break;
  case 'dfjs-eu':
    HOST = 'https://omsm-eu.easttopintl.com'
    CONTENT_INFO = '';
    TITLE_NAME = '东方嘉盛'
    SYS_FLAG = 'dfjs'
    break;
  case 'hanjin':
    HOST = 'http://*************:8089/'
    CONTENT_INFO = '';
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'zfx':
    HOST = 'http://**************:38080/'
    CONTENT_INFO = '';
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  case 'mdoms':
    HOST = 'http://**************:38080/'
    CONTENT_INFO = '';
    TITLE_NAME = '麥點供应链'
    SYS_FLAG = 'zfx'
    break;
  case 'mdzfx':
    HOST = 'http://zfxm.puxinc.com'
    CONTENT_INFO = '';
    TITLE_NAME = '货运管家'
    SYS_FLAG = 'zfx'
    break;
  default:
    break;
}

console.log(HOST)

module.exports = {
  NODE_ENV: '"production"',
  // BASE_API: '"http://manage.faivstar.com"',
  HOST: '"'+HOST+'"',
  CONTENT_INFO:'"'+CONTENT_INFO+'"',
  TITLE_NAME:'"'+TITLE_NAME+'"',
  SYS_FLAG:'"'+SYS_FLAG+'"'
}
