{"name": "eladmin-qt", "version": "1.2.0", "license": "Apache License 2.0", "description": "eladmin 前端代码", "author": "jie <<EMAIL>>", "scripts": {"dev": "cross-env BABEL_ENV=development webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "dev:fast": "cross-env BABEL_ENV=development NODE_ENV=development webpack-dev-server --inline --hot --config build/webpack.dev.conf.js --no-info --quiet", "build": "cross-env NODE_ENV=production env_config=prod node --max_old_space_size=8192 build/build.js", "build:fast": "cross-env NODE_ENV=production env_config=prod FAST_BUILD=true node --max_old_space_size=6144 build/build.js", "lint": "eslint --ext .js,.vue src", "test": "npm run lint", "precommit": "lint-staged", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "repository": {"type": "git", "url": "https://github.com/elunez/eladmin-qd"}, "dependencies": {"@handsontable/vue": "^4.1.1", "@riophae/vue-treeselect": "0.0.37", "axios": "0.18.0", "clipboard": "^2.0.8", "connect": "3.6.6", "file-saver": "1.3.8", "handsontable": "^7.3.0", "js-cookie": "2.2.0", "jsbarcode": "^3.11.0", "jszip": "3.1.5", "node-sass": "^7.0.1", "nprogress": "0.2.0", "qs": "^6.5.3", "screenfull": "3.3.3", "vue": "2.5.17", "vue-count-to": "1.0.13", "vue-i18n": "^8.21.0", "vue-markdown": "^2.2.4", "vuex-router-sync": "^5.0.0", "wangeditor": ">=3.0.0", "xlsx": "^0.11.16"}, "devDependencies": {"autoprefixer": "8.5.0", "babel-core": "6.26.3", "babel-eslint": "8.2.6", "babel-helper-vue-jsx-merge-props": "2.0.3", "babel-loader": "7.1.5", "babel-plugin-dynamic-import-node": "2.0.0", "babel-plugin-syntax-jsx": "6.18.0", "babel-plugin-transform-runtime": "6.23.0", "babel-plugin-transform-vue-jsx": "3.7.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "1.7.0", "babel-preset-stage-2": "6.24.1", "chalk": "2.4.1", "compression-webpack-plugin": "^3.1.0", "copy-webpack-plugin": "4.5.2", "cross-env": "5.2.0", "css-loader": "1.0.0", "eslint": "4.19.1", "eslint-friendly-formatter": "4.0.1", "eslint-loader": "2.0.0", "eslint-plugin-vue": "4.7.1", "eventsource-polyfill": "0.9.6", "file-loader": "1.1.11", "friendly-errors-webpack-plugin": "1.7.0", "html-webpack-plugin": "^4.5.2", "mini-css-extract-plugin": "0.4.1", "node-notifier": "5.2.1", "optimize-css-assets-webpack-plugin": "5.0.0", "ora": "3.0.0", "path-to-regexp": "2.4.0", "portfinder": "1.0.16", "postcss-import": "12.0.0", "postcss-loader": "2.1.6", "postcss-url": "7.3.2", "rimraf": "2.6.2", "sass-loader": "7.0.3", "script-ext-html-webpack-plugin": "^2.1.5", "script-loader": "0.7.2", "semver": "5.5.0", "shelljs": "0.8.2", "svg-sprite-loader": "3.8.0", "svgo": "1.0.5", "uglifyjs-webpack-plugin": "1.2.7", "url-loader": "1.0.1", "vue-loader": "15.3.0", "vue-style-loader": "4.1.2", "vue-template-compiler": "2.5.17", "webpack": "4.16.5", "webpack-bundle-analyzer": "2.13.1", "webpack-cli": "3.1.0", "webpack-dev-server": "3.1.5", "webpack-merge": "4.1.4"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}