<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="openSet"
      width="800px"
      append-to-body
    >
      <el-tabs v-model="activeName" @tab-click="clickTab">
        <el-tab-pane label="基本信息" name="info" lazy>
          <el-row :gutter="20">
            <el-form :model="form" label-position="top" @submit.prevent>
              <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
                <el-form-item label="订单号">
                  <el-input v-model="form.order_num" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
                <el-form-item label="仓库">
                  <el-select
                    v-model="form.warehouse_id"
                    style="width: 100%"
                    class="form-select"
                  >
                    <el-option
                      v-for="dict in whList"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                      :disabled="dict.disabled"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
                <el-form-item label="外部订单号">
                  <el-input
                    v-model="form.external_order_num"
                    :disabled="true"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
                <el-form-item label="外部订单状态">
                  <el-select
                    v-model="form.external_order_status"
                    :disabled="true"
                    style="width: 100%"
                    placeholder="请选择状态"
                    size="small"
                  >
                    <el-option
                      v-for="dict in externalOrderStatusList"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
                <el-alert
                  title="温馨提示"
                  type="info"
                  description="当前操作将会调用八达仓仓单预约"
                  show-icon
                ></el-alert>
              </el-col>
            </el-form>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="操作日志" name="log" lazy>
          <el-table
            v-loading="loading"
            :data="subList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
            ></el-table-column>
            <el-table-column label="订单号" align="center" prop="order_num" />
            <el-table-column
              label="供应商"
              align="center"
              prop="supplier_name"
            />
            <el-table-column
              label="操作"
              align="center"
              prop="operation_name"
            />
            <el-table-column label="结果" align="center" prop="result">
              <template slot-scope="scope">
                <el-tag
                  v-if="scope.row.result == true"
                  class="card-header-tag"
                  effect="dark"
                  type="success"
                >
                  成功
                </el-tag>
                <el-tag
                  v-else
                  class="card-header-tag"
                  effect="dark"
                  type="danger"
                >
                  失败
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="请求报文">
              <template slot-scope="scope">
                <div>
                  <a class="tag" @click="copy(scope.row.request_content)">
                    复制
                  </a>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="响应报文">
              <template slot-scope="scope">
                <div>
                  <a class="tag" @click="copy(scope.row.response_content)">
                    复制
                  </a>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="创建时间"
              align="center"
              prop="createTime"
              width="180"
            >
              <template slot-scope="scope">
                <span>
                  {{ parseTime(scope.row.create_date, "datetime") }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="deleteLoading"
          icon="el-icon-delete"
          type="danger"
          plain
          @click="deletePreOrder"
        >
          预约仓单删除
        </el-button>
        <el-button @click="cancel">{{ $t('content.Cancel') }}</el-button>
        <el-button
          :loading="submitLoading"
          icon="el-icon-edit"
          type="info"
          plain
          @click="updatePreOrder"
        >
          预约仓单更新
        </el-button>
        <el-button
          :loading="submitLoading"
          icon="el-icon-thumb"
          type="primary"
          plain
          @click="submitPreOrder"
        >
          预约仓单创建
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getChoiceData, actionPost, get } from '@/api/data'
import { parseTime, copyData } from '@/utils/index'

export default {
  name: 'SupplierOrderDialog',
  props: {
    orders: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      deleteLoading: false,
      openSet: false,
      title: '预约仓单',
      isShow: false,
      activeName: 'info',
      operationList: [],
      queryParams: {
        customer_order_num: null,
        ordering: '-id',
        page: 1,
        size: 100
      },
      order_id: null,
      orderData: null,
      form: {
        warehouse_id: '4'
      },
      orderType: '',
      subList: [],
      ids: [],
      whList: [
        // 后台默认取订单对应的仓库，只是做展示
        { name: '平湖仓', code: '4' },
        { name: '华南普通仓', code: '6', disabled: true }
      ],
      externalOrderStatusList: [
        { name: '已办单', code: '10' },
        { name: '已收货', code: '20' },
        { name: '已删除', code: 'X' }
      ]
    }
  },
  created() {},
  methods: {
    parseTime,
    copy(data) {
      copyData(data, this)
    },
    handleSelectionChange(selection) {
      console.log(selection)
      this.selectedIndex = selection.map((item) => item.row_index)
      this.ids = selection.map((item) => item.id)
    },
    open() {
      // 校验只能选择一条订单
      if (this.orders.length > 1) {
        this.$message.error('只能选择一条订单')
        return
      }
      this.openSet = true

      const order = this.orders[0]

      this.order_id = order.id
      this.order_num = order.order_num
      this.form.order_num = order.order_num
      this.form.order_id = order.id
      this.queryParams.customer_order_num = order.id

      this.getOcOrderDetail()
      // this.listRecords();
    },
    cancel() {
      this.openSet = false
      this.subList = []
    },
    clickTab() {
      if (this.activeName === 'log') {
        this.listRecords()
      }
    },
    getOcOrderDetail() {
      get({
        api: 'customerOrders',
        id: this.order_id
      }).then((response) => {
        this.orderData = response
        if (this.orderData) {
          const { external_order_num, external_order_status } = this.orderData
          this.form.external_order_num = external_order_num
          this.form.external_order_status = external_order_status
        }
      })
    },
    listRecords() {
      getChoiceData(`api/supplierOperations/`, this.queryParams)
        .then((res) => {
          if (res.code === 200) {
            this.subList = res.data
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
        })
        .catch((err) => {
          this.$message.error(err)
        })
    },
    deletePreOrder() {
      try {
        this.deleteLoading = true
        this.$confirm('此操作将删除预约仓单, 是否继续?', '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            actionPost({
              api: 'api/customerOrders/delete_pre_order/',
              data: this.form
            })
              .then((response) => {
                const { code } = response
                if (code === 200) {
                  console.log(response)
                  this.$message.success('订单删除成功')
                  this.cancel()
                } else {
                  const msg =
                    response.msg || response.detail || response.message
                  this.$message.error(msg)
                }
              })
              .catch(() => {})
          })
          .catch(() => {})
      } finally {
        this.deleteLoading = false
      }
    },
    submitPreOrder() {
      try {
        this.submitLoading = true
        actionPost({
          api: 'api/customerOrders/submit_pre_order/',
          data: this.form
        })
          .then((response) => {
            const { code } = response
            console.log(`response: ${code}`)
            if (code === 200) {
              this.cancel()
              this.$message.success('处理成功')
            } else {
              const msg = response.msg || response.detail || response.message
              this.$message.error(msg)
            }
          })
          .catch(() => {})
      } finally {
        this.submitLoading = false
      }
    },
    updatePreOrder() {
      try {
        this.submitLoading = true
        actionPost({
          api: 'api/customerOrders/update_pre_order/',
          data: this.form
        })
          .then((response) => {
            const { code } = response
            if (code === 200) {
              this.$message.success('处理成功')
              this.cancel()
            } else {
              const msg = response.msg || response.detail || response.message
              this.$message.error(msg)
            }
          })
          .catch(() => {})
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
