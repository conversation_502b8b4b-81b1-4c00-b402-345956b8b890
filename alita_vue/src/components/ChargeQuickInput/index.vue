<template>
  <el-drawer
    :visible.sync="visible"
    :before-close="handleClose"
    title="成本收入快捷录入"
    direction="rtl"
    size="70%"
  >
    <div class="drawer-main">
      <div class="drawer-content" style="height: 90%">
        <el-card class="box-card">
          <el-form
            ref="templateFormRef"
            :inline="true"
            :model="templateForm"
            label-width="200"
            @submit.native.prevent
          >
            <el-form-item label="供应商">
              <el-select
                v-model="templateSupplier"
                filterable
                clearable
                placeholder="可选供应商"
                style="width: 100%"
                default-first-option
              >
                <el-option
                  v-for="item in supplierOptions"
                  :key="item.id"
                  :label="`${item.name}-${item.short_name}`"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="metaData && metaData.order_type.startsWith('customerOrderTask') || metaData && metaData.order_type === 'revenueAndCost'" label="客户">
              <el-select
                v-model="templateCustomer"
                filterable
                clearable
                placeholder="可选客户"
                style="width: 100%"
                default-first-option
              >
                <el-option
                  v-for="item in companyOptions"
                  :key="item.id"
                  :label="`${item.name}-${item.short_name}`"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="模板名称">
              <el-select
                v-model="templateId"
                filterable
                clearable
                placeholder="当前模板"
                style="width: 100%"
                default-first-option
                @focus="getTemplateData"
                @change="showTemplateDetail"
              >
                <el-option
                  v-for="item in templateList"
                  :key="item.id"
                  :label="item.template_name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card v-if="calShowChargeIn(is_revenue_lock, is_share_revenue, metaData)" class="box-card">
          <div slot="header" class="clearfix">
            <span>收入明细</span>

            <!-- <el-button
            style="float: right; margin-left: 20px"
            type="success"
            plain
            icon="el-icon-circle-check"
            size="mini"
            @click="handleChargeInSave"
          >
            保存
          </el-button> -->

            <el-button
              style="float: right; margin-left: 20px"
              type="primary"
              plain
              icon="el-icon-circle-check"
              size="mini"
              @click="handleChargeInAddRow"
            >
              新增一行
            </el-button>

            <el-button plain type="danger" @click="fillTable('reset-in')">
              全量覆盖
            </el-button>

            <el-button
              type="primary"
              style="margin-right: 20px"
              size="mini"
              plain
              @click="fillTable('auto-in')"
            >
              追加填充
            </el-button>

          </div>

          <el-table v-loading="listLoading" :data="chargeInList" border>
            <el-table-column label="费用名称" align="center" min-width="120">
              <template slot-scope="scope">
                <el-select
                  v-if="showChargeInInput"
                  v-model="scope.row.charge"
                  filterable
                  clearable
                  placeholder="请选择费用名称"
                  default-first-option
                >
                  <el-option
                    v-for="item in chargeOptions"
                    :key="item.id"
                    :label="`${item.name}-${item.code}`"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <span v-else>{{ scope.row.charge_name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="单价"
              align="center"
              prop="charge_rate"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="showChargeInInput"
                  v-model="scope.row.charge_rate"
                  placeholder="">
                </el-input>
                <span v-else>{{ scope.row.charge_rate }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="数量"
              align="center"
              prop="charge_count"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="showChargeInInput"
                  v-model="scope.row.charge_count"
                  placeholder="">
                </el-input>
                <span v-else>{{ scope.row.charge_count }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="合计"
              min-width="100"
              align="center"
              prop="charge_total"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.charge_total }}</span>
              </template>
            </el-table-column>
            <el-table-column label="币种" align="center" min-width="100">
              <template slot-scope="scope">
                <el-select
                  v-if="showChargeInInput"
                  v-model="scope.row.currency_type"
                  filterable
                  clearable
                  placeholder="请选择币种"
                  default-first-option
                >
                  <el-option
                    v-for="item in currencyOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <span v-else>{{ scope.row.currency_type }}</span>
              </template>
            </el-table-column>
            <!--所有收入明细的付款方不需要填写, 默认为该订单的客户, 由后端赋值-->
            <!--<el-table-column
              min-width="120"
              label="付款方"
              align="center"
            >
              <template slot-scope="scope">
                <el-select
                  v-if="showChargeInInput"
                  v-model="scope.row.customer"
                  filterable
                  clearable
                  placeholder="请选择付款方"
                  default-first-option
                >
                  <el-option
                    v-for="item in companyOptions"
                    :key="item.id"
                    :label="`${item.name}-${item.short_name}`"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <span v-else>{{ scope.row.customer_name }}</span>
              </template>
            </el-table-column>-->

            <el-table-column
              label="备注"
              min-width="120"
              align="center"
              prop="remark"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="showChargeInInput"
                  v-model="scope.row.remark"
                  placeholder="">
                </el-input>
                <span v-else>{{ scope.row.remark }}</span>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              align="center"
              fixed="right"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleChargeInDelete(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <el-card v-if="!is_cost_lock && !is_share_cost" class="box-card">
          <div slot="header" class="clearfix">
            <span>成本明细</span>

            <!-- <el-button
              style="float: right; margin-left: 20px"
              type="success"
              plain
              icon="el-icon-circle-check"
              size="mini"
              @click="handleChargeOutSave"
            >
              保存
            </el-button> -->

            <el-button
              style="float: right; margin-left: 20px"
              type="primary"
              plain
              icon="el-icon-circle-check"
              size="mini"
              @click="handleChargeOutAddRow"
            >
              新增一行
            </el-button>

            <el-button plain size="mini" type="danger" @click="fillTable('reset')">
              全量覆盖
            </el-button>

            <el-button
              plain
              size="mini"
              type="primary"
              style="margin-right: 20px"
              @click="fillTable('auto')"
            >
              追加填充
            </el-button>
          </div>

          <el-table v-loading="listLoading" :data="chargeOutList" border>
            <el-table-column label="费用名称" align="center" min-width="120">
              <template slot-scope="scope">
                <el-select
                  v-if="showChargeOutInput"
                  v-model="scope.row.charge"
                  filterable
                  clearable
                  placeholder="请选择费用名称"
                  default-first-option
                >
                  <el-option
                    v-for="item in chargeOptions"
                    :key="item.id"
                    :label="`${item.name}-${item.code}`"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <span v-else>{{ scope.row.charge_name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="单价"
              align="center"
              prop="charge_rate"
            >
              <template slot-scope="scope">
                <!--<input
                  v-if="showChargeOutInput"
                  v-model="scope.row.charge_rate"
                  type="number"
                >-->
                <el-input
                  v-if="showChargeOutInput"
                  v-model="scope.row.charge_rate"
                  placeholder=""
                  type="number">
                </el-input>
                <span v-else>{{ scope.row.charge_rate }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="数量"
              align="center"
              prop="charge_count"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="showChargeOutInput"
                  v-model="scope.row.charge_count"
                  placeholder=""
                  type="number">
                </el-input>
                <span v-else>{{ scope.row.charge_count }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="合计"
              min-width="100"
              align="center"
              prop="charge_total"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.charge_total }}</span>
              </template>
            </el-table-column>
            <el-table-column label="币种" align="center" min-width="100">
              <template slot-scope="scope">
                <el-select
                  v-if="showChargeOutInput"
                  v-model="scope.row.currency_type"
                  filterable
                  clearable
                  placeholder="请选择币种"
                  default-first-option
                >
                  <el-option
                    v-for="item in currencyOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <span v-else>{{ scope.row.currency_type }}</span>
              </template>
            </el-table-column>
            <el-table-column label="供应商" align="center" min-width="120">
              <template slot-scope="scope">
                <el-select
                  v-if="showChargeOutInput"
                  v-model="scope.row.supplier"
                  filterable
                  clearable
                  placeholder="请选择供应商"
                  default-first-option
                >
                  <el-option
                    v-for="item in supplierOptions"
                    :key="item.id"
                    :label="`${item.name}-${item.short_name}`"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <span v-else>{{ scope.row.supplier_name }}</span>
              </template>
            </el-table-column>

            <!-- <el-table-column
              label="分摊"
              min-width="100"
              align="center"
              prop="is_share"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.is_share }}</span>
              </template>
            </el-table-column> -->

            <!-- <el-table-column
              label="分摊单号"
              min-width="100"
              align="center"
              prop="share_charge_id"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.share_charge_id }}</span>
              </template>
            </el-table-column> -->

            <el-table-column
              label="备注"
              min-width="120"
              align="center"
              prop="remark"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="showChargeOutInput"
                  v-model="scope.row.remark"
                  placeholder="">
                </el-input>
                <span v-else>{{ scope.row.remark }}</span>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              align="center"
              fixed="right"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

      </div>

      <div
        class="drawer-footer"
        style="position: fixed; right: 10px; bottom: 5px"
      >
        <el-button @click="handleClose">{{ $t('content.Cancel') }}</el-button>

        <el-button
          :loading="saveLoading"
          type="primary"
          style="margin-right: 20px"
          @click="clickConfirm()"
        >
          保 存
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { actionPost, edit, get, getChoiceData } from '@/api/data'
// import { getChoiceData, get, edit } from '@/api/data'

export default {
  name: 'ChargeQuickInput',
  props: {
    // visible: Boolean
  },
  data() {
    return {
      saveLoading: false,
      visible: false,
      visible2: false,
      rowData: null,
      metaData: null,
      form: {},
      rules: {
        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
      },
      drawer: false,
      border: true,
      stripe: false,
      showChargeOutInput: true,
      showChargeInInput: true,
      lineHeight: 'medium',
      list: [],

      listLoading: false,
      layout: 'total, sizes, prev, pager, next, jumper',
      total: 0,

      selectRows: '',
      queryForm: {
        order_num: '',
        order_type: 'customerOrderTask'
      },
      order_num: '',
      is_revenue_lock: false,
      is_cost_lock: false,
      is_share_cost: false,
      is_share_revenue: false,
      order_id: '',
      detail_url: '',
      api_endpoint: '',
      orderTypes: [
        { value: 'customerOrderTask', label: '运输订单' },
        { value: 'customerOrderTask_fba', label: 'FBA订单' },
        { value: 'collectOrder', label: '揽收单' },
        { value: 'masterOrder', label: '空运主单' },
        { value: 'oceanOrder', label: '海运提单' },
        { value: 'truckOrder', label: '卡派单' },
        { value: 'clearanceOutOrders', label: '出口报关单' }
      ],
      charge_out_key: null,
      charge_in_key: null,
      templateForm: {},
      formData: {},
      templateList: [],
      chargeOutList: [],
      chargeInList: [],
      chargeOptions: [],
      supplierOptions: [],
      companyOptions: [],
      templateId: null,
      templateSupplier: null,
      templateCustomer: null,
      currencyOptions: [
        { id: 'CNY', name: 'CNY' },
        { id: 'USD', name: 'USD' },
        { id: 'GBP', name: 'GBP' },
        { id: 'EUR', name: 'EUR' },
        { id: 'JPY', name: 'JPY' },
        { id: 'HKD', name: 'HKD' },
        { id: 'CAD', name: 'CAD' },
        { id: 'MXN', name: 'MXN' }
      ]
    }
  },
  watch: {
    // 监听 charge_count 和 charge_rate 的变化
    chargeOutList: {
      handler: function(val) {
        val.forEach((item) => {
          if (item.charge_count && item.charge_rate) {
            item.charge_total = (
              item.charge_count * item.charge_rate
            ).toFixed(2)
          }

          // 通过charge的id获取charge_name
          const charge = this.chargeOptions.find((c) => c.id === item.charge)
          if (charge) {
            item.charge_name = charge.name
          }

          // 通过supplier的id获取supplier_name
          const supplier = this.supplierOptions.find(
            (s) => s.id === item.supplier
          )
          if (supplier) {
            item.supplier_name = supplier.name
          }
        })
      },
      deep: true
    },

    chargeInList: {
      handler: function(val) {
        val.forEach((item) => {
          if (item.charge_count && item.charge_rate) {
            item.charge_total = (
              item.charge_count * item.charge_rate
            ).toFixed(2)
          }

          // 通过charge的id获取charge_name
          const charge = this.chargeOptions.find((c) => c.id === item.charge)
          if (charge) {
            item.charge_name = charge.name
          }

          item.customer_name = item.customer_code
        })
      },
      deep: true
    }
  },
  created() {
    // this.listTemplate()
    this.listCharges()
    this.listSupplier()
    this.listCompany()
  },
  methods: {
    reset() {
      this.form = {}
    },
    cancel() {
      this.$emit('update:visible', false)
    },
    async handleShow(metaData, row) {
      this.metaData = metaData
      this.rowData = row
      this.order_num = metaData.order_num
      this.is_revenue_lock = metaData.is_revenue_lock
      this.is_cost_lock = metaData.is_cost_lock
      this.is_share_cost = metaData.is_share_cost
      this.is_share_revenue = metaData.is_share_revenue
      this.templateSupplier = metaData.supplier
      this.templateCustomer = metaData.customer
      // console.log(`handleShow ${JSON.stringify(row)}`)
      // console.log(`metaData ${JSON.stringify(metaData)}`)

      this.visible = true

      const data = await this.fetchDetail()

      console.log('>>data-->', data)
      this.formData = data

      if (metaData.charge_out_key != null) {
        this.chargeOutList = data[metaData.charge_out_key]
        console.log('this.chargeOutList-->', this.chargeOutList)
      }

      if (metaData.charge_in_key != null) {
        this.chargeInList = data[metaData.charge_in_key]
        console.log('this.chargeInList-->', this.chargeInList)
      }
    },
    async clickConfirm() {
      this.$confirm('您确定提交数据吗？')
        .then(() => {
          this.confirm()
        })
        .catch(() => { })
    },
    calShowChargeIn(is_revenue_lock, is_share_revenue, metaData) {
      // console.log(`calShowChargeIn_is_revenue_lock:${is_revenue_lock}|is_share_revenue:${is_share_revenue}`)
      if (is_revenue_lock || is_share_revenue) {
        return false
      }
      if (!metaData) {
        return false
      }
      const order_type = metaData.order_type
      if (order_type.startsWith('customerOrderTask')) {
        return true
      }
      const supportTypeList = ['revenueAndCost', 'oceanOrder', 'ClearanceOut']
      if (supportTypeList.includes(order_type)) {
        return true
      }
      return false
    },
    async confirm() {
      if (this.chargeOutList.length === 0 && this.chargeInList.length === 0) {
        this.$notify.error({
          title: 'error',
          message: '请先录入收入/成本数据'
        })
        return
      }

      // 校验数据supplier
      if (this.chargeOutList.length > 0) {
        const validata_flag = this.validateChargeInfo(this.chargeOutList, true)
        if (!validata_flag) {
          return false
        }
      }

      if (this.chargeInList.length > 0) {
        const validata_flag = this.validateChargeInfo(this.chargeInList, false)
        if (!validata_flag) {
          return false
        }
      }

      // 保存时，加载最新的订单数据
      const editFormData = await this.fetchDetail()

      // 序列化问题，需要剔除对应非表单key
      delete editFormData['attachments']
      delete editFormData['signForAttachments']
      delete editFormData['parcel']
      // console.log('editFormData-->', editFormData)

      // 赋值成本数据
      if (this.chargeOutList.length > 0) {
        editFormData[this.metaData.charge_out_key] = this.chargeOutList
      }

      // 赋值收入数据
      if (this.chargeInList.length > 0) {
        editFormData[this.metaData.charge_in_key] = this.chargeInList
      }

      // console.log(`editFormData ${JSON.stringify(editFormData)}`)
      let result = null
      editFormData['onlyEditCharge'] = true

      try {
        // add loading
        this.saveLoading = true
        if (this.metaData.api_endpoint === 'customerOrders') {
        // result = await actionPost({
          result = await edit({
          // api: 'api/customerOrders/quick_entry_charge/',
            api: this.metaData.api_endpoint,
            data: editFormData,
            id: this.rowData.id
          })
        } else {
          result = await edit({
            api: this.metaData.api_endpoint,
            data: editFormData,
            id: this.rowData.id
          })
        }
      } finally {
        this.saveLoading = false
      }

      const { data, detail } = result

      if (data) {
        this.$notify.success({
          message: `订单${this.order_num}费用录入成功`
        })
        this.$emit('update:visible', false)
        this.visible = false
      } else {
        if (detail) {
          this.$notify.error({
            message: `订单${this.order_num}费用录入失败，原因:${detail}`
          })
        } else {
          const resultData = new Map(Object.entries(result))
          const errors = this.extractErrors(resultData)
          console.log(`errors: ${errors}`)
          this.$notify.error({
            message: `订单${this.order_num}费用录入失败，原因:${errors.join(
              ';'
            )}`
          })
        }
      }
    },
    async fetchDetail() {
      if (this.metaData.api_endpoint === 'customerOrders') {
        try {
          const res = await actionPost({
            api: `api/${this.metaData.api_endpoint}/get_customer_order_charge/`,
            data: { id: this.rowData.id }
          })
          if (res.code === 200) {
            // 确保返回数据
            return res.data
          } else {
            this.$message.error(res.msg || res.detail || res.message)
            return null
          }
        } catch (error) {
          this.bus.$emit('fullLoading', false)
          return null
        } finally {
          this.bus.$emit('fullLoading', false)
        }
      } else {
        const { data } = await get({
          api: this.metaData.api_endpoint,
          id: this.rowData.id
        })
        return data
      }
    },
    async getTemplateData() {
      const { data } = await getChoiceData('/api/chargeTemplateConfigs/', {
        page: 1,
        size: 100000,
        ordering: '-id',
        supplier: this.templateSupplier,
        customer: this.templateCustomer
      })
      console.log('this.templateList-->', this.templateList)
      this.templateList = data
    },
    async showTemplateDetail() {
      if (this.templateId) {
        const { data } = await get({
          api: 'chargeTemplateConfigs',
          id: this.templateId
        })
        if (data) {
          this.templateForm = data
        }
      }
    },
    async listTemplate() {
      const { data } = await getChoiceData('/api/chargeTemplateConfigs/', {
        page: 1,
        size: 100000,
        ordering: '-id'
      })
      console.log('this.templateList-->', this.templateList)
      this.templateList = data
    },
    async listCharges() {
      const { data } = await getChoiceData('/api/charges/', {
        page: 1,
        size: 1000
      })
      this.chargeOptions = data
    },
    async listSupplier() {
      const { data } = await getChoiceData('/api/companies/list_company2/', {
        page: 1,
        size: 1000,
        is_supplier: true
      })
      this.supplierOptions = data
    },
    async listCompany() {
      const { data } = await getChoiceData('/api/companies/list_company2/', {
        page: 1,
        size: 1000,
        is_customer: true
      })
      this.companyOptions = data
    },
    handleChargeOutAddRow() {
      this.chargeOutList.push({
        charge: null,
        charge_name: '',
        charge_rate: 0,
        charge_count: 0,
        charge_total: 0,
        currency_type: 'CNY',
        supplier: null,
        supplier_name: '',
        is_share: false,
        share_charge_id: '',
        remark: ''
      })
    },
    handleChargeInAddRow() {
      this.chargeInList.push({
        charge: null,
        charge_name: '',
        charge_rate: 0,
        charge_count: 0,
        charge_total: 0,
        currency_type: 'CNY',
        customer: null,
        customer_name: '',
        remark: ''
      })
    },
    validateChargeInfo(chargeList, is_supplier) {
      let validata_flag = true
      for (let i = 0; i < chargeList.length; i++) {
        const charge = chargeList[i]
        if (!charge.charge) {
          this.$notify.error({
            title: 'error',
            message: `第${i + 1}行费用名称不能为空`
          })
          validata_flag = false
          break
        }
        if (!charge.charge_rate || charge.charge_rate === 0) {
          this.$notify.error({
            title: 'error',
            message: `第${i + 1}行费用单价不能为空`
          })
          validata_flag = false
          break
        }
        if (!charge.charge_count || charge.charge_count === 0) {
          this.$notify.error({
            title: 'error',
            message: `第${i + 1}行费用数量不能为空`
          })
          validata_flag = false
          break
        }
        if (!charge.currency_type || charge.currency_type === '') {
          this.$notify.error({
            title: 'error',
            message: `第${i + 1}行费用币种不能为空`
          })
          validata_flag = false
          break
        }
        if (is_supplier) {
          // 先判断是否有 supplier 字段，如果有，则判断是否为空
          if (charge.supplier && !charge.supplier_name) {
            this.$notify.error({
              title: 'error',
              message: `第${i + 1}行的费用供应商不能为空`
            })
            validata_flag = false
            break
          }
        } else {
          // console.log('charge0-->', charge)
          // console.log('charge.customer-->', charge.customer)
          // if (charge.customer && !charge.customer_name) {
          // // if (!charge.customer) {
          //   this.$notify.error({
          //     title: 'error',
          //     message: `第${i + 1}行的费用付款方不能为空`
          //   })
          //   validata_flag = false
          //   break
          // }
        }
      }
      return validata_flag
    },
    handleDelete(row) {
      // 找到要删除的对象在数组中的位置
      const index = this.chargeOutList.indexOf(row)
      if (index !== -1) {
        // 从数组中移除该对象
        this.chargeOutList.splice(index, 1)
      }
    },
    handleChargeInDelete(row) {
      // 找到要删除的对象在数组中的位置
      const index = this.chargeInList.indexOf(row)
      if (index !== -1) {
        // 从数组中移除该对象
        this.chargeInList.splice(index, 1)
      }
    },
    fillTable(type) {
      console.log('this.templateForm-->', this.templateForm)
      if (type === 'auto' || type === 'reset') {
        if (
          !this.templateForm.chargeOuts ||
            this.templateForm.chargeOuts.length <= 0
        ) {
          this.$notify.error({
            title: 'error',
            message: '请选择成本模板'
          })
          return false
        }
      } else {
        if (
          !this.templateForm.chargeIns ||
            this.templateForm.chargeIns.length <= 0
        ) {
          this.$notify.error({
            title: 'error',
            message: '请选择收入模板'
          })
          return false
        }
      }

      if (type === 'auto') {
        this.templateForm.chargeOuts.forEach((item) => {
          console.log(item)

          this.chargeOutList.push({
            charge: item.charge,
            charge_name: item.charge_name,
            charge_rate: item.charge_rate,
            charge_count: item.charge_count,
            charge_total: item.charge_total,
            currency_type: item.currency_type,
            supplier: item.supplier,
            supplier_name: item.supplier_name,
            is_share: item.is_share,
            share_charge_id: item.share_charge_id,
            remark: item.remark
          })
        })
      } else if (type === 'reset') {
        this.chargeOutList = []
        this.templateForm.chargeOuts.forEach((item) => {
          console.log(item)

          this.chargeOutList.push({
            charge: item.charge,
            charge_name: item.charge_name,
            charge_rate: item.charge_rate,
            charge_count: item.charge_count,
            charge_total: item.charge_total,
            currency_type: item.currency_type,
            supplier: item.supplier,
            supplier_name: item.supplier_name,
            is_share: item.is_share,
            share_charge_id: item.share_charge_id,
            remark: item.remark
          })
        })
      } else if (type === 'auto-in') {
        this.templateForm.chargeIns.forEach((item) => {
          this.chargeInList.push({
            charge: item.charge,
            charge_name: item.charge_name,
            charge_rate: item.charge_rate,
            charge_count: item.charge_count,
            charge_total: item.charge_total,
            currency_type: item.currency_type,
            customer: item.customer,
            customer_name: item.customer_name,
            customer_code: item.customer_name,
            remark: item.remark
          })
        })
      } else if (type === 'reset-in') {
        this.chargeInList = []
        this.templateForm.chargeIns.forEach((item) => {
          this.chargeInList.push({
            charge: item.charge,
            charge_name: item.charge_name,
            charge_rate: item.charge_rate,
            charge_count: item.charge_count,
            charge_total: item.charge_total,
            currency_type: item.currency_type,
            customer: item.customer,
            customer_name: item.customer_name,
            customer_code: item.customer_name,
            remark: item.remark
          })
        })
      }

      this.drawer = false
      this.$notify.success({
        message: '模板数据填充成功'
      })
    },
    extractErrors(resultMap) {
      const errors = []
      for (const [key, values] of resultMap) {
        if (Array.isArray(values)) {
          values.forEach((error) => {
            errors.push(`${key}: ${JSON.stringify(error)}`)
          })
        } else if (
          typeof values === 'object' &&
            !Array.isArray(values) &&
            key !== 'code' &&
            key !== 'message'
        ) {
          // 如果值是对象且不是数组，递归调用 extractErrors
          errors.push(...this.extractErrors(values))
        }
      }
      return errors
    },
    handleClose() {
      if (this.chargeOutList.length > 0 || this.chargeInList.length > 0) {
        this.$confirm('您需要放弃当前变更吗？')
          .then(() => {
            this.$emit('update:visible', false)
            this.visible = false
          })
          .catch(() => { })
      } else {
        this.$emit('update:visible', false)
        this.visible = false
      }
    }
  }
}
</script>
<style lang="scss">
  .drawer-main {
    position: relative;
    height: 100%;
    overflow: hidden;
  }

  .drawer-content {
    box-sizing: border-box;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .el-drawer__body {
    overflow-y: auto;
  }
</style>

