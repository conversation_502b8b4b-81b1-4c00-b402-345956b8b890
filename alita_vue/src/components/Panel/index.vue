<template>
  <div class="panel">
    <div v-if="label" class="panel__label" v-html="label"/>
    <div class="panel__body"><slot/></div>
  </div>
</template>

<script>
export default {
  name: 'Panel',
  props: {
    label: {
      type: String,
      default: ''
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.panel {
  position: relative;

  & > .panel__label {
    position: relative;
    height: 40px;
    box-sizing: border-box;
    line-height: 40px;
    font-size: 14px;
    font-weight: 600;
    color: rgb(48, 65, 86);
    border-bottom: 2px solid #E4E7ED;
  }

  & > .panel__body {
    padding: 20px 10px;
  }
}
</style>
