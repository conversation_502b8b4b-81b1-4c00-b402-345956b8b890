<template>
  <div>
    <div id="pdfDom" class="code-box">
      <div v-for="(item, index) in codeList" :key="item" class="code-item">
        <img :id="'barcode'+index">
        <div class="line-two">{{ lineTwoCode[index] }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import JsBarcode from 'jsbarcode'

export default {
  name: 'BarCode',
  props: {
    codeList: {
      type: Array,
      default: () => { return [] }
    },
    lineTwoCode: {
      type: Array,
      default: () => { return [] }
    }
  },
  data() {
    return {
    }
  },
  computed: {
    fontSize() {
      return this.lineTwoCode.length ? 12 : 20
    }
  },
  watch: {
    codeList(n, o) {
      this.$nextTick(() => {
        this.codeList.forEach((i, index) => {
          JsBarcode('#barcode' + index, i, { fontSize: this.fontSize })
        })
      })
    }
  },
  methods: {
  }
}
</script>

<style lang="scss">
#pdfDom {
  width: 340px;
  // height: 142px;
  text-align: center;
  position: fixed;
  top: -100000px;
  left: -100000px;
  .code-item {
    .line-two {
    height: 30px;
    line-height: 30px;
    font-size: 24px;
    // text-align: center;
   }
  }
}
</style>
