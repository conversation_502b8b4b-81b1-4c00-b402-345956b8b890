<template>
  <div>
    <div style="margin-top: 10px;font-size: 12px">
      <span>{{ expression }}</span>
    </div>
    <el-button
      v-if="isEdit"
      size="mini"
      type="text"
      @click="addExtendField"
    >增加扩展属性
    </el-button>

    <el-dialog :title="title" :visible.sync="subOpen" width="880px" append-to-body>
      <el-table v-loading="loading" ref="fileTable" :data="applicabilityList" :default-selection="selectedIndex" @selection-change="handleSelectionChange">

        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="属性" align="center" prop="label"></el-table-column>
        <el-table-column label="值" align="center" prop="label_value">
          <template slot-scope="scope">
            <el-input v-model="scope.row.label_value" placeholder="请输入值" clearable @change="showExpression"/>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('content.Ok_1') }}</el-button>
        <el-button @click="cancel">{{ $t('content.Cancel') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import { getDicts } from '../../api/baseInfo'

export default {
  name: 'ExtendFieldForm',
  props: {
    // 值
    value: {
      type: [String, Object, Array],
      default() {
        return ''
      }
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      subForm: {},
      subOpen: false,
      title: '编辑扩展属性',
      editConditions: this.isEdit,
      applicabilityList: [],
      expression: '',
      selectedIndex: []
    }
  },
  async created() {
    await this.getDicts()
    await this.init_data()
    await this.showExpression()
  },
  mounted() {
  },
  methods: {
    async getDicts() {
      await getDicts('product_extend_field').then(response => {
        this.applicabilityList = response.data
        console.log('bbbb--->>', this.applicabilityList)
      })
    },
    async init_data() {
      const list = JSON.parse(this.value)
      await this.applicabilityList.forEach(item => {
        if (list[item.value]) {
          item.label_value = list[item.value]
        }
      })
    },
    addExtendField() {
      this.subOpen = true
      console.log('------>>', this.value)
      if (!this.value) {
        return
      }
      const list = JSON.parse(this.value)
      const _that = this
      this.applicabilityList.forEach(item => {
        if (list[item.value]) {
          item.label_value = list[item.value]
          _that.$nextTick(() => {
            _that.$refs.fileTable.toggleRowSelection(item, true)
          })
        }
      })
    },
    handleSelectionChange(selection) {
      this.selectedIndex = selection.map(item => item.value)
    },
    submitForm() {
      console.log('----select--->>>', this.selectedIndex)
      const values = {}
      const error_message = []
      this.selectedIndex.forEach(item => {
        const extend_filed = this.applicabilityList.find(v => (v.value === item))
        if (!extend_filed.label_value) {
          error_message.push(extend_filed.label)
        } else {
          values[extend_filed.value] = extend_filed.label_value
        }
      })
      if (error_message.length > 0) {
        this.$message.warning('选中项' + error_message + '必填')
        return
      }
      this.value = JSON.stringify(values)
      console.log('----this.propValue--->>>', this.value)
      this.showExpression()
      this.$emit('input', this.value)
      this.subOpen = false
    },
    cancel() {
      this.subOpen = false
    },
    showExpression() {
      const _that = this
      this.expression = ''
      const list = JSON.parse(this.value)
      this.applicabilityList.forEach(item => {
        if (list[item.value]) {
          _that.expression += item.label + ':' + item.label_value + '\n'
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
