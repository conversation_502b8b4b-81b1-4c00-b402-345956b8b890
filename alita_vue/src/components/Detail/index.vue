<!--mixins.js详情页form表单-->
<template>
  <div class="pageRoot">
    <el-form ref="commomForm" :model="formData" :rules="filterRules" label-width="140px" label-position="right">
      <div class="form-btns form-btns__header">
        <!-- 修改付款信息如果初始pay_amount与初始余额不相等才显示 -->
        <template v-if="detailEdit">
          <el-button v-if="formData.pay_amount===null||formData.pay_balance===null||pay_amount===formData.pay_balance" type="success" @click="savePayInfo(true)">修改付款信息</el-button>
        </template>
        <template v-if="detailEdit===false">
          <el-button v-if="formData.pay_amount===null||formData.pay_balance===null||pay_amount===formData.pay_balance" type="success" @click="savePayInfo(false)">编辑</el-button>
        </template>
        <template v-if="!readOnly">
          <el-button v-if="!isAdd&&showBtn&&authButtons(authModify)" type="primary" @click="modify(true)">{{ $t('common.modify') }}</el-button>
          <el-button v-if="isAdd" :loading="flag" type="success" @click="save()">{{ $t('common.save') }}</el-button>
          <el-button v-if="isAdd" :loading="flag" type="success" @click="save(true)">{{ $t('common.saveAndedit') }}</el-button>
          <el-button v-if="!isAdd&&id&&!deteleDisabled" type="danger" @click="del()">{{ $t('common.delete') }}</el-button>
          <!-- 其他传入的按钮 -->
          <template v-if="bottomBtn.length!==0">
            <template v-for="btn in bottomBtn" >
              <el-button v-if="btn.type==='saveAndSubmit'" :key="btn.method" :loading="flag" type="primary" @click="save(false, 'save_and_submit')">{{ btn.name }}</el-button>
              <!--<el-button v-if="!readOnly&&!isAdd&&showBtn&&btn.type==='authModify'" :key="btn.method" :loading="flag" type="primary" @click="modify(true)">{{ $t('common.modify') }}</el-button>-->
              <el-dropdown v-else-if="btn.type==='selectBtn'&&bottomBtnShow(btn)" :key="btn.method">
                <el-button >
                  {{ btn.name || btn.placeholder }}<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="(item, index) in btn.btns" :key="index">
                    <template v-if="item.import">
                      <el-upload
                        ref="header_hidden_update"
                        :show-file-list="false"
                        :http-request="(data)=>{importExcel(data, item)}"
                        :limit="10"
                        action="https://jsonplaceholder.typicode.com/posts/">
                        <span>{{ item.name }}</span>
                      </el-upload>
                    </template>
                    <template v-else>
                      <span @click="bottomBtnEvent(item)">{{ item.name }}</span>
                    </template>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-upload
                v-else-if="btn.import&&bottomBtnShow(btn)&&authButtons(btn)"
                ref="header_hidden_update"
                :key="btn.method"
                :show-file-list="false"
                :http-request="(data)=>{importExcel(data, btn)}"
                :limit="10"
                style="display: inline-block;margin-right:8px;"
                action="https://jsonplaceholder.typicode.com/posts/">
                <el-button>{{ btn.name || $t('content.import') }}</el-button>
              </el-upload>
              <el-button v-else-if="bottomBtnShow(btn)&&authButtons(btn)" :key="btn.method" :type="btn.type" @click="bottomBtnEvent(btn)">{{ btn.name }}</el-button>
            </template>
          </template>
        </template>
        <el-button v-if="isAdd&&id" @click="modify(false)">{{ $t('common.cancle') }}</el-button>
        <el-button @click="closePage()">{{ $t('common.close') }}</el-button>
      </div>
      <!-- 头部表单 -->
      <template v-for="i in initData">
        <div v-if="(!i.isShowKey || formData[i.isShowKey])" :key="i.label">
          <panel v-if="(!i.detailShow || (i.detailShow&&$route.query.id)) && !($route.query.edit === 'true' && i.editHidden === true) && i.type!=='hiddenTab'" :label="i.label">
            <el-row>
              <!-- 任何情况下都可编辑: 保存按钮 -->
              <div v-if="tableModifyShowCommon(i.saveCondition, false)" class="clearfix" style="margin-bottom:10px">
                <el-button v-if="!isAdd&&!showBtn" class="fr" type="primary" @click="save(true, null)">{{ $t('common.save') }}</el-button>
              </div>
              <el-col v-for="(item,index) in i.formDataInit.filter(h=>!h.$hide&&((h.addHiden&&id) || (!h.addHiden)))" :key="item.prop" :sm="item.type==='table'||item.type==='tabTable'||item.type==='edittable'||item.type==='textarea'||item.singleColumn === true?24:(index+1)%2===1||item.type==='files'||item.type==='files2'||item.type==='files3'||item.type==='files_upload'||item.type==='files_show'?11:{span: 11, offset: 2}" :xs="24">
                <!-- 表格 -->
                <template v-if="item.type==='table'">
                  <!-- 添加按钮 -->
                  <div v-if="isAdd && tableModifyShowCommon(item.modifyBtnShow) || (item.singleEdit&&tableModifyShow(item.singleEditTradition))" class="clearfix" style="margin-bottom:10px">
                    <el-button v-if="item.action&&item.action.add" class="fl" type="success" @click="tableOperate('add',item)">{{ $t('common.create') }}</el-button>
                    <el-button v-if="item.action&&item.action.detele" class="fl" type="danger" @click="tableOperate('detele',item)">{{ $t('common.delete') }}</el-button>
                    <el-button v-if="item.action&&item.action.batchEdit" class="fl" type="success" @click="tableOperate('batchEdit',item)">{{ $t('common.batchEdit') }}</el-button>
                    <el-button v-if="item.action&&item.action.batchUnbindOutbound" class="fl" type="success" @click="tableOperate('batchUnbindOutbound',item)">{{ $t('common.batchEdit') }}</el-button>
                    <el-button v-if="item.action&&item.action.batchUnbindYunshu" class="fl" type="success" @click="tableOperate('batchUnbindYunshu',item)">{{ $t('common.batchEdit') }}</el-button>
                    <el-button v-if="item.action&&item.action.batchUnbind" class="fl" type="success" @click="tableOperate('batchUnbind',item)">{{ $t('common.batchEdit') }}</el-button>
                    <el-button v-if="item.action&&item.action.chargeOff" class="fl" type="primary" @click="tableOperate('chargeOff',item)">{{ $t('common.chargeOff') }}</el-button>
                    <el-button v-if="item.action&&item.action.chargeOff" class="fl" type="danger" @click="tableOperate('detele',item)">{{ $t('common.batchEdit') }}</el-button>
                    <el-button v-if="!isAdd&&!showBtn" class="fr" type="primary" @click="save(true,item.action.flag)">{{ $t('common.save') }}</el-button>
                  </div>
                  <div v-if="isAdd === false && tableModifyShowCommon(item.modifyBtnShow) || (item.singleEdit&&tableModifyShow(item.singleEditTradition))" class="clearfix" style="margin-bottom:10px">
                    <el-button v-if="item.action&&item.action.exportDetail" class="fl" type="success" @click="tableOperate('exportDetail',item)">{{ $t('common.export') }}</el-button>
                  </div>
                  <!-- 合计 -->
                  <div v-if="item.summary&&item.summaryVal" class="table-summary" style="display: inline-block">
                    {{ $t('common.total') }}：<span>{{ item.summaryVal }}</span>
                  </div>
                  <div v-if="item.summary&&item.getSupplierCostSummary" class="table-summary-supplier">
                    供应商合计:
                    <span v-for="(item, index) in Object.keys(supplierCostSummary)" :key="index" class="table-summary" style="margin-left: 6px">
                      <span style="color: #409eff">{{ item }}</span>：<span>{{ supplierCostSummary[item] }}</span>
                    </span>
                  </div>
                  <!-- todo_c: 多对多的话进行id过滤 -->
                  <el-table :empty-text="$t('content.NoData')" :data="item.multiRelation?formData[item.datavalue]&&formData[item.datavalue].filter(i=>i.relation&&i.relation.length):formData[item.datavalue]" border @selection-change="selectionChange">
                    <el-table-column v-if="(item.action&&item.action.detele&&isAdd) || (item.singleEdit&&tableModifyShow(item.singleEditTradition)) || item.batchEdit || item.batchUnbind || item.batchUnbindYunshu || item.batchUnbindOutbound || (item.action&&item.action.check)" :label="$t('common.operating')" width="40" type="selection" fixed/>
                    <el-table-column v-for="k in item.data" :key="k.prop" :label="k.label">
                      <template slot-scope="scope">
                        <!-- 对错icon -->
                        <div v-if="k.type === 'radio'">
                          <i v-if="scope.row[k.prop]" style="color:green" class="iconfont icon-dui"/>
                          <i v-else style="color:red" class="iconfont icon-cuowu"/>
                        </div>
                        <div v-else-if="k.link"><router-link :to="k.link+'?id='+scope.row[k.link_prop || 'id']" style="color:blue;">{{ scope.row[k.prop] }}</router-link></div>
                        <!-- 多对多 -->
                        <div v-else-if="k.value">
                          {{ k.parseTime?parseTime(scope.row[k.value]&&scope.row[k.value].length&&scope.row[k.value][0][k.prop]):scope.row[k.value]&&scope.row[k.value].length&&scope.row[k.value][0][k.prop] }}
                        </div>
                        <!-- 解决弹窗select和展示冲突-->
                        <div v-else-if="k.type==='select' && k.showValue === true">{{ scope.row[k.prop] || scope.row[k.prop+'_name'] }}</div>
                        <!-- 日期转换 -->
                        <span v-else-if="k.type==='date' || k.type==='datetime' || k.type==='month'">
                          {{ parseTime(scope.row[k.prop], k.type) }}
                        </span>
                        <div v-else-if="k.type==='operate'">
                          <el-button :type="k.buttonType?k.buttonType:'success'" plain @click="customOperate(item, scope.row, k)">{{ k.title }}</el-button>
                        </div>
                        <!--<div v-else-if="k.type==='transferButton'">
                          <el-button type="success" @click="transferDialog(item, scope.row, k)">{{ k.title }}</el-button>
                        </div>-->
                        <!-- todo_s: 正常渲染明细数据 -->
                        <!--<div v-else>{{ scope.row[k.prop] }}</div>-->
                        <div v-else>{{ k.filter ? k.filter[scope.row[k.prop]]: k.type==='select'?scope.row[k.prop+'_name']:k.parseTime?parseTime(scope.row[k.prop]): scope.row[k.prop] }}</div>
                      </template>
                    </el-table-column>
                    <!--是否展示tab页签中每一行数据的修改按钮-->
                    <el-table-column v-if="(item.action&&(item.action.edit||item.action.etcBtn)&&isAdd && tableModifyShowCommon(item.modifyBtnShow)) || (item.singleEdit&&tableModifyShow(item.singleEditTradition))" :label="$t('common.operating')" width="100">
                      <template slot-scope="scope">
                        <el-button v-if="item.action.edit" type="text" @click="tableOperate('edit', item, scope.row,scope.$index+1)">{{ $t('common.modify') }}</el-button>
                        <el-button v-for="k in item.action.etcBtn" :key="k.event" type="text" @click="etcEvent(k, formData[item.datavalue], scope.row, scope.$index)">{{ k.label }}</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
                <!-- tab切换表格 -->
                <template v-else-if="item.type==='tabTable'">
                  <!--只允许已预报/等待作业的订单编辑包裹-->
                  <template v-if="isAdd&&item.hasEditTable&&(!item.editingRestriction||['WO', 'PDC'].includes(formData.order_status)||!formData.order_status)">
                    <!-- 添加按钮 -->
                    <div v-if="isAdd&&!item.tabData[0].noEdit" style="margin-bottom:10px">
                      <el-select v-if="!item.tabData[0].onlyAll" v-model="newParceType" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')" style="width:200px;" filterable @change="switchGoodType()">
                        <el-option :value="1" :label="$t('common.complete')"/>
                        <el-option :value="0" :label="$t('common.simple')"/>
                      </el-select>
                      <el-input v-model.number="count" :placeholder="$t('common.enter')" style="width:220px;margin:0 15px;" type="number">
                        <template slot="prepend">{{ parceType?$t('common.pieces'):$t('common.numbersizes') }}</template>
                        <el-button slot="append" icon="el-icon-check" @click="excelRowSet()"/>
                      </el-input>
                      <el-button v-if="item.tabData[0].primary" :disabled="!formData.parcelItem || formData.parcelItem.length==0" type="primary" @click="summaryGoods()">{{ $t('common.aggregate') }}</el-button>
                    </div>
                    <div v-if="isAdd&&!item.noEdit" style="font-size:12px;">{{ $t('common.with') }} <span style="color:red;font-size:20px;">*</span> {{ $t('common.required') }}<span v-if="!item.tabData[0].onlyAll">，{{ $t('common.iffill') }}</span></div>
                    <div v-if="isAdd&&!item.noEdit" class="hansontable" style="overflow: hidden;">
                      <!--在线excel表格-->
                      <div :style="{overflow: 'auto', height: (30 * (formData.parcelItem&&formData.parcelItem.length || 1) + 70) + 'px'}">
                        <hot-table :parce-type="parceType" :table-type="item.tabData[0].tableType" :tabledata="item.tabData[0].data" :data-value="formData.parcelItem"/>
                      </div>
                    </div>
                  </template>
                  <el-tabs v-else v-model="item.tabValue" type="border-card" style="width:100%;" class="el-tabs-margin" @tab-click="tagClick()">
                    <el-tab-pane v-for="(table,t_index) in item.tabData.filter(i=>i.type!=='editTable')" :key="table.label" :name="t_index+''" :label="table.label" lazy>
                      <!-- 表头按钮 -->
                      <div v-if="isAdd && tableModifyShowCommon(item.modifyBtnShow) || (table.singleEdit&&tableModifyShow(table.singleEditTradition)) || table.showBtn" class="clearfix" style="margin-bottom:10px">
                        <el-button v-if="table.action&&table.action.add" class="fl" type="success" @click="tableOperate('add',table)">{{ $t('common.create') }}</el-button>
                        <el-button v-if="table.action&&table.action.detele" style="margin-right:10px;" class="fl" type="danger" @click="tableOperate('detele',table)">{{ $t('common.delete') }}</el-button>
                        <template v-if="table.headerBtn&&table.headerBtn.length">
                          <span v-for="btn in table.headerBtn" :key="btn.prop">
                            <!-- 导入功能 -->
                            <el-upload
                              v-if="btn.import&&((btn.detail&&id) ||!btn.detail)"
                              ref="hidden_update"
                              :show-file-list="false"
                              :http-request="(data)=>{importExcel(data, btn)}"
                              :limit="10"
                              style="margin-right:10px"
                              class="fl"
                              action="https://jsonplaceholder.typicode.com/posts/">
                              <el-button type="primary">{{ btn.label || $t('content.import') }}</el-button>
                            </el-upload>
                            <!--详情里tab页表格上方的操作按钮-->
                            <el-button v-else-if="(btn.detail&&id) ||!btn.detail" :icon="btn.icon || ''" :type="btn.type || 'success'" style="margin-right:10px" class="fl" @click="etcEvent(btn,table.datavalue.startsWith('$')?extraData[table.datavalue.slice(1)]:formData[table.datavalue])">{{ btn.label }}</el-button>
                          </span>
                        </template>
                      </div>
                      <div v-if="table.type==='form'">
                        <el-col v-for="form_item in table.data" :key="form_item.prop">
                          <span>{{ form_item.label+':' }}</span> <span>{{ formData[table.datavalue]&&formData[table.datavalue][form_item.prop] }}</span>
                        </el-col>
                      </div>
                      <div v-else-if="table.type==='images'">
                        <span v-for="item in formData[table.datavalue]" :key="item.prop" style="margin-right: 6px">
                          <a v-if="item.img_url" :href="getImageUrl(item.img_url)" style="color:#409EFF;" target="_blank">
                            <img :src="getImageUrl(item.img_url)" alt="图片" style="max-width: 83px;max-height: 69px">
                          </a>
                        </span>
                      </div>
                      <el-table v-else :empty-text="$t('content.NoData')" :data="table.datavalue.startsWith('$')?extraData[table.datavalue.slice(1)]:formData[table.datavalue]" border @selection-change="selectionChange">
                        <el-table-column v-if="(table.action&&table.action.detele&&isAdd) || (table.singleEdit&&tableModifyShow(table.singleEditTradition)) || (table.action&&table.action.check)" :label="$t('common.operating')" width="40" type="selection" fixed/>
                        <el-table-column v-for="j in table.data || table.table" :key="j.prop" :prop="j.prop" :label="j.label">
                          <template slot-scope="scope">
                            <!-- 处理次数 -->
                            <div v-if="j.prop==='handle_times' && !j.editable">
                              <div v-if="!scope.row.edit">{{ scope.row[j.prop] }} <el-button icon="el-icon-edit" type="text" @click="changeTimes(true, scope.row )"/></div>
                              <el-input v-else v-model="scope.row.value" :placeholder="$t('content.Required')">
                                <el-button slot="append" icon="el-icon-close" @click="changeTimes(false, scope.row, true )"/>
                                <el-button slot="append" icon="el-icon-check" @click="changeTimes(false, scope.row )"/>
                              </el-input>
                            </div>

                            <!-- 更新尾程订单内容-->
                            <div v-else-if="j.prop==='update_label_order'">
                              <el-button type="primary" @click="updateLabelOrder(scope.row)">更新下单信息
                              </el-button>
                            </div>
                            <!-- 通用筛选项修改 -->
                            <div v-else-if="j.editable&&j.type==='select'">
                              <div v-if="!j.edit">{{ scope.row[j.prop] }} <el-button icon="el-icon-edit" type="text" @click="changeTask(true, j)"/></div>
                              <div v-else>
                                <el-select :no-data-text="$t('content.NoData')" :disabled="j.disabled" v-model="scope.row[j.prop]" :placeholder="$t('common.select')" style="display: inline-block;" clearable filterable >
                                  <el-option
                                    v-for="j in Object.keys(j.filter).map(i=>{return {label:j.filter[i],id: i}})"
                                    :key="j.id"
                                    :label="j.label"
                                    :value="j.id"/>
                                </el-select>
                                <el-button slot="append" icon="el-icon-close" style="margin:0" @click="changeTask(false, j)"/>
                                <el-button slot="append" icon="el-icon-check" style="margin:0" @click="changeTask(false, j, true, scope.row)"/>
                              </div>
                            </div>
                            <!-- 通用输入框修改 -->
                            <div v-else-if="j.editable">
                              <div v-if="!j.edit">{{ scope.row[j.prop] }} <el-button icon="el-icon-edit" type="text" @click="changeTask(true, j)"/></div>
                              <el-input v-else v-model="scope.row[j.prop]" :placeholder="$t('content.Required')">
                                <el-button slot="append" icon="el-icon-close" @click="changeTask(false, j)"/>
                                <el-button slot="append" icon="el-icon-check" @click="changeTask(false, j, true, scope.row)"/>
                              </el-input>
                            </div>
                            <!-- 时间转换 -->
                            <div v-else-if="j.type === 'time'">
                              {{ parseTime(scope.row[j.prop]) }}
                            </div>
                            <!-- 时间转换 -->
                            <div v-else-if="j.type === 'datetime'">
                              {{ parseTime(scope.row[j.prop]) }}
                            </div>
                            <!-- 对错icon -->
                            <div v-else-if="j.type === 'radio'">
                              <i v-if="scope.row[j.prop]" style="color:green" class="iconfont icon-dui"/>
                              <i v-else style="color:red" class="iconfont icon-cuowu"/>
                            </div>
                            <!-- 对错icon -->
                            <div v-else-if="j.type === 'radioCharacter'">
                              <span v-if="scope.row[j.prop]">是</span>
                              <span v-else>否</span>
                            </div>
                            <div v-else-if="j.link"><router-link :to="j.link+'?id='+scope.row[j.link_prop || 'id']" style="color:blue;">{{ scope.row[j.prop] }}</router-link></div>
                            <!-- -->
                            <div v-else-if="j.type==='s_file'">
                              <a v-if="scope.row[j.prop]" :href="'//'+scope.row[j.prop]" style="color:blue;" target="_blank">{{ $t('common.download') }}</a>
                            </div>
                            <div v-else-if="j.type==='is_intercept'">
                              <!--二次确认-->
                              <el-popover
                                :ref="scope.row.id"
                                placement="top"
                                width="180">
                                <p>确定操作本条数据吗？</p>
                                <div style="text-align: right; margin: 0">
                                  <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">取消</el-button>
                                  <el-button v-if="!scope.row.intercept_mark" :loading="delLoading" type="primary" size="mini" @click="interceptParcel(scope.row.id)">确定</el-button>
                                  <el-button v-else :loading="delLoading" type="primary" size="mini" @click="interceptParcel(scope.row.id, 'cancel')">确定</el-button>
                                </div>
                                <el-button v-if="!scope.row.intercept_mark" slot="reference" type="danger">拦截</el-button>
                                <el-button v-else slot="reference" type="warning">取消拦截</el-button>
                              </el-popover>
                            </div>
                            <!-- 展示图片 -->
                            <div v-else-if="j.type==='image'">
                              <a v-if="scope.row[j.prop]" :href="getImageUrl(scope.row[j.prop])" style="color:#409EFF;" target="_blank">
                                <img :src="getImageUrl(scope.row[j.prop])" alt="图片" style="max-width: 83px;max-height: 69px">
                              </a>
                            </div>
                            <!-- 使用原生图片预览 -->
                            <div v-else-if="j.type==='el-image'">
                              <el-image
                                :src="getImageUrl(scope.row[j.prop])"
                                :preview-src-list="[getImageUrl(scope.row[j.prop])]"
                                style="max-width: 83px;max-height: 69px">
                              </el-image>
                            </div>
                            <!-- 增加适用条件 -->
                            <div v-else-if="j.type==='additional_charge'">
                              <!-- <el-button slot="reference" type="warning" @click="subOpen=true">适用条件</el-button>-->
                              <applicability-form ref="applicabilityFormRef" :is-edit="isAdd" :sub-list="scope.row.product_extra_charge_rule"></applicability-form>
                            </div>
                            <div v-else-if="j.type==='truck_options'">
                              <!-- <el-button slot="reference" type="warning" @click="subOpen=true">适用条件</el-button>-->
                              <truck-options-form ref="TruckOptionsFormRef" :is-edit="isAdd" :name="scope.row.inquiry_num" :row-id="scope.row.inquiry_num_id" @send="receiveTruckOptionsForm"></truck-options-form>
                            </div>
                            <div v-else-if="j.type==='extend_field_form'">
                              <extend-field-form ref="extendFieldFormRef" v-model="scope.row[j.prop]" :is-edit="isAdd"></extend-field-form>
                            </div>
                            <!-- 在一个单元格中显示长宽高和打单长宽高-->
                            <div v-else-if="j.pre">
                              <span>{{ (scope.row[j.prop]) }}</span> <span>|</span> <span>{{ (scope.row[j.prop.replace("parcel_", "actual_")]) }}</span>
                            </div>
                            <div v-else-if="j.type==='s_file_url'">
                              <a v-if="scope.row[j.prop]" :href="scope.row[j.prop]" style="color:blue;" target="_blank">{{ $t('common.download') }}</a>
                            </div>
                            <!--cwf最新增加
                            <div v-else-if="j.type==='radios'" >
                              <el-radio v-model="scope.row[j.prop]" :label="true">{{ $t('common.require') }}</el-radio>
                              <el-radio v-model="scope.row[j.prop]" :label="false">{{ $t('common.unRequire') }}</el-radio>
                            </div>
                            -->
                            <!-- 多对多 -->
                            <div v-else-if="j.value">
                              {{ j.parseTime?parseTime(scope.row[j.value]&&scope.row[j.value].length&&scope.row[j.value][0][j.prop]):scope.row[j.value]&&scope.row[j.value].length&&scope.row[j.value][0][j.prop] }}
                            </div>
                            <!--对话框显示文件/图片集-->
                            <div v-else-if="j.type==='dialogFiles'">
                              <!--<a v-if="scope.row[j.prop]" :href="'//'+scope.row[j.prop]" style="color:blue;" target="_blank">查看</a>-->
                              <!--<file-upload v-model="formData[item.prop]" :path-title="requestUrl.baseUrl" :is-add="isAdd"></file-upload>-->
                              <!--<upload-dialog v-model="scope.row[j.prop]" :is-edit="isAdd" :base-url="requestUrl.baseUrl" ></upload-dialog>-->
                              <upload-dialog v-model="scope.row[j.prop]" :file-list="scope.row[j.prop]" :is-edit="true" :row-id="scope.row['id']" :upload-url="j.uploadUrl" :foreignkey="j.foreignkey" ></upload-dialog>
                              <!--<file-upload v-model="scope.row[j.prop]" :path-title="requestUrl.baseUrl" :is-add="isAdd"></file-upload>-->
                            </div>
                            <!-- 正常渲染 -->
                            <div v-else>{{ j.filter ? j.filter[scope.row[j.prop]]: j.type==='select'?j.showInit?scope.row[j.prop]:scope.row[j.prop+'_name']:j.parseTime?parseTime(scope.row[j.prop]): scope.row[j.prop] }}  </div>
                          </template>
                        </el-table-column>
                        <el-table-column v-if="(table.action&&(table.action.edit||table.action.etcBtn)&&isAdd) || (table.singleEdit&&tableModifyShow(table.singleEditTradition))" :label="$t('common.operating')" :width="table.action.width ? table.action.width : 100">
                          <template slot-scope="scope">
                            <el-button v-if="table.action.edit" type="text" @click="tableOperate('edit', table, scope.row,scope.$index+1)">{{ $t('common.modify') }}</el-button>
                            <!--部分入仓和全部入仓状态才可以移除包裹-->
                            <el-button v-for="k in table.action.etcBtn" v-if="k.label!=='移除'||!item.removeRestriction||['PW', 'AW'].includes(formData.order_status)||!formData.order_status" :key="k.event" type="text" @click="etcEvent(k, formData[table.datavalue], scope.row, scope.$index)">{{ k.label }}</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-tab-pane>
                  </el-tabs>
                </template>
                <template v-else-if="item.type==='readOnlyArray' && (isAdd || (detailEdit&&item.detailEdit))">
                </template>
                <template v-else-if="item.type==='hotTable' && $route.query.edit === 'true' && ['DR', 'WO', 'PDC', 'VC'].includes(formData.order_status)">
                  <div :style="{overflow: 'auto', height: (30 * (formData.parcelItem&&formData.parcelItem.length || 1) + 70) + 'px'}">
                    <hot-table :parce-type="1" :tabledata="item.tableHeadData" :data-value="formData.parcelItem"/>
                  </div>
                </template>
                <template v-else>
                  <!-- 表单 -->
                  <el-form-item :label="item.type==='blank'||item.type==='hidden'?'':item.label+':'" :prop="item.prop" style="margin-bottom:20px;">
                    <!--可编辑详情页-->
                    <template v-if="isAdd || (detailEdit&&item.detailEdit) || tableModifyShowCommon(item.editCondition, false)">
                      <!-- 空一格 -->
                      <div v-if="item.type==='blank'" style="height:29px;"/>
                      <!-- 隐藏字段 -->
                      <div v-else-if="item.type==='hidden'" style="height:29px;"/>
                      <!-- 单选 -->
                      <div v-else-if="item.type==='radio'" style="height:29px;">
                        <el-radio :disabled="item.disabled" v-model="formData[item.prop]" :label="true" @change="radioChange(formData[item.prop],item)"> {{ $t('common.radioTrue') }} </el-radio>
                        <el-radio :disabled="item.disabled" v-model="formData[item.prop]" :label="false" @change="radioChange(formData[item.prop],item)"> {{ $t('common.radioFalse') }} </el-radio>
                      </div>
                      <!-- 日期选择 -->
                      <el-date-picker
                        v-else-if="item.type==='date' || item.type==='daterange' || item.type=== 'datetime' || item.type=== 'daterangerange'"
                        v-model="formData[item.prop]"
                        :type="item.type"
                        :disabled="item.disabled"
                        :value-format="item.type==='date' || item.type==='daterange'?'yyyy-MM-dd':'yyyy-MM-ddTHH:mm:ss'"
                        :placeholder="$t('common.selectDate')"
                        :picker-options="item.restrict==='laterDate'? expireTimeOption: true"
                        style="width:100%"/>
                      <!-- 币种选择框 -->
                      <el-select v-else-if="item.type==='current'" :no-data-text="$t('content.NoData')" :disabled="item.disabled" v-model="formData[item.prop]" :placeholder="$t('common.select')" style="width:100%;" clearable filterable>
                        <el-option
                          v-for="item in initCurrency"
                          :key="item.prop"
                          :label="item.prop"
                          :value="item.prop"/>
                      </el-select>
                      <!-- 自带下拉过滤 -->
                      <el-select v-else-if="item.type==='select'&&item.filter" :no-data-text="$t('content.NoData')" :disabled="item.disabled" v-model="formData[item.prop]" :placeholder="$t('common.select')" style="width:100%;" clearable filterable @change="selectChangeGet(formData[item.prop],optionData[item.prop],item)">
                        <el-option
                          v-for="item in Object.keys(item.filter).map(i => {
                            const isInt = /^-?\d+$/.test(i)
                            return {
                              label: item.filter[i],
                              id: isInt ? Number(i) : i
                            }
                          })"
                          :key="item.id"
                          :label="item.label"
                          :value="item.id"/>
                      </el-select>
                      <!-- 详情页数据下拉框 -->
                      <el-select v-else-if="item.type === 'select'" :no-data-text="$t('content.NoData')" :multiple="item.multiSelect" :disabled="item.disabled" v-model="formData[item.prop]" :placeholder="$t('common.select')" style="width:100%;" clearable filterable @change="selectChangeGet(formData[item.prop],optionData[item.prop],item)">
                        <el-option
                          v-for="k in optionData[item.prop]&&optionData[item.prop]['data']"
                          :key="k[optionData[item.prop]&&optionData[item.prop]['value']]"
                          :label="item.show === 'nameAndShortName' ? `${k['final_name']}` : k[optionData[item.prop]&&optionData[item.prop]['label']]"
                          :value="k[optionData[item.prop]&&optionData[item.prop]['value']]"/>
                      </el-select>
                      <!-- 下拉list -->
                      <el-select v-else-if="item.type==='range_list'" v-model="formData[item.prop]" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')" style="width:100%;" clearable filterable>
                        <el-option
                          v-for="item in Array.from(new Array(101).keys())"
                          :key="item"
                          :label="item+'%'"
                          :value="item"/>
                      </el-select>
                      <!--todo_s: 填写预计件数同步更新包裹数量-->
                      <div v-else-if="item.type==='preCartonChange'">
                        <el-input v-model="formData[item.prop]" :placeholder="$t('common.enter')" type="number" :disabled="item.disabled" @change="preCartonChange(item)"/>
                      </div>
                      <!-- 收件人和发件人弹窗组件 -->
                      <div v-else-if="item.type==='dialogselect'" style="margin-bottom: 10px;margin-top: 5px">
                        <el-select v-model="formData[item.prop]" :no-data-text="$t('content.NoData')" :multiple="item.multiSelect" :placeholder="$t('common.select')" filterable clearable style="width:calc(100% - 62px);" @change="addSelect('change', item)">
                          <el-option
                            v-for="k in optionData[item.prop]&&optionData[item.prop]['data']"
                            :key="k[optionData[item.prop]&&optionData[item.prop]['value']]"
                            :label="k[optionData[item.prop]&&optionData[item.prop]['label']]"
                            :value="k[optionData[item.prop]&&optionData[item.prop]['value']]"/>
                        </el-select>
                        <!-- <el-button @click="addSelect('add', item)">{{ $t('common.add') }}</el-button>
                        <el-input v-model="item.textVal" :rows="6" disabled resize="none" style="margin-top: 20px;" type="textarea"/> -->
                      </div>
                      <!-- 单附件上传 -->
                      <el-upload
                        v-else-if="item.type==='files' && item.single && !item.addHiden"
                        :ref="'upload' + item.type"
                        :on-remove="(file, fileList) => singlehandleRemove(file, fileList, item)"
                        :before-remove="(file, fileList) => beforeRemove(file, fileList, item, true)"
                        :on-change="(file, fileList) => singleFileChange(file, fileList, item)"
                        :auto-upload="false"
                        :file-list="item.fileList"
                        :limit="1"
                        :on-exceed="handleExceedOne"
                        :http-request="uploadSingleAttchment(item)"
                        class="upload-demo"
                        action="https://jsonplaceholder.typicode.com/posts/"
                      >
                        <el-button size="small" type="success">{{ $t('common.clickupload') }} {{ item.label }}</el-button>
                      </el-upload>
                      <!-- 自定义文件上传 -->
                      <el-upload
                        v-else-if="item.type==='customFiles' && item.single && !item.addHiden"
                        :ref="'upload' + item.type"
                        :on-remove="(file, fileList) => singlehandleRemove(file, fileList, item)"
                        :before-remove="(file, fileList) => beforeRemove(file, fileList, item, true)"
                        :on-change="(file, fileList) => singleCustomFileChange(file, fileList, item)"
                        :auto-upload="false"
                        :file-list="item.fileList"
                        :limit="1"
                        :on-exceed="handleExceedOne"
                        :http-request="uploadSingleAttchment(item)"
                        class="upload-demo"
                        action="https://jsonplaceholder.typicode.com/posts/"
                      >
                        <el-button size="small" type="success">{{ $t('common.clickupload') }} {{ item.label }}</el-button>
                      </el-upload>
                      <!-- todo_c: 多附件上传 -->
                      <el-upload
                        v-else-if="item.type==='files' && !item.addHiden"
                        ref="upload"
                        :on-remove="(file, fileList) => handleRemove(file, fileList, item)"
                        :before-remove="(file, fileList) => beforeRemove(file, fileList, item, false)"
                        :before-upload="beforeUpload"
                        :on-change="fileChange"
                        :auto-upload="true"
                        :limit="5"
                        :on-exceed="handleExceedFive"
                        :http-request="(file, fileList) =>uploadAttchment(file, item)"
                        :file-list="fileList"
                        class="upload-demo"
                        action="https://jsonplaceholder.typicode.com/posts/"
                        multiple
                      >
                        <el-button size="small" type="success">{{ $t('common.clickupload') }}</el-button>
                      </el-upload>
                      <el-upload
                        v-else-if="item.type==='files_upload'"
                        ref="upload"
                        :on-remove="(file, fileList2) => handleRemove(file, item.fileList, item)"
                        :before-remove="(file, fileList2) => beforeRemove(file, item.fileList, item, false)"
                        :before-upload="beforeUpload"
                        :on-change="fileChange"
                        :auto-upload="true"
                        :limit="5"
                        :http-request="(file, fileList2) =>uploadAttchment(file,item)"
                        :file-list="item.fileList"
                        class="upload-demo"
                        action="https://jsonplaceholder.typicode.com/posts/"
                      >
                        <el-button size="small" type="success">{{ $t('common.clickupload') }}</el-button>
                      </el-upload>
                      <!-- 兼容一个页面有2个文件上传 -->
                      <el-upload
                        v-else-if="item.type==='files2'"
                        ref="upload"
                        :on-remove="(file, fileList2) => handleRemove(file, fileList2, item)"
                        :before-remove="(file, fileList2) => beforeRemove(file, fileList2, item, false)"
                        :before-upload="beforeUpload"
                        :on-change="fileChange"
                        :auto-upload="true"
                        :limit="5"
                        :http-request="(file, fileList2) =>uploadAttchment(file,item)"
                        :file-list="fileList2"
                        class="upload-demo"
                        action="https://jsonplaceholder.typicode.com/posts/"
                      >
                        <el-button size="small" type="success">{{ $t('common.clickupload') }}</el-button>
                      </el-upload>
                      <file-upload v-else-if="item.type==='files3'" :is-show-tip="true" :file-type="item.fileType" v-model="formData[item.prop]" :path-title="requestUrl.baseUrl" :detail-edit="isAdd"></file-upload>

                      <div v-else-if="item.type==='truck_options'">
                        <!-- <el-button slot="reference" type="warning" @click="subOpen=true">适用条件</el-button>-->
                        <truck-options-form ref="TruckOptionsFormRef" :name="formData.inquiry_num" :is-edit="isAdd" :sub-list="truck_options" :row-id="formData.truck_inquiry" @send="receiveTruckOptionsForm"></truck-options-form>
                      </div>

                      <!-- 自动添加-输入框 -->
                      <el-input v-else-if="item.type==='autoInput'" :disabled="item.disabled" v-model="formData[item.prop]" :placeholder="item.readOnly?'':$t('common.enter')"/>
                      <!-- 分格输入框 -->
                      <div v-else-if="item.type==='splitInput'" class="clearfix">
                        <el-input :disabled="item.disabled" v-model="formData[item.prop]" :placeholder="item.readOnly?'':$t('common.enter')" style="width:calc(50% - 10px);float:left"/>
                        <span style="width:20px;display:inline-block;text-align:center;float:left">-</span>
                        <el-input :disabled="item.disabled" v-model="formData[item.prop+'$']" :placeholder="item.readOnly?'':$t('common.enter')" style="width:calc(50% - 10px);float:left"/>
                      </div>
                      <div v-else-if="item.type==='readOnlyArray'" style="height:29px;"/>
                      <div v-else-if="item.type==='remoteSelect'">
                        <el-select
                          v-model="formData[item.prop]"
                          :placeholder="item.config.placeholder || '请输入关键词前缀'"
                          :filterable="item.config.filterable || true"
                          :remote="item.config.remote || true"
                          :remote-method="query => defaultRemoteMethod(query, item.config)"
                          :reserve-keyword="item.config.reserveKeyword || true"
                          :loading="item.config.loading || false"
                          style="width:100%">
                          <el-option
                            v-for="option in remoteSelectOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value">
                          </el-option>
                        </el-select>
                      </div>
                      <!--快递信息自动识别内容渲染区域-->
                      <div v-else-if="item.type==='textarea' && item.contentRecognition===true">
                        <el-input :type="item.type" :autosize="{ minRows: 6, maxRows: 8}" :disabled="item.disabled" :maxlength="item.maxlength" v-model="formData[item.prop]" :placeholder="'请按类似如下格式填写，否则可能识别不成功！\n取货时间: xx月xx日上午xx点\n地址: xx省xx市xxxxxxx 张三 123****12\n数量: xxx'"/>
                        <el-button :loading="buttonLoadingState" type="primary" round style="margin-top: 10px;" @click="contentRecognition(item)">内容识别</el-button>
                      </div>
                      <!-- 文本域或者文本 -->
                      <el-input v-else :type="item.type || 'text'" :disabled="item.disabled" :maxlength="item.maxlength" v-model="formData[item.prop]" :placeholder="item.placeholderStr || $t('common.enter')" @focus="formEvent('focus',item)" @blur="formEvent('blur',item)" @change="formEvent('change',item)" @keyup.enter.native="formEvent('enter',item)"/>
                    </template>
                    <!--不可编辑详情页-->
                    <template v-else>
                      <!-- 对错icon -->
                      <div v-if="item.type === 'radio'">
                        <i v-if="formData[item.prop]" style="color:green" class="iconfont icon-dui"/>
                        <i v-else style="color:red" class="iconfont icon-cuowu"/>
                      </div>
                      <!-- 单文件 -->
                      <div v-else-if="item.type === 'files'&& item.single">
                        <a v-if="formData[item.prop]" :href="getSingleFileName(item,false)" style="color:blue;font-weight:bold;" target="_blank">
                          <!-- {{ item.prop==='main_file'? formData['order_num']||formData['clearance_num'] + formData[item.prop].substring(formData[item.prop].lastIndexOf('.')):item.prop==='cabin_file'?formData['order_num']||formData['clearance_num']+'-mainfest' +formData[item.prop].substring(formData[item.prop].lastIndexOf('.')):formData['truck_order_num'] + formData[item.prop].substring(formData[item.prop].lastIndexOf('.')) }} -->
                          {{ getSingleFileName(item,true) }}
                        </a>
                      </div>
                      <!-- 多附件 -->
                      <div v-else-if="item.type === 'files'">
                        <!-- 多附件上传 -->
                        <el-upload
                          ref="upload"
                          :on-remove="(file, fileList) =>handleRemove(file, fileList,item)"
                          :before-remove="(file, fileList) => beforeRemove(file, fileList, item, false)"
                          :before-upload="beforeUpload"
                          :on-change="fileChange"
                          :show-file-list="false"
                          :auto-upload="true"
                          :limit="10"
                          :http-request="(file, fileList) =>uploadAttchment(file,item)"
                          :file-list="fileList"
                          class="upload-demo"
                          action="https://jsonplaceholder.typicode.com/posts/"
                        >
                          <el-button size="small" type="success">{{ $t('common.clickupload') }}</el-button>
                        </el-upload>
                        <div v-for="item in formData[item.prop]" :key="item.id" style="color:#000;font-weight:bold;">
                          &lt;&lt;<a :href="item.url.substr(0,item.url.indexOf('/api')).replace('http://','//') + item.url.substr(item.url.indexOf('/media'))" style="color:blue;" target="_blank">{{ item.name }}</a>&gt;&gt;
                        </div>
                      </div>
                      <div v-else-if="item.type==='files_show'">
                        <ul style="list-style-type: none;padding: 0;margin: 0;">
                          <li v-for="(item, index) in item.fileList" :key="index">
                            <a :href="item.url.substr(0,item.url.indexOf('/api')).replace('http://','//') + item.url.substr(item.url.indexOf('/media'))" style="color:blue;" target="_blank">{{ item.name }}</a>
                          </li>
                        </ul>
                      </div>
                      <div v-else-if="item.type==='files_upload'" >
                        <el-upload
                          ref="upload"
                          :on-remove="(file, fileList2) => handleRemove(file, item.fileList, item)"
                          :before-remove="(file, fileList2) => beforeRemove(file, item.fileList, item, false)"
                          :before-upload="beforeUpload"
                          :on-change="fileChange"
                          :auto-upload="true"
                          :show-file-list="false"
                          :limit="5"
                          :http-request="(file, fileList2) =>uploadAttchment(file,item)"
                          :file-list="item.fileList"
                          class="upload-demo"
                          action="https://jsonplaceholder.typicode.com/posts/"
                        >
                          <el-button size="small" type="success">{{ $t('common.clickupload') }}</el-button>
                        </el-upload>
                        <div v-for="item in item.fileList" :key="item.id" style="color:#000;font-weight:bold;">
                          &lt;&lt;<a :href="item.url.substr(0,item.url.indexOf('/api')).replace('http://','//') + item.url.substr(item.url.indexOf('/media'))" style="color:blue;" target="_blank">{{ item.name }}</a>&gt;&gt;
                        </div>
                      </div>
                      <div v-else-if="item.type === 'files2'">
                        <!-- 多附件上传 -->
                        <el-upload
                          ref="upload"
                          :on-remove="(file, fileList2) =>handleRemove(file, fileList2,item)"
                          :before-remove="(file, fileList2) => beforeRemove(file, fileList2, item, false)"
                          :before-upload="beforeUpload"
                          :on-change="fileChange"
                          :show-file-list="false"
                          :auto-upload="true"
                          :limit="10"
                          :http-request="(file, fileList2) =>uploadAttchment(file,item)"
                          :file-list="fileList2"
                          class="upload-demo"
                          action="https://jsonplaceholder.typicode.com/posts/"
                        >
                          <el-button size="small" type="success">{{ $t('common.clickupload') }}</el-button>
                        </el-upload>
                        <div v-for="item in formData[item.prop]" :key="item.id" style="color:#000;font-weight:bold;">
                          &lt;&lt;<a :href="item.url.substr(0,item.url.indexOf('/api')).replace('http://','//') + item.url.substr(item.url.indexOf('/media'))" style="color:blue;" target="_blank">{{ item.name }}</a>&gt;&gt;
                        </div>
                      </div>
                      <div v-else-if="item.type === 'files3'" >
                        <file-upload v-model="formData[item.prop]" :path-title="requestUrl.baseUrl" :is-add="isAdd"></file-upload>
                      </div>

                      <div v-else-if="item.type==='truck_options'">
                        <!-- <el-button slot="reference" type="warning" @click="subOpen=true">适用条件</el-button>-->
                        <truck-options-form ref="TruckOptionsFormRef" :is-edit="isAdd" :name="formData.inquiry_num" :sub-list="truck_options" :row-id="formData.truck_inquiry" @send="receiveTruckOptionsForm"></truck-options-form>
                      </div>
                      <div v-else-if="item.type === 'datetime'">
                        {{ parseTime(formData[item.prop]) }}
                      </div>
                      <div v-else-if="item.type === 'readOnlyArray'">
                        <ul style="list-style-type: none;padding: 0;margin: 0;">
                          <li v-for="(item, index) in formData[item.prop]" :key="index">
                            {{ item }}
                          </li>
                        </ul>
                      </div>
                      <div v-else :style="{height: item.type==='dialogselect'?'180px':'28px', color: judgeHighLightCondition(formData[item.prop], item) && item.type==='highLight' ? item.color : ''}">
                        <!-- 这里初始化详情 select -->
                        <div>{{ item.filter? item.filter[formData[item.prop]]: item.type==='select'||item.type==='dialogselect'?formData[item.prop+'_name'] || formData[item.prop]: item.type==='blank'?'':formData[item.prop] }}</div>
                        <el-input v-if="item.type==='dialogselect'" v-model="item.textVal" :rows="item.prop==='shipper'||item.prop==='receiver'?8:6" disabled resize="none" style="margin-bottom:20px" type="textarea"/>
                      </div>
                    </template>
                  </el-form-item>
                </template>
              </el-col>
            </el-row>
          </panel>
        </div>
      </template>
    </el-form>
    <!-- tab页签的弹窗 -->
    <el-dialog :visible.sync="dialogShow" :width="dialogType==='select'?'500px':dialogForm.filter(i=>i.readOnly).length<8?'500px':'800px'" :close-on-click-modal="false" :title="dialogTitle" @close="closeDialog">
      <el-form ref="detailForm" :model="dialogFormData" :rules="dialogRules">
        <!-- 选择框添加 -->
        <el-row v-if="dialogType==='select'">
          <el-select v-model="dialogSelect" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')" style="width:100%;" clearable filterable>
            <el-option
              v-for="k in dialogOptionData['dialogSelect']&&dialogOptionData['dialogSelect']['data']"
              :key="k[dialogOptionData['dialogSelect']&&dialogOptionData['dialogSelect']['value']]"
              :label="k[dialogOptionData['dialogSelect']&&dialogOptionData['dialogSelect']['label']]"
              :value="k[dialogOptionData['dialogSelect']&&dialogOptionData['dialogSelect']['value']]"/>
          </el-select>
        </el-row>
        <!-- 表单添加 -->
        <el-row v-else>
          <el-col v-for="(item,index) in dialogForm.filter(i=>!i.readOnly)" :key="item.prop" :sm="dialogForm.filter(i=>i.readOnly).length<8 || item.type==='textarea'?24:(index+1)%2===1?11:{span: 11, offset: 2}" :xs="24">
            <!--隐藏tab页签的编辑对话框中的字段-->
            <el-form-item v-if="item.type!=='hidden' && item.type!=='operate'" :label="item.label+':'" :prop="item.prop" style="margin-bottom:10px;">
              <template v-if="item.type==='radio'">
                <el-radio v-model="dialogFormData[item.prop]" :disabled="item.readOnly" :label="true">{{ $t('common.radioTrue') }}</el-radio>
                <el-radio v-model="dialogFormData[item.prop]" :disabled="item.readOnly" :label="false">{{ $t('common.radioFalse') }}</el-radio>
              </template>
              <el-date-picker
                v-else-if="item.type==='date' || item.type==='daterange' || item.type=== 'datetime' || item.type=== 'daterangerange'"
                :disabled="item.readOnly"
                v-model="dialogFormData[item.prop]"
                :type="item.type"
                :value-format="item.type==='date' || item.type==='daterange'?'yyyy-MM-dd':'yyyy-MM-ddTHH:mm:ss'"
                :placeholder="$t('common.selectDate')"
                style="width:100%"/>
              <el-select v-else-if="item.type==='current'" :no-data-text="$t('content.NoData')" :disabled="item.readOnly" v-model="dialogFormData[item.prop]" :placeholder="$t('common.select')" style="width:100%;" clearable filterable>
                <el-option
                  v-for="item in initCurrency"
                  :key="item.prop"
                  :label="item.prop"
                  :value="item.prop"/>
              </el-select>
              <el-select v-else-if="item.type==='selectSource'" :no-data-text="$t('content.NoData')" :disabled="item.readOnly" v-model="dialogFormData[item.prop]" :placeholder="$t('common.select')" :multiple="item.multiSelect" style="width:100%;" clearable filterable >
                <el-option
                  v-for="ite in (formData[item.filter_source.source] || []).filter(i=> i[item.filter_source.label]!==dialogFormData[item.filter_source.label])"
                  :key="ite[item.filter_source.label]"
                  :label="ite[item.filter_source.label]"
                  :value="ite[item.filter_source.label]"/>
              </el-select>
              <el-select v-else-if="item.type==='select'&&item.filter" :no-data-text="$t('content.NoData')" :disabled="item.disabled" v-model="dialogFormData[item.prop]" :placeholder="$t('common.select')" style="width:100%;" clearable filterable >
                <el-option
                  v-for="item in Object.keys(item.filter).map(i => {
                    const isInt = /^-?\d+$/.test(i)
                    return {
                      label: item.filter[i],
                      id: isInt ? Number(i) : i
                    }
                  })"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"/>
              </el-select>
              <div v-else-if="item.type==='remoteSelect'">
                <el-select
                  v-model="dialogFormData[item.prop]"
                  :placeholder="item.config.placeholder || '请输入关键词前缀'"
                  :filterable="item.config.filterable || true"
                  :clearable="item.config.clearable || true"
                  :remote="item.config.remote || true"
                  :remote-method="query => dialogRemoteSelectMethod(query, item.config, item.prop)"
                  :reserve-keyword="item.config.reserveKeyword || true"
                  :loading="item.config.loading || false"
                  style="width:100%"
                  @change="selectChange(item.prop, dialogOptionData[item.prop]&&dialogOptionData[item.prop]['data'], item)">
                  <el-option
                    v-for="k in dialogOptionData[item.prop]&&dialogOptionData[item.prop]['data']"
                    :key="k[dialogOptionData[item.prop]&&dialogOptionData[item.prop]['value']]"
                    :label="k[dialogOptionData[item.prop]&&dialogOptionData[item.prop]['label']]"
                    :value="k[dialogOptionData[item.prop]&&dialogOptionData[item.prop]['value']]">
                  </el-option>
                </el-select>
              </div>
              <!--详情页弹窗下拉框-->
              <!--<el-select v-else-if="item.type==='select'" v-model="dialogFormData[item.prop]" :placeholder="$t('common.select')" style="width:100%;" clearable filterable @change="selectChange(item.prop,dialogOptionData[item.prop]&&dialogOptionData[item.prop]['data'])">-->
              <el-select v-else-if="item.type==='select'" v-model="dialogFormData[item.prop]" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')" style="width:100%;" clearable filterable @change="selectChange(item.prop, dialogOptionData[item.prop]&&dialogOptionData[item.prop]['data'], item)">
                <el-option
                  v-for="k in dialogOptionData[item.prop]&&dialogOptionData[item.prop]['data']"
                  :key="k[dialogOptionData[item.prop]&&dialogOptionData[item.prop]['value']]"
                  :label="item.show === 'nameAndAffiliated' ? k.join_name : k[dialogOptionData[item.prop]&&dialogOptionData[item.prop]['label']]"
                  :value="k[dialogOptionData[item.prop]&&dialogOptionData[item.prop]['value']]"/>
              </el-select>
              <div v-else-if="item.type==='transferButton'">
                <br>
                <!--<div>{{ dialogFormData[item.prop] }}</div>-->
                <!--<el-input :type="item.type || 'text'" :readonly="item.readOnly" :disabled="item.disabled" v-model="dialogFormData[item.prop]" placeholder="不选择出仓包裹则默认自动选择" style="display: inline-block; width: 50%" @change="transferDialog(item, false)"/>-->
                <el-input :type="item.type || 'text'" :readonly="item.readOnly" :disabled="item.disabled" v-model="dialogFormData[item.prop]" placeholder="不选择出仓包裹则默认自动选择" style="display: inline-block; width: 50%"/>
                <!--<br>-->
                <el-button type="success" @click="transferDialog(item)">{{ item.title }}</el-button>
              </div>
              <el-input v-else :type="item.type || 'text'" :readonly="item.readOnly" :disabled="item.disabled" v-model="dialogFormData[item.prop]" :placeholder="item.ktype === 'lengths' ? $t('common.lengths') : (item.ktype === 'censored' ? $t('common.censored') : (item.readOnly ? '' : $t('common.enter')))"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <span v-if="syncRevenue" style="margin-left: auto;" class="fl">
          <el-checkbox v-model="isSyncRevenue">同时添加一条收入</el-checkbox>
        </span>
        <el-button @click="closeDialog()">{{ $t('common.cancle') }}</el-button>
        <el-button type="primary" @click="comfirm">{{ $t('common.sure') }}</el-button>
      </div>
    </el-dialog>
    <!--自定义对话框-->
    <el-dialog
      :visible="innerDialogVisible"
      :before-close="innerHandleClose"
      :close-on-click-modal="false"
      :width="fullScreen? '70%':'740px'"
      :title="actionParams.data.label ? actionParams.data.label : $t('common.select') + ' '+actionParams.data.label">
      <div v-for="item in innerDialogTableFilters" ref="inputContainer" :key="item.prop" :style="{ display: 'inline-block', float: item.type === 'input' ? 'right' : 'none' }" style="display: inline-block; ">
        <!-- 列表页头部下拉框 -->
        <template v-if="item.type === 'select'">
          <el-select v-model="item.value" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')+' '+item.placeholder" :multiple="item.multiple" :class="{linghtUp:item.value}" style="width:132px;" clearable filterable @focus="getSelectData(item)" @change="innerDialogQuery(item.value)">
            <el-option
              v-for="i in item.data"
              :key="i.id"
              :label="item.show === 'nameAndShortName' ? `${i.final_name}` : i[item['label']] || i.label || i.name || i.short_name || i.code || i.order_num"
              :value="item.submit?i[item.submit]:i.id"/>
          </el-select>
        </template>
        <!-- 单选布尔值 -->
        <template v-if="item.type === 'boolean'">
          <el-select v-model="item.value" :no-data-text="$t('content.NoData')" :class="{linghtUp:item.value===true||item.value===false}" :placeholder="item.placeholder" style="width:100px;" clearable filterable @change="innerDialogQuery(item.value)">
            <el-option :value="true" :label="$t('common.radioTrue')"/>
            <el-option :value="false" :label="$t('common.radioFalse')"/>
          </el-select>
        </template>
      </div>
      <!--搜索框-->
      <template v-if="innerTable.searchable">
        <div class="fr" style="display: inline-block">
          <el-input v-model="innerDialogSearch.value" :placeholder="innerTable.dialogSearchHolder || $t('common.enter')" clearable style="width: 240px;" @keyup.enter.native="innerDialogQuery(innerDialogSearch.value)">
            <el-button slot="append" icon="el-icon-search" @click="innerDialogQuery(innerDialogSearch.value)"></el-button>
          </el-input>
        </div>
      </template>
      <!-- 批量搜索 -->
      <el-button v-if="innerTable.multiSearch&&innerTable.multiSearch.length" class="hvr-float-shadow filter-item fr" type="primary" @click="innerMultiSearchShow=true">{{ $t('common.batchSearch') }}</el-button>
      <!-- 表格筛选 -->
      <el-table v-if="innerTable.dialogType === 'table'" ref="dialogTable" :empty-text="$t('content.NoData')" :data="innerTableData" max-height="320px" size="small" border style="width: 100%;" @selection-change="selectionChangeInner">
        <el-table-column type="selection" fixed width="40"/>
        <el-table-column v-for="item in innerTable.colums" :key="item.prop" :label="item.label" :prop="item.prop" :width="item.width">
          <template slot-scope="scope">
            <div v-if="item.type === 'input'">
              <el-input v-model="scope.row[item.prop]" :placeholder="$t('common.enter')" type="number"/>
            </div>
            <div v-else-if="item.type==='radio'">
              <i v-if="scope.row[item.prop] === true || (Array.isArray(scope.row[item.prop]) && scope.row[item.prop].length)" style="color:green" class="iconfont icon-dui"/>
              <i v-else style="color:#838181" class="iconfont icon-cuowu"/>
            </div>
            <div v-else-if="item.type==='operate'">
              <el-button type="danger" @click="dialogOperate(scope.row, item)">{{ item.title }}</el-button>
            </div>
            <div v-else>
              {{ item.filters?item.filters[scope.row[item.prop]]:scope.row[item.prop] }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerHandleClose()">{{ $t('common.cancel') }}</el-button>
        <el-button v-if="innerTable.purpose==='chargeOff'" type="primary" @click="executeChargeOffDebit({method:innerTable.method,check:true})">{{ $t('common.chargeOff') }}</el-button>
        <el-button v-else-if="!innerTable.noConfirm" type="primary" @click="innerDialogConfirm()">{{ $t('common.sure') }}</el-button>
      </span>
      <!--二层对话框表格分页组件-->
      <el-pagination
        v-if="dialogType === 'table'"
        :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
        :total="innerDialogTotal"
        :page-size="innerDialogSize"
        :current-page="innerDialogPage"
        style="margin-top: 8px;"
        layout="total, prev, pager, next, sizes"
        @size-change="innerSizeChangeDialog"
        @current-change="innerPageChangeDialog"/>
    </el-dialog>
    <!--transfer对话框-->
    <el-dialog
      :append-to-body="true"
      :visible.sync="transferDialogVisible"
      :close-on-click-modal="false"
      title="选择出仓包裹"
      width="850px">
      <!--<div v-if="actionParams.data.overheadButton" style="display: flex; align-items: center; margin-bottom: 10px">-->
      <!--<div style="display: flex; align-items: center; margin-bottom: 10px">
        <el-input v-model="autoSelectedCount" placeholder="请输入个数" type="number" style="width: 160px; margin-right: 10px" clearable/>
        <el-button type="success" @click="autoSelect">自动选择</el-button>
      </div>-->
      <template>
        <el-transfer
          v-model="actionParams.data.selectData"
          :titles="['未出仓包裹', '出仓包裹']"
          :props="{
            key: 'id',
            label: 'parcel_format'
          }"
          :data="transferDialogFormData"
          filterable>
        </el-transfer>
      </template>
      <span slot="footer" class="dialog-footer">
        <el-button @click="transferDialogVisible=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="innerDialogConfirm(true)">{{ $t('common.sure') }}</el-button>
      </span>
    </el-dialog>
    <!-- 添加收件人  发件人弹窗 -->
    <el-dialog :visible.sync="dialogselectShow" :close-on-click-modal="false" :title="`${$t('common.add')}${select_item.prop.startsWith('s')?$t('common.addresser'):$t('common.addressee')}`" width="800px">
      <el-form :model="dialogFormData">
        <el-row>
          <el-col v-for="(item,index) in adressForm" :key="item.prop" :sm="(index+1)%2===1?11:{span: 11, offset: 2}" :xs="24">
            <el-form-item :label="$t('common.'+item.prop)+':'" :prop="item.prop" style="margin-bottom:10px;">
              <template v-if="item.type==='radio'">
                <el-radio v-model="adressFormData[item.prop]" :label="true">是</el-radio>
                <el-radio v-model="adressFormData[item.prop]" :label="false">否</el-radio>
              </template>
              <el-input v-else v-model="adressFormData[item.prop]" :placeholder="$t('common.enter')" type="text"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addSelect('cancle')">{{ $t('common.cancle') }}</el-button>
        <el-button type="primary" @click="addSelect('comfirm')">{{ $t('common.sure') }}</el-button>
      </div>
    </el-dialog>
    <!--todo_c: 账单核销弹窗-->
    <el-dialog :visible.sync="dialogChargeOffShow" :close-on-click-modal="false" :title="`${$t('common.chargeOff')}${$t('common.bill')}`" width="650px">
      <el-form :model="dialogFormData">
        <el-table :empty-text="$t('content.NoData')" :data="selection" border @selection-change="selectionChange" >
          <!--<el-table-column v-if="(item.action&&item.action.detele&&isAdd) || (item.singleEdit&&tableModifyShow(item.singleEditTradition)) || item.batchEdit || item.batchUnbindYunshu || item.batchUnbindOutbound || item.action.check" :label="$t('common.operating')" width="40" type="selection" fixed/>-->
          <el-table-column v-for="item in chargeOffTable" :key="item.prop" :label="item.label" :prop="item.prop" :width="item.width">
            <template slot-scope="scope">
              <div v-if="item.type === 'input'">
                <el-input v-model="scope.row[item.prop]" :placeholder="$t('common.enter')" type="number"/>
              </div>
              <div v-else>
                {{ scope.row[item.prop] }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addChargeOffSelect('cancel')">{{ $t('common.cancle') }}</el-button>
        <el-button type="primary" @click="addChargeOffSelect('confirm')">{{ $t('common.sure') }}</el-button>
      </div>
    </el-dialog>
    <!--todo_c: 更新客户用户昵称密码弹窗-->
    <el-dialog :title="title" :visible.sync="dialogUserUpdateOffShow" :width="dWidth">
      <el-form ref="dialogUserUpdateForm" :model="dialogUserUpdateData" label-width="80px">
        <el-form-item label="姓名">
          <el-input v-model="dialogUserUpdateData.name"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="dialogUserUpdateData.password"></el-input>
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input v-model="dialogUserUpdateData.confirm"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onUserUpdateSubmit">确认</el-button>
          <el-button @click="onUserUpdateCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getChoiceData, edit, add, del, get, actionPost, initMutilData } from '@/api/data'
import { parseTime } from '@/utils'
import HotTable from '@/components/InitTable/hansontable'
import { download_Template } from '@/utils'
import { axiosExport } from '@/utils/index'
import ApplicabilityForm from '../ApplicabilityForm'
import TruckOptionsForm from '../TruckOptionsForm'
import ExtendFieldForm from '../ExtendFieldForm'
import FileUpload from '../FileUpload'
import UploadDialog from '../FileUpload/UploadDialog'
import request from '@/utils/request'
import { getToken } from '@/utils/auth' // getToken from cookie
import { judgeHighLightCondition } from '../../utils/common'

// import item from '../../views/layout/components/Sidebar/Item.vue'
// import en from '../../lang/en'

export default {
  components: {
    ApplicabilityForm,
    TruckOptionsForm,
    ExtendFieldForm,
    HotTable,
    FileUpload,
    UploadDialog
  },
  props: {
    deleteProp: {
      type: Array,
      default: () => { return [] }
    },
    initData: {
      type: Array,
      required: true
    },
    deteleDisabled: {
      type: Boolean,
      default: false
    },
    editDisabled: {
      type: Object,
      default: () => { return {} }
    },
    rules: {
      type: Object,
      required: true
    },
    isAdd: {
      type: Boolean,
      required: true
    },
    // 部分字段详情页面修改
    detailEdit: {
      type: Boolean,
      default: false
    },
    requestUrl: {
      type: Object,
      default: () => {}
    },
    option: {
      type: Array,
      default: () => []
    },
    dialogOption: {
      type: Array,
      default: () => []
    },
    detailApi: {
      type: Array,
      default: () => []
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    modifyBtnShow: {
      type: Object,
      default: () => { return {} }
    },
    formModifyBtnShow: {
      type: Object,
      default: () => { return {} }
    },
    setOtherVal: {
      type: Array,
      default: () => { return [] }
    },
    bottomBtn: {
      type: Array,
      default: () => { return [] }
    },
    language: {
      type: String,
      default: 'cn'
    },
    authModify: {
      type: String,
      default: null
    },
    customizationRules: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    extendFields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 按钮点击后是否显示加载中
      buttonLoadingState: false,
      rightLoading: false, // 是否正确加载详情
      // 初始parceType
      initParceType: null,
      firstEntry: true,
      goods: [], // 商品
      propsLabelsMap: {},
      extraData: {
        parce: [],
        goods: [],
        parcel_goods: []
        // relative_invoices: [],
        // relative_debits: []
      },
      parce: [], // 包裹
      pay_amount: null,
      // init: true,
      // 下拉数据
      optionData: {},
      dialogOptionData: {},
      // formData: {},
      dialogShow: false,
      dialogType: '',
      dialogForm: [],
      dialogFormData: {},
      dialogSelect: '',
      dialogVal: '',
      selection: [],
      selectIndex: null,
      initCurrency: [
        { prop: 'CNY' },
        { prop: 'USD' },
        { prop: 'GBP' },
        { prop: 'HKD' },
        { prop: 'EUR' },
        { prop: 'CAD' },
        { prop: 'CHF' },
        { prop: 'AUD' },
        { prop: 'MXN' }
      ],
      dialogTitle: '',
      activeName: '0',
      // 保存loading
      flag: false,
      // 在线Excel
      count: 0,
      parceType: 1,
      // tableType: '',
      newParceType: 1,
      // 文件上传
      fileList: [],
      fileList2: [],
      fileList3: new Map(),
      remoteSelectOptions: [],
      waitToUpload: new FormData(),
      // 收件人和发件人文本域
      text_receiver: '',
      text_shipper: '',
      dialogselectShow: false,
      dialogChargeOffShow: false,
      dialogUserUpdateOffShow: false,
      dialogUserUpdateData: {
        name: '',
        password: '',
        confirm: ''
      },
      chargeOffTable: [],
      adressFormData: {},
      // 弹窗操作的项目
      select_item: { prop: '' },
      // 单个文件上传
      singlefile: new FormData(),
      // 弹窗表单验证规则
      dialogRules: {},
      // 汇总合计当前选项
      currentTable: {},
      syncRevenue: false,
      isSyncRevenue: false,
      // 接收弹框id
      rowId: 0,
      supplierCostSummary: {},
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      filterProp: {},
      headers: { Authorization: 'Bearer ' + getToken() },
      innerDialogVisible: false,
      innerDialogTableFilters: [],
      fullScreen: false,
      innerTable: {},
      innerDialogTotal: null,
      innerDialogPage: 1,
      innerDialogSize: 10,
      innerDialogOrdering: '-id',
      innerDialogSearch: { value: '' },
      innerMultiSearchShow: false,
      innerTableData: [],
      innerMultiParams: {},
      innerTablewithFilter: '0',
      actionParams: {
        methods: '',
        data: {
          name: '',
          label: '',
          purpose: '',
          overheadButton: '',
          overheadTitle: '',
          showRebinding: '',
          method: '',
          batchMethod: '',
          width: '',
          selectData: []
        }
      },
      dialogBtn: null,
      transferDialogVisible: false,
      transferDialogFormData: [],
      autoSelectedCount: null
    }
  },
  computed: {
    params() {
      const data = JSON.parse(JSON.stringify(this.formData))
      if (this.extendFields && !data.json_extend) {
        data.json_extend = {}
      }
      console.log('this.formData-->', this.formData)
      Object.keys(data).forEach(k => {
        if (this.extendFields.includes(k)) {
          data.json_extend[k] = data[k]
          delete data[k]
        }
        if (k.endsWith('$')) {
          const val = k.substring(0, k.length - 1)
          data[val] = data[val] + '-' + data[k]
        }
      })
      console.log('json_extend', data['json_extend'])
      // 设置包裹类型
      data['parceType'] = this.parceType
      return {
        api: this.requestUrl.baseUrl,
        id: this.id,
        data
      }
    },
    id() {
      return this.$route.query.id
    },
    // 过滤是否显示修改按钮
    showBtn() {
      const len = (Object.keys(this.modifyBtnShow)).length
      const tem = new Array(len).fill(false)
      if (len === 0) {
        return true
      } else if (this.$route.query.is_copy && this.$route.path.includes('customerOrderTask_fbm')) {
        return true
      } else {
        let flag = true
        Object.keys(this.modifyBtnShow).forEach((i, index) => {
          // 支持 or条件
          if (i === 'conditions_or') {
            const conditions = this.modifyBtnShow[i]
            flag = this.parseConditions(conditions)
          } else if (i.startsWith('$')) {
            if (this.modifyBtnShow[i] instanceof Array) {
              flag = !this.modifyBtnShow[i].includes(this.formData[i.substr(1)])
            } else {
              flag = this.formData[i.substr(1)] !== this.modifyBtnShow[i]
            }
          } else if (this.modifyBtnShow[i] instanceof Array) {
            flag = this.modifyBtnShow[i].includes(this.formData[i])
          } else {
            flag = this.formData[i] === this.modifyBtnShow[i]
          }
          tem[index] = flag
        })
        return tem.every(i => i)
      }
    },
    filterRules() {
      const filteredRules = {}
      for (const field in this.rules) {
        filteredRules[field] = this.rules[field].filter(rule => {
          return !rule.targetCompany || rule.targetCompany.includes(process.env.SYS_FLAG)
        })
      }
      return filteredRules
    },
    // 收件人发件人弹窗form
    adressForm() {
      return [
        {
          label: '地址编码',
          prop: 'address_num'
        },
        {
          label: '联系人',
          prop: 'contact_name'
        },
        {
          label: '公司名',
          prop: 'company_name'
        },
        {
          label: '邮箱',
          prop: 'contact_email'
        },
        {
          label: '电话',
          prop: 'contact_phone'
        },
        {
          label: '国家编码',
          prop: 'country_code'
        },
        {
          label: '省份(州)编码',
          prop: 'state_code'
        },
        {
          label: '城市编码',
          prop: 'city_code'
        },
        {
          label: '邮编',
          prop: 'postcode'
        },
        {
          label: '门牌号',
          prop: 'house_no'
        },
        {
          label: '地址行1',
          prop: 'address_one'
        },
        {
          label: '地址行2',
          prop: 'address_two'
        },
        {
          label: '是否同步至地址库',
          prop: 'async',
          type: 'radio'
        }
      ]
    },
    isEn() {
      return this.language && this.language === 'en'
    },
    allParams() {
      return { ...this.$route.params, ...this.$route.query }
    }
  },
  watch: {
    // 包裹类型变化
    parceType(n, o) {
      if (!this.firstEntry) {
        this.$set(this.formData, 'weight', null)
        this.$set(this.formData, 'volume', null)
        this.$set(this.formData, 'carton', null)
        this.$set(this.formData, 'charge_weight', null)
        this.$set(this.formData, 'volume_weight', 0)
        this.$set(this.formData, 'parcelItem', [])
        this.count = 0
      } else {
        this.firstEntry = false
        if (this.$route.query.id && this.initParceType) {
          this.$set(this.formData, 'weight', null)
          this.$set(this.formData, 'volume', null)
          this.$set(this.formData, 'carton', null)
          this.$set(this.formData, 'charge_weight', null)
          this.$set(this.formData, 'volume_weight', 0)
          this.$set(this.formData, 'parcelItem', [])
          this.count = 0
        }
      }
    }
    // formData: {
    //   deep: true,
    //   handler: function(n, o) {
    //     const t = this.hasAutoInput()
    //     t.forEach(i => {
    //       if (n[i.prop] && n[i.prop].length > 3 && n[i.prop].charAt(3) !== '-') {
    //         this.formData[i.prop] = o[i.prop].slice(0, 3) + '-' + o[i.prop].slice(3)
    //       }
    //     })
    //   }
    // }
  },
  created() {
    // console.log('created')
    // this.initFormData()
  },
  mounted() {
    console.log('mounted')
    // const id = this.id
    // if (id) return
    // this.initChoiceData()
    // this.initDefaultVal()
  },
  activated() {
    console.log('activated')
    this.detailEdit = false
    this.initFormData()
    if (this.id) return
    // 新增直接置为true
    this.rightLoading = true
    this.initChoiceData()
    this.initDefaultVal()
    this.copy()
    this.handleRadioShow()
    this.createRelatedOrder()
    this.taskdatacopy()
  },
  methods: {
    judgeHighLightCondition,
    taskdatacopy() {
      // 复制任务数据
      this.formData = {}
      this.modify(true)
      if (this.$store.state.basicInfo.task_id) {
        // 清除之前的定时器
        if (this.pollingTimer) {
          clearInterval(this.pollingTimer)
          this.pollingTimer = null
        }
        console.log('开始轮询', this.$store.state.basicInfo.task_id)
        this.bus.$emit('fullLoading2', true, '正在解析文件，请稍候...')
        this.pollingTimer = setInterval(() => {
          getChoiceData('/api/consignmentOrderInfos/task_result/', {
            task_id: this.$store.state.basicInfo.task_id
          }).then(res => {
            console.log('轮询结果:', res.code)

            if (res.code === 200) {
              // 检查任务状态
              if (res.data.status === 'SUCCESS') {
                // 任务成功，加载数据
                this.formData = res.data.result
                // 清除定时器
                if (this.pollingTimer) {
                  clearInterval(this.pollingTimer)
                  this.pollingTimer = null
                }
                this.bus.$emit('fullLoading2', false)
                this.$message.success('数据获取成功')
              } else if (res.data.status === 'FAILURE') {
                // 任务失败，结束轮询并返回上一个界面
                if (this.pollingTimer) {
                  clearInterval(this.pollingTimer)
                  this.pollingTimer = null
                }
                this.bus.$emit('fullLoading2', false)
                this.$message.error('任务处理失败')
                // 返回上一个界面
                this.$router.go(-1)
              } else if (res.data.status === 'PENDING') {
                // 任务处理中，更新loading文字
                this.bus.$emit('fullLoading2', true, '解析文件中，请稍候...')
              }
              // 如果状态是其他值，继续轮询
            }
          }).catch(err => {
            console.error('轮询出错:', err)
            // 清除定时器
            if (this.pollingTimer) {
              clearInterval(this.pollingTimer)
              this.pollingTimer = null
            }
            this.bus.$emit('fullLoading2', false)
            this.$message.error('数据获取失败')
          })
        }, 1000) // 每秒轮询一次

        // 组件销毁时清除定时器（重要！）
        this.$once('hook:beforeDestroy', () => {
          if (this.pollingTimer) {
            clearInterval(this.pollingTimer)
            this.pollingTimer = null
          }
        })
      }
    },
    copy() {
      if (this.$store.state.basicInfo.data) {
        console.log('开始复制单据-->', this.$store.state.basicInfo.data)
        // this.formData = this.$store.state.basicInfo.data
        // 去掉路径中的开头和结尾的斜杠，并按斜杠分隔
        const segments = this.$route.path.replace(/^\/|\/$/g, '').split('/')
        // 获取倒数第二个元素
        const api = segments[segments.length - 2] || ''
        const datas = {
          api: api + 's',
          id: this.$store.state.basicInfo.data.id
        }
        console.log('复制api-->', api)
        console.log('copy参数输出', datas)
        get(datas).then(res => {
          console.log(res)
          if (res.code === 200) {
            this.formData = res.data
            if (api === 'oceanOrder') {
              this.formData['order_num'] = null
              this.formData['order_status'] = 'SM'
              this.formData['actual_arrivals_date'] = null
              this.formData['create_date'] = this.formData['update_date'] = null
              this.formData['container_no'] = null
              this.formData['oceanOrderTrack'] = []
              this.formData['ocean_customer_order'] = []
              this.formData['carton'] = null
              this.formData['weight'] = null
              this.formData['volume'] = null
              this.formData['customer_carton'] = null
              this.formData['customer_weight'] = null
              this.formData['customer_volume'] = null
              this.formData['charge_total'] = null
              this.formData['charge_weight'] = null
              this.formData['clearance_num'] = null
              this.formData['clearanceOut_num'] = null
            } else if (api === 'inbound') {
              delete this.formData['del_flag']
              this.formData['order_status'] = 'DR'
              // 将子元素复制到最顶层
              this.getValByOtherVal()
              this.deleteKeysRecursively(this.formData, ['id', 'update_by', 'create_by', 'update_date', 'create_date',
                'inboundOrderChargeIns', 'inboundOrderChargeOuts', 'deduction_time', 'completed_time', 'sync_status', 'order_time', 'warehouse_task_log',
                'pre_shelf_carton', 'shelf_carton', 'position_status', 'carton', 'received_carton', 'weight', 'volume', 'charge_weight', 'bubble_weight', 'volume_weight'])
            } else if (api === 'outbound') {
              delete this.formData['del_flag']
              // 将子元素复制到最顶层
              this.getValByOtherVal()
              this.deleteKeysRecursively(this.formData, ['id', 'update_by', 'create_by', 'update_date', 'create_date', 'outboundOrderChargeIns', 'outboundOrderChargeOuts', 'outboundOrderLabelTasks', 'warehouse_task_log', 'tracking_num'])
              this.formData['order_status'] = 'DR'
            }
            delete this.formData.id
            for (const arr in this.formData) {
              if (this.formData[arr] === null || this.formData[arr] === undefined || this.formData[arr].length <= 0 || !this.formData[arr]) {
                delete this.formData[arr]
              }
            }
            // JLL remark
            if ((res.data.parcel && res.data.parcel.length !== 0) || res.data.parcel_size && res.data.parcel_size.length !== 0) {
              this.$set(this.formData, 'parcelItem', [])
              this.count = 0
              // 包裹汇总
              this.extraData.goods = []
              this.extraData.parcel_goods = []
              this.extraData.parce = JSON.parse(JSON.stringify(res.data.parceType === undefined || res.data.parceType === true ? res.data.parcel : res.data.parcel_size))
              if (this.parceType) {
                // 完整
                this.extraData.parce.map(i => {
                  i.parcelItem.forEach(k => {
                    this.count += 1
                    // 商品汇总
                    this.extraData.goods.push(k)
                    k.parcel_number = i.parcel_num
                    this.extraData.parcel_goods.push(k)
                    // 在线表格的汇总
                    this.formData.parcelItem.push({
                      // p_id: i.id,
                      // g_id: k.id,
                      // 包裹
                      parcel_num: i.parcel_num,
                      reference_id: i.reference_id,
                      shipment_id: i.shipment_id,
                      parcel_weight: i.parcel_weight,
                      parcel_length: i.parcel_length,
                      parcel_width: i.parcel_width,
                      parcel_height: i.parcel_height,
                      remark: i.remark,
                      label_weight: i.label_weight,
                      label_length: i.label_length,
                      label_width: i.label_width,
                      label_height: i.label_height,
                      actual_weight: i.actual_weight,
                      actual_length: i.actual_length,
                      actual_width: i.actual_width,
                      actual_height: i.actual_height,
                      // 商品
                      item_code: k.item_code,
                      declared_nameCN: k.declared_nameCN,
                      declared_nameEN: k.declared_nameEN,
                      declared_price: k.declared_price,
                      item_qty: k.item_qty,
                      item_weight: k.item_weight,
                      // item_size: k.item_size,
                      texture: k.texture,
                      use: k.use,
                      brand: k.brand,
                      model: k.model,
                      customs_code: k.customs_code,
                      fba_no: k.fba_no,
                      fba_track_code: k.fba_track_code,
                      item_picture: k.item_picture
                    })
                  })
                })
              } else {
                // 简易
                this.extraData.parce.map(i => {
                  this.count += 1
                  // 在线表格的汇总
                  this.formData.parcelItem.push({
                    // p_id: i.id,
                    // 包裹
                    parcel_num: i.parcel_num,
                    parcel_weight: i.parcel_weight,
                    parcel_length: i.parcel_length,
                    parcel_width: i.parcel_width,
                    parcel_height: i.parcel_height,
                    actual_weight: i.actual_weight,
                    actual_length: i.actual_length,
                    actual_width: i.actual_width,
                    actual_height: i.actual_height,
                    parcel_qty: i.parcel_qty,
                    remark: i.remark
                  })
                })
              }
              delete this.formData['parcel']
              delete this.formData['tracks']
              delete this.formData['order_time']
              delete this.formData['create_date']
              delete this.formData['update_date']
              delete this.formData['parcelOrderLabelTasks']
              delete this.formData['parcelItem']
              delete this.formData['parcel_customer_order_charge_out']
              delete this.formData['parcel_customer_order_charge_in']
              delete this.formData['is_weighing']
              delete this.formData['weighing_weight']
              delete this.formData['inbound_time']
              delete this.formData['is_revenue_lock']
              delete this.formData['is_cost_lock']
              delete this.formData['income']
              delete this.formData['cost']
              delete this.formData['big_parcel']
              delete this.formData['third_orderNo']
              delete this.formData['tracking_num']
              delete this.formData['order_type']
              delete this.formData['intercept_mark']
              delete this.formData['real_product']
              delete this.formData['big_parcel_name']
              this.formData['order_status'] = 'DR'
            } else {
              this.extraData.parce = []
              this.extraData.goods = []
              this.extraData.parcel_goods = []
            }
          }
          delete this.formData['remark']
          delete this.formData['recipientInfo']
          delete this.formData['shipperInfo']
        })

        // for (let arr in this.formData) {
        //   if (this.formData[arr] == null || this.formData[arr].length <= 0 || !this.formData[arr]) {
        //     delete this.formData[arr]
        //   }
        // }
        // this.$store.commit('editData', null)
        console.log('复制单据完成-->', this.formData)
      }
    },
    createRelatedOrder() {
      const rowData = this.$store.state.basicInfo.relatedOrderData
      if (rowData) {
        console.log('开始创建关联单据-->', this.$store.state.basicInfo.relatedOrderData)
        // this.formData = this.$store.state.basicInfo.data
        // 去掉路径中的开头和结尾的斜杠，并按斜杠分隔
        const segments = this.$route.path.replace(/^\/|\/$/g, '').split('/')
        // 获取倒数第二个元素
        const api = segments[segments.length - 2] || ''
        if (!['outboundInstruct'].includes(api)) {
          return
        }

        if (api === 'outboundInstruct') {
          console.log('this.formData是个啥-->', this.formData)
          this.formData['appointment_time'] = rowData['scheduled_time']
          this.formData['details'] = []
          this.formData['details'].push({
            'shipment_id': rowData['shipment_id'],
            'reference_id': rowData['reference_id'],
            'shop_type': rowData['shop_type'],
            'parcel_qty': rowData['parcel_qty'] || 0,
            'outbound_box': rowData['outbound_box'] || 0
          })
        }

        // const datas = {
        //   api: api + 's',
        //   id: this.$store.state.basicInfo.data.id
        // }
        // console.log('创建关联单据, 参数输出', datas)
        // get(datas).then(res => {
        //   console.log('创建关联单据, get data', res)
        //   if (res.code === 200) {
        //     if (api === 'outboundInstruct') {
        //       console.log('this.formData是个啥-->', this.formData)
        //       this.formData['appointment_time'] = res.data['scheduled_time']
        //       this.formData['details'] = []
        //       this.formData['details'].push({
        //         'shipment_id': res.data['shipment_id'],
        //         'reference_id': res.data['reference_id'],
        //         'shop_type': res.data['shop_type'],
        //         'parcel_qty': res.data['parcel_qty'],
        //         'outbound_box': res.data['outbound_box']
        //       })
        //     }
        //     delete this.formData.id
        //     // for (const arr in this.formData) {
        //     //   if (this.formData[arr] === null || this.formData[arr] === undefined || this.formData[arr].length <= 0 || !this.formData[arr]) {
        //     //     delete this.formData[arr]
        //     //   }
        //     // }
        //   }
        // })
        console.log('创建关联单据完成-->', this.formData)
      }
    },
    deleteKeysRecursively(obj, removeKeys) {
      for (const key in obj) {
        // 如果属性是对象，则递归调用
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          this.deleteKeysRecursively(obj[key], removeKeys)
        }
        if (removeKeys.includes(key)) {
          delete obj[key]
        }
      }
    },
    parseTime,
    // 判断当前表单有无autoInput类型的输入框
    // hasAutoInput() {
    //   const t = []
    //   this.initData.forEach(i => {
    //     i.formDataInit.forEach(k => {
    //       if (k.type === 'autoInput') {
    //         t.push(k)
    //       }
    //     })
    //   })
    //   return t
    // },
    handleRadioShow() {
      console.log('handleRadioShow-->')
      this.initData.forEach(i => {
        i.formDataInit.forEach(k => {
          if (k.type === 'radio' && k.change) {
            k.change.forEach(item => {
              if (item.type === 'hidden' && item.showVal === this.formData[k.prop]) {
                if (item.showVal === false) {
                  this.radioChange(false, k)
                } else if (item.showVal === true) {
                  this.radioChange(true, k)
                }
              }
            })
          }
        })
      })
    },
    // 新增设置默认值
    initDefaultVal() {
      console.log('initDefaultVal-->')
      this.initData.forEach(i => {
        i.formDataInit.forEach(k => {
          if (k.type === 'radio' && (k.defaultVal === false || k.defaultVal === true)) {
            this.$set(this.formData, k.prop, k.defaultVal)
          }
          if (k.type !== 'table' && k.type !== 'blank' && (k.default || (typeof k.defaultVal !== undefined && k.defaultVal === null))) {
            this.$set(this.formData, k.prop, k.defaultVal)
          }
          // 支持跳转设置默认值
          if (this.allParams && this.allParams[k.prop]) {
            this.$set(this.formData, k.prop, this.allParams[k.prop])
          }
          if (k.defaultValue) {
            if (!this.formData[k.prop]) {
              this.$set(this.formData, k.prop, k.defaultValue)
              console.log('k.prop Value1-->', this.formData[k.prop])
            }
          } else if (k.default !== undefined) {
            // 处理 default 属性
            if (!this.formData[k.prop]) {
              this.$set(this.formData, k.prop, k.default)
              console.log('k.prop default-->', this.formData[k.prop])
            }
          } else if (k.getDefaultValue) {
            // console.log('详情页getDefaultValue-->', k.getDefaultValue())
            // 如果有 getDefaultValue 函数，调用它获取最新的默认值
            if (!this.formData[k.prop]) {
              this.$set(this.formData, k.prop, k.getDefaultValue())
              console.log('k.prop Value2-->', this.formData[k.prop])
            }
          }
        })
      })
    },
    filesUrl(item) {
      const path = this.$route.path
      let url = ''
      let order = ''
      console.log('filesUrl path-->', path)
      if (item.prop === 'signForAttachments') {
        // 上传访问的url
        url = 'orderSignForAttachments'
        // 上传的附件关联主单的外键字段名
        order = 'customerOrder'
      } else if (path.includes('customerOrder')) {
        url = 'orderAttachments'
        order = 'customerOrder'
      } else if (path.includes('masterOrder')) {
        url = 'masterAttachments'
        order = 'masterOrder'
      } else if (path.includes('oceanOrder')) {
        url = 'oceanAttachments'
        order = 'oceanOrder'
      } else if (path.includes('truckOrder')) {
        url = 'truckAttachments'
        order = 'truckOrder'
      } else if (path.includes('houseOrder')) {
        url = 'houseAttachments'
        order = 'houseOrder'
      } else if (path.includes('payment/')) {
        url = 'paymentAttachments'
        order = 'payment'
      } else if (path.includes('clearanceOrder')) {
        url = 'clearanceAttachments'
        order = 'clearanceOrder'
      } else if (path.includes('accountDetail')) {
        url = 'accountDetailAttachments'
        order = 'account_detail'
      } else if (path.includes('billing')) {
        url = 'billingAttachments'
        order = 'billing'
      } else if (path.includes('receipt')) {
        url = 'receiptAttachments'
        order = 'receipt'
      } else if (path.includes('claimOrder')) {
        url = 'claimOrderAttachments'
        order = 'claimOrder'
      } else if (path.includes('case')) {
        url = 'caseAttachments'
        order = 'case'
      } else if (path.includes('paymentAdjust')) {
        url = 'paymentAdjustAttachments'
        order = 'payment_adjust'
      } else if (path.includes('debitAdjust')) {
        url = 'debitAdjustAttachments'
        order = 'debit_adjust'
      } else if (path.includes('inquiryPrice')) {
        url = 'inquiryPriceAttachments'
        order = 'order'
      } else if (path.includes('company')) {
        url = 'companyAttachment'
        order = 'company'
      } else if (path.includes('clearanceOutOrder')) {
        url = 'clearanceOutOrderAttachment'
        // 这里order需要填写的是ClearanceOutAttachment中的外键: clearance_out
        order = 'clearance_out'
        if (item.prop === 'pre_recording_attachments') {
          url = 'clearanceOutOrderPreRecordingAttachment'
        } else if (item.prop === 'release_attachments') {
          url = 'clearanceOutOrderReleaseAttachment'
        }
      } else if (path.includes('vasOrder')) {
        url = 'vasOrderAttachments'
        order = 'vasOrder'
      } else if (path.includes('returnOrder')) {
        url = 'customerReturnOrderAttachments'
        order = 'returnOrder'
      } else if (path.includes('expressManager')) {
        url = 'expressManagerFile'
        order = 'voucher'
      } else {
        url = item.url
        // order = this.requestUrl.baseUrl
        order = item.mainOrder
      }
      return {
        url,
        order
      }
    },
    // 获取详情附属信息
    getDetailAtach() {
      this.detailApi.forEach(i => {
        const params = {
          api: i.api,
          data: {
            ids: this.formData[i.value]
          }
        }
        actionPost(params).then(res => {
          this.$set(this.formData, i.prop, res.data)
        })
      })
      // if (this.$route.path === '/order/customerOrder/detail') {
      //   this.summaryGoods()
      // }
    },
    // 初始化获取下拉数据等
    initChoiceData() {
      const params = {
        size: 100000,
        page: 1,
        sort: 'id'
      }
      // 下拉数据获取
      this.option.forEach((i, index, arr) => {
        if (!i.getSignal || i.getSignal && Object.keys(i.getSignal).every(k => this.formData[k] === i.getSignal[k])) {
          // 特殊处理：如果是export_recorder且客户有值，跳过初始化
          if (i.prop === 'export_recorder' && this.formData && this.formData.customer) {
            console.log('跳过export_recorder初始化，因为客户有值')
            return
          }
          if (i.query) {
            Object.keys(i.query).forEach(key => {
              if (key === 'id' && i.query[key] === 'this.id') {
                i.query[key] = this.id
              }
            })
          }
          getChoiceData(i.api, { ...params, ...i.query }).then(res => {
            this.$set(this.optionData, i.prop,
              {
                data: res.data || res.results,
                label: i.label || 'name',
                value: i.value || 'id'
              }
            )
            // console.log('this.optionData-->', this.optionData)
          })
        }
      })
      // 条件下拉数据获取
      this.initData.forEach(i => {
        i.formDataInit.forEach(k => {
          if (this.formData[k.prop] && k.apis) {
            Object.keys(k.apis).forEach(j => {
              // eslint-disable-next-line
              if (this.formData[k.prop] === j || (k.apis&&k.apis.api)) {
                this.selectChangeGet('', '', k, false, true)
              }
            })
          }
        })
      })
    },
    // 初始化获取详情
    initFormData() {
      console.log('initFormData-->')
      const id = this.id
      if (!id) {
        // 变更首次进入
        this.firstEntry = false
        return
      }
      this.$emit('switchAdd', false)
      this.bus.$emit('fullLoading', true)
      // 获取初始数据
      get(this.params).then(res => {
        if (res.code === 200) {
          console.log('首次加载获取详情', res.data)
          // 正确加载
          this.rightLoading = true
          for (const k in res.data) {
            if (k !== 'parcel' && !this.deleteProp.includes(k)) {
              this.$set(this.formData, k, res.data[k])
            }
            if (k === 'json_extend' && this.extendFields) {
              this.formData = { ...this.formData, ...res.data['json_extend'] }
            }
          }
          console.log('formData赋值后', this.formData)
          // 切换显示隐藏初始化
          const change_tem = []
          this.initData.forEach(i => {
            i.formDataInit.forEach(k => {
              if (k.change && k.change.length && k.change.some(j => j.type === 'hidden' || j.type === 'visible')) {
                change_tem.push(k)
              }
            })
          })
          // change_tem.forEach(item => {
          //   item.change.forEach(i => {
          //     if (i.showVal === this.formData[item.prop]) {
          //       this.initData.forEach(t => {
          //         t.formDataInit.forEach(k => {
          //           this.$set(k, '$hide', i.hiddenProp.includes(k.prop))
          //         })
          //       })
          //     }
          //   })
          // })
          change_tem.forEach(item => {
            this.change_hidden_prop_show(item)
          })
          // 判断是否不可编辑
          this.initData.forEach(i => {
            i.formDataInit.forEach(k => {
              if (k.disabledCondition && this.judgeCondition(k.disabledCondition)) {
                this.$set(k, 'disabled', true)
              }
            })
          })
          // 通过一个字段的值设置另一个字段
          this.getValByOtherVal()
          // 设置转单信息处理次数编辑
          if (this.formData.orderLabelTasks) {
            this.formData.orderLabelTasks.forEach(i => {
              this.$set(i, 'edit', false)
              this.$set(i, 'value', i.handle_times)
            })
          }
          // 设置转单信息处理次数编辑
          if (this.formData.parcelOrderLabelTasks) {
            this.formData.parcelOrderLabelTasks.forEach(i => {
              this.$set(i, 'edit', false)
              this.$set(i, 'value', i.handle_times)
            })
          }
          // 设置转单信息处理次数编辑
          if (this.formData.outboundOrderLabelTasks) {
            this.formData.outboundOrderLabelTasks.forEach(i => {
              this.$set(i, 'edit', false)
              this.$set(i, 'value', i.handle_times)
            })
          } else if (this.formData.bigParcelLabelTasks) {
            this.formData.bigParcelLabelTasks.forEach(i => {
              this.$set(i, 'edit', false)
              this.$set(i, 'value', i.handle_times)
            })
          } else if (this.formData.insuranceOrderTasks) {
            this.formData.insuranceOrderTasks.forEach(i => {
              this.$set(i, 'edit', false)
              this.$set(i, 'value', i.handle_times)
            })
          } else if (this.formData.customsClearanceOrderSupplierTasks) {
            this.formData.customsClearanceOrderSupplierTasks.forEach(i => {
              this.$set(i, 'edit', false)
              this.$set(i, 'value', i.handle_times)
            })
          }
          // 直接点编辑
          if (this.$route.query.edit === 'true') {
            this.modify(true)
          }
          // 收件人和发件人文本域回显
          this.initData.forEach(i => {
            i.formDataInit.forEach(k => {
              k.textVal = ''
              if (k.prop === 'shipper') {
                if (res.data['clearanceAddressList']) {
                  const shipper = res.data['clearanceAddressList'].find(item => item.address_type === 'SHIPPER')
                  if (shipper) {
                    k.textVal = `${shipper['company_name'] || ''}\n${shipper['address_one'] || ''},${shipper['house_no'] || ''}\n${shipper['address_two'] || ''}\n${shipper['city_code'] || ''},${shipper['state_code'] || ''},${shipper['country_code'] || ''},${shipper['postcode'] || ''}\n${shipper['contact_name'] || ''}${shipper['contact_email'] || ''}`
                  }
                } else {
                  k.textVal = `${res.data['company_name'] || ''}\n${res.data['address_one'] || ''},${res.data['house_no'] || ''}\n${res.data['address_two'] || ''}\n${res.data['city_code'] || ''},${res.data['state_code'] || ''},${res.data['country_code'] || ''},${res.data['postcode'] || ''}\n${res.data['contact_name'] || ''}${res.data['contact_email'] || ''}`
                }
              }
              if (k.prop === 'receiver') {
                if (res.data['clearanceAddressList']) {
                  const receivers = res.data['clearanceAddressList'].filter(item => item.address_type === 'receiver')
                  if (receivers) {
                    let i = 1
                    for (const shipper of receivers) {
                      console.log(shipper)
                      k.textVal += `转运仓${i}:\n  ${shipper['company_name'] || ''}\n  ${shipper['address_one'] || ''},${shipper['house_no'] || ''}\n  ${shipper['address_two'] || ''}\n  ${shipper['city_code'] || ''},${shipper['state_code'] || ''},${shipper['country_code'] || ''},${shipper['postcode'] || ''}\n  ${shipper['contact_name'] || ''}${shipper['contact_email'] || ''}\n\n`
                      i++
                    }
                  }
                } else {
                  k.textVal = `${res.data['buyer_company_name'] || ''}\n${res.data['buyer_address_one'] || ''},${res.data['buyer_house_num'] || ''}\n${res.data['buyer_address_two'] || ''}\n${res.data['buyer_city_code'] || ''},${res.data['buyer_state'] || ''},${res.data['buyer_country_code'] || ''},${res.data['buyer_postcode'] || ''}\n${res.data['buyer_name'] || ''}${res.data['buyer_mail'] || ''}`
                }
              }
              if (k.prop === 'notify_party') {
                if (res.data['clearanceAddressList']) {
                  const shipper = res.data['clearanceAddressList'].find(item => item.address_type === 'notify_party')
                  if (shipper) {
                    k.textVal = `${shipper['company_name'] || ''}\n${shipper['address_one'] || ''},${shipper['house_no'] || ''}\n${shipper['address_two'] || ''}\n${shipper['city_code'] || ''},${shipper['state_code'] || ''},${shipper['country_code'] || ''},${shipper['postcode'] || ''}\n${shipper['contact_name'] || ''}${shipper['contact_email'] || ''}`
                  }
                } else {
                  k.textVal = `${res.data['buyer_company_name'] || ''}\n${res.data['buyer_address_one'] || ''},${res.data['buyer_house_num'] || ''}\n${res.data['buyer_address_two'] || ''}\n${res.data['buyer_city_code'] || ''},${res.data['buyer_state'] || ''},${res.data['buyer_country_code'] || ''},${res.data['buyer_postcode'] || ''}\n${res.data['buyer_name'] || ''}${res.data['buyer_mail'] || ''}`
                }
              }
              // 表格右上角合计
              if (k.type === 'table' && k.summary) {
                const dataArray = this.formData[k.prop] || [] // 如果为undefined则使用空数组
                this.$set(k, 'summaryVal', dataArray.reduce((cur, next) => {
                  return this.cal.accAdd(cur, next.charge_total, 4)
                }, 0))
              }
              // 表格右上角供应商成本合计
              if (k.type === 'table' && k.getSupplierCostSummary) {
                actionPost({
                  api: `/api/${this.requestUrl.baseUrl}/${k.getSupplierCostSummary}/`,
                  data: { id: this.$route.query.id }
                }).then(res => {
                  if (res.code === 200) {
                    this.supplierCostSummary = res.data
                  } else {
                    this.$message.error(res.detail || res.message || res.msg)
                  }
                  // this.bus.$emit('fullLoading', false)
                }).catch(() => { this.bus.$emit('fullLoading', false) })
              }
            })
          })
          // 如果有附件进行回显(点击编辑后回显已存在的附件)
          const att_a = res.data.attachments || res.data.relative_attachments || []
          if (Array.isArray(att_a)) {
            console.log('附件回显att_a-->', att_a)
            this.fileList = att_a.map(k => {
              return {
                id: k.id,
                name: k.name,
                url: k.url
              }
            })
          }
          const att_a2 = res.data.signForAttachments || []
          this.fileList2 = att_a2.map(k => {
            return {
              id: k.id,
              name: k.name,
              url: k.url
            }
          })
          // 有单个附件进行修改的页面回显
          this.initData.forEach(i => {
            i.formDataInit.forEach(k => {
              k.fileList = []
              if (k.type === 'files' && k.single) {
                if (res.data[k.prop]) {
                  const pre_name = k.origin_name ? this.getUrlName(this.formData[k.prop], false) : this.formData['order_num'] || this.formData['clearance_num']
                  const name = k.origin_name ? pre_name : k.prop === 'check_file' ? pre_name + '_check_bills' + this.getUrlName(this.formData[k.prop], true)
                    : k.prop === 'main_file' ? pre_name + this.getUrlName(this.formData[k.prop], true)
                      : k.prop === 'cabin_file' ? pre_name + '-mainfest' + this.getUrlName(this.formData[k.prop], true)
                        // : this.formData['truck_order_num'] + this.getUrlName(this.formData[k.prop], true)
                        : this.getUrlName(this.formData[k.prop], false)
                  // 点击编辑后回显已存在的单文件
                  k.fileList.push({
                    id: '',
                    name: name,
                    url: res.data[k.prop]
                  })
                }
              } else if (k.prop === 'pre_recording_attachments') {
                const atta = res.data.pre_recording_attachments || []
                k.fileList = atta.map(sub => {
                  return {
                    id: sub.id,
                    name: sub.name,
                    url: sub.url
                  }
                })
              } else if (k.prop === 'release_attachments') {
                const atta = res.data.release_attachments || []
                k.fileList = atta.map(sub => {
                  return {
                    id: sub.id,
                    name: sub.name,
                    url: sub.url
                  }
                })
              } else if (k.prop === 'main_file' && res.data[k.prop]) {
                k.fileList = [{
                  name: res.data[k.prop].split('/').pop(), // 从URL中提取文件名
                  url: res.data[k.prop]
                }]
              }
            })
          })
          // 付款信息操作
          if (res.data.pay_amount) this.pay_amount = res.data.pay_amount
          // 客户订单的话进行包裹信息的处理
          this.initParceType = res.data.parceType
          this.parceType = res.data.parceType === undefined || res.data.parceType === true ? 1 : 0
          this.newParceType = res.data.parceType === undefined || res.data.parceType === true ? 1 : 0
          // 客户订单包裹
          if ((res.data.parcel && res.data.parcel.length !== 0) || res.data.parcel_size && res.data.parcel_size.length !== 0) {
            this.$set(this.formData, 'parcelItem', [])
            this.count = 0
            // 包裹汇总
            this.extraData.goods = []
            this.extraData.parcel_goods = []
            this.extraData.parce = JSON.parse(JSON.stringify(res.data.parceType === undefined || res.data.parceType === true ? res.data.parcel : res.data.parcel_size))
            console.log('this.parceType8-->', this.parceType)
            if (this.parceType) {
              // 完整
              this.extraData.parce.map(i => {
                i.parcelItem.forEach(k => {
                  this.count += 1
                  // 商品汇总
                  this.extraData.goods.push(k)
                  k.parcel_number = i.parcel_num
                  this.extraData.parcel_goods.push(k)

                  const path = this.$route.path
                  if (path.includes('customerOrder_fbm') || path.includes('customerOrderTask_fbm')) {
                    // fbm在线表格的汇总
                    this.formData.parcelItem.push({
                      p_id: i.id,
                      g_id: k.id,
                      // 包裹
                      parcel_num: i.parcel_num, // 包裹号
                      shipment_id: i.shipment_id, // shipment_id
                      reference_id: i.reference_id, // reference_id
                      parcel_weight: i.parcel_weight, // 包裹重量
                      parcel_length: i.parcel_length, // 包裹长
                      parcel_width: i.parcel_width, // 包裹宽
                      parcel_height: i.parcel_height, // 包裹高
                      label_weight: i.label_weight, // 打单重
                      label_length: i.label_length, // 打单长
                      label_width: i.label_width, // 打单宽
                      label_height: i.label_height, // 打单高
                      actual_weight: i.actual_weight, // 实际包裹重量
                      actual_length: i.actual_length, // 实际包裹长
                      actual_width: i.actual_width, // 实际包裹宽
                      actual_height: i.actual_height, // 实际包裹高
                      remark: i.remark, // 包裹备注
                      shop_type: i.shop_type, // 店铺类型
                      // 商品
                      item_code: k.item_code, // 物品号
                      declared_nameCN: k.declared_nameCN, // 中文申报品名
                      declared_nameEN: k.declared_nameEN, // 英文申报品名
                      declared_price: k.declared_price, // 申报价格
                      item_qty: k.item_qty, // 商品数量
                      box_qty: k.box_qty, // 商品箱数
                      combined_parcel_num: k.combined_parcel_num, // 合箱箱号
                      item_weight: k.item_weight, // 商品重量
                      // item_size: k.item_size, //
                      texture: k.texture, // 材质
                      use: k.use, // 用途
                      brand: k.brand, // 品牌
                      model: k.model, // 型号
                      customs_code: k.customs_code, // 海关编码
                      custom_clearance: k.custom_clearance, // 报关方式
                      is_electric: k.is_electric ? '是' : '否', // 是否带电
                      is_magnetic: k.is_magnetic ? '是' : '否', // 是否带磁
                      item_picture: k.item_picture // 包裹图片
                    })
                  } else if (path.includes('customerOrderTask_fba_zrh')) {
                    // fba_zrh在线表格的汇总 
                    this.formData.parcelItem.push({
                      p_id: i.id,
                      g_id: k.id,
                      // 包裹
                      parcel_num: i.parcel_num, // 包裹号
                      shipment_id: i.shipment_id, // shipment_id
                      reference_id: i.reference_id, // reference_id
                      parcel_weight: i.parcel_weight, // 包裹重量
                      parcel_length: i.parcel_length, // 包裹长
                      parcel_width: i.parcel_width, // 包裹宽
                      parcel_height: i.parcel_height, // 包裹高
                      label_weight: i.label_weight, // 打单重
                      label_length: i.label_length, // 打单长
                      label_width: i.label_width, // 打单宽
                      label_height: i.label_height, // 打单高
                      actual_weight: i.actual_weight, // 实际包裹重量
                      actual_length: i.actual_length, // 实际包裹长
                      actual_width: i.actual_width, // 实际包裹宽
                      actual_height: i.actual_height, // 实际包裹高
                      remark: i.remark, // 包裹备注
                      shop_type: i.shop_type, // 店铺类型
                      // 商品
                      item_code: k.item_code, // 物品号
                      item_weight: k.item_weight, // 商品重量
                      item_length: k.item_length, // 长
                      item_width: k.item_width, // 宽
                      item_height: k.item_height, // 高
                      declared_nameCN: k.declared_nameCN, // 中文申报品名
                      declared_nameEN: k.declared_nameEN, // 英文申报品名
                      declared_price: k.declared_price, // 申报价格
                      item_qty: k.item_qty, // 商品数量
                      box_qty: k.box_qty, // 商品箱数
                      combined_parcel_num: k.combined_parcel_num, // 合箱箱号
                      // item_weight: k.item_weight, // 商品重量
                      // item_size: k.item_size, //
                      texture: k.texture, // 材质
                      use: k.use, // 用途
                      brand: k.brand, // 品牌
                      model: k.model, // 型号
                      customs_code: k.customs_code, // 海关编码
                      custom_clearance: k.custom_clearance, // 报关方式
                      // is_electric: k.is_electric ? '是' : '否', // 是否带电
                      // is_magnetic: k.is_magnetic ? '是' : '否', // 是否带磁
                      sku_url: k.sku_url, // 销售链接
                      item_picture: k.item_picture // 包裹图片
                    })
                  } else {
                    // fba在线表格的汇总
                    this.formData.parcelItem.push({
                      p_id: i.id,
                      g_id: k.id,
                      // 包裹
                      parcel_num: i.parcel_num, // 包裹号
                      reference_id: i.reference_id, // reference_id
                      shipment_id: i.shipment_id, // shipment_id
                      parcel_weight: i.parcel_weight, // 包裹重量
                      parcel_length: i.parcel_length, // 包裹长
                      parcel_width: i.parcel_width, // 包裹宽
                      parcel_height: i.parcel_height, // 包裹高
                      remark: i.remark, // 包裹备注
                      label_weight: i.label_weight, // 打单重
                      label_length: i.label_length, // 打单长
                      label_width: i.label_width, // 打单宽
                      label_height: i.label_height, // 打单高
                      actual_weight: i.actual_weight, // 实际包裹重量
                      actual_length: i.actual_length, // 实际包裹长
                      actual_width: i.actual_width, // 实际包裹宽
                      actual_height: i.actual_height, // 实际包裹高
                      // 商品
                      item_code: k.item_code, // 物品号
                      declared_nameCN: k.declared_nameCN, // 中文申报品名
                      declared_nameEN: k.declared_nameEN, // 英文申报品名
                      declared_price: k.declared_price, // 申报价格
                      declared_currency: k.declared_currency, // 申报币种
                      item_qty: k.item_qty, // 商品数量
                      item_weight: k.item_weight, // 重量
                      // item_size: k.item_size, //
                      texture: k.texture, // 材质
                      use: k.use, // 用途
                      brand: k.brand, // 品牌
                      model: k.model, // 型号
                      customs_code: k.customs_code, // 海关编码
                      tax_rate: k.tax_rate, // 税率
                      fba_no: k.fba_no, // FBA号
                      fba_track_code: k.fba_track_code, // FBA货物追踪编号
                      item_picture: k.item_picture // 包裹图片
                    })
                  }
                })
              })
            } else {
              // 简易
              this.extraData.parce.map(i => {
                this.count += 1
                // 在线表格的汇总
                this.formData.parcelItem.push({
                  p_id: i.id,
                  // 包裹
                  parcel_num: i.parcel_num,
                  parcel_weight: i.parcel_weight,
                  parcel_length: i.parcel_length,
                  parcel_width: i.parcel_width,
                  parcel_height: i.parcel_height,
                  actual_weight: i.actual_weight,
                  actual_length: i.actual_length,
                  actual_width: i.actual_width,
                  actual_height: i.actual_height,
                  parcel_qty: i.parcel_qty,
                  remark: i.remark
                })
              })
            }
            console.log('当前包裹数据-->', this.formData.parcelItem)
          // } else if (res.data.relative_invoices && res.data.relative_invoices.length !== 0) {
          //   // 核销的账单汇总和账单
          //   console.log('核销的账单汇总')
          //   this.formData.relative_invoices.map(i => {
          //     if (i.relative_debits && i.relative_debits.length > 0) {
          //       i.relative_debits.forEach(k => {
          //         this.count += 1
          //         // 商品汇总
          //         k.invoice_num = i.invoice_num
          //         this.extraData.relative_debits.push(k)
          //       })
          //     }
          //   })
          //   console.log('嘎嘎-->', this.extraData)
          } else {
            this.extraData.parce = []
            this.extraData.goods = []
            this.extraData.parcel_goods = []
            // this.extraData.relative_invoices = []
            // this.extraData.relative_debits = []
          }
          this.getDetailAtach()
        } else if (res.code === 404) {
          this.$message.error(res.message || this.$t('common.detailFail'))
        } else {
          this.$message.error(res.detail || this.$t('common.detailFail'))
        }
        this.bus.$emit('fullLoading', false)
      }).catch((err) => {
        console.log(err)
        this.bus.$emit('fullLoading', false)
        this.$message.error(this.$t('common.detailFailFreash'))
      })
    },
    // 抓单任务的处理次数修改
    changeTimes(show, item, close) {
      if (close) {
        item.value = item.handle_times
        item.edit = show
      } else if (show) {
        item.edit = show
      } else {
        const params = {
          api: `/api/${this.requestUrl.baseUrl}/change_handle_times/`,
          data: {
            id: item.id,
            num: item.value
          }
        }
        actionPost(params).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg)
            item.handle_times = item.value
            item.edit = show
          } else {
            this.$message.error(res.detail || res.message || res.msg)
          }
        })
      }
    },

    // 更新尾程订单信息
    updateLabelOrder(item) {
      console.log(item)
      this.$confirm(this.$t('common.updateLableComfirm'), this.$t('common.tips'), {
        confirmButtonText: this.$t('common.sure'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        const params = {
          api: `/api/${this.requestUrl.baseUrl}/update_label_data/`,
          data: {
            id: item.id
          }
        }

        actionPost(params)
          .then(res => {
            if (res.code === 200) {
              this.$message.success(res.msg)
            } else {
              this.$message.error(res.detail || res.message || res.msg)
            }
          })
          .catch(err => {
            this.$message.error(this.$t('common.modifyfail'))
            console.error(err)
          })
      }).catch(() => {

      })
    },

    // 通用任务修改
    changeTask(showInput, item, isSubmit = false, row = null) {
      console.log('current item-->', item)
      console.log('current row-->', row)
      // if (!item.hasOwnProperty('edit')) {
      //   this.$set(item, 'edit', showInput)
      // } else {
      //   item.edit = showInput
      // }
      item.edit = showInput
      console.log('current item2-->', item)
      if (!isSubmit) {
        // item.value = item.pull_times
        // row.edit = showInput
      } else {
        const params = {
          api: `/api/${this.requestUrl.baseUrl}/${item.method}/`,
          data: {
            id: row.id,
            value: row[item.prop]
          }
        }
        actionPost(params).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg)
            // item.edit = showInput
          } else {
            this.$message.error(res.detail || res.message || res.msg)
          }
        })
      }
    },
    // 修改
    modify(b) {
      // 输入类型为splitInput的输入框回显
      this.initChoiceData()
      this.$emit('switchAdd', b)
    },
    // 保存付款信息
    savePayInfo(b) {
      if (!b) {
        this.detailEdit = true
        return
      }
      const { pay_currency, pay_amount, invoice_num, pay_date, pay_rate, remark } = this.formData
      if (!pay_currency || !pay_amount) {
        this.$message.error('请输入付款币种、付款金额')
        return
      }
      const params = {
        api: `/api/${this.requestUrl.baseUrl}/modify_pay/`,
        data: {
          id: this.$route.query.id,
          pay_currency,
          pay_amount,
          remark,
          pay_rate,
          invoice_num,
          pay_date
        }
      }
      // 提交
      actionPost(params).then(res => {
        if (res.code === 200) {
          this.$message[res.info || 'success'](res.msg)
          this.closePage()
        } else {
          this.$message.error(res.detail || res.message || res.msg)
        }
      })
    },
    // 保存
    save(flag, action_flag) {
      console.log('---save->>>')
      this.formData.action_flag = action_flag
      if (!this.rightLoading) {
        this.$message.error(this.$t('common.norightloading'))
        return
      }
      this.$refs.commomForm.validate((valid, data) => {
        // 添加表格子表单box_list验证
        let tableValid = true
        let tableErrorMsg = ''

        if (this.formData.box_list && Array.isArray(this.formData.box_list) && this.$store.state.basicInfo.task_id) {
          if (this.formData.box_list.length === 0) {
            tableValid = false
            tableErrorMsg = '货物列表不能为空'
          } else {
            for (let i = 0; i < this.formData.box_list.length; i++) {
              const item = this.formData.box_list[i]
              if (!item.description || item.description.toString().trim() === '') {
                tableValid = false
                tableErrorMsg = `货物品名称必填`
                break
              }
              if (!item.package_unit || item.package_unit.toString().trim() === '') {
                tableValid = false
                tableErrorMsg = `件数单位必填`
                break
              }
              if (!item.weight || item.weight.toString().trim() === '') {
                tableValid = false
                tableErrorMsg = `重量必填`
                break
              }
              if (!item.volume || item.volume.toString().trim() === '') {
                tableValid = false
                tableErrorMsg = `体积必填`
                break
              }
            }
          }
        }

        if (!tableValid) {
          this.$message.error(tableErrorMsg)
          return
        }

        if (valid) {
          // 保存前修正 SKU_SalesLink 里的 platform 字段
          if (Array.isArray(this.formData.SKU_SalesLink)) {
            this.formData.SKU_SalesLink = this.formData.SKU_SalesLink.map(item => ({
              ...item,
              platform: (item.platform === '' || item.platform === null || item.platform === undefined)
                ? 0
                : Number(item.platform)
            }))
          }

          // 处理产品收费项中的组合收费比率和组合费用项
          if (this.formData.product_charge && Array.isArray(this.formData.product_charge)) {
            this.formData.product_charge = this.formData.product_charge.map(item => ({
              ...item,
              charge_combine_rate: (item.charge_combine_rate === '' || item.charge_combine_rate === null || item.charge_combine_rate === undefined)
                ? 0
                : Number(item.charge_combine_rate),
              relate_charges: (item.relate_charges === null || item.relate_charges === undefined ||
                              (Array.isArray(item.relate_charges) && item.relate_charges.length === 0) ||
                              item.relate_charges === '')
                ? ['None']
                : item.relate_charges
            }))
          }
          // 暂时注释掉
          // if (this.count) {
          //   if (!this.summaryGoods()) {
          //     return
          //   }
          // }
          if (this.rowId) {
            this.params.data.truck_inquiry = this.rowId
          }
          const id = this.id
          console.log('什么情况id-->', id)
          if (this.formData.insurance_orders) {
            // eslint-disable-next-line
              this.formData.insurance_orders = (this.formData.insurance_orders.replace(/\n/g, ' ').split(' ').map(i => {
              // eslint-disable-next-line
                return i.trim()
              // eslint-disable-next-line
              }).filter(i => i)).join(' ')
          }
          if (id) {
            // 修改
            this.flag = true
            // outboundInstructs货件管理的附件需要进行保存, 因为这个附件是字符串类型
            if (this.params.data.attachments && this.requestUrl.baseUrl !== 'outboundInstructs') {
              delete this.params.data.attachments
            }
            if (this.params.data.signForAttachments) { delete this.params.data.signForAttachments }
            // 保存表单时不上传附件, 因为在上传附件的时候已经上传了, 保存的时候去掉该字段
            if (this.params.data.relative_attachments) { delete this.params.data.relative_attachments }
            if (this.params.data.master_ocean_file) { delete this.params.data.master_ocean_file }
            if (this.params.data.dispatch_file) { delete this.params.data.dispatch_file }
            if (this.params.data.main_file) { delete this.params.data.main_file }
            if (this.params.data.cabin_file) { delete this.params.data.cabin_file }
            if (this.params.data.check_file) { delete this.params.data.check_file }
            if (this.params.data.bill_file) { delete this.params.data.bill_file }
            if (this.params.data.tax_file) { delete this.params.data.tax_file }
            // if (this.params.data.pod_file) { delete this.params.data.pod_file }
            // if (this.params.data.crm_file) { delete this.params.data.crm_file }
            // if (this.params.data.awb_file) { delete this.params.data.awb_file }
            if (this.params.data.sop_file || this.params.data.sop_file === '') {
              delete this.params.data.sop_file
            }
            console.log('this.params--------------', this.params)
            const path = this.$route.path
            // if (this.params.data.manifest_file) { delete this.params.data.manifest_file }
            // 添加fbm订单详情页,是否带电是否带磁的数值转换逻辑
            if ((path.includes('customerOrder_fbm') || path.includes('customerOrderTask_fbm')) && this.params.data.parcelItem && Array.isArray(this.params.data.parcelItem)) {
              const convertToBoolean = (value) => {
                const trueValues = ['是', '1', true, 'true', 1]
                const falseValues = ['否', '0', false, 'false', 0]
                if (trueValues.includes(value)) return true
                if (falseValues.includes(value)) return false
                return value
              }
              this.params.data.parcelItem = this.params.data.parcelItem.map(item => ({
                ...item,
                is_electric: convertToBoolean(item.is_electric),
                is_magnetic: convertToBoolean(item.is_magnetic)
              }))
            }
            edit(this.params).then(res => {
              if (res.code === 200 || res.code === 201) {
                if (this.fileList3 && this.fileList3.size > 0) {
                  this.uploadFileList3(res.data.id)
                }
                this.$message.success(this.$t('common.modifysuccess'))
                if (flag) {
                  this.count = 0
                  this.initFormData()
                } else {
                  this.closePage()
                }
              } else {
                let str = ''
                Object.keys(res).forEach(i => {
                  if (Array.isArray(res[i])) {
                    str += i + JSON.stringify(res[i][0])
                  }
                })
                // this.$message.error(this.$t('common.modifyfail') + ':' + (str || res.detail))
                this.$message.error(this.$t('common.modifyfail') + ':' + (res.msg || str || res.detail))
              }
              this.flag = false
            }).catch(() => { this.flag = false })
          } else {
            // 新增
            this.flag = true
            add(this.params).then(res => {
              if (res.code === 200 || res.code === 201) {
                this.$message.success(this.$t('common.addsuccess'))
                // 判断是否有新增的单个文件
                let fileNum = 0
                // this.singlefile
                this.singlefile.forEach((value, key) => {
                  fileNum += 1
                })
                console.log('fileNum--> result', res)
                console.log('fileNum--> result1', res.data)
                console.log('fileNum--> fileNum', fileNum)
                if (fileNum !== 0) {
                  this.handleFile(res.data.id, '', '', true)
                  console.log('fileNum--> result1212', fileNum)
                }
                //         if (this.fileList3 && this.fileList3.size > 0) {
                //   this.uploadFileList3(res.data.id)
                // }
                // 判断是否新增上传附件
                const jsonData = {}
                // eslint-disable-next-line
                this.waitToUpload.forEach((value, key) => jsonData[key] = value)
                if (JSON.stringify(jsonData) !== '{}') {
                  this.newAttachments(res.data.id)
                } else {
                  if (flag) {
                    this.formData = {}
                    this.cachesInit()
                    const path = this.$route.path.split('/')
                    path[path.length - 1] = 'detail'
                    this.$router.replace(path.join('/') + '?id=' + res.data.id + '&edit=true')
                  } else {
                    // 缓存时初始化值
                    this.formData = {}
                    // 上传后清除文件
                    if (this.$refs.uploadfiles) {
                      if (this.$refs.uploadfiles.length > 1) {
                        this.$refs.uploadfiles.forEach(e => {
                          e.clearFiles()
                        })
                      }
                    }
                    this.cachesInit(true)
                  }
                }
              } else {
                let str = ''
                Object.keys(res).forEach(i => {
                  if (Array.isArray(res[i])) {
                    str += i + JSON.stringify(res[i][0])
                  }
                })
                // this.$message.error(this.$t('common.addfail') + ':' + (str || res.detail))
                this.$message.error(this.$t('common.addfail') + ':' + (res.msg || str || res.detail))
              }
              this.flag = false
            }).catch(() => { this.flag = false })
          }
        } else {
          const inCheck = Object.keys(data).map(i => i)
          if (Object.keys(this.propsLabelsMap).length === 0) {
            this.initData.forEach(i => {
              i.formDataInit.forEach(k => {
                this.propsLabelsMap[k.prop] = k.label
              })
            })
          }
          let message = this.$t('content.PleaseFillIn')
          inCheck.forEach((i, index) => {
            message += this.propsLabelsMap[i] + '、'
          })
          this.$notify({
            title: 'Tips',
            message,
            type: 'warning'
          })
        }
      })
    },
    // 缓存初始化
    cachesInit(clsePage = false) {
      this.initParceType = null
      this.firstEntry = true
      this.extraData.parce = []
      this.extraData.goods = []
      this.pay_amount = null
      this.formData = {}
      this.select_item = { prop: '' }
      this.fileList = []
      this.fileList2 = []
      this.waitToUpload = new FormData()
      this.singlefile = new FormData()
      this.text_receiver = ''
      this.text_shipper = ''
      this.count = 0
      this.parceType = 1
      this.newParceType = 1
      this.$refs.commomForm.resetFields()
      this.clearReceiveShipper()
      if (clsePage) {
        this.closePage()
      }
    },
    clearReceiveShipper() {
      // 置空收发件人
      this.formData['contact_name'] ? this.formData['contact_name'] = null : ''
      this.formData['company_name'] ? this.formData['company_name'] = null : ''
      this.formData['contact_email'] ? this.formData['contact_email'] = null : ''
      this.formData['country_code'] ? this.formData['country_code'] = null : ''
      this.formData['state_code'] ? this.formData['state_code'] = null : ''
      this.formData['city_code'] ? this.formData['city_code'] = null : ''
      this.formData['postcode'] ? this.formData['postcode'] = null : ''
      this.formData['house_no'] ? this.formData['house_no'] = null : ''
      this.formData['address_one'] ? this.formData['address_one'] = null : ''
      this.formData['address_two'] ? this.formData['address_two'] = null : ''
      this.formData['contact_phone'] ? this.formData['contact_phone'] = null : ''
      this.formData['address_num'] ? this.formData['address_num'] = null : ''
      this.formData['is_r_async'] ? this.formData['is_r_async'] = null : ''
      // 发件人
      this.formData['buyer_name'] ? this.formData['buyer_name'] = null : ''
      this.formData['buyer_company_name'] ? this.formData['buyer_company_name'] = null : ''
      this.formData['buyer_mail'] ? this.formData['buyer_mail'] = null : ''
      this.formData['buyer_country_code'] ? this.formData['buyer_country_code'] = null : ''
      this.formData['buyer_state'] ? this.formData['buyer_state'] = null : ''
      this.formData['buyer_city_code'] ? this.formData['buyer_city_code'] = null : ''
      this.formData['buyer_postcode'] ? this.formData['buyer_postcode'] = null : ''
      this.formData['buyer_house_num'] ? this.formData['buyer_house_num'] = null : ''
      this.formData['buyer_address_one'] ? this.formData['buyer_address_one'] = null : ''
      this.formData['buyer_address_two'] ? this.formData['buyer_address_two'] = null : ''
      this.formData['buyer_phone'] ? this.formData['buyer_phone'] = null : ''
      this.formData['buyer_address_num'] ? this.formData['buyer_address_num'] = null : ''
      this.formData['is_b_async'] ? this.formData['is_b_async'] = null : ''

      this.initData.forEach(i => {
        i.formDataInit.forEach(k => {
          if (k.prop === 'shipper' && k.type === 'dialogselect') {
            k.textVal = ''
          }
          if (k.prop === 'receiver' && k.type === 'dialogselect') {
            k.textVal = ''
          }
          if (k.prop === 'notify_party' && k.type === 'dialogselect') {
            k.textVal = ''
          }
        })
      })
    },
    // 删除
    del() {
      this.$confirm(this.$t('common.delecomfirm'), this.$t('common.tips'), {
        confirmButtonText: this.$t('common.sure'),
        cancelButtonText: this.$t('common.calcle'),
        type: 'error'
      }).then(() => {
        del(this.params).then(res => {
          if (res.code === 200) {
            this.$message.success(this.$t('common.deletesuccess'))
            this.closePage()
          } else {
            this.$message.error(this.$t('common.deletefail'))
          }
        })
      }).catch(() => {})
    },
    // 关闭页面
    closePage() {
      const path = this.$route.path.split('/')
      path[path.length - 1] = 'list'
      this.$router.push(path.join('/'))
    },
    // 获取表格操作弹窗下拉数据
    getTableSelectOptins() {
      const propArr = this.dialogForm.map(i => i.prop)
      const params = {
        size: 100000,
        page: 1,
        sort: 'id'
      }
      this.dialogOption.forEach((i, index, arr) => {
        if (propArr.includes(i.prop)) {
          i.id && this.id ? params['id'] = this.id : ''
          const data = {
            ...params, ...i.query
          }
          getChoiceData(i.api, data).then(res => {
            this.$set(this.dialogOptionData, i.prop,
              {
                data: res.data || res.results,
                label: i.label || 'name',
                value: i.value || 'id'
              }
            )
          })
        }
      })
    },
    // 表格相关操作
    tableOperate(actiontype, tableData, item, index) {
      console.log('tableOperate-->')
      const that = this
      console.log('actiontype-->', actiontype)
      console.log('tableData-->', tableData)
      const { data, datavalue, action, label } = tableData
      console.log(data, '1')
      console.log(datavalue, '2')
      console.log(action, '3')
      console.log(label, '4')
      var rules = {}
      this.dialogVal = datavalue
      // 赋值当前表格
      this.currentTable = tableData
      if (item && item['is_share']) {
        this.$message.error('分摊价格，不允许编辑或删除')
        return
      }
      switch (actiontype) {
        // eslint-disable-next-line
        case 'add':
          console.log('actiontype: add')
          if ((tableData.datavalue === 'outboundOrderChargeIns' || tableData.datavalue === 'inboundOrderChargeIns') && !this.formData.product) {
            this.$message.error('请先选择产品')
            return
          }
          this.dialogTitle = this.$t('common.create') + `${label}`
          console.log(this.dialogTitle, '5')
          this.dialogForm = JSON.parse(JSON.stringify(data))
          console.log(this.dialogForm, '6')
          // 默认值设定
          this.dialogForm.forEach(i => {
            if (i.default !== undefined) {
              this.$set(this.dialogFormData, i.prop, i.default)
            }
          })
          this.dialogType = action.type
          this.dialogShow = true
          this.getTableSelectOptins()
          // 赋值检验规则
          this.dialogForm.forEach(i => {
            if (!i.not_required) {
              rules[i.prop] = [{
                required: true,
                message: i.type === 'select' ? this.$t('common.select') : this.$t('common.enter'),
                trigger: i.type === 'select' ? 'change' : 'blur'
              }]
            }
            if (i.type === 'email') {
              rules[i.prop] = [{
                required: true, type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur'
              }]
            }
          })
          this.dialogRules = rules
          console.log(this.dialogRules, 'ttt')
          this.$nextTick(() => {
            that.$refs.detailForm.clearValidate()
          })
          // 给空运主单/海运提单号添加到新增出口报关单位置
          this.initData.forEach(t => {
            t.formDataInit.forEach(k => {
              // const matchedItem = this.currentTable.data.find(i => i.linkage === k.prop)
              // if (matchedItem) {
              //   this.$set(this.dialogFormData, matchedItem.prop, this.formData[k.prop])
              // }
              // todo_s: 判断是否绑定单号(linkage), 若绑定则同步显示到this.dialogFormData中
              this.currentTable.data.forEach(i => {
                if (i.linkage && i.linkage === k.prop) {
                  this.$set(this.dialogFormData, i.prop, this.formData[k.prop])
                }
              })
            })
          })
          this.syncRevenue = tableData.syncRevenue
          break
          // eslint-disable-next-line
        case 'detele':
          console.log('actiontype: detele')
          const ids = this.selection.map(i => i.id || JSON.stringify(i))
          if (ids.length === 0) {
            this.$message.error(this.$t('content.pleaseSelectOperation'))
          }
          this.formData[this.dialogVal] = this.formData[this.dialogVal].filter(i => {
            return !ids.includes(i.id || JSON.stringify(i))
          })
          this.setOtherVal.forEach(i => {
            if (i.type === 'child') {
              this.formData[i.from] = this.formData[i.from].filter(j => {
                if (this.dialogVal in j) {
                  j[this.dialogVal] = j[this.dialogVal].filter(k => {
                    return !ids.includes(k.id || JSON.stringify(k))
                  })
                }
                return j
              })
            }
          })
          console.log('detele this.formData-->', this.formData)
          console.log('detele this.currentTable-->', this.currentTable)
          this.getValByOtherVal()
          this.getRightToSummary()
          this.getSubValueSummary(this.formData[this.dialogVal], this.currentTable)
          if (this.currentTable.popTransfer && this.currentTable.popTransfer.listProp === 'outboundParcelDetails') {
            // this.formData[this.currentTable.popTransfer.listProp][shipmentId] = parcels
            this.selection.forEach(i => {
              if (i.shipment_id in this.formData[this.currentTable.popTransfer.listProp]) {
                this.$delete(this.formData[this.currentTable.popTransfer.listProp], i.shipment_id)
              }
            })
          }
          break
        case 'batchEdit': {
          console.log('actiontype: batchEdit')
          const idss = this.selection.map(i => i.id || JSON.stringify(i))
          const big_parcel_name = this.selection.map(i => i.big_parcel_name || JSON.stringify(i))
          const order_num = this.selection.map(i => i.order_num || JSON.stringify(i))
          const params = {
            api: `/api/bigParcels/batch_unbind_small/`,
            data: { id: idss, big_order_num: big_parcel_name, order_num: order_num }
          }
          console.log(params, '参数输出')
          actionPost(params).then(res => {
            if (res.code === 200) {
              this.$message.success('操作成功')
              that.bus.$emit('fullLoading', false)
              console.log(this.$route.query.id)
            } else {
              this.$message.error(res.detail || res.message || res.msg)
            }
          })
          this.formData[this.dialogVal] = this.formData[this.dialogVal].filter(i => {
            return !idss.includes(i.id || JSON.stringify(i))
          })
          // this.getValByOtherVal()
          // this.getRightToSummary()
          break
        }
        case 'exportDetail': {
          console.log('actiontype: exportDetail')
          const big_id = this.params.id
          const params = {
            api: `/api/bigParcels/export_detail/`,
            data: { big_id: big_id },
            responseType: 'blob'
          }
          console.log(params, '参数输出')
          actionPost(params).then(res => {
            axiosExport(res, (this.params.data.parcel_num + '_big_parcel_detail') + '.xlsx')
            this.$message.success('success!')
          })
          break
        }
        case 'batchUnbindOutbound': {
          console.log('actiontype: batchUnbindOutbound')
          console.log(this.selection)
          const idss = this.selection.map(i => i.id || JSON.stringify(i))
          const parcel_num = this.selection.map(i => i.parcel_num || JSON.stringify(i))
          const params = {
            api: `/api/parcelOutboundOrders/batch_unbind_large/`,
            data: { parcel_num: parcel_num }
          }
          console.log(params, '参数输出')
          actionPost(params).then(res => {
            if (res.code === 200) {
              this.$message.success('操作成功')
              that.bus.$emit('fullLoading', false)
              console.log(this.$route.query.id)
            } else {
              this.$message.error(res.detail || res.message || res.msg)
            }
          })
          this.formData[this.dialogVal] = this.formData[this.dialogVal].filter(i => {
            return !idss.includes(i.id || JSON.stringify(i))
          })
          // this.getValByOtherVal()
          // this.getRightToSummary()
          break
        }
        case 'batchUnbindYunshu': {
          console.log('actiontype: batchUnbindYunshu')
          const idss = this.selection.map(i => i.id || JSON.stringify(i))
          console.log(this.selection)
          const outbound_num = this.selection.map(i => i.outbound_num || JSON.stringify(i))
          const params = {
            api: `/api/customerOrders/batch_unbind_outbound_order/`,
            data: { outbound_num: outbound_num }
          }
          console.log(params, '参数输出')
          actionPost(params).then(res => {
            if (res.code === 200) {
              this.$message.success('操作成功')
              that.bus.$emit('fullLoading', false)
              console.log(this.$route.query.id)
            } else {
              this.$message.error(res.detail || res.message || res.msg)
            }
          })
          this.formData[this.dialogVal] = this.formData[this.dialogVal].filter(i => {
            return !idss.includes(i.id || JSON.stringify(i))
          })
          // this.getValByOtherVal()
          // this.getRightToSummary()
          break
        }
        case 'batchUnbind': {
          console.log('actiontype: batchUnbind')
          const idss = this.selection.map(i => i.id || JSON.stringify(i))
          console.log(this.selection)
          const params = {
            api: action.api,
            data: { ids: idss }
          }
          console.log(params, '参数输出')
          actionPost(params).then(res => {
            if (res.code === 200) {
              this.$message.success('操作成功')
              that.bus.$emit('fullLoading', false)
              console.log(this.$route.query.id)
            } else {
              this.$message.error(res.detail || res.message || res.msg)
            }
          })
          this.formData[this.dialogVal] = this.formData[this.dialogVal].filter(i => {
            return !idss.includes(i.id || JSON.stringify(i))
          })
          break
        }
        case 'edit': {
          console.log('actiontype: edit')
          if ((tableData.datavalue === 'outboundOrderChargeIns' || tableData.datavalue === 'inboundOrderChargeIns') && !this.formData.product) {
            this.$message.error('请先选择产品')
            return
          }
          this.dialogTitle = this.$t('common.modify') + `${label}`
          this.selectIndex = index
          this.dialogFormData = {}
          this.dialogForm = JSON.parse(JSON.stringify(data))
          this.dialogShow = true
          this.dialogFormData = Object.assign({}, item)
          // 赋值检验规则
          this.dialogForm.forEach(i => {
            if (!i.not_required) {
              rules[i.prop] = [{
                required: true,
                message: i.type === 'select' ? this.$t('common.select') : this.$t('common.enter'),
                trigger: i.type === 'select' ? 'change' : 'blur'
              }]
            }
            if (i.type === 'email') {
              rules[i.prop] = [{
                required: true, type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur'
              }]
            }
            // rules[i.prop] = []
            // 验证只能输入正整数
            if (i.ruleType === 'number') {
              rules[i.prop].push(
                { pattern: /^(0|[1-9]\d*)(.\d{1,2})?$/, message: i.label + '请输入数字格式，最多二位小数' },
              )
            }
            // 验证最大不能超过
            if (i.max) {
              rules[i.prop].push(
                {
                  validator: (rule, value, callback) => {
                    if (value > i.max) {
                      callback(new Error(i.label + '不能大于' + i.max))
                    } else {
                      callback()
                    }
                  },
                  trigger: 'blur'
                }
              )
            }
          })
          this.getTableSelectOptins()
          this.dialogRules = rules
          break
        }
        case 'chargeOff': {
          console.log('actiontype: chargeOff')
          if (this.selection.length === 0) {
            this.$message.error(this.$t('content.pleaseSelectOperation'))
            return
          } else {
            this.chargeOffTable = action.chargeOffTable.columns
            this.dialogChargeOffShow = true
          }
        }
      }
    },
    // 表格额外的按钮操作
    async etcEvent(btn, sourceData, item, index) {
      console.log('表格额外的按钮操作')
      if (btn.event === 'download_box_label') {
        const params = {
          'btn': btn,
          'sourceData': this.selection
        }
        // 子组件调用父组件方法
        this.$emit('tabelEtcEvent', params)
        return
      }
      if (btn.event === 'download_template') {
        download_Template(btn.link, process.env.HOST)
        return
      }
      if (item.id && btn.event === 'modify_customer_user_update') {
        this.dialogUserUpdateData.name = item.name
        this.dialogUserUpdateData.id = item.id
        this.dialogUserUpdateOffShow = true
      }
      if (btn.comfirm) {
        this.$confirm(btn.comfirmMsg, this.$t('common.tips'), {
          confirmButtonText: this.$t('common.sure'),
          cancelButtonText: this.$t('common.cancle'),
          type: 'warning'
        }).then(() => {
          // console.log('触发fullLoading和tabelEtcEvent')
          // this.bus.$emit('fullLoading', true)
          this.$emit('tabelEtcEvent', { btn, sourceData, item, index })
        }).catch(() => {
        })
      } else {
        // this.bus.$emit('fullLoading', true)
        this.$emit('tabelEtcEvent', { btn, sourceData, item, index })
      }
    },
    // 下拉框选中
    selectChange(val, options, item) {
      console.log('selectChange-->', item)
      options.find(i => {
        if (i[this.dialogOptionData[val]['value']] === this.dialogFormData[val]) {
          this.dialogFormData[val + '_name'] = i[this.dialogOptionData[val]['label']]
        }
      })
      // 联动修改
      if (item.change && item.change.length > 0) {
        console.log('联动修改-->')
        item.change.forEach(i => {
          // 设置值
          if (i.type === 'getValue') {
            // this.dialogFormData[i.prop] = ''
            if (!this.dialogFormData[val]) return
            // this.dialogFormData[i.prop] = (this.dialogOptionData[val].data.find(item => this.dialogFormData[val] === item[this.dialogOptionData[val].value]))[i.label]
            this.$set(this.dialogFormData, i.prop, (this.dialogOptionData[val].data.find(item => this.dialogFormData[val] === item[this.dialogOptionData[val].value]))[i.label])
          } else if (i.type === 'getRelatedValue') {
            this.dialogFormData[i.prop] = ''
            if (!this.dialogFormData[val]) return
            this.dialogFormData[i.prop] = (this.dialogOptionData[val].data.find(item => this.dialogFormData[val] === item[this.dialogOptionData[val].value]))[i.label]
          } else if (i.type === 'setNull') {
            this.dialogFormData[i.prop] = null
          } else if (i.type === 'hidden') {
            // 隐藏某些字段
            if (i.showVal === this.formData[item.prop]) {
              this.initData.forEach(t => {
                t.formDataInit.forEach(k => {
                  this.$set(k, '$hide', i.hiddenProp.includes(k.prop))
                  if (i.hiddenProp.includes(k.prop)) {
                    this.$set(this.formData, k.prop, k.type === 'table' ? [] : null)
                  }
                })
              })
            } else {
              this.initData.forEach(t => {
                t.formDataInit.forEach(k => {
                  this.$set(k, '$hide', false)
                })
              })
            }
          } else if (i.type === 'visible') {
            this.initData.forEach(t => {
              t.formDataInit.forEach(k => {
                if (i.visibleProp.includes(k.prop)) {
                  if (i.showVal === this.formData[item.prop]) {
                    this.$delete(k, '$hide')
                  } else {
                    this.$set(k, '$hide', true)
                  }
                }
              })
            })
          }
        })
      }
      if (item.changeThis) {
        console.log('联动置空', this.dialogFormData)
        item.changeThis.forEach(i => {
          if (i.type === 'setNull') {
            this.$set(this.dialogFormData, i.prop, null)
          }
        })
      }
    },
    // 通过预计件数修改包裹数量
    preCartonChange(item) {
      this.excelRowSet(this.formData[item.prop])
    },
    radioChange(val, item) {
      console.log('radioChange-->')
      // item.change.forEach(i => {
      //   if (i.type === 'hidden') {
      //     // 隐藏某些字段
      //     if (i.showVal === this.formData[item.prop]) {
      //       this.initData.forEach(t => {
      //         t.formDataInit.forEach(k => {
      //           this.$set(k, '$hide', i.hiddenProp.includes(k.prop))
      //           if (i.hiddenProp.includes(k.prop)) {
      //             this.$set(this.formData, k.prop, k.type === 'table' ? [] : null)
      //           }
      //         })
      //       })
      //     } else {
      //       this.initData.forEach(t => {
      //         t.formDataInit.forEach(k => {
      //           this.$set(k, '$hide', false)
      //         })
      //       })
      //     }
      //   }
      // })

      this.change_hidden_prop_show(item, true)
    },
    change_hidden_prop_show(item, isSetNull = false) {
      console.log('change_hidden_prop_show-->')
      // item 是 formDataInit 的 item
      if (!item.change) {
        return
      }
      item.change.forEach(i => {
        if (i.type === 'hidden') {
          // bool字段隐藏关联字段
          this.initData.forEach(t => {
            t.formDataInit.forEach(k => {
              if (i.hiddenProp.includes(k.prop)) {
                if (i.showVal === this.formData[item.prop]) {
                  this.$set(k, '$hide', true)
                  if (isSetNull) {
                    this.$set(this.formData, k.prop, k.type === 'table' ? [] : null)
                  }
                } else {
                  this.$set(k, '$hide', false)
                }
              }
            })
          })
        } else if (i.type === 'visible') {
          this.initData.forEach(t => {
            t.formDataInit.forEach(k => {
              if (i.visibleProp.includes(k.prop)) {
                if (i.showVal === this.formData[item.prop]) {
                  this.$delete(k, '$hide')
                } else {
                  this.$set(k, '$hide', true)
                }
              }
            })
          })
        }
      })
    },
    // 下拉框选中并给相应的字段赋值
    selectChangeGet(val, arr, item, reset = true, init = false) {
      console.log('selectChangeGet-->')
      if ((this.$route.path.includes('oms/outbound') || this.$route.path.includes('oms/inbound')) && item.prop === 'product') {
        // eslint-disable-next-line
        const z_index = this.initData.findIndex(i=>i.label==='收入明细')
        let table_data = this.initData[z_index]['formDataInit'][0]['data']
        if (val) {
          const is_valuation = arr.data.find(i => i.id === val).is_valuation
          // 有值
          table_data = table_data.splice(5, 0, { label: '指定扣费', prop: 'is_deduction', type: 'radio', default: is_valuation })
        } else {
          // 没值
          // this.formData[this.$route.path.includes('oms/outbound') ? 'outboundOrderChargeIns' : 'inboundOrderChargeIns'] = []
          if (table_data.length === 7) {
            table_data = table_data.splice(5, 1)
          }
        }
      }
      // 通过选中值获取别的字段下拉数据
      if (item.getSelectDataVal) {
        console.log('this.optionData666-->', this.optionData)
        this.$set(this.formData, item.getSelectDataVal, reset ? '' : this.formData[item.getSelectDataVal])
        this.$set(this.optionData, item.getSelectDataVal, [])
        // 获取其他字段下拉数据
        const params = {
          size: 100000,
          page: 1,
          sort: 'id'
        }
        const { api, query, label, value } = item.splice ? item.apis : item.apis[this.formData[item.prop]]
        getChoiceData(api + (item.splice ? this.formData[item.prop] : ''), { ...params, ...query }).then(res => {
          this.$set(this.optionData, item.getSelectDataVal,
            {
              data: res.data || res.results,
              label: label || 'name',
              value: value || 'id'
            }
          )
          // 设置缺省值
          if (res.data && item.setDefaultVal && !init) {
            this.formData[item.getSelectDataVal] = res.data[0].id
          }
        })
      }
      // 设置缺省值
      if (item.defaultVal && !item.change) {
        this.formData[item.prop] = item.defaultVal
      }

      // 例如通过选中银行设置币种
      if (!item.change || init) return
      item.change.forEach(i => {
        // 设置值
        if (i.type === 'getValue') {
          this.formData[i.prop] = ''
          if (!val) return
          this.formData[i.prop] = (arr.data.find(i => val === i[arr.value]))[i.label]
        } else if (i.type === 'setRequired') {
          // 设置必填
          const flag = (arr.data.find(i => val === i[arr.value]))[i.label].includes(i.prop)
          this.rules[i.requireProp][0].required = true
          if (flag) {
            // 设置必填
            this.rules[i.requireProp][0].required = true
          } else {
            // 取消必填
            this.rules[i.requireProp][0].required = false
          }
        } else if (i.type === 'hidden') {
          // 隐藏某些字段
          if (i.showVal === this.formData[item.prop]) {
            this.initData.forEach(t => {
              t.formDataInit.forEach(k => {
                this.$set(k, '$hide', i.hiddenProp.includes(k.prop))
                if (i.hiddenProp.includes(k.prop)) {
                  this.$set(this.formData, k.prop, k.type === 'table' ? [] : '')
                }
              })
            })
          }
        } else if (i.type === 'nowDataTime') {
          this.formData[i.prop] = null
          if (!val || val !== 'RESERVED') return
          this.formData[i.prop] = new Date()
        } else if (i.type === 'visible') {
          this.initData.forEach(t => {
            t.formDataInit.forEach(k => {
              if (i.visibleProp.includes(k.prop)) {
                if (i.showVal === this.formData[item.prop]) {
                  this.$delete(k, '$hide')
                } else {
                  this.$set(k, '$hide', true)
                }
              }
            })
          })
        }
      })
    },
    // 表格弹窗确认
    comfirm() {
      console.log('确认修改执行api', this.dialogVal)
      console.log('comfirm dialogFormData', this.dialogFormData)
      let currentFormData = this.formData[this.dialogVal]
      let item = null
      this.$refs.detailForm.validate(async(valid) => {
        if (valid) {
          // let item = null
          if (currentFormData === undefined) {
            this.$set(this.formData, this.dialogVal, [])
            currentFormData = this.formData[this.dialogVal]
          }
          if (this.dialogType === 'select') {
            item = this.dialogOptionData['dialogSelect']['data'].find(i => i.id === this.dialogSelect) || {}
          } else {
            item = JSON.parse(JSON.stringify(this.dialogFormData))
          }
          if (this.selectIndex) {
            // 修改
            currentFormData.splice(this.selectIndex - 1, 1, item)
          } else {
            currentFormData.push(item)
          }
          console.log('currentFormData----->', currentFormData)
          if (this.currentTable.action && this.currentTable.action.editMsg) {
            this.$message.warning(this.currentTable.action.editMsg)
          }
          this.getRightToSummaryCommon(currentFormData, this.currentTable)
          this.getSubValueSummary(currentFormData, this.currentTable)
          if (this.isSyncRevenue) {
            // console.log('this.formData-->', this.formData)
            // console.log('this.formData2-->', this.formData[this.syncRevenue])
            // console.log('this.dialogFormData-->', this.dialogFormData)
            // console.log('this.dialogForm-->', this.dialogForm)
            // this.formData[this.syncRevenue]
            // 新增成本时同步新增一条相同的收入
            this.initData.forEach(t => {
              t.formDataInit.forEach(k => {
                if (k.prop === this.syncRevenue) {
                  if (!this.tableModifyShowCommon(k.singleEditTradition)) {
                    this.$message.error('已收入确认, 不会自动添加收入')
                    return
                  }
                  const newRevenue = {}
                  k.data.forEach(i => {
                    if (i.type === 'select') {
                      this.$set(newRevenue, i.prop + '_name', this.dialogFormData[i.prop + '_name'])
                    }
                    this.$set(newRevenue, i.prop, this.dialogFormData[i.prop])
                  })
                  // console.log('newRevenue-->', newRevenue)
                  this.formData[this.syncRevenue].push(newRevenue)
                  this.getRightToSummaryCommon(this.formData[this.syncRevenue], k)
                }
              })
            })
          }
          // console.log('这个会执行吗-->', this.dialogBtn)
          // if (this.dialogBtn && this.dialogBtn.type === 'transferForm') {
          //   this.innerDialogConfirm(null)
          // }
          console.log('哈哈哈哈哈哈哈哈-->', this.formData['outboundParcelDetails'])
          // 在表单验证成功后，主动检查并处理transferButton类型的字段
          for (const formItem of this.dialogForm) {
            if (formItem.type === 'transferButton') {
              // console.log('formItem.type transferButton-->', formItem)
              // 主动调用transferDialog方法处理未触发change事件的输入
              await this.transferDialog(formItem, false)
            }
          }
          this.closeDialog()
          // this.getRightToSummary()
        }
      })
    },
    saveOutboundParcelMap(shipmentId, parcels) {
      console.log('saveOutboundParcelMap-->', shipmentId, parcels)
      // if (this.dialogBtn && this.dialogBtn.type === 'transferForm' && this.dialogBtn.listProp) {
      //   // 设置当前所选件数
      //   this.$set(this.formData, this.dialogBtn.prop, this.actionParams.data.selectData.length)
      //   this.$set(this.dialogFormData, this.dialogBtn.prop, this.actionParams.data.selectData.length)
      //
      //   if (typeof this.formData[this.dialogBtn.listProp] === 'undefined') {
      //     this.$set(this.formData, this.dialogBtn.listProp, [])
      //   }
      //   // const ids = this.formData[this.dialogBtn.listProp].map(i => i.id)
      //   const ids = this.formData[this.dialogBtn.listProp]
      //   this.actionParams.data.selectData.forEach(i => {
      //     if (!ids.includes(i.id)) {
      //       this.formData[this.dialogBtn.listProp].push(i)
      //     }
      //   })
      // }
      if (this.dialogBtn && this.dialogBtn.type === 'transferForm') {
        // 设置当前所选件数
        this.$set(this.formData, this.dialogBtn.prop, this.actionParams.data.selectData.length)
        this.$set(this.dialogFormData, this.dialogBtn.prop, this.actionParams.data.selectData.length)

        if (typeof this.formData[this.dialogBtn.listProp] === 'undefined') {
          this.$set(this.formData, this.dialogBtn.listProp, {})
        }
        this.formData[this.dialogBtn.listProp][shipmentId] = parcels
        console.log('神功大成-->', this.formData[this.dialogBtn.listProp])
      }
    },
    customOperate(item, row, btn) {
      console.log('customOperate0-->', this.currentTable.popUp)
      console.log('customOperate item-->', item)
      console.log('customOperate row-->', row)
      console.log('customOperate btn-->', btn)
      this.currentTable = item
      if (this.currentTable.popUp) {
        this.dialogBtn = btn
        const popUpData = JSON.parse(JSON.stringify(this.currentTable.popUp))
        this.innerTable = popUpData || {}
        this.innerDialogTableFilters = this.innerTable.innerTableFilters
        this.dialogType = this.innerTable.dialogType
        // this.actionParams.data.date = this.getCurrentNow(item.dateType === 'datetime')
        this.actionParams.data.label = this.innerTable.name
        this.actionParams.data.purpose = this.innerTable.purpose || ''
        this.actionParams.data.overheadButton = this.innerTable.overheadButton
        this.actionParams.data.overheadTitle = this.innerTable.overheadTitle
        this.actionParams.data.showRebinding = this.innerTable.showRebinding
        this.actionParams.data.method = this.innerTable.method
        this.actionParams.data.batchMethod = this.innerTable.batchMethod
        this.actionParams.data.width = this.innerTable.width
        this.innerDialogVisible = true
        if (Object.keys(this.innerTable).length > 0) {
          Object.keys(this.innerTable.query).forEach(key => {
            if (this.currentTable.popUp.query[key].startsWith('this.')) {
              console.log('获取默认查询值-->', row[this.currentTable.popUp.query[key].replace('this.', '')])
              this.innerTable.query[key] = row[this.currentTable.popUp.query[key].replace('this.', '')]
            }
          })
          // this.innerTable.query['shipment_id'] = item.shipment_id
          console.log('这是个啥-->', item)
          this.getInnerTableData(this.innerTable.api, this.innerTable.query, this.innerTable.filters)
        }
      }
    },
    async transferDialog(btn, isShowDialog = true) {
      console.log('transferDialog btn-->', btn)
      if (btn.type === 'transferButton') {
        // if (this.currentTable.popUp) {
        // this.dialogBtn = item
        const item = this.currentTable.popTransfer
        this.dialogBtn = this.currentTable.popTransfer
        console.log('赋值 this.dialogBtn-->', this.dialogBtn)
        // const popUpData = JSON.parse(JSON.stringify(this.currentTable.popUp))
        // this.innerTable = popUpData || {}
        // this.innerDialogTableFilters = this.innerTable.innerTableFilters
        // this.actionParams.data.label = this.innerTable.name
        // this.actionParams.data.purpose = this.innerTable.purpose || ''
        // this.actionParams.data.overheadButton = this.innerTable.overheadButton
        // this.actionParams.data.overheadTitle = this.innerTable.overheadTitle
        // this.actionParams.data.showRebinding = this.innerTable.showRebinding
        this.actionParams.data.method = item.method
        if (item.listProp) {
          console.log('这是个啥this.formData[item.listProp]-->', item.listProp, this.formData[item.listProp])
          // this.$set(this.dialogFormData, item.listProp, this.formData[item.listProp])
          // console.log('this.dialogFormData设置好了-->', this.dialogFormData.outbound_parcel_details)
          const queryAll = { 'queryAll': true }
          if (this.id) {
            queryAll['outbound_instruct_id'] = this.id
          }
          if (item.query) {
            Object.keys(item.query).forEach(key => {
              if (item.query[key].startsWith('this.')) {
                console.log('获取默认查询值-->', this.dialogFormData[item.query[key].replace('this.', '')])
                queryAll[key] = this.dialogFormData[item.query[key].replace('this.', '')]
              }
            })
          }
          // const dialogFormData = JSON.parse(JSON.stringify(this.dialogFormData))
          console.log('选了多少个-->', this.dialogFormData[this.dialogBtn.prop])
          try {
            const res = await getChoiceData(item.api, queryAll)
            if (res.code === 200) {
              this.transferDialogFormData = res.data
              console.log('this.transferDialogFormData设置好了-->', this.transferDialogFormData)
              // this.innerDialogTotal = res.count
              this.autoSelectInit()
            } else {
              this.$message.error(res.detail || res.message || res.msg)
            }
          } catch (error) {
            this.$message.error('请求失败，请重试')
            console.error('getChoiceData error:', error)
          }
          // if (this.id) {
          //   const queryThis = JSON.parse(JSON.stringify(queryAll))
          //   queryThis['queryAll'] = false
          //   getChoiceData(item.api, queryThis).then(res => {
          //     if (res.code === 200) {
          //       // this.$set(this.dialogFormData, item.listProp, res.data.map(i => i.id))
          //       // this.$set(this.actionParams, item.listProp, res.data.map(i => i.id))
          //       this.actionParams.data.selectData = res.data.map(i => i.id)
          //       // console.log('this.dialogFormData设置好了-->', this.dialogFormData.outbound_parcel_details)
          //       // this.innerDialogTotal = res.count
          //     } else {
          //       this.$message.error(res.detail || res.message || res.msg)
          //     }
          //   })
          // }
        }
        if (isShowDialog) {
          this.transferDialogVisible = true
        }
      }
    },
    // 初始化内部表格弹框数据
    getInnerTableData(api, query, filters) {
      this.innerDialogUrl = api
      const t_query = Object.assign({}, query)
      this.innerTableData = []

      getChoiceData(api, t_query).then(res => {
        if (res.code === 200) {
          // 过滤掉filters状态
          Object.keys(filters || {}).forEach(i => {
            res.data = res.data.filter(k => {
              if (filters[i] instanceof Array) {
                let flag = false
                filters[i].forEach(j => {
                  flag = flag || k[i] === j
                })
                return flag
              } else {
                return k[i] === filters[i]
              }
            })
          })
          this.innerTableData = res.data
          this.innerDialogTotal = res.count
        } else {
          this.$message.error(res.detail || res.message || res.msg)
        }
      })
      // console.log('this.innerTableData-->', this.innerTableData)
      // 添加筛选框数据
      this.innerDialogTableFilters && this.innerDialogTableFilters.forEach(i => {
        if (i.api) {
          getChoiceData(i.api, t_query).then(res => {
            this.$set(i, 'data', res.data || res.results)
          })
        }
      })
    },
    // 获取右上角合计
    getRightToSummary() {
      if (!this.currentTable.summary) return
      const sum = this.formData[this.dialogVal].reduce((cur, next) => {
        next.charge_total = this.cal.accMul(next.charge_rate || 0, Number(next.charge_count || 0), 2)
        return this.cal.accAdd(cur, next.charge_total, 4)
      }, 0)
      this.$set(this.currentTable, 'summaryVal', sum)
      this.currentTable = {}
    },
    // 获取右上角合计
    getRightToSummaryCommon(currentFormData, currentTable) {
      if (!currentTable.summary) return
      const sum = currentFormData.reduce((cur, next) => {
        next.charge_total = this.cal.accMul(next.charge_rate || 0, Number(next.charge_count || 0), 2)
        return this.cal.accAdd(cur, next.charge_total, 4)
      }, 0)
      this.$set(currentTable, 'summaryVal', sum)
      currentTable = {}
    },
    // 详情页tab页签数据汇总
    getSubValueSummary(currentFormData, currentTable) {
      console.log('getSubValueSummary-->', currentFormData)
      // console.log('this.formData?-->', this.formData)
      if (!currentTable.subValueSummary) return
      const prop = currentTable.subValueSummary.prop
      const sum = currentFormData.reduce((cur, next) => {
        // next.charge_total = this.cal.accMul(next.charge_rate || 0, Number(next.charge_count || 0), 2)
        return this.cal.accAdd(cur, next[prop])
      }, 0)
      // this.$set(currentTable, 'summaryVal', sum)
      // for (const target of currentTable.subValueSummary.target) {
      //   this.formData[target] = sum
      // }
      currentTable.subValueSummary.target.forEach(target => {
        this.formData[target] = sum
      })
      currentTable = {}
    },
    // 关闭弹窗
    closeDialog() {
      console.log('关闭tab弹窗')
      this.dialogFormData = {}
      this.selectIndex = null
      this.dialogSelect = ''
      this.dialogShow = false
      this.syncRevenue = false
      this.isSyncRevenue = false
    },
    // 表格选中
    selectionChange(val) {
      this.selection = val
    },
    // 表格选中
    selectionChangeInner(val) {
      this.actionParams.data.selectData = val
      console.log('选择1-->', this.actionParams.data.selectData)
    },
    // 通过一个字段的值设置另一个字段
    getValByOtherVal() {
      console.log('getValByOtherVal-->')
      // 设置其他字段
      this.setOtherVal.forEach(i => {
        if (i.type === 'child') {
          const val = this.formData[i.from].reduce((cur, next) => {
            const pre = i.pre || ''
            const exit_index = cur.findIndex(z => z[pre + 'code'] === next[i.get][pre + 'code'])
            if (exit_index === -1) {
              return cur.concat(next[i.get])
            } else {
              cur.findIndex(z => z[pre + 'code'] === next[pre + 'code'])[pre + 'qty'] += Number(next[i.get][pre + 'qty'])
              return cur.concat([])
            }
          }, [])
          this.$set(this.formData, i.val, val)
        } else if (i.type === 'merge') {
          const val = []
          this.formData[i.from].forEach(k => {
            k[i.get].forEach(j => {
              val.push({
                parcel_num: k.parcel_num,
                parcel_desc: k.parcel_desc,
                parcel_qty: k.parcel_qty,
                code: j.code,
                name: j.name
              })
            })
          })
          this.$set(this.formData, i.val, val)
        }
      })
    },
    // Excel设置行数
    excelRowSet(defaultRow = null) {
      let flag = false
      if (this.formData.parcelItem === undefined) { this.$set(this.formData, 'parcelItem', []); flag = true }
      if (defaultRow !== null) {
        this.count = defaultRow
      }
      const count_row = this.count - this.formData.parcelItem.length
      new Array(Math.abs(count_row)).fill(1).forEach((i, index) => {
        if (count_row < 0) {
          // 减少
          this.formData.parcelItem.pop()
        } else {
          // 增加
          if (this.parceType) {
            if (this.$route.path.includes('parcelCustomerOrder')) {
              this.formData.parcelItem.push({
                parcel_num: flag ? index + 1 : '',
                parcel_weight: '',
                parcel_length: '',
                parcel_width: '',
                parcel_height: '',
                actual_weight: '',
                actual_length: '',
                actual_width: '',
                actual_height: '',
                remark: '',
                item_code: '',
                declared_nameCN: '',
                declared_nameEN: '',
                declared_price: '',
                item_qty: '',
                item_weight: '',
                item_size: '',
                texture: '',
                use: '',
                brand: '',
                model: '',
                customs_code: '',
                fba_no: '',
                fba_track_code: ''
              })
            } else {
              this.formData.parcelItem.push({
                parcel_num: flag ? index + 1 : '',
                parcel_weight: '',
                parcel_length: '',
                parcel_width: '',
                parcel_height: '',
                remark: '',
                label_weight: '',
                label_length: '',
                label_width: '',
                label_height: '',
                actual_weight: '',
                actual_length: '',
                actual_width: '',
                actual_height: '',
                item_code: '',
                declared_nameCN: '',
                declared_nameEN: '',
                declared_price: '',
                item_qty: '',
                item_weight: '',
                // item_size: '',
                texture: '',
                use: '',
                brand: '',
                model: '',
                customs_code: '',
                fba_no: '',
                fba_track_code: ''
              })
            }
          } else {
            this.formData.parcelItem.push({
              // parcel_num: flag ? index + 1 : '',
              parcel_weight: '',
              parcel_length: '',
              parcel_width: '',
              parcel_height: '',
              actual_weight: '',
              actual_length: '',
              actual_width: '',
              actual_height: '',
              parcel_qty: '',
              remark: ''
            })
          }
        }
      })
    },
    // tag点击
    tagClick() {},
    // 表单输入框事件
    formEvent(e, item) {
      // if (!item.event) return
      switch (e) {
        case 'change':
          if (item.uperCase && this.formData[item.prop]) {
            this.formData[item.prop] = (this.formData[item.prop]).toUpperCase()
          }
          // 汇率和付款金额来回换算
          if (item.pay_amount) {
            // 通过汇率算付款金额
            if (this.formData[item.prop]) {
              this.formData['pay_amount'] = this.cal.accMul(this.formData['amount'], this.formData['pay_rate'], 2)
            } else {
              this.formData['pay_amount'] = null
            }
          } else if (item.pay_rate) {
            // 通过付款金额算汇率
            if (this.formData[item.prop]) {
              this.formData['pay_rate'] = this.cal.accDiv(this.formData['pay_amount'], this.formData['amount'], 4)
            } else {
              this.formData['pay_rate'] = null
            }
          }
          break
        case 'blur':
          break
        case 'enter':
          if (item.onEnter && item.onEnter === true) {
            this.$emit('handleEnter', {
              key: item.prop,
              value: this.formData[item.prop]
            })
          }
          break
        case 'focus':
          break
        default:
          break
      }
    },
    // 按钮权限设置
    authButtons(item) {
      // ['mz', 'yqf'].includes(process.env.SYS_FLAG)
      if (!item) {
        return true
      }
      if (this.$store.state.user.name === 'admin') {
        return true
      } else if (item.type === 'selectBtn') {
        return item.btns.some(i => !i.auth || (i.auth && this.$store.state.user.btns.includes(i.auth)))
      } else {
        return !item.auth || this.$store.state.user.btns.includes(item.auth)
      }
    },
    // 汇总货物信息的件数、重量、体积
    summaryGoods() {
      this.$set(this.formData, 'weight', 0)
      this.$set(this.formData, 'volume', 0)
      this.$set(this.formData, 'carton', 0)
      this.$set(this.formData, 'charge_weight', 0)
      this.$set(this.formData, 'volume_weight', 0)
      // 获取总共的包裹数来计算件数、重量、体积
      var flag = true
      const parces = this.formData.parcelItem.reduce((prev, cur) => {
        if (this.parceType) {
          // console.log('cur.parcel_num-->', cur.parcel_num)
          // 完整清单汇总
          if (prev.every(i => i.parcel_num + '' !== cur.parcel_num + '')) {
            prev.push(cur)
            if (!cur.parcel_length) flag = false
            if (!cur.parcel_width) flag = false
            if (!cur.parcel_height) flag = false
            if (!cur.parcel_weight) flag = false
            if (!cur.parcel_num && this.parceType) flag = false
            this.formData.weight = this.cal.accAdd(this.formData.weight, Number(cur.parcel_weight))
            this.formData.volume = Math.max(this.cal.accAdd(this.formData.volume, (Number(cur.parcel_length) * Number(cur.parcel_width) * Number(cur.parcel_height)) * 0.000001), 0.01)
            this.formData.carton = this.formData.carton + 1
          }
        } else {
          // 简易清单汇总
          prev.push(cur)
          if (!cur.parcel_length) flag = false
          if (!cur.parcel_width) flag = false
          if (!cur.parcel_height) flag = false
          if (!cur.parcel_weight) flag = false
          if (!cur.parcel_qty) flag = false
          if (!cur.parcel_num && this.parceType) flag = false
          this.formData.weight = this.cal.accAdd(this.formData.weight, Number(cur.parcel_weight))
          this.formData.volume = Math.max(this.cal.accAdd(this.formData.volume, (Number(cur.parcel_length) * Number(cur.parcel_width) * Number(cur.parcel_height)) * 0.000001 * Number(cur.parcel_qty)), 0.01)
          this.formData.carton = this.formData.carton + Number(cur.parcel_qty)
        }
        return prev
      }, [])
      console.log(parces)
      // 体积最终四舍五入保留3位小数并与0.001相比较取大的值
      if (this.formData.volume) this.formData.volume = Math.max(this.formData.volume.toFixed(2), 0.001)

      if (!flag) {
        this.$message.error(this.$t('common.enter') + `${this.parceType ? this.$t('common.casenumber') + '、' : ''}${this.$t('common.weight')}、${this.$t('common.length')}、${this.$t('common.width')}、${this.$t('common.height')}${!this.parceType ? '、' + this.$t('common.casenumber') : ''}`)
        return false
      }

      // 设置计费重量
      const num = this.cal.accDiv(this.formData.volume, this.formData.charge_trans)
      this.formData.charge_weight = (num > this.formData.weight ? num : this.formData.weight).toFixed(1)
      // 设置重泡比
      this.formData.volume_weight = this.cal.accDiv(this.formData.weight, this.formData.volume).toFixed(0)

      // 重量和计费重量二次设置小数位小于0.5取0.5      大于0.5向上取整
      this.formData.weight = this.fixNum(this.formData.weight)
      this.formData.charge_weight = this.fixNum(this.formData.charge_weight)
      return true
    },
    // 新四舍五入
    fixNum(num) {
      num = num + ''
      if (!num.includes('.')) {
        num = num + '.0'
      } else if (Number(num.replace(/\d+\.(\d*)/, '$1')) === 0) {
        num = Math.floor(num) + '.0'
      } else if (num.replace(/\d+\.(\d*)/, '$1').charAt(0) < 5) {
        num = Math.floor(num) + '.5'
      } else {
        num = Math.ceil(num) + '.0'
      }
      return num
    },
    // 文件上传相关---------------------------------------
    // 文件变更
    fileChange(file, fileList) {
    },
    // 文件上传之前
    beforeUpload(file) {},
    // 文件移除之前
    beforeRemove(file, fileList, item, singel) {
      // 单文件移除
      // 如果是 customFiles 类型，使用新的删除方法
      if (item.type === 'customFiles') {
        return this.customBeforeRemove(file, fileList, item, singel)
      }
      if (singel) {
        return new Promise((resolve, reject) => {
          this.$confirm(this.$t('common.delecomfirm'), this.$t('common.tips'), {
            confirmButtonText: this.$t('common.sure'),
            cancelButtonText: this.$t('common.cancle'),
            type: 'warning'
          }).then(() => {
            if (!this.id) {
              resolve(true)
              return true
            }
            var api
            if (item.url) {
              // api = `api/${item.url}/`
              const params = {
                id: this.id,
                api: `${item.url}`,
                data: {}
              }
              del(params).then(res => {
                if (res.code === 200) {
                  this.$message.success(this.$t('common.deletesuccess'))
                } else {
                  this.$message.error(this.$t('common.deletefail'))
                }
                resolve(true)
                return true
              }).catch(() => {
                resolve(false)
                return false
              })
            } else {
              api = `api/${this.requestUrl.baseUrl}/detele_file/`
              const params = {
                api: api,
                data: { id: this.id, fileName: item.prop }
              }
              console.log('干啥去了?-->')
              actionPost(params).then(res => {
                if (res.code === 200) {
                  this.$message.success(this.$t('common.deletesuccess'))
                } else {
                  this.$message.error(this.$t('common.deletefail'))
                }
                resolve(true)
                return true
              }).catch(() => {
                resolve(false)
                return false
              })
            }
          }).catch(() => {
            reject(false)
            return false
          })
        })
      } else {
        return new Promise((resolve, reject) => {
          this.$confirm(this.$t('common.delecomfirm'), this.$t('common.tips'), {
            confirmButtonText: this.$t('common.sure'),
            cancelButtonText: this.$t('common.cancle'),
            type: 'warning'
          }).then(() => {
            if (!file.id) {
              resolve(true)
              return true
            }
            del({ api: this.filesUrl(item).url, id: file.id, data: {}}).then(res => {
              resolve(true)
              return true
            }).catch(() => {
              resolve(false)
              return false
            })
          }).catch(() => {
            reject(false)
            return false
          })
        })
      }
    },
    // 自定义文件删除方法
    customBeforeRemove(file, fileList, item, singel) {
      return new Promise((resolve, reject) => {
        this.$confirm(this.$t('common.delecomfirm'), this.$t('common.tips'), {
          confirmButtonText: this.$t('common.sure'),
          cancelButtonText: this.$t('common.cancle'),
          type: 'warning'
        }).then(() => {
          // 新增状态直接返回
          const id = this.id
          if (!id) {
            resolve(true)
            return true
          }
          // 调用删除文件接口
          const api = `api/${this.requestUrl.baseUrl}/delete_file/`
          const params = {
            api: api,
            data: {
              id: this.id,
              file_url: file.url,
              fileName: 'main_file'
            }
          }
          actionPost(params).then(res => {
            if (res.code === 200) {
              this.$message.success(this.$t('common.deletesuccess'))
              resolve(true)
              return true
            } else {
              this.$message.error(this.$t('common.deletefail'))
              resolve(false)
              return false
            }
          }).catch(() => {
            resolve(false)
            return false
          })
        }).catch(() => {
          reject(false)
          return false
        })
      })
    },
    // 文件移除
    handleRemove(file, fileList, item) {
      if (item.prop === 'signForAttachments') {
        this.fileList2 = fileList
        this.formData.signForAttachments = this.formData.signForAttachments.filter(i => i.name !== file.name)
      } else if (item.prop === 'relative_attachments') {
        // 保存表单时不上传附件, 因为在上传附件的时候已经上传了, 保存的时候去掉该字段
        this.fileList = fileList
        this.formData.relative_attachments = this.formData.relative_attachments.filter(i => i.name !== file.name)
      } else if (item.prop === 'pre_recording_attachments') {
        item.fileList = item.fileList.filter(i => i.id !== file.id)
        this.formData.pre_recording_attachments = this.formData.pre_recording_attachments.filter(i => i.name !== file.name)
      } else if (item.prop === 'release_attachments') {
        item.fileList = item.fileList.filter(i => i.id !== file.id)
        this.formData.release_attachments = this.formData.release_attachments.filter(i => i.name !== file.name)
      } else {
        this.fileList = fileList
        this.formData.attachments = this.formData.attachments.filter(i => i.name !== file.name)
      }
    },
    // 自定义上传
    uploadAttchment(data, item) {
      if (!this.formData.id) {
        // 新增上传
        if (item.prop === 'signForAttachments') {
          this.waitToUpload.append('files2', data.file)
        } else {
          this.waitToUpload.append('files', data.file)
        }
        return
      }
      const formData = new FormData()
      formData.append('name', data.file.name)
      formData.append('url', data.file)
      this.formData.id ? formData.append(this.filesUrl(item).order, this.formData.id) : ''
      const p = {
        api: this.filesUrl(item).url,
        data: formData
      }
      add(p).then(res => {
        if (res.code === 200 || res.code === 201) {
          this.$message.success(this.$t('common.uploadedsuccess'))
          if (item.prop === 'signForAttachments') {
            this.fileList2.push({ name: res.data.name, url: res.data.url, id: res.data.id })
            this.formData.signForAttachments.push({ name: res.data.name, url: res.data.url })
          } else if (item.prop === 'pre_recording_attachments') {
            item.fileList.push({ name: res.data.name, url: res.data.url, id: res.data.id })
            this.formData.pre_recording_attachments.push({ name: res.data.name, url: res.data.url })
          } else if (item.prop === 'release_attachments') {
            item.fileList.push({ name: res.data.name, url: res.data.url, id: res.data.id })
            this.formData.release_attachments.push({ name: res.data.name, url: res.data.url })
          } else {
            this.fileList.push({ name: res.data.name, url: res.data.url, id: res.data.id })
            this.formData.attachments.push({ name: res.data.name, url: res.data.url })
          }

          if (item.flag === 'pre_recording_attachments') {
            this.handlePreOrderAttachmentsUploadSuccess(res, data)
          }
        } else {
          this.$message.error(res.detail || this.$t('common.uploadedfail'))
        }
      }).catch(() => {})
    },
    // 新增的时候添加附件
    newAttachments(id) {
      if (id) this.waitToUpload.append(this.filesUrl('').order, id)
      console.log('newAttachments 什么url-->', this.filesUrl('').url)
      actionPost({ api: `api/${this.filesUrl('').url}/batch_upload/`, data: this.waitToUpload }).then(res => {
        this.cachesInit(true)
      })
    },
    uploadSingleAttchment(data, item) {
    },
    singleFileChange(file, fileList, item) {
      this.singlefile.delete(`${item.prop}`)
      this.singlefile.append(`${item.prop}`, file.raw)
      if (this.id) this.handleFile(this.id, file.raw, item)
    },
    singleCustomFileChange(file, fileList, item) {
      // 存储文件二进制到 FormData 对象中
      this.singlefile.delete(`${item.prop}`)
      this.singlefile.append(`${item.prop}`, file.raw)

      // 将文件二进制数据存储到 fileList3
      // 将文件二进制数据存储到 fileList3
      this.fileList3.set(item.prop, file.raw)

      // // 将文件二进制数据存储到 main_file 字段
      // this.formData[item.prop] = file.raw

      // // 更新文件列表显示
      // item.fileList = [{
      //   name: file.name,
      //   url: URL.createObjectURL(file.raw)
      // }]
      // console.log('singleCustomFileChange 什么url-->', this.formData[item.prop])
      // console.log('singleCustomFileChange 什么url-->', this.formData)
    },
    // 上传单个附件
    handleFile(id, file, item, isAdd) {
      if (!id) return
      const data = isAdd ? this.singlefile : new FormData()
      !isAdd ? data.append(`${item.prop}`, file) : ''
      data.append(`id`, id)
      var api
      if (item.url) {
        api = `api/${item.url}/`
      } else {
        api = `api/${this.requestUrl.baseUrl}/upload_file/`
      }
      const params = {
        api: api,
        data
      }
      actionPost(params).then(res => {
        if (res.code === 200) {
          !isAdd ? this.$message.success(this.$t('common.uploadedsuccess')) : ''
          console.log('上传成功了-->', res)
        } else {
          !isAdd ? this.$message.error(res.detail || this.$t('common.uploadedfail')) : ''
        }
      })
    },
    // 上传 fileList3 中的文件
    async uploadFileList3(id) {
      if (!this.fileList3 || this.fileList3.size === 0) return

      const uploadPromises = []
      this.fileList3.forEach((file, prop) => {
        const formData = new FormData()
        formData.append(prop, file)
        formData.append('id', id)

        const uploadPromise = actionPost({
          api: `api/${this.requestUrl.baseUrl}/upload_file/`,
          data: formData
        })
        uploadPromises.push(uploadPromise)
      })

      try {
        await Promise.all(uploadPromises)
        this.$message.success(this.$t('common.uploadedsuccess'))
        // 上传成功后清空 fileList3
        this.fileList3.clear()
      } catch (error) {
        this.$message.error(this.$t('common.uploadedfail'))
      }
    },
    singlehandleRemove(file, fileList, item) {
      this.singlefile.delete(`${item.prop}`)
    },
    // 最多只允许上传1个文件
    handleExceedOne(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    // 最多只允许上传5个文件
    handleExceedFive(files, fileList) {
      this.$message.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    // 切换商品类型
    switchGoodType() {
      if (this.parceType !== this.newParceType && this.count) {
        this.$confirm(this.$t('common.switchType'), this.$t('common.attention'), {
          confirmButtonText: this.$t('common.sure'),
          cancelButtonText: this.$t('common.cancle'),
          type: 'danger'
        }).then(() => {
          this.parceType = this.newParceType
        }).catch(() => {
          this.newParceType = this.parceType
        })
      } else {
        this.parceType = this.newParceType
      }
    },
    // 添加收件人和发件人
    // todo_s: 表单修改数据后同步生成其他字段的数据(设置change属性后生效)
    addSelect(type, item) {
      // eslint-disable-next-line
      if (type === 'change') {
        const data = this.optionData[item.prop]['data'].find(i => i[this.optionData[item.prop]['value']] === this.formData[item.prop])
        // 回显到文本域
        if (data === undefined) {
          item.textVal = ''
        }
        const prefix = item.prop.startsWith('s') ? '' : 'buyer_'
        const { address_num = '', contact_phone = '', contact_name = '', company_name = '', contact_email = '', country_code = '', state_code = '', city_code = '', postcode = '', house_no = '', address_one = '', address_two = '' } = data || {}
        this.formData[!prefix ? 'contact_name' : 'buyer_name'] = contact_name
        this.formData[!prefix ? 'company_name' : 'buyer_company_name'] = company_name
        this.formData[!prefix ? 'contact_email' : 'buyer_mail'] = contact_email
        this.formData[!prefix ? 'country_code' : 'buyer_country_code'] = country_code
        this.formData[!prefix ? 'state_code' : 'buyer_state'] = state_code
        this.formData[!prefix ? 'city_code' : 'buyer_city_code'] = city_code
        this.formData[!prefix ? 'postcode' : 'buyer_postcode'] = postcode
        this.formData[!prefix ? 'house_no' : 'buyer_house_num'] = house_no
        this.formData[!prefix ? 'address_one' : 'buyer_address_one'] = address_one
        this.formData[!prefix ? 'address_two' : 'buyer_address_two'] = address_two
        this.formData[!prefix ? 'contact_phone' : 'buyer_phone'] = contact_phone
        this.formData[!prefix ? 'address_num' : 'buyer_address_num'] = address_num
        if (data === undefined) {
          item.textVal = ''
          return
        }
        item.textVal = `${company_name}\n${address_one},${house_no}\n${address_two}\n${city_code},${state_code},${country_code},${postcode}\n${contact_name},${contact_email}`
      } else if (type === 'add') {
        this.select_item = item
        // 打开弹窗
        this.dialogselectShow = true
      } else if (type === 'comfirm') {
        // 确定添加
        const prefix = this.select_item.prop.startsWith('s') ? '' : 'buyer_'
        const { async, address_num, contact_phone, contact_name, company_name, contact_email, country_code, state_code, city_code, postcode, house_no, address_one, address_two } = this.adressFormData
        this.formData[!prefix ? 'contact_name' : 'buyer_name'] = contact_name
        this.formData[!prefix ? 'company_name' : 'buyer_company_name'] = company_name
        this.formData[!prefix ? 'contact_email' : 'buyer_mail'] = contact_email
        this.formData[!prefix ? 'country_code' : 'buyer_country_code'] = country_code
        this.formData[!prefix ? 'state_code' : 'buyer_state'] = state_code
        this.formData[!prefix ? 'city_code' : 'buyer_city_code'] = city_code
        this.formData[!prefix ? 'postcode' : 'buyer_postcode'] = postcode
        this.formData[!prefix ? 'house_no' : 'buyer_house_num'] = house_no
        this.formData[!prefix ? 'address_one' : 'buyer_address_one'] = address_one
        this.formData[!prefix ? 'address_two' : 'buyer_address_two'] = address_two
        this.formData[!prefix ? 'contact_phone' : 'buyer_phone'] = contact_phone
        this.formData[!prefix ? 'address_num' : 'buyer_address_num'] = address_num
        this.formData[!prefix ? 'is_b_async' : 'is_r_async'] = async
        this.select_item.textVal = `${company_name}\n${address_one},${house_no}\n${address_two}\n${city_code},${state_code},${country_code},${postcode}\n${contact_name},${contact_email}`
        this.dialogselectShow = false
        Object.keys(this.adressFormData).forEach(i => {
          this.adressFormData[i] = ''
        })
        // 清除收发件人下拉外键关联
        this.formData[this.select_item.prop] = null
      } else if (type === 'cancle') {
        this.dialogselectShow = false
        Object.keys(this.adressFormData).forEach(i => {
          this.adressFormData[i] = ''
        })
      }
    },
    addChargeOffSelect(type, item) {
      if (type === 'confirm') {
        // 确定添加
        this.dialogChargeOffShow = false
      } else if (type === 'cancel') {
        this.dialogChargeOffShow = false
      }
    },
    // 显示隐藏单独增删改查
    tableModifyShow(obj = {}) {
      let flag = false
      Object.keys(obj).forEach(i => {
        if (i.startsWith('$')) {
          // flag = this.formData[i.substr(1)] !== obj[i]
          flag = this.formData[i] !== '$' + obj[i]
        } else {
          flag = this.formData[i] === obj[i]
        }
      })
      // 详情页底部不显示修改按钮的时候才显示这个flag
      return flag && !this.showBtn
    },
    tableModifyShowCommon(obj, defaultValue = true) {
      if (obj === undefined) {
        return defaultValue
      }
      let flag = true
      Object.keys(obj).forEach(i => {
        if (i.startsWith('$')) {
          flag = this.formData[i.substr(1)] !== obj[i]
        } else {
          flag = this.formData[i] === obj[i]
        }
      })
      return flag
    },
    // 单文件的文件名回显或者url获取
    getSingleFileName(item = { prop: '' }, getName) {
      // todo_s: 单文件显示为 当前单号+文件后缀名
      if (getName) {
        if (item.origin_name) {
          return decodeURI(this.getUrlName(this.formData[item.prop]))
        } else if (item.prop === 'main_file') {
          return this.formData['order_num'] || this.formData['clearance_num'] + this.getUrlName(this.formData[item.prop], true)
        } else if (item.prop === 'cabin_file') {
          return this.formData['order_num'] || this.formData['clearance_num'] + '-mainfest' + this.getUrlName(this.formData[item.prop], true)
        } else if (item.prop === 'check_file') {
          return this.formData['order_num'] || this.formData['clearance_num'] + '_check_bills' + this.getUrlName(this.formData[item.prop], true)
        } else {
          // return this.formData['truck_order_num'] + this.getUrlName(this.formData[item.prop], true)
          return this.getUrlName(this.formData[item.prop], false)
        }
      } else {
        return this.formData[item.prop].split('api')[0] + 'media' + this.formData[item.prop].split('media')[1]
      }
    },
    // 拼接图片url
    getImageUrl(url) {
      return url.split('api')[0] + 'media' + url.split('media')[1]
    },
    // 获取文件名
    getUrlName(url, suffix = false) {
      if (suffix) return url.substring(url.lastIndexOf('.'))
      return decodeURI(url.substring(url.lastIndexOf('/') + 1))
    },
    // 按钮导入excel，例如包裹商品
    importExcel(data, item) {
      const api = `api/${this.requestUrl.baseUrl}/${item.method || item.link}/`
      const formData = new FormData()
      formData.append('file', data.file)
      formData.append('id', this.id)
      actionPost({ api, data: formData }).then(res => {
        if (res.code === 200) {
          this.$message.success(this.$t('common.importsusses'))
          this.initFormData()
        } else {
          this.$message.error(res.detail || res.message || res.msg)
        }
      })
    },
    // 按钮显示隐藏
    bottomBtnShow(btn) {
      if (btn.id && !this.id) return false
      if (btn.edit && !this.isAdd) return false
      return true
    },
    // 按钮事件
    bottomBtnEvent(btn) {
      var f = () => {
        if (btn.method === 'download_template') {
          this.$message.success(`btn-->${btn.method}, ${btn.link}`)
          download_Template(btn.link, process.env.HOST)
          return
        }
        const api = `api/${this.requestUrl.baseUrl}/${btn.method}/`
        const formData = {}
        if (btn.method === 'submit_fbm_order') {
          formData.ids = [this.id]
        } else if (btn.method === 'change_fbm_next_track') {
          formData.id = this.id
          // this.closePage()
        }
        actionPost({ api, data: formData }).then(res => {
          if (res.code === 200) {
            this.$message.success('Success!')
            this.initFormData()
          } else {
            this.$message.error(res.detail || res.message || res.msg)
          }
        })
      }
      if (btn.comfirm) {
        this.$confirm(this.$t('content.continue'), this.$t('content.tips'), {
          confirmButtonText: this.$t('content.Ok'),
          cancelButtonText: this.$t('content.cancel'),
          type: 'danger'
        }).then(() => {
          f()
        }).catch(() => {
        })
      } else {
        f()
      }
    },
    // 拦截/取消拦截包裹
    interceptParcel(parcel_id, type = 'operate') {
      let api
      if (type === 'operate') {
        api = `/api/customerOrders/parcel_intercept/`
      } else {
        api = `/api/customerOrders/parcel_cancel_intercept/`
      }
      const params = {
        api: api,
        data: { id: parcel_id }
      }
      console.log('拦截', params)
      actionPost(params).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.initFormData()
        } else {
          this.$message.error(res.detail || res.message || res.msg)
        }
      })
    },
    // 接收弹框选项
    receiveTruckOptionsForm(data) {
      this.rowId = data.rowId
    },
    onUserUpdateSubmit() {
      const params = {
        api: '/api/companies/update_user_info/',
        data: this.dialogUserUpdateData
      }
      actionPost(params).then(res => {
        if (res.code === 200) {
          this.$message[res.info || 'success'](res.msg)
          this.initFormData()
        } else {
          this.$message.error(res.detail || res.message || res.msg)
        }
      })
      this.dialogUserUpdateOffShow = false
    },
    onUserUpdateCancel() {
      this.dialogUserUpdateOffShow = false
    },
    defaultRemoteMethod(query, config) {
      if (query !== '') {
        config.loading = true // 显示loading
        this.fetchOptions(query, config).then((options) => {
          this.remoteSelectOptions = options // 更新远程搜索选项
        }).finally(() => {
          config.loading = false // 隐藏loading
        })
      } else {
        this.remoteSelectOptions = [] // 清空远程搜索选项
      }
    },
    dialogRemoteSelectMethod(query, config, prop) {
      if (query !== '') {
        config.loading = true // 显示loading
        this.fetchOriginOptions(query, config).then((options) => {
          this.$set(this.dialogOptionData, prop,
            {
              data: options,
              label: config.label || 'name',
              value: config.value || 'id'
            }
          )
        }).finally(() => {
          config.loading = false // 隐藏loading
        })
      } else {
        this.$set(this.dialogOptionData, prop,
          {
            data: [],
            label: config.label || 'name',
            value: config.value || 'id'
          }
        )
      }
    },
    fetchOptions(query, config) {
      return getChoiceData(config.remoteUrl, { q: query })
        .then(response => {
          // 假设接口返回的数据格式为[{ value: '1', label: '选项1' }, { value: '2', label: '选项2' }]
          return response.data.map(item => ({
            value: config.valueKey ? item[config.valueKey] : item.value,
            label: item.label
          }))
        })
    },
    fetchOriginOptions(query, config) {
      return getChoiceData(config.remoteUrl, { q: query })
        .then(response => {
          // 假设接口返回的数据格式为[{ value: '1', label: '选项1' }, { value: '2', label: '选项2' }]
          return response.data
        })
    },
    // 过滤条件
    judgeCondition(orderCondition) {
      if (!orderCondition) {
        return true
      }
      const len = (Object.keys(orderCondition)).length
      const tem = new Array(len).fill(false)
      if (len === 0) {
        return true
      } else {
        let flag = true
        Object.keys(orderCondition).forEach((i, index) => {
          if (i.startsWith('$')) {
            if (orderCondition[i] instanceof Array) {
              flag = !orderCondition[i].includes(this.formData[i.substr(1)])
            } else {
              flag = this.formData[i.substr(1)] !== orderCondition[i]
            }
          } else if (orderCondition[i] instanceof Array) {
            flag = orderCondition[i].includes(this.formData[i])
          } else {
            flag = this.formData[i] === orderCondition[i]
          }
          tem[index] = flag
        })
        console.log('判断结果:', tem.every(i => i))
        return tem.every(i => i)
      }
    },
    // 调用后端内容识别接口
    contentRecognition(item) {
      // 判断不能为空
      const contentValue = this.formData[item.prop]
      const customer = this.formData['customer']
      if (customer === undefined) {
        this.$message({
          message: '客户 必填，否则无法识别新建揽收地址！',
          type: 'warning'
        })
        return
      }
      if (contentValue === undefined || contentValue.trim().length === 0) {
        this.$message({
          message: '快递信息内容不能为空！',
          type: 'warning'
        })
        return
      }

      this.buttonLoadingState = true // 请求开始，按钮进入加载状态
      const params = {
        api: `api/address_info/recognition`,
        data: {
          contentValue: contentValue,
          customer: customer
        }
      }
      // 提交
      actionPost(params).then(res => {
        if (res.code === 200) {
          this.formData['remark'] = res['remark']
          this.formData['plan_visit_time'] = res['plan_visit_time']
          this.formData['address'] = res['address_info']
          this.$message.success('识别成功，请检查结果！')
        } else if (res.code === 400) {
          this.$message.error(res.msg)
        } else {
          this.$message.error('识别失败！')
        }
      })
        .catch(error => {
          console.error('请求失败:', error)
        })
        .finally(() => {
          this.buttonLoadingState = false // 请求完成后，按钮停止加载
        })
    },
    handlePreOrderAttachmentsUploadSuccess(response, data) {
      // 上传成功之后，解析pdf，并更新表单数据
      const formData = new FormData()
      formData.append('id', response.data.clearance_out)
      formData.append('files', data.file)

      request({
        url: '/api/clearanceOutOrders/parse_customs_clearance_files/', // 上传地址
        method: 'post',
        data: formData,
        headers: {
          ...this.headers, // 添加请求头（如 Authorization）
          'Content-Type': 'multipart/form-data' // 设置内容类型
        }
      })
        .then(response => {
          if (response.code === 200 && response.data) {
            if (response.msg) {
              this.$message.warning(response.msg)
            }
            // 更新表单数据
            const { transaction_mode, trade_mode, gross_weight, net_weight, carton } = response.data
            if (transaction_mode) {
              this.$set(this.formData, 'transaction_mode', transaction_mode)
            }
            if (trade_mode) {
              this.$set(this.formData, 'trade_mode', trade_mode)
            }
            if (gross_weight) {
              this.$set(this.formData, 'gross_weight', gross_weight)
            }
            if (net_weight) {
              this.$set(this.formData, 'net_weight', net_weight)
            }
            if (carton) {
              this.$set(this.formData, 'carton', carton)
            }
          } else {
            this.$message.error(response.msg || '文件自动解析失败，请上传正确的PDF文件！')
          }
        })
        .catch(error => {
          console.error('请求失败:', error)
        })
    },
    // 解析条件表达式
    parseConditions(conditionStr) {
      // 分割多个条件（用 or 分隔）
      const conditions = conditionStr.split(/\s+or\s+/)
      return conditions.some(condition => {
        // 分析单个条件，支持带引号的字符串值
        const matches = condition.match(/(\w+)\s*=\s*(?:'([^']*)'|null)/)
        if (!matches) return false

        const [, field, value] = matches
        if (value === undefined) {
          // 处理 null 值的情况
          return this.formData[field] === null
        } else {
          // 处理普通值比较，包括字符串值
          return this.formData[field] === value
        }
      })
    },
    executeChargeOffDebit(btn, isSelectBtn = false) {},
    innerDialogConfirm(isTransferDialog = false) {
      // 暂存到当前表单, 点击保存时提交后台保存
      this.$message.warning('暂存到当前表单, 点击保存时提交后台保存')
      console.log('当前选择的值-->', this.actionParams.data.selectData)
      console.log('当前大表单数据-->', this.formData)
      console.log('当前小表单数据-->', this.dialogFormData)
      console.log('当前操作按钮-->', this.dialogBtn)
      // 暂时未使用
      // if (this.dialogBtn && this.dialogBtn.type === 'operate' && this.dialogBtn.prop) {
      //   if (typeof this.formData[this.dialogBtn.prop] === 'undefined') {
      //     this.$set(this.formData, this.dialogBtn.prop, [])
      //   }
      //   const ids = this.formData[this.dialogBtn.prop].map(i => i.id)
      //   this.actionParams.data.selectData.forEach(i => {
      //     if (!ids.includes(i.id)) {
      //       this.formData[this.dialogBtn.prop].push(i)
      //     }
      //   })
      // }

      this.saveOutboundParcelMap(this.dialogFormData['shipment_id'], this.actionParams.data.selectData)

      if (isTransferDialog) {
        this.transferDialogVisible = false
      } else {
        this.innerDialogVisible = false
        this.dialogFormData = {}
      }
    },
    dialogOperate(row, item) {
      const params = { method: item.method, thisId: this.id, data: row }
      this.execute(params)
      this.getInnerTableData(this.innerTable.api, this.innerTable.query, this.innerTable.filters)
    },
    execute(item) {
      console.log('detail execute-->')
      const { method, thisId, ids, data, isCheckEmpty } = item
      // const ids = this.formData.checkData.map(i => i.id)
      if (isCheckEmpty && ids.length === 0) {
        this.$message.error(this.$t('content.selectItem'))
        return
      }
      var f = () => {
        this.bus.$emit('fullLoading', true)
        const api = 'api/' + this.requestUrl.baseUrl + `/${method}/`
        actionPost({ api, data: { thisId, ids, ...data }}).then(res => {
          if (res.code === 200) {
            this.$message[res.info || 'success'](res.msg)
            // this.getInnerTableData(this.innerTable.api, this.innerTable.query, this.innerTable.filters)
          } else {
            this.$message.error(res.detail || res.message || res.msg)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => { this.bus.$emit('fullLoading', false) })
      }

      const comfirm = (this.formData.popUp && this.formData.popUp.find(i => i.method === method) || {})['comfirm']
      if (comfirm) {
        this.$confirm('确定执行此操作, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        }).then(() => {
          f()
        }).catch(() => {})
      } else {
        f()
      }
    },
    innerHandleClose() {
      this.innerDialogVisible = false
      this.innerDialogTableFilters.forEach(i => {
        i.value = ''
      })
    },
    innerSizeChangeDialog(e) {
      this.innerDialogPage = 1
      this.innerDialogSize = e
      this.innerDialogPageChange(this.innerMultiParams)
    },
    innerPageChangeDialog(e) {
      this.innerDialogPage = e
      this.innerDialogPageChange(this.innerMultiParams)
    },
    innerDialogPageChange(queryParams) {
      this.innerDialogQuery(queryParams, Object.keys(this.innerMultiParams).length !== 0)
    },
    innerDialogQuery(queryParams, mutil = false) {
      console.log('queryParams-->', queryParams, mutil)
      console.log('this.innerTable.query-->', this.innerTable.query)
      const t_query = Object.assign({}, this.innerTable.query)
      // 对query进行处理,$代表从选中的行取值
      // let flag = true
      Object.keys(t_query).forEach(i => {
        if (typeof (t_query[i]) === 'string' && t_query[i].startsWith('$')) {
          t_query[i] = this.innerTableData[this.innerTable.query[i].slice(1)]
          // flag = this.$parent.formData.checkData.every(k => k[query[i].slice(1)] === t_query[i])
        } else if (typeof (t_query[i]) === 'string' && t_query[i].endsWith('$')) {
          // 传选中id
          // t_query[i] = this.actionQuery.checkData[0].id
        }
      })
      if (mutil) {
        this.innerMultiParams = queryParams
        this.$parent.page = 1
        this.innerMultiInit(queryParams, this.innerTablewithFilter)
        this.innerMultiSearchShow = false
        return
      } else {
        this.innerMultiParams = {}
      }
      // this.innerDialogTableParams
      this.innerDialogTableParams = {
        page: this.innerDialogPage,
        size: this.innerDialogSize,
        ordering: this.innerDialogOrdering,
        ...t_query
      }
      this.innerDialogTableParams['search'] = this.innerDialogSearch.value
      this.innerDialogTableFilters && this.innerDialogTableFilters.forEach(i => {
        console.log('i.prop-->', i.prop)
        if (typeof i.value === 'boolean' || i.value) {
          this.innerDialogTableParams[i.prop] = (i.value).toString()
          this.filterProp[i.prop] = (i.value).toString()
        } else {
          this.$delete(this.filterProp, i.prop)
        }
      })
      this.sentRequestInner(this.innerDialogUrl, this.innerDialogTableParams)
    },
    sentRequestInner(api, params = {}) {
      // 发送请求
      getChoiceData(api, params).then(res => {
        if (res.code === 200) {
          console.log('成功获取数据')
        } else if (res.code === 404) {
          console.log('没有搜索到数据')
        } else {
          this.$message.error(res.detail || res.message || res.msg)
        }
        this.innerTableData = res.data
        console.log('怎么回事-->', this.innerTableData)
        this.innerDialogTotal = res.count
        this.bus.$emit('fullLoading', false)
      }).catch((error) => {
        this.$message.error(error)
        this.bus.$emit('fullLoading', false)
      })
    },
    innerMultiInit(mutiData, withFilter = '0') {
      this.loading = true
      const params = {
        multiData: mutiData,
        page: this.innerDialogPage,
        size: this.innerDialogSize
      }
      if (withFilter === '1') {
        // 带过滤条件
        this.innerTable && this.innerTable.filters && this.innerTable.filters.forEach(i => {
          if (typeof i.value === 'boolean' || i.value) {
            this.innerDialogTableParams[i.prop] = (i.value).toString()
            this.filterProp[i.prop] = (i.value).toString()
          } else {
            this.$delete(this.filterProp, i.prop)
          }
        })
        params['filterProp'] = this.filterProp
      }
      initMutilData(`${this.innerTable.api}`, params).then(res => {
        // this.total = res.count
        this.innerTableData = res.data
        this.innerDialogTotal = res.count
        setTimeout(() => { this.loading = false }, 200)
      }).catch(err => {
        this.loading = false
        console.log(err)
      })
    },
    // 点击下拉框
    getSelectData(item) {
      // this.initChoiceData(item)
    },
    autoSelect() {
      let count = parseInt(this.autoSelectedCount, 10)
      if (isNaN(count) || count <= 0) {
        this.$message.warning('请输入有效的正整数')
        return
      }
      // 筛选出未被选择的包裹
      const availableParcels = this.transferDialogFormData.filter(item =>
        !this.actionParams.data.selectData.includes(item.id)
      )
      // 获取未出仓包裹的数量
      const availableCount = availableParcels.length
      if (count > availableCount) {
        count = availableCount
        this.$message.warning('输入的数量超过了未出仓包裹的数量，已自动调整为最大可选择数量。')
      }
      // 获取未出仓包裹的前 n 个 id
      const newSelectedIds = availableParcels
        .slice(0, count)
        .map(item => item.id)
      // 合并已选择的 id 和新选择的 id
      this.actionParams.data.selectData = [
        ...this.actionParams.data.selectData,
        ...newSelectedIds
      ]
      // 去重（理论上不需要，但为了保险）
      this.actionParams.data.selectData = Array.from(new Set(this.actionParams.data.selectData))
    },
    autoSelectInit() {
      const selectInitCount = this.dialogFormData[this.dialogBtn.prop]
      console.log('选了多少-->', selectInitCount)
      // 检查输入的数量是否为有效的正整数
      let count = parseInt(selectInitCount, 10)
      if (isNaN(count) || count <= 0) {
        this.$message.warning('请输入有效的正整数')
        return
      }

      this.actionParams.data.selectData = []

      // 筛选出未被选择的包裹
      const availableParcels = this.transferDialogFormData.filter(item =>
        !this.actionParams.data.selectData.includes(item.id)
      )
      console.log('this.transferDialogFormData-->', this.transferDialogFormData)
      console.log('availableParcels-->', availableParcels)
      // 获取未出仓包裹的数量
      const availableCount = availableParcels.length

      // 检查输入的数量是否超过了未出仓包裹的数量
      if (count > availableCount) {
        count = availableCount
        this.$message.warning('输入的数量超过了未出仓包裹的数量，已自动调整为最大可选择数量。')
        this.dialogFormData[this.dialogBtn.prop] = count
      }

      // 获取未出仓包裹的前 n 个 id
      const newSelectedIds = availableParcels
        .slice(0, count)
        .map(item => item.id)

      // 合并已选择的 id 和新选择的 id
      this.actionParams.data.selectData = [
        ...this.actionParams.data.selectData,
        ...newSelectedIds
      ]

      // 去重（理论上不需要，但为了保险）
      this.actionParams.data.selectData = Array.from(new Set(this.actionParams.data.selectData))
      this.innerDialogConfirm()
    }

  }
}
</script>

<style lang="scss" scoped>
.el-form-item__content span {
color:black;
}

.pageRoot {
margin: 20px;
}

.iconfont {
font-size: 14px;
}
.table-summary-supplier {
  float: right;
  font-size: 14px;
  margin-left: 10px;
}
.table-summary {
  //text-align: right;
  float: right;
  font-size: 14px;
  margin-left: 10px;
span {
  color: red;
  font-weight: bold;
  }
}
///deep/ .el-textarea__inner {
//  max-height: 120px;
//}

/deep/ .el-transfer-panel {
  width: 320px;
}

</style>
