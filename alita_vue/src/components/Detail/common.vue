<template>
  <div class="pageRoot">
    <page
      ref="commonPage"
      :detail-edit="initObject.detailEdit"
      :detele-disabled="initObject.deteleDisabled"
      :detail-api="initObject.detailApi"
      :request-url="initObject.requestUrl"
      :read-only="initObject.readOnly"
      :option="initObject.option"
      :dialog-option="initObject.dialogOption"
      :init-data="initObject.initData"
      :is-add="initObject.isADD"
      :rules="initObject.rules"
      :delete-prop="initObject.deleteProp"
      :modify-btn-show="initObject.modifyBtnShow"
      :set-other-val="initObject.setOtherVal"
      :language="initObject.language"
      :bottom-btn="initObject.bottomBtn"
      :auth-modify="initObject.authModify"
      :extend-fields="initObject.extendFields"
      @switchAdd="switchAdd"
      @handleEnter="handleEnter"
      @tabelEtcEvent="tabelEtcEvent"
    />
  </div>
</template>

<script>
import page from '@/components/Detail'

export default {
  components: { page },
  props: {
    initObject: {
      type: Object,
      default: () => {},
      required: true
    }
  },
  data() {
    return {
    }
  },
  methods: {
    switchAdd(b) {
      console.log('b', this.initObject)
      this.$emit('switchAdd', b)
    },
    handleEnter(data) {
      this.$emit('handleEnter', data)
    },
    tabelEtcEvent(data) {
      this.$emit('tabelEtcEvent', data)
    },
    refresh() {
      this.$refs.commonPage.initFormData()
    }
  }
}
</script>
