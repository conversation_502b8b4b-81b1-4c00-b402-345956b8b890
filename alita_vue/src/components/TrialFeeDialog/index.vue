<template>
  <div>
    <el-dialog :title="title" :visible.sync="openSet" width="800px" append-to-body>
      <el-table :data="calcFeeResultList" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" ></el-table-column>
        <el-table-column label="产品名称" prop="product_name" />
        <el-table-column label="总费用" prop="total_fee" />
        <el-table-column label="运费" prop="yf_fee" />
        <el-table-column label="燃油费" prop="ryf_fee" />
        <el-table-column label="偏远费" prop="pyf_fee" />
        <el-table-column label="私人住宅费" prop="zz_fee" />
        <el-table-column label="其他费用" prop="other_fee" />
        <el-table-column label="币种" prop="currency" />
      </el-table>
      <el-button @click="toggleTable">显示计费失败的产品</el-button>
      <el-table v-if="showTable" :data="failFeeResultList">
        <el-table-column label="产品名称" prop="product_name" />
        <el-table-column label="结果" prop="calc_result" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitSetOrder()">更改产品</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import { actionPost } from '@/api/data'

export default {
  name: 'TrialFeeDialog',
  props: {
    orders: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      title: '试算费用结果',
      openSet: false,
      form: {
        id: null,
        product_code: ''
      },
      rules: [],
      calcFeeResultList: [],
      failFeeResultList: [],
      selectedIndex: [],
      showTable: false
    }
  },
  watch: {
    orders(newValue) {
      console.log('newValue1-->', newValue)
    }
  },
  created() {
  },
  methods: {
    open() {
      this.bus.$emit('fullLoading', true)
      this.getTrialFeeList()
    },
    handleClose() {
      this.openSet = false
    },
    toggleTable() {
      this.showTable = !this.showTable
    },
    handleSelectionChange(selection) {
      console.log(selection)
      this.selectedIndex = selection.map(item => item.product_code)
      console.log(this.selectedIndex)
    },
    getTrialFeeList() {
      const api = 'api/parcelCustomerOrders/trial_fee/'
      const params = {}
      params['ids'] = this.orders.map(i => i.id)
      actionPost({ api, data: params }).then(res => {
        if (res.code === 200) {
          console.log('--->', res.data)
          this.calcFeeResultList = res.data.success_data
          this.failFeeResultList = res.data.fail_data
          this.openSet = true
        } else {
          this.$message.error(res.msg || res.detail || res.message)
          this.openSet = false
        }
        this.bus.$emit('fullLoading', false)
      }).catch(() => { this.bus.$emit('fullLoading', false) })
    },
    submitSetOrder() {
      console.log(this.form)
      const api = 'api/parcelCustomerOrders/update_product/'
      this.form['id'] = this.orders.map(i => i.id)[0]
      if (!this.selectedIndex.length) {
        this.$message.warning('请选择需要更改的产品')
        return
      }
      if (this.selectedIndex.length > 1) {
        this.$message.warning('只能选择一个产品')
        return
      }
      this.form['product_code'] = this.selectedIndex[0]
      actionPost({ api, data: this.form }).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg || '操作成功')
          this.$emit('toQuery') // 调用父函数，刷新页面
          this.openSet = false
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
      }).catch(() => { this.bus.$emit('fullLoading', false) })
    }
  }
}

</script>
