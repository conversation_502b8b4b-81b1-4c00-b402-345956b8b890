<template>
  <!--异步上传文件进度查询框-->
  <el-dialog
    :visible="visible || syncUploadDialogVisible"
    :before-close="handleDialogClose"
    :close-on-click-modal="false"
    :width="fullScreen? '70%':'1100px'"
    :height="syncUploadForm.searchable? '50px':'30px'"
    :title="dialogTitle">
    <div v-if="syncUploadForm.dialogType === 'table' && syncUploadForm.searchable" class="bottom clearfix" style="margin-bottom: 10px">
      <el-button :disabled="buttonDisabled" @click="getUploadProgress()">刷新</el-button>
      <div v-for="item in finalTableFilters" ref="inputContainer" :key="item.prop" :style="{ display: 'inline-block', float: item.type === 'input' ? 'right' : 'none' }" style="display: inline-block; ">
        <!-- 列表页头部下拉框 -->
        <template v-if="item.type === 'select'">
          <el-select v-model="item.value" :placeholder="$t('common.select')+' '+item.placeholder" :multiple="item.multiple" :class="{linghtUp:item.value}" style="width:132px;" clearable filterable @focus="getSelectData(item)" @change="dialogQuery(item.value)">
            <el-option
              v-for="(value, key) in item.filters"
              :key="key"
              :label="value"
              :value="key"/>
          </el-select>
        </template>
        <!-- 单选布尔值 -->
        <template v-if="item.type === 'boolean'">
          <el-select v-model="item.value" :class="{linghtUp:item.value===true||item.value===false}" :placeholder="item.placeholder" style="width:100px;" clearable filterable @change="dialogQuery(item.value)">
            <el-option :value="true" :label="$t('common.radioTrue')"/>
            <el-option :value="false" :label="$t('common.radioFalse')"/>
          </el-select>
        </template>
        <!-- 时间范围选择器 abandon -->
      </div>
      <!-- 输入框查询 -->
      <template>
        <div class="fr" style="display: inline-block">
          <el-input v-model="dialogSearch.value" :placeholder="syncUploadForm.dialogSearchHolder || $t('common.enter')" clearable style="width: 240px;" @keyup.enter.native="dialogQuery(dialogSearch.value)">
            <el-button slot="append" icon="el-icon-search" @click="dialogQuery(dialogSearch.value)"></el-button>
          </el-input>
        </div>
      </template>
    </div>
    <!-- 表格筛选 -->
    <el-table ref="dialogTable" :data="tableData" max-height="320px" size="small" border style="width: 100%;" @selection-change="selectionChange">
      <!--<el-table-column type="selection" fixed width="40"/>-->
      <el-table-column v-for="item in finalColumns" :key="item.prop" :label="item.label" :prop="item.prop" :width="item.width">
        <template slot-scope="scope">
          <div v-if="item.type === 'input'">
            <el-input v-model="scope.row[item.prop]" :placeholder="$t('common.enter')" type="number"/>
          </div>
          <div v-else-if="item.type === 'datetime'">
            {{ parseTime(scope.row[item.prop]) }}
          </div>
          <div v-else-if="item.type === 'time'">
            {{ removeMilliseconds(scope.row[item.prop]) }}
          </div>

          <!-- 新增：错误信息列 - 只在export类型时处理 -->
          <div v-else-if="item.prop === 'error_info' && dialogType === 'export'">
            <span v-if="scope.row.status === 'Failed'" style="color: #f56c6c; font-size: 12px;">
              {{ (scope.row.stack_trace || '未知错误').substring(0, 40) }}...
            </span>
            <span v-else style="color: #909399;">--</span>
          </div>

          <!--          &lt;!&ndash; 新增：操作列 - 只在export类型时显示下载按钮 &ndash;&gt;-->
          <!--          <div v-else-if="item.prop === 'actions' && dialogType === 'export'">-->
          <!--            <el-button-->
          <!--              v-if="scope.row.status === 'Completed' && scope.row.result_file"-->
          <!--              size="mini"-->
          <!--              type="primary"-->
          <!--              @click="handleDownloadFile(scope.row)">-->
          <!--              下载-->
          <!--            </el-button>-->
          <!--          </div>-->
          <!-- 新增：操作列 - 只在export类型时显示下载链接 -->
          <div v-else-if="item.prop === 'actions' && dialogType === 'export'">
            <a
              v-if="scope.row.status === 'Completed' && scope.row.url"
              :href="'//'+ scope.row.url"
              :download="scope.row.file_name || 'export_file.xlsx'"
              style="color: #409eff; text-decoration: none;" target="_blank">
              下载
            </a>
            <span v-else style="color: #c0c4cc;">--</span>
          </div>

          <!-- 原有逻辑保持不变，只是扩展状态支持 -->
          <div v-else>
            <span v-if="item.prop==='status'">
              <!-- 直接在template中判断，兼容新旧状态 -->
              <i
                v-if="scope.row[item.prop]==='Success' || scope.row[item.prop]==='Completed'"
                style="color:green; font-size: 16px"
                class="el-icon-success"/>
              <i
                v-else-if="scope.row[item.prop]==='Failure' || scope.row[item.prop]==='Failed' || scope.row[item.prop]==='VO' || scope.row[item.prop]==='Cancelled'"
                style="color:#fa044e; font-size: 16px"
                class="el-icon-error"/>
              <i v-else class="el-icon-upload" style="color: #409eff; font-size: 16px"></i>
            </span>
            {{ item.filters?item.filters[scope.row[item.prop]]:scope.row[item.prop] }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleDialogClose()">{{ $t('common.cancel') }}</el-button>
      <el-button type="primary" @click="handleDialogClose()" >{{ $t('common.sure') }}</el-button>
    </span>
    <!--异步上传文件进度查询框分页组件-->
    <el-pagination
      v-if="syncUploadForm.dialogType === 'table'"
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="dialogTotal"
      :page-size="dialogSize"
      :current-page="dialogPage"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChangeDialog"
      @current-change="pageChangeDialog"/>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import LangSelect from '@/components/LangSelect'
import { getChoiceData } from '@/api/data'
import { parseTime, removeMilliseconds } from '@/utils/index'

export default {
  name: 'UploadListDialog',
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull,
    LangSelect
  },
  dicts: ['criteria_type'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    pollingInterval: {
      type: Number,
      default: 5000
    },
    onCloseCallback: {
      type: Function,
      default: null
    },
    customColumns: {
      type: Array,
      default: null
    },
    customApiUrl: {
      type: String,
      default: ''
    },
    customTableFilters: {
      type: Array,
      default: null
    },
    dialogTitle: {
      type: String,
      default: '文件上传列表'
    },
    dialogType: {
      type: String,
      default: 'upload'
    }
  },
  data() {
    return {
      dark: false,
      syncUploadDialogVisible: false,
      dialogTotal: null,
      dialogPage: 1,
      dialogSize: 10,
      dialogOrdering: '-id',
      tableData: [],
      dialogUrl: null,
      dialogTableParams: {},
      dialogTableFilters: [],
      tableDataQuery: null,
      dialogSearch: { value: '' },
      dialogSearchHolder: '',
      actionParams: {
        methods: '',
        data: {
          date: '',
          amount: '',
          selectData: [],
          selectActionVal: '',
          label: '',
          purpose: '',
          is_part_set: false
        }
      },
      lastClickTime: 0,
      isCooldown: false,
      fullScreen: false,
      progressTimer: null // 新增：轮询定时器
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device'
    ]),
    // 异步上传文件进度弹窗
    syncUploadForm() {
      return {
        dialogType: 'table',
        // 功能待补充
        searchable: true,
        dialogSearchHolder: '文件名/订单号',
        columns: [
          { label: '上传时间', prop: 'create_date', type: 'datetime', width: '140px' },
          { label: '文件名', prop: 'file_name', width: '260px' },
          { label: '业务单据号', prop: 'order_num' },
          // { label: 'md5', prop: 'file_md5' },
          { label: '单据类型', prop: 'order_type', type: 'select',
            filters: { 'TR': '运输', 'CL': '清关', 'PC': '小包', 'OW': '海外仓', 'BP': '大包'
            }, width: '75px'
          },
          { label: '任务类型', prop: 'task_type', type: 'select',
            filters: { 'UploadOrderExcel': '导入订单', 'UploadParcelVoucher': '上传包裹面单',
              'BatchWeighting': '批量称重', 'BatchPick': '批量装箱'
            }, width: '75px'
          },
          { label: '处理用时', prop: 'execution_time', type: 'time', width: '80px' },
          { label: '任务描述', prop: 'task_desc', width: '250px' },
          {
            label: '上传状态', prop: 'status', type: 'select',
            filters: {
              'Waiting': '等待中', 'Processed': '处理中', 'Success': '处理成功', 'Failure': '处理失败',
              'VO': '已作废'
            }, width: '100px'
          }
        ],
        tableFilters: [
          {
            prop: 'status',
            type: 'select',
            placeholder: '上传状态',
            filters: {
              'Waiting': '等待中', 'Processed': '处理中', 'Success': '处理成功', 'Failure': '处理失败',
              'VO': '已作废'
            }
          }
        ]
      }
    },
    buttonDisabled() {
      // 计算是否禁用按钮
      return this.isCooldown || (Date.now() - this.lastClickTime < 3000)
    },
    finalColumns() {
      return this.customColumns || this.syncUploadForm.columns
    },
    finalTableFilters() {
      return this.customTableFilters || this.syncUploadForm.tableFilters
    },
    finalApiUrl() {
      return this.customApiUrl || 'api/uploadTasksProgress/upload_tasks_progress/'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && !this.syncUploadDialogVisible) {
      // 根据类型选择接口，保证上传功能不受影响
        this.dialogUrl = this.dialogType === 'export'
          ? this.finalApiUrl
          : 'api/uploadTasksProgress/upload_tasks_progress/'

        // 开始轮询
        this.startProgressPolling()
      }
    }
  },
  async created() {
  },
  beforeDestroy() {
    // 组件销毁时清理定时器
    this.stopProgressPolling()
  },
  methods: {
    parseTime,
    removeMilliseconds,
    submitForm() {
      this.subOpen = false
    },
    cancel() {
      this.subOpen = false
    },
    init_option() {
      // for (let i = 0; i < this.applicabilityList.length; i++) {
      //   var element = this.applicabilityList[i]
      //   if (parseInt(element.id) === this.rowId) {
      //     console.log(element)
      //     console.log(element.id)
      //     this.$refs.truckOption.setCurrentRow(element)
      //   }
      // }
    },
    // async get_applicabilityList() {
    //   const api = '/api/truckInquiryPriceOrders/?size=100000&page=1&sort=id'
    //
    //   await getChoiceData(api).then(res => {
    //     console.log(res)
    //     if (res.code === 200) {
    //       console.log(res.data)
    //       this.applicabilityList = res.data
    //       console.log('this.formData0-->', this.formData)
    //     }
    //   })
    // },
    handleCurrentChange(val) {
      this.prefix_currentRow = this.currentRow
      this.curr_name = val.inquiry_num
      if (this.currentRow || !this.rowId) {
        this.currentRow = val
        const data = { 'rowId': val.id }
        this.$emit('send', data)
        this.subOpen = false
        return
      }
      this.currentRow = val
    },
    subOpenFile() {
      this.subOpen = true
      this.$nextTick(() => {
        if (!this.currentRow && this.rowId !== 0) {
          this.init_option()
        }
      })
    },
    getUploadProgress() {
      if (!this.buttonDisabled) {
        this.lastClickTime = Date.now()
        this.isCooldown = true
        setTimeout(() => {
          this.isCooldown = false
        }, 3000)
      }

      // 根据类型选择接口，保证上传功能不受影响
      this.dialogUrl = this.dialogType === 'export'
        ? this.finalApiUrl
        : 'api/uploadTasksProgress/upload_tasks_progress/'

      getChoiceData(this.dialogUrl, {}).then(res => {
        if (res.code === 200) {
          console.log('查询进度:\n', res.data)
          this.tableData = res.data
          this.dialogTotal = res.count
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
      }).catch((err) => {
        this.$message.error(err)
      })
      this.syncUploadDialogVisible = true
    },
    // 开始轮询
    startProgressPolling() {
      // 立即执行一次查询
      this.dialogQuery()

      // 设置定时器，每5秒查询一次
      this.progressTimer = setInterval(() => {
        // 检查对话框是否还显示
        if (this.visible || this.syncUploadDialogVisible) {
          this.dialogQuery()
        } else {
          // 如果对话框关闭，停止轮询
          this.stopProgressPolling()
        }
      }, this.pollingInterval || 5000)
    },
    // 停止轮询
    stopProgressPolling() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
    },
    handleDialogClose() {
      if (this.visible) {
        this.$emit('update:visible', false)
        // 停止轮询
        this.stopProgressPolling()
        if (this.onCloseCallback) {
          this.onCloseCallback()
        }
      } else {
        this.syncUploadDialogClose()
      }
    },
    syncUploadDialogClose() {
      this.syncUploadDialogVisible = false
      // 停止轮询
      this.stopProgressPolling()
      this.init()
    },
    sizeChangeDialog(e) {
      // this.page = 1
      // this.size = e
      // this.init()
      this.dialogPage = 1
      this.dialogSize = e
      this.dialogQuery(false)
    },
    pageChangeDialog(e) {
      // this.page = e
      // this.init()
      this.dialogPage = e
      // this.sentRequest(api, this.dialogTableParams)
      this.dialogQuery(false)
    },
    sentRequest(api, params = {}) {
      // 发送请求
      getChoiceData(api, params).then(res => {
        if (res.code === 200) {
          console.log('成功获取数据')
        } else if (res.code === 404) {
          console.log('没有搜索到数据')
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
        this.tableData = res.data
        this.dialogTotal = res.count
        this.bus.$emit('fullLoading', false)
      }).catch((error) => {
        this.$message.error(error)
        this.bus.$emit('fullLoading', false)
      })
      // this.resetUrl()
    },
    // 初始化获取下拉数据等
    initChoiceData(item) {
      console.log('initChoiceData2-->')
      const params = {
        size: 100000,
        page: 1,
        sort: 'id'
      }
      // 点击加载
      if (item) {
        if (!item.data) {
          if (item.query) {
            Object.keys(item.query).forEach(i => {
              params[i] = item.query[i]
            })
          }
          getChoiceData(item.api, params).then(res => {
            this.$set(item, 'data', res.data || res.results)
          })
        }
        return
      }
      // 进入页面就加载
      if (!this.firstEntry) return
      this.dialogTableFilters && this.dialogTableFilters.forEach(i => {
        if (i.api) {
          getChoiceData(i.api, params).then(res => {
            this.$set(i, 'data', res.data || res.results)
          })
        }
      })
      this.firstEntry = false
    },
    // 点击下拉框
    getSelectData(item) {
      console.log('选择2-->', this.actionParams.data.selectData)
      // this.$parent.initChoiceData(item)
      this.initChoiceData(item)
    },
    dialogQuery(search = true) {
      console.log('dialogQuery-->')
      if (search) {
        this.dialogPage = 1
      }

      const t_query = Object.assign({}, this.tableDataQuery)
      Object.keys(t_query).forEach(i => {
        if (typeof (t_query[i]) === 'string' && t_query[i].startsWith('$')) {
          t_query[i] = this.$parent.formData.checkData[0][this.tableDataQuery[i].slice(1)]
        } else if (typeof (t_query[i]) === 'string' && t_query[i].endsWith('$')) {
          t_query[i] = this.actionQuery.checkData[0].id
        }
      })

      this.dialogTableParams = {
        page: this.dialogPage,
        size: this.dialogSize,
        ordering: this.dialogOrdering,
        ...t_query
      }

      this.dialogTableParams['search'] = this.dialogSearch.value
      console.log('this.dialogTableParams-->', this.dialogTableParams)

      // 这里是修改的重点：根据类型选择筛选器
      const filtersToUse = this.dialogType === 'export'
        ? this.finalTableFilters
        : this.syncUploadForm.tableFilters

      filtersToUse && filtersToUse.forEach(i => {
        console.log('i.prop-->', i.prop)
        if (typeof i.value === 'boolean' || i.value) {
          this.dialogTableParams[i.prop] = (i.value).toString()
        }
      })

      this.sentRequest(this.dialogUrl, this.dialogTableParams)
    }
    // // 处理文件下载
    // handleDownloadFile(row) {
    //   // 方案1：直接使用后端返回的文件URL
    //   if (row.url) {
    //     const link = document.createElement('a')
    //     link.href = row.url
    //     link.download = row.file_name || 'export_file.xlsx'
    //     document.body.appendChild(link)
    //     link.click()
    //     document.body.removeChild(link)
    //     this.$message.success('文件下载已开始')
    //   }
    // }
  }
}
</script>

<style scoped lang="scss">

</style>
