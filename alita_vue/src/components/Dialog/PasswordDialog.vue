<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :before-close="handleDialogClose"
    :close-on-click-modal="false"
    :title="title"
    width="500px">
    <div style="text-align: center;">
      <p style="margin-bottom: 20px; font-size: 16px;">{{ message }}</p>
      <div style="background-color: #f5f7fa; padding: 15px; border-radius: 4px; border: 1px solid #dcdfe6; margin-bottom: 20px;">
        <span style="font-size: 18px; font-weight: bold; color: #409EFF; letter-spacing: 2px;">{{ password }}</span>
      </div>
      <p style="color: #909399; font-size: 14px; margin-bottom: 20px;">
        <i class="el-icon-warning" style="color: #e6a23c;"></i>
        请妥善保管此密码，关闭弹窗时密码将自动复制到剪贴板
      </p>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" icon="el-icon-document-copy" @click="copyPassword">复制密码</el-button>
      <el-button @click="closeDialog">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'PasswordDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    password: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '密码显示'
    },
    message: {
      type: String,
      default: '生成的随机密码为：'
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
    },
    dialogVisible(newVal) {
      this.$emit('update:visible', newVal)
    }
  },
  methods: {
    // 复制密码到剪贴板
    copyPassword() {
      this.copyText(this.password)
      this.$message.success('密码已复制到剪贴板')
    },
    // 弹窗关闭时的处理
    handleDialogClose(done) {
      // 自动复制密码
      this.copyPassword()
      done()
    },
    // 手动关闭弹窗
    closeDialog() {
      this.copyPassword()
      this.dialogVisible = false
    },
    // 复制文本到剪贴板的方法
    copyText(text) {
      if (navigator.clipboard && window.isSecureContext) {
        // 现代浏览器使用 Clipboard API
        navigator.clipboard.writeText(text).catch(err => {
          console.error('复制失败:', err)
          this.fallbackCopyText(text)
        })
      } else {
        // 降级方案
        this.fallbackCopyText(text)
      }
    },
    // 降级复制方案
    fallbackCopyText(text) {
      const textarea = document.createElement('textarea')
      textarea.value = text
      textarea.style.position = 'fixed'
      textarea.style.opacity = '0'
      document.body.appendChild(textarea)
      textarea.select()
      try {
        document.execCommand('copy')
      } catch (err) {
        console.error('复制失败:', err)
      }
      document.body.removeChild(textarea)
    }
  }
}
</script>

<style scoped>
</style>
