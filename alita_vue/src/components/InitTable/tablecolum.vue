<template>
  <div>
    <subcolum v-if="item.type!=='multiRow'" :item="item" :row="row"/>
    <div v-else>
      <div v-for="i in item.data" :key="i.prop">
        <subcolum v-if="i.blankHide&&row[i.prop] || !i.blankHide" :type="item.type" :item="i" :row="row"/>
      </div>
    </div>
  </div>
</template>

<script>
import subcolum from '@/components/InitTable/subcolum'

export default {
  components: {
    subcolum
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    row: {
      type: Object,
      required: true
    }
  }
}
</script>
