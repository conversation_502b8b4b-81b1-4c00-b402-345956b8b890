<!--列表页编辑数据弹框组件-->
<template>
  <el-dialog :append-to-body="true" :visible.sync="dialog" :title="`${isAdd? $t('common.create'):$t('common.edit')}${formData.title}`" :width="dialogWidth?dialogWidth:dataLen<8?'500px':'800px'" :close-on-click-modal="false">
    <el-row>
      <el-form ref="form" :model="form" :rules="rules" :key="formKey" :label-width="labelWidth?labelWidth:'110px'" size="small">
        <el-col v-for="(item,index) in filterFormData" :key="item.prop" :sm="dataLen<8 || item.type==='textarea'|| item.type==='file'?24:(index+1)%2===1?11:{span: 11, offset: 2}" :xs="24">
          <el-form-item v-if="!item.hidden" :label="item.alias || item.label" :prop="item.prop">
            <template v-if="item.type === 'date' || item.type === 'datetime'">
              <el-date-picker
                v-model="form[item.prop]"
                :type="item.type"
                :disabled="item.disabled || (item.editdisabled&&form.id)"
                :value-format="item.type === 'date'?'yyyy-MM-dd':'yyyy-MM-ddTHH:mm:ss'"
                :placeholder="item.type === 'date'?$t('common.selectDate'):$t('common.selectDateTime')"
                style="width:100%;"/>
            </template>
            <template v-else-if="item.type === 'monthrange'">
              <el-date-picker
                v-model="form[item.prop]"
                :range-separator="$t('common.to')"
                :start-placeholder="$t('common.startMonth')"
                :end-placeholder="$t('common.endMonth')"
                value-format="yyyy-MM-dd"
                style="width:100%;"
                type="monthrange"/>
            </template>
            <el-select v-else-if="item.type==='select'&&item.filter&&item.prop!=='effective_month'" v-model="form[item.prop]" :no-data-text="$t('content.NoData')" :disabled="item.disabled" :multiple="item.multiple" :placeholder="$t('common.select')" style="width:100%;" clearable filterable @change="selectChange($event, item)">
              <el-option
                v-for="item in Object.keys(item.filter).map(i=>{return {label:item.filter[i],id:i}})"
                :key="item.id"
                :label="item.label"
                :value="item.id"/>
            </el-select>
            <el-select v-else-if="item.type==='select'&&item.filter&&item.prop==='effective_month'" v-model="form[item.prop]" :disabled="item.disabled" :multiple="item.multiple" :placeholder="$t('common.select')" style="width:100%;" clearable filterable>
              <el-option
                v-for="item in Object.keys(item.filter).map(i=>{return {label:item.filter[i],id:item.filter[i]}})"
                :key="item.id"
                :label="item.label"
                :value="item.label"/>
            </el-select>
            <div v-else-if="item.type==='remoteSelect'">
              <el-select
                v-model="form[item.prop]"
                :placeholder="item.config.placeholder || '请输入关键词前缀'"
                :filterable="item.config.filterable || true"
                :clearable="item.config.clearable || true"
                :remote="item.config.remote || true"
                :remote-method="query => defaultRemoteMethod(query, item.config)"
                :reserve-keyword="item.config.reserveKeyword || true"
                :loading="item.config.loading || false"
                style="width:100%">
                <el-option
                  v-for="option in remoteSelectOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value">
                </el-option>
              </el-select>
            </div>
            <!-- 币种 -->
            <template v-else-if="item.type === 'currency'">
              <el-select :disabled="item.disabled" v-model="form[item.prop]" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')" style="width:100%;" clearable filterable>
                <el-option value="CNY" label="CNY"/>
                <el-option value="USD" label="USD"/>
                <el-option value="GBP" label="GBP"/>
                <el-option value="EUR" label="EUR"/>
                <el-option value="HKD" label="HKD"/>
                <el-option value="CAD" label="CAD"/>
                <el-option value="CHF" label="CHF"/>
                <el-option value="AUD" label="AUD"/>
                <el-option value="MXN" label="MXN"/>
              </el-select>
            </template>
            <template v-else-if="item.type === 'select'&&item.value_type==='code'">
              <el-select :disabled="item.disabled || (item.editdisabled&&form.id)" v-model="form[item.prop]" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')" style="width:100%;" filterable clearable>
                <el-option
                  v-for="op_item in item.data"
                  :key="op_item.value || op_item.code"
                  :label="op_item[item.option] || op_item.label || op_item.name"
                  :value="op_item.value || op_item.code"/>
              </el-select>
            </template>
            <template v-else-if="item.type === 'select'">
              <el-select :disabled="item.disabled || (item.editdisabled&&form.id)" v-model="form[item.prop]" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')" style="width:100%;" filterable clearable @change="selectChange($event, item)">
                <el-option
                  v-for="op_item in item.data"
                  :key="op_item.value || op_item.id"
                  :label="item.show === 'nameAndCode' ? `${op_item.name}-${op_item.code}` : op_item[item.option] || op_item.label || op_item.code || op_item.name"
                  :value="item.key?op_item[item.key]: op_item.value || op_item.id"/>
              </el-select>
            </template>
            <template v-else-if="item.type==='boolean'">
              <div style="height:33px;">
                <el-radio :disabled="item.disabled || (item.editdisabled&&form.id)" v-model="form[item.prop]" :label="true">{{ $t('common.radioTrue') }}</el-radio>
                <el-radio :disabled="item.disabled || (item.editdisabled&&form.id)" v-model="form[item.prop]" :label="false">{{ $t('common.radioFalse') }}</el-radio>
              </div>
            </template>
            <template v-else-if="item.type === 'file'">
              <el-upload
                ref="upload"
                :on-remove="handleRemove"
                :before-remove="(file, fileList) => beforeRemove(file, fileList, item)"
                :before-upload="beforeUpload"
                :on-change="fileChange"
                :auto-upload="true"
                :limit="5"
                :http-request="(data) => uploadAttchment(data, item)"
                :file-list="fileList"
                class="upload-demo"
                action="https://jsonplaceholder.typicode.com/posts/"
              >
                <el-button size="small" type="success">{{ $t('common.clickToUpload') }}</el-button>
              </el-upload>
            </template>
            <template v-else>
              <el-input :disabled="item.disabled || (item.editdisabled&&form.id)" :type="item.type || 'input'" v-model="form[item.prop]" :placeholder="item.placeholderStr || $t('common.enter')"/>
            </template>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="cancel">{{ $t('common.cancel') }}</el-button>
      <el-button :loading="loading" type="primary" @click="doSubmit">{{ $t('common.sure') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { add, edit, del, actionPost, getChoiceData } from '@/api/data'

export default {
  props: {
    isAdd: {
      type: Boolean,
      required: true
    },
    sup_this: {
      type: Object,
      default: null
    },
    formData: {
      type: Object,
      required: true
    },
    dialogWidth: {
      type: String,
      default: null
    },
    labelWidth: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      dialog: false,
      form: {},
      rules: {},
      fileList: [],
      waitToUpload: new FormData(),
      fileUrl: '',
      filterFormData: [],
      formKey: 0,
      remoteSelectOptions: []
    }
  },
  computed: {
    dataLen() {
      return this.filterFormData.length
    }
  },
  watch: {
    '$route'(n, o) {
      if (this.dialog) this.cancel()
    },
    // 获取下拉数据
    dialog(n, o) {
      if (n) {
        this.initChoiceData()
      }
    }
  },
  mounted() {
    const tem = this.formData.data.reduce((cur, next) => {
      return cur.concat(next.type === 'multiRow' ? next.data : next)
    }, [])
    this.filterFormData = tem.filter(i => {
      if (i.type === 'file') {
        this.$set(this.form, i.prop, [])
      } else if (i.type === 'boolean') {
        this.$set(this.form, i.prop, i.default)
      } else if (i.type === 'select' && i.defaultVal) {
        console.log('设置了没啊-->', i.defaultVal)
        this.$set(this.form, i.prop, i.defaultVal)
      } else {
        this.$set(this.form, i.prop, '')
      }
      if (i.required) {
        this.rules[i.prop] = [
          { required: true, message: this.$t('common.enter'), trigger: i.type === undefined || i.type === 'input' || i.type === 'textarea' ? 'blur' : 'change' }
        ]
      }
      return !i.hidden
    })
  },
  methods: {
    // 初始化获取下拉数据等
    initChoiceData() {
      console.log('进入initChoiceData0-->', this.form)
      const params = {
        size: 100000,
        page: 1,
        sort: 'id'
      }
      this.formData.data.forEach((i, index) => {
        if (i.type === 'boolean' && i.default !== undefined) {
          console.log('这到底是个啥-->', i.prop, this.form[i.prop])
          if (this.form[i.prop] !== 0 && this.form[i.prop] !== false) {
            if (typeof i.default === 'object' && i.default !== null) {
              if (i.default[true] === 'other') {
                this.$set(this.form, i.prop, true)
                i.default[false].forEach(system => {
                  if (system === process.env.SYS_FLAG) {
                    this.$set(this.form, i.prop, false)
                  }
                })
              } else {
                this.$set(this.form, i.prop, false)
                i.default[false].forEach(item => {
                  if (item === process.env.SYS_FLAG) {
                    this.$set(this.form, i.prop, true)
                  }
                })
              }
            } else {
              this.$set(this.form, i.prop, i.default)
            }
          }
        } else if (i.type === 'select' && i.defaultVal !== undefined) {
          if (this.form[i.prop] !== 0 && !this.form[i.prop]) {
            this.$set(this.form, i.prop, i.defaultVal)
          }
        }

        if (i.type === 'select' && i.options) {
          let query_params = {}
          if (i.query) {
            query_params = { ...params, ...i.query }
          } else if (i.parent_query) {
            query_params = { ...params }
            query_params[i.parent_query] = this.form[i.parent_query]
          } else {
            query_params = { ...params }
          }
          getChoiceData(i.options, query_params).then(res => {
            this.$set(this.formData.data[index], 'data', res.results || res.data)
          })
        }
      })
    },
    selectChange(val, item) {
      // sub_prop 判断是否需要联动显示的属性
      console.log('selectChange form-->', val, item)
      if (item.prop === 'is_general') {
        if (val === '1') {
          this.dynamicChangeAttr(false, true)
        } else if (val === '0') {
          this.dynamicChangeAttr(true, false)
        }
        return
      }
      if (item.sub_prop) {
        const subItem = this.formData.data.find(i => i.prop === item.sub_prop)
        this.form[subItem.prop] = ''
        const query_params = {
          size: 100000,
          page: 1,
          sort: 'id'
        }
        // sub_query 联动查询条件
        query_params[item.sub_query] = val
        getChoiceData(subItem.options, query_params).then(res => {
          console.log(res.data.length)
          if (res.data.length === 0) {
            this.$message.error(item.label + '还未有关联' + subItem.label + '的数据，请先关联' + subItem.label + '数据')
          }
          this.$set(subItem, 'data', res.data)
        })
      }
      // 联动修改
      if (item.change && item.change.length > 0) {
        const options = Object.keys(item.filter).map(i => {
          return { label: item.filter[i], id: i }
        })
        options.find(i => {
          console.log('联动修改find-->', i, val)
          if (i.id === this.form[val]) {
            this.form[val + '_name'] = i[this.form[val]['label']]
          }
        })
        item.change.forEach(i => {
          // 设置值
          if (i.type === 'getValue') {
            this.form[i.prop] = ''
            if (!this.form[val]) return
            this.form[i.prop] = (this.form[val].data.find(item => this.form[val] === item[this.form[val].value]))[i.label]
          } else if (i.type === 'getRelatedValue') {
            this.form[i.prop] = ''
            if (!this.form[val]) return
            this.form[i.prop] = (this.form[val].data.find(item => this.form[val] === item[this.form[val].value]))[i.label]
          } else if (i.type === 'hidden') {
            // 隐藏某些字段
            console.log('是否隐藏-->', i.showVal, this.form[item.prop])
            if (i.showVal === this.form[item.prop]) {
              this.hiddenItem(i, false)
            } else if (i.hiddenVals.includes(this.form[item.prop])) {
              this.hiddenItem(i, false)
            } else {
              this.hiddenItem(i, true)
            }
          }
        })
      }
    },
    cancel() {
      this.resetForm()
    },
    doSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.isAdd) {
            this.doAdd()
          } else this.doEdit()
        } else {
          return false
        }
      })
    },
    // 获取参数
    getFormData() {
      const formData = new FormData()
      // const formData = {}
      this.formData.data.forEach((i, index) => {
        if (i.type === 'multiRow') {
          i.data.forEach(k => {
            this.setFromData(k, formData)
          })
        } else {
          this.setFromData(i, formData)
        }
      })
      return formData
    },
    // 设置参数
    setFromData(item, formData) {
      console.log('进入setFromData', item)
      if (item.type === 'file') {
        // this.fileList.forEach((k, K_index) => {
        //   formData.append(`${i.prop}`, k.raw)
        // })
      } else {
        console.log('加了个啥?-->', item.prop, this.form[item.prop])
        if (item.alias === '会计区间') {
          formData.append('start_month', this.form[item.prop][0])
          // formData['start_month'] = this.form[item.prop][0]
          const t = this.form[item.prop][1].split('-')
          formData.append('end_month', new Date(t[0], t[1]).toJSON().substring(0, 10))
          // formData['end_month'] = new Date(t[0], t[1]).toJSON().substring(0, 10)
        } else {
          // 避免出现字符串的"null"或字符串的"undefined"
          if (this.form[item.prop] !== null && this.form[item.prop] !== undefined && !['effective_month', 'is_general'].includes(item.prop)) {
            formData.append(item.prop, this.form[item.prop])
          }
          // 转换bool
          if (item.prop === 'is_general' && item.type === 'select') {
            if (this.form[item.prop] === '1' || this.form[item.prop] === '0') {
              formData.append(item.prop, this.form[item.prop])
            } else {
              formData.append(item.prop, this.form[item.prop] === '是' ? 1 : 0)
            }
          }
          // 转换月份数组
          if (item.prop === 'effective_month' && item.multiple === true) {
            const monthData = this.form[item.prop].filter(i => i !== undefined)
            if (monthData.length === 0) {
              // 数组为空，直接赋空值
              formData.append(item.prop, monthData.join(','))
            } else {
              // 是否都是数字或者可以直接转为数字
              const allNumbers = monthData.every(item => !isNaN(item))
              if (allNumbers === false) {
                const convertedMonthData = monthData.map(month => {
                  for (const key in item.filter) {
                    if (item.filter[key] === month) {
                      return key
                    }
                  }
                }).sort((a, b) => a - b)
                // 如果是['一月', '二月'] 转为 ['1', '2']
                formData.append(item.prop, convertedMonthData.join(','))
              } else {
                // 都是数字，直接拼接赋值
                formData.append(item.prop, monthData.sort((a, b) => a - b).join(','))
              }
            }
          }

          // console.log('this.form-->', this.form)
          // formData[item.prop] = this.form[item.prop]
        }
      }
    },
    doAdd() {
      const p = {
        api: this.formData.api,
        data: this.getFormData()
      }
      add(p).then(res => {
        if (res.code === 200 || res.code === 201) {
          this.resetForm()
          // this.$notify({
          //   title: '添加成功',
          //   type: 'success',
          //   duration: 2500
          // })
          this.$message.success(res.msg || res.detail || res.message)
          this.loading = false
          // 判断是否有文件
          this.newAttachments(res.data.id)
          setTimeout(() => {
            this.$parent.$parent.init()
          }, 500)
        } else {
          // let str = ''
          // Object.keys(res).forEach(i => {
          //   if (Array.isArray(res[i])) {
          //     str += i + res[i][0]
          //   }
          // })
          // this.$notify({
          //   title: '添加失败:' + (str || res.detail || res.msg),
          //   type: 'error',
          //   duration: 2500
          // })
          this.$message.error(res.msg || res.detail || res.message)
          this.loading = false
        }
      }).catch(err => {
        this.loading = false
        console.log(err.response.data.message)
      })
    },
    doEdit() {
      const p = {
        api: this.formData.api,
        id: this.form.id,
        data: this.getFormData()
      }
      edit(p).then(res => {
        if (res.code === 200 || res.code === 201) {
          this.resetForm()
          // this.$notify({
          //   title: this.$t('common.modifysuccess'),
          //   type: 'success',
          //   duration: 2500
          // })
          this.$message.success(res.msg || res.detail || res.message)
          this.sup_this.init()
          this.loading = false
        } else {
          // let str = ''
          // Object.keys(res).forEach(i => {
          //   if (Array.isArray(res[i])) {
          //     str += i + res[i][0]
          //   }
          // })
          // this.$notify({
          //   title: '修改失败:' + (str || res.detail || ''),
          //   type: 'error',
          //   duration: 2500
          // })
          this.$message.error(res.msg || res.detail || res.message)
          this.loading = false
        }
      }).catch(err => {
        this.loading = false
        console.log(err.response.data.message)
      })
    },
    resetForm() {
      this.fileList = []
      this.dialog = false
      this.$refs['form'].resetFields()
      Object.keys(this.form).forEach(i => {
        console.log(i, '---->>', this.form[i])
        // if (this.formData && this.formData.data){
        //   let subData = this.formData.data.find(item => item.prop === i)
        //   if (subData.default){
        //     console.log('---->>>', i, subData.default)
        //     this.form[i] = subData.default
        //   }else{
        //     this.form[i] = ''
        //   }
        // }else{
        //   this.form[i] = ''
        // }
      })

      delete this.form.id
    },
    // 文件变更
    fileChange(file, fileList) {
    },
    // 文件上传之前
    beforeUpload(file) {

    },
    // 文件移除之前
    beforeRemove(file, fileList, item) {
      this.fileUrl = item.url
      return new Promise((resolve, reject) => {
        this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (!file.id) {
            resolve(true)
            return true
          }
          del({ api: this.fileUrl, id: file.id, data: {}}).then(res => {
            resolve(true)
            return true
          }).catch(() => {
            resolve(false)
            return false
          })
        }).catch(() => {
          reject(false)
          return false
        })
      })
    },
    // 文件移除
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    // 自定义上传
    uploadAttchment(data, item) {
      this.fileUrl = item.url
      if (!this.form.id) {
        // 新增上传
        this.waitToUpload.append('files', data.file)
        return
      }
      const formData = new FormData()
      formData.append('name', data.file.name)
      formData.append('url', data.file)
      const fk = item.fk
      this.form.id ? formData.append(fk, this.form.id) : ''
      const p = {
        api: this.fileUrl,
        data: formData
      }
      add(p).then(res => {
        if (res.code === 200 || res.code === 201) {
          this.$message.success('成功上传')
          this.fileList.push({ name: res.data.name, url: res.data.url, id: res.data.id })
        } else {
          this.$message.error('上传失败')
        }
      }).catch(() => {})
    },
    // 新增的时候添加附件
    newAttachments(id) {
      if (!this.waitToUpload.get('files')) return
      if (id) this.waitToUpload.append('id', id)
      actionPost({ api: `api/${this.fileUrl}/batch_upload/`, data: this.waitToUpload }).then(res => {
        console.log(res)
      })
    },

    dynamicChangeAttr(requireVal, hiddenVal) {
      for (let i = 0; i < this.filterFormData.length; i++) {
        if (this.filterFormData[i].prop === 'customer') {
          this.rules['customer'][0].required = requireVal
          this.filterFormData[i].hidden = hiddenVal
          this.formKey += 1
          break
        }
      }
    },
    hiddenItem(changeItem, isShow) {
      this.filterFormData.forEach(i => {
        console.log('是否显示-->', changeItem.hiddenProp.includes(i.prop), !isShow)
        if (changeItem.hiddenProp.includes(i.prop)) {
          this.$set(i, 'hidden', !isShow)
          this.$set(this.filterFormData, i.prop, i.type === 'table' ? [] : null)
        }
      })
    },
    defaultRemoteMethod(query, config) {
      if (query !== '') {
        config.loading = true // 显示loading
        this.fetchOptions(query, config).then((options) => {
          this.remoteSelectOptions = options // 更新远程搜索选项
        }).finally(() => {
          config.loading = false // 隐藏loading
        })
      } else {
        this.remoteSelectOptions = [] // 清空远程搜索选项
      }
    },
    // dialogRemoteSelectMethod(query, config, prop) {
    //   if (query !== '') {
    //     config.loading = true // 显示loading
    //     this.fetchOriginOptions(query, config).then((options) => {
    //       this.$set(this.dialogOptionData, prop,
    //         {
    //           data: options,
    //           label: config.label || 'name',
    //           value: config.value || 'id'
    //         }
    //       )
    //     }).finally(() => {
    //       config.loading = false // 隐藏loading
    //     })
    //   } else {
    //     this.$set(this.dialogOptionData, prop,
    //       {
    //         data: [],
    //         label: config.label || 'name',
    //         value: config.value || 'id'
    //       }
    //     )
    //   }
    // },
    fetchOptions(query, config) {
      return getChoiceData(config.remoteUrl, { q: query })
        .then(response => {
          // 假设接口返回的数据格式为[{ value: '1', label: '选项1' }, { value: '2', label: '选项2' }]
          return response.data.map(item => ({
            value: config.valueKey ? item[config.valueKey] : item.value,
            label: item[config.label]
          }))
        })
    },
    fetchOriginOptions(query, config) {
      return getChoiceData(config.remoteUrl, { q: query })
        .then(response => {
          // 假设接口返回的数据格式为[{ value: '1', label: '选项1' }, { value: '2', label: '选项2' }]
          return response.data
        })
    }
  }
}
</script>

<style scoped>

</style>
