<template>
  <div v-if="dataValue.length">
    <hot-table
      :data="dataValue"
      :license-key="licenseKey"
      :col-widths="100"
      :context-menu="contextMenu"
      :start-rows="2"
      :row-headers="rowHeaders"
      :manual-column-resize="true"
      :manual-row-resize="true"
      :col-headers="colHeaders"
      :columns="columns"
    />
  </div>
</template>

<script>
import { HotTable } from '@handsontable/vue'

export default {
  name: '',
  components: { HotTable },
  props: {
    tabledata: {
      type: Array,
      default: () => { return [] }
    },
    dataValue: {
      type: Array,
      default: () => { return [] }
    },
    parceType: {
      type: Number,
      default: 1
    },
    tableType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      licenseKey: '908f0-7ca55-1d54b-01e28-f1176',
      columns: [],
      // todo_i: fba订单下完整包裹表格编辑字段
      single_columns: [
        { data: 'parcel_num' },
        { data: 'reference_id' },
        { data: 'shipment_id' },
        { data: 'parcel_weight' },
        { data: 'parcel_length' },
        { data: 'parcel_width' },
        { data: 'parcel_height' },
        { data: 'remark' },
        { data: 'actual_weight' },
        { data: 'actual_length' },
        { data: 'actual_width' },
        { data: 'actual_height' },
        { data: 'label_weight' },
        { data: 'label_length' },
        { data: 'label_width' },
        { data: 'label_height' },
        { data: 'item_code' },
        { data: 'declared_nameCN' },
        { data: 'declared_nameEN' },
        { data: 'declared_price' },
        { data: 'item_qty' },
        { data: 'item_weight' },
        { data: 'texture' },
        { data: 'use' },
        { data: 'brand' },
        { data: 'model' },
        { data: 'customs_code' },
        { data: 'tax_rate' },
        { data: 'fba_no' },
        { data: 'fba_track_code' },
        { data: 'item_picture' }
      ],
      // fbm包裹编辑字段
      fbm_columns: [
        { data: 'parcel_num' },
        { data: 'shipment_id' },
        { data: 'reference_id' },
        { data: 'shop_type' },
        { data: 'declared_nameCN' },
        { data: 'declared_nameEN' },
        { data: 'brand' },
        { data: 'model' },
        { data: 'texture' },
        { data: 'use' },
        { data: 'box_qty' },
        { data: 'combined_parcel_num' },
        { data: 'declared_price' },
        { data: 'customs_code' },
        { data: 'custom_clearance' },
        { data: 'is_electric' },
        { data: 'is_magnetic' },
        { data: 'item_qty' },
        // { data: 'parcel_qty' },
        { data: 'parcel_length' },
        { data: 'parcel_width' },
        { data: 'parcel_height' },
        { data: 'parcel_weight' }
      ],
      // 订单下简易包裹表格编辑字段
      all_columns: [
        // { data: 'parcel_num' },
        { data: 'parcel_qty' },
        { data: 'parcel_weight' },
        { data: 'parcel_length' },
        { data: 'parcel_width' },
        { data: 'parcel_height' },
        { data: 'remark' }
      ],
      // 小包单下的包裹字段
      parcel_customer_order_columns: [
        { data: 'parcel_num' },
        { data: 'parcel_weight' },
        { data: 'parcel_length' },
        { data: 'parcel_width' },
        { data: 'parcel_height' },
        // { data: 'parcel_qty' },
        { data: 'remark' },
        { data: 'item_code' },
        { data: 'declared_nameCN' },
        { data: 'declared_nameEN' },
        { data: 'declared_price' },
        { data: 'declared_currency' },
        { data: 'item_qty' },
        { data: 'item_weight' },
        { data: 'texture' },
        { data: 'use' },
        { data: 'brand' },
        { data: 'model' },
        { data: 'customs_code' }
      ],
      single_columns_zrh: [
        { data: 'parcel_num' },
        { data: 'reference_id' },
        { data: 'shipment_id' },
        { data: 'parcel_weight' },
        { data: 'parcel_length' },
        { data: 'parcel_width' },
        { data: 'parcel_height' },
        { data: 'remark' },
        { data: 'actual_weight' },
        { data: 'actual_length' },
        { data: 'actual_width' },
        { data: 'actual_height' },
        { data: 'label_weight' },
        { data: 'label_length' },
        { data: 'label_width' },
        { data: 'label_height' },
        { data: 'item_code' },
        { data: 'item_weight' },
        { data: 'item_length' },
        { data: 'item_width' },
        { data: 'item_height' },
        { data: 'declared_nameCN' },
        { data: 'declared_nameEN' },
        { data: 'declared_price' },
        { data: 'item_qty' },
        { data: 'texture' },
        { data: 'use' },
        { data: 'brand' },
        { data: 'model' },
        { data: 'customs_code' },
        { data: 'tax_rate' },
        { data: 'fba_no' },
        { data: 'fba_track_code' },
        { data: 'sku_url' },
        { data: 'item_picture' }
      ],
      contextMenu: { // 自定义右键菜单，可汉化，默认布尔值
        items: {
          'row_above': {
            name: '上方插入一行'
          },
          'row_below': {
            name: '下方插入一行'
          },
          'remove_row': {
            name: '删除行'
          },
          'make_read_only': {
            name: '只读'
          }
        }
      }
    }
  },
  computed: {
    colHeaders() {
      console.log('colHeaders-->')
      const that = this
      const tem = this.tabledata.filter(i => {
        // console.log('tem-->', i.label, i.hidden, !this.parceType, !i.onlySimle)
        if (i.hidden) {
          return false
        }
        if (!this.parceType) {
          return i.simple
        }
        return !i.onlySimle
      })

      if (this.parceType) {
        const path = this.$route.path
        if (path.includes('customerOrder_fbm') || path.includes('customerOrderTask_fbm')) {
          console.log('fbm_columns-->')
          that.columns = this.fbm_columns
        } else if (path.includes('customerOrderTask_fba_zrh')) {
          console.log('single_columns_zrh-->')
          that.columns = this.single_columns_zrh
        } else {
          that.columns = this.single_columns
        }
      } else {
        that.columns = this.all_columns
      }
      if (this.tableType === 'ParcelCustomerOrder') {
        that.columns = this.parcel_customer_order_columns
      }
      console.log('----parceT--->>>', this.parceType)
      // 这里再对数组进行删除操作就重复删除了, 因为created方法已经做删除了
      // this.tabledata.forEach(i => {
      //   if (i.hidden) {
      //     let index = null
      //     this.columns.forEach((l, l_index) => {
      //       if (l.data === i.prop) {
      //         console.log('l_index1-->', i.prop, l_index)
      //         index = l_index
      //       }
      //     })
      //     this.columns.splice(index, 1) // eslint-disable-line
      //   }
      // })
      return tem.map(i => i.label)
    },
    rowHeaders() {
      return this.tabledata.map((i, index) => index + 1)
    },
    data() {
      return new Array(100).fill('*').map((i) => {
        return new Array(this.tabledata.map(i => i.label).length).fill('*')
      })
    }
  },
  created() {
    // console.log(this.genKey('11111-11111-11111-11111-11111', 100))
    // this.bus.$on('switchReadOnly', (b) => {
    // this.columns.find(i => i.data === 'parcel_qty').readOnly = b
    // })
    // this.columns = this.parceType ? this.single_columns : this.all_columns

    this.tabledata.forEach(i => {
      if (i.hidden) {
        // let index = null
        this.columns.forEach((l, l_index) => {
          if (l.data === i.prop) {
            // index = l_index
            this.columns.splice(l_index, 1)
          }
        })
        // this.single_columns.splice(index, 1)
      }

      // if (i.hidden) {
      //   for (let index = this.columns.length - 1; index >= 0; index--) {
      //     const item = this.columns[index]
      //     // 在这里执行你的逻辑代码
      //     if (item.data === i.prop) {
      //       console.log('index-->', i.prop, index)
      //       this.columns.splice(index, 1)
      //     }
      //   }
      // }
    })
    // console.log('this.columns2-->', this.columns, this.columns.length)
  },
  methods: {
    genKey(pKey, days) {
      // 生成指定长度的随机16进制码
      var g = function(len, s) {
        s = s || ''
        var rev = []
        for (var i = s.length; i < len; i++) {
          rev.push(Math.floor(Math.random() * 16).toString(16))
        }
        return s + rev.join('')
      }

      // 校验Key中指定区间字符串，并生成校验码替换
      var e = function(str, s, len, vlen) {
        var j = '00'
        var j2 = parseInt(j, 16).toString()
        var j3 = parseInt(str.substr(s, len), 16) + j2.padStart(2, '0')
        var j4 = parseInt(j3, 10)
        var key = (97 - (j4 % 97) + 1).toString(16).padStart(2, '0')
        var rev = str.substr(0, len + s) + key + str.substr(s + len + vlen)
        return rev
      }

      // 注册天数
      days = days || 10
      pKey = pKey && pKey.replace(/-/g, '')
      if (!pKey || pKey.length !== 25) {
        pKey = g(25)
      }

      // 生成days天有效期
      var p = parseInt(pKey.substr(1, 1), 16)// 时间变量（随机）
      var dayKey = ((p || 9) * (days + Math.floor(Date.now() / 8.64e7)))
        .toString(16)
        .padStart(5, 0)// days*p 得出key
        // eslint-disable-next-line
      var pKey = pKey.substr(0, 18) + dayKey + pKey.substr(23)// 替换Key

      // '\x42\x3C\x48\x34\x50\x2B';//"B<H4P+"; [1,-5,7,-13,15,-22]
      // eslint-disable-next-line
      var i = [1, -5, 7, -13, 15, -22]

      pKey = e(pKey, 0, 6, 2)// 生成0-5位的校验码，并替换6-7位
      pKey = e(pKey, 6, 8, 2)// 生成6-14位的校验码，并替换14-15位
      pKey = e(pKey, 14, 9, 2)// 生成14-23位校验码，并替换23-24位

      return [
        pKey.substr(0, 5),
        pKey.substr(5, 5),
        pKey.substr(10, 5),
        pKey.substr(15, 5),
        pKey.substr(20, 5)
      ].join('-')
    }
  }
}
</script>

<style src="../../../node_modules/handsontable/dist/handsontable.full.css"></style>
