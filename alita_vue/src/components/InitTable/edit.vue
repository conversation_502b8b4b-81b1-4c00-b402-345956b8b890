<template>
  <div v-if="!formData.readOnly && !formData.changedOrder">
    <el-button
      v-if="!customButton"
      :type="buttonType"
      size="mini"
      @click="to"
    >
      {{ $t('common.edit') }}
    </el-button>

    <!-- 自定义图标按钮（带提示） -->
    <el-tooltip
      v-else
      :content="$t('common.edit')"
      placement="top"
    >
      <el-button
        :icon="customButton.icon || 'el-icon-edit'"
        size="mini"
        plain
        @click="to"
      />
    </el-tooltip>
    <eForm v-if="!isDetail" ref="form" :form-data="formData" :sup_this="sup_this" :is-add="false" :dialog-width="dialogWidth" :label-width="labelWidth" />
  </div>
  <div v-else-if="formData.changedOrder">
    <el-button
      v-if="!customButton"
      :type="buttonType"
      size="mini"
      @click="to"
    >
      {{ $t('common.edit') }}
    </el-button>

    <!-- 自定义图标按钮（带提示） -->
    <el-tooltip
      v-else
      :content="$t('common.edit')"
      placement="top"
    >
      <el-button
        :icon="customButton.icon || 'el-icon-edit'"
        size="mini"
        plain
        @click="to"
      />
    </el-tooltip>
    <eForm v-if="!isDetail" ref="form" :form-data="formData" :sup_this="sup_this" :is-add="false" :dialog-width="dialogWidth" :label-width="labelWidth" />
  </div>
</template>
<script>
import eForm from './form'
// import { parseTime } from '@/utils/index'

export default {
  components: { eForm },
  props: {
    data: {
      type: Object,
      required: true
    },
    sup_this: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    },
    isDetail: {
      type: String,
      default: ''
    },
    dialogWidth: {
      type: String,
      default: null
    },
    labelWidth: {
      type: String,
      default: null
    },
    customButton: {
      type: [Boolean, Object],
      default: false
    },
    buttonType: {
      type: String,
      default: 'success'
    }
  },
  methods: {
    to() {
      if (this.isDetail) {
        this.$router.push(this.isDetail + '&edit=true')
        return
      }
      const _this = this.$refs.form
      this.formData.data.forEach(i => {
        if (i.prop === 'customer' && this.data[i.prop] === null && i.isLinkageHiding) {
          _this.rules['customer'][0].required = false
          i.hidden = true
        } else { i.hidden = false }

        if (i.type === 'multiRow') {
          i.data.forEach(l => {
            this.setVal(_this, l)
          })
        } else {
          this.setVal(_this, i)
        }
      })
      _this.form.id = this.data.id
      _this.dialog = true
    },
    setVal(_this, i) {
      if (i.type === 'file') {
        this.$nextTick(() => {
          _this.fileList = this.data[i.prop].map(k => {
            return {
              id: k.id,
              name: k.name,
              url: k.url
            }
          })
        })
      } else {
        if (i.alias === '会计区间') {
          _this.form[i.prop] = [this.data['start_month'], this.data['end_month']]
        } else {
          if (i.type === 'datetime' && this.data[i.prop]) {
            // _this.form[i.prop] = parseTime(this.data[i.prop])
            // 用parseTime解析后变成了 "2024-10-16 19:18:25", 而src/components/InitTable/form.vue中的渲染组件中格式是yyyy-MM-ddTHH:mm:ss, 所以会报错
            _this.form[i.prop] = this.data[i.prop]
            return
          }
          if (i.trans_bool === true && i.prop === 'is_general') {
            _this.form[i.prop] = this.data[i.prop] === true ? '是' : '否'
            return
          }
          if (i.combineStr === true && i.prop === 'effective_month' && i.filter !== undefined && this.data[i.prop] !== null) {
            _this.form[i.prop] = this.data[i.prop].split(',').map(month => i.filter[month])
            return
          }
          // console.log('form普通赋值-->', i.prop, this.data[i.prop])
          _this.form[i.prop] = this.data[i.prop]
        }
      }
    }
  }
}
</script>

<style scoped>
  div{
    display: inline-block;
    /*margin-right: 3px;*/
  }
</style>
