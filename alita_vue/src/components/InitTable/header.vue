<template>
  <div ref="fixedBtn" class="head-container clearfix">
    <!-- 隐藏式上传按钮 -->
    <el-upload
      ref="hiddenUpload"
      :show-file-list="false"
      :http-request="(data)=>{importExcel(data,true)}"
      :limit="1"
      style="display:none;"
      class="upload"
      action="https://jsonplaceholder.typicode.com/posts/">
      <el-button id="hiddenUpload" type="primary">隐藏上传</el-button>
    </el-upload>

    <!-- 隐藏式上传按钮 -->
    <el-upload
      ref="hiddenUploadFile"
      :show-file-list="false"
      :http-request="(data)=>{importExcel(data,true)}"
      :limit="20"
      style="display:none;"
      class="upload"
      action="https://jsonplaceholder.typicode.com/posts/"
      multiple
    >
      <el-button id="hiddenUploadFile" type="primary">隐藏上传</el-button>
    </el-upload>

    <!-- 上层按钮 -->
    <div :class="{fixed:fixed.isFixed}" :style="{width:fixed.w}" class="clearfix">
      <!-- 排序 -->
      <el-button type="text" class="filter-item fr ml10 reverse" @click="reverseTable()"><i style="font-size:16px;font-weight:bold;" class="el-icon-sort"/></el-button>
      <!-- 导出全部数据  && !['mz', 'yqf', 'clt'].includes(sysFlag) -->
      <el-button v-if="formData.exportAll" :loading="downloadLoading" class="hvr-float-shadow filter-item fr ml10" type="primary" icon="el-icon-download" @click="downloadAll">{{ $t('common.exportAll') }}</el-button>
      <!-- 导出当前页 -->
      <el-button v-if="!formData.noExport" :loading="downloadLoading" class="hvr-float-shadow filter-item fr ml10" type="primary" icon="el-icon-download" @click="download">{{ $t('common.export') }}</el-button>
      <!-- 批量搜索 -->
      <el-button v-if="formData.multiSearch&&formData.multiSearch.length" class="hvr-float-shadow filter-item fr" type="primary" @click="multiSearchShow=true">{{ $t('common.batchSearch') }}</el-button>
      <el-button class="hvr-float-shadow filter-item fr" type="primary" icon="el-icon-search" @click="toQuery()">{{ $t('common.search') }}</el-button>
      <!-- 搜索 -->
      <el-input v-model="query.value" :placeholder="formData.searchholder || $t('common.enter')" clearable style="width: 200px;" class="filter-item fr ml10" @keyup.enter.native="toQuery()"/>
      <!--重置-->
      <el-button v-if="formData.resetSearch" class="filter-item fr ml10" type="info" @click="resetQuery()">{{ $t('common.reset') }}</el-button>
      <!-- 新增 -->
      <div v-if="!formData.readOnly&&!formData.noAdd" class="hvr-float-shadow fl" style="display: inline-block;margin: 0px 10px 0px 0px">
        <el-button
          v-if="checkPermission(role)"
          class="filter-item"
          type="success"
          @click="addItem()">{{ $t('common.create') }}</el-button>
        <eForm v-if="!isDetail" ref="form" :form-data="formData" :is-add="true"/>
      </div>
      <!-- 其他传入的按钮操作 -->
      <template v-for="(item,index) in formData.action">
        <template v-if="authButtons(item)">
          <el-upload
            v-if="item.import"
            ref="uploadExcel"
            :key="item.method"
            :show-file-list="false"
            :http-request="(data)=>{importExcel(data, item.name)}"
            :limit="10"
            class="fl upload hvr-float-shadow"
            style="margin-right:10px; margin-bottom:10px; height:28px;"
            action="https://jsonplaceholder.typicode.com/posts/">
            <el-button
              :class="['filter-item',item.position||'fl']"
              style="margin-left:10px;"
              type="primary">{{ item.name }}</el-button>
          </el-upload>
          <!-- 下拉形式的按钮 -->
          <el-select v-else-if="item.type==='selectBtn'" :no-data-text="$t('content.NoData')" :class="['filter-item', 'selectBtn', 'hvr-float-shadow', item.position||'fl']" :key="item.type+index" v-model="item.value" :placeholder="item.placeholder" :style="{width:(item.placeholder.length===2?'70':'100')+'px'}" @change="execute(item, true)">
            <template v-for="i in item.btns" >
              <el-option v-if="authButtons(i)" :key="i.method" :label="i.name" :value="i.method"/>
            </template>
          </el-select>
          <el-select v-else-if="item.type==='selectOptionMethod'" :no-data-text="$t('content.NoData')" :class="['filter-item', 'selectBtn', 'hvr-float-shadow', item.position||'fl']" :key="item.type+index" v-model="item.value" :placeholder="item.placeholder" :style="{width:(item.placeholder.length===2?'70':'100')+'px'}" @change="resetOption(item)">
            <template v-for="i in item.btns">
              <el-option v-if="authButtons(i)" :key="i.method" :value="i.method">
                <div @click="selectOptionMethod(i)">
                  {{ i.name }}
                </div>
              </el-option>
            </template>
          </el-select>
          <!--          <el-button v-else-if="item.type==='monthOption'" :loading="item.loading" :key="item.method" :type="item.type || ''" :class="['filter-item','hvr-float-shadow',item.position||'fl']" class="filter-item">-->
          <span v-else-if="item.type==='monthOption'" :key="item.id" :class="['filter-item', 'hvr-float-shadow', item.position||'fl']">
            <template>
              <el-date-picker
                :key="item.method"
                :placeholder="item.name"
                v-model="monthOptionDate"
                value-format="yyyy-MM-dd"
                type="month"
                @change="monthOptionChange"
              >
              </el-date-picker>
            </template>
          </span>
          <!--          </el-button>-->
          <el-button v-else-if="item.type==='secondConfirm'" :loading="item.loading" :key="item.method" :type="item.type || ''" :class="['filter-item','hvr-float-shadow',item.position||'fl']" class="filter-item" @click="execute(item)">{{ item.name }}</el-button>
          <!--列表页头部按钮(无type标识的)-->
          <el-button v-else :loading="item.loading" :key="item.method" :type="item.type || ''" :class="['filter-item','hvr-float-shadow',item.position||'fl']" class="filter-item" @click="execute(item)">{{ item.name }}</el-button>
        </template>
      </template>
    </div>
    <!-- 下层过滤按钮 -->
    <div class="bottom clearfix">
      <div v-for="item in formData.filters" :key="item.prop" class="fl" style="margin-right:10px;margin-bottom:6px">
        <!-- 列表页头部下拉框 -->
        <template v-if="item.type === 'select'">
          <el-select
            v-show="!(formData.showFold === true && item.isFold === true && !fold)"
            v-model="item.value"
            :no-data-text="$t('content.NoData')"
            :placeholder="$t('common.select')+' '+item.placeholder"
            :multiple="item.multiple"
            :class="{linghtUp:item.value}"
            style="width:132px;"
            clearable
            filterable
            @focus="getSelectData(item)"
            @change="change(item.value)">
            <el-option
              v-for="i in item.data"
              :key="i.id"
              :label="item.show === 'nameAndShortName' ? `${i.final_name}` : i[item['label']] || i.label || i.name || i.short_name || i.code || i.order_num"
              :value="item.submit?i[item.submit]:i.id"/>
          </el-select>
        </template>
        <!-- 币种下拉框 -->
        <template v-else-if="item.type === 'currency'">
          <el-select v-show="!(formData.showFold === true && item.isFold === true && !fold)" v-model="item.value" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')+' '+item.placeholder" :class="{linghtUp:item.value}" style="width:132px;" clearable filterable @change="change(item.value)">
            <el-option value="CNY" label="CNY"/>
            <el-option value="USD" label="USD"/>
            <el-option value="GBP" label="GBP"/>
            <el-option value="EUR" label="EUR"/>
            <el-option value="HKD" label="HKD"/>
            <el-option value="CAD" label="CAD"/>
            <el-option value="CHF" label="CHF"/>
            <el-option value="AUD" label="AUD"/>
            <el-option value="MXN" label="MXN"/>
          </el-select>
        </template>
        <!-- 单选布尔值 -->
        <template v-if="item.type === 'boolean'">
          <el-select v-show="!(formData.showFold === true && item.isFold === true && !fold)" v-model="item.value" :no-data-text="$t('content.NoData')" :class="{linghtUp:item.value===true||item.value===false}" :placeholder="item.placeholder" style="width:100px;" clearable filterable @change="change(item.value)">
            <el-option :value="true" :label="$t('common.radioTrue')"/>
            <el-option :value="false" :label="$t('common.radioFalse')"/>
          </el-select>
        </template>
        <!-- 时间范围选择器 -->
        <template v-else-if="item.type === 'datetime'||item.type === 'daterange'||item.type === 'datetimerange'||item.type==='monthrange' || item.type === 'date'">
          <el-date-picker
            v-show="!(formData.showFold === true && item.isFold === true && !fold)"
            v-model="item.value"
            :type="item.type"
            :value-format="item.type === 'daterange'||item.type === 'date'?'yyyy-MM-dd':item.type==='monthrange'?'yyyy-MM-dd':'yyyy-MM-ddTHH:mm:ss'"
            :start-placeholder="`${item.placeholder}${item.type === 'daterange'?$t('common.startDate'):item.type==='monthrange'?$t('common.startMonth'):$t('common.startTime')}`"
            :end-placeholder="`${item.placeholder}${item.type === 'daterange'?$t('common.endDate'):item.type==='monthrange'?$t('common.endMonth'):$t('common.endTime')}`"
            :default-time="item.type === 'date'?'':item.type === 'datetime'?'00:00:00':['00:00:00','23:59:59']"
            :placeholder="item.placeholder"
            :range-separator="$t('common.to')"
            :class="{linghtUp:item.value}"
            unlink-panels
            clearable
            @change="change(item.value)"/>
        </template>
        <!--周选择器-->
        <template v-else-if="item.type === 'weekRange'">
          <el-date-picker
            v-show="!(formData.showFold === true && item.isFold === true && !fold)"
            v-model="item.value"
            :picker-options="{'firstDayOfWeek': item.firstDayOfWeek ? item.firstDayOfWeek : 7}"
            :placeholder="`选择${item.placeholder}（周）`"
            type="week"
            format="yyyy-MM 第 WW 周"
            clearable
            @change="change(item.value)"
          />
        </template>
        <!--单月周选择器-->
        <template v-else-if="item.type === 'weekRangeOneMonth'">
          <el-date-picker
            v-show="!(formData.showFold === true && item.isFold === true && !fold)"
            v-model="item.value"
            :picker-options="{'firstDayOfWeek': item.firstDayOfWeek ? item.firstDayOfWeek : 7}"
            :placeholder="`选择${item.placeholder}（周）`"
            :format="getWeekRangeOneMonth(item.value)"
            type="week"
            clearable
            @change="change(item.value)"
          />
        </template>
        <!--月选择器-->
        <div v-else-if="item.type === 'monthRange'">
          <el-date-picker
            v-show="!(formData.showFold === true && item.isFold === true && !fold)"
            v-model="item.value"
            :placeholder="`选择${item.placeholder}（月）`"
            type="month"
            clearable
            @change="change(item.value)"
          >
          </el-date-picker>
        </div>
        <!-- 输入框查询 -->
        <template v-else-if="item.type==='input'">
          <el-input v-show="!(formData.showFold === true && item.isFold === true && !fold)" :placeholder="$t('common.enter')+item.placeholder" v-model="item.value" :class="{linghtUp:item.value}" clearable @change="change(item.value)"/>
        </template>

        <template v-else-if="item.type==='multiInput'">
          <el-row v-show="!(formData.showFold === true && item.isFold === true && !fold)">
            <el-col :span="24">
              <el-input :placeholder="$t('common.enter') + item.placeholder" v-model="item.value" :class="{linghtUp: item.value}" clearable @change="change(item.value)">
                <template slot="prepend">
                  <el-select slot="prepend" v-model="item.queryType" :no-data-text="$t('content.NoData')" style="min-width: 120px;">
                    <el-option v-for="inputOption in item.options" :key="inputOption.prop" :label="inputOption.label" :value="inputOption.prop"></el-option>
                  </el-select>
                </template>
              </el-input>
            </el-col>
          </el-row>
        </template>
      </div>
      <template v-if="formData.showFold === true">
        <el-button type="text" size="mini" @click="handleFold">
          <span v-if="!fold">{{ $t('common.fold') }}</span>
          <span v-else>{{ $t('common.unfold') }}</span>
          <i :class="fold ? 'el-icon-arrow-up': 'el-icon-arrow-down'"></i>
        </el-button>
      </template>

    </div>
    <!-- 合计金额 -->
    <div class="clearfix">
      <div v-for="item in formData.checkSumary" :key="item.title" :class="[item.position||'fl']">
        <template v-if="item.blankHide&&item.value || !item.blankHide">
          <span>{{ item.title }}</span><span :style="{color:item.color || 'red', 'font-weight':'bold'}" v-html="item.value"/>&nbsp;&nbsp;&nbsp;&nbsp;
        </template>
      </div>
    </div>
    <!-- <div ref="fixedBtn"/> -->
    <!-- 一层对话框 action操作弹窗 -->
    <el-dialog
      :visible="dialogVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :width="actionParams.data.width? actionParams.data.width : fullScreen? '75%':dialogType==='date'||dialogType==='selectAciton'||dialogType==='form'?'440px':'900px'"
      :title="actionParams.data.overheadTitle ? actionParams.data.overheadTitle : dialogType==='form' ? $t('common.enter') : $t('common.select') + ' '+actionParams.data.label">
      <!-- 卡派单批量配置 -->
      <div v-if="actionParams.data.overheadButton" style="display: flex; align-items: center; margin-bottom: 10px">
        <el-button @click="overheadClick(actionParams.data, false)">{{ actionParams.data.overheadButton }}</el-button>
        <div style="padding-left: 10px;display: flex;align-items: center">已选择订单数: <span style="padding-left: 5px;color: red;font-size: 20px">{{ this.$parent.formData.checkData.length }}</span></div>
      </div>
      <hr v-if="actionParams.data.overheadTitle">
      <div v-if="actionParams.data.overheadTitle" class="el-dialog__header2">
        <span class="el-dialog__title" style="">{{ dialogType==='form' ? $t('common.enter') : $t('common.select') + ' '+actionParams.data.label }}</span>
      </div>
      <!-- 海运提单部分配载/全部配载 -->
      <div v-if="headerBtn" style="display: flex; align-items: center; margin-bottom: 10px">
        <!--<span v-if="$parent.formData.checkData.length===1 && $parent.formData.checkData[0]['carton'] > 0">
          <el-button @click="addFreightNum()">部分配载</el-button>
          <el-button @click="hiddenFreightNum()">全部配载</el-button>
        </span>-->
        <el-select v-model="orderStowageType" placeholder="请选择配载方式" clearable @change="orderStowage">
          <el-option :value="0" label="全部配载"/>
          <el-option :value="1" label="部分配载"/>
        </el-select>
        <div style="padding-left: 10px;display: flex;align-items: center">订单总件数: <span style="padding-left: 5px;color: red;font-size: 20px">{{ $parent.formData.checkData.length && $parent.formData.checkData[0].carton || 0 }}</span></div>
        <span v-if="actionParams.data.showRebinding" style="margin-left: auto;">
          <el-checkbox v-model="actionParams.data.is_rebinding">重新绑定</el-checkbox>
        </span>
      </div>
      <!-- 对话框表格过滤按钮 -->
      <div v-if="dialogType === 'table' && searchable" class="bottom clearfix" style="margin-bottom: 10px">
        <!--<div v-for="action in formData.action" :key="action.prop" class="fl" style="width: 100%">-->
        <div v-for="item in dialogTableFilters" ref="inputContainer" :key="item.prop" :style="{ display: 'inline-block', float: item.type === 'input' ? 'right' : 'none' }" style="display: inline-block; ">
          <!-- 列表页头部下拉框 -->
          <template v-if="item.type === 'select'">
            <el-select v-model="item.value" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')+' '+item.placeholder" :multiple="item.multiple" :class="{linghtUp:item.value}" style="width:132px;" clearable filterable @focus="getSelectData(item)" @change="dialogQuery(item.value)">
              <el-option
                v-for="i in item.data"
                :key="i.id"
                :label="item.show === 'nameAndShortName' ? `${i.final_name}` : i[item['label']] || i.label || i.name || i.short_name || i.code || i.order_num"
                :value="item.submit?i[item.submit]:i.id"/>
            </el-select>
          </template>
          <!-- 币种下拉框 -->
          <template v-else-if="item.type === 'currency'">
            <el-select v-model="item.value" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')+' '+item.placeholder" :class="{linghtUp:item.value}" style="width:132px;" clearable filterable @change="dialogQuery(item.value)">
              <el-option value="CNY" label="CNY"/>
              <el-option value="USD" label="USD"/>
              <el-option value="GBP" label="GBP"/>
              <el-option value="EUR" label="EUR"/>
              <el-option value="HKD" label="HKD"/>
              <el-option value="CAD" label="CAD"/>
              <el-option value="AUD" label="AUD"/>
            </el-select>
          </template>
          <!-- 单选布尔值 -->
          <template v-if="item.type === 'boolean'">
            <el-select v-model="item.value" :no-data-text="$t('content.NoData')" :class="{linghtUp:item.value===true||item.value===false}" :placeholder="item.placeholder" style="width:100px;" clearable filterable @change="dialogQuery(item.value)">
              <el-option :value="true" :label="$t('common.radioTrue')"/>
              <el-option :value="false" :label="$t('common.radioFalse')"/>
            </el-select>
          </template>
          <!-- 时间范围选择器 -->
          <template v-else-if="item.type === 'datetime'||item.type === 'daterange'||item.type === 'datetimerange'||item.type==='monthrange' || item.type === 'date'">
            <el-date-picker
              v-model="item.value"
              :type="item.type"
              :value-format="item.type === 'daterange'||item.type === 'date'?'yyyy-MM-dd':item.type==='monthrange'?'yyyy-MM-dd':'yyyy-MM-ddTHH:mm:ss'"
              :start-placeholder="`${item.placeholder}${item.type === 'daterange'?$t('common.startDate'):item.type==='monthrange'?$t('common.startMonth'):$t('common.startTime')}`"
              :end-placeholder="`${item.placeholder}${item.type === 'daterange'?$t('common.endDate'):item.type==='monthrange'?$t('common.endMonth'):$t('common.endTime')}`"
              :default-time="item.type === 'date'?'':item.type === 'datetime'?'00:00:00':['00:00:00','23:59:59']"
              :placeholder="item.placeholder"
              :range-separator="$t('common.to')"
              unlink-panels
              clearable
              @change="dialogQuery(item.value)"/>
          </template>
        </div>
        <!-- 输入框查询 -->
        <!--<template v-else-if="item.type==='input'" class="fr">-->
        <template v-if="searchable">
          <div class="fr" style="display: inline-block">
            <!--<el-input :placeholder="$t('common.enter')+item.placeholder" v-model="item.value" clearable style="width: 240px;">
              <el-button slot="append" icon="el-icon-search" @click="dialogQuery(item.value)"></el-button>
            </el-input>-->
            <el-input v-model="dialogSearch.value" :placeholder="dialogSearchHolder || $t('common.enter')" clearable style="width: 240px;" @keyup.enter.native="dialogQuery()">
              <el-button slot="append" icon="el-icon-search" @click="dialogQuery(dialogSearch.value)"></el-button>
            </el-input>
          </div>
        </template>
      </div>
      <!--对话框表格搜索框-->
      <!--<div v-if="dialogType === 'table' && searchable" class="clearfix head-container">
        <el-input v-model="query.value" clearable placeholder="输入关键字搜索" style="width: 240px;" class="input-with-select fr" @keyup.enter.native="toQuery">
          <el-button slot="append" icon="el-icon-search" @click="aaa"></el-button>
        </el-input>
      </div>-->

      <!-- 选择日期 -->
      <template v-if="dialogType === 'date'">
        <div>
          <el-input v-model="actionParams.data.amount" :placeholder="$t('common.amount')" style="margin-bottom:10px;"/>
          <el-date-picker
            v-model="actionParams.data.date"
            :placeholder="$t('common.selectDate')"
            value-format="yyyy-MM-dd"
            style="width:100%"
            type="date"/>
        </div>
      </template>
      <!-- 表格筛选 -->
      <el-table v-else-if="dialogType === 'table'" ref="dialogTable" :empty-text="$t('content.NoData')" :data="tableData" max-height="350px" size="small" border style="width: 100%;" @selection-change="selectionChange">
        <el-table-column type="selection" fixed width="40"/>
        <div v-for="item in colums" :key="item.prop" >
          <el-table-column v-if="!hiddenColums.includes(item.prop)" :label="item.label" :prop="item.prop" :width="item.width">
            <template slot-scope="scope">
              <div v-if="item.type==='link'">
                <a :href="backHost() + (scope.row.url.substr(12))" style="color:blue;" target="_blank">{{ scope.row[item.prop] }}</a>
              </div>
              <div v-else-if="item.type==='debit_detail'">
                <el-button type="success" @click="chargeOffDebit(scope.row, item)">单笔核销</el-button>
              </div>
              <div v-else-if="item.type==='input'">
                <el-input v-model="scope.row[item.prop]" :placeholder="$t('common.enter')" size="medium" />
              </div>
              <!-- 在一个单元格中显示预计长宽高和长宽高-->
              <div v-else-if="item.pre">
                <span>{{ scope.row['pre_' + item.prop] }}</span> <span>|</span> <span>{{ scope.row[item.prop] || 0 }}</span>
              </div>
              <!-- 日期转换 -->
              <span v-else-if="item.type==='date' || item.type==='datetime' || item.type==='month'">
                {{ parseTime(scope.row[item.prop], item.type) }}
              </span>
              <div v-else>
                {{ item.filters?item.filters[scope.row[item.prop]]:scope.row[item.prop] }}
              </div>
            </template>
          </el-table-column>
        </div>
      </el-table>
      <!--一层对话框表格分页组件-->
      <el-pagination
        v-if="dialogType === 'table'"
        :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
        :total="dialogTotal"
        :page-size="dialogSize"
        :current-page="dialogPage"
        style="margin-top: 8px;"
        layout="total, prev, pager, next, sizes"
        @size-change="sizeChangeDialog"
        @current-change="pageChangeDialog"/>

      <!-- 轨迹下拉操作筛选 -->
      <template v-if="dialogType==='selectAciton'">
        <div>
          <div v-if="!noTrackCode">{{ $t('common.trackCode') }}：</div>
          <el-select v-if="!noTrackCode" v-model="actionParams.data.selectActionVal" :no-data-text="$t('content.NoData')" clearable filterable class="custom-dialog-item" @change="judgeRequiredAddressNameEn">
            <el-option
              v-for="item in colums"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
          <div v-if="!noQty">{{ $t('common.pieces') }}：</div>
          <el-input v-if="!noQty" v-model="actionParams.data.amount" :placeholder="$t('common.enter')" type="number" class="custom-dialog-item"/>
          <div>{{ $t('common.date') }}：</div>
          <el-date-picker
            v-model="actionParams.data.date"
            :placeholder="$t('common.selectDate')"
            :value-format="dateType==='date'?'yyyy-MM-dd':'yyyy-MM-dd HH:mm:ss'"
            :type="dateType"
            class="custom-dialog-item"/>
          <div v-if="actionParams.data.method === 'change_first_track'">{{ $t('content.Locations') }}：</div>
          <el-input v-if="actionParams.data.method === 'change_first_track'" v-model="actionParams.data.location" :placeholder="$t('common.enter')" class="custom-dialog-item"/>
          <!--<div v-if="nodeAddress.required">{{ nodeAddress.address_name }}：</div>-->
          <div v-if="nodeAddress.required"><span style="color: red">*&nbsp;</span>节点地址中文名：</div>
          <el-input v-if="nodeAddress.required" v-model="nodeAddress.address_name" :placeholder="nodeAddress.msg" class="custom-dialog-item" disabled clearable/>
          <div v-if="nodeAddress.required"><span style="color: red">*&nbsp;</span>节点地址英文名：</div>
          <el-input v-if="nodeAddress.required" v-model="nodeAddress.address_name_en" :placeholder="$t('common.enter') + '：用于亚马逊轨迹'" class="custom-dialog-item" clearable/>
        </div>
      </template>
      <!--自定义条码大小-->
      <template v-if="dialogType==='barcodeSize'">
        <div>
          <div>{{ $t('content.paperHeight') }}：
            <el-input-number v-model="actionParams.data.paperHeight" :min="1" :precision="1" :step="0.1" :label="$t('content.paperHeight')"></el-input-number>
            <span>{{ $t('content.paperWidth') }}：</span>
            <el-input-number v-model="actionParams.data.paperWidth" :min="1" :precision="1" :step="0.1" :label="$t('content.paperWidth')"></el-input-number>
          </div>
          <div>{{ $t('content.barcodeWidth') }}：
            <el-input-number v-model="actionParams.data.barcodeWidth" :min="0.1" :precision="1" :step="0.1" :max="10" :label="$t('content.barcodeWidth')"></el-input-number>
            <span>{{ $t('content.barcodeHeight') }}：</span>
            <el-input-number v-model="actionParams.data.barcodeHeight" :min="0.1" :precision="1" :step="0.1" :max="10" :label="$t('content.barcodeHeight')"></el-input-number>
          </div>
        </div>
      </template>
      <!-- 表单形式 -->
      <template v-if="dialogType==='form'">
        <el-row>
          <el-form ref="headerForm" :model="dialogFormData" :label-width="fullScreen?'130px':'120px'">
            <el-form-item
              v-for="item in dialogForm"
              :label="item.type === 'hidden' ? '' : item.label"
              :style="{
                width: fullScreen ? '48%' : '100%',
                float: 'left',
                height: item.height ? item.height : '29px',
                display: item.type === 'hidden' ? 'none' : 'block'
              }"
              :key="item.prop"
              :prop="item.prop"
              :rules="[
                { required: item.require, message: item.label + $t('common.noBlank')}
              ]"
              style="margin-bottom:20px;">
              <div v-if="item.defaultContent" style="display: flex; align-items: center; margin-bottom: 10px">
                <div style="padding-left: 10px;display: flex;align-items: center;font-size: 12px"><span style="white-space: pre-line">{{ item.defaultContent }}</span></div>
              </div>
              <!-- radio单选项 -->
              <!-- 已经线下发送了, 为true, 没有线下发送为fase -->
              <template v-if="item.type==='radio'&&item.options">
                <el-radio v-for="option in item.options" :key="option.id" v-model="dialogFormData[item.prop]" :label="option.value" @change="clickRedioButton('email_content', dialogFormData[item.prop])">{{ option.label }}</el-radio>
              </template>
              <template v-else-if="item.type==='radio'">
                <el-radio v-model="dialogFormData[item.prop]" :label="true">{{ $t('common.radioTrue') }}</el-radio>
                <el-radio v-model="dialogFormData[item.prop]" :label="false">{{ $t('common.radioFalse') }}</el-radio>
              </template>
              <el-select v-else-if="item.type==='select'&&item.filter" v-model="dialogFormData[item.prop]" :no-data-text="$t('content.NoData')" :disabled="item.disabled" :placeholder="$t('common.select')" style="width:100%;" clearable filterable>
                <el-option
                  v-for="item in Object.keys(item.filter).map(i=>{return {label:item.filter[i],id:i}})"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"/>
              </el-select>
              <!-- 币种 -->
              <template v-else-if="item.type === 'currency'">
                <el-select :disabled="item.disabled" v-model="dialogFormData[item.prop]" :placeholder="$t('common.select')" style="width:100%;" clearable filterable>
                  <el-option value="CNY" label="CNY"/>
                  <el-option value="USD" label="USD"/>
                  <el-option value="GBP" label="GBP"/>
                  <el-option value="EUR" label="EUR"/>
                  <el-option value="HKD" label="HKD"/>
                  <el-option value="CAD" label="CAD"/>
                  <el-option value="CHF" label="CHF"/>
                  <el-option value="AUD" label="AUD"/>
                </el-select>
              </template>
              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="item.type==='date' || item.type==='daterange' || item.type=== 'datetime' || item.type=== 'daterangerange'"
                v-model="dialogFormData[item.prop]"
                :type="item.type"
                :disabled="item.disabled"
                :value-format="item.type==='date' || item.type==='daterange'?'yyyy-MM-dd':'yyyy-MM-ddTHH:mm:ss'"
                :picker-options="item.restrict==='laterDate'? expireTimeOption: true"
                :placeholder="$t('common.selectDate')"
                style="width:100%"/>
              <!--单月周选择器-->
              <template v-else-if="item.type === 'weekRangeOneMonth'">
                <el-date-picker
                  v-model="dialogFormData[item.prop]"
                  :picker-options="{'firstDayOfWeek': item.firstDayOfWeek ? item.firstDayOfWeek : 7}"
                  :placeholder="`选择${item.label}（周）`"
                  :format="getWeekRangeOneMonth(dialogFormData[item.prop])"
                  type="week"
                  clearable
                />
              </template>
              <template v-else-if="item.type==='select'&& item.value_type==='code'">
                <el-select v-model="dialogFormData[item.prop]" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')" style="width:100%" clearable filterable @focus="getSelectData(item)">
                  <el-option
                    v-for="i in item.data"
                    :key="i.code"
                    :label="i[item['label']] || i.label || i.name "
                    :value="item.submit?i[item.submit]:i.code"/>
                </el-select>
              </template>
              <template v-else-if="item.type==='select'">
                <el-select v-model="dialogFormData[item.prop]" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')" style="width:100%" clearable filterable @focus="getSelectData(item)">
                  <el-option
                    v-for="i in item.data"
                    :key="i.id"
                    :label="i[item['label']] || i[item['dialogLabelKey']] || i.label || i.name || i.short_name || i.code || i.order_num || i[item['labelKey']]"
                    :value="item.submit?i[item.submit]:i.id"/>
                </el-select>
              </template>
              <!--周选择器-->
              <template v-else-if="item.type === 'weekRange'">
                <el-date-picker
                  v-model="dialogFormData[item.prop]"
                  :picker-options="{'firstDayOfWeek': item.firstDayOfWeek ? item.firstDayOfWeek : 7}"
                  :placeholder="`选择${item.label}（周）`"
                  type="week"
                  format="yyyy-MM 第 WW 周"
                  clearable
                />
              </template>
              <el-input v-else-if="item.type==='textarea'" v-model="dialogFormData[item.prop]" :placeholder="$t('common.enter')" :rows="item.rows ? item.rows : 2" type="textarea"/>
              <el-upload
                v-else-if="item.type==='files'"
                ref="upload"
                :on-remove="(file, fileList) => handleRemove(file, fileList, item)"
                :before-remove="(file, fileList) => beforeRemove(file, fileList, item, false)"
                :before-upload="beforeUpload"
                :on-change="fileChange"
                :auto-upload="true"
                :limit="5"
                :on-exceed="handleExceedFive"
                :http-request="(file, fileList) =>uploadAttchment(file, item)"
                :file-list="fileList"
                class="upload-demo"
                action="https://jsonplaceholder.typicode.com/posts/"
                multiple
              >
                <el-button size="small" type="success">{{ $t('common.clickupload') }}</el-button>
              </el-upload>
              <el-input v-else v-model="dialogFormData[item.prop]" :placeholder="$t('common.enter')"/>
            </el-form-item>
          </el-form>
        </el-row>
      </template>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">{{ $t('common.cancel') }}</el-button>
        <el-button v-if="actionParams.data.purpose==='chargeOff'" type="primary" @click="execute({method:actionParams.methods,check:true})">{{ $t('common.chargeOff') }}</el-button>
        <el-button v-else type="primary" @click="execute({method:actionParams.methods, check:true})">{{ $t('common.sure') }}</el-button>
      </span>
    </el-dialog>
    <!--二层对话框-->
    <el-dialog
      :visible="innerDialogVisible"
      :before-close="innerHandleClose"
      :close-on-click-modal="false"
      :width="fullScreen? '70%':innerTable.dialogType==='innerTable'?'410px':'740px'"
      :title="innerTable.purpose==='chargeOff'?$t('common.chargeOff') + ' '+innerTable.name:$t('common.enter') + ' '+actionParams.data.label">
      <div v-for="item in innerDialogTableFilters" ref="inputContainer" :key="item.prop" :style="{ display: 'inline-block', float: item.type === 'input' ? 'right' : 'none' }" style="display: inline-block; ">
        <!-- 列表页头部下拉框 -->
        <template v-if="item.type === 'select'">
          <el-select v-model="item.value" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')+' '+item.placeholder" :multiple="item.multiple" :class="{linghtUp:item.value}" style="width:132px;" clearable filterable @focus="getSelectData(item)" @change="innerDialogQuery(item.value)">
            <el-option
              v-for="i in item.data"
              :key="i.id"
              :label="item.show === 'nameAndShortName' ? `${i.final_name}` : i[item['label']] || i.label || i.name || i.short_name || i.code || i.order_num"
              :value="item.submit?i[item.submit]:i.id"/>
          </el-select>
        </template>
      </div>
      <!--搜索框-->
      <template v-if="innerTable.searchable">
        <div class="fr" style="display: inline-block">
          <el-input v-model="innerDialogSearch.value" :placeholder="innerTable.dialogSearchHolder || $t('common.enter')" clearable style="width: 240px;" @keyup.enter.native="innerDialogQuery(innerDialogSearch.value)">
            <el-button slot="append" icon="el-icon-search" @click="innerDialogQuery(innerDialogSearch.value)"></el-button>
          </el-input>
        </div>
      </template>
      <!-- 批量搜索 -->
      <el-button v-if="innerTable.multiSearch&&innerTable.multiSearch.length" class="hvr-float-shadow filter-item fr" type="primary" @click="innerMultiSearchShow=true">{{ $t('common.batchSearch') }}</el-button>
      <!-- 表格筛选 -->
      <el-table v-if="innerTable.dialogType === 'table'" ref="dialogTable" :empty-text="$t('content.NoData')" :data="innerTableData" max-height="320px" size="small" border style="width: 100%;" @selection-change="selectionChange">
        <el-table-column type="selection" fixed width="40"/>
        <el-table-column v-for="item in innerTable.colums" :key="item.prop" :label="item.label" :prop="item.prop" :width="item.width">
          <template slot-scope="scope">
            <div v-if="item.type === 'input'">
              <el-input v-model="scope.row[item.prop]" :placeholder="$t('common.enter')" type="number"/>
            </div>
            <div v-else>
              {{ item.filters?item.filters[scope.row[item.prop]]:scope.row[item.prop] }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerHandleClose()">{{ $t('common.cancel') }}</el-button>
        <el-button v-if="innerTable.purpose==='chargeOff'" type="primary" @click="executeChargeOffDebit({method:innerTable.method,check:true})">{{ $t('common.chargeOff') }}</el-button>
        <el-button v-else type="primary" @click="executeChargeOffDebit({method:innerTable.method,check:true})">{{ $t('common.sure') }}</el-button>
      </span>
      <!--二层对话框表格分页组件-->
      <el-pagination
        v-if="dialogType === 'table'"
        :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
        :total="innerDialogTotal"
        :page-size="innerDialogSize"
        :current-page="innerDialogPage"
        style="margin-top: 8px;"
        layout="total, prev, pager, next, sizes"
        @size-change="innerSizeChangeDialog"
        @current-change="innerPageChangeDialog"/>
    </el-dialog>
    <!-- 多项搜索框 -->
    <el-dialog
      :visible="multiSearchShow"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :width="multiDiolagWidth"
      :title="$t('common.batchSearch')">
      <!-- 是否带入外部查询条件 -->
      <div>
        {{ $t('common.condition') }}
        <el-radio v-model="withFilter" label="0" style="margin-bottom: 10px;">{{ $t('common.radioFalse') }}</el-radio>
        <el-radio v-model="withFilter" label="1" style="margin-bottom: 10px;">{{ $t('common.radioTrue') }}</el-radio>
      </div>
      <!-- 类型 -->
      <el-radio v-for="(item,index) in formData.multiSearch" v-model="multiSearchType" :key="item.label+index" :label="item.prop" style="margin-bottom: 10px;">{{ item.label }}</el-radio>
      <!-- 值 -->
      <div v-for="item in formData.multiSearch" :key="item.prop">
        <el-input v-if="multiSearchType===item.prop" :autosize="{minRows:2, maxRows: 8}" v-model="item.value" :placeholder="`${$t('common.enter')}${item.label}，${$t('common.oneRecord')}`" type="textarea"/>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="getMultiSearchData()">{{ $t('common.search') }}</el-button>
      </span>
    </el-dialog>
    <!-- innerTable多项搜索框 -->
    <el-dialog
      :visible="innerMultiSearchShow"
      :before-close="innerTableHandleClose"
      :close-on-click-modal="false"
      :width="multiDiolagWidth"
      :title="$t('common.batchSearch')">
      <!-- 是否袋入外部查询条件 -->
      <div>
        {{ $t('common.condition') }}
        <el-radio v-model="innerTablewithFilter" label="0" style="margin-bottom: 10px;">{{ $t('common.radioFalse') }}</el-radio>
        <el-radio v-model="innerTablewithFilter" label="1" style="margin-bottom: 10px;">{{ $t('common.radioTrue') }}</el-radio>
      </div>
      <!-- 类型 -->
      <el-radio v-for="(item,index) in innerTable.multiSearch" v-model="innerTableMultiSearchType" :key="item.label+index" :label="item.prop" style="margin-bottom: 10px;">{{ item.label }}</el-radio>
      <!-- 值 -->
      <div v-for="item in innerTable.multiSearch" :key="item.prop">
        <el-input v-if="innerTableMultiSearchType===item.prop" :autosize="{minRows:2, maxRows: 8}" v-model="item.value" :placeholder="`${$t('common.enter')}${item.label}，${$t('common.oneRecord')}`" type="textarea"/>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerMultiSearchShow=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="innerTableGetMultiSearchData()">{{ $t('common.search') }}</el-button>
      </span>
    </el-dialog>
    <!--异步上传文件进度查询框-->
    <UploadListDialog
      :visible.sync="syncUploadDialogVisible"
      :polling-interval="3000"
      :on-close-callback="handleUploadDialogClose"
    />
    <!--    <el-dialog-->
    <!--      :visible="syncUploadDialogVisible"-->
    <!--      :before-close="syncUploadDialogClose"-->
    <!--      :close-on-click-modal="false"-->
    <!--      :width="fullScreen? '80%':'1300px'"-->
    <!--      title="文件上传列表">-->
    <!--      <div v-if="syncUploadForm.dialogType === 'table' && syncUploadForm.searchable" class="bottom clearfix" style="margin-bottom: 10px">-->
    <!--        <el-button :disabled="buttonDisabled" @click="refreshUploadProgress()">刷新</el-button>-->
    <!--        <div v-for="item in syncUploadForm.tableFilters" ref="inputContainer" :key="item.prop" :style="{ display: 'inline-block', float: item.type === 'input' ? 'right' : 'none' }" style="display: inline-block; ">-->
    <!--          &lt;!&ndash; 列表页头部下拉框 &ndash;&gt;-->
    <!--          <template v-if="item.type === 'select'">-->
    <!--            <el-select v-model="item.value" :no-data-text="$t('content.NoData')" :placeholder="$t('common.select')+' '+item.placeholder" :multiple="item.multiple" :class="{linghtUp:item.value}" style="width:132px;" clearable filterable @focus="getSelectData(item)" @change="dialogQuery(item.value)">-->
    <!--              <el-option-->
    <!--                v-for="(value, key) in item.filters"-->
    <!--                :key="key"-->
    <!--                :label="value"-->
    <!--                :value="key"/>-->
    <!--            </el-select>-->
    <!--          </template>-->
    <!--          &lt;!&ndash; 单选布尔值 &ndash;&gt;-->
    <!--          <template v-if="item.type === 'boolean'">-->
    <!--            <el-select v-model="item.value" :no-data-text="$t('content.NoData')" :class="{linghtUp:item.value===true||item.value===false}" :placeholder="item.placeholder" style="width:100px;" clearable filterable @change="dialogQuery(item.value)">-->
    <!--              <el-option :value="true" :label="$t('common.radioTrue')"/>-->
    <!--              <el-option :value="false" :label="$t('common.radioFalse')"/>-->
    <!--            </el-select>-->
    <!--          </template>-->
    <!--          &lt;!&ndash; 时间范围选择器 abandon &ndash;&gt;-->
    <!--        </div>-->
    <!--        &lt;!&ndash; 输入框查询 &ndash;&gt;-->
    <!--        <template>-->
    <!--          <div class="fr" style="display: inline-block">-->
    <!--            <el-input v-model="dialogSearch.value" :placeholder="syncUploadForm.dialogSearchHolder || $t('common.enter')" clearable style="width: 240px;" @keyup.enter.native="dialogQuery(dialogSearch.value)">-->
    <!--              <el-button slot="append" icon="el-icon-search" @click="dialogQuery(dialogSearch.value)"></el-button>-->
    <!--            </el-input>-->
    <!--          </div>-->
    <!--        </template>-->
    <!--      </div>-->
    <!--      &lt;!&ndash; 表格筛选 &ndash;&gt;-->
    <!--      <el-table ref="dialogTable" :data="tableData" :empty-text="$t('content.NoData')" max-height="320px" size="small" border style="width: 100%;" @selection-change="selectionChange">-->
    <!--        &lt;!&ndash;<el-table-column type="selection" fixed width="40"/>&ndash;&gt;-->
    <!--        <el-table-column v-for="item in syncUploadForm.columns" :key="item.prop" :label="item.label" :prop="item.prop" :width="item.width">-->
    <!--          <template slot-scope="scope">-->
    <!--            <div v-if="item.type === 'input'">-->
    <!--              <el-input v-model="scope.row[item.prop]" :placeholder="$t('common.enter')" type="number"/>-->
    <!--            </div>-->
    <!--            <div v-else-if="item.type === 'datetime'">-->
    <!--              {{ parseTime(scope.row[item.prop]) }}-->
    <!--            </div>-->
    <!--            <div v-else-if="item.type === 'time'">-->
    <!--              {{ removeMilliseconds(scope.row[item.prop]) }}-->
    <!--            </div>-->
    <!--            <div v-else>-->
    <!--              <span v-if="item.prop==='status'">-->
    <!--                <i v-if="scope.row[item.prop]==='Success'" style="color:green; font-size: 16px" class="el-icon-success"/>-->
    <!--                <i v-else-if="scope.row[item.prop]==='Failure' || scope.row[item.prop]==='VO'" style="color:#fa044e; font-size: 16px" class="el-icon-error"/>-->
    <!--                <i v-else class="el-icon-upload" style="color: #409eff; font-size: 16px"></i>-->
    <!--              </span>-->
    <!--              {{ item.filters?item.filters[scope.row[item.prop]]:scope.row[item.prop] }}-->
    <!--            </div>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--      </el-table>-->
    <!--      <span slot="footer" class="dialog-footer">-->
    <!--        <el-button @click="syncUploadDialogClose()">{{ $t('common.cancel') }}</el-button>-->
    <!--        <el-button type="primary" @click="syncUploadDialogClose()" >{{ $t('common.sure') }}</el-button>-->
    <!--      </span>-->
    <!--      &lt;!&ndash;异步上传文件进度查询框分页组件&ndash;&gt;-->
    <!--      <el-pagination-->
    <!--        v-if="syncUploadForm.dialogType === 'table'"-->
    <!--        :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"-->
    <!--        :total="dialogTotal"-->
    <!--        :page-size="dialogSize"-->
    <!--        :current-page="dialogPage"-->
    <!--        style="margin-top: 8px;"-->
    <!--        layout="total, prev, pager, next, sizes"-->
    <!--        @size-change="sizeChangeDialog"-->
    <!--        @current-change="pageChangeDialog"/>-->
    <!--    </el-dialog>-->
    <set-truck-order-dialog ref="setTruckOrderRef" :orders="formData.checkData"/>
    <trial-fee-dialog ref="trialFeeRef" :orders="formData.checkData" @toQuery="toQuery"/>
    <supplier-order-dialog
      ref="supplierOrderRef"
      :orders="formData.checkData"
    />
  </div>
</template>

<script>
import checkPermission from '@/utils/permission' // 权限判断函数
import { download_Template } from '@/utils'
import { axiosExport, parseTime, removeMilliseconds } from '@/utils/index'
import eForm from './form'
import { actionPost, del, getChoiceData, initMutilData } from '@/api/data'
import SetTruckOrderDialog from '../SetTruckOrderDialog'
import TrialFeeDialog from '../TrialFeeDialog'
import SupplierOrderDialog from '../SupplierOrderDialog'
import UploadListDialog from '@/components/Dialog/UploadListDialog'
// todo_a: 影响了this.filterProp, 如果不要这个则this.filterProp值为未定义
// import initData from '@/mixins/initData'
// 查询条件
export default {
  components: { eForm, SetTruckOrderDialog, TrialFeeDialog, SupplierOrderDialog, UploadListDialog },
  // mixins: [initData],
  props: {
    query: {
      type: Object,
      required: true
    },
    // innerTableQuery: {
    //   type: Object,
    //   required: true
    // },
    formData: {
      type: Object,
      required: true
    },
    isDetail: {
      type: String,
      default: ''
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    innerDialogVisible: {
      type: Boolean,
      default: false
    },
    role: {
      type: Array,
      default: () => { return [] }
    }
    // dialogSearch: {
    //   type: Object,
    //   required: true
    // }
  },
  data() {
    return {
      fixed: {
        isFixed: false,
        w: '100%'
      },
      withFilter: '0',
      innerTablewithFilter: '0',
      monthOptionDate: '',
      actionParams: {
        methods: '',
        data: {
          paperHeight: 50,
          paperWidth: 80,
          barcodeWidth: 0.9,
          barcodeHeight: 0.9,
          date: '',
          amount: '',
          selectData: [],
          selectActionVal: '',
          label: '',
          purpose: '',
          is_part_set: false,
          is_rebinding: false,
          overheadButton: '',
          overheadTitle: '',
          method: '',
          batchMethod: '',
          width: '',
          params: {}
        }
      },
      fileList: [],
      waitToUpload: new FormData(),
      innerTable: {},
      // innerTableQuery: {},
      dialogFormData: {},
      dialogForm: [],
      hiddenValue: null,
      // dialogFormRules: {},
      tableData: [],
      tableDataQuery: null,
      innerTableData: [],
      innerDialogUrl: null,
      colums: [],
      dialogType: '',
      type: '',
      hiddenColums: [],
      headerBtn: false,
      searchable: false,
      fullScreen: false,
      noQty: false,
      noTrackCode: false,
      dateType: 'datetime',
      downloadLoading: false,
      // 是否默认
      defaultCheck: null,
      multiSearchShow: false,
      innerMultiSearchShow: false,
      innerMultiParams: {},
      multiSearchData: [],
      multiSearchType: '',
      innerTableMultiSearchType: '',
      // 隐藏式上传操作的按钮
      currentUploadBtn: {},
      dialogTotal: null,
      dialogPage: 1,
      dialogSize: 10,
      dialogOrdering: '-id',
      innerDialogTotal: null,
      innerDialogPage: 1,
      innerDialogSize: 10,
      innerDialogOrdering: '-id',
      dialogUrl: null,
      // dialogApi: null,
      dialogTableFilters: [],
      innerDialogTableFilters: [],
      dialogTableParams: {},
      innerDialogTableParams: {},
      dialogSearch: { value: '' },
      innerDialogSearch: { value: '' },
      dialogSearchHolder: '',
      progressTimer: null,
      elapsedTime: 0,
      syncUploadDialogVisible: false,
      syncUploadTable: {},
      syncUploadTableData: [],
      lastClickTime: 0,
      isCooldown: false,
      clickOverheadButton: false,
      fold: false,
      filterProp: {},
      secondConfirmMethod: null,
      orderStowageType: 0,
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      nodeAddress: {
        required: false,
        address_name: null,
        address_name_en: null,
        msg: ''
      }
    }
  },
  computed: {
    sysFlag() {
      console.log('process.env.SYS_FLAG是个啥-->', process.env.SYS_FLAG)
      return process.env.SYS_FLAG || ''
    },
    host() {
      return '//' + (location.host.replace('shipping', 'manage'))
    },
    multiDiolagWidth() {
      const w = this.formData.multiSearch && this.formData.multiSearch.length * 125
      return (w < 500 ? 500 : w > 1000 ? 1000 : w) + 'px'
    },
    // 异步上传文件进度弹窗
    // syncUploadForm() {
    //   return {
    //     dialogType: 'table',
    //     // 功能待补充
    //     searchable: true,
    //     dialogSearchHolder: '文件名/订单号',
    //     columns: [
    //       { label: '上传时间', prop: 'create_date', type: 'datetime', width: '140px' },
    //       { label: '文件名', prop: 'file_name', width: '260px' },
    //       { label: '业务单据号', prop: 'order_num' },
    //       // { label: 'md5', prop: 'file_md5' },
    //       { label: '单据类型', prop: 'order_type', type: 'select',
    //         filters: { 'TR': '运输', 'CL': '清关', 'PC': '小包', 'OW': '海外仓', 'BP': '大包'
    //         }, width: '75px'
    //       },
    //       { label: '任务类型', prop: 'task_type', type: 'select',
    //         filters: { 'UploadOrderExcel': '导入订单', 'UploadParcelVoucher': '上传包裹面单',
    //           'BatchWeighting': '批量称重', 'BatchPick': '批量装箱'
    //         }, width: '75px'
    //       },
    //       { label: '处理用时', prop: 'execution_time', type: 'time', width: '80px' },
    //       { label: '任务描述', prop: 'task_desc', width: '250px' },
    //       {
    //         label: '上传状态', prop: 'status', type: 'select',
    //         filters: {
    //           'Waiting': '等待中', 'Processed': '处理中', 'Success': '处理成功', 'Failure': '处理失败',
    //           'VO': '已作废'
    //         }, width: '100px'
    //       }
    //     ],
    //     tableFilters: [
    //       {
    //         prop: 'status',
    //         type: 'select',
    //         placeholder: '上传状态',
    //         filters: {
    //           'Waiting': '等待中', 'Processed': '处理中', 'Success': '处理成功', 'Failure': '处理失败',
    //           'VO': '已作废'
    //         }
    //       }
    //     ]
    //   }
    // },
    buttonDisabled() {
      // 计算是否禁用按钮
      return this.isCooldown || (Date.now() - this.lastClickTime < 3000)
    }
  },
  watch: {
    '$route'(n, o) {
      if (this.multiSearchShow) this.multiSearchShow = false
      if (this.dialogVisible) this.handleClose()
    }
  },
  created() {
    if (this.formData.multiSearch && this.formData.multiSearch.length) {
      this.formData.multiSearch.forEach(i => {
        this.$set(i, 'value', '')
      })
      this.multiSearchType = this.formData.multiSearch[0].prop
      this.dialogType = this.formData.dialogType
    }
  },
  mounted() {
    window.addEventListener('scroll', this.getScrollTop)
  },
  methods: {
    checkPermission,
    parseTime,
    removeMilliseconds,
    handleUploadDialogClose() {
      this.$parent.init()
    },

    clickRedioButton(item, isShow) {
      console.log('this.dialogForm00000-->', this.dialogForm, item, isShow)
      console.log('this.dialogFormData111111-->', this.dialogFormData)

      if (!isShow) {
        if (this.hiddenValue) {
          this.dialogForm.push(this.hiddenValue)
          this.hiddenValue = null
          this.dialogForm.forEach(i => {
            if (i.inherit) {
              this.$set(this.dialogFormData, i.prop, this.$parent.formData.checkData[0][i.prop])
            }
            if (i.defaultValue) {
              this.$set(this.dialogFormData, i.prop, i.defaultValue)
            } else if (i.getDefaultValue) {
              // 如果有 getDefaultValue 函数，调用它获取最新的默认值
              this.$set(this.dialogFormData, i.prop, i.getDefaultValue())
            }
          })
        }
      } else {
        console.log('77777777777777')
        this.dialogForm.forEach(i => {
          console.log(i, '888888888888')
          if (i && i.prop === item) {
            this.hiddenValue = this.dialogForm.pop(i)
          }
        })
      }
    },
    sentRequest(api, params = {}) {
      // 发送请求
      getChoiceData(api, params).then(res => {
        if (res.code === 200) {
          console.log('成功获取数据')
        } else if (res.code === 404) {
          console.log('没有搜索到数据')
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
        this.tableData = res.data
        this.dialogTotal = res.count
        this.bus.$emit('fullLoading', false)
      }).catch((error) => {
        this.$message.error(error)
        this.bus.$emit('fullLoading', false)
      })
      // this.resetUrl()
    },
    sentRequestInner(api, params = {}) {
      // 发送请求
      getChoiceData(api, params).then(res => {
        if (res.code === 200) {
          console.log('成功获取数据')
        } else if (res.code === 404) {
          console.log('没有搜索到数据')
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
        // const filters = {}
        // console.log('啥情况-->', filters)
        // // 获取数据后的过滤
        // Object.keys(filters || {}).forEach(i => {
        //   res.data = res.data.filter(k => {
        //     if (filters[i] instanceof Array) {
        //       let flag = false
        //       filters[i].forEach(j => {
        //         flag = flag || k[i] === j
        //       })
        //       return flag
        //     } else {
        //       return k[i] === filters[i]
        //     }
        //   })
        // })
        this.innerTableData = res.data
        console.log('怎么回事-->', this.innerTableData)
        this.innerDialogTotal = res.count
        this.bus.$emit('fullLoading', false)
      }).catch((error) => {
        this.$message.error(error)
        this.bus.$emit('fullLoading', false)
      })
      // this.resetUrl()
    },
    toQuery(mutil = false, mutiData) {
      console.log('toQuery-->')
      this.lastSearch = 'single'
      sessionStorage.setItem('isMulti', '0')
      sessionStorage.setItem('withFilter', this.withFilter)
      if (mutil) {
        this.$parent.page = 1
        this.$parent.multiInit(mutiData, this.withFilter)
        this.multiSearchShow = false
        sessionStorage.setItem('isMulti', '1')
        return
      }
      this.$parent.page = 1
      this.$parent.init()
    },
    resetQuery() {
      // // 1. 重置顶部搜索框
      // this.query.value = ''
      this.$set(this.query, 'value', '')

      // 2. 重置所有过滤器
      this.formData.filters.forEach(item => {
        // item.value = ''
        this.$set(item, 'value', '')
      })
      // 3. 多项查询置空
      sessionStorage.setItem('multiParams', JSON.stringify({}))
      this.formData.multiSearch.forEach(item => {
        // item.value = ''
        this.$set(item, 'value', '')
      })

      // 3. 特殊处理需要保留的过滤器（示例）
      // this.formData.filters.find(f => f.prop === 'status').value = 'active'

      // 4. 重置分页和触发查询（保持原有逻辑）
      if (this.$parent.page !== undefined) {
        this.$parent.page = 1
      }
      this.toQuery()
    },
    dialogQuery(search = true) {
      if (search) {
        this.dialogPage = 1
      }
      // let api
      // if (this.dialogUrl.includes('?')) {
      //   api = this.dialogUrl + `&page=${this.dialogPage}&size=${this.dialogSize}&ordering=${this.dialogOrdering}`
      // } else {
      //   api = this.dialogUrl + `?page=${this.dialogPage}&size=${this.dialogSize}&ordering=${this.dialogOrdering}`
      // }
      // 对query进行处理, $代表从选中的行取值
      const t_query = Object.assign({}, this.tableDataQuery)
      Object.keys(t_query).forEach(i => {
        if (typeof (t_query[i]) === 'string' && t_query[i].startsWith('$')) {
          t_query[i] = this.$parent.formData.checkData[0][this.tableDataQuery[i].slice(1)]
          // flag = this.$parent.formData.checkData.every(k => k[query[i].slice(1)] === t_query[i])
        } else if (typeof (t_query[i]) === 'string' && t_query[i].endsWith('$')) {
          // 传选中id
          t_query[i] = this.actionQuery.checkData[0].id
        }
      })
      console.log('t_query-->', t_query)
      // 组装分页参数
      this.dialogTableParams = {
        page: this.dialogPage,
        size: this.dialogSize,
        ordering: this.dialogOrdering,
        ...t_query
      }
      // 组装查询参数
      this.dialogTableParams['search'] = this.dialogSearch.value
      // 过滤器
      console.log('this.dialogTableFilters-->', this.dialogTableFilters)
      this.dialogTableFilters && this.dialogTableFilters.forEach(i => {
        console.log('i.prop-->', i.prop)
        if (typeof i.value === 'boolean' || i.value) {
          this.dialogTableParams[i.prop] = (i.value).toString()
          this.filterProp[i.prop] = (i.value).toString()
        } else {
          // todo_a
          console.log('this.filterProp456-->', this.filterProp, i.prop)
          this.$delete(this.filterProp, i.prop)
        }
      })
      this.syncUploadForm.tableFilters && this.syncUploadForm.tableFilters.forEach(i => {
        if (typeof i.value === 'boolean' || i.value) {
          this.dialogTableParams[i.prop] = (i.value).toString()
        } else {
          // this.$delete(this.filterProp, i.prop)
        }
      })
      console.log('this.dialogTableParams-->', this.dialogTableParams)
      // this.sentRequest(this.dialogApi, this.dialogTableParams)
      this.sentRequest(this.dialogUrl, this.dialogTableParams)
    },
    innerDialogPageChange(queryParams) {
      this.innerDialogQuery(queryParams, Object.keys(this.innerMultiParams).length !== 0)
    },
    innerDialogQuery(queryParams, mutil = false) {
      console.log('queryParams-->', queryParams, mutil)
      console.log('this.innerTable.query-->', this.innerTable.query)
      const t_query = Object.assign({}, this.innerTable.query)
      // 对query进行处理,$代表从选中的行取值
      // let flag = true
      Object.keys(t_query).forEach(i => {
        if (typeof (t_query[i]) === 'string' && t_query[i].startsWith('$')) {
          t_query[i] = this.innerTableData[this.innerTable.query[i].slice(1)]
          // flag = this.$parent.formData.checkData.every(k => k[query[i].slice(1)] === t_query[i])
        } else if (typeof (t_query[i]) === 'string' && t_query[i].endsWith('$')) {
          // 传选中id
          t_query[i] = this.actionQuery.checkData[0].id
        }
      })
      // innerTableRowId
      // const mutilParams = {
      //   ...queryParams
      // }
      if (mutil) {
        this.innerMultiParams = queryParams
        this.$parent.page = 1
        this.innerMultiInit(queryParams, this.innerTablewithFilter)
        this.innerMultiSearchShow = false
        return
      } else {
        this.innerMultiParams = {}
      }
      // this.innerDialogTableParams
      this.innerDialogTableParams = {
        page: this.innerDialogPage,
        size: this.innerDialogSize,
        ordering: this.innerDialogOrdering,
        ...t_query
      }
      this.innerDialogTableParams['search'] = this.innerDialogSearch.value
      // 过滤器
      // this.innerDialogTableFilters && this.innerDialogTableFilters.forEach(i => {
      //   if (typeof i.value === 'boolean' || i.value) {
      //     params[i.prop] = (i.value).toString()
      //     this.filterProp[i.prop] = (i.value).toString()
      //   } else {
      //     this.$delete(this.filterProp, i.prop)
      //   }
      // })
      this.innerDialogTableFilters && this.innerDialogTableFilters.forEach(i => {
        console.log('i.prop-->', i.prop)
        if (typeof i.value === 'boolean' || i.value) {
          this.innerDialogTableParams[i.prop] = (i.value).toString()
          this.filterProp[i.prop] = (i.value).toString()
        } else {
          this.$delete(this.filterProp, i.prop)
        }
      })
      this.sentRequestInner(this.innerDialogUrl, this.innerDialogTableParams)
    },
    innerMultiInit(mutiData, withFilter = '0') {
      this.loading = true
      const params = {
        multiData: mutiData,
        page: this.innerDialogPage,
        size: this.innerDialogSize
      }
      if (withFilter === '1') {
        // 带过滤条件
        this.innerTable && this.innerTable.filters && this.innerTable.filters.forEach(i => {
          if (typeof i.value === 'boolean' || i.value) {
            this.innerDialogTableParams[i.prop] = (i.value).toString()
            this.filterProp[i.prop] = (i.value).toString()
          } else {
            this.$delete(this.filterProp, i.prop)
          }
        })
        params['filterProp'] = this.filterProp
      }
      initMutilData(`${this.innerTable.api}`, params).then(res => {
        // this.total = res.count
        this.innerTableData = res.data
        this.innerDialogTotal = res.count
        setTimeout(() => { this.loading = false }, 200)
      }).catch(err => {
        this.loading = false
        console.log(err)
      })
    },
    pageChangeDialog(e) {
      // this.page = e
      // this.init()
      this.dialogPage = e
      // this.sentRequest(api, this.dialogTableParams)
      this.dialogQuery(false)
    },
    sizeChangeDialog(e) {
      // this.page = 1
      // this.size = e
      // this.init()
      this.dialogPage = 1
      this.dialogSize = e
      this.dialogQuery(false)
    },
    innerPageChangeDialog(e) {
      this.innerDialogPage = e
      this.innerDialogPageChange(this.innerMultiParams)
    },
    innerSizeChangeDialog(e) {
      this.innerDialogPage = 1
      this.innerDialogSize = e
      this.innerDialogPageChange(this.innerMultiParams)
    },
    // 按钮权限设置
    authButtons(item) {
      if (this.$store.state.user.name === 'admin') {
        return true
      } else if (item.type === 'selectBtn') {
        return item.btns.some(i => !i.auth || (i.auth && this.$store.state.user.btns.includes(i.auth)))
      } else {
        return !item.auth || this.$store.state.user.btns.includes(item.auth)
      }
    },
    startProgressTimer() {
      this.elapsedTime = 30 * 60 * 1000 // 最多展示30分钟
      this.progressTimer = setInterval(() => {
        // const showProgressApi = this.$parent.apiUrl + 'upload_tasks_progress/'
        this.dialogUrl = 'api/uploadTasksProgress/upload_tasks_progress/'
        getChoiceData(this.dialogUrl, {}).then(res => {
          if (res.code === 200) {
            // console.log('查询上传进度:\n', res.data)
            // this.syncUploadTableData = res.data
            this.tableData = res.data
            this.dialogTotal = res.count
            var closeFlag = true
            for (const i of res.data) {
              if (['Waiting', 'Processed'].includes(i.status)) {
                closeFlag = false
              }
            }
            if (closeFlag || res.unfinished === 0) {
              if (this.elapsedTime > 10000) {
                this.elapsedTime = 10000
              }
            }
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
        }).catch((err) => { this.$message.error(err) })
        // 更新已经过的时间
        // this.elapsedTime += 1000
        console.log('this.elapsedTime-->', this.elapsedTime)
        this.elapsedTime -= 1000
        // 如果已经过的时间超过了最长持续时间，停止定时任务
        if (this.elapsedTime <= 0) {
          clearInterval(this.progressTimer)
        }
      }, 1000) // 每秒执行一次，以毫秒为单位
    },
    // syncUploadDialogClose() {
    //   console.log('关闭访问')
    //   clearInterval(this.progressTimer) // 停止定时器
    //   this.syncUploadDialogVisible = false
    //   this.$parent.init()
    // },
    // EXCEL导入
    importExcel(data, name) {
      console.log('data, name22-->', data, name)
      if (name === true) {
        data.item = this.currentUploadBtn
      } else {
        data.item = this.formData.action.find(i => i.import && i.name === name)
      }
      this.$emit('uploadExcel', data)
      if (!this.syncUploadDialogVisible && (data.item.method.startsWith('upload_excel_sync') || data.item.method.startsWith('upload_file_sync'))) {
        this.dialogSearch.value = data.file.name
        // this.colums = this.syncUploadForm.columns
        this.syncUploadDialogVisible = true
        this.dialogUrl = 'api/uploadTasksProgress/upload_tasks_progress/'
        // 轮询效率差, 改为用户点击刷新
        // this.startProgressTimer()
        this.getUploadProgressPolling()
      }
      if (name) {
        this.$refs.hiddenUpload.clearFiles()
      } else {
        this.$refs.uploadExcel[0].clearFiles()
      }
    },
    // 开始轮询
    getUploadProgressPolling() {
      // this.getUploadChoiceData()
      this.dialogQuery(this.dialogSearch.value)
      this.syncUploadDialogVisible = true
      this.startTime = Date.now() // 记录开始时间
      this.pollingInterval = setInterval(async() => {
        try {
          // 检查是否超时（10 分钟）
          if (Date.now() - this.startTime > 10 * 60 * 1000) {
            this.endUploadProgressPolling()
            console.log('轮询超时，已停止')
            return
          }

          // 检查对话框是否关闭
          if (!this.syncUploadDialogVisible) {
            this.endUploadProgressPolling()
            console.log('对话框已关闭，停止轮询')
            return
          }

          // 查询后端上传结果
          // if (this.tableData.length > 0) {
          //   if (this.tableData[0].file_name) {
          //     this.dialogSearch.value = this.tableData[0].file_name // 自动填充的值
          //     this.dialogQuery(this.dialogSearch.value)
          //   }
          // }
          this.dialogQuery(this.dialogSearch.value)
        } catch (error) {
          console.error('轮询出错:', error)
          this.endUploadProgressPolling()
        }
      }, 3000) // 每 3 秒轮询一次
    },
    // 停止轮询
    endUploadProgressPolling() {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval) // 清除定时器
        this.pollingInterval = null
      }
    },
    // refreshUploadProgress() {
    //   if (!this.buttonDisabled) {
    //     this.lastClickTime = Date.now()
    //     this.isCooldown = true
    //
    //     // 模拟按钮冷却结束后恢复可点击状态
    //     setTimeout(() => {
    //       this.isCooldown = false
    //     }, 3000) // 3秒后恢复按钮可点击
    //   }
    //   this.dialogQuery()
    // },
    getUploadProgress() {
      if (!this.buttonDisabled) {
        this.lastClickTime = Date.now()
        this.isCooldown = true

        // 模拟按钮冷却结束后恢复可点击状态
        setTimeout(() => {
          this.isCooldown = false
        }, 3000) // 3秒后恢复按钮可点击
      }
      // this.lastClickTime = Date.now()
      this.getUploadChoiceData()
      setTimeout(() => {
        this.getUploadChoiceData()
      }, 3000)
      this.syncUploadDialogVisible = true
    },
    getUploadChoiceData() {
      getChoiceData(this.dialogUrl, {}).then(res => {
        if (res.code === 200) {
          console.log('查询上传进度:\n', res.data)
          // this.syncUploadTableData = res.data
          this.tableData = res.data
          this.dialogTotal = res.count
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
      }).catch((err) => { this.$message.error(err) })
    },
    // 按钮操作
    execute(btn, isSelectBtn = false) {
      console.log('header execute-->', this.formData)
      let item = null
      // 下拉按钮
      if (isSelectBtn) {
        item = btn.btns.find(i => {
          return i.method === btn.value
        })
        console.log('execute item', item)
        console.log('execute btn-->', btn)
        if (item.method.startsWith('upload_excel')) {
          // 下拉上传按钮
          this.currentUploadBtn = item
          document.querySelector('#hiddenUpload')
          document.querySelector('#hiddenUpload').click()
          btn.value = ''
          return
        } else if (item.method.startsWith('upload_file')) {
          // 下拉多文件上传按钮
          this.currentUploadBtn = item
          document.querySelector('#hiddenUploadFile')
          document.querySelector('#hiddenUploadFile').click()
          btn.value = ''
          return
        }
        // 重置下拉按钮框
        btn.value = ''
      } else {
        item = btn
      }
      const { fullScreen, defaultCheck, method, dialogProp, dialogType, check, api, query, filters, colums, onlyOne, setProp, not_checked, not_data, noQty, noTrackCode, dateType, hiddenColums, headerBtn, searchable, dialogSearchHolder } = item
      if (btn.type === 'secondConfirm') {
        this.secondConfirmMethod = btn.secondConfirmMethod
      }
      console.log('dialog item:', item)
      // 导出模板
      if (item.method && item.method.startsWith('download_template') && item.link) {
        // if (process.env.SYS_FLAG === 'yqf' && item.method.startsWith('download_template_fba_order')) {
        //   const newLink = item.link.replace('.xlsx', '_yqf.xlsx')
        //   download_Template(newLink, process.env.HOST)
        //   return
        // } else {
        download_Template(item.link, process.env.HOST)
        return
        // }
      } else if (item.type === 'quick_bi') {
        // quick bi
        actionPost({
          api: '/api/supplierButtBI/get_statement_url/',
          data: { 'system_url': process.env.HOST, 'statement_code': item.statement_code }
        }).then(res => {
          if (res.code === 200) {
            window.open(res.url, '_blank')
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
        }).catch(err => {
          this.$message.error(err)
        })
        return
      }
      if (btn.dialogType === 'selectAciton' && !check) {
        // 默认为当前时间
        if (btn.method === 'change_track') {
          this.actionParams.data.date = this.getCurrentNow(true)
          console.log(this.actionParams.data.date)
        } else {
          this.actionParams.data.date = this.getCurrentNow(btn.dateType === 'datetime')
        }
        this.actionParams.data.selectActionVal = ''
      }
      // if (item.query) {
      //   this.actionQuery = item.query
      // }
      // if (item.innerTable) {
      //   this.innerTableQuery = item.innerTable.query
      // }
      console.log('当前的type-->', item.type)
      if (api) {
        this.dialogUrl = api
      }
      // 弹窗点击确定
      if (check) {
        if (this.dialogType === 'date' && (!this.actionParams.data.date || !this.actionParams.data.amount)) {
          this.$message.error('请输入金额和选择日期！')
          return
        }
        // 表格判断
        if (this.dialogType === 'table' && this.actionParams.data.selectData.length === 0 && !this.defaultCheck) {
          this.$message.error(this.$t('content.pleaseSelect'))
          return
        }
        // 轨迹判断
        if (this.dialogType === 'selectAciton' && !this.actionParams.data.selectActionVal && !this.noQty) {
          this.$message.error('请选择操作项！')
          return
        }
        if (this.dialogType === 'selectAciton' && !this.actionParams.data.date) {
          this.$message.error('请选择日期')
          return
        }
      }
      if (dialogProp) {
        console.log('execute dialogProp start-->', this.formData)
        this.actionParams.data.date = this.getCurrentNow(item.dateType === 'datetime')
        this.actionParams.data.label = item.name
        this.actionParams.data.purpose = item.purpose || ''
        this.actionParams.data.overheadButton = item.overheadButton
        this.actionParams.data.overheadTitle = item.overheadTitle
        this.actionParams.data.showRebinding = item.showRebinding
        this.actionParams.data.method = item.method
        this.actionParams.data.batchMethod = item.batchMethod
        this.actionParams.data.width = item.width
        this.innerTable = item.innerTable || {}
        this.innerDialogTableFilters = this.innerTable.innerTableFilters
        if (this.innerTable.multiSearch && this.innerTable.multiSearch.length) {
          this.innerTableMultiSearchType = this.innerTable.multiSearch[0].prop
          console.log('2-->', this.innerTableMultiSearchType)
        }
        console.log('selected data:', this.$parent.formData.checkData)
        const selectLen = this.$parent.formData.checkData.length
        if (selectLen === 0 && !not_checked) {
          this.$message.error('请选择要操作的条目')
          return
        }
        if (item.dialogType === 'customAction') {
          this.$refs[item.refName].open()
          return
        }
        // 弹窗确认的时候不经过这里
        if (onlyOne && selectLen !== 1) {
          this.$message.error('请选择一条进行操作')
          return
        }
        // 是否默认带出数值
        console.log('set default value:', setProp)
        if (setProp) {
          setProp.forEach(i => {
            this.actionParams.data[i.prop] = this.$parent.formData.checkData[0][i.data]
          })
        }

        // item.selectSameDataRestrict.forEach(i => {
        //   this.$parent.formData.checkData.filter((item, index) => {
        //     const different = this.$parent.formData.checkData.find((x, inner_index) => x[i.prop] !== item[i.prop] && inner_index !== index)
        //     console.log('different-->', different)
        //     if (different) {
        //       this.$message.error(`选择的数据存在不同的${i.prop}: ${item[i.prop]} 和 ${different[i.prop]}`)
        //       return true
        //     }
        //   })
        // })

        // 是否限制勾选数据
        console.log('selectSameDataRestrict:', setProp)
        if (item.selectSameDataRestrict) {
          let shouldStop = false // 定义一个标志变量
          item.selectSameDataRestrict.forEach(i => {
            if (shouldStop) return // 如果标志变量为true，停止遍历
            for (let index = 0; index < this.$parent.formData.checkData.length; index++) {
              const item = this.$parent.formData.checkData[index]
              const different = this.$parent.formData.checkData.find((x, inner_index) => x[i.prop] !== item[i.prop] && inner_index !== index)
              if (different) {
                this.$message.error(`选择的数据存在不同的${i.label}: ${item[i.prop]} 和 ${different[i.prop]}`)
                shouldStop = true // 设置标志变量为true，停止后续遍历
                return // 退出当前循环
              }
            }
          })
          if (shouldStop) return
        }

        // 显示弹窗
        this.$emit('toggleDialog', true)
        this.dialogType = dialogType
        this.fullScreen = !!fullScreen
        this.noQty = noQty
        this.noTrackCode = noTrackCode
        this.dateType = dateType
        if (hiddenColums) {
          this.hiddenColums = hiddenColums
        }
        if (dialogSearchHolder) {
          this.dialogSearchHolder = dialogSearchHolder
        }
        this.headerBtn = headerBtn
        this.searchable = searchable
        if (item.dialogForm && item.dialogForm.length) {
          this.dialogForm = item.dialogForm
          item.dialogForm.forEach(i => {
            this.$set(this.dialogForm, i.prop, '')
            // if (i.required) {
            //   // 必填
            //   const message = ['select', 'currency', 'date', 'datetime', 'radio'].includes(i.type) ? this.$t('content.pleaseSelect') : '请输入'
            //   const trigger = ['select', 'currency', 'date', 'datetime', 'radio'].includes(i.type) ? 'change' : 'blur'
            //   this.$set(this.dialogFormRules, i.prop, [{ required: true, message, trigger }])
            // }
          })
        }
        // 对弹框数据进行赋值
        if (item.dialogForm && item.dialogForm.length) {
          this.dialogForm = item.dialogForm
          item.dialogForm.forEach(i => {
            if (i.inherit) {
              // this.$set(this.dialogForm, i.prop, this.$parent.formData.checkData[0][i.prop])
              // this.actionParams.data[i.prop] = this.$parent.formData.checkData[0][i.prop]

              // 这种方式赋值会导致输入框内的数据无法修改
              // this.dialogFormData[i.prop] = this.$parent.formData.checkData[0][i.prop]
              this.$set(this.dialogFormData, i.prop, this.$parent.formData.checkData[0][i.prop])
            }
            if (i.defaultValue) {
              this.$set(this.dialogFormData, i.prop, i.defaultValue)
            } else if (i.getDefaultValue) {
              // 如果有 getDefaultValue 函数，调用它获取最新的默认值
              this.$set(this.dialogFormData, i.prop, i.getDefaultValue())
            }
          })
        }
        console.log('this.dialogForm1-->', this.dialogForm, item.dialogForm)
        // 对 dialogTableFilters 进行赋值
        this.dialogSearch.value = ''
        // this.dialogTableFilters = []
        if (item.tableFilters) {
          this.dialogTableFilters = item.tableFilters
        }
        // this.dialogTableFilters.forEach(i => {
        //   i.value = ''
        // })

        this.actionParams.methods = method
        const mainCheckTemp = []
        if (item.mainCheck === true) {
          // console.log('2-->', this.$parent.formData.checkData)
          this.$parent.formData.checkData.forEach(i => {
            // console.log('i-->', i)
            // mainCheck = mainCheck + i.id
            mainCheckTemp.push(i.id)
          })
        }
        const mainCheck = mainCheckTemp.join(',')
        // 判断colums是否从接口获取
        if (typeof colums === 'string') {
          // 接口获取
          this.getTableData(colums, query, {}, false, 'colums')
        } else {
          this.colums = colums
        }
        // 设置是否默认
        this.defaultCheck = defaultCheck
        // 获取表格数据
        if (api) this.getTableData(api, query, filters, defaultCheck, null, mainCheck)
        console.log('execute dialogProp end-->', this.formData)
      } else {
        console.log('execute not dialogProp start-->', this.formData)
        let current_method = ''
        if (this.clickOverheadButton) {
          current_method = this.actionParams.data.batchMethod
        } else {
          current_method = method
        }

        if (query) {
          this.actionParams.data.params = query
        }

        const params = { method: current_method, data: this.actionParams.data }
        if (item.fullMethod) {
          params['fullMethod'] = item.fullMethod
        }
        console.log('222-->', this.dialogType)
        let valid_flag = true
        if (this.dialogType === 'form') {
          this.$refs.headerForm.validate((valid) => {
            if (valid) {
              Object.keys(this.dialogFormData).forEach(i => {
                params['data'][i] = this.dialogFormData[i]
              })
              if (this.$parent.formData.checkData) params['ids'] = this.$parent.formData.checkData.map(i => i.id)
            } else {
              valid_flag = false
            }
          })
        } else if (not_checked && !not_data) {
          this.formData && this.formData.filters && this.formData.filters.forEach(i => {
            params[i.prop] = i.value || (i.type.includes('date') ? [] : '')
          })
          if (this.$parent.formData && this.$parent.formData.checkData && this.$parent.formData.checkData.length > 0) {
            params['ids'] = this.$parent.formData.checkData.map(i => i.id)
          } else {
            params['ids'] = [2, 3, 3]
          }
        } else if (not_checked && not_data) {
          if (this.$parent.formData && this.$parent.formData.checkData && this.$parent.formData.checkData.length > 0) {
            params['ids'] = this.$parent.formData.checkData.map(i => i.id)
          } else {
            params['ids'] = [2, 3, 3]
          }
        } else {
          params['ids'] = this.$parent.formData.checkData.map(i => i.id)
        }
        console.log('当前params-->', params, valid_flag)

        // 验证节点地址字段
        if (this.nodeAddress.required) {
          if (!this.nodeAddress.address_name) {
            this.$message.error(this.nodeAddress.msg)
            return
          }
          if (!this.nodeAddress.address_name_en) {
            this.$message.error('节点地址英文名必填(用于亚马逊轨迹对接)')
            return
          }
          // 将节点地址数据添加到参数中
          params.data.nodeAddress = this.nodeAddress
        }

        if (!valid_flag) return

        console.log('this.secondConfirmMethod-->', this.secondConfirmMethod)
        if (this.secondConfirmMethod) {
          console.log('能拿到子组件数据吗-->', this.$parent.apiUrl)
          const ids = this.$parent.formData.checkData.map(i => i.id)
          actionPost({
            api: this.$parent.apiUrl + this.secondConfirmMethod + '/',
            data: { ids }
          }).then(res => {
            console.log('弹出二次确认', res.code)
            if (res.code === 202) {
              this.$confirm(res.msg, this.$t('common.tips'), {
                confirmButtonText: this.$t('common.sure'),
                cancelButtonText: this.$t('common.cancle'),
                type: 'warning'
              }).then(() => {
                this.$emit('execute', params)
                this.clearParams()
                this.$emit('toggleDialog', false)
              }).catch(() => {
                this.$emit('toggleDialog', false)
              })
            } else if (res.code === 200) {
              this.$emit('execute', params)
              this.clearParams()
              this.$emit('toggleDialog', false)
            } else {
              this.$message.error(res.msg || res.detail || res.message)
              this.$emit('toggleDialog', false)
            }
          }).catch(err => {
            this.$message.error(err)
            this.$emit('toggleDialog', false)
          })
          this.secondConfirmMethod = null
        } else {
          // 执行当前页面下的execute方法
          console.log('this.$emit execute------------', params)
          if (params.method === 'one_click_export' || params.method === 'export_order_report') {
            params['data']['finalSearchObj'] = this.getFinalSearchConditions()
          }
          this.$emit('execute', params)
          this.clearParams()
          this.$emit('toggleDialog', false)
          console.log('execute not dialogProp end-->', this.formData)
        }
      }
      console.log('execute end-->', this.formData)
    },
    getFinalSearchConditions() {
      // console.log('顶部单号等查询------------------', this.query)
      // console.log('多项查询------------------', this.formData.multiSearch)
      // console.log('过滤查询------------------', this.formData.filters)
      const filtersObj = {}
      this.formData.filters.forEach(i => {
        if (i.value !== '' && i.value !== undefined) {
          filtersObj[i.prop] = i.value
          // console.log('过滤查询------------------', i.prop, i.value)
        }
      })
      const multiSearchObj = {}
      this.formData.multiSearch.forEach(i => {
        if (i.value !== '' && i.value !== undefined) {
          multiSearchObj[i.prop] = i.value
          // console.log('多项查询------------------', i.prop, i.value)
        }
      })

      // console.log('multiSearchObj-----------', multiSearchObj)
      // console.log('this.query.value', this.query.value)
      const queryObj = {}
      if (this.query !== undefined && this.query.value !== undefined && this.query.value !== '') {
        queryObj['search'] = this.query.value
      }
      // console.log('queryObj-----------', queryObj)

      const finalSearchObj = {}
      const storageIsMulti = sessionStorage.getItem('isMulti')
      // console.log('execute storageIsMulti-->', storageIsMulti)
      const storagewithFilter = sessionStorage.getItem('withFilter')
      // console.log('execute storagewithFilter-->', storagewithFilter)
      // 如果当前是多项查询且多项查询有填写查询值
      if (storageIsMulti === '1' && Object.keys(multiSearchObj).length > 0) {
        Object.assign(finalSearchObj, multiSearchObj)
        if (storagewithFilter === '1') {
          Object.assign(finalSearchObj, filtersObj)
        }
      } else {
        Object.assign(finalSearchObj, filtersObj, queryObj)
      }
      console.log('execute finalSearchObj-->', finalSearchObj)

      return finalSearchObj
    },
    overheadClick(btn, isSelectBtn = false) {
      this.clickOverheadButton = true
      this.execute(btn, isSelectBtn)
      this.clickOverheadButton = false
    },
    clearParams() {
      console.log('执行clearParams')
      this.dialogFormData = {}
      this.orderStowageType = 0
      this.actionParams.data = {
        paperHeight: 50,
        paperWidth: 80,
        barcodeWidth: 0.9,
        barcodeHeight: 0.9,
        date: '',
        amount: '',
        selectData: [],
        selectActionVal: '',
        label: '',
        purpose: '',
        is_part_set: false,
        is_rebinding: false,
        overheadButton: '',
        overheadTitle: '',
        method: '',
        batchMethod: '',
        params: {}
      }
      // 重置节点地址字段
      this.nodeAddress = {
        required: false,
        address_name: null,
        address_name_en: null
      }
      if (this.dialogType === 'form') this.$refs['headerForm'].resetFields()
      this.dialogType = ''
    },
    selectOptionMethod(item) {
      console.log('selectOptionMethod: item-->', item)
      if (item.type === 'method') {
        // eslint-disable-next-line no-unused-vars
        // const { fullScreen, defaultCheck, method, dialogProp, dialogType, check, api, query, filters, colums, onlyOne, setProp, not_checked, not_data, noQty, noTrackCode, dateType, hiddenColums, headerBtn } = item
        const params = { method: item.method, data: this.actionParams.data }
        if (item.fullMethod) {
          params['fullMethod'] = item.fullMethod
        }
        let valid_flag = true
        if (this.dialogType === 'form') {
          this.$refs.headerForm.validate((valid) => {
            if (valid) {
              Object.keys(this.dialogFormData).forEach(i => {
                params['data'][i] = this.dialogFormData[i]
              })
              if (this.$parent.formData.checkData) params['ids'] = this.$parent.formData.checkData.map(i => i.id)
            } else {
              valid_flag = false
            }
          })
        } else if (item.not_checked && !item.not_data) {
          this.formData && this.formData.filters && this.formData.filters.forEach(i => {
            params[i.prop] = i.value || (i.type.includes('date') ? [] : '')
          })
          if (this.$parent.formData && this.$parent.formData.checkData && this.$parent.formData.checkData.length > 0) {
            params['ids'] = this.$parent.formData.checkData.map(i => i.id)
          } else {
            params['ids'] = [2, 3, 3]
          }
        } else if (item.not_checked && item.not_data) {
          if (this.$parent.formData && this.$parent.formData.checkData && this.$parent.formData.checkData.length > 0) {
            params['ids'] = this.$parent.formData.checkData.map(i => i.id)
          } else {
            params['ids'] = [2, 3, 3]
          }
        } else {
          params['ids'] = this.$parent.formData.checkData.map(i => i.id)
        }

        // if (this.$parent.formData && this.$parent.formData.checkData && this.$parent.formData.checkData.length === 0 && item.dateType === 'weekRange') {
        if (item.dialogProp) {
          console.log('dateQuery start')
          this.$emit('toggleDialog', true)
          // this.dialogVisible = true
          this.actionParams.data = item
          this.actionParams.methods = item.method
          this.dialogType = item.dialogType
          this.fullScreen = !!item.fullScreen
          this.noQty = item.noQty
          this.noTrackCode = item.noTrackCode
          this.dateType = item.dateType
          if (item.dialogForm && item.dialogForm.length) {
            this.dialogForm = item.dialogForm
            item.dialogForm.forEach(i => {
              this.$set(this.dialogForm, i.prop, '')
            })
            console.log('this.dialogForm-->', this.dialogForm, this.dialogForm.length)
          }
          return
        }

        if (!valid_flag) return
        console.log('当前params2-->', params)
        if (params.method === 'export_order_report' || params.method === 'export_pre_order') {
          params['data']['finalSearchObj'] = this.getFinalSearchConditions()
        }
        this.$emit('execute', params)
        this.dialogFormData = {}
        if (this.dialogType === 'form') this.$refs['headerForm'].resetFields()
        this.dialogType = ''
        this.$emit('toggleDialog', false)
      } else {
        // const api = '/api/supplierButtBI/get_statement_url/'
        let postApi = item.method
        let postData = {}
        if (item.type === 'quick_bi') {
          postApi = '/api/supplierButtBI/get_statement_url/'
          postData = { 'system_url': process.env.HOST, 'statement_code': item.statement_code }
        }
        // 发送请求
        actionPost({
          api: postApi,
          data: postData
        }).then(res => {
          if (res.code === 200) {
            window.open(res.url, '_blank')
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
        }).catch(err => {
          this.$message.error(err)
        })
        // this.$emit('refresh', false)
        // item.value = null
      }
    },
    resetOption(item) {
      item.value = null
    },
    executeChargeOffDebit(btn, isSelectBtn = false) {
      let item = null
      // 下拉按钮
      if (isSelectBtn) {
        item = btn.btns.find(i => {
          return i.method === btn.value
        })
        // 重置下拉按钮框
        btn.value = ''
      } else {
        item = btn
      }
      console.log('new2-->', item)
      const { check, method } = item
      // 弹窗点击确定
      if (check) {
        // 表格判断
        if (this.dialogType === 'table' && this.actionParams.data.selectData.length === 0 && !this.defaultCheck) {
          this.$message.error(this.$t('content.pleaseSelect'))
          return
        }
      }
      // 不需要选中的数据操作  把过滤器参数传入服务器
      const params = { method, data: this.actionParams.data }
      let valid_flag = true
      if (this.dialogType === 'form') {
        this.$refs.headerForm.validate((valid) => {
          if (valid) {
            Object.keys(this.dialogFormData).forEach(i => {
              params['data'][i] = this.dialogFormData[i]
            })
            if (this.$parent.formData.checkData) params['ids'] = this.$parent.formData.checkData.map(i => i.id)
          } else {
            valid_flag = false
          }
        })
      } else {
        params['ids'] = this.$parent.formData.checkData.map(i => i.id)
      }
      if (!valid_flag) return
      this.$emit('execute', params)
      this.dialogFormData = {}
      if (this.dialogType === 'form') this.$refs['headerForm'].resetFields()
      this.dialogType = ''

      this.$emit('toggleDialog', false)
      this.innerDialogVisible = false
    },
    // 核销账单汇总中的单个账单
    chargeOffDebit(row, item) {
      this.innerDialogVisible = true
      // console.log('innerTable-->', this.innerTable, this.innerTable.length)
      // console.log('this1-->', row)
      // console.log('this2-->', item)
      if (Object.keys(this.innerTable).length > 0) {
        this.innerTable.query['invoice'] = row.id
        this.getInnerTableData(this.innerTable.api, this.innerTable.query, this.innerTable.filters, false)
      }
    },
    // 多项搜索
    getMultiSearchData() {
      this.page = 1
      this.size = 10
      this.current_status = ''
      const searchParams = this.formData.multiSearch.find(i => i.prop === this.multiSearchType).value
      if (searchParams) {
        const s = searchParams.replace(/\n/g, ' ').split(' ').map(i => {
          return i.trim()
        }).filter(i => i)
        if (s.length > 999999 || s.length === 0) {
          this.$message.error('搜索条数必须大于0小于999999个')
          return
        }
        const params = {
          [this.multiSearchType]: s
        }
        this.toQuery(true, params)
        sessionStorage.setItem('multiParams', JSON.stringify(params))
        sessionStorage.setItem('fullPath', this.$route.fullPath)
      }
    },
    // 多项搜索
    innerTableGetMultiSearchData() {
      this.innerDialogPage = 1
      this.innerDialogSize = 10
      console.log('inner 多项搜索')
      const searchParams = this.innerTable.multiSearch.find(i => i.prop === this.innerTableMultiSearchType).value
      if (searchParams) {
        const s = searchParams.replace(/\n/g, ' ').split(' ').map(i => {
          return i.trim()
        }).filter(i => i)
        if (s.length > 999999 || s.length === 0) {
          this.$message.error('搜索条数必须大于0小于999999个')
          return
        }
        const queryParams = {
          [this.innerTableMultiSearchType]: s
        }
        this.innerDialogQuery(queryParams, true)
        // sessionStorage.setItem('multiParams', JSON.stringify(queryParams))
        // sessionStorage.setItem('fullPath', this.$route.fullPath)
      }
    },
    // resetUrl() {
    //   if (this.dialogUrl.includes('?')) {
    //     this.dialogApi = this.dialogUrl + `&page=${this.dialogPage}&size=${this.dialogSize}&ordering=${this.dialogOrdering}`
    //   } else {
    //     this.dialogApi = this.dialogUrl + `?page=${this.dialogPage}&size=${this.dialogSize}&ordering=${this.dialogOrdering}`
    //   }
    // },
    // 获取弹窗数据
    getTableData(api, query, filters, defaultCheck, type, mainCheck = []) {
      console.log('getTableData-->', api)
      console.log('getTableData-query-->', query)
      // this.dialogUrl = api
      this.tableDataQuery = query
      // this.resetUrl()
      // this.dialogSearch.value = ''
      // this.dialogTableFilters = []
      // this.formData.action.forEach(action => {
      //   if (action.btns) {
      //     action.btns.forEach(btns => {
      //       if (btns.tableFilters) {
      //         this.dialogTableFilters = btns.tableFilters
      //       }
      //     })
      //   }
      // })
      // this.dialogTableFilters.forEach(i => {
      //   i.value = ''
      // })
      console.log('this.dialogTableFilters-->', this.dialogTableFilters)
      // const inputs = this.$refs.inputContainer.querySelectorAll('input')
      // inputs.forEach(input => {
      //   input.value = ''
      // })

      const t_query = Object.assign({}, query)
      // 对query进行处理,$代表从选中的行取值
      let flag = true
      Object.keys(t_query).forEach(i => {
        if (typeof (t_query[i]) === 'string' && t_query[i].startsWith('$')) {
          t_query[i] = this.$parent.formData.checkData[0][query[i].slice(1)]
          flag = this.$parent.formData.checkData.every(k => k[query[i].slice(1)] === t_query[i])
        } else if (typeof (t_query[i]) === 'string' && t_query[i].endsWith('$')) {
          // 传选中id
          t_query[i] = this.$parent.formData.checkData[0].id
        }
      })
      this.tableData = []
      if (!flag) {
        // 提示只能选同一币种和同一供应商
        setTimeout(() => {
          this.$message.error('只能选同一币种和同一客户的项进行操作')
        }, 500)
        return
      }
      let params = {}
      if (!type) {
        params = {
          // size: 100000,
          size: this.dialogSize,
          page: this.dialogPage,
          ordering: this.dialogOrdering,
          ...t_query
        }
        if (mainCheck !== []) {
          params['mainCheck'] = mainCheck
        }
      } else {
        params = { ...t_query }
      }
      console.log('this.actionParams.methods-->', this.actionParams.methods)
      if (this.actionParams.methods === 'change_track' && this.$parent.formData.checkData[0]) {
        this.judgeRequiredAddressNameEn(this.$parent.formData.checkData[0].order_status)
      }

      getChoiceData(api, params).then(res => {
        if (res.code === 200) {
          // 过滤掉filters状态
          Object.keys(filters || {}).forEach(i => {
            res.data = res.data.filter(k => {
              if (filters[i] instanceof Array) {
                let flag = false
                filters[i].forEach(j => {
                  flag = flag || k[i] === j
                })
                return flag
              } else {
                return k[i] === filters[i]
              }
            })
          })
          if (type && type === 'colums') {
            var compare = function(obj1, obj2) {
              var val1 = obj1['sort']
              var val2 = obj2['sort']
              if (val1 < val2) {
                return -1
              } else if (val1 > val2) {
                return 1
              } else {
                return 0
              }
            }
            this.colums = (res.data.map(i => { return { label: i.track_code_name, value: i.track_code_code, sort: i.sort } })).sort(compare)
            const select = this.$parent.formData.checkData[0]
            // if (res.current_track) {
            //   console.log('res.current_track-->', res.current_track)
            //   this.actionParams.data.selectActionVal = res.current_track
            // } else
            if (res.data.some(item => item.track_code_code === select.order_status)) {
              console.log('select.order_status-->', select.order_status)
              this.actionParams.data.selectActionVal = select.order_status
            }
          } else {
            this.tableData = res.data
            const sort = '-id'
            const query = this.query
            const value = query.value
            console.log('value2-->', value)
            this.params = { page: this.page, size: this.size, ordering: sort }
          }
          // 显示表格后默认勾上
          if (defaultCheck) {
            const select = this.$parent.formData.checkData[0]
            // 过滤掉master_num_name为空值的订单, 为什么?? 如果配置别的单据过滤掉就没了
            // const hasMain = this.$parent.formData.checkData.filter(i => i.master_num_name)
            const hasMain = this.$parent.formData.checkData.filter(i => i.order_num)
            // console.log('hasMain-->', hasMain)
            if (hasMain.length > 0) {
              let row = null
              let row_index = null
              this.$nextTick(() => {
                Object.keys(defaultCheck).forEach(i => {
                  row = this.tableData.find((k, index) => {
                    // console.log('0-->', defaultCheck[i])
                    // console.log('1-->', k[defaultCheck[i]])
                    // console.log('2-->', select[i])
                    // console.log('3-->', hasMain)
                    // console.log('4-->', hasMain.every(j => j[i] === k[defaultCheck[i]]))
                    if ((k[defaultCheck[i]] === select[i]) && hasMain.every(j => j[i] === k[defaultCheck[i]])) {
                      row_index = index
                      return k[defaultCheck[i]] === select[i]
                    }
                  })
                })
                if (row) {
                  this.tableData.unshift(this.tableData.splice(row_index, 1)[0])
                  this.$refs.dialogTable.toggleRowSelection(row)
                }
              })
            }
          }
          this.dialogTotal = res.count
        } else {
          this.$message.error(res.msg || res.detail)
        }
      })
    },
    // 初始化内部表格弹框数据
    getInnerTableData(api, query, filters, invoiceId, type) {
      this.innerDialogUrl = api
      const t_query = Object.assign({}, query)
      // 对query进行处理,$代表从选中的行取值
      Object.keys(t_query).forEach(i => {
        if (typeof (t_query[i]) === 'string' && t_query[i].startsWith('$')) {
          t_query[i] = this.$parent.formData.checkData[0][query[i].slice(1)]
          // flag = this.$parent.formData.checkData.every(k => k[query[i].slice(1)] === t_query[i])
        } else if (typeof (t_query[i]) === 'string' && t_query[i].endsWith('$')) {
          // 传选中id
          t_query[i] = this.$parent.formData.checkData[0].id
        }
      })
      this.innerTableData = []
      let params = {}
      if (!type) {
        params = {
          // size: 100000,
          // page: 1,
          ordering: '-id',
          ...t_query
        }
      } else {
        params = {
          ...t_query
        }
      }

      getChoiceData(api, params).then(res => {
        if (res.code === 200) {
          // 过滤掉filters状态
          Object.keys(filters || {}).forEach(i => {
            res.data = res.data.filter(k => {
              if (filters[i] instanceof Array) {
                let flag = false
                filters[i].forEach(j => {
                  flag = flag || k[i] === j
                })
                return flag
              } else {
                return k[i] === filters[i]
              }
            })
          })
          this.innerTableData = res.data
          this.innerDialogTotal = res.count
        } else {
          this.$message.error(res.msg || res.detail)
        }
      })
      // console.log('this.innerTableData-->', this.innerTableData)
      // 添加筛选框数据
      this.innerDialogTableFilters && this.innerDialogTableFilters.forEach(i => {
        if (i.api) {
          getChoiceData(i.api, params).then(res => {
            this.$set(i, 'data', res.data || res.results)
          })
        }
      })
    },
    addItem() {
      if (this.isDetail) {
        if (this.$route.path === '/transport/customerOrderTask/list') {
          this.$router.push('/transport/customerOrderTask/add')
          return
        }
        this.$router.push(this.isDetail)
        return
      }
      this.$refs.form.dialog = true
    },
    reverseTable() {
      this.$parent.reverse()
    },
    download() {
      console.log('----download--->>')
      if (this.formData.exportUrl) {
        // 接口导出
        const ids = this.$parent.formData.checkData.map(i => i.id)
        const filters = this.$parent.filterProp
        // if (ids.length === 0) {
        //   this.$message.error('请选择订单')
        //   return
        // }
        const api = `/api/${this.formData.api}/${this.formData.exportUrl}/`
        const responseType = 'blob'
        actionPost({ api, data: { ids, filters }, responseType }).then(res => {
          axiosExport(res, (this.formData.title || 'excel') + '.xlsx')
          this.$message.success('success!')
        })
        return
      }
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = ['ID']
        const filterVal = ['id']
        const filters = []
        this.formData.data.forEach(i => {
          if (i.type === 'multiRow') {
            i.data.forEach(k => {
              tHeader.push(k.label)
              if (k.type === 'select' && k.options) {
                filterVal.push(k.prop + '_name')
              } else {
                filterVal.push(k.prop)
              }
              if (k.filter) {
                filters.push({ prop: k.prop, filter: k.filter })
              }
            })
          } else {
            tHeader.push(i.label)
            if (i.type === 'select' && i.options) {
              filterVal.push(i.prop + '_name')
            } else {
              filterVal.push(i.prop)
            }
            if (i.filter) {
              filters.push({ prop: i.prop, filter: i.filter })
            }
          }
        })
        if (this.formData.excelExport) {
          this.formData.excelExport.forEach(i => {
            if (i.position) {
            // 有位置规定
              const p = tHeader.findIndex(k => k === i.position) + 1
              tHeader.splice(p, 0, i.label)
              filterVal.splice(p, 0, i.prop)
            } else {
            // 没有位置规定末尾添加
              tHeader.push(i.label)
              filterVal.push(i.prop)
            }
          })
        }
        const data = this.formatJson(filterVal, this.$parent.data, filters)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: `${this.formData.title}-table-list`
        })
        this.downloadLoading = false
      })
    },
    formatJson(filterVal, jsonData, filters) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'create_date' || j === 'update_date') {
          return parseTime(v[j])
        } else if (typeof v[j] === 'boolean') {
          return v[j] ? '是' : '否'
        } else if (filters.some(i => i.prop === j)) {
          return filters.find(i => i.prop === j).filter[v[j]]
        } else {
          return v[j]
        }
      }))
    },
    // 下拉框和输入框变更
    change(val) {
      console.log('change-->', val)
      this.toQuery()
    },
    // dialogQuery() {
    //   console.log('对话框查询-->')
    //   // this.$parent.init()
    //   this.sentRequest(this.dialogApi)
    // },
    // 弹窗
    handleClose() {
      this.dialogVisible = false
      this.actionParams.data.date = ''
      this.actionParams.data.amount = ''
      this.actionParams.data.selectActionVal = ''
      this.actionParams.data.selectData = []
      this.multiSearchData.forEach(i => {
        i.value = ''
      })
      console.log('this.dialogTableFilters-->', this.dialogTableFilters)
      this.dialogTableFilters.forEach(i => {
        i.value = ''
      })
      this.multiSearchShow = false
      this.$emit('toggleDialog', false)
      this.actionParams.data.is_part_set = false
      this.orderStowageType = 0
      this.actionParams.data.is_rebinding = false
    },
    innerTableHandleClose() {
      this.innerMultiSearchShow = false
    },
    innerHandleClose() {
      // this.actionParams.data.date = ''
      // this.actionParams.data.amount = ''
      // this.actionParams.data.selectActionVal = ''
      // this.actionParams.data.selectData = []
      // this.multiSearchData.forEach(i => {
      //   i.value = ''
      // })
      // this.multiSearchShow = false
      this.innerDialogVisible = false
      this.innerDialogTableFilters.forEach(i => {
        i.value = ''
      })
    },
    // 表格选中
    selectionChange(val) {
      this.actionParams.data.selectData = val
      console.log('选择1-->', this.actionParams.data.selectData)
    },
    // 初始化获取下拉数据等
    initChoiceData(item) {
      console.log('initChoiceData0-->')
      const params = {
        size: 100000,
        page: 1,
        sort: 'id'
      }
      // 点击加载
      if (item) {
        if (!item.data) {
          if (item.query) {
            Object.keys(item.query).forEach(i => {
              params[i] = item.query[i]
            })
          }
          getChoiceData(item.api, params).then(res => {
            this.$set(item, 'data', res.data || res.results)
          })
        }
        return
      }
      // 进入页面就加载
      if (!this.firstEntry) return
      this.dialogTableFilters && this.dialogTableFilters.forEach(i => {
        if (i.api) {
          getChoiceData(i.api, params).then(res => {
            this.$set(i, 'data', res.data || res.results)
          })
        }
      })
      // this.formData && this.formData.filters && this.formData.filters.forEach(i => {
      //   if (i.api) {
      //     getChoiceData(i.api, params).then(res => {
      //       this.$set(i, 'data', res.data || res.results)
      //     })
      //   }
      // })
      this.firstEntry = false
    },
    // 点击下拉框
    getSelectData(item) {
      console.log('选择2-->', this.actionParams.data.selectData)
      // this.$parent.initChoiceData(item)
      this.initChoiceData(item)
    },
    // 获取当前日期或者日期时间
    getCurrentNow(time = false) {
      var myDate = new Date()
      console.log('getCurrentNow')
      const y = myDate.getFullYear()
      const m = myDate.getMonth() + 1
      const d = myDate.getDate()
      const min = myDate.getMinutes()
      const h = myDate.getHours()
      const s = myDate.getSeconds()
      if (time) {
        return `${y + '-' + (m > 9 ? m : '0' + m) + '-' + (d > 9 ? d : '0' + d)} ${h > 9 ? h : '0' + h}:${min > 9 ? min : '0' + min}:${s > 9 ? s : '0' + s}`
      } else {
        return `${y + '-' + (m > 9 ? m : '0' + m) + '-' + (d > 9 ? d : '0' + d)}`
      }
    },
    getScrollTop() {
      this.$nextTick(() => {
        const h = document.documentElement.scrollTop || document.body.scrollTop
        if (h > 230) {
        // 固定
          this.fixed.isFixed = true
          this.fixed.w = (this.$refs.fixedBtn && this.$refs.fixedBtn.offsetWidth ? this.$refs.fixedBtn.offsetWidth : this.fixed.w) + 'px'
        } else {
        // 取消固定
          this.fixed.isFixed = false
          this.fixed.w = '100%'
        }
      })
    },
    backHost() {
      return process.env.HOST
    },
    // addFreightNum() {
    //   console.log(this.hiddenColums)
    //   this.hiddenColums = this.hiddenColums.filter(item => item !== 'freight_num')
    //   this.actionParams.data.is_part_set = true
    // },
    // hiddenFreightNum() {
    //   this.hiddenColums.push('freight_num')
    //   this.actionParams.data.is_part_set = false
    // },
    orderStowage() {
      if (this.orderStowageType === 0) {
        // 全部配载
        this.hiddenColums.push('freight_num')
        this.actionParams.data.is_part_set = false
      } else if (this.orderStowageType === 1) {
        // 部分配载
        if (this.formData.checkData.length !== 1) {
          this.$message.error('只能选择一个订单做部分配载')
          this.orderStowageType = 0
          // this.handleClose()
          return
        } else if (this.formData.checkData[0]['carton'] <= 0) {
          this.$message.error('做部分配载的订单件数必须 > 0')
          this.orderStowageType = 0
          // this.handleClose()
          return
        }
        this.hiddenColums = this.hiddenColums.filter(item => item !== 'freight_num')
        // console.log(this.hiddenColums)
        this.actionParams.data.is_part_set = true
      }
    },
    monthOptionChange(value) {
      const item = {}
      item.name = '归属期'
      item.method = 'option_attribution_period'
      item.type = 'monthOption'
      this.actionParams.data.month = value
      this.execute(item)
      this.monthOptionDate = ''
    },
    handleFold() {
      this.fold = !this.fold
    },
    // 文件上传相关---------------------------------------
    // 文件变更
    fileChange(file, fileList) {
    },
    // 文件上传之前
    beforeUpload(file) {},
    // 文件移除之前
    beforeRemove(file, fileList, item, singel) {
      // 单文件移除
      if (singel) {
        return new Promise((resolve, reject) => {
          this.$confirm(this.$t('common.delecomfirm'), this.$t('common.tips'), {
            confirmButtonText: this.$t('common.sure'),
            cancelButtonText: this.$t('common.cancle'),
            type: 'warning'
          }).then(() => {
            if (!this.id) {
              resolve(true)
              return true
            }
            var api
            if (item.url) {
              // api = `api/${item.url}/`
              const params = {
                id: this.id,
                api: `${item.url}`,
                data: {}
              }
              del(params).then(res => {
                if (res.code === 200) {
                  this.$message.success(this.$t('common.deletesuccess'))
                } else {
                  this.$message.error(this.$t('common.deletefail'))
                }
                resolve(true)
                return true
              }).catch(() => {
                resolve(false)
                return false
              })
            } else {
              api = `api/${this.requestUrl.baseUrl}/detele_file/`
              const params = {
                api: api,
                data: { id: this.id, fileName: item.prop }
              }
              console.log('干啥去了?-->')
              actionPost(params).then(res => {
                if (res.code === 200) {
                  this.$message.success(this.$t('common.deletesuccess'))
                } else {
                  this.$message.error(this.$t('common.deletefail'))
                }
                resolve(true)
                return true
              }).catch(() => {
                resolve(false)
                return false
              })
            }
          }).catch(() => {
            reject(false)
            return false
          })
        })
      } else {
        return new Promise((resolve, reject) => {
          this.$confirm(this.$t('common.delecomfirm'), this.$t('common.tips'), {
            confirmButtonText: this.$t('common.sure'),
            cancelButtonText: this.$t('common.cancle'),
            type: 'warning'
          }).then(() => {
            if (!file.id) {
              resolve(true)
              return true
            }
            del({ api: this.filesUrl(item.prop).url, id: file.id, data: {}}).then(res => {
              resolve(true)
              return true
            }).catch(() => {
              resolve(false)
              return false
            })
          }).catch(() => {
            reject(false)
            return false
          })
        })
      }
    },
    // 文件移除
    handleRemove(file, fileList, item) {
      this.fileList = fileList
      this.formData.attachments = this.formData.attachments.filter(i => i.name !== file.name)
    },
    // 自定义上传
    uploadAttchment(data, item) {
      if (!this.dialogFormData.waitToUpload) {
        this.dialogFormData.waitToUpload = new FormData()
      }

      this.dialogFormData.waitToUpload.append('files', data.file)
    },
    // 最多只允许上传5个文件
    handleExceedFive(files, fileList) {
      this.$message.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    // 单月周选择器的显示格式(每个月都从第一周开始算起), 将输入的日期转换为 "YYYY-MM 第X周" 的格式
    getWeekRangeOneMonth(dateStr) {
      if (!dateStr) {
        return
      }
      // 将传入的日期字符串转换为 Date 对象
      const date = new Date(dateStr)
      date.setDate(date.getDate() + 5)
      console.log('getWeekRangeOneMonth-->', date)

      // 获取该周周四的日期
      const thursday = new Date(date)
      // 计算当前日期是周几（0 表示周日，1 表示周一，以此类推）
      const dayOfWeek = date.getDay()
      // 计算与周四的偏移量（周四是 4）
      const offset = 4 - dayOfWeek
      thursday.setDate(date.getDate() + offset)

      // 获取周四日期的年份和月份
      const year = thursday.getFullYear()
      const month = String(thursday.getMonth() + 1).padStart(2, '0')

      // 计算该月的第一周周四的日期
      const firstThursday = new Date(year, thursday.getMonth(), 1)
      const firstDayOfWeek = firstThursday.getDay()
      const firstThursdayOffset = 4 - firstDayOfWeek
      if (firstThursdayOffset < 0) {
        firstThursday.setDate(1 + 7 + firstThursdayOffset)
      } else {
        firstThursday.setDate(1 + firstThursdayOffset)
      }

      // 计算该周是该月的第几周
      const weekNumber = Math.ceil((thursday.getDate() - firstThursday.getDate()) / 7) + 1

      // 返回格式化后的结果
      return `${year}-${month} 第${weekNumber}周`
    },
    judgeRequiredAddressNameEn(selectActionVal = null) {
      if (!selectActionVal) {
        selectActionVal = this.actionParams.data.selectActionVal
      }
      const params = {
        selectActionVal: selectActionVal,
        affiliated_track: this.tableDataQuery && this.tableDataQuery.affiliated_track
      }
      params['ids'] = this.$parent.formData.checkData.map(i => i.id)
      actionPost({
        api: '/api/oceanOrders/get_track_node_address/',
        data: params
      }).then(res => {
        if (res.code === 200) {
          this.nodeAddress = res.data
          if (this.nodeAddress.required && this.nodeAddress.msg) {
            this.$message.error(this.nodeAddress.msg)
          }
        } else {
          this.$message.error(res.detail || res.message || res.msg)
        }
      }).catch((error) => {
        this.$message.error(error)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .reverse {
    transition: all .3s;
    &:hover {
      transform: rotate(180deg);
    }
  }

  .ml10 {
    margin-left: 10px;
  }

  .bottom /deep/ .el-input__icon {
    line-height: 20px;
  }

  .selectBtn {
    width: 108px;
    margin:0 10px 10px 10px;
    &:hover /deep/input{
      border-color: #c6e2ff;
      background-color: #ecf5ff;
      &::placeholder {
        color: #409eff!important;
      }
    }

    /deep/ input {
      text-align: center;
    }
    /deep/ input::placeholder{
      color: #606266;
      font-weight: 500;
    }
  }

  // 动效果
.hvr-float-shadow:before {
  pointer-events: none;
  position: absolute;
  z-index: -1;
  content: '';
  top: 100%;
  left: 5%;
  height: 10px;
  width: 90%;
  opacity: 0;
  background: -webkit-radial-gradient(ellipse at center, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 80%);
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 80%);
  /* W3C */
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -webkit-transition-property: transform, opacity;
  transition-property: transform, opacity;
}
.hvr-float-shadow:hover {
  -webkit-transform: translateY(-3px);
  transform: translateY(-3px);
  /* move the element up by 5px */
}
.hvr-float-shadow:hover:before {
  opacity: 1;
  -webkit-transform: translateY(3px);
  transform: translateY(3px);
  /* move the element down by 5px (it will stay in place because it's attached to the element that also moves up 5px) */
}

.linghtUp {
  /deep/ input {
    animation-name: magentaPulse;
    animation-duration:1.5s;
    animation-direction: alternate;
    animation-iteration-count: infinite;
  }
}

@keyframes magentaPulse {
    from { box-shadow: 0 0 0 #ccc; }
    // 50% { transform: rotate(0deg);}
    to { box-shadow: 0 0 18px #888;;}
}

@-webkit-keyframes bounce {
    0%,20%,53%,80%,100% {
        -webkit-animation-timing-function: cubic-bezier(0.215,0.610,0.355,1.000);
        animation-timing-function: cubic-bezier(0.215,0.610,0.355,1.000);
        -webkit-transform: translate3d(0,0,0);
        transform: translate3d(0,0,0)
    }

    40%,43% {
        -webkit-animation-timing-function: cubic-bezier(0.755,0.050,0.855,0.060);
        animation-timing-function: cubic-bezier(0.755,0.050,0.855,0.060);
        -webkit-transform: translate3d(0,-30px,0);
        transform: translate3d(0,-30px,0)
    }

    70% {
        -webkit-animation-timing-function: cubic-bezier(0.755,0.050,0.855,0.060);
        animation-timing-function: cubic-bezier(0.755,0.050,0.855,0.060);
        -webkit-transform: translate3d(0,-15px,0);
        transform: translate3d(0,-15px,0)
    }

    90% {
        -webkit-transform: translate3d(0,-4px,0);
        transform: translate3d(0,-4px,0)
    }
}

@keyframes bounce {
    0%,20%,53%,80%,100% {
        -webkit-animation-timing-function: cubic-bezier(0.215,0.610,0.355,1.000);
        animation-timing-function: cubic-bezier(0.215,0.610,0.355,1.000);
        -webkit-transform: translate3d(0,0,0);
        transform: translate3d(0,0,0)
    }

    40%,43% {
        -webkit-animation-timing-function: cubic-bezier(0.755,0.050,0.855,0.060);
        animation-timing-function: cubic-bezier(0.755,0.050,0.855,0.060);
        -webkit-transform: translate3d(0,-8px,0);
        transform: translate3d(0,-8px,0)
    }

    70% {
        -webkit-animation-timing-function: cubic-bezier(0.755,0.050,0.855,0.060);
        animation-timing-function: cubic-bezier(0.755,0.050,0.855,0.060);
        -webkit-transform: translate3d(0,-4px,0);
        transform: translate3d(0,-4px,0)
    }

    90% {
        -webkit-transform: translate3d(0,-2px,0);
        transform: translate3d(0,-2px,0)
    }
}

.bounce {
    -webkit-animation-name: bounce;
    animation-name: bounce;
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    animation-duration: 1s;
    animation-delay: .5s;
    animation-iteration-count: infinite;
}

.fixed {
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 100;
  padding: 10px 0;
  background-color: rgba(255, 255, 255, .6);
}
.head-container {
    padding-bottom: 10px;
}
.filter-item{
  margin: 0 0 10px 10px;
}
/deep/ .el-form-item__label{
  //width: 100px !important;
  width: 100px;
}
/deep/ .el-form-item__content{
  //margin-left: 100px !important;
  margin-left: 100px;
}
/deep/ .el-dialog__header2{
  padding: 20px 0 20px;
}
.custom-dialog-item{
  width:100%;margin-bottom:10px;margin-top:6px;
}
</style>
