<!--列表页数据展示组件-->
<template>
  <div>
    <!-- 多行显示显示前面得label -->
    <span v-if="type==='multiRow'">{{ item.label }}:</span>
    <!-- 链接跳转 -->
    <span v-if="item.link">
      <span v-if="item.multiRow">
        <router-link
          v-for="(subRow, index) in row[item.prop]"
          :key="subRow.id"
          :to="item.link+'?id='+(subRow[item.link_prop] || subRow['id'])"
          style="color:blue;"
        >
          {{ item.type === 'datetime' || item.type === 'date' || item.type === 'month' ? parseTime(subRow[item.title], item.type) : subRow[item.title] }}<span v-if="index < row[item.prop].length - 1"> / </span>
        </router-link>
      </span >
      <span v-else>
        <router-link :to="item.link+'?id='+(row[item.link_prop] ||row['id'])" style="color:blue;">
          {{ row[item.prop] }}
        </router-link>
      </span >
    </span>
    <!-- 文件 -->
    <span v-else-if="item.type==='file'">
      <div v-for="item in row[item.prop]" :key="item.id" style="color:#000;font-weight:bold;">
        &lt;&lt;<a :href="getFilePath(item.url)" style="color:blue;" target="_blank">{{ item.name }}</a>&gt;&gt;
      </div>
    </span>
    <span v-else-if="item.type==='boolean'">
      <i v-if="row[item.prop] === true || (Array.isArray(row[item.prop]) && row[item.prop].length)" style="color:green" class="iconfont icon-dui"/>
      <i v-else style="color:#838181" class="iconfont icon-cuowu"/>
    </span>
    <!-- 在一个单元格中显示实际长宽高和长宽高-->
    <span v-else-if="item.actualComparison">
      <span>{{ (row[item.prop]) | fixZero }}</span> <span>|</span> <span :style="{color:parseFloat(row[item.prop])!==parseFloat(row['actual_'+item.prop])?'red':''}">{{ row['actual_'+item.prop] | fixZero }}</span>
    </span>
    <!-- 对错iconfont -->
    <span v-else-if="item.filter===true">
      <i v-if="row[item.prop]" style="color:green" class="iconfont icon-dui"/>
      <i v-else style="color:#838181" class="iconfont icon-cuowu"/>
    </span>
    <!-- 过滤器 -->
    <span v-else-if="item.filter">
      <span v-if="item.multiRow">
        <span
          v-for="(subRow, index) in row[item.prop]"
          :key="subRow.id"
        >
          {{ item.filter[subRow[item.title]] }}<span v-if="index < row[item.prop].length - 1"> / </span>
        </span>
      </span >
      <span v-else-if="item.combineStr">
        {{ combineStr(item, row) }}
      </span >
      <span v-else-if="item.trans_bool">
        {{ row[item.prop] === true ? '是': '否' }}
      </span >
      <span v-else>
        {{ item.filter[row[item.prop]] }}
      </span >
    </span>
    <span v-else-if="item.multiRowText">
      <span
        v-for="(subRow, index) in row[item.prop]"
        :key="subRow.id"
      >
        {{ subRow[item.title] }}<span v-if="index < row[item.prop].length - 1"> / </span>
      </span>
    </span>
    <!-- 条件渲染颜色 -->
    <!-- 高货值显示红色-->
    <span v-else-if="item.color && item.type==='highLight'">
      <span :style="{ color: judgeHighLightCondition(row[item.prop], item) ? item.color : '', 'font-size': item.fontSize}">{{ row[item.prop] }}</span>
    </span>
    <span v-else-if="item.color || item.bold">
      <span :style="{color: !item.condition || Object.keys(item.condition).every(i=>{return Array.isArray(item.condition[i])?item.condition[i].includes(row[i]):row[i]===item.condition[i]})?item.color: '', 'font-size': item.fontSize, 'font-weight': item.bold ? 'bold' : 'normal'}">{{ row[item.prop] }}</span>
    </span>
    <span v-else-if="item.type==='arrayFirst'">
      {{ (row[item.prop]).length !== 0 ? ((row[item.prop])[0]['label_desc'] || []).includes('null')?'':(row[item.prop])[0]['label_desc'] : '' }}
    </span>
    <!-- 下拉框的值显示名字 -->
    <span v-else-if="item.options">{{ row[item.prop+'_name'] }}</span>
    <!-- 日期转换 -->
    <span v-else-if="item.type==='date' || item.type==='datetime' || item.type==='month'">
      {{ parseTime(row[item.prop], item.type) }}
    </span>
    <!-- 大于0小于0显示颜色 -->
    <span v-else-if="item.compare">
      <span :style="{color:Number(row[item.prop])==0?'#606266':Number(row[item.prop])>0?'#409eff':'red'}">{{ row[item.prop] }}</span>
    </span>
    <!-- 老预计与实际 -->
    <span v-else-if="item.pre">
      <span>{{ (row['pre_'+item.prop]) | fixZero }}</span> <span>|</span> <span :style="{color:row[item.prop]!==row['pre_'+item.prop]?'red':''}">{{ (row[item.prop]) | fixZero }}</span>
    </span>
    <!-- 直接下载文件 -->
    <span v-else-if="item.type==='s_file'">
      <a v-if="row[item.prop]" :href="'//'+row[item.prop]" style="color:blue;" target="_blank">{{ $t('common.download') }}</a>
    </span>
    <span v-else-if="item.type==='s_file_url'">
      <a v-if="row[item.prop]" :href="row[item.prop]" style="color:blue;" target="_blank">{{ $t('common.download') }}</a>
    </span>
    <span v-else-if="item.type==='file_path'">
      <a v-if="row[item.prop]" :href="processFilePath(row[item.prop])" style="color:blue;" target="_blank">{{ $t('common.download') }}</a>
    </span>
    <span v-else-if="item.type==='file_path_3'">
      <a v-if="row[item.prop]" :href="processFilePath3(row[item.prop])" style="color:blue;" target="_blank">{{ $t('common.download') }}</a>
    </span>
    <span v-else-if="item.type==='list'">
      <div v-for="(item, index) in row[item.prop]" :key="index">
        <span>{{ item }}</span>
      </div>
    </span>
    <!-- 去除0 -->
    <span v-else-if="item.fixZero">
      {{ row[item.prop]| fixZero }}
    </span>
    <!-- pic -->
    <span v-else-if="item.pic">
      <el-image :src="row[item.prop]" :fit="fit" style="width: 60px; height: 60px"><span slot="error" style="color:#ccc;line-height:60px;">{{ row[item.prop]?'load fail':'no image' }}</span></el-image>
    </span>
    <!-- 值不存在或者为空的 -->
    <span v-else-if="row[item.prop]===undefined||row[item.prop]===null"/>
    <span v-else-if="item.no_etc" :title="row[item.prop]">{{ row[item.prop] === 'null' || row[item.prop] === 'undefined'? '' : (row[item.prop] + (item.otherProp&&row[item.otherProp]!==undefined&&row[item.otherProp]!==null?' '+row[item.otherProp]:'')) }}</span>
    <!--    增加复制按钮-->
    <span v-else-if="item.copyButton" :title="row[item.prop]">
      {{
        row[item.prop] === 'null' || row[item.prop] === 'undefined' ? '' : (row[item.prop] + (item.otherProp && row[item.otherProp] !== undefined && row[item.otherProp] !== null ? ' ' + row[item.otherProp] : '')) | etc
      }}
      <span slot="reference">
        <el-button type="text" icon="el-icon-document-copy" @click="copyData(row, item)">{{ $t('content.copy') }}</el-button>
      </span>
    </span>
    <span v-else-if="item.isAllContent" :title="row[item.prop]">{{ row[item.prop] === 'null' || row[item.prop] === 'undefined'? '' : row[item.prop] }}</span>
    <span v-else-if="item.type==='textarea'" :title="row[item.prop]">{{ row[item.prop] }}</span>
    <span v-else-if="!item.type_2" :title="row[item.prop]">{{ row[item.prop] === 'null' || row[item.prop] === 'undefined'? '' : (formatValue(row[item.prop]) + (item.otherProp&&row[item.otherProp]!==undefined&&row[item.otherProp]!==null?' '+row[item.otherProp]:'')) | etc }}</span>
    <!--    增加复制按钮(带图标不带复制两个字)-->
    <span v-if="item.copyButtonIcon" :title="row[item.prop]">
      <span slot="reference">
        <el-button type="text" icon="el-icon-document-copy" @click="copyData(row, item)"></el-button>
      </span>
    </span>
    <!-- 可修改的项 -->
    <template v-if="item.type==='popover'">
      <!--<span :title="row[item.prop]">{{ (row[item.prop]||'') | etc }}</span>-->
      <el-popover placement="right" width="500" trigger="click" @show="modifyVal=row[item.prop]" @hide="modifyVal=''">
        <div class="replybox">
          <p style="font-weight:bold;">{{ $t('common.modify') }}{{ item.label }}：</p>
          <el-input v-model="modifyVal" :placeholder="`${$t('common.enter')}${item.label}`" type="textarea" rows="4" maxlength="100"/>
          <div style="text-align:right;margin-top:10px;">
            <el-button type="success" @click="modifyItem(row, item)">{{ $t('common.sure') }}</el-button>
          </div>
        </div>
        <span slot="reference">
          <el-button type="text" icon="el-icon-edit" @click="showPopo=true;">{{ $t('common.modify') }}</el-button>
        </span>
      </el-popover>
    </template>
    <template v-else-if="item.type==='datetimePopover'">
      <!--<span :title="row[item.prop]">{{ (row[item.prop]||'') | etc }}</span>-->
      <el-popover placement="right" width="500" trigger="click" @show="modifyVal=row[item.prop]" @hide="modifyVal=''">
        <div class="replybox">
          <p style="font-weight:bold;">{{ $t('common.modify') }}{{ item.label }}：</p>
          <el-date-picker
            v-model="modifyVal"
            :disabled="item.disabled"
            :value-format="'yyyy-MM-ddTHH:mm:ss'"
            :placeholder="$t('common.selectDate')"
            :picker-options="item.restrict==='laterDate'? expireTimeOption: true"
            type="datetime"
            style="width:100%"/>
          <div style="text-align:right;margin-top:10px;">
            <el-button type="success" @click="modifyItem(row, item)">{{ $t('common.sure') }}</el-button>
          </div>
        </div>
        <span slot="reference">
          <el-button type="text" icon="el-icon-edit" @click="showPopo=true;">{{ $t('common.modify') }}</el-button>
        </span>
      </el-popover>
    </template>

    <!--增加文件上传按钮-->
    <span v-if="item.uploadButton" :title="row[item.prop]">
      <!--<span>
        <i v-if="row[item.prop]" style="color:green" class="iconfont icon-dui"/>
        <i v-else style="color:#838181" class="iconfont icon-cuowu"/>
      </span>-->
      <span slot="reference">
        <el-button type="text" icon="el-icon-upload" @click="openUploadDialog">{{ '上传' }}</el-button>
      </span>
    </span>
    <span v-if="item.type_2==='file_1'">
      <span v-for="item in row[item.prop]" :key="item.id" style="color:#000;font-weight:bold;">
        <a :href="getFilePath(item.url)" style="color:blue;" target="_blank">{{ item.name }}</a>
      </span>
    </span>
    <el-dialog :visible.sync="showUploadDialog" width="500px" @close="hiddenUploadDialog">
      <upload-dialog2 v-model="row[item.prop]" :upload-url="item.uploadUrl" :foreignkey="item.foreignkey" :row-id="row['id']"></upload-dialog2>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import { actionPost } from '@/api/data'
import UploadDialog2 from '@/components/FileUpload/UploadDialog2'
import { copyText, judgeHighLightCondition } from '../../utils/common'

export default {
  components: {
    UploadDialog2
  },
  filters: {
    etc(val) {
      const s = val + ''
      if (s.length < 30) {
        return s
      } else {
        return s.substr(0, 30) + '...'
      }
    },
    fixZero(val) {
      if (!val) {
        return ''
      }
      val = String(val)
      if ((val + '').indexOf('.') === -1) {
        return val
      } else {
        let flag = false
        const t = []
        val.split('').reverse().forEach(i => {
          if (i !== '0') {
            flag = true
          }
          if (flag) {
            t.push(i)
          }
        })
        if (t[0] === '.') {
          t[0] = ''
          const result = t.reverse().join('')
          return result
        } else {
          const result = t.reverse().join('')
          return result
        }
      }
    }
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    row: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showPopo: false,
      modifyVal: '',
      showMessage: false,
      message: '',
      showUploadDialog: false,
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      }
    }
  },
  methods: {
    parseTime,
    judgeHighLightCondition,
    formatValue(value) {
      // 处理时间格式 2025-01-01T00:00:00替换成2025-01-01 00:00:00
      if (typeof value === 'string') {
        const isoDatePattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/
        if (isoDatePattern.test(value)) {
          return value.replace('T', ' ')
        }
      }
      return value
    },
    getFilePath(val) {
      if (!val) {
        return ''
      }
      const prefix = val.slice(0, val.indexOf('api'))
      const subfix = val.slice(val.indexOf('alita') + 5)
      return prefix + subfix
    },
    processFilePath(val) {
      if (!val) {
        return ''
      }
      var prefix = val.indexOf('openApi/outboundInstructs')
      if (prefix !== -1) {
        return `media/${val}`
      }
      // openApi替换为media
      return val.replace('openApi', 'media')
    },
    processFilePath3(val) {
      if (!val) {
        return ''
      }
      var prefix = val.indexOf('openApi/outboundInstructs')
      if (prefix !== -1) {
        return `media/${val}`
      }
      // 如果没有 'openApi'，则在前面拼接 'media/'
      if (val.indexOf('openApi') === -1) {
        return `media/${val}`
      }
      // openApi替换为media
      return val.replace('openApi', 'media')
    },
    modifyItem(row, item) {
      const params = {
        api: item.api,
        data: {
          ...item,
          ...row,
          modifyVal: this.modifyVal
        }
      }
      actionPost(params).then(res => {
        if (res.code === 200) {
          row[item.prop] = this.modifyVal
          this.$message.success(res.msg || 'success')
          document.querySelector('#app').click()
        } else {
          this.$message.error(res.msg || res.detail || '修改失败！')
        }
      })
    },
    combineStr(item, row) {
      if (row.effective_month !== null) {
        return row.effective_month.split(',').map(month => item.filter[month]).join(', ')
      }
    },
    copyData(row, item) {
      const content = row[item.prop] === 'null' || row[item.prop] === 'undefined'
        ? '' : (row[item.prop])
      // 使用 Clipboard API 进行复制
      // navigator.clipboard.writeText(content).then(() => {
      //   this.$message({
      //     message: '复制成功！',
      //     type: 'success'
      //   })
      // }).catch((error) => {
      //   this.$message({
      //     message: '复制失败！,' + error,
      //     type: 'warning'
      //   })
      // })

      copyText(content, this)
    },
    openUploadDialog() {
      this.showUploadDialog = true
    },
    hiddenUploadDialog() {
      this.showUploadDialog = false
    },
    handleUploadInput(value) {
      this.$set(this.row, this.item.prop, value)
    }
  }
}
</script>

<style>

</style>
