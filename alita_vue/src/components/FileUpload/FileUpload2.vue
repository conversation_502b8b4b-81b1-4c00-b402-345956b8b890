<template>
  <div class="upload-demo">
    <!--:action="uploadFileUrl"-->
    <el-upload
      ref="upload"
      :data="extraParams"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :http-request="(file) => uploadAttachment(file, uploadUrl)"
      class="upload-file-uploader"
    >
      <!-- 上传按钮 -->
      <el-button v-if="isAdd" size="small" type="success">{{ $t('content.clickToUpload') }}</el-button>
      <!-- 上传提示 -->
      <div v-if="showTip" slot="tip" class="el-upload__tip" >
        {{ $t('content.pleaseUpload') }}
        <template v-if="fileSize"> {{ $t('content.notExceeding') }} <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
        <template v-if="fileType"> {{ $t('content.format') }} <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
        {{ $t('content.docs') }}
      </div>
    </el-upload>

    <ul class="upload-file-list el-upload-list el-upload-list--text">
      <!--<li v-for="item in fileList" :key="item.uid" class="el-upload-list__item ele-upload-list__item-content">-->
      <li v-for="item in fileList" :key="item.id" class="el-upload-list__item ele-upload-list__item-content">
        <a v-if="/\.(jpg|png|jpeg)$/i.test(item.url)" :href="getFileUrl(item.url)" style="color:#409EFF;" target="_blank">
          <img :src="getFileUrl(item.url)" :alt="getFileName(item.name)" style="max-height: 69px">
        </a>
        <el-link v-else :href="getFileUrl(item.url)" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(item.name) }} </span>
        </el-link>
        <div v-if="isAdd" class="ele-upload-list__item-content-action">
          <el-link :underline="false" type="danger" @click="handleDelete(item, fileList, uploadUrl)" >删除</el-link>
        </div>
      </li>
    </ul>

  </div>
</template>

<script>

import { actionPost, add, del } from '@/api/data'

export default {
  name: 'FileUpload2',
  props: {
    // 值
    value: {
      type: [String, Object, Array],
      default() {
        return ''
      }
    },
    // 数量限制
    limit: {
      type: Number,
      default: 10
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 50
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['doc', 'xls', 'ppt', 'txt', 'pdf', 'jpg', 'png', 'jpeg', 'xlsx', 'docs']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: false
    },
    // 保存路径头
    pathTitle: {
      type: String,
      default: 'files'
    },
    // 是否编辑
    isAdd: {
      type: Boolean,
      default: true
    },
    fileList: {
      type: Object,
      required: true
    },
    rowId: {
      type: Number,
      default: 0
    },
    uploadUrl: {
      type: String,
      default: ''
    },
    baseUrl: {
      type: String,
      default: process.env.HOST
    },
    foreignkey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // baseUrl: process.env.HOST,
      // uploadFileUrl: process.env.HOST + '/api/common/upload', // 上传的图片服务器地址
      uploadFileUrl: 'common/upload', // 上传的图片服务器地址
      // fileList: [],
      extraParams: {
        path_title: this.pathTitle
      }
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      console.log('fileList4-->', this.fileList)
      return this.isShowTip && (this.fileType || this.fileSize)
    }
  },
  watch: {
    value: {
      handler(val) {
        console.log('val------->', val)
        if (val) {
          let temp = 1
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(',')
          console.log('this.fileList init-->', this.fileList)
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            console.log('item------->', item)
            if (typeof item === 'string') {
              item = { name: item, url: item }
              // item = { name: item.name, url: item.url, id: item.id }
            }
            item.uid = item.uid || new Date().getTime() + temp++
            return item
          })
          console.log('this.fileList00-->', this.fileList)
        } else {
          this.fileList = []
          return []
        }
      },
      deep: true,
      immediate: true
    }
  },
  async created() {
    console.log('fileList2-->', this.fileList)
    console.log('rowId-->', this.rowId)
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          return !!(fileExtension && fileExtension.indexOf(type) > -1)
        })
        if (!isTypeOk) {
          this.$message.error(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`)
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      return true
    },
    // 自定义上传
    uploadAttachment(data, url) {
      if (url) {
        const formData = new FormData()
        formData.append('name', data.file.name)
        formData.append('url', data.file)
        formData.append(this.foreignkey, this.rowId)
        const p = {
          api: url,
          data: formData
        }
        console.log('上传fileList-->', this.fileList)
        add(p).then(res => {
          if (res.code === 200 || res.code === 201) {
            this.$message.success('上传成功')
            // if (item.prop === 'signForAttachments') {
            //   this.fileList2.push({ name: res.data.name, url: res.data.url, id: res.data.id })
            //   this.formData.signForAttachments.push({ name: res.data.name, url: res.data.url })
            // } else {
            //   this.fileList.push({ name: res.data.name, url: res.data.url, id: res.data.id })
            //   this.formData.attachments.push({ name: res.data.name, url: res.data.url })
            // }
            this.fileList.push({ name: res.data.name, url: res.data.url, id: res.data.id })
            this.formData.attachments.push({ name: res.data.name, url: res.data.url })
            this.$emit('input', this.listToString(this.fileList))
          } else {
            this.$message.error(res.detail || this.$t('common.uploadedfail'))
          }
        }).catch(() => {})
      } else {
        console.log('this.uploadFileUrl-->', this.uploadFileUrl)
        const p = {
          api: this.uploadFileUrl,
          data: data
        }
        add(p).then(res => {
          if (res.code === 200 || res.code === 201) {
            this.$message.success('上传成功')
          } else {
            this.$message.error(res.detail || this.$t('common.uploadedfail'))
          }
        }).catch(() => {})
      }
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传失败
    handleUploadError(err) {
      console.log(err)
      this.$message.error('上传失败, 请重试')
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      this.$message.success('上传成功')
      this.fileList.push({ name: res.fileName, url: res.fileName })
      // console.log('上传成功: ', this.listToString(this.fileList))
      this.$emit('input', this.listToString(this.fileList))
      console.log('上传成功this.fileList:', this.fileList)
    },
    // 删除文件
    handleDelete(item, fileList, url = null) {
      console.log('删除url: ', url, item)
      if (url) {
        new Promise((resolve, reject) => {
          this.$confirm(this.$t('common.delecomfirm'), this.$t('common.tips'), {
            confirmButtonText: this.$t('common.sure'),
            cancelButtonText: this.$t('common.cancle'),
            type: 'warning'
          }).then(() => {
            if (!item.id) {
              resolve(true)
              return true
            }
            var api
            if (url) {
              // api = `api/${item.url}/`
              const params = {
                id: item.id,
                api: url,
                data: {}
              }
              console.log('删除params-->', params)
              del(params).then(res => {
                console.log('删除res-->', res)
                if (res.code === 200) {
                  this.$message.success(this.$t('common.deletesuccess'))
                } else if (res.status === 204) {
                  this.$message.success(this.$t('common.deletesuccess'))
                } else {
                  // this.$message.error(this.$t('common.deletefail'))
                }
                resolve(true)
                this.fileList = this.fileList.filter(i => i.id !== item.id)
                this.formData.attachments = this.formData.attachments.filter(i => i.id !== item.id)
                console.log('删除fileList-->', this.fileList)
                // 通知父组件 fileList 已经更新
                // this.$emit('input', this.listToString(this.fileList))
                return true
              }).catch(() => {
                resolve(false)
                return false
              })
            } else {
              api = `api/${this.requestUrl.baseUrl}/delete_file/`
              const params = {
                api: api,
                data: { id: this.rowId, fileName: item.prop }
              }
              console.log('干啥去了?-->')
              actionPost(params).then(res => {
                if (res.code === 200) {
                  this.$message.success(this.$t('common.deletesuccess'))
                } else {
                  this.$message.error(this.$t('common.deletefail'))
                }
                resolve(true)
                return true
              }).catch(() => {
                resolve(false)
                return false
              })
            }
          }).catch(() => {
            reject(false)
            return false
          })
        })
        // this.fileList.splice(item.index, 1)
        // this.$emit('input', this.listToString(this.fileList))
      } else {
        this.fileList.splice(item.index, 1)
        this.$emit('input', this.listToString(this.fileList))
      }
    },
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf('/') > -1) {
        return name.slice(name.lastIndexOf('/') + 1).toLowerCase()
      } else {
        return name
      }
    },
    // 获取文件url
    getFileUrl(url) {
      let mediaPath = url.match(/\/media\/(.*)/)
      if (mediaPath) {
        mediaPath = url.match(/\/media\/(.*)/)[1]
      } else {
        mediaPath = url
      }
      // return mediaPath
      return `${this.baseUrl}/media/${mediaPath}`
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = ''
      separator = separator || ','
      for (const i in list) {
        strs += list[i].url + separator
      }
      return strs !== '' ? strs.substr(0, strs.length - 1) : ''
    }
  }
}
</script>

<style scoped lang='scss'>
.upload-file-uploader {
  margin-bottom: 5px
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px
}
</style>
