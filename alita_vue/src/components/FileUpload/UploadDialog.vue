<template>
  <div>
    <el-button
      v-if="isEdit"
      size="mini"
      type="text"
      @click="subOpenFile"
    >查看</el-button>

    <el-dialog :title="title" :visible.sync="subOpen" :width="dWidth" @close="cancel">
      <file-upload v-model="fileList" :file-list="fileList" :path-title="baseUrl" :detail-edit="isEdit" :row-id="rowId" :upload-url="uploadUrl" :foreignkey="foreignkey"></file-upload>
    </el-dialog>

  </div>
</template>

<script>
// import { getChoiceData } from '@/api/data'
import FileUpload from './FileUpload2.vue'

export default {
  // name: 'ApplicabilityForm',
  components: {
    FileUpload
  },
  dicts: ['criteria_type'],
  props: {
    // subList: {
    //   type: Array,
    //   default: null
    // },
    isEdit: {
      type: Boolean,
      default: false
    },
    rowId: {
      type: Number,
      default: 0
    },
    name: {
      type: String,
      default: ''
    },
    baseUrl: {
      type: String,
      default: ''
    },
    fileList: {
      type: Object,
      required: true
    },
    uploadUrl: {
      type: String,
      default: ''
    },
    foreignkey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // base_url: "",
      loading: false,
      subForm: {},
      subOpen: false,
      title: '上传文件',
      editConditions: this.isEdit,
      // applicabilityList: this.subList,
      expression: '',
      currentRow: null,
      dWidth: '800px',
      prefix_currentRow: null,
      curr_name: '',
      formData: {}
    }
  },
  async created() {
    // await this.get_applicabilityList()
    this.curr_name = this.name
    // console.log('fileList-->', this.fileList)
    // console.log('baseUrl-->', this.baseUrl)
  },
  methods: {
    submitForm() {
      this.subOpen = false
    },
    cancel() {
      this.subOpen = false
    },
    init_option() {
      // for (let i = 0; i < this.applicabilityList.length; i++) {
      //   var element = this.applicabilityList[i]
      //   if (parseInt(element.id) === this.rowId) {
      //     console.log(element)
      //     console.log(element.id)
      //     this.$refs.truckOption.setCurrentRow(element)
      //   }
      // }
    },
    // async get_applicabilityList() {
    //   const api = '/api/truckInquiryPriceOrders/?size=100000&page=1&sort=id'
    //
    //   await getChoiceData(api).then(res => {
    //     console.log(res)
    //     if (res.code === 200) {
    //       console.log(res.data)
    //       this.applicabilityList = res.data
    //       console.log('this.formData0-->', this.formData)
    //     }
    //   })
    // },
    handleCurrentChange(val) {
      this.prefix_currentRow = this.currentRow
      this.curr_name = val.inquiry_num
      if (this.currentRow || !this.rowId) {
        this.currentRow = val
        const data = { 'rowId': val.id }
        this.$emit('send', data)
        this.subOpen = false
        return
      }
      this.currentRow = val
    },
    subOpenFile() {
      this.subOpen = true
      this.$nextTick(() => {
        if (!this.currentRow && this.rowId !== 0) {
          this.init_option()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
