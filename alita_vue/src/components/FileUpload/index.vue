<template>
  <div class="upload-demo">
    <el-upload
      ref="upload"
      :action="uploadFileUrl"
      :data="extraParams"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      class="upload-file-uploader"
    >
      <!-- 上传按钮 -->
      <el-button v-if="isAdd" size="small" type="success">{{ $t('content.clickToUpload') }}</el-button>
      <!-- 上传提示 -->
      <div v-if="showTip" slot="tip" class="el-upload__tip" >
        {{ $t('content.pleaseUpload') }}
        <template v-if="fileSize"> {{ $t('content.notExceeding') }} <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
        <template v-if="fileType"> {{ $t('content.format') }} <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
        {{ $t('content.docs') }}
      </div>
    </el-upload>

    <ul class="upload-file-list el-upload-list el-upload-list--text">
      <li v-for="(item,index) in fileList" :key="item.uid" class="el-upload-list__item ele-upload-list__item-content">
        <el-link :href="`${baseUrl}/media/${item.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(item.name) }} </span>
        </el-link>
        <div v-if="isAdd" class="ele-upload-list__item-content-action">
          <el-link :underline="false" type="danger" @click="handleDelete(item, index)" >删除</el-link>
        </div>
      </li>
    </ul>

  </div>
</template>

<script>

import { actionPost } from '@/api/data'

export default {
  name: 'FileUpload',
  props: {
    // 值
    value: {
      type: [String, Object, Array],
      default() {
        return ''
      }
    },
    // 数量限制
    limit: {
      type: Number,
      default: 10
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 50
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['doc', 'xls', 'ppt', 'txt', 'pdf', 'jpg', 'png', 'jpeg', 'xlsx', 'docs']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: false
    },
    // 保存路径头
    pathTitle: {
      type: String,
      default: 'files'
    },
    // 是否编辑
    isAdd: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      baseUrl: process.env.HOST,
      uploadFileUrl: process.env.HOST + '/api/common/upload', // 上传的图片服务器地址
      removeFileUrl: process.env.HOST + '/api/common/remove',
      fileList: [],
      extraParams: {
        path_title: this.pathTitle
      }
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1
          let list
          if (Array.isArray(val)) {
            list = val
          } else {
            // 智能分割，处理文件名中包含逗号的情况
            const parts = this.value.split(',')
            list = []
            let currentFile = ''

            for (let i = 0; i < parts.length; i++) {
              const part = parts[i].trim()
              if (currentFile) {
                currentFile += ',' + part
              } else {
                currentFile = part
              }

              // 检查是否像一个完整的文件名（包含文件扩展名）
              const hasExtension = /\.[a-zA-Z0-9]{1,10}$/.test(currentFile)
              const isLastPart = i === parts.length - 1

              if (hasExtension || isLastPart) {
                list.push(currentFile)
                currentFile = ''
              }
            }
          }
          // console.log('---list-->>', list)
          this.fileList = list.map(item => {
            if (typeof item === 'string') {
              item = { name: item, url: item }
            }
            item.uid = item.uid || new Date().getTime() + temp++
            return item
          })
        } else {
          this.fileList = []
          return []
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
        if (!isTypeOk) {
          this.$message.error(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`)
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      return true
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传失败
    handleUploadError(err) {
      console.log(err)
      this.$message.error('上传失败, 请重试')
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      this.$message.success('上传成功')
      this.fileList.push({ name: res.fileName, url: res.fileName })
      console.log('---res-->>', this.listToString(this.fileList))
      this.$emit('input', this.listToString(this.fileList))
    },
    // 删除文件
    handleDelete(item, index) {
      console.log('要删除的是啥?-->', this.pathTitle)
      if (this.pathTitle === 'outboundInstructs') {
        // const data = {
        //   // id: item.id,
        //   file_url: item.url
        // }
        actionPost({ api: this.removeFileUrl, data: { attachment_url: item.url }, responseType: 'json' }).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg || 'Success')
            // this.init()
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => {
          this.bus.$emit('fullLoading', false)
        })
      }
      this.fileList.splice(index, 1)
      this.$emit('input', this.listToString(this.fileList))
    },
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf('/') > -1) {
        return name.slice(name.lastIndexOf('/') + 1).toLowerCase()
      } else {
        return ''
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = ''
      separator = separator || ','
      for (const i in list) {
        strs += list[i].url + separator
      }
      return strs !== '' ? strs.substr(0, strs.length - 1) : ''
    }
  }
}
</script>

<style scoped lang='scss'>
.upload-file-uploader {
  margin-bottom: 5px
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px
}
</style>
