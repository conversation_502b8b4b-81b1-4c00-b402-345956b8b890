<template>
  <div>
    <ul class="diy-steps">
      <li v-for="(item, index) in stepList" :key="index">
        <div class="diy-step">
          <div v-if="index === 0 && item.status === 'undone'" :style="'background-image: linear-gradient(' + '90deg' + ', #ffffff, #bbbbbb)'" class="diy-step-line_left"/>
          <div v-if="index === 0 && item.status !== 'undone'" :style="'background-image: linear-gradient(' + '90deg' + ', #ffffff, #4cb45b)'" class="diy-step-line_left"/>
          <div v-else-if="index !== 0 && item.status === 'undone'" :style="'background-image: linear-gradient(' + '0deg' + ', #bbbbbb, #bbbbbb)'" class="diy-step-line_left"/>
          <div v-else-if="index !== 0 && item.status !== 'undone'" :style="'background-image: linear-gradient(' + '0deg' + ', #4cb45b, #4cb45b)'" class="diy-step-line_left"/>

          <div v-if="item.status === 'success'" class="diy-step-box diy-step__success">
            <span>
              <i class="el-icon-check"></i>
            </span>
            <strong>{{ item.label }}</strong>
          </div>

          <div v-else-if="item.status === 'fail'" class="diy-step-box diy-step__fail">
            <span>
              <i class="el-icon-close"></i>
            </span>
            <strong>{{ item.label }}</strong>
          </div>

          <div v-else-if="item.status === 'doing'" class="diy-step-box diy-step__doing">
            <span>
              <i class=""></i>
            </span>
            <strong>{{ item.label }}</strong>
          </div>

          <div v-else-if="item.status === 'undone'" class="diy-step-box diy-step__undone">
            <span>
              <i class=""></i>
            </span>
            <strong>{{ item.label }}</strong>
          </div>

          <div
            v-if="index === stepList.length - 1 && item.status === 'success'"
            :style="'background-image: linear-gradient(90deg, #4cb45b, #ffffff);'"
            class="diy-step-line_right"
          />
          <div
            v-else-if="index === stepList.length - 1 && item.status === 'fail'"
            :style="'background-image: linear-gradient(90deg, #4cb45b, #ffffff);'"
            class="diy-step-line_right"
          />
          <div
            v-else-if="index === stepList.length - 1 && item.status === 'doing'"
            :style="'background-image: linear-gradient(90deg, #bbbbbb, #ffffff);'"
            class="diy-step-line_right"
          />
          <div
            v-else-if="index === stepList.length - 1 && item.status === 'undone'"
            :style="'background-image: linear-gradient(90deg, #bbbbbb, #ffffff);'"
            class="diy-step-line_right"
          />
          <div
            v-else-if="index !== stepList.length - 1 && item.status === 'undone' && (index + 1 <= stepList.length - 1) && stepList[index + 1].status === 'undone'"
            :style="'background-image: linear-gradient(180deg, #bbbbbb, #bbbbbb);'"
            class="diy-step-line_right"
          />
          <div
            v-else-if="index !== stepList.length - 1 && item.status !== 'undone' && (index + 1 <= stepList.length - 1) && stepList[index + 1].status === 'undone'"
            :style="'background-image: linear-gradient(180deg, #bbbbbb, #bbbbbb);'"
            class="diy-step-line_right"
          />
          <div
            v-else-if="index !== stepList.length - 1 && item.status !== 'undone' && (index + 1 <= stepList.length - 1) && stepList[index + 1].status !== 'undone'"
            :style="'background-image: linear-gradient(180deg, #4cb45b, #4cb45b);'"
            class="diy-step-line_right"
          />
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    stepList: {
      type: Array,
      default: () => { return [] }
    }
  },
  data: () => ({

  })
}
</script>

<style lang="scss">
.diy-steps {
  display: flex;
  width: 100%;
  height: 55px;
  margin: 15px auto 0 auto;
  padding: 0;
  position: relative;
  overflow: hidden;

  li {
    width: auto;
    height: 100%;
    flex: 1;
    list-style-type: none;
    font-size: 12px;
    text-align: center;
    position: relative;
    float: left;

    .diy-step {
      width: 100%;
      display: inline-block;
      position: relative;
      z-index: 1;
    }

    .diy-step-line_left {
      //border-bottom: 1px solid transparent;
      //background-image: linear-gradient(90deg, #bbbbbb, #4cb45b);
      width: 50%;
      height: 1px;
      display: inline-block;
      position: absolute;
      top: 9px;
      left: 0;
      z-index: 0;
    }

    .diy-step-line_right {
      //border-bottom: 1px solid transparent;
      //background-image: linear-gradient(180deg, #bbbbbb, #4cb45b);
      width: 50%;
      height: 1px;
      display: inline-block;
      position: absolute;
      top: 9px;
      right: 0;
      z-index: 0;
    }

    .diy-step .diy-step-box {
      position: relative;
      z-index: 1;

      span {
        display: block;
        width: 13px;
        height: 13px;
        border-radius: 100%;
        color: #fff;
        text-align: center;
        margin: 0 auto 8px auto;
      }

      span i {
        display: block;
        width: 13px;
        height: 13px;
        border-radius: 100%;
        line-height: 13px;
      }

      strong {
        letter-spacing: 0.5px;
      }
    }
  }
}

.diy-step__success {
  span {
    border: rgba(55, 171, 71, 0.2) 4px solid;
    border: rgb(215, 238, 218) 4px solid;
  }

  span i {
    background-color: #37ab47;
  }

  strong {
    color: #37ab47;
  }
}

.diy-step__fail {
  span {
    border: rgba(252, 94, 90, 0.2) 4px solid;
    border: rgb(254, 223, 222) 4px solid;
  }

  span i {
    background-color: #fc5e5a;
  }

  strong {
    color: #fc5e5a;
  }
}

.diy-step__doing {
  span {
    border: rgba(94, 124, 224, 0.2) 4px solid;
    border: rgb(223, 229, 249) 4px solid;
  }

  span i {
    background-color: #5e7ce0;
  }

  strong {
    color: #5e7ce0;
  }
}

.diy-step__undone {
  span {
    border: rgba(153, 153, 153, 0.2) 4px solid;
    border: rgb(235, 235, 235) 4px solid;
  }

  span i {
    background-color: #999999;
  }

  strong {
    color: #999999;
  }
}
</style>
