<template>
  <div class="borderTitle">
    <span><span v-if="required" style="color: red">*</span>{{ title }}</span>
    <div style="display: flex;justify-content: center;align-items: center">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
  props: {
    title: {
      type: String,
      default: ''
    },
    required: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.borderTitle {
  position: relative;
  border: 1px solid #dcdfe6;
  width: auto;
  display: inline-block;
  border-radius: 4px;
  padding: 10px;
  margin: 10px;
  > span {
    position: absolute;
    left: 20px;
    top: -11px;
    //width: 50px;
    width:auto;
    text-align: center;
    font-size: 16px;
    color: #606266;
    font-weight: 900;
    background: #fff;
  }
}

</style>
