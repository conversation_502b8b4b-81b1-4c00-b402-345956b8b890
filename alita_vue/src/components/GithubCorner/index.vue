<template>
  <!-- <a
    href="https://github.com/elunez/eladmin"
    target="_blank"
    class="github-corner"
    aria-label="View source on Github"
  > -->
  <!-- <svg
      width="80"
      height="80"
      viewBox="0 0 250 250"
      style="fill:#40c9c6; color:#fff;"
      aria-hidden="true">
      <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"/>
      <path
        d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
        fill="currentColor"
        style="transform-origin: 130px 106px;"
        class="octo-arm"/>
      <path
        d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
        fill="currentColor"
        class="octo-body"/>
    </svg> -->
  <!-- 点我查看版本信息 -->
  <div class="">
    <i class="el-icon-bell btn shake-slow" style="color:green;font-size:20px;cursor:pointer" @click="getMdFile" />
    <el-dialog
      :visible.sync="showMd"
      :fullscreen="true"
      title="">
      <div class="md-box">
        <template v-if="htmlMD">
          <VueMarkdown :source="htmlMD" class="Mdbox"/>
        </template>
      </div>
    </el-dialog>
  </div>
  <!-- </a> -->
</template>

<script>
// import { initData } from '@/api/data'
import axios from 'axios'
import VueMarkdown from 'vue-markdown'

export default {
  components: {
    VueMarkdown
  },
  data() {
    return {
      showMd: false,
      htmlMD: ''
    }
  },
  watch: {
    '$route'(n, o) {
      if (this.showMd) this.showMd = false
    }
  },
  mounted() {
  },
  methods: {
    getMdFile() {
      const url = window.location.origin + '/static/UPDATE.md'
      axios.get(url, {}).then((response) => {
        this.htmlMD = response.data
        this.showMd = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.md-box {
  background-color: rgba(255, 255, 255, .6);
  width: 100vw;
  height: 100vh;
}
.Mdbox {
  width: 1024px;
  height: 100vh;
  margin:0 auto;
  // background-color: #fff;
}
/deep/ .el-dialog__wrapper {
  z-index: 10000000!important;
  width: 100vw;
  background-color: rgba(255, 255, 255, .6);
}

.github-corner:hover .octo-arm {
  animation: octocat-wave 560ms ease-in-out;
}

@keyframes octocat-wave {
  0%,
  100% {
    transform: rotate(0);
  }
  20%,
  60% {
    transform: rotate(-25deg);
  }
  40%,
  80% {
    transform: rotate(10deg);
  }
}

@media (max-width: 500px) {
  .github-corner:hover .octo-arm {
    animation: none;
  }
  .github-corner .octo-arm {
    animation: octocat-wave 560ms ease-in-out;
  }
}
.shake-slow {
  display: inherit;
  animation: shake-slow 5s 1s infinite;
  transform-origin: center center;
}
.shake-freeze,
.shake-constant.shake-constant--hover:hover,
.shake-trigger:hover .shake-constant.shake-constant--hover {
  animation-play-state: running;
}
.shake-freeze:hover,
.shake-trigger:hover .shake-freeze,
.shake-slow:hover,
.shake-trigger:hover .shake-slow {
  animation-play-state: running;
}
@keyframes shake-slow {
  2% {
    transform: translate(2px, -2px) rotate(3.5deg);
  }
  4% {
    transform: translate(7px, 3px) rotate(-0.5deg);
  }
  6% {
    transform: translate(6px, 3px) rotate(0.5deg);
  }
  8% {
    transform: translate(4px, 7px) rotate(0.5deg);
  }
  10% {
    transform: translate(-8px, 3px) rotate(1.5deg);
  }
  12% {
    transform: translate(3px, -6px) rotate(-2.5deg);
  }
  14% {
    transform: translate(2px, -8px) rotate(1.5deg);
  }
  16% {
    transform: translate(2px, 7px) rotate(2.5deg);
  }
  18% {
    transform: translate(1px, 5px) rotate(-1.5deg);
  }
  20% {
    transform: translate(-1px, 0px) rotate(0.5deg);
  }
  22% {
    transform: translate(1px, -2px) rotate(3.5deg);
  }
  24% {
    transform: translate(1px, -8px) rotate(-2.5deg);
  }
  26% {
    transform: translate(-7px, -6px) rotate(3.5deg);
  }
  28% {
    transform: translate(-6px, -5px) rotate(3.5deg);
  }
  30% {
    transform: translate(9px, -7px) rotate(-1.5deg);
  }
  32% {
    transform: translate(3px, 5px) rotate(3.5deg);
  }
  34% {
    transform: translate(10px, -9px) rotate(-1.5deg);
  }
  36% {
    transform: translate(8px, -8px) rotate(3.5deg);
  }
  38% {
    transform: translate(-9px, 5px) rotate(0.5deg);
  }
  40% {
    transform: translate(-3px, -9px) rotate(1.5deg);
  }
  42% {
    transform: translate(5px, 7px) rotate(1.5deg);
  }
  44% {
    transform: translate(-4px, 1px) rotate(-0.5deg);
  }
  46% {
    transform: translate(3px, 1px) rotate(1.5deg);
  }
  48% {
    transform: translate(-9px, 6px) rotate(2.5deg);
  }
  50% {
    transform: translate(1px, 3px) rotate(-2.5deg);
  }
  52% {
    transform: translate(7px, -9px) rotate(2.5deg);
  }
  54% {
    transform: translate(10px, 4px) rotate(-0.5deg);
  }
  56% {
    transform: translate(3px, -5px) rotate(1.5deg);
  }
  58% {
    transform: translate(4px, -3px) rotate(-0.5deg);
  }
  60% {
    transform: translate(9px, 5px) rotate(-1.5deg);
  }
  62% {
    transform: translate(8px, -9px) rotate(0.5deg);
  }
  64% {
    transform: translate(-8px, -9px) rotate(-1.5deg);
  }
  66% {
    transform: translate(-9px, -1px) rotate(1.5deg);
  }
  68% {
    transform: translate(-4px, -2px) rotate(3.5deg);
  }
  70% {
    transform: translate(-2px, 6px) rotate(-2.5deg);
  }
  72% {
    transform: translate(3px, 1px) rotate(1.5deg);
  }
  74% {
    transform: translate(-4px, -3px) rotate(2.5deg);
  }
  76% {
    transform: translate(3px, 1px) rotate(-0.5deg);
  }
  78% {
    transform: translate(8px, 0px) rotate(2.5deg);
  }
  80% {
    transform: translate(10px, -6px) rotate(2.5deg);
  }
  82% {
    transform: translate(-4px, 6px) rotate(1.5deg);
  }
  84% {
    transform: translate(-2px, 2px) rotate(3.5deg);
  }
  86% {
    transform: translate(-5px, 3px) rotate(2.5deg);
  }
  88% {
    transform: translate(7px, -4px) rotate(2.5deg);
  }
  90% {
    transform: translate(10px, 9px) rotate(-0.5deg);
  }
  92% {
    transform: translate(8px, 4px) rotate(0.5deg);
  }
  94% {
    transform: translate(7px, 10px) rotate(2.5deg);
  }
  96% {
    transform: translate(2px, -2px) rotate(-0.5deg);
  }
  98% {
    transform: translate(-1px, -5px) rotate(3.5deg);
  }
  0%,
  100% {
    transform: translate(0, 0) rotate(0);
  }
}
.shake-slow:hover,
.shake-trigger:hover .shake-slow,
.shake-slow.shake-freeze,
.shake-slow.shake-constant {
  animation-name: shake-slow;
  animation-duration: 5s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}
</style>
