<template>
  <div>
    <el-button
      size="mini"
      type="text"
      @click="subOpen=true"
    >适用条件
    </el-button>

    <el-dialog v-if="editConditions" :title="title" :visible.sync="subOpen" width="1200px" append-to-body>
      <el-row style="margin-bottom: 10px">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增
          </el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="applicabilityList" @selection-change="handleSelectionChange">
        <!--          <el-table-column type="selection" width="55" align="center" />-->

        <el-table-column label="左括号" align="center" prop="left_bracket">
          <template slot-scope="scope">
            <el-select v-model="scope.row.left_bracket" placeholder="请选择左括号" clearable @change="showExpression">
              <el-option value="(">(</el-option>
              <el-option value="((">((</el-option>
              <el-option value="(((">(((</el-option>
              <el-option value="((((">((((</el-option>
              <el-option value="(((((">(((((</el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="计费要素" align="center" prop="criteria_code" width="150">
          <template slot-scope="scope">
            <el-select v-model="scope.row.criteria_code" placeholder="请选择计费要素" clearable @change="showExpression">
              <el-option value="length" label="包裹长"></el-option>
              <el-option value="width" label="包裹宽"></el-option>
              <el-option value="height" label="包裹高"></el-option>
              <el-option value="sum_width_height" label="包裹宽高之和"></el-option>
              <el-option value="sum_length_width" label="包裹长宽之和"></el-option>
              <el-option value="sum_length_width_height" label="包裹长宽高之和"></el-option>
              <el-option value="weight" label="包裹重量"></el-option>
              <el-option value="volume" label="包裹体积"></el-option>
              <el-option value="perimeter" label="包裹周长【长+(宽+高)*2】"></el-option>
              <el-option value="purpose_code" label="目的分区编码"></el-option>
              <el-option value="bubble_ratio" label="泡比"></el-option>
              <el-option value="parcel_skus_count" label="单个包裹SKU数量"></el-option>
              <el-option value="is_signature" label="是否需要签名"></el-option>
              <el-option value="is_bill_of_lading" label="是否清关提单计费"></el-option>

              <!--<el-option
                v-for="dict in dict.type.criteria_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>-->
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="条件" align="center" prop="match_method">
          <template slot-scope="scope">
            <el-select v-model="scope.row.match_method" placeholder="请选择条件" clearable>
              <el-option v-for="condition in getConditions(scope.row.criteria_code)" :key="condition.value" :label="condition.label" :value="condition.value"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!--        <el-table-column label="条件" align="center" prop="match_method">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-select v-model="scope.row.match_method" placeholder="请选择条件" clearable @change="showExpression">-->
        <!--              <el-option value="eq" label="等于"></el-option>-->
        <!--              <el-option value="gteq" label="大于等于"></el-option>-->
        <!--              <el-option value="lteq" label="小于等于"></el-option>-->
        <!--              <el-option value="gt" label="大于"></el-option>-->
        <!--              <el-option value="lt" label="小于"></el-option>-->
        <!--            </el-select>-->
        <!--          </template>-->
        <!--        </el-table-column>-->

        <el-table-column label="值" align="center" prop="condition_value">
          <template slot-scope="scope">
            <el-input v-model="scope.row.condition_value" placeholder="请输入值" clearable @change="showExpression"/>
          </template>
        </el-table-column>

        <!--          <el-table-column label="值类型" align="center" prop="criteriaType" />-->

        <el-table-column label="右括号" align="center" prop="right_bracket">
          <template slot-scope="scope">
            <el-select v-model="scope.row.right_bracket" placeholder="请选择右括号" clearable @change="showExpression">
              <el-option value=")" label=")"></el-option>
              <el-option value="))" label="))"></el-option>
              <el-option value=")))" label=")))"></el-option>
              <el-option value="))))" label="))))"></el-option>
              <el-option value=")))))" label=")))))"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="关系" align="center" prop="logical_operator">
          <template slot-scope="scope">
            <el-select v-model="scope.row.logical_operator" placeholder="请选择连接关系" clearable @change="showExpression">
              <el-option value="&&" label="并且"></el-option>
              <el-option value="||" label="或者"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!--          <el-table-column label="顺序" align="center" prop="serial_code" />-->

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <!--              <el-button-->
            <!--                size="mini"-->
            <!--                type="text"-->
            <!--                icon="el-icon-edit"-->
            <!--                @click="handleUpdate(scope.row)"-->
            <!--                v-hasPermi="['oms:applicability:edit']"-->
            <!--              >修改</el-button>-->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除
            </el-button>
          </template>
        </el-table-column>

      </el-table>
      <div style="margin-top: 10px;font-size: 18px">
        <span>表达式:{{ expression }}</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('content.Ok_1') }}</el-button>
        <el-button @click="cancel">{{ $t('content.Cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog v-else :title="title" :visible.sync="subOpen" width="1200px" append-to-body>
      <el-row style="margin-bottom: 10px">
        <el-col :span="1.5">
          <el-button
            v-show="detailEdit"
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增
          </el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="applicabilityList" @selection-change="handleSelectionChange">
        <!--          <el-table-column type="selection" width="55" align="center" />-->

        <el-table-column label="左括号" align="center" prop="left_bracket">
          <template slot-scope="scope">
            <el-select v-model="scope.row.left_bracket" disabled placeholder="请选择左括号" clearable @change="showExpression">
              <el-option value="(">(</el-option>
              <el-option value="((">((</el-option>
              <el-option value="(((">(((</el-option>
              <el-option value="((((">((((</el-option>
              <el-option value="(((((">(((((</el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="计费要素" align="center" prop="criteria_code" width="150">
          <template slot-scope="scope">
            <el-select v-model="scope.row.criteria_code" disabled placeholder="请选择计费要素" clearable @change="showExpression">
              <el-option value="length" label="包裹长"></el-option>
              <el-option value="width" label="包裹宽"></el-option>
              <el-option value="height" label="包裹高"></el-option>
              <el-option value="weight" label="包裹重量"></el-option>
              <el-option value="volume" label="包裹体积"></el-option>
              <el-option value="perimeter" label="包裹周长【长+(宽+高)*2】"></el-option>
              <el-option value="purpose_code" label="目的分区编码"></el-option>
              <!--<el-option
                v-for="dict in dict.type.criteria_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>-->
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="条件" align="center" prop="match_method">
          <template slot-scope="scope">
            <el-select v-model="scope.row.match_method" disabled placeholder="请选择条件" clearable>
              <el-option v-for="condition in getConditions(scope.row.criteria_code)" :key="condition.value" :label="condition.label" :value="condition.value"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!--        <el-table-column label="条件" align="center" prop="match_method">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-select v-model="scope.row.match_method" placeholder="请选择条件" clearable @change="showExpression">-->
        <!--              <el-option value="eq" label="等于"></el-option>-->
        <!--              <el-option value="gteq" label="大于等于"></el-option>-->
        <!--              <el-option value="lteq" label="小于等于"></el-option>-->
        <!--              <el-option value="gt" label="大于"></el-option>-->
        <!--              <el-option value="lt" label="小于"></el-option>-->
        <!--            </el-select>-->
        <!--          </template>-->
        <!--        </el-table-column>-->

        <el-table-column label="值" align="center" prop="condition_value">
          <template slot-scope="scope">
            <el-input v-model="scope.row.condition_value" disabled placeholder="请输入值" clearable @change="showExpression"/>
          </template>
        </el-table-column>

        <!--          <el-table-column label="值类型" align="center" prop="criteriaType" />-->

        <el-table-column label="右括号" align="center" prop="right_bracket">
          <template slot-scope="scope">
            <el-select v-model="scope.row.right_bracket" disabled placeholder="请选择右括号" clearable @change="showExpression">
              <el-option value=")" label=")"></el-option>
              <el-option value="))" label="))"></el-option>
              <el-option value=")))" label=")))"></el-option>
              <el-option value="))))" label="))))"></el-option>
              <el-option value=")))))" label=")))))"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="关系" align="center" prop="logical_operator">
          <template slot-scope="scope">
            <el-select v-model="scope.row.logical_operator" disabled placeholder="请选择计费要素" clearable @change="showExpression">
              <el-option value="&&" label="并且"></el-option>
              <el-option value="||" label="或者"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!--          <el-table-column label="顺序" align="center" prop="serial_code" />-->

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <!--              <el-button-->
            <!--                size="mini"-->
            <!--                type="text"-->
            <!--                icon="el-icon-edit"-->
            <!--                @click="handleUpdate(scope.row)"-->
            <!--                v-hasPermi="['oms:applicability:edit']"-->
            <!--              >修改</el-button>-->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              disabled
              @click="handleDelete(scope.row)"
            >删除
            </el-button>
          </template>
        </el-table-column>

      </el-table>
      <div style="margin-top: 10px;font-size: 18px">
        <span>表达式:{{ expression }}</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-show="detailEdit" type="primary" @click="submitForm">{{ $t('content.Ok_1') }}</el-button>
        <el-button v-show="detailEdit" @click="cancel">{{ $t('content.Cancel') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

export default {
  name: 'ApplicabilityForm',
  dicts: ['criteria_type'],
  props: {
    subList: {
      type: Array,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      subForm: {},
      subOpen: false,
      title: '新增/编辑适用条件',
      editConditions: this.isEdit,
      applicabilityList: this.subList,
      expression: ''
    }
  },
  created() {
    this.showExpression()
  },
  methods: {
    handleAdd() {
      if (!this.applicabilityList) {
        this.applicabilityList = []
      }
      this.applicabilityList.push({
        charge: null,
        left_bracket: '',
        criteria_code: '',
        match_method: '',
        condition_value: '',
        criteria_type: '',
        right_bracket: '',
        logical_operator: '',
        serial_code: this.applicabilityList ? this.applicabilityList.length : 0
      })
    },
    handleSelectionChange() {

    },
    submitForm() {
      for (var i = 0; i < this.applicabilityList.length - 1; i++) {
        console.log(this.applicabilityList[i].logical_operator)
        if (this.applicabilityList[i].logical_operator === '') {
          this.$message.error('不能添加，请注意关系的填写')
          return
        }
      }
      if (this.applicabilityList.length !== 0 && this.applicabilityList[this.applicabilityList.length - 1].logical_operator !== '') {
        this.$message.error('不能添加，请将最后一个条件的关系置为空')
        return
      }
      this.subOpen = false
    },
    cancel() {
      this.subOpen = false
    },
    showExpression() {
      const _that = this
      this.expression = ''
      this.applicabilityList.forEach(item => {
        _that.expression += item.left_bracket + ' ' +
          item.criteria_code + ' ' +
          item.match_method + ' ' +
          item.condition_value + ' ' +
          item.right_bracket + ' ' +
          item.logical_operator + ' '
      })
    },
    handleDelete(row) {
      const index = this.applicabilityList.findIndex(v => (v.serial_code === row.serial_code))
      this.$delete(this.applicabilityList, index)
      this.showExpression()

      let i = 0
      this.applicabilityList.forEach(item => {
        item.serial_code = i
        i++
      })
    },

    updateConditions() {
      // 在这里更新条件列表
    },
    getConditions(criteriaCode) {
      // 根据计费要素的值返回对应的条件列表
      if (criteriaCode === 'purpose_code') {
        return [
          // { value: 'eq', label: '是' },
          // { value: 'neq', label: '否' },
          { value: 'belongs', label: '属于' },
          { value: 'not_belongs', label: '不属于' }
        ]
      } else if (criteriaCode === 'is_signature') {
        return [
          { value: 'belongs', label: '属于' }
        ]
      } else {
        // 其他计费要素的条件列表
        return [
          { value: 'eq', label: '等于' },
          { value: 'gteq', label: '大于等于' },
          { value: 'lteq', label: '小于等于' },
          { value: 'gt', label: '大于' },
          { value: 'lt', label: '小于' }
        ]
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
