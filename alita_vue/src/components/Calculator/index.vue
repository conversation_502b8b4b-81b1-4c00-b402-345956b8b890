<template>
  <div>
    <transition
      appear
      enter-active-class="animated zoomIn"
      leave-active-class="animated zoomOut"
      appear-class="animated zoomIn"
    >
      <div v-if="showCalc" id="calculator">
        <!-- 显示结果区域 -->
        <div id="display">
          <div class="enterValue">
            {{ enterValue }}
            <span v-if="!enterValue">请输入</span>
          </div>
          <div class="result">
            {{ result }}
          </div>
        </div>
        <!-- 输入区域 -->
        <div id="enter">
          <ul>
            <li v-for="(item,index) in items" :key="index">
              <ul>
                <li v-for="(key,index) in item" :key="index" :id="key.className" @click="enterKey(key.value, key.className)">
                  {{ key.value }}
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <!-- 关闭按钮 -->
        <i class="close-btn el-icon-close" @click="showCalc=false"/>
      </div>
    </transition>
    <!-- 抽屉 -->
    <div class="drawer-box">
      <el-drawer
        :visible.sync="drawerShow"
        title=""
        direction="rtl"
        size="50%">
        <el-table :data="tableData" border>
          <el-table-column v-for="(item,index) in 7" :key="item" label="">
            <template slot-scope="scope">
              <el-input v-model="scope.row['val'+index]"/>
            </template>
          </el-table-column>
        </el-table>
      </el-drawer>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Calculator',
  data() {
    return {
      drawerShow: false,
      showCalc: false,
      numberType: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, '.'],
      calculateType: ['+', '-', '*', '/'],
      // 计算器项
      items: {
        row1: [
          {
            value: 'C',
            idx: 0,
            className: 'clear'
          },
          {
            value: '(',
            idx: 0,
            className: 'brackets-left'
          },
          {
            value: ')',
            idx: 0,
            className: 'brackets-right'
          }, {
            value: '/',
            idx: 0,
            className: 'divide'
          }
        ],
        row2: [
          {
            value: 7,
            idx: 0,
            className: 'number7'
          },
          {
            value: 8,
            idx: 1,
            className: 'number8'
          }, {
            value: 9,
            idx: 2,
            className: 'number9'
          }, {
            value: '*',
            idx: 3,
            className: 'multiply'
          }
        ],
        row3: [
          {
            value: 4,
            idx: 0,
            className: 'number4'
          },
          {
            value: 5,
            idx: 1,
            className: 'number5'
          }, {
            value: 6,
            idx: 2,
            className: 'number6'
          }, {
            value: '-',
            idx: 3,
            className: 'minus'
          }
        ],
        row4: [
          {
            value: 1,
            idx: 0,
            className: 'number1'
          },
          {
            value: 2,
            idx: 1,
            className: 'number2'
          }, {
            value: 3,
            idx: 2,
            className: 'number3'
          }, {
            value: '+',
            idx: 3,
            className: 'plus'
          }
        ],
        row5: [
          {
            value: 'DEL',
            idx: 0,
            className: 'delete'
          },
          {
            value: 0,
            idx: 1,
            className: 'number0'
          }, {
            value: '.',
            idx: 2,
            className: 'dot'
          }, {
            value: '=',
            idx: 3,
            className: 'equal'
          }
        ]
      },
      enterValue: '',
      result: ''
    }
  },
  computed: {
    tableData() {
      return new Array(28).fill(1).map((i, index) => {
        return {
          ['val' + index]: ''
        }
      })
    }
  },
  created() {
    this.bus.$on('togglePlugin', (type) => {
      if (type === 'showCalc') {
        this.showCalc = !this.showCalc
      } else if (type === 'drawerShow') {
        this.drawerShow = !this.drawerShow
      }
    })
  },
  methods: {
    enterKey(value, id) {
      // 点击效果
      this.clickEffect(value, id)
      switch (value) {
        case 0:
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
        case 7:
        case 8:
        case 9:
        case '(':
        case ')':
        case '.':
          // 数字处理
          this.enterNumber(value)
          break
        case '+':
        case '-':
        case '*':
        case '/':
          // 加减乘除字处理
          this.enterNumber(value)
          break
        case '=':
          // 计算结果
          // 加减乘除字处理
          this.getResult()
          break
        case 'C':
          this.enterValue = ''
          this.result = ''
          break
        case 'DEL':
          this.result = ''
          var old_enter_len = this.enterValue.length
          if (old_enter_len !== 0) {
            this.enterValue = this.enterValue.substr(0, old_enter_len - 1)
          }
          break
      }
    },
    // 输入限制
    enterNumber(val, id) {
      // 判断之前输入的表达式
      const old_val_length = this.enterValue.length
      if (old_val_length !== 0) {
        // 获取最后一个字符
        const lastStr = this.enterValue.charAt((this.enterValue.length - 1))
        if (val === '.' && (lastStr === '.' || this.calculateType.includes(this.enterValue.charAt(old_val_length - 1)))) return
        // 最后一个是小数点然后输入的是符号删除小数点
        if (this.calculateType.includes(val) && this.enterValue.charAt(old_val_length - 1) === '.') {
          this.enterValue = this.enterValue.substr(0, old_val_length - 1)
        }
        // 最后一位为加减乘除就不能再输入加减乘除
        if (this.calculateType.includes(lastStr) && this.calculateType.includes(val)) return
        // 有输入+-*/
        if (this.enterValue.split('').some(i => this.calculateType.includes(i))) {
          // 获取最后一个符号后面的字符串
          const last_number = this.enterValue.substr(this.enterValue.length - this.enterValue.split('').reverse().findIndex(i => this.calculateType.includes(i)))
          // 如果输入的是.  判断最后的数字有没有输入点  或者 最后一个数字已经有小数点
          if (val === '.' && last_number.includes('.')) return
        } else {
          // 没有输入+-*/    即为纯数字
          // 只有一个数字直接判断整个数字
          if (val === '.' && this.enterValue.includes('.')) return
          // 没有输入小数点即整数输入了0后就不能输入了
          if (!this.enterValue.includes('.') && Number(val) === 0 && Number(this.enterValue) === 0) return
        }
      } else if (this.calculateType.includes(val) || val === '.') {
        return
      }
      this.enterValue += val
    },
    enterCalculate(val) {

    },
    // 计算结果
    getResult() {
      if (this.enterValue.length === 0) return
      const old_val_length = this.enterValue.length
      const last_str = this.enterValue.charAt(old_val_length - 1)
      if (last_str === '.' || this.calculateType.includes(last_str)) {
        this.enterValue = this.enterValue.substr(0, old_val_length - 1)
      }
      if (!this.enterValue.split('').some(i => this.calculateType.includes(i))) {
        return
      }
      /* eslint-disable */
      this.result = math.format(math.evaluate(this.enterValue),10)
    },
    clickEffect(val, id) {
      const el = document.querySelector('#' + id)
      el.style.backgroundColor = 'pink'
      el.style.colorcolor = '#fff'
      setTimeout(() => {
        el.style.backgroundColor = '#fff'
        el.style.colorcolor = '#9e7676'
      }, 100)
    }
  }
}
</script>

<style lang="scss">
// 计算器
#calculator {
  position: relative;
  box-shadow: 0 0 10px 1px #ccc;
  z-index: 9999;
  width: 300px;
  height: 500px;
  position: fixed;
  padding: 16px 14px 8px 14px;
  top: 45px;
  right:10px;
  border: 1px solid #d9cccc;
  border-radius: 4px;
  background-color: #fff;
  .close-btn {
    position: absolute;
    top: 0px;
    right: 0px;
    padding: 4px;
    cursor: pointer;
    font-weight: bold;
    &:hover{
      transform: rotate(180deg) scale(1.3);
      transition: .4s all;
    }
  }
  #display{
    height: 200px;
    padding-top: 6px;
    border-bottom: 1px solid #eee;
    .enterValue {
      height:120px;
      word-break: break-all;
      word-break: break-word;
      span {
        color:#ccc;
        user-select: none;
      }
    }
    .result {
      text-align: right;
    }
  }
  #enter {
    height: 276px;
    ul {
        padding: 0;
        margin: 0;
        height: 100%;
        display: flex;
        list-style: none;
        flex-direction: column;
        align-items: space-around;
        justify-content: space-around;
        font-size: 20px;
        li {
          list-style: none;
          ul {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            li {
              flex-flow: 1;
              cursor: pointer;
              user-select: none;
              width: 40px;
              color: #9e7676;
              height: 40px;
              text-align: center;
              line-height: 40px;
            }
          }
        }
    }
  }
}
// 抽屉
.drawer-box {
  /deep/ #el-drawer__title{
    margin-bottom: 0px;
    padding-top: 8px;
    padding-bottom: 8px;
  }
  /deep/ .el-table__header-wrapper {
    height: 0px;
  }
  /deep/ .el-input__inner {
    border: none;
    padding: 0;
  }
  /deep/ td {
    padding: 0px;
    border-color: #606266;
  }
  /deep/ .el-table--border {
    border-color: #606266!important;
  }
}
.animated {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}
@-webkit-keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    50% {
        opacity: 1
    }
}

@keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    50% {
        opacity: 1
    }
}

.zoomIn {
    -webkit-animation-name: zoomIn;
    animation-name: zoomIn
}
@-webkit-keyframes zoomOut {
    0% {
        opacity: 1
    }

    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    to {
        opacity: 0
    }
}

@keyframes zoomOut {
    0% {
        opacity: 1
    }

    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    to {
        opacity: 0
    }
}

.zoomOut {
    -webkit-animation-name: zoomOut;
    animation-name: zoomOut
}
</style>
