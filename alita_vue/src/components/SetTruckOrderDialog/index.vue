<template>
  <div>
    <el-dialog :title="title" :visible.sync="openSet" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="卡车单号" prop="truck_order_id">
          <el-select v-model="form.truck_order_id" clearable placeholder="请选择卡车单" class="filter-item" style="width: 200px">
            <el-option v-for="item in truckOrderList" :key="item.id" :label="item.truck_order_num+ ',' +item.car_number" :value="item.id"/>
          </el-select>
        </el-form-item>

        <el-table :data="orders" >
          <el-table-column label="清关单号" align="center" prop="clearance_num" />
          <el-table-column label="预计包裹数量" align="center" prop="pre_package_num" />
          <el-table-column label="转运包裹数量" align="center" prop="package_num" >
            <template slot-scope="scope">
              <el-input v-model="form.package_nums[scope.$index]" :placeholder="$t('content.Required')" size="medium" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitSetOrder()">{{ $t('common.sure') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import { getChoiceData, actionPost } from '@/api/data'

export default {
  name: 'SetTruckOrderDialog',
  props: {
    orders: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      title: '配置卡车单',
      openSet: false,
      form: {
        truck_order_id: null,
        package_nums: []
      },
      rules: [],
      truckOrderList: []
    }
  },
  watch: {
    orders(newValue) {
      console.log('newValue2-->', newValue)
    }
  },
  created() {
  },
  methods: {
    open() {
      this.openSet = true
      this.getTruckOrderList()
    },
    handleClose() {
      this.openSet = false
    },
    getTruckOrderList() {
      const url = 'api/customsClearanceTruckOrders/all_truck_order/'
      const params = {}
      getChoiceData(url, params).then(res => {
        this.truckOrderList = res.data
      })
    },
    submitSetOrder() {
      console.log(this.form)
      const api = 'api/customsClearanceOrders/set_truck_num/'
      this.form['ids'] = this.orders.map(i => i.id)
      actionPost({ api, data: this.form }).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg || '操作成功')
          this.openSet = false
          this.$emit('execute', { api: '/api/customsClearanceOrders/' })
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
        // this.bus.$emit('fullLoading', false)
      }).catch(() => { this.bus.$emit('fullLoading', false) })
    }
  }
}

</script>
