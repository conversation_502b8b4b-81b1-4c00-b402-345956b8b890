<template>
  <div>
    <el-button
      v-if="isEdit"
      size="mini"
      type="text"
      @click="subOpenFile"
    >选择询价单 {{ curr_name || name }}
    </el-button>
    <el-lable
      v-else
      size="mini"
      type="text"
    >{{ curr_name || name }}
    </el-lable>

    <el-dialog :title="title" :visible.sync="subOpen" :width="dWidth">

      <el-table ref="truckOption" :data="applicabilityList" highlight-current-row @current-change="handleCurrentChange">
        <el-table-column label="询价单号" align="center" prop="inquiry_num">
        </el-table-column>
        <el-table-column label="收件人国家" align="center" prop="buyer_country_code">
        </el-table-column>
        <el-table-column label="报价" align="center" prop="inquiry_price">
        </el-table-column>

      </el-table>
    </el-dialog>

  </div>
</template>

<script>
import { getChoiceData } from '@/api/data'
export default {
  // name: 'ApplicabilityForm',
  dicts: ['criteria_type'],
  props: {
    subList: {
      type: Array,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    rowId: {
      type: Number,
      default: 0
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // base_url: "",
      loading: false,
      subForm: {},
      subOpen: false,
      title: '选择合适的询价单',
      editConditions: this.isEdit,
      applicabilityList: this.subList,
      expression: '',
      currentRow: null,
      dWidth: '1200px',
      prefix_currentRow: null,
      curr_name: ''
    }
  },
  async created() {
    await this.get_applicabilityList()
    this.curr_name = this.name
  },
  methods: {
    submitForm() {
      this.subOpen = false
    },
    cancel() {
      this.subOpen = false
    },
    init_option() {
      for (let i = 0; i < this.applicabilityList.length; i++) {
        var element = this.applicabilityList[i]
        if (parseInt(element.id) === this.rowId) {
          console.log(element)
          console.log(element.id)
          this.$refs.truckOption.setCurrentRow(element)
        }
      }
    },
    async get_applicabilityList() {
      const api = '/api/truckInquiryPriceOrders/?size=100000&page=1&sort=id'

      await getChoiceData(api).then(res => {
        console.log(res)
        if (res.code === 200) {
          console.log(res.data)
          this.applicabilityList = res.data
          console.log('this.formData0-->', this.formData)
        }
      })
    },
    handleCurrentChange(val) {
      this.prefix_currentRow = this.currentRow
      this.curr_name = val.inquiry_num
      if (this.currentRow || !this.rowId) {
        this.currentRow = val
        const data = { 'rowId': val.id }
        this.$emit('send', data)
        this.subOpen = false
        return
      }
      this.currentRow = val
    },
    subOpenFile() {
      this.subOpen = true
      this.$nextTick(() => {
        if (!this.currentRow && this.rowId !== 0) {
          this.init_option()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
