<template>
  <el-dropdown trigger="click" class="international" @command="handleSetLanguage">
    <div style="padding-right:12px;">
      <!-- <svg-icon style="fill:#fff;cursor:pointer;margin:0 4px;" class-name="international-icon" icon-class="language" /> -->
      <img style="width:20px;transform:translateY(2px); cursor:pointer;" src="./lang.png" alt="">
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :disabled="language==='zh'" command="zh">
        中文
      </el-dropdown-item>
      <el-dropdown-item :disabled="language==='en'" command="en">
        English
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  computed: {
    language() {
      return this.$store.getters.language
    }
  },
  methods: {
    handleSetLanguage(lang) {
      this.$i18n.locale = lang
      this.$store.dispatch('setLanguage', lang)
      localStorage.setItem('locale', lang) // 语言选择记录
      this.$message({
        message: `${lang === 'zh' ? '切换语言成功' : 'Switch Language Success'}`,
        type: 'success'
      })

      console.log(this.$store.getters.language)
      // 添加页面刷新
      window.location.reload()
    }
  }
}
</script>
