<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    append-to-body
    @close="close"
  >
    <el-card shadow="never">
      <div slot="header" class="clearfix">
        <div class="header-container">
          <div class="left-content">
            <div :style="headerStyle">
              <i :class="currentClass" :style="currentStyle"></i>
            </div>
            <div style="margin-left: 10px;margin-top: 5px;">
              <div>
                <span>{{ orderNum }}</span><span style="margin-left: 5px;">({{ refNum }})</span>
                <i class="el-icon-copy-document tag" style="color: #bbbbbb;" @click="copy(orderNum)"></i>
              </div>
              <div style="margin-top: 5px;"><span>{{ newestTrackName }}</span></div>
            </div>
            <div style="margin-top: 5px;margin-left: 60px;">
              <span style="margin-right: 10px;">{{ country_code }}</span>
              <i class="el-icon-s-promotion" style="color: #909399;"></i>
              <span style="margin-left: 10px;">{{ buyer_country_code }}</span>
            </div>
            <div style="margin-top: 5px;margin-left: 30px;">
              <span style="margin-right: 10px;">产品：{{ product_name }}</span>
            </div>
            <div style="margin-top: 5px;margin-left: 30px;">
              <span style="margin-right: 10px;">仓库编码：{{ receiver_name }}</span>
            </div>
            <div style="margin-top: 5px;margin-left: 30px;">
              <span style="margin-right: 10px;">邮编：{{ buyer_postcode }}</span>
            </div>
            <div style="margin-top: 5px;margin-left: 30px;">
              <span style="margin-right: 10px;">件数：{{ carton }}</span>
            </div>
          </div>
          <el-button size="small" class="export-button" @click.stop="export_pod(trackData.id)">导出POD文件</el-button>
        </div>
      </div>

      <div class="card-body">
        <div style="text-align: center; margin-bottom: 20px">
          <h3>{{ newestTrackInfo }}</h3>
        </div>

        <SkyStep :step-list="trackStepList"/>

        <div class="block">
          <div><strong>{{ newestTrackRemark }}</strong></div>
          <div style="margin-top: 30px;">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in trackDetails"
                :key="index"
                :icon="activity.icon"
                :type="activity.type"
                :color="activity.color"
                :size="activity.size"
                :timestamp="activity.timestamp">
                {{ activity.content }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>

      </div>

    </el-card>
  </el-dialog>
</template>

<script>
import SkyStep from '@/components/SkyStep'
import Clipboard from 'clipboard'
import { actionPost } from '@/api/data'

export default {
  name: 'TrackInfoDialog',
  components: {
    SkyStep
  },
  props: {
  },
  data() {
    return {
      title: '查看轨迹',
      dialogFormVisible: false,
      trackData: [],
      orderNum: '',
      refNum: '',
      country_code: '',
      buyer_country_code: '',
      newestTrackName: '未知',
      newestTrackInfo: '无最新轨迹更新',
      newestTrackRemark: '-',
      product_name: '',
      receiver_name: '',
      buyer_postcode: '',
      carton: 0,
      item_id: null,
      trackDetails: [],
      trackInfoList2: [],
      trackInfoList3: [],
      trackStepList: [],
      currentClass: 'el-icon-circle-check',
      currentStyle: {
        color: '#fff'
      },
      headerStyle: {
        'border-radius': '8px',
        'background-color': '#67C23A',
        padding: '15px',
        display: 'flex',
        'align-items': 'center'
      },
      fbmTrackStepList: [
        { label: '已提交', orderStatus: ['WO'] },
        { label: '已审核', orderStatus: ['VC'] },
        { label: '已收货', orderStatus: ['IW'] },
        { label: '已报关', orderStatus: ['DE'] },
        { label: '已出仓', orderStatus: ['OWH'] },
        { label: '已离港/已起飞', orderStatus: ['SO'] },
        { label: '已到港/已降落', orderStatus: ['AR'] },
        { label: '已清关', orderStatus: ['CC'] },
        { label: '已到达海外仓', orderStatus: ['IWW'] },
        { label: '派送中', orderStatus: ['OOD'] },
        { label: '已签收', orderStatus: ['FC', 'SF'] }
      ],
      defaultTrackStepList: [
        { label: '已预报', orderStatus: ['PDC'] },
        { label: '已拦截', orderStatus: ['ITP'] },
        { label: '已部分入仓', orderStatus: ['PW'] },
        { label: '已全部入仓', orderStatus: ['AW'] },
        { label: '已确认入仓数据', orderStatus: ['CWED'] },
        { label: '已出国内仓', orderStatus: ['OW'] },
        { label: '已离港', orderStatus: ['DEP'] },
        { label: '转运', orderStatus: ['TF'] },
        { label: '已签收', orderStatus: ['FC', 'SF'] }
      ]
    }
  },
  created() {},
  methods: {
    copy(data) {
      // console.log('data是啥-->', data, typeof data)
      const clipboard = new Clipboard('.tag', {
        text: () => data
      })
      clipboard.on('success', () => {
        this.$message.success(`${data} 复制成功`)
        clipboard.destroy()
      })
      clipboard.on('error', () => {
        this.$message.error('复制失败')
        clipboard.destroy()
      })
      // clipboard.onClick()
    },
    async getOrderTrack(row, url) {
      // await actionPost({ api: `api/${url}/get_customer_order_track/`, data: { id: row.id }}).then(res => {
      //   if (res.code === 200) {
      //     return res.data
      //   } else {
      //     this.$message.error(res.msg || res.detail || res.message)
      //   }
      //   this.bus.$emit('fullLoading', false)
      //   return null
      // }).catch(() => {
      //   this.bus.$emit('fullLoading', false)
      //   return null
      // })
      try {
        const res = await actionPost({ api: `api/${url}/get_customer_order_track/`, data: { id: row.id }})
        if (res.code === 200) {
          // 确保返回数据
          console.log('能拿到数据吗-->', res.data)
          return res.data
        } else {
          this.$message.error(res.msg || res.detail || res.message)
          return null
        }
      } catch (error) {
        this.bus.$emit('fullLoading', false)
        return null
      } finally {
        this.bus.$emit('fullLoading', false)
      }
    },
    async handleShow(row, url) {
      const data = await this.getOrderTrack(row, url)
      console.log('获取轨迹data-->', data)
      const order_status = data.order_status
      const order_type = data.order_type
      this.orderNum = data.order_num || '-'
      this.refNum = data.ref_num || '-'
      this.country_code = data.country_code || '未知'
      this.buyer_country_code = data.buyer_country_code || '未知'
      this.product_name = data.product_name || '未知'
      this.receiver_name = data.receiver_name || '未知'
      this.buyer_postcode = data.buyer_postcode || '未知'
      this.carton = data.carton
      this.trackData = data

      // 处理头部logo
      if (order_status === 'FC' || order_status === 'SF') {
        this.currentClass = 'el-icon-check'
        this.headerStyle['background-color'] = '#67C23A'
      } else {
        this.currentClass = 'el-icon-position'
        this.headerStyle['background-color'] = '#409EFF'
      }

      if (order_type === 'FBM') {
        this.processSteps(order_status, this.fbmTrackStepList)
      } else {
        this.processSteps(order_status, this.defaultTrackStepList)
      }
      this.processTracks(data.tracks)
      this.dialogFormVisible = true
    },
    close() {
      this.dialogFormVisible = false
    },
    getFileUrl(url) {
      return process.env.HOST + '/media' + url.split('media')[1]
      // return url.split('api')[0] + '/media' + url.split('media')[1]
    },
    downloadFile(fileUrl) {
      window.open(this.getFileUrl(fileUrl), '_blank')
    },
    export_pod(item_id) {
      // actionPost({ api: '/api/customerOrders/custom_pod_export/', data: { ids: this.order_id }, responseType: 'blob' }).then(res => {
      actionPost({ api: '/api/customerOrders/download_pod_file/', data: { id: item_id }, responseType: 'json' }).then(res => {
        if (res === null || ('code' in res && res.code !== 200)) {
          this.$message.error(res.msg)
        } else {
          console.log('res.data000-->', res)
          // axiosExport(res, 'output_pdf_file.pdf')
          const fileUrl = res.data.file_url // 获取文件链接
          this.downloadFile(fileUrl) // 调用下载方法
        }
      })
    },
    processSteps(order_status, fbmTrackStepList) {
      // 判断状态对应在fbmTrackStepList中的索引
      const currentStepIndex = fbmTrackStepList.findIndex(item => item.orderStatus.includes(order_status))
      if (currentStepIndex === -1) {
        this.trackStepList = []
        return false
      }
      // 遍历fbmTrackStepList 以及索引
      const trackStepList = []
      for (let i = 0; i < fbmTrackStepList.length; i++) {
        const stepMap = fbmTrackStepList[i]
        if (currentStepIndex === i) {
          stepMap['status'] = 'doing'
        }
        if (i < currentStepIndex) {
          stepMap['status'] = 'success'
        }
        if (i > currentStepIndex) {
          stepMap['status'] = 'undone'
        }
        // 状态优先
        if (order_status === 'FC' || order_status === 'SF') {
          stepMap['status'] = 'success'
        }
        if (order_status === 'VO') {
          stepMap['status'] = 'undone'
        }
        trackStepList.push(stepMap)
      }

      // 作废
      if (order_status === 'VO') {
        const firstStep = { label: '已预报', orderStatus: ['VO'], status: 'fail' }
        trackStepList.unshift(firstStep)
      }

      this.trackStepList = trackStepList
    },
    processTracks(tracks) {
      if (!tracks) {
        this.tracks = []
        return false
      }

      // 处理轨迹
      const trackInfoList = []
      for (let i = 0; i < tracks.length; i++) {
        const track = tracks[i]
        const trackInfo = {

        }

        if (track.actual_time) {
          trackInfo['timestamp'] = track.actual_time.replace('T', ' ')
        } else if (track.operation_time) {
          trackInfo['timestamp'] = track.operation_time.replace('T', ' ')
        } else {
          trackInfo['timestamp'] = track.update_date.replace('T', ' ')
        }

        trackInfo['content'] = track.track_name || '未知'
        if (track.remark) {
          trackInfo['timestamp'] += `（${track.remark.replace('None', '')}）`
        }

        if (i === 0) {
          trackInfo['icon'] = 'el-icon-location-outline'
          trackInfo['type'] = 'success'
          trackInfo['size'] = 'large'

          this.newestTrackInfo = `${trackInfo['content']} - ${trackInfo['timestamp']}`
          this.newestTrackName = trackInfo['content']
          if (track.remark) {
            this.newestTrackRemark = track.remark.replace('None', '')
          } else {
            this.newestTrackRemark = '无最新备注'
          }
        }

        trackInfoList.push(trackInfo)
      }

      this.trackDetails = trackInfoList
    }

  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  overflow-y: auto;
  height: 500px;
}
/deep/ .el-step__main {
  margin-bottom: 32px;
}
///deep/ .el-dialog__body {
//  padding: 5px 10px;
//}
.rounded-icon-button {
  border-radius: 8px; /* Adjust the value for desired roundness */
  background-color: #007bff; /* Adjust background color as needed */
  padding: 6px; /* Adjust the padding for the button */
}
.card-body {
  height: 500px;
}

.block {
/* 若要哪个轴不需要滚动条, 则设置为hidden即可 */
    overflow-x: auto;
    overflow-y: auto;
  height: 82%;
}

/* 整个滚动条 */
.block::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 10px;
    /* 对应横向滚动条的宽度 */
    height: 10px;
}

/* 滚动条上的滚动滑块 */
.block::-webkit-scrollbar-thumb {
    background-color: #E1E1E1;
    border-radius: 32px;
}

/* 滚动条轨道 */
.block::-webkit-scrollbar-track {
    background-color: #F7F7F7;
    border-radius: 32px;
}

/deep/ .el-dialog {
  margin-top: 20px !important;
  width: 75%; /* 设置宽度为 50% */
  height: 96%; /* 设置高度为 60% */
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.left-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.export-button {
  margin-left: 20px;

  &.el-button--small {
    padding: 9px 15px;
    font-size: 12px;
    height: 32px;
    line-height: 1;
  }
}

</style>
