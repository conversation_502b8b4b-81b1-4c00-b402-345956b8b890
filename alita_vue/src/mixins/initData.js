import { initData, getChoiceData, initMutilData } from '@/api/data'
import i18n from '../i18n/index'

export default {
  data() {
    return {
      loading: true, data: [], page: 1, size: 10, total: 0, url: '', params: {}, query: {}, time: 170, firstEntry: true, dialogVisible: false, filterProp: {}
    }
  },
  activated() {
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    // 关闭弹窗
    toggleDialog(show) {
      this.dialogVisible = show
    },
    // 初始化获取下拉数据等
    initChoiceData(item) {
      const params = {
        size: 100000,
        page: 1,
        sort: 'id'
      }
      // 点击加载
      if (item) {
        if (!item.data) {
          if (item.query) {
            Object.keys(item.query).forEach(i => {
              params[i] = item.query[i]
            })
          }
          getChoiceData(item.api, params).then(res => {
            this.$set(item, 'data', res.data || res.results)
          })
        }
        return
      }
      // 进入页面就加载
      if (!this.firstEntry) return
      this.formData && this.formData.filters && this.formData.filters.forEach(i => {
        if (i.api) {
          getChoiceData(i.api, params).then(res => {
            this.$set(i, 'data', res.data || res.results)
          })
        }
      })
      this.firstEntry = false
    },
    // 倒叙
    reverse() {
      this.data.reverse()
    },
    async init() {
      console.log('execute-->init')
      if (sessionStorage.getItem('isMulti') === '1' && this.$route.fullPath === sessionStorage.getItem('fullPath')) {
        console.log('$route.params-->', this.$route.params)
        const multiParams = JSON.parse(sessionStorage.getItem('multiParams'))
        this.multiInit(multiParams, sessionStorage.getItem('withFilter'))
        return
      }
      if (!this.beforeInit()) {
        return
      }
      if (typeof this.cancelAjax === 'function') {
        this.cancelAjax()
      }
      return new Promise((resolve, reject) => {
        this.loading = true
        // console.log('this.params0-->', this.params)
        // 过滤器
        this.formData && this.formData.filters && this.formData.filters.forEach(i => {
          // if (typeof i.value === 'boolean' || (i.value !== undefined && i.value !== null)) {
          if (typeof i.value === 'boolean' || i.value || i.value === 0) {
            if (i.type === 'multiInput') {
              // 如果没有选类型，则不传
              if (i.queryType) {
                this.params[i.queryType] = (i.value).toString()
              }
            } else {
              this.params[i.prop] = (i.value).toString()
              this.filterProp[i.prop] = (i.value).toString()
            }
          } else {
            this.$delete(this.filterProp, i.prop)
          }
        })

        // 切换分页/切换一页展示数据量时需要添加tags(tabs标签状态)参数
        this.params.tags_order_status = this.current_status
        if (this.current_status) {
          this.params.order_status = this.current_status
        }
        if (this.status) {
          this.params.status = this.status
        }
        // console.log('222-->', this.url, this.params)
        // 获取下拉数据
        // this.initChoiceData()
        initData(this.url, this.params, this).then(res => {
          if (res.code === 403 && res.detail === i18n.t('content.NotPermission')) {
            this.$message.error(i18n.t('content.NoAccess'))
          }
          this.total = res.count
          this.data = res.data || res.results
          // 获取合计得数量
          const flag = this.formData && this.formData.checkSumary && this.formData.checkSumary.some(i => i.apiGet)
          if (flag) {
            if (this.formData.checkSumary && Object.keys(this.filterProp).length !== 0) {
              this.getSumary()
            } else {
              this.getSumary(false)
            }
          }
          setTimeout(() => {
            this.loading = false
          }, this.time)
          resolve(res)
        }).catch(err => {
          this.loading = false
          // reject(err)
          console.log(err)
        })
      })
    },
    multiInit(mutiData, withFilter = '0') {
      // 获取合计得数量
      const flag = this.formData && this.formData.checkSumary && this.formData.checkSumary.some(i => i.apiGet)
      if (flag) {
        if (this.formData.checkSumary && Object.keys(mutiData).length !== 0) {
          this.getSumary()
        } else {
          this.getSumary(false)
        }
      }
      this.loading = true
      const params = {
        multiData: mutiData,
        page: this.page,
        size: this.size
      }
      if (withFilter === '1') {
        // 带过滤条件
        this.formData && this.formData.filters && this.formData.filters.forEach(i => {
          if (typeof i.value === 'boolean' || i.value) {
            this.params[i.prop] = (i.value).toString()
            this.filterProp[i.prop] = (i.value).toString()
          } else {
            this.$delete(this.filterProp, i.prop)
          }
        })
        params['filterProp'] = this.filterProp
      }
      // 判断是否有默认查询参数
      if (this.formData.multiDefaultQuery) {
        if (params['filterProp']) {
          Object.assign(params['filterProp'], this.formData.multiDefaultQuery)
        } else {
          params['filterProp'] = this.formData.multiDefaultQuery
        }
      }
      initMutilData(`api/${this.formData.api}/`, params).then(res => {
        this.total = res.count
        this.data = res.data
        setTimeout(() => { this.loading = false }, 200)
      }).catch(err => {
        this.loading = false
        console.log(err)
      })
    },
    beforeInit() {
      return true
    },
    pageChange(e) {
      this.page = e
      this.init()
    },
    sizeChange(e) {
      this.page = 1
      this.size = e
      this.init()
    },
    // 通过方法名获取按钮item
    getBtnByName(name, loading = false) {
      const btn = this.formData.action && this.formData.action.find(i => {
        if (i.type === 'selectBtn') {
          // 下拉按钮形式
          i.btns.find(k => {
            return name === k.name
          })
        } else {
          return name === i.name
        }
      })
      this.$set(btn, 'loading', loading)
    }
  }
}
