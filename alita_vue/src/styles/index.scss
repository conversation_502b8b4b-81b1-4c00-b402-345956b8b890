@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  margin: 0px;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

.el-dialog__body {
  padding: 20px 20px;
  color: #606266;
  font-size: 14px;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app{
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus{
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.warn-content{
  background: rgba(66,185,131,.1);
  border-radius: 2px;
  padding: 16px;
  padding: 1rem;
  line-height: 1.6rem;
  word-spacing: .05rem;
  a{
    color: #42b983;
    font-weight: 600;
  }
}

//main-container全局样式
.app-main{
  min-height: 100%
}

.app-container {
  padding: 20px;
}

.head-container {
  padding-bottom: 10px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
  input {
    height: 30.5px;
    line-height: 30.5px;
  }
  .el-input__icon {
    //line-height: 31px;
    line-height: 20px;
  }
}

.el-avatar {
  display: inline-block;
  text-align: center;
  background: #ccc;
  color: #fff;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  vertical-align: middle;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 16px;
}


.form-btns {
  &.form-btns__header {
    position: fixed;
    padding: 10px 0;
    z-index: 200;
    right: 20px;
    bottom: 0px;
    left: 180px;
    right: 0px;
    text-align: center;
    background: #f5f7fa;
    border-top: 1px solid #ebeef5;
    transition: left .28s;

    #app .hideSidebar & {
      left: 36px;
    }
  }
}

.el-table__header-wrapper tr th {
  background-color: #f5f7fa;
  color: #3c3838;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// 隐藏箭头
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"]{
  -moz-appearance: textfield;
}
