import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css'// progress bar style
import { getToken, removeToken } from '@/utils/auth' // getToken from cookie
import { buildMenus } from '@/api/login'
import { filterAsyncRouter } from './store/modules/permission'
import { Notification } from 'element-ui'

NProgress.configure({ showSpinner: false })// NProgress Configuration

const whiteList = ['/login', '/wechat/supplier']// no redirect whitelist

// 添加缓存标志，避免重复获取用户信息和菜单
let isGettingUserInfo = false
let isMenusBuilt = false

router.beforeEach((to, from, next) => {
  NProgress.start() // start progress bar
  if (getToken()) {
    // 已经登陆,且携带跳转链接过来，授权跳转
    const urlParams = new URLSearchParams(window.location.search)
    const redirectLinkValue = urlParams.get('redirectLink')
    if (redirectLinkValue) {
      const redirectLinkInfo = { token: getToken(), redirectLink: redirectLinkValue }
      store.dispatch('GetInfoRedirect', redirectLinkInfo)
    }
    // 已登录且要跳转的页面是登录页
    if (to.path === '/login') {
      if (to.query.token !== undefined) {
        // 微信授权
        removeToken()
        location.reload()
      } else {
        next({ path: '/' })
      }
      NProgress.done() // if current page is dashboard will not trigger	afterEach hook, so manually handle it
    } else {
      if (store.getters.roles.length === 0 && !isGettingUserInfo) { // 判断当前用户是否已拉取完user_info信息
        isGettingUserInfo = true // 设置标志，防止重复请求
        store.dispatch('GetInfo').then(res => { // 拉取user_info
          isGettingUserInfo = false // 请求完成，重置标志
          // 检查密码是否需要强制重置
          if (!res.is_password_changed) {
            console.log('用户密码为初始密码123456, 需要重置密码', to.path)
            // 如果不是个人中心页面，则强制跳转到个人中心
            if (to.path !== '/user/center') {
              next({ path: '/user/center', query: { forcePasswordReset: 'true' }})
              NProgress.done()
              return
            }
          }
          // console.log('获取用户信息------->')
          if (res.roles.length === 0) {
            Notification.error({
              title: '该用户暂未分配角色，无法获取相应菜单！',
              duration: 2500
            })
            setTimeout(() => {
              removeToken()
              location.reload()
            }, 2500)
          } else if (res.code === 401 && res.message === 'Auth failed' && res.detail === 'Signature has expired.') {
            // 登陆失效 移除token 刷新页面
            removeToken()
            location.reload()
          } else {
            // 只有在菜单未构建时才构建菜单
            if (!isMenusBuilt) {
              buildMenus().then(res => {
                isMenusBuilt = true // 标记菜单已构建
                const asyncRouter = filterAsyncRouter(res)
                asyncRouter.push({ path: '*', redirect: '/404', hidden: true })
                store.dispatch('GenerateRoutes', asyncRouter).then(() => { // 存储路由
                  router.addRoutes(asyncRouter) // 动态添加可访问路由表
                  next({ ...to, replace: true })// hack方法 确保addRoutes已完成
                })
              })
            } else {
              // 菜单已构建，直接跳转
              next()
            }
          }
        }).catch((err) => {
          isGettingUserInfo = false // 请求失败，重置标志
          console.log(err)
          store.dispatch('LogOut').then(() => {
            location.reload() // 为了重新实例化vue-router对象 避免bug
          })
        })
      } else if (isGettingUserInfo) {
        // 如果正在获取用户信息，等待完成
        const checkInterval = setInterval(() => {
          if (!isGettingUserInfo) {
            clearInterval(checkInterval)
            next()
          }
        }, 100)
      } else {
        // 检查是否需要强制重置密码
        const userInfo = store.getters.user
        if (userInfo && !userInfo.is_password_changed) {
          // 如果不是个人中心页面，则强制跳转到个人中心
          if (to.path !== '/user/center') {
            next({ path: '/user/center', query: { forcePasswordReset: 'true' }})
            NProgress.done()
            return
          }
        }
        next()
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
      next()
    } else {
      if (to.path === '/tracking') {
        next()
        // 将图标和标题设置为空白
        document.title = '\u200B'
        // 移除所有可能的 favicon
        const favicons = document.querySelectorAll('link[rel*="icon"]')
        favicons.forEach(favicon => {
          favicon.parentNode.removeChild(favicon)
        })
        // 创建一个透明的 favicon
        const transparentFavicon = document.createElement('link')
        transparentFavicon.rel = 'icon'
        transparentFavicon.href = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"></svg>'
        document.head.appendChild(transparentFavicon)
        return
      }
      next(`/login`) // 否则全部重定向到登录页
      // next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
