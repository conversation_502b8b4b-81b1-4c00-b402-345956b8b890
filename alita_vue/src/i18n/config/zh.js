const zh = {
  el: {
    colorpicker: {
      confirm: '确定',
      clear: '清空?'
    },
    datepicker: {
      now: '此刻',
      today: '今天',
      cancel: '取消',
      clear: '清空',
      confirm: '确定',
      selectDate: '选择日期',
      selectTime: '选择时间',
      startDate: '开始日期',
      startTime: '开始时间',
      endDate: '结束日期',
      endTime: '结束时间',
      prevYear: '前一年',
      nextYear: '后一年',
      prevMonth: '上个月',
      nextMonth: '下个月',
      year: '年',
      month1: '1 月',
      month2: '2 月',
      month3: '3 月',
      month4: '4 月',
      month5: '5 月',
      month6: '6 月',
      month7: '7 月',
      month8: '8 月',
      month9: '9 月',
      month10: '10 月',
      month11: '11 月',
      month12: '12 月',
      // week: '周次',
      weeks: {
        sun: '日',
        mon: '一',
        tue: '二',
        wed: '三',
        thu: '四',
        fri: '五',
        sat: '六'
      },
      months: {
        jan: '一月',
        feb: '二月',
        mar: '三月',
        apr: '四月',
        may: '五月',
        jun: '六月',
        jul: '七月',
        aug: '八月',
        sep: '九月',
        oct: '十月',
        nov: '十一月',
        dec: '十二月'
      }
    },
    select: {
      loading: '加载中',
      noMatch: '无匹配数据',
      noData: '无数据',
      placeholder: '请选择'
    },
    cascader: {
      noMatch: '无匹配数据',
      loading: '加载中',
      placeholder: '请选择',
      noData: '暂无数据'
    },
    pagination: {
      goto: '前往',
      pagesize: '条/页',
      total: '共 {total} 条',
      pageClassifier: '页'
    },
    messagebox: {
      title: '提示',
      confirm: '确定',
      cancel: '取消',
      error: '输入的数据不合法!'
    },
    upload: {
      deleteTip: '按 delete 键可删除',
      delete: '删除',
      preview: '查看图片',
      continue: '继续上传'
    },
    table: {
      emptyText: '暂无数据',
      confirmFilter: '筛选',
      resetFilter: '重置',
      clearFilter: '全部',
      sumText: '合计'
    },
    tree: {
      emptyText: '暂无数据'
    },
    transfer: {
      noMatch: '无匹配数据',
      noData: '无数据',
      titles: ['列表 1', '列表 2'],
      filterPlaceholder: '请输入搜索内容',
      noCheckedFormat: '共 {total} 项',
      hasCheckedFormat: '已选 {checked}/{total} 项'
    },
    image: {
      error: '加载失败'
    },
    pageHeader: {
      title: '返回'
    },
    popconfirm: {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }
  },
  menu: {
    uploadList: '上传列表',
    exportList: '导出列表',
    home: '首页',
    msg: '这是一个关于页面',
    personalCenter: '个人中心',
    customer: '客户',
    parcelCustomerOrder: '小包订单',
    changedParcelCustomerOrder: '小包订单清关信息更改',
    parcelCustomerOrderList: '小包订单列表',
    parcelCustomerOrderDetail: '小包订单详情',
    parcelCustomerOrderAdd: '小包订单新增',
    FBAOrder: 'FBA订单',
    systemManagement: '系统管理',
    userManagement: '用户管理',
    menuManagement: '菜单管理',
    permissionManagement: '权限管理',
    organizationalStructure: '组织架构',
    basicData: '基础资料',
    cooperativePartner: '合作伙伴',
    productManagement: '产品管理',
    threeLetterCode: '三字代码',
    exchangeRateManagement: '汇率管理',
    businessExpenseCode: '业务费用代码',
    businessPartnerManagement: '业务伙伴管理',
    airline: '航空公司',
    address: '地址',
    receivingAddress: '揽收地址',
    CustomerPreOrder: '箱唛预录',
    CustomerOutboundOrder: '出仓指令',
    product: '产品',
    productList: '产品列表',
    productAdd: '产品新增',
    productDetail: '产品详情',
    settlement: '结算',
    billSummary: '账单汇总',
    bill: '账单',
    receiptDetails: '收款明细',
    paymentOrder: '付款单',
    paymentDetails: '付款明细',
    billSummaryDetails: '账单汇总详情',
    billSummaryList: '账单汇总列表',
    billList: '账单列表',
    billDetail: '账单详情',
    receiptDetailsList: '收款明细列表',
    receiptDetailsDetail: '收款明细详情',
    paymentOrderList: '付款单列表',
    paymentOrderDetail: '付款单详情',
    paymentDetailsList: '付款明细列表',
    transport: '运输',
    mainOrder: '空运主单',
    subOrder: '分单',
    mainOrderList: '空运主单列表',
    mainOrderAdd: '空运主单新增',
    mainOrderDetail: '空运主单详情',
    subOrderList: '分单列表',
    subOrderAdd: '分单新增',
    subOrderDetail: '分单详情',
    financial: '财务',
    managementExpense: '管理费用',
    bankFlowList: '银行流水列表',
    bankFlowAdd: '银行流水新增',
    bankFlowDetail: '银行流水详情',
    incomeAdjustment: '收入调整单',
    expenditureAdjustment: '支出调整单',
    businessReceipt: '业务出账单',
    businessPayment: '业务进账单',
    incomeAdjustmentAdd: '收入调整单新增',
    incomeAdjustmentDetail: '收入调整单详情',
    incomeAdjustmentList: '收入调整单列表',
    expenditureAdjustmentAdd: '支出调整单新增',
    expenditureAdjustmentDetail: '支出调整单情',
    expenditureAdjustmentList: '支出调整单列表',
    businessReceiptAdd: '出账单新增',
    businessReceiptDetail: '出账单详情',
    businessReceiptList: '出账单列表',
    businessPaymentAdd: '进账单新增',
    businessPaymentDetail: '进账单详情',
    businessPaymentList: '进账单列表',
    importerExporter: '进/出口商',
    cardDispatch: '卡派单',
    cardDispatchList: '卡派单列表',
    cardDispatchAdd: '卡派单新增',
    cardDispatchDetail: '卡派单详情',
    bank: '银行',
    exportCustomsOrder: '出口报关单',
    exportCustomsOrderList: '出口报关单列表',
    exportCustomsOrderAdd: '出口报关单新增',
    exportCustomsOrderDetail: '出口报关单详情',
    jobOrder: '运输订单作业',
    jobOrderList: '作业订单列表',
    jobOrderDetail: '作业订单详情',
    oceanTransportBill: '海运提单',
    oceanTransportBillList: '海运提单列表',
    oceanTransportBillAdd: '海运提单新增',
    oceanTransportBillDetail: '海运提单详情',
    receivableSummary: '应收汇总',
    payableSummary: '应付汇总',
    smallOrder: '小包订单',
    smallOrderList: '小包单列表',
    smallOrderAdd: '小包单新增',
    smallOrderDetail: '小包单详情',
    transportOrder: '运输订单',
    transportOrderList: '运输单列表',
    transportOrderAdd: '运输单新增',
    transportOrderDetail: '运输单详情',
    reconciliationOrder: '对账单',
    reconciliationOrderDetail: '对账单明细',
    reconciliationOrderList: '对账单列表',
    reconciliationOrderAdd: '对账单新增',
    order: '订单',
    smallOrderManagement: '小包单管理',
    changedSmallOrder: '更改小包清关信息',
    changedParcelCustomerOrderList: '更改小包清关信息列表',
    transportOrderManagement: '运输单管理',
    importCustomsOrder: '进口报关单',
    importCustomsOrderList: '进口报关单列表',
    importCustomsOrderAdd: '进口报关单新增',
    importCustomsOrderDetail: '进口报关单详情',
    systemIntegration: '系统集成',
    supplierInformationTable: '供应商信息表',
    supplierServiceConnection: '供应商服务对接',
    supplierAccountInformation: '供应商对接账户信息',
    buttonManagement: '按钮管理',
    receivableDailySummary: '应收日汇总',
    payableDailySummary: '应付日汇总',
    roleManagement: '角色管理',
    report: '报表',
    profitAnalysis: '利润分析',
    managementExpenseCode: '管理费用代码',
    jobOrderAdd: '作业订单新增',
    productPartitionPostalCode: '产品分区邮编',
    incomePriceVersion: '收入价格版本',
    incomePriceDetail: '收入价格明细',
    costPriceVersion: '成本价格版本',
    costPriceDetail: '成本价格明细',
    accountManagement: '账户管理',
    account: '账户',
    rechargeOrder: '充值单',
    accountDetail: '账户明细',
    paymentDetailsDetail: '付款明细详情',
    bigPackageOrder: '大包单',
    bigPackageOrderList: '大包单列表',
    bigPackageOrderAdd: '大包单新增',
    bigPackageOrderDetail: '大包单详情',
    accountingPeriod: '会计区间',
    smallPackagePick: '小包拣货',
    smallPackagePickRecord: '小包拣货记录',
    bigPackageWeight: '大包称重',
    overseasWarehouseOrder: '海外仓订单',
    GoodsManagement: '商品管理',
    skuList: 'sku列表',
    skuAdd: 'sku新增',
    skuDetail: 'sku详情',
    warehouse: '仓库',
    warehouseList: '仓库列表',
    warehouseAdd: '仓库新增',
    warehouseDetail: '仓库详情',
    inboundOrder: '入库订单',
    inboundOrderList: '入库订单列表',
    inboundOrderAdd: '入库订单新增',
    inboundOrderDetail: '入库订单详情',
    outboundOrder: '出库订单',
    outboundOrderList: '出库订单列表',
    outboundOrderAdd: '出库订单新增',
    outboundOrderDetail: '出库订单详情',
    warehouseStock: '仓库库存',
    stockTransactionRecord: '库存交易记录',
    goods: '商品',
    goodsList: '商品列表',
    goodsAdd: '商品新增',
    goodsDetail: '商品详情',
    warehouseManagement: '仓库管理',
    stock: '库存',
    stockTransaction: '库存交易',
    workOrderType: '工单类型',
    customerService: '客服',
    workOrder: '工单',
    workOrderList: '工单列表',
    workOrderAdd: '工单新增',
    workOrderDetail: '工单详情',
    claimOrder: '索赔单',
    claimOrderAdd: '索赔单新增',
    claimOrderList: '索赔单列表',
    claimOrderDetail: '索赔单详情',
    claim: '索赔',
    trackCode: '轨迹代码',
    track: '轨迹',
    bankTransaction: '银行交易流水',
    customsDeclaration: '清关报关单',
    customsDeclarationList: '报关单列表',
    customsDeclarationAdd: '报关单新增',
    customsDeclarationDetail: '报关单详情',
    cancelOrder: '取消订单',
    cancelOrderList: '取消单列表',
    MerchandiseMapping: '商品映射',
    mappingList: '映射列表',
    mappingAdd: '商品映射新增',
    mappingDetail: '商品映射详情',
    WMS: 'WMS',
    wmsProduct: 'wms商品',
    wmsProductList: 'wms商品列表',
    wmsProductAdd: 'wms商品新增',
    thirdPlatformWHMapList: '平台映射列表',
    thirdPlatformWHMapAdd: '平台仓库映射新增',
    thirdPlatformWHMapDetail: '平台仓库映射详情',
    thirdPlatformWHMap: '平台仓库映射',
    wmsProductDetail: 'wms商品详情',
    wmsWarehouse: 'wms仓库',
    wmsWarehouseList: 'wms仓库列表',
    wmsWarehouseAdd: 'wms仓库新增',
    wmsWarehouseDetail: 'wms仓库详情',
    wmsInbound: 'wms入库',
    wmsInboundList: 'wms入库列表',
    wmsInboundAdd: 'wms入库新增',
    wmsInboundDetail: 'wms入库详情',
    wmsOutbound: 'wms出库',
    wmsOutboundList: 'wms出库列表',
    wmsOutboundAdd: 'wms出库新增',
    wmsOutboundDetail: 'wms出库详情',
    wmsWarehouseStock: 'wms仓库库存',
    wmsStockTransactionRecord: 'wms库存交易记录',
    unload: '卸货',
    receive: '收货',
    receiverAddress: '收货地址',
    receiveOrderList: '收货单列表',
    receiveOrderDetail: '收货单详情',
    shelf: '上架',
    shelfJobOrder: '上架作业单',
    shelfOrderDetail: '上架单详情',
    pickOrder: '拣选单',
    pickJobOrder: '拣选作业单',
    pickOrderDetail: '拣选单详情',
    pack: '打包',
    packJobOrder: '打包作业单',
    packOrderDetail: '打包单详情',
    delivery: '发货',
    deliveryJobOrder: '发货作业单',
    deliveryOrderDetail: '发货单明细',
    unloadOrderList: '卸货单列表',
    unloadOrderDetail: '卸货单详情',
    unloadDetail: '卸货明细',
    receiveDetail: '收货明细',
    location: '库位',
    partition: '分区',
    partitionType: '分区类型',
    shelfDetail: '上架明细',
    pickOutboundOrderManagement: '拣选出库单管理',
    pickQueue: '拣选队列',
    areaManagement: '区域管理',
    service: '服务类',
    pickQueueDetail: '拣选队列详情',
    pallet: '出库Pallet',
    workOrderProblem: '工单问题',
    transportList: '运输列表',
    afterSaleService: '售后服务',
    claimAdd: '索赔新增',
    claimList: '索赔列表',
    claimDetail: '索赔详情',
    inquiry: '询价',
    inquiryList: '询价列表',
    inquiryAdd: '询价新增',
    inquiryDetail: '询价详情',
    inquiryOrder: '询价单',
    inquiryOrderList: '询价单列表',
    inquiryOrderAdd: '询价单新增',
    inquiryOrderDetail: '询价单详情',
    accountAmount: '账户金额',
    accountInfo: '账户信息',
    accountInfoDetail: '账户信息明细',
    orderLabelTask: '小包单任务面单',
    orderLabelTaskList: '小包单任务面单列表',
    orderLabelTaskDetail: '小包单任务面单详情',
    outboundOrderLabelTask: '出库单任务面单',
    outboundOrderLabelTaskList: '出库单任务面单列表',
    outboundOrderLabelTaskDetail: '出库单任务面单详情',
    customsOrder: '清关订单',
    customsOrderList: '清关单列表',
    customsOrderAdd: '清关单新增',
    customsOrderDetail: '清关订单详情',
    customsManagement: '清关管理',
    customsTruckOrder: '清关卡车单',
    customsTruckOrderList: '清关卡车单列表',
    customsTruckOrderAdd: '清关卡车单添加',
    customsTruckOrderDetail: '清关卡车单详情',
    billTo: '提单收件人',
    transferDestinationWarehouse: '转运目的仓',
    notifyParty: 'Notify Part',
    countryCode: '国家编码',
    smallPackageInquiry: '小包询价',
    smallPackageWeighing: '小包称重',
    smallPackagePacking: '小包装箱',
    smallPackageRelabeling: '小包换标',
    batchLargePackageOperation: '批量大包操作',
    batchSmallPackageOperation: '批量小包操作',
    orderTrack: '订单轨迹',
    orderTrackQuery: '订单轨迹查询',
    agreementScheme: '协议方案',
    agreementSchemeList: '协议方案列表',
    agreementSchemeAdd: '协议方案新增',
    agreementSchemeDetail: '协议方案详情',
    batchModifyTrackingNumber: '批量修改跟踪号',
    trackInsert: '轨迹插入',
    fbaOrderManagement: 'FBA订单管理',
    fbaOrderList: 'FBA订单列表',
    largePackageSignIn: '大包签入',
    fbaOrderAdd: 'FBA订单新增',
    fbaOrderDetail: 'FBA订单详情',
    fbaWarehouse: 'FBA仓库',
    largePackageUpdate: '大包更新',
    fbaOrder: 'FBA订单',
    sortingLineConfiguration: '分拣线配置项',
    fbaOrderOperation: 'FBA订单作业',
    fbaOperationList: 'FBA作业列表',
    fbaOperationDetail: 'FBA作业详情',
    fbaOperationAdd: 'FBA作业新增',
    transferWarehouse: '中转仓',
    transferWarehouseList: '中转仓列表',
    transferWarehouseAdd: '中转仓新增',
    transferWarehouseDetail: '中转仓详情',
    collectionOrder: '揽收单',
    collectionOrderList: '揽收单列表',
    collectionOrderAdd: '揽收单新增',
    collectionOrderDetail: '揽收单详情',
    fbaPackageManagement: 'FBA包裹管理',
    oceanTransport: '运',
    oceanTransportList: '海运列表',
    oceanTransportAdd: '海运新增',
    oceanTransportDetail: '海运详情',
    batchSignIn: '批量签入',
    abnormalOrderManagement: '异常单管理',
    abnormalOrderAdd: '异常单新增',
    abnormalOrderList: '异常单列表',
    abnormalOrderDetail: '异常单详情',
    fbaOrderSimple: 'FBA订单(简易)',
    businessPartnerList: '业务伙伴列表',
    businessPartnerAdd: '业务伙伴新增',
    businessPartnerDetail: '业务伙伴详情',
    customerManagement: '客户管理',
    customerList: '客户列表',
    customerAdd: '客户新增',
    customerDetail: '客户详情',
    bigPackageRelabeling: '大包换标',
    smallPackageTrackManagement: '小包轨迹管理',
    transferDestinationWarehouseList: '转运目的仓列表',
    transferDestinationWarehouseAdd: '转运目的仓新增',
    transferDestinationWarehouseDetail: '转运目的仓详情',
    printThirdPartyBoxLabel: '打印第三方箱唛',
    productPartitionManagement: '产品分区管理',
    productPartitionList: '产品分区列表',
    productPartitionAdd: '产品分区新增',
    productPartitionDetail: '产品分区详情',
    agreementSchemeSimple: '协议方案简易',
    agreementSchemeSimpleList: '协议方案简易列表',
    agreementSchemeSimpleAdd: '协议方案简易新增',
    agreementSchemeSimpleDetail: '协议方案简易详情',
    fbaPackageTrack: 'FBA包裹轨迹',
    preOrder: '预录单',
    preOrderList: '预录单列表',
    preOrderAdd: '预录单新增',
    preOrderDetail: '预录单详情',
    allocationOrder: '配仓单',
    allocationOrderList: '配仓单列表',
    allocationOrderAdd: '配仓单新增',
    allocationOrderDetail: '配仓单详情',
    productServiceManagement: '产品服务管理',
    productSalesPricing: '产品销售定价',
    productSalesPricingList: '产品销售定价列表',
    productSalesPricingAdd: '产品销售定价新增',
    productSalesPricingDetail: '产品销售定价详情',
    fbaPickRecord: 'FBA拣货记录',
    supplierService: '供商服务',
    supplierServiceList: '供应商服务列表',
    supplierServiceAdd: '供应商服务新增',
    supplierServiceDetail: '供应商服务详情',
    supplierJointBI: '供应商对接BI',
    packageList: '包裹列表',
    packageAdd: '包裹新增',
    packageDetail: '包裹详情',
    logisticsCodeInformation: '物流商编码信息',
    valueAddedServiceManagement: '增值服务管理',
    valueAddedServiceAdd: '增值服务新增',
    valueAddedServiceList: '增值服务单列表',
    valueAddedServiceDetail: '增值服务单详情',
    smallPackageTrackTask: '小包轨迹任务',
    ExportDeclarationOrder: '出口报关订单',
    ExportDeclarationOrderList: '出口报关订单列表',
    ExportDeclarationOrderDetail: '出口报关订单详情',
    ExportDeclarationOrderAdd: '出口报关订单新增',
    smallPackageTrackTaskList: '小包轨迹任务列表',
    smallPackageTrackTaskAdd: '小包轨迹任务新增',
    smallPackageTrackTaskDetail: '小包轨迹任务详情',
    wmsBasic: 'wms基础',
    locationBindingRelationship: '库位绑定关系',
    inventoryReconciliationRecord: '库存对账记录',
    costTemplateConfiguration: '费用模板配置',
    costTemplateAdd: '费用模板新增',
    costTemplateList: '费用模板列表',
    costTemplateDetail: '费用模板详情',
    costQuickEntry: '费用快捷录入',
    dictionary: '字典',
    attributeRestrictionConfiguration: '属性限制配置',
    warehouseRentRuleConfiguration: '仓租费规则配置',
    cardQuery: '卡派询价',
    cardQueryAdd: '卡派询价新增',
    cardQueryList: '卡派询价列表',
    cardQueryDetail: '卡派询价详情',
    fbmOrderManagement: 'FBM订单管理',
    fbmOrderManagementList: 'FBM订单管理列表',
    fbmOrderManagementAdd: 'FBM订单管理添加',
    fbmOrderManagementDetail: 'FBM订单管理详情',
    expressDeliveryOrder: '快递派送单',
    expressDeliveryOrderList: '快递派送单列表',
    expressDeliveryOrderAdd: '快递派送单新增',
    expressDeliveryOrderDetail: '快递派送单情',
    warehouseRentManagement: '仓租单管理',
    pushEMSLargePackage: '推送EMS大包',
    inventoryBatchManagement: '库存批次管理',
    inventoryAgeManagement: '库龄管理',
    trackSupplier: '轨迹供应商',
    trackConversionInformation: '轨迹转换信息',
    trackBlacklist: '轨迹黑名单',
    insuranceOrder: '保险单',
    insuranceOrderList: '保险单列表',
    insuranceOrderAdd: '保险单新增',
    insuranceOrderDetail: '保险单详情',
    insuranceProductManagement: '保险产品管理',
    insuranceProduct: '保险产品',
    insuranceProductList: '保险产品列表',
    insuranceProductAdd: '保险产品新增',
    insuranceProductDetail: '保险产品详情',
    insuranceIncomePriceVersion: '保险收入价格版本',
    insuranceCostPriceVersion: '保险成本价格版本',
    insuranceIncomePriceDetail: '保险收入价格明细',
    insuranceCostPriceDetail: '保险成本价格明细',
    valueAddedService: '增值服务',
    cargoManagement: '货件管理',
    cargoManagementList: '货件管理列表',
    cargoManagementDetail: '货件管理详情',
    cargoManagementAdd: '货件管理新增',
    fbmSpecialLineOrder: 'FBM专线订单',
    fbmSpecialLineOrderAdd: 'FBM专线订单新增',
    fbmSpecialLineOrderDetail: 'FBM专线订单详情',
    fbmSpecialLineOrderList: 'FBM专线订单列表',
    fbmSpecialLineOrderEdit: 'FBM专线订单编辑',
    outboundOrderManagement: '出仓指令管理',
    outboundOrderManagementList: '出仓指令管理列表',
    outboundOrderManagementAdd: '出仓指令管理新增',
    outboundOrderManagementDetail: '出仓指令管理详情',
    cargoInventory: '集货仓盘点',
    cargoInventoryAdd: '集货仓盘点新增',
    cargoInventoryDetail: '集货仓盘点详情',
    cargoInventoryList: '集货仓盘点列表',
    batchReturnSmallPackageOrder: '批量回退小包订单',
    pricePrediction: '价格预测',
    customerExportCustomsDeclaration: '客户出口报关单',
    customerExportCustomsDeclarationDetail: '客户出口报关单详情',
    customerExportCustomsDeclarationList: '客户出口报关单列表',
    warehouseInventoryManagement: '盘仓详情管理',
    logisticsPlan: '物流计划',
    logisticsPlanAdd: '物流计划新增',
    logisticsPlanDetail: '物流计划详情',
    logisticsPlanList: '物流计划列表',
    importForecastOrder: '导入预报单',
    exportPreRecordedOrder: '导出预录单',
    volume: '体积',
    isRemote: '是否偏远',
    remark: '备注',
    withdrawalSlip: '提现单',
    storage: '仓储',
    inventoryAdjustment: '库存调整',
    inventoryAdjustmentOrder: '库存调整单',
    inventoryAdjustmentOrderList: '库存调整单列表',
    inventoryAdjustmentOrderDetail: '库存调整单详情',
    inventoryAdjustmentOrderAdd: '库存调整单添加',
    removedFromStorage: '移库下架',
    MoveFromStorageToShelves: '移库上架',
    wmsInventory: 'WMS库存',
    supplier: '供应商',
    productAgreementScheme: '产品协议方案',
    returnOrder: '退货单',
    returnOrderADD: '退货单新增',
    returnOrderList: '退货单列表',
    returnOrderDetail: '退货单详情',
    InventoryReconciliation: '库存对账',
    ReturnOrder: '退件入库单',
    ReturnOrderList: '退件入库单列表',
    ReturnOrderAdd: '退件入库单新增',
    ReturnOrderDetail: '退件入库单详情',
    CustomerReturnOrder: '退件入库单',
    CustomerReturnOrderList: '退件入库单列表',
    CustomerReturnOrderAdd: '退件入库单新增',
    CustomerReturnOrderDetail: '退件入库单详情',
    ProductPostCodeCheck: '邮编校验',
    ProductInterceptRule: '产品拦截规则',
    productInterceptRuleList: '产品拦截规则列表',
    productInterceptRuleAdd: '产品拦截规则新增',
    ProductInterceptRuleDetail: '产品拦截规则详情',
    labelChangeConfig: '面单更改配置',
    labelChangeConfigList: '面单更改配置列表',
    labelChangeConfigAdd: '面单更改配置新增',
    labelChangeConfigDetail: '面单更改配置详情',
    RmManifestConfig: 'RM Manifest配置',
    RmManifestConfigList: 'RM Manifest配置列表',
    RmManifestConfigAdd: 'RM Manifest配置新增',
    RmManifestConfigDetail: 'RM Manifest配置详情',
    inboundTransfer: '入库中转单',
    inboundTransferAdd: '入库中转单新增',
    inboundTransferList: '入库中转单列表',
    inboundTransferDetail: '入库中转单详情',
    outboundTransfer: '出库中转单',
    outboundTransferAdd: '出库中转单新增',
    outboundTransferList: '出库中转单列表',
    outboundTransferDetail: '出库中转单详情',
    creditLine: '信用额度管理',
    creditLineAdd: '新增信用额度',
    OCShippingRules: '出库规则管理',
    selectCondition: '选择条件',
    setCondition: '已设定条件',
    OCShippingRulesList: '出库规则管理列表',
    list: '列表',
    add: '新增',
    edit: '编辑',
    detail: '详情',
    TrustDocumentParser: '托书解析',
    TrustDocumentList: '托书列表',
    TrustDocumentParserAdd: '托书解析新增',
    TrustDocumentDetail: '托书详情'
  },
  search: {
    jobOrderAndOperator: '作业单号/操作人',
    baleDetailSearch: '卸货作业单号/入库订单号/操作人',
    checkSearch: '盘点作业单号/操作人',
    clearanceSearch: '系统代码/主单号/客户编号/CMR number',
    cancelOrder: '客户订单号/跟踪号',
    claimOrdersSearch: '单据号/跟踪主单号/跟踪单号/英文品名',
    customerOrderSearch: '订单号/中文品名/英文品名/航班号/目的港',
    customerInboundSearch: '订单号/联系人/航班号/启运港/目的港',
    customerSkuSearch: '编码/名称/英文品名/中文品名',
    customerWarehouseSearch: '仓库编码/仓库名称/联系人/邮箱',
    accountDetailsSearch: '输入交易号/收付款人/收付款账号/摘要/发票账单号码查询',
    bankSearch: '输入名称/简称/账号/分行代码/Swift',
    billingSearch: '交易号/收款人/收款人账号/摘要',
    remarkSearch: '备注',
    omsWarehouseSearch: '仓库编码/仓库名称/邮箱',
    pickDetailList: 'sku/库位/操作人',
    pickListSearch: '拣选队列号/拣选单号',
    outboundOrderNoSearch: '出库单号',
    receiptDetailSearch: '收货作业单号/入库订单号/操作人',
    trackSearch: '订单号',
    caseSearch: '工单号/订单号/内容',
    accountPayableSearch: '凭证号/业务单据/客户订单号',
    accountReceivableSearch: '账单编号/凭证号/客户订单号/业务单据',
    debitSearch: '账单号/客户单号/跟踪号/业务单据号/产品编码',
    debitSummarySearch: '名称/简称/账号/分行代码/Swift',
    payMentSearch: '发票号/付款单/币种',
    reonconSearch: '费用/第三方服务单号/客户单号/跟踪号/业务单据号',
    clearanceOrderSearch: '订单号/提单号/柜号',
    partitionSearch: '编号/名称',
    LocationNoAndChannelSearch: '库位编号/通道',
    freezeOrderSearch: '冻结单号/sku/po_no',
    creditLineSearch: '客户名称/备注',
    thirdPartyWHMapSearch: '本地仓库编码/平台仓库编码',
    OCShippingRulesSearch: '规则名称'
  },
  content: {
    pleaseSelectExportConditions: '请选择导出条件',
    shipmentsLabel: '货件操作标签',
    operateTag: '操作标签',
    operateContent: '操作描述',
    MulProductPriceVersionTaskComfirm: '确认批量创建价格版本及明细?',
    versionType: '版本类型',
    MulProductPriceVersionTask: '批量创建产品价格版本任务',
    productAndPriceDetail: '产品与价格明细',
    priceVersionLines: '价格明细',
    unknow: '未知',
    cancelStatus: '取消状态',
    orderLabelTask: '小包单任务面单',
    outboundOrderLabelTask: '出库单任务面单',
    expired: '已过期',
    taskStatus: '任务状态',
    OrderCreateDate: '订单创建时间',
    cancelOrderLabelTaskList: '面单二次取消任务列表',
    ExportFliterPacelCustomerOrder: '根据条件导出小包单',
    taskRestart: '任务重启',
    ExportCondition: '导出条件',
    parcelCustomerOrderBathchExportTask: '小包订单批量导出任务',
    adjust_type: '调整单类型',
    income_adjust_type_default: '根据收入价格生成调整单',
    income_adjust_type_service: '退费并根据收入加收X%的手续费',
    CancelSuccess: '取消成功',
    CancelFailed: '取消失败',
    UpdateOrderStatus: '更新订单状态',
    ScanformTask: 'Scanform任务',
    taskCreate: '任务创建',
    importsusses: '导入成功',
    ScanformRefNumber: 'scanform批次号',
    ScanformDate: 'Scanform时间',
    ScanformNo: 'Scanform单号',
    IsCreatescanform: '是否创建Scanform任务',
    SignOrNot: '是否签名',
    BeingCancel: '取消中',
    cancelFeePercentage: '取消费应收百分比',
    ShipDate: '发货时间',
    paperHeight: '纸张高',
    paperWidth: '纸张宽',
    barcodeWidth: '条码宽',
    barcodeHeight: '条码高',
    selectProductDeletedTips: '请先选择需要删除的商品数据',
    chooseCopyProductTips: '请先选择复制商品',
    productInfoNotEmpty: '商品信息不能为空',
    QuantityAndPrice: '数量/单价',
    Usage2Type: '用途(英/中)',
    Material2Type: '材质(英/中)',
    brandAndModel: '品牌/型号',
    declaredName: '商品名称(英/中)',
    singBoxWeight: '单箱重量',
    specificationOfSingleCase: '单箱规格',
    cargoInfo: '货物信息',
    enterCountryOrAreaCode: '请输入国家或地区编码',
    countryOrAreaCode: '国家或地区编码:',
    selectDestination: '请选择目的地',
    selectShippingAreaTips: '请先选择发货区域',
    selfDelivery: '自行送货',
    fullAddress: '详细地址',
    searchAddress: '搜索地址',
    clickToSelectShippingAddress: '请点击选择发货地址',
    shippingAddress: '发货地址',
    parseSuccessTips: '解析成功，请检查后保存草稿',
    saveDraftTips: '请先保存草稿再导出箱单',
    noParcelEstimatedVolumeTips: '无订单包裹预计体积不能提交',
    noParcelEstimatedWeightTips: '无订单包裹预计重量不能提交',
    noParcelEstimatedPiecesTips: '无订单包裹预计件数不能提交',
    buyerInformaotionRequirTips: '私人地址，收货人，手机号，国家或地区，城市，邮编，地址必须填',
    shippingAddressImformationMustFilledIn: '收件地址信息必须填写',
    shippingAddressMustFilledIn: '发货地址必须填写',
    editSuccess: '编辑成功',
    orderByCustomsClearance: '买单报关',
    refundsAndDeclarations: '退税报关',
    partialRefund: '部分退税',
    addRowOfBoxes: '新增箱单行',
    totalValue: '总货值',
    cubicMeter: '立方米',
    exportingCaseListData: '导出箱单数据',
    placeOfShipmentAndDestination: '发货地与目的地',
    customsDeclarationInformation: '报关资料:',
    enterOrderNotes: '请输入订单备注',
    selectEstimatedDeliveryTime: '选择预计可派送时间',
    estimatedDeliveryTime: '预计可派送时间',
    selectExpectedArrivalDate: '选择预计到仓日期',
    expectedArrivalDate: '预计到仓日期',
    chooseBuyInsuranceOrNot: '请选择是否购买保险',
    enterCustomerNo: '请输入客户订单号',
    importBoxListData: '导入箱单数据',
    importOrderData: '导入订单数据',
    importOverwriteTip: '导入会覆盖原有数据,如已打印箱唛请保持箱单数据一致',
    downloadTemplates: '下载模板',
    importFormatOnlyXlsOrXlsx: '仅允许导入xls、xlsx格式文件。',
    dragFileOr: '将文件拖到此处，或',
    quantityNotEmpty: '数量不能为空',
    shipmentNumberNotEmpty: '货件号不能为空',
    quickImportOrder: '快捷导入订单',
    export_rebate_order: '导出返利账单',
    oneWarehouseCodeOrPostCode: '仓库代码或者邮编必填一个',
    addParcel: '添加包裹',
    modificationProductCharge: '修改产品收费项',
    enterParcelNumber: '请输入箱数',
    enterShipmentNumber: '请输入货件号',
    quickImport: '快捷导入',
    saveDraft: '保存草稿',
    enterWarehouseCode: '请输入仓库代码',
    confirmDeleteWarehouseItems: '是否确认删除仓库选择的数据项？',
    confirmDeleteItemSelectedByCustomer: '是否确认删除选择的数据项？',
    confirmDeleteSelectedItem: '是否确认删除选择的数据项？',
    confirmDeleteProductItem: '是否确认删除产品收费项选择的数据项？',
    confirmDeleteParcelItem: '是否确认删除包裹选择的数据项？',
    pleaseEnterTheRecipientPostCode: '请输入收件人邮编',
    pleaseEnterTheRecipientAddresseeCountry: '请输入收件人国家',
    pleaseEnterTherShipperPostCode: '请输入发货人邮编',
    pleaseEnterTheShipperCountry: '请输入发货人国家',
    pleaseEnterProductCode: '请输入产品编码',
    preOrder: '预录单',
    title: '货运管家',
    remember: '记住账号',
    language: '语言',
    login: '登录',
    loging: '登录中',
    account: '账号',
    password: '密码',
    zh: '中文',
    en: '英文',
    personalCenter: '个人中心',
    fullScreen: '全屏',
    logOff: '退出登录',
    uploadAvatar: '点击上传头像',
    joinDate: '注册时间',
    accountStatus: '账号状态',
    normal: '正常',
    passwordTips: '安全性高的密码可使账号更安全，建议设置同时包含字母，数字，符号的密码。',
    mailboxAuthentication: '邮箱验证',
    yourMailbox: '你的邮箱',
    boundMailboxes: '绑定邮箱可用于',
    securityManagement: '安全管理，密码重置与修改',
    accountUse: '账号使用，使用邮箱登录系统',
    edit: '编辑',
    oldPass: '旧密码',
    newPass: '新密码',
    confirmPass: '确认密码',
    cancel: '取消',
    confirm: '确认',
    delete: '删除',
    submit: '提交',
    salesReviews: '销售审核',
    financeReviews: '财务审核',
    pwdInconsistent: '两次输入的密码不一致',
    editPwd: '修改密码',
    enterOldPwd: '请输入旧密码',
    enterNewPwd: '请输入新密码',
    pwdLength: '长度在 6 到 20 个字符',
    loginAgain: '密码修改成功!请重新登录!',
    avatarEditSuccess: '头像修改成功',
    newEmail: '新邮箱',
    verificationCode: '验证码',
    errPwd: '密码错误，请重新输入',
    emptyEmail: '新邮箱不能为空',
    sameEmail: '新邮箱不能与旧邮箱相同',
    errorEmail: '邮箱格式错误',
    modifyEmail: '修改邮箱',
    getVCode: '获取验证码',
    emptyVCode: '验证码不能为空',
    sendInVCode: '验证码发送中',
    vCodeSendSuccess: '发送成功，验证码有效期5分钟',
    reSend: '重新发送',
    editEmailSuccess: '邮箱修改成功',
    smallOrders: '小包订单',
    transportOrder: '运输订单',
    importCustomsDeclaration: '进口报关单',
    customer: '客户',
    currency: '币种',
    NoSync: '不同步',
    WaitSync: '待同步',
    FinishSync: '同步完成',
    availableBalance: '可用余额',
    RebateBalance: '可用返利余额',
    AvailableCreditLimit: '可用信用额度',
    totalBalance: '账户总余额',
    creditLimit: '信用额度',
    creditOrNot: '是否信用额度支付',
    rebateAmount: '返利金额',
    rebateAmountOrNot: '是否返利金额支付',
    realAmount: '真实金额',
    amountType: '金额类型',
    remark: '备注',
    taskRemark: '任务执行记录',
    deletedSuccessfully: '删除成功',
    success: '成功',
    operation: '操作',
    sureDeleteThisData: '确定删除本条数据吗？',
    accountDetails: '账户明细',
    transactionType: '交易类型',
    recharge: '充值',
    deductions: '扣款',
    refund: '退费',
    amount: '交易金额',
    transactionNo: '交易号',
    transactionUniqueNo: '交易唯一号',
    transactionChargeCode: '交易费用Code',
    transactionChargeName: '交易费用名称',
    exchangeRate: '交易汇率',
    accountCurrency: '账户币种',
    accountChargeAmount: '记账金额',
    accountBalance: '账户余额',
    rechargeOrder: '充值单',
    serialNumber: '流水号',
    rechargeStatus: '充值状态',
    draft: '草稿',
    submitted: '已提交',
    pass: '通过',
    reject: '拒绝',
    processing: '处理中',
    invalid: '作废',
    thirdTransactionNo: '第三方流水号',
    voucherLink: '凭证链接',
    selectItem: '请选择要执行的条目！',
    continue: '确定执行此操作, 是否继续?',
    tips: '提示',
    adjustsOrder: '调整单',
    warehouses: '仓库',
    calculateAgeDate: '库龄计算日期',
    positionNum: '库位编号',
    warehousesOrderNumber: '仓库单号',
    warehouseType: '仓库类型',
    normalWarehouse: '普通仓',
    autoWarehouse: '自动仓',
    isAutoWarehouse: '是否自动仓',
    intercept_order: '拦截订单',
    status: '状态',
    isAudit: '是否审核',
    ShipperName: '发货人',
    ClearanceInformation: '报关信息',
    createTime: '创建时间',
    finishTime: '完成时间',
    inventoryAdjustmentOrder: '库存调整单',
    inventoryWarehouse: '盘点仓库',
    adjustQuantity: '调整件数',
    updateTime: '更新时间',
    basicInformation: '基本信息',
    shipmentDigest: '第三方digest',
    adjustType: '调整类型',
    accordingInventory: '按盘点单',
    DestroyInventory: '销毁',
    sinceTheLift: '自提',
    directlyTreasury: '直接入库',
    interceptWarehousing: '拦截入库',
    AbnormalShipment: '异常发货',
    inventoryLoss: '盘亏',
    inventoryProfit: '盘盈',
    inventoryNumber: '盘点单号',
    adjustGoods: '调整商品',
    productName: '产品名称',
    bookInventory: '账面库存',
    inventoryCount: '盘点数',
    inventoryDifferences: '盘点差异',
    targetLocation: '目标库位',
    import: '导入',
    importGrossCommission: '导入提成',
    downloadTemplate: '下载导入模板',
    pleaseSelect: '请选择',
    Required: '请输入',
    clickToUpload: '点击上传文件',
    pleaseUpload: '请上传',
    notExceeding: '大小不超过',
    format: '格式为',
    docs: '的文件',
    pleaseSelectOperation: '请选择操作的对象',
    detailsLandingDocuments: '卸货单据明细',
    operatingTime: '操作时间',
    operator: '操作人',
    baleOrderNum: '打包作业单号',
    outboundOrderNum: '出库订单号',
    skuNo: 'SKU NO',
    skuBarcode: 'M码',
    piece: '件数',
    pickingOrderNum: '拣选单号',
    fileGeneration: '导出文件正在后台生成，请稍后查看',
    makeInvoice: '开票成功',
    waitingJob: '等待作业',
    ongoing: '进行中',
    completed: '已完成',
    palletNum: 'pallet数量',
    expectedPickNum: '预计件数',
    actualPickNum: '实际件数',
    totalNum: '总件数',
    packageJobDetails: '打包作业明细',
    balingSteps: '打包单作业操作步骤',
    steps: '步骤',
    params: '入参',
    message: '消息',
    accountingInterval: '会计区间',
    startMonth: '开始月份',
    endMonth: '结束月份',
    isLock: '是否锁定',
    workOrderType: '工单类型',
    exceptionInfor: '异常名称',
    proposal: '建议方案',
    dictionaryManagement: '字典管理',
    keyName: '键名',
    value: '值',
    businessExpenseCodeManagement: '业务费用代码管理',
    costCode: '费用代码',
    costName: '费用名称',
    exchangeRateManagement: '汇率管理',
    currentCurrency: '当前币种',
    targetCurrency: '目标币种',
    conversionRate: '转换率',
    exchangeRateDate: '汇率日期',
    salesManageCharges: '销管费用代码管理',
    costType: '费',
    businessCosts: '业务费用',
    salesManagementCost: '销管费用',
    type: '类型',
    airPortCode: '三字代码',
    countryCode: '国家代码',
    cityCode: '城市代码',
    airportEnglishName: '机场英文名字',
    airportChineseName: '机场中文名字',
    trackCode: '轨迹代码',
    trackInfor: '轨迹说明',
    checkDetails: '收货单作业明细',
    forInventory: '待盘点',
    inventoryComplete: '盘点完成',
    inventorySequence: '盘点顺序',
    InventoryPlan: '盘点方案',
    InventoryRange: '盘点范围',
    AccordingGoods: '按商品',
    AccordingLocation: '按库位',
    AccordingLocationAndGoods: '按库位和商品',
    Location: '库位',
    AccordingBookInventory: '明盘',
    NotAccordingBookInventory: '盲盘',
    InventoryGoodsDetail: '盘点商品明细',
    InventoryOperationDetails: '盘点作业明细',
    ScanLocation: '扫描库位',
    WalkingTime: '行走时',
    InventoryTime: '盘点耗时',
    InventoryOperationSteps: '盘点单作业操作步骤',
    ForCustomsClearance: '待清关',
    HaveCustomsClearance: '已清关',
    ImportOrder: '导入订单',
    SystemCode: '系统代码',
    DateOfArrival: '到库日期',
    MasterNo: '主单号',
    CustomerNo: '客户编号',
    Weight: '重量',
    PickBoxes: '从海关提货的箱数',
    ForecastBoxes: '预报箱数',
    TotalParcelNumber: '总包裹数量',
    ProperNumber: '应有包裹数',
    ClearanceDate: '清关日期',
    // DeliveryDate: '送货日期',
    Destination: '目的地',
    ETA: '估计到达时间',
    Address: '地址',
    Recipient: '收件人',
    Sender: '发件人',
    AddressNo: '地址编码',
    AddressName: '地址简称',
    Contact: '联系人',
    CompanyName: '公司名称',
    CompanyShortName: '公司简称',
    Email: '邮箱',
    PhoneNo: '电话',
    CountryCode: '国家代码',
    ProvinceStateCode: '省份(州)编码',
    CityCode: '城市编码',
    PostCode: '邮编',
    HouseNo: '门牌号',
    AddressLineOne: '地址行1',
    AddressLineTwo: '地址行2',
    vat_no: 'VAT NO',
    eori_no: 'eori NO',
    AirLines: '航空公司',
    Code: '代码',
    Prefix: '前缀',
    Url: '网址',
    Companies: '业务伙伴管理',
    IsSupplier: '是否供应商',
    IsCustomer: '是否客户',
    SalesDirector: '销售负责人',
    DebitEmail: '账单邮箱',
    BusinessEmail: '业务邮箱',
    FullName: '全称',
    Tax: '税号',
    OpeningBank: '开户行',
    DebitDate: '账单结算日',
    Nonautomatic: '不自动',
    WeeksBalance: '周结',
    MonthlyBalance: '月结',
    Attachment: '附件',
    ImportExporter: '进/出口商',
    CustomsCode: '海关编码',
    LegalPerson: '法人',
    IsExporters: '是否出口商',
    IsImporter: '是否进口商',
    CancelOrder: '取消订单',
    SubmitTime: '提交时间',
    Total: '合计 ',
    DealWith: '处理',
    customerOrderNo: '客户订单号',
    TrackingNo: '跟踪号',
    DocumentType: '单据类型',
    Transport: '运输',
    Packet: '小包',
    OrderGenerationDate: '订单生成日期',
    HandleRes: '处理结果',
    ClaimOrders: '索赔单',
    OrderNo: '单据号',
    TrackMasterOrderNo: '跟踪主单号',
    CaseNo: 'CASE号',
    ShipmentValue: '货值',
    IndemnityToCustomer: '赔付客户金额(CNY)',
    Freight: '运费',
    CargoName: '英文品名',
    ProblemDescription: '问题描述',
    CompensationResult: '赔偿结果',
    CustomerOrder: '客户订单',
    OrderNumProductNameParcelNum: '订单号|产品名称|包裹号',
    ActualLeaveDate: '实际离港日期',
    ExpectedLeaveDate: '预计离港日期',
    ArrivalDate: '到货日期',
    SurfaceSingleManagement: '面单管理',
    CreateSurfaceSingle: '创建单',
    DownloadSurfaceSingle: '下载面单',
    CancelLabel: '取消面单',
    UploadLabel: '上传面单',
    DownloadTransferNo: '下载转单号',
    InvalidOrders: '作废订单',
    InvalidOrdersReason: '作废原因',
    Route: '路线',
    Departure: '启运港',
    Flight: '航班',
    FlightNo: '航班号',
    Goods: '货物',
    Number: '件数',
    ParcelNumber: '箱数',
    Volume: '体积',
    TotalSingle: '总单',
    AirMainBillNo: '空运主单号',
    OceanOrderNo: '海运提单号',
    OceanOrderNum: '提单号',
    OnlyChooseOne: '只能选择一个',
    Main: '主',
    Branch: '分',
    SingleOperation: '单操作',
    TransportInformation: '运输信息',
    ExpectArrivalDate: '预计到港日期',
    ActualArrivalDate: '实际到港日期',
    HouseBill: '分单号',
    RecipientSender: '收/发件人',
    CargoInformation: '货物信息',
    EstimatedWeight: '预计重量',
    EstimatedVolume: '预计体积',
    ConversionRate: '计费转换乘率',
    ChargeableWeight: '计费重量',
    BubbleWeight: '分泡后计费重',
    volumeWeight: '重泡比',
    TotalCost: '总费用',
    UnitPrice: '单价',
    Payer: '付款方',
    IncomingOrders: '入库订单',
    IncomingTransferOrders: '入库中转订单',
    InTransit: '运输中',
    ArrivedWarehouse: '已到仓',
    Received: '已收货',
    HasBeenOn: '已上架',
    IsSendDebit: '发送单',
    IsRevenueLock: '收入确认',
    IsCostLock: '成本确认',
    IsArrivalNotice: '到货通知',
    Settlement: '结算',
    InCome: '收入',
    Cost: '成本',
    GrossProfit: '毛利',
    QuoteCurrency: '报价币种',
    DeclareCurrency: '申报币种',
    PrintBarcode: '打印条码',
    SalesPrice: '销售价格',
    DeclaredPrice: '申报价格',
    DeclaredChineseName: '中文申报品名',
    DeclaredEnglishName: '英文申报品名',
    Length: '长',
    Width: '宽',
    Height: '高',
    Material: '材质',
    Size: '尺寸',
    Use: '用途',
    Brand: '品牌',
    Model: '型号',
    ForecastSize: '预报尺寸',
    ChineseNameOfGoods: '中文品名',
    EnglishNameOfGoods: '英文品',
    EstimatedNumber: '预计件数',
    CustomerPointsBubble: '客户分泡(%)',
    IncomeDetails: '收入明细',
    CostDetail: '成本明细',
    Supplier: '供应商',
    Parcel: '包裹',
    PackageNo: '包裹号',
    PackageDescription: '包裹描述',
    Product: '产品',
    ProductProperties: '产品性质',
    ProductCode: '产品编码',
    GoodsCode: '商品编码',
    PackingListDetails: '箱单明细',
    OperationType: '操作类型',
    Outbound: '出库',
    Inbound: '入库',
    IsSynchronizedInventory: '同步库存',
    InHandBefore: '在手库存',
    StockInTransit: '在途库存',
    InventoryInHand: '在手变更数量(在手库存)',
    InventoryInWay: '在途变更数量(在途库存)',
    InventoryInKeep: '保留变更数量(保留库存)',
    OperatingNo: '操作单号',
    CustomerOrderNum: '客户单号',
    OutboundOrder: '出库订单',
    DeliveryCompleted: '发货完成',
    CancelSurfaceSingle: '取消面单',
    Order: '订单',
    PartitionValue: '分区值',
    PurchaseOrders: '采购订单集',
    ShopManager: '销售负责人',
    IsNuclearHeavy: '是否核重',
    OrderTime: '下单时间',
    BillingDate: '记账日期',
    PlatformNo: '平台号',
    PlatformFlag: '平台标识',
    BigParcel: '大包单',
    TheOrderNo: '订单号:',
    NotObtained: '还未获取转单号，是否确认发货完成？如以获取跟踪号，请刷新页面后重试！',
    WarehouseCode: '仓库编码',
    Service: '服务',
    isMasterProduct: '是否主产品',
    isNeedConfirm: '是否需要确认',
    PushOrder: '推送订单',
    RecipientEmail: '收件邮箱',
    RecipientPhone: '收件人电话',
    RecipientCountry: '收件人国家',
    RecipientState: '收件人州',
    RecipientCity: '收件人城市',
    RecipientArea: '收件人区域',
    CatchSingleTask: '抓单任务',
    TurnSingleInformation: '转单信息',
    UnHandled: '未处理',
    HandledBy3rdNo: '根据第三方处理',
    StatusSuccess: '处理成功',
    HandledFailure: '处理失败',
    ThirdPartyOrderNo: '第三方订单号',
    ModulusValue: '取模值',
    HandleTimes: '处理次数',
    CommodityRelated: '商品相关',
    ParcelsAndMerchandise: '包裹和商品',
    ParcelNo: '箱号*',
    WeightKG: '重量*(KG)',
    LengthCM: '长*(CM)',
    WidthCM: '宽*(CM)',
    HeightCM: '高*(CM)',
    SingleWeight: '打单重量*(KG)',
    DeclareUnitPrice: '申报单价',
    ParcelSummary: '包裹汇总',
    CommoditySummary: '商品汇总',
    TrackInformation: '轨迹信息',
    Time: '时间',
    UKTime: '英国时间',
    WarehouseInventory: '仓库库存',
    DischargeDetails: '卸货单作业明细',
    DischargeOrderNo: '卸货作业单号',
    WarehousingOrderNo: '入库订单号',
    Discharge: '卸货',
    StartTime: '开始时间',
    DischargeSteps: '卸货单作业操作步',
    SourceOfThePicture: '图片来源',
    Page: '页面',
    NoPermission: '你没有权去该页面',
    ContactLeader: '如有不满请联系你领导',
    OrYouCanGo: '或者你可以去:',
    BackToHomePage: '回首页',
    LookAround: '随便看看',
    ClickLookPicture: '点我看图',
    AllRightsReserved: '版权所有',
    Wallstreetcn: '华尔街见闻',
    ErrorReport: '请检您输入的网址是否正确，请点击以下按钮返回主页或者发送错误报告',
    CantAccessThisPage: '网管说这个页面你不能进......',
    AccountStatement: '银行流水',
    AccountDate: '发生日期',
    Spending: '支出',
    PayeeOrPayer: '收/付款人',
    PayeeOrPayerAccount: '收/付款人账号',
    Abstract: '摘要',
    InvoiceOrBillNo: '发票/账单号码',
    Bank: '银行',
    ShortName: '简称',
    BankAddress: '银行地址',
    BranchCode: '分行代码',
    WriteOffBalance: '核销余额 ',
    BusinessCompany: '业务公司',
    AssociatedPaymentOrder: '关联付款单',
    PaymentOrderNo: '付款单号',
    BalanceOfPayment: '付款余额',
    InvoiceNo: '发票号',
    PaymentDate: '付款日期',
    LinkId: '关联Id',
    Payee: '收款人',
    PayeeAccount: '收款人账号',
    Remove: '移除',
    RemoveBill: '确定移除该付款单？',
    ToSubmit: '待提交',
    ToAudit: '待审核',
    ManagerAudit: '经理审核',
    FinancialAudit: '财务审核',
    ToBePaid: '待支付',
    Paid: '已支付',
    ThisBillWriteOffBalance: '此单核销余额',
    ProcessingDate: '此单核销时间',
    AssociatedBillsSummary: '关联账单汇总',
    BillingSummaryNo: '账单汇总号',
    CollectionDays: '收款日期',
    PayerAccount: '付款人账号',
    VirtualBank: '虚拟银行',
    RemoveBillSummary: '确定移除该付款单？',
    BankTransactionFlow: '银行交易流水',
    OperationDate: '操作日期',
    NoData: '暂无数据',
    clear: '清空',
    SupplierInformation: '供应商信息',
    SupplierCode: '供应商编码',
    SupplierName: '供应商名称',
    AttributionInstitution: '归属机构',
    FileStorageDirectory: '文件存储目录',
    SupplierServiceDocking: '供应商服务对接',
    DockingCode: '对接编码',
    DockingName: '对接名称',
    SupplierID: '供应商ID',
    DockingInformationDescription: '对接信息描述',
    ClassName: '实现类',
    HermesServiceClass: 'Hermes服务类',
    WinitServiceClass: 'Winit服务类',
    PNNServiceClass: 'PNN服务类',
    SideSingleFileExtension: '面单文件扩展名',
    ForecastFileExtension: '预报文件扩展名',
    VendorServiceCode: '供应商服务编码',
    CarrierCode: 'CarrierCode',
    IsSupportCancel: '是否支持取消',
    SupplierDockingAccountInformation: '供应商对接账户信息',
    AccountName: '账户名',
    AccountPassword: '账户密码',
    AccountDescription: '账户描述',
    SurfaceSingle: '面单',
    Invoice: '发货单',
    Forecast: '预报',
    Track: '轨迹',
    AuthorizationID: '授权ID',
    AuthorizationCode: '授权码',
    Refresh: '刷新',
    Close: '关闭',
    CloseTheOther: '关闭其他',
    CloseAll: '关闭所有',
    LicensedSuccessfully: '已成功授权',
    CompleteDischarge: '卸货完成',
    MandatoryShelves: '强制上架',
    FinishOrder: '收入确认',
    OrderUnlock: '收入解锁',
    CostFinish: '成本确认',
    CostUnlock: '成本解锁',
    PositionStatus: '库内状态',
    WaitingForWarehousing: '等待入库',
    ReceiptCompleted: '收货完成',
    ShelvingCompleted: '上架完成',
    WarehouseProducts: '仓库产品',
    EstimatedNumberOfBoxes: '预计箱数',
    ActualNumberOfBoxes: '实际箱数',
    CabinetNo: '柜号',
    TrackInTheLibrary: '库内轨迹',
    Description: '描述',
    TrackCode: '轨迹code',
    InventoryTransactionRecord: '库存交易记录',
    Adjustment: '调整',
    IsPrinted: '是否打印',
    Printed: '已打印',
    NotPrinted: '未打印',
    WaitingForPicking: '等待拣选',
    PickingUp: '拣中',
    PickingCompleted: '拣完成',
    SortingCompleted: '分拣完成',
    PackagingCompleted: '打包完成',
    PickingFailed: '拣选失败',
    CatchSingleException: '抓单异常',
    PrintTimes: '打印次数',
    SkuPictures: 'sku图片',
    SystemSKUCode: '系统sku编码',
    CustomerSKUCode: '客户sku编码',
    Picture: '图片',
    EnterTransportationNo: '请输入运输单号',
    CartonNo: '箱号',
    PleaseEnterWeight: '请输入重量',
    Update: '更新',
    SystemBoxLabel: '系统箱标',
    ActionButtons: '操作按钮',
    Copies: '份数',
    DoNotPrint: '不打印',
    Printer: '打印机',
    FailedToGetPrinter: '获取打印机失败',
    PrintSuccess: '打印成功',
    PrintFailed: '打印失败',
    MakeSureBoxNoWeight: '请确保填写了箱号、重量',
    PleaseSelectPrinter: '请选择打印机',
    IsLinkParcelOrder: '关联小包',
    IsLinkCustomerOrder: '关联运输',
    BatchAdding: '批量新增',
    Country: '国家',
    ConfigureTransportationDocuments: '配置运输单',
    PrintLabels: '打印标',
    SmallPieces: '小件',
    TransportationOrderNo: '运输订单号',
    ParcelOrderDetails: '小包订单明细',
    CustomsDeclarationNo: '报关单号',
    TransportState: '运输状态',
    ArrivedDestinationPort: '已到目的港',
    WarehouseToComplete: '入库完成',
    HandoverDispatch: '移交派送',
    OrderType: '订单类型',
    IsCheck: '是否查验',

    IsISFMatch: '是否ISF匹配',
    IsCustomsDeclarationCompleted: '是否报关完成',
    ForecastSending: '预报发送',
    ReleaseTime: '放行时间',
    ReleaseOrder: '放行单',
    DeliveryTime: '交货时间',
    TrajectoryChange: '轨迹更改',
    CustomsOperation: '关务操作',
    Check: '查验',
    CancleCheck: '取消查验',
    ISFMatch: 'ISF匹配',
    CancleISFMatch: '取消ISF匹配',
    CustomsClearanceCompleted: '清关完成',
    CancelClearanceComplete: '取消清关完成',
    TransportationOperation: '运输操作',
    Send: '发送',
    SendISF: '发送ISF',
    SendBills: '发送账单',
    SendForecast: '发送预报',
    DeliveryInformation: '交派信息',
    Date: '日期',
    ReleaseDate: '放行日期',
    DeliveryDate: '交货日期',
    CustomsDeclarationType: '报关单类型',
    Importer: '进口商',
    Exporter: '出口商',
    TaxBill: '税单',
    ShipName: '船',
    VoyageTime: '航',
    NoticeReleaseTime: '通知放行时间',
    WarehousingCDate: '入库完成日期',
    TruckReservationTime: '预约卡车时间',
    InternalCarton: '内件数',
    YouEmail: '邮件',
    DeliveryTheme: '送货主题',
    DeliveryAddress: '送货地址',
    EmailBody: '邮件正文',
    MasterOceanFile: '主单/提单',
    DeliveryList: '派送清单',
    InvoicesAndCases: '发票和箱单',
    CustomsDeclarationList: '报关清单',
    ReleaseBillOfLading: '电放提单',
    NoteRecords: '备注记录',
    Creator: '建人',
    ThirdPartyServiceBillNo: '第三方服务单号',
    TransferOrderNo: '转单号',
    ConfigurationDocument: '配置单据',
    ConfigurationOfAirFreightMasterBill: '配置空运主单',
    TransnationalTransportation: '跨国运输',
    MasterDocBelongsTo: '主单所属',
    FlightDate: '航班日期',
    ConfiguredOceanBillOfLading: '配置海运提单',
    BillOfLadingBelongs: '提单所属',
    ConfigurationOfSingle: '配置分单',
    ConfigureTruckList: '配置卡车单',
    TruckOrderNo: '卡车订单号',
    DepartureStation: '发站',
    ArrivalNotice: '到货通知',
    RecoverOrder: '恢订单',
    PrintNeutralSheet: '打印中性面单',
    ProductPartition: '产品分区',
    Share: '分摊',
    ShareNo: '分摊单号',
    BigBagDetails: '大包明细',
    ConfigureLargePackageOrder: '配置大包单',
    PleaseEnterContent: '请输入内容',
    ContainerNo: '装箱号',
    ExpressLabel: '快递标',
    ThePackingNohasBeenScanned: '该装箱号已经扫描单号',
    PleaseWeightAndPackingNo: '请确保填写了运单号、重量、装箱号',
    PickingRecord: '拣货记录',
    User: '用户',
    ErrorMessage: '错误信息',
    PickingOrderDetails: '拣选单明细',
    // EstimatedPickingQuantity: '预计拣选数量',
    ActualPickQuantity: '实际拣选数量',
    PickingOrder: '拣选顺序',
    PickingOrderList: '拣选单',
    PrintPickingOrder: '打印拣选单',
    FinishPackOrder: '完成打包',
    PickingStrategy: '拣选策略',
    BigGoodsOutbound: '大货出库',
    PickingArea: '拣选区域',
    OutboundQuantity: '出库数量',
    QueueMode: '队列方式',
    QueueModeValue: '队列方式值',
    TotalOrders: '总订单数',
    QuantityCompleted: '完数量',
    NumberOfFailures: '失败数量',
    MaximumQuantityOfOutboundOrders: '最大出库单数量',
    PickingQueue: '拣选队列',
    PickingQueueNo: '拣选队列号',
    EstimatedPickingQuantity: '预计拣选件数',
    ActualPickingQuantity: '实际拣选件数',
    PickingConfrontation: '拣选对垒',
    Pause: '暂停',
    Recover: '恢复',
    PickingOutboundOrder: '拣选出库单',
    OutboundOrderNo: '出库单号',
    IsPickingOrderGenerated: '是否生成拣选单',
    IsSingleOrder: '是否单一订单',
    GeneratePickingOrder: '生成拣选单',
    OneKeyGenerate: '一键生成拣选单',
    SKUAndMCode: 'SKU & M码',
    OperationStepsOfPickingOperation: '拣选单作业操作步骤',
    // IncomeAdjustment: '收入调整单',
    ProductType: '产品类型',
    masterNumber: '空运主单',
    CustomsClearance: '清关',
    OverseasWarehouse: '海外仓',
    OverseasWarehouseIN: '海外仓入库单',
    OverseasWarehouseOUT: '海外仓出库单',
    OverseasWarehouseReturn: '海外仓退货单',
    OrderPleaseCheckLater: '订单正在后台处理中，请稍后查看',
    ImportProductInformation: '导入产品信息',
    TrackCodeConfiguration: '轨迹代码配置',
    WarehouseInTransfer: '海外仓入库中转单',
    WarehouseOutTransfer: '海外仓出库中转单',
    Aging: '时效',
    Sort: '排序',
    ServiceName: '服务名称',
    ServiceCode: '服务编码',
    IsDefaultService: '是否默认服务',
    PartitionName: '分区名称',
    Buyer: '买家',
    Seller: '卖家',
    ZipCodeOfProductDivision: '产品分区邮编',
    ProductPostCodeCheck: '邮编校验',
    AccurateZipCode: '精准邮编',
    IntervalSegment: '区间段(用~分割)',
    ProductRoute: '产品路线',
    ProductStartPartition: '产品起始分区',
    ProductEndpointPartition: '产品终点分区',
    ProductCustomerRestrictions: '产品客户限制',
    EndTime: '结束时间',
    AttributionToCustomer: '归属客户',
    ProductChargeItem: '产品收费项',
    Name: '名称',
    Mix: '混合',
    ChargeUnit: '计费单位',
    BillingReconversionRate: '计费重转换率',
    ChargingInstructions: '计费说明',
    MaximumWeightLimitOfPackage: '包裹最大限重',
    MinimumWeightLimitOfWholeOrder: '整单最小限重',
    ExpenseItem: '费用项',
    RevenuePriceVersion: '收入价格版本',
    ChargeItemName: '收费项名称',
    VersionName: '版本名称',
    CostPriceVersion: '成本价格版本',
    ProductDiscount: '产品折扣',
    DiscountType: '折扣类型',
    DesignatedProduct: '指定产品',
    AllProducts: '所有产品',
    DiscountRate: '折扣率',
    ProductExpenseItem: '产费用项',
    VersionPartition: '版本分区',
    OnlyOneOperation: '只能选择一个项进行操作',
    PleaseSelectOneUpload: '请选择一项进行上传',
    CostPriceDetails: '成本价格明细',
    Partition: '分区',
    PriceListVersionName: '价格表版本名称',
    ChargingMethods: '计费方式',
    IncrementalPrice: '递增价',
    GradeStartingPoint: '等级起点',
    GradeEndPoint: '等级终点',
    Price: '价格',
    BasePrice: '基础价格',
    IncrementalGrade: '递增等级',
    BillingCarry: '计费进位',
    IncomePriceDetails: '收入价格明细',
    IsExpectedActualPieces: '是否预计大于实际件数',
    ReceivingOperationNo: '收货作业单号',
    ReceivingOrders: '收货单',
    TotalGrossWeight: '总毛重',
    TotalVolume: '总体积',
    TotalWeight: '总重量',
    GrossWeight: '毛量',
    NetWeight: '净量',
    OperationStepsOfReceiptOperation: '收货单作业操作步骤',
    EnterNote: '输入备注',
    PayableSummary: '应付汇总',
    IsReset: '是否清零',
    TotalSummaryBookkeepingAmount: '总汇总记账金额:',
    TotalSummaryBookkeepingBalance: '总汇总记账余额',
    SummaryBookkeepingAmount: '汇总记账金额',
    SummaryBookkeepingBalance: '汇总记账余额',
    Month: '月份',
    TotalAdjustmentBookkeepingAmount: '总调整记账金额：',
    OriginalBookkeepingAmount: '原记账金额',
    AdjustBookkeepingAmount: '调整记账金额',
    TotalBillingAmount: '出账总额',
    Difference: '差额',
    ProfitAnalysis: '利润分析',
    TotalReceivables: '汇总应收总额：',
    TotalAmountPayable: '汇总应付总额：',
    SummaryProfit: '汇总利润：',
    Profit: '利润',
    OrderQuantity: '订单数量',
    ARSummary: '应收汇总',
    TotalAmountOfReceipts: '进账总额',
    EstimatedTime: '预计时间',
    ActualTime: '实际时间',
    OnlySelectOneProductOrderNo: '只能选一个产品按订单号查！',
    SubmissionDate: '提交日期',
    WorkOrder: '工单',
    ExceptionTypes: '异常类型',
    AbnormalNumber: '异常单',
    WorkOrderNo: '工单号',
    Content: '内容',
    TimeConsuming: '耗时',
    WorkOrderTask: '工单任务',
    CaseShipment: '货件列表',
    NewCase: '新建工单',
    IsPush: '是否推送',
    TaskContentUpdate: '任务内容更新',
    IssueAdjustmentOrder: '开具调整单',
    GenerateAdjustmentOrder: '生成调整单',
    SystemOrderNo: '系统单据号',
    ClaimType: '索赔类型',
    ActiveClaim: '主动索赔',
    PassiveClaim: '被动索赔',
    ClaimOrderType: '索赔单据类型',
    CustomersSuppliers: '客户/供应商',
    IsAdjust: '开具调整',
    ValueAndCompensation: '货值与赔付',
    SupplierCompensationAmount: '供应商赔付金额CNY',
    CreateInformation: '创建信息',
    CompensationOrderInformation: '赔偿单信息',
    PaymentDetails: '付款明细',
    VoucherNo: '凭证号',
    BusinessOrders: '业务单据',
    StatementNo: '对账单编号',
    OriginalPaymentCurrency: '原付款币种',
    PaymentDetailsStatus: '付款明细状态',
    WaitingForManagerReview: '等待经理审核',
    WaitingForFinancialAudit: '等待财务审核',
    Cancelled: '已取消',
    PartialPayment: '部分付款',
    IsCheckAccounts: '是否对账',
    IssuePaymentOrder: '开具付款单',
    ChargeTime: '记账时间',
    OriginalPaymentAmount: '原付款金额:',
    AccountBalance: '记账余额：',
    TotalOriginalPaymentAmount: '总原付款金额：',
    TotalAccountingAmount: '总记账金额：',
    TotalAccountingBalance: '总记账余额：',
    CheckCompleted: '核对完成',
    CancelTheCheck: '取消核对',
    CreatePaymentOrder: '创建付款单',
    OneClickCreatePaymentOrder: '一键创建付款单',
    Export: '导出',
    ExportAllData: '导出全部数据',
    ExportData: '导出数据',
    DownloadSummaryData: '下载汇总数据',
    FileName: '文件名',
    Voucher: '凭证',
    PaymentInformation: '付款信息',
    BusinessOrderNo: '业务单据号',
    AccountingInformation: '记账信息',
    OriginalPaymentInformation: '原付款信息',
    OriginalPaymentBalance: '原付款余额',
    CheckTheInformation: '对账信息',
    Mark: '标注',
    UpdatedBy: '更新人',
    ImportBills: '导入账单',
    BillWeight: '账单重量',
    NumberOfBills: '账单件数',
    BillAmount: '账单金额',
    DifferencesAmount: '差异金额',
    PaymentTime: '付款时间',
    CollectionDetails: '收款明细',
    CollectionStatus: '收款状态',
    ForCollection: '待收款',
    CollectionCompleted: '收款完成',
    CollectionInformation: '收款信息',
    CollectionAmount: '收款金额',
    BillNo: '单编号',
    OriginalCollectionBalance: '原收款余额',
    OriginalCollectionAmount: '原收款金额',
    Bill: '账单',
    BillStatus: '账单状态',
    IsInvoicing: '是否开票',
    BillDate: '账单日期',
    TotalM: '总合计：',
    BillingSummary: '开具账单汇总',
    OneClickBillingSummary: '一键开具账单汇总',
    BillInformation: '账单信息',
    BillSummary: '账单汇总',
    Balance: '余额',
    OriginalCurrency: '原币种',
    AdjustmentOrderStatus: '调整单状态',
    Approved: '审核通过',
    BillManagement: '账单管理',
    UploadFedExBill: '上传Fedex账单',
    AdjustsOrderNo: '调整单编号',
    AdjustDetails: '调整明细',
    BillingCurrency: '账单币种',
    PaymentCurrency: '付款币种',
    PaymentExchangeRate: '付款汇率',
    BillSummaryStatus: '账单汇总状态',
    PartialCollection: '部分收款',
    IsCancelAftererification: '是否核销',
    SendCustomer: '寄送客户',
    PaymentAmount: '付款金额：',
    PaymentWriteOffBalance: '付款核销余额：',
    Invoiced: '已开票',
    AssociatedIncomingBill: '关联进账单',
    CancelBillSummary: '取消账单汇总',
    ResetBillPaymentInformation: '重设账单付款信息',
    PaymentDaysInterval: '期区间',
    Notice: '通知',
    BillingDetails: '账单明细',
    PaymentOrder: '付款单',
    PaymentOrderStatus: '付款单状态',
    AssociatedBilling: '关联出账单',
    VoidPaymentBill: '作废付款单',
    PaymentCompleted: '付款完成',
    Payment: '付款',
    PaymentInterval: '付款区间',
    ExpenditureAdjustmentBill: '支出调整单',
    AccountStatements: '对账单',
    PendingProcessing: '待处理',
    InReconciliation: '对账中',
    ReconciliationCompleted: '对账完成',
    ReconciliationFailed: '对账失败',
    ReconciliationType: '对账类型',
    DetailedReconciliation: '明细对账',
    SummaryReconciliation: '汇总对账',
    BillType: '账单类型',
    UniversalType: '通用类型',
    FedexReconciliation: 'Fedex对账',
    BillStartDate: '账单开始日期',
    BillEndDate: '账单结束日期',
    OpenReconciliation: '开启对账',
    VoidedStatement: '作废对账单',
    PDFConversion: 'PDF转换',
    ProcessingInformation: '处理信息',
    BillFile: '账单文件',
    StatementDetails: '对账单明细',
    TheAmounPositiveNegative: '金额正负差异',
    NegativeDifferences: '负差异',
    PositiveDifferences: '正差异',
    IsWeightDifference: '重量差异',
    DifferenceNegativeWeights: '重量正负差异',
    IsNumberDifference: '件数差异',
    PositivPieces: '件数正差异',
    PartitionDifferences: '分区差异',
    PartitionNegative: '分区正负差异',
    IsSystemNotExist: '系统不存在',
    CurrentAmount: '当前金额:',
    TotalVarianceAmount: '总差异金额：',
    TotalSystemAmount: '总系统金额：',
    TotalAmount: '总金额：',
    ExpenditureAdjustment: '支出调整',
    GenerateExpenseAdjustmentOrders: '生成支出调整单',
    OneExpenseAdjustmentOrders: '一键生成支出调整单',
    IncomeAdjustment: '收入调整',
    GenerateIncomeAdjustmentOrders: '生成收入调整单',
    OneIncomeAdjustment: '一键生成收入调整单',
    OffsetTheCost: '冲抵成本',
    GenerateExpensesCosts: '生成支出冲抵成本',
    OneOffsetCosts: '一键生成支出冲抵成本',
    ReferenceNo: '参考号',
    ReconciliationStatus: '对账状态',
    IsComplete: '是齐全',
    IsDifferent: '是否差异',
    IncomeAmount: '收入金额',
    PricingAmount: '计价金额',
    RevenueVarianceAmount: '收入差异金额',
    SystemCostAmount: '系统成本金额',
    CostVarianceAmount: '成本差异金额',
    SystemNumber: '系统件数',
    QuantityOfDifference: '差异件数',
    SystemWeight: '系统重量',
    DifferencesInWeight: '差异重量',
    SystemPartition: '系统分区',
    DifferencePartition: '差异分区',
    JobDetailsShelvingList: '上架单作业明细',
    ShelvingJobNo: '上架作业单号',
    ShelvingOrders: '上架单',
    RefundJobNo: '退货作业单号',
    RefundOrders: '退货单',
    JobDetailsRefundList: '退货单作业明细',
    StepsOnShelves: '上架单作业操作步骤',
    ShipmentProgress: '发货中',
    DeliverGoods: '发货',
    BatchNo: '批次号',
    TotalOrderNumber: '总单数',
    WarehouseOutList: '发货出库单',
    IsGenerateInvoice: '是否生成发货单',
    GenerateInvoice: '生成发货单',
    OrderOperation: '订单操作',
    CheckBill: '账单查验',
    AddButton: '新增按钮',
    EditButton: '编辑按钮',
    PermissionsLogo: '权限标志',
    ParentMenu: '父级菜单',
    MenuNotSelectParent: '不选为父级菜单',
    PleaseEnterName: '请输入名称',
    PleaseEnterPermissionID: '请输入权限标识',
    PleaseEnterSerialNo: '请输入序号',
    PleaseSelectMenuType: '请选择菜单类型',
    EnterNameSearch: '输入名称搜索',
    Search: '搜索',
    Add: '新增',
    PermissionId: '权限标识',
    CannotBeUndone: '确定删除吗,如果存在下级节点则节点上升，此操作不能撤销！',
    AddMenu: '新增菜',
    EditMenu: '编辑菜单',
    MenuIcon: '菜单图标',
    ClickTheSelectIcon: '点击选择图标',
    MenuName: '菜单名称',
    MenuSorting: '菜单排序',
    SmallerComesFirst: '序号小的靠前',
    Yes: '是',
    No: '否',
    InternalMenu: '内部菜单',
    LinkAddress: '链接地址',
    MenuPath: '菜单路径',
    ComponentPath: '组件路径',
    Icon: '图标',
    AddOrganization: '新增组织',
    EditOrganization: '编辑组织',
    PleaseSelectOrganizationType: '请选择组织类型',
    ParentOrganization: '父级组织',
    NotAsParentOrganization: '不选为父级组织',
    Company: '公司',
    Department: '部门',
    AddPermissions: '新增权限',
    EditPermissions: '编辑权限',
    Methods: '方法',
    ContextMenu: '关联菜单',
    SelectWithPermission: '选择权限关联的菜单',
    ParentAuthority: '父级权限',
    PermissionNotAsParent: '不选为父级权限',
    PleaseEnterMethod: '请输入方法',
    AddRole: '新增角色',
    EditRole: '编辑角色',
    CreationDate: '创建日期',
    AddUsers: '新增用户',
    EditUsers: '编辑用户',
    Username: '用户名',
    XingMing: '姓名',
    Activated: '激活',
    Locked: '锁定',
    InternalUsers: '内部用户',
    Position: '职位',
    CompanyOfAffiliation: '所属的公司',
    PleaseSelectCompany: '请选择所属的公司',
    Role: '角色',
    PleaseRole: '请选择角色',
    PleaseEnterPhoneNo: '请输入手机号码',
    PleaseEnterSYPhoneNo: '请输入正确的11位手机号码',
    PleaseEnterUserName: '请输入用户名',
    LengthSanAndEScharacters: '长度在 3 到 20 个字符',
    NameCannotEmpty: '姓名不能为空',
    PleaseEnterEmail: '请输入邮箱地址',
    PleaseEnterCorrectEmail: '请输入正确的邮箱地址',
    StateCannotEmpty: '状态不能为空',
    DefaultPassword: '默认密码：123456',
    UserNameAlreadyExists: '用户名已经存在！',
    EnterKeywordSearch: '输入关键字搜索',
    Headurl: '头像地址',
    DateLastPasswordChange: '最后修改密码的日期',
    Enable: '启用',
    Disable: '禁用',
    HeadPortrait: '头像',
    Supervisor: '上级主管',
    NumberofWarehouseRemoval: '移库下架作业单号',
    WarehouseRemovalShelf: '移库下架单',
    DetailsWrehouseRemoval: '移库下架作业明细',
    OperationOperation: '移库下架单作业操作步骤',
    WarehouseShelvingSheet: '移库上架作业单号',
    WarehouseShelving: '移库上架单',
    RemoveDetailsShelving: '移库上架作业明细',
    OperationStepsOfMovingWarehouse: '移库上架单作业操作步骤',
    WarehouseShelvingOrderNo: '移库上架单号',
    TotalIncome: '收入',
    DownloadVasOrder: '下载增值服务单模板',
    ImportVasOrder: '导入增值服务单',
    TotalSpending: '总支出',
    TruckCollection: '卡车揽收',
    ReceivingGoodsWarehouse: '集货仓收货',
    TransportationRouteConfirmation: '运输路线确认',
    ExitCustomsClearanceInspection: '出境清关查验',
    ExitCustomsClearanceCompleted: '出境清关完成',
    DepartureInternationalTransportation: '国际运输出港',
    EntryCustomsClearanceInspection: '入境清关查验',
    EntryCustomsClearanceCompleted: '入境清关完成',
    TruckTransshipment: '卡车转运',
    InTheDelivery: '派送中',
    OrderCancellation: '订单取消',
    Finvoice: '发票',
    CourierNo: '快递单号',
    SupplierServices: '供应商服务',
    SenderAddress: '发件人地址',
    RecipientAddress: '收件人地址',
    BillingConversion: '计费转换',
    ExpressBillCost: '快递单费用',
    ToDelete: '删除？',
    MainOrderComplete: '主单完成',
    StateBack: '状态回退',
    GenerateImportCustomsDeclaration: '生成进口报关单',
    GenerateExportCustomsDeclaration: '生成出口报关单',
    ChargeWeight: '计量重量',
    CustomsDeclaration: '报关',
    ImportDeclarationNo: '进口报关单号',
    ExportDeclarationNo: '出口报关单号',
    NumberOfCustomerPieces: '客户单件数',
    CustomerOrderWeight: '客户单重量',
    CustomerOrderVolume: '客户单体积',
    MainSingle: '主单',
    ShippingBill: '舱单',
    MasterBillExpense: '主单费用',
    RemoveCustomerOrder: '确定移除该客户订单？',
    OceanOrderFinish: '提单完成',
    VoyageNumber: '船次',
    OceanOrderCost: '提单费用',
    NoBeenSubmitted: '暂无已提交的订单！',
    SystemError: '系统错误，请联系管理员！',
    PrintInboundOrders: '打入库的订单',
    NumberOfShelves: '上架数量',
    RecipientCityCode: '收件人城市编码',
    RegionalManagement: '区域管理',
    PackagePartition: '打包分区',
    PrintCode: '打印编码',
    PrinterCode: '打印机编码',
    PalletCode: '托盘编码',
    OutboundPallet: '出库Pallet',
    PalletSummary: '托盘汇总',
    PalletAndParcels: '托盘和包裹',
    LocationNo: '库位编号',
    PartitionNo: '编号',
    PartitionType: '分区类型',
    PickingPriority: '拣货优先级',
    Channel: '通道',
    ShelfNo: '货架号',
    FloorNumber: '层数',
    LocationLength: '库位的长(CM)',
    LocationWidth: '库位宽(CM)',
    LocationHeight: '库位高(CM)',
    VolumeCBM: '容积(CBM)',
    FrozenOrders: '冻结单',
    Freeze: '冻结',
    Thawed: '已解冻',
    FreezeTime: '冻结时间',
    ThawingTime: '解冻时间',
    SubmitToFreeze: '提交冻结',
    Unfreeze: '解冻',
    FreezeOrderNo: '冻结单号',
    FreezeInventory: '冻结库存',
    UnfreezeInventory: '解冻库存',
    Transfer: '移库',
    ImportFailed: '导入失败！',
    StockAgeReport: '库龄报表',
    StatisticalDate: '统计日期',
    GenerateStockAge: '生成库龄',
    Days: '天',
    PleaseEnterTheQuantity: '请输入数量',
    AvailableInventory: '可用库存',
    RequestTimeout: '请求超时',
    LoginAgain: '登录状态过期了哦，您可以继续留在该页面，或者重新登录',
    ReLogin: '重新登录',
    NotPermission: '您没有执行该操作的权限。',
    NoAccess: '您的账号拥有的角色暂未拥有查看的权限，请联系系统管理员进行配置！',
    ModifyPaymentInfor: '修改付款信息',
    InputCurrencyPayment: '请输入付款币种、付款金额',
    QueryError: '查询错误：',
    PleaseFillIn: '请填写',
    PleaseEnterAnIconName: '请输入图标名称',
    SDate: '选择日期',
    STime: '选择时间',
    PleaseEnterzero: '请输入0',
    selectProduct: '请选择产品',
    expectedStatuteOfLimitations: '预计时效',
    sailingScheduleReference: '船期参考',
    pleaseSelectConsignee: '请选择提单收件人',
    ClickUpload: '点击上传',
    Failure: '失败:',
    ConfirmDeletionContinue: '确认删除, 是否继续?',
    UploadedSuccessfully: '成功上传',
    UploadFailed: '上传失败',
    InsertALineAbove: '上方插入一行',
    InsertALineBelow: '下方插入一行',
    DeleteRow: '删除行',
    ReadOnly: '只读',
    HideUpload: '隐藏上传',
    Download: '下载',
    PleaseEnterCorrectQty: '请输入正确的数量',
    OrderDate: '订单日期',
    StartDate: '开始日期',
    EndDate: '结束日期',
    LastWeek: '最近一周',
    LastMonth: '最近一个月',
    LastThreeMonths: '最近三个月',
    FailedGetData: '获取数据失败!',
    EnglishNameOfMenu: '菜单英文名称',
    Carrier: '承运商',
    CarrierNum: '承运商编号',
    CarrierName: '承运商名称',
    CarrierNickName: '承运商简称',
    Contacts: '联系人',
    Telephone: '联系电话',
    Fax: '传真号码',
    EntrustCustomer: '委托客户',
    EntrustCustomerNum: '委托客户编号',
    EntrustCustomerName: '委托客户名称',
    EntrustCustomerNickName: '委托客户简称',
    ConsigneeConsignor: '收发货人',
    ConsigneeConsignorNum: '收发货人编号',
    ConsigneeConsignorName: '收发货人姓名',
    BelongingCarrier: '所属承运商',
    Car: '车辆',
    CarNo: '车牌号码',
    CarType: '车辆类型',
    CarSource: '车辆来源',
    FuelType: '燃油类型',
    CarLoad: '车辆载质量(kg)',
    CarVolume: '车容积（m³）',
    TmsDriver: '司机',
    Idcard_num: '身份证号',
    DriverName: '司机姓名',
    Gender: '性别',
    EmergencyPerson: '紧急联人',
    EmergencyTelephone: '紧联系人电话',
    MALE: '男性',
    FEMALE: '女性',
    EntrustOrder: '委托订单',
    EntrustOrderNo: '委托单号',
    TotalWeightKG: '总重量KG',
    Consignor: '发货人',
    ConsignorTelephone: '发货电话',
    StartPoint: '起点',
    Consignee: '收货人',
    ConsigneeTelephone: '收货电话',
    ReceiverAddress: '收货地址',
    EndPoint: '终点',
    CurrentState: '当前状态',
    PickupDate: '取件日期',
    ConsignorInfo: '发货人信息',
    ConsignorName: '发货人姓名',
    ShippingState: '发货人州',
    ShippingCity: '发货人城市',
    ShippingArea: '发货人区',
    ConsigneeInfo: '收货人信息',
    ConsigneeName: '收货人姓名',
    SerialNo: '序号',
    GoodsName: '货物名称',
    Barcode: '条形码',
    VolumeM3: '体积m³）',
    ExpensesReceivable: '应收费用',
    taxrate: '* 税率（%）',
    tax_rate: '最新税率',
    CV: '整车',
    BC: '散货',
    DispatchOrder: '调度单',
    DispatchOrderNo: '调度单号',
    SAD: '已保存',
    DP: '制单',
    REJ: '已拒绝',
    UNC: '待领取',
    TBP: '待提货',
    TD: '提货中',
    WD: '待发车',
    ITR: '运输中',
    CMP: '已完成',
    CAN: '已取消',
    DeliveryTime2: '开始配送时间',
    RequiredArrivalTime: '要求到达时间',
    CarInfo: '车辆信息',
    DriverInfo: '司机信息',
    TransportationRoute: '运输路线',
    TransportationDetail: '运输明细',
    EstimatedArrivalTime: '预计到达时间',
    ZHOU: ' 州',
    SHI: '市',
    CurrentLocation: '当前位置',
    CompartmentTemperature: '车厢温度℃',
    CompartmentHumidity: '车厢湿度%',
    IsDelayed: '是否延误',
    Maintain: '维保保养',
    MaintainNum: '维保单号',
    REP: '维修',
    MAI: '保养',
    MaintainType: '维保类型',
    MaintainLocation: '维保地点',
    MaintainContent: '维保内容',
    MaintainTime: '维保时间',
    ActualExecutionTime: '实际执行时间',
    FinishTime: '完成时间',
    TBI: '待执行',
    EXEC: '执行中',
    AbnormalLog: '异常记录',
    AbnormalLogNo: '异常单号',
    FAU: '车辆故障',
    TRO: '交通事故',
    TEM: '温度异常',
    HUM: '湿度异常',
    DVT: '偏离预定路线',
    AbnormalTime: '异常时间',
    AbnormalLocation: '异常地点',
    NAC: '未受理',
    ACC: '已受理',
    AcceptanceResult: '受理结果',
    PEND: '待受理',
    FAAL: '误报',
    COTR: '继续运输',
    REVE: '更换车辆',
    CHDR: '更换司机',
    OTHE: '其他',
    AbnormalDescribe: '异常描述',
    AcceptanceDescribe: '受理描述',
    AcceptanceTime: '受理时间',
    LogNo: '记录号',
    DelayLog: '延误记录',
    TranNode: '运输节点',
    ActualArrivalTime: '实际到达时间',
    DelayReason: '延误原因',
    Mobile: '手机',
    FRE: '空闲',
    MIP: '维保中',
    DIS: '禁用',
    Fleet: '车队',
    FleetNum: '车队编号',
    TaskNo: '任务编号',
    TRAN: '运输',
    RMAI: '维保',
    DriverIncome: '司机收入',
    EstimatedMileage: '预估里程',
    EstimatedOtherIncome: '预估其它收入',
    EstimatedTotal: '预估合计',
    WaitingForPost: '待岗',
    Uncollected: '未收款',
    Collected: '已收款',
    FullCollection: '全部收款',
    PaymentMethod: '收款方式',
    CashSettlement: '现结',
    IsCollection: '是否收款',
    TotalPriceExcludingTax: '无税总价',
    TotalPriceAndTax: '价税合计',
    AmountIncTax: '含税金额',
    AmountExcTax: '无税金额',
    DiscountAmount: '折扣金额',
    DiscountAmountIncTax: '折后含税金额',
    DiscountAmountExcTax: '折后无税金额',
    ReceiveAmount: '已收金额',
    UncollectedAmount: '未收金额',
    ExpensesReceivableNo: '应收费用单号',
    CollectionLog: '收款记录',
    CollectionTime: '收款时间',
    TransferAccounts: '转账',
    Cash: '现金',
    AliPay: '支付宝',
    WXPay: '微信',
    BlankAccount: '银行账号',
    PageInvoice: '纸质发票',
    ElectInvoice: '电子发票',
    InvoiceAmount: '开票金额',
    InvoiceTitle: '发票抬头',
    CompanyPhone: '公司电话',
    RegisteredAddress: '注册地址',
    ReceiveEmail: '接收邮箱',
    InvoiceTaker: '收票人',
    TakerPhone: '收票人电话',
    TakerAddress: '收票人地址',
    Area: '区域',
    RelPostcode: '关联邮编',
    DeliverySite: '配送站点',
    RO: '接单',
    CB: '退单',
    AD: '已分配',
    TBD: '待派送',
    SF: '已签收',
    WTW: '待回仓',
    RTW: '已回仓',
    DC: '派送完成',
    Longitude: '经度',
    Latitude: '纬度',
    RefreshCoordinates: '刷新坐标',
    NewDeliveryTask: '生成配送任务',
    DR: '草稿',
    TL: '干线',
    TE: '终端',
    DL: '配送',
    SD: '派送中',
    DeliverySitePlanDate: '配送计划日期',
    RefreshPlan: '刷新计划',
    SortByTime: '按订单时间排序',
    SortByMileage: '按里程排序',
    SortByRoute: '按路径排序',
    SortByMinRange: '按最近距离排序',
    CK: '仓库',
    ZD: '站点',
    DeliverySiteZone: '站点分区',
    FulfilTheQuota: '完成定额',
    OrderTime2: '订单时间',
    IsClosed: '已经关闭',
    NewSiteTask: '生成站点任务',
    RoutePlaning: '路径规划',
    PlanNum: '计划批号',
    ArrivalTime: '送达时间',
    DeliveryIndex: '配送顺序',
    PnnTmsServiceClass: 'PnnTms服务类',
    BatchAddPlan: '批量添加计划',
    StartAddress: '开始地址',
    EndAddress: '结束地址',
    Distance: '距离',
    RouteForecast: '路线测算',
    TrackingInfo: '跟踪信息',
    RefreshTrack: '刷新轨迹',
    Version: '版本号',
    SingninTime: '签收耗时',
    TranType: '运输类型',
    T1Num: 'T1编号',
    ULD_PMC: 'ULD/PMC数量',
    PreChargeWeight: '预计计费重',
    Collection: '揽收',
    CreateCollectOrder: '创建揽件订单',
    SiteTaskNum: '站点任务编号',
    LabelsIndex: '标签序号',
    LabelsCount: '标签数量',
    CancelReason: '取消原因',
    IsDelivery: '已配送',
    UnpackingOrder: '拆包单',
    OrderDetails: '单据明细',
    PjDetail: '配件详情',
    PjSKU: '配件编号',
    PjDesc: '配件描述',
    RelateOrderNo: '关联单号',
    ArrivalStatus: '到货状态',
    GeneratePlacementOrder: '生成摆放顺序',
    Quantity: '数量',
    GoodsQuantity: '商品数量',
    Statistical: '统计',
    DataCollect: '数据采集',
    OutboxCode: '外箱编码',
    OtherOp: '其他操作',
    ModifyDeclaredPrice: '修改申报价格',
    OnlyOneRecord: '每次只能修改一条记录',
    ImportExport: '导入导出',
    AirFreightMainBillNo: '空运主单号',
    InsertOneLineAbove: '上面插入一行',
    transactionTime: '交易时间',
    transactionCurrency: '交易币种',
    creationInformation: '创建信息',
    creator: '创建人',
    updater: '更新人',
    abnormalTag: '异常标签',
    abnormalName: '异常名称',
    description: '描述',
    chargeTemplateConfig: '费用模板配置',
    templateName: '模板名称',
    presetTemplateCostDetails: '预设模板-成本明细',
    presetTemplateIncomeDetails: '预设模板-收入明细',
    costDetails: '成本明细',
    incomeDetails: '收入明细',
    chargeName: '费用名称',
    unitPrice: '单价',
    quantity: '数量',
    payer: '付款方',
    twoCharCode: '二字代码',
    threeCharCode: '三字代码',
    chineseName: '中文名称',
    englishName: '英文名称',
    labelName: '标签名',
    dataValue: '数据值',
    typeValue: '类型值',
    sort: '排序',
    dictionary: '字典',
    shareLogic: '分摊逻辑',
    chargeWeight: '计费重',
    confirmChargeWeight: '确认计费重',
    isHighValue: '是否高货值',
    confirmVolume: '确认体积',
    isValueAddedCost: '是否增值费用',
    billingUnit: '计费单位',
    minimumCharge: '最低消费',
    deleteSuccess: '删除成功',
    skuLabel: 'SKU标签',
    pickLine: '分拣线',
    chuteNumber: '道口号',
    priority: '优先级',
    isOccupied: '是否占用',
    orderNumber: '单号',
    businessExpenses: '业务费用',
    managementExpense: '管理费用',
    serviceClassCode: '服务类代码',
    serviceClassName: '服务类名称',
    waybill: '面单',
    track: '轨迹',
    overseasWarehouse: '海外仓',
    customsClearance: '清关',
    isEnabled: '是否启用',
    affiliatedTrack: '所属轨迹',
    productConfiguration: '产品配置',
    airwayBill: '空运主单',
    oceanBillOfLading: '海运提单',
    trackDescription: '轨迹说明',
    trackEnglishDescription: '轨迹英文说明',
    copy: '复制',
    parcelTrackingNumber: '包裹跟踪号',
    customerOrderNumber: '客户订单号',
    RedispatchNum: '重派单号',
    thirdPartyServiceOrderNumber: '第三方服务单号',
    AGNumber: 'AG号',
    searchPlaceholder: '单号/提单号/主单号',
    searchPlaceholderNew: '单号/提单号/主单号/客户订单号',
    isCheck: '是否查验',
    clearType: '报关方式',
    ProductFeatures: '产品特性',
    singleClearance: '单独报关',
    mergedClearance: '合并报关',
    clearanceNumber: '报关单号',
    pieces: '件数',
    weight: '重量',
    exporter: '出口商',
    importer: '进口商',
    masterOrderNumber: '主单号',
    oceanOrderNumber: '提单号',
    supplier: '供应商',
    orderStatus: '订单状态',
    isConfirmShip: '是否确认发货',
    orderTime: '下单时间',
    cancelLabel: '取消面单',
    statusFailure: '失败',
    export: '导出',
    OneClickExport: '一键导出',
    exportSmallPackageOrder: '导出小包订单',
    exportSmallPackageOrderByParcel: '导出小包订单-多包裹',
    failOrder: '作废订单',
    printNeutralLabel100x150: '打印中性面单100X150',
    printNeutralLabel100x100: '打印中性面单100X100',
    trialFee: '试算费用',
    printShippingLabel: '打印发货证明',
    warehouse: '仓库',
    recipient: '收件人',
    postcode: '邮编',
    goods: '货物',
    isWeighing: '是否核重',
    weighingWeight: '核重重量',
    transport: '运输',
    totalOrder: '总单',
    exportSuccess: '导出成功',
    operationSuccess: '操作成功',
    onlyOneRecord: '每次只能修改一条记录',
    warehouseCode: '仓库编码',
    goodsRelated: '商品相关',
    parcelAndGoods: '包裹和商品',
    addressee: '收件人',
    contactEmail: '联系邮箱',
    contactPhone: '联系电话',
    stateCode: '省份代码',
    houseNo: '门牌号',
    addressOne: '地址一',
    addressTwo: '地址二',
    companyName: '公司名称',
    ioss: 'IOSS编号',
    tax: '税号',
    caseNumber: '箱号*',
    parcelQty: '箱数',
    parcelWeight: '重量*(KG)',
    parcelLength: '长*(CM)',
    parcelWidth: '宽*(CM)',
    parcelHeight: '高*(CM)',
    parcelVolume: '体积*(m³)',
    labelWeight: '打单重量*(KG)',
    labelLength: '打单长*(CM)',
    labelWidth: '打宽*(CM)',
    labelHeight: '打单高*(CM)',
    itemCode: '商品SKU',
    declaredNameCN: '中文品名',
    declaredNameEN: '英文品名',
    declaredPrice: '申报单价',
    itemQty: '数量',
    PackingQty: '装箱数量',
    receiveQty: '接收数量',
    itemWeight: '产品重量',
    texture: '材质',
    use: '用途',
    brand: '品牌',
    model: '型号',
    line_num: '行号',
    sku_name: '商品名称',
    material: '材质',
    specification: '规格',
    sku_code: '商品编码',
    uom: '计量单位',
    unit_price: '单价',
    origin_country: '原产国',
    destination_country: '目的国',
    customsCode: '海关编码',
    fbaNo: 'FBA号',
    fbaTrackCode: 'FBA货物追踪编号',
    packageSummary: '包裹汇总',
    parcelNum: '包裹号',
    trackingNum: '跟踪号',
    VenderNo: '供应商单号',
    goodsSummary: '商品汇总',
    sku: 'SKU',
    TEMU: 'TEMU',
    eBay: 'eBay',
    Winit: 'Winit',
    Wish: 'Wish',
    irobotbox: 'irobotbox',
    Amazon: 'Amazon',
    itemSize: '尺寸',
    labelTasks: '抓单任务',
    labelDesc: '转单信息',
    unhandled: '未处理',
    handledBy3rdNo: '根据第三方号码处理',
    confirmLabel: '已确认面单',
    failure: '处理失败',
    thirdOrderNo: '第三方订单号',
    modeKey: '取模值',
    handleTimes: '处理次数',
    trackInfo: '轨迹信息',
    time: '时间',
    trackName: '轨迹',
    chargeDetails: '费用明细',
    chargeRate: '单价',
    chargeCount: '数量',
    chargeTotal: '合计',
    currencyType: '币种',
    viewTrack: '查看轨迹',
    orderNum: '订单号',
    PersonResponsible: '责任人',
    PersonAmount: '责任人金额(CNY)',
    PlatformFee: '平台罚金',
    customerOrderNum: '客户订单号',
    ETDTime: 'ETD时间',
    ETATime: 'ETA时间',
    ShipperWh: '发货仓',
    DestinationWh: '目的仓',
    information: '信息',
    departurePort: '启运港',
    destinationPort: '目的港',
    dischargePort: '卸货港',
    vesselName: '船名',
    voyageNum: '航次',
    flight: '航班',
    flightNum: '航班号',
    departureDate: '离港日期',
    estimatedArrivalDate: '预计到货日期',
    actualArrivalDate: '实际到货日期',
    reassignment_deadline_date: '重派截止日期',
    cargo: '货物',
    masterBill: '总单',
    airwayBillNum: '空运主单号',
    oceanBillNum: '海运提单号',
    isCustomsDeclaration: '是否报关件',
    customsDeclarationNum: '报单号',
    submitOrder: '提交订单',
    isSubmitOrder: '是否提交订单',
    downloadTrackingNumber: '下载转单号',
    cancelOrder: '作废订单',
    downloadImportTemplate: '下载导入模板',
    importFBAOrder: '导入FBA订单',
    downloadSingleProductTemplate: '下载单一商品模板',
    importSingleProductOrder: '导入单一商品订单',
    exportPODData: '导出POD数据',
    configureExportDeclaration: '配置出口报关单',
    orderNo: '订单号',
    waitingForPost: '等待作业',
    preDeclared: '已预报',
    intercepted: '已拦截',
    partiallyInWarehouse: '已部分入仓',
    fullyInWarehouse: '已全部入仓',
    confirmedWarehouseData: '已确认入仓数据',
    outOfDomesticWarehouse: '已出国内仓',
    departed: '已离港',
    transfer: '转运',
    signed: '已签收',
    void: '作废',
    RedispatchOrder: '重派',
    SuperVoid: '超级作废',
    roleName: '名称',
    roleDesc: '描述',
    sync_status: '同步状态',
    sync_wait: '未同步',
    sync_finish: '已同步',
    sync_fail: '同步失败',
    menu: '菜单',
    button: '按钮',
    deleteConfirm: '确定删除吗,如果存在下级节点则节点上升，此操作不能撤销！',
    languageIdentifier: '语言标识',
    avatar: '头像',
    username: '用户名',
    name: '姓名',
    email: '邮箱',
    mobile: '手机号码',
    department: '部门',
    companyBelongs: '所属公司',
    warehouseBelongs: '所属仓库',
    position: '职位',
    superiorManager: '上级主管',
    internalUser: '内部用户',
    activated: '激活',
    locked: '锁定',
    consolidationWarehouse: '集运仓',
    belongingWarehouse: '所属仓库',
    permissionName: '名称',
    associatedMenu: '关联菜单',
    method: '方法',
    manifestOrder: '总单',
    relatedMenu: '关联菜单',
    parentPermission: '父级权限',
    customers: '客户',
    transferWarehouse: '中转仓',
    is_general: '是否通用',
    billing_type: '计费类型',
    permissionIdentifier: '权限标识',
    permissionFlag: '权限标志',
    notParentMenu: '不选为父级菜单',
    buttonName: '名称',
    buttonType: '类型',
    sysCode: '系统识别码',
    productCode: '产品编码',
    DisplaySort: '展示排序',
    LocationId: 'LocationId',
    AccountId: 'AccountId',
    quoteCurrency: '报价币种',
    salePrice: '销售价格',
    declaredCurrency: '申报币种',
    declaredNameCn: '中文申报品名',
    declaredNameEn: '英文申报品名',
    productPackaging: '产品发货包装',
    packageShape: '外包装形状',
    IrregularShape: '不规则形状',
    rectangle: '长方形',
    cylinder: '圆柱体',
    packageType: '外包装类型',
    HardPackaging: '硬包装',
    SPackagingH: '软包装+硬物',
    SPackagingS: '软包装+软物',
    category: '类目',
    cat1Name: '类目1',
    cat2Name: '类目2',
    cat3Name: '类目3',
    SKUProperty: 'SKU属性',
    propName: '属性名',
    propValue: '属性值',
    propUnit: '属性单位',
    SKUSalesLink: 'SKU销售链接',
    SaleUrl: '销售链接',
    platform: '平台',
    NoPlatform: '无平台',
    size: '尺寸',
    systemCode: '系统识别码',
    skuCode: 'sku编码',
    mapSkuCode: '销售SKU',
    sync: '同步SKU',
    SalesSKU: '销售SKU',
    MappingType: '映射类型',
    commodityPlatformID: '商品平台ID',
    customID: '自定义ID',
    verificationcodeskucodeplantformaccount: '销售SKU/SKU/平台/账户',
    syncSalesPlatform: '同步第三方销售平台',
    print: '打印条码',
    originalPackaging: '原包装发货',
    repackaging: '重新打包',
    warehouseName: '仓库名称',
    warehouseSearchHolder: '仓库编码/仓库名称/邮箱',
    isOpenToPublic: '是否对外开放',
    contactPerson: '联系人',
    houseNumber: '门牌号',
    tateCode: '省份(州)编码',
    ship: '发运',
    dischargeComplete: '卸货完成',
    receiptComplete: '收货完成',
    shelfComplete: '已上架',
    cost: '成本',
    leaveDate: '离港日期',
    SP: '已发运',
    waiting: '等待同步',
    syncSuccess: '同步成功',
    syncFailed: '同步失败',
    canceling: '取消中',
    cancelException: '取消异常',
    cancelSuccess: '取消成功',
    cancelFailed: '取消失败',
    Withdraw: '提现',
    withdrawalStatus: '提现状态',
    sortNode: '顺序节点',
    subTrackCode: '子轨迹代码',
    pushCode: '推送代码',
    TrackConf: '轨迹配置',
    IossNumber: 'ioss号',
    IossNumberAfter: 'ioss号-改后',
    AgTrackingNumber: 'AG单号',
    CostVersion: '成本版本',
    ChargeValue: '计费值',
    RevenueVersion: '收入版本',
    Position_1: '位置',
    TraceSubcode: '轨迹子代码',
    PullSingleTimeSeconds: '拉单用时(秒)',
    OrderWeight: '打单重量',
    TypeSingleWidthCm: '打单宽*(CM)',
    CompanyName_1: '公司名',
    Address2: '地址2',
    Address1: '地址1',
    RecipientEmailAddress: '收件人邮箱',
    Sender_1: '收发件人',
    Operator: '操作员',
    WarehousingTime: '入库时间',
    NuclearWeight: '核重',
    ProductService: '产品服务',
    BatchUpdate: '批量更新',
    InterceptOperation: '拦截操作',
    ThirdOrderNumber: '第三方单号',
    RecipientCountryCode: '收件人国家编码',
    BigBagNumber: '大包单号',
    InterceptOrNot: '是否拦截',
    WhetherThereIsATrackingNumber: '是否有跟踪号',
    FetchMessage: '获取报文',
    ChangeRemarks: '更改备注',
    UpdateRecipientInformation: '更新收件人信息',
    BatchRenewalOrder: '批量更新订单',
    ModificationProcessingTimes: '修改处理次数',
    UpdateParcelTask: '修改拉单任务',
    ConfirmDelivery: '确认发货',
    CreateInvoice: '创建面单',
    DownloadInvoiceAg: '下载AG面单',
    CompleteTheOrder: '完成订单',
    RestoreOrder: '恢复订单',
    ForceCancellationOfTheOrder: '强制作废订单',
    ExportThePacketReportFile: '导出小包报表文件',
    Redenomination: '重新计价',
    confirmShipOrder: '确认要发运这些订单吗？',
    OrderNumberTrackingNumberCustomerOrderNumberAgNumber: '订单号/跟踪号/客户订单号/AG号',
    ShippingNumber: '出货单号',
    TrackingNumber: '运输单号',
    InterceptCauseRemarks: '拦截原因(备注)',
    WidgetCount: '小件数',
    Complete: '完成',
    OutOfStorage: '已出库',
    BeInStorage: '已入库',
    Intercept: '拦截',
    AreYouSureAboutTheInterceptData: '确定拦截条数据吗？',
    CancelIntercept: '取消拦截',
    Ok: '确定',
    AreYouSureToUnblockThisData: '确定取消拦截本条数据吗？',
    Account: '账户',
    Message: '报文:',
    RecipientPostcode: '收件人邮编',
    AddresseeCountry: '收件人国家',
    ShippersPostalCode: '发货人邮编',
    ShippersCountry: '发货人国家:',
    ProductCode_1: '产品编码:',
    Cancel: '取 消',
    Ok_1: '确 定',
    MessageInquiry: '报文询价',
    OrderNumberInquiry: '订单号询价',
    Inquiry: '询价',
    Expense: '费用',
    ExchangeRate: '汇率',
    StowageQuantity: '配载数量',
    ReasonForCorrection: '批改理由',
    ContactAddressOfTheInsured: '被保人联系地址',
    InsuredTelephoneNumber: '被保险人电话',
    DocumentNumberOfTheInsured: '被保人证件号码',
    TypeOfDocumentOfTheInsured: '被保人证件类型',
    RiotProtection: '暴动保障',
    ListingLocation: '上架的地点',
    ShelfGuarantee: '是否上架保障',
    CountryOfDestination: '目的国',
    DestinationType: '目的地类型',
    PointOfDeparture: '起运地',
    CountryOfDeparture: '起运国',
    TimeOfDeparture: '起运时间',
    ClassOfGoods: '货物类别',
    CargoDescription: '货物描述',
    PackingQuantity: '包装数量',
    PackagingType: '包装类型',
    ExpressCompany: '快递公司',
    DeliveryMethod: '派送方式',
    DeliveryAddresses: '派送地址',
    BillOfLadingNumber: '提单号/运单号',
    MeansOfTransportAndVoyage: '运输工具及航次',
    ModeOfTransport: '运输方式',
    ActualWeightKg: '实际重量(KG)',
    PolicyCurrency: '保单币种',
    AdditionRatio: '加成比例',
    HowTheInsuredAmountIsDetermined: '保额确定方式',
    FreightCurrency: '运费币种',
    NameOfTheInsured: '被保险人名称',
    ProductCode_2: '产品代码',
    OriginalTrackingNumber: '原单号',
    MeasurementData: '测量数据',
    MagneticOrNot: '是否带磁',
    LiveOrNot: '是否带电',
    UnitPriceUsd: '单价(USD)',
    EnglishUsage: '英文用途',
    ChineseUse: '中文用途',
    EnglishMaterial: '英文材质',
    ChineseMaterial: '中文材质',
    ProductPicture: '商品图片',
    CommodityInformation: '商品信息',
    GenerateWarehouseEntryTemplate: '生成入仓模板',
    ParcelInformation: '包裹信息',
    ExpirationTime: '过期时间',
    BookedTime: '已预约时间',
    PassTheWhitelistTime: '通过白名单时间',
    ShipmentStatus: '货件状态',
    TypeOfShop: '店铺类型',
    ShipmentNumber: '货件号',
    ShopId: '店铺ID',
    DownloadBoxLabel: '下载箱唛',
    ShipmentManagement: '货件管理',
    MerchandiseAndDeclarationInformation: '商品与申报信息',
    SupplierTotalVolume: '供应商总体积',
    SupplierGrossWeight: '供应商总重量',
    TotalNumberOfBoxesFromSuppliers: '供应商总箱数',
    TotalInboundVolume: '入仓总体积',
    TotalInboundWeight: '入仓总重量',
    TotalNumberOfContainersInWarehouse: '入仓总箱数',
    TotalCartons: '总箱数',
    CountryOrAreaCode: '国家或地区编码',
    ReceivingDetailsAddress: '收件详细地址',
    DeliveryNote: '提货备注',
    DeliveryTime_1: '提货时间',
    PickupPhoneNumber: '提货手机号',
    DeliveryAddress_1: '提货地址',
    DeliveryInformation_1: '提货信息',
    PriorityLocus: '优先轨迹',
    RequisitionForm: '揽收单',
    ShippingPriorityTrackNumber: '海运优先轨迹单号',
    EstimatedDateOfArrivalOverseas: '预计到海外仓日期',
    ActualDateOfArrivalOverseas: '实际到海外仓日期',
    ArrivalDate_1: '到仓日期',
    CustomsDeclaration_1: '报关资料',
    OrderNote: '订单备注',
    PortOfDestinationClearance: '目的港清关',
    TimeWhenTheShipmentNumberExpires: '货件号过期时间',
    DriversName: '司机名称',
    LicensePlateNumber: '车牌号',
    PlaceOfOrigin: '始发地',
    TruckNumber: '卡车单号',
    TransportDetail: '转运明细',
    RelevantInformation: '相关信息',
    TranshipmentBin: '转运目的仓',
    TheConsigneeOfTheBillOfLadingIsAwbconsignee: '提单收件人（awb consignee）',
    AddressInformation: '地址信息',
    SubdivisionRequirement: '分货要求',
    WhetherToDivideGoodsOrNot: '是否分货',
    ActualPallets: '实际托盘数',
    EstimatedPallets: '预计托盘数',
    PalletMaxLoad: '最大承重量(kg)',
    ActualWeight: '实际重量',
    NOAWeight: 'NOA重量',
    WarehouseWeight: '仓库称重',
    ActualVolume: '实际体积',
    ActualNumberOfParcels: '实际包裹数',
    ExpectedNumberOfParcels: '预计包裹数',
    CustomsClearanceCompletionTime: '清关完成时间',
    AirportPickupTime: '机场提货时间',
    TimeOfArrivalNotice: '已到货通知时间',
    CustomsClearanceNumber: '清关单号',
    PriceValidity: '价格有效期',
    VoyageDescription: '航程说明',
    RemoteOrNot: '是否偏远',
    AdditionalCharge: '附加费',
    FirstLegPrice: '头程价格',
    PlaceOfDelivery: '交货地点',
    DetailOfQuotationScheme: '报价方案明细',
    QuotationScheme: '报价方案',
    TaxfreeOrNot: '是否包税',
    Channel_1: '渠道',
    WhetherToDeclare: '是否报关',
    PrescriptionRequirement: '时效要求',
    DangerousGoodsInGeneral: '普货/危品',
    TheNameOfAProductOrCommodity: '品名',
    Sell: '销售',
    InquiryScheme: '询价方案',
    IsoCountryOfArrival: 'ISO到达国别',
    Orderdependent: '订单相关',
    PingAnInquiryCode: '平安询价code',
    NameOfConsignee: '收货人名称',
    ResultDescription: '结果描述',
    ResultCode: '结果码',
    TerminationDate: '保险终止日期',
    Premium: '保费',
    PolicyNumber: '保单号',
    CurrencyOfValue: '货值币种',
    DeclaredValueOfGoods: '货物价值',
    DestinationAddress: '目的地地址',
    DepartureAddress: '出发地地址',
    SellerId: '卖家ID',
    BuyerId: '买家ID',
    QuantityOfGoods: '货物数量',
    TypeOfGoods: '货物类型',
    ExpressCompanyName: '快递公司名称',
    DeliveryTime_2: '寄件时间',
    ProjectName: '项目名称',
    SchemeName: '方案名',
    TotalAmountInsured: '投保总金额',
    CommencementOfInsurance: '保险起期',
    BeneficiarysDocumentNumber: '受益人证件号码',
    BeneficiaryDocumentType: '受益人证件类型',
    NameOfBeneficiary: '受益人名称',
    NameOfTheInsured_1: '被保人姓名',
    ApplicantsCertificateNumber: '投保人证件号码',
    ApplicantsDocumentType: '投保人证件类型',
    NameOfPolicyholder: '投保人名称',
    ExternalProductCoding: '外部产品编码',
    ExternalChannelCoding: '保险产品编码',
    OtherAccessories: '其它附件',
    NewProductLabel: '新产品标',
    NumberOfProductsChanged: '换标产品数',
    NewShipmentBoxLabel: '新货件箱标',
    NumberOfContainersChanged: '换标箱数',
    NewShipmentNumber: '新货件号',
    businessNumber: '业务单号',
    businessType: '业务类型',
    vasNumber: '增值单',
    vasStatus: '增值状态',
    parseOceanOrder: '解析提单',
    ChargeList: '费目',
    AbnormalInfo: '异常信息',
    ChangeMarkInstruction: '换标指令',
    ChangeMarkOrNot: '是否换标',
    NumberOfOutgoingBins: '出仓箱数',
    NumberOfOutgoingBinsDetail: '出仓箱数详情',
    DestinationWarehouse: '目的仓',
    EngagementLetter: '预约信',
    AppointmentTime: '预约时间',
    BindReservationOrNot: '是否绑定预约',
    ChargeQuantity: '计费数量',
    ValueaddedServiceOrderNumber: '增值服务单号',
    OutboundOrderNumber: '出库单号',
    ConfirmWarehouseEntryData: '确认入仓数据',
    Revocation: '撤回',
    VAS: '增值服务',
    ImportFbmOrder: '导入FBM订单',
    DownloadTheReceipt: '下载进仓单',
    OneClickDownloadTheReceipt: '一键下载进仓单及箱唛',
    ExportPackingList: '导出箱单',
    TrackingNumberCustomerTrackingNumberPackageNumberTrackingNumber: '单号/客户单号/包裹号/跟踪号',
    SigningTime: '签收时间',
    RecipientInformation: '收件人信息',
    WhetherToTakeDeliveryOrNot: '是否提货',
    WhetherToInsuranceOrNot: '是否购买保险',
    ProductCharacteristics: '产品特性',
    MeikeMoreAppointmentTime: '美客多预约时间',
    ShipmentNumberOrderNumberCustomerOrderNumberStoreId: '货件号/订单号/客户订单号/店铺ID',
    HaveBeenCancelled: '已作废',
    LastOperatingTime: '最近操作时间',
    DateInformation: '日期信息',
    LatestOperationTime: '最早可约时间',
    SpecialRequirements: '特殊要求',
    WaitingForNotice: '等通知预约',
    QuicklyBook: '尽快预约',
    BookUntilTheEndOfTheMonth: '预约月底',
    BookingInformation: '预约信息',
    OutboundInstructionStatus: '出仓指令状态',
    LatestTrackTime: '最新轨迹时间',
    LatestTrackName: '最新轨迹名称',
    OrderInformation: '订单信息',
    ShipmentInformation: '货件信息',
    ShopInformation: '店铺信息',
    ThereIsNo: '无',
    TemporaryStorageOverseas: '暂存海外仓',
    PrivateAddress: '私人地址',
    AmazonWarehouse: '亚马逊仓',
    CrossborderStore: '跨境店',
    LocalStore: '本土店',
    HaveBeenDelivered: '已送达',
    WaitingToBeReleasedFromStorage: '待出库',
    HaveAlreadyReserved: '已预约',
    HaveArrivedAtPort: '已到港',
    EnRoute: '在途',
    ImportUpdateFbmOrder: '导入订单数据',
    ImportUpdateFbaOrder: '导入更新订单',
    ImportCustomsClearanceForm: '导入清关单',
    ImportManifest: '导入manifest',
    ExportTheOperationSummaryTable: '导出运营总表',
    ExportCustomsClearanceOrder: '导出清关订单',
    OrderNumberChineseProductNameEnglishProductNameFlightNumberPortOfDestination: '订单号/中文品名/英文品名/航班号/目的港',
    GhaDisplaysTheDlvTime: 'GHA显示DLV时间',
    GhaHasSentNoaTime: 'GHA已发NOA时间',
    UploadingPodcrmIsComplete: '上传POD/CRM完成',
    ItsInTheTransferBin: '已到转运仓',
    TransferCarHasBeenDispatched: '转运车已发',
    CustomsClearanceDocumentsHaveBeenReceived: '已收到清关文件',
    PodcrmHasBeenReceived: '已收到POD/CRM',
    PartialCustomsClearance: '部分清关',
    PartialArrival: '部分到仓',
    WarehouseNotifiesCustomsClearanceTime: '仓库通知清关时间',
    WarehouseActualReceiptTime: '仓库实际收货时间',
    TimeWhenTheWarehouseReceivedNoa: '仓库收到NOA时间',
    TheFlightShipHasArrived: '航班/船只已到达',
    Audited: '已审核',
    PlacedAnOrder: '已下单',
    CustomsClearanceOrder: '清关订单',
    Reorder: '转订单',
    SendInquiry: '发送询价',
    Total_1: '合计：',
    InquirySheet: '询价单',
    ApplyForClaim: '申请理赔',
    PriceInformation: '价格信息',
    PolicyInformation: '保单信息',
    InsuredInformation: '被保人信息',
    PolicyholderInformation: '投保人信息',
    Warranty: '保单',
    SystemPolicyNumber: '系统保单号',
    CompletionOfInsurance: '投保完成',
    ClaimFailure: '理赔失败',
    ClaimCompletion: '理赔完成',
    ClaimsInProgress: '理赔中',
    SuccessfulInsurance: '投保成功',
    AuditFailure: '审核失败',
    AuditCompleted: '审核完成',
    PolicyOfInsurance: '保险单',
    StartSubmission: '开始提交',
    ShipmentNumberOrderNumber: '货件号/订单号',
    ExitOrder: '出仓指令',
    OutboundBoxQty: '已出箱数',
    HoldingBoxQty: '占用箱数',
    InventoryBoxQty: '库存箱数',
    AllParcelVolume: '包裹总体积',
    OutboundOrderNums: '出库订单总数',
    ActualOverseasArrivalTime: '实际到海外仓时间',
    OverseasOrderNumber: '海外出库单号',
    SubmitValueAddedOrder: '提交增值单',
    ValueAddedOrderShipmentNumber: '增值单/订单/货件号',
    AuditTime: '审核时间',
    IncrementCharge: '增值费目',
    IncrementNumber: '增值单号',
    InOperation: '操作中',
    ValueaddedService: '增值服务',
    HasArrivedOverseasWarehouse: '已到达海外仓',
    ArrivedAtPortAndLanded: '已到港/已降落',
    HaveLeftPortAndTakenOff: '已离港/已起飞',
    Declared: '已报关',
    OutOfStorage_1: '已出仓',
    ConfirmationOfWarehousing: '确认入仓',
    HaveArrived: '已到货',
    TotalData: '全部数据',
    InquiryNumber: '询价单号',
    HaveQuotedThePrice: '已报价',
    CardPieInquirySheet: '卡派询价单',
    CustomerOrderNumberTrackingNumber: '客户订单号/跟踪号',
    StateType: '状态类型',
    BillFxNumber: '账单FX号',
    ProcessingComplete: '处理完成',
    TrackingNumber_1: '跟踪单号',
    DocumentNumberTrackingMainDocumentNumberTrackingDocumentNumberEnglishName: '单据号/跟踪主单号/跟踪单号/英文品名',
    TrackingDocumentNumber: '跟踪单据号',
    ResultOfClaim: '索赔结果',
    SuccessfulClaim: '索赔成功',
    GetTrackOrNot: '是否获取轨迹',
    ExtendedField: '扩展字段',
    GenerateFinancialStatisticalCharts: '生成财务统计图',
    GenerateABillingChart: '生成计费统计图',
    GenerateOrderStatisticsChart: '生成下单统计图',
    ReportCoding: '报表编码',
    ReportName: '报表名称',
    SelectOrderDate: '选择下单日期',
    BiReportOfSupplierInterconnection: '供应商对接BI报表',
    OutOfService: '停用',
    TrajectoryDescription: '轨迹描述',
    TrackSupplier: '轨迹供应商',
    TrackBlacklist: '轨迹黑名单',
    VendorSubcode: '供应商子编码',
    SupplierTrackCoding: '供应商轨迹编码',
    ConversionType: '转换类型',
    SystemTrajectoryCoding: '系统轨迹编码',
    TrajectoryTransformationInformation: '轨迹转换信息',
    WhetherMultipleOrderNumbersAreSupported: '是否支持多个单号',
    TrackSupplierInformation: '轨迹供应商信息',
    ParcelTrackDetails: '包裹轨迹详情',
    ParcelTrack: '包裹轨迹',
    TheOrderNumberOfTheThirdpartyServiceProvider: '第三方服务商的订单号',
    PullNumber: '拉单次数',
    Priority: '优先级',
    PullState: '拉取状态',
    PushStatus: '推送状态',
    Abnormal: '异常',
    Edit: '修改',
    RePull: '重新拉取',
    SingleBindingTrackingNumberOfTheUploadPlane: '上传面单绑定跟踪号',
    OrderNumberTrackingNumber: '订单号/跟踪号',
    ExceptionMessage: '异常信息',
    TimeoutPrompt: '超时提示',
    NotOverTime: '没超时',
    Overtime: '超时',
    SmallPacketTrackTask: '小包轨迹任务',
    ShippingOrder: '海运订单',
    AirOrder: '空运订单',
    PullNumber_1: '拉取次数',
    PullComplete: '拉取完成',
    Nopull: '不拉取',
    WaitingToBeDrawn: '待拉取',
    Unbind: '取消绑定',
    OrderNumberPackageNumberWarehouseLocationNumber: '订单号/包裹号/库位号',
    UntyingTime: '解绑时间',
    VoidOrNot: '是否作废',
    Unbound: '已解绑',
    Bound: '已绑定',
    NumberOfParcels: '包裹数',
    TransshipmentDestinationBinCode: '转运目的仓编码',
    AssociatedOrderNumber: '关联订单号',
    Untie: '解绑',
    ParcelOrderDetails_1: '包裹订单明细',
    UnpackOrNot: '是否开箱',
    TapeColor: '胶带颜色',
    CustomsClearancePackageNumber: '清关大包单号',
    CustomsClearancePackage: '清关大包单',
    TimeTheTransferCarHasBeenDispatched: '转运车已发时间',
    CustomsClearanceCustomerCode: '清关客户代码',
    Locations: '地点',
    MonetaryUnit: '货币单位',
    CostExpense: '成本费用',
    ActualArrivalDate_1: '实际到达日期',
    ActualDepartureTime: '实际出发时间',
    EstimatedTimeOfDeparture: '预计出发时间',
    ConfigureCustomsClearance: '配置清关单',
    CustomsClearanceOrderNumber: '清关订单号',
    TheNumberOfTheThirdPartyPackage: '第三方大包单号',
    SendToSite: '发往站点',
    OriginatingSite: '发出站点',
    LargePackageOrderNumber: '大包订单号',
    BulkOrder: '大包订单',
    SupplierCancelsWaybill: '供应商取消运单',
    SyncVasOrder: '同步增值服务单',
    PushToSupplier: '推送至供应商',
    WithdrawSubmission: '撤回提交',
    ReviewTheOrder: '审核订单',
    EstimatedNumberOfParcels: '预计包裹数量',
    MailingAddressZipCode: '收件地址邮编',
    ConfigureCustomsClearancePackage: '配置清关大包单',
    CustomsClearanceParcelOrder: '清关包裹订单',
    GrossIncome: '总收入',
    Printer_1: '打印机：',
    Copies_1: '份数：',
    ImportANewTraceNumber: '导入新跟踪号',
    SystemCodeMasterOrderNumberCustomerNumberCmrnumber: '系统代码/主单号/客户编号/CMR number',
    UpdateStatus: '更新状态',
    LeadinTime: '导入时间',
    WeighLargeBagsInBulk: '批量大包称重',
    Backorder: '回退订单',
    ImportABatchOfSmallPackingCases: '导入批量小包装箱',
    DownloadTheBatchSmallPackingCaseTemplate: '下载批量小包装箱模板',
    ImportBatchPacketWeighing: '导入批量小包称重',
    DownloadBatchPacketWeighingTemplate: '下载批量小包称重模板',
    RecordType: '记录类型',
    ChangeTheMark: '换标',
    Encasement: '装箱',
    Weigh: '称重',
    WaybillNumberAndCaseNumber: '运单号/箱号',
    Result: '结果',
    PostSheet: '邮政面单',
    NeutralFaceSheet: '中性面单',
    DefaultSheet: '默认面单',
    Ticket: '票',
    ResultThisOperationHasBeenPerformed: '操作结果: 本次已操作:',
    SheetType: '面单类型：',
    Operator_1: '操作人员',
    WaybillNumberPackageNumber: '运单号/包裹号',
    WeightKg: '重量KG',
    HeightCm: '高CM',
    WidthCm: '宽CM',
    LengthInCm: '长CM',
    TakeOutOfStorage: '出仓',
    PackingNumberPackageNumber: '装箱号(包裹号)',
    HwbNumber: '运单号',
    BulkExport: '批量导出',
    SelectiveDerivation: '选择导出',
    CaseNumberOrderNumberWarehouseReceiptNumber: '箱号/订单号/盘仓单号',
    LastOperatingTime_1: '最后操作时间',
    ArrivalTime_1: '盘点时间',
    WarehouseReceiptNumber: '盘仓单号',
    WarehouseInventoryDetails: '集货仓盘点详情',
    ActualHeight: '实际高',
    ActualWidth: '实际宽',
    ActualLength: '实际长',
    OrderHigh: '打单高',
    SingleWidth: '打单宽',
    OrderLength: '打单长',
    ForcedUpdate: '强制更新',
    ClickConnectClient: '点击连接客户端',
    Connected: '已连接',
    CheckinTime: '签入时间',
    OrganizationCode: '组织代码',
    PushResult: '推送结果',
    TrackNumber: '跟踪号',
    PushCompleteOrNot: '是否推送完成',
    Source: '来源',
    OrganizationalStructure: '组织机构',
    TissueCoding: '组织编码',
    PortOfShipment: '起运港',
    DepartureTime: '离港时间',
    DestinationCountry: '目的国家',
    PostalTrackingNumber: '邮政单号',
    PushEmsBulkPackageData: '推送Ems大包单数据',
    Repush: '重新推送',
    RetryTimes: '重试次数',
    NotProcess: '不处理',
    SuccessfulProcessing: '处理成功',
    Error: '误差',
    Lose: '失败',
    A: '件',
    Predict: '预计',
    FrontTicketPackageNumber: '前票包裹号：',
    CollapsePanel: 'Collapse 折叠面板',
    TheCarouselWasOnTheMove: 'Carousel 走马灯',
    Card: 'Card 卡片',
    PopoverIndicatesThePopupBox: 'Popover 弹出框',
    TooltipTextPrompts: 'Tooltip 文字提示',
    DialogDialogBox: 'Dialog 对话框',
    StepsStepBar: 'Steps 步骤条',
    DropdownDropdownMenu: 'Dropdown 下拉菜单',
    Breadcrumb: 'Breadcrumb 面包屑',
    Tabs: 'Tabs 标签页',
    NavmenuIndicatesTheNavigationMenu: 'NavMenu 导航菜单',
    Notification: 'Notification 通知',
    MessageboxDisplaysABox: 'MessageBox 弹框',
    MessageMessagePrompt: 'Message 消息提示',
    Loading: 'Loading 加载',
    Alert: 'Alert 警告',
    BadgeMark: 'Badge 标记',
    Pagination: 'Pagination 分页',
    TreeTreeControl: 'Tree 树形控件',
    ProgressProgressBar: 'Progress 进度条',
    TagTag: 'Tag 标签',
    TableTable: 'Table 表格',
    FormForm: 'Form 表单',
    RateScore: 'Rate 评分',
    Upload: 'Upload 上传',
    DatetimepickerDateAndTimeSelector: 'DateTimePicker 日期时间选择器',
    DatepickerDateSelector: 'DatePicker 日期选择器',
    TimepickerTimeSelector: 'TimePicker 时间选择器',
    SliderSlider: 'Slider 滑块',
    SwitchSwitch: 'Switch 开关',
    CascaderCascadeSelector: 'Cascader 级联选择器',
    SelectSelector: 'Select 选择器',
    InputnumberIndicatesTheCounter: 'InputNumber 计数器',
    InputInputBox: 'Input 输入框',
    CheckboxCheckbox: 'Checkbox 多选框',
    RadioOptionBox: 'Radio 单选框',
    ButtonButton: 'Button 按钮',
    IconIcon: 'Icon 图标',
    Typography: 'Typography 字体',
    ColorColor: 'Color 色彩',
    LayoutLayout: 'Layout 布局',
    ComponentInteractionDocument: '组件交互文档',
    Assets: '资源',
    Module: '组件',
    TopNavigation: '顶部导航',
    LateralNavigation: '侧向导航',
    Navigation: '导航',
    Steerable: '可控',
    Efficiency: '效率',
    Feedback: '反馈',
    Accord: '一致',
    DesignPrinciple: '设计原则',
    Guide: '指南',
    PreviousTicketNumber: '前票单号：',
    Weight_1: '重量: ',
    HwbNumber_1: '运单数',
    ScanFrame: '扫描框',
    ForecastHeight: '预报高',
    ForecastWidth: '预报宽',
    ChiefForecaster: '预报长',
    ForecastWeight: '预报重量',
    Lose_1: '失败：',
    Complete_1: '完成：',
    Actual: '实际：',
    Predict_1: '预计：',
    PreticketOrderNumber: '前票订单号：',
    ErrorDiffweight: '误差',
    FrontweightFrontweight: '前票重量',
    ForcedReplacementWeight: '强制更新重量',
    CustomerCode: '客户代码',
    NoPacketOrderFoundPleaseConfirm: '未找到小包订单,请确认',
    SheetInformation: '面单信息',
    PrintLabel: '打印标签',
    AggregateCoreWeight: '汇总核重',
    DeliveryDate_1: '送货日期',
    CustomsClearanceStatus: '清关状态',
    CrossborderInsurancePolicyInformation: '跨境保保险单信息',
    ActualHeightCm: '实际高(CM)',
    ActualWidthCm: '实际宽(CM)',
    ActualLengthCm: '实际长(CM)',
    Color: '颜色',
    ShippingOrder_1: '出货单',
    ShipmentDetail: '出货单明细',
    SignatureForm: '签收单',
    CustomerNotice: '客户通知',
    OperationRequirement: '操作要求',
    CustomerSplit: '客户分泡（%）',
    CountryCode_1: '国家编码',
    PictureDisplay: '图片展示',
    ProcessingTime: '处理用时',
    WarehousingValuation: '入仓计价',
    OutgoingQuantity: '出仓数量',
    ShippingInformation: '海运信息',
    TaskDescription: '任务描述',
    TaskType: '任务类型',
    SynchronizationTask: '同步任务',
    LatestTrack: '最新轨迹',
    WeightActualWeight: '重量/实际重量',
    HeightActualHeight: '高/实际高',
    WidthActualWidth: '宽/实际宽',
    LongActualLength: '长/实际长',
    AdjustmentOrderNumber: '调整单号',
    IncomeAdjustmentDetail: '收入调整明细',
    ChargeVolume: '计费体积',
    Capapod: '卡派POD',
    Cappai: '卡派单',
    LogisticsPlan: '物流计划',
    PrivateAddressOrNot: '是否私人地址',
    WhetherOverweightOrNot: '是否超重',
    IsItTooLong: '是否超长',
    WhetherInterceptedOrNot: '是否被拦截',
    TrailerLicensePlateNumber: '拖车车牌号',
    EmptyTankWeight: '空柜重量',
    SealNumber: '封条号',
    Shipowner: '船东',
    Contractor: '合约方',
    NumberOfCountedPieces: '盘点件数',
    InventoryDetails: '盘点详情',
    CountTheJobTicketNumber: '盘点作业单号',
    InventoryTaskNumber: '盘点任务号',
    TaskInformation: '任务信息',
    InventoryTask: '盘点任务',
    InsuredShippingOrderSpacesOrLineBreaksAreAcceptable: '投保运输订单(空格或换行皆可)',
    InsuranceOrder: '投保订单',
    PolicyRemarks: '保单备注',
    PolicyStatus: '保单状态',
    QuantityOfSmallPieces: '小件数量',
    BulkQuantity: '大包数量',
    DispatchTime: '发出时间',
    ShippingDocument: '运输单',
    WeighingTime: '称重时间',
    RelatedShipment: '关联出货',
    UpdateTheWeightOfTheLargeBag: '更新大包单重量',
    PushLargePackageOrder: '推送大包单',
    ConfigurationInvoice: '配置出货单',
    NuclearGravityTime: '核重时间',
    ThirdPartyTrackingNumber: '第三方跟踪号',
    NumberOfSmallPackages: '小包件数',
    Insurance: '保险',
    BillSending: '账单发送',
    AmendmentOfPolicy: '修改保险单',
    SearchPolicy: '查询保险单',
    SubmissionOfPolicy: '提交保险单',
    OperationRequirementsAndTransferInformation: '操作要求与转单信息',
    RecipientPostcode_1: '收件人邮编',
    InsuredDetails: '被保险人详细',
    DestinationStation: '目的站',
    StationOfDeparture: '始发站',
    WhetherToConfigureDocuments: '是否配置单据',
    PacketAging: '小包时效',
    AgingOfEachWarehouse: '各仓时效',
    SalesBi: '销售BI',
    PrerecordedTicket: '预录单',
    InvoiceData: '打单数据',
    OrderBoxSize: '订单箱子尺寸',
    CustomerConfirmation: '客户确认',
    ExportOrder: '导出订单',
    ExportShipment: '导出货件',
    ConfigureReceivingOrders: '配置揽收单',
    ForceAllWarehousing: '强制全部入仓',
    WmsWarehouse: 'wms仓库',
    WarehouseCode_1: '仓库代码',
    OrderDate_1: '下单日期',
    StatusOfCustomsDeclaration: '报关单状态',
    OwnedCustomer: '所属客户',
    GetTheReceiptNumber: '揽收单号',
    CollectionBin: '揽收仓',
    OrderNumberCustomerOrderNumberTransferNumberThirdpartyServiceOrderNumber: '订单号/客户订单号/转单号/第三方服务单号',
    ExpressDeliveryNote: '快递派送单',
    OrderNumberFlightNumberPortOfDestination: '订单号/航班号/目的港',
    WarehouseTaskNumberCrateNumberOrderNumber: '盘仓任务号/箱号/订单号',
    SystemInformation: '系统信息',
    SellProducts: '销售产品',
    CustomerCode_1: '客户编码',
    ServiceProviderBillNumber: '服务商单号',
    JobStatus: '作业状态',
    JobInformation: '作业信息',
    PalletTask: '仓盘任务',
    On: '开启',
    CompleteInventory: '完成盘点',
    OpenCount: '开启盘点',
    TimeInformation: '时间信息',
    WarehouseOrder: '仓盘单',
    ReturnDraft: '退回草稿',
    DestinationInformation: '目的地信息',
    DepartureInformation: '起运信息',
    SettlementOfClaims: '理赔',
    Examine: '审核',
    ReturnedCargo: '回退出货',
    Shipment: '出货',
    ShipmentRM: 'RM 出货',
    ShippingNote: '出货单/运输单',
    AssociatedTransportBill: '关联运输单',
    Shipped: '已出货',
    ShipmentShipped: '已发货',
    ShipmentInTransit: '已在途',
    ShipmentPicked: '已拣货',
    WaitToBeShipped: '待出货',
    ShippingTime: '出货时间',
    SelectShippingTime: '选择出货时间',
    ExpectedSigningTime: '预计签收时间',
    EstimatedTimeOfArrival: '预计到港时间',
    EstimatedTimeOfDeparture_1: '预计离港时间',
    OriginatingBin: '起运仓',
    ShippingWarehouse: '启运仓',
    ProjectName_1: '计划名称',
    WhetherToSignAContractOrNot: '是否签合同',
    LogisticsProviderCodeLogisticsProviderForShort: '物流商编码/物流商简称',
    LogisticsBusinessChineseName: '物流商中文名',
    ShortForLogisticsProvider: '物流商简称',
    LogisticsProviderCode: '物流商编码',
    FbaPackage: 'FBA包裹',
    DeliveryCompletionDate: '派送完成日期',
    TransactionMethod: '成交方式',
    ModeOfTrade: '贸易方式',
    FbaOrderDetails: 'FBA订单明细',
    EstimatedTimeOfArrival_1: '预计上门时间',
    ContactInformation: '联系方式',
    PayOrNot: '是否代付',
    ExpressInfo: '快递信息',
    CarryOrNot: '是否搬运',
    FreightInformation: '货运信息',
    DestinationWarehouseCoding: '目的地仓库编码',
    ReceivingAddress: '揽收地址',
    ExportDeclarationForm: '出口报关单',
    PackingList: '装箱清单',
    InterceptParcel: '拦截包裹',
    WhetherItIsOutOfTheWarehouse: '是否已出仓',
    SystemPackageNumber: '系统包裹号',
    ConfirmData: '是否确认数据',
    SalePriceCardTime: '销售价卡时间',
    SheetAddress: '面单地址',
    BillNumber: '账单编号',
    PlannedTimeOfArrival: '计划到达时间',
    ActualDepartureTime_1: '实际起飞时间',
    PlannedDepartureTime: '计划起飞时间',
    Path: '路径',
    Range: '航程',
    CargoTrack: '货物追踪轨迹',
    PushTask: '推送任务',
    BookingTime: '订舱时间',
    PortOfTransshipment: '中转港',
    CostService: '成本服务',
    StowageVolume: '配载体积',
    StowageWeight: '配载重量',
    CustomerInformation: '客户信息',
    BookedVolume: '订舱体积',
    BookedWeight: '订舱重量',
    NumberOfBookingPieces: '订舱件数',
    CabinetType: '柜型',
    RouteAbbreviation: '航线简称',
    ActualLoadingTime: '实际装柜时间',
    RecommendedLoadingTime: '建议装柜时间',
    PriorityOrNot: '是否优先',
    BillOfLading: '提单',
    CompleteStowage: '完成配载',
    TrackTheBillOfLadingNumber: '跟踪海运提单号',
    TheCardPieList: '卡派子单',
    ActualDeliveryTime: '实际送仓时间',
    MakeAnAppointmentForDeliveryTime: '预约送仓时间',
    BookingNumber: '预约单号',
    ExportList: '导出清单',
    EliminationOfCostAllocation: '取消成本分摊',
    CostAllocation: '成本分摊',
    OrderFulfillment: '订单完成',
    QuickEntry: '快捷录入',
    GenerateBillOfLadingTemplate: '生成提单模板',
    StatusChange: '状态更改',
    OrderNumberBillOfLadingNumberMainOrderNumber: '单号/订单号/提单号/主单号',
    OtherInformation: '其他信息',
    RelatedDocuments: '关联单据',
    ImporterAndExporter: '进出口商',
    CustomerSupplier: '客户供应商',
    Receipt: '单据',
    GeneralTrade: '一般贸易',
    ThePrerecordHasBeenConfirmed: '预录单已确认',
    ThePrerecordWasNotConfirmed: '预录单未确认',
    InformationProvided: '资料已提供',
    WaitingForCustomerInformation: '等待客户资料',
    Confirmed: '已确认',
    ToBeConfirmed: '待确认',
    ToBeDeclared: '待报关',
    CustomsDeclarationFailure: '报关失败',
    CustomsDeclarationCompleted: '报关完成',
    WaitForAudit: '待审核',
    WaitForCustomsDeclaration: '等待报关',
    DocumentStatus: '单据状态',
    EliminationOfIncomeSharing: '取消收入分摊',
    IncomeSharing: '收入分摊',
    OrderNumberCustomerOrderNumberAddressRemarks: '揽收单号/客户订单号/地址/备注',
    DestinationBin: '目的地仓',
    IncomeSharingOrNot: '是否收入分摊',
    ReceivingOrderStatus: '揽收单状态',
    WhetherTheGoodsHaveArrived: '是否到货',
    SynchronousOrder: '创建订单',
    MaritimePriorityBillNumber: '海运优先单号',
    SeaWaybillNumber: '海运单号',
    SynchronousState: '同步状态',
    SyncWarehouseState: '同步仓库状态',
    SalesPriceCard: '销售价卡',
    SynchronizeRevenueToSuppliers: '同步收入至供应商',
    SynchronizeOrdersToSuppliers: '同步订单至供应商',
    ConfigureTheCardDeliveryOrder: '配置卡派单',
    AllocationOfShippingPriority: '配置海运优先',
    TrackingNumberCustomerTrackingNumberPackageNumberTransferNumber: '单号/客户单号/包裹号/转单号',
    ConfirmData_1: '确认数据',
    SyncToSupplier: '同步至供应商',
    NumberOfPackages: '配载件数',
    ConfirmedChargeVolume: '确认计费体积',
    Unsynchronized: '未同步',
    ModificationOfOverseasPosition: '修改海外仓',
    ReserveWarehouseReceipt: '预约仓单',
    UpdateImport: '更新导入',
    CreateAnExpressDeliveryNote: '创建快递派送单',
    BatchUpdateImport: '批量更新导入',
    ImportTransferOrderExcel: '导入转单Excel',
    OrderNumberPackageNumberSystemPackageNumberTransferNumber: '订单号/包裹号/系统包裹号/转单号',
    WhetherToCombinePackages: '是否合并包裹',
    PushTrunkTraffic: '推送干线交航',
    CostPricing: '成本计价',
    PushMasterOrder: '推送主单',
    PushMasterOrderToImile: '推送主单到Imile',
    PushMasterOrderToGwe2: '推送主单到Gwe2',
    PushMasterOrderToAsendia: '推送主单到Asendia',
    PushMasterOrder4PX: '推送主单到4px',
    HaveReturnedToTheContainer: '已还柜',
    ContainerRemoved: '已提柜',
    DeliveryCompleted_1: '出库完成',
    OutOfStorage_2: '出库中',
    StowageComplete: '配载完成',
    Stowage: '配载中',
    DeliveryHasBeenBooked: '已预约送仓',
    HaveBeenSentToOverseasWarehouse: '已到海外仓',
    HaveBeenReleased: '已放行',
    Inspected: '已查验',
    ForecastList: '预报清单',
    OrderNumberAndCabinetNumber: '单号/柜号',
    LoadingTime: '装柜时间',
    MeasuredWeight: '计量重',
    ParsPili: '件毛体',
    RouteAbbreviation_1: '航线简称.',
    AirRoute: '航线',
    ShippingInformation_1: '航运信息',
    BillOfLadingStatus: '提单状态',
    ImportBinding: '导入绑定',
    PriorityTrajectoryChange: '优先轨迹更改',
    ImportShippingPrioritySingleBindOrder: '导入海运优先单绑定订单',
    ImportPriorityShippingOrder: '导入海运优先单',
    TrackingBillOfLadingNumberImportDeclarationNumberExportDeclarationNumber: '单号/跟踪提单号/进口报关单号/出口报关单号',
    TrackingBillOfLadingNumber: '跟踪提单号',
    BatchModification: '批量修改',
    OriginalShipmentNumber: '原货件号',
    SortingCode: '分拣码',
    CardDeliveryNumber: '卡派单号',
    StowagePreview: '配载预览',
    ToWhichTheReceiptBelongs: '揽收单所属',
    RecoveryDocument: '恢复单据',
    VoidedDocument: '作废单据',
    GeneratorSublist: '生成子单',
    TransportationCompleted: '运输完成',
    NumberOriginatingStationNumberOfDestinationSeaWaybill: '单号/始发站/目的站/海运单号',
    AssociateTheParentNumber: '关联父单号',
    TotalCost_1: '成本合计：',
    OperationRecord: '操作记录',
    FieldName: '字段名',
    BeforeModification: '修改前',
    AfterModification: '修改后',
    Collate: '核对',
    AccountBalance_1: '记账余额',
    OriginalPaymentAmount_1: '原付款金额',
    BasicPrice: '基础价',
    StatementNumber: '对账单号',
    PaymentAmount_1: '付款金额',
    BalanceWrittenOff: '核销余额',
    Amount: '金额',
    ChargeUp: '记账',
    PositiveAndNegativeDifference: '正负差异',
    TheAmountIs0: '金额为0',
    CancelEntry: '取消记账',
    BookkeepingCompleted: '记账完成',
    VoucherNumberBusinessDocumentCustomerOrderNumberStatementNumber: '凭证号/业务单据/客户订单号/对账单号',
    WhetherToAccount: '是否记账',
    WhetherToIssuePaymentSlip: '是否开具付款单',
    AdjustOrNot: '是否调整',
    CheckWhether: '是否核对',
    DocumentInformation: '单据信息',
    PendingFinancialReview: '待财务审核',
    PendingManagerReview: '待经理审核',
    OriginalPaymentAmount_2: '原付款金额：',
    BillNumberVoucherNumberCustomerOrderNumberBusinessDocument: '账单编号/凭证号/客户订单号/业务单据',
    BillNumberCustomerBillNumberTrackingNumberBusinessBillNumberAdjustmentBillNumber: '账单号/客户单号/跟踪号/业务单据号/调整单号',
    SmallPackageProduct: '小包产品',
    TransportProduct: '运输产品',
    ReturnProduct: '退货产品',
    BillNumberStatementNumberBillOfLadingNumberContainerNumberTrackingNumber: '单号/对账单号/提单号/柜号/跟踪号',
    IncomeAdjustmentSheet: '收入调整单',
    ConfirmedVestingPeriod: '确认归属期',
    DateOfOccurrence: '业务发生日期',
    NonvestingPeriod: '有无归属期',
    Unconfirm: '取消确认',
    SetTheVestingPeriod: '设置归属期',
    RegenerateDetail: '重新生成明细',
    TheNameIsReferredToAsSwiftBranchCodeOfTheAccount: '名称/简称/账号/分行代码/Swift',
    WhetherToConfirmTheVestingPeriod: '是否确认归属期',
    VestingPeriod: '归属期',
    BalanceWrittenOffByPayment: '付款核销余额',
    AccountPeriodInterval: '账期区间',
    Unconfirmed: '未确认',
    None: '没有',
    ThereAre: '有',
    BalanceWrittenOff_1: '核销余额：',
    BillingDetailSummary: '账单明细汇总',
    InvoiceNumberPaymentDocumentCurrency: '发票号/付款单/币种',
    AdjustBillNumberStatementNumberBusinessDocumentCabinetNumberTrackingNumberBillOfLadingNumber: '调整单号/对账单号/业务单据/柜号/跟踪号/提单号',
    DownloadTheBillImportTemplate: '下载账单导入模板',
    NumberOfPositiveAndNegativeDifferences: '件数正负差异',
    AmountDifference: '金额差异',
    HaveProduct: '具有产品',
    UpdateCustomerProducts: '更新客户产品',
    ExpenseBillNumberCustomerBillNumberCabinetTrackingNumberThirdpartyServiceBillNumber: '费用/业务单据号/客户单号/柜号/跟踪号/第三方服务单号',
    IsItComplete: '是否齐全',
    CurrentAmount_1: '当前金额：',
    EnterTheNameForShortAccountBranchCodeSwift: '输入名称/简称/账号/分行代码/Swift',
    BankName: '银行名称',
    ExpenseType: '费用类型',
    AssociationId: '关联id',
    BillNumber_1: '账单号',
    BillWriteoffRecord: '账单核销记录',
    CancelWriteoff: '取消核销',
    BalanceOfInvoiceWrittenOff: '发票核销余额',
    CancelId: '核销id',
    BillingSummaryVerificationRecord: '账单汇总核销记录',
    WriteoffRecord: '核销记录',
    Drawee: '付款人',
    EnterTransactionNumberPayerPayerPayerAccountSummaryInvoiceBillingNumberQuery: '输入交易号/收付款人/收付款账号/摘要/发票账单号码查询',
    Receipts: '进账',
    Amount_1: '金额：',
    CheckWhetherTheBalanceIsNot0: '核销余额是否不为0',
    ImportAndExportBill: '导入出账单',
    TransactionNumberPayeeSummaryOfPayeeAccountNumber: '交易号/收款人/收款人账号/摘要',
    ImportBill: '导入进账单',
    ImportCostTemplate: '导入费用模板',
    AssociatedStatement: '关联账单',
    WriteOffTheBillingDetails: '核销账单明细',
    BusinessReceipt: '业务进账单',
    TotalSummaryAccountBalance: '总汇总记账余额：',
    TotalSummaryBookkeepingAmount_1: '总汇总记账金额：',
    SalesProfitStatement: '销售利润报表',
    FinancialSummary: '财务汇总',
    TotalPayable: '应付总额',
    TotalReceivable: '应收总额',
    Salesman: '业务员',
    ActualTime_1: '实际时间：',
    EstimatedTime_1: '预计时间：',
    TypeOfOwnership: '所属类型',
    AttributeType: '属性类型',
    LimitFieldChineseName: '限制字段中文名',
    FieldChineseName: '字段中文名',
    LimitField: '限制字段',
    AttributeRestrictionConfiguration: '属性限制配置',
    ProductCostItem: '产品费用项',
    PriceListVersion: '价格表版本',
    ProtocolSchemeName: '协议方案名称',
    PriceType: '价格类型',
    ProductPriceListVersionRemarks: '产品/价格表版本/备注',
    SynchronousWarehouseService: '同步易仓服务',
    PushManifest: '推送manifest',
    WhetherToPrintASheet: '是否打印面单',
    TailLogisticsProviderCode: '尾程物流商编码',
    ServiceType: '服务类型',
    ProductPartitionCode: '产品分区code',
    EnableOrNot: '是否开启',
    ChargingRules: '收费规则',
    CombinedChargeRatio: '组合收费比率',
    BelongExpenses: '归属费用',
    CombinedExpenses: '组合费用项',
    ProductPartitionRestriction: '产品分区限制',
    PostcodeRestriction: '邮编限制',
    EffectiveCountry: '生效国家',
    ContainAlpNum: '包含英文和数字',
    DefaultValue: '默认值',
    RequiredOrNot: '是否必填',
    WithoutNumbers: '不含数字',
    EnglishFree: '不含英文',
    NoChinese: '不含中文',
    SuffixRestriction: '后缀限制',
    AllowCharacter: '允许字符',
    MaskCharacter: '屏蔽字符',
    PrefixRestriction: '前缀限制',
    FieldLength: '字段长度',
    CheckType: '校验类型',
    Encoding: '编码',
    AttributeRestriction: '属性限制',
    ExtendedAttribute: '扩展属性',
    WhetherToManuallyConfirm: '是否手动确认',
    TrackName: '轨迹名称',
    TriggerCondition: '触发条件',
    Subproduct: '子产品',
    CombinationProduct: '组合产品',
    MainProductName: '主产品名称',
    ProductStatus: '产品状态',
    ProductDescription: '产品说明',
    ChargingUnitCarry: '计费单位进位',
    PolicyType: '策略类型',
    PushorderNumberType: '推单号类型',
    PricingMethod: '定价方式',
    SheetType_1: '面单类型',
    CostOrNot: '是否成本计价',
    RevenueOrNotPricing: '是否收入计价',
    VirtualProductOrNot: '是否虚拟产品',
    AssociatedProduct: '关联产品',
    ProductInformation: '产品信息',
    PriceVersion: '价格版本',
    ProtocolSolutionProduct: '协议方案产品',
    ProtocolSchemeCoding: '协议方案编码',
    DetailsOfServicePrices: '服务价格明细',
    MaskWord: '屏蔽词',
    SynchronousTransportMode: '同步运输方式',
    SeaTransport: '海运',
    AirFreight: '空运',
    Dabao: '大包',
    ProductSalesPricing: '产品销售定价',
    ProductInterceptRule: '产品拦截规则',
    ProtocolScheme: '协议方案',
    ServiceStatus: '服务状态',
    ImportCustomersInBulk: '批量导入客户',
    ImportPartnersInBulk: '批量导入合作伙伴',
    ProductTrajectoryRule: '产品轨迹规则',
    RateCoefficient: '费率系数',
    Rate: '费率',
    EndOfValue: '货值终点',
    ValueThreshold: '货值起点',
    InsuranceProduct: '保险产品',
    ImportAddress: '导入地址',
    ReceivingTime: '收货时间',
    ChineseAddressDetails: '中文地址详情',
    DeadlineDays: '存放期限天数',
    SupplierWarehouseCode: '供应商仓库编码',
    WhetherToSynchronizeWithSuppliers: '是否同步供应商',
    ShippingAddressType: '发货地址类型',
    AddressType: '地址类型',
    DomesticWarehouseAddress: '国内仓地址',
    OverseasReturnerAddress: '境外退件地址',
    ConsigneeOfBillOfLading: '提单收件人',
    OpenOrNot: '是否开放',
    NotifypartyForShort: 'Notify Party简称',
    NotifypartyCoding: 'Notify Party编码',
    PortType: '港口类型',
    PortName: '港口名称',
    PortCode: '港口编码',
    Harbor: '港口',
    SalesStaff: '销售人员',
    BatchDelete: '批量删除',
    BatchShutdown: '批量关闭',
    BatchEnable: '批量启用',
    BatchChangeShopManager: '批量修改销售负责人',
    YesWalletManagement: '是否钱包管理',
    AccountManager: '客户负责人',
    CompanyCode: '公司编码',
    ShortForConsignee: '提单收件人简称',
    ConsigneeCodeOfBillOfLading: '提单收件人编码',
    CompanyTaxNumber: '公司税号',
    CompanyAddress: '公司地址',
    SubordinateDepartment: '所属部门',
    InternalUserOrNot: '是否内部用户',
    UserManagement: '用户管理',
    SubsidiaryCompany: '所属分公司',
    IsSleep: '是否休眠',
    CustomerAcquireType: '获客类型',
    CustomerClassification: '客户分类',
    City: '城市',
    Province: '省份',
    StateProvince: '州省',
    FirstDeliveryTime: '首次发货时间',
    AccountOpeningTime: '开户时间',
    BillingName: '开票名称',
    BillingInformation: '开票信息',
    AddressClientRestriction: '地址客户限制',
    TransshipmentWarehouseForShort: '转运目的仓简称',
    PerformReconciliationManually: '手动执行对账',
    GenerationTime: '生成时间',
    CreateOutboundInstruct: '创建出库单',
    ClaimOrderNum: '索赔单号',
    RelatedNumber: '相关单号',
    ClaimOrderStatus: '索赔状态',
    WriteOffNum: '核销单号',
    WriteOffAttachment: '核销附件',
    InventoryInfo: '库存信息',
    IntransitInventoryVariance: '在途库存差异',
    AvailableInventoryVariance: '可用库存差异',
    ThirdPartyAvailableInventory: '第三方可用库存',
    SystemAvailableInventory: '系统可用库存',
    QuantityOfReservedChanges: '保留变更数量',
    ChangeQuantityInTransit: '在途变更数量',
    QuantityChangeInHand: '在手变更数量',
    QuantityChangeStartInHand: '在手变更前数量',
    QuantityChangeEndInHand: '在手变更后数量',
    QuantityBeforeChange: '变更前数量',
    QuantityAfterChanged: '变更后数量',
    InventoryReconciliation: '库存对账',
    WhetherToSynchronizeInventory: '是否同步库存',
    HoldingStock: '保留库存',
    availableQty: '可售数量',
    unsalableQty: '不可售接收数量',
    TradeName: '商品名称',
    Product_1: '商品',
    SpecifiedDeduction: '指定扣费',
    DeductionTime: '扣费时间',
    WhetherATrayHasBeenDialed: '是否已打托',
    QuantityOfPallets: '托盘数',
    ContainerType: '集装箱类型',
    ContainerNumber: '集装箱箱号',
    LogisticsTrackingNumber: '物流单号',
    WeightKg_1: '重量(kg)',
    HeightCm_1: '高(cm)',
    WidthCm_1: '宽(cm)',
    LengthInCm_1: '长(cm)',
    VolumeM_1: '体积(m³)',
    SynchronizationCancellation: '取消订单',
    GetDetails: '获取详情',
    PrintedBoxMark: '打印箱唛',
    SynchronizeTheWarehouseEntryTicket: '同步入库单',
    OrderNumberContactFlightNumberPortOfDeparturePortOfDestination: '订单号/联系人/航班号/启运港/目的港',
    MaterialFlow: '物流',
    PickComplete: '拣选完成',
    CodeNameEnglishProductNameChineseProductName: '编码/名称/英文品名/中文品名',
    SynchronousWarehouse: '同步仓库',
    CustomersAndResults: '客户与结果',
    WorkOrderProblem: '工单问题',
    FbaOrder: 'FBA订单',
    WarmReminder: '温馨提示',
    RevenueEntryTemplate: '收入录入模板',
    CostEntryTemplate: '成本录入模板',
    PushTime: '推送时间',
    TrajectoryChinese: '轨迹中文',
    TrajectoryCoding: '轨迹编码',
    PushFailure: '推送失败',
    PushComplete: '推送完成',
    ToBePushed: '待推送',
    DoNotPush: '不推送',
    PacketLocus: '小包轨迹',
    ProcessingConclusion: '处理结论',
    Offer: '报价',
    BatchReply: '批量回复',
    AbnormalOrderNumberOrderNumberShipmentNumberContainerNumber: '异常单号/订单号/货件号/柜号',
    AbnormalOrderNumberOrderNumberShipmentNumber: '异常单号/订单号/货件号',
    CompleteOrNot: '是否完结',
    AbnormalFeedback: '异常反馈',
    CustomerOrderAbnormal: '订单异常标签',
    NewCases: '新增CASES',
    WorkOrderNumberAndOrderNumber: '工单号/订单号/内容',
    TaskContent: '任务内容',
    ImportUpdate: '导入更新',
    DownloadUpdateTemplate: '下载更新模板',
    StatusModification: '状态修改',
    CompleteTheQuotation: '完成报价',
    ModifiedState: '修改状态',
    ValueaddedServiceOrderNumberOrderNumber: '增值服务单号/订单号',
    ValueaddedServiceList: '增值服务单',
    ProductCodeNameOfGoodsDeclaredInChineseNameOfGoodsDeclaredInEnglish: '商品编码/中文申报品名/英文申报品名',
    ActualStorageAge: '实际库龄',
    ShelfTime: '上架时间',
    QuantityOfStockInTheLibrary: '在库库存数量',
    LibraryAgeCalculationDate: '库龄计算日期',
    StorageAgeManagement: '库龄管理',
    ShelfLotNumber: '上架批次号',
    WarehouseEntryNumber: '入库单号',
    InventoryBatchManagement: '库存批次管理',
    WarehouseCustomer: '仓库/客户',
    UnitPriceOfWarehouseRent: '仓租单价',
    TheStorageAgeIntervalEnds: '库龄区间结束',
    EffectiveMonth: '生效月份',
    EffectiveTime: '生效时间',
    BeginningOfLibraryAgeInterval: '库龄区间起始',
    CopeWith: '应付',
    Receivable: '应收',
    ConfigurationOfWarehouseRentChargingRules: '仓租计费规则配置',
    WarehousingCharge: '仓租费用',
    TotalVolumeCbm: '总体积(CBM)',
    TotalNumberOfGoods: '商品总件数',
    WarehouseRentCalculationDate: '仓租计算日期',
    WarehouseRentalNumber: '仓租单号',
    WarehouseRentalOrderManagement: '仓租单管理',
    modifyConfirmationData: '修改确认数据',
    CustomerRequirement: '客户要求',
    CaptureSingleTaskDetails: '抓单任务明细',
    SynchronizeWarehouseTaskDetails: '抓单任务明细',
    QuantityReturned: '退货数量',
    SalePriceOfReturnedGoods: '退货商品销售价格',
    CurrencyOfSaleOfReturnedGoods: '退货商品销售币种',
    ReturnedGoods: '退货商品',
    ReturnItemDetails: '退货商品明细',
    ItemDetails: '商品明细',
    ReasonForReturn: '退货原因',
    ReturnOrder: '退货订单',
    BulkShipmentExtension: '批量延期船期',
    isTenCarry: '是否10KG进位',
    RequestDate: '请求时间',
    SynchronousAcknowledgementData: '同步确认数据',
    PublishedPrice: '公布价',
    ChargePrice: '计费价',
    ShopTypeStar: '店铺类型*',
    DeclaredNameenStar: '英文品名*',
    DeclaredNamecnStar: '中文品名*',
    Texture: '材质*',
    ItemUse: '用途*',
    BrandStar: '品牌*',
    CustomsCodeStar: '海关编码*',
    ParcelNum: '合箱箱号',
    OrderSn: 'O单',
    parentOrderSn: 'PO单',
    ParcelQtyStar: '箱数*',
    ParcelLengthStar: '单箱长(CM)*',
    ParcelWidthStar: '单箱宽(CM)*',
    ParcelHeightStar: '单箱高(CM)*',
    ParcelWeightStar: '单箱重量(KG)*',
    ItemQtyStar: '总个数*',
    DeclaredPriceStar: '单个单价*（USD）',
    ValueAdded: '报关方式*',
    CustomsDeclarationMethod: '报关方式',
    ChannelDefault: '渠道默认',
    SingleTax: '单证退税报关',
    IsElectrified: '是否带电',
    IsMagnetic: '是否带磁',
    orderProducts: '下单产品',
    ReturnOrderNumber: '退件订单号',
    ReturnReason: '退件原因',
    ReturnOrderType: '退件类型',
    ReturnOrderService: '退件服务',
    ReturnOrderStatus: '退件状态',
    ReturnOrderConfirmReceive: '确认收货',
    ReturnOrderArriveWarehouse: '确认到仓',
    ReturnOrderArriveApprove: '审核订单',
    ReturnOrderConfirmCancel: '取消订单',
    ReturnOrderSearchHolder: '退件单号',
    ReturnOrderSubmitTime: '提交时间',
    ReturnOrderArrivalTime: '到仓时间',
    ReturnOrderReceiveTime: '收货时间',
    ReturnOrderShipmentList: '退件入库单货件列表',
    RuleName: '规则名',
    basicOrderInformation: '订单基本信息',
    InterceptRuleName: '拦截规则名',
    IsMarked: '是否标记',
    IsIntercept: '是否拦截',
    labelChangeConfig: '面单更改配置',
    ClientType: '客户类型',
    HWT: 'HWT账号',
    private: '客户自有账号',
    CompletedTime: '完成时间',
    isPickup: '是否上门提货',
    doorToDoorPickUp: '上门提货',
    deliveryToWarehouse: '送货到仓',
    loadingAtFactory: '工厂装柜',
    transportType: '到仓运输类型',
    airFreight: '空运',
    seaFreight: '海运',
    express: '快递',
    truck: '卡车',
    rail: '火车',
    selfPickup: '在库自提',
    licensePlate: '卡车车牌',
    isTrailer: '是否自拖车',
    logisticsType: '物流类型',
    logisticsPlan: '物流计划',
    fullContainerOrder: '整柜拼单',
    refConsignmentNo: '拼柜单号',
    ocustomsService: '出口报关类型',
    customerSelfExport: '客户自行出口报关',
    generalTradeWithRebate: '一般贸易【出口退税】',
    nonGeneralTrade: '非一般贸易报关【出口不退税】',
    specialSupervisionZoneManual: '特别监管区手册报关',
    icustomsService: '进口报关类型',
    customerOwnTaxClearance: '客户自有税号清关',
    nonCustomerOwnTaxClearance: '非客户自有税号清关',
    isWood: '是否含木质',
    woodType: '木质类型',
    solidWood: '原木',
    hardwood: '实木',
    engineeredWood: '复合木',
    exporterBusinessunit: '经营单位',
    warehouseInboundtrackingNum: '到仓运输快递单号',
    currencyAccount: '币种账户',
    expiryTime: '失效时间',
    approveTemporaryQuota: '申请临时额度',
    approveFixedQuota: '申请固定额度',
    pendingApprove: '审核中',
    approvedRejected: '审核不通过',
    effectiveApprove: '生效',
    expiredApprove: '失效',
    creditType: '信用额度类型',
    TemporaryQuota: '临时额度',
    FixedQuota: '固定额度',
    approver: '审核人',
    approveTime: '审核时间',
    pleaseEnterValidNumber: '请输入有效数字',
    SKUPieces: 'SKU 件数',
    PreSKUPieces: '预报SKU件数',
    third_party_warehouse_code: '平台仓库编码',
    local_address_num: '本地仓库编码',
    local_address_name: '本地仓库名称',
    ThirdPartyWarehouseMapping: '平台仓库映射',
    allDataAlreadyEnabled: '选中的数据已全部为启用状态',
    allDataAlreadyDisabled: '选中的数据已全部为禁用状态',
    confirmEnableSelected: '确定要批量启用选中的数据吗？',
    confirmDisableSelected: '确定要批量禁用选中的数据吗？',
    is_new_sku: '是否新品',
    recipientInfo: '收件人信息(国家/州/城市)',
    postcodeLimit: '限制邮编',
    carrierBrands: '物流商品牌',
    orderTotal: '订单总金额在',
    goodsTotal: '商品总数量',
    isSingle: '是否单品',
    ruleName: '规则名称',
    orderProcess: '订单处理方式',
    product: '发货方式',
    submitDirectly: '直接提交订单',
    manualReview: '需人工审核订单',
    postcodeTip1: '说明：邮编支持模糊匹配，以英文符号 * 代替;如果邮编范围使用英文符号 ~',
    postcodeTip2: '如：78987；可以使用7*来匹配',
    postcodeTip3: '如：200~300；可以匹配200345，字母邮编不支持范围',
    minAmount: '最小金额',
    maxAmount: '最大金额',
    yes: '是',
    no: '否',
    range: '范围',
    specifiedRange: '指定范围',
    select: '选择',
    dataLoadFailed: '数据加载失败',
    unknownError: '未知错误',
    condition: '条件',
    selectCarrierBrands: '选择物流商品牌',
    orderAmountRange: '订单金额范围',
    limitPostcode: '限制邮编',
    recipientInfoDialog: '收件人信息',
    country: '国家',
    city: '城市',
    state: '省/州',
    addPostcode: '新增邮编',
    changeBefore: '更改前',
    changeAfter: '更改后',
    GoodsWeight: '商品重量',
    ChangedParcelCustomerOrder: '小包清关信息更改',
    TriggerTailLogistics: '触发尾程',
    TrustDocumentParser: '托书解析',
    Serial_Number: '序列号',
    OrderNmber: '托单号',
    CustomerName: '客户名称',
    LoadingPort: '装货港',
    ReceivingPlace: '收货地',
    Notifier: '通知人',
    CustomerContact: '客户联系人',
    CustomerPhone: '客户联系电话',
    CustomerFax: '客户联系传真',
    CustomerQq: '客户联系QQ',
    CustomerEmail: '客户联系邮件',
    PaymentTerms: '运费条款',
    ConsolidationRemarks: '配仓备注',
    LoadingRemarks: '装箱备注',
    DocumentRemarks: '单证备注',
    InvoiceRemarks: '结算备注',
    LetterAuthorizationUpload: '托书上传',
    shipping_marks: '唛头',
    packages: '件数',
    packagesunit: '件数单位',
    syncSuccessTime: '同步成功时间',
    handleParcelCustomerExtend: '修复数据（临时使用后续删除）',
    intercepting: '拦截中',
    DelayedScan: '延误扫描',
    LastMileDocumentExchange: '尾程换单',
    PackagePacking: '包裹装箱',
    WarehouseDispatch: '仓库发货',
    DomesticTransit: '国内中转',
    ExportCustomsInspection: '出口清关查验',
    ExportCustomsDeclaration: '出口报关',
    MainLineDeparture: '干线离开',
    MainLineArrival: '干线抵达',
    ImportCustomsInspection: '进口清关查验',
    CustomsRelease: '海关放行',
    OverseasInTransit: '海外在途',
    ArrivedatDestinationCountry: '到达目的国',
    FinalDelivery: '交付末端',
    FlightDelay: '航班延误'
  },
  common: {
    // header
    sureResetPassword: '确定重置密码？',
    TheResetPassword: '重置后的密码是:123456',
    ResetPassword: '重置密码',
    lengths: '范围如填1-12,等于如填5',
    censored: 'xxx,001,屏蔽词',
    select: '请选择',
    enter: '请输入',
    radioTrue: '是',
    radioFalse: '否',
    startTime: '开始时间',
    startMonth: '开始月份',
    startDate: '开始日期',
    endTime: '结束时间',
    endMonth: '结束月份',
    endDate: '结束日期',
    to: '至',
    amount: '金额',
    selectDate: '选择日期',
    selectDateTime: '选择时间',
    noBlank: '不能为空',
    sure: '确 定',
    cancel: '取消',
    search: '搜索',
    condition: '是否带入外部过滤条件：',
    oneRecord: '一行代表一记录',
    batchSearch: '多项查询',
    trackCode: '轨迹代码',
    pieces: '件数',
    date: '日期',
    export: '导出',
    exportAll: '导出全部',
    gettingData: '正在获取数据，请稍候...',
    apiNotFound: '未找到API地址，无法获取全部数据',
    getDataFailed: '获取数据失败',
    exportSuccess: '成功导出 {count} 条数据!',
    exportFailed: '导出失败',
    allData: '全部数据',
    create: '新增',
    reset: '重置',
    batchEdit: '一键解绑',
    chargeOff: '核销',
    bill: '账单',
    edit: '编辑',
    clickToUpload: '编辑',
    // 列表
    modify: '修改',
    download: '下载',
    setting: '设置',
    // 详情---------------------
    save: '保存',
    saveAndedit: '保存并编辑',
    delete: '删除',
    cancle: '取消',
    close: '关闭',
    total: '总合计',
    operating: '操作',
    clickupload: '点击上传',
    add: '添加',
    addressee: '收件人',
    addresser: '发件人',
    simple: '简易商品清单',
    complete: '完整商品清单',
    with: '带',
    required: '为必填项',
    iffill: '如填写了包裹信息请务必手动汇总货物信息',
    aggregate: '汇总货物信息',
    numbersizes: '尺寸数量',
    packagesummary: '包裹汇总',
    customerorder: '客户订单',
    goodsummary: '商品汇总',
    switchType: '切换类型会清除已有的商品包裹',
    detailFail: '获取详情失败,请重试~',
    detailFailFreash: '获取详情失败,请刷新页面后重试~',
    norightloading: '页面信息未正确加载，请刷新后重新操作！',
    modifysuccess: '修改成功',
    modifyfail: '修改失败',
    addsuccess: '新增成功',
    addfail: '新增失败',
    delecomfirm: '确定删除?',
    updateLableComfirm: '确定更新订单?',
    tips: '温馨提示',
    deletesuccess: '删除成功',
    deletefail: '删除失败',
    casenumber: '箱号',
    weight: '重量',
    length: '长',
    width: '宽',
    height: '高',
    uploadedsuccess: '上传成功',
    uploadedfail: '上传失败',
    importsusses: '导入成功！',
    attention: '注意',
    // 收发件人
    address_num: '地址编码',
    contact_name: '联系人',
    company_name: '公司名',
    contact_email: '邮箱',
    contact_phone: '电话',
    country_code: '国家编码',
    state_code: '省份(州)编码',
    city_code: '城市编码',
    postcode: '邮编',
    house_no: '门牌号',
    address_one: '地址行1',
    address_two: '地址行2',
    async: '是否同步',
    fold: '展开',
    unfold: '合并',
    deleteSuccess: '删除成功'
  }
}
export default zh
