const en = {
  el: {
    colorpicker: {
      confirm: 'OK',
      clear: 'Clear'
    },
    datepicker: {
      now: 'Now',
      today: 'Today',
      cancel: 'Cancel',
      clear: 'Clear',
      confirm: 'OK',
      selectDate: 'Select date',
      selectTime: 'Select time',
      startDate: 'Start Date',
      startTime: 'Start Time',
      endDate: 'End Date',
      endTime: 'End Time',
      prevYear: 'Previous Year',
      nextYear: 'Next Year',
      prevMonth: 'Previous Month',
      nextMonth: 'Next Month',
      year: '',
      month1: 'January',
      month2: 'February',
      month3: 'March',
      month4: 'April',
      month5: 'May',
      month6: 'June',
      month7: 'July',
      month8: 'August',
      month9: 'September',
      month10: 'October',
      month11: 'November',
      month12: 'December',
      week: 'week',
      weeks: {
        sun: 'Sun',
        mon: 'Mon',
        tue: 'Tue',
        wed: 'Wed',
        thu: 'Thu',
        fri: 'Fri',
        sat: 'Sat'
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'May',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aug',
        sep: 'Sep',
        oct: 'Oct',
        nov: 'Nov',
        dec: 'Dec'
      }
    },
    select: {
      loading: 'Loading',
      noMatch: 'No matching data',
      noData: 'No data',
      placeholder: 'Select'
    },
    cascader: {
      noMatch: 'No matching data',
      loading: 'Loading',
      placeholder: 'Select',
      noData: 'No data'
    },
    pagination: {
      goto: 'Go to',
      pagesize: '/page',
      total: 'Total {total}',
      pageClassifier: ''
    },
    messagebox: {
      title: 'Message',
      confirm: 'OK',
      cancel: 'Cancel',
      error: 'Illegal input'
    },
    upload: {
      deleteTip: 'press delete to remove',
      delete: 'Delete',
      preview: 'Preview',
      continue: 'Continue'
    },
    table: {
      emptyText: 'No Data',
      confirmFilter: 'Confirm',
      resetFilter: 'Reset',
      clearFilter: 'All',
      sumText: 'Sum'
    },
    tree: {
      emptyText: 'No Data'
    },
    transfer: {
      noMatch: 'No matching data',
      noData: 'No data',
      titles: ['List 1', 'List 2'], // to be translated
      filterPlaceholder: 'Enter keyword', // to be translated
      noCheckedFormat: '{total} items', // to be translated
      hasCheckedFormat: '{checked}/{total} checked' // to be translated
    },
    image: {
      error: 'FAILED'
    },
    pageHeader: {
      title: 'Back' // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Yes',
      cancelButtonText: 'No'
    }
  },
  menu: {
    expired: 'Expired',
    taskStatus: 'Task Status',
    OrderCreateDate: 'Order Create Date',
    cancelOrderLabelTaskList: 'Label Cancel Task List',
    uploadList: 'upload list',
    home: 'Home',
    msg: 'this is an about page',
    personalCenter: 'Personal Center',
    customer: 'Customer',
    parcelCustomerOrder: 'SmallPackageOrder',
    parcelCustomerOrderList: 'SmallPackageOrderList',
    parcelCustomerOrderDetail: 'SmallPackageOrderDetail',
    parcelCustomerOrderAdd: 'SmallPackageOrderAdd',
    FBAOrder: 'FBA Order',
    systemManagement: 'System Management',
    userManagement: 'User Management',
    menuManagement: 'Menu Management',
    permissionManagement: 'Permission Management',
    organizationalStructure: 'Organizational Structure',
    basicData: 'Basic Data',
    cooperativePartner: 'Cooperative Partner',
    productManagement: 'Product Management',
    threeLetterCode: 'Three Letter Code',
    exchangeRateManagement: 'Exchange Rate Management',
    businessExpenseCode: 'Business Expense Code',
    businessPartnerManagement: 'Business Partner Management',
    airline: 'Airline',
    address: 'Address',
    receivingAddress: 'Receiving Address',
    CustomerPreOrder: 'Pre Order',
    CustomerOutboundOrder: 'Outbound Order',
    product: 'Product',
    productList: 'Product List',
    productAdd: 'Product Add',
    productDetail: 'Product Detail',
    settlement: 'Settlement',
    billSummary: 'Bill Summary',
    bill: 'Bill',
    receiptDetails: 'Receipt Details',
    paymentOrder: 'Payment Order',
    paymentDetails: 'Payment Details',
    billSummaryDetails: 'Bill Summary Details',
    billSummaryList: 'Bill Summary List',
    billList: 'Bill List',
    billDetail: 'Bill Detail',
    receiptDetailsList: 'Receipt Details List',
    receiptDetailsDetail: 'Receipt Details Detail',
    paymentOrderList: 'Payment Order List',
    paymentOrderDetail: 'Payment Order Detail',
    paymentDetailsList: 'Payment Details List',
    transport: 'Transport',
    mainOrder: 'Main Order',
    subOrder: 'Sub Order',
    mainOrderList: 'Main Order List',
    mainOrderAdd: 'Main Order Add',
    mainOrderDetail: 'Main Order Detail',
    subOrderList: 'Sub Order List',
    subOrderAdd: 'Sub Order Add',
    subOrderDetail: 'Sub Order Detail',
    financial: 'Financial',
    managementExpense: 'Management Expense',
    bankFlowList: 'Bank Flow List',
    bankFlowAdd: 'Bank Flow Add',
    bankFlowDetail: 'Bank Flow Detail',
    incomeAdjustment: 'Income Adjustment',
    expenditureAdjustment: 'Expenditure Adjustment',
    businessReceipt: 'Business Receipt',
    businessPayment: 'Business Payment',
    incomeAdjustmentAdd: 'Income Adjustment Add',
    incomeAdjustmentDetail: 'Income Adjustment Detail',
    incomeAdjustmentList: 'Income Adjustment List',
    expenditureAdjustmentAdd: 'Expenditure Adjustment Add',
    expenditureAdjustmentDetail: 'Expenditure Adjustment Detail',
    expenditureAdjustmentList: 'Expenditure Adjustment List',
    businessReceiptAdd: 'Business Receipt Add',
    businessReceiptDetail: 'Business Receipt Detail',
    businessReceiptList: 'Business Receipt List',
    businessPaymentAdd: 'Business Payment Add',
    businessPaymentDetail: 'Business Payment Detail',
    businessPaymentList: 'Business Payment List',
    importerExporter: 'Importer/Exporter',
    cardDispatch: 'Card Dispatch',
    cardDispatchList: 'Card Dispatch List',
    cardDispatchAdd: 'Card Dispatch Add',
    cardDispatchDetail: 'Card Dispatch Detail',
    bank: 'Bank',
    exportCustomsOrder: 'Export Customs Order',
    exportCustomsOrderList: 'Export Customs Order List',
    exportCustomsOrderAdd: 'Export Customs Order Add',
    exportCustomsOrderDetail: 'Export Customs Order Detail',
    jobOrder: 'Job Order',
    jobOrderList: 'Job Order List',
    jobOrderDetail: 'Job Order Detail',
    oceanTransportBill: 'Ocean Transport Bill',
    oceanTransportBillList: 'Ocean Transport Bill List',
    oceanTransportBillAdd: 'Ocean Transport Bill Add',
    oceanTransportBillDetail: 'Ocean Transport Bill Detail',
    receivableSummary: 'Receivable Summary',
    payableSummary: 'Payable Summary',
    smallOrder: 'Small Order',
    smallOrderList: 'Small Order List',
    smallOrderAdd: 'Small Order Add',
    smallOrderDetail: 'Small Order Detail',
    transportOrder: 'Transport Order',
    transportOrderList: 'Transport Order List',
    transportOrderAdd: 'Transport Order Add',
    transportOrderDetail: 'Transport Order Detail',
    reconciliationOrder: 'Reconciliation Order',
    reconciliationOrderDetail: 'Reconciliation Order Detail',
    reconciliationOrderList: 'Reconciliation Order List',
    reconciliationOrderAdd: 'Reconciliation Order Add',
    order: 'Order',
    smallOrderManagement: 'Small Order Management',
    transportOrderManagement: 'Transport Order Management',
    importCustomsOrder: 'Import Customs Order',
    importCustomsOrderList: 'Import Customs Order List',
    importCustomsOrderAdd: 'Import Customs Order Add',
    importCustomsOrderDetail: 'Import Customs Order Detail',
    systemIntegration: 'System Integration',
    supplierInformationTable: 'Supplier Information Table',
    supplierServiceConnection: 'Supplier Service Connection',
    supplierAccountInformation: 'Supplier Account Information',
    buttonManagement: 'Button Management',
    receivableDailySummary: 'Receivable Daily Summary',
    payableDailySummary: 'Payable Daily Summary',
    roleManagement: 'Role Management',
    report: 'Report',
    profitAnalysis: 'Profit Analysis',
    managementExpenseCode: 'Management Expense Code',
    jobOrderAdd: 'Job Order Add',
    productPartitionPostalCode: 'Product Partition Postal Code',
    incomePriceVersion: 'Income Price Version',
    incomePriceDetail: 'Income Price Detail',
    costPriceVersion: 'Cost Price Version',
    costPriceDetail: 'Cost Price Detail',
    accountManagement: 'Account Management',
    account: 'Account',
    rechargeOrder: 'Recharge Order',
    accountDetail: 'Account Detail',
    paymentDetailsDetail: 'Payment Details Detail',
    bigPackageOrder: 'Big Package Order',
    bigPackageOrderList: 'Big Package Order List',
    bigPackageOrderAdd: 'Big Package Order Add',
    bigPackageOrderDetail: 'Big Package Order Detail',
    accountingPeriod: 'Accounting Period',
    smallPackagePick: 'Small Package Pick',
    smallPackagePickRecord: 'Small Package Pick Record',
    bigPackageWeight: 'Big Package Weight',
    overseasWarehouseOrder: 'Overseas Warehouse Order',
    GoodsManagement: 'Goods Management',
    skuList: 'Sku List',
    skuAdd: 'Sku Add',
    skuDetail: 'Sku Detail',
    SalesSKU: 'Sales SKU',
    sku: 'SKU',
    TEMU: 'TEMU',
    eBay: 'eBay',
    Winit: 'Winit',
    Wish: 'Wish',
    irobotbox: 'irobotbox',
    Amazon: 'Amazon',
    MappingType: 'Mapping Type',
    commodityPlatformID: 'Commodity Platform ID',
    verificationcodeskucodeplantformaccount: 'Verification Code sku Code Plantform Account',
    customID: 'Custom ID',
    warehouse: 'Warehouse',
    warehouseList: 'Warehouse List',
    warehouseAdd: 'Warehouse Add',
    warehouseDetail: 'Warehouse Detail',
    inboundOrder: 'Inbound Order',
    inboundOrderList: 'Inbound Order List',
    inboundOrderAdd: 'Inbound Order Add',
    inboundOrderDetail: 'Inbound Order Detail',
    outboundOrder: 'Outbound Order',
    outboundOrderList: 'Outbound Order List',
    outboundOrderAdd: 'Outbound Order Add',
    outboundOrderDetail: 'Outbound Order Detail',
    warehouseStock: 'Warehouse Stock',
    stockTransactionRecord: 'Stock Transaction Record',
    goods: 'Goods',
    goodsList: 'Goods List',
    goodsAdd: 'Goods Add',
    goodsDetail: 'Goods Detail',
    warehouseManagement: 'Warehouse Management',
    stock: 'Stock',
    stockTransaction: 'Stock Transaction',
    workOrderType: 'Work Order Type',
    customerService: 'Customer Service',
    claimOrder: 'Claim Order',
    claimOrderAdd: 'Claim Order Add',
    claimOrderList: 'Claim Order List',
    claimOrderDetail: 'Claim Order Detail',
    claim: 'Claim',
    trackCode: 'Track Code',
    track: 'Track',
    bankTransaction: 'Bank Transaction',
    customsDeclaration: 'Customs Declaration',
    customsDeclarationList: 'Customs Declaration List',
    thirdPlatformWHMapList: 'Third Platform Mapping List',
    thirdPlatformWHMapAdd: 'Add Third Platform Warehouse Mapping',
    thirdPlatformWHMapDetail: 'Third Platform Warehouse Mapping Detail',
    thirdPlatformWHMap: 'Third Platform Warehouse Mapping',
    customsDeclarationAdd: 'Customs Declaration Add',
    customsDeclarationDetail: 'Customs Declaration Detail',
    cancelOrder: 'Cancel Order',
    cancelOrderList: 'Cancel Order List',
    MerchandiseMapping: 'Merchandise Mapping',
    mappingList: 'Mapping List',
    mappingAdd: 'Mapping Add',
    mappingDetail: 'Mapping Detail',
    WMS: 'WMS',
    wmsProduct: 'WMS Product',
    wmsProductList: 'WMS Product List',
    wmsProductAdd: 'WMS Product Add',
    wmsProductDetail: 'WMS Product Detail',
    wmsWarehouse: 'WMS Warehouse',
    wmsWarehouseList: 'WMS Warehouse List',
    wmsWarehouseAdd: 'WMS Warehouse Add',
    wmsWarehouseDetail: 'WMS Warehouse Detail',
    wmsInbound: 'WMS Inbound',
    wmsInboundList: 'WMS Inbound List',
    wmsInboundAdd: 'WMS Inbound Add',
    wmsInboundDetail: 'WMS Inbound Detail',
    wmsOutbound: 'WMS Outbound',
    wmsOutboundList: 'WMS Outbound List',
    wmsOutboundAdd: 'WMS Outbound Add',
    wmsOutboundDetail: 'WMS Outbound Detail',
    wmsWarehouseStock: 'WMS Warehouse Stock',
    wmsStockTransactionRecord: 'WMS Stock Transaction Record',
    unload: 'Unload',
    receive: 'Receive',
    receiveOrderList: 'Receive Order List',
    receiveOrderDetail: 'Receive Order Detail',
    shelf: 'Shelf',
    shelfJobOrder: 'Shelf Job Order',
    shelfOrderDetail: 'Shelf Order Detail',
    pickOrder: 'Pick Order',
    pickJobOrder: 'Pick Job Order',
    pickOrderDetail: 'Pick Order Detail',
    pack: 'Pack',
    packJobOrder: 'Pack Job Order',
    packOrderDetail: 'Pack Order Detail',
    delivery: 'Delivery',
    deliveryJobOrder: 'Delivery Job Order',
    deliveryOrderDetail: 'Delivery Order Detail',
    unloadOrderList: 'Unload Order List',
    unloadOrderDetail: 'Unload Order Detail',
    unloadDetail: 'Unload Detail',
    receiveDetail: 'Receive Detail',
    location: 'Location',
    partition: 'Partition',
    partitionType: 'Partition Type',
    shelfDetail: 'Shelf Detail',
    pickOutboundOrderManagement: 'Pick Outbound Order Management',
    pickQueue: 'Pick Queue',
    areaManagement: 'Area Management',
    service: 'Service',
    pickQueueDetail: 'Pick Queue Detail',
    pallet: 'Pallet',
    workOrderProblem: 'Work Order Problem',
    transportList: 'Transport List',
    afterSaleService: 'After Sale Service',
    claimAdd: 'Claim Add',
    claimList: 'Claim List',
    claimDetail: 'Claim Detail',
    inquiry: 'Inquiry',
    inquiryList: 'Inquiry List',
    inquiryAdd: 'Inquiry Add',
    inquiryDetail: 'Inquiry Detail',
    inquiryOrder: 'Inquiry Order',
    inquiryOrderList: 'Inquiry Order List',
    inquiryOrderAdd: 'Inquiry Order Add',
    inquiryOrderDetail: 'Inquiry Order Detail',
    accountAmount: 'Account Amount',
    accountInfo: 'Account Info',
    orderLabelTask: 'Order Label Task',
    orderLabelTaskList: 'Order Label Task List',
    orderLabelTaskDetail: 'Order Label Task Detail',
    outboundOrderLabelTask: 'Outbound Order Label Task',
    outboundOrderLabelTaskList: 'Outbound Order Label Task List',
    outboundOrderLabelTaskDetail: 'Outbound Order Label Task Detail',
    accountInfoDetail: 'Account Info Detail',
    customsOrder: 'Customs Order',
    customsOrderList: 'Customs Order List',
    customsOrderAdd: 'Customs Order Add',
    customsOrderDetail: 'Customs Order Detail',
    customsManagement: 'Customs Management',
    customsTruckOrder: 'Customs Truck Order',
    customsTruckOrderList: 'Customs Truck Order List',
    customsTruckOrderAdd: 'Customs Truck Order Add',
    customsTruckOrderDetail: 'Customs Truck Order Detail',
    billTo: 'Bill To',
    transferDestinationWarehouse: 'Transfer Destination Warehouse',
    notifyParty: 'Notify Party',
    countryCode: 'Country Code',
    smallPackageInquiry: 'Small Package Inquiry',
    smallPackageWeighing: 'Small Package Weighing',
    smallPackagePacking: 'Small Package Packing',
    smallPackageRelabeling: 'Small Package Relabeling',
    batchLargePackageOperation: 'Batch Large Package Operation',
    batchSmallPackageOperation: 'Batch Small Package Operation',
    orderTrack: 'Order Track',
    orderTrackQuery: 'Order Track Query',
    agreementScheme: 'Agreement Scheme',
    agreementSchemeList: 'Agreement Scheme List',
    agreementSchemeAdd: 'Agreement Scheme Add',
    agreementSchemeDetail: 'Agreement Scheme Detail',
    batchModifyTrackingNumber: 'Batch Modify Tracking Number',
    trackInsert: 'Track Insert',
    fbaOrderManagement: 'FBA Order Management',
    fbaOrderList: 'FBA Order List',
    largePackageSignIn: 'Large Package Sign In',
    fbaOrderAdd: 'FBA Order Add',
    fbaOrderDetail: 'FBA Order Detail',
    fbaWarehouse: 'FBA Warehouse',
    largePackageUpdate: 'Large Package Update',
    fbaOrder: 'FBA Order',
    sortingLineConfiguration: 'Sorting Line Configuration',
    fbaOrderOperation: 'FBA Order Operation',
    fbaOperationList: 'FBA Operation List',
    fbaOperationDetail: 'FBA Operation Detail',
    fbaOperationAdd: 'FBA Operation Add',
    transferWarehouse: 'Transfer Warehouse',
    transferWarehouseList: 'Transfer Warehouse List',
    transferWarehouseAdd: 'Transfer Warehouse Add',
    transferWarehouseDetail: 'Transfer Warehouse Detail',
    collectionOrder: 'Collection Order',
    collectionOrderList: 'Collection Order List',
    collectionOrderAdd: 'Collection Order Add',
    collectionOrderDetail: 'Collection Order Detail',
    fbaPackageManagement: 'FBA Package Management',
    oceanTransport: 'Ocean Transport',
    oceanTransportList: 'Ocean Transport List',
    oceanTransportAdd: 'Ocean Transport Add',
    oceanTransportDetail: 'Ocean Transport Detail',
    batchSignIn: 'Batch Sign In',
    abnormalOrderManagement: 'Abnormal Order Management',
    abnormalOrderAdd: 'Abnormal Order Add',
    abnormalOrderList: 'Abnormal Order List',
    abnormalOrderDetail: 'Abnormal Order Detail',
    fbaOrderSimple: 'FBA Order (Simple)',
    businessPartnerList: 'Business Partner List',
    businessPartnerAdd: 'Business Partner Add',
    businessPartnerDetail: 'Business Partner Detail',
    customerManagement: 'Customer Management',
    customerList: 'Customer List',
    customerAdd: 'Customer Add',
    customerDetail: 'Customer Detail',
    bigPackageRelabeling: 'Big Package Relabeling',
    smallPackageTrackManagement: 'Small Package Track Management',
    transferDestinationWarehouseList: 'Transfer Destination Warehouse List',
    transferDestinationWarehouseAdd: 'Transfer Destination Warehouse Add',
    transferDestinationWarehouseDetail: 'Transfer Destination Warehouse Detail',
    printThirdPartyBoxLabel: 'Print Third Party Box Label',
    productPartitionManagement: 'Product Partition Management',
    productPartitionList: 'Product Partition List',
    productPartitionAdd: 'Product Partition Add',
    productPartitionDetail: 'Product Partition Detail',
    agreementSchemeSimple: 'Agreement Scheme Simple',
    agreementSchemeSimpleList: 'Agreement Scheme Simple List',
    agreementSchemeSimpleAdd: 'Agreement Scheme Simple Add',
    agreementSchemeSimpleDetail: 'Agreement Scheme Simple Detail',
    fbaPackageTrack: 'FBA Package Track',
    preOrder: 'Pre Order',
    preOrderList: 'Pre Order List',
    preOrderAdd: 'Pre Order Add',
    preOrderDetail: 'Pre Order Detail',
    allocationOrder: 'Allocation Order',
    allocationOrderList: 'Allocation Order List',
    allocationOrderAdd: 'Allocation Order Add',
    allocationOrderDetail: 'Allocation Order Detail',
    productServiceManagement: 'Product Service Management',
    productSalesPricing: 'Product Sales Pricing',
    productSalesPricingList: 'Product Sales Pricing List',
    productSalesPricingAdd: 'Product Sales Pricing Add',
    productSalesPricingDetail: 'Product Sales Pricing Detail',
    fbaPickRecord: 'FBA Pick Record',
    supplierService: 'Supplier Service',
    supplierServiceList: 'Supplier Service List',
    supplierServiceAdd: 'Supplier Service Add',
    supplierServiceDetail: 'Supplier Service Detail',
    supplierJointBI: 'Supplier对接BI',
    packageList: 'Package List',
    packageAdd: 'Package Add',
    packageDetail: 'Package Detail',
    logisticsCodeInformation: 'Logistics Code Information',
    valueAddedServiceManagement: 'Value Added Service Management',
    valueAddedServiceAdd: 'Value Added Service Add',
    valueAddedServiceList: 'Value Added Service List',
    valueAddedServiceDetail: 'Value Added Service Detail',
    smallPackageTrackTask: 'Small Package Track Task',
    ExportDeclarationOrder: 'Export Declaration Order',
    ExportDeclarationOrderList: 'Export Declaration Order List',
    ExportDeclarationOrderDetail: 'Export Declaration Order Detail',
    ExportDeclarationOrderAdd: 'Export Declaration Order Add',
    smallPackageTrackTaskList: 'Small Package Track Task List',
    smallPackageTrackTaskAdd: 'Small Package Track Task Add',
    smallPackageTrackTaskDetail: 'Small Package Track Task Detail',
    wmsBasic: 'WMS Basic',
    locationBindingRelationship: 'Location Binding Relationship',
    inventoryReconciliationRecord: 'Inventory Reconciliation Record',
    costTemplateConfiguration: 'Cost Template Configuration',
    costTemplateAdd: 'Cost Template Add',
    costTemplateList: 'Cost Template List',
    costTemplateDetail: 'Cost Template Detail',
    costQuickEntry: 'Cost Quick Entry',
    dictionary: 'Dictionary',
    attributeRestrictionConfiguration: 'Attribute Restriction Configuration',
    warehouseRentRuleConfiguration: 'Warehouse Rent Rule Configuration',
    cardQuery: 'Card Query',
    cardQueryAdd: 'Card Query Add',
    cardQueryList: 'Card Query List',
    cardQueryDetail: 'Card Query Detail',
    fbmOrderManagement: 'FBM Order Management',
    fbmOrderManagementList: 'FBM Order Management List',
    fbmOrderManagementAdd: 'FBM Order Management Add',
    fbmOrderManagementDetail: 'FBM Order Management Detail',
    expressDeliveryOrder: 'Express Delivery Order',
    expressDeliveryOrderList: 'Express Delivery Order List',
    expressDeliveryOrderAdd: 'Express Delivery Order Add',
    expressDeliveryOrderDetail: 'Express Delivery Order Detail',
    warehouseRentManagement: 'Warehouse Rent Management',
    pushEMSLargePackage: 'Push EMS Large Package',
    inventoryBatchManagement: 'Inventory Batch Management',
    inventoryAgeManagement: 'Inventory Age Management',
    trackSupplier: 'Track Supplier',
    trackConversionInformation: 'Track Conversion Information',
    trackBlacklist: 'Track Blacklist',
    insuranceOrder: 'Insurance Order',
    insuranceOrderList: 'Insurance Order List',
    insuranceOrderAdd: 'Insurance Order Add',
    insuranceOrderDetail: 'Insurance Order Detail',
    insuranceProductManagement: 'Insurance Product Management',
    insuranceProduct: 'Insurance Product',
    insuranceProductList: 'Insurance Product List',
    insuranceProductAdd: 'Insurance Product Add',
    insuranceProductDetail: 'Insurance Product Detail',
    insuranceIncomePriceVersion: 'Insurance Income Price Version',
    insuranceCostPriceVersion: 'Insurance Cost Price Version',
    insuranceIncomePriceDetail: 'Insurance Income Price Detail',
    insuranceCostPriceDetail: 'Insurance Cost Price Detail',
    valueAddedService: 'Value Added Service',
    cargoManagement: 'Cargo Management',
    cargoManagementList: 'Cargo Management List',
    cargoManagementDetail: 'Cargo Management Detail',
    cargoManagementAdd: 'Cargo Management Add',
    fbmSpecialLineOrder: 'FBM Special Line Order',
    fbmSpecialLineOrderAdd: 'FBM Special Line Order Add',
    fbmSpecialLineOrderDetail: 'FBM Special Line Order Detail',
    fbmSpecialLineOrderList: 'FBM Special Line Order List',
    OutboundOrderNumber: 'Outbound Order Number',
    fbmSpecialLineOrderEdit: 'FBM Special Line Order Edit',
    outboundOrderManagement: 'Outbound Order Management',
    outboundOrderManagementAdd: 'Outbound Order Management Add',
    outboundOrderManagementDetail: 'Outbound Order Management Detail',
    outboundOrderManagementList: 'Outbound Order Management List',
    cargoInventory: 'Cargo Inventory',
    cargoInventoryAdd: 'Cargo Inventory Add',
    cargoInventoryDetail: 'Cargo Inventory Detail',
    cargoInventoryList: 'Cargo Inventory List',
    batchReturnSmallPackageOrder: 'Batch Return Small Package Order',
    pricePrediction: 'Price Prediction',
    customerExportCustomsDeclaration: 'Customer Export Customs Declaration',
    customerExportCustomsDeclarationDetail: 'Customer Export Customs Declaration Detail',
    customerExportCustomsDeclarationList: 'Customer Export Customs Declaration List',
    warehouseInventoryManagement: 'Warehouse Inventory Management',
    logisticsPlan: 'Logistics Plan',
    logisticsPlanAdd: 'Logistics Plan Add',
    logisticsPlanDetail: 'Logistics Plan Detail',
    logisticsPlanList: 'Logistics Plan List',
    withdrawalSlip: 'Withdrawal slip',
    storage: 'storage',
    inventoryAdjustment: 'Inventory adjustment',
    inventoryAdjustmentOrder: '库存调整单',
    inventoryAdjustmentOrderList: 'Inventory adjustment list',
    inventoryAdjustmentOrderDetail: 'Inventory adjustment detail',
    inventoryAdjustmentOrderAdd: 'Inventory adjustment add',
    removedFromStorage: 'Removed from storage',
    moveFromStorageToShelves: 'Move from storage to shelves',
    wmsInventory: 'WMS Inventory',
    supplier: 'Supplier',
    productAgreementScheme: 'Product agreement scheme',
    returnOrder: 'Return order',
    returnOrderADD: 'Return order add',
    returnOrderList: 'Return order list',
    returnOrderDetail: 'Return order detail',
    ReturnOrder: 'Return Order',
    ReturnOrderList: 'Return Order List',
    ReturnOrderAdd: 'Return Order Add',
    ReturnOrderDetail: 'Return Order Detail',
    CustomerReturnOrder: 'Return Order',
    CustomerReturnOrderList: 'Return Order List',
    CustomerReturnOrderAdd: 'Return Order Add',
    CustomerReturnOrderDetail: 'Return Order Detail',
    InventoryReconciliation: 'Inventory reconciliation',
    ProductInterceptRule: 'Product Intercept Rule',
    productInterceptRuleList: 'Product Intercept Rule List',
    productInterceptRuleAdd: 'Product Intercept Rule Add',
    ProductInterceptRuleDetail: 'Product Intercept Rule Detail',
    inboundTransfer: 'Inbound Transfer',
    inboundTransferAdd: 'Inbound Transfer Add',
    inboundTransferList: 'Inbound Transfer List',
    inboundTransferDetail: 'Inbound Transfer Detail',
    outboundTransfer: 'Outbound Transfer',
    outboundTransferAdd: 'Outbound Transfer Add',
    outboundTransferList: 'Outbound Transfer List',
    outboundTransferDetail: 'Outbound Transfer Detail',
    creditLine: 'Credit Line Management',
    creditLineAdd: 'Add Credit Line',
    OCShippingRules: 'OC Shipping Rules',
    selectCondition: 'Select Condition',
    setCondition: 'Set Condition',
    list: 'List',
    add: 'Add',
    edit: 'Edit',
    detail: 'Detail',
    changedParcelCustomerOrder: 'Change ParcelCustomerOrder',
    changedSmallOrder: 'Change ParcelCustomerOrder',
    changedParcelCustomerOrderList: 'Change ParcelCustomerOrder List',
    TrustDocumentParser: 'Trust Document Parser',
    TrustDocumentList: 'Trust Document List',
    TrustDocumentParserAdd: 'Trust Document Add',
    TrustDocumentDetail: 'Trust Document Detail'
  },
  search: {
    jobOrderAndOperator: 'Job Order/Operator',
    baleDetailSearch: 'Order No/Operator',
    checkSearch: 'Inventory work No/Operator',
    clearanceSearch: 'System code/Main No/Customer No/CMR No',
    cancelOrder: 'CustomerOrderNo/TrackingNo',
    claimOrdersSearch: 'OrderNo/TrackingMainNo/TrackingNo/CargoName',
    customerOrderSearch: 'OrderNo/ChineseName/CargoName/FlightNo/DestinationPort',
    customerInboundSearch: 'OrderNo/Contact/FlightNo/ShipmentPort/Destination',
    customerSkuSearch: 'Coding/Name/CargoName/ChineseName',
    customerWarehouseSearch: 'Warehouse code/Warehouse/Contact/Email',
    accountDetailsSearch: 'TransactionNo/PayeeOrPayer/Receipt&PaymentAccount/Abstract/InvoiceNo',
    bankSearch: 'Name/Short Name/Account/BranchCode/Swift',
    billingSearch: 'TransactionNo/Payee/PayeeAccount/Abstract',
    remarkSearch: 'Remark',
    omsWarehouseSearch: 'WarehouseCode/WarehouseName/Email',
    pickDetailList: 'sku/Location/Operator',
    pickListSearch: 'PickingQueueNo/pickingOrderNum',
    outboundOrderNoSearch: 'OutboundOrderNo',
    receiptDetailSearch: 'ReceivingOperationNo/WarehousingOrderNo/operator',
    trackSearch: 'OrderNo',
    caseSearch: 'WorkOrderNo/OrderNo/Content',
    accountPayableSearch: 'VoucherNo/BusinessOrder/CustomerOrderNo',
    accountReceivableSearch: 'BillNo/VoucherNo/CustomerOrderNo/BusinessOrders',
    debitSearch: 'BillNo/CustomerOrderNo/TrackingNo/BusinessOrderNo/SkuNo',
    debitSummarySearch: 'Name/ShortName/Account/BranchCode/Swift',
    payMentSearch: 'InvoiceNo/PaymentOrder/Currency',
    reonconSearch: 'Cost/ThirdPartyServiceBillNo/CustomerOrderNo/TrackingNo/BusinessOrders',
    clearanceOrderSearch: 'OrderNo/OceanOrderNo/CabinetNo',
    partitionSearch: 'No/Name',
    LocationNoAndChannelSearch: 'LocationNo/channel',
    freezeOrderSearch: 'FrozenOrderNo/sku/po_no',
    creditLineSearch: 'Customer Name/Remark',
    thirdPartyWHMapSearch: 'Local Warehouse Code/Platform Warehouse Code',
    OCShippingRulesSearch: 'Rule Name'
  },
  content: {
    pleaseSelectExportConditions: 'Please select export conditions',
    MulProductPriceVersionTaskComfirm: 'Are you sure to batch creation of price versions and details?',
    versionType: 'Version Type',
    MulProductPriceVersionTask: 'Mul Product Price Version Task',
    productAndPriceDetail: 'Product and Price Details',
    priceVersionLines: 'Price Details',
    unknown: 'Unknown',
    cancelStatus: 'Cancel Status',
    orderLabelTask: 'order Label Task',
    outboundOrderLabelTask: 'Outbound Order Label Task',
    ExportFliterPacelCustomerOrder: 'Export orders according to filter',
    taskRestart: 'Task Restart',
    ExportCondition: 'Export Condition',
    parcelCustomerOrderBathchExportTask: 'Batch Export Task',
    adjust_type: 'Adjustment Type',
    income_adjust_type_default: 'Generate adjustment order based on revenue',
    income_adjust_type_service: 'X% commission based on revenue',
    UpdateOrderStatus: 'update order status',
    CancelSuccess: 'Cancelled Successfully',
    CancelFailed: 'Cancelled Failed',
    ScanformTask: 'Scanform Task',
    importsusses: 'Import Success',
    taskCreate: 'Task Create',
    ScanformRefNumber: 'Scanform Batch No',
    ScanformDate: 'Scanform Date',
    ScanformNo: 'Scanform No',
    IsCreatescanform: 'Is Create scanform task',
    SignOrNot: 'Sign or not',
    BeingCancel: 'Being Cancel',
    cancelFeePercentage: 'Percentage for cancellation fees',
    paperHeight: 'Paper Height',
    paperWidth: 'Paper Width',
    barcodeWidth: 'Barcode Width',
    barcodeHeight: 'Barcode Height',
    ShipDate: 'Ship Date',
    BatchDelete: 'Batch Delete',
    selectProductDeletedTips: 'Please select the product data to be deleted first',
    chooseCopyProductTips: 'Please choose to copy the product first',
    productInfoNotEmpty: 'Product information cannot be empty',
    QuantityAndPrice: 'Item Qty*/UnitPrice',
    Usage2Type: 'Usage(CN/EN)',
    Material2Type: 'Material(CN/EN)',
    brandAndModel: 'Brand/Model',
    declaredName: 'Declared Name(CN/EN)',
    singBoxWeight: 'Single box weight',
    specificationOfSingleCase: 'Specification of single case',
    cargoInfo: 'Cargo Info',
    enterCountryOrAreaCode: 'Please enter country or area code',
    countryOrAreaCode: 'Country or area code:',
    selectDestination: 'Please select destination',
    selectShippingAreaTips: 'Please select shipping area first',
    selfDelivery: 'Self-delivery',
    fullAddress: 'Full address',
    searchAddress: 'Search Address',
    clickToSelectShippingAddress: 'Please click to select shipping address',
    shippingAddress: 'shipping address',
    parseSuccessTips: 'Parsing successful, please check and save the draft',
    saveDraftTips: 'Please save the draft before exporting the box list',
    noParcelEstimatedVolumeTips: 'No order parcel estimated volume can not be submitted',
    noParcelEstimatedWeightTips: 'No order parcel estimated weight can not be submitted',
    noParcelEstimatedPiecesTips: 'No Order Parcel Estimated Pieces Cannot Be Submitted',
    buyerInformaotionRequirTips: 'Private address, consignee, cell phone number, country or region, city, post code, address required',
    shippingAddressImformationMustFilledIn: 'The shipping address information must be filled in',
    shippingAddressMustFilledIn: 'The shipping address must be filled in',
    editSuccess: 'Edit Success',
    orderByCustomsClearance: 'Buy orders for customs clearance',
    refundsAndDeclarations: 'Tax refunds and customs declarations',
    partialRefund: 'partial refund',
    addRowOfBoxes: 'Add a single row of boxes',
    totalValue: 'Total value',
    cubicMeter: 'cubic meter',
    exportingCaseListData: 'Exporting case list data',
    placeOfShipmentAndDestination: 'Place of shipment and destination',
    customsDeclarationInformation: 'Customs declaration information.',
    enterOrderNotes: 'Please enter order notes',
    selectEstimatedDeliveryTime: 'Select estimated delivery time',
    estimatedDeliveryTime: 'Estimated delivery time',
    selectExpectedArrivalDate: 'Select the expected date of arrival',
    expectedArrivalDate: 'Expected date of arrival',
    chooseBuyInsuranceOrNot: 'Please choose whether to buy insurance or not',
    enterCustomerNo: 'Please enter the customer order number',
    importBoxListData: 'Importing Box List Data',
    importOrderData: 'Importing Order Data',
    importOverwriteTip: 'Import will overwrite the original data, if you have printed the box mark please keep the box list data consistent',
    downloadTemplates: 'Download Templates',
    importFormatOnlyXlsOrXlsx: 'Only xls, xlsx format files are allowed to be imported.',
    dragFileOr: 'Drag the file here, or',
    quantityNotEmpty: 'Quantity cannot be empty',
    shipmentNumberNotEmpty: 'The shipment number cannot be empty',
    quickImportOrder: 'export_rebate_order',
    export_rebate_order: 'Export rebate order',
    oneWarehouseCodeOrPostCode: 'One warehouse code or post code is required',
    addParcel: 'Add Parcel',
    modificationProductCharge: 'Modification of product charges',
    enterParcelNumber: 'Please enter the number of Parcel',
    enterShipmentNumber: 'Please enter the shipment number',
    quickImport: 'Quick Import',
    saveDraft: 'Save Draft',
    enterWarehouseCode: 'Please enter the warehouse code',
    selectProduct: 'Please select a product',
    pleaseSelectConsignee: 'Please select the consignee of the bill of lading',
    pleaseSelectOperation: 'Please select the object of the operation',
    docs: 'documents',
    format: 'format',
    notExceeding: 'not exceeding',
    pleaseUpload: 'Please upload',
    clickToUpload: 'Click to upload file',
    confirmDeleteWarehouseItems: 'Is it confirmed to delete the data items of the warehouse selection?',
    confirmDeleteItemSelectedByCustomer: 'Is it confirmed to delete the data item selected by the customer?',
    confirmDeleteSelectedItem: 'Are you confirming the deletion of the selected data items?',
    confirmDeleteProductItem: 'Is it confirmed to delete the data item selected for the product charge item?',
    confirmDeleteParcelItem: 'Are you confirming the deletion of the parcel-selected data items?',
    pleaseEnterTheRecipientPostCode: "Please enter the recipient's postal code",
    pleaseEnterTheRecipientAddresseeCountry: "Please enter the recipient's country",
    pleaseEnterTherShipperPostCode: "Please enter the shipper's postal code",
    pleaseEnterTheShipperCountry: "Please enter the shipper's country",
    pleaseEnterProductCode: 'Please enter the product code',
    preOrder: 'Pre-Recording Declaration',
    title: 'Freight Butler',
    remember: 'RememberAccount',
    language: 'Language',
    login: 'LogIn',
    loging: 'Loging',
    account: 'Account',
    password: 'Password',
    zh: 'Chinese',
    en: 'English',
    personalCenter: 'PersonalCenter',
    fullScreen: 'FullScreen',
    logOff: 'Log Off',
    uploadAvatar: 'UploadAvatar',
    joinDate: 'JoinDate',
    accountStatus: 'AccountStatus',
    normal: 'Normal',
    passwordTips: 'A password with high security can make your account more secure. It is recommended to set a password that contains letters, numbers and symbols at the same time.',
    mailboxAuthentication: 'MailboxAuthentication',
    yourMailbox: 'YourMailbox',
    boundMailboxes: 'Bound mailboxes can be used to',
    securityManagement: 'Security management, password reset and modification',
    accountUse: 'Account use, use email to log in to the system',
    edit: 'Edit',
    oldPass: 'OldPassword',
    newPass: 'NewPassword',
    confirmPass: 'ConfirmPassword',
    cancel: 'Cancel',
    confirm: 'Confirm',
    delete: 'Delete',
    submit: 'Submit',
    pwdInconsistent: 'The passwords entered twice are inconsistent',
    editPwd: 'EditPassword',
    enterOldPwd: 'Please enter the old password',
    enterNewPwd: 'Please enter the new password',
    pwdLength: 'The length is between 6 and 20 characters',
    loginAgain: 'Password changed successfully! Please log in again!',
    avatarEditSuccess: 'Head portrait modified successfully',
    newEmail: 'NewEmail',
    verificationCode: 'Verification code',
    errPwd: 'Wrong password. Please re-enter it',
    emptyEmail: 'The new mailbox cannot be empty',
    sameEmail: 'The new mailbox cannot be the same as the old one',
    errorEmail: 'Email format error',
    modifyEmail: 'ModifyEmail',
    getVCode: 'Get Verification code',
    emptyVCode: 'The Verification code cannot be empty',
    sendInVCode: 'The Verification code in transmission',
    vCodeSendSuccess: 'Sent successfully, verification code valid for 5 minutes',
    reSend: 'To resend',
    editEmailSuccess: 'Email has been modified successfully',
    smallOrders: 'SmallOrders',
    transportOrder: 'TransportOrders',
    importCustomsDeclaration: 'Import customs declaration',
    customer: 'Customer',
    currency: 'Currency',
    NoSync: 'No Sync',
    WaitSync: 'Wait Sync',
    FinishSync: 'Finish Sync',
    availableBalance: 'AvailableBalance',
    RebateBalance: 'Available rebate balance',
    AvailableCreditLimit: 'Available Credit Limit',
    totalBalance: 'Total Balance',
    creditLimit: 'Credit Limit',
    creditOrNot: 'Credit or not',
    rebateAmount: 'Rebate Amount',
    rebateAmountOrNot: 'Rebate Amount or not',
    realAmount: 'Real Amount',
    amountType: '金额类型',
    remark: 'Remark',
    deletedSuccessfully: 'Deleted Successfully',
    success: 'Success',
    operation: 'Operation',
    sureDeleteThisData: 'Are you sure to delete this data?',
    accountDetails: 'AccountDetails',
    transactionType: 'TransactionType',
    recharge: 'Recharge',
    deductions: 'Deductions',
    refund: 'Refund',
    amount: 'TransactionAmount',
    transactionNo: 'TransactionNo',
    transactionUniqueNo: 'TransactionUniqueNo',
    transactionChargeCode: 'TransactionChargeCode',
    transactionChargeName: 'TransactionChargeName',
    exchangeRate: 'ExchangeRate',
    accountCurrency: 'AccountCurrency',
    accountChargeAmount: 'AccountChargeAmount',
    accountBalance: 'AccountBalance',
    rechargeOrder: 'RechargeOrder',
    serialNumber: 'SerialNo',
    rechargeStatus: 'RechargeStatus',
    draft: 'Draft',
    submitted: 'Submitted',
    pass: 'Pass',
    reject: 'Reject',
    processing: 'Processing',
    invalid: 'Invalid',
    thirdTransactionNo: 'Third-party Serial No',
    voucherLink: 'VoucherLink',
    selectItem: 'Please select the item to execute！',
    continue: 'Determine to perform this operation, do you want to continue?',
    tips: 'Tips',
    adjustsOrder: 'AdjustsOrder',
    warehouses: 'Warehouse',
    calculateAgeDate: 'Calculate Age Date',
    positionNum: 'Location Number',
    warehousesOrderNumber: 'warehouses order number',
    warehouseType: 'Warehouse Type',
    normalWarehouse: 'Normal Warehouse',
    autoWarehouse: 'Auto Warehouse',
    isAutoWarehouse: 'Is Auto Warehouse',
    intercept_order: 'Intercept Order',
    status: 'Status',
    isAudit: 'IsAudit',
    createTime: 'CreateTime',
    finishTime: 'FinishTime',
    inventoryAdjustmentOrder: 'Inventory Adjustment Order',
    inventoryWarehouse: 'InventoryWarehouse',
    adjustQuantity: 'AdjustQuantity',
    updateTime: 'UpdateTime',
    basicInformation: 'Basic Information',
    shipmentDigest: 'Shipment Digest',
    isMasterProduct: 'Is Master Product',
    isNeedConfirm: 'Is Need Confirm',
    adjustType: 'AdjustType',
    accordingInventory: 'AccordingInventory',
    DestroyInventory: 'Destroy',
    sinceTheLift: 'Since the Lift',
    directlyTreasury: 'Directly into the Treasury',
    interceptWarehousing: 'Intercept warehousing',
    AbnormalShipment: 'Abnormal shipment',
    inventoryLoss: 'Inventory Loss',
    inventoryProfit: 'Inventory Profit',
    inventoryNumber: 'InventoryNo',
    adjustGoods: 'Adjust the Goods',
    productName: 'ProductName',
    bookInventory: 'BookInventory',
    inventoryCount: 'InventoryCount',
    inventoryDifferences: 'Inventory Differences',
    targetLocation: 'Target Location',
    import: 'Import',
    downloadTemplate: 'Download Template',
    pleaseSelect: 'Please Select',
    Required: 'Please enter',
    detailsLandingDocuments: 'Details of Landing Documents',
    operatingTime: 'OperatingTime',
    operator: 'Operator',
    baleOrderNum: 'BalingOrderNo',
    outboundOrderNum: 'OutboundOrderNo',
    skuNo: 'SKUNO',
    skuBarcode: 'SkuBarcode',
    piece: 'Quantity',
    pickingOrderNum: 'PickingOrderNo',
    fileGeneration: 'The export file is being generated in the background, please check it later',
    makeInvoice: 'Make invoice Success',
    waitingJob: 'Waiting Job',
    ongoing: 'Ongoing',
    completed: 'Completed',
    palletNum: 'Pallet No',
    expectedPickNum: 'ExpectedPickNo',
    actualPickNum: 'ActualPickNo',
    totalNum: 'TotalNumber',
    packageJobDetails: 'BalingJobDetails',
    balingSteps: 'Baling single job operation steps',
    steps: 'Steps',
    params: 'Params',
    message: 'Message',
    accountingInterval: 'Accounting Interval',
    startMonth: 'Start Month',
    endMonth: 'End Month',
    isLock: 'Is Locked',
    workOrderType: 'WorkOrderType',
    exceptionInfor: 'ExceptionInformation',
    proposal: 'Proposal',
    dictionaryManagement: 'DictionaryManagement',
    keyName: 'KeyName',
    value: 'Value',
    businessExpenseCodeManagement: 'Business expense code management',
    costCode: 'CostCode',
    costName: 'CostName',
    exchangeRateManagement: 'ExchangeRateManagement',
    currentCurrency: 'CurrentCurrency',
    targetCurrency: 'TargetCurrency',
    conversionRate: 'ConversionRate',
    exchangeRateDate: 'ExchangeRateDate',
    salesManageCharges: 'SalesManageCharges',
    costType: 'CostType',
    businessCosts: 'BusinessCosts',
    salesManagementCost: 'SalesManagementCosts',
    type: 'Type',
    airPortCode: 'AirPortCode',
    countryCode: 'CountryCode',
    cityCode: 'CityCode',
    airportEnglishName: 'AirportEnglishName',
    airportChineseName: 'AirportChineseName',
    trackCode: 'TrackCode',
    trackInfor: 'TrackInfor',
    checkDetails: 'CheckDetails',
    forInventory: 'ForInventory',
    inventoryComplete: 'InventoryCompleted',
    inventorySequence: 'InventoryOrder',
    InventoryPlan: 'InventoryPlan',
    InventoryRange: 'InventoryRange',
    AccordingGoods: 'AccordingGoods',
    AccordingLocation: 'AccordingLocation',
    AccordingLocationAndGoods: 'AccordingLocationAndGoods',
    Location: 'Location',
    AccordingBookInventory: 'AccordingBookInventory',
    NotAccordingBookInventory: 'NotAccordingBookInventory',
    InventoryGoodsDetail: 'Inventory of goods details',
    InventoryOperationDetails: 'InventoryOperationDetails',
    ScanLocation: 'ScanLocation',
    WalkingTime: 'WalkingTime',
    InventoryTime: 'InventoryTime',
    InventoryOperationSteps: 'InventoryOperationSteps',
    ForCustomsClearance: 'For Customs Clearance',
    HaveCustomsClearance: 'Have Customs Clearance',
    ImportOrder: 'Import Order',
    SystemCode: 'SystemCode',
    DateOfArrival: 'Date Of Arrival',
    MasterNo: 'MasterNo',
    CustomerNo: 'CustomerNo',
    Weight: 'Weight',
    PickBoxes: 'Pick Boxes',
    ForecastBoxes: 'Forecast Boxes',
    TotalParcelNumber: 'TotalParcelNumber',
    ProperNumber: 'ProperNumber',
    ClearanceDate: 'ClearanceDate',
    // DeliveryDate: 'DeliveryDate',
    Destination: 'Destination',
    ETA: 'ETA',
    Address: 'Address',
    Recipient: 'Recipient',
    Sender: 'Sender',
    AddressNo: 'AddressCode',
    AddressName: 'AddressName',
    Contact: 'Contact',
    CompanyName: 'Company',
    CompanyShortName: 'CompanyShortName',
    Email: 'Email',
    PhoneNo: 'PhoneNo',
    CountryCode: 'CountryCode',
    ProvinceStateCode: 'Province(State)Code',
    CityCode: 'CityCode',
    PostCode: 'PostCode',
    HouseNo: 'HouseNo',
    AddressLineOne: 'AddressLine1',
    AddressLineTwo: 'AddressLine2',
    vat_no: 'VAT NO',
    eori_no: 'eori NO',
    AirLines: 'AirLines',
    Code: 'Code',
    Prefix: 'Prefix',
    Url: 'Url',
    Companies: 'Companies',
    IsSupplier: 'IsSupplier',
    IsCustomer: 'IsCustomer',
    SalesDirector: 'SalesDirector',
    DebitEmail: 'DebitEmail',
    BusinessEmail: 'BusinessEmail',
    FullName: 'FullName',
    Tax: 'Tax',
    OpeningBank: 'OpeningBank',
    DebitDate: 'DebitDate',
    Nonautomatic: 'Nonautomatic',
    WeeksBalance: 'WeeksBalance',
    MonthlyBalance: 'MonthlyBalance',
    Attachment: 'Attachment',
    ImportExporter: 'Import/Exporter',
    CustomsCode: 'CustomsCode',
    LegalPerson: 'LegalPerson',
    IsExporters: 'IsExporters',
    IsImporter: 'IsImporter',
    CancelOrder: 'CancelOrder',
    SubmitTime: 'SubmitTime',
    Total: 'Total ',
    DealWith: 'Deal With',
    customerOrderNo: 'Customer Order No',
    TrackingNo: 'TrackingNo',
    DocumentType: 'OrderType',
    Transport: 'Transport',
    Packet: 'Packet',
    OrderGenerationDate: 'Order Generation Date',
    HandleRes: 'Handle Result',
    ClaimOrders: 'Claim Orders',
    OrderNo: 'OrderNo',
    TrackMasterOrderNo: 'TrackMainNo',
    CaseNo: 'CaseNo',
    ShipmentValue: 'ShipmentValue',
    IndemnityToCustomer: 'IndemnityToCustomer',
    Freight: 'Freight',
    CargoName: 'Cargo Name',
    ProblemDescription: 'Problem Description',
    CompensationResult: 'Compensation Result',
    CustomerOrder: 'Customer Order',
    OrderNumProductNameParcelNum: 'Order Number Product Name Parcel Number',
    ActualLeaveDate: 'Actual Leave Date',
    ExpectedLeaveDate: 'ExpectedLeaveDate',
    ArrivalDate: 'ArrivalDate',
    SurfaceSingleManagement: 'Surface Single Management',
    CreateSurfaceSingle: 'Create Surface Single',
    DownloadSurfaceSingle: 'Download Surface Single',
    CancelLabel: 'Cancel Label',
    UploadLabel: 'Upload Lable',
    DownloadTransferNo: 'Download Transfer No',
    InvalidOrders: 'Invalid Orders',
    InvalidOrdersReason: 'Invalid Reason',
    Route: 'Route',
    Departure: 'Departure',
    parseOceanOrder: 'Parse Ocean Order',
    Flight: 'Flight',
    FlightNo: 'FlightNo',
    ItemDetails: 'Goods Details',
    GrossWeight: 'Gross Weight',
    NetWeight: 'Net Weight',
    line_num: 'Line Number',
    sku_name: 'Product Name',
    material: 'Material',
    specification: 'Specification',
    sku_code: 'SKU Code',
    uom: 'Unit of Measure',
    unit_price: 'Unit Price',
    origin_country: 'Origin Country',
    destination_country: 'Destination Country',
    ShipmentShipped: 'Shipped',
    ShipmentInTransit: 'In Transit',
    ShipmentPicked: 'Picked',
    LatestOperationTime: 'Latest Operation Time',
    SpecialRequirements: 'Special Requirements',
    WaitingForNotice: 'waiting for notice',
    QuicklyBook: 'Quickly Book',
    RelatedNumber: 'Related Number',
    OutboundBoxQty: 'Outbound Box Qty',
    HoldingBoxQty: 'Holding Box Qty',
    InventoryBoxQty: 'Inventory Box Qty',
    OutboundOrderNums: 'Outbound Order Numbers',
    OverseasOrderNumber: 'Overseas Order Number',
    ActualOverseasArrivalTime: 'Actual Overseas Arrival Time',
    BookUntilTheEndOfTheMonth: 'Book Until the End of the Month',
    Goods: 'Goods',
    ShipperName: 'Shipper Name',
    ClearanceInformation: 'Clearance Information',
    Number: 'Number',
    ParcelNumber: 'Parcel Number',
    Volume: 'Volume',
    TotalSingle: 'Total Single',
    AirMainBillNo: 'AirMainBillNo',
    OceanOrderNo: 'OceanOrderNo',
    OnlyChooseOne: 'Only Choose One',
    Main: 'Main',
    Branch: 'Branch',
    SingleOperation: 'Single Operation',
    TransportInformation: 'Transport Information',
    ExpectArrivalDate: 'ExpectArrivalDate',
    ActualArrivalDate: 'ActualArrivalDate',
    HouseBill: 'SubNo',
    RecipientSender: 'Recipient/Sender',
    CargoInformation: 'Cargo Information',
    EstimatedWeight: 'EstimatedWeight',
    EstimatedVolume: 'EstimatedVolume',
    ConversionRate: 'ConversionRate',
    ChargeableWeight: 'ChargeableWeight',
    BubbleWeight: 'BubbleWeight',
    volumeWeight: 'VolumeWeight',
    TotalCost: 'TotalCost',
    UnitPrice: 'UnitPrice',
    Payer: 'Payer',
    IncomingOrders: 'IncomingOrders',
    InTransit: 'InTransit',
    ArrivedWarehouse: 'ArrivedWarehouse',
    Received: 'Received',
    HasBeenOn: 'Has Been On',
    IsSendDebit: 'IsSendDebit',
    IsRevenueLock: 'IsRevenueLock',
    IsCostLock: 'IsCostLock',
    IsArrivalNotice: 'IsArrivalNotice',
    Settlement: 'Settlement',
    InCome: 'InCome',
    Cost: 'Cost',
    GrossProfit: 'Gross Profit',
    DeductionTime: 'Deduction Time',
    QuoteCurrency: 'QuoteCurrency',
    DeclareCurrency: 'DeclareCurrency',
    PrintBarcode: 'PrintBarcode',
    SalesPrice: 'SalesPrice',
    DeclaredPrice: 'Declared Price',
    DeclaredChineseName: 'ZhName',
    DeclaredEnglishName: 'EnName',
    Length: 'Length',
    Width: 'Width',
    Height: 'Height',
    Material: 'Material',
    Size: 'Size',
    Use: 'Use',
    Brand: 'Brand',
    Model: 'Model',
    ForecastSize: 'Forecast Size',
    ChineseNameOfGoods: 'ChineseNameOfGoods',
    EnglishNameOfGoods: 'EnglishNameOfGoods',
    EstimatedNumber: 'EstimatedNumber',
    CustomerPointsBubble: 'CustomerPointsBubble(%)',
    IncomeDetails: 'Income Details',
    CostDetail: 'Cost Detail',
    Supplier: 'Supplier',
    Parcel: 'Parcel',
    PackageNo: 'PackageNo',
    PackageDescription: 'Package Description',
    Product: 'Product',
    ProductProperties: 'Product Properties',
    ProductCode: 'ProductCode',
    GoodsCode: 'GoodsCode',
    PackingListDetails: 'Packing List Details',
    OperationType: 'OperationType',
    Outbound: 'Outbound',
    Inbound: 'Inbound',
    IsSynchronizedInventory: 'IsSynchronizedInventory',
    InHandBefore: 'InHandBefore',
    InventoryInHand: 'InventoryInHand',
    InventoryInWay: 'InventoryInWay',
    InventoryInKeep: 'InventoryInKeep',
    OperatingNo: 'OperatingNo',
    CustomerOrderNum: 'CustomerOrderNum',
    OutboundOrder: 'OutboundOrder',
    DeliveryCompleted: 'DeliveryCompleted',
    CancelSurfaceSingle: 'Cancel Surface Single',
    Order: 'Order',
    PartitionValue: 'PartitionValue',
    PurchaseOrders: 'PurchaseOrders',
    ShopManager: 'Shop Manager',
    IsNuclearHeavy: 'IsNuclearHeavy',
    OrderTime: 'Order Time',
    BillingDate: 'Billing Date',
    PlatformNo: 'PlatformNo',
    PlatformFlag: 'PlatformFlag',
    syncSalesPlatform: 'Sync Sales Platform',
    BigParcel: 'Big Parcel',
    TheOrderNo: 'The Order No:',
    NotObtained: 'We have not obtained the transfer number, do you want to confirm the completion of shipment? To get the tracking number, refresh the page and try again!',
    WarehouseCode: 'Warehouse Code',
    Service: 'Service',
    PushOrder: 'Push Order',
    RecipientEmail: 'Recipient Email',
    RecipientPhone: 'Recipient Phone',
    RecipientCountry: 'Recipient Country',
    RecipientState: 'Recipient State',
    RecipientCity: 'Recipient City',
    RecipientArea: 'Recipient Area',
    CatchSingleTask: 'Catch Single Task',
    TurnSingleInformation: 'Turn Single Information',
    UnHandled: 'UnHandled',
    StatusSuccess: 'Success',
    HandledBy3rdNo: 'Handled By 3rdNo',
    HandledFailure: 'Handled Failure',
    ThirdPartyOrderNo: 'Third Party OrderNo',
    ModulusValue: 'Modulus Value',
    HandleTimes: 'Handle Times',
    CommodityRelated: 'Commodity Related',
    ParcelsAndMerchandise: 'Parcels And Merchandise',
    ParcelNo: 'ParcelNo*',
    WeightKG: 'Weight(KG)',
    LengthCM: 'Length(CM)',
    WidthCM: 'Width(CM)',
    HeightCM: 'Height(CM)',
    SingleWeight: 'SingleWeight(KG)',
    DeclareUnitPrice: 'Declare Unit Price',
    ParcelSummary: 'Parcel Summary',
    CommoditySummary: 'Commodity Summary',
    TrackInformation: 'Track Information',
    Time: 'Time',
    UKTime: 'UK Time',
    WarehouseInventory: 'Warehouse Inventory',
    DischargeDetails: 'Discharge Details',
    chargeDetails: 'Details of charge',
    chargeRate: 'charge rate',
    chargeCount: 'charge count',
    DischargeOrderNo: 'Discharge OrderNo',
    WarehousingOrderNo: 'Warehousing OrderNo',
    Discharge: 'Discharge',
    StartTime: 'Start Time',
    DischargeSteps: 'Discharge Steps',
    SourceOfThePicture: 'Source Of The Picture',
    Page: 'Page',
    NoPermission: 'You have no permission to go to this page',
    ContactLeader: 'If you have any dissatisfaction, please contact your leader',
    OrYouCanGo: 'Or you can go:',
    BackToHomePage: 'Back to the home page',
    LookAround: 'Look Around',
    ClickLookPicture: 'Click me to look at the picture',
    AllRightsReserved: 'All Rights Reserved',
    Wallstreetcn: 'Wallstreetcn',
    ErrorReport: 'Please check whether the URL you entered is correct, please click the button below to return to the home page or send error report',
    CantAccessThisPage: 'The webmaster said you cant access this page......',
    AccountStatement: 'Account Statement',
    AccountDate: 'Account Date',
    Spending: 'Spending',
    PayeeOrPayer: 'PayeeOrPayer',
    PayeeOrPayerAccount: 'PayeeOrPayerAccount',
    Abstract: 'Abstract',
    InvoiceOrBillNo: 'Invoice Or BillNo',
    Bank: 'Bank',
    ShortName: 'ShortName',
    BankAddress: 'BankAddress',
    BranchCode: 'BranchCode',
    WriteOffBalance: 'Write Off Balance',
    BusinessCompany: 'BusinessCompany',
    AssociatedPaymentOrder: 'Associated Payment Order',
    PaymentOrderNo: 'Payment OrderNo',
    BalanceOfPayment: 'Balance Of Payment',
    InvoiceNo: 'InvoiceNo',
    PaymentDate: 'Payment Date',
    LinkId: 'Link to Id',
    Payee: 'Payee',
    PayeeAccount: 'Payee Account',
    Remove: 'Remove',
    RemoveBill: 'Are you sure to remove the bill?',
    ToSubmit: 'ToSubmit',
    ToAudit: 'ToAudit',
    ManagerAudit: 'ManagerAudit',
    FinancialAudit: 'FinancialAudit',
    ToBePaid: 'ToBePaid',
    Paid: 'Paid',
    ThisBillWriteOffBalance: 'This Bill Write Off Balance',
    ProcessingDate: 'ProcessingDate',
    AssociatedBillsSummary: 'Associated Bills Summary',
    BillingSummaryNo: 'Billing Summary No',
    CollectionDays: 'Collection Days',
    PayerAccount: 'PayerAccount',
    VirtualBank: 'VirtualBank',
    RemoveBillSummary: 'Are you sure to remove this bill summary?',
    BankTransactionFlow: 'Bank Transaction Flow',
    OperationDate: 'Operation Date',
    NoData: 'No Data',
    SupplierInformation: 'Supplier Information',
    SupplierCode: 'SupplierCode',
    SupplierName: 'SupplierName',
    AttributionInstitution: 'Attribution',
    FileStorageDirectory: 'FileStorageDirectory',
    SupplierServiceDocking: 'SupplierServiceDocking',
    DockingCode: 'DockingCode',
    DockingName: 'DockingName',
    SupplierID: 'SupplierID',
    DockingInformationDescription: 'DockingDesc ',
    ClassName: 'ClassName',
    HermesServiceClass: 'HermesServiceClass',
    WinitServiceClass: 'WinitServiceClass',
    PNNServiceClass: 'PNNServiceClass',
    SideSingleFileExtension: 'Side Single File Extension',
    ForecastFileExtension: 'Forecast File Extension',
    VendorServiceCode: 'Vendor Service Code',
    CarrierCode: 'CarrierCode',
    IsSupportCancel: 'IsSupportCancel',
    SupplierDockingAccountInformation: 'Supplier Docking Account Information',
    AccountName: 'AccountName',
    AccountPassword: 'AccountPassword',
    AccountDescription: 'AccountDescription',
    SurfaceSingle: 'SurfaceSingle',
    Invoice: 'Invoice',
    Forecast: 'Forecast',
    Track: 'Track',
    AuthorizationID: 'AuthorizationID',
    AuthorizationCode: 'AuthorizationCode',
    Refresh: 'Refresh',
    Close: 'Close',
    CloseTheOther: 'CloseTheOther',
    CloseAll: 'CloseAll',
    LicensedSuccessfully: 'Licensed Successfully',
    CompleteDischarge: 'Complete Discharge',
    MandatoryShelves: 'Mandatory Shelves',
    FinishOrder: 'Finish Order',
    OrderUnlock: 'Order Unlock',
    CostFinish: 'Cost Finish',
    CostUnlock: 'Cost Unlock',
    PositionStatus: 'Position Status',
    WaitingForWarehousing: 'Waiting For Warehousing',
    ReceiptCompleted: 'Receipt Completed',
    ShelvingCompleted: 'Shelving Completed',
    WarehouseProducts: 'Warehouse Products',
    EstimatedNumberOfBoxes: 'Estimated Number Of Boxes',
    ActualNumberOfBoxes: 'Actual Number Of Boxes',
    CabinetNo: 'Cabinet No',
    TrackInTheLibrary: 'Track In The Library',
    Description: 'Description',
    TrackCode: 'Track Code',
    InventoryTransactionRecord: 'Inventory Transaction Record',
    Adjustment: 'Adjustment',
    IsPrinted: 'IsPrinted',
    Printed: 'Printed',
    NotPrinted: 'Not Printed',
    WaitingForPicking: 'Waiting For Picking',
    PickingUp: 'PickingUp',
    PickingCompleted: 'PickingCompleted',
    SortingCompleted: 'SortingCompleted',
    PackagingCompleted: 'PackagingCompleted',
    PickingFailed: 'PickingFailed',
    CatchSingleException: 'Catch Single Exception',
    PrintTimes: 'PrintTimes',
    SkuPictures: 'Sku Pictures',
    SystemSKUCode: 'System SKU Code',
    CustomerSKUCode: 'Customer SKU Code',
    Picture: 'Picture',
    EnterTransportationNo: 'Please enter the TransportationNo',
    CartonNo: 'CartonNo',
    PleaseEnterWeight: 'Please Enter Weight',
    Update: 'Update',
    SystemBoxLabel: 'System Box Label',
    ActionButtons: 'Action Buttons',
    Copies: 'Copies',
    DoNotPrint: 'Do Not Print',
    Printer: 'Printer',
    FailedToGetPrinter: 'Failed To Get Printer',
    PrintSuccess: 'Print Success',
    PrintFailed: 'Print Failed',
    MakeSureBoxNoWeight: 'Please make sure to fill in the box number and weight',
    PleaseSelectPrinter: 'Please Select Printer',
    IsLinkParcelOrder: 'IsLinkParcelOrder',
    IsLinkCustomerOrder: 'IsLinkCustomerOrder',
    BatchAdding: 'Batch Adding',
    Country: 'Country',
    ConfigureTransportationDocuments: 'Configure Transportation Documents',
    PrintLabels: 'Print Labels',
    SmallPieces: 'Small Pieces',
    TransportationOrderNo: 'TransportationOrderNo',
    ParcelOrderDetails: 'Parcel Order Details',
    CustomsDeclarationNo: 'CustomsDeclarationNo',
    TransportState: 'Transport State',
    ArrivedDestinationPort: 'Arrived Destination Port',
    WarehouseToComplete: 'Warehouse To Complete',
    HandoverDispatch: 'Handover Dispatch',
    OrderType: 'Order Type',
    IsCheck: 'IsCheck',
    IsISFMatch: 'IsISFMatch',
    IsCustomsDeclarationCompleted: 'IsCustomsDeclarationCompleted',
    ForecastSending: 'Forecast Sending',
    ReleaseTime: 'Release Time',
    ReleaseOrder: 'Release Order',
    DeliveryTime: 'Delivery Time',
    TrajectoryChange: 'Trajectory Change',
    CustomsOperation: 'Customs Operation',
    Check: 'Check',
    CancleCheck: 'Cancle Check',
    ISFMatch: 'ISF Match',
    CancleISFMatch: 'Cancle ISF Match',
    CustomsClearanceCompleted: 'Customs Clearance Completed',
    CancelClearanceComplete: 'Cancel Clearance Complete',
    TransportationOperation: 'Transportation Operation',
    Send: 'Send',
    SendISF: 'Send ISF',
    SendBills: 'Send Bills',
    SendForecast: 'Send Forecast',
    DeliveryInformation: 'Delivery Information',
    Date: 'Date',
    ReleaseDate: 'Release Date',
    DeliveryDate: 'Delivery Date',
    CustomsDeclarationType: 'CustomsDeclarationType',
    Importer: 'Importer',
    Exporter: 'Exporter',
    TaxBill: 'Tax Bill',
    ShipName: 'Ship Name',
    VoyageTime: 'Voyage Time',
    NoticeReleaseTime: 'Notice Release Time',
    WarehousingCDate: 'Warehousing Completion Date',
    TruckReservationTime: 'Truck Reservation Time',
    InternalCarton: 'Internal Carton',
    YouEmail: 'Email',
    DeliveryTheme: 'Delivery Theme',
    DeliveryAddress: 'Delivery Address',
    EmailBody: 'Email Body',
    MasterOceanFile: 'Master Or Ocean File',
    DeliveryList: 'Delivery List',
    InvoicesAndCases: 'Invoices And Cases',
    CustomsDeclarationList: 'Customs Declaration List',
    ReleaseBillOfLading: 'Release the bill of lading',
    NoteRecords: 'Note Records',
    Creator: 'Creator',
    ThirdPartyServiceBillNo: 'Third Party Service Bill No',
    TransferOrderNo: 'TransferOrderNo',
    ConfigurationDocument: 'Configuration Document',
    ConfigurationOfAirFreightMasterBill: 'Configuration Of Air Freight Master Bill',
    TransnationalTransportation: 'Transnational Transportation',
    MasterDocBelongsTo: 'MasterDocBelongsTo',
    FlightDate: 'Flight Date',
    ConfiguredOceanBillOfLading: 'Configured Ocean Bill Of Lading',
    BillOfLadingBelongs: 'BillOfLadingBelongs',
    ConfigurationOfSingle: 'Configuration Of Single',
    ConfigureTruckList: 'Configure Truck List',
    TruckOrderNo: 'Truck Order No',
    DepartureStation: 'Departure Station',
    ArrivalNotice: 'Arrival Notice',
    RecoverOrder: 'Recover Order',
    PrintNeutralSheet: 'Print Neutral Sheet',
    ProductPartition: 'Product Partition',
    Share: 'Share',
    ShareNo: 'ShareNo',
    BigBagDetails: 'Big Bag Details',
    ConfigureLargePackageOrder: 'Configure Large Package Order',
    PleaseEnterContent: 'Please Enter Content',
    ContainerNo: 'ContainerNo',
    ExpressLabel: 'Express Label',
    ThePackingNohasBeenScanned: 'The packing number has been scanned',
    PleaseWeightAndPackingNo: 'Please make sure you fill in the waybill number, weight and packing number',
    PickingRecord: 'Picking Record',
    User: 'User',
    ErrorMessage: 'Error Message',
    PickingOrderDetails: 'Picking Order Details',
    // EstimatedPickingQuantity: 'Estimated Picking Quantity',
    ActualPickQuantity: 'Actual Pick Quantity',
    PickingOrder: 'Picking Order',
    PickingOrderList: 'Picking Order List',
    PrintPickingOrder: 'Print Picking Order',
    FinishPackOrder: 'Finish Pack Order',
    PickingStrategy: 'Picking Strategy',
    BigGoodsOutbound: 'Big Goods Outbound',
    PickingArea: 'Picking Area',
    OutboundQuantity: 'Outbound Quantity',
    QueueMode: 'Queue Mode',
    QueueModeValue: 'Queue Mode Value',
    TotalOrders: 'Total Orders',
    QuantityCompleted: 'Quantity Completed',
    NumberOfFailures: 'Number Of Failures',
    MaximumQuantityOfOutboundOrders: 'Maximum Quantity Of OutboundOrders',
    PickingQueue: 'Picking Queue',
    PickingQueueNo: 'Picking Queue No',
    EstimatedPickingQuantity: 'Estimated Picking Quantity',
    ActualPickingQuantity: 'Actual Picking Quantity',
    PickingTimeConsuming: 'Picking Time Consuming',
    TotalTimeConsuming: 'Total Time Consuming',
    PleaseSelectOperator: 'Please Select Operator',
    AllocatingTask: 'Allocating Task',
    PickingConfrontation: 'Picking Confrontation',
    Pause: 'Pause',
    Recover: 'Recover',
    PickingOutboundOrder: 'Picking Outbound Order',
    OutboundOrderNo: 'Outbound Order No',
    IsPickingOrderGenerated: 'IsPickingOrderGenerated',
    IsSingleOrder: 'IsSingleOrder',
    GeneratePickingOrder: 'Generate PickingOrder',
    OneKeyGenerate: 'OneKey Generate',
    SKUAndMCode: 'SKU & SkuBarCode',
    Color: 'Color',
    OperationStepsOfPickingOperation: 'Operation steps of picking order',
    // IncomeAdjustment: 'Income Adjustment',
    ProductType: 'Product Type',
    CustomsClearance: 'Customs Clearance',
    OverseasWarehouse: 'Overseas Warehouse',
    OverseasWarehouseIN: 'Overseas Warehouse Inbound Order',
    OverseasWarehouseOUT: 'Overseas Warehouse Outbound Order',
    OverseasWarehouseReturn: 'Overseas Warehouse Return Order',
    OrderPleaseCheckLater: 'Order is being processed in the background, please check later',
    ImportProductInformation: 'Import Product Information',
    TrackCodeConfiguration: 'Track Code Configuration',
    Aging: 'Aging',
    Sort: 'Sort',
    ServiceName: 'Service Name',
    ServiceCode: 'Service Code',
    IsDefaultService: 'IsDefaultService',
    PartitionName: 'Partition Name',
    Buyer: 'Buyer',
    Seller: 'Seller',
    ZipCodeOfProductDivision: 'Zip code of product division',
    AccurateZipCode: 'Accurate Zip Code',
    IntervalSegment: 'Interval segment (divided by ~)',
    ProductRoute: 'Product Route',
    ProductStartPartition: 'Product Start Partition',
    ProductEndpointPartition: 'Product Endpoint Partition',
    ProductCustomerRestrictions: 'Product Customer Restrictions',
    EndTime: 'End Time',
    AttributionToCustomer: 'Attribution To Customer',
    ProductChargeItem: 'Product Charge Item',
    Name: 'Name',
    Mix: 'Mix',
    ChargeUnit: 'Charge Unit',
    BillingReconversionRate: 'Billing Reconversion Rate',
    ChargingInstructions: 'Charging Instructions',
    MaximumWeightLimitOfPackage: 'Maximum weight limit of package',
    MinimumWeightLimitOfWholeOrder: 'Minimum weight limit of whole order',
    ExpenseItem: 'Cost Item',
    RevenuePriceVersion: 'Revenue Price Version',
    ChargeItemName: 'Charge Item Name',
    VersionName: 'Version Name',
    CostPriceVersion: 'Cost Price Version',
    ProductDiscount: 'Product Discount',
    DiscountType: 'Discount Type',
    DesignatedProduct: 'Designated Product',
    AllProducts: 'All Products',
    DiscountRate: 'Discount Rate',
    ProductExpenseItem: 'Product Expense Item',
    VersionPartition: 'Version Partition',
    OnlyOneOperation: 'Only one item can be selected for operation',
    PleaseSelectOneUpload: 'Please select one to upload',
    CostPriceDetails: 'Cost Price Details',
    Partition: 'Partition',
    PriceListVersionName: 'PriceListVersionName',
    ChargingMethods: 'Charge Ways',
    IncrementalPrice: 'Incremental Price',
    GradeStartingPoint: 'GradeStart',
    GradeEndPoint: 'GradeEnd',
    Price: 'Price',
    BasePrice: 'BasePrice',
    IncrementalGrade: 'IncrementalGrade',
    BillingCarry: 'BillingCarry',
    IncomePriceDetails: 'Income Price Details',
    IsExpectedActualPieces: 'EstimatedQuantity Greater than Actual Quantity?',
    ReceivingOperationNo: 'Receiving Operation No',
    ReceivingOrders: 'Receiving Orders',
    TotalGrossWeight: 'Total Gross Weight',
    TotalVolume: 'Total Volume',
    TotalWeight: 'Total Weight',
    OperationStepsOfReceiptOperation: 'Operation steps of receipt operation',
    EnterNote: 'Enter the Note',
    PayableSummary: 'Payable Summary',
    IsReset: 'IsReset',
    TotalSummaryBookkeepingAmount: 'Total Summary Bookkeeping Amount:',
    TotalSummaryBookkeepingBalance: 'Total Summary Bookkeeping Balance:',
    SummaryBookkeepingAmount: 'Summary Bookkeeping Amount',
    SummaryBookkeepingBalance: 'Summary Bookkeeping Balance',
    Month: 'Month',
    TotalAdjustmentBookkeepingAmount: 'Total Adjustment Bookkeeping Amount',
    OriginalBookkeepingAmount: 'Original Bookkeeping Amount',
    AdjustBookkeepingAmount: 'Adjust Bookkeeping Amount',
    TotalBillingAmount: 'Total Billing Amount',
    Difference: 'Difference',
    ProfitAnalysis: 'Profit Analysis',
    TotalReceivables: 'Total Receivables',
    TotalAmountPayable: 'Total amount payable:',
    SummaryProfit: 'Summary Profit:',
    Profit: 'Profit',
    OrderQuantity: 'Order Quantity',
    ARSummary: 'A/R Summary',
    TotalAmountOfReceipts: 'Total amount of receipts',
    EstimatedTime: 'Estimated Time',
    ActualTime: 'Actual Time',
    OnlySelectOneProductOrderNo: 'Only select one product or order number!',
    SubmissionDate: 'Submission Date',
    WorkOrder: 'Work Order',
    ExceptionTypes: 'ExceptionTypes',
    WorkOrderNo: 'WorkOrderNo',
    Content: 'Content',
    TimeConsuming: 'TimeConsuming',
    WorkOrderTask: 'WorkOrderTask',
    IsPush: 'IsPush',
    TaskContentUpdate: 'Task Content Update',
    IssueAdjustmentOrder: 'Issue Adjustment Order',
    GenerateAdjustmentOrder: 'Generate Adjustment Order',
    SystemOrderNo: 'System Order No',
    ClaimType: 'ClaimType',
    ActiveClaim: 'ActiveClaim',
    PassiveClaim: 'PassiveClaim',
    ClaimOrderType: 'ClaimOrderType',
    CustomersSuppliers: 'Customers/suppliers',
    IsAdjust: 'IsAdjust',
    ValueAndCompensation: 'Value And Compensation',
    SupplierCompensationAmount: 'CompensationAmount',
    CreateInformation: 'Create Information',
    CompensationOrderInformation: 'Compensation Order Information',
    PaymentDetails: 'Payment Details',
    VoucherNo: 'VoucherNo',
    BusinessOrders: 'Business Orders',
    StatementNo: 'StatementNo',
    OriginalPaymentCurrency: 'Original Payment Currency',
    PaymentDetailsStatus: 'Payment Details Status',
    WaitingForManagerReview: 'Waiting For Manager Review',
    WaitingForFinancialAudit: 'Waiting For Financial Audit',
    Cancelled: 'Cancelled',
    PartialPayment: 'Partial Payment',
    IsCheckAccounts: 'IsCheckAccounts',
    IssuePaymentOrder: 'Issue Payment Order',
    ChargeTime: 'Charge Time',
    OriginalPaymentAmount: 'Original Payment Amount:',
    AccountBalance: 'Account Balance:',
    TotalOriginalPaymentAmount: 'Total original payment amount:',
    TotalAccountingAmount: 'Total accounting amount:',
    TotalAccountingBalance: 'Total accounting balance:',
    CheckCompleted: 'Check Completed',
    CancelTheCheck: 'Cancel the check',
    CreatePaymentOrder: 'Create Payment Order',
    OneClickCreatePaymentOrder: 'One click to create a payment order',
    Export: 'Export',
    ExportAllData: 'Export All Data',
    ExportData: 'Export Data',
    DownloadSummaryData: 'Download Summary Data',
    FileName: 'File Name',
    Voucher: 'Voucher',
    PaymentInformation: 'Payment Information',
    BusinessOrderNo: 'Business OrderNo',
    AccountingInformation: 'Accounting Information',
    OriginalPaymentInformation: 'Original Payment Information',
    OriginalPaymentBalance: 'Original Payment Balance',
    CheckTheInformation: 'Check the Information',
    Mark: 'Mark',
    UpdatedBy: 'UpdatedBy',
    ImportBills: 'Import Bills',
    BillWeight: 'Bill Weight',
    NumberOfBills: 'Number Of Bills',
    BillAmount: 'Bill Amount',
    DifferencesAmount: 'Differences Amount',
    PaymentTime: 'Payment Time',
    CollectionDetails: 'Collection Details',
    CollectionStatus: 'Collection Status',
    ForCollection: 'For Collection',
    CollectionCompleted: 'Collection Completed',
    CollectionInformation: 'Collection Information',
    CollectionAmount: 'Collection Amount',
    BillNo: 'BillNo',
    OriginalCollectionBalance: 'Original collection balance',
    OriginalCollectionAmount: 'Original Collection Amount',
    Bill: 'Bill',
    BillStatus: 'Bill Status',
    IsInvoicing: 'IsInvoicing',
    BillDate: 'Bill Date',
    TotalM: 'Total:',
    BillingSummary: 'Billing Summary',
    OneClickBillingSummary: 'One-click billing summary',
    BillInformation: 'Bill Information',
    BillSummary: 'Bill Summary',
    Balance: 'Balance',
    OriginalCurrency: 'Original Currency',
    AdjustmentOrderStatus: 'Adjustment Order Status',
    Approved: 'Approved',
    BillManagement: 'Bill Management',
    UploadFedExBill: 'Upload FedEx Bill',
    AdjustsOrderNo: 'Adjusts OrderNo',
    AdjustDetails: 'Adjust Details',
    BillingCurrency: 'Billing Currency',
    PaymentCurrency: 'Payment Currency',
    PaymentExchangeRate: 'Payment Exchange Rate',
    BillSummaryStatus: 'Bill Summary Status',
    PartialCollection: 'Partial Collection',
    IsCancelAftererification: 'IsCancelAftererification',
    SendCustomer: 'Send Customer',
    PaymentAmount: 'Payment amount:',
    PaymentWriteOffBalance: 'Payment write-off balance:',
    Invoiced: 'Invoiced',
    AssociatedIncomingBill: 'Associated Incoming Bill',
    CancelBillSummary: 'Cancel Bill Summary',
    ResetBillPaymentInformation: 'Reset Bill Payment Information',
    PaymentDaysInterval: 'Payment Days Interval',
    Notice: 'Notice',
    BillingDetails: 'Billing Details',
    PaymentOrder: 'Payment Order',
    PaymentOrderStatus: 'Payment Order Status',
    AssociatedBilling: 'Associated Billing',
    VoidPaymentBill: 'Void Payment Bill',
    PaymentCompleted: 'Payment Completed',
    Payment: 'Payment',
    PaymentInterval: 'Payment Interval',
    ExpenditureAdjustmentBill: 'Expenditure Adjustment Bill',
    AccountStatements: 'Account Statements',
    PendingProcessing: 'Pending Processing',
    InReconciliation: 'In Reconciliation',
    ReconciliationCompleted: 'Reconciliation Completed',
    ReconciliationFailed: 'Reconciliation Failed',
    ReconciliationType: 'Reconciliation Type',
    DetailedReconciliation: 'Detailed Reconciliation',
    SummaryReconciliation: 'Summary Reconciliation',
    BillType: 'Bill Type',
    UniversalType: 'Universal Type',
    FedexReconciliation: 'Fedex Reconciliation',
    BillStartDate: 'Bill Start Date',
    BillEndDate: 'Bill End Date',
    OpenReconciliation: 'Open Reconciliation',
    VoidedStatement: 'Voided Statement',
    PDFConversion: 'PDF Conversion',
    ProcessingInformation: 'Processing Information',
    BillFile: 'Bill File',
    StatementDetails: 'Statement Details',
    TheAmounPositiveNegative: 'The amount is positive or negative',
    NegativeDifferences: 'Negative Differences',
    PositiveDifferences: 'Positive Differences',
    IsWeightDifference: 'IsWeightDifference',
    DifferenceNegativeWeights: 'Difference between positive and negative weights',
    IsNumberDifference: 'IsNumberDifference',
    PositivPieces: 'Positive and negative differences in the number of pieces',
    PartitionDifferences: 'Partition Differences',
    PartitionNegative: 'Partition differences are positive and negative',
    IsSystemNotExist: 'IsSystemNotExist',
    CurrentAmount: 'Current Amount:',
    TotalVarianceAmount: 'Total Variance Amount:',
    TotalSystemAmount: 'Total System Amount:',
    TotalAmount: 'Total Amount：',
    ExpenditureAdjustment: 'Expenditure Adjustment',
    GenerateExpenseAdjustmentOrders: 'Generate expense adjustment orders',
    OneExpenseAdjustmentOrders: 'One click to generate an expense adjustment Orders',
    IncomeAdjustment: 'Income Adjustment',
    GenerateIncomeAdjustmentOrders: 'Generate income adjustment orders',
    OneIncomeAdjustment: 'One click to generate an income adjustment orders',
    OffsetTheCost: 'Offset the cost',
    GenerateExpensesCosts: 'Generate expenses to offset costs',
    OneOffsetCosts: 'One key generates expenses to offset costs',
    ReferenceNo: 'ReferenceNo',
    ReconciliationStatus: 'Reconciliation Status',
    IsComplete: 'IsComplete',
    IsDifferent: 'IsDifferent',
    IncomeAmount: 'Income Amount',
    PricingAmount: 'Pricing Amount',
    RevenueVarianceAmount: 'Revenue Variance Amount',
    SystemCostAmount: 'System Cost Amount',
    CostVarianceAmount: 'Cost Variance Amount',
    SystemNumber: 'SystemNumber',
    QuantityOfDifference: 'The quantity of difference',
    SystemWeight: 'System weight',
    DifferencesInWeight: 'Differences In Weight',
    SystemPartition: 'System Partition',
    DifferencePartition: 'Difference Partition',
    JobDetailsShelvingList: 'Job Details of Shelving List',
    ShelvingJobNo: 'Shelving job No',
    ShelvingOrders: 'Shelving Orders',
    RefundJobNo: 'Refund Job No',
    RefundOrders: 'Refund Orders',
    JobDetailsRefundList: 'Job Details of Refund List',
    StepsOnShelves: 'Steps of single operation on shelves',
    ShipmentProgress: 'Shipment in progress',
    DeliverGoods: 'Deliver Goods',
    BatchNo: 'BatchNo',
    TotalOrderNumber: 'Total Order Number',
    WarehouseOutList: 'Warehouse Out List',
    IsGenerateInvoice: 'IsGenerateInvoice',
    GenerateInvoice: 'Generate Invoice',
    OrderOperation: 'OrderOperation',
    CheckBill: 'Check the Bill',
    AddButton: 'Add Button',
    EditButton: 'Edit Button',
    PermissionsLogo: 'PermissionsLogo',
    ParentMenu: 'ParentMenu',
    MenuNotSelectParent: 'Menu not selected as parent',
    PleaseEnterName: 'Please enter a name',
    PleaseEnterPermissionID: 'Please enter permission ID',
    PleaseEnterSerialNo: 'Please enter the serial number',
    PleaseSelectMenuType: 'Please select the menu type',
    EnterNameSearch: 'Enter name search',
    Search: 'Search',
    Add: 'Add',
    PermissionId: 'PermissionId',
    CannotBeUndone: 'Are you sure you want to delete? If a child node exists, the node will rise. This operation cannot be undone!',
    AddMenu: 'AddMenu',
    EditMenu: 'EditMenu',
    MenuIcon: 'MenuIcon',
    ClickTheSelectIcon: 'Click The Select Icon',
    MenuName: 'MenuName',
    MenuSorting: 'MenuSorting',
    SmallerComesFirst: 'The smaller number comes first',
    Yes: 'Yes',
    No: 'No',
    InternalMenu: 'InternalMenu',
    LinkAddress: 'LinkAddress',
    MenuPath: 'MenuPath',
    ComponentPath: 'ComponentPath',
    Icon: 'Icon',
    AddOrganization: 'Add Organization',
    EditOrganization: 'Edit Organization',
    PleaseSelectOrganizationType: 'Please select an organization type',
    ParentOrganization: 'ParentOrganization',
    NotAsParentOrganization: 'Not selected as a parent organization',
    Company: 'Company',
    Department: 'Department',
    AddPermissions: 'Add Permissions',
    EditPermissions: 'Edit Permissions',
    Methods: 'Methods',
    ContextMenu: 'ContextMenu',
    SelectWithPermission: 'Select the menu associated with the permission',
    ParentAuthority: 'ParentAuthority',
    PermissionNotAsParent: 'Permission not selected as parent',
    PleaseEnterMethod: 'Please enter a method',
    AddRole: 'Add Role',
    EditRole: 'Edit Role',
    CreationDate: 'Creation Date',
    AddUsers: 'Add Users',
    EditUsers: 'Edit Users',
    Username: 'UserName',
    XingMing: 'Name',
    Activated: 'Activated',
    Locked: 'Locked',
    InternalUsers: 'Internal Users',
    Position: 'Position',
    CompanyOfAffiliation: 'Company Of Affiliation',
    PleaseSelectCompany: 'Please select your company',
    Role: 'Role',
    PleaseRole: 'Please select a role',
    PleaseEnterPhoneNo: 'Please enter your mobile phone number',
    PleaseEnterSYPhoneNo: 'Please enter the correct 11 digit cell phone number',
    PleaseEnterUserName: 'Please enter a user name',
    LengthSanAndEScharacters: 'The length is between 3 and 20 characters',
    NameCannotEmpty: 'The name cannot be empty',
    PleaseEnterEmail: 'Please enter the email address',
    PleaseEnterCorrectEmail: 'Please enter the correct email address',
    StateCannotEmpty: 'The state cannot be empty',
    DefaultPassword: 'Default password: 123456',
    UserNameAlreadyExists: 'The user name already exists!',
    EnterKeywordSearch: 'Enter a keyword search',
    Headurl: 'Headurl',
    DateLastPasswordChange: 'Date of last password change',
    Enable: 'ON',
    Disable: 'OFF',
    HeadPortrait: 'Head Portrait',
    Supervisor: 'Supervisor',
    NumberofWarehouseRemoval: 'The number of warehouse removal work sheet',
    WarehouseRemovalShelf: 'Warehouse removal and off shelf list',
    DetailsWrehouseRemoval: 'Details of warehouse removal and shelf removal operations',
    OperationOperation: 'Operation steps of moving warehouse and removing shelf single operation',
    WarehouseShelvingSheet: 'Warehouse moving and shelving operation sheet order No',
    WarehouseShelving: 'Storage removal list',
    RemoveDetailsShelving: 'Details of warehouse moving and shelving operation',
    OperationStepsOfMovingWarehouse: 'Operation steps of single operation of moving warehouse and putting on shelves',
    WarehouseShelvingOrderNo: 'Transfer shelf No',
    TotalIncome: 'Total Income',
    TotalSpending: 'Total Spending',
    TruckCollection: 'Truck collection',
    ReceivingGoodsWarehouse: 'Receiving goods in warehouse',
    TransportationRouteConfirmation: 'Transportation route confirmation',
    ExitCustomsClearanceInspection: 'Exit customs clearance inspection',
    ExitCustomsClearanceCompleted: 'Exit customs clearance completed',
    DepartureInternationalTransportation: 'Departure of international transportation',
    EntryCustomsClearanceInspection: 'Entry customs clearance inspection',
    EntryCustomsClearanceCompleted: 'Entry customs clearance completed',
    TruckTransshipment: 'Truck transshipment',
    InTheDelivery: 'In the Delivery',
    OrderCancellation: 'Order Cancellation',
    Finvoice: 'invoice',
    CourierNo: 'CourierNo',
    SupplierServices: 'Supplier Services',
    SenderAddress: 'Sender Address',
    RecipientAddress: 'Recipient Address',
    BillingConversion: 'Billing Conversion',
    ExpressBillCost: 'Express Bill Cost',
    ToDelete: 'To delete?',
    MainOrderComplete: 'Main Order Complete',
    StateBack: 'State Back',
    GenerateImportCustomsDeclaration: 'Generate import customs declaration',
    GenerateExportCustomsDeclaration: 'Generate export customs declaration',
    ChargeWeight: 'Charge Weight',
    CustomsDeclaration: 'Customs Declaration',
    ImportDeclarationNo: 'Import DeclarationNo',
    ExportDeclarationNo: 'Export DeclarationNo',
    NumberOfCustomerPieces: 'NumberOfCustomerPieces',
    CustomerOrderWeight: 'CustomerOrderWeight',
    CustomerOrderVolume: 'CustomerOrderVolume',
    MainSingle: 'Main Single',
    ShippingBill: 'Shipping Bill',
    MasterBillExpense: 'Master Bill Expense',
    RemoveCustomerOrder: 'Are you sure you want to remove this customer order?',
    OceanOrderFinish: 'Ocean Order Finish',
    VoyageNumber: 'Voyage Number',
    OceanOrderCost: 'Ocean Order Cost',
    NoBeenSubmitted: 'No order has been submitted yet!',
    SystemError: 'System error, please contact administrator!',
    PrintInboundOrders: 'Print inbound orders',
    NumberOfShelves: 'Number of shelves',
    RecipientCityCode: 'Recipient City Code',
    RegionalManagement: 'Regional Management',
    PackagePartition: 'Package Partition',
    PrintCode: 'Print Code',
    PrinterCode: 'Printer Code',
    PalletCode: 'Pallet Code',
    PalletMaxLoad: 'Pallet Max Load (kg)',
    OutboundPallet: 'Outbound Pallet',
    PalletSummary: 'Pallet Summary',
    PalletAndParcels: 'Pallet And Parcels',
    LocationNo: 'LocationNo',
    PartitionNo: 'No',
    PartitionType: 'PartitionType',
    PickingPriority: 'PickingPriority',
    Channel: 'Channel',
    ShelfNo: 'Shelf No',
    FloorNumber: 'Floor Number',
    LocationLength: 'Location Length (CM)',
    LocationWidth: 'Location Width (CM)',
    LocationHeight: 'Location Height (CM)',
    VolumeCBM: 'Volume (CBM)',
    FrozenOrders: 'Frozen Orders',
    Freeze: 'Freeze',
    Thawed: 'Thawed',
    FreezeTime: 'Freeze Time',
    ThawingTime: 'Thawing Time',
    SubmitToFreeze: 'Freeze',
    Unfreeze: 'Unfreeze',
    FreezeOrderNo: 'FreezeOrderNo',
    FreezeInventory: 'Freeze Inventory',
    UnfreezeInventory: 'Unfreeze Inventory',
    Transfer: 'Transfer',
    ImportFailed: 'Import Failed!',
    StockAgeReport: 'Stock Age Report',
    StatisticalDate: 'Statistical Date',
    GenerateStockAge: 'Generate StockAge',
    Days: 'Days',
    PleaseEnterTheQuantity: 'Please enter the quantity',
    AvailableInventory: 'Available Inventory',
    RequestTimeout: 'Request Timeout',
    LoginAgain: 'The login status has expired. You can stay on this page or log in again',
    ReLogin: 'Re-login',
    NotPermission: 'You do not have permission to perform this operation.',
    NoAccess: 'Your account has no access to view the role, please contact the system administrator to configure!',
    ModifyPaymentInfor: 'Modify payment information',
    InputCurrencyPayment: 'Please input the currency and amount of payment',
    QueryError: 'Query Error',
    PleaseFillIn: 'Please fill in',
    PleaseEnterAnIconName: 'Please Enter An Icon Name',
    SDate: 'Data',
    STime: 'Time',
    PleaseEnterzero: 'Please Enter zero',
    ClickUpload: 'Upload',
    Failure: 'Failure:',
    ConfirmDeletionContinue: 'Confirm deletion, continue?',
    UploadedSuccessfully: 'Uploaded successfully',
    UploadFailed: 'Upload failed',
    InsertOneLineAbove: 'Insert a line above',
    InsertALineBelow: 'Insert a line below',
    DeleteRow: 'Delete Row',
    ReadOnly: 'Read Only',
    HideUpload: 'Hide Upload',
    Download: 'Download',
    PleaseEnterCorrectQty: 'Please enter the correct quantity',
    OrderDate: 'OrderDate',
    StartDate: 'StartDate',
    EndDate: 'EndDate',
    LastWeek: 'LastWeek',
    LastMonth: 'LastMonth',
    LastThreeMonths: 'LastThreeMonths',
    FailedGetData: 'Failed to get data!',
    EnglishNameOfMenu: 'English name of menu',
    Carrier: 'Carrier',
    CarrierNum: 'Carrier Code',
    CarrierName: 'Carrier Name',
    CarrierNickName: 'Carrier Abbreviation',
    Contacts: 'Contacts',
    Telephone: 'Telephone',
    Fax: 'Fax',
    EntrustCustomer: 'Entrust Customer',
    EntrustCustomerNum: 'Entrust Customer Code',
    EntrustCustomerName: 'Entrust Customer Name',
    EntrustCustomerNickName: 'Entrust Customer Nick Name',
    ConsigneeConsignor: 'Consignee or Consignor',
    ConsigneeConsignorNum: 'Consignee / Consignor No',
    ConsigneeConsignorName: 'Consignee / Consignor Name',
    BelongingCarrier: 'Carrier',
    Car: 'Car',
    CarNo: 'Car No',
    CarType: 'Vehicle type',
    CarSource: 'Vehicle source',
    FuelType: 'Fuel Type',
    CarLoad: 'Vehicle load (kg)',
    CarVolume: 'Vehicle volume（m³）',
    TmsDriver: 'Tms Driver',
    Idcard_num: 'Id Card No',
    DriverName: "Driver's Name",
    Gender: 'Gender',
    EmergencyPerson: 'Emergency Person',
    EmergencyTelephone: 'Emergency Contact Number',
    MALE: 'MALE',
    FEMALE: 'FEMALE',
    EntrustOrder: 'Entrust Order',
    EntrustOrderNo: 'Entrust Order No',
    TotalWeightKG: 'Total Weight(Kg)',
    Consignor: 'Consignor',
    ConsignorTelephone: 'ConsignorTelephone',
    StartPoint: 'StartPoint',
    Consignee: 'Consignee',
    ConsigneeTelephone: 'ConsigneeTelephone',
    EndPoint: 'EndPoint',
    CurrentState: 'CurrentState',
    PickupDate: 'Pickup Date',
    ConsignorInfo: 'Consignor Info',
    ConsignorName: 'Consignor Name',
    ShippingState: 'Shipper state',
    ShippingCity: 'Shipper city',
    ShippingArea: 'Shipper area',
    ConsigneeInfo: 'Consignee Info',
    ConsigneeName: 'Consignee Name',
    SerialNo: 'Serial No',
    GoodsName: 'Goods Name',
    Barcode: 'Barcode',
    VolumeM3: 'Volume（m³）',
    ExpensesReceivable: 'Expenses Receivable',
    taxrate: 'Tax Rate（%）',
    CV: 'Complete vehicle',
    BC: 'Bulk Cargo',
    DispatchOrder: 'Dispatch Order',
    DispatchOrderNo: 'Dispatch order No                                                                                                                                                                                                          ',
    SAD: 'Saved',
    DP: 'Document preparation',
    REJ: 'Rejected',
    UNC: 'Unclaimed',
    TBP: 'To be picked up',
    TD: 'Taking delivery',
    WD: 'Waiting for departure',
    ITR: 'In transit',
    CMP: 'Completed',
    CAN: 'Cancelled',
    DeliveryTime2: 'Delivery Time',
    RequiredArrivalTime: 'Required Arrival Time',
    CarInfo: 'Car Info',
    DriverInfo: 'Driver Info',
    TransportationRoute: 'Transportation Route',
    TransportationDetail: 'Transportation Detail',
    EstimatedArrivalTime: 'Estimated Arrival Time',
    ZHOU: ' State',
    SHI: 'City',
    CurrentLocation: 'Current Location',
    CompartmentTemperature: 'Compartment Temperature(℃)',
    CompartmentHumidity: 'Compartment Humidity%',
    IsDelayed: 'Is Delayed',
    Maintain: 'Maintain',
    MaintainNum: 'Maintain Num',
    REP: 'Repair',
    MAI: 'Maintain',
    MaintainType: 'Maintenance Type',
    MaintainLocation: 'Maintenance Location',
    MaintainContent: 'Maintenance Content',
    MaintainTime: 'Maintenance Time',
    ActualExecutionTime: 'Actual Execution Time',
    FinishTime: 'Finish Time',
    TBI: 'To be implemented',
    EXEC: 'Executing',
    AbnormalLog: 'Abnormal Log',
    AbnormalLogNo: 'Exception No',
    FAU: 'Vehicle Fault',
    TRO: 'Traffic Accident',
    TEM: 'Abnormal temperature',
    HUM: 'Abnormal humidity',
    DVT: 'Deviation from scheduled route',
    AbnormalTime: 'Abnormal Time',
    AbnormalLocation: 'Abnormal Location',
    NAC: 'Not accepted yet',
    ACC: 'Accepted',
    AcceptanceResult: 'Acceptance Result',
    PEND: 'Pending',
    FAAL: 'False alarm',
    COTR: 'Continue transportation',
    REVE: 'Replace the vehicle',
    CHDR: 'Change driver',
    OTHE: 'Other',
    AbnormalDescribe: 'Abnormal Describe',
    AcceptanceDescribe: 'Acceptance Describe',
    AcceptanceTime: 'Acceptance Time',
    LogNo: 'Record Number',
    DelayLog: 'Delay Log',
    TranNode: 'Transportation Node',
    ActualArrivalTime: 'Actual Arrival Time',
    DelayReason: 'Delay Reason',
    Mobile: 'Mobile Phone',
    FRE: 'Free',
    MIP: 'Maintenance',
    DIS: 'Disabled',
    Fleet: 'Fleet',
    FleetNum: 'Fleet No',
    TaskNo: 'Task Number',
    TRAN: 'Transport',
    RMAI: 'Maintenance',
    DriverIncome: 'Driver Income',
    EstimatedMileage: 'Estimated Mileage',
    EstimatedOtherIncome: 'Estimated Other Income',
    EstimatedTotal: 'Estimated Total',
    WaitingForPost: 'WaitingForPost',
    Uncollected: 'Uncollected',
    Collected: 'Collected',
    FullCollection: 'Full Collection',
    PaymentMethod: 'PaymentMethod',
    CashSettlement: 'Cash Settlement',
    IsCollection: 'Collection Or Not',
    TotalPriceExcludingTax: 'Total Price Excluding Tax',
    TotalPriceAndTax: 'Total Price And Tax',
    AmountIncTax: 'Amount Including Tax',
    AmountExcTax: 'Amount Exclufing Tax',
    DiscountAmount: 'Discount Amount',
    DiscountAmountIncTax: 'Discount Amount Including Tax',
    DiscountAmountExcTax: 'Discount Amount Excluding Tax',
    ReceiveAmount: 'Receive Amount',
    UncollectedAmount: 'Uncollected Amount',
    ExpensesReceivableNo: 'Expenses No',
    CollectionLog: 'Collection Log',
    CollectionTime: 'Collection Time',
    TransferAccounts: 'Transfer Accounts',
    Cash: 'Cash',
    AliPay: 'AliPay',
    WXPay: 'WechatPay',
    BlankAccount: 'Blank Account',
    PageInvoice: 'Paper Invoice',
    ElectInvoice: 'Electronic invoice',
    InvoiceAmount: 'Invoice amount',
    InvoiceTitle: 'Invoice header',
    CompanyPhone: 'Company Phone',
    RegisteredAddress: 'Registered Address',
    ReceiveEmail: 'Receiving Email',
    InvoiceTaker: 'Payee',
    TakerPhone: 'Receiver telephone',
    TakerAddress: 'Address of the payee',
    Area: 'Area',
    RelPostcode: 'RelPostcode',
    DeliverySite: 'DeliverySite',
    AirFreightMainBillNo: 'MasterNo',
    transactionTime: 'Transaction Time',
    transactionCurrency: 'Transaction Currency',
    creationInformation: 'Creation Information',
    creator: 'Creator',
    updater: 'Updater',
    abnormalTag: 'Abnormal Type',
    abnormalName: 'Abnormal Name',
    description: 'Description',
    chargeTemplateConfig: 'Charge Template Configuration',
    templateName: 'Template Name',
    presetTemplateCostDetails: 'Preset Template - Cost Details',
    presetTemplateIncomeDetails: 'Preset Template - Income Details',
    costDetails: 'Cost Details',
    incomeDetails: 'Income Details',
    chargeName: 'Charge Name',
    unitPrice: 'Unit Price',
    quantity: 'Quantity',
    payer: 'Payer',
    twoCharCode: 'Two-char Code',
    threeCharCode: 'Three-char Code',
    chineseName: 'Chinese Name',
    englishName: 'English Name',
    executeCommand: 'Execute Command',
    pleaseEnterExecuteCommand: 'Please enter execute command',
    clearExecutionResults: 'Clear Execution Results',
    restartService: 'Restart Service',
    restartScheduledTask: 'Restart Scheduled Task',
    logoutSuccess: 'Logout successful!',
    executeSuccess: 'Execute successful!',
    executeFailed: 'Execute failed!',
    labelName: 'Label Name',
    dataValue: 'Data Value',
    typeValue: 'Type Value',
    sort: 'Sort',
    dictionary: 'Dictionary',
    shareLogic: 'Share Logic',
    chargeWeight: 'Charge Weight',
    confirmChargeWeight: 'Confirm Charge Weight',
    confirmVolume: 'Confirm Volume',
    isValueAddedCost: 'Is Value Added Cost',
    billingUnit: 'Billing Unit',
    minimumCharge: 'Minimum Charge',
    deleteSuccess: 'Delete Successful',
    pickLine: 'Pick Line',
    chuteNumber: 'Chute Number',
    priority: 'Priority',
    isOccupied: 'Is Occupied',
    orderNumber: 'Order Number',
    businessExpenses: 'Business Expenses',
    managementExpense: 'Management Expense',
    serviceClassCode: 'Service Class Code',
    serviceClassName: 'Service Class Name',
    waybill: 'Waybill',
    track: 'Track',
    overseasWarehouse: 'Overseas Warehouse',
    customsClearance: 'Customs Clearance',
    isEnabled: 'Is Enabled',
    affiliatedTrack: 'Affiliated Track',
    productConfiguration: 'Product Configuration',
    airwayBill: 'Airway Bill',
    oceanBillOfLading: 'Ocean Bill of Lading',
    trackDescription: 'Track Description',
    trackEnglishDescription: 'Track English Description',
    copy: 'Copy',
    trackingNumber: 'Tracking Number',
    parcelTrackingNumber: 'Parcel Tracking Number',
    customerOrderNumber: 'Customer Order Number',
    thirdPartyServiceOrderNumber: 'Third Party Service Order Number',
    AGNumber: 'AG Number',
    searchPlaceholder: 'Order Number/Bill of Lading Number/Master Order Number',
    isCheck: 'Is Check',
    clearType: 'Clearance Type',
    ProductFeatures: 'Product Features',
    singleClearance: 'Single Clearance',
    mergedClearance: 'Merged Clearance',
    clearanceNumber: 'Clearance Number',
    pieces: 'Pieces',
    weight: 'Weight',
    volume: 'Volume',
    exporter: 'Exporter',
    importer: 'Importer',
    masterOrderNumber: 'Master Order Number',
    oceanOrderNumber: 'Ocean Order Number',
    supplier: 'Supplier',
    product: 'Product',
    orderStatus: 'Order Status',
    statusFailure: 'Status Failure',
    isConfirmShip: 'Is Confirm Ship',
    orderTime: 'Order Time',
    labelManagement: 'Label Management',
    createLabel: 'Create Label',
    downloadLabel: 'Download Label',
    cancelLabel: 'Cancel Label',
    importOrder: 'Import Order',
    export: 'Export',
    exportSmallPackageOrder: 'Export Small Package Order',
    exportSmallPackageOrderByParcel: 'Export Small Package Order-Multiple Packages',
    failOrder: 'Fail Order',
    sync_status: 'Sync Status',
    printNeutralLabel: 'Print Neutral Label',
    printNeutralLabel100x150: 'Print Neutral Label 100X150',
    printNeutralLabel100x100: 'Print Neutral Label 100X100',
    trialFee: 'Trial Fee',
    printShippingLabel: 'Print Shipping Label',
    order: 'Order',
    route: 'Route',
    warehouse: 'Warehouse',
    recipient: 'Recipient',
    postcode: 'Postcode',
    goods: 'Goods',
    isWeighing: 'Is Weighing',
    weighingWeight: 'Weighing Weight',
    transport: 'Transport',
    totalOrder: 'Total Order',
    exportSuccess: 'Export Success',
    operationSuccess: 'Operation Success',
    onlyOneRecord: 'Only one record can be modified at a time',
    viewTrack: 'View Track',
    trackInfo: 'Track Info',
    orderNum: 'Order No.',
    customerOrderNum: 'Customer Order No.',
    information: 'Information',
    warehouseCode: 'Warehouse Code',
    departurePort: 'Departure Port',
    destinationPort: 'Destination Port',
    vesselName: 'Vessel Name',
    voyageNum: 'Voyage No.',
    flight: 'Flight',
    flightNum: 'Flight No.',
    departureDate: 'Departure Date',
    estimatedArrivalDate: 'Estimated Arrival Date',
    actualArrivalDate: 'Actual Arrival Date',
    cargo: 'Cargo',
    masterBill: 'Master Bill',
    airwayBillNum: 'Airway Bill No.',
    oceanBillNum: 'Ocean Bill No.',
    trackingNum: 'Tracking No.',
    VenderNo: 'Vender No',
    isCustomsDeclaration: 'Is Customs Declaration',
    customsDeclarationNum: 'Customs Declaration No.',
    submitOrder: 'Submit Order',
    isSubmitOrder: 'Is Submit Order',
    downloadTrackingNumber: 'Download Tracking Number',
    cancelOrder: 'Cancel Order',
    downloadImportTemplate: 'Download Import Template',
    importFBAOrder: 'Import FBA Order',
    downloadSingleProductTemplate: 'Download Single Product Template',
    importSingleProductOrder: 'Import Single Product Order',
    exportPODData: 'Export POD Data',
    configureExportDeclaration: 'Configure Export Declaration',
    orderNo: 'Order No',
    waitingForPost: 'Waiting for Post',
    preDeclared: 'Pre Declared',
    intercepted: 'Intercepted',
    partiallyInWarehouse: 'Partially In Warehouse',
    fullyInWarehouse: 'Fully In Warehouse',
    confirmedWarehouseData: 'Confirmed Warehouse Data',
    outOfDomesticWarehouse: 'Out Of Domestic Warehouse',
    departed: 'Departed',
    transfer: 'Transfer',
    signed: 'Signed',
    void: 'Void',
    SuperVoid: 'Super Void',
    roleName: 'Name',
    roleDesc: 'Description',
    menu: 'Menu',
    button: 'Button',
    deleteConfirm: 'Are you sure to delete? If there are child nodes, the nodes will rise. This operation cannot be undone!',
    languageIdentifier: 'Language Identifier',
    avatar: 'Avatar',
    username: 'Username',
    name: 'Name',
    email: 'Email',
    mobile: 'Mobile',
    department: 'Department',
    companyBelongs: 'Company',
    warehouseBelongs: 'Warehouse',
    position: 'Position',
    superiorManager: 'Superior',
    internalUser: 'Internal User',
    activated: 'Activated',
    locked: 'Locked',
    consolidationWarehouse: 'Consolidation Warehouse',
    belongingWarehouse: 'Belonging Warehouse',
    permissionName: 'Name',
    associatedMenu: 'Associated Menu',
    method: 'Method',
    relatedMenu: 'Related Menu',
    parentPermission: 'Parent Permission',
    customers: 'Customers',
    transferWarehouse: 'Transfer Warehouse',
    permissionIdentifier: 'Permission Identifier',
    permissionFlag: 'Permission Flag',
    notParentMenu: 'Not Parent Menu',
    buttonName: 'Name',
    buttonType: 'Type',
    sysCode: 'System Code',
    productCode: 'Product Code',
    quoteCurrency: 'Quote Currency',
    salePrice: 'Sale Price',
    declaredCurrency: 'Declared Currency',
    declaredNameCn: 'Declared Name (CN)',
    declaredNameEn: 'Declared Name (EN)',
    productPackaging: 'Product Packaging',
    packageShape: 'Package Shape',
    IrregularShape: 'Irregular Shape',
    rectangle: 'rectangle',
    cylinder: 'cylinder',
    packageType: 'Package Type',
    HardPackaging: 'Hard packaging',
    SPackagingH: 'Software packaging + Hard object',
    SPackagingS: 'Software packaging + Soft object',
    category: 'category',
    cat1Name: 'category 1',
    cat2Name: 'category 2',
    cat3Name: 'category 3',
    SKUProperty: 'SKU Property',
    propName: 'property name',
    propValue: 'property value',
    propUnit: 'property unit',
    SKUSalesLink: 'SKU SalesLink',
    SaleUrl: 'Sale Link',
    platform: 'platform',
    NoPlatform: 'No platform',
    size: 'Size',
    systemCode: 'System Code',
    skuCode: 'SKU Code',
    mapSkuCode: 'Sales SKU',
    sync: 'Sync SKU',
    print: 'Print Barcode',
    originalPackaging: 'original packaging for shipment',
    repackaging: 'repackaging',
    declaredPrice: 'Declared Price',
    use: 'Use',
    brand: 'Brand',
    model: 'Model',
    customsCode: 'CustomsCode',
    warehouseName: 'Warehouse Name',
    warehouseSearchHolder: 'Warehouse Code/Name/Email',
    isOpenToPublic: 'Open to Public',
    contactPerson: 'Contact Person',
    houseNumber: 'House Number',
    StateCode: 'State Code',
    ship: 'Ship',
    dischargeComplete: 'Discharge Complete',
    receiptComplete: 'Receipt Complete',
    shelfComplete: 'Shelf Complete',
    cost: 'Cost',
    leaveDate: 'Leave Date',
    SP: 'shipped',
    waiting: 'waiting For Sync',
    syncSuccess: 'Sync Success',
    syncFailed: 'Sync Failed',
    canceling: 'Canceling',
    cancelException: 'Cancel Exception',
    cancelSuccess: 'Cancel Success',
    cancelFailed: 'Cancel Failed',
    Withdraw: 'Withdraw',
    withdrawalStatus: 'Withdrawal Status',
    sortNode: 'Sort Node',
    subTrackCode: 'sub trakc code',
    pushCode: 'push code',
    TrackConf: 'Track Config',
    fbaTrackCode: 'Fba tracking number',
    fbaNo: 'Fba number',
    IossNumber: 'Ioss number',
    IossNumberAfter: 'Ioss number after',
    AgTrackingNumber: 'Ag tracking number',
    CostVersion: 'Cost version',
    ChargeValue: 'Charge value',
    RevenueVersion: 'Revenue version',
    currencyType: 'Currency',
    chargeTotal: 'Total',
    Position_1: 'Position',
    trackName: 'Trajectory',
    TraceSubcode: 'Trace subcode',
    time: 'Time',
    PullSingleTimeSeconds: 'Pull single time seconds',
    handleTimes: 'Processing times',
    modeKey: 'Modulo value',
    thirdOrderNo: 'Third party order number',
    labelDesc: 'Transfer information',
    labelTasks: 'Grab single task',
    goodsSummary: 'Commodity summary',
    OrderWeight: 'Order weight',
    parcelNum: 'Parcel number',
    OrderSn: 'O order number',
    parentOrderSn: 'PO order number',
    packageSummary: 'Parcel summary',
    texture: 'Material',
    itemWeight: 'Product weight',
    declaredNameEN: 'English product name',
    declaredNameCN: 'Chinese product name',
    itemCode: 'Product sku',
    labelHeight: 'Play single height cm',
    TypeSingleWidthCm: 'Type single width cm',
    labelLength: 'Order length cm',
    labelWeight: 'Order weight kg',
    parcelHeight: 'Height cm',
    parcelWidth: 'Width cm',
    parcelLength: 'Length cm',
    parcelWeight: 'Weight kg',
    parcelVolume: 'Volume',
    parcelQty: 'Carton number',
    caseNumber: 'Case number',
    parcelAndGoods: 'Parcels and goods',
    goodsRelated: 'Commodity correlation',
    CompanyName_1: 'Company name',
    Address2: 'Address 2',
    Address1: 'Address 1',
    RecipientEmailAddress: 'Recipient email address',
    Sender_1: 'Sender',
    Operator: 'Operator',
    WarehousingTime: 'Warehousing time',
    NuclearWeight: 'Nuclear weight',
    ProductService: 'Product service',
    BatchUpdate: 'Batch update',
    InterceptOperation: 'Intercept operation',
    ThirdOrderNumber: 'Third order number',
    RecipientCountryCode: 'Recipient country code',
    BigBagNumber: 'Big bag number',
    InterceptOrNot: 'Intercept or not',
    WhetherThereIsATrackingNumber: 'Whether there is a tracking number',
    FetchMessage: 'Fetch message',
    ChangeRemarks: 'Change remarks',
    UpdateRecipientInformation: 'Update recipient information',
    BatchRenewalOrder: 'Batch renewal order',
    ModificationProcessingTimes: 'Modification processing times',
    UpdateParcelTask: 'UpdateParcelTask',
    ConfirmDelivery: 'Confirm delivery',
    CreateInvoice: 'Create invoice',
    CreateInvoiceAg: 'Download Ag invoice',
    CompleteTheOrder: 'Complete the order',
    RestoreOrder: 'Restore order',
    ForceCancellationOfTheOrder: 'Force cancellation of the order',
    ExportThePacketReportFile: 'Export the packet report file',
    Redenomination: 'Redenomination',
    OrderNumberTrackingNumberCustomerOrderNumberAgNumber: 'Order number tracking number customer order number ag number',
    ShippingNumber: 'Shipping number',
    TrackingNumber: 'Tracking number',
    InterceptCauseRemarks: 'Intercept cause remarks',
    WidgetCount: 'Widget count',
    Complete: 'Complete',
    OutOfStorage: 'Out of storage',
    BeInStorage: 'Be in storage',
    Intercept: 'Intercept',
    AreYouSureAboutTheInterceptData: 'Are you sure about the intercept data',
    CancelIntercept: 'Cancel intercept',
    Ok: 'Ok',
    confirmShipOrder: 'Confirm Ship Order',
    AreYouSureToUnblockThisData: 'Are you sure to unblock this data',
    Account: 'Account',
    Message: 'Message',
    RecipientPostcode: 'Recipient postcode',
    AddresseeCountry: 'Addressee country',
    ShippersPostalCode: 'Shippers postal code',
    ShippersCountry: 'Shippers country',
    ProductCode_1: 'Product code',
    Cancel: 'Cancel',
    Ok_1: 'Ok',
    MessageInquiry: 'Message inquiry',
    OrderNumberInquiry: 'Order number inquiry',
    Inquiry: 'Inquiry',
    Expense: 'Expense',
    ExchangeRate: 'Exchange rate',
    StowageQuantity: 'Stowage quantity',
    ReasonForCorrection: 'Reason for correction',
    ContactAddressOfTheInsured: 'Contact address of the insured',
    InsuredTelephoneNumber: 'Insured telephone number',
    DocumentNumberOfTheInsured: 'Document number of the insured',
    TypeOfDocumentOfTheInsured: 'Type of document of the insured',
    RiotProtection: 'Riot protection',
    ListingLocation: 'Listing location',
    ShelfGuarantee: 'Shelf guarantee',
    CountryOfDestination: 'Country of destination',
    DestinationType: 'Destination type',
    PointOfDeparture: 'Point of departure',
    CountryOfDeparture: 'Country of departure',
    TimeOfDeparture: 'Time of departure',
    ClassOfGoods: 'Class of goods',
    CargoDescription: 'Cargo description',
    PackingQuantity: 'Packing quantity',
    PackagingType: 'Packaging type',
    ExpressCompany: 'Express company',
    DeliveryMethod: 'Delivery method',
    BillOfLadingNumber: 'Bill of lading number',
    MeansOfTransportAndVoyage: 'Means of transport and voyage',
    ModeOfTransport: 'Mode of transport',
    ActualWeightKg: 'Actual weight kg',
    PolicyCurrency: 'Policy currency',
    AdditionRatio: 'Addition ratio',
    HowTheInsuredAmountIsDetermined: 'How the insured amount is determined',
    FreightCurrency: 'Freight currency',
    NameOfTheInsured: 'Name of the insured',
    ProductCode_2: 'Product code',
    OriginalTrackingNumber: 'Original tracking number',
    MeasurementData: 'Measurement data',
    MagneticOrNot: 'Magnetic or not',
    LiveOrNot: 'Live or not',
    UnitPriceUsd: 'Unit price usd',
    EnglishUsage: 'English usage',
    ChineseUse: 'Chinese use',
    EnglishMaterial: 'English material',
    ChineseMaterial: 'Chinese material',
    ProductPicture: 'Product picture',
    CommodityInformation: 'Commodity information',
    GenerateWarehouseEntryTemplate: 'Generate warehouse entry template',
    ParcelInformation: 'Parcel information',
    ExpirationTime: 'Expiration time',
    BookedTime: 'Booked time',
    PassTheWhitelistTime: 'Pass the whitelist time',
    ShipmentStatus: 'Shipment status',
    TypeOfShop: 'Type of shop',
    ShipmentNumber: 'Shipment number',
    ShopId: 'Shop id',
    BookingInformation: 'Reservation information',
    OutboundInstructionStatus: 'Outbound instruction status',
    DownloadBoxLabel: 'Download box label',
    ShipmentManagement: 'Shipment management',
    VAS: 'Value added service',
    vasStatus: 'VAS Status',
    ChargeList: 'Charge List',
    AbnormalNumber: 'Abnormal Number',
    InventoryInfo: 'Inventory Info',
    CreateOutboundInstruct: 'Create outbound instruct',
    MerchandiseAndDeclarationInformation: 'Merchandise and declaration information',
    SupplierTotalVolume: 'Supplier total volume',
    SupplierGrossWeight: 'Supplier gross weight',
    TotalNumberOfBoxesFromSuppliers: 'Total number of boxes from suppliers',
    TotalInboundVolume: 'Total inbound volume',
    TotalInboundWeight: 'Total inbound weight',
    TotalNumberOfContainersInWarehouse: 'Total number of containers in warehouse',
    TotalCartons: 'Total cartons',
    tateCode: 'Province state code',
    CountryOrAreaCode: 'Country or area code',
    ReceivingDetailsAddress: 'Receiving details address',
    DeliveryNote: 'Delivery note',
    DeliveryTime_1: 'Delivery time',
    PickupPhoneNumber: 'Pickup phone number',
    DeliveryAddress_1: 'Delivery address',
    DeliveryInformation_1: 'Delivery information',
    PriorityLocus: 'Priority locus',
    RequisitionForm: 'Requisition form',
    ShippingPriorityTrackNumber: 'Shipping priority track number',
    EstimatedDateOfArrivalOverseas: 'Estimated date of arrival overseas',
    ActualDateOfArrivalOverseas: 'Actual date of arrival overseas',
    ArrivalDate_1: 'Arrival date',
    CustomsDeclaration_1: 'Customs declaration',
    OrderNote: 'Order note',
    ClaimOrderNum: 'Claim order number',
    ClaimOrderStatus: 'Claim status',
    WriteOffNum: 'Write off number',
    WriteOffAttachment: 'Write off attachment',
    PersonResponsible: 'Person responsible',
    PlatformFee: 'Platform fee',
    PersonAmount: 'Person Responsible Amount(CNY)',
    PortOfDestinationClearance: 'Port of destination clearance',
    ETDTime: 'ETD time',
    ETATime: 'ETA time',
    ShipperWh: 'Shipper warehouse',
    DestinationWh: 'Destination warehouse',
    CustomsDeclarationMethod: 'Customs declaration method',
    ChannelDefault: 'Channel Default',
    SingleTax: 'Single Tax',
    TimeWhenTheShipmentNumberExpires: 'Time when the shipment number expires',
    DriversName: 'Drivers name',
    LicensePlateNumber: 'License plate number',
    PlaceOfOrigin: 'Place of origin',
    TruckNumber: 'Truck number',
    TransportDetail: 'Transport detail',
    RelevantInformation: 'Relevant information',
    TranshipmentBin: 'Transhipment bin',
    TheConsigneeOfTheBillOfLadingIsAwbconsignee: 'The consignee of the bill of lading is awbconsignee',
    AddressInformation: 'Address information',
    SubdivisionRequirement: 'Subdivision requirement',
    WhetherToDivideGoodsOrNot: 'Whether to divide goods or not',
    ActualPallets: 'Actual pallets',
    EstimatedPallets: 'Estimated pallets',
    ActualWeight: 'Actual weight',
    ActualVolume: 'Actual volume',
    ActualNumberOfParcels: 'Actual number of parcels',
    ExpectedNumberOfParcels: 'Expected number of parcels',
    CustomsClearanceCompletionTime: 'Customs clearance completion time',
    AirportPickupTime: 'Airport pickup time',
    TimeOfArrivalNotice: 'Time of arrival notice',
    CustomsClearanceNumber: 'Customs clearance number',
    PriceValidity: 'Price validity',
    VoyageDescription: 'Voyage description',
    RemoteOrNot: 'Remote or not',
    AdditionalCharge: 'Additional charge',
    FirstLegPrice: 'First leg price',
    PlaceOfDelivery: 'Place of delivery',
    DetailOfQuotationScheme: 'Detail of quotation scheme',
    QuotationScheme: 'Quotation scheme',
    TaxfreeOrNot: 'Taxfree or not',
    Channel_1: 'Channel',
    WhetherToDeclare: 'Whether to declare',
    PrescriptionRequirement: 'Prescription requirement',
    DangerousGoodsInGeneral: 'Dangerous goods in general',
    TheNameOfAProductOrCommodity: 'The name of a product or commodity',
    Sell: 'Sell',
    InquiryScheme: 'Inquiry scheme',
    IsoCountryOfArrival: 'Iso country of arrival',
    Orderdependent: 'Orderdependent',
    PingAnInquiryCode: 'Ping an inquiry code',
    NameOfConsignee: 'Name of consignee',
    ResultDescription: 'Result description',
    ResultCode: 'Result code',
    TerminationDate: 'Termination date',
    Premium: 'Premium',
    PolicyNumber: 'Policy number',
    CurrencyOfValue: 'Currency of value',
    DeclaredValueOfGoods: 'Declared value of goods',
    DestinationAddress: 'Destination address',
    DepartureAddress: 'Departure address',
    SellerId: 'Seller id',
    BuyerId: 'Buyer id',
    QuantityOfGoods: 'Quantity of goods',
    TypeOfGoods: 'Type of goods',
    ExpressCompanyName: 'Express company name',
    DeliveryTime_2: 'Delivery time',
    ProjectName: 'Project name',
    SchemeName: 'Scheme name',
    TotalAmountInsured: 'Total amount insured',
    CommencementOfInsurance: 'Commencement of insurance',
    BeneficiarysDocumentNumber: 'Beneficiarys document number',
    BeneficiaryDocumentType: 'Beneficiary document type',
    NameOfBeneficiary: 'Name of beneficiary',
    NameOfTheInsured_1: 'Name of the insured',
    ApplicantsCertificateNumber: 'Applicants certificate number',
    ApplicantsDocumentType: 'Applicants document type',
    NameOfPolicyholder: 'Name of policyholder',
    ExternalProductCoding: 'External product coding',
    ExternalChannelCoding: 'External channel coding',
    OtherAccessories: 'Other accessories',
    NewProductLabel: 'New product label',
    NumberOfProductsChanged: 'Number of products changed',
    NewShipmentBoxLabel: 'New shipment box label',
    NumberOfContainersChanged: 'Number of containers changed',
    NewShipmentNumber: 'New shipment number',
    ChangeMarkInstruction: 'Change mark instruction',
    ChangeMarkOrNot: 'Change mark or not',
    NumberOfOutgoingBins: 'Number of outgoing bins',
    DestinationWarehouse: 'Destination warehouse',
    EngagementLetter: 'Engagement letter',
    AppointmentTime: 'Appointment time',
    BindReservationOrNot: 'Bind reservation or not',
    ChargeQuantity: 'Charge quantity',
    ValueaddedServiceOrderNumber: 'Valueadded service order number',
    ConfirmWarehouseEntryData: 'Confirm warehouse entry data',
    Revocation: 'Revocation',
    ImportFbmOrder: 'Import fbm order',
    DownloadTheReceipt: 'Download the receipt',
    ExportPackingList: 'Export packing list',
    TrackingNumberCustomerTrackingNumberPackageNumberTrackingNumber: 'Tracking number customer tracking number package number tracking number',
    SigningTime: 'Signing time',
    RecipientInformation: 'Recipient information',
    WhetherToTakeDeliveryOrNot: 'Whether to take delivery or not',
    WhetherToInsuranceOrNot: 'Is Insurance',
    ProductCharacteristics: 'Product characteristics',
    MeikeMoreAppointmentTime: 'Meike more appointment time',
    ShipmentNumberOrderNumberCustomerOrderNumberStoreId: 'Shipment number order number customer order number store id',
    HaveBeenCancelled: 'Have been cancelled',
    LastOperatingTime: 'Last operating time',
    DateInformation: 'Date information',
    LatestTrackTime: 'Latest track time',
    LatestTrackName: 'Latest track name',
    OrderInformation: 'Order information',
    ShipmentInformation: 'Shipment information',
    ShopInformation: 'Shop information',
    ThereIsNo: 'There is no',
    TemporaryStorageOverseas: 'Temporary storage overseas',
    PrivateAddress: 'Private address',
    AmazonWarehouse: 'Amazon warehouse',
    CrossborderStore: 'Crossborder store',
    LocalStore: 'Local store',
    HaveBeenDelivered: 'Have been delivered',
    WaitingToBeReleasedFromStorage: 'Waiting to be released from storage',
    HaveAlreadyReserved: 'Have already reserved',
    HaveArrivedAtPort: 'Have arrived at port',
    EnRoute: 'En route',
    ImportUpdateFbmOrder: 'Import update order',
    ImportUpdateFbaOrder: 'Import update order',
    ImportCustomsClearanceForm: 'Import customs clearance form',
    ExportTheOperationSummaryTable: 'Export the operation summary table',
    ExportCustomsClearanceOrder: 'Export customs clearance order',
    OrderNumberChineseProductNameEnglishProductNameFlightNumberPortOfDestination: 'Order number chinese product name english product name flight number port of destination',
    GhaDisplaysTheDlvTime: 'Gha displays the dlv time',
    GhaHasSentNoaTime: 'Gha has sent noa time',
    CaseShipment: 'Case of Shipment',
    NewCase: 'New case',
    UploadingPodcrmIsComplete: 'Uploading podcrm is complete',
    ItsInTheTransferBin: 'Its in the transfer bin',
    TransferCarHasBeenDispatched: 'Transfer car has been dispatched',
    CustomsClearanceDocumentsHaveBeenReceived: 'Customs clearance documents have been received',
    PodcrmHasBeenReceived: 'Podcrm has been received',
    PartialCustomsClearance: 'Partial customs clearance',
    PartialArrival: 'Partial arrival',
    WarehouseNotifiesCustomsClearanceTime: 'Warehouse notifies customs clearance time',
    WarehouseActualReceiptTime: 'Warehouse actual receipt time',
    TimeWhenTheWarehouseReceivedNoa: 'Time when the warehouse received noa',
    TheFlightShipHasArrived: 'The flight ship has arrived',
    Audited: 'Audited',
    PlacedAnOrder: 'Placed an order',
    CustomsClearanceOrder: 'Customs clearance order',
    Reorder: 'Reorder',
    SendInquiry: 'Send inquiry',
    Total_1: 'Total',
    InquirySheet: 'Inquiry sheet',
    ApplyForClaim: 'Apply for claim',
    PriceInformation: 'Price information',
    PolicyInformation: 'Policy information',
    InsuredInformation: 'Insured information',
    PolicyholderInformation: 'Policyholder information',
    Warranty: 'Warranty',
    SystemPolicyNumber: 'System policy number',
    CompletionOfInsurance: 'Completion of insurance',
    ClaimFailure: 'Claim failure',
    ClaimCompletion: 'Claim completion',
    ClaimsInProgress: 'Claims in progress',
    SuccessfulInsurance: 'Successful insurance',
    AuditFailure: 'Audit failure',
    AuditCompleted: 'Audit completed',
    PolicyOfInsurance: 'Policy of insurance',
    StartSubmission: 'Start submission',
    ShipmentNumberOrderNumber: 'Shipment number order number',
    ExitOrder: 'Exit order',
    SubmitValueAddedOrder: 'Submit value added order',
    ValueAddedOrderShipmentNumber: 'Value added order shipment number',
    AuditTime: 'Audit time',
    IncrementCharge: 'Increment charge',
    IncrementNumber: 'Increment number',
    InOperation: 'In operation',
    ValueaddedService: 'Valueadded service',
    SD: 'Out of delivery',
    HasArrivedOverseasWarehouse: 'Has arrived overseas warehouse',
    ArrivedAtPortAndLanded: 'Arrived at port and landed',
    HaveLeftPortAndTakenOff: 'Have left port and taken off',
    Declared: 'Declared',
    OutOfStorage_1: 'Out of storage',
    ConfirmationOfWarehousing: 'Confirmation of warehousing',
    HaveArrived: 'Have arrived',
    TotalData: 'Total data',
    ReceiverAddress: 'Delivery address',
    InquiryNumber: 'Inquiry number',
    HaveQuotedThePrice: 'Have quoted the price',
    CardPieInquirySheet: 'Card pie inquiry sheet',
    CustomerOrderNumberTrackingNumber: 'Customer order number tracking number',
    StateType: 'State type',
    BillFxNumber: 'Bill fx number',
    ProcessingComplete: 'Processing complete',
    TrackingNumber_1: 'Tracking number',
    DocumentNumberTrackingMainDocumentNumberTrackingDocumentNumberEnglishName: 'Document number tracking main document number tracking document number english name',
    TrackingDocumentNumber: 'Tracking document number',
    ResultOfClaim: 'Result of claim',
    SuccessfulClaim: 'Successful claim',
    GetTrackOrNot: 'Get track or not',
    ExtendedField: 'Extended field',
    GenerateFinancialStatisticalCharts: 'Generate financial statistical charts',
    GenerateABillingChart: 'Generate a billing chart',
    GenerateOrderStatisticsChart: 'Generate order statistics chart',
    ReportCoding: 'Report coding',
    ReportName: 'Report name',
    SelectOrderDate: 'Select order date',
    BiReportOfSupplierInterconnection: 'Bi report of supplier interconnection',
    OutOfService: 'Out of service',
    TrajectoryDescription: 'Trajectory description',
    TrackSupplier: 'Track supplier',
    TrackBlacklist: 'Track blacklist',
    VendorSubcode: 'Vendor subcode',
    SupplierTrackCoding: 'Supplier track coding',
    ConversionType: 'Conversion type',
    SystemTrajectoryCoding: 'System trajectory coding',
    TrajectoryTransformationInformation: 'Trajectory transformation information',
    WhetherMultipleOrderNumbersAreSupported: 'Whether multiple order numbers are supported',
    TrackSupplierInformation: 'Track supplier information',
    ParcelTrackDetails: 'Parcel track details',
    ParcelTrack: 'Parcel track',
    TheOrderNumberOfTheThirdpartyServiceProvider: 'The order number of the thirdparty service provider',
    Priority: 'Priority',
    PullState: 'Pull state',
    PushStatus: 'Push status',
    Abnormal: 'Abnormal',
    Edit: 'Edit',
    RePull: 'Re-Pull',
    SingleBindingTrackingNumberOfTheUploadPlane: 'Single binding tracking number of the upload plane',
    OrderNumberTrackingNumber: 'Order number tracking number',
    ExceptionMessage: 'Exception message',
    TimeoutPrompt: 'Timeout prompt',
    NotOverTime: 'Not over time',
    Overtime: 'Overtime',
    SmallPacketTrackTask: 'Small packet track task',
    ShippingOrder: 'Shipping order',
    AirOrder: 'Air order',
    PullNumber_1: 'Pull number',
    PullComplete: 'Pull complete',
    Nopull: 'Nopull',
    WaitingToBeDrawn: 'Waiting to be drawn',
    Unbind: 'Unbind',
    OrderNumberPackageNumberWarehouseLocationNumber: 'Order number package number warehouse location number',
    UntyingTime: 'Untying time',
    VoidOrNot: 'Void or not',
    Unbound: 'Unbound',
    Bound: 'Bound',
    NumberOfParcels: 'Number of parcels',
    TransshipmentDestinationBinCode: 'Transshipment destination bin code',
    AssociatedOrderNumber: 'Associated order number',
    Untie: 'Untie',
    ParcelOrderDetails_1: 'Parcel order details',
    UnpackOrNot: 'Unpack or not',
    TapeColor: 'Tape color',
    CustomsClearancePackageNumber: 'Customs clearance package number',
    CustomsClearancePackage: 'Customs clearance package',
    TimeTheTransferCarHasBeenDispatched: 'Time the transfer car has been dispatched',
    CustomsClearanceCustomerCode: 'Customs clearance customer code',
    Locations: 'Locations',
    MonetaryUnit: 'Monetary unit',
    CostExpense: 'Cost expense',
    ActualArrivalDate_1: 'Actual arrival date',
    ActualDepartureTime: 'Actual departure time',
    EstimatedTimeOfDeparture: 'Estimated time of departure',
    ConfigureCustomsClearance: 'Configure customs clearance',
    CustomsClearanceOrderNumber: 'Customs clearance order number',
    TheNumberOfTheThirdPartyPackage: 'The number of the third party package',
    SendToSite: 'Send to site',
    OriginatingSite: 'Originating site',
    LargePackageOrderNumber: 'Large package order number',
    BulkOrder: 'Bulk order',
    SupplierCancelsWaybill: 'Supplier cancels waybill',
    PushToSupplier: 'Push to supplier',
    WithdrawSubmission: 'Withdraw submission',
    ReviewTheOrder: 'Review the order',
    EstimatedNumberOfParcels: 'Estimated number of parcels',
    MailingAddressZipCode: 'Mailing address zip code',
    ConfigureCustomsClearancePackage: 'Configure customs clearance package',
    CustomsClearanceParcelOrder: 'Customs clearance parcel order',
    GrossIncome: 'Gross income',
    Printer_1: 'Printer',
    Copies_1: 'Copies',
    ImportANewTraceNumber: 'Import a new trace number',
    SystemCodeMasterOrderNumberCustomerNumberCmrnumber: 'System code master order number customer number cmrnumber',
    UpdateStatus: 'Update status',
    LeadinTime: 'Leadin time',
    WeighLargeBagsInBulk: 'Weigh large bags in bulk',
    Backorder: 'Backorder',
    ImportABatchOfSmallPackingCases: 'Import a batch of small packing cases',
    DownloadTheBatchSmallPackingCaseTemplate: 'Download the batch small packing case template',
    ImportBatchPacketWeighing: 'Import batch packet weighing',
    DownloadBatchPacketWeighingTemplate: 'Download batch packet weighing template',
    RecordType: 'Record type',
    ChangeTheMark: 'Change the mark',
    Encasement: 'Encasement',
    Weigh: 'Weigh',
    WaybillNumberAndCaseNumber: 'Waybill number and case number',
    Result: 'Result',
    PostSheet: 'Post sheet',
    NeutralFaceSheet: 'Neutral face sheet',
    DefaultSheet: 'Default sheet',
    Ticket: 'Ticket',
    ResultThisOperationHasBeenPerformed: 'Result this operation has been performed',
    SheetType: 'Sheet type',
    Operator_1: 'Operator',
    WaybillNumberPackageNumber: 'Waybill number package number',
    WeightKg: 'Weight kg',
    HeightCm: 'Height cm',
    WidthCm: 'Width cm',
    LengthInCm: 'Length in cm',
    TakeOutOfStorage: 'Take out of storage',
    PackingNumberPackageNumber: 'Packing number package number',
    HwbNumber: 'Hwb number',
    BulkExport: 'Bulk export',
    SelectiveDerivation: 'Selective derivation',
    CaseNumberOrderNumberWarehouseReceiptNumber: 'Case number order number warehouse receipt number',
    LastOperatingTime_1: 'Last operating time',
    ArrivalTime_1: 'Inventory time',
    WarehouseReceiptNumber: 'Warehouse receipt number',
    WarehouseInventoryDetails: 'Warehouse inventory details',
    ActualHeight: 'Actual height',
    ActualWidth: 'Actual width',
    ActualLength: 'Actual length',
    OrderHigh: 'Order high',
    SingleWidth: 'Single width',
    OrderLength: 'Order length',
    ForcedUpdate: 'Forced update',
    ClickConnectClient: 'Click connect client',
    Connected: 'Connected',
    CheckinTime: 'Checkin time',
    Error: 'Error',
    Lose: 'Lose',
    A: 'A',
    Predict: 'Predict',
    FrontTicketPackageNumber: 'Front ticket package number',
    OrganizationCode: 'Organization code',
    PushResult: 'Push result',
    TrackNumber: 'Tracking number',
    PushCompleteOrNot: 'Push complete or not',
    Source: 'Source',
    OrganizationalStructure: 'Organizational structure',
    TissueCoding: 'Tissue coding',
    PortOfShipment: 'Port of shipment',
    DepartureTime: 'Departure time',
    DestinationCountry: 'Destination country',
    PostalTrackingNumber: 'Postal tracking number',
    PushEmsBulkPackageData: 'Push ems bulk package data',
    Repush: 'Repush',
    RetryTimes: 'Retry times',
    NotProcess: 'Not process',
    SuccessfulProcessing: 'Successful processing',
    unhandled: 'Untreated',
    CollapsePanel: 'Collapse panel',
    TheCarouselWasOnTheMove: 'The carousel was on the move',
    Card: 'Card',
    PopoverIndicatesThePopupBox: 'Popover indicates the popup box',
    TooltipTextPrompts: 'Tooltip text prompts',
    DialogDialogBox: 'Dialog dialog box',
    StepsStepBar: 'Steps step bar',
    DropdownDropdownMenu: 'Dropdown dropdown menu',
    Breadcrumb: 'Breadcrumb',
    Tabs: 'Tabs',
    NavmenuIndicatesTheNavigationMenu: 'Navmenu indicates the navigation menu',
    Notification: 'Notification',
    MessageboxDisplaysABox: 'Messagebox displays a box',
    MessageMessagePrompt: 'Message message prompt',
    Loading: 'Loading',
    Alert: 'Alert',
    BadgeMark: 'Badge mark',
    Pagination: 'Pagination',
    TreeTreeControl: 'Tree tree control',
    ProgressProgressBar: 'Progress progress bar',
    TagTag: 'Tag tag',
    TableTable: 'Table table',
    FormForm: 'Form form',
    RateScore: 'Rate score',
    Upload: 'Upload',
    DatetimepickerDateAndTimeSelector: 'Datetimepicker date and time selector',
    DatepickerDateSelector: 'Datepicker date selector',
    TimepickerTimeSelector: 'Timepicker time selector',
    SliderSlider: 'Slider slider',
    SwitchSwitch: 'Switch switch',
    CascaderCascadeSelector: 'Cascader cascade selector',
    SelectSelector: 'Select selector',
    InputnumberIndicatesTheCounter: 'Inputnumber indicates the counter',
    InputInputBox: 'Input input box',
    CheckboxCheckbox: 'Checkbox checkbox',
    RadioOptionBox: 'Radio option box',
    ButtonButton: 'Button button',
    IconIcon: 'Icon icon',
    Typography: 'Typography',
    ColorColor: 'Color color',
    LayoutLayout: 'Layout layout',
    ComponentInteractionDocument: 'Component interaction document',
    Assets: 'Assets',
    Module: 'Module',
    TopNavigation: 'Top navigation',
    LateralNavigation: 'Lateral navigation',
    Navigation: 'Navigation',
    Steerable: 'Steerable',
    Efficiency: 'Efficiency',
    Feedback: 'Feedback',
    Accord: 'Accord',
    DesignPrinciple: 'Design principle',
    Guide: 'Guide',
    PreviousTicketNumber: 'Previous ticket number',
    Weight_1: 'Weight',
    HwbNumber_1: 'Hwb number',
    ScanFrame: 'Scan frame',
    ErrorDiffweight: 'Error diffweight',
    FrontweightFrontweight: 'Frontweight frontweight',
    ForcedReplacementWeight: 'Forced replacement weight',
    ForecastHeight: 'Forecast height',
    ForecastWidth: 'Forecast width',
    ChiefForecaster: 'Chief forecaster',
    ForecastWeight: 'Forecast weight',
    Lose_1: 'Lose',
    Complete_1: 'Complete',
    Actual: 'Actual',
    Predict_1: 'Predict',
    PreticketOrderNumber: 'Preticket order number',
    CustomerCode: 'Customer code',
    NoPacketOrderFoundPleaseConfirm: 'No packet order found please confirm',
    SheetInformation: 'Sheet information',
    PrintLabel: 'Print label',
    AggregateCoreWeight: 'Aggregate core weight',
    DeliveryDate_1: 'Delivery date',
    CustomsClearanceStatus: 'Customs clearance status',
    CrossborderInsurancePolicyInformation: 'Crossborder insurance policy information',
    ActualHeightCm: 'Actual height cm',
    ActualWidthCm: 'Actual width cm',
    ActualLengthCm: 'Actual length cm',
    ShippingOrder_1: 'Shipping order',
    ShipmentDetail: 'Shipment detail',
    SignatureForm: 'Signature form',
    CustomerNotice: 'Customer notice',
    OperationRequirement: 'Operation requirement',
    CustomerSplit: 'Customer split',
    CountryCode_1: 'Country code',
    PictureDisplay: 'Picture display',
    ProcessingTime: 'Processing time',
    WarehousingValuation: 'Warehousing valuation',
    OutgoingQuantity: 'Outgoing quantity',
    ShippingInformation: 'Shipping information',
    TaskDescription: 'Task description',
    TaskType: 'Task type',
    SynchronizationTask: 'Synchronization task',
    LatestTrack: 'Latest track',
    WeightActualWeight: 'Weight actual weight',
    HeightActualHeight: 'Height actual height',
    WidthActualWidth: 'Width actual width',
    LongActualLength: 'Long actual length',
    AdjustmentOrderNumber: 'Adjustment order number',
    IncomeAdjustmentDetail: 'Income adjustment detail',
    ChargeVolume: 'Charge volume',
    Capapod: 'Capapod',
    Cappai: 'Cappai',
    LogisticsPlan: 'Logistics plan',
    PrivateAddressOrNot: 'Private address or not',
    WhetherOverweightOrNot: 'Whether overweight or not',
    IsItTooLong: 'Is it too long',
    WhetherInterceptedOrNot: 'Whether intercepted or not',
    TrailerLicensePlateNumber: 'Trailer license plate number',
    EmptyTankWeight: 'Empty tank weight',
    SealNumber: 'Seal number',
    Shipowner: 'Shipowner',
    Contractor: 'Contractor',
    NumberOfCountedPieces: 'Number of counted pieces',
    InventoryDetails: 'Inventory details',
    CountTheJobTicketNumber: 'Count the job ticket number',
    InventoryTaskNumber: 'Inventory task number',
    TaskInformation: 'Task information',
    InventoryTask: 'Inventory task',
    InsuredShippingOrderSpacesOrLineBreaksAreAcceptable: 'Insured shipping order spaces or line breaks are acceptable',
    InsuranceOrder: 'Insurance order',
    PolicyRemarks: 'Policy remarks',
    PolicyStatus: 'Policy status',
    QuantityOfSmallPieces: 'Quantity of small pieces',
    BulkQuantity: 'Bulk quantity',
    DispatchTime: 'Dispatch time',
    ShippingDocument: 'Shipping document',
    WeighingTime: 'Weighing time',
    RelatedShipment: 'Related shipment',
    UpdateTheWeightOfTheLargeBag: 'Update the weight of the large bag',
    PushLargePackageOrder: 'Push large package order',
    ConfigurationInvoice: 'Configuration invoice',
    NuclearGravityTime: 'Nuclear gravity time',
    ThirdPartyTrackingNumber: 'Third party tracking number',
    NumberOfSmallPackages: 'Number of small packages',
    Insurance: 'Insurance',
    BillSending: 'Bill sending',
    AmendmentOfPolicy: 'Amendment of policy',
    SearchPolicy: 'Search policy',
    SubmissionOfPolicy: 'Submission of policy',
    OperationRequirementsAndTransferInformation: 'Operation requirements and transfer information',
    RecipientPostcode_1: 'Recipient postcode',
    InsuredDetails: 'Insured details',
    DestinationStation: 'Destination station',
    StationOfDeparture: 'Station of departure',
    WhetherToConfigureDocuments: 'Whether to configure documents',
    PacketAging: 'Packet aging',
    AgingOfEachWarehouse: 'Aging of each warehouse',
    SalesBi: 'Sales bi',
    PrerecordedTicket: 'Prerecorded ticket',
    InvoiceData: 'Invoice data',
    OrderBoxSize: 'Order box size',
    CustomerConfirmation: 'Customer confirmation',
    ExportOrder: 'Export order',
    ExportShipment: 'Export Shipment',
    ConfigureReceivingOrders: 'Configure receiving orders',
    ForceAllWarehousing: 'Force all warehousing',
    WmsWarehouse: 'Wms warehouse',
    WarehouseCode_1: 'Warehouse code',
    OrderDate_1: 'Order date',
    StatusOfCustomsDeclaration: 'Status of customs declaration',
    OwnedCustomer: 'Owned customer',
    GetTheReceiptNumber: 'Get the receipt number',
    CollectionBin: 'Collection bin',
    OrderNumberCustomerOrderNumberTransferNumberThirdpartyServiceOrderNumber: 'Order number customer order number transfer number thirdparty service order number',
    ExpressDeliveryNote: 'Express delivery note',
    OrderNumberFlightNumberPortOfDestination: 'Order number flight number port of destination',
    WarehouseTaskNumberCrateNumberOrderNumber: 'Warehouse task number crate number order number',
    SystemInformation: 'System information',
    SellProducts: 'Sell products',
    CustomerCode_1: 'Customer code',
    ServiceProviderBillNumber: 'Service provider bill number',
    JobStatus: 'Job status',
    JobInformation: 'Job information',
    PalletTask: 'Pallet task',
    On: 'On',
    CompleteInventory: 'Complete inventory',
    OpenCount: 'Open count',
    TimeInformation: 'Time information',
    WarehouseOrder: 'Warehouse order',
    ReturnDraft: 'Return draft',
    DestinationInformation: 'Destination information',
    DepartureInformation: 'Departure information',
    SettlementOfClaims: 'Settlement of claims',
    Examine: 'Examine',
    ReturnedCargo: 'Returned cargo',
    Shipment: 'Shipment',
    ShipmentRM: 'RM Shipment',
    ShippingNote: 'Shipping note',
    AssociatedTransportBill: 'Associated transport bill',
    Shipped: 'Shipped',
    WaitToBeShipped: 'Wait to be shipped',
    ShippingTime: 'Shipping time',
    SelectShippingTime: 'Select shipping time',
    ExpectedSigningTime: 'Expected signing time',
    EstimatedTimeOfArrival: 'Estimated time of arrival',
    EstimatedTimeOfDeparture_1: 'Estimated time of departure',
    OriginatingBin: 'Originating bin',
    ShippingWarehouse: 'Shipping warehouse',
    ProjectName_1: 'Project name',
    WhetherToSignAContractOrNot: 'Whether to sign a contract or not',
    LogisticsProviderCodeLogisticsProviderForShort: 'Logistics provider code logistics provider for short',
    LogisticsBusinessChineseName: 'Logistics business chinese name',
    ShortForLogisticsProvider: 'Short for logistics provider',
    LogisticsProviderCode: 'Logistics provider code',
    FbaPackage: 'Fba package',
    DeliveryCompletionDate: 'Delivery completion date',
    TransactionMethod: 'Transaction method',
    ModeOfTrade: 'Mode of trade',
    FbaOrderDetails: 'Fba order details',
    EstimatedTimeOfArrival_1: 'Estimated time of arrival',
    ContactInformation: 'Contact information',
    PayOrNot: 'Pay or not',
    CarryOrNot: 'Carry or not',
    FreightInformation: 'Freight information',
    DestinationWarehouseCoding: 'Destination warehouse coding',
    ReceivingAddress: 'Receiving address',
    ExportDeclarationForm: 'Export declaration form',
    PackingList: 'Packing list',
    InterceptParcel: 'Intercept parcel',
    WhetherItIsOutOfTheWarehouse: 'Whether it is out of the warehouse',
    SystemPackageNumber: 'System package number',
    ConfirmData: 'Confirm data',
    SalePriceCardTime: 'Sale price card time',
    SheetAddress: 'Sheet address',
    BillNumber: 'Bill number',
    PlannedTimeOfArrival: 'Planned time of arrival',
    ActualDepartureTime_1: 'Actual departure time',
    PlannedDepartureTime: 'Planned departure time',
    Path: 'Path',
    Range: 'Range',
    CargoTrack: 'Cargo track',
    PushTask: 'Push task',
    BookingTime: 'Booking time',
    PortOfTransshipment: 'Port of transshipment',
    CostService: 'Cost service',
    StowageVolume: 'Stowage volume',
    StowageWeight: 'Stowage weight',
    CustomerInformation: 'Customer information',
    BookedVolume: 'Booked volume',
    BookedWeight: 'Booked weight',
    NumberOfBookingPieces: 'Number of booking pieces',
    CabinetType: 'Cabinet type',
    RouteAbbreviation: 'Route abbreviation',
    ActualLoadingTime: 'Actual loading time',
    RecommendedLoadingTime: 'Recommended loading time',
    PriorityOrNot: 'Priority or not',
    BillOfLading: 'Bill of lading',
    CompleteStowage: 'Complete stowage',
    TrackTheBillOfLadingNumber: 'Track the bill of lading number',
    TheCardPieList: 'The card pie list',
    ActualDeliveryTime: 'Actual delivery time',
    MakeAnAppointmentForDeliveryTime: 'Make an appointment for delivery time',
    BookingNumber: 'Booking number',
    ExportList: 'Export list',
    EliminationOfCostAllocation: 'Elimination of cost allocation',
    CostAllocation: 'Cost allocation',
    OrderFulfillment: 'Order fulfillment',
    QuickEntry: 'Quick entry',
    GenerateBillOfLadingTemplate: 'Generate bill of lading template',
    StatusChange: 'Status change',
    OrderNumberBillOfLadingNumberMainOrderNumber: 'Order number bill of lading number main order number',
    OtherInformation: 'Other information',
    RelatedDocuments: 'Related documents',
    ImporterAndExporter: 'Importer and exporter',
    CustomerSupplier: 'Customer supplier',
    Receipt: 'Receipt',
    GeneralTrade: 'General trade',
    ThePrerecordHasBeenConfirmed: 'The prerecord has been confirmed',
    ThePrerecordWasNotConfirmed: 'The prerecord was not confirmed',
    InformationProvided: 'Information provided',
    WaitingForCustomerInformation: 'Waiting for customer information',
    Confirmed: 'Confirmed',
    ToBeConfirmed: 'To be confirmed',
    ToBeDeclared: 'To be declared',
    CustomsDeclarationFailure: 'Customs declaration failure',
    CustomsDeclarationCompleted: 'Customs declaration completed',
    WaitForAudit: 'Wait for audit',
    WaitForCustomsDeclaration: 'Wait for customs declaration',
    DocumentStatus: 'Document status',
    EliminationOfIncomeSharing: 'Elimination of income sharing',
    IncomeSharing: 'Income sharing',
    OrderNumberCustomerOrderNumberAddressRemarks: 'Order number customer order number address remarks',
    DestinationBin: 'Destination bin',
    Collection: 'Collect money',
    IncomeSharingOrNot: 'Income sharing or not',
    ReceivingOrderStatus: 'Receiving order status',
    WhetherTheGoodsHaveArrived: 'Whether the goods have arrived',
    SynchronousOrder: 'Synchronous order',
    MaritimePriorityBillNumber: 'Maritime priority bill number',
    SeaWaybillNumber: 'Sea waybill number',
    SynchronousState: 'Synchronous state',
    SyncWarehouseState: 'Synchronous warehouse state',
    SalesPriceCard: 'Sales price card',
    SynchronizeRevenueToSuppliers: 'Synchronize revenue to suppliers',
    SynchronizeOrdersToSuppliers: 'Synchronize orders to suppliers',
    ConfigureTheCardDeliveryOrder: 'Configure the card delivery order',
    AllocationOfShippingPriority: 'Allocation of shipping priority',
    TrackingNumberCustomerTrackingNumberPackageNumberTransferNumber: 'Tracking number customer tracking number package number transfer number',
    ConfirmData_1: 'Confirm data',
    SyncToSupplier: 'Sync to supplier',
    NumberOfPackages: 'Number of packages',
    ConfirmedChargeVolume: 'Confirmed charge volume',
    failure: 'Processing failure',
    Unsynchronized: 'Unsynchronized',
    ModificationOfOverseasPosition: 'Modification of overseas position',
    ReserveWarehouseReceipt: 'Reserve warehouse receipt',
    UpdateImport: 'Update import',
    CreateAnExpressDeliveryNote: 'Create an express delivery note',
    BatchUpdateImport: 'Batch update import',
    ImportTransferOrderExcel: 'Import transfer order excel',
    OrderNumberPackageNumberSystemPackageNumberTransferNumber: 'Order number package number system package number transfer number',
    WhetherToCombinePackages: 'Whether to combine packages',
    PushTrunkTraffic: 'Push trunk traffic',
    CostPricing: 'Cost pricing',
    PushMasterOrder: 'Push To Imile',
    DC: 'Delivery completed',
    HaveReturnedToTheContainer: 'Have returned to the container',
    ContainerRemoved: 'Container removed',
    DeliveryCompleted_1: 'Delivery completed',
    OutOfStorage_2: 'Out of storage',
    StowageComplete: 'Stowage complete',
    Stowage: 'Stowage',
    DeliveryHasBeenBooked: 'Delivery has been booked',
    HaveBeenSentToOverseasWarehouse: 'Have been sent to overseas warehouse',
    HaveBeenReleased: 'Have been released',
    Inspected: 'Inspected',
    ForecastList: 'Forecast list',
    OrderNumberAndCabinetNumber: 'Order number and cabinet number',
    LoadingTime: 'Loading time',
    MeasuredWeight: 'Measured weight',
    ParsPili: 'Pars pili',
    RouteAbbreviation_1: 'Route abbreviation',
    AirRoute: 'Air route',
    ShippingInformation_1: 'Shipping information',
    BillOfLadingStatus: 'Bill of lading status',
    ImportBinding: 'Import binding',
    PriorityTrajectoryChange: 'Priority trajectory change',
    ImportShippingPrioritySingleBindOrder: 'Import shipping priority single bind order',
    ImportPriorityShippingOrder: 'Import priority shipping order',
    TrackingBillOfLadingNumberImportDeclarationNumberExportDeclarationNumber: 'Tracking bill of lading number import declaration number export declaration number',
    TrackingBillOfLadingNumber: 'Tracking bill of lading number',
    BatchModification: 'Batch modification',
    OriginalShipmentNumber: 'Original shipment number',
    SortingCode: 'Sorting code',
    CardDeliveryNumber: 'Card delivery number',
    StowagePreview: 'Stowage preview',
    ToWhichTheReceiptBelongs: 'To which the receipt belongs',
    RecoveryDocument: 'Recovery document',
    VoidedDocument: 'Voided document',
    GeneratorSublist: 'Generator sublist',
    TransportationCompleted: 'Transportation completed',
    NumberOriginatingStationNumberOfDestinationSeaWaybill: 'Number originating station number of destination sea waybill',
    AssociateTheParentNumber: 'Associate the parent number',
    TotalCost_1: 'Total cost',

    OperationRecord: 'Operation record',
    FieldName: 'Field Name',
    BeforeModification: 'Before Modification',
    AfterModification: 'After Modification',
    Collate: 'Collate',
    AccountBalance_1: 'Account balance',
    OriginalPaymentAmount_1: 'Original payment amount',
    BasicPrice: 'Basic price',
    StatementNumber: 'Statement number',
    PaymentAmount_1: 'Payment amount',
    BalanceWrittenOff: 'Balance written off',
    Amount: 'Amount',
    ChargeUp: 'Charge up',
    PositiveAndNegativeDifference: 'Positive and negative difference',
    TheAmountIs0: 'The amount is 0',
    CancelEntry: 'Cancel entry',
    BookkeepingCompleted: 'Bookkeeping completed',
    VoucherNumberBusinessDocumentCustomerOrderNumberStatementNumber: 'Voucher number business document customer order number statement number',
    WhetherToAccount: 'Whether to account',
    WhetherToIssuePaymentSlip: 'Whether to issue payment slip',
    AdjustOrNot: 'Adjust or not',
    CheckWhether: 'Check whether',
    DocumentInformation: 'Document information',
    PendingFinancialReview: 'Pending financial review',
    PendingManagerReview: 'Pending manager review',
    OriginalPaymentAmount_2: 'Original payment amount',
    BillNumberVoucherNumberCustomerOrderNumberBusinessDocument: 'Bill number voucher number customer order number business document',
    BillNumberCustomerBillNumberTrackingNumberBusinessBillNumberAdjustmentBillNumber: 'Bill number customer bill number tracking number business bill number adjustment bill number',
    SmallPackageProduct: 'Small package product',
    TransportProduct: 'Transport product',
    BillNumberStatementNumberBillOfLadingNumberContainerNumberTrackingNumber: 'Bill number statement number bill of lading number container number tracking number',
    IncomeAdjustmentSheet: 'Income adjustment sheet',
    ConfirmedVestingPeriod: 'Confirmed vesting period',
    DateOfOccurrence: 'Date of occurrence',
    NonvestingPeriod: 'Nonvesting period',
    Unconfirm: 'Unconfirm',
    SetTheVestingPeriod: 'Set the vesting period',
    RegenerateDetail: 'Regenerate detail',
    TheNameIsReferredToAsSwiftBranchCodeOfTheAccount: 'The name is referred to as swift branch code of the account',
    WhetherToConfirmTheVestingPeriod: 'Whether to confirm the vesting period',
    VestingPeriod: 'Vesting period',
    BalanceWrittenOffByPayment: 'Balance written off by payment',
    AccountPeriodInterval: 'Account period interval',
    Unconfirmed: 'Unconfirmed',
    None: 'None',
    ThereAre: 'There are',
    BalanceWrittenOff_1: 'Balance written off',
    BillingDetailSummary: 'Billing detail summary',
    InvoiceNumberPaymentDocumentCurrency: 'Invoice number payment document currency',
    AdjustBillNumberStatementNumberBusinessDocumentCabinetNumberTrackingNumberBillOfLadingNumber: 'Adjust bill number statement number business document cabinet number tracking number bill of lading number',
    DownloadTheBillImportTemplate: 'Download the bill import template',
    NumberOfPositiveAndNegativeDifferences: 'Number of positive and negative differences',
    AmountDifference: 'Amount difference',
    HaveProduct: 'Have product',
    UpdateCustomerProducts: 'Update customer products',
    ExpenseBillNumberCustomerBillNumberCabinetTrackingNumberThirdpartyServiceBillNumber: 'Expense bill number customer bill number cabinet tracking number thirdparty service bill number',
    IsItComplete: 'Is it complete',
    CurrentAmount_1: 'Current amount',
    EnterTheNameForShortAccountBranchCodeSwift: 'Enter the name for short account branch code swift',
    BankName: 'Bank name',
    ExpenseType: 'Expense type',
    AssociationId: 'Association id',
    BillNumber_1: 'Bill number',
    BillWriteoffRecord: 'Bill writeoff record',
    CancelWriteoff: 'Cancel writeoff',
    BalanceOfInvoiceWrittenOff: 'Balance of invoice written off',
    CancelId: 'Cancel id',
    BillingSummaryVerificationRecord: 'Billing summary verification record',
    WriteoffRecord: 'Writeoff record',
    Drawee: 'Drawee',
    EnterTransactionNumberPayerPayerPayerAccountSummaryInvoiceBillingNumberQuery: 'Enter transaction number payer payer payer account summary invoice billing number query',
    Receipts: 'Receipts',
    Amount_1: 'Amount',
    CheckWhetherTheBalanceIsNot0: 'Check whether the balance is not 0',
    ImportAndExportBill: 'Import and export bill',
    TransactionNumberPayeeSummaryOfPayeeAccountNumber: 'Transaction number payee summary of payee account number',
    ImportBill: 'Import bill',
    AssociatedStatement: 'Associated statement',
    WriteOffTheBillingDetails: 'Write off the billing details',
    BusinessReceipt: 'Business receipt',
    TotalSummaryAccountBalance: 'Total summary account balance',
    TotalSummaryBookkeepingAmount_1: 'Total summary bookkeeping amount',
    SalesProfitStatement: 'Sales profit statement',
    FinancialSummary: 'Financial summary',
    TotalPayable: 'Total payable',
    TotalReceivable: 'Total receivable',
    Salesman: 'Salesman',
    ActualTime_1: 'Actual time',
    EstimatedTime_1: 'Estimated time',
    TypeOfOwnership: 'Type of ownership',
    AttributeType: 'Attribute type',
    LimitFieldChineseName: 'Limit field chinese name',
    LimitField: 'Limit field',
    AttributeRestrictionConfiguration: 'Attribute restriction configuration',
    ProductCostItem: 'Product cost item',
    PriceListVersion: 'Price list version',
    ProtocolSchemeName: 'Protocol scheme name',
    PriceType: 'Price type',
    ProductPriceListVersionRemarks: 'Product price list version remarks',
    SynchronousWarehouseService: 'Synchronous warehouse service',
    PushManifest: 'Push manifest',
    WhetherToPrintASheet: 'Whether to print a sheet',
    TailLogisticsProviderCode: 'Tail logistics provider code',
    ServiceType: 'Service type',
    ProductPartitionCode: 'Product partition code',
    EnableOrNot: 'Enable or not',
    ChargingRules: 'Charging rules',
    CombinedChargeRatio: 'Combined charge ratio',
    CombinedExpenses: 'Combined expenses',
    ProductPartitionRestriction: 'Product partition restriction',
    PostcodeRestriction: 'Postcode restriction',
    EffectiveCountry: 'Effective country',
    ContainAlpNum: 'Contain English And Number',
    DefaultValue: 'Default value',
    RequiredOrNot: 'Required or not',
    WithoutNumbers: 'Without numbers',
    EnglishFree: 'English free',
    NoChinese: 'No chinese',
    SuffixRestriction: 'Suffix restriction',
    AllowCharacter: 'Allow Character',
    MaskCharacter: 'Mask character',
    PrefixRestriction: 'Prefix restriction',
    FieldLength: 'Field length',
    CheckType: 'Check type',
    Encoding: 'Encoding',
    AttributeRestriction: 'Attribute restriction',
    ExtendedAttribute: 'Extended attribute',
    WhetherToManuallyConfirm: 'Whether to manually confirm',
    TrackName: 'Track name',
    TriggerCondition: 'Trigger condition',
    Subproduct: 'Subproduct',
    CombinationProduct: 'Combination product',
    MainProductName: 'Main product name',
    ProductStatus: 'Product status',
    ProductDescription: 'Product description',
    ChargingUnitCarry: 'Charging unit carry',
    PolicyType: 'Policy type',
    PushorderNumberType: 'Pushorder number type',
    PricingMethod: 'Pricing method',
    SheetType_1: 'Sheet type',
    CostOrNot: 'Cost or not',
    RevenueOrNotPricing: 'Revenue or not pricing',
    VirtualProductOrNot: 'Virtual product or not',
    AssociatedProduct: 'Associated product',
    ProductInformation: 'Product information',
    PriceVersion: 'Price version',
    ProtocolSolutionProduct: 'Protocol solution product',
    ProtocolSchemeCoding: 'Protocol scheme coding',
    DetailsOfServicePrices: 'Details of service prices',
    MaskWord: 'Mask word',
    SynchronousTransportMode: 'Synchronous transport mode',
    SeaTransport: 'Sea transport',
    WarehouseInTransfer: 'Warehouse Inbound Transfer',
    WarehouseOutTransfer: 'Warehouse Outbound Transfer',
    AirFreight: 'Air freight',
    Dabao: 'Dabao',
    ProductSalesPricing: 'Product sales pricing',
    ProductInterceptRule: 'Product Intercept Rule',
    ProtocolScheme: 'Protocol scheme',
    ServiceStatus: 'Service status',
    ImportCustomersInBulk: 'Import customers in bulk',
    ProductTrajectoryRule: 'Product trajectory rule',
    RateCoefficient: 'Rate coefficient',
    Rate: 'Rate',
    EndOfValue: 'End of value',
    ValueThreshold: 'Value threshold',
    InsuranceProduct: 'Insurance product',
    ImportAddress: 'Import address',
    ReceivingTime: 'Receiving time',
    ChineseAddressDetails: 'Chinese address details',
    SupplierWarehouseCode: 'Supplier warehouse code',
    WhetherToSynchronizeWithSuppliers: 'Whether to synchronize with suppliers',
    ShippingAddressType: 'Shipping address type',
    AddressType: 'Address type',
    DomesticWarehouseAddress: 'Domestic warehouse address',
    OverseasReturnerAddress: 'Overseas Returner Address',
    ConsigneeOfBillOfLading: 'Consignee of bill of lading',
    companyName: 'Company name',
    OpenOrNot: 'Open or not',
    NotifypartyForShort: 'Notifyparty for short',
    NotifypartyCoding: 'Notifyparty coding',
    PortType: 'Port type',
    PortName: 'Port name',
    PortCode: 'Port code',
    Harbor: 'Harbor',
    SalesStaff: 'Sales staff',
    BatchShutdown: 'Batch shutdown',
    BatchEnable: 'Batch enable',
    YesWalletManagement: 'Yes wallet management',
    tax: 'Duty number',
    AccountManager: 'Account manager',
    CompanyCode: 'Company code',
    ShortForConsignee: 'Short for consignee',
    ConsigneeCodeOfBillOfLading: 'Consignee code of bill of lading',
    CompanyTaxNumber: 'Company tax number',
    CompanyAddress: 'Company address',
    SubordinateDepartment: 'Subordinate department',
    InternalUserOrNot: 'Internal user or not',
    UserManagement: 'User management',
    SubsidiaryCompany: 'Subsidiary company',
    CustomerClassification: 'Customer classification',
    City: 'City',
    Province: 'Province',
    FirstDeliveryTime: 'First delivery time',
    AccountOpeningTime: 'Account opening time',
    BillingName: 'Billing name',
    BillingInformation: 'Billing information',
    AddressClientRestriction: 'Address client restriction',
    TransshipmentWarehouseForShort: 'Transshipment warehouse for short',
    contactPhone: 'Contact number',
    PerformReconciliationManually: 'Perform reconciliation manually',
    GenerationTime: 'Generation time',
    IntransitInventoryVariance: 'Intransit inventory variance',
    AvailableInventoryVariance: 'Available inventory variance',
    ThirdPartyAvailableInventory: 'Third party available inventory',
    SystemAvailableInventory: 'System available inventory',
    QuantityOfReservedChanges: 'Quantity of reserved changes',
    ChangeQuantityInTransit: 'Change quantity in transit',
    QuantityChangeInHand: 'Quantity change in hand',
    QuantityChangeStartInHand: 'Quantity change start in hand',
    QuantityChangeEndInHand: 'Quantity change end in hand',
    QuantityBeforeChange: 'Quantity before change',
    QuantityAfterChanged: 'Quantity after changed',
    InventoryReconciliation: 'Inventory reconciliation',
    WhetherToSynchronizeInventory: 'Whether to synchronize inventory',
    HoldingStock: 'Holding stock',
    availableQty: 'Available quantity',
    unsalableQty: 'Unsalable quantity',
    StockInTransit: 'Stock in transit',
    TradeName: 'Trade name',
    Product_1: 'Product',
    SpecifiedDeduction: 'Specified deduction',
    WhetherATrayHasBeenDialed: 'Whether a tray has been dialed',
    QuantityOfPallets: 'Quantity of pallets',
    ContainerType: 'Container type',
    ContainerNumber: 'Container number',
    LogisticsTrackingNumber: 'Logistics tracking number',
    WeightKg_1: 'Weight kg',
    HeightCm_1: 'Height cm',
    WidthCm_1: 'Width cm',
    LengthInCm_1: 'Length in cm',
    VolumeM_1: 'Volume m³',
    SynchronizationCancellation: 'Synchronization cancellation',
    GetDetails: 'Get details',
    PrintedBoxMark: 'Printed box mark',
    SynchronizeTheWarehouseEntryTicket: 'Synchronize the warehouse entry ticket',
    OrderNumberContactFlightNumberPortOfDeparturePortOfDestination: 'Order number contact flight number port of departure port of destination',
    MaterialFlow: 'Material flow',
    PickComplete: 'Pick complete',
    CodeNameEnglishProductNameChineseProductName: 'Code name english product name chinese product name',
    SynchronousWarehouse: 'Synchronous warehouse',
    CustomersAndResults: 'Customers and results',
    WorkOrderProblem: 'Work order problem',
    FbaOrder: 'Fba order',
    WarmReminder: 'Warm reminder',
    RevenueEntryTemplate: 'Revenue entry template',
    CostEntryTemplate: 'Cost entry template',
    PushTime: 'Push time',
    TrajectoryChinese: 'Trajectory chinese',
    TrajectoryCoding: 'Trajectory coding',
    PushFailure: 'Push failure',
    PushComplete: 'Push complete',
    ToBePushed: 'To be pushed',
    DoNotPush: 'Do not push',
    PacketLocus: 'Packet locus',
    ProcessingConclusion: 'Processing conclusion',
    Offer: 'Offer',
    BatchReply: 'Batch reply',
    AbnormalOrderNumberOrderNumberShipmentNumberContainerNumber: 'Abnormal order number order number shipment number container number',
    CompleteOrNot: 'Complete or not',
    AbnormalFeedback: 'Abnormal feedback',
    NewCases: 'New cases',
    WorkOrderNumberAndOrderNumber: 'Work order number and order number',
    TaskContent: 'Task content',
    ImportUpdate: 'Import update',
    DownloadUpdateTemplate: 'Download update template',
    StatusModification: 'Status modification',
    CompleteTheQuotation: 'Complete the quotation',
    ModifiedState: 'Modified state',
    ValueaddedServiceOrderNumberOrderNumber: 'Valueadded service order number order number',
    ValueaddedServiceList: 'Valueadded service list',
    ProductCodeNameOfGoodsDeclaredInChineseNameOfGoodsDeclaredInEnglish: 'Product code name of goods declared in chinese name of goods declared in english',
    ActualStorageAge: 'Actual storage age',
    ShelfTime: 'Shelf time',
    QuantityOfStockInTheLibrary: 'Quantity of stock in the library',
    LibraryAgeCalculationDate: 'Library age calculation date',
    StorageAgeManagement: 'Storage age management',
    ShelfLotNumber: 'Shelf lot number',
    WarehouseEntryNumber: 'Warehouse entry number',
    InventoryBatchManagement: 'Inventory batch management',
    WarehouseCustomer: 'Warehouse customer',
    UnitPriceOfWarehouseRent: 'Unit price of warehouse rent',
    TheStorageAgeIntervalEnds: 'The storage age interval ends',
    BeginningOfLibraryAgeInterval: 'Beginning of library age interval',
    CopeWith: 'Cope with',
    Receivable: 'Receivable',
    ConfigurationOfWarehouseRentChargingRules: 'Configuration of warehouse rent charging rules',
    WarehousingCharge: 'Warehousing charge',
    TotalVolumeCbm: 'Total volume cbm',
    TotalNumberOfGoods: 'Total number of goods',
    WarehouseRentCalculationDate: 'Warehouse rent calculation date',
    WarehouseRentalNumber: 'Warehouse rental number',
    WarehouseRentalOrderManagement: 'Warehouse rental order management',
    modifyConfirmationData: 'Modify confirmation data',
    CustomerRequirement: 'Customer requirement',
    CaptureSingleTaskDetails: 'Capture single task details',
    SynchronizeWarehouseTaskDetails: 'Synchronize warehouse task details',
    QuantityReturned: 'Quantity returned',
    SalePriceOfReturnedGoods: 'Sale price of returned goods',
    CurrencyOfSaleOfReturnedGoods: 'Currency of sale of returned goods',
    ReturnedGoods: 'Returned goods',
    ReturnItemDetails: 'Return item details',
    ReasonForReturn: 'Reason for return',
    ReturnOrder: 'Return order',
    BulkShipmentExtension: 'Bulk shipment extension',
    isTenCarry: 'Is it 10KG carry',
    RequestDate: 'Request Date',
    SynchronousAcknowledgementData: 'Synchronous acknowledgement data',
    PublishedPrice: 'Published price',
    ChargePrice: 'Charge price',
    ShopTypeStar: 'Shop type*',
    DeclaredNameenStar: 'Declared Name en*',
    DeclaredNamecnStar: 'Declared Name cn*',
    Texture: 'Texture*',
    ItemUse: 'Item use*',
    BrandStar: 'Brand*',
    CustomsCodeStar: 'HS CODE*',
    expectedStatuteOfLimitations: 'Expected statute of limitations',
    sailingScheduleReference: 'Sailing Schedule Reference',
    orderProducts: 'Order Products',
    ReturnOrderNumber: '退件订单号',
    ReturnReason: 'Return reason',
    ReturnOrderType: 'Return order type',
    ReturnOrderService: 'Return order service',
    ReturnOrderStatus: 'Return order status',
    ReturnOrderConfirmReceive: 'Confirm receipt',
    ReturnOrderArriveWarehouse: 'Arrive warehouse',
    ReturnOrderArriveApprove: 'Approve Order',
    ReturnOrderConfirmCancel: 'Confirm cancellation',
    ReturnOrderSearchHolder: 'Search holder',
    ReturnOrderSubmitTime: 'Submit time',
    ReturnOrderArrivalTime: 'Arrival time',
    ReturnOrderReceiveTime: 'Receive time',
    ReturnOrderShipmentList: 'Return Shipment list',
    basicOrderInformation: 'Basic Order Information',
    ParcelNum: 'Parcel No.*',
    ParcelQtyStar: 'Parcel Qty*',
    ParcelLengthStar: 'Parcel Length(CM)*',
    ParcelWidthStar: 'Parcel Width(CM)*',
    ParcelHeightStar: 'Parcel Height(CM)*',
    ParcelWeightStar: 'Parcel Weight(KG)*',
    ItemQtyStar: 'Item Qty*',
    PackingQty: 'Packing quantity',
    receiveQty: 'Quantity received',
    DeclaredPriceStar: 'Declared Price*（USD）',
    ValueAdded: 'Value*',
    IsElectrified: 'Is electrified',
    IsMagnetic: 'Is magnetic',
    InterceptRuleName: 'Intercept Rule Name',
    IsMarked: 'Is Marked',
    RuleName: 'Rule name',
    IsIntercept: 'Is Intercept',
    CompletedTime: 'Completed Time',
    pleaseEnterValidNumber: 'Please Enter Valid Number',
    currencyAccount: 'Currency Account',
    expiryTime: 'Expiry Time',
    approveTemporaryQuota: 'Apply for Temporary Quota',
    approveFixedQuota: 'Apply for Fixed Quota',
    pendingApprove: 'Pending Approval',
    approvedRejected: 'Approval Rejected',
    effectiveApprove: 'Approval Effective',
    expiredApprove: 'Approval Expired',
    creditType: 'Credit Type',
    TemporaryQuota: 'Temporary Quota',
    FixedQuota: 'Fixed Quota',
    approver: 'Approver',
    approveTime: 'Approval Time',
    SKUPieces: 'SKU Pieces',
    PreSKUPieces: 'Pre SKU Pieces',
    third_party_warehouse_code: 'Platform Warehouse Code',
    local_address_num: 'Local Warehouse Code',
    local_address_name: 'Local Warehouse Name',
    ThirdPartyWarehouseMapping: 'Third Party Warehouse Mapping',
    is_new_sku: 'Is New SKU',
    recipientInfo: 'Recipient Info (Country/State/City)',
    postcodeLimit: 'Postcode Limit',
    carrierBrands: 'Carrier Brands',
    orderTotal: 'Order Total Amount',
    goodsTotal: 'Total Goods Quantity',
    isSingle: 'Is Single Product',
    ruleName: 'Rule Name',
    orderProcess: 'Order Process',
    submitDirectly: 'Submit Order Directly',
    manualReview: 'Manual Review Required',
    postcodeTip1: 'Note: Postcode supports fuzzy match, use * for wildcard; use ~ for range.',
    postcodeTip2: 'e.g. 78987; use 7* to match',
    postcodeTip3: 'e.g. 200~300; matches 200345, letter postcodes do not support range',
    minAmount: 'Min Amount',
    maxAmount: 'Max Amount',
    yes: 'Yes',
    no: 'No',
    range: 'Range',
    specifiedRange: 'Specified Range',
    select: 'Select',
    dataLoadFailed: 'Data loading failed',
    unknownError: 'Unknown error',
    condition: 'Condition',
    selectCarrierBrands: 'Select Carrier Brands',
    orderAmountRange: 'Order Amount Range',
    limitPostcode: 'Limit Postcode',
    recipientInfoDialog: 'Recipient Info',
    country: 'Country',
    city: 'City',
    state: 'State',
    addPostcode: 'Add Postcode',
    GoodsQuantity: 'Goods Quantity',
    changeBefore: 'Change Before',
    changeAfter: 'Change after',
    GoodsWeight: 'Goods Weight',
    ChangedParcelCustomerOrder: 'Changed ParcelCustomerOrder',
    TriggerTailLogistics: 'Trigger Tail Logistics',
    Serial_Number: 'Serial Number',
    OrderNmber: 'Ordern Nmber',
    CustomerName: 'Customer Name',
    LoadingPort: 'Port of loading',
    ReceivingPlace: 'Receiving Place',
    Notifier: 'Notifier',
    CustomerContact: 'Customer Contact person',
    CustomerPhone: 'Customer Phone',
    CustomerFax: 'Customer Fax',
    CustomerQq: ' Customer QQ',
    CustomerEmail: 'Customer Email',
    PaymentTerms: 'Payment Terms',
    ConsolidationRemarks: 'Consolidation Remarks',
    LoadingRemarks: 'Loading Remarks',
    DocumentRemarks: 'Document Remarks',
    InvoiceRemarks: 'Invoice Remarks',
    LetterAuthorizationUpload: 'Letter of Authorization upload',
    shipping_marks: 'Shipping Marks',
    packages: 'Packages',
    packagesunit: 'Packages Unit',
    syncSuccessime: 'Sync Success Time',
    intercepting: 'Intercepting',
    DelayedScan: 'DelayedScan',
    LastMileDocumentExchange: 'LastMileDocumentExchange',
    PackagePacking: 'PackagePacking',
    WarehouseDispatch: 'WarehouseDispatch',
    DomesticTransit: 'DomesticTransit',
    ExportCustomsInspection: 'ExportCustomsInspection',
    ExportCustomsDeclaration: 'ExportCustomsDeclaration',
    MainLineDeparture: 'MainLineDeparture',
    MainLineArrival: 'MainLineArrival',
    ImportCustomsInspection: 'ImportCustomsInspection',
    CustomsRelease: 'CustomsRelease',
    OverseasInTransit: 'OverseasInTransit',
    ArrivedatDestinationCountry: 'ArrivedatDestinationCountry',
    FinalDelivery: 'FinalDelivery',
    FlightDelay: 'FlightDelay'
  },
  common: {
    // header---------------------
    sureResetPassword: 'Are you sure to reset the password?',
    TheResetPassword: 'The reset password is:123456',
    ResetPassword: 'Reset password',
    select: 'please choose',
    enter: 'please enter',
    radioTrue: 'true',
    radioFalse: 'false',
    startTime: 'starting time',
    startMonth: 'start month',
    startDate: 'start date',
    endTime: 'end time',
    endMonth: 'end month',
    endDate: 'end date',
    to: 'to',
    amount: 'amount',
    selectDate: 'select date',
    selectDateTime: 'select time',
    noBlank: `can't be empty`,
    sure: 'Sure',
    cancel: 'cancel',
    clear: 'Clear',
    search: 'search',
    condition: 'whether to bring in external filtering conditions:',
    oneRecord: 'one line represents one record',
    batchSearch: 'Multiple queries',
    trackCode: 'trakc code',
    pieces: 'pieces',
    date: 'date',
    export: 'export',
    exportAll: 'export all',
    gettingData: 'Getting data, please wait...',
    apiNotFound: 'API address not found, unable to get all data',
    getDataFailed: 'Failed to get data',
    exportSuccess: 'Successfully exported {count} records!',
    exportFailed: 'Export failed',
    allData: 'All Data',
    create: 'create',
    edit: 'edit',
    clickToUpload: 'click to upload',
    // 列表---------------------
    modify: 'modify',
    download: 'download',
    setting: 'setting',
    require: 'require',
    unRequire: 'unRequire',
    // 详情---------------------
    save: 'save',
    saveAndedit: 'modify',
    delete: 'delete',
    cancle: 'cancle',
    close: 'close',
    total: 'total',
    operating: 'operating',
    clickupload: 'click to upload',
    add: 'add',
    addressee: 'addressee',
    addresser: 'addresser',
    simple: 'Simple Product List',
    complete: 'Complete product list',
    with: 'with',
    required: ' is required',
    iffill: 'If you fill in the package information, please be sure to manually summarize the cargo information',
    aggregate: 'Aggregate cargo information',
    numbersizes: 'number&size',
    packagesummary: 'Package summary',
    customerorder: 'Customer order',
    goodsummary: 'merchandise summary',
    switchType: 'Switching type will clear existing product packages',
    detailFail: 'Failed to get details, please try again',
    detailFailFreash: 'Failed to get details, please refresh the page and try again~',
    norightloading: 'The page information is not loaded correctly, please refresh and try again',
    modifysuccess: 'Successfully modified',
    modifyfail: 'Failed modified',
    addsuccess: 'added successfully',
    addfail: 'Add failed',
    delecomfirm: 'confirm delete ?',
    tips: 'Tips',
    deletesuccess: 'successfully deleted',
    deletefail: 'failed to delete',
    casenumber: 'case No.',
    weight: 'weight',
    length: 'length',
    width: 'width',
    height: 'height',
    uploadedsuccess: 'Uploaded successfully',
    uploadedfail: 'upload failed',
    importsusses: 'Import succeeded!',
    attention: 'Attention',
    // 收发件人
    address_num: 'address code',
    contact_name: 'contact',
    company_name: 'company name',
    contact_email: 'email',
    contact_phone: 'phone',
    country_code: 'country code',
    state_code: 'province/state code',
    city_code: 'city code',
    postcode: 'postcode',
    house_no: 'house No.',
    OceanOrderNum: 'Ocean order number',
    address_one: 'address one',
    address_two: 'address two',
    async: 'is async',
    fold: 'fold',
    unfold: 'unfold',
    deleteSuccess: 'Delete Successful'
  }
}

export default en
