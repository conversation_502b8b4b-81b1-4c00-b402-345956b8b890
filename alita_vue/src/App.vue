<template>
  <div v-loading="fullLoading" id="app">
    <div v-if="fullLoading2" class="custom-loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingText2 }}</div>
      </div>
    </div>
    <router-view/>
    <calcutor />
  </div>
</template>

<script>
import calcutor from '@/components/Calculator'

export default {
  name: 'App',
  components: {
    calcutor
  },
  data() {
    return {
      fullLoading: false,
      fullLoading2: false,
      loadingText2: ''
    }
  },
  watch: {
    '$route'(n, o) {
      this.fullLoading = false
    }
  },
  created() {
    this.bus.$on('fullLoading', (show) => {
      this.fullLoading = show
    })
    this.bus.$on('fullLoading2', (show, text) => {
      this.fullLoading2 = show
      if (text) {
        this.loadingText2 = text
      } else {
        this.loadingText2 = ''
      }
    })
  }
}
</script>

<style>
.custom-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

.loading-text {
  color: #606266;
  font-size: 14px;
  margin-top: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
