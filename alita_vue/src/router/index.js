// import Vue from 'vue'
import Router from 'vue-router'
import i18n from '@/i18n'

// Vue.use(Router)

/* Layout */
import Layout from '../views/layout/Layout'

/**
* hidden: true                   if `hidden:true` will not show in the sidebar(default is false)
* alwaysShow: true               if set true, will always show the root menu, whatever its child routes length
*                                if not set alwaysShow, only more than one route under the children
*                                it will becomes nested mode, otherwise not show the root menu
* redirect: noredirect           if `redirect:noredirect` will no redirect in the breadcrumb
* name:'router-name'             the name is used by <keep-alive> (must set!!!)
* meta : {
    title: 'title'               the name show in submenu and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar,
  }
**/

export const constantRouterMap = [
  { path: '/track',
    component: () => import('@/views/track/index'),
    hidden: true
  },
  { path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/errorPage/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/errorPage/401'),
    hidden: true
  },
  {
    path: '/wechat/supplier',
    component: () => import('@/views/wechat/index'),
    hidden: true
  },
  {
    path: '/sys/deploy',
    component: () => import('@/views/baseInfo/deploy/index'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index/index'),
        name: i18n.t('menu.home'),
        meta: { title: i18n.t('menu.home'), icon: 'index', noCache: true, en_flag: 'home' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'center',
        component: () => import('@/views/system/user/center'),
        name: i18n.t('menu.personalCenter'),
        meta: { title: i18n.t('menu.personalCenter'), icon: 'user', en_flag: 'personalCenter' }
      }
    ]
  },
  {
    path: '/tracking',
    component: () => import('@/views/openFbaTrack/index'),
    hidden: true
  }
  // { path: '*', redirect: '/404', hidden: true }
]

export default new Router({
  mode: 'hash',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})
