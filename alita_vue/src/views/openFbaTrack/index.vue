<template>
  <div class="box">
    <!-- 登陆按钮 -->
    <!--<div class="login-box">
      <el-button v-if="!getToken()" class="btn" icon="el-icon-user-solid" type="text" @click="$router.push('/login')">登陆</el-button>
      <el-button v-if="getToken()" class="btn" icon="el-icon-switch-button" type="text" @click="logOut">退出</el-button>
    </div>-->
    <!-- 标题 -->
    <el-row :class="{header:!isMobile,'mobile-header':isMobile}">
      轨迹查询
    </el-row>
    <el-container v-if="!isMobile">
      <!-- 左边搜索栏 -->
      <el-aside class="computer seacher-box">
        <div class="out-box" @click="focusInput($event)">
          <!-- <div class="out-box"> -->
          <!-- <div v-for="(item,index) in inputNum" :key="index" style="position: relative;">
            <input v-model.trim="inputNum[index]" :id="'order_'+index" autocomplete="off" class="num-iput" type="text" placeholder="请输入单号/包裹号查询" @keydown="back_space_clear($event, inputNum[index], index)" @keypress.enter="enterEvent(inputNum[index], index)">
          </div>-->
          <div style="position: relative;">
            <el-input :autosize="{minRows:16, maxRows: 30}" v-model="serach" class="num-iput" type="textarea" placeholder="请输入单号/包裹号查询" />
          </div>
        </div>
        <!-- <el-input v-model="serach" placeholder="请输入单号查询" clearable @keypress.enter="enterEvent()" @clear="clear()"/> -->
        <!-- 右边按钮 -->
        <el-row class="btns">
          <!-- <i class="el-icon-search" @click="search()"/> -->
          <el-button class="el-btn" @click="search()">查询</el-button>
        </el-row>
        <!-- 查件说明 -->
        <el-row>
          <span class="description" @click="dialogVisible=true"><i class="el-icon-question"/> 查件说明</span>
        </el-row>
      </el-aside>
      <el-container>
        <!-- 左边栏 -->
        <el-header>
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="全部" name="1"/>
            <!-- <el-tab-pane label="查询不到" name="2"/>
            <el-tab-pane label="运输中" name="3"/>
            <el-tab-pane label="到达待取" name="4"/>
            <el-tab-pane label="投递失败" name="5"/>
            <el-tab-pane label="成功签收" name="6"/>
            <el-tab-pane label="可能异常" name="7"/>
            <el-tab-pane label="运输过久" name="8"/> -->
            <!-- 表格 -->
            <div class="table-box">
              <el-collapse v-model="activeNames">
                <el-collapse-item v-for="(item,index) in activities" :key="item.order_num" :name="index">
                  <template slot="title">
                    单号/包裹号：<span style="font-weight:bold;font-size:18px;">{{ item.order_num }}</span>&nbsp;&nbsp;&nbsp;&nbsp;
                    跟踪号：<span style="font-weight:bold;font-size:14px;">{{ item.tracking_num }}</span>&nbsp;&nbsp;&nbsp;&nbsp;
                    {{ $t('content.WarehouseCode_1') }}：<span style="font-weight:bold;font-size:14px;">{{ item.warehouse_code }}</span>&nbsp;&nbsp;&nbsp;&nbsp;
                    渠道：<span style="font-weight:bold;font-size:14px;">{{ item.product_name }}</span>&nbsp;&nbsp;&nbsp;&nbsp;
                    国家：<span style="font-weight:bold;font-size:14px;">{{ item.country_code }}</span>&nbsp;&nbsp;&nbsp;&nbsp;
                    <el-button class="el-btn" style="margin-left: 20px" @click.stop="export_pod(item.order_id)">导出POD文件</el-button>
                  </template>
                  <el-table v-if="item.track_list" :data="item.track_list">
                    <el-table-column prop="create_date" label="时间"/>
                    <el-table-column prop="track_code" label="code"/>
                    <el-table-column prop="track_name" label="名称"/>
                    <el-table-column prop="description" label="描述"/>
                    <!--<el-table-column prop="warehouse_code" label="仓库代码"/>-->
                    <!--<el-table-column prop="product_name" label="产品名称"/>-->
                    <!--<el-table-column prop="country_code" label="国家"/>-->
                    <el-table-column prop="location" label="地点"/>
                  </el-table>
                  <span v-else style="color:#909399;">暂无轨迹信息</span>
                </el-collapse-item>
                <div v-if="activities.length===0" style="padding: 20px; font-size: 14px; color: #606266;">请在左侧输入单号/包裹号查询</div>
              </el-collapse>
            </div>
          </el-tabs>
        </el-header>
      </el-container>
    </el-container>
    <!-- 手机版本 -->
    <el-row v-else>
      <!-- 上边搜索栏 -->
      <el-aside class="seacher-box mobile">
        <div :style="{'min-height':isMobile?'50px':'300px'}" class="out-box">
          <!--          <div v-for="(item,index) in inputNum" :key="index" style="position: relative;">-->
          <!--            <input v-model.trim="inputNum[index]" :id="'order_'+index" autocomplete="off" class="num-iput" type="text" placeholder="请输入单号/包裹号查询" @keypress.enter="enterEvent(inputNum[index], index)">-->
          <!--            <i v-if="inputNum[index]" class="el-input__icon el-icon-circle-close el-input__clear close" @click="clear(inputNum[index], index)"/>-->
          <!--          </div>-->
          <div style="position: relative;">
            <input v-model.trim="serach" autocomplete="off" class="num-iput" type="text" placeholder="请输入单号/包裹号查询" @keypress.enter="search()">
            <i v-if="serach" class="el-icon-circle-close el-input__clear close" @click="serach=''"/>
          </div>
        </div>
        <!-- 右边按钮 -->
        <el-row class="btns">
          <!-- <i class="el-icon-search" @click="search()"/> -->
          <el-button class="el-btn" @click="search()">查询</el-button>
        </el-row>
        <!-- 查件说明 -->
        <el-row>
          <span class="description" @click="dialogVisible=true"><i class="el-icon-question"/> 查件说明</span>
        </el-row>
      </el-aside>
      <!-- 下面显示边栏 -->
      <el-row style="padding: 0 4px;">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="全部" name="1"/>
          <!-- <el-tab-pane label="查询不到" name="2"/>
            <el-tab-pane label="运输中" name="3"/>
            <el-tab-pane label="到达待取" name="4"/>
            <el-tab-pane label="投递失败" name="5"/>
            <el-tab-pane label="成功签收" name="6"/>
            <el-tab-pane label="可能异常" name="7"/>
            <el-tab-pane label="运输过久" name="8"/> -->
          <!-- 表格 -->
          <div class="table-box" style="padding-bottom:50px;">
            <el-collapse v-model="activeNames">
              <el-collapse-item v-for="(item,index) in activities" :key="item.order_num" :name="index">
                <template slot="title">
                  <div style="font-size:12px">单号/包裹号：<span style="font-weight:bold;font-size:14px;">{{ item.order_num }}</span>&nbsp;
                  </div>
                </template>
                <el-table :data="item.track_list">
                  <el-table-column prop="create_date" label="时间"/>
                  <el-table-column prop="track_code" label="code"/>
                  <el-table-column prop="track_name" label="名称"/>
                  <el-table-column prop="description" label="描述"/>
                  <el-table-column prop="receiver" label="仓库代码"/>
                  <el-table-column prop="product_name" label="产品名称"/>
                  <el-table-column prop="country_code" label="国家"/>
                  <el-table-column prop="location" label="地点"/>
                </el-table>
                <span v-if="!item.data" style="color:#909399;">暂无轨迹信息</span>
              </el-collapse-item>
              <div v-if="!activities===0" style="padding: 20px; font-size: 14px; color: #606266;">请在顶部输入单号/包裹号查询</div>
            </el-collapse>
          </div>
        </el-tabs>
      </el-row>
    </el-row>
    <!-- 查件说明 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :width="isMobile?'80%':'700px'"
      title="查件说明">
      <p class="title-tip">当您需要了解您包裹的当前状态时，请您仔细阅读以下内容</p>
      <p style="padding: 0 30px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <br><br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <br><br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <br><br>
      </p>
    </el-dialog>
  </div>
</template>

<script>
import { actionPost, getChoiceData } from '@/api/data'
import { parseTime } from '@/utils'
import { getToken } from '@/utils/auth'
// import { axiosExport } from '@/utils/index'

export default {
  name: 'Track',
  data() {
    return {
      inputNum: [''],
      serach: '',
      activeName: '1',
      activeNames: 0,
      order_id: [],
      activities: [
        // {
        //   content: '活动按期开始',
        //   timestamp: '2018-04-15'
        // }, {
        //   content: '通过审核',
        //   timestamp: '2018-04-13'
        // }, {
        //   content: '创建成功',
        //   timestamp: '2018-04-11'
        // }
      ],
      dialogVisible: false,
      // 判断是否移动设备
      isMobile: false,
      // 轨迹代码
      trackCodeList: []
    }
  },
  created() {
    this.isMobileDevice()
    this.order_id = []
    // 获取轨迹代码
    // this.getTrackCode()
  },
  // mounted() {
  //   this.order_id = []
  // },
  // beforeUpdate() {
  //   this.order_id = []
  // },
  methods: {
    getToken,
    parseTime,
    getTrackCode() {
      // getChoiceData('/api/trackinfo_parcel/', {}).then(res => {
      getChoiceData('/api/FbaOrderParcelTrack/', {}).then(res => {
        if (res.code === 200) {
          this.trackCodeList = res.data
        }
      })
    },
    logOut() {
      this.$store.dispatch('LogOut').then(() => {
        this.$message.success('退出成功！')
        location.reload()
      })
    },
    search() {
      actionPost({ api: '/api/FbaOrderParcelTrack/', data: { order_nums: this.serach }}).then(res => {
        if (res.code === 200) {
          this.activities = res.data
          this.order_id = res.order_id
          console.log('this.activities-->', this.activities)
          this.$message.success('查询成功！')
        } else {
          this.order_id = []
          this.$message.error('查询失败！')
        }
      })
    },
    getFileUrl(url) {
      return process.env.HOST + '/media' + url.split('media')[1]
      // return url.split('api')[0] + '/media' + url.split('media')[1]
    },
    downloadFile(fileUrl) {
      window.open(this.getFileUrl(fileUrl), '_blank')
    },
    export_pod(item_id) {
      if (this.order_id.length === 0) {
        this.$message.error('请先查询轨迹！')
        return
      }
      // actionPost({ api: '/api/customerOrders/custom_pod_export/', data: { ids: this.order_id }, responseType: 'blob' }).then(res => {
      actionPost({ api: '/api/customerOrders/download_pod_file/', data: { id: item_id }, responseType: 'json' }).then(res => {
        if (res === null || ('code' in res && res.code !== 200)) {
          this.$message.error(res.msg)
        } else {
          console.log('res.data000-->', res)
          // axiosExport(res, 'output_pdf_file.pdf')
          const fileUrl = res.data.file_url // 获取文件链接
          this.downloadFile(fileUrl) // 调用下载方法
        }
      })
    },
    handleClick() {
      console.log('click')
    },
    // 清空的时候
    clear(val, index) {
      if (this.inputNum.length === 1) {
        return
      }
      this.inputNum.splice(index, 1)
    },
    // 删除
    back_space_clear(event, val, index) {
      console.log(event)
      console.log(val)
      console.log(index)
      if (event.keyCode === 8) {
        if (!val && index > 0) {
          this.inputNum.splice(index, 1)
          this.$nextTick(() => {
            document.querySelector('#order_' + (index - 1)).focus()
          })
        }
      }
    },
    // 回车事件
    enterEvent(val, index) {
      const values = val.split(/[ \n,;]/)
      if (values.length > 1) {
        console.log(values)
        if (values) {
          this.inputNum.splice(0, this.inputNum.length)
          for (let i = 0; i < values.length; i++) {
            document.querySelector('#order_' + (index)).blur()
            this.inputNum.push(values[i])
            this.$nextTick(() => {
              document.querySelector('#order_' + (index + 1)).focus()
            })
          }
        }
      } else {
        document.querySelector('#order_' + (index)).blur()
        this.inputNum.push('')
        this.$nextTick(() => {
          document.querySelector('#order_' + (index + 1)).focus()
        })
      }
    },
    isMobileDevice() {
      // var sUserAgent = navigator.userAgent.toLowerCase()
      // var bIsIpad = sUserAgent.match(/ipad/i) === 'ipad'
      // var bIsIphoneOs = sUserAgent.match(/iphone os/i) === 'iphone os'
      // var bIsMidp = sUserAgent.match(/midp/i) === 'midp'
      // var bIsUc7 = sUserAgent.match(/rv:*******/i) === 'rv:*******'
      // var bIsUc = sUserAgent.match(/ucweb/i) === 'ucweb'
      // var bIsAndroid = sUserAgent.match(/android/i) === 'android'
      // var bIsCE = sUserAgent.match(/windows ce/i) === 'windows ce'
      // var bIsWM = sUserAgent.match(/windows mobile/i) === 'windows mobile'
      // if (bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM) {
      //   // 移动端
      //   this.isMobile = true
      //   console.log('手机')
      // } else {
      //   // pc端
      //   this.isMobile = false
      //   console.log('电脑')
      // }
      var userAgentInfo = navigator.userAgent
      var Agents = ['Android', 'iPhone',
        'SymbianOS', 'Windows Phone',
        'iPad', 'iPod']
      var flag = true
      for (var v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
          flag = false
          break
        }
      }
      this.isMobile = !flag
    },
    focusInput(e) {
      if (e.target.className === 'out-box') {
        document.querySelector('#order_' + (this.inputNum.length - 1)).focus()
      }
    }
  }
}
</script>

<style lang="scss" scope>
.box {
  height: 100%;
  overflow: auto;
  // padding-right: 20px;
  padding-bottom: 50px;
  background-image: linear-gradient(#F2F6FC, #DCDFE6);
  &::-webkit-scrollbar{
    width : 10px;
    height: 1px;
  }
  &::-webkit-scrollbar-thumb{
    border-radius: 10px;
    box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    background   : #535353;
  }
  &::-webkit-scrollbar-track{
    box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background   : #ededed;
  }
  .login-box {
    position: fixed;
    top: 0px;
    right: 20px;
    z-index: 100;
    cursor: pointer;
    .btn {
      font-size: 16px;
    }
  }
  .header {
    text-align: center;
    font-size: 30px;
    padding: 30px 0 50px 0;
  }
  .mobile-header {
    text-align: center;
    font-size: 18px;
    padding: 10px 0 10px 0;
    font-weight: bold;
  }
  // 右边
  .table-box {
    padding: 0 10px;
    background:#fff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    .el-collapse {
      border: none;
    }
  }
  // 左边
  .mobile {
    width: 100%!important;
  }
  .computer {
    width: 30%!important;
    max-width: 300px;
  }
  .seacher-box {
    border-radius: 4px;
    overflow: hidden;
    padding: 0 10px;
    // width: 30%!important;
    // max-width: 300px;
    .out-box {
      min-height: 300px;
      background-color: #fff;
      .close {
        display: inline-block;
        font-size: 12px;
        font-weight: 400;
        color: #909399;
        line-height: 28px;
        position: absolute;
        right: 0;
        top: 0;
        cursor: pointer;
      }
      .num-iput {
        width: 100%;
        height:100%;
        line-height: 28px;
        outline: none;
        border: none;
        //padding: 0 15px;
        //padding-right: 30px;
        font-size: 12px;
        -webkit-appearance: none;
        background-color: #FFF;
        background-image: none;
        border: none;
        color: #606266;
      }
    }
    .btns {
      padding: 10px 0;
      margin-top: 10px;
      .el-btn {
        background-color: #66adfe;
        width: 100%;
        border: none;
        height: 40px;
        color: #fff;
      }
    }
    .description {
      color: #467dc8;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .btns_pod {
    text-align: right;
    font-size: 30px;
    padding: 0 100px 50px 0;
  }
  // .btns_pod {
  //   margin-left: 100px;
  //   margin-top: 100px;
  //   .el-btn {
  //     background-color: #66adfe;
  //     width: 100%;
  //     border: none;
  //     height: 40px;
  //     color: #fff;
  //   }
  // }
  ///deep/ .el-dialog__body {
  //  padding-left: 0px;
  //  padding-right: 0px;
  //}
  // 提示
  .title-tip {
    color: #ff7221;
    font-size: 14px;
    background-color: #fff0e5;
    padding: 10px 30px;
  }
}
</style>
