import common from '@/components/Detail/common'

export default {
  components: {
    common
  },
  data() {
    return {
      initObject: {
        deteleDisabled: true,
        modifyBtnShow: {
          $is_revenue_lock: true,
          $is_cost_lock: true,
          order_status: 'WO',
          $is_sync_yqf: 'Success'
        },
        isADD: false,
        // 请求url
        requestUrl: { baseUrl: 'openApi/customerOrders' },
        // 表单底部传入按钮
        bottomBtn: [
          {
            type: 'selectBtn',
            value: '',
            name: this.$t('content.ImportUpdateFbaOrder'),
            placeholder: this.$t('content.ImportUpdateFbaOrder'),
            id: true,
            edit: true,
            btns: [
              {
                name: this.$t('content.downloadTemplate'),
                method: 'download_template',
                link: 'template_customer_fba_order.xlsx?t=1',
                auth: 'customer/ysdd/download_template'
              },
              {
                name: this.$t('content.importFBAOrder'),
                // method: 'upload_excel',
                // method: 'upload_excel_fba',
                method: 'import_update_fba_order_normal',
                // showProgress: true,
                not_checked: true,
                import: true,
                auth: 'customer/ysdgl/upload_excel'
              },
              {
                name: this.$t('content.downloadSingleProductTemplate'),
                method: 'download_template',
                link: 'template_customer_fba_order_single.xlsx?t=1',
                auth: 'customer/ysdgl/download_template'
              },
              {
                name: this.$t('content.importSingleProductOrder'),
                // method: 'upload_excel_fba_single',
                method: 'import_update_fba_order_single',
                not_checked: true,
                import: true,
                auth: 'customer/ysdgl/upload_excel_fba_single'
              }
            ]
          }
        ],
        // 表单数据
        initData: [
          {
            label: this.$t('content.basicInformation'),
            formDataInit: [
              {
                label: this.$t('content.customerOrderNumber'),
                prop: 'ref_num'
              },
              {
                label: this.$t('content.orderNum'),
                prop: 'order_num',
                addHiden: true,
                disabled: true
              },
              {
                label: this.$t('content.Product'),
                prop: 'product',
                type: 'select'
              },
              {
                label: this.$t('content.status'),
                prop: 'order_status',
                addHiden: true,
                filter: {
                  'DR': '草稿',
                  'WO': '等待作业',
                  'PDC': '已预报',
                  'ITP': '已拦截',
                  'PW': '已部分入仓',
                  'AW': '已全部入仓',
                  'CWED': '已确认入仓数据',
                  'OW': '已出国内仓',
                  'DEP': '已离港',
                  'TF': '转运',
                  'SF': '已签收',
                  'FC': '完成',
                  'VO': '作废'
                },
                type: 'select',
                disabled: true
              },
              {
                label: this.$t('content.contactPerson'),
                prop: 'contact',
                disabled: true
              },
              {
                label: this.$t('content.ShopManager'),
                prop: 'saler',
                addHiden: true,
                disabled: true
              },
              {
                prop: 'is_customs_declaration',
                label: this.$t('content.isCustomsDeclaration'),
                type: 'radio',
                filter: true
              }
            ]
          },
          {
            label: this.$t('content.TransportInformation'),
            formDataInit: [
              {
                label: this.$t('content.LogisticsPlan'),
                prop: 'logistics_planning',
                type: 'select',
                change: [
                  {
                    label: 'departure_port_name',
                    prop: 'departure',
                    type: 'getValue'
                  },
                  {
                    label: 'destination_port_name',
                    prop: 'destination',
                    type: 'getValue'
                  },
                  {
                    label: 'departure_port_dt_date',
                    prop: 'expected_leave_date',
                    type: 'getValue'
                  },
                  {
                    label: 'destination_port_dt_date',
                    prop: 'expected_arrivals_date',
                    type: 'getValue'
                  }
                ]
              },
              {
                label: this.$t('content.estimatedArrivalDate'),
                prop: 'arrival_date',
                type: 'date'
              },
              {
                prop: 'actual_arrival_date',
                label: this.$t('content.actualArrivalDate'),
                type: 'datetime'
              },
              {
                label: this.$t('content.TransferOrderNo'),
                prop: 'tracking_num'
              }
            ]
          },
          {
            label: this.$t('content.Sender_1'),
            formDataInit: [
              {
                label: this.$t('content.Sender'),
                prop: 'shipper',
                type: 'dialogselect',
                textVal: ''
              },
              {
                label: this.$t('content.recipient'),
                prop: 'receiver',
                type: 'dialogselect',
                textVal: ''
              }
            ]
          },
          {
            label: this.$t('content.recipient'),
            formDataInit: [
              {
                label: this.$t('content.AddressNo'),
                prop: 'buyer_address_num'
              },
              {
                label: this.$t('content.contactPerson'),
                prop: 'buyer_name'
              },
              {
                label: this.$t('content.email'),
                prop: 'buyer_mail'
              },
              {
                label: this.$t('content.PhoneNo'),
                prop: 'buyer_phone'
              },
              {
                label: this.$t('content.CountryCode_1'),
                prop: 'buyer_country_code'
              },
              {
                label: this.$t('content.tateCode'),
                prop: 'buyer_state'
              },
              {
                label: this.$t('content.CityCode'),
                prop: 'buyer_city_code'
              },
              {
                label: this.$t('content.postcode'),
                prop: 'buyer_postcode'
              },
              {
                label: this.$t('content.houseNumber'),
                prop: 'buyer_house_num'
              },
              {
                label: this.$t('content.AddressLineOne'),
                prop: 'buyer_address_one'
              },
              {
                label: this.$t('content.AddressLineTwo'),
                prop: 'buyer_address_two'
              },
              {
                label: this.$t('content.CompanyName_1'),
                prop: 'buyer_company_name'
              }
            ]
          },
          // {
          //   label: this.$t('content.Sender'),
          //   formDataInit: [
          //     {
          //       label: this.$t('content.AddressNo'),
          //       prop: 'address_num'
          //     },
          //     {
          //       label: this.$t('content.contactPerson'),
          //       prop: 'contact_name'
          //     },
          //     {
          //       label: this.$t('content.email'),
          //       prop: 'contact_email'
          //     },
          //     {
          //       label: this.$t('content.PhoneNo'),
          //       prop: 'contact_phone'
          //     },
          //     {
          //       label: this.$t('content.CountryCode_1'),
          //       prop: 'country_code'
          //     },
          //     {
          //       label: this.$t('content.tateCode'),
          //       prop: 'state_code'
          //     },
          //     {
          //       label: this.$t('content.CityCode'),
          //       prop: 'city_code'
          //     },
          //     {
          //       label: this.$t('content.postcode'),
          //       prop: 'postcode'
          //     },
          //     {
          //       label: this.$t('content.houseNumber'),
          //       prop: 'house_no'
          //     },
          //     {
          //       label: this.$t('content.AddressLineOne'),
          //       prop: 'address_one'
          //     },
          //     {
          //       label: this.$t('content.AddressLineTwo'),
          //       prop: 'address_two'
          //     },
          //     {
          //       label: this.$t('content.CompanyName_1'),
          //       prop: 'company_name'
          //     }
          //   ]
          // },
          {
            label: this.$t('content.CargoInformation'),
            formDataInit: [
              {
                label: this.$t('content.expectedPickNum'),
                prop: 'pre_carton',
                event: 'blur',
                type: 'preCartonChange',
                disabled: true
              },
              {
                label: this.$t('content.pieces'),
                prop: 'carton',
                disabled: true
              },
              {
                label: this.$t('content.EstimatedWeight'),
                prop: 'pre_weight',
                disabled: true
              },
              {
                label: this.$t('content.weight'),
                prop: 'weight',
                disabled: true
              },
              {
                label: this.$t('content.EstimatedVolume'),
                prop: 'pre_volume',
                disabled: true
              },
              {
                label: this.$t('content.Volume'),
                prop: 'volume',
                disabled: true
              },
              {
                label: this.$t('content.ConversionRate'),
                prop: 'charge_trans',
                default: true,
                defaultVal: 6000,
                disabled: true
              },
              {
                label: this.$t('content.chargeWeight'),
                prop: 'charge_weight',
                disabled: true
              },
              {
                label: this.$t('content.confirmChargeWeight'),
                prop: 'confirm_charge_weight'
              },
              {
                prop: 'confirm_volume',
                label: this.$t('content.confirmVolume')
              },
              // {
              //   label: this.$t('content.CustomerSplit'),
              //   prop: 'bubble',
              //   type: 'range_list',
              //   disabled: true
              // },
              // {
              //   label: this.$t('content.BubbleWeight'),
              //   prop: 'bubble_weight',
              //   disabled: true
              // },
              {
                label: this.$t('content.volumeWeight'),
                prop: 'volume_weight',
                disabled: true
              },
              {
                label: this.$t('content.CustomerRequirement'),
                prop: 'customer_remark',
                type: 'textarea'
              },
              {
                label: this.$t('content.Attachment'),
                prop: 'attachments',
                type: 'files'
              },
              {
                prop: 'truck_order_pod',
                label: this.$t('content.Capapod'),
                type: 'files',
                disabled: true,
                addHiden: true,
                single: true
              }
            ]
          },
          {
            label: this.$t('content.TotalCost'),
            formDataInit: [
              {
                label: this.$t('content.TotalCost'),
                prop: 'customerOrderChargeIns',
                type: 'table',
                singleEdit: true,
                singleEditTradition: {
                  is_revenue_lock: false
                },
                datavalue: 'customerOrderChargeIns',
                summary: true,
                data: [
                  { label: this.$t('content.costName'), prop: 'charge', type: 'select' },
                  { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
                  { label: this.$t('content.quantity'), prop: 'charge_count' },
                  { label: this.$t('content.chargeTotal'), prop: 'charge_total', readOnly: true },
                  { label: this.$t('content.currencyType'), prop: 'currency_type', type: 'current', default: 'CNY' },
                  { label: this.$t('content.payer'), prop: 'customer', type: 'select' }
                ],
                action: {
                  // add: true,
                  // detele: true,
                  // edit: true
                }
              }
            ]
          },
          {
            label: this.$t('content.goodsRelated'),
            formDataInit: [
              {
                type: 'tabTable',
                tabValue: '0',
                hasEditTable: true,
                editingRestriction: true,
                removeRestriction: true,
                tabData: [
                  {
                    datavalue: 'parcelItem',
                    label: this.$t('content.parcelAndGoods'),
                    type: 'editTable',
                    primary: true,
                    data: [
                      { label: this.$t('content.caseNumber'), prop: 'parcel_num' },
                      { label: this.$t('content.parcelQty'), prop: 'parcel_qty', simple: true, onlySimle: true },
                      { label: 'Reference Id', prop: 'reference_id' },
                      { label: 'Shipment Id', prop: 'shipment_id' },
                      { label: this.$t('content.parcelWeight'), prop: 'parcel_weight', simple: true },
                      { label: this.$t('content.parcelLength'), prop: 'parcel_length', simple: true },
                      { label: this.$t('content.parcelWidth'), prop: 'parcel_width', simple: true },
                      { label: this.$t('content.parcelHeight'), prop: 'parcel_height', simple: true },
                      { label: this.$t('content.remark'), prop: 'remark', simple: true },
                      { label: this.$t('content.labelWeight'), prop: 'label_weight', hidden: true },
                      { label: this.$t('content.labelLength'), prop: 'label_length', hidden: true },
                      { label: this.$t('content.TypeSingleWidthCm'), prop: 'label_width', hidden: true },
                      { label: this.$t('content.labelHeight'), prop: 'label_height', hidden: true },
                      { label: this.$t('content.ActualWeightKg'), prop: 'actual_weight', hidden: true },
                      { label: this.$t('content.ActualLengthCm'), prop: 'actual_length', hidden: true },
                      { label: this.$t('content.ActualWidthCm'), prop: 'actual_width', hidden: true },
                      { label: this.$t('content.ActualHeightCm'), prop: 'actual_height', hidden: true },
                      { label: this.$t('content.itemCode'), prop: 'item_code' },
                      { label: this.$t('content.declaredNameCN'), prop: 'declared_nameCN' },
                      { label: this.$t('content.declaredNameEN'), prop: 'declared_nameEN' },
                      { label: this.$t('content.declaredPrice'), prop: 'declared_price' },
                      { label: this.$t('content.quantity'), prop: 'item_qty' },
                      { label: this.$t('content.itemWeight'), prop: 'item_weight' },
                      // { label: this.$t('content.size'), prop: 'item_size' },
                      { label: this.$t('content.texture'), prop: 'texture' },
                      { label: this.$t('content.use'), prop: 'use' },
                      { label: this.$t('content.brand'), prop: 'brand' },
                      { label: this.$t('content.model'), prop: 'model' },
                      { label: this.$t('content.customsCode'), prop: 'customs_code' },
                      { label: this.$t('content.fbaNo'), prop: 'fba_no' },
                      { label: this.$t('content.fbaTrackCode'), prop: 'fba_track_code' },
                      { label: this.$t('content.ProductPicture'), prop: 'item_picture', hidden: true }
                    ]
                  },
                  {
                    datavalue: '$parce',
                    label: this.$t('content.packageSummary'),
                    data: [
                      { label: this.$t('content.CartonNo'), prop: 'parcel_num' },
                      { label: this.$t('content.trackingNum'), prop: 'tracking_num' },
                      { label: 'Reference Id', prop: 'reference_id' },
                      { label: 'Shipment Id', prop: 'shipment_id' },
                      { label: this.$t('content.LongActualLength'), prop: 'parcel_length', pre: true },
                      { label: this.$t('content.WidthActualWidth'), prop: 'parcel_width', pre: true },
                      { label: this.$t('content.HeightActualHeight'), prop: 'parcel_height', pre: true },
                      // { label: this.$t('content.parcelQty'), prop: 'parcel_qty' },
                      { label: this.$t('content.WeightActualWeight'), prop: 'parcel_weight', pre: true },
                      // { label: this.$t('content.OrderWeight'), prop: 'label_weight' },
                      // { label: this.$t('content.OrderLength'), prop: 'label_length' },
                      // { label: this.$t('content.SingleWidth'), prop: 'label_width' },
                      // { label: this.$t('content.OrderHigh'), prop: 'label_height' },
                      { label: this.$t('content.Volume'), prop: 'parcel_volume' },
                      // { label: this.$t('content.ActualWeight'), prop: 'actual_weight' },
                      // { label: this.$t('content.ActualLength'), prop: 'actual_length' },
                      // { label: this.$t('content.ActualWidth'), prop: 'actual_width' },
                      // { label: this.$t('content.ActualHeight'), prop: 'actual_height' },
                      { label: this.$t('content.remark'), prop: 'remark' },
                      { label: this.$t('content.Download'), prop: 'img_url', type: 's_file' }
                    ],
                    action: {
                      etcBtn: [
                        {
                          label: this.$t('content.Remove'),
                          event: 'remove_parcel',
                          comfirm: true,
                          comfirmMsg: '确定移除该包裹？'
                        }
                      ]
                    }
                  },
                  {
                    datavalue: '$goods',
                    label: this.$t('content.goodsSummary'),
                    data: [
                      { label: 'SKU', prop: 'item_code' },
                      { label: this.$t('content.declaredNameCn'), prop: 'declared_nameCN' },
                      { label: this.$t('content.declaredNameEn'), prop: 'declared_nameEN' },
                      { label: this.$t('content.DeclaredPrice'), prop: 'declared_price' },
                      { label: this.$t('content.quantity'), prop: 'item_qty' },
                      { label: this.$t('content.weight'), prop: 'item_weight' },
                      { label: this.$t('content.texture'), prop: 'texture' },
                      // { label: this.$t('content.size'), prop: 'item_size' },
                      { label: this.$t('content.use'), prop: 'use' },
                      { label: this.$t('content.brand'), prop: 'brand' },
                      { label: this.$t('content.model'), prop: 'model' },
                      { label: this.$t('content.customsCode'), prop: 'customs_code' },
                      { label: this.$t('content.fbaNo'), prop: 'fba_no' },
                      { label: this.$t('content.fbaTrackCode'), prop: 'fba_track_code' }
                    ]
                  },
                  {
                    datavalue: 'orderLabelTasks',
                    label: this.$t('content.labelTasks'),
                    data: [
                      { label: this.$t('content.labelDesc'), prop: 'label_desc' },
                      { label: this.$t('content.status'), prop: 'status', filter: {
                        UnHandled: '未处理',
                        Success: '处理成功',
                        HandledBy3rdNo: '根据第三方号码处理',
                        Failure: '处理失败',
                        VO: '作废'
                      }
                      },
                      { label: this.$t('content.thirdOrderNo'), prop: 'third_order_no' },
                      { label: this.$t('content.modeKey'), prop: 'mode_key' },
                      { label: this.$t('content.handleTimes'), prop: 'handle_times' }
                    ]
                  },
                  {
                    datavalue: 'orderSyncTasks',
                    label: this.$t('content.SynchronizationTask'),
                    data: [
                      { label: this.$t('content.TaskType'), prop: 'task_type', filter: {
                        PUSH_ORDER: '同步订单',
                        PULL_ORDER_STATUS: '拉取订单状态',
                        PULL_TRACK: '拉取订单轨迹',
                        PULL_PARCEL_TRACK: '拉取包裹轨迹状态',
                        PULL_POD_FILE: '拉取Pod文件',
                        PUSH_REVENUE: '推送收入',
                        PULL_COST: '拉取成本' }
                      },
                      { label: this.$t('content.TaskDescription'), prop: 'task_desc' },
                      { label: this.$t('content.status'), prop: 'status', filter: {
                        UnHandled: '未处理',
                        Success: '处理成功',
                        HandledBy3rdNo: '已提交',
                        Failure: '处理失败',
                        VO: '作废'
                      }
                      },
                      { label: this.$t('content.PullNumber'), prop: 'pull_times' }
                    ]
                  },
                  {
                    datavalue: 'tracks',
                    label: this.$t('content.trackInfo'),
                    data: [
                      { label: this.$t('content.time'), prop: 'actual_time' },
                      { label: this.$t('content.trackCode'), prop: 'track_code' },
                      { label: this.$t('content.trackName'), prop: 'track_name' },
                      { label: this.$t('content.trackInfor'), prop: 'remark' }
                    ],
                    action: {
                      add: 'select',
                      edit: true,
                      detele: true,
                      etcBtn: []
                    }
                  },
                  {
                    datavalue: 'insurance',
                    label: this.$t('content.CrossborderInsurancePolicyInformation'),
                    type: 'form',
                    data: [
                      { label: this.$t('content.OriginalTrackingNumber'), prop: 'trackingNo' },
                      { label: this.$t('content.ProductCode_2'), prop: 'productCode', type: 'select', filter: { 'KJB001': '跨境大货创新类保险服务' }},
                      { label: this.$t('content.NameOfTheInsured'), prop: 'insuredName' },
                      { label: this.$t('content.ShipmentValue'), prop: 'cargoValue' },
                      { label: this.$t('content.Freight'), prop: 'freight' },
                      { label: this.$t('content.FreightCurrency'), prop: 'freightCurrencyCode', type: 'currency' },
                      { label: this.$t('content.HowTheInsuredAmountIsDetermined'), prop: 'baseAmountWay', type: 'select', filter: { '01': '货值*加成比例', '02': '货值+运费', '03': '仅运费' }},
                      { label: this.$t('content.AdditionRatio'), prop: 'ratio' },
                      { label: this.$t('content.PolicyCurrency'), prop: 'currencyCode', type: 'currency' },
                      { label: this.$t('content.ActualWeightKg'), prop: 'chargeableWeight' },
                      { label: this.$t('content.remark'), prop: 'remark' },
                      { label: this.$t('content.ModeOfTransport'), prop: 'transportModeCode', type: 'select',
                        filter: {
                          '01': '海运',
                          '02': '空运',
                          '03': '公路',
                          '04': '铁路',
                          '05': '全程邮政',
                          '06': '全程快递'
                        }
                      },
                      { label: this.$t('content.MeansOfTransportAndVoyage'), prop: 'transportTool'
                      },
                      { label: this.$t('content.BillOfLadingNumber'), prop: 'blNo' },
                      { label: this.$t('content.DeliveryMethod'), prop: 'deliverywayCode', type: 'select', filter: { '0': '待定', '1': '快速派', '2': '卡车派' }
                      },
                      { label: this.$t('content.ExpressCompany'), prop: 'expressCompanyCode' },
                      { label: this.$t('content.CourierNo'), prop: 'expressNo' },
                      { label: 'shipmentId', prop: 'shipmentId' },
                      { label: this.$t('content.PackagingType'), prop: 'packingCode', type: 'select',
                        filter: {
                          '002': '纸箱',
                          '001': '木箱',
                          '025': '集装箱',
                          '020': '托盘',
                          '99': '其他'
                        }
                      },
                      { label: this.$t('content.PackingQuantity'), prop: 'packingQuantity' },
                      { label: this.$t('content.CargoDescription'), prop: 'cargoDesc' },
                      { label: this.$t('content.ClassOfGoods'), prop: 'cargoCategoryCode', type: 'select',
                        filter: {
                          '01': '无易碎品',
                          '02': '木箱',
                          '03': '集装箱'
                        }
                      },
                      { label: this.$t('content.TimeOfDeparture'), prop: 'departureDate', type: 'date' },
                      { label: this.$t('content.CountryOfDeparture'), prop: 'departureCountryCode' },
                      { label: this.$t('content.PointOfDeparture'), prop: 'departureAddress' },
                      { label: this.$t('content.DestinationType'), prop: 'destType', type: 'select',
                        filter: {
                          'FBA': 'FBA',
                          'PRIVATE': '私人地址',
                          'CAIBIAO': '菜鸟海外仓',
                          'OVERSEAS': '海外仓',
                          'OTHER': '其它'
                        }
                      },
                      { label: this.$t('content.CountryOfDestination'), prop: 'destinationCountryCode' },
                      { label: this.$t('content.Destination'), prop: 'destinationAddress' },
                      { label: this.$t('content.ShelfGuarantee'), prop: 'shelf', type: 'radio' },
                      { label: this.$t('content.ListingLocation'), prop: 'shelfName' },
                      { label: this.$t('content.RiotProtection'), prop: 'unrestRisk', type: 'radio' },
                      { label: this.$t('content.TypeOfDocumentOfTheInsured'), prop: 'certificateType', type: 'select',
                        filter: {
                          '101': '个人身份证号',
                          '110': '企业统一信用代码'
                        }
                      },
                      { label: this.$t('content.DocumentNumberOfTheInsured'), prop: 'certificateNo' },
                      { label: this.$t('content.InsuredTelephoneNumber'), prop: 'mobile' },
                      { label: this.$t('content.ContactAddressOfTheInsured'), prop: 'contactAddress' },
                      { label: this.$t('content.ReasonForCorrection'), prop: 'amendmentReason' }
                    ]
                  },
                  {
                    label: this.$t('content.WarehousingValuation'),
                    prop: 'orderAsyncTask',
                    datavalue: 'orderAsyncTask',
                    data: [
                      { label: '任务类型', prop: 'task_type', filter: {
                        BL: '计费',
                        CostFinish: '成本确认',
                        CostShare: '成本分摊'
                      }
                      },
                      { label: this.$t('content.TaskDescription'), prop: 'task_desc' },
                      { label: this.$t('content.status'), prop: 'status', filter: {
                        UnHandled: '未处理',
                        Waiting: '等待中',
                        Processed: '处理中',
                        Success: '处理成功',
                        Failure: '处理失败',
                        VO: '已作废'
                      }
                      },
                      { label: this.$t('content.handleTimes'), prop: 'handle_times' },
                      { label: this.$t('content.ProcessingTime'), prop: 'execution_time' }
                    ]
                    // action: { add: true, detele: true, edit: true }
                  }
                ]
              }
            ]
          }
        ],
        // 必填项
        rules: {
          // pre_carton: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          // pre_weight: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          // pre_volume: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }]
        },
        // 初始化下拉数据
        option: [
          { api: '/api/users/', prop: 'contact', label: '', value: '' },
          { api: '/api/products/', prop: 'product', label: '', value: '', query: { type: 'TR', status: 'ON' }},
          { api: '/api/customerOrders/get_order_status/', prop: 'order_status', label: '', value: '' },
          { api: '/api/addresses/', prop: 'shipper', label: 'address_num', value: '', query: { address_type: 'SP' }},
          { api: '/api/addresses/', prop: 'receiver', label: 'address_num', value: '', query: { address_type: 'RC' }},
          // { api: '/api/masterOrders/', prop: 'master_num', label: 'order_num', value: '' },
          // { api: '/api/houseOrders/', prop: 'house_num', label: 'order_num', value: '' },
          { api: '/api/users/', prop: 'saler', label: 'name' },
          { api: '/api/logisticsPlanning/', prop: 'logistics_planning', label: 'planning_code', value: '' }
        ],
        dialogOption: [
          { api: '/api/companies/list_company2/', prop: 'supplier', label: 'name', value: '', query: { is_supplier: true }},
          // { api: '/api/companies/list_company2/', prop: 'customer', label: 'short_name', value: '', query: { is_customer: true }},
          { api: '/api/companies/list_company/', prop: 'customer', label: 'name', value: '', query: { is_customer: true }},
          { api: '/api/charges/', prop: 'charge', label: '', value: '' }
        ]
      }
    }
  },
  methods: {
    switchAdd(b) {
      this.initObject.isADD = b
    }
  }
}
