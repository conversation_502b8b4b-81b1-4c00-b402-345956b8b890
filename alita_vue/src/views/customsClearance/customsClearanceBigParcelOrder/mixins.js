import common from '@/components/Detail/common'
import { actionPost } from '@/api/data'
export default {
  components: {
    common
  },
  data() {
    return {
      check: true,
      initObject: {
        deteleDisabled: true,
        isADD: false,
        // 请求url
        requestUrl: {
          baseUrl: 'customsClearanceBigParcelOrders'
          // add: '',
          // get: '',
          // del: '',
          // put: ''
        },
        // 表单数据
        initData: [
          {
            label: this.$t('content.basicInformation'),
            formDataInit: [
              {
                label: this.$t('content.BigBagNumber'),
                prop: 'order_num',
                disabled: true
              },
              // {
              //   label: this.$t('content.TapeColor'),
              //   prop: 'adhesive_tape_color'
              // },
              // {
              //   label: this.$t('content.UnpackOrNot'),
              //   prop: 'is_open_box',
              //   type: 'radio',
              //   default: true,
              //   defaultVal: false
              // },
              {
                label: this.$t('content.remark'),
                prop: 'remark',
                type: 'textarea'
              }
            ]
          },
          {
            label: this.$t('content.ParcelOrderDetails_1'),
            formDataInit: [
              {
                label: this.$t('content.ParcelOrderDetails_1'),
                prop: 'big_parcel',
                type: 'table',
                singleEdit: true,
                batchUnbind: true,
                datavalue: 'big_parcel',
                data: [
                  { label: this.$t('content.orderNum'), prop: 'order_num' },
                  {
                    prop: 'order_status',
                    label: this.$t('content.orderStatus'),
                    filter: {
                      'DR': '草稿',
                      'WO': '等待作业',
                      'FC': '完成',
                      'VO': '作废'
                    }
                  },
                  // { prop: 'customer_name', label: this.$t('content.customers') },
                  { prop: 'product_name', label: this.$t('content.Product') }
                  // { prop: 'weighing_weight', label: this.$t('content.NuclearWeight') }
                ],
                action: {
                  etcBtn: [
                    {
                      label: this.$t('content.Untie'),
                      event: 'unbind_small',
                      comfirm: true,
                      comfirmMsg: '确定解绑该清关包裹单？'
                    }
                  ],
                  api: `/api/customsClearanceBigParcelOrders/unbind_small/`,
                  batchUnbind: true
                }
              }
            ]
          }
        ],
        // 必填项
        rules: {
        },
        // 初始化下拉数据
        option: [
        ],
        dialogOption: [
        ]
      }
    }
  },
  methods: {
    tabelEtcEvent(data) {
      const that = this
      console.log('解绑清关包裹单')
      const event = data.btn.event
      // 移除客户订单
      if (event === 'unbind_small') {
        const { btn, sourceData, item, index } = data
        console.log(btn, sourceData, item, index)
        const params = {
          api: `/api/${this.initObject.requestUrl.baseUrl}/${btn.event}/`,
          data: { ids: [item.id] }
        }
        console.log(params, '参数输出')
        actionPost(params).then(res => {
          if (res.code === 200) {
            this.$message.success('操作成功')
            that.bus.$emit('fullLoading', false)
            // 移除那一项
            sourceData.splice(index, 1)
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
        })
      }
    },
    switchAdd(b) {
      this.initObject.isADD = b
    }
  }
}
