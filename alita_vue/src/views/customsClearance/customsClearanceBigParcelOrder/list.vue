<template>
  <div class="app-container">
    <eHeader :role="ROLE" :is-detail="`${linkUrl}add`" :dialog-visible="dialogVisible" :form-data="formData" :query="query" @toggleDialog="toggleDialog" @execute="execute"/>
    <!--表格渲染-->
    <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;" @selection-change="selectionChange">
      <el-table-column v-if="formData.check" type="selection" fixed width="40"/>
      <el-table-column v-for="item in formData.data" :sortable="item.sortable" :key="item.prop" :label="item.label" :min-width="item.width || ''">
        <template slot-scope="scope">
          <tablecolum :row="scope.row" :item="item"/>
        </template>
      </el-table-column>
      <el-table-column :label="$t('content.operation')" width="150px" align="center">
        <template slot-scope="scope">
          <edit v-if="checkPermission(ROLE)" :is-detail="`${linkUrl}detail?id=${scope.row.id}`" :form-data="formData" :data="scope.row" :sup_this="sup_this"/>
          <!-- <el-popover
            v-if="checkPermission(ROLE)"
            :ref="scope.row.id"
            placement="top"
            width="180">
            <p>{{ $t('content.sureDeleteThisData') }}</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">{{ $t('content.cancel') }}</el-button>
              <el-button :loading="delLoading" type="primary" size="mini" @click="subDelete(scope.row.id)">{{ $t('content.Ok') }}</el-button>
            </div>
            <el-button slot="reference" type="danger" size="mini">{{ $t('content.delete') }}</el-button>
          </el-popover> -->
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <el-pagination
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="total"
      :page-size="size"
      :current-page="page"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChange"
      @current-change="pageChange"/>
    <!-- 打印标签 -->
    <bar-code :code-list="formData.checkData.map(i=>i.parcel_num)"/>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import initData from '@/mixins/initData'
import { del } from '@/api/data'
import { parseTime, print_label } from '@/utils/index'
import eHeader from '@/components/InitTable/header'
import edit from '@/components/InitTable/edit'
import tablecolum from '@/components/InitTable/tablecolum'
import BarCode from '@/components/BarCode'
import { actionPost } from '@/api/data'

export default {
  components: { eHeader, edit, tablecolum, BarCode },
  mixins: [initData],
  data() {
    return {
      ROLE: ['ADMIN', 'ORDER'],
      delLoading: false,
      sup_this: this,
      linkUrl: '/customsClearance/customsClearanceBigParcelOrder/',
      apiUrl: '/api/customsClearanceBigParcelOrders/',
      formData: {
        api: 'customsClearanceBigParcelOrders',
        title: this.$t('content.BulkOrder'),
        printTitle: this.$t('content.BulkOrder'),
        check: true,
        checkData: [],
        multiSearch: [
          {
            label: this.$t('content.LargePackageOrderNumber'),
            prop: 'order_num'
          }
        ],
        filters: [
          // {
          //   prop: 'is_open_box',
          //   type: 'boolean',
          //   placeholder: this.$t('content.UnpackOrNot')
          // }
        ],
        action: [
          {
            name: this.$t('content.ConfigureCustomsClearance'),
            method: 'set_customs_clearance_order',
            dialogProp: true,
            dialogType: 'table',
            defaultCheck: {
              // master_num_name: 'order_num'
            },
            api: '/api/customsClearanceOrders/all_notFC_order/',
            query: { status: 'WO' },
            colums: [
              { label: this.$t('content.orderNum'), prop: 'order_num' },
              { prop: 'clear_status', label: this.$t('content.orderStatus'),
                filters: {
                  'DR': '草稿',
                  'WO': '待完成'
                }
              },
              {
                label: 'mawb',
                type: 'textarea',
                prop: 'mawb'
              },
              {
                label: this.$t('content.OriginatingSite'),
                prop: 'departure'
              },
              {
                label: this.$t('content.SendToSite'),
                prop: 'destination'
              }
            ],
            auth: 'order/dbd/set_customer_order'
          }
        ],
        data: [
          {
            prop: 'order_num',
            label: this.$t('content.BigBagNumber'),
            link: '/customsClearance/customsClearanceBigParcelOrder/detail'
          },
          {
            prop: 'third_order_no',
            label: this.$t('content.TheNumberOfTheThirdPartyPackage')
          },
          {
            prop: 'customer_order_num',
            label: this.$t('content.CustomsClearanceOrderNumber')
          },
          {
            prop: 'remark',
            label: this.$t('content.remark')
          }
        ] }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.init()
    // })
  },
  methods: {
    parseTime,
    checkPermission,
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort }
      if (value) { this.params['search'] = value }
      return true
    },
    subDelete(id) {
      this.delLoading = true
      const p = {
        id,
        api: this.formData.api
      }
      del(p).then(res => {
        this.delLoading = false
        this.$refs[id].doClose()
        this.init()
        this.$notify({
          title: this.$t('content.deletedSuccessfully'),
          type: 'success',
          duration: 2500
        })
      }).catch(err => {
        this.delLoading = false
        this.$refs[id].doClose()
        console.log(err.response.data.message)
      })
    },
    selectionChange(val) {
      this.formData.checkData = val
    },
    // 统一action操作
    execute(item) {
      console.log('---item-->>', item)
      // item从header组件传过来的
      const { method, ids, data } = item
      const api = this.apiUrl + method + '/'
      if ((method === 'set_customer_order') && data.selectData.length > 1) {
        this.$message.error(`只能选择一个${method === 'set_customer_order' ? '运输单' : ''}单操作`)
        return
      }
      if (method !== 'multi_create' && ids.length === 0) {
        this.$message.error(this.$t('content.selectItem'))
        return
      }
      var f = () => {
        this.bus.$emit('fullLoading', true)
        actionPost({ api, data: { ids, ...data }}).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg)
            if (method === 'print_outbound') {
              print_label(res.base64)
            }
            if (method === 'multi_create') {
              console.log(item.data.product)
              item.data.product = ''
            }
            this.init()
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => { this.bus.$emit('fullLoading', false) })
      }

      const comfirm = (this.formData.action.find(i => i.method === method) || {})['comfirm']
      if (comfirm) {
        this.$confirm('确定执行此操作, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        }).then(() => {
          f()
        }).catch(() => {})
      } else {
        f()
      }
    }
  }
}
</script>

<style scoped>

</style>
