import common from '@/components/Detail/common'
import { actionPost } from '@/api/data'
export default {
  components: {
    common
  },
  data() {
    return {
      initObject: {
        deteleDisabled: true,
        modifyBtnShow: {
          $is_revenue_lock: true,
          $is_cost_lock: true,
          clear_status: 'WO'
        },
        isADD: false,
        action_flag: '',
        // 请求url
        requestUrl: { baseUrl: 'customsClearanceOrders' },
        // 表单数据
        initData: [
          {
            label: this.$t('content.basicInformation'),
            formDataInit: [
              {
                label: this.$t('content.CustomsClearanceNumber'),
                prop: 'order_num',
                disabled: true
              },
              {
                label: this.$t('content.customers'),
                prop: 'customer',
                type: 'select',
                change: [
                  {
                    prop: 'contact',
                    label: 'contact_name',
                    type: 'getValue'
                  },
                  {
                    prop: 'saler',
                    label: 'saler_name',
                    type: 'getValue'
                  }
                ]
              },
              {
                label: this.$t('content.status'),
                prop: 'clear_status',
                addHiden: true,
                filter: {
                  'DR': '草稿',
                  'WO': '已下单',
                  'CO': '已审核',
                  'HI': '航班/船只已到达',
                  'NT': '仓库收到NOA时间',
                  'DH': 'GHA已发NOA时间',
                  'TH': 'GHA显示DLV时间',
                  'AW': '仓库实际收货时间',
                  'WN': '仓库通知清关时间',
                  'PA': '部分到仓',
                  'PC': '部分清关',
                  'CC': '清关完成',
                  'ZY': '转运车已发',
                  'FC': '已到转运仓',
                  'VO': '作废'
                },
                type: 'select',
                disabled: true
              },
              {
                label: this.$t('content.Product'),
                prop: 'product',
                type: 'select'
              },
              // {
              //   label: this.$t('content.remark'),
              //   prop: 'remark',
              //   type: 'textarea'
              // },
              {
                label: 'POD',
                prop: 'pod_file',
                type: 'files3'
              },
              {
                label: 'CRM',
                prop: 'crm_file',
                type: 'files3'
              },
              {
                label: 'AWB',
                prop: 'awb_file',
                type: 'files3'
              },
              {
                label: 'Manifest',
                prop: 'manifest_file',
                type: 'files3'
              },
              {
                label: 'NOA',
                prop: 'noa_file',
                type: 'files3'
              },
              {
                label: this.$t('content.CustomsClearanceCustomerCode'),
                prop: 'waybills_customer',
                type: 'select',
                filter: {
                  'TLI': 'TECHLINKINFORMATION',
                  'ZHI': 'ZHIHE'
                }
              }
            ]
          },
          {
            label: this.$t('content.TransportInformation'),
            formDataInit: [
              {
                label: this.$t('content.departurePort'),
                prop: 'departure'
              },
              {
                label: this.$t('content.destinationPort'),
                prop: 'destination'
              },
              {
                label: 'MAWB',
                prop: 'mawb'
              },
              {
                label: this.$t('content.flightNum'),
                prop: 'airline_num'
              },
              {
                label: this.$t('content.ExpectedLeaveDate'),
                prop: 'estimated_time_departure',
                type: 'datetime'
              },
              {
                label: this.$t('content.ExpectArrivalDate'),
                prop: 'estimated_time_arrival',
                type: 'datetime'
              },
              {
                label: this.$t('content.ActualLeaveDate'),
                prop: 'actual_leave_date',
                type: 'datetime',
                addHiden: true
              },
              {
                label: this.$t('content.ActualArrivalDate'),
                prop: 'actual_arrivals_date',
                type: 'datetime',
                addHiden: true
              },
              {
                prop: 'noa_date',
                label: this.$t('content.TimeOfArrivalNotice'),
                type: 'datetime',
                addHiden: true,
                disabled: true
              },
              {
                prop: 'receive_noa_date',
                label: this.$t('content.TimeWhenTheWarehouseReceivedNoa'),
                type: 'datetime',
                addHiden: true,
                disabled: true
              },
              {
                prop: 'pickup_date',
                label: this.$t('content.AirportPickupTime'),
                type: 'datetime',
                addHiden: true,
                disabled: true
              },
              {
                prop: 'ataw_date',
                label: this.$t('content.WarehouseActualReceiptTime'),
                type: 'datetime',
                addHiden: true,
                disabled: true
              },
              {
                prop: 'clearance_date',
                label: this.$t('content.CustomsClearanceCompletionTime'),
                type: 'datetime',
                addHiden: true,
                disabled: true
              },
              {
                prop: 'actual_departure_date',
                label: this.$t('content.TimeTheTransferCarHasBeenDispatched'),
                type: 'datetime',
                addHiden: true,
                disabled: true
              },
              {
                label: this.$t('content.TranType'),
                prop: 'transport_type',
                type: 'select',
                filter: {
                  'AWB': 'AWB',
                  'T1': 'T1'
                },
                change: [
                  {
                    type: 'visible',
                    showVal: 'T1', // 当 transport_type 为 'T1' 时显示 T1_num
                    visibleProp: ['T1_num'] // 要控制的字段
                  }
                ]
              },
              {
                label: this.$t('content.T1Num'),
                prop: 'T1_num',
                $hide: true // 默认隐藏，只有当选择T1时才显示
              }
            ]
          },
          {
            label: this.$t('content.CargoInformation'),
            formDataInit: [
              {
                label: this.$t('content.EstimatedNumberOfBoxes'),
                prop: 'pre_carton'
              },
              {
                label: this.$t('content.ActualNumberOfBoxes'),
                prop: 'carton'
              },
              {
                label: this.$t('content.ExpectedNumberOfParcels'),
                prop: 'pre_package_num'
              },
              {
                label: this.$t('content.ActualNumberOfParcels'),
                prop: 'package_num'
              },
              {
                label: this.$t('content.EstimatedWeight'),
                prop: 'pre_weight'
              },
              {
                label: this.$t('content.ActualWeight'),
                prop: 'weight'
              },
              {
                label: this.$t('content.EstimatedPallets'),
                prop: 'pre_pallet_num'
              },
              {
                label: this.$t('content.ActualPallets'),
                prop: 'pallet_num'
              },
              {
                label: this.$t('content.NOAWeight'),
                prop: 'NOA_weight'
              },
              {
                label: this.$t('content.WarehouseWeight'),
                prop: 'warehouse_weight'
              },
              {
                label: this.$t('content.EstimatedVolume'),
                prop: 'pre_volume'
              },
              {
                label: this.$t('content.ActualVolume'),
                prop: 'volume'
              },
              {
                label: this.$t('content.PreChargeWeight'),
                prop: 'pre_charge_weight'
              },
              {
                label: this.$t('content.WhetherToDivideGoodsOrNot'),
                prop: 'is_split_goods',
                defaultVal: false,
                type: 'radio',
                change: [
                  {
                    hiddenProp: ['sop_file'],
                    showVal: false,
                    type: 'hidden'
                  }
                ]
              },
              {
                label: this.$t('content.ULD_PMC'),
                prop: 'ULD_PMC'
              },

              {
                label: this.$t('content.SubdivisionRequirement'),
                prop: 'sop_file',
                type: 'files',
                fileList: [],
                origin_name: true,
                single: true,
                $hide: false
              }
            ]
          },
          {
            label: this.$t('content.AddressInformation'),
            formDataInit: [
              {
                label: this.$t('content.TheConsigneeOfTheBillOfLadingIsAwbconsignee'),
                prop: 'shipper',
                type: 'dialogselect',
                textVal: ''
              },
              {
                label: 'Notify Party',
                prop: 'notify_party',
                type: 'dialogselect',
                textVal: ''
              }
            ]
          },
          {
            label: this.$t('content.TranshipmentBin'),
            formDataInit: [
              {
                label: this.$t('content.TranshipmentBin'),
                prop: 'receivers',
                type: 'table',
                singleEdit: true,
                singleEditTradition: {
                  // is_revenue_lock: false
                },
                datavalue: 'receivers',
                // summary: true,
                data: [
                  { label: this.$t('content.DeliveryMethod'), prop: 'delivery_method' },
                  { label: this.$t('content.TranshipmentBin'), prop: 'receiver_address', type: 'select' },
                  { label: this.$t('content.DeliveryAddresses'), prop: 'delivery_address' },
                  { label: this.$t('content.TapeColor'), prop: 'adhesive_tape_color' },
                  { label: this.$t('content.parcelQty'), prop: 'package_num' },
                  { label: this.$t('content.ActualNumberOfBoxes'), prop: 'actual_package_num', readOnly: true },
                  { label: this.$t('content.ExpectedNumberOfParcels'), prop: 'pre_carton' },
                  { label: this.$t('content.ActualNumberOfParcels'), prop: 'carton', readOnly: true }
                ],
                action: {
                  add: true,
                  detele: true,
                  edit: true
                  // flag: 'revenue_save'
                }
              }
            ]
          },
          {
            label: this.$t('content.incomeDetails'),
            formDataInit: [
              {
                label: this.$t('content.incomeDetails'),
                prop: 'clearanceChargeIns',
                type: 'table',
                singleEdit: true,
                singleEditTradition: {
                  is_revenue_lock: false
                },
                datavalue: 'clearanceChargeIns',
                summary: true,
                data: [
                  { label: this.$t('content.costName'), prop: 'charge', type: 'select' },
                  { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
                  { label: this.$t('content.quantity'), prop: 'charge_count' },
                  { label: this.$t('content.chargeTotal'), prop: 'charge_total', readOnly: true },
                  { label: this.$t('content.currencyType'), prop: 'currency_type', type: 'current', default: 'CNY' },
                  { label: this.$t('content.payer'), prop: 'customer', type: 'select' },
                  { label: this.$t('content.remark'), prop: 'remark', type: 'textarea', not_required: true }
                ],
                action: {
                  add: true,
                  detele: true,
                  edit: true,
                  flag: 'revenue_save'
                }
              }
            ]
          },
          {
            label: this.$t('content.costDetails'),
            formDataInit: [
              {
                label: this.$t('content.costDetails'),
                prop: 'clearanceChargeOuts',
                type: 'table',
                singleEdit: true,
                singleEditTradition: {
                  is_cost_lock: false
                },
                datavalue: 'clearanceChargeOuts',
                summary: true,
                data: [
                  { label: this.$t('content.costName'), prop: 'charge', type: 'select' },
                  { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
                  { label: this.$t('content.quantity'), prop: 'charge_count' },
                  { label: this.$t('content.chargeTotal'), prop: 'charge_total', readOnly: true },
                  { label: this.$t('content.currencyType'), prop: 'currency_type', type: 'current' },
                  { label: this.$t('content.supplier'), prop: 'supplier', type: 'select' },
                  { label: this.$t('content.Share'), prop: 'is_share', type: 'radio', readOnly: true },
                  { label: this.$t('content.ShareNo'), prop: 'share_charge_id', readOnly: true },
                  { label: this.$t('content.remark'), prop: 'remark', type: 'textarea', not_required: true }
                ],
                rules: {
                  charge: [{ required: true, message: this.$t('content.pleaseSelect'), trigger: 'change' }],
                  supplier: [{ required: true, message: this.$t('content.pleaseSelect'), trigger: 'change' }]
                },
                action: {
                  add: true,
                  detele: true,
                  edit: true,
                  flag: 'cost_save'
                }
              }
            ]
          },
          {
            label: this.$t('content.CustomsClearancePackage'),
            formDataInit: [
              {
                label: this.$t('content.CustomsClearancePackage'),
                // prop: 'customsClearanceBigParcelOrderList',
                type: 'table',
                singleEdit: true,
                datavalue: 'customsClearanceBigParcelOrderList',
                batchUnbind: true,
                data: [
                  { label: this.$t('content.CustomsClearancePackageNumber'), prop: 'order_num' }
                  // { label: this.$t('content.TapeColor'), prop: 'adhesive_tape_color' },
                  // { label: this.$t('content.UnpackOrNot'), prop: 'is_open_box' }
                ],
                action: {
                  etcBtn: [
                    {
                      label: this.$t('content.Untie'),
                      event: 'batch_unbind_order',
                      comfirm: true,
                      comfirmMsg: '确定解绑该清关大包单？'
                    }
                  ],
                  api: `/api/customsClearanceOrders/batch_unbind_order/`,
                  batchUnbind: true
                }
              }
            ]
          },
          {
            label: this.$t('content.RelevantInformation'),
            formDataInit: [
              {
                type: 'tabTable',
                tabValue: '0',
                tabData: [
                  {
                    datavalue: 'relate_orders',
                    label: this.$t('content.TransportDetail'),
                    data: [
                      { label: this.$t('content.TruckNumber'), prop: 'truck_order_num' },
                      // { label: this.$t('content.PlaceOfOrigin'), prop: 'start_destination' },
                      // { label: this.$t('content.Destination'), prop: 'arrive_destination' },
                      { label: this.$t('content.LicensePlateNumber'), prop: 'car_number' },
                      { label: this.$t('content.DriversName'), prop: 'driver_name' },
                      { label: this.$t('content.TotalParcelNumber'), prop: 'package_num' }

                    ]
                  },
                  {
                    datavalue: 'clearance_tracks',
                    label: this.$t('content.trackInfo'),
                    data: [
                      { label: this.$t('content.time'), prop: 'operation_time' },
                      { label: this.$t('content.trackName'), prop: 'track_name' },
                      { label: this.$t('content.TrajectoryDescription'), prop: 'remark' }
                    ],
                    action: {
                      // add: 'select',
                      // edit: true,
                      // detele: true,
                      // etcBtn: []
                    }
                  },
                  {
                    label: this.$t('content.CatchSingleTask'),
                    type: 'table',
                    datavalue: 'customsClearanceOrderSupplierTasks',
                    data: [
                      { label: this.$t('content.labelDesc'), prop: 'task_desc' },
                      { label: this.$t('content.status'), prop: 'status', filter: {
                        UnHandled: '未处理',
                        Success: '处理成功',
                        HandledBy3rdNo: '根据第三方号码处理',
                        Failure: '处理失败',
                        VO: '作废' }
                      },
                      { label: this.$t('content.thirdOrderNo'), prop: 'third_order_no' },
                      { label: this.$t('content.modeKey'), prop: 'mode_key' },
                      { label: this.$t('content.handleTimes'), prop: 'handle_times' }
                    ]
                  }
                ]
              }
            ]
          }

        ],
        // 必填项
        rules: {
          departure: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          destination: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          mawb: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          airline_num: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          estimated_time_departure: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          estimated_time_arrival: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          pre_carton: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          carton: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          pre_package_num: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          package_num: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          pre_weight: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          weight: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          pallet_num: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          shipper: [{ required: true, message: this.$t('content.pleaseSelectConsignee'), trigger: 'change' }],
          // receiver: [{ required: true, message: $this.$('content.pleaseSelectConsignee'), trigger: 'change' }],
          product: [{ required: true, message: this.$t('content.selectProduct'), trigger: 'change' }],
          customer: [{ required: true, message: '请选择客户', trigger: 'change' }]
        },
        // 初始化下拉数据
        option: [
          { api: '/api/companies/list_company2/', prop: 'customer', label: 'short_name', value: '', query: { is_customer: true }},
          { api: '/api/users/', prop: 'contact', label: '', value: '' },
          { api: '/api/products/', prop: 'product', label: '', value: '', query: { type: 'CL', status: 'ON' }},
          { api: '/api/addresses/', prop: 'shipper', label: 'address_num', value: '', query: { address_type: 'TD' }},
          { api: '/api/addresses/', prop: 'notify_party', label: 'address_num', value: '', query: { address_type: 'NP' }},
          { api: '/api/users/', prop: 'saler', label: 'name' }
        ],
        dialogOption: [
          { api: '/api/companies/list_company2/', prop: 'supplier', label: 'short_name', value: '', query: { is_supplier: true }},
          { api: '/api/companies/list_company2/', prop: 'customer', label: 'short_name', value: '', query: { is_customer: true }},
          { api: '/api/addresses/', prop: 'receiver_address', label: 'address_num', value: '', query: { address_type: 'ZW' }},
          { api: '/api/charges/', prop: 'charge', label: '', value: '' }
        ]
      }
    }
  },
  methods: {
    tabelEtcEvent(data) {
      const that = this
      console.log('清关大包单解绑')
      const event = data.btn.event
      // 移除客户订单
      if (event === 'batch_unbind_order') {
        const { btn, sourceData, item, index } = data
        console.log(btn, sourceData, item, index)
        const params = {
          api: `/api/customsClearanceOrders/batch_unbind_order/`,
          data: { ids: [item.id] }
        }
        console.log(params, '参数输出')
        actionPost(params).then(res => {
          if (res.code === 200) {
            this.$message.success('操作成功')
            that.bus.$emit('fullLoading', false)
            // 移除那一项
            sourceData.splice(index, 1)
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
        })
      }
    },
    switchAdd(b) {
      this.initObject.isADD = b
    }
  }
}
