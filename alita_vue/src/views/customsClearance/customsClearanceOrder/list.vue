<template>
  <div class="app-container">
    <eHeader :role="ROLE" :is-detail="`${linkUrl}add`" :dialog-visible="dialogVisible" :form-data="formData" :query="query" @execute="execute" @toggleDialog="toggleDialog" @uploadExcel="uploadExcel"/>
    <!--表格渲染-->
    <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;" @selection-change="selectionChange">
      <el-table-column v-if="formData.check" type="selection" fixed width="40"/>
      <el-table-column v-for="item in formData.data" :sortable="item.sortable" :key="item.prop" :label="item.label" :min-width="item.width || ''">
        <template slot-scope="scope">
          <tablecolum :row="scope.row" :item="item"/>
        </template>
      </el-table-column>
      <el-table-column :label="$t('content.operation')" width="100px" align="center">
        <template slot-scope="scope">
          <edit v-if="checkPermission(ROLE)" :is-detail="`${linkUrl}detail?id=${scope.row.id}`" :form-data="formData" :data="scope.row" :sup_this="sup_this"/>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <el-pagination
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="total"
      :page-size="size"
      :current-page="page"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChange"
      @current-change="pageChange"/>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import initData from '@/mixins/initData'
import { del } from '@/api/data'
import { parseTime, axiosExport } from '@/utils/index'
import eHeader from '@/components/InitTable/header'
import edit from '@/components/InitTable/edit'
import tablecolum from '@/components/InitTable/tablecolum'
// import BarCode from '@/components/BarCode'
import { actionPost } from '@/api/data'

export default {
  components: { eHeader, edit, tablecolum },
  mixins: [initData],
  data() {
    return {
      ROLE: ['ADMIN', 'ORDER'],
      delLoading: false,
      sup_this: this,
      linkUrl: '/customsClearance/customsClearanceOrder/',
      apiUrl: '/api/customsClearanceOrders/',
      formData: {
        api: 'customsClearanceOrders',
        title: this.$t('content.CustomsClearanceOrder'),
        printTitle: this.$t('content.CustomsClearanceOrder'),
        dialogType: 'customAction',
        check: true,
        checkData: [],
        multiSearch: [
          {
            label: this.$t('content.orderNum'),
            prop: 'order_num'
          },
          {
            label: 'MAWB',
            prop: 'mawb'
          }
        ],
        searchholder: this.$t('content.OrderNumberChineseProductNameEnglishProductNameFlightNumberPortOfDestination'),
        excelExport: [
          { label: this.$t('content.currencyType'), prop: 'gross_currency', position: '毛利' },
          { label: this.$t('content.TheNameOfAProductOrCommodity'), prop: 'declared_name_cn', position: '收件人' },
          { label: this.$t('content.MailingAddressZipCode'), prop: 'buyer_postcode', position: '收件人' }
        ],
        // 过滤器
        filters: [
          {
            prop: 'customer',
            type: 'select',
            placeholder: this.$t('content.customers'),
            label: 'short_name',
            api: '/api/companies/list_company2/',
            query: {
              is_customer: true
            }
          },
          {
            prop: 'clear_status',
            type: 'select',
            placeholder: this.$t('content.orderStatus'),
            // value: 'WO',
            data: [
              { id: 'DR', label: this.$t('content.draft') },
              { id: 'WO', label: this.$t('content.PlacedAnOrder') },
              { id: 'CO', label: this.$t('content.Audited') },
              { id: 'HI', label: this.$t('content.TheFlightShipHasArrived') },
              { id: 'DH', label: this.$t('content.GhaHasSentNoaTime') },
              { id: 'NT', label: this.$t('content.TimeWhenTheWarehouseReceivedNoa') },
              { id: 'TH', label: this.$t('content.GhaDisplaysTheDlvTime') },
              { id: 'AW', label: this.$t('content.WarehouseActualReceiptTime') },
              { id: 'WN', label: this.$t('content.WarehouseNotifiesCustomsClearanceTime') },
              { id: 'PA', label: this.$t('content.PartialArrival') },
              { id: 'PC', label: this.$t('content.PartialCustomsClearance') },
              { id: 'RP', label: this.$t('content.PodcrmHasBeenReceived') },
              { id: 'RC', label: this.$t('content.CustomsClearanceDocumentsHaveBeenReceived') },
              { id: 'CC', label: this.$t('content.CustomsClearanceCompleted') },
              { id: 'ZY', label: this.$t('content.TransferCarHasBeenDispatched') },
              { id: 'FC', label: this.$t('content.ItsInTheTransferBin') },
              { id: 'UF', label: this.$t('content.UploadingPodcrmIsComplete') },
              { id: 'VO', label: this.$t('content.void') }
            ]
          },
          {
            prop: 'is_revenue_lock',
            type: 'boolean',
            placeholder: this.$t('content.IsRevenueLock')
          },
          {
            prop: 'is_cost_lock',
            type: 'boolean',
            placeholder: this.$t('content.IsCostLock')
          }
        ],
        checkSumary: [

        ],
        action: [
          {
            name: this.$t('content.submitOrder'),
            method: 'submit_order',
            comfirm: true,
            auth: 'customsClearance/cco/submit'
          },
          {
            name: this.$t('content.ReviewTheOrder'),
            method: 'check_order',
            comfirm: true,
            auth: 'customsClearance/cco/check_order'
          },
          {
            name: this.$t('content.TrajectoryChange'),
            method: 'change_track',
            dialogProp: true,
            dialogType: 'selectAciton',
            dateType: 'datetime',
            noQty: true,
            colums: [
              { label: this.$t('content.TheFlightShipHasArrived'), value: 'HI' },
              { label: this.$t('content.GhaHasSentNoaTime'), value: 'DH' },
              { label: this.$t('content.TimeWhenTheWarehouseReceivedNoa'), value: 'NT' },
              { label: this.$t('content.GhaDisplaysTheDlvTime'), value: 'TH' },
              { label: this.$t('content.WarehouseActualReceiptTime'), value: 'AW' },
              { label: this.$t('content.WarehouseNotifiesCustomsClearanceTime'), value: 'WN' },
              { label: this.$t('content.PartialArrival'), value: 'PA' },
              { label: this.$t('content.PartialCustomsClearance'), value: 'PC' },
              { label: this.$t('content.CustomsClearanceCompleted'), value: 'CC' },
              { label: this.$t('content.TransferCarHasBeenDispatched'), value: 'ZY' },
              { label: this.$t('content.ItsInTheTransferBin'), value: 'FC' },
              { label: this.$t('content.UploadingPodcrmIsComplete'), value: 'UF' }
            ],
            auth: 'customsClearance/cco/change_track'
          },
          {
            name: this.$t('content.ConfigureTruckList'),
            method: 'set_truck_num',
            dialogProp: true,
            dialogType: 'customAction',
            refName: 'setTruckOrderRef',
            auth: 'customsClearance/cco/set_truck_num'
          },
          {
            name: this.$t('content.failOrder'),
            method: 'cancel_order',
            comfirm: true,
            auth: 'customsClearance/cco/cancel_order'
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.TotalIncome'),
            btns: [
              {
                name: this.$t('content.IsRevenueLock'),
                method: 'finish_order',
                auth: 'order/cco/finish_order'
              },
              {
                name: this.$t('content.OrderUnlock'),
                method: 'order_unlock',
                auth: 'order/cco/order_unlock'
              }
            ]
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.cost'),
            btns: [
              {
                name: this.$t('content.IsCostLock'),
                method: 'cost_finish',
                auth: 'order/coo/cost_finish'
              },
              {
                name: this.$t('content.CostUnlock'),
                method: 'cost_unlock',
                auth: 'order/coo/cost_unlock'
              }
            ]
          },
          {
            name: this.$t('content.WithdrawSubmission'),
            method: 'back_order',
            comfirm: true,
            auth: 'customsClearance/cco/cancel_order'
          },
          {
            name: this.$t('content.Redenomination'),
            method: 'revaluation',
            comfirm: true,
            auth: 'customsClearance/cco/revaluation'
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.ImportCustomsClearanceForm'),
            btns: [
              {
                name: this.$t('content.downloadTemplate'),
                method: 'download_template',
                link: 'template_customer_clearance_order.xlsx?t=1',
                auth: 'order/qgdgl/download_template'
              },
              {
                name: this.$t('content.ImportOrder'),
                method: 'upload_excel',
                not_checked: true,
                import: true,
                auth: 'order/qgdgl/upload_excel'
              }
            ]
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.ImportManifest'),
            btns: [
              {
                name: this.$t('content.downloadTemplate'),
                method: 'download_template',
                link: 'template_manifest_clearance.xlsx?t=1',
                auth: 'order/im/download_template'
              },
              {
                name: this.$t('content.ImportOrder'),
                method: 'upload_excel_manifest',
                not_checked: true,
                import: true,
                auth: 'order/im/upload_excel_manifest'
              }
            ]
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.export'),
            btns: [
              {
                name: this.$t('content.ExportCustomsClearanceOrder'),
                method: 'download_excel',
                auth: 'customer/cco/download_excel'
              },
              {
                name: this.$t('content.ExportTheOperationSummaryTable'),
                method: 'download_excel_general_list',
                auth: 'customer/cco/download_excel_general_list'
              }
            ]
          },
          {
            name: this.$t('content.PushToSupplier'),
            method: 'push_orders',
            comfirm: true,
            auth: 'customsClearance/cco/push_orders'
          },
          {
            name: this.$t('content.SupplierCancelsWaybill'),
            method: 'cancellation_order',
            comfirm: true,
            auth: 'customsClearance/cco/cancellation_order'
          },
          {
            name: this.$t('content.SyncVasOrder'),
            method: 'submit_vasOrder',
            comfirm: true,
            auth: 'customsClearance/cco/submit_vasOrder'
          }
        ],
        data: [
          // {
          //   prop: 'aging',
          //   label: 'SLA',
          //   width: '90px',
          //   color: 'red',
          //   fontSize: '50px',
          //   condition: { aging_status: ['0'] }
          // },
          {
            type: 'multiRow',
            label: this.$t('content.Order'),
            width: '170px',
            data: [
              {
                prop: 'order_num',
                label: this.$t('content.orderNum'),
                link: '/customsClearance/customsClearanceOrder/detail'
              },
              {
                prop: 'mawb',
                label: 'MAWB'
              },
              {
                prop: 'clear_status',
                label: this.$t('content.orderStatus'),
                filter: {
                  'DR': '草稿',
                  'WO': '已下单',
                  'CO': '已审核',
                  'HI': '航班/船只已到达',
                  'DH': 'GHA已发NOA时间',
                  'NT': '仓库收到NOA时间',
                  'TH': 'GHA显示DLV时间',
                  'AW': '仓库实际收货时间',
                  'WN': '仓库通知清关时间',
                  'PA': '部分到仓',
                  'PC': '部分清关',
                  'RP': '已收到POD/CRM',
                  'RC': '已收到清关文件',
                  'CC': '清关完成',
                  'ZY': '转运车已发',
                  'FC': '已到转运仓',
                  'UF': '上传POD/CRM完成',
                  'VO': '作废'
                }
              },
              {
                prop: 'customer_name',
                label: this.$t('content.customers')
              }
            ]
          },
          {
            label: this.$t('content.Product'),
            type: 'multiRow',
            width: '150px',
            data: [
              {
                prop: 'product_name',
                label: this.$t('content.Product'),
                width: '170px'
              }
              // {
              //   prop: 'supplier_name',
              //   label: this.$t('content.supplier')
              // }
            ]
          },
          {
            type: 'multiRow',
            label: this.$t('content.Settlement'),
            width: '150px',
            data: [
              {
                prop: 'income',
                label: this.$t('content.TotalIncome'),
                width: '120px',
                color: '#409EFF',
                condition: { is_revenue_lock: true }
              },
              {
                prop: 'cost',
                label: this.$t('content.cost'),
                width: '120px',
                color: '#409EFF',
                condition: { is_cost_lock: true }
              },
              {
                prop: 'gross_profit',
                label: this.$t('content.GrossProfit'),
                width: '120px',
                otherProp: 'gross_currency'
              }
            ]
          },
          {
            label: this.$t('content.totalOrder'),
            type: 'multiRow',
            width: '210px',
            data: [
              {
                prop: 'estimated_time_arrival',
                label: this.$t('content.ExpectArrivalDate')
              },
              {
                prop: 'airline_num',
                label: this.$t('content.flightNum'),
                blankHide: true
              }
            ]
          },
          {
            label: this.$t('content.Date'),
            type: 'multiRow',
            width: '164px',
            data: [
              {
                prop: 'actual_arrivals_date',
                label: this.$t('content.ActualArrivalDate')
              },
              {
                prop: 'noa_date',
                label: this.$t('content.TimeOfArrivalNotice')
              },
              {
                prop: 'pickup_date',
                label: this.$t('content.AirportPickupTime')
              },
              {
                prop: 'clearance_date',
                label: this.$t('content.CustomsClearanceCompletionTime')
              }
            ]
          },
          {
            prop: 'remark',
            label: this.$t('content.remark'),
            api: '/api/customsClearanceOrders/modify_remark/',
            type: 'popover',
            width: '150px'
          },
          {
            label: this.$t('content.goods'),
            type: 'multiRow',
            width: '100px',
            data: [
              {
                prop: 'carton',
                label: this.$t('content.EstimatedNumberOfBoxes')
              },
              {
                prop: 'pre_package_num',
                label: this.$t('content.EstimatedNumberOfParcels')
              },
              {
                prop: 'weight',
                label: this.$t('content.EstimatedWeight'),
                fixZero: true
              }
            ]
          }
        ] }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.init()
    // })
  },
  methods: {
    parseTime,
    checkPermission,
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort }
      if (value) { this.params['search'] = value }
      return true
    },
    subDelete(id) {
      this.delLoading = true
      const p = {
        id,
        api: this.formData.api
      }
      del(p).then(res => {
        this.delLoading = false
        this.$refs[id].doClose()
        this.init()
        this.$notify({
          title: this.$t('content.deletedSuccessfully'),
          type: 'success',
          duration: 2500
        })
      }).catch(err => {
        this.delLoading = false
        this.$refs[id].doClose()
        console.log(err.response.data.message)
      })
    },
    selectionChange(val) {
      this.formData.checkData = val
    },
    // 统一action操作
    execute(item) {
      const { method, data, ids } = item

      if (ids.length === 0) {
        this.$message.error(this.$t('content.selectItem'))
        return
      }

      var f = () => {
        this.bus.$emit('fullLoading', true)
        const api = this.apiUrl + method + '/'

        if (method === 'download_excel') {
          const responseType = 'blob'
          actionPost({ api, data: { ids, ...data }, responseType }).then(res => {
            const time = new Date()
            axiosExport(res, '清关订单_' + parseTime(time) + '.xlsx')
            this.$message.success('导出成功')
            this.bus.$emit('fullLoading', false)
          })
          return
        }
        if (method === 'download_excel_general_list') {
          const responseType = 'blob'
          actionPost({ api, data: { ids, ...data }, responseType }).then(res => {
            const time = new Date()
            axiosExport(res, '运营总表_' + parseTime(time) + '.xlsx')
            this.$message.success('导出成功')
            this.bus.$emit('fullLoading', false)
          })
          return
        }

        actionPost({ api, data: { ids, ...data }}).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg || res.message || 'Successful operation')
            this.init()
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => { this.bus.$emit('fullLoading', false) })
      }

      const comfirm = (this.formData.action.find(i => i.method === method) || {})['comfirm']
      if (comfirm) {
        this.$confirm('确定执行此操作, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        }).then(() => {
          f()
        }).catch(() => {})
      } else {
        f()
      }
    },
    // 导入Excel
    uploadExcel(data) {
      const api = this.apiUrl + data.item.method + '/'
      const formData = new FormData()
      formData.append('file', data.file)
      actionPost({ api, data: formData }).then(res => {
        if (res.code === 200) {
          this.$message.success('导入成功！')
          this.init()
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
