import common from '@/components/Detail/common'

export default {
  components: {
    common
  },
  data() {
    return {
      initObject: {
        deteleDisabled: true,
        modifyBtnShow: {
          $is_revenue_lock: true,
          $is_cost_lock: true,
          order_status: ['DR', 'WO', 'INBOUND']
        },
        deleteProp: ['address_num'],
        isADD: false,
        // 请求url
        requestUrl: {
          baseUrl: 'parcelCustomerOrders'
        },
        // 表单数据
        initData: [
          {
            label: this.$t('content.basicInformation'),
            formDataInit: [
              {
                label: this.$t('content.orderNum'),
                prop: 'order_num',
                disabled: true,
                addHiden: true
              },
              {
                label: this.$t('content.status'),
                prop: 'order_status',
                filter: {
                  'DR': '草稿',
                  'VO': '已作废',
                  'WO': '等待作业',
                  'INBOUND': '已入库',
                  'OUTBOUND': '已出库',
                  'FC': '完成'
                },
                type: 'select',
                addHiden: true,
                disabled: true
              },
              {
                label: this.$t('content.customerOrderNumber'),
                prop: 'customer_order_num'
              },
              {
                label: this.$t('content.customers'),
                prop: 'customer',
                type: 'select'
              },
              {
                label: this.$t('content.warehouseCode'),
                prop: 'warehouse_code',
                type: 'select'
              },
              {
                label: this.$t('content.Product'),
                prop: 'product',
                type: 'select',
                getSelectDataVal: 'service',
                setDefaultVal: true,
                splice: true,
                apis: {
                  api: '/api/services/?page=1&size=10000&ordering=-id&product='
                },
                change: [
                  {
                    prop: 'warehouse_code',
                    label: 'warehouse_code',
                    type: 'getValue'
                  }
                ]
              },
              {
                label: this.$t('content.ProductService'),
                prop: 'service',
                type: 'select',
                addHiden: true
              },
              {
                prop: 'order_time',
                label: this.$t('content.orderTime'),
                type: 'datetime',
                addHiden: true,
                disabled: true
              },
              {
                label: this.$t('content.weight'),
                prop: 'weight'
              },
              {
                label: this.$t('content.Volume'),
                prop: 'volume',
                disabled: true
              },
              {
                label: this.$t('content.NuclearWeight'),
                prop: 'weighing_weight',
                disabled: true,
                addHiden: true
              },
              {
                label: this.$t('content.ConversionRate'),
                prop: 'charge_trans',
                disabled: true,
                addHiden: true
              },
              {
                label: this.$t('content.ChargeableWeight'),
                prop: 'charge_weight',
                disabled: true,
                addHiden: true
              },
              { label: this.$t('content.AgTrackingNumber'),
                prop: 'label_billid',
                disabled: true,
                addHiden: true
              },
              {
                label: this.$t('content.trackingNum'),
                prop: 'tracking_num',
                disabled: true,
                addHiden: true
              },
              {
                label: this.$t('content.PartitionValue'),
                prop: 'zone_value',
                disabled: true,
                addHiden: true
              },
              {
                label: this.$t('content.WarehousingTime'),
                prop: 'inbound_time',
                type: 'datetime',
                addHiden: true,
                disabled: true
              },
              {
                prop: 'order_type',
                label: '',
                type: 'hidden',
                filter: true,
                default: true,
                defaultVal: 'CC',
                hidden: true
              }
            ]
          },
          {
            label: this.$t('content.Sender_1'),
            formDataInit: [
              {
                label: this.$t('content.recipient'),
                prop: 'buyer_name'
              },
              {
                label: this.$t('content.RecipientEmailAddress'),
                prop: 'buyer_mail'
              },
              {
                label: this.$t('content.RecipientPhone'),
                prop: 'buyer_phone',
                maxlength: 15
              },
              {
                label: this.$t('content.RecipientCountry'),
                prop: 'buyer_country_code'
              },
              {
                label: this.$t('content.RecipientState'),
                prop: 'buyer_state'
              },
              {
                label: this.$t('content.RecipientCity'),
                prop: 'buyer_city_code'
              },
              {
                label: this.$t('content.postcode'),
                prop: 'buyer_postcode'
              },
              {
                label: this.$t('content.houseNumber'),
                prop: 'buyer_house_num'
              },
              {
                label: this.$t('content.Address1'),
                prop: 'buyer_address_one'
              },
              {
                label: this.$t('content.Address2'),
                prop: 'buyer_address_two'
              },
              {
                label: this.$t('content.CompanyName_1'),
                prop: 'buyer_company_name'
              },
              {
                label: this.$t('content.IossNumber'),
                prop: 'ioss_num'
              },
              {
                label: 'tax',
                prop: 'buyer_tax'
              }
            ]
          },
          {
            label: this.$t('content.goodsRelated'),
            formDataInit: [
              {
                type: 'tabTable',
                onlyAll: true,
                tabValue: '0',
                hasEditTable: true,
                tabData: [
                  {
                    datavalue: 'parcelItem',
                    label: this.$t('content.parcelAndGoods'),
                    type: 'editTable',
                    onlyAll: true,
                    data: [
                      { label: this.$t('content.caseNumber'), prop: 'parcel_num' },
                      { label: this.$t('content.parcelWeight'), prop: 'parcel_weight', simple: true },
                      { label: this.$t('content.parcelLength'), prop: 'parcel_length', simple: true },
                      { label: this.$t('content.parcelWidth'), prop: 'parcel_width', simple: true },
                      { label: this.$t('content.parcelHeight'), prop: 'parcel_height', simple: true },
                      { label: this.$t('content.parcelQty'), prop: 'parcel_qty', simple: true, onlySimle: true },
                      { label: this.$t('content.remark'), prop: 'remark', simple: true },
                      { label: this.$t('content.labelWeight'), prop: 'label_weight', hidden: true },
                      { label: this.$t('content.labelLength'), prop: 'label_length', hidden: true },
                      { label: this.$t('content.TypeSingleWidthCm'), prop: 'label_width', hidden: true },
                      { label: this.$t('content.labelHeight'), prop: 'label_height', hidden: true },
                      { label: this.$t('content.itemCode'), prop: 'item_code' },
                      { label: this.$t('content.declaredNameCN'), prop: 'declared_nameCN' },
                      { label: this.$t('content.declaredNameEN'), prop: 'declared_nameEN' },
                      { label: this.$t('content.declaredPrice'), prop: 'declared_price' },
                      { label: this.$t('content.quantity'), prop: 'item_qty' },
                      { label: this.$t('content.itemWeight'), prop: 'item_weight' },
                      { label: this.$t('content.texture'), prop: 'texture' },
                      { label: this.$t('content.use'), prop: 'use' },
                      { label: this.$t('content.brand'), prop: 'brand' },
                      { label: this.$t('content.model'), prop: 'model' },
                      { label: this.$t('content.customsCode'), prop: 'customs_code' },
                      { label: this.$t('content.fbaNo'), prop: 'fba_no', hidden: true },
                      { label: this.$t('content.fbaTrackCode'), prop: 'fba_track_code', hidden: true }
                    ]
                  },
                  {
                    datavalue: '$parce',
                    label: this.$t('content.packageSummary'),
                    data: [
                      { label: this.$t('content.parcelNum'), prop: 'parcel_num' },
                      { label: this.$t('content.trackingNum'), prop: 'tracking_num' },
                      { label: this.$t('content.Length'), prop: 'parcel_length' },
                      { label: this.$t('content.Width'), prop: 'parcel_width' },
                      { label: this.$t('content.Height'), prop: 'parcel_height' },
                      { label: this.$t('content.parcelQty'), prop: 'parcel_qty' },
                      { label: this.$t('content.weight'), prop: 'parcel_weight' },
                      // { label: this.$t('content.OrderWeight'), prop: 'label_weight' },
                      { label: this.$t('content.Volume'), prop: 'parcel_volume' },
                      { label: this.$t('content.remark'), prop: 'remark' }
                    ]
                  },
                  {
                    datavalue: '$goods',
                    label: this.$t('content.goodsSummary'),
                    data: [
                      { label: 'SKU', prop: 'item_code' },
                      { label: this.$t('content.declaredNameCn'), prop: 'declared_nameCN' },
                      { label: this.$t('content.declaredNameEn'), prop: 'declared_nameEN' },
                      { label: this.$t('content.DeclaredPrice'), prop: 'declared_price' },
                      { label: this.$t('content.MonetaryUnit'), prop: 'declared_currency' },
                      { label: this.$t('content.quantity'), prop: 'item_qty' },
                      { label: this.$t('content.weight'), prop: 'item_weight' },
                      { label: this.$t('content.texture'), prop: 'texture' },
                      // { label: this.$t('content.size'), prop: 'item_size' },
                      { label: this.$t('content.use'), prop: 'use' },
                      { label: this.$t('content.brand'), prop: 'brand' },
                      { label: this.$t('content.model'), prop: 'model' },
                      { label: this.$t('content.customsCode'), prop: 'customs_code' }
                    ]
                  },
                  {
                    datavalue: 'parcelOrderLabelTasks',
                    label: this.$t('content.labelTasks'),
                    data: [
                      { label: this.$t('content.labelDesc'), prop: 'label_desc' },
                      { label: this.$t('content.status'), prop: 'status', filter: {
                        UnHandled: '未处理',
                        Success: '处理成功',
                        HandledBy3rdNo: '根据第三方号码处理',
                        Failure: '处理失败',
                        VO: '作废'
                      }
                      },
                      { label: this.$t('content.thirdOrderNo'), prop: 'third_order_no' },
                      { label: this.$t('content.modeKey'), prop: 'mode_key' },
                      { label: this.$t('content.handleTimes'), prop: 'handle_times' }
                    ]
                  },
                  {
                    datavalue: 'tracks',
                    label: this.$t('content.trackInfo'),
                    data: [
                      { label: this.$t('content.time'), prop: 'actual_time' },
                      { label: this.$t('content.trackCode'), prop: 'track_code' },
                      { label: this.$t('content.trackName'), prop: 'track_name' },
                      { label: this.$t('content.Locations'), prop: 'location' },
                      { label: this.$t('content.roleDesc'), prop: 'remark' }
                    ],
                    action: {
                      add: 'select',
                      edit: true,
                      detele: true,
                      etcBtn: []
                    }
                  }
                ]
              }
            ]
          },
          {
            label: this.$t('content.incomeDetails'),
            formDataInit: [
              {
                label: this.$t('content.incomeDetails'),
                prop: 'parcel_customer_order_charge_in',
                type: 'table',
                datavalue: 'parcel_customer_order_charge_in',
                summary: true,
                singleEdit: true,
                singleEditTradition: {
                  is_revenue_lock: false
                },
                data: [
                  { label: this.$t('content.costName'), prop: 'charge', type: 'select' },
                  { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
                  { label: this.$t('content.quantity'), prop: 'charge_count' },
                  { label: this.$t('content.chargeTotal'), prop: 'charge_total', readOnly: true },
                  { label: this.$t('content.currencyType'), prop: 'currency_type', type: 'current', default: 'CNY' },
                  { label: this.$t('content.payer'), prop: 'customer', type: 'select' },
                  { label: this.$t('content.RevenueVersion'), prop: 'revenue_version_name', readOnly: true },
                  { label: this.$t('content.ChargeValue'), prop: 'charge_value', readOnly: true }
                ],
                action: {
                  // add: true,
                  // detele: true,
                  // edit: true
                }
              }
            ]
          },
          {
            label: this.$t('content.costDetails'),
            formDataInit: [
              {
                label: this.$t('content.costDetails'),
                prop: 'parcel_customer_order_charge_out',
                type: 'table',
                datavalue: 'parcel_customer_order_charge_out',
                summary: true,
                singleEdit: true,
                singleEditTradition: {
                  is_cost_lock: false
                },
                data: [
                  { label: this.$t('content.costName'), prop: 'charge', type: 'select' },
                  { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
                  { label: this.$t('content.quantity'), prop: 'charge_count' },
                  { label: this.$t('content.chargeTotal'), prop: 'charge_total', readOnly: true },
                  { label: this.$t('content.currencyType'), prop: 'currency_type', type: 'current' },
                  { label: this.$t('content.supplier'), prop: 'supplier', type: 'select' },
                  { label: this.$t('content.Share'), prop: 'is_share', type: 'radio', readOnly: true },
                  { label: this.$t('content.ShareNo'), prop: 'share_charge_id', readOnly: true },
                  { label: this.$t('content.CostVersion'), prop: 'cost_version_name', readOnly: true },
                  { label: this.$t('content.ChargeValue'), prop: 'charge_value', readOnly: true }
                ],
                action: {
                  // add: true,
                  // detele: true,
                  // edit: true
                }
              }
            ]
          }

        ],
        // 必填项
        rules: {
          // pre_carton: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          // pre_weight: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          // pre_volume: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }]
        },
        // 初始化下拉数据
        option: [
          { api: '/api/addresses/', prop: 'warehouse_code', label: 'address_num', value: '', query: { address_type: 'SP' }},
          { api: '/api/products/', prop: 'product', label: '', value: '', query: { type: 'PC', status: 'ON' }},
          { api: '/api/companies/list_company2/', prop: 'customer', label: 'short_name', value: '' }
        ],
        dialogOption: [
          { api: '/api/companies/list_company2/', prop: 'supplier', label: 'short_name', value: '', query: { is_supplier: true }},
          { api: '/api/companies/list_company2/', prop: 'customer', label: 'short_name', value: '', query: { is_customer: true }},
          { api: '/api/charges/', prop: 'charge', label: '', value: '' }
        ]
      }
    }
  },
  methods: {
    switchAdd(b) {
      this.initObject.isADD = b
    }
  }
}
