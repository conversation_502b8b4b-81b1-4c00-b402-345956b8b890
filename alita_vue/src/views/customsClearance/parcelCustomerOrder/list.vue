<template>
  <div class="app-container">
    <eHeader :role="ROLE" :is-detail="`${linkUrl}add`" :dialog-visible="dialogVisible" :form-data="formData" :query="query" @execute="execute" @toggleDialog="toggleDialog" @uploadExcel="uploadExcel"/>
    <!--表格渲染-->
    <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;" @selection-change="selectionChange">
      <el-table-column v-if="formData.check" type="selection" fixed width="40"/>
      <el-table-column v-for="item in formData.data" :sortable="item.sortable" :key="item.prop" :label="item.label" :min-width="item.width || ''">
        <template slot-scope="scope">
          <tablecolum :row="scope.row" :item="item"/>
        </template>
      </el-table-column>
      <el-table-column :label="$t('content.operation')" width="100px" align="center">
        <template slot-scope="scope">
          <edit v-if="checkPermission(ROLE)&&scope.row.order_status==='DR'&&!scope.row.is_revenue_lock&&!scope.row.is_cost_lock" :is-detail="`${linkUrl}detail?id=${scope.row.id}`" :form-data="formData" :data="scope.row" :sup_this="sup_this"/>
          <el-button
            slot="reference"
            type="success"
            size="mini"
            style="margin-top: 2px;"
            @click="copy(scope.row,formData)"
          >
            复制
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <el-pagination
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="total"
      :page-size="size"
      :current-page="page"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChange"
      @current-change="pageChange"/>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import initData from '@/mixins/initData'
import { del } from '@/api/data'
import { parseTime, print_label, axiosExport, get_previous_date } from '@/utils/index'
import eHeader from '@/components/InitTable/header'
import edit from '@/components/InitTable/edit'
import tablecolum from '@/components/InitTable/tablecolum'
import { actionPost } from '@/api/data'

export default {
  components: { eHeader, edit, tablecolum },
  mixins: [initData],
  data() {
    return {
      ROLE: ['ADMIN', 'ORDER'],
      delLoading: false,
      sup_this: this,
      linkUrl: '/customsClearance/parcelCustomerOrder/',
      apiUrl: '/api/parcelCustomerOrders/',
      requestUrl: { baseUrl: 'parcelCustomerOrders' },
      formData: {
        api: 'parcelCustomerOrders',
        title: this.$t('content.CustomsClearanceParcelOrder'),
        printTitle: this.$t('content.CustomsClearanceParcelOrder'),
        check: true,
        checkData: [],
        multiSearch: [
          {
            label: this.$t('content.orderNum'),
            prop: 'order_num'
          },
          {
            label: this.$t('content.trackingNum'),
            prop: 'tracking_num'
          },
          {
            label: this.$t('content.customerOrderNumber'),
            prop: 'customer_order_num'
          },
          {
            label: this.$t('content.thirdPartyServiceOrderNumber'),
            prop: 'third_orderNo'
          }
        ],
        searchholder: this.$t('content.OrderNumberChineseProductNameEnglishProductNameFlightNumberPortOfDestination'),
        filters: [
          {
            prop: 'customer',
            type: 'select',
            placeholder: this.$t('content.customers'),
            api: '/api/companies/list_company2/',
            query: {
              is_customer: true
            }
          },
          {
            prop: 'product',
            type: 'select',
            placeholder: this.$t('content.Product'),
            api: '/api/products/',
            query: {
              status: 'ON',
              type: 'PC'
            }
          },
          {
            prop: 'order_status',
            type: 'select',
            placeholder: this.$t('content.orderStatus'),
            value: '',
            data: [
              { id: 'DR', label: this.$t('content.draft') },
              { id: 'WO', label: this.$t('content.waitingJob') },
              { id: 'INBOUND', label: this.$t('content.BeInStorage') },
              { id: 'OUTBOUND', label: this.$t('content.OutOfStorage') },
              { id: 'FC', label: this.$t('content.Complete') },
              { id: 'VO', label: this.$t('content.void') }
            ]
          },
          {
            prop: 'order_time',
            type: 'datetimerange',
            placeholder: this.$t('content.orderTime'),
            value: [get_previous_date(15, true), get_previous_date(0, true, false)]
          }
        ],
        action: [
          {
            name: this.$t('content.submit'),
            method: 'submit_order',
            comfirm: true,
            auth: 'order/xbdgl/submit'
          },
          {
            name: this.$t('content.ConfigureCustomsClearancePackage'),
            method: 'set_customs_clearance_big_parcel_order',
            dialogProp: true,
            dialogType: 'table',
            defaultCheck: {
              // master_num_name: 'order_num'
            },
            api: '/api/customsClearanceBigParcelOrders/all_not_order/',
            colums: [
              { label: this.$t('content.parcelNum'), prop: 'order_num', width: '104px' },
              { label: this.$t('content.TapeColor'), prop: 'adhesive_tape_color' },
              { label: this.$t('content.UnpackOrNot'), prop: 'is_open_box' }
            ],
            auth: 'order/xbdgl/set_customs_clearance_big_parcel_order'
          },
          {
            name: this.$t('content.failOrder'),
            method: 'fail_order',
            comfirm: true,
            auth: 'order/xbdgl/fail_order'
          },
          {
            name: this.$t('content.RestoreOrder'),
            method: 'recovery_order',
            comfirm: true,
            auth: 'order/xbdgl/recovery_order'
          },
          {
            name: this.$t('content.CompleteTheOrder'),
            method: 'finish_order',
            comfirm: true,
            auth: 'order/xbdgl/finish_order'
          }
        ],
        data: [
          {
            type: 'multiRow',
            label: this.$t('content.Order'),
            width: '150px',
            data: [
              {
                prop: 'order_num',
                label: this.$t('content.orderNum'),
                link: '/customsClearance/parcelCustomerOrder/detail'
              },
              {
                prop: 'order_status',
                label: this.$t('content.orderStatus'),
                filter: {
                  'DR': '草稿',
                  'WO': '等待作业',
                  'FC': '完成',
                  'INBOUND': '已入库',
                  'OUTBOUND': '已出库',
                  'VO': '作废'
                }
              },
              {
                prop: 'customer_name',
                label: this.$t('content.customers')
              }
            ]
          },
          {
            label: this.$t('content.Product'),
            type: 'multiRow',
            width: '150px',
            data: [
              {
                prop: 'product_name',
                label: this.$t('content.Product'),
                width: '170px'
              },
              {
                prop: 'customer_order_num',
                label: this.$t('content.customerOrderNumber')
              }
            ]
          },
          {
            label: this.$t('content.Route'),
            type: 'multiRow',
            width: '150px',
            data: [
              {
                prop: 'warehouse_code_name',
                label: this.$t('content.warehouses')
              },
              {
                prop: 'buyer_name',
                label: this.$t('content.recipient')
              },
              {
                prop: 'buyer_postcode',
                label: this.$t('content.postcode')
              }
            ]
          },
          {
            label: this.$t('content.goods'),
            type: 'multiRow',
            width: '180px',
            data: [
              {
                prop: 'weight',
                label: this.$t('content.weight')
              },
              {
                prop: 'volume',
                label: this.$t('content.Volume')
              }
            ]
          },
          {
            label: this.$t('content.transport'),
            type: 'multiRow',
            width: '190px',
            data: [
              {
                prop: 'order_time',
                label: this.$t('content.orderTime'),
                type: 'datetime'
              }
            ]
          },
          {
            label: this.$t('content.totalOrder'),
            type: 'multiRow',
            width: '190px',
            data: [
              {
                prop: 'tracking_num',
                label: this.$t('content.trackingNum')
              },
              {
                prop: 'customs_clearance_big_parcel_order',
                label: this.$t('content.CustomsClearancePackage')
              }
            ]
          }
        ] }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.init()
    // })
    console.log(this.data)
    this.data.forEach((item) => {
      item.referenceType = false
    })
  },
  methods: {
    parseTime,
    checkPermission,
    copy(row, s) {
      console.log(s)
      this.$store.commit('editData', row)
      this.$router.push('/customsClearance/parcelCustomerOrder/detail')
    },
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort, order_type: 'CC' }
      if (value) { this.params['search'] = value }
      return true
    },
    intceptOrUnintceptOrder(order_num, types, id) {
      // 参数输出
      this.delLoading = true
      // 预备参数
      const params = {
        api: `${this.apiUrl}order_intercept/`,
        data: { order_num: order_num, types: types }
      }
      // 发起请求
      actionPost(params).then(res => {
        if (res.code === 200) {
          this.delLoading = false
          this.init()
          this.$notify({
            title: res.msg,
            type: 'success',
            duration: 2500
          })
          // this.visible = false
          this.$refs[id].doClose()
        } else {
          this.delLoading = false
          this.$message.error(res.msg || res.detail || res.message)
          this.$refs[id].doClose()
        }
      })
    },
    subDelete(id) {
      this.delLoading = true
      const p = {
        id,
        api: this.formData.api
      }
      del(p).then(res => {
        this.delLoading = false
        this.$refs[id].doClose()
        this.init()
        this.$notify({
          title: this.$t('content.deletedSuccessfully'),
          type: 'success',
          duration: 2500
        })
      }).catch(err => {
        this.delLoading = false
        this.$refs[id].doClose()
        console.log(err.response.data.message)
      })
    },
    selectionChange(val) {
      this.formData.checkData = val
    },
    // 统一action操作
    execute(item) {
      const { method, data, ids } = item
      if ((method === 'set_customs_clearance_big_parcel_order') && data.selectData.length > 1) {
        this.$message.error(`只能选择一个${method === 'set_master_num' ? '主' : '分'}单操作`)
        return
      }
      if (ids.length === 0) {
        this.$message.error(this.$t('content.selectItem'))
        return
      }

      var f = () => {
        this.bus.$emit('fullLoading', true)
        const api = this.apiUrl + method + '/'
        if (method === 'download_excel') {
          const responseType = 'blob'
          actionPost({ api, data: { ids, ...data }, responseType }).then(res => {
            const time = new Date()
            axiosExport(res, '小包订单_' + parseTime(time) + '.xlsx')
            this.$message.success('导出成功')
            this.bus.$emit('fullLoading', false)
          })
          return
        }

        actionPost({ api, data: { ids, ...data }}).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg || res.message || 'Successful operation')
            if (method === 'get_label') {
            // 下载面单
              var a = document.createElement('a')
              // const href = location.origin.replace('shipping', 'manage') + res.data.substring(18)
              a.setAttribute('href', res.data)
              a.setAttribute('target', '_blank')
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
            } else if (method === 'print_label') {
              print_label(res.base64)
            }
            this.init()
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => { this.bus.$emit('fullLoading', false) })
      }

      const comfirm = (this.formData.action.find(i => i.method === method) || {})['comfirm']
      if (comfirm) {
        this.$confirm('确定执行此操作, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        }).then(() => {
          f()
        }).catch(() => {})
      } else {
        f()
      }
    },
    // 导入Excel
    uploadExcel(data) {
      const api = this.apiUrl + data.item.method + '/'
      const formData = new FormData()
      formData.append('file', data.file)
      actionPost({ api, data: formData }).then(res => {
        if (res.code === 200) {
          this.$message.success('导入成功！')
          this.init()
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
