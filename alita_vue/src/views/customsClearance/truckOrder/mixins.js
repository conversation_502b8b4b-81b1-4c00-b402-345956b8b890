import common from '@/components/Detail/common'

export default {
  components: {
    common
  },
  data() {
    return {
      initObject: {
        isADD: false,
        // 请求url
        requestUrl: {
          baseUrl: 'customsClearanceTruckOrders'
          // add: '',
          // get: '',
          // del: '',
          // put: ''
        },
        // 表单数据
        initData: [
          {
            label: this.$t('content.basicInformation'),
            formDataInit: [
              {
                label: this.$t('content.TruckOrderNo'),
                prop: 'truck_order_num',
                disabled: true
              },
              {
                label: this.$t('content.status'),
                prop: 'status',
                filter: {
                  'DR': '草稿',
                  'WO': '已提交',
                  'FC': '已完成',
                  'VO': '作废'
                },
                disabled: true,
                type: 'select',
                defaultVal: 'DR',
                default: true
              },
              {
                label: this.$t('content.LicensePlateNumber'),
                prop: 'car_number'
              },
              {
                label: this.$t('content.DriversName'),
                prop: 'driver_name'
              },
              {
                label: this.$t('content.remark'),
                prop: 'remark',
                type: 'textarea'
              },
              {
                label: 'POD',
                prop: 'pod_file',
                type: 'files3'
              },
              {
                label: 'CRM',
                prop: 'crm_file',
                type: 'files3'
              }
            ]
          },
          {
            label: this.$t('content.TransportInformation'),
            formDataInit: [
              {
                label: this.$t('content.TruckReservationTime'),
                prop: 'booking_truck_date',
                type: 'datetime'
              },
              {
                label: this.$t('content.operatingTime'),
                prop: 'operation_time',
                type: 'datetime'
              },
              {
                label: this.$t('content.EstimatedTimeOfDeparture'),
                prop: 'estimated_time_departure',
                type: 'datetime'
              },
              {
                label: this.$t('content.EstimatedArrivalTime'),
                prop: 'estimated_time_arrival',
                type: 'datetime'
              },
              {
                label: this.$t('content.ActualDepartureTime'),
                prop: 'actual_leave_date',
                type: 'datetime',
                addHiden: true
              },
              {
                label: this.$t('content.ActualArrivalDate_1'),
                prop: 'actual_arrivals_date',
                type: 'datetime',
                addHiden: true
              }
            ]
          },
          {
            label: this.$t('content.CargoInformation'),
            formDataInit: [

              {
                label: this.$t('content.parcelQty'),
                prop: 'carton'
              },
              {
                label: this.$t('content.NumberOfParcels'),
                prop: 'package_num'
              },
              {
                label: this.$t('content.ActualWeight'),
                prop: 'weight',
                addHiden: true
              }
            ]
          },
          {
            label: this.$t('content.AddressInformation'),
            formDataInit: [
              {
                label: this.$t('content.TheConsigneeOfTheBillOfLadingIsAwbconsignee'),
                prop: 'shipper',
                type: 'dialogselect',
                textVal: ''
              },
              {
                label: this.$t('content.TranshipmentBin'),
                prop: 'receiver',
                type: 'dialogselect',
                textVal: ''
              }
            ]
          },
          {
            label: this.$t('content.CostExpense'),
            formDataInit: [
              {
                label: this.$t('content.CostExpense'),
                prop: 'truckChargeOuts',
                type: 'table',
                datavalue: 'truckChargeOuts',
                data: [
                  { label: this.$t('content.Expense'), prop: 'charge', type: 'select' },
                  { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
                  { label: this.$t('content.quantity'), prop: 'charge_count' },
                  { label: this.$t('content.chargeTotal'), prop: 'charge_total', readOnly: true },
                  { label: this.$t('content.currencyType'), prop: 'currency_type', type: 'current', default: 'CNY' },
                  { label: this.$t('content.supplier'), prop: 'supplier', type: 'select' }
                ],
                action: {
                  add: true,
                  detele: true,
                  edit: true
                }
              }
            ]
          },
          {
            label: this.$t('content.CustomerOrder'),
            formDataInit: [
              {
                label: this.$t('content.CustomerOrder'),
                prop: 'relate_trucks',
                type: 'table',
                datavalue: 'relate_trucks',
                data: [
                  { prop: 'clearance_num', label: this.$t('content.orderNum'), link: '/customsClearance/customsClearanceOrder/detail' },
                  { prop: 'departure', label: this.$t('content.departurePort') },
                  { prop: 'destination', label: this.$t('content.destinationPort') },
                  { prop: 'airline_num', label: this.$t('content.flightNum') },
                  { prop: 'carton', label: this.$t('content.parcelQty') },
                  { prop: 'weight', label: this.$t('content.weight') }
                ],
                action: {
                  etcBtn: [
                    {
                      label: this.$t('content.Remove'),
                      event: 'remove_order',
                      comfirm: true,
                      comfirmMsg: '确定移除该客户订单？'
                    }
                  ]
                }
              }
            ]
          }
        ],
        // 必填项
        rules: {
          car_number: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          driver_name: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }]
          // customer: [{ required: true, message: this.$t('content.pleaseSelect'), trigger: 'change' }],
          // relative_debits: [{ required: true, message: this.$t('content.pleaseSelect'), trigger: 'change' }]
        },
        // 初始化下拉数据
        option: [
          { api: '/api/companies/list_company2/', prop: 'supplier', label: '', value: '' },
          { api: '/api/addresses/', prop: 'shipper', label: 'address_num', value: '', query: { address_type: 'TD' }},
          { api: '/api/addresses/', prop: 'receiver', label: 'address_num', value: '', query: { address_type: 'ZW' }}
        ],
        dialogOption: [
          { api: '/api/companies/list_company2/', prop: 'supplier', label: '', value: '', query: { is_supplier: true }},
          { api: '/api/charges/', prop: 'charge', label: '', value: '' }
        ]
      }
    }
  },
  methods: {
    switchAdd(b) {
      this.initObject.isADD = b
    }
  }
}
