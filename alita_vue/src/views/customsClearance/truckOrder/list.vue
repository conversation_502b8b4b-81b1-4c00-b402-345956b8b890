<template>
  <div class="app-container">
    <eHeader :role="ROLE" :is-detail="`${linkUrl}add`" :form-data="formData" :query="query" @execute="execute"/>
    <!--表格渲染-->
    <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;" @selection-change="selectionChange">
      <el-table-column v-if="formData.check" type="selection" fixed width="40"/>
      <el-table-column v-for="item in formData.data" :sortable="item.sortable" :key="item.prop" :label="item.label" :min-width="item.width || ''">
        <template slot-scope="scope">
          <tablecolum :row="scope.row" :item="item"/>
        </template>
      </el-table-column>
      <el-table-column :label="$t('content.operation')" width="150px" align="center">
        <template slot-scope="scope">
          <edit v-if="checkPermission(ROLE)" :is-detail="`${linkUrl}detail?id=${scope.row.id}`" :form-data="formData" :data="scope.row" :sup_this="sup_this"/>
          <el-popover
            v-if="checkPermission(ROLE) && scope.row.status==='DR'"
            :ref="scope.row.id"
            placement="top"
            width="180">
            <p>{{ $t('content.sureDeleteThisData') }}</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">{{ $t('content.cancel') }}</el-button>
              <el-button :loading="delLoading" type="primary" size="mini" @click="subDelete(scope.row.id)">{{ $t('content.Ok') }}</el-button>
            </div>
            <el-button slot="reference" type="danger" size="mini">{{ $t('content.delete') }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <el-pagination
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="total"
      :page-size="size"
      :current-page="page"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChange"
      @current-change="pageChange"/>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import initData from '@/mixins/initData'
import { del } from '@/api/data'
import { parseTime } from '@/utils/index'
import eHeader from '@/components/InitTable/header'
import edit from '@/components/InitTable/edit'
import tablecolum from '@/components/InitTable/tablecolum'
import { actionPost } from '@/api/data'

export default {
  components: { eHeader, edit, tablecolum },
  mixins: [initData],
  data() {
    return {
      ROLE: ['ADMIN', 'TRANSPORT'],
      delLoading: false,
      sup_this: this,
      linkUrl: '/customsClearance/truckOrder/',
      apiUrl: '/api/customsClearanceTruckOrders/',
      formData: {
        api: 'customsClearanceTruckOrders',
        title: '',
        check: true,
        checkData: [],
        action: [

          {
            name: this.$t('content.submitOrder'),
            method: 'submit_order',
            comfirm: true,
            auth: 'truckorder/ccto/submit'
          },
          {
            name: this.$t('content.CompleteTheOrder'),
            method: 'finish_order',
            comfirm: true,
            auth: 'truckorder/ccto/finish'
          },
          {
            name: this.$t('content.failOrder'),
            method: 'cancel_order',
            comfirm: true,
            auth: 'truckorder/ccto/cancel_order'
          }

        ],
        data: [
          {
            prop: 'truck_order_num',
            label: this.$t('content.TruckOrderNo'),
            link: '/customsClearance/truckOrder/detail'
          },
          {
            prop: 'car_number',
            label: this.$t('content.LicensePlateNumber')
          },
          {
            prop: 'driver_name',
            label: this.$t('content.DriversName')
          },
          {
            prop: 'status',
            label: this.$t('content.orderStatus'),
            filter: {
              'DR': '草稿',
              'WO': '已提交',
              'FC': '订单完成',
              'VO': '作废'
            }
          },
          {
            prop: 'carton',
            label: this.$t('content.totalNum')
          },
          {
            prop: 'weight',
            label: this.$t('content.weight')
          },
          {
            prop: 'volume',
            label: this.$t('content.Volume')
          },
          {
            prop: 'charge_weight',
            label: this.$t('content.ChargeableWeight')
          },
          {
            prop: 'trans_weight',
            label: this.$t('content.conversionRate')
          },
          {
            prop: 'total_charge_in',
            label: this.$t('content.GrossIncome')
          },
          {
            prop: 'total_charge_out',
            label: this.$t('content.TotalSpending')
          },
          {
            prop: 'remark',
            label: this.$t('content.remark')
          }
        ] }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.init()
    // })
  },
  methods: {
    parseTime,
    checkPermission,
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort }
      if (value) { this.params['search'] = value }
      return true
    },
    subDelete(id) {
      this.delLoading = true
      const p = {
        id,
        api: this.formData.api
      }
      del(p).then(res => {
        this.delLoading = false
        this.$refs[id].doClose()
        this.init()
        this.$notify({
          title: this.$t('content.deletedSuccessfully'),
          type: 'success',
          duration: 2500
        })
      }).catch(err => {
        this.delLoading = false
        this.$refs[id].doClose()
        console.log(err.response.data.message)
      })
    },
    selectionChange(val) {
      this.formData.checkData = val
    },
    // 统一action操作
    execute(item) {
      const { method, ids } = item
      // const ids = this.formData.checkData.map(i => i.id)
      const api = this.apiUrl + method + '/'
      if (ids.length === 0) {
        this.$message.error(this.$t('content.selectItem'))
        return
      }
      this.bus.$emit('fullLoading', true)
      actionPost({ api, data: { ids }}).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg)
          this.init()
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
        this.bus.$emit('fullLoading', false)
      }).catch(() => { this.bus.$emit('fullLoading', false) })
    }
  }
}
</script>

<style scoped>

</style>
