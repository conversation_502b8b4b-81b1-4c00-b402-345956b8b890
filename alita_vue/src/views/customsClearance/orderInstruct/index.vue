<template>
  <div class="app-container">
    <eHeader :role="ROLE" :form-data="formData" :dialog-visible="dialogVisible" :query="query" @execute="execute" @toggleDialog="toggleDialog"/>
    <!--表格渲染-->
    <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;" @selection-change="selectionChange">
      <el-table-column v-if="formData.check" type="selection" fixed width="40"/>
      <el-table-column v-for="item in formData.data" :key="item.prop" :label="item.label" :min-width="item.width || ''">
        <template slot-scope="scope">
          <tablecolum :row="scope.row" :item="item"/>
        </template>
      </el-table-column>
      <el-table-column :label="$t('content.operation')" width="150px" align="center">
        <template slot-scope="scope">
          <edit v-if="checkPermission(ROLE)&&scope.row.order_status==='DR'" :form-data="formData" :data="scope.row" :sup_this="sup_this"/>
          <el-popover
            v-if="checkPermission(ROLE)"
            :ref="scope.row.id"
            placement="top"
            width="180">
            <p>{{ $t('content.sureDeleteThisData') }}</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">{{ $t('content.cancel') }}</el-button>
              <el-button :loading="delLoading" type="primary" size="mini" @click="subDelete(scope.row.id)">{{ $t('content.Ok') }}</el-button>
            </div>
            <!-- <el-button slot="reference" type="danger" size="mini">{{ $t('content.delete') }}</el-button>-->
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <el-pagination
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="total"
      :page-size="size"
      :current-page="page"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChange"
      @current-change="pageChange"/>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import initData from '@/mixins/initData'
import { del } from '@/api/data'
import { parseTime } from '@/utils/index'
import eHeader from '@/components/InitTable/header'
import edit from '@/components/InitTable/edit'
import tablecolum from '@/components/InitTable/tablecolum'
import { actionPost } from '@/api/data'

export default {
  components: { eHeader, edit, tablecolum },
  mixins: [initData],
  data() {
    return {
      ROLE: ['ADMIN', 'ACCOUNT'],
      delLoading: false,
      sup_this: this,
      apiUrl: '/api/orderInstructs/',
      formData: {
        api: 'orderInstructs',
        title: this.$t('content.rechargeOrder'),
        check: true,
        checkData: [],
        filters: [

        ],
        action: [
          {
            name: this.$t('content.submit'),
            method: 'submit_order',
            auth: 'cco/ddzl/submit_order'
          },
          {
            name: this.$t('content.Approved'),
            method: 'check_pass',
            auth: 'cco/ddzl/check_pass'
          },
          {
            name: this.$t('content.AuditFailure'),
            method: 'check_fail',
            auth: 'cco/ddzl/check_fail'
          },
          {
            name: this.$t('content.void'),
            method: 'cancel_order',
            auth: 'cco/ddzl/cancel_order'
          }
        ],
        data: [
          {
            prop: 'order_num',
            label: this.$t('content.AssociatedOrderNumber'),
            required: true
          },
          {
            prop: 'order_type',
            label: this.$t('content.type'),
            type: 'select',
            filter: {
              'IN': '入库单',
              'OUT': '出库单'
            },
            required: true
          },
          {
            prop: 'order_status',
            label: this.$t('content.status'),
            type: 'select',
            filter: {
              'DR': '草稿',
              'WO': '已提交',
              'TG': '通过',
              'JJ': '拒绝',
              'VO': '作废'
            },
            hidden: true
          },
          {
            prop: 'warehouse_code',
            label: this.$t('content.TransshipmentDestinationBinCode')
          },
          {
            prop: 'actual_package_num',
            label: this.$t('content.parcelQty'),
            required: true
          },
          {
            prop: 'carton',
            label: this.$t('content.NumberOfParcels'),
            required: true
          },
          {
            prop: 'operation_time',
            label: this.$t('content.operatingTime'),
            type: 'datetime',
            required: true
          },
          {
            prop: 'remark',
            label: this.$t('content.remark'),
            type: 'textarea'
          }
        ] }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.init()
    // })
  },
  methods: {
    parseTime,
    checkPermission,
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort }
      if (value) { this.params['search'] = value }
      return true
    },
    subDelete(id) {
      this.delLoading = true
      const p = {
        id,
        api: this.formData.api
      }
      del(p).then(res => {
        this.delLoading = false
        this.$refs[id].doClose()
        this.init()
        this.$notify({
          title: this.$t('content.deletedSuccessfully'),
          type: 'success',
          duration: 2500
        })
      }).catch(err => {
        this.delLoading = false
        this.$refs[id].doClose()
        console.log(err.response.data.message)
      })
    },
    selectionChange(val) {
      this.formData.checkData = val
    },
    // 统一action操作
    execute(item) {
      const { method, data, ids } = item
      if (ids.length === 0) {
        this.$message.error(this.$t('content.selectItem'))
        return
      }

      var f = () => {
        this.bus.$emit('fullLoading', true)
        const api = this.apiUrl + method + '/'
        actionPost({ api, data: { ids, ...data }}).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg || res.message || 'Successful operation')
            this.init()
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => { this.bus.$emit('fullLoading', false) })
      }

      const comfirm = (this.formData.action.find(i => i.method === method) || {})['comfirm']
      if (comfirm) {
        this.$confirm('确定执行此操作, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        }).then(() => {
          f()
        }).catch(() => {})
      } else {
        f()
      }
    }
  }
}
</script>

<style scoped>

</style>
