<template>
  <div class="app-container">
    <!-- <eHeader :role="ROLE" :form-data="formData" :query="query" @execute="execute" @uploadExcel="uploadExcel"/> -->
    <eHeader :role="ROLE" :form-data="formData" :query="query" @execute="execute"/>
    <!--表格渲染-->
    <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;" @selection-change="selectionChange">
      <el-table-column v-if="formData.check" type="selection" fixed width="40"/>
      <el-table-column v-for="item in formData.data" :key="item.prop" :label="item.label" :min-width="item.width || ''">
        <template slot-scope="scope">
          <tablecolum :row="scope.row" :item="item"/>
        </template>
      </el-table-column>
      <el-table-column :label="$t('content.operation')" width="150px" align="center">
        <template slot-scope="scope">
          <edit v-if="checkPermission(ROLE)" :form-data="formData" :data="scope.row" :sup_this="sup_this"/>
          <el-popover
            v-if="checkPermission(ROLE)"
            :ref="scope.row.id"
            placement="top"
            width="180">
            <p>{{ $t('content.sureDeleteThisData') }}</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">{{ $t('content.cancel') }}</el-button>
              <el-button :loading="delLoading" type="primary" size="mini" @click="subDelete(scope.row.id)">{{ $t('content.Ok') }}</el-button>
            </div>
            <el-button slot="reference" type="danger" size="mini">{{ $t('content.delete') }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <el-pagination
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="total"
      :page-size="size"
      :current-page="page"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChange"
      @current-change="pageChange"/>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import initData from '@/mixins/initData'
import { del } from '@/api/data'
import { parseTime, axiosExport } from '@/utils/index'
import eHeader from '@/components/InitTable/header'
import edit from '@/components/InitTable/edit'
import tablecolum from '@/components/InitTable/tablecolum'
import { actionPost } from '@/api/data'

export default {
  components: { eHeader, edit, tablecolum },
  mixins: [initData],
  data() {
    return {
      ROLE: ['ADMIN', 'PMS'],
      delLoading: false,
      sup_this: this,
      apiUrl: '/api/insuranceProductCostVersions/',
      formData: {
        api: 'insuranceProductCostVersions',
        title: this.$t('content.CostPriceVersion'),
        check: true,
        checkData: [],
        action: [
          // {
          //   type: 'selectBtn',
          //   value: '',
          //   placeholder: this.$t('content.import'),
          //   btns: [
          //     // {
          //     //   name: this.$t('content.downloadTemplate'),
          //     //   method: 'download_excel',
          //     //   auth: 'pms/productCostVersion/download_template'
          //     // },
          //     // {
          //     //   name: this.$t('content.import'),
          //     //   method: 'upload_excel',
          //     //   not_checked: true,
          //     //   import: true,
          //     //   auth: 'pms/productCostVersion/upload_excel'
          //     // }
          //   ]
          // }
        ],
        filters: [
          {
            prop: 'product',
            type: 'select',
            placeholder: this.$t('content.Product'),
            api: '/api/insuranceProducts/'
          },
          {
            prop: 'start_time',
            type: 'datetime',
            placeholder: this.$t('content.StartTime')
          }
        ],
        data: [
          {
            prop: 'version_name',
            label: this.$t('content.VersionName')
          },
          {
            prop: 'product',
            label: this.$t('content.Product'),
            option: 'name',
            type: 'select',
            options: '/api/insuranceProducts/',
            required: true
          },
          {
            prop: 'start_time',
            type: 'datetime',
            label: this.$t('content.StartTime')
          },
          {
            prop: 'remark',
            label: this.$t('content.remark'),
            type: 'textarea'
          }
        ] }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.init()
    // })
  },
  methods: {
    parseTime,
    checkPermission,
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort, price_type: 'COST' }
      if (value) { this.params['search'] = value }
      return true
    },
    selectionChange(val) {
      this.formData.checkData = val
    },
    subDelete(id) {
      this.delLoading = true
      const p = {
        id,
        api: this.formData.api
      }
      del(p).then(res => {
        this.delLoading = false
        this.$refs[id].doClose()
        this.init()
        this.$notify({
          title: this.$t('content.deletedSuccessfully'),
          type: 'success',
          duration: 2500
        })
      }).catch(err => {
        this.delLoading = false
        this.$refs[id].doClose()
        console.log(err.response.data.message)
      })
    },
    // 统一action操作
    execute(item) {
      const { method, data, ids } = item
      const responseType = method === 'download_excel' ? 'blob' : 'json'
      if (method === 'download_excel' && ids.length !== 1) {
        this.$message.error(`只能选择一个项进行操作`)
        return
      }

      var f = () => {
        this.bus.$emit('fullLoading', true)
        const api = this.apiUrl + method + '/'
        actionPost({ api, data: { ids, ...data, id: this.formData.checkData[0].id }, responseType }).then(res => {
          if (method === 'download_excel') {
            axiosExport(res.data, 'price_version.xlsx')
            this.init()
            return
          }
          if (res.code === 200) {
            this.$message.success(res.msg || res.message || 'Successful operation')
            this.init()
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => { this.bus.$emit('fullLoading', false) })
      }

      const comfirm = (this.formData.action.find(i => i.method === method) || {})['comfirm']
      if (comfirm) {
        this.$confirm('确定执行此操作, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        }).then(() => {
          f()
        }).catch(() => {})
      } else {
        f()
      }
    }
    // 导入Excel
    // uploadExcel(data) {
    //   if (this.formData.checkData.length !== 1) {
    //     this.$message.error('请选择一项进行上传')
    //     return
    //   }
    //   const api = this.apiUrl + data.item.method + '/'
    //   const formData = new FormData()
    //   formData.append('file', data.file)
    //   formData.append('id', this.formData.checkData[0].id)
    //   actionPost({ api, data: formData }).then(res => {
    //     if (res.code === 200) {
    //       this.$message.success('导入成功！')
    //       this.init()
    //     } else {
    //       this.$message.error(res.msg || res.detail || res.message)
    //     }
    //   })
    // }
  }
}
</script>

<style scoped>

</style>
