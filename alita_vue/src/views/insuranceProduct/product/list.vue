<template>
  <div class="app-container">
    <!--    头部-->
    <eHeader :role="ROLE" :is-detail="`${linkUrl}add`" :form-data="formData" :query="query" @execute="execute" @uploadExcel="uploadExcel"/>
    <!--表格渲染-->
    <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;" @selection-change="selectionChange">
      <el-table-column v-if="formData.check" type="selection" fixed width="40"/>
      <el-table-column v-for="item in formData.data" :key="item.prop" :label="item.label" :min-width="item.width || ''">
        <template slot-scope="scope">
          <tablecolum :row="scope.row" :item="item"/>
        </template>
      </el-table-column>
      <!-- <el-table-column v-if="!formData.readOnly" :label="$t('content.operation')" width="80px" align="center"> -->
      <!-- <template slot-scope="scope">
          <edit v-if="scope.row.debit_status===1||scope.row.debit_status===2&&checkPermission(ROLE)" :is-detail="`${linkUrl}detail?id=${scope.row.id}`" :form-data="formData" :data="scope.row" :sup_this="sup_this"/> -->
      <!--删除弹出框-->
      <!-- <el-popover
            v-if="checkPermission(ROLE)"
            :ref="scope.row.id"
            placement="top"
            width="180">
            <p>{{ $t('content.sureDeleteThisData') }}</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">{{ $t('content.cancel') }}</el-button>
              <el-button :loading="delLoading" type="primary" size="mini" @click="subDelete(scope.row.id)">{{ $t('content.Ok') }}</el-button>
            </div>
            <el-button slot="reference" type="danger" size="mini">{{ $t('content.delete') }}</el-button>
          </el-popover>
        </template> -->
      <!-- </el-table-column> -->
    </el-table>
    <!--分页组件-->
    <el-pagination
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="total"
      :page-size="size"
      :current-page="page"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChange"
      @current-change="pageChange"/>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import initData from '@/mixins/initData'
// import { dels } from '@/api/data'
import { parseTime } from '@/utils/index'
import eHeader from '@/components/InitTable/header'
import edit from '@/components/InitTable/edit'
import tablecolum from '@/components/InitTable/tablecolum'
import { actionPost } from '@/api/data'

export default {
  components: { eHeader, edit, tablecolum },
  mixins: [initData],
  data() {
    return {
      ROLE: ['ADMIN', 'SETTLE'],
      delLoading: false,
      sup_this: this,
      linkUrl: '/insuranceProduct/product/',
      apiUrl: '/api/insuranceProducts/',
      formData: {
        api: 'insuranceProducts',
        title: this.$t('content.InsuranceProduct'),
        check: true,
        // readOnly: false,
        // 过滤器
        filters: [
          {
            prop: 'butt_code',
            type: 'select',
            placeholder: this.$t('content.DockingCode'),
            api: '/api/supplierButts/'
          }
        ],
        checkData: [],
        action: [
        ],
        data: [
          {
            prop: 'name',
            label: this.$t('content.productName'),
            link: '/insuranceProduct/product/detail',
            required: true
          },
          {
            prop: 'code',
            label: this.$t('content.productCode'),
            required: true
          },
          {
            prop: 'butt_code_name',
            label: this.$t('content.DockingCode'),
            required: true
          },
          {
            prop: 'external_channel_code',
            label: this.$t('content.ExternalChannelCoding'),
            required: true
          },
          {
            prop: 'remark',
            label: this.$t('content.remark'),
            type: 'textarea'
          },
          {
            prop: 'revenue_lock',
            label: this.$t('content.IsRevenueLock'),
            type: 'boolean',
            filter: true
          },
          {
            prop: 'cost_lock',
            label: this.$t('content.IsCostLock'),
            type: 'boolean',
            filter: true,
            default: true
          },
          {
            prop: 'is_valuation',
            label: this.$t('content.RevenueOrNotPricing'),
            type: 'boolean',
            filter: true
          },
          {
            prop: 'is_cost_valuation',
            label: this.$t('content.CostOrNot'),
            type: 'boolean',
            filter: true,
            default: true
          }
        ] }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.init()
    // })
  },
  methods: {
    parseTime,
    checkPermission,
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort }
      if (value) { this.params['search'] = value }
      return true
    },
    // subDelete(id) {
    //   console.log('hhjksd', id)
    //   this.delLoading = true
    //   const p = {
    //     data: { id: id },
    //     api: this.formData.api + '/' + 'delete_product'
    //   }
    //   dels(p).then(res => {
    //     this.delLoading = false
    //     this.$refs[id].doClose()
    //     this.init()
    //     this.$notify({
    //       title: this.$t('content.deletedSuccessfully'),
    //       type: 'success',
    //       duration: 2500
    //     })
    //   }).catch(err => {
    //     this.delLoading = false
    //     this.$refs[id].doClose()
    //     console.log(err.response.data.message)
    //   })
    // },
    selectionChange(val) {
      this.formData.checkSumary.filter(i => !i.apiGet).forEach(i => { i.value = 0 })
      this.formData.checkData = val.map((i, index) => {
        this.formData.checkSumary.forEach(k => {
          k.value = this.cal.accAdd(k.value, (Number(i[k.prop]) || 0))
        })
        return i
      })
    },
    // 统一处理函数
    execute(item) {
      const { method, ids } = item
      // const ids = this.formData.checkData.map(i => i.id)
      const api = this.apiUrl + method + '/'
      if (ids.length === 0) {
        this.$message.error(this.$t('content.selectItem'))
        return
      }
      var f = () => {
        this.bus.$emit('fullLoading', true)
        actionPost({ api, data: { ids }}).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg)
            this.init()
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => { this.bus.$emit('fullLoading', false) })
      }

      const comfirm = (this.formData.action.find(i => i.method === method) || {})['comfirm']
      if (comfirm) {
        this.$confirm('确定执行此操作, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        }).then(() => {
          f()
        }).catch(() => {})
      } else {
        f()
      }
    }
    // 导入Excel
    // uploadExcel(data) {
    //   if (data.item.name === '导入') {
    //     const api = this.apiUrl + data.item.method + '/'
    //     const formData = new FormData()
    //     formData.append('file', data.file)
    //     actionPost({ api, data: formData }).then(res => {
    //       if (res.code === 200) {
    //         this.$message.success('导入成功！')
    //         this.init()
    //       } else {
    //         this.$message.error(res.detail || res.message)
    //       }
    //     })
    //   } else {
    //     const api = '/api/fedexStatement/'
    //     const formData = new FormData()
    //     formData.append('name', data.file.name)
    //     formData.append('url', data.file)
    //     formData.append('status', 1)
    //     formData.append('handler_msg', '订单正在后台处理中，请稍后查看')
    //     actionPost({ api, data: formData }).then(res => {
    //       if (res.code === 201 || res.code === 200) {
    //         this.$message.success(res.data.handler_msg)
    //         this.init()
    //       }
    //     })
    //   }
    // }
  }
}
</script>

<style scoped>

</style>
