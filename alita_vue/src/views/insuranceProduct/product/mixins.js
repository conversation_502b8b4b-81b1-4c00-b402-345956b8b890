import common from '@/components/Detail/common'
export default {
  components: {
    common
  },
  data() {
    return {
      additional_charge: null,
      initObject: {
        deteleDisabled: true,
        // readOnly: true,
        isADD: false,
        // 请求url
        requestUrl: {
          baseUrl: 'insuranceProducts'
        },
        // 表单底部传入按钮
        bottomBtn: [
          {
            name: this.$t('content.ImportProductInformation'),
            method: 'import_info',
            id: true,
            import: true
          },
          {
            name: this.$t('content.downloadTemplate'),
            id: true,
            link: 'template_product_info.xlsx',
            method: 'download_template'
          }
        ],
        // 表单数据
        initData: [
          {
            label: this.$t('content.basicInformation'),
            formDataInit: [
              {
                label: this.$t('content.productName'),
                prop: 'name'
              },
              {
                label: this.$t('content.productCode'),
                prop: 'code'
              },
              {
                label: this.$t('content.IsRevenueLock'),
                prop: 'revenue_lock',
                type: 'radio'
              },
              {
                label: this.$t('content.IsCostLock'),
                prop: 'cost_lock',
                type: 'radio'
              },
              {
                label: this.$t('content.RevenueOrNotPricing'),
                prop: 'is_valuation',
                type: 'radio'
              },
              {
                label: this.$t('content.CostOrNot'),
                prop: 'is_cost_valuation',
                type: 'radio'
              },
              {
                label: this.$t('content.DockingCode'),
                prop: 'butt_code',
                type: 'select',
                options: '/api/supplierButts/',
                option: 'name',
                required: true
              },
              {
                label: this.$t('content.ExternalChannelCoding'),
                prop: 'external_channel_code'
              },
              {
                label: '保司编码',
                prop: 'insurance_company_no'
              },
              {
                label: '保司方案代码',
                prop: 'insurance_options_code'
              },
              {
                label: '保司项目代码',
                prop: 'insurance_project_code'
              },
              {
                label: this.$t('content.remark'),
                prop: 'remark',
                type: 'textarea'
              }
            ]
          },
          {
            label: '',
            formDataInit: [
              {
                type: 'tabTable',
                tabValue: '0',
                tabData: [
                  {
                    label: this.$t('content.RevenuePriceVersion'),
                    datavalue: 'product_revenue_version',
                    action: {
                      add: 'select',
                      edit: true,
                      detele: true
                    },
                    data: [
                      { label: this.$t('content.VersionName'), prop: 'version_name' },
                      { label: this.$t('content.StartTime'), prop: 'start_time', type: 'datetime' }
                    ]
                  },
                  {
                    label: this.$t('content.CostPriceVersion'),
                    datavalue: 'product_cost_version',
                    action: {
                      add: 'select',
                      edit: true,
                      detele: true
                    },
                    data: [
                      { label: this.$t('content.VersionName'), prop: 'version_name' },
                      { label: this.$t('content.StartTime'), prop: 'start_time', type: 'datetime' }
                    ]
                  }
                ]
              }
            ]
          }
        ],
        // 必填项
        rules: {
          is_own: [{ required: true, message: this.$t('content.pleaseSelect'), trigger: 'change' }],
          type: [{ required: true, message: this.$t('content.pleaseSelect'), trigger: 'change' }],
          name: [{ required: true, message: this.$t('content.Required'), trigger: 'change' }],
          code: [{ required: true, message: this.$t('content.Required'), trigger: 'change' }]
        },
        // 初始化下拉数据
        option: [
          { api: '/api/supplierButts/', prop: 'butt_code', label: 'name' }

        ],
        // 弹窗下拉数据
        dialogOption: [
          { api: '/api/charges/', prop: 'charge', label: '', value: '' },
          { api: '/api/supplierButts/', prop: 'butt_code', label: '', value: '' },
          { api: '/api/companies/list_company2/', prop: 'supplier', label: '', value: '', query: { is_supplier: true }},
          { api: '/api/companies/list_company/', prop: 'customer', label: 'name', value: '', query: { is_customer: true }}
        ]
      }
    }
  },
  methods: {
    switchAdd(b) {
      this.initObject.isADD = b
    }
  }
}
