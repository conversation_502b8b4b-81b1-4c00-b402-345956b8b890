<template>
  <div class="app-container">
    <eHeader :role="ROLE" :form-data="formData" :query="query"/>
    <!--表格渲染-->
    <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;">
      <el-table-column v-for="item in formData.data" :key="item.prop" :label="item.label" :min-width="item.width || ''">
        <template slot-scope="scope">
          <tablecolum :row="scope.row" :item="item"/>
        </template>
      </el-table-column>
      <el-table-column v-if="!formData.readOnly" :label="$t('content.operation')" width="150px" align="center">
        <template slot-scope="scope">
          <edit v-if="checkPermission(ROLE)" :form-data="formData" :data="scope.row" :sup_this="sup_this"/>
          <el-popover
            v-if="checkPermission(ROLE)"
            :ref="scope.row.id"
            placement="top"
            width="180">
            <p>{{ $t('content.sureDeleteThisData') }}</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">{{ $t('content.cancel') }}</el-button>
              <el-button :loading="delLoading" type="primary" size="mini" @click="subDelete(scope.row.id)">{{ $t('content.confirm') }}</el-button>
            </div>
            <el-button slot="reference" type="danger" size="mini">{{ $t('content.delete') }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <el-pagination
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="total"
      :page-size="size"
      :current-page="page"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChange"
      @current-change="pageChange"/>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import initData from '@/mixins/initData'
import { del } from '@/api/data'
import { parseTime } from '@/utils/index'
import eHeader from '@/components/InitTable/header'
import edit from '@/components/InitTable/edit'
import tablecolum from '@/components/InitTable/tablecolum'

export default {
  components: { eHeader, edit, tablecolum },
  mixins: [initData],
  data() {
    return {
      ROLE: ['ADMIN', 'ACCOUNT'],
      delLoading: false,
      sup_this: this,
      formData: {
        api: 'accountDetailss',
        title: this.$t('content.accountDetails'),
        readOnly: true,
        multiSearch: [
          {
            label: this.$t('content.transactionNo'),
            prop: 'transaction_no'
          },
          {
            label: this.$t('content.customerOrderNo'),
            prop: 'customer_order_num'
          },
          {
            label: this.$t('content.ReferenceNo'),
            prop: 'ref_no'
          }
        ],
        filters: [
          {
            prop: 'account',
            type: 'select',
            placeholder: this.$t('content.customer'),
            api: '/api/accounts/',
            label: 'customer_name'
          },
          {
            prop: 'transaction_type',
            type: 'select',
            placeholder: this.$t('content.transactionType'),
            data: [
              { id: 'recharge', label: this.$t('content.recharge') },
              { id: 'deduction', label: this.$t('content.deductions') },
              { id: 'refund', label: this.$t('content.refund') },
              { id: 'withdraw', label: this.$t('content.Withdraw') }
            ]
          },
          {
            prop: 'currency',
            type: 'currency',
            placeholder: this.$t('content.currency')
          },
          {
            prop: 'create_date',
            type: 'daterange',
            placeholder: this.$t('content.transactionTime')
          }
        ],
        data: [
          {
            prop: 'account_name',
            label: this.$t('content.account')
          },
          {
            prop: 'transaction_type',
            label: this.$t('content.transactionType'),
            type: 'select',
            filter: {
              'recharge': this.$t('content.recharge'),
              'deduction': this.$t('content.deductions'),
              'refund': this.$t('content.refund'),
              'withdraw': this.$t('content.Withdraw')
            },
            required: true
          },
          {
            prop: 'amount_type',
            label: this.$t('content.amountType'),
            type: 'select',
            filter: {
              'Amount': this.$t('content.realAmount'),
              'RebateAmount': this.$t('content.rebateAmount'),
              'CreditLimit': this.$t('content.creditLimit')
            },
            required: true
          },
          {
            prop: 'amount',
            label: this.$t('content.amount'),
            required: true
          },
          {
            prop: 'currency',
            label: this.$t('content.transactionCurrency'),
            type: 'currency',
            required: true
          },
          {
            prop: 'transaction_no',
            label: this.$t('content.transactionNo')
          },
          {
            prop: 'customer_order_num',
            label: this.$t('content.customerOrderNo')
          },
          {
            prop: 'transaction_charge_name',
            label: this.$t('content.transactionChargeName')
          },
          {
            prop: 'exchange_rate',
            label: this.$t('content.exchangeRate')
          },
          {
            prop: 'account_currency',
            label: this.$t('content.accountCurrency'),
            type: 'currency'
          },
          {
            prop: 'account_charge_amount',
            label: this.$t('content.accountChargeAmount')
          },
          {
            prop: 'account_balance',
            label: this.$t('content.accountBalance')
          },
          {
            prop: 'create_date',
            label: this.$t('content.transactionTime'),
            type: 'datetime'
          },
          {
            prop: 'remark',
            label: this.$t('content.remark'),
            type: 'textarea'
          }
        ] }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.init()
    // })
  },
  methods: {
    parseTime,
    checkPermission,
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort }
      if (value) { this.params['search'] = value }
      return true
    },
    subDelete(id) {
      this.delLoading = true
      const p = {
        id,
        api: this.formData.api
      }
      del(p).then(res => {
        this.delLoading = false
        this.$refs[id].doClose()
        this.init()
        this.$notify({
          title: this.$t('content.deletedSuccessfully'),
          type: 'success',
          duration: 2500
        })
      }).catch(err => {
        this.delLoading = false
        this.$refs[id].doClose()
        console.log(err.response.data.message)
      })
    }
  }
}
</script>

<style scoped>

</style>
