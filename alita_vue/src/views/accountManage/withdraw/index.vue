<template>
  <div class="app-container">
    <eHeader :role="ROLE" :form-data="formData" :dialog-visible="dialogVisible" :query="query" @execute="execute" @toggleDialog="toggleDialog"/>
    <!--表格渲染-->
    <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;" @selection-change="selectionChange">
      <el-table-column v-if="formData.check" type="selection" fixed width="40"/>
      <el-table-column v-for="item in formData.data" :key="item.prop" :label="item.label" :min-width="item.width || ''">
        <template slot-scope="scope">
          <tablecolum :row="scope.row" :item="item"/>
        </template>
      </el-table-column>
      <el-table-column :label="$t('content.operation')" width="150px" align="center">
        <template slot-scope="scope">
          <edit v-if="checkPermission(ROLE)&&scope.row.status==='draft'" :form-data="formData" :data="scope.row" :sup_this="sup_this"/>
          <el-popover
            v-if="checkPermission(ROLE)"
            :ref="scope.row.id"
            placement="top"
            width="180">
            <p>{{ $t('content.sureDeleteThisData') }}</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">{{ $t('content.cancel') }}</el-button>
              <el-button :loading="delLoading" type="primary" size="mini" @click="subDelete(scope.row.id)">{{ $t('content.confirm') }}</el-button>
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <el-pagination
      :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      :total="total"
      :page-size="size"
      :current-page="page"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChange"
      @current-change="pageChange"/>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import initData from '@/mixins/initData'
import { del } from '@/api/data'
import { parseTime } from '@/utils/index'
import eHeader from '@/components/InitTable/header'
import edit from '@/components/InitTable/edit'
import tablecolum from '@/components/InitTable/tablecolum'
import { actionPost } from '@/api/data'

export default {
  components: { eHeader, edit, tablecolum },
  mixins: [initData],
  data() {
    return {
      ROLE: ['ADMIN', 'ACCOUNT'],
      delLoading: false,
      sup_this: this,
      apiUrl: '/api/withdraws/',
      formData: {
        api: 'withdraws',
        title: this.$t('content.withdrawOrder'),
        check: true,
        checkData: [],
        filters: [
          {
            prop: 'customer',
            type: 'select',
            placeholder: this.$t('content.customer'),
            api: '/api/companies/list_company2/',
            query: {
              is_customer: true
            }
          },
          {
            prop: 'currency',
            type: 'currency',
            placeholder: this.$t('content.currency')
          }
        ],
        action: [
          {
            name: this.$t('content.submit'),
            method: 'submit_recharge',
            auth: ''
          }
        ],
        data: [
          {
            prop: 'customer',
            label: this.$t('content.customer'),
            type: 'select',
            options: '/api/companies/list_company2/',
            query: {
              is_customer: true
            },
            required: true
          },
          {
            prop: 'order_num',
            label: this.$t('content.serialNumber'),
            hidden: true
          },
          {
            prop: 'status',
            label: this.$t('content.withdrawalStatus'),
            type: 'select',
            filter: {
              'draft': this.$t('content.draft'),
              'submitted': this.$t('content.submitted')
            },
            hidden: true
          },
          {
            prop: 'amount',
            label: this.$t('content.amount'),
            required: true
          },
          {
            prop: 'currency',
            label: this.$t('content.currency'),
            type: 'currency',
            required: true
          },
          {
            prop: 'third_transaction_no',
            label: this.$t('content.thirdTransactionNo')
          },
          {
            prop: 'remark',
            label: this.$t('content.remark'),
            type: 'textarea'
          }
        ]
      }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.init()
    // })
  },
  methods: {
    parseTime,
    checkPermission,
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort }
      if (value) { this.params['search'] = value }
      return true
    },
    subDelete(id) {
      this.delLoading = true
      const p = {
        id,
        api: this.formData.api
      }
      del(p).then(res => {
        this.delLoading = false
        this.$refs[id].doClose()
        this.init()
        this.$notify({
          title: this.$t('content.deletedSuccessfully'),
          type: 'success',
          duration: 2500
        })
      }).catch(err => {
        this.delLoading = false
        this.$refs[id].doClose()
        console.log(err.response.data.message)
      })
    },
    selectionChange(val) {
      this.formData.checkData = val
    },
    execute(item) {
      const { method, data, ids } = item
      if (ids.length === 0) {
        this.$message.error(this.$t('content.selectItem'))
        return
      }

      var f = () => {
        this.bus.$emit('fullLoading', true)
        const api = this.apiUrl + method + '/'
        actionPost({ api, data: { ids, ...data }}).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg || res.message || this.$t('content.success'))
            this.init()
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => { this.bus.$emit('fullLoading', false) })
      }

      const comfirm = (this.formData.action.find(i => i.method === method) || {})['comfirm']
      if (comfirm) {
        this.$confirm(this.$t('content.continue'), this.$t('content.tips'), {
          confirmButtonText: this.$t('content.confirm'),
          cancelButtonText: this.$t('content.cancel'),
          type: 'danger'
        }).then(() => {
          f()
        }).catch(() => {})
      } else {
        f()
      }
    }
  }
}
</script>

<style scoped>

</style>
