<template>
  <div class="app-container">
    <eHeader :role="ROLE" :is-detail="`${linkUrl}add`" :dialog-visible="dialogVisible" :form-data="formData" :query="query" @execute="execute" @toggleDialog="toggleDialog" @uploadExcel="uploadExcel"/>

    <el-tabs type="border-card" @tab-click="handleTabClick">
      <el-tab-pane
        v-loading="loading"
        v-for="item in orderStatus"
        :key="item.status"
        :label="item.info"
        :name="item.label"
        lazy
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <template v-if="data && data.length > 0">
          <!--表格渲染-->
          <el-table v-loading="loading" :data="data" size="small" border style="width: 100%;" @selection-change="selectionChange">
            <el-table-column v-if="formData.check" type="selection" fixed width="40"/>
            <el-table-column v-for="item in formData.data" :key="item.prop" :prop="item.prop" :label="item.label" :min-width="item.width || ''" :sortable="item.sortable">
              <template slot-scope="scope">
                <tablecolum :row="scope.row" :item="item"/>
              </template>
            </el-table-column>
            <el-table-column :label="$t('content.operation')" width="120px" align="center">
              <template slot-scope="scope">
                <!--控制是否显示编辑按钮-->
                <edit v-if="checkPermission(ROLE)&&!scope.row.is_revenue_lock&&!scope.row.is_cost_lock" :is-detail="`${linkUrl}detail?id=${scope.row.id}`" :form-data="formData" :data="scope.row" :sup_this="sup_this"/>
                <el-button v-if="checkPermission(ROLE)&&(!scope.row.is_revenue_lock||!scope.row.is_cost_lock)" style="margin-top: 5px;" size="mini" type="primary" @click="handleQuickInput(scope.row)">{{ $t('content.QuickEntry') }}</el-button>
                <el-button v-if="checkPermission(ROLE)" style="margin-top: 5px;" size="mini" type="info" @click="handleTrackInfo(scope.row)">{{ $t('content.viewTrack') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <el-pagination
            :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
            :total="total"
            :page-size="size"
            :current-page="page"
            style="margin-top: 8px;"
            layout="total, prev, pager, next, sizes"
            @size-change="sizeChange"
            @current-change="pageChange"/>
        </template>
        <template v-else>
          <div style="text-align: center; padding: 20px;">{{ $t('content.NoData') }}</div>
        </template>
      </el-tab-pane>
    </el-tabs>

    <charge-quick-input ref="chargeQuickInputRef" />
    <track-info-dialog ref="trackInfoDialogRef" />
  </div>
</template>

<script>
import { del } from '@/api/data'
import edit from '@/components/InitTable/edit'
import eHeader from '@/components/InitTable/header'
import TrackInfoDialog from '@/components/TrackInfoDialog'
import initData from '@/mixins/initData'
import { parseTime, print_label, print_label_with_name, axiosExport } from '@/utils/index'
import checkPermission from '@/utils/permission'
// import { actionPost, get, getChoiceData } from '@/api/data'
import { actionPost, getChoiceData } from '@/api/data'
import tablecolum from '@/components/InitTable/tablecolum'
import ChargeQuickInput from '@/components/ChargeQuickInput'

export default {
  components: { eHeader, edit, tablecolum, ChargeQuickInput, TrackInfoDialog },
  mixins: [initData],
  data() {
    return {
      loading: false,
      // chargeQuickInputRef: null,
      trackInfoDialogRef: null,
      current_status: '',
      orderNum: '',
      refNum: '',
      orderStatus: [
        // { status: 'DR', info: this.$t('content.draft') },
        // { status: 'WO', info: this.$t('content.waitingJob') },
        { status: '', info: this.$t('content.TotalData') },
        { status: 'PDC', info: this.$t('content.preDeclared') },
        { status: 'ITP', info: this.$t('content.intercepted') },
        { status: 'PW', info: this.$t('content.partiallyInWarehouse') },
        { status: 'AW', info: this.$t('content.fullyInWarehouse') },
        { status: 'CWED', info: this.$t('content.confirmedWarehouseData') },
        { status: 'OW', info: this.$t('content.outOfDomesticWarehouse') },
        { status: 'DEP', info: this.$t('content.departed') },
        { status: 'TF', info: this.$t('content.transfer') },
        { status: 'SF', info: this.$t('content.signed') },
        // { status: 'FC', info: this.$t('content.Complete') },
        { status: 'VO', info: this.$t('content.void') }
      ],
      bi_url_list: [
        'export_order',
        'export_client_confirmation',
        'export_order_data',
        'export_tracking_data',
        'export_pre_recorded_order',
        'export_saler_data',
        'export_timeliness_warehouse_fba',
        'export_timeliness_warehouse_parcel'
      ],
      ROLE: ['ADMIN', 'TRANSPORT'],
      delLoading: false,
      sup_this: this,
      linkUrl: '/transport/customerOrderTask_fba_zrh/',
      apiUrl: '/api/customerOrders/',
      formData: {
        api: 'customerOrders',
        title: this.$t('content.CustomerOrder'),
        check: true,
        checkData: [],
        multiSearch: [
          {
            label: this.$t('content.orderNum'),
            prop: 'order_num'
          },
          {
            label: this.$t('content.customerOrderNumber'),
            prop: 'ref_num'
          },
          { label: this.$t('content.parcelNum'), prop: 'parcel__parcel_num' },
          {
            label: this.$t('content.TransferOrderNo'),
            prop: 'tracking_num'
          },
          {
            label: 'Reference Id',
            prop: 'parcel__reference_id'
          },
          {
            label: 'Shipment Id',
            prop: 'parcel__shipment_id'
          }
        ],
        multiDefaultQuery: {
          order_type: 'FBA'
        },
        searchholder: this.$t('content.TrackingNumberCustomerTrackingNumberPackageNumberTransferNumber'),
        // 过滤器
        filters: [
          {
            // 为什么要改这里??
            prop: 'customer',
            type: 'select',
            show: 'nameAndShortName',
            placeholder: this.$t('content.customers'),
            // api: '/api/companies/list_company2/',
            api: '/api/companies/list_company2/',
            query: {
              is_customer: true
            }
          },
          {
            prop: 'product',
            type: 'select',
            placeholder: this.$t('content.Product'),
            api: '/api/products/',
            query: {
              status: 'ON',
              type: 'TR'
            }
          },
          {
            prop: 'logistics_planning',
            label: 'planning_code',
            type: 'select',
            placeholder: this.$t('content.LogisticsPlan'),
            api: '/api/logisticsPlanning/'
          },
          // {
          //   prop: 'order_status',
          //   type: 'select',
          //   placeholder: this.$t('content.orderStatus'),
          //   api: '/api/customerOrders/get_order_status/'
          // },
          {
            prop: 'is_intercept',
            type: 'boolean',
            placeholder: this.$t('content.InterceptOrNot')
          },
          // {
          //   prop: 'is_send_debit',
          //   type: 'boolean',
          //   placeholder: this.$t('content.BillSending')
          // },
          {
            prop: 'is_arrival_notice',
            type: 'boolean',
            placeholder: this.$t('content.WhetherTheGoodsHaveArrived')
          },
          {
            prop: 'is_customs_declaration',
            type: 'boolean',
            placeholder: this.$t('content.WhetherToDeclare')
          },
          {
            prop: 'is_config_order',
            placeholder: this.$t('content.WhetherToConfigureDocuments'),
            type: 'boolean'
          },
          {
            prop: 'is_sales_price_card',
            placeholder: this.$t('content.SalesPriceCard'),
            type: 'boolean'
          },
          {
            prop: 'is_sync_yqf',
            type: 'select',
            placeholder: this.$t('content.SynchronousState'),
            data: [
              // { id: 'UnSync', label: this.$t('content.Unsynchronized') },
              { id: 'UnHandled', label: this.$t('content.submitted') },
              { id: 'Success', label: this.$t('content.SuccessfulProcessing') },
              { id: 'Failure', label: this.$t('content.failure') },
              { id: 'VO', label: this.$t('content.void') }
            ]
          },
          {
            prop: 'is_revenue_lock',
            type: 'boolean',
            placeholder: this.$t('content.IsRevenueLock')
          },
          {
            prop: 'is_cost_lock',
            type: 'boolean',
            placeholder: this.$t('content.IsCostLock')
          },
          {
            prop: 'ocean_num',
            type: 'input',
            placeholder: this.$t('content.SeaWaybillNumber')
          },
          {
            prop: 'ocean_num__order_num',
            type: 'input',
            placeholder: this.$t('content.MaritimePriorityBillNumber')
          },
          {
            prop: 'arrival_date',
            type: 'daterange',
            placeholder: this.$t('content.estimatedArrivalDate')
          },
          {
            prop: 'create_date',
            type: 'daterange',
            placeholder: this.$t('content.orderTime')
          },
          {
            prop: 'check_in_time',
            type: 'daterange',
            placeholder: this.$t('content.CheckinTime')
          },
          {
            prop: 'receiver_name',
            type: 'input',
            placeholder: this.$t('content.WarehouseCode_1')
          },
          {
            prop: 'create_by',
            type: 'select',
            placeholder: this.$t('content.Operator'),
            api: '/api/users/',
            label: 'name'
          }
          // {
          //   prop: 'warehouse_type',
          //   type: 'select',
          //   placeholder: this.$t('content.WmsWarehouse'),
          //   width: '150px',
          //   data: [
          //     { id: 'CollectWarehouse', label: this.$t('content.CollectionBin') },
          //     { id: 'ConsolidationWarehouse', label: this.$t('content.consolidationWarehouse') }
          //   ]
          // }
        ],
        checkSumary: [
          {
            title: '',
            prop: 'carton',
            value: '0CTNS/0KGS/0CBM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0CTNS/0KGS/0CBM',
            color: '#3c3838'
          }
        ],
        action: [
          {
            name: this.$t('content.TrajectoryChange'),
            dateType: 'datetime',
            method: 'change_track',
            dialogProp: true,
            dialogType: 'selectAciton',
            colums: '/api/customerOrders/get_product_track_code/',
            query: { id: 'id$', affiliated_track: 'P' },
            auth: 'transport/ysdzy/change_track'
          },
          {
            name: this.$t('content.ForceAllWarehousing'),
            method: 'complete_warehousing',
            comfirm: true,
            auth: 'order/ysdgl/complete_warehousing'
          },
          {
            name: this.$t('content.Redenomination'),
            method: 'revaluation',
            comfirm: true,
            auth: 'order/ysdgl/revaluation'
          },
          {
            name: this.$t('content.BatchUpdate'),
            method: 'batch_update_order',
            dialogProp: true,
            dialogType: 'form',
            formData: {},
            dialogForm: [
              {
                label: this.$t('content.Product'),
                prop: 'product',
                type: 'select',
                api: '/api/products/',
                query: {
                  status: 'ON',
                  type: 'TR'
                },
                require: false
              },
              {
                label: this.$t('content.confirmChargeWeight'),
                prop: 'confirm_charge_weight',
                require: false
              },
              {
                label: this.$t('content.ConfirmedChargeVolume'),
                prop: 'confirm_volume',
                require: false
              },
              {
                label: this.$t('content.isCustomsDeclaration'),
                type: 'radio',
                prop: 'is_customs_declaration',
                require: false
              },
              {
                label: this.$t('content.Sender'),
                prop: 'shipper',
                type: 'select',
                api: '/api/addresses/',
                query: {
                  address_type: 'SP'
                },
                require: false
              }
              // {
              //   prop: 'is_confirm_data',
              //   label: this.$t('content.ConfirmData'),
              //   type: 'radio',
              //   require: false
              // }
            ],
            auth: 'order/xbdgl/batch_update_order'
          },
          {
            name: this.$t('content.SynchronousAcknowledgementData'), // 同步确认数据/Synchronous acknowledgement data
            method: 'sync_confirm_data',
            dialogProp: true,
            dialogType: 'form',
            onlyOne: true,
            formData: {},
            dialogForm: [
              {
                label: this.$t('content.confirmChargeWeight'),
                prop: 'confirm_charge_weight',
                require: false
              },
              {
                label: this.$t('content.ConfirmedChargeVolume'),
                prop: 'confirm_volume',
                require: false
              },
              {
                label: this.$t('content.CheckinTime'),
                prop: 'check_in_time',
                type: 'datetime',
                require: false
              }
            ],
            auth: 'transport/customerOrderTask_fba_zrh/sync_confirm_data'
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.ConfigurationDocument'),
            btns: [
              {
                name: this.$t('content.ConfigurationOfAirFreightMasterBill'),
                method: 'set_master_num',
                dialogProp: true,
                dialogType: 'table',
                searchable: true,
                dialogSearchHolder: '空运单号',
                defaultCheck: {
                  // defaultCheck设置规则: 列表页后端传过来的字段(订单的外键): 对应单据(这里是空运单)需要默认选择的字段(这里就是空运单单号)
                  master_num_name: 'order_num'
                },
                api: '/api/masterOrders/all_notFC_order/',
                colums: [
                  { label: this.$t('content.masterOrderNumber'), prop: 'order_num', width: '104px' },
                  {
                    label: this.$t('content.orderStatus'),
                    prop: 'order_status',
                    filters: {
                      'DR': '草稿',
                      'SM': '已提交',
                      'WO': '等待作业',
                      'ML': '跨国运输',
                      'FC': '完成',
                      'VO': '作废'
                    }
                  },
                  { label: this.$t('content.pieces'), prop: 'carton' },
                  { label: this.$t('content.weight'), prop: 'weight' },
                  { label: this.$t('content.Volume'), prop: 'volume' },
                  { label: this.$t('content.MasterDocBelongsTo'), prop: 'supplier' },
                  { label: this.$t('content.flightNum'), prop: 'airline_num' },
                  { label: this.$t('content.FlightDate'), prop: 'airline_date' }
                ],
                auth: 'transport/ysdzy/set_master_num'
              },
              {
                name: this.$t('content.ConfiguredOceanBillOfLading'),
                method: 'set_ocean_num',
                dialogProp: true,
                dialogType: 'table',
                headerBtn: true,
                searchable: true,
                showRebinding: true,
                dialogSearchHolder: '海运单号',
                hiddenColums: ['freight_num'],
                // defaultCheck: {
                //   ocean_num_name: 'order_num'
                // },
                api: '/api/oceanOrders/all_notFC_order/?is_first=false',
                colums: [
                  { label: this.$t('content.oceanOrderNumber'), prop: 'order_num', width: '104px' },
                  {
                    label: this.$t('content.orderStatus'),
                    prop: 'order_status',
                    filters: {
                      // 'DR': '草稿',
                      'SM': '已提交',
                      'WO': '等待作业',
                      'ML': '跨国运输',
                      // 'FC': '完成',
                      'DEC': '已报关',
                      'DEP': '已离港',
                      'ARR': '已到港',
                      'CC': '已清关',
                      'VER': '已查验',
                      'REL': '已放行',
                      'PL': '已提柜',
                      'RTC': '已还柜',
                      'AOW': '已到海外仓',
                      'BDW': '已预约送仓',
                      'SIG': '已签收',
                      'LOA': '配载中',
                      'LOC': '配载完成',
                      'OUS': '出库中',
                      'DCO': '出库完成',
                      'VO': '作废'
                    }
                  },
                  { label: this.$t('content.pieces'), prop: 'carton' },
                  { label: this.$t('content.NumberOfPackages'), prop: 'freight_num', type: 'input', width: 100 },
                  { label: this.$t('content.weight'), prop: 'weight' },
                  { label: this.$t('content.Volume'), prop: 'volume' },
                  // { label: this.$t('content.BillOfLadingBelongs'), prop: 'supplier' },
                  { label: this.$t('content.voyageNum'), prop: 'voyage_num' },
                  { label: this.$t('content.vesselName'), prop: 'vessel' }
                ],
                tableFilters: [
                  {
                    prop: 'order_status',
                    type: 'select',
                    placeholder: this.$t('content.orderStatus'),
                    api: '/api/oceanOrders/get_order_status/'
                  },
                  {
                    prop: 'create_date',
                    type: 'daterange',
                    placeholder: this.$t('content.createTime'),
                    value: ''
                  }
                  // {
                  //   prop: 'order_num',
                  //   type: 'input',
                  //   placeholder: this.$t('content.oceanBillOfLading'),
                  //   value: ''
                  // }
                ],
                auth: 'transport/ysdzy/set_ocean_num'
              },
              {
                name: this.$t('content.AllocationOfShippingPriority'),
                method: 'set_ocean_first',
                dialogProp: true,
                dialogType: 'table',
                searchable: true,
                dialogSearchHolder: '海运优先单号',
                // headerBtn: true,
                // hiddenColums: ['freight_num'],
                defaultCheck: {
                  ocean_num_name: 'order_num'
                },
                api: '/api/oceanOrders/all_notFC_order/?is_first=true',
                colums: [
                  { label: this.$t('content.oceanOrderNumber'), prop: 'order_num', width: '104px' },
                  {
                    label: this.$t('content.orderStatus'), prop: 'order_status',
                    filters: {
                      // 'DR': '草稿',
                      'SM': '已提交',
                      'WO': '等待作业',
                      'ML': '跨国运输',
                      // 'FC': '完成',
                      'DEC': '已报关',
                      'DEP': '已离港',
                      'ARR': '已到港',
                      'CC': '已清关',
                      'VER': '已查验',
                      'REL': '已放行',
                      'PL': '已提柜',
                      'RTC': '已还柜',
                      'AOW': '已到海外仓',
                      'BDW': '已预约送仓',
                      'SIG': '已签收',
                      'LOA': '配载中',
                      'LOC': '配载完成',
                      'OUS': '出库中',
                      'DCO': '出库完成',
                      'VO': '作废'
                    }
                  },
                  { label: this.$t('content.pieces'), prop: 'carton' },
                  // { label: this.$t('content.NumberOfPackages'), prop: 'freight_num', type: 'input', width: 100 },
                  { label: this.$t('content.weight'), prop: 'weight' },
                  { label: this.$t('content.Volume'), prop: 'volume' },
                  // { label: this.$t('content.BillOfLadingBelongs'), prop: 'supplier' },
                  { label: this.$t('content.voyageNum'), prop: 'voyage_num' },
                  { label: this.$t('content.vesselName'), prop: 'vessel' }
                ],
                tableFilters: [
                  {
                    prop: 'order_status',
                    type: 'select',
                    placeholder: this.$t('content.orderStatus'),
                    api: '/api/oceanOrders/get_order_status/'
                  },
                  {
                    prop: 'create_date',
                    type: 'daterange',
                    placeholder: this.$t('content.createTime'),
                    value: ''
                  }
                ],
                auth: 'order/ysdgl/set_ocean_num_first'
              },
              {
                name: this.$t('content.ConfigurationOfSingle'),
                method: 'set_house_num',
                dialogProp: true,
                dialogType: 'table',
                defaultCheck: {
                  house_num_name: 'order_num'
                },
                api: '/api/houseOrders/all_notFC_order/',
                colums: [
                  { label: this.$t('content.HouseBill'), prop: 'order_num' },
                  {
                    label: this.$t('content.orderStatus'),
                    prop: 'order_status',
                    filters: { 'DR': '草稿', 'SM': '已提交', 'WO': '等待作业', 'FC': '完成', 'VO': '作废' }
                  },
                  { label: this.$t('content.pieces'), prop: 'carton' },
                  { label: this.$t('content.weight'), prop: 'weight' },
                  { label: this.$t('content.Volume'), prop: 'volume' },
                  { label: this.$t('content.declaredNameEN'), prop: 'goods_name' },
                  { label: this.$t('content.flightNum'), prop: 'airline_num' },
                  { label: this.$t('content.FlightDate'), prop: 'airline_date' }
                ],
                auth: 'transport/ysdzy/set_house_num'
              },
              {
                name: this.$t('content.ConfigureTheCardDeliveryOrder'),
                method: 'set_truck_num',
                dialogProp: true,
                dialogType: 'table',
                // 必须有件数才会显示部分配载和全部配载
                headerBtn: true,
                searchable: true,
                showRebinding: true,
                overheadTitle: '自动创建卡派单',
                overheadButton: '批量创建并配载',
                batchMethod: 'batch_set_truck_num',
                dialogSearchHolder: '卡派单号/海运单号',
                // defaultCheck: {
                //   truck_order_id_name: 'truck_order_num'
                // },
                api: '/api/truckOrders/all_truck_order/',
                colums: [
                  { label: this.$t('content.TruckOrderNo'), prop: 'truck_order_num' },
                  {
                    label: this.$t('content.orderStatus'), prop: 'order_status',
                    filters: { 'DRA': '草稿', 'BOO': '已预约', 'CBE': '已延约', 'DEL': '已送达' }
                  },
                  { label: this.$t('content.oceanBillOfLading'), prop: 'ocean_order_num' },
                  { label: this.$t('content.StationOfDeparture'), prop: 'start_destination' },
                  { label: this.$t('content.DestinationStation'), prop: 'arrive_destination' },
                  { label: this.$t('content.totalNum'), prop: 'carton' },
                  { label: this.$t('content.weight'), prop: 'weight' },
                  { label: this.$t('content.Volume'), prop: 'volume' },
                  { label: this.$t('content.NumberOfPackages'), prop: 'freight_num', type: 'input', width: 100 }
                ],
                tableFilters: [
                  {
                    prop: 'order_status',
                    type: 'select',
                    placeholder: this.$t('content.orderStatus'),
                    api: '/api/truckOrders/get_order_status/'
                  }
                ],
                auth: 'transport/ysdzy/set_truck_num'
              },
              {
                name: this.$t('content.ConfigureReceivingOrders'),
                method: 'set_collect_num',
                width: '1100px',
                dialogProp: true,
                dialogType: 'table',
                searchable: true,
                overheadTitle: '自动创建揽收单',
                overheadButton: '批量创建并配载',
                batchMethod: 'batch_set_collect_order',
                dialogSearchHolder: '揽收单号',
                defaultCheck: {
                  collect_number: 'order_num'
                },
                api: '/api/collectOrders/all_not_fc_order/',
                colums: [
                  { label: this.$t('content.GetTheReceiptNumber'), prop: 'order_num', width: '110px' },
                  // {
                  //   label: this.$t('content.orderStatus'), prop: 'order_status', filters: {
                  //     'DR': '草稿', 'SM': '已提交', 'WO': '等待作业', 'FC': '完成', 'VO': '作废'
                  //   }
                  // },
                  { prop: 'driver_name', label: this.$t('content.DriverName') },
                  { prop: 'license_plate', label: this.$t('content.LicensePlateNumber') },
                  { prop: 'address', label: this.$t('content.ReceivingAddress') },
                  { prop: 'plan_visit_time', label: this.$t('content.EstimatedTimeOfArrival_1'), type: 'datetime' },
                  { label: this.$t('content.pieces'), prop: 'carton', pre: true, width: '80px' },
                  { label: this.$t('content.weight'), prop: 'weight', pre: true },
                  { label: this.$t('content.Volume'), prop: 'volume', pre: true },
                  { label: this.$t('content.OwnedCustomer'), prop: 'customer' }
                ],
                tableFilters: [
                  {
                    prop: 'plan_visit_time',
                    type: 'daterange',
                    placeholder: this.$t('content.EstimatedTimeOfArrival_1'),
                    value: ''
                  }
                ],
                auth: 'transport/ysdzy/set_collect_num'
              },
              {
                name: this.$t('content.configureExportDeclaration'),
                method: 'set_clearance_out_num',
                dialogProp: true,
                dialogType: 'table',
                searchable: true,
                dialogSearchHolder: '出口报关单号',
                headerBtn: false,
                // hiddenColums: ['freight_num'],
                mainCheck: true,
                defaultCheck: {
                  clearance_num: 'clearance_num'
                },
                api: '/api/clearanceOutOrders/get_all_available_clearance/',
                // api: '/api/clearanceOutOrders/',
                colums: [
                  { label: this.$t('content.clearanceNumber'), prop: 'clearance_num', width: '104px' },
                  {
                    label: this.$t('content.StatusOfCustomsDeclaration'), prop: 'clear_status',
                    filters: {
                      'DR': '草稿', 'WO': '等待作业', 'FC': '报关完成', 'ITP': '报关失败', 'VO': '作废'
                    }
                  },
                  { label: this.$t('content.pieces'), prop: 'carton' },
                  { label: this.$t('content.weight'), prop: 'weight' },
                  { label: this.$t('content.Volume'), prop: 'volume' },
                  { label: this.$t('content.OwnedCustomer'), prop: 'customer_name' }
                ],
                auth: 'transport/ysdzy/set_clearance_out_num'
              }
            ]
          },
          // {
          //   name: this.$t('content.SendBills'),
          //   method: 'send_debit',
          //   auth: 'transport/ysdzy/send_debit'
          // },
          // {
          //   name: this.$t('content.IsArrivalNotice'),
          //   method: 'arrival_notice',
          //   auth: 'transport/ysdzy/arrival_notice'
          // },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.SurfaceSingleManagement'),
            btns: [
              {
                name: this.$t('content.CreateInvoice'),
                method: 'create_label',
                auth: 'transport/ysdzy/create_label'
              },
              // {
              //   name: this.$t('content.DownloadSurfaceSingle'),
              //   method: 'get_label',
              //   auth: 'transport/ysdzy/get_label'
              // },
              {
                name: this.$t('content.PrintNeutralSheet'),
                method: 'print_fba_label',
                auth: 'order/ysdgl/print_fba_label'
              },
              {
                name: '打印卡派面单',
                method: 'print_truck_label',
                auth: 'transport/customerOrderTask_fba_zrh/print_truck_label'
              },
              {
                name: this.$t('content.downloadTrackingNumber'),
                method: 'download_excel',
                auth: 'transport/ysdzy/download_excel'
              }
            ]
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.SynchronousOrder'),
            btns: [
              {
                name: this.$t('content.SynchronizeOrdersToSuppliers'),
                method: 'create_sync_order_info',
                auth: 'transport/ysdgl/create_sync_order_info'
              },
              {
                name: this.$t('content.SynchronizeRevenueToSuppliers'),
                method: 'create_sync_order_revenue',
                auth: 'transport/ysdgl/create_sync_order_revenue'
              }
            ]
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.void'),
            btns: [
              {
                name: this.$t('content.failOrder'),
                method: 'cancel_order',
                comfirm: true,
                auth: 'transport/ysdzy/cancel_order'
              },
              {
                name: this.$t('content.RestoreOrder'),
                method: 'recovery_order',
                comfirm: true,
                auth: 'transport/ysdzy/recovery_order'
              },
              {
                name: this.$t('content.ForceCancellationOfTheOrder'),
                method: 'cancel_order_force',
                comfirm: true,
                auth: 'transport/customerOrderTask_fba_zrh/cancel_order_force'
              }
            ]
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.Intercept'),
            btns: [
              {
                name: this.$t('content.intercept_order'),
                method: 'customer_order_intercept',
                comfirm: true,
                auth: 'transport/ysdzy/customer_order_intercept'
              },
              {
                name: this.$t('content.CancelIntercept'),
                method: 'cancel_customer_order_intercept',
                comfirm: true,
                auth: 'transport/ysdzy/cancel_customer_order_intercept'
              }
            ]
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.TotalIncome'),
            btns: [
              {
                name: this.$t('content.IsRevenueLock'),
                method: 'revenue_order',
                auth: 'transport/ysdgl/revenue_order'
              },
              {
                name: this.$t('content.OrderUnlock'),
                method: 'order_unlock',
                auth: 'transport/ysdgl/order_unlock'
              }
            ]
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.cost'),
            btns: [
              {
                name: this.$t('content.IsCostLock'),
                method: 'cost_finish',
                auth: 'transport/ysdgl/cost_finish'
              },
              {
                name: this.$t('content.CostUnlock'),
                method: 'cost_unlock',
                auth: 'transport/ysdgl/cost_unlock'
              }
              // 改为在运输单上使用
              // {
              //   name: this.$t('content.CostAllocation'),
              //   method: 'cost_share',
              //   auth: 'order/ysdgl/cost_share'
              // },
              // {
              //   name: this.$t('content.EliminationOfCostAllocation'),
              //   method: 'cost_share_cancel',
              //   auth: 'order/ysdgl/cost_share'
              // }
            ]
          },
          {
            type: 'selectBtn',
            value: '',
            placeholder: this.$t('content.import'),
            btns: [
              {
                name: this.$t('content.downloadTemplate'),
                // 按钮的method: download_template不能重复, 否则会默认调用第一个method对应的link: item = btn.btns.find(i => {
                method: 'download_template_fba_order',
                link: 'template_customer_fba_order.xlsx?t=1',
                auth: 'transport/ysdgl/upload_excel_fba'
              },
              {
                name: this.$t('content.importFBAOrder'),
                // method: 'upload_excel_fba',
                method: 'upload_file_sync_fba_zrh',
                not_checked: true,
                import: true,
                auth: 'transport/ysdgl/upload_excel_fba_zrh'
              }
            ]
          },
          // {
          //   name: this.$t('content.export'),
          //   method: 'export_order',
          //   comfirm: true,
          //   // dialogProp: true,
          //   // dialogType: 'selectAciton',
          //   colums: '/api/customerOrders/export_order/',
          //   // query: { id: 'id$' },
          //   auth: 'transport/ysdzy/change_track'
          // }
          {
            type: 'selectOptionMethod',
            // commonApi: '/api/supplierButtBI/get_statement_url/',
            value: '',
            placeholder: this.$t('content.export'),
            btns: [
              {
                name: this.$t('content.ExportOrder'),
                // method: 'export_order',
                type: 'quick_bi',
                statement_code: 'export_order',
                auth: 'order/customerOrder_fba/export_order'
              },
              {
                name: this.$t('content.CustomerConfirmation'),
                type: 'quick_bi',
                statement_code: 'customer_confirm',
                auth: 'order/customerOrder_fba/customer_confirm'
              },
              {
                name: this.$t('content.OrderBoxSize'),
                type: 'quick_bi',
                statement_code: 'order_box_size',
                auth: 'order/customerOrder_fba/order_box_size'
              },
              {
                name: this.$t('content.InvoiceData'),
                type: 'quick_bi',
                statement_code: 'print_data',
                auth: 'order/customerOrder_fba/print_data'
              },
              {
                name: this.$t('content.TransferOrderNo'),
                type: 'quick_bi',
                statement_code: 'tracking_data',
                auth: 'order/customerOrder_fba/tracking_data'
              },
              {
                name: this.$t('content.PrerecordedTicket'),
                type: 'quick_bi',
                statement_code: 'pre_recorded_order',
                auth: 'transport/ysdgl/pre_recorded_order'
              },
              {
                name: this.$t('content.exportPODData'),
                type: 'method',
                method: 'custom_pod_export',
                auth: 'transport/ysdgl/custom_pod_export'
              }
              // {
              //   name: this.$t('content.SalesBi'),
              //   method: 'export_saler_data',
              //   auth: ''
              // },
              // {
              //   name: this.$t('content.AgingOfEachWarehouse'),
              //   method: 'export_timeliness_warehouse_fba',
              //   auth: ''
              // },
              // {
              //   name: this.$t('content.PacketAging'),
              //   method: 'export_timeliness_warehouse_parcel',
              //   auth: ''
              // }
            ]
          }
        ],
        data: [
          {
            label: this.$t('content.Order'),
            type: 'multiRow',
            width: '160px',
            data: [
              {
                prop: 'order_num',
                label: this.$t('content.orderNum'),
                link: '/transport/customerOrderTask_fba_zrh/detail',
                copyButtonIcon: true
              },
              {
                prop: 'order_status',
                label: this.$t('content.orderStatus'),
                filter: {
                  'DR': '草稿',
                  'WO': '等待作业',
                  'PDC': '已预报',
                  'ITP': '已拦截',
                  'PW': '已部分入仓',
                  'AW': '已全部入仓',
                  'CWED': '已确认入仓数据',
                  'OW': '已出国内仓',
                  'DEP': '已离港',
                  'TF': '转运',
                  'SF': '已签收',
                  'FC': '完成',
                  'VO': '作废'
                }
              },
              {
                prop: 'customer_name',
                label: this.$t('content.customers')
              },
              {
                prop: 'ref_num',
                label: this.$t('content.CustomerOrderNum')
              },
              {
                prop: 'create_date',
                label: this.$t('content.orderTime'),
                type: 'datetime'
              },
              {
                prop: 'check_in_time',
                label: this.$t('content.CheckinTime')
              }
            ]
          },
          {
            label: this.$t('content.Product'),
            type: 'multiRow',
            width: '150px',
            data: [
              {
                prop: 'product_name',
                label: this.$t('content.Product')
              },
              // {
              //   prop: 'zone_value',
              //   label: this.$t('content.PartitionValue')
              // },
              {
                prop: 'buyer_postcode',
                label: this.$t('content.RecipientPostcode_1')
              },
              {
                prop: 'income',
                label: this.$t('content.TotalIncome'),
                color: '#409EFF',
                condition: { is_revenue_lock: true }
              }
            ]
          },
          {
            label: this.$t('content.Notice'),
            type: 'multiRow',
            width: '100px',
            data: [
              // {
              //   prop: 'is_send_debit',
              //   label: this.$t('content.BillSending'),
              //   type: 'boolean',
              //   filter: true
              // },
              {
                prop: 'is_arrival_notice',
                label: this.$t('content.IsArrivalNotice'),
                type: 'boolean',
                filter: true
              },
              {
                prop: 'is_sync_yqf',
                label: this.$t('content.SyncToSupplier'),
                filter: {
                  // 'UnHandled': '未处理',
                  // 'HandledBy3rdNo': '已提交',
                  'UnHandled': '已提交',
                  'Success': '处理成功',
                  'Failure': '处理失败',
                  'VO': '已作废'
                }
              },
              {
                prop: 'sales_price_card_time',
                label: this.$t('content.SalePriceCardTime')
              },
              {
                prop: 'warehouse_type',
                label: this.$t('content.WmsWarehouse')
              },
              {
                prop: 'delivery_address_type',
                label: this.$t('content.ShippingAddressType')
              }
            ]
          },
          {
            label: this.$t('content.Route'),
            type: 'multiRow',
            width: '150px',
            data: [
              {
                prop: 'departure',
                label: this.$t('content.departurePort')
              },
              {
                prop: 'destination',
                label: this.$t('content.destinationPort')
              },
              {
                prop: 'shipper_name',
                label: this.$t('content.Sender')
              },
              {
                prop: 'buyer_address_num',
                // prop: 'buyer_name',
                label: this.$t('content.recipient')
              },
              // {
              //   prop: 'receiver_name',
              //   label: this.$t('content.WarehouseCode_1')
              // },
              {
                prop: 'reference_id',
                label: 'Reference Id'
              },
              {
                prop: 'shipment_id',
                label: 'Shipment Id'
              }
            ]
          },
          {
            label: this.$t('content.flight'),
            type: 'multiRow',
            width: '140px',
            data: [
              {
                prop: 'airline_num',
                label: this.$t('content.flightNum')
              },
              {
                prop: 'actual_leave_date',
                label: this.$t('content.leaveDate')
              },
              {
                prop: 'arrival_date',
                label: this.$t('content.estimatedArrivalDate')
              },
              {
                prop: 'actual_arrival_date',
                label: this.$t('content.actualArrivalDate')
              },
              {
                prop: 'vessel',
                label: this.$t('content.vesselName')
              },
              {
                prop: 'voyage_num',
                label: this.$t('content.voyageNum')
              }
            ]
          },
          {
            label: this.$t('content.goods'),
            type: 'multiRow',
            width: '150px',
            data: [
              {
                prop: 'carton',
                label: this.$t('content.pieces'),
                pre: true
              },
              {
                prop: 'weight',
                label: this.$t('content.weight'),
                pre: true
              },
              {
                prop: 'volume',
                label: this.$t('content.Volume'),
                pre: true
              },
              {
                prop: 'charge_weight',
                label: this.$t('content.chargeWeight'),
                pre: false
              },
              {
                prop: 'confirm_charge_weight',
                label: this.$t('content.confirmChargeWeight'),
                type: 'popover',
                api: '/api/customerOrders/modify_confirm_charge_weight/'
              },
              {
                prop: 'confirm_volume',
                label: this.$t('content.confirmVolume'),
                type: 'popover',
                api: '/api/customerOrders/modify_confirm_charge_volume/'
              }
            ]
          },
          // {
          //   label: this.$t('content.ConfirmData_1'),
          //   type: 'multiRow',
          //   width: '120px',
          //   data: [
          //     // {
          //     //   prop: 'is_confirm_data',
          //     //   label: this.$t('content.ConfirmData'),
          //     //   type: 'boolean',
          //     //   filter: true
          //     // }
          //   ]
          // },
          {
            label: this.$t('content.totalOrder'),
            type: 'multiRow',
            width: '180px',
            data: [
              {
                prop: 'collect_number',
                label: this.$t('content.GetTheReceiptNumber'),
                blankHide: true
              },
              {
                prop: 'master_number',
                label: this.$t('content.airwayBillNum'),
                blankHide: true
              },
              {
                prop: 'first_ocean_num',
                label: this.$t('content.ShippingPriorityTrackNumber'),
                blankHide: true
              },
              {
                prop: 'ocean_num_name',
                label: this.$t('content.oceanBillNum'),
                blankHide: true
              },
              // {
              //   prop: 'truck_order_id_name',
              //   label: this.$t('content.Cappai'),
              //   blankHide: true
              // },
              {
                prop: 'truck_order_num',
                label: this.$t('content.Cappai'),
                blankHide: true
              },
              {
                prop: 'tracking_num',
                label: this.$t('content.TransferOrderNo'),
                api: '/api/customerOrders/modify_tracking_num/',
                type: 'popover',
                width: '150px'
              },
              {
                prop: 'clearance_num',
                label: this.$t('content.ExportDeclarationNo'),
                blankHide: true
              },
              {
                prop: 'combine_billing_order_num',
                label: '合并计费单',
                blankHide: true
              }
            ]
          },
          {
            label: this.$t('content.OTHE'),
            type: 'multiRow',
            width: '160px',
            data: [
              {
                prop: 'orderLabelTasks',
                label: this.$t('content.labelDesc'),
                type: 'arrayFirst'
              },
              {
                prop: 'create_by',
                label: this.$t('content.Operator')
              },
              {
                prop: 'saler',
                label: this.$t('content.ShopManager')
              },
              {
                prop: 'is_customs_declaration',
                label: this.$t('content.isCustomsDeclaration'),
                type: 'boolean',
                filter: true
              },
              {
                prop: 'is_intercept',
                label: this.$t('content.WhetherInterceptedOrNot'),
                type: 'boolean',
                filter: true
              },
              {
                prop: 'remark',
                label: this.$t('content.remark'),
                api: '/api/customerOrders/modify_remark/',
                type: 'popover',
                width: '150px',
                color: 'red'
              }
            ]
          }
        ]
      }
    }
  },
  // created() {

  // // 这里重复发送请求了
  // mounted() {
  //   const api = this.apiUrl + '?page=1&size=10&ordering=-id&order_status='
  //   // 发送请求
  //   getChoiceData(api).then(res => {
  //     if (res.code === 200) {
  //       // console.log(res.data.length)
  //       console.log(res.data)
  //       this.data = res.data
  //       this.total = res.count
  //     } else {
  //       this.data = res.data
  //       this.total = res.count
  //       // this.$message.error(res.msg || res.detail || res.message)
  //     }
  //     this.bus.$emit('fullLoading', false)
  //   }).catch(() => {
  //     this.bus.$emit('fullLoading', false)
  //   })
  // },
  methods: {
    parseTime,
    checkPermission,
    beforeInit() {
      this.url = `api/${this.formData.api}/`
      const sort = '-id'
      const query = this.query
      const value = query.value
      this.params = { page: this.page, size: this.size, ordering: sort, order_type: 'FBA' }
      // const orderStatus = this.formData.filters.find(i => i.prop === 'order_status')
      // if (!orderStatus.value) {
      //   this.params['not_status'] = 'FC'
      // }
      if (value) {
        this.params['search'] = value
      }
      return true
    },
    subDelete(id) {
      this.delLoading = true
      const p = {
        id,
        api: this.formData.api
      }
      del(p).then(res => {
        this.delLoading = false
        this.$refs[id].doClose()
        // 列表页局部刷新(列表页刷新)
        this.init()
        this.$notify({
          title: this.$t('content.deletedSuccessfully'),
          type: 'success',
          duration: 2500
        })
      }).catch(err => {
        this.delLoading = false
        this.$refs[id].doClose()
        console.log(err.response.data.message)
      })
    },
    selectionChange(val) {
      this.formData.checkData = val
      console.log('check-->', this.formData.checkData)
      // 汇总件数、重量、体积等等
      let pre_carton = 0
      let pre_weight = 0
      let pre_volume = 0
      let carton = 0
      let weight = 0
      let volume = 0
      // let income = 0
      // let cost = 0
      // let gross_profit = 0
      val.forEach((i, index) => {
        pre_carton = this.cal.accAdd(pre_carton, (Number(i['pre_carton']) || 0))
        pre_weight = this.cal.accAdd(pre_weight, (Number(i['pre_weight']) || 0))
        pre_volume = this.cal.accAdd(pre_volume, (Number(i['pre_volume']) || 0))
        carton = this.cal.accAdd(carton, (Number(i['carton']) || 0))
        weight = this.cal.accAdd(weight, (Number(i['weight']) || 0))
        volume = this.cal.accAdd(volume, (Number(i['volume']) || 0))
        // income = this.cal.accAdd(income, (Number(i['income']) || 0))
        // cost = this.cal.accAdd(cost, (Number(i['cost']) || 0))
        // gross_profit = this.cal.accAdd(gross_profit, (Number(i['gross_profit']) || 0))
      })
      this.formData.checkSumary[0].value = `${pre_carton}CTNS/${pre_weight}KGS/${pre_volume}CBM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${carton}CTNS/${weight}KGS/${volume}CBM`
    },
    // 统一action操作

    // 导入Excel
    execute(item) {
      console.log('item---->>', item)
      const { method, data, ids } = item
      const api = this.apiUrl + method + '/'
      let responseType = 'json'
      if (method === 'export_list') {
        responseType = 'blob'
      }
      if (this.bi_url_list.includes(method)) {
        actionPost({ api, data: { ids, ...data }, responseType }).then(res => {
          window.open(res.url, '_blank')
          if (res.code === 200) {
            // this.$message.success(res.message || res.msg || 'Successful operation')
            // this.init()
            // window.open(res.url, '_blank')
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => {
          this.bus.$emit('fullLoading', false)
        })
      } else {
        if ((method === 'set_master_num' || method === 'set_house_num') && data.selectData.length > 1) {
          this.$message.error(`只能选择一个${method === 'set_master_num' ? '主' : '分'}单操作`)
          return
        }
        if ((method === 'set_collect_num') && data.selectData.length > 1) {
          this.$message.error(`只能选择一个单操作`)
          return
        }
        if (ids.length === 0) {
          this.$message.error(this.$t('content.selectItem'))
          return
        }

        var f = () => {
          this.bus.$emit('fullLoading', true)
          // this.getBtnByName(method, true)
          const api = this.apiUrl + method + '/'
          const params = {
            api,
            data: {
              ids, ...data
            },
            responseType: method === 'download_excel' || method === 'custom_pod_export' ? 'blob' : ''
            // responseType: method === 'download_excel' ? 'blob' : ''
          }
          actionPost(params).then(res => {
            // if (method === 'download_excel' || method === 'custom_pod_export') {
            if (method === 'download_excel') {
              // 下载转单号
              axiosExport(res, 'download_excel.xlsx')
              this.bus.$emit('fullLoading', false)
              this.init()
              return
            } else if (method === 'custom_pod_export') {
              axiosExport(res, 'output_pdf_file.pdf')
              this.bus.$emit('fullLoading', false)
              this.init()
              return
            }
            if (res.code === 200) {
              this.$message.success(res.msg || res.message || 'Successful operation')
              if (['print_label'].includes(method)) {
                print_label(res.base64)
                // return
              } else if (['print_fba_label', 'print_truck_label'].includes(method)) {
                // 遍历res.base64
                res.base64.forEach((item, index) => {
                  if (index > 0 && index % 10 === 0) {
                    // 同步延迟3秒
                    const start = Date.now()
                    while (Date.now() - start < 1000) {
                      // 阻塞等待3秒
                    }
                  }
                  print_label_with_name(item.base64_data, item.file_name, 'pdf')
                })
              } else if (method === 'get_label') {
                // 下载面单
                var a = document.createElement('a')
                // const href = location.origin.replace('shipping', 'manage') + res.data.substring(18)
                a.setAttribute('href', res.data)
                a.setAttribute('target', '_blank')
                document.body.appendChild(a)
                a.click()
                document.body.removeChild(a)
              }
              this.init()
            } else {
              this.$message.error(res.msg || res.detail || res.message)
            }
            this.bus.$emit('fullLoading', false)
          }).catch(() => {
            this.bus.$emit('fullLoading', false)
          })
        }
        const comfirm = (this.formData.action.find(i => i.method === method) || {})['comfirm']
        if (comfirm) {
          this.$confirm('确定执行此操作, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'danger'
          }).then(() => {
            f()
          }).catch(() => {
          })
        } else {
          f()
        }
      }
    },

    uploadExcel(data) {
      const api = this.apiUrl + data.item.method + '/'
      const formData = new FormData()
      formData.append('file', data.file)
      actionPost({ api, data: formData }).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg || res.detail || res.message)
          // this.init()
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
      })
    },
    // sizeChange(value) {
    //   this.size = value
    //   this.page = 1
    //   this.loadTabData()
    // },
    //
    // loadTabData() {
    //   const params = {
    //     page: this.page,
    //     size: this.size,
    //     ordering: '-id',
    //     order_status: this.current_status,
    //     search: this.query.value,
    //     order_type: 'FBA'
    //   }
    //
    //   this.loading = true
    //   this.data = [] // 清空数据，避免切换时显示旧数据
    //
    //   getChoiceData(this.apiUrl, params)
    //     .then((res) => {
    //       if (res.code === 200) {
    //         this.data = res.data
    //         this.total = res.count
    //       } else {
    //         this.data = []
    //         this.total = 0
    //         this.$message.error(res.msg || res.detail || res.message)
    //       }
    //       this.loading = false
    //       this.bus.$emit('fullLoading', false)
    //     })
    //     .catch(() => {
    //       this.loading = false
    //       this.bus.$emit('fullLoading', false)
    //       this.$message.error('加载数据失败')
    //     })
    // },

    pageChange2(value) {
      console.log('pageChange2-->', value)
      this.formData && this.formData.filters && this.formData.filters.forEach(i => {
        if (typeof i.value === 'boolean' || i.value) {
          this.params[i.prop] = (i.value).toString()
          this.filterProp[i.prop] = (i.value).toString()
        } else {
          this.$delete(this.filterProp, i.prop)
        }
      })
      this.params.page = value
      const api = this.apiUrl + `?page=${value}` + '&size=10&ordering=-id&order_status=' + `${this.current_status}`
      // 发送请求
      getChoiceData(api, this.params).then(res => {
        if (res.code === 200) {
          console.log(res.data.length)
          this.data = res.data
          this.total = res.count
        } else {
          this.$message.error(res.msg || res.detail || res.message)
        }
        this.bus.$emit('fullLoading', false)
      }).catch(() => {
        this.bus.$emit('fullLoading', false)
      })
    },

    handleTabClick(tab, event) {
      const clickedTab = this.orderStatus.find(item => item.info === tab.label)
      this.current_status = clickedTab.status
      // const params = {
      //   page: 1,
      //   size: 10,
      //   ordering: '-id',
      //   order_status: this.current_status,
      //   search: this.query.value
      // }
      const params = this.params
      params['order_status'] = this.current_status
      params['search'] = this.query.value
      // this.formData.filters.map(i => {
      //   if (i.value !== undefined && !(i.prop in params)) {
      //     params[i.prop] = i.value
      //   }
      // })
      // 将日期范围选择转换成逗号分隔
      console.log('params1-->', params)
      if (clickedTab) {
        getChoiceData(this.apiUrl, params).then(res => {
          if (res.code === 200) {
            this.data = res.data
            this.total = res.count
          } else {
            this.data = res.data
            this.total = res.count
            // this.$message.error(res.msg || res.detail || res.message)
          }
          this.bus.$emit('fullLoading', false)
        }).catch(() => {
          this.bus.$emit('fullLoading', false)
        })
      }
    },
    handleQuickInput(row) {
      const metaData = {
        order_type: 'customerOrderTask_fba',
        order_num: row.order_num,
        api_endpoint: 'customerOrders',
        charge_out_key: 'customerOrderChargeOuts',
        charge_in_key: 'customerOrderChargeIns',
        is_revenue_lock: row.is_revenue_lock,
        is_cost_lock: row.is_cost_lock
      }
      this.$refs.chargeQuickInputRef.handleShow(metaData, row)
    },
    // async handleTrackInfo(row) {
    //   const { data } = await get({
    //     api: this.formData.api,
    //     id: row.id
    //   })
    //   this.$refs.trackInfoDialogRef.handleShow(data)
    // }
    handleTrackInfo(row) {
      this.$refs.trackInfoDialogRef.handleShow(row, this.formData.api)
    }

    // handleTrackInfo(row) {
    //   get({
    //     api: this.formData.api,
    //     id: row.id
    //   }).then(res => {
    //     this.$refs.trackInfoDialogRef.handleShow(res.data.tracks)
    //     this.orderNum = res.data.order_num
    //     this.refNum = res.data.ref_num
    //     console.log('this.orderNum-->', this.orderNum)
    //     console.log('res.data-->', res.data)
    //   })
    // }
  }
}
</script>

<style scoped>
.el-button+.el-button {
  margin-left: 0
}
</style>
