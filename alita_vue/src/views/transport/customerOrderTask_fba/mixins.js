import common from '@/components/Detail/common'
import { actionPost } from '@/api/data'
export default {
  components: {
    common
  },
  data() {
    return {
      initObject: {
        deteleDisabled: true,
        // 是否在详情页最底部显示修改按钮: showBtn()
        modifyBtnShow: {
          $is_revenue_lock: true,
          $is_cost_lock: true,
          order_status: 'WO'
          // $is_sync_yqf: 'Success'
        },
        isADD: false,
        // 请求url
        requestUrl: { baseUrl: 'customerOrders' },
        // 表单底部传入按钮
        bottomBtn: [{
          type: 'selectBtn',
          value: '',
          name: this.$t('content.ImportUpdateFbaOrder'),
          placeholder: this.$t('content.ImportUpdateFbaOrder'),
          id: true,
          edit: true,
          btns: [{
            name: this.$t('content.downloadTemplate'),
            method: 'download_template',
            link: 'template_customer_fba_order.xlsx?t=1',
            auth: 'transport/ysdd/download_template'
          },
          {
            name: this.$t('content.importFBAOrder'),
            // method: 'upload_excel',
            // method: 'upload_excel_fba',
            method: 'import_update_fba_order_normal',
            // showProgress: true,
            not_checked: true,
            import: true,
            auth: 'transport/ysdgl/upload_excel'
          },
          {
            name: this.$t('content.downloadSingleProductTemplate'),
            method: 'download_template',
            link: 'template_customer_fba_order_single.xlsx?t=1',
            auth: 'transport/ysdgl/download_template'
          },
          {
            name: this.$t('content.importSingleProductOrder'),
            // method: 'upload_excel_fba_single',
            method: 'import_update_fba_order_single',
            not_checked: true,
            import: true,
            auth: 'transport/ysdgl/upload_excel_fba_single'
          }
          ]
        }],
        // 表单数据
        initData: [{
          label: this.$t('content.basicInformation'),
          formDataInit: [{
            label: this.$t('content.customerOrderNumber'),
            prop: 'ref_num'
          },
          {
            label: this.$t('content.orderNum'),
            prop: 'order_num',
            addHiden: true,
            disabled: true
          },
          {
            label: this.$t('content.Product'),
            prop: 'product',
            type: 'select'
          },
          {
            label: this.$t('content.status'),
            prop: 'order_status',
            addHiden: true,
            filter: {
              DR: '草稿',
              WO: '等待作业',
              PDC: '已预报',
              ITP: '已拦截',
              PW: '已部分入仓',
              AW: '已全部入仓',
              CWED: '已确认入仓数据',
              OW: '已出国内仓',
              DEP: '已离港',
              TF: '转运',
              SF: '已签收',
              FC: '完成',
              VO: '作废'
            },
            type: 'select',
            disabled: true
          },
          {
            label: this.$t('content.customers'),
            prop: 'customer',
            type: 'select',
            show: 'nameAndShortName',
            change: [{
              label: 'contact',
              prop: 'contact',
              type: 'getValue'
            },
            {
              label: 'saler',
              prop: 'saler',
              type: 'getValue'
            }
            ]
          },
          {
            label: this.$t('content.contactPerson'),
            prop: 'contact',
            disabled: true
          },
          {
            label: this.$t('content.ShopManager'),
            prop: 'saler',
            // addHiden: true,
            disabled: true
          },
          {
            prop: 'zone_value',
            label: this.$t('content.PartitionValue'),
            disabled: true
          },
          {
            prop: 'is_customs_declaration',
            label: this.$t('content.WhetherToDeclare'),
            type: 'radio',
            disabled: true
          },
          {
            prop: 'is_intercept',
            label: this.$t('content.WhetherInterceptedOrNot'),
            type: 'radio',
            disabled: true
          },
          {
            prop: 'is_overlength',
            label: this.$t('content.IsItTooLong'),
            type: 'radio',
            disabled: true,
            addHiden: true
          },
          {
            prop: 'is_overweight',
            label: this.$t('content.WhetherOverweightOrNot'),
            type: 'radio',
            disabled: true,
            addHiden: true
          },
          {
            prop: 'is_remote',
            label: this.$t('content.RemoteOrNot'),
            type: 'radio',
            disabled: true,
            addHiden: true
          },
          {
            prop: 'is_private_address',
            label: this.$t('content.PrivateAddressOrNot'),
            type: 'radio',
            disabled: true,
            addHiden: true
          },
          {
            prop: 'check_in_time',
            label: this.$t('content.CheckinTime'),
            disabled: true,
            addHiden: true
          },
          {
            prop: 'sales_price_card_time',
            label: this.$t('content.SalePriceCardTime'),
            type: 'datetime',
            disabled: true,
            addHiden: true
          },
          {
            label: this.$t('content.remark'),
            prop: 'remark',
            type: 'textarea'
          }
          ]
        },
        {
          label: this.$t('content.TransportInformation'),
          formDataInit: [{
            label: this.$t('content.LogisticsPlan'),
            prop: 'logistics_planning',
            type: 'select',
            change: [{
              label: 'departure_port_name',
              prop: 'departure',
              type: 'getValue'
            },
            {
              label: 'destination_port_name',
              prop: 'destination',
              type: 'getValue'
            },
            {
              label: 'departure_port_dt_date',
              prop: 'expected_leave_date',
              type: 'getValue'
            },
            {
              label: 'destination_port_dt_date',
              prop: 'expected_arrivals_date',
              type: 'getValue'
            }
            ]
          },
          {
            label: this.$t('content.departurePort'),
            prop: 'departure'
          },
          {
            label: this.$t('content.destinationPort'),
            prop: 'destination'
          },
          {
            label: this.$t('content.estimatedArrivalDate'),
            prop: 'arrival_date',
            type: 'date'
          },
          {
            prop: 'actual_arrival_date',
            label: this.$t('content.actualArrivalDate'),
            type: 'datetime'
          },
          {
            label: this.$t('content.ExpectedLeaveDate'),
            prop: 'expected_leave_date',
            type: 'date'
          },
          {
            label: this.$t('content.ActualLeaveDate'),
            prop: 'actual_leave_date',
            type: 'date',
            addHiden: true,
            disabled: true
          },
          {
            label: this.$t('content.TransferOrderNo'),
            prop: 'tracking_num'
          },
          {
            label: this.$t('content.ExpectArrivalDate'),
            prop: 'expected_arrivals_date',
            type: 'date'
          },
          {
            label: this.$t('content.ActualArrivalDate'),
            prop: 'actual_arrivals_date',
            type: 'date',
            addHiden: true,
            disabled: true
          },
          {
            label: this.$t('content.HouseBill'),
            prop: 'house_num',
            type: 'select',
            addHiden: true,
            disabled: true
          },
          {
            label: this.$t('content.flightNum'),
            prop: 'airline_num'
          },
          {
            label: this.$t('content.airwayBillNum'),
            prop: 'master_number'
          },
          {
            label: this.$t('content.ShippingPriorityTrackNumber'),
            prop: 'first_ocean_num',
            disabled: true
          },
          {
            prop: 'ocean_num_name',
            label: this.$t('content.oceanBillNum'),
            disabled: true
          },
          {
            label: this.$t('content.vesselName'),
            prop: 'vessel'
          },
          {
            label: this.$t('content.voyageNum'),
            prop: 'voyage_num'
          },
          {
            prop: 'collect_number',
            label: this.$t('content.RequisitionForm')
          },
          {
            prop: 'first_track',
            label: this.$t('content.PriorityLocus'),
            type: 'radio'
          },
          {
            prop: 'is_importer_clearance',
            label: '使用进口商清关',
            type: 'radio'
          },
          {
            prop: 'truck_order_num',
            label: this.$t('content.Cappai'),
            disabled: true
          },
          {
            prop: 'truck_order_pod',
            label: this.$t('content.Capapod'),
            type: 'files',
            disabled: true,
            addHiden: true,
            single: true
          }
          ]
        },
        {
          label: this.$t('content.Sender_1'),
          formDataInit: [{
            label: this.$t('content.Sender'),
            prop: 'shipper',
            type: 'dialogselect',
            textVal: ''
          },
          {
            label: this.$t('content.recipient'),
            prop: 'receiver',
            type: 'dialogselect',
            textVal: ''
          }
          ]
        },
        {
          label: this.$t('content.recipient'),
          formDataInit: [{
            label: this.$t('content.AddressNo'),
            prop: 'buyer_address_num'
          },
          {
            label: this.$t('content.contactPerson'),
            prop: 'buyer_name'
          },
          {
            label: this.$t('content.email'),
            prop: 'buyer_mail'
          },
          {
            label: this.$t('content.PhoneNo'),
            prop: 'buyer_phone'
          },
          {
            label: this.$t('content.CountryCode_1'),
            prop: 'buyer_country_code'
          },
          {
            label: this.$t('content.tateCode'),
            prop: 'buyer_state'
          },
          {
            label: this.$t('content.CityCode'),
            prop: 'buyer_city_code'
          },
          {
            label: this.$t('content.postcode'),
            prop: 'buyer_postcode'
          },
          {
            label: this.$t('content.houseNumber'),
            prop: 'buyer_house_num'
          },
          {
            label: this.$t('content.AddressLineOne'),
            prop: 'buyer_address_one'
          },
          {
            label: this.$t('content.AddressLineTwo'),
            prop: 'buyer_address_two'
          },
          {
            label: this.$t('content.CompanyName_1'),
            prop: 'buyer_company_name'
          },
          {
            label: this.$t('content.IossNumber'),
            prop: 'ioss_num'
          }
          ]
        },
        {
          label: this.$t('content.CargoInformation'),
          formDataInit: [{
            label: this.$t('content.expectedPickNum'),
            prop: 'pre_carton',
            event: 'blur',
            type: 'preCartonChange',
            disabled: true
          },
          {
            label: this.$t('content.pieces'),
            prop: 'carton',
            disabled: true
          },
          {
            label: this.$t('content.EstimatedWeight'),
            prop: 'pre_weight',
            disabled: true
          },
          {
            label: this.$t('content.weight'),
            prop: 'weight',
            disabled: true
          },
          {
            label: this.$t('content.EstimatedVolume'),
            prop: 'pre_volume',
            disabled: true
          },
          {
            label: this.$t('content.Volume'),
            prop: 'volume',
            disabled: true
          },
          {
            label: this.$t('content.ConversionRate'),
            prop: 'charge_trans',
            default: true,
            defaultVal: 6000
          },
          {
            label: this.$t('content.chargeWeight'),
            prop: 'charge_weight',
            disabled: true
          },
          {
            prop: 'confirm_charge_weight',
            label: this.$t('content.confirmChargeWeight')
            // disabled: true
          },
          {
            prop: 'confirm_volume',
            label: this.$t('content.confirmVolume')
            // disabled: true
          },
            // {
            //   prop: 'is_confirm_data',
            //   label: this.$t('content.ConfirmData'),
            //   type: 'radio',
            //   disabled: true
            // },
          {
            label: this.$t('content.ChargeVolume'),
            prop: 'charge_volume',
            disabled: true
          },
          {
            label: this.$t('content.CustomerSplit'),
            prop: 'bubble',
            type: 'range_list'
          },
          {
            label: this.$t('content.BubbleWeight'),
            prop: 'bubble_weight',
            disabled: true
          },
          {
            label: this.$t('content.volumeWeight'),
            prop: 'volume_weight',
            defaultVal: 0,
            disabled: true
          },
          {
            label: this.$t('content.OrderNote'),
            prop: 'order_remark'
          },
          {
            label: this.$t('content.CustomerNotice'),
            prop: 'customer_remark',
            type: 'textarea'
          },
          {
            label: this.$t('content.Attachment'),
            prop: 'attachments',
            type: 'files'
          },
          {
            label: this.$t('content.SignatureForm'),
            prop: 'signForAttachments',
            type: 'files2'
          }
          ]
        },
        {
          label: this.$t('content.ShipmentDetail'),
          formDataInit: [{
            label: this.$t('content.ShipmentDetail'),
            prop: 'customer_parcel_outbound',
            type: 'table',
            singleEdit: true,
            batchUnbindYunshu: true,
            // singleEditTradition: {
            //   is_revenue_lock: false
            // },
            datavalue: 'customer_parcel_outbound',
            data: [
              { label: this.$t('content.ShippingOrder_1'), prop: 'outbound_num' },
              {
                prop: 'status',
                label: this.$t('content.orderStatus'),
                filters: {
                  'WO': '待完成',
                  'OUT': '已完成'
                }
              },
              {
                label: this.$t('content.ReferenceNo'),
                type: 'textarea',
                prop: 'references'
              },
              {
                label: this.$t('content.OriginatingSite'),
                prop: 'departure'
              },
              {
                label: this.$t('content.SendToSite'),
                prop: 'destination'
              },
              { label: this.$t('content.remark'), prop: 'remark' }
            ],
            action: {
              etcBtn: [{
                label: this.$t('content.Untie'),
                event: 'batch_unbind_outbound_order',
                comfirm: true,
                comfirmMsg: '确定解绑该出货单？'
              }],
              batchUnbindYunshu: true
            }
          }]
        },
        {
          label: this.$t('content.goodsRelated'),
          formDataInit: [{
            type: 'tabTable',
            tabValue: '0',
            hasEditTable: true,
            editingRestriction: true,
            removeRestriction: true,
            tabData: [{
              datavalue: 'parcelItem',
              label: this.$t('content.parcelAndGoods'),
              type: 'editTable',
              primary: true,
              data: [
                // 在线上表格中可编辑的字段
                // 这些字段要按顺序排列, 并且在hansontable.vue中同步按顺序列出这些字段数据, 这里完整包裹对应的是 single_columns
                // 同时也需要在src/components/Detail/index.vue中添加, 详细可参考reference_id
                { label: this.$t('content.caseNumber'), prop: 'parcel_num' },
                { label: this.$t('content.parcelQty'), prop: 'parcel_qty', simple: true, onlySimle: true },
                { label: 'Reference Id', prop: 'reference_id' },
                { label: 'Shipment Id', prop: 'shipment_id' },
                { label: this.$t('content.parcelWeight'), prop: 'parcel_weight', simple: true },
                { label: this.$t('content.parcelLength'), prop: 'parcel_length', simple: true },
                { label: this.$t('content.parcelWidth'), prop: 'parcel_width', simple: true },
                { label: this.$t('content.parcelHeight'), prop: 'parcel_height', simple: true },
                { label: this.$t('content.remark'), prop: 'remark', simple: true },
                { label: this.$t('content.ActualWeightKg'), prop: 'actual_weight' },
                { label: this.$t('content.ActualLengthCm'), prop: 'actual_length' },
                { label: this.$t('content.ActualWidthCm'), prop: 'actual_width' },
                { label: this.$t('content.ActualHeightCm'), prop: 'actual_height' },
                { label: this.$t('content.labelWeight'), prop: 'label_weight' },
                { label: this.$t('content.labelLength'), prop: 'label_length' },
                { label: this.$t('content.TypeSingleWidthCm'), prop: 'label_width' },
                { label: this.$t('content.labelHeight'), prop: 'label_height' },
                { label: this.$t('content.itemCode'), prop: 'item_code' },
                { label: this.$t('content.declaredNameCN'), prop: 'declared_nameCN' },
                { label: this.$t('content.declaredNameEN'), prop: 'declared_nameEN' },
                { label: this.$t('content.declaredPrice'), prop: 'declared_price' },
                { label: this.$t('content.quantity'), prop: 'item_qty' },
                { label: this.$t('content.itemWeight'), prop: 'item_weight' },
                // { label: this.$t('content.size'), prop: 'item_size' },
                { label: this.$t('content.texture'), prop: 'texture' },
                { label: this.$t('content.use'), prop: 'use' },
                { label: this.$t('content.brand'), prop: 'brand' },
                { label: this.$t('content.model'), prop: 'model' },
                { label: this.$t('content.customsCode'), prop: 'customs_code' },
                { label: this.$t('content.tax_rate'), prop: 'tax_rate' },
                { label: this.$t('content.fbaNo'), prop: 'fba_no' },
                { label: this.$t('content.fbaTrackCode'), prop: 'fba_track_code' },
                { label: this.$t('content.ProductPicture'), prop: 'item_picture', hidden: true }
              ]
            },
            {
              datavalue: '$parce',
              label: this.$t('content.packageSummary'),
              data: [
                { label: this.$t('content.CartonNo'), prop: 'parcel_num', link: '/transport/expressManager/detail' },
                { label: this.$t('content.SystemPackageNumber'), prop: 'sys_parcel_num' },
                { label: '客户系统包裹号', prop: 'customer_sys_parcel_num' },
                { label: this.$t('content.trackingNum'), prop: 'tracking_num' },
                { label: 'Reference Id', prop: 'reference_id' },
                { label: 'Shipment Id', prop: 'shipment_id' },
                { label: this.$t('content.LongActualLength'), prop: 'parcel_length', pre: true },
                { label: this.$t('content.WidthActualWidth'), prop: 'parcel_width', pre: true },
                { label: this.$t('content.HeightActualHeight'), prop: 'parcel_height', pre: true },
                // { label: this.$t('content.parcelQty'), prop: 'parcel_qty' },
                { label: this.$t('content.WeightActualWeight'), prop: 'parcel_weight', pre: true },
                // { label: this.$t('content.OrderWeight'), prop: 'label_weight' },
                // { label: this.$t('content.OrderLength'), prop: 'label_length' },
                // { label: this.$t('content.SingleWidth'), prop: 'label_width' },
                // { label: this.$t('content.OrderHigh'), prop: 'label_height' },
                { label: this.$t('content.Volume'), prop: 'parcel_volume' },
                { label: '确认计费重', prop: 'parcel_confirm_weight' },
                { label: '确认计费体积', prop: 'parcel_confirm_volume' },
                // { label: this.$t('content.remark'), prop: 'remark' },
                // { label: this.$t('content.Download'), prop: 'img_url', type: 's_file' },
                { label: this.$t('content.LatestTrack'), prop: 'last_track' },
                { label: this.$t('content.WhetherInterceptedOrNot'), prop: 'intercept_mark', type: 'radio', dialogProp: true },
                { label: this.$t('content.WhetherItIsOutOfTheWarehouse'), prop: 'out_warehouse', type: 'radio', dialogProp: true },
                { label: this.$t('content.isWeighing'), prop: 'is_weighing', type: 'radio', dialogProp: true },
                { label: this.$t('content.OceanOrderNum'), prop: 'ocean_order_num' },
                {
                  label: '包裹图片',
                  prop: 'attachment_parcel',
                  type: 'dialogFiles',
                  dialogProp: true,
                  // uploadPath: 'Parcel',
                  uploadUrl: 'parcelAttachments',
                  foreignkey: 'parcel'
                },
                { label: this.$t('content.InterceptParcel'), prop: 'is_intercept', type: 'is_intercept' }
              ],
              action: {
                // todo_c:
                // add: 'select',
                // edit: true,
                // detele: true,
                etcBtn: [{
                  label: this.$t('content.Remove'),
                  event: 'remove_parcel',
                  comfirm: true,
                  comfirmMsg: '确定移除该包裹？'
                }]
              }
            },
            {
              datavalue: '$parcel_goods',
              label: this.$t('content.PackingList'),
              data: [
                { label: this.$t('content.CartonNo'), prop: 'parcel_number' },
                { label: 'SKU', prop: 'item_code' },
                { label: this.$t('content.declaredNameCn'), prop: 'declared_nameCN' },
                { label: this.$t('content.declaredNameEn'), prop: 'declared_nameEN' },
                { label: this.$t('content.DeclaredPrice'), prop: 'declared_price' },
                { label: this.$t('content.quantity'), prop: 'item_qty' },
                { label: this.$t('content.weight'), prop: 'item_weight' },
                { label: this.$t('content.texture'), prop: 'texture' },
                { label: this.$t('content.size'), prop: 'item_size' },
                { label: this.$t('content.use'), prop: 'use' },
                { label: this.$t('content.brand'), prop: 'brand' },
                { label: this.$t('content.model'), prop: 'model' },
                { label: this.$t('content.customsCode'), prop: 'customs_code' },
                { label: this.$t('content.fbaNo'), prop: 'fba_no' },
                { label: this.$t('content.fbaTrackCode'), prop: 'fba_track_code' }
              ]
            },
            {
              datavalue: '$goods',
              label: this.$t('content.goodsSummary'),
              data: [
                { label: this.$t('content.ProductPicture'), prop: 'item_picture', type: 'image' },
                { label: 'SKU', prop: 'item_code' },
                { label: this.$t('content.declaredNameCn'), prop: 'declared_nameCN' },
                { label: this.$t('content.declaredNameEn'), prop: 'declared_nameEN' },
                { label: this.$t('content.DeclaredPrice'), prop: 'declared_price' },
                { label: this.$t('content.quantity'), prop: 'item_qty' },
                { label: this.$t('content.weight'), prop: 'item_weight' },
                { label: this.$t('content.texture'), prop: 'texture' },
                { label: this.$t('content.size'), prop: 'item_size' },
                { label: this.$t('content.use'), prop: 'use' },
                { label: this.$t('content.brand'), prop: 'brand' },
                { label: this.$t('content.model'), prop: 'model' },
                { label: this.$t('content.customsCode'), prop: 'customs_code' },
                { label: this.$t('content.fbaNo'), prop: 'fba_no' },
                { label: this.$t('content.fbaTrackCode'), prop: 'fba_track_code' }
              ]
            },
            {
              datavalue: 'orderLabelTasks',
              label: this.$t('content.labelTasks'),
              data: [
                { label: this.$t('content.TaskType'), prop: 'task_type' },
                { label: this.$t('content.labelDesc'), prop: 'label_desc' },
                {
                  label: this.$t('content.status'),
                  prop: 'status',
                  filter: {
                    UnHandled: '未处理',
                    Success: '处理成功',
                    HandledBy3rdNo: '根据第三方号码处理',
                    Failure: '处理失败',
                    VO: '作废'
                  }
                },
                { label: this.$t('content.thirdOrderNo'), prop: 'third_order_no' },
                { label: this.$t('content.modeKey'), prop: 'mode_key' },
                { label: this.$t('content.handleTimes'), prop: 'handle_times' }
              ]
            },
            {
              datavalue: 'orderSyncTasks',
              label: this.$t('content.SynchronizationTask'),
              data: [{
                label: this.$t('content.TaskType'),
                prop: 'task_type',
                filter: {
                  PUSH_ORDER: '同步订单',
                  PULL_ORDER_STATUS: '拉取订单状态',
                  PULL_TRACK: '拉取订单轨迹',
                  PULL_PARCEL_TRACK: '拉取包裹轨迹状态',
                  PULL_POD_FILE: '拉取Pod文件',
                  PUSH_REVENUE: '推送收入',
                  PULL_COST: '拉取成本'
                }
              },
              { label: this.$t('content.TaskDescription'), prop: 'task_desc' },
              { label: this.$t('content.createTime'), prop: 'create_date', type: 'datetime' },
              { label: this.$t('content.updateTime'), prop: 'update_date', type: 'datetime' },
              {
                label: this.$t('content.status'),
                prop: 'status',
                type: 'select',
                filter: {
                  UnHandled: '未处理',
                  Success: '处理成功',
                  HandledBy3rdNo: '已提交',
                  Failure: '处理失败',
                  VO: '作废'
                },
                editable: true,
                edit: false,
                method: 'change_sync_task_status'
              },
              {
                label: this.$t('content.handleTimes'),
                prop: 'handle_times',
                editable: true,
                edit: false,
                method: 'change_sync_task_handle_times'
              }
              ]
            },
            {
              datavalue: 'tracks',
              label: this.$t('content.trackInfo'),
              data: [
                { label: this.$t('content.time'), prop: 'actual_time' },
                { label: this.$t('content.trackCode'), prop: 'track_code' },
                { label: this.$t('content.trackName'), prop: 'track_name' },
                { label: this.$t('content.Position_1'), prop: 'location' },
                { label: this.$t('content.trackInfor'), prop: 'remark' }
              ],
              action: {
                // add: 'select',
                // edit: true,
                // detele: true,
                etcBtn: []
              }
            },
            {
              datavalue: 'customerOrderRelateList',
              label: this.$t('content.ShippingInformation'),
              data: [
                { label: this.$t('content.oceanBillOfLading'), prop: 'oceanOrder_num' },
                { label: this.$t('content.StowageQuantity'), prop: 'freight_num' },
                { label: this.$t('content.OutgoingQuantity'), prop: 'out_warehouse_num' }
              ],
              action: {

              }
            },
            {
              label: this.$t('content.WarehousingValuation'),
              prop: 'orderAsyncTask',
              datavalue: 'orderAsyncTask',
              data: [{
                label: '任务类型',
                prop: 'task_type',
                filter: {
                  BL: '计费',
                  CostFinish: '成本确认',
                  CostShare: '成本分摊'
                }
              },
              { label: this.$t('content.TaskDescription'), prop: 'task_desc' },
              {
                label: this.$t('content.status'),
                prop: 'status',
                filter: {
                  UnHandled: '未处理',
                  Waiting: '等待中',
                  Processed: '处理中',
                  Success: '处理成功',
                  Failure: '处理失败',
                  VO: '已作废'
                }
              },
              { label: this.$t('content.handleTimes'), prop: 'handle_times' },
              { label: this.$t('content.ProcessingTime'), prop: 'execution_time' }
              ]
              // action: { add: true, detele: true, edit: true }
            },
            {
              label: this.$t('content.OperationRecord'),
              prop: 'fields_change_logs',
              datavalue: 'fields_change_logs',
              data: [
                { label: this.$t('content.FieldChineseName'), prop: 'field_verbose_name' },
                // { label: this.$t('content.FieldName'), prop: 'field_name' },
                { label: this.$t('content.BeforeModification'), prop: 'old_value_display' },
                { label: this.$t('content.AfterModification'), prop: 'new_value_display' },
                { label: this.$t('content.updateTime'), prop: 'update_date', type: 'datetime' },
                { label: this.$t('content.UpdatedBy'), prop: 'username' }
              ]
            },
            {
              label: this.$t('content.abnormalTag'),
              prop: 'order_abnormal_tags',
              datavalue: 'order_abnormal_tags',
              data: [
                { label: this.$t('content.AbnormalLogNo'), prop: 'order_num' },
                { label: this.$t('content.status'), prop: 'status', filter: { 'W': '待处理', 'I': '处理中', 'C': '已完成' }},
                { label: this.$t('content.abnormalTag'), prop: 'abnormal_tag_name' },
                { label: this.$t('content.AbnormalDescribe'), prop: 'content' },
                { label: this.$t('content.FinishTime'), prop: 'complete_time', type: 'datetime' },
                { label: this.$t('content.updateTime'), prop: 'update_date', type: 'datetime' },
                { label: this.$t('content.UpdatedBy'), prop: 'username' }
              ]
            }
              // {
              //   datavalue: 'images',
              //   label: this.$t('content.PictureDisplay'),
              //   type: 'images',
              //   data: []
              // }
            ],
            action: {
              add: true,
              detele: true,
              edit: true
            }
          }]
        },
          // {
          //   label: this.$t('content.incomeDetails'),
          //   formDataInit: [
          //     {
          //       label: this.$t('content.incomeDetails'),
          //       prop: 'customerOrderChargeIns',
          //       type: 'table',
          //       singleEdit: true,
          //       singleEditTradition: {
          //         is_revenue_lock: false
          //       },
          //       datavalue: 'customerOrderChargeIns',
          //       summary: true,
          //       data: [
          //         { label: this.$t('content.costName'), prop: 'charge', type: 'select' },
          //         { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
          //         { label: this.$t('content.quantity'), prop: 'charge_count' },
          //         { label: this.$t('content.chargeTotal'), prop: 'charge_total', readOnly: true },
          //         { label: this.$t('content.currencyType'), prop: 'currency_type', type: 'current', default: 'CNY' },
          //         { label: this.$t('content.payer'), prop: 'customer', type: 'select' },
          //         { label: this.$t('content.remark'), prop: 'remark', type: 'textarea', not_required: true }
          //       ],
          //       action: {
          //         add: true,
          //         detele: true,
          //         edit: true
          //       }
          //     }
          //   ]
          // },
        {
          label: this.$t('content.incomeDetails'),
          formDataInit: [{
            type: 'tabTable',
            tabValue: '0',
            // hasEditTable: true,
            tabData: [{
              label: this.$t('content.incomeDetails'),
              prop: 'customerOrderChargeIns',
              datavalue: 'customerOrderChargeIns',
              singleEdit: true,
              singleEditTradition: {
                is_revenue_lock: false
              },
              summary: true,
              data: [
                { label: this.$t('content.costName'), prop: 'charge', type: 'select' },
                { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
                { label: this.$t('content.quantity'), prop: 'charge_count' },
                { label: this.$t('content.chargeTotal'), prop: 'charge_total', readOnly: true },
                { label: this.$t('content.ChargePrice'), prop: 'charge_price', readOnly: true },
                { label: this.$t('content.PublishedPrice'), prop: 'published_price', readOnly: true },
                { label: this.$t('content.BasicPrice'), prop: 'base_price', readOnly: true },
                { label: this.$t('content.currencyType'), prop: 'currency_type', type: 'current', default: 'CNY' },
                { label: this.$t('content.payer'), prop: 'customer', type: 'select', readOnly: true },
                { label: this.$t('content.ShareNo'), prop: 'share_charge_id', readOnly: true },
                { label: '是否系统添加', prop: 'is_system', type: 'radio', readOnly: true },
                { label: this.$t('content.remark'), prop: 'remark', type: 'textarea', not_required: true }
              ],
              action: { add: true, detele: true, edit: true }
            },
            {
              label: this.$t('content.IncomeAdjustmentDetail'),
              prop: 'relative_debitAdjustDetails',
              datavalue: 'relative_debitAdjustDetails',
              singleEdit: true,
              singleEditTradition: {
                $order_status: ['SF', 'FC']
              },
              data: [
                { label: this.$t('content.AdjustmentOrderNumber'), prop: 'debit_adjust_num' },
                { label: this.$t('content.trackingNum'), prop: 'track_num' },
                // { label: this.$t('content.customerOrderNumber'), prop: 'customer_orderNum' },
                { label: this.$t('content.costName'), prop: 'charge', type: 'select' },
                { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
                { label: this.$t('content.quantity'), prop: 'charge_count' },
                {
                  label: this.$t('content.currencyType'),
                  prop: 'currency_type',
                  type: 'select',
                  filter: {
                    'CNY': 'CNY',
                    'USD': 'USD',
                    'GBP': 'GBP',
                    'EUR': 'EUR',
                    'HKD': 'HKD',
                    'CAD': 'CAD',
                    'AUD': 'AUD',
                    'MXN': 'MXN'
                  }
                },
                { label: this.$t('content.payer'), prop: 'customer', type: 'select' },
                { label: this.$t('content.remark'), prop: 'remark', type: 'textarea', not_required: true }
              ],
              action: { add: true, detele: true, edit: true }
            }
            ],
            action: {
              // add: true,
              // detele: true,
              // edit: true
            }
          }]
        },
        {
          label: this.$t('content.costDetails'),
          formDataInit: [{
            label: this.$t('content.costDetails'),
            prop: 'customerOrderChargeOuts',
            type: 'table',
            // 不在列表页点编辑或者不在详情页点修改(isAdd为false)时, 控制是否显示页签上的新增/删除按钮
            singleEdit: true,
            singleEditTradition: {
              is_cost_lock: false
            },
            datavalue: 'customerOrderChargeOuts',
            summary: true,
            data: [
              { label: this.$t('content.costName'), prop: 'charge', type: 'select' },
              { label: this.$t('content.unitPrice'), prop: 'charge_rate' },
              { label: this.$t('content.quantity'), prop: 'charge_count' },
              { label: this.$t('content.chargeTotal'), prop: 'charge_total', readOnly: true },
              { label: this.$t('content.currencyType'), prop: 'currency_type', type: 'current' },
              { label: this.$t('content.supplier'), prop: 'supplier', type: 'select' },
              { label: this.$t('content.Share'), prop: 'is_share', type: 'radio', readOnly: true },
              { label: this.$t('content.ShareNo'), prop: 'share_charge_id', readOnly: true },
              { label: '是否系统添加', prop: 'is_system', type: 'radio', readOnly: true },
              { label: this.$t('content.remark'), prop: 'remark', type: 'textarea', not_required: true }
            ],
            action: {
              add: true,
              detele: true,
              edit: true
            }
          }]
        },
        {
          label: this.$t('content.ExportDeclarationForm'),
          formDataInit: [{
            type: 'tabTable',
            tabValue: '0',
            // hasEditTable: true,
            tabData: [{
              datavalue: 'masterOrderOut',
              label: this.$t('content.airwayBill'),
              data: [
                { label: this.$t('content.airwayBillNum'), prop: 'master_number', linkage: 'master_number' },
                { label: this.$t('content.ExportDeclarationNo'), prop: 'clearance_num' },
                {
                  label: this.$t('content.status'),
                  prop: 'clear_status',
                  type: 'select',
                  filter: {
                    'DR': '草稿',
                    'WO': '等待报关',
                    'FC': '报关完成',
                    'ITP': '报关失败',
                    'VO': '作废'
                  }
                }
              ],
              action: {
                add: 'select',
                edit: true,
                detele: true,
                etcBtn: []
              }
            },
            {
              datavalue: 'oceanOrderOut',
              label: this.$t('content.oceanBillOfLading'),
              data: [
                { label: this.$t('content.oceanBillNum'), prop: 'ocean_number', linkage: 'ocean_number' },
                { label: this.$t('content.ExportDeclarationNo'), prop: 'clearance_num' },
                {
                  label: this.$t('content.status'),
                  prop: 'clear_status',
                  type: 'select',
                  filter: {
                    'DR': '草稿',
                    'WO': '等待报关',
                    'FC': '报关完成',
                    'ITP': '报关失败',
                    'VO': '作废'
                  }
                }
              ],
              action: {
                add: 'select',
                edit: true,
                detele: true,
                etcBtn: []
              }
            }
            ],
            action: {
              // add: true,
              // detele: true,
              // edit: true
            }
          }]
        }
        ],
        // 必填项
        rules: {
          // pre_carton: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          // pre_weight: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }],
          // pre_volume: [{ required: true, message: this.$t('content.Required'), trigger: 'blur' }]
        },
        // 初始化下拉数据
        option: [
          { api: '/api/companies/list_company/', prop: 'customer', label: 'name', value: '', query: { is_customer: true }},
          { api: '/api/users/list_user', prop: 'contact', label: 'name', value: '' },
          { api: '/api/products/', prop: 'product', label: 'name', value: '', query: { type: 'TR', status: 'ON' }},
          { api: '/api/customerOrders/get_order_status/', prop: 'order_status', label: '', value: '' },
          { api: '/api/addresses/', prop: 'shipper', label: 'address_num', value: '', query: { address_type: 'SP' }},
          { api: '/api/addresses/', prop: 'receiver', label: 'address_num', value: '', query: { address_type: 'RC' }},
          // { api: '/api/masterOrders/', prop: 'master_num', label: 'order_num', value: '' },
          { api: '/api/houseOrders/', prop: 'house_num', label: 'order_num', value: '' },
          { api: '/api/users/', prop: 'saler', label: 'name', value: '' },
          { api: '/api/logisticsPlanning/', prop: 'logistics_planning', label: 'planning_code', value: '' }
        ],
        dialogOption: [
          // { api: '/api/companies/list_company2/', prop: 'customer', label: 'short_name', value: '', query: { is_customer: true }},
          { api: '/api/companies/list_company2/', prop: 'supplier', label: 'name', value: '', query: { is_supplier: true }},
          { api: '/api/companies/list_company2/', prop: 'customer', label: 'name', value: '', query: { is_customer: true }},
          // { api: '/api/masterOrders/get_related_number/', prop: 'master_number', label: 'master_number', value: '', query: { type: 'master_number', id: 111 }},
          { api: '/api/charges/', prop: 'charge', label: '', value: '' }
        ]
      }
    }
  },
  created() {
    this.bus.$on('remove_order', (data) => {
      this.remove_order(data)
    })
  },
  methods: {
    tabelEtcEvent(data) {
      const that = this
      console.log('解绑---------------------------------')
      console.log(data)
      const outbound_num = data.item.outbound_num
      const event = data.btn.event
      // 移除客户订单
      if (event === 'batch_unbind_outbound_order') {
        const { btn, sourceData, item, index } = data
        console.log(btn, sourceData, item, index)
        const params = {
          api: `/api/customerOrders/batch_unbind_outbound_order/`,
          data: { id: item.id, outbound_num: [outbound_num], customer_order: item.customer_order }
        }
        console.log(params, '参数输出')
        actionPost(params).then(res => {
          if (res.code === 200) {
            this.$message.success('操作成功')
            // 详情页缓冲(详情页加载)
            that.bus.$emit('fullLoading', false)
            // 移除那一项
            sourceData.splice(index, 1)
          } else {
            this.$message.error(res.msg)
          }
        })
      } else if (event === 'remove_parcel') {
        const { btn, sourceDat, item, index } = data
        /* istanbul ignore next */
        console.log('what-->', sourceDat, index)
        const params = {
          api: `/api/${this.initObject.requestUrl.baseUrl}/${btn.event}/`,
          data: {
            id: item.id,
            customer_order_id: this.$route.query.id
          }
        }
        actionPost(params).then(res => {
          if (res.code === 200) {
            this.$message.success('操作成功')
            location.reload()
          } else {
            this.$message.error(res.msg || res.detail || res.message)
          }
        })
      }
    },
    switchAdd(b) {
      this.initObject.isADD = b
    }
  }
}
