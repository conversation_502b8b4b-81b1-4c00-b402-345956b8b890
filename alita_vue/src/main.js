import Vue from 'vue'

import ELEMENT from 'element-ui'
// import 'element-ui/lib/theme-chalk/index.css'
// import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n
import Panel from '@/components/Panel'
// import i18n from './lang'
import '@/styles/index.scss' // global css
import i18n from './i18n/'

// 打印pdf
// import htmlToPdf from '@/utils/htmlToPdf'
// Vue.use(htmlToPdf)

import App from './App'
import router from './router'
import store from './store'

import '@/icons' // icon
import '@/assets/font/iconfont.css'
import './permission' // permission control
import cal from './utils/calculate'

import { sync } from 'vuex-router-sync'
import 'babel-polyfill'
import qs from 'qs'
sync(store, router)

Vue.prototype.cal = cal
// Vue.use(ElementUI, { locale, size: 'mini' })
// Vue.use(ELEMENT, { size: 'mini', i18n: (key, value) => i18n.t(key, value) })
Vue.use(ELEMENT, { size: 'mini', i18n: (key, value) => i18n.t(key, value) })
Vue.component('panel', Panel)
Vue.config.productionTip = false
Vue.prototype.bus = new Vue()
Vue.prototype.qs = qs

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})

router.beforeEach((to, from, next) => {
  if (to.path.endsWith('/tracking')) {
    // 将图标和标题设置为空白
    document.title = '\u200B'
    // 移除所有可能的 favicon
    const favicons = document.querySelectorAll('link[rel*="icon"]')
    favicons.forEach(favicon => {
      favicon.parentNode.removeChild(favicon)
    })
    // 创建一个透明的 favicon
    const transparentFavicon = document.createElement('link')
    transparentFavicon.rel = 'icon'
    transparentFavicon.href = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"></svg>'
    document.head.appendChild(transparentFavicon)
  } else {
    document.title = process.env.TITLE_NAME
    const favicon = document.querySelector('link[rel="icon"]')
    if (favicon) {
      favicon.href = `/favicon_${process.env.SYS_FLAG}.ico`
    }
  }
  next()
})
