<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title><%=htmlWebpackPlugin.options.title%></title>
    <link href="/static/css/normalize/8.0.1/normalize.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/element-ui/2.13.0/theme-chalk/index.css">
    <style>html{background-color: #fff;}.skeleton-loading{width:54px;height:40px;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.skeleton-loading span{display:inline-block;width:8px;height:100%;border-radius:4px;background:lightgreen;-webkit-animation:load 1s ease infinite}@-webkit-keyframes load{0%,100%{height:40px;background:lightgreen}50%{height:70px;margin:-15px 0;background:lightblue}}.skeleton-loading span:nth-child(2){margin-left:4px;-webkit-animation-delay:.2s}.skeleton-loading span:nth-child(3){margin-left:4px;-webkit-animation-delay:.4s}.skeleton-loading span:nth-child(4){margin-left:4px;-webkit-animation-delay:.6s}.skeleton-loading span:nth-child(5){margin-left:4px;-webkit-animation-delay:.8s}</style>
  </head>
  <body>
    <div id="app">
      <!-- 白屏loadding优化 -->
      <div class="skeleton-loading"><span></span><span></span><span></span><span></span><span></span></div>
    </div>
    <!-- built files will be auto injected -->
    <script src="/static/mathjs/7.0.2/math.min.js"></script>
    <script src="/static/vue/2.5.17/vue.min.js"></script>
    <script src="/static/vuex/3.0.1/vuex.min.js"></script>
    <script src="/static/vue-router/3.0.2/vue-router.min.js"></script>
    <script src="/static/element-ui/2.13.0/index.js"></script>
    <script src="/static/element-ui/lib/zh-CN.js"></script>
    <script src="/static/element-ui/lib/en.js"></script>
    <script src="/static/echarts/4.1.0/echarts.min.js"></script>
  </body>
</html>
