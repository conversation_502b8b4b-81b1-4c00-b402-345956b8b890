#!/usr/bin/env bash
git pull

APP_NAME=$(basename $(dirname $(dirname "$PWD")))
#APP_NAME=$1

echo "当前时间: $(date)"

echo "当前运行环境:$APP_NAME"


export alita_end_url=registry.cn-shenzhen.aliyuncs.com/alita_px/alita_backend:v.1.1
export deploy_version=$APP_NAME
export alita_front_url=registry.cn-shenzhen.aliyuncs.com/alita_px/alita_vue:v1.1


docker build --build-arg deploy_version=${APP_NAME} -t registry.cn-shenzhen.aliyuncs.com/alita_px/alita_vue:v1.1 .


docker-compose -f ../docker-compose.yml stop alita-front
docker-compose -f ../docker-compose.yml rm -f alita-front
docker-compose -f ../docker-compose.yml up alita-front -d

# 重启
#docker-compose -f ../docker-compose.yml restart alita-front

# 清理缓存
docker image prune -f
#docker system prune -f
