# 使用 Node.js 镜像来构建前端项目
FROM registry.cn-shenzhen.aliyuncs.com/alita_px/nodejs12:v1 AS builder

# 访问pipeline变量
ARG deploy_version

# 将项目文件复制到工作目录
COPY . .

# 构建前端项目
RUN npm run build:fast -- ${deploy_version}

# 使用 Nginx 镜像作为最终镜像
FROM registry.cn-shenzhen.aliyuncs.com/alita_px/nginx:v1 AS deploy

# 复制构建好的前端项目文件到 Nginx 的默认 web 目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制自定义 Nginx 配置文件到容器中 ==> 如果不使用这一样，后面直接从外面映射进来？
COPY ./nginx-front.conf /etc/nginx/conf.d/default.conf

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
