server {
    listen       28080;
    listen  [::]:28080;
    server_name  localhost;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
    }

    error_page   500 502 503 504  /50x.html;

    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}

server {
        listen       38080;
        listen  [::]:38080;
        server_name  localhost;

        # 增加文件上传大小
        client_max_body_size 50m;

        # 这里的两个文件是指向nginx容器中的
        location /static {
            alias /data/static/alita;
        }

        location /media {
            alias /data/static/alita/media;
        }

        location / {
            include uwsgi_params;
            uwsgi_pass 127.0.0.1:3038;
        }
    }
