#!/bin/bash

PROG_NAME=$0
APP_NAME=$(basename $(dirname $(dirname "$PWD")))
echo "当前运行环境:$APP_NAME"

echo "当前时间: $(date)"

APP_HOME=/data/$APP_NAME

if [ -z "$APP_NAME" ]; then
    echo "Usage: $PROG_NAME {test|zhengfx|tao|szx|tao_fs|ship}"
    exit 2
fi

cd ..
git pull
cd -
npm run build -- ${APP_NAME}

# 发布之前先将PDA的安装包复制到别的目录保存, 发布之后再复制回来, 否则安装包会丢失
package_folder="/data/$APP_NAME/data/package"

if [ -d "$package_folder" ]; then
  mv /data/${APP_NAME}/angle/*.apk /data/${APP_NAME}/data/package/
fi

if [ ! -d $APP_HOME/alita/alita_vue/dist ];then
  echo "发布失败,文件夹不存在"
else
   rm -rf $APP_HOME/angle
   mv $APP_HOME/alita/alita_vue/dist $APP_HOME/angle
   echo "发布成功"
fi

if [ -d "$package_folder" ]; then
  mv /data/${APP_NAME}/data/package/*.apk /data/${APP_NAME}/angle/
fi

