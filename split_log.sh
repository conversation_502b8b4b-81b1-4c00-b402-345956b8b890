#!/usr/bin/env bash
# 切割日志脚本
#0 1 * * * sh /data/zh/alita/split_log.sh zh >/dev/null 2>&1

#APP_NAME=$(basename $(dirname "$PWD"))
APP_NAME=$1
echo "当前运行环境:$APP_NAME"

dir="/data/${APP_NAME}/log"
this_path=$(cd `dirname $0`;pwd)

cd $this_path
echo $this_path
current_date=`date -d "-1 day" "+%Y-%m-%d"`
echo $current_date
cp ${dir}/celery_worker.log ${dir}/celery_worker.log.${current_date}
cat /dev/null > ${dir}/celery_worker.log


cp ${dir}/celery_beat.out ${dir}/celery_beat.log.${current_date}
cat /dev/null > ${dir}/celery_beat.out

cp ${dir}/${APP_NAME}_uwsgi.log ${dir}/${APP_NAME}_uwsgi.log.${current_date}
cat /dev/null > ${dir}/${APP_NAME}_uwsgi.log
