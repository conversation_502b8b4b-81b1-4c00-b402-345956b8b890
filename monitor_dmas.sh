#!/bin/bash

APP_NAME=$1
echo "当前运行环境:$APP_NAME"

if [ "$APP_NAME" = "mz" ] || [ "$APP_NAME" = "yqf" ]; then
    NAME='玄冬'
elif [ "$APP_NAME" = "zj" ] || [ "$APP_NAME" = "zhs" ]; then
    NAME='辛宁'
elif [ "$APP_NAME" = "md" ]; then
    NAME='yif__xu'
elif [ "$APP_NAME" = "zh" ]; then
    NAME='抵达冰结界的晴岚'
elif [ "$APP_NAME" = "hanjin" ] || [ "$APP_NAME" = "hanjinsg" ]; then
    NAME='sunuavx'
else
    NAME='donnie'
fi

# 拼接 JSON 数据
generate_json_data() {
  local content=$1
  local name=$2
  cat <<EOF
{"list": [{"groupNameList": ["\u666e\u4fe1IT\u652f\u6301\u7fa4"], "atList": ["$name"], "receivedContent": "alita: $content"}]}
EOF
}

# 网络带宽监控配置
# 当前服务器配置：阿里云ECS ecs.g7.2xlarge，公网带宽 10 Mbps
# 阈值设置建议：
# - 公网带宽 10 Mbps：建议设置为 8-9 Mbps (网络利用率80-90%)
# - 千兆网络 (1Gbps)：建议设置为 700-800 Mbps (网络利用率70-80%)
# - 万兆网络 (10Gbps)：建议设置为 5000-8000 Mbps
# - 百兆网络 (100Mbps)：建议设置为 70-80 Mbps
# - 可以通过运行 'ethtool <interface>' 查看网络接口的最大速率
# - 建议根据历史流量峰值的80-90%设置阈值
NETWORK_THRESHOLD_MBPS=25    # 网络带宽阈值，单位：Mbps（根据当前10Mbps公网带宽设置为80%）
SAMPLE_INTERVAL=10          # 采样间隔，单位：秒（增加到10秒减少瞬时波动影响）

# 获取网络带宽使用情况
check_network_bandwidth() {
  echo "开始检查外网带宽使用情况..."
  
  # 清理之前的 iptables 规则（如果存在）
  cleanup_old_iptables_rules() {
    if iptables -L EXTERNAL_TRAFFIC -n >/dev/null 2>&1; then
      echo "清理旧的 iptables 监控规则..."
      iptables -D INPUT -j EXTERNAL_TRAFFIC 2>/dev/null || true
      iptables -D OUTPUT -j EXTERNAL_TRAFFIC 2>/dev/null || true
      iptables -F EXTERNAL_TRAFFIC 2>/dev/null || true
      iptables -X EXTERNAL_TRAFFIC 2>/dev/null || true
    fi
  }
  
  # 执行清理
  cleanup_old_iptables_rules
  
  # 定义内网IP段
  PRIVATE_NETWORKS="10.0.0.0/8,**********/12,***********/16,*********/8"
  
  # 使用网络接口统计，然后排除内网流量
  get_interface_stats() {
    # 获取主要网络接口
    local interface=$(ip route | grep default | awk '{print $5}' | head -1)
    if [ -z "$interface" ]; then
      interface=$(cat /proc/net/dev | grep -v "lo:" | grep ":" | head -1 | cut -d':' -f1 | xargs)
    fi
    
    if [ -z "$interface" ]; then
      echo "0 0 unknown"
      return
    fi
    
    # 获取接口总流量
    local stats=$(cat /proc/net/dev | grep "$interface:" | awk '{print $2 " " $10}')
    echo "$stats $interface"
  }
  
  # 获取内网连接的流量估算
  get_internal_traffic_estimate() {
    # 使用 ss 命令获取内网连接并估算流量
    local internal_connections=0
    
    # 统计内网连接数
    IFS=',' read -ra NETWORKS <<< "$PRIVATE_NETWORKS"
    for network in "${NETWORKS[@]}"; do
      local network_prefix=$(echo $network | cut -d'/' -f1 | cut -d'.' -f1-2)
      case $network_prefix in
        "10.0"|"172.16"|"192.168"|"127.0")
          local count=$(ss -tuln 2>/dev/null | grep -E "${network_prefix//./\\.}" | wc -l)
          internal_connections=$((internal_connections + count))
          ;;
      esac
    done
    
    # 简单估算：假设内网连接平均每秒传输较少数据
    # 这是一个粗略估算，实际应该根据具体情况调整
    local estimated_internal_bytes=$((internal_connections * 1024))  # 每个连接每秒1KB
    echo "0 $estimated_internal_bytes"  # packets设为0，只估算字节数
  }
  
  # 第一次采样 - 接口总流量
  INTERFACE_STATS1=$(get_interface_stats)
  INTERFACE_RX1=$(echo $INTERFACE_STATS1 | awk '{print $1}')
  INTERFACE_TX1=$(echo $INTERFACE_STATS1 | awk '{print $2}')
  INTERFACE_NAME=$(echo $INTERFACE_STATS1 | awk '{print $3}')
  INTERFACE_TOTAL1=$((INTERFACE_RX1 + INTERFACE_TX1))
  
  # 第一次采样 - 内网流量估算
  INTERNAL_STATS1=$(get_internal_traffic_estimate)
  INTERNAL_BYTES1=$(echo $INTERNAL_STATS1 | awk '{print $2}')
  
  # 增加活跃网络连接统计
  ACTIVE_CONNECTIONS1=$(ss -tuln | wc -l)
  EXTERNAL_CONNECTIONS1=$(ss -tun | grep -v "127\.\|10\.\|172\.\|192\.168\." | wc -l)
  
  echo "========== 第一次采样 [$(date)] =========="
  echo "监控接口: $INTERFACE_NAME"
  echo "接口RX: $INTERFACE_RX1 字节, TX: $INTERFACE_TX1 字节, 总计: $INTERFACE_TOTAL1 字节"
  echo "估算内网流量: $INTERNAL_BYTES1 字节"
  echo "活跃连接数: $ACTIVE_CONNECTIONS1, 外网连接数: $EXTERNAL_CONNECTIONS1"
  
  # 等待采样间隔
  echo "等待 $SAMPLE_INTERVAL 秒进行第二次采样..."
  sleep $SAMPLE_INTERVAL
  
  # 第二次采样 - 接口总流量
  INTERFACE_STATS2=$(get_interface_stats)
  INTERFACE_RX2=$(echo $INTERFACE_STATS2 | awk '{print $1}')
  INTERFACE_TX2=$(echo $INTERFACE_STATS2 | awk '{print $2}')
  INTERFACE_TOTAL2=$((INTERFACE_RX2 + INTERFACE_TX2))
  
  # 第二次采样 - 内网流量估算
  INTERNAL_STATS2=$(get_internal_traffic_estimate)
  INTERNAL_BYTES2=$(echo $INTERNAL_STATS2 | awk '{print $2}')
  
  # 增加活跃网络连接统计
  ACTIVE_CONNECTIONS2=$(ss -tuln | wc -l)
  EXTERNAL_CONNECTIONS2=$(ss -tun | grep -v "127\.\|10\.\|172\.\|192\.168\." | wc -l)
  
  echo "========== 第二次采样 [$(date)] =========="
  echo "接口RX: $INTERFACE_RX2 字节, TX: $INTERFACE_TX2 字节, 总计: $INTERFACE_TOTAL2 字节"
  echo "估算内网流量: $INTERNAL_BYTES2 字节"
  echo "活跃连接数: $ACTIVE_CONNECTIONS2, 外网连接数: $EXTERNAL_CONNECTIONS2"
  
  # 计算差值
  INTERFACE_RX_DIFF=$((INTERFACE_RX2 - INTERFACE_RX1))
  INTERFACE_TX_DIFF=$((INTERFACE_TX2 - INTERFACE_TX1))
  INTERFACE_BYTES_DIFF=$((INTERFACE_TOTAL2 - INTERFACE_TOTAL1))
  INTERNAL_BYTES_DIFF=$((INTERNAL_BYTES2 - INTERNAL_BYTES1))
  
  # 估算外网流量 = 总流量 - 内网流量估算
  EXTERNAL_BYTES_DIFF=$((INTERFACE_BYTES_DIFF - INTERNAL_BYTES_DIFF))
  
  # 确保外网流量不为负数
  if [ $EXTERNAL_BYTES_DIFF -lt 0 ]; then
    EXTERNAL_BYTES_DIFF=$INTERFACE_BYTES_DIFF
  fi
  
  echo "========== 流量计算分析 =========="
  echo "RX差值: $INTERFACE_RX_DIFF 字节 ($(echo "scale=2; $INTERFACE_RX_DIFF/1024/1024" | bc) MB)"
  echo "TX差值: $INTERFACE_TX_DIFF 字节 ($(echo "scale=2; $INTERFACE_TX_DIFF/1024/1024" | bc) MB)"
  echo "总流量差值: $INTERFACE_BYTES_DIFF 字节 ($(echo "scale=2; $INTERFACE_BYTES_DIFF/1024/1024" | bc) MB)"
  echo "内网流量差值: $INTERNAL_BYTES_DIFF 字节"
  echo "估算外网流量差值: $EXTERNAL_BYTES_DIFF 字节"
  echo "采样间隔: $SAMPLE_INTERVAL 秒"
  
  # 避免除零错误
  if [ $SAMPLE_INTERVAL -eq 0 ]; then
    echo "错误：采样间隔为0"
    return
  fi
  
  # 计算外网流量速率 (字节/秒)
  EXTERNAL_BYTES_RATE=$(echo "scale=2; $EXTERNAL_BYTES_DIFF / $SAMPLE_INTERVAL" | bc)
  
  # 转换为 Mbps
  EXTERNAL_MBPS=$(echo "scale=2; $EXTERNAL_BYTES_RATE * 8 / 1000000" | bc)
  
  echo "========== 最终结果 =========="
  echo "外网流量速率: ${EXTERNAL_MBPS} Mbps"
  echo "外网流量字节速率: ${EXTERNAL_BYTES_RATE} bytes/s"
  
  # 如果流量异常高，建议检查网络进程
  if (( $(echo "$EXTERNAL_MBPS > 5" | bc -l) )); then
    echo "========== 高流量警告 - 建议检查网络进程 =========="
    echo "活跃的外网连接 (前10个):"
    ss -tupn | grep -v "127\.\|10\.\|172\.\|192\.168\." | head -10
    echo ""
    echo "网络使用量较高的进程:"
    netstat -tupln 2>/dev/null | grep -v "127\.\|10\.\|172\.\|192\.168\." | head -5
  fi
  
  # 检查是否超过阈值
  if (( $(echo "$EXTERNAL_MBPS > $NETWORK_THRESHOLD_MBPS" | bc -l) )); then
    content="${APP_NAME}系统警告：外网流量速率已达到 ${EXTERNAL_MBPS} Mbps，超过阈值 ${NETWORK_THRESHOLD_MBPS} Mbps"
    JSON_DATA=$(generate_json_data "$content" "$NAME")
    curl -X POST "http://dmas.puxinc.com/api/v2/wecom/sendMessage?robotId=dmas_local_00001&sendType=async" -H 'Content-Type: application/json' -d "$JSON_DATA"
  fi
}

# 获取磁盘使用情况
df -h | awk 'NR>1 {print $5 " " $1}' | while read output;
do
  # 提取使用率和磁盘名称
  usep=$(echo $output | awk '{print $1}' | sed 's/%//')
  partition=$(echo $output | awk '{print $2}')

  # 检查使用率是否超过95%
  if [ $usep -ge 95 ]; then
    content="${APP_NAME}系统警告：磁盘分区 $partition 使用率已达到 $usep%"
    JSON_DATA=$(generate_json_data "$content" "$NAME")
    curl -X POST "http://dmas.puxinc.com/api/v2/wecom/sendMessage?robotId=dmas_local_00001&sendType=async" -H 'Content-Type: application/json' -d "$JSON_DATA"
  fi
done

# 获取总内存和已使用内存
total_mem=$(free -m | awk '/^Mem:/{print $2}')
used_mem=$(free -m | awk '/^Mem:/{print $3}')

# 计算内存使用率
mem_usage=$(echo "scale=2; $used_mem / $total_mem * 100" | bc)

echo "内存使用率: $mem_usage"

# 检查内存使用率是否达到 90%（可根据需要调整阈值）
if (( $(echo "$mem_usage >= 90" | bc -l) )); then
  echo "警告：内存使用率已达到 $mem_usage%!"
  content="${APP_NAME} 警告：内存使用率已达到 $mem_usage%!"
  JSON_DATA=$(generate_json_data "$content" "$NAME")
  curl -X POST "http://dmas.puxinc.com/api/v2/wecom/sendMessage?robotId=dmas_local_00001&sendType=async" -H 'Content-Type: application/json' -d "$JSON_DATA"
fi

# CPU设置负载阈值
# 设置负载阈值（每个核心的阈值）
THRESHOLD_PER_CORE=3
# 获取系统的 CPU 核心数
CPU_CORES=$(nproc)
# 计算总负载阈值
THRESHOLD=$(echo "$THRESHOLD_PER_CORE * $CPU_CORES" | bc)
# 获取系统的 1 分钟平均负载
LOAD=$(uptime | awk -F'load average:' '{ print $2 }' | cut -d, -f1 | xargs)
echo "总负载阈值:$THRESHOLD, 系统的 1 分钟平均负载:$LOAD"
# 比较负载是否超过阈值
if (( $(echo "$LOAD > $THRESHOLD" | bc -l) )); then
  echo "警告：CPU 1 分钟平均负载已达到 $LOAD，超过阈值 $THRESHOLD"
  content="${APP_NAME} 警告：CPU 1 分钟平均负载已达到 $LOAD，超过阈值 $THRESHOLD"
  JSON_DATA=$(generate_json_data "$content" "$NAME")
  curl -X POST "http://dmas.puxinc.com/api/v2/wecom/sendMessage?robotId=dmas_local_00001&sendType=async" -H 'Content-Type: application/json' -d "$JSON_DATA"
fi

# 执行网络带宽检查
check_network_bandwidth