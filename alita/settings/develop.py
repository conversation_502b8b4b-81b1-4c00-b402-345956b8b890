from .base import *

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# Database
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases

# 邮件设置
EMAIL_HOST = 'smtp.exmail.qq.com'
EMAIL_PORT = '465'
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'QDwx2019'
DEFAULT_FROM_EMAIL = '<EMAIL>'
EMAIL_USE_SSL = True
# EMAIL_USE_TLS
# EMAIL_USE_SSL

APP_DIR = '/home'

# 账单目录
DEBIT_DIR = APP_DIR + '/data/debit/'

# 账单汇总
DEBIT_DIR_ALL = APP_DIR + '/data/bill/'

# 静态资源目录
STATIC_ROOT = APP_DIR + '/static/alita/'

# 静态资源media
STATIC_MEDIA_DIR = STATIC_ROOT + 'media/'

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'alita',
#         'HOST': '**************',
#         'USER': 'root',
#         'PASSWORD': 'zfx@2020',
#         'PORT': '3306',
#     },
#     'postkeeper': {
#         'ENGINE': 'django.db.backends.mysql',
#         'HOST': '************',
#         'NAME': 'postkeeper',
#         'USER': 'admin_postkeeper',
#         'PASSWORD': 'xE#et!oHY7eYhzfx@2019@#$',
#         'PORT': '3306',
#     },
# }
DATABASE_ROUTERS = ['alita.database_router.DatabaseAppsRouter']
DATABASE_APPS_MAPPING = {
    # example:
    # 'app_name':'database_name',
    'indicator': 'postkeeper',
}

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        # 'NAME': 'alita_hanjin',
        'NAME': 'hanjin',
        'HOST': '127.0.0.1',
        'USER': 'root',
        'PASSWORD': 'zfx@2021',
        'PORT': '13336',
        'OPTIONS': {
            'init_command': 'SET sql_mode="STRICT_TRANS_TABLES"',
        }
    }
}

# 多币种配置
MULTI_CURRENCY = True

# 当前币种
CURRENT_CURRENCY = 'CNY'

# redis配置
REDIS_HOST = '127.0.0.1'
REDIS_PORT = '16379'
REDIS_DB = '8'
REDIS_PASSWD = ''

CELERY_APP_NAME = 'alita'
CELERY_BROKER_URL = 'redis://:' +REDIS_PASSWD+ '@' +REDIS_HOST + ':' + REDIS_PORT + '/' + REDIS_DB

# redis配置
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": CELERY_BROKER_URL,
        "KEY_PREFIX": "track_",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100}
            # "PASSWORD": "123",
        }
    }
}

# 业务配置
WEIGHT_CONTROL = True
WEIGHT_PER = 0.7
WEIGHT_PLACES = 1

# 订单首字母设置
SYSTEM_ORDER_MARK = 'FX'

# 订单首字母设置
CUSTOMER_ORDER_MARK = 'FR'

# 清关
CLEARANCE_ORDER_MARK = 'CCO'

# 小包首字母设置
PARCEL_ORDER_MARK = 'P'
# 小包尾字母设置
PARCEL_ORDER_END_MARK = ''

# 系统版本
SYSTEM_VERSION = 'V1'
# 公司系统唯一标识
SYSTEM_MARK = 'DEVELOP'
# 公司配置
COMPANY_NAME = '深圳市雨果通达物流供应链有限公司'
DOMAIN_URL = 'http://test.puxinc.com:38080'
LOGIN_URL = 'http://test.shipping.zhengfx.com/#/login'

# 微信配置
WX_APPID = 'wxef34f9d6835fdf3f'
WX_APPSECRET = '7f11de4ce6afb3f340574b4412134606'
WX_TOKEN = 'abcdefghabcdefghabcdefghabcdefgh'
WX_TEMPLATE_ID = 'd_IJFtNgLmmnc7LZVgI7T8c9bp26BaCkwBlbtQ3O_6g'

# OMS  SKU   识别码前缀
OMS_SKU_PREFIX = 'OW'
OMS_INBOUND_ORDER = 'IN'
OMS_OUTBOUND_ORDER = 'OUT'
OMS_RETURN_ORDER = 'RETURN'

# 是否自动计算打单重量
IS_AUTO_CALC_LABEL_WEIGHT = True


#modekey默认数量
MODE_KEY_DEFAULT_NUM = 5
PARCEL_MODE_KEY_DEFAULT_NUM = 5
CUSTOMER_ORDER_TIMES = 30.0
PARCEL_CUSTOMER_ORDER_TIMES = 30.0

# 调整单是否扣减账户
IS_DEDUCTION_ACCOUNT = False

# 是否发送钉钉
IS_SEND_DINGDING = False

# 是否同步alita订单
IS_SYNC_ALITA_ORDER = False

# 马帮
MABANG_ACCOUNT_NAME = ''
MABANG_API_KEY = ''
MABANG_ACCOUNT_ID = ''

# logger
OPENOBSERVE_ORG = ''
OPENOBSERVE_USER = ''
OPENOBSERVE_PWD = ''
OPENOBSERVE_HOST = ''

#同步订单
MODE_KEY_SYNC_ORDER_NUM = 15
CUSTOMER_ORDER_SYNC_TIMES = 25

# 自动确认时间
AUTO_CONFIRM_TIME = 2880

MINIO_SERVER_URL = ''

UPLOAD_FILE_TO = 'local'

# 同步到WMS
# SYNC_WMS_HOST = 'http://localhost:8001'
SYNC_WMS_HOST = ''
