
from .base import *

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False
# DEBUG = True

# Database
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'alita_hanjin',
        # 公网ip
        'HOST': '***********',
        # 'HOST': '127.0.0.1',
        'USER': 'mdoms_db',
        'PASSWORD': 'alita_mdoms@123qwertyuiop',
        'PORT': '3306',
        'CONN_MAX_AGE': 60 * 60 * 6,
        'OPTIONS': {
            'init_command': 'SET sql_mode="STRICT_TRANS_TABLES"',
        }
    }
}

# 邮件设置
EMAIL_HOST = 'smtp.exmail.qq.com'
EMAIL_PORT = '465'
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'Zyh2022'
DEFAULT_FROM_EMAIL = '<EMAIL>'
EMAIL_USE_SSL = True
# EMAIL_USE_TLS
# EMAIL_USE_SSL

# 应用目录
APP_DIR = '/data/hanjinTest'
# 账单目录
DEBIT_DIR = APP_DIR + '/data/debit/'

# 账单汇总
DEBIT_DIR_ALL = APP_DIR + '/data/bill/'

# 静态资源目录
STATIC_ROOT = APP_DIR + '/static/alita/'

# 静态资源media
STATIC_MEDIA_DIR = STATIC_ROOT + 'media/'

# 多币种配置
MULTI_CURRENCY = True

# 当前币种
CURRENT_CURRENCY = 'CNY'

# redis配置
REDIS_HOST = '127.0.0.1'
REDIS_PORT = '16379'
REDIS_DB = '8'
REDIS_PASSWD = ''

CELERY_APP_NAME = 'alita'
CELERY_BROKER_URL = 'redis://:' +REDIS_PASSWD+ '@' +REDIS_HOST + ':' + REDIS_PORT + '/' + REDIS_DB

# redis配置
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": CELERY_BROKER_URL,
        "KEY_PREFIX": "track_",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100}
            # "PASSWORD": "123",
        }
    }
}

# CELERY_BROKER_URL = 'redis://:alita%40202307251045@127.0.0.1:16379/0'

# redis配置
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": CELERY_BROKER_URL,
        "KEY_PREFIX": "track_",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100}
            # "PASSWORD": "123",
        }
    }
}

# 业务配置
WEIGHT_CONTROL = True
WEIGHT_PER = 0.7
WEIGHT_PLACES = 1

# 订单首字母设置
SYSTEM_ORDER_MARK = 'HG'

# 订单首字母设置
CUSTOMER_ORDER_MARK = 'FB'

# 小包首字母设置
PARCEL_ORDER_MARK = 'E'

# 小包尾字母设置
PARCEL_ORDER_END_MARK = ''

#清关单首字母设置
CLEARANCE_ORDER_MARK = 'CCO'

# 系统版本
SYSTEM_VERSION = 'V2'
# 公司系统唯一标识
SYSTEM_MARK = 'HJ'
# 公司配置
COMPANY_NAME = '韩进集团'
ADJUST_COMPANY_NAME = '韩进集团'
ADJUST_PRODUCT_CODE = 'HJ'
DOMAIN_URL = 'test-omp.hanjinexp.com/hjBackend'

# 文件代理链接
FILE_PROXY = 'https://test-omp.hanjinexp.com/hjBackend'


# 微信配置
WX_APPID = ''
WX_APPSECRET = ''
WX_TOKEN = ''
WX_TEMPLATE_ID = ''

# OMS  SKU   识别码前缀
OMS_SKU_PREFIX = 'OW'
OMS_INBOUND_ORDER = 'IN'
OMS_OUTBOUND_ORDER = 'OUT'

# 是否自动计算打单重量
IS_AUTO_CALC_LABEL_WEIGHT = False

#modekey默认数量
MODE_KEY_DEFAULT_NUM = 5
PARCEL_MODE_KEY_DEFAULT_NUM = 15
CUSTOMER_ORDER_TIMES = 30.0
PARCEL_CUSTOMER_ORDER_TIMES = 1.0
#同步订单
MODE_KEY_SYNC_ORDER_NUM = 5
CUSTOMER_ORDER_SYNC_TIMES = 60

# 调整单是否扣减账户
IS_DEDUCTION_ACCOUNT = True

# 是否发送钉钉
IS_SEND_DINGDING = True

# 是否同步alita订单
IS_SYNC_ALITA_ORDER = False

# 马帮
MABANG_ACCOUNT_NAME = ''
MABANG_API_KEY = ''
MABANG_ACCOUNT_ID = ''

# logger
OPENOBSERVE_ORG = 'default'
OPENOBSERVE_USER = '<EMAIL>'
OPENOBSERVE_PWD = 'HGE123456'
OPENOBSERVE_HOST = 'http://127.0.0.1:3355'
CUSTOMER_API_LOG = True

# 出库入库中转单识别码前缀
OMS_INBOUND_TRANSFER_ORDER = 'IR'
OMS_OUTBOUND_TRANSFER_ORDER = 'OR'

# 自动确认时间
AUTO_CONFIRM_TIME = 2880

MINIO_SERVER_URL = ''

UPLOAD_FILE_TO = 'local'

# 同步到WMS
#SYNC_WMS_HOST = 'http://127.0.0.1:8079'
SYNC_WMS_HOST = 'http://127.0.0.1:8000'

TEST_ENV = True
