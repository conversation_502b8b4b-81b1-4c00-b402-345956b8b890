#!/bin/bash

# Worker心跳发送脚本
# 用途: 定期向所有队列发送心跳任务保持worker活跃

# 配置
API_URL="${DJANGO_URL:-http://localhost:38080}"
HEARTBEAT_ENDPOINT="$API_URL/api/task/simple-heartbeat/send/"
PING_ENDPOINT="$API_URL/api/task/simple-heartbeat/ping/"
LOG_FILE="/tmp/worker_heartbeat.log"

# DMAS告警配置
DMAS_URL="http://dmas.puxinc.com/api/v2/wecom/sendMessage?robotId=dmas_local_00001&sendType=async"
DMAS_GROUP="普信IT支持群"

# 环境联系人配置 - 根据SYSTEM_ENV自动选择@的用户
get_contact_info() {
    local env_key="${1:-default}"
    
    case "$env_key" in
        "mz"|"yqf")
            echo "玄冬"
            ;;
        "zj"|"zhs") 
            echo "辛宁"
            ;;
        "md")
            echo "yif__xu"
            ;;
        "zh")
            echo "zhp17671772491"
            ;;
        "hanjin"|"hanjinsg")
            echo "sunuavx"
            ;;
        *)
            echo "donnie"
            ;;
    esac
}

# 检查命令行参数，如果第一个参数不是选项则作为环境标识
if [ $# -gt 0 ] && [[ ! "$1" =~ ^-- ]] && [[ ! "$1" =~ ^-[ht] ]]; then
    SYSTEM_ENV="$1"
fi

# 获取当前环境对应的联系人
DMAS_AT_USER="${DMAS_AT_USER:-$(get_contact_info "${SYSTEM_ENV}")}"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# DMAS告警函数
send_dmas_alert() {
    local message="$1"
    local environment="${SYSTEM_ENV:-unknown}"
    local hostname=$(hostname)
    
    # 构建告警消息，包含alita前缀
    local alert_content="alita:Worker心跳告警 - 环境:${environment} 服务器:${hostname} - ${message}"
    
    log_message "📨 准备发送DMAS告警 -> @${DMAS_AT_USER} (环境: ${environment})"
    
    # 构建JSON数据
    local json_data=$(cat <<EOF
{
    "list": [
        {
            "groupNameList": ["${DMAS_GROUP}"],
            "atList": ["${DMAS_AT_USER}"],
            "receivedContent": "${alert_content}"
        }
    ]
}
EOF
)
    
    # 发送DMAS告警
    local response=$(curl -s -w "HTTP_CODE:%{http_code}" -X POST "$DMAS_URL" \
        -H 'Content-Type: application/json' \
        -d "$json_data" \
        --connect-timeout 10 \
        --max-time 30 2>/dev/null)
    
    local http_code=$(echo "$response" | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        log_message "📢 DMAS告警发送成功 (HTTP $http_code)"
        return 0
    else
        log_message "❌ DMAS告警发送失败 (HTTP $http_code)"
        return 1
    fi
}

# 检查Django API是否可用
check_api_health() {
    log_message "🔍 检查Django API健康状态..."
    
    response=$(curl -s -w "%{http_code}" -o /dev/null "$PING_ENDPOINT" --connect-timeout 5 --max-time 10)
    
    if [ "$response" = "200" ]; then
        log_message "✅ Django API健康检查通过"
        return 0
    else
        log_message "❌ Django API不可用 (HTTP状态码: $response)"
        return 1
    fi
}

# 发送心跳任务
send_heartbeat() {
    log_message "🔥 开始发送Worker心跳..."
    
    # 构建JSON数据 - 包含所有需要的队列
    json_data='{
        "queues": [
            "default",
            "logqueue", 
            "trackqueue",
            "label_queue_1",
            "label_queue_6", 
            "label_queue_11",
            "label_queue_16",
            "label_queue_21",
            "label_queue_31",
            "label_queue_36",
            "label_queue_26"
        ]
    }'
    
    # 发送POST请求
    response=$(curl -s -X POST "$HEARTBEAT_ENDPOINT" \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        --connect-timeout 10 \
        --max-time 30)
    
    # 添加调试信息：输出原始响应
    # log_message "🔍 API原始响应: $response"
    
    # 检查响应
    if [ $? -eq 0 ]; then
        # 解析响应中的success字段
        success=$(echo "$response" | grep -o '"success":[^,}]*' | cut -d':' -f2 | tr -d ' ')
        
        if [ "$success" = "true" ]; then
            # 提取发送成功的数量 - 修复JSON解析
            sent_count=$(echo "$response" | grep -o '"sent_count"[[:space:]]*:[[:space:]]*[0-9]*' | grep -o '[0-9]*$')
            failed_count=$(echo "$response" | grep -o '"failed_count"[[:space:]]*:[[:space:]]*[0-9]*' | grep -o '[0-9]*$')
            
            log_message "✅ 心跳发送成功! 成功: ${sent_count:-0}, 失败: ${failed_count:-0}"
            
            # 检查是否有失败的任务，如果有则发送DMAS告警
            if [ "${failed_count:-0}" -gt 0 ]; then
                local alert_msg="心跳任务发送完成，但有 ${failed_count} 个队列发送失败，请检查Celery worker状态"
                log_message "⚠️ 发现失败任务，发送DMAS告警..."
                send_dmas_alert "$alert_msg"
            fi
            
            return 0
        else
            # 提取错误信息
            error=$(echo "$response" | grep -o '"error":"[^"]*"' | cut -d'"' -f4)
            log_message "❌ 心跳发送失败: ${error:-未知错误}"
            
            # 发送DMAS告警
            local alert_msg="心跳发送API调用失败: ${error:-未知错误}"
            log_message "🚨 API调用失败，发送DMAS告警..."
            send_dmas_alert "$alert_msg"
            
            return 1
        fi
    else
        log_message "❌ 网络请求失败，无法连接到Django API"
        
        # 发送DMAS告警
        local alert_msg="无法连接到Django API ($API_URL)，网络请求失败"
        log_message "🚨 网络请求失败，发送DMAS告警..."
        send_dmas_alert "$alert_msg"
        
        return 1
    fi
}

# 主函数
main() {
    log_message "==================== Worker心跳检查开始 ===================="
    
    # 显示环境配置来源
    if [ $# -gt 0 ] && [[ ! "$1" =~ ^-- ]] && [[ ! "$1" =~ ^-[ht] ]]; then
        log_message "🔧 环境配置: SYSTEM_ENV=${SYSTEM_ENV:-未设置} (来源: 命令行参数), 告警联系人: @${DMAS_AT_USER}"
    else
        log_message "🔧 环境配置: SYSTEM_ENV=${SYSTEM_ENV:-未设置} (来源: 环境变量), 告警联系人: @${DMAS_AT_USER}"
    fi
     
    # 检查API健康状态
    if ! check_api_health; then
        log_message "❌ API不可用，跳过心跳发送"
        log_message "💡 请确保Django服务运行在 $API_URL"
        exit 1
    fi
    
    # 发送心跳
    if send_heartbeat; then
        log_message "🎉 心跳发送任务完成"
        exit 0
    else
        log_message "💥 心跳发送失败"
        exit 1
    fi
}

# 帮助信息
show_help() {
    echo "Worker心跳发送脚本"
    echo ""
    echo "用法:"
    echo "  $0                    # 发送心跳(使用默认设置)"
    echo "  $0 <环境>             # 发送心跳并指定环境"
    echo "  $0 --help (-h)       # 显示帮助"
    echo "  $0 --test (-t)       # 测试模式(只检查API)"
    echo ""
    echo "环境变量:"
    echo "  DJANGO_URL          # Django服务地址 (默认: http://localhost:38080)"
    echo "  SYSTEM_ENV          # 系统环境标识，任意值，不存在则使用默认设置"
    echo "  DMAS_AT_USER        # 手动指定DMAS告警@的用户名 (可选，会覆盖自动选择)"
    echo ""
    echo "支持的环境联系人映射:"
    echo "  mz/yqf      -> @玄冬"
    echo "  zj/zhs      -> @辛宁" 
    echo "  md          -> @yif__xu"
    echo "  zh          -> @zhp17671772491"
    echo "  hanjin/hanjinsg -> @sunuavx"
    echo "  其他任意值   -> @donnie (默认)"
    echo ""
    echo "DMAS告警功能:"
    echo "  - 网络请求失败时自动发送告警"
    echo "  - API调用失败时自动发送告警"  
    echo "  - 心跳任务发送失败数量>0时自动发送告警"
    echo "  - 根据SYSTEM_ENV自动选择对应负责人"
    echo ""
    echo "示例:"
    echo "  # 命令行参数方式 (推荐)"
    echo "  $0 mz                                # 铭志环境(@玄冬)"
    echo "  $0 md                                # 麦点环境(@yif__xu)"
    echo "  $0 hanjin                            # 韩进环境(@sunuavx)"
    echo "  $0 test                              # 任意环境名(@donnie)"
    echo ""
    echo "  # 环境变量方式"
    echo "  $0                                   # 使用默认设置(@donnie)"
    echo "  SYSTEM_ENV=mz $0                     # 铭志环境(@玄冬)"
    echo "  DJANGO_URL=http://192.168.1.100:38080 $0 zj  # 指定地址+命令行环境"
    echo "  DMAS_AT_USER=admin $0 md             # 手动指定告警人+命令行环境"
}

# 测试模式
test_mode() {
    log_message "🧪 测试模式: 仅检查API连通性"
    
    if check_api_health; then
        log_message "✅ 测试通过: Django API可正常访问"
        exit 0
    else
        log_message "❌ 测试失败: Django API不可访问"
        exit 1
    fi
}

# 参数处理
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --test|-t)
        test_mode
        ;;
    "")
        # 没有参数，直接执行主函数
        main
        ;;
    *)
        # 任何非选项参数都作为环境参数，执行主函数（环境已在上面设置）
        main
        ;;
esac 