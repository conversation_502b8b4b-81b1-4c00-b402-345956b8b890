#!/usr/bin/env python3
import configparser
import os
import sys
import redis
import requests
import json


def get_name_by_app(app_name):
    """根据应用名称获取对应的告警接收人"""
    if app_name in ["mz", "yqf"]:
        return "玄冬"
    elif app_name in ["zj", "zhs"]:
        return "辛宁"
    elif app_name == "md":
        return "yif__xu"
    elif app_name == "zh":
        return "zhp17671772491"
    elif app_name == "hanjin":
        return "sunuavx"
    else:
        return "donnie"


def send_dmas_alert(content, name):
    """发送 DMAS 告警"""
    json_data = {
        "list": [
            {
                "groupNameList": ["\u666e\u4fe1IT\u652f\u6301\u7fa4"], 
                "atList": [name], 
                "receivedContent": content
            }
        ]
    }
    
    url = "http://dmas.puxinc.com/api/v2/wecom/sendMessage?robotId=dmas_local_00001&sendType=async"
    headers = {'Content-Type': 'application/json'}
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(json_data))
        print(f"告警发送结果: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"发送告警失败: {e}")
        return False


def check_redis_dbsize(profile, threshold=500):
    """检查 Redis dbsize 并发送告警"""
    print(f"检查环境: {profile}")
    
    current_path = os.getcwd()
    config_file = f'{current_path}/redis_config.ini'
    
    if not os.path.exists(config_file):
        print(f"配置文件不存在: {config_file}")
        return False
    
    config = configparser.ConfigParser()
    config.read(config_file)
    
    if not config.has_section(profile):
        print(f'配置文件中没有找到 {profile} 配置')
        return False
    
    try:
        # 创建 Redis 连接对象
        redis_client = redis.StrictRedis(
            host=config.get(profile, 'REDIS_HOST'),
            port=int(config.get(profile, 'REDIS_PORT')),
            db=int(config.get(profile, 'REDIS_DB')),
            password=config.get(profile, 'REDIS_PASSWD'),
            decode_responses=True
        )
        
        # 测试连接
        redis_client.ping()
        
        # 获取 dbsize
        dbsize = redis_client.dbsize()
        print(f"当前 dbsize: {dbsize}")
        
        # 检查是否超过阈值
        if dbsize > threshold:
            name = get_name_by_app(profile)
            content = f" {profile}系统警告：{profile} Redis dbsize 已达到 {dbsize}，超过阈值 {threshold}"
            
            print(f"警告：{content}")
            success = send_dmas_alert(content, name)
            
            if success:
                print("告警发送成功")
            else:
                print("告警发送失败")
            
            return True
        else:
            print(f"dbsize ({dbsize}) 正常，未超过阈值 ({threshold})")
            return False
            
    except redis.ConnectionError as e:
        print(f"Redis 连接失败: {e}")
        return False
    except Exception as e:
        print(f"检查过程中发生错误: {e}")
        return False


def main():
    if len(sys.argv) < 2:
        print("用法: python3 redis_dbsize_monitor.py <环境名称> [阈值]")
        print("示例: python3 redis_dbsize_monitor.py hanjin 500")
        sys.exit(1)
    
    profile = sys.argv[1]
    threshold = int(sys.argv[2]) if len(sys.argv) > 2 else 500
    
    print(f"开始监控 Redis dbsize - 环境: {profile}, 阈值: {threshold}")
    check_redis_dbsize(profile, threshold)


if __name__ == '__main__':
    main() 