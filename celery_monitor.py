#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Celery监控脚本 (Python版本) - 改进版

本脚本提供了全面的Celery监控功能，特别针对worker假死问题进行了优化：
1. 检查Celery进程状态
2. 检查Worker响应状态  
3. 监控任务处理效率（智能检测假死）
4. 检查长时间运行任务
5. 队列积压趋势分析
6. Worker负载监控

== 假死检测改进内容 ==
✅ 时间阈值调整：10分钟 → 30分钟（减少误判）
✅ 增加Ping检查：确认Worker真的在线且响应
✅ 增加队列检查：区分正常空闲和真正假死
✅ 增加活跃任务检查：排除正在工作的Worker
✅ 智能告警：只对真正假死的Worker发送告警

假死判断逻辑：
- Worker在线且响应正常
- AND 长时间（30分钟）未处理任务
- AND 队列中有待处理任务
- AND Worker当前无活跃任务
= 才判定为真正假死

使用方法:
    python celery_monitor.py <环境名称>
    例如: python celery_monitor.py zj
"""

import os
import sys
import json
import time
import logging
import subprocess
import configparser
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import traceback

# 导入告警模块和解析器
try:
    from alarm.alarm_common import AlarmConfig, AlarmSender
    from celery_parser import CeleryOutputParser
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保alarm_common.py和celery_parser.py在正确目录下")
    sys.exit(1)


class CeleryMonitor:
    """Celery监控主类"""
    
    def __init__(self, app_name: str):
        self.app_name = app_name
        self.celery_app = "alita"
        
        # 配置日志
        self._setup_logging()
        
        # 监控配置
        self.config = {
            # 队列告警阈值
            'warn_queue_threshold': 10,
            'error_queue_threshold': 50,
            'critical_queue_threshold': 100,
            
            # 假死检测配置 - 改进版
            'max_task_duration': 300,  # 最长任务执行时间（秒）
            'min_processing_rate': 0.1,  # 最小处理速率（任务/秒）
            'no_processing_timeout': 1800,  # 无任务处理超时时间（秒）= 30分钟（改进：从10分钟调整为30分钟）
            'max_idle_time_alert': 7200,  # 最大空闲时间告警阈值（秒）= 2小时
            'queue_growth_threshold': 20,  # 队列快速增长阈值
            
            # 文件路径
            'log_dir': '/data/logs/celery_monitor',
            'stats_file': '/data/logs/celery_monitor/celery_stats.log',
            'queue_history_file': '/data/logs/celery_monitor/queue_history.log',
        }
        
        # 创建日志目录
        Path(self.config['log_dir']).mkdir(parents=True, exist_ok=True)
        
        # 初始化Redis连接
        self._setup_redis_connection()
        
        self.logger.info(f"Celery监控初始化完成 - 环境: {app_name}")
    
    def _setup_logging(self):
        """设置日志配置"""
        log_dir = Path('/data/logs/celery_monitor')
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / 'celery_monitor_python.log'
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.DEBUG,  # 改为DEBUG级别以便查看调试信息
            format='[%(asctime)s] [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def _setup_redis_connection(self):
        """设置Redis连接"""
        try:
            config_file = './redis_config.ini'
            config = configparser.ConfigParser()
            config.read(config_file)
            
            if self.app_name not in config:
                raise ValueError(f"配置文件中未找到环境 {self.app_name}")
            
            db_config = config[self.app_name]
            self.broker_url = (
                f"redis://:{db_config['REDIS_PASSWD']}@"
                f"{db_config['REDIS_HOST']}:{db_config['REDIS_PORT']}/"
                f"{db_config['REDIS_DB']}"
            )
            
            # 设置环境变量
            os.environ['CELERY_BROKER_URL'] = self.broker_url
            
            self.logger.info(f"Redis连接配置完成: {db_config['REDIS_HOST']}:{db_config['REDIS_PORT']}")
            
        except Exception as e:
            self.logger.error(f"Redis连接配置失败: {e}")
            raise
    
    def _run_celery_command(self, command: List[str], timeout: int = 30) -> Tuple[bool, str]:
        """执行Celery命令"""
        try:
            full_command = ['celery', '-A', self.celery_app] + command
            self.logger.debug(f"执行命令: {' '.join(full_command)}")
            
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout,
                env=os.environ.copy()
            )
            
            if result.returncode == 0:
                return True, result.stdout
            else:
                self.logger.error(f"命令执行失败: {result.stderr}")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"命令执行超时: {' '.join(full_command)}")
            return False, "命令执行超时"
        except Exception as e:
            self.logger.error(f"命令执行异常: {e}")
            return False, str(e)
    
    def check_celery_process(self) -> bool:
        """检查Celery进程是否运行"""
        try:
            result = subprocess.run(
                ['pgrep', '-f', f'celery.*-A.*{self.celery_app}'],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                self.logger.info("Celery进程正常运行")
                return True
            else:
                self.logger.error("Celery进程未运行")
                AlarmSender.send_alarm(
                    f"[{self.app_name}] Celery进程未运行，请立即检查",
                    self.app_name
                )
                return False
                
        except Exception as e:
            self.logger.error(f"检查Celery进程失败: {e}")
            return False
    
    def check_worker_status(self) -> Tuple[bool, int]:
        """检查Worker状态"""
        success, output = self._run_celery_command(['inspect', 'ping'])
        
        if not success:
            self.logger.error("无法连接到Celery Workers")
            AlarmSender.send_alarm(
                f"[{self.app_name}] 无法连接到Celery Workers，可能所有worker都已离线",
                self.app_name
            )
            return False, 0
        
        try:
            # 使用解析器计算在线worker数量
            online_workers = CeleryOutputParser.parse_ping_output(output)
            
            self.logger.info(f"当前在线worker数量: {online_workers}")
            
            if online_workers == 0:
                self.logger.error("未检测到在线Worker")
                AlarmSender.send_alarm(
                    f"[{self.app_name}] 没有在线的Worker",
                    self.app_name
                )
                return False, 0
            
            return True, online_workers
            
        except Exception as e:
            self.logger.error(f"解析worker状态失败: {e}")
            return False, 0
    
    def get_worker_stats(self) -> Optional[Dict]:
        """获取Worker统计信息"""
        success, output = self._run_celery_command(['inspect', 'stats'])
        
        if not success:
            self.logger.warning("无法获取worker统计信息")
            return None
        
        try:
            # 调试：打印原始输出
            self.logger.debug(f"Stats原始输出长度: {len(output)}")
            self.logger.debug(f"Stats原始输出前200字符: {repr(output[:200])}")
            
            if not output.strip():
                self.logger.warning("Stats命令返回空内容")
                return None
            
            # 使用新的解析器解析输出
            stats_data = CeleryOutputParser.parse_inspect_output(output)
            return stats_data
        except Exception as e:
            self.logger.error(f"解析worker统计信息失败: {e}")
            self.logger.error(f"原始输出: {repr(output[:500])}")
            return None
    
    def check_task_processing_efficiency(self) -> bool:
        """检查任务处理效率（关键：检测假死）"""
        self.logger.info("开始检查任务处理效率...")
        
        stats_data = self.get_worker_stats()
        if not stats_data:
            return False
        
        current_time = int(time.time())
        total_processed = 0
        total_failed = 0
        dead_workers = []
        
        # 使用解析器提取统计信息
        worker_stats_dict = CeleryOutputParser.extract_worker_stats(stats_data)
        
        for worker_name, worker_stats in worker_stats_dict.items():
            processed = worker_stats.get('task_succeeded', 0)
            failed = worker_stats.get('task_failed', 0)
            total_processed += processed
            total_failed += failed
            
            self.logger.info(
                f"Worker {worker_name} - 成功: {processed}, 失败: {failed}"
            )
            
            # 为每个worker创建单独的统计文件
            worker_stats_file = f"{self.config['stats_file'].replace('.log', '')}_{worker_name}.log"
            
            # 记录当前worker统计
            worker_stats_line = f"{current_time},{processed},{failed}\n"
            with open(worker_stats_file, 'a', encoding='utf-8') as f:
                f.write(worker_stats_line)
            
            # 保持文件大小
            self._trim_file(worker_stats_file, 50)
            
            # 检查单个worker的处理效率
            if self._is_worker_dead(worker_name, worker_stats_file, current_time, processed):
                dead_workers.append(worker_name)
        
        # 记录总体统计信息
        stats_line = f"{current_time},{total_processed},{total_failed}\n"
        with open(self.config['stats_file'], 'a', encoding='utf-8') as f:
            f.write(stats_line)
        
        # 保持文件大小，只保留最近100条记录
        self._trim_file(self.config['stats_file'], 100)
        
        # 发送具体的假死告警（改进版：只有真正假死的才告警）
        if dead_workers:
            dead_worker_list = ', '.join(dead_workers)
            AlarmSender.send_alarm(
                f"[{self.app_name}] 检测到真正的假死Worker: {dead_worker_list} - 这些worker在线但长时间不处理任务且队列中有待处理任务，需要立即检查",
                self.app_name
            )
        else:
            self.logger.info("未检测到真正的假死Worker（改进版检测逻辑已排除正常空闲的Worker）")
        
        self.logger.info(f"总处理统计 - 成功: {total_processed}, 失败: {total_failed}")
        
        # 检查总体处理效率（作为备用检查）
        return self._analyze_processing_efficiency(current_time, total_processed)
    
    def _is_worker_dead(self, worker_name: str, worker_stats_file: str, current_time: int, current_processed: int) -> bool:
        """改进版：检查单个worker是否假死"""
        try:
            with open(worker_stats_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) < 2:
                self.logger.debug(f"Worker {worker_name} - 首次记录或数据不足")
                return False
            
            # 获取前一次的记录
            prev_line = lines[-2].strip()
            prev_time, prev_processed, _ = map(int, prev_line.split(','))
            
            time_diff = current_time - prev_time
            processed_diff = current_processed - prev_processed
            
            if time_diff > 0:
                processing_rate = processed_diff / time_diff
                self.logger.debug(
                    f"Worker {worker_name} 处理速率: {processing_rate:.3f} 任务/秒 "
                    f"(处理: {processed_diff}, 时间: {time_diff}秒)"
                )
                
                # 改进的假死检测逻辑
                if processed_diff == 0 and time_diff > self.config['no_processing_timeout']:
                    self.logger.info(f"Worker {worker_name} 在过去{time_diff}秒内没有处理任务，进行进一步检查...")
                    
                    # 检查1: Worker是否真的在线且响应
                    if not self._check_worker_online(worker_name):
                        self.logger.warning(f"Worker {worker_name} ping失败，真的可能有问题")
                        return True
                    
                    # 检查2: 是否有任务在队列中等待
                    if self._has_pending_tasks():
                        self.logger.warning(f"Worker {worker_name} 长时间未处理任务且队列中有待处理任务")
                        return True
                    else:
                        self.logger.info(f"Worker {worker_name} 长时间未处理任务，但队列中无待处理任务，属于正常空闲状态")
                        
                        # 只有超过最大空闲时间才作为可疑（但不发送假死告警）
                        if time_diff > self.config['max_idle_time_alert']:
                            self.logger.info(f"Worker {worker_name} 空闲时间过长({time_diff}秒)，建议关注")
                        return False
                    
                    # 检查3: Worker是否有活跃任务
                    if self._check_worker_active_tasks(worker_name):
                        self.logger.info(f"Worker {worker_name} 当前有活跃任务，不是假死")
                        return False
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查Worker {worker_name} 状态失败: {e}")
            return False
    
    def _check_worker_online(self, worker_name: str) -> bool:
        """检查指定Worker是否在线"""
        try:
            success, output = self._run_celery_command(['inspect', 'ping', '--destination', worker_name])
            return success and worker_name in output
        except Exception as e:
            self.logger.error(f"检查Worker {worker_name} 在线状态失败: {e}")
            return False
    
    def _has_pending_tasks(self) -> bool:
        """检查是否有待处理的任务"""
        try:
            queue_length, _ = self.check_queue_status()
            return queue_length > 0
        except Exception as e:
            self.logger.error(f"检查待处理任务失败: {e}")
            return False
    
    def _check_worker_active_tasks(self, worker_name: str) -> bool:
        """检查指定Worker是否有活跃任务"""
        try:
            success, output = self._run_celery_command(['inspect', 'active', '--destination', worker_name])
            if success:
                active_data = CeleryOutputParser.parse_inspect_output(output)
                active_count = CeleryOutputParser.count_total_tasks(active_data)
                return active_count > 0
            return False
        except Exception as e:
            self.logger.error(f"检查Worker {worker_name} 活跃任务失败: {e}")
            return False
    
    def _analyze_processing_efficiency(self, current_time: int, total_processed: int) -> bool:
        """分析总体处理效率（作为备用检查）"""
        try:
            with open(self.config['stats_file'], 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) < 2:
                self.logger.info("首次运行或数据不足，跳过总体效率检查")
                return True
            
            # 获取前一次的记录
            prev_line = lines[-2].strip()
            prev_time, prev_processed, _ = map(int, prev_line.split(','))
            
            time_diff = current_time - prev_time
            processed_diff = total_processed - prev_processed
            
            if time_diff > 0:
                processing_rate = processed_diff / time_diff
                self.logger.info(
                    f"总体任务处理速率: {processing_rate:.3f} 任务/秒 "
                    f"(时间窗口: {time_diff}秒, 处理任务数: {processed_diff})"
                )
            
            return True
            
        except Exception as e:
            self.logger.error(f"分析总体处理效率失败: {e}")
            return False
    
    def check_active_tasks(self) -> Tuple[int, List[Dict]]:
        """检查活跃任务"""
        success, output = self._run_celery_command(['inspect', 'active'])
        
        if not success:
            self.logger.warning("无法获取活跃任务信息")
            return 0, []
        
        try:
            # 调试信息
            self.logger.debug(f"Active原始输出长度: {len(output)}")
            if not output.strip():
                self.logger.warning("Active命令返回空内容")
                return 0, []
            
            # 使用解析器解析活跃任务
            active_data = CeleryOutputParser.parse_inspect_output(output)
            all_tasks = []
            total_active = 0
            
            for worker_name, tasks in active_data.items():
                if isinstance(tasks, list):
                    total_active += len(tasks)
                    all_tasks.extend(tasks)
            
            self.logger.info(f"当前活跃任务数: {total_active}")
            return total_active, all_tasks
            
        except Exception as e:
            self.logger.error(f"解析活跃任务信息失败: {e}")
            self.logger.error(f"Active原始输出: {repr(output[:300])}")
            return 0, []
    
    def check_long_running_tasks(self) -> bool:
        """检查长时间运行的任务"""
        self.logger.info("检查长时间运行任务...")
        
        _, active_tasks = self.check_active_tasks()
        
        if not active_tasks:
            return True
        
        current_time = time.time()
        long_running_count = 0
        
        for task in active_tasks:
            time_start = task.get('time_start')
            if time_start:
                try:
                    # time_start是Unix时间戳
                    task_duration = current_time - float(time_start)
                    
                    if task_duration > self.config['max_task_duration']:
                        long_running_count += 1
                        task_name = task.get('name', 'Unknown')
                        task_id = task.get('id', 'Unknown')
                        
                        self.logger.warning(
                            f"发现长时间运行任务: {task_name} (ID: {task_id}), "
                            f"运行时间: {task_duration:.0f}秒"
                        )
                        
                except (ValueError, TypeError) as e:
                    self.logger.debug(f"解析任务时间失败: {e}")
        
        if long_running_count > 0:
            AlarmSender.send_alarm(
                f"[{self.app_name}] 发现 {long_running_count} 个长时间运行任务"
                f"（>{self.config['max_task_duration']}秒），可能存在任务卡死",
                self.app_name
            )
        
        return True
    
    def check_queue_status(self) -> Tuple[int, bool]:
        """检查队列状态"""
        success, output = self._run_celery_command(['inspect', 'reserved'])
        
        if not success:
            self.logger.warning("无法获取队列信息")
            return 0, False
        
        try:
            # 调试信息
            self.logger.debug(f"Reserved原始输出长度: {len(output)}")
            if not output.strip():
                self.logger.warning("Reserved命令返回空内容")
                return 0, False
            
            # 使用解析器解析预留任务
            reserved_data = CeleryOutputParser.parse_inspect_output(output)
            total_reserved = CeleryOutputParser.count_total_tasks(reserved_data)
            
            self.logger.info(f"当前队列长度: {total_reserved}")
            
            # 队列告警检查
            alarm_sent = False
            if total_reserved >= self.config['critical_queue_threshold']:
                AlarmSender.send_alarm(
                    f"[{self.app_name}] 任务队列严重积压，当前队列长度: {total_reserved}",
                    self.app_name
                )
                alarm_sent = True
            elif total_reserved >= self.config['error_queue_threshold']:
                AlarmSender.send_alarm(
                    f"[{self.app_name}] 任务队列积压，当前队列长度: {total_reserved}",
                    self.app_name
                )
                alarm_sent = True
            elif total_reserved >= self.config['warn_queue_threshold']:
                AlarmSender.send_alarm(
                    f"[{self.app_name}] 任务队列轻微积压，当前队列长度: {total_reserved}",
                    self.app_name
                )
                alarm_sent = True
            
            return total_reserved, alarm_sent
            
        except Exception as e:
            self.logger.error(f"解析队列信息失败: {e}")
            self.logger.error(f"Reserved原始输出: {repr(output[:300])}")
            return 0, False
    
    def analyze_queue_trend(self) -> bool:
        """分析队列积压趋势"""
        self.logger.info("分析队列积压趋势...")
        
        queue_length, _ = self.check_queue_status()
        current_time = int(time.time())
        
        # 记录队列长度历史
        queue_line = f"{current_time},{queue_length}\n"
        with open(self.config['queue_history_file'], 'a', encoding='utf-8') as f:
            f.write(queue_line)
        
        # 保持文件大小
        self._trim_file(self.config['queue_history_file'], 50)
        
        # 分析趋势
        try:
            with open(self.config['queue_history_file'], 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) < 3:
                return True
            
            # 获取前一次的队列长度
            prev_line = lines[-2].strip()
            prev_time, prev_queue = map(int, prev_line.split(','))
            
            trend_diff = queue_length - prev_queue
            
            if trend_diff > 0:
                self.logger.info(f"队列长度增加趋势: +{trend_diff}")
                
                if trend_diff > self.config['queue_growth_threshold']:
                    AlarmSender.send_alarm(
                        f"[{self.app_name}] 队列长度快速增长，"
                        f"当前: {queue_length}, 增长: +{trend_diff}，可能存在处理问题",
                        self.app_name
                    )
            else:
                self.logger.info(f"队列长度变化: {trend_diff}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"分析队列趋势失败: {e}")
            return False
    
    def _trim_file(self, file_path: str, max_lines: int):
        """保持文件行数，删除旧记录"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) > max_lines:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(lines[-max_lines:])
                    
        except Exception as e:
            self.logger.error(f"文件清理失败 {file_path}: {e}")
    
    def run_monitor(self) -> bool:
        """运行完整监控检查"""
        self.logger.info("=" * 60)
        self.logger.info("开始Celery监控检查...")
        
        try:
            # 1. 检查Celery进程
            if not self.check_celery_process():
                return False
            
            # 2. 检查Worker状态
            worker_ok, worker_count = self.check_worker_status()
            if not worker_ok:
                return False
            
            # 3. 检查任务处理效率（重点：检测假死）
            self.check_task_processing_efficiency()
            
            # 4. 检查长时间运行任务
            self.check_long_running_tasks()
            
            # 5. 检查队列状态
            self.check_queue_status()
            
            # 6. 分析队列趋势
            self.analyze_queue_trend()
            
            self.logger.info("Celery监控检查完成")
            return True
            
        except Exception as e:
            self.logger.error(f"监控检查异常: {e}")
            self.logger.error(traceback.format_exc())
            
            # 发送监控异常告警
            AlarmSender.send_alarm(
                f"[{self.app_name}] Celery监控脚本执行异常: {str(e)}",
                self.app_name
            )
            return False


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python celery_monitor.py <环境名称>")
        print("例如: python celery_monitor.py zj")
        sys.exit(1)
    
    app_name = sys.argv[1]
    
    try:
        monitor = CeleryMonitor(app_name)
        success = monitor.run_monitor()
        
        if not success:
            sys.exit(1)
            
    except Exception as e:
        print(f"监控脚本启动失败: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 