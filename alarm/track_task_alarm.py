import configparser
import sys
# coding=UTF-8
import pymysql
import datetime
import requests,json

# 轨迹拉取任务监控
def track_task_delay_alarm(database_info):

   config = configparser.ConfigParser()
   config.read('/data/'+database_info+'/alita/alarm/config.ini')
   
   print(f"监控环境: {database_info}")
   print(f"数据库主机: {config.get(database_info, 'host')}")

   conn = pymysql.connect(host=config.get(database_info, 'host'), 
                         database=config.get(database_info, 'name'), 
                         port=int(config.get(database_info, 'port')), 
                         user=config.get(database_info, 'user'),
                         password=config.get(database_info, 'password'), 
                         charset="utf8")

   cursor = conn.cursor()

   # 检查超过2小时未处理的轨迹拉取任务
   sql = """SELECT count(1) as num FROM track_tracktask 
            WHERE pull_status = 'WAIT_PULL' AND del_flag = 0
              AND update_date < DATE_SUB(NOW(), INTERVAL 3 HOUR)"""
   
   print(f"执行SQL: {sql}")
   cursor.execute(sql)
   result = cursor.fetchone()
   
   cursor.close()
   conn.close()

   # 根据环境设置告警联系人
   if database_info in ['mz', 'yqf']:
      name = '玄冬'
   elif database_info in ['zj', 'zhs']:
      name = '辛宁'
   elif database_info in ['md']:
      name = 'yif__xu'
   elif database_info in ['zh']:
      name = '抵达冰结界的晴岚'
   elif database_info in ['hanjin']:
      name = 'sunuavx'
   else:
      name = 'donnie'

   if result and result[0] > 0:
      count = result[0]
      content = f"{database_info} 系统告警：发现 {count} 个轨迹拉取任务超过3小时未处理，状态为WAIT_PULL，请及时查看！"
      
      url = 'http://dmas.puxinc.com/api/v2/wecom/sendMessage?robotId=dmas_local_00001&sendType=async'
      json_dict = {
         "list": [{
            "groupNameList": ["普信IT支持群"], 
            "atList": [name], 
            "receivedContent": content
         }]
      }
      headers = {'Content-Type': 'application/json'}
      json_data = json.dumps(json_dict, ensure_ascii=False)
      r = requests.post(url, data=json_data.encode('utf-8'), headers=headers).json()
      print(f"DMAS告警已发送: {r}")
   else:
      print("未发现超时的轨迹拉取任务")


if __name__ == '__main__':
   args = sys.argv
   if args and len(args) > 1:
      print(f"接收到参数: {args[1]}")
      track_task_delay_alarm(args[1])
   else:
      print("请提供环境参数") 