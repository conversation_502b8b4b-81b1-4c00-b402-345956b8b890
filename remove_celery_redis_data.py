import configparser
import os
import sys

import redis


def remove_redis_key(profile):
    print(profile)

    current_path = os.getcwd()
    print(current_path)

    config = configparser.ConfigParser()
    config.read(f'{current_path}/redis_config.ini')

    if not config.has_section(profile):
        print(f'not config {profile}')
        return

    # 创建Redis连接对象
    redis_client = redis.StrictRedis(host=config.get(profile, 'REDIS_HOST'),
                                     port=int(config.get(profile, 'REDIS_PORT')),
                                     db=int(config.get(profile, 'REDIS_DB')),
                                     password=config.get(profile, 'REDIS_PASSWD'))

    # 使用keys方法查找所有以'qo_'开头的key
    keys_to_delete = redis_client.keys('qo_*')

    # 遍历并删除这些key
    for key in keys_to_delete:
        # 删除每一个key
        redis_client.delete(key)
        print(key)

    # 打印删除的key数量
    print(f"Total keys deleted: {len(keys_to_delete)}")


if __name__ == '__main__':
    args = sys.argv
    if args:
        print(args[1])
        remove_redis_key(args[1])

    # remove_redis_key('mz')