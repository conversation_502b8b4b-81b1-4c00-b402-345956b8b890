from __future__ import absolute_import, unicode_literals

import time
import traceback
import zipfile
from datetime import date, timedelta
import json
from shutil import copyfile
import openpyxl
from decimal import InvalidOperation

from django.core.exceptions import ObjectDoesNotExist, MultipleObjectsReturned
from openpyxl.styles import Font
import pandas as pd
import xlwt
from openpyxl.utils import column_index_from_string
from pydash import get

from django.db import transaction
from django.db.models.functions import Trunc<PERSON>onth, TruncDay
from django.db.models import Sum, Count, Q
import os, random
from datetime import datetime
from decimal import Decimal
import xlrd
from celery_once import QueueOnce
from django.forms import model_to_dict
from openpyxl import Workbook
from openpyxl.styles import Alignment
from django.conf import settings
from django.core.files.base import ContentFile, File

from common.service.clt_bill_service import CltBillService
from common.utils.dmas_util import send_dmas_msg_by_customer
from common.utils.file_util import upload_file
from common import xlrd_compat
from common.common_parameter import ORDER_MANE_MAP
from common.common_const import ShopTypeEnum
from common.database import MysqlConn
from common.service.big_parcel_service import handler_parcel_weighting
from common.service.fba_order_import_service import FbaOrderImportService, MultiFbaOrderImportService, \
    clean_fba_order_before_import
from common.service.fba_query_export import FbaDebitsDetailsExport
from common.service.fbm_order_import_service import FbmOrderImportService
from common.service.parcel_order import upload_parcel_customer_order, assemble_params_item, \
    upload_parcel_customer_order_item, upload_changed_parcel_customer_order
from common.service.settle_bill_service import SettleBillService
from common.utils.pdf_barcode_read import calculate_md5, handle_uploaded_file, read_and_split_pdf_label, \
    identify_pdf_barcode
from cs.models import VasOrder, VasOrderChargeIn
from oms.models import WarehouseOutboundOrder, WarehouseInboundOrder, OutboundOrderChargeIn, InboundOrderChargeIn, \
    ReturnOrder, ReturnOrderChargeIn
from order.integration.schemas.customer_order_schema import CustomerOrderFbmExcelSchema, \
    CustomerOrderFbmParcelExcelSchema
from order.integration.util.customerOrderUtil import request_server
from order.views.clearance import calc_day_diff
from pms.util.calc import common_judge_fba_and_remote, check_customer_order_shipment_id
from pms.util.revenue_calc import calc_price_by_zone_value
from alita.celery_config import app
from common.error import ErrorCode, ParamError
from common.order_num_gen_rule import create_order_num
from common.tools import get_update_params, gen_order_num, change_order_status, judge_parcel_num_rule, \
    create_sys_parcel_num, create_order_reference_id, print_fba_label_common, summary_predict_parcels_data, \
    save_address, billing_customer_order, save_bulk_model_obj, get_parcel_order_by_order_num, \
    GetExcelCellData, common_order_cost_share, get_order_with_order_num, get_order_by_order_num, \
    order_import_verification_product_code, product_sales_price_trans_protocol_price, validate_weighing_and_packing
from common.order_num_gen_rule import is_parcel_customer_order

from sms.views.supplier_clearance import calc_day_diff
from django.core.cache import cache


from alita.logger import logger
from common.order_num_gen_rule import create_order_num
from common.tools import order_revenue_confirm, order_cost_confirm, is_lock_time, num_to_str, \
    get_update_params_by_user, gen_parcel_order_num, get_excel_cell
from company.models import Company, Address
from info.models import Charge
from order.models import CustomerOrder, ParcelCustomerOrder, Clearance, CustomerOrderChargeOut, CustomerOrderChargeIn, \
    ParcelOrderChargeIn, ParcelOrderChargeOut, ClearanceChargeOut, ClearanceChargeIn, Parcel, CustomsClearanceOrder, \
    CustomsClearanceOrderChargeIn, CustomsClearanceAddress, OrderAttachment, \
    ParcelItem, OrderSyncUploadTask, OceanOrder, TruckOrder, OrderAsyncTask, BigParcel, OcShipment, \
    OrderSyncUploadTaskResult, RmManifestConfig, RmManifestResFile, DmasMsgSendTask
from pms.models import Product, Service, ProductAsyncTask, ProductSalesPrice
from rbac.models import UserProfile
from report.models import ProfitAnalysis
from settle.models import AccountPayable, Export, Debit, Payment, Invoice, AccountReceivable, ReceivableSummary, \
    PayableSummary, Reconciliation, ReconciliationDetails, ReceivableDaySummary, PayableDaySummary, Receipt, Billing, \
    DebitAdjust, DebitAdjustDetail
from wms.models import InventoryBatch,InventoryAge,RentChargesConfig,RentOrder,RentOrderDetails
from order.integration.util.royalMailUtil import manifests_shipment

# 导入任务结果模型用于自动清理
from django_celery_results.models import TaskResult
from django.utils import timezone


# 自动清理任务执行记录的定时任务
@app.task(bind=True, base=QueueOnce, max_retries=0, once={'graceful': True, 'keys': ['auto_cleanup_task_results']})
def auto_cleanup_task_results(self, days=7):
    """
    自动清理超过指定天数的任务执行记录
    
    Args:
        days (int): 保留天数，默认7天
    """
    try:
        logger.info(f'开始自动清理超过 {days} 天的任务执行记录')
        
        # 计算删除的截止时间
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # 删除旧记录
        deleted_count, _ = TaskResult.objects.filter(
            date_created__lt=cutoff_date
        ).delete()
        
        logger.info(f'自动清理完成：删除了 {deleted_count} 条超过 {days} 天的任务执行记录')
        
        return {
            'success': True,
            'deleted_count': deleted_count,
            'message': f'自动清理成功：删除了 {deleted_count} 条记录',
            'cutoff_date': cutoff_date.isoformat()
        }
        
    except Exception as e:
        logger.error(f'自动清理任务执行记录失败: {e}')
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e),
            'message': f'自动清理失败: {str(e)}'
        }


# 对账单写入系统对账明细(生成对账明细 / 生成对账单明细)
@app.task(bind=True, base=QueueOnce, max_retries=0, once={'graceful': True, 'keys': ['mode_key']})
def handler_reconciliation_task(self, mode_key):
    logger.info(f'start handler_reconciliation_task {mode_key}')
    queryset = Reconciliation.objects.filter(debit_status=1, del_flag=False, mode_key=mode_key)
    for item in queryset:
        logger.info(f"handler_reconciliation_task item = {item.debit_num}")
        if not item.main_file or not item.main_file.path:
            item.debit_status = 6
            item.handler_msg = '请确保上传了账单文件'
            item.save()
        else:
            try:
                execute_reconciliation_task(item)
            except Exception as e:
                logger.error('handler_reconciliation_task:' + str(traceback.format_exc()))
                item.handler_msg = str(e)
                item.update_date = datetime.now()
                item.debit_status = 5
                item.save()
                continue
    logger.info('end handler_reconciliation_task')


# 对账单对账任务(点击开启对账后)(开始对账)
@app.task(bind=True, base=QueueOnce, max_retries=0, once={'graceful': True, 'keys': ['mode_key']})
def handler_statement_compared_task(self, mode_key):
    logger.info(f'start handler_statement_compared_task {mode_key}')
    queryset = Reconciliation.objects.filter(debit_status=3, del_flag=False, mode_key=mode_key)
    for item in queryset:
        logger.info(f'handler_statement_compared_task reconciliation--->{item.debit_num}')
        # 获取该对账单下面的对账明细记录
        reconciliation_details = ReconciliationDetails.objects.filter(reconciliation=item, del_flag=False, is_compared=False)

        i = 1
        total_count = reconciliation_details.count()
        for recon_detail in reconciliation_details:

            logger.info(f'handler_statement_compared_task recon_detail--->{recon_detail.order_num}, {total_count}, {i}')
            i = i + 1
            try:
                handler_details(item, recon_detail)
            except Exception as e:
                logger.info(traceback.format_exc())
                logger.error(
                    'handler_statement_compared_task:' + str(e) + ", recon_detail =" + str(recon_detail.__dict__))
                item.handler_msg = str(e)
                item.update_date = datetime.now()
                item.save()
                continue

        details = ReconciliationDetails.objects.filter(reconciliation=item, is_compared=False, del_flag=False)
        if details.count() == 0:
            item.debit_status = 4
            item.update_date = datetime.now()
            item.handler_msg = '对账完成'
            item.save()

    logger.info('end handler_statement_compared_task')


@transaction.atomic
def handler_details(item, recon_detail: ReconciliationDetails):
    order_num = recon_detail.order_num
    track_num = recon_detail.track_num
    customer_orderNum = recon_detail.customer_orderNum
    third_orderNo = recon_detail.third_orderNo
    ocean_order_num = recon_detail.ocean_order_num
    container_no = recon_detail.container_no
    mawb = recon_detail.mawb
    amount = recon_detail.amount
    pieces = recon_detail.pieces
    weight = recon_detail.weight
    zone_value = recon_detail.zone_value
    charge = recon_detail.charge

    query_set = AccountPayable.objects.none()
    current_order = None
    # 获取queryset
    if order_num:
        query_set = AccountPayable.objects.filter(order_num=str(order_num).strip(), is_adjust=False,
                                                  supplier=recon_detail.supplier,
                                                  charge_name__name=charge,
                                                  del_flag=False)
        # query_set = order_query_set.filter(charge_name__name=charge)
    elif third_orderNo:
        query_set = AccountPayable.objects.filter(third_orderNo=str(third_orderNo).strip(), is_adjust=False,
                                                  supplier=recon_detail.supplier,
                                                  charge_name__name=charge,
                                                  del_flag=False)
        # query_set = order_query_set.filter(charge_name__name=charge)
    elif customer_orderNum:
        query_set = AccountPayable.objects.filter(customer_orderNum=str(customer_orderNum).strip(),
                                                  supplier=recon_detail.supplier,
                                                  charge_name__name=charge,
                                                  is_adjust=False, del_flag=False)
    elif mawb:
        query_set = AccountPayable.objects.filter(mawb=str(mawb).strip(),
                                                  supplier=recon_detail.supplier,
                                                  charge_name__name=charge,
                                                  is_adjust=False, del_flag=False)

    elif track_num:
        current_order_num = None
        if recon_detail.bill_type == 'PC':
            orders = ParcelCustomerOrder.objects.filter(Q(tracking_num=track_num) | Q(order_num=order_num),
                                                        del_flag=False)
            if orders.exists():
                current_order = orders.first()
                current_order_num = current_order.order_num
        if current_order_num:
            query_set = AccountPayable.objects.filter(order_num=current_order_num, is_adjust=False,
                                                      supplier=recon_detail.supplier,
                                                      charge_name__name=charge,
                                                      del_flag=False)
        else:
            query_set = AccountPayable.objects.filter(track_num=str(track_num).strip(), is_adjust=False,
                                                      supplier=recon_detail.supplier,
                                                      charge_name__name=charge,
                                                      del_flag=False)
        # query_set = order_query_set.filter(charge_name__name=charge)
    elif container_no:
        # 如果有柜号, 则通过柜号获取对应单据, 再根据单据号获取对应的付款明细
        if recon_detail.bill_type == 'CLI':
        # if recon_detail.order_num.startswith('CLI'):
            # 进口报关单
            orders = Clearance.objects.filter(container_no=container_no, del_flag=False)
            # if recon_detail.supplier:
            #     orders = orders.filter(supplier=recon_detail.supplier)
            if orders:
                current_order = orders.first()
                query_set = AccountPayable.objects.filter(order_num=current_order.clearance_num,
                                                          is_adjust=False,
                                                          supplier=recon_detail.supplier,
                                                          charge_name__name=charge,
                                                          del_flag=False,
                                                          debit_num__isnull=True,
                                                          )
        elif recon_detail.bill_type == 'OC':
            # 海运提单
            orders = OceanOrder.objects.filter(container_no=recon_detail.container_no, del_flag=False)
            # if recon_detail.supplier:
            #     orders = orders.filter(supplier=recon_detail.supplier)
            if orders:
                current_order = orders.first()
                query_set = AccountPayable.objects.filter(order_num=current_order.order_num, is_adjust=False,
                                                          supplier=recon_detail.supplier,
                                                          del_flag=False, charge_name__name=charge)
        elif recon_detail.bill_type == 'TO':
            # 卡派单
            ocean_orders = OceanOrder.objects.filter(container_no=recon_detail.container_no, del_flag=False)
            print('ocean_orders-->', ocean_orders)
            if ocean_orders.exists():
                ocean_order = ocean_orders.first()
                truck_orders = TruckOrder.objects.filter(ocean_order_num=ocean_order.order_num,
                                                         arrive_destination=recon_detail.arrive_destination,
                                                         del_flag=False)
                print('truck_orders-->', truck_orders)
                if truck_orders.exists():
                    current_order = truck_orders.first()
                    query_set = AccountPayable.objects.filter(order_num=current_order.truck_order_num,
                                                              supplier=recon_detail.supplier,
                                                              is_adjust=False,
                                                              del_flag=False, charge_name__name=charge)
                else:
                    recon_detail.compares_msg = f'未查询到海运单号为: {ocean_order.order_num}, ' \
                                                f'目的站为: {recon_detail.arrive_destination}的卡派单'
                    recon_detail.save()
                    return
            else:
                recon_detail.compares_msg = f'未查询到柜号为: {recon_detail.container_no}的海运单'
                recon_detail.save()
                return
        else:
            recon_detail.compares_msg = f'未知的对账账单类型: {recon_detail.bill_type}'
            recon_detail.save()
            return
    # 只提供海运提单
    elif ocean_order_num:
        if recon_detail.bill_type == 'CLI':
            # 只查财务数据, 跟订单业务数据隔离开
            query_set = AccountPayable.objects.filter(order_num__startswith='CLI',
                                                      ocean_order_num=ocean_order_num,
                                                      supplier=recon_detail.supplier,
                                                      charge_name__name=charge,
                                                      is_adjust=False, del_flag=False)
            print('query_set6-->', query_set)

    # 排除已对账的付款明细
    if query_set.count() > 0:
       query_set = query_set.filter(
            Q(debit_num__isnull=True) |  # 保留 debit_num 为 NULL 的记录
            Q(debit_num='') |            # 保留 debit_num 为空字符串的记录
            Q(debit_num=item.debit_num)  # 保留 debit_num 等于当前对账单号的记录
        )

    logger.info(f'付款明细query_set-->{query_set}')
    if query_set.count() > 0:
        # 判断对账单是明细对账还是汇总对账
        if item.reconciliation_type == '1':
            # 明细对账，对比项为金额差值最小的那一项
            tem_list = [{'abs': abs(Decimal(x.account_amount) - Decimal(amount)), 'id': x.id} for x in query_set]
            logger.info(f'明细对账，对比项为金额差值最小的那一项: {tem_list}')

            # 找出绝对值最小的那一条并且来对账
            account_payable = AccountPayable.objects.get(id=sorted(tem_list, key=lambda x: x['abs'])[0]['id'])
            compare_amount = account_payable.account_amount
        else:
            # 汇总对账，对比项为第一项，金额为汇总金额
            account_payable = query_set.first()
            compare_amount = query_set.aggregate(total=Sum('account_amount'))['total'] or 0
            # 有则进行对比   记录差异金额、件数、重量
        # account_payable = query_set.first()
        if not recon_detail.order_num:
            # 如果对账明细没有业务单据号写进去付款明细的业务单据号
            recon_detail.order_num = account_payable.order_num

        # 设置单据号
        if not current_order:
            current_order = get_order_by_order_num(order_num)

        if current_order:
            recon_detail.customer = getattr(current_order, 'customer', None)
            recon_detail.product = getattr(current_order, 'product', None)

        if not recon_detail.track_num:
            # 如果对账明细没有业务单据号写进去付款明细的业务单据号
            recon_detail.track_num = account_payable.track_num
        if not recon_detail.customer_orderNum:
            # 如果对账明细没有业务单据号写进去付款明细的业务单据号
            recon_detail.customer_orderNum = account_payable.customer_orderNum
        if not recon_detail.third_orderNo:
            # 如果对账明细没有业务单据号写进去付款明细的业务单据号
            recon_detail.third_orderNo = account_payable.third_orderNo

        recon_detail._amount = Decimal(compare_amount or 0) - Decimal(amount or 0)
        recon_detail._weight = Decimal(account_payable.weight or 0) - Decimal(weight or 0)
        recon_detail._pieces = Decimal(account_payable.pieces or 0) - Decimal(pieces or 0)
        # 分区
        recon_detail._zone_value = 'diff' if account_payable.zone_value != zone_value and zone_value != '' else None
        recon_detail.s_amount = compare_amount
        recon_detail.s_weight = account_payable.weight
        recon_detail.s_pieces = account_payable.pieces
        # 分区
        recon_detail.s_zone_value = account_payable.zone_value
        recon_detail.debit_date = account_payable.account_time
        # 记金额差异
        if recon_detail._amount == 0:
            recon_detail.is_diff = False
        else:
            # 正负差异
            if recon_detail._amount > 0:
                recon_detail.diff_direction = 1
            else:
                recon_detail.diff_direction = 2
            recon_detail.is_diff = True
        # 记件数差异
        if recon_detail._pieces == 0:
            recon_detail.is_pieces_diff = False
        else:
            # 正负差异
            if recon_detail._pieces > 0:
                recon_detail.diff_pieces_direction = 1
            else:
                recon_detail.diff_pieces_direction = 2
            recon_detail.is_pieces_diff = True
        # 记重量差异
        if recon_detail._weight == 0:
            recon_detail.is_weight_diff = False
        else:
            # 正负差异
            if recon_detail._weight > 0:
                recon_detail.diff_weight_direction = 1
            else:
                recon_detail.diff_weight_direction = 2
            recon_detail.is_weight_diff = True
        # 记分区差异
        if not recon_detail._zone_value:
            recon_detail.is_zone_diff = False
        else:
            # 正负差异
            if recon_detail._zone_value:
                recon_detail.diff_zone_direction = 1
            # else:
            #     recon_detail.diff_zone_direction = 2
            recon_detail.is_zone_diff = True
        # 付款明细和对账单明细写入账单件数并判断账单是否齐全
        complete_num = account_payable.complete_num + pieces
        is_complete_bill = complete_num == account_payable.pieces
        # 付款明细
        account_payable.is_complete_bill = is_complete_bill
        account_payable.complete_num = complete_num
        account_payable.save()
        # 调整单明细
        recon_detail.compares_msg = '对账成功'
        recon_detail.is_complete_bill = is_complete_bill
        query_set.update(debit_num=item.debit_num)
    else:
        recon_detail.compares_msg = '未找到付款明细'
        recon_detail._amount = - recon_detail.amount
        recon_detail._weight = recon_detail.weight
        recon_detail._pieces = recon_detail.pieces

        # 正负差异
        if recon_detail._amount > 0:
            recon_detail.diff_direction = 1
        elif recon_detail._amount < 0:
            recon_detail.diff_direction = 2

        recon_detail.is_diff = True
        recon_detail.is_not_exist = True
        if current_order:
            recon_detail.customer = getattr(current_order, 'customer', None)
            recon_detail.product = getattr(current_order, 'product', None)
        # 不会走到if里面啊
        if query_set.count() != 0:
            first_queryset = query_set.first()
            # 设置账单日期、跟踪号、业务单据号、客户订单号、客户
            recon_detail.track_num = first_queryset.track_num
            recon_detail.customer_orderNum = first_queryset.customer_orderNum
            recon_detail.third_orderNo = first_queryset.third_orderNo
            recon_detail.debit_date = first_queryset.account_time
            # 设置产品--------------------
            if is_parcel_customer_order(first_queryset.order_num):
                # 小包
                try:
                    order = ParcelCustomerOrder.objects.get(order_num=first_queryset.order_num, del_flag=False)
                except ParcelCustomerOrder.DoesNotExist:
                    order = None
            elif first_queryset.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK) or \
                    first_queryset.order_num.startswith('ZJ' + settings.CUSTOMER_ORDER_MARK):
                # 客户
                try:
                    order = CustomerOrder.objects.get(order_num=first_queryset.order_num, del_flag=False)
                except CustomerOrder.DoesNotExist:
                    order = None
            elif recon_detail.order_num.startswith('CLI'):
                # 进口报关单
                try:
                    order = Clearance.objects.get(clearance_num=first_queryset.order_num, del_flag=False)
                except Clearance.DoesNotExist:
                    order = None
            elif recon_detail.order_num.startswith('TR'):
                # 卡派单
                try:
                    order = TruckOrder.objects.get(truck_order_num=first_queryset.order_num, del_flag=False)
                except TruckOrder.DoesNotExist:
                    order = None
            else:
                # 海运提单
                try:
                    order = OceanOrder.objects.get(order_num=first_queryset.order_num, del_flag=False)
                except OceanOrder.DoesNotExist:
                    order = None
            # 能找到系统对用得客户
            if order and hasattr(order, 'customer'):
                recon_detail.customer = order.customer
                recon_detail.product = order.product
            # 没有产品
            if recon_detail.product is not None and not recon_detail.product:
                recon_detail.is_product = True
        recon_detail.save()
    # 设置为已对账
    recon_detail.is_compared = True
    recon_detail.save()


class Parcel1:
    parcel_weight = ''
    parcel_volume = ''


# 计算对账单明细的收入金额、计价金额、收入差异金额
@transaction.atomic
def calc_income_amount(order, fee):
    # 获取账单金额
    print(order.order_num)
    print(fee)
    account_receivables = AccountReceivable.objects.filter(order_num=order.order_num, del_flag=False,
                                                           charge_name__code=fee)
    income_amount = account_receivables.aggregate(total=Sum('account_amount'))['total'] or 0
    print('----------------------获取收款明细并汇总收入金额--------------------------')
    print(income_amount)
    return income_amount


# 通过第三方服务单号找到系统FX号回写包裹重量，然后计算价格
@transaction.atomic
def get_sys_order(third_no, all_parcel, fee, zone_value):
    # 每种费用的集合
    print('--------------------通过第三方服务单号:%s获取运输单-------------------------' % third_no)
    order = CustomerOrder.objects.get(third_orderNo=third_no, del_flag=False)
    parcel1 = []
    parcel2 = []
    for item in all_parcel:
        parcel = Parcel.objects.filter(customer_order=order, tracking_num=item['tracking_num'], del_flag=False)
        if parcel.count() == 1:
            parcel.update(debit_weight=Decimal(item['weight']) * Decimal(0.4536))
            p1 = Parcel1()
            p1.parcel_weight = Decimal(parcel.first().label_weight)
            p1.parcel_volume = Decimal(parcel.first().parcel_volume)
            p2 = Parcel1()
            p2.parcel_weight = Decimal(parcel.first().debit_weight)
            p2.parcel_volume = Decimal('0')
            parcel1.append(p1)
            parcel2.append(p2)
        else:
            # print('找不到--------------------------------------------------')
            print(third_no)
            print(order)
            # print('找不到--------------------------------------------------')
            raise ParamError('跟踪号%s找不到对应的包裹！' % (str(item['tracking_num'])), ErrorCode.PARAM_ERROR)

    zone = zone_value if zone_value > order.zone_value else order.zone_value
    # 这里计算计价金额
    print(order.product)
    print(zone)
    print('包裹1-----------------------------------')
    for z in parcel1:
        print(z.parcel_weight)
        print(z.parcel_volume)
    print('包裹2-----------------------------------')
    for z in parcel2:
        print(z.parcel_weight)
        print(z.parcel_volume)
    fee1_results = calc_price_by_zone_value(order.product, zone, parcel1, datetime.now())
    num1 = 0
    for fee_item in fee1_results:
        num1 += fee_item.result_fee
    fee1_results1 = calc_price_by_zone_value(order.product, zone, parcel2, datetime.now())
    num2 = 0
    for fee_item in fee1_results1:
        num2 += fee_item.result_fee
    calc_amount = num1 if num1 > num2 else num2
    income_amount = calc_income_amount(order, fee)
    _income_amount = income_amount - calc_amount
    params = {
        'income_amount': income_amount,
        'calc_amount': calc_amount,
        '_income_amount': income_amount - calc_amount,
        'is_income_diff': True if (income_amount - calc_amount) != 0 else False
    }
    return params


# 获取去除空格和逗号之后的数字
def get_check_number(num):
    logger.info('get_check_number num ='+num)
    if num == '':
        return 0
    return Decimal(str(num).strip(' ').replace(',', '')) or 0


@transaction.atomic
def handler_reconciliation_sheet(sheet, table, third_orderNos, rows, reconciliation, supplier):
    # 是否第一个sheet
    cell_num = 0 if sheet == 1 else -1
    # 过滤第三方服务单号放在dict里面
    set_dict = {}
    for t in third_orderNos:
        set_dict[t] = []
    # 按第三方服务单号归类
    print('-----------------------进入第%s张表------------------------------' % (str(sheet)))
    print('-----------------------cell_num: %s ------------------------------' % (str(cell_num)))
    for cell in range(1, rows):
        # 第三方单号
        params = {
            'third_orderNo': table.cell_value(cell, 0),
            'tracking_num': table.cell_value(cell, 1),
            'weight': get_check_number(table.cell_value(cell, 2)) * Decimal(0.4536),
            'packages': get_check_number(table.cell_value(cell, 4 - cell_num)),
            # 运费
            '101': get_check_number(table.cell_value(cell, 5 - cell_num)) + get_check_number(
                table.cell_value(cell, 6 - cell_num)),
            # 额外操作费
            '102': get_check_number(table.cell_value(cell, 9 + cell_num)),
            # 私人住宅费
            '103': get_check_number(table.cell_value(cell, 10 + cell_num)),
            # 单件超尺寸
            '109': get_check_number(table.cell_value(cell, 11 + cell_num)),
            # 单件超重
            '110': get_check_number(table.cell_value(cell, 12 + cell_num)),
            # 偏远附加费
            '111': get_check_number(table.cell_value(cell, 13 + cell_num)) + get_check_number(
                table.cell_value(cell, 14 + cell_num)),
            # 偏远地区私人住宅
            '120': get_check_number(table.cell_value(cell, 15 + cell_num)),
            # 地址更正费
            '124': get_check_number(table.cell_value(cell, 16 + cell_num)),
            # 超大件
            '255': get_check_number(table.cell_value(cell, 17 + cell_num)),
            # 旺季额外操作附加费
            '227': get_check_number(table.cell_value(cell, 18 + cell_num)),
            # 分区
            'zone_value': table.cell_value(cell, 20 + cell_num),
        }
        if sheet == 1:
            params['101'] += get_check_number(table.cell_value(cell, 8)) + get_check_number(table.cell_value(cell, 21))
        elif sheet == 2:
            params['amount'] = get_check_number(table.cell_value(cell, 3))
        if params['third_orderNo'] in set_dict.keys():
            set_dict[params['third_orderNo']].append(params)
    # 按运费在归类
    for third in set_dict:
        # 第二个sheet的件数
        pieces = set_dict[third][0]['packages']
        # 分区都是一样的
        zone_value = set_dict[third][0]['zone_value']
        # 额外操作费
        ewczf = sum([x['102'] for x in set_dict[third]])
        # 私人住宅费
        srzzf = sum([x['103'] for x in set_dict[third]]) if sheet == 1 else set_dict[third][0]['103']
        # 单件超尺寸
        djccc = sum([x['109'] for x in set_dict[third]]) if sheet == 1 else set_dict[third][0]['109']
        # 单件超重
        djcz = sum([x['110'] for x in set_dict[third]])
        # 偏远附加费
        pyfjf = sum([x['111'] for x in set_dict[third]]) if sheet == 1 else set_dict[third][0]['111']
        # 偏远地区私人住宅
        pydqsrzz = sum([x['120'] for x in set_dict[third]]) if sheet == 1 else set_dict[third][0]['120']
        # 地址更正费
        dzgzf = sum([x['124'] for x in set_dict[third]]) if sheet == 1 else set_dict[third][0]['124']
        # 超大件
        cdj = sum([x['255'] for x in set_dict[third]]) if sheet == 1 else set_dict[third][0]['255']
        # 旺季额外操作附加费
        wjewczfjf = sum([x['227'] for x in set_dict[third]]) if sheet == 1 else set_dict[third][0]['227']
        # 运费总和
        if sheet == 1:
            yf = sum([x['101'] for x in set_dict[third]])
        else:
            # sheet2的运费拿汇总amount减去除地址更正费用外的所有费用
            yf = sum(
                [x['amount'] for x in set_dict[third]]) - ewczf - djccc - srzzf - pyfjf - pydqsrzz - cdj - wjewczfjf

        params = {
            'order_num': '',
            'third_orderNo': third,
            'customer_orderNum': '',
            'track_num': '',
            'reconciliation': reconciliation,
            'currency': 'USD',
            'zone_value': zone_value,
            'supplier': supplier
        }
        print('------------------------------------运费和私人住宅非------------------------------------')
        print(yf)
        print('======================')
        print(third)
        print('======================')
        print(srzzf)
        print('------------------------------------运费和私人住宅非------------------------------------')

        if yf != 0:
            # 把有运费的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['101'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='运费')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 101, zone_value)
                ReconciliationDetails.objects.create(**params, amount=yf,
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='运费', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + yf
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()

        if ewczf != 0:
            # 把有额外操作费的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['102'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='额外操作费')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 102, zone_value)
                ReconciliationDetails.objects.create(**params,
                                                     amount=ewczf if sheet == 1 else tem[0]['102'],
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='额外操作费', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + (ewczf if sheet == 1 else tem[0]['102'])
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()

        if srzzf != 0:
            # 把有私人住宅费的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['103'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='私人住宅费')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 103, zone_value)
                ReconciliationDetails.objects.create(**params,
                                                     amount=srzzf if sheet == 1 else tem[0]['103'],
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='私人住宅费', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + (srzzf if sheet == 1 else tem[0]['103'])
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()

        if djccc != 0:
            # 把有单件超尺寸的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['109'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='单件超尺寸')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 109, zone_value)
                ReconciliationDetails.objects.create(**params,
                                                     amount=djccc if sheet == 1 else tem[0]['109'],
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='单件超尺寸', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + (djccc if sheet == 1 else tem[0]['109'])
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()

        if djcz != 0:
            # 把有单件超重的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['110'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='单件超重')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 110, zone_value)
                ReconciliationDetails.objects.create(**params, amount=djcz if sheet == 1 else tem[0]['110'],
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='单件超重', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + (djcz if sheet == 1 else tem[0]['110'])
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()

        if pyfjf != 0:
            # 把有偏远附加费的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['111'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='偏远附加费')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 111, zone_value)
                ReconciliationDetails.objects.create(**params,
                                                     amount=pyfjf if sheet == 1 else tem[0]['111'],
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='偏远附加费', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + (pyfjf if sheet == 1 else tem[0]['111'])
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()

        if pydqsrzz != 0:
            # 把偏远地区私人住宅的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['120'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='偏远地区私人住宅')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 120, zone_value)
                ReconciliationDetails.objects.create(**params,
                                                     amount=pydqsrzz if sheet == 1 else tem[0]['120'],
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='偏远地区私人住宅', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + (pydqsrzz if sheet == 1 else tem[0]['120'])
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()

        if dzgzf != 0:
            # 把地址更正费的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['124'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='地址更正费')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 124, zone_value)
                ReconciliationDetails.objects.create(**params,
                                                     amount=dzgzf if sheet == 1 else tem[0]['124'],
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='地址更正费', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + (dzgzf if sheet == 1 else tem[0]['124'])
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()

        if cdj != 0:
            # 把超大件的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['255'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='超大件')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 255, zone_value)
                ReconciliationDetails.objects.create(**params, amount=cdj if sheet == 1 else tem[0]['255'],
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='超大件', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + (cdj if sheet == 1 else tem[0]['255'])
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()

        if wjewczfjf != 0:
            # 把旺季额外操作附加费的明细集合起来，创建一条明细
            tem = [x for x in set_dict[third] if x['227'] != 0]
            detail = ReconciliationDetails.objects.filter(**params, del_flag=False, charge='旺季额外操作附加费')
            if detail.count() == 0:
                price_params = get_sys_order(third, set_dict[third], 227, zone_value)
                ReconciliationDetails.objects.create(**params,
                                                     amount=wjewczfjf if sheet == 1 else tem[0]['227'],
                                                     weight=sum([x['weight'] for x in tem]),
                                                     pieces=len(tem),
                                                     charge='旺季额外操作附加费', **price_params)
            else:
                # 已存在费用、重量、件数进行累加
                exit_detail = detail.first()
                exit_detail.amount = exit_detail.amount + (wjewczfjf if sheet == 1 else tem[0]['227'])
                exit_detail.weight = exit_detail.weight + sum([x['weight'] for x in tem])
                exit_detail.pieces = exit_detail.pieces + len(tem)
                exit_detail.save()


@transaction.atomic
def execute_reconciliation_task(item):
    if item.template_type == '0':
        # 通用写入
        excel_file = item.main_file.path
        logger.info(excel_file)
        # 打开excel
        wb = xlrd.open_workbook(excel_file)
        # 获取第一张表
        table = wb.sheets()[0]
        rows = table.nrows  # 行数
        cols = table.ncols  # 列数
        rows_title = [table.cell_value(0, x) for x in range(0, cols)]
        cost_index = rows_title.index('账单金额')
        weight_index = rows_title.index('账单重量')
        pieces_index = rows_title.index('账单件数')
        # 分区
        zone_index = rows_title.index('账单分区')
        order_num = rows_title.index('订单号')
        third_orderNo = rows_title.index('第三方服务订单号')
        customer_orderNum = rows_title.index('客户订单号')
        track_num = rows_title.index('跟踪号')
        charge = rows_title.index('费用')
        currency = rows_title.index('币种')
        ocean_order_num_value = rows_title.index('提单号')
        container_no = rows_title.index('柜号')
        bill_type = rows_title.index('账单类型')
        debit_date = rows_title.index('账单日期')
        supplier_index = rows_title.index('供应商')
        arrive_destination = rows_title.index('目的站')

        for index in range(1, rows):
            # 获取当前行账单成本金额、订单号、第三方服务订单号、客户订单号、跟踪号的值
            val1 = str(table.cell_value(index, order_num)).strip()
            val2 = str(table.cell_value(index, third_orderNo)).strip()
            val3 = str(table.cell_value(index, customer_orderNum)).strip()
            val4 = str(table.cell_value(index, track_num)).strip()
            val5 = table.cell_value(index, cost_index) or 0
            val6 = table.cell_value(index, weight_index) or 0
            val7 = table.cell_value(index, pieces_index) or 0
            val8 = table.cell_value(index, charge).strip()
            val9 = table.cell_value(index, currency).strip()
            ocean_order_num = str(table.cell_value(index, ocean_order_num_value)).strip()
            val10 = str(table.cell_value(index, container_no)).strip()
            val11 = str(table.cell_value(index, bill_type)).strip()
            val12 = table.cell_value(index, debit_date)
            val13 = table.cell_value(index, arrive_destination)
            if isinstance(val12, float):
                val12 = xlrd.xldate_as_datetime(val12, wb.datemode)
            # 分区
            val0 = table.cell_value(index, zone_index)
            if isinstance(val0, float) and val0.is_integer():
                val0 = int(val0)
            logger.info(f'获取当前的分区{val0}重量{val6}件数{val7},0425hh')
            # 账单类型转换
            bill_type_map = {v: k for k, v in ReconciliationDetails.BILL_TYPE}
            if val11 in bill_type_map:
                val11 = bill_type_map.get(val11)
            detail_queryset = ReconciliationDetails.objects.filter(zone_value=val0, order_num=val1, third_orderNo=val2,
                                                                   customer_orderNum=val3, del_flag=False,
                                                                   track_num=val4, reconciliation=item,
                                                                   charge=val8, ocean_order_num=ocean_order_num,
                                                                   container_no=val10, bill_type=val11,
                                                                   arrive_destination=val13)
            supplier = item.supplier
            if isinstance(table.cell_value(index, supplier_index), (int, float)):
                company_short_name = str(int(table.cell_value(index, supplier_index))).strip()
                if company_short_name:
                    supplier = Company.objects.filter(short_name=company_short_name, is_supplier=True,
                                                      del_flag=False).first()
                    if supplier is None:
                        raise ParamError(f'未查到客户编码 {company_short_name}', ErrorCode.PARAM_ERROR)
                        # supplier = None

            if detail_queryset.count() == 0:
                logger.info(
                    f'{item.debit_num} - create ReconciliationDetails: zone_value={val0}, order_num={val1}, third_orderNo={val2},customer_orderNum={val3}, track_num={val4},amount={val5}, weight={val6}, pieces={val7}, reconciliation={item},supplier={supplier}, charge={val8}, currency={val9},debit_date={val12},ocean_order_num={ocean_order_num},container_no={val10}, bill_type={val11},arrive_destination={val13}')
                ReconciliationDetails.objects.create(zone_value=val0, order_num=val1, third_orderNo=val2,
                                                     customer_orderNum=val3, track_num=val4,
                                                     amount=val5, weight=val6, pieces=val7, reconciliation=item,
                                                     supplier=supplier, charge=val8, currency=val9,
                                                     debit_date=val12,
                                                     ocean_order_num=ocean_order_num,
                                                     container_no=val10, bill_type=val11,
                                                     arrive_destination=val13)
            else:
                # 重复相加
                detail = detail_queryset.first()
                detail.amount = detail.amount + Decimal(val5)
                detail.save()

        item.debit_status = 2
        item.handler_msg = 'handle success!'
        item.save()
    else:
        supplier = item.supplier
        # 联邦写入
        excel_file = item.main_file.path
        logger.info(excel_file)
        # 打开excel
        wb = xlrd.open_workbook(excel_file)
        if len(wb.sheets()) < 2:
            item.debit_status = 5
            item.handler_msg = '请确保上传账单是Fedex对账类型而不是通用类型！'
            item.save()
        # 获取第一张表
        table1 = wb.sheets()[0]
        rows1 = table1.nrows  # 行数
        # cols1 = table1.ncols  # 列数
        table2 = wb.sheets()[1]
        rows2 = table2.nrows  # 行数
        # cols2 = table1.ncols  # 列数
        # 获取去重后第一列第三方服务单号
        third_orderNos1 = list(set([table1.cell_value(x, 0) for x in range(1, rows1)]))
        third_orderNos2 = list(set([table2.cell_value(x, 0) for x in range(1, rows2)]))
        if 'NO REFERENCE INFORMATION' in third_orderNos1 or 'NO REFERENCE INFORMATION' in third_orderNos2 \
                or 'NO' in third_orderNos1 or 'NO' in third_orderNos2:
            item.debit_status = 5
            item.handler_msg = '请确保上传账单都有第三方服务单号！'
            item.save()
        else:
            not_find_third = []
            for third_number in third_orderNos1:
                if CustomerOrder.objects.filter(third_orderNo=third_number, del_flag=False).count() == 0:
                    not_find_third.append(third_number)
            for third_number in third_orderNos2:
                if CustomerOrder.objects.filter(third_orderNo=third_number, del_flag=False).count() == 0:
                    not_find_third.append(third_number)
            # 过滤掉找不到订单第三方服务单号
            for no_number in not_find_third:
                if no_number in third_orderNos1:
                    third_orderNos1.remove(no_number)
                if no_number in third_orderNos2:
                    third_orderNos2.remove(no_number)
            # if len(not_find_third) != 0:
            #     item.debit_status = 5
            #     item.handler_msg = '如下第三方单号:%s在系统中找不到！' % (','.join(not_find_third))
            #     item.save()
            # 第一个sheet
            handler_reconciliation_sheet(1, table1, third_orderNos1, rows1, item, supplier)
            # 第二个sheet
            handler_reconciliation_sheet(2, table2, third_orderNos2, rows2, item, supplier)
            item.debit_status = 2
            item.handler_msg = 'handle success!'
            item.save()
    # 用完记得删除释放资源
    wb.release_resources()


# 小包订单收入确认
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_parcel_order_revenue_task(self):
    logger.info('--------------start handler_parcel_order_revenue_task--------------')
    # 获取需要自动收入确认和成本确认得产品
    revenue_product = [item.id for item in Product.objects.filter(del_flag=False, revenue_lock=True)]

    current_time = datetime.now()
    # one_hour_ago = current_time - timedelta(hours=48)
    one_hour_ago = current_time - timedelta(minutes=settings.AUTO_CONFIRM_TIME)

    # 获取未收入确认和成本确认得小包订单
    for product in revenue_product:
        not_revenue_parcel_order_queryset = ParcelCustomerOrder.objects.filter(~Q(tracking_num=''), del_flag=False,
                                                                      product=product,
                                                                      order_status='WO',
                                                                      is_revenue_lock=False,
                                                                      create_date__lt=one_hour_ago,
                                                                      tracking_num__isnull=False
                                                                      ).order_by('-id')[:10000]
        bc_orders = ParcelCustomerOrder.objects.filter(
            del_flag=False,
            product=product,
            order_status='BC',
            is_revenue_lock=False,
            create_date__lt=one_hour_ago,
        ).order_by('-id')[:10000]

        not_revenue_parcel_order = []
        if not_revenue_parcel_order_queryset.exists():
            for item in not_revenue_parcel_order_queryset:
                # 增加redis 锁，防止重复执行
                cache_key = f'handler_revenue_lock_{item.order_num}'
                if cache.get(cache_key):
                    continue
                cache.set(cache_key, "1", 1800)
                not_revenue_parcel_order.append(item)
        if bc_orders.exists():
            for item in bc_orders:
                # 增加redis 锁，防止重复执行
                cache_key = f'handler_revenue_lock_{item.order_num}'
                if cache.get(cache_key):
                    continue
                cache.set(cache_key, "1", 1800)
                not_revenue_parcel_order.append(item)

        if not not_revenue_parcel_order:
           continue

        user = UserProfile.objects.get(id=1)
        for item in not_revenue_parcel_order:
            logger.info("handler count ->" + str(item.id))
            # 进行收入确认
            order_revenue_confirm(item.id, 'ParcelCustomerOrder', user)

            cache_key = f'handler_revenue_lock_{item.order_num}'
            cache.delete(cache_key)

    logger.info('------------------end handler_parcel_order_revenue_task-------------------')


# 小包订单成本确认
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_parcel_order_cost_task(self):
    logger.info(f'handler_parcel_order_cost_task start -->{self.request} ')
    # 获取需要自动收入确认和成本确认得产品
    cost_product = [item.id for item in Product.objects.filter(del_flag=False, cost_lock=True)]

    current_time = datetime.now()
    one_hour_ago = current_time - timedelta(hours=48)

    for product in cost_product:
        # 获取未收入确认和成本确认得小包订单
        not_cost_parcel_order_queryset = ParcelCustomerOrder.objects.filter(~Q(tracking_num=''),
                                                                   del_flag=False,
                                                                   product=product,
                                                                   order_status='WO', 
                                                                   is_revenue_lock=True,
                                                                   create_date__lt=one_hour_ago,
                                                                   is_cost_lock=False,
                                                                   tracking_num__isnull=False
                                                                   )[:10000]

        bc_orders = ParcelCustomerOrder.objects.filter(
            del_flag=False,                       
            product=product,
            order_status='BC', 
            is_revenue_lock=True,
            create_date__lt=one_hour_ago,
            is_cost_lock=False,
            )[:10000]


        not_cost_parcel_order = []
        if not_cost_parcel_order_queryset.exists():
            for item in not_cost_parcel_order_queryset:
                # 增加redis 锁，防止重复执行
                cache_key = f'handler_cost_lock_{item.order_num}'
                if cache.get(cache_key):
                    continue
                cache.set(cache_key, "1", 1800)
                not_cost_parcel_order.append(item)
        if bc_orders.exists():
            for item in bc_orders:
                # 增加redis 锁，防止重复执行
                cache_key = f'handler_cost_lock_{item.order_num}'
                if cache.get(cache_key):
                    continue
                cache.set(cache_key, "1", 1800)
                not_cost_parcel_order.append(item)

        if not not_cost_parcel_order:
           continue


        user = UserProfile.objects.get(id=1)
        for item in not_cost_parcel_order:
            # 进行成本确认
            order_cost_confirm(item.id, 'ParcelCustomerOrder', user)

            cache_key = f'handler_cost_lock_{item.order_num}'
            cache.delete(cache_key)

    logger.info('handler_parcel_order_cost_task end --->')


# 导出任务
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_export_task(self):
    logger.info("--------------start handler_export_task--------------")

    exports = Export.objects.filter(export_status=1, del_flag=False)
    if not exports:
        logger.info("no export task")
        return

    for item in exports:
        try:
            logger.info(
                f"--------------start handler_export_task item--------------{item.filter_json}"
            )
            # 解析过滤器
            temp_filter = parse_export_filters(item.filter_json)

            # 获取查询集和状态过滤器
            queryset, status_filter = get_export_queryset(item.export_type, temp_filter)

            # 解析表头
            header_data = json.loads(item.header_json)
            rows_title = [x["label"] for x in header_data]
            rows_prop = [x["prop"] for x in header_data]

            # 创建Excel文件
            wb_out = Workbook()
            ws = wb_out.active
            ws.append(rows_title)

            if queryset and queryset.count() > 0:
                # 只有在有数据时才进行批量处理
                row_count = 0
                for prop_val in batch_process_export_data(
                    queryset, rows_prop, status_filter
                ):
                    ws.append(prop_val)
                    row_count += 1

                    # 每1000行记录一次进度
                    if row_count % 1000 == 0:
                        logger.info(f"Processed {row_count} rows for export item {item.id}")
            else:
                logger.info(f"No data found for export item {item.id}, creating empty file with headers")

            # 保存文件
            fn = datetime.now().strftime("%Y%m%d%H%M%S") + "_%d_" % random.randint(
                10000, 99999
            )
            url = (
                settings.STATIC_MEDIA_DIR
                + "exportTable/"
                + (datetime.now().strftime("%Y/%m/%d"))
            )
            if not os.path.exists(url):
                os.makedirs(url)
            file_name = "导出_" + fn + ".xlsx"
            wb_out.save(url + "/" + file_name)

            # 更新导出状态
            item.export_status = 2
            item.file_name = file_name
            item.url = (
                "static/alita/media/exportTable/"
                + (datetime.now().strftime("%Y/%m/%d"))
                + "/"
                + file_name
            )
            item.save()

            logger.info(f"Successfully exported {row_count} rows for item {item.id}")

        except Exception as e:
            logger.error(f"Error processing export item {item.id}: {str(e)}")
            # 可以选择更新状态为失败
            item.export_status = 3  # 假设3表示失败状态
            item.remark = str(e)
            item.save()
            continue

    logger.info("------------------end handler_export_task-------------------")


# 发票核销后收款明细记账余额核销
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_invoice_detail(self):
    logger.info('--------------start handler_invoice_detail--------------')
    invoices = Invoice.objects.filter(del_flag=False, new_verification=True)
    for item in invoices:
        account_receivable = AccountReceivable.objects.filter(debit_num__invoice=item.id, del_flag=False).order_by(
            'account_time')
        # 获取发票核销的金额
        if item.pay_balance == 0:
            # 全部核销完成
            account_receivable.update(account_balance=0)
        else:
            # 获取发票已经核销的余额
            check_balance = item.pay_amount - item.pay_balance
            # 获取发票下所有收款明细的
            receivable_balance = account_receivable.aggregate(total=Sum('account_balance'))['total'] or 0
            flag = check_balance + receivable_balance - item.pay_amount
            operate_balance = abs(flag)
            if flag > 0:
                # 核销明细
                for detail in account_receivable:
                    if detail.account_balance != 0 and operate_balance != 0:
                        if operate_balance > detail.account_balance:
                            operate_balance -= detail.account_balance
                            detail.account_balance = 0
                        else:
                            detail.account_balance -= operate_balance
                            operate_balance = 0
                        detail.save()
            elif flag < 0:
                # 明细余额加回去
                for detail in account_receivable.order_by('-account_time'):
                    if detail.account_balance != detail.account_amount and operate_balance != 0:
                        # 获取要加回去多少钱
                        add_balance = detail.account_amount - detail.account_balance
                        if operate_balance > add_balance:
                            operate_balance -= add_balance
                            detail.account_balance += add_balance
                        else:
                            detail.account_balance += operate_balance
                            operate_balance = 0
                        detail.save()

        # 把是否有新核销设置为False
        Invoice.objects.filter(id=item.id).update(new_verification=False)

    logger.info('------------------end handler_invoice_detail-------------------')


# 付款单核销后付款明细记账余额核销
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_payment_detail(self):
    logger.info('--------------start handler_payment_detail--------------')
    payments = Payment.objects.filter(del_flag=False, new_verification=True)
    for item in payments:
        account_payable = AccountPayable.objects.filter(payment_num=item.id, del_flag=False).order_by('account_time')
        # 获取付款单核销的金额
        if item.pay_balance == 0:
            # 全部核销完成
            account_payable.update(account_balance=0)
        else:
            # 获取付款单已经核销的余额
            check_balance = item.pay_amount - item.pay_balance
            # 获取付款单下所有付款明细的记账余额
            receivable_balance = account_payable.aggregate(total=Sum('account_balance'))['total'] or 0
            flag = check_balance + receivable_balance - item.pay_amount
            operate_balance = abs(flag)
            if flag > 0:
                # 核销明细
                for detail in account_payable:
                    if detail.account_balance != 0 and operate_balance != 0:
                        if operate_balance > detail.account_balance:
                            operate_balance -= detail.account_balance
                            detail.account_balance = 0
                        else:
                            detail.account_balance -= operate_balance
                            operate_balance = 0
                        detail.save()
            elif flag < 0:
                # 明细余额加回去
                for detail in account_payable.order_by('-account_time'):
                    if detail.account_balance != detail.account_amount and operate_balance != 0:
                        # 获取要加回去多少钱
                        add_balance = detail.account_amount - detail.account_balance
                        if operate_balance > add_balance:
                            operate_balance -= add_balance
                            detail.account_balance += add_balance
                        else:
                            detail.account_balance += operate_balance
                            operate_balance = 0
                        detail.save()

        # 把是否有新核销设置为False
        Payment.objects.filter(id=item.id).update(new_verification=False)

    logger.info('------------------end handler_payment_detail-------------------')


# 收入支出每月汇总--------------------应付汇总表&&应收汇总表
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_summary_report(self):
    logger.info('--------------start handler_summary_report--------------')
    # 获取所有的收款明细不同客户不同币种不同月份的金额和余额的汇总
    r_query = AccountReceivable.objects.filter(del_flag=False)
    # 已经清空了当月数据的月份
    receive_delete_month = []
    pay_delete_month = []
    # 判断系统为单币种还是多币种
    if settings.MULTI_CURRENCY:
        receive_summary = r_query.values_list('customer', 'origin_currency').annotate(
            total=Sum('account_amount'), balance=Sum('account_balance'), account_month=TruncMonth('account_time'))
    else:
        receive_summary = r_query.values_list('customer').annotate(
            total=Sum('account_amount'), balance=Sum('account_balance'), account_month=TruncMonth('account_time'))
    for receive in receive_summary:
        # 获取原总金额和调整总金额
        if settings.MULTI_CURRENCY:
            params1 = {'origin_currency': receive[1]}
            params2 = {'currency': receive[1]}
            currency = receive[1]
            total = receive[2]
            balance = receive[3]
            month = receive[4]
        else:
            params1 = {}
            params2 = {'currency': settings.CURRENT_CURRENCY}
            currency = settings.CURRENT_CURRENCY
            total = receive[1]
            balance = receive[2]
            month = receive[3]
        is_lock = is_lock_time(month)
        if not is_lock:
            currency_month = str(month).split(' ')[0]
            if currency_month not in receive_delete_month:
                # 清除当月当前客户当前币种记录重新生成
                ReceivableSummary.objects.filter(account_date=str(month).split(' ')[0], del_flag=False).update(
                    del_flag=True)
                receive_delete_month.append(currency_month)
            adjust_amount = \
                r_query.filter(customer=receive[0], account_time__year=month.year,
                               account_time__month=month.month, is_adjust=True, **params1).aggregate(
                    total=Sum('account_amount'))['total'] or None
            origin_amount = \
                r_query.filter(customer=receive[0], account_time__year=month.year,
                               account_time__month=month.month, is_adjust=False, **params1).aggregate(
                    total=Sum('account_amount'))['total'] or None

            try:
                current_summary = ReceivableSummary.objects.get(customer=Company.objects.get(id=receive[0]),
                                                                del_flag=False,
                                                                account_date=str(month).split(' ')[0],
                                                                **params2)
                current_summary.adjust_amount = adjust_amount
                current_summary.origin_amount = origin_amount
                current_summary.amount = Decimal(total)
                current_summary.balance = Decimal(balance)
                current_summary.is_locked = balance == 0
            except ReceivableSummary.DoesNotExist:
                current_summary = ReceivableSummary.objects.create(customer=Company.objects.get(id=receive[0]),
                                                                   amount=total,
                                                                   balance=balance,
                                                                   adjust_amount=adjust_amount,
                                                                   origin_amount=origin_amount,
                                                                   account_date=str(month).split(' ')[0],
                                                                   is_locked=balance == 0,
                                                                   **params2)
            business_amount = \
                Receipt.objects.filter(account_date__month=month.month, account_date__year=month.year,
                                       company=Company.objects.get(id=receive[0]), currency=currency).aggregate(
                    total=Sum('amount'))['total'] or 0
            current_summary.business_amount = business_amount
            current_summary.difference = Decimal(total) - business_amount
            current_summary.save()

    # 获取所有的付款明细不同客户不同币种不同月份的金额和余额的汇总
    p_queryset = AccountPayable.objects.filter(del_flag=False)
    if settings.MULTI_CURRENCY:
        pay_summary = p_queryset.values_list('supplier', 'origin_currency').annotate(total=Sum('account_amount'),
                                                                                     balance=Sum('account_balance'),
                                                                                     account_month=TruncMonth(
                                                                                         'account_time'))
    else:
        pay_summary = p_queryset.values_list('supplier').annotate(total=Sum('account_amount'),
                                                                  balance=Sum('account_balance'),
                                                                  account_month=TruncMonth(
                                                                      'account_time'))

    for pay in pay_summary:
        if settings.MULTI_CURRENCY:
            params1 = {'origin_currency': pay[1]}
            params2 = {'currency': pay[1]}
            currency = pay[1]
            total = pay[2]
            balance = pay[3]
            month = pay[4]
        else:
            params1 = {}
            params2 = {'currency': settings.CURRENT_CURRENCY}
            currency = settings.CURRENT_CURRENCY
            total = pay[1]
            balance = pay[2]
            month = pay[3]
        is_lock = is_lock_time(month)
        if not is_lock:
            currency_month = str(month).split(' ')[0]
            if currency_month not in pay_delete_month:
                # 清除当月当前客户当前币种记录重新生成
                PayableSummary.objects.filter(account_date=str(month).split(' ')[0], del_flag=False).update(
                    del_flag=True)
                pay_delete_month.append(currency_month)
            adjust_amount = p_queryset.filter(supplier=pay[0], account_time__year=month.year,
                                              account_time__month=month.month, is_adjust=True, **params1).aggregate(
                total=Sum('account_amount'))['total'] or None
            origin_amount = p_queryset.filter(supplier=pay[0], account_time__year=month.year,
                                              account_time__month=month.month, is_adjust=False, **params1).aggregate(
                total=Sum('account_amount'))['total'] or None
            try:
                current_summary = PayableSummary.objects.get(supplier=Company.objects.get(id=pay[0]),
                                                             del_flag=False,
                                                             account_date=str(month).split(' ')[0],
                                                             **params2)
                current_summary.adjust_amount = adjust_amount
                current_summary.origin_amount = origin_amount
                current_summary.amount = Decimal(total)
                current_summary.balance = Decimal(balance)
                current_summary.is_locked = balance == 0
            except PayableSummary.DoesNotExist:
                current_summary = PayableSummary.objects.create(supplier=Company.objects.get(id=pay[0]),
                                                                amount=total,
                                                                balance=balance,
                                                                adjust_amount=adjust_amount,
                                                                origin_amount=origin_amount,
                                                                account_date=str(month).split(' ')[0],
                                                                is_locked=pay[3] == 0,
                                                                **params2)

            business_amount = \
                Billing.objects.filter(account_date__month=month.month, account_date__year=month.year,
                                       company=Company.objects.get(id=pay[0]), currency=currency).aggregate(
                    total=Sum('amount'))['total'] or 0
            current_summary.business_amount = business_amount
            current_summary.difference = Decimal(total) - business_amount
            current_summary.save()

    logger.info('------------------end handler_summary_report-------------------')


# 收入支出每日汇总--------------------应付日汇总表&&应收日汇总表
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_summary_day_report(self):
    logger.info('--------------start handler_summary_day_report--------------')
    # 获取所有的收款明细不同客户不同币种不同月份的金额和余额的汇总
    yesterday = (date.today() + timedelta(days=-1)).strftime("%Y-%m-%d")
    if settings.MULTI_CURRENCY:
        receive_summary = AccountReceivable.objects.filter(del_flag=False).values_list('customer',
                                                                                       'origin_currency').annotate(
            total=Sum('account_amount'), balance=Sum('account_balance'))
    else:
        receive_summary = AccountReceivable.objects.filter(del_flag=False).values_list('customer').annotate(
            total=Sum('account_amount'), balance=Sum('account_balance'))

    for receive in receive_summary:
        if settings.MULTI_CURRENCY:
            currency = receive[1]
            amount = receive[2]
            balance = receive[3]
        else:
            currency = settings.CURRENT_CURRENCY
            amount = receive[1]
            balance = receive[2]
        ReceivableDaySummary.objects.create(customer=Company.objects.get(id=receive[0]), currency=currency,
                                            amount=amount,
                                            balance=balance,
                                            is_clear=balance == 0,
                                            account_date=yesterday)

    # 获取所有的付款明细不同客户不同币种不同月份的金额和余额的汇总
    if settings.MULTI_CURRENCY:
        pay_summary = AccountPayable.objects.filter(del_flag=False).values_list('supplier', 'origin_currency').annotate(
            total=Sum('account_amount'), balance=Sum('account_balance'))
    else:
        pay_summary = AccountPayable.objects.filter(del_flag=False).values_list('supplier').annotate(
            total=Sum('account_amount'), balance=Sum('account_balance'))

    for pay in pay_summary:
        if settings.MULTI_CURRENCY:
            currency = pay[1]
            amount = pay[2]
            balance = pay[3]
        else:
            currency = settings.CURRENT_CURRENCY
            amount = pay[1]
            balance = pay[2]
        PayableDaySummary.objects.create(supplier=Company.objects.get(id=pay[0]), currency=currency,
                                         amount=amount,
                                         balance=balance,
                                         is_clear=balance == 0,
                                         account_date=yesterday)

    logger.info('------------------end handler_summary_day_report-------------------')


# 获取小包订单每个客户每天每个币种每个产品的利润
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_parcel_profit_report(self):
    logger.info('--------------start handler_parcel_profit_report--------------')

    charge_ins_queryset = ParcelOrderChargeIn.objects.filter(del_flag=False, customer_order_num__del_flag=False,
                                                             customer_order_num__order_status__in=['FC'],
                                                             customer_order_num__customer__isnull=False,
                                                             customer_order_num__account_time__isnull=False,
                                                             customer_order_num__product__isnull=False)
    # 币种区别
    if settings.MULTI_CURRENCY:
        charge_ins = charge_ins_queryset.values_list(
            'customer_order_num__customer', 'customer_order_num__product', 'currency_type').annotate(
            total_income=Sum('charge_total'), account_month=TruncDay('customer_order_num__account_time'))
        month_index = 4
        total_index = 3
        currency = 2
    else:
        charge_ins = charge_ins_queryset.values_list(
            'customer_order_num__customer', 'customer_order_num__product').annotate(
            total_income=Sum('account_charge'), account_month=TruncDay('customer_order_num__account_time'))
        month_index = 3
        total_index = 2
        currency = settings.CURRENT_CURRENCY

    for item in charge_ins:
        counts = ParcelCustomerOrder.objects.filter(
            order_time__range=[str(item[month_index]).split(' ')[0] + ' 00:00:00',
                               str(item[month_index]).split(' ')[0] + ' 23:59:59'],
            customer=item[0], del_flag=False, product=item[1]).count()

        try:
            current = ProfitAnalysis.objects.get(account_date=str(item[month_index]).split(' ')[0], customer=item[0],
                                                 product_type='PC', del_flag=False, product=item[1],
                                                 currency=item[2] if settings.MULTI_CURRENCY else currency)

            current.profit = (item[total_index] or 0) - (current.pay_amount or 0)
            current.receive_amount = item[total_index]
            current.order_qty = counts
            current.save()
        except ProfitAnalysis.DoesNotExist:
            ProfitAnalysis.objects.create(account_date=str(item[month_index]).split(' ')[0], product_type='PC',
                                          customer=Company.objects.get(id=item[0]), profit=item[total_index],
                                          currency=item[2] if settings.MULTI_CURRENCY else currency, order_qty=counts,
                                          product=Product.objects.get(id=item[1]), receive_amount=item[total_index])

    charge_outs_queryset = ParcelOrderChargeOut.objects.filter(del_flag=False, customer_order_num__del_flag=False,
                                                               customer_order_num__order_status__in=['FC'],
                                                               customer_order_num__account_time__isnull=False,
                                                               customer_order_num__customer__isnull=False,
                                                               customer_order_num__product__isnull=False)

    # 币种区别
    if settings.MULTI_CURRENCY:
        charge_outs = charge_outs_queryset.values_list(
            'customer_order_num__customer', 'customer_order_num__product', 'currency_type').annotate(
            total_income=Sum('charge_total'), account_month=TruncDay('customer_order_num__account_time'))
    else:
        charge_outs = charge_outs_queryset.values_list(
            'customer_order_num__customer', 'customer_order_num__product').annotate(
            total_income=Sum('account_charge'), account_month=TruncDay('customer_order_num__account_time'))

    for item in charge_outs:
        counts = ParcelCustomerOrder.objects.filter(
            order_time__range=[str(item[month_index]).split(' ')[0] + ' 00:00:00',
                               str(item[month_index]).split(' ')[0] + ' 23:59:59'],
            customer=item[0], del_flag=False, product=item[1]).count()

        try:
            current = ProfitAnalysis.objects.get(account_date=str(item[month_index]).split(' ')[0], product=item[1],
                                                 customer=item[0], product_type='PC', del_flag=False,
                                                 currency=item[2] if settings.MULTI_CURRENCY else currency)
            current.profit = (current.receive_amount or 0) - (item[total_index] or 0)
            current.pay_amount = item[total_index]
            current.order_qty = counts
            current.save()
        except ProfitAnalysis.DoesNotExist:
            ProfitAnalysis.objects.create(account_date=str(item[month_index]).split(' ')[0], order_qty=counts,
                                          pay_amount=item[total_index], profit=-(item[total_index] or 0),
                                          customer=Company.objects.get(id=item[0]),
                                          currency=item[2] if settings.MULTI_CURRENCY else currency,
                                          product_type='PC', product=Product.objects.get(id=item[1]))

    logger.info('------------------end handler_parcel_profit_report-------------------')


# 获取运输订单每个客户每天每个币种每个产品的利润
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_customer_profit_report(self):
    logger.info('--------------start handler_customer_profit_report--------------')
    charge_ins_queryset = CustomerOrderChargeIn.objects.filter(customer_order_num__del_flag=False, del_flag=False,
                                                               customer_order_num__order_status__in=['FC'],
                                                               customer_order_num__account_time__isnull=False,
                                                               customer_order_num__customer__isnull=False,
                                                               customer_order_num__product__isnull=False)

    # 币种区别
    if settings.MULTI_CURRENCY:
        charge_ins = charge_ins_queryset.values_list(
            'customer_order_num__customer', 'customer_order_num__product', 'currency_type').annotate(
            total_income=Sum('charge_total'), account_month=TruncDay('customer_order_num__account_time'))
        month_index = 4
        total_index = 3
        currency = 2
    else:
        charge_ins = charge_ins_queryset.values_list(
            'customer_order_num__customer', 'customer_order_num__product').annotate(
            total_income=Sum('account_charge'), account_month=TruncDay('customer_order_num__account_time'))
        month_index = 3
        total_index = 2
        currency = settings.CURRENT_CURRENCY

    for item in charge_ins:
        counts = CustomerOrder.objects.filter(del_flag=False, arrival_date=str(item[month_index]).split(' ')[0],
                                              customer=item[0], product=item[1]).count()

        try:
            current = ProfitAnalysis.objects.get(account_date=str(item[month_index]).split(' ')[0], customer=item[0],
                                                 product_type='TR', del_flag=False, product=item[1],
                                                 currency=item[2] if settings.MULTI_CURRENCY else currency)

            current.profit = (item[total_index] or 0) - (current.pay_amount or 0)
            current.receive_amount = item[total_index]
            current.order_qty = counts
            current.save()
        except ProfitAnalysis.DoesNotExist:
            ProfitAnalysis.objects.create(account_date=str(item[month_index]).split(' ')[0], profit=item[total_index],
                                          currency=item[2] if settings.MULTI_CURRENCY else currency, order_qty=counts,
                                          receive_amount=item[total_index], product_type='TR',
                                          product=Product.objects.get(id=item[1]),
                                          customer=Company.objects.get(id=item[0]))

    charge_outs_queryset = CustomerOrderChargeOut.objects.filter(customer_order_num__del_flag=False, del_flag=False,
                                                                 customer_order_num__order_status__in=['FC'],
                                                                 customer_order_num__account_time__isnull=False,
                                                                 customer_order_num__customer__isnull=False,
                                                                 customer_order_num__product__isnull=False)

    # 币种区别
    if settings.MULTI_CURRENCY:
        charge_outs = charge_outs_queryset.values_list(
            'customer_order_num__customer', 'customer_order_num__product', 'currency_type').annotate(
            total_income=Sum('charge_total'), account_month=TruncDay('customer_order_num__account_time'))
    else:
        charge_outs = charge_outs_queryset.values_list(
            'customer_order_num__customer', 'customer_order_num__product').annotate(
            total_income=Sum('account_charge'), account_month=TruncDay('customer_order_num__account_time'))

    for item in charge_outs:
        counts = CustomerOrder.objects.filter(del_flag=False, arrival_date=str(item[month_index]).split(' ')[0],
                                              customer=item[0],
                                              product=item[1]).count()
        try:
            current = ProfitAnalysis.objects.get(account_date=str(item[month_index]).split(' ')[0], customer=item[0],
                                                 product_type='TR', del_flag=False, product=item[1],
                                                 currency=item[2] if settings.MULTI_CURRENCY else currency)

            current.profit = (current.receive_amount or 0) - (item[total_index] or 0)
            current.pay_amount = item[total_index]
            current.order_qty = counts
            current.save()
        except ProfitAnalysis.DoesNotExist:
            ProfitAnalysis.objects.create(account_date=str(item[month_index]).split(' ')[0], order_qty=counts,
                                          profit=-(item[total_index] or 0), customer=Company.objects.get(id=item[0]),
                                          currency=item[2] if settings.MULTI_CURRENCY else currency, product_type='TR',
                                          product=Product.objects.get(id=item[1]), pay_amount=item[total_index])

    logger.info('------------------end handler_customer_profit_report-------------------')


# 获取进口报关订单每个客户每天每个币种每个产品的利润
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_clearance_profit_report(self):
    logger.info('--------------start handler_clearance_profit_report--------------')
    charge_ins_queryset = ClearanceChargeIn.objects.filter(customer_order_num__del_flag=False, del_flag=False,
                                                           customer_order_num__clear_status__in=['FC'],
                                                           customer_order_num__customer__isnull=False,
                                                           customer_order_num__account_time__isnull=False,
                                                           customer_order_num__product__isnull=False)

    if not charge_ins_queryset.exists():
        return

    # 币种区别
    if settings.MULTI_CURRENCY:
        charge_ins = charge_ins_queryset.values_list(
            'clearance_id__customer', 'clearance_id__product', 'currency_type').annotate(
            total_income=Sum('charge_total'), account_month=TruncDay('clearance_id__account_time'))
        month_index = 4
        total_index = 3
        currency = 2
    else:
        charge_ins = charge_ins_queryset.values_list('clearance_id__customer', 'clearance_id__product').annotate(
            total_income=Sum('account_charge'), account_month=TruncDay('clearance_id__account_time'))
        month_index = 3
        total_index = 2
        currency = settings.CURRENT_CURRENCY

    for item in charge_ins:
        counts = Clearance.objects.filter(del_flag=False, account_time=str(item[month_index]).split(' ')[0],
                                          customer=item[0], product=item[1]).count()
        try:
            current = ProfitAnalysis.objects.get(account_date=str(item[month_index]).split(' ')[0], customer=item[0],
                                                 product_type='CL', del_flag=False, product=item[1],
                                                 currency=item[2] if settings.MULTI_CURRENCY else currency)

            current.profit = (item[total_index] or 0) - (current.pay_amount or 0)
            current.receive_amount = item[total_index]
            current.order_qty = counts
            current.save()
        except ProfitAnalysis.DoesNotExist:
            ProfitAnalysis.objects.create(account_date=str(item[month_index]).split(' ')[0], product_type='CL',
                                          receive_amount=item[total_index], profit=item[total_index],
                                          customer=Company.objects.get(id=item[0]), order_qty=counts,
                                          currency=item[2] if settings.MULTI_CURRENCY else currency,
                                          product=Product.objects.get(id=item[1]))

    charge_outs_queryset = ClearanceChargeOut.objects.filter(customer_order_num__del_flag=False, del_flag=False,
                                                             customer_order_num__clear_status__in=['FC'],
                                                             customer_order_num__customer__isnull=False,
                                                             customer_order_num__account_time__isnull=False,
                                                             customer_order_num__product__isnull=False)
    # 币种区别
    if settings.MULTI_CURRENCY:
        charge_outs = charge_outs_queryset.values_list(
            'clearance_id__customer', 'clearance_id__product', 'currency_type').annotate(
            total_income=Sum('charge_total'), account_month=TruncDay('clearance_id__account_time'))
        month_index = 4
        total_index = 3
        currency = 2
    else:
        charge_outs = charge_outs_queryset.values_list(
            'clearance_id__customer', 'clearance_id__product').annotate(
            total_income=Sum('charge_total'), account_month=TruncDay('clearance_id__account_time'))
        month_index = 3
        total_index = 2
        currency = settings.CURRENT_CURRENCY

    for item in charge_outs:
        counts = Clearance.objects.filter(del_flag=False, account_time=str(item[month_index]).split(' ')[0],
                                          customer=item[0], product=item[1]).count()

        try:
            current = ProfitAnalysis.objects.get(account_date=str(item[month_index]).split(' ')[0], customer=item[0],
                                                 product_type='CL', del_flag=False, product=item[1],
                                                 currency=item[2] if settings.MULTI_CURRENCY else currency)
            current.profit = (current.receive_amount or 0) - (item[total_index] or 0)
            current.pay_amount = item[total_index]
            current.order_qty = counts
            current.save()
        except ProfitAnalysis.DoesNotExist:
            ProfitAnalysis.objects.create(account_date=str(item[month_index]).split(' ')[0], product_type='CL',
                                          pay_amount=item[total_index], profit=-(item[total_index] or 0),
                                          customer=Company.objects.get(id=item[0]), order_qty=counts,
                                          currency=item[2] if settings.MULTI_CURRENCY else currency,
                                          product=Product.objects.get(id=item[1]))

    logger.info('------------------end handler_clearance_profit_report-------------------')


# 获取调整单生成的付款明细和收款明细的每日汇总利润
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_adjust_profit_report(self):
    logger.info('--------------start handler_adjust_profit_report--------------')
    try:
        customer = Company.objects.get(name=settings.ADJUST_COMPANY_NAME)
    except Company.DoesNotExist:
        customer = None
    charge_ins = AccountReceivable.objects.filter(del_flag=False, is_adjust=True).values_list(
        'origin_currency').annotate(total_income=Sum('account_amount'), account_month=TruncDay('account_time'))

    try:
        product = Product.objects.get(code=settings.ADJUST_PRODUCT_CODE)
    except Product.DoesNotExist:
        product = None
    for item in charge_ins:
        try:
            current = ProfitAnalysis.objects.get(account_date=str(item[2]).split(' ')[0],
                                                 customer=customer,
                                                 product=product,
                                                 product_type='AD',
                                                 del_flag=False,
                                                 currency=item[0])

            current.profit = (item[1] or 0) - (current.pay_amount or 0)
            current.receive_amount = item[1]
            current.save()
        except ProfitAnalysis.DoesNotExist:
            ProfitAnalysis.objects.create(account_date=str(item[2]).split(' ')[0], product=product, customer=customer,
                                          currency=item[0],
                                          receive_amount=item[1], profit=item[1], product_type='AD')

    charge_outs = AccountPayable.objects.filter(del_flag=False, is_adjust=True).values_list('origin_currency').annotate(
        total_income=Sum('account_amount'), account_month=TruncDay('account_time'))

    for item in charge_outs:
        try:
            current = ProfitAnalysis.objects.get(account_date=str(item[2]).split(' ')[0],
                                                 customer=customer,
                                                 product_type='AD',
                                                 product=product,
                                                 del_flag=False,
                                                 currency=item[0])
            current.profit = (current.receive_amount or 0) - (item[1] or 0)
            current.pay_amount = item[1]
            current.save()
        except ProfitAnalysis.DoesNotExist:
            ProfitAnalysis.objects.create(account_date=str(item[2]).split(' ')[0], product=product, customer=customer,
                                          currency=item[0], pay_amount=item[1], profit=-(item[1] or 0),
                                          product_type='AD', )

    logger.info('------------------end handler_adjust_profit_report-------------------')


# 生成公共账单明细
def handler_generate_debit_detail_file_common(invoice):
    logger.info('--------------start handler_generate_debit_detail_file_common--------------')
    # # 获取当前发票
    # invoice = Invoice.objects.filter(is_debit_detail=False, del_flag=False).order_by('-id').first()
    if invoice:
        logger.info(f'handler_generate_debit_detail_file--Invoice number-->: {invoice.invoice_num}')
        # 获取当前发票下的账单
        debits_details = AccountReceivable.objects.filter(debit_num__invoice=invoice, debit_num__del_flag=False,
                                                          del_flag=False)

        logger.info('handler_generate_debit_detail_file debit count is' + str(debits_details.count()))

        if settings.DOMAIN_URL == 'manage.zhengfx.com':
            file_prefix = 'zfx_'
        else:
            file_prefix = ''

        bill_file = settings.DEBIT_DIR_ALL + file_prefix + 'bills_details.xlsx'
        file_name = 'Bill-Details-' + invoice.invoice_num + '-' + datetime.now().strftime(
            "%Y%m%d%H%M%S") + '.xlsx'
        new_filename = settings.STATIC_MEDIA_DIR + 'bill/' + file_name

        try:
            copyfile(bill_file, new_filename)
        except Exception as e:
            save_invoice_err(invoice.id, f'未有账单模版-->{e}')
            logger.error(traceback.format_exc())
            logger.error(f'未有账单模版-->{e}')
            return

        wb = openpyxl.load_workbook(new_filename)
        ws = wb.worksheets[0]
        style = xlwt.XFStyle()
        lineal = xlwt.Alignment()
        lineal.horz = 0x02
        lineal.vert = 0x01
        style.alignment = lineal
        if settings.DOMAIN_URL == 'manage.zhengfx.com':
            # 设置账单日期为开票的时间
            ws.cell(13, 6, invoice.create_date.strftime("%Y-%m-%d"))
            # 设置客户名称
            ws.cell(3, 2, invoice.customer.name)
            # 设置发票号
            ws.cell(3, 6, invoice.invoice_num)
            # 设置账期区间
            ws.cell(13, 2, invoice.debit_time_range)
            # 设置付款汇率
            ws.cell(10, 2, invoice.pay_rate)
            # 设置付款金额
            ws.cell(10, 6, invoice.pay_amount)
            # 设置付款币种
            ws.cell(10, 7, invoice.pay_currency)
            # 设置合计
            ws.cell(7, 6, invoice.amount)
            # 设置币种
            ws.cell(7, 7, invoice.currency)
        else:
            # 设置公司中文名,以模版设置为准
            # ws.cell(1, 1, settings.COMPANY_NAME)
            # 设置账单日期为开票的时间
            ws.cell(2, 8, invoice.create_date.strftime("%Y-%m-%d"))
            # 设置客户名称
            ws.cell(4, 2, invoice.customer.name)
            # 设置发票号
            ws.cell(4, 7, invoice.invoice_num)
            # 设置账期区间
            ws.cell(8, 2, invoice.debit_time_range)
            # 设置合计
            ws.cell(8, 7, invoice.amount)
            # 设置币种
            ws.cell(8, 9, invoice.currency)

        wss = wb.worksheets[1]
        # 第二张表写账单明细
        szx_start_row = 7 if settings.DOMAIN_URL != 'manage.zhengfx.com' else 0
        start_row = 2 + szx_start_row
        start_col = 1
        # 循环添加账单
        logger.info('start write detail to excel！')
        for item in debits_details:
            # 写入客户订单号
            logger.info(f'handler_generate_debit_detail_file {start_row} {start_col + 1} {item.customer_orderNum}')
            if not item.customer_orderNum:
                try:
                    ls = ReconciliationDetails.objects.filter(track_num=item.track_num, del_flag=False).first()
                    item.customer_orderNum = ls.customer_orderNum
                    logger.info(f'测试通过12{item.customer_orderNum}')
                except:
                    pass
            wss.cell(start_row, start_col + 1, str(item.customer_orderNum))
            # 获取客户订单
            customer_order = ''
            adjust_number = None
            if item.adjust_number and item.adjust_number.startswith('AI'):
                adjust_number = item.adjust_number if item.adjust_number.startswith('AI') else item.order_num
                customer_order = DebitAdjust.objects.get(debit_adjust_num=adjust_number, del_flag=False)
            elif is_parcel_customer_order(item.order_num):
                try:
                    customer_order = ParcelCustomerOrder.objects.get(order_num=item.order_num, del_flag=False)
                except ParcelCustomerOrder.DoesNotExist:
                    save_invoice_err(invoice.id, "ParcelCustomerOrder查无此订单 order_num =" + item.order_num)
                    logger.error("ParcelCustomerOrder查无此订单 order_num =" + item.order_num)
                    return
            elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CLEARANCE_ORDER_MARK):
                try:
                    customer_order = CustomsClearanceOrder.objects.get(order_num=item.order_num, del_flag=False)
                except CustomsClearanceOrder.DoesNotExist:
                    save_invoice_err(invoice.id, "CustomerOrder查无此订单 order_num =" + item.order_num)
                    logger.error("CustomerOrder查无此订单 order_num =" + item.order_num)
                    return
            elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK) \
                    or item.order_num.startswith('ZJ' + settings.CUSTOMER_ORDER_MARK):
                try:
                    customer_order = CustomerOrder.objects.get(order_num=item.order_num, del_flag=False)
                except CustomerOrder.DoesNotExist:
                    save_invoice_err(invoice.id, "CustomerOrder查无此订单 order_num =" + item.order_num)
                    logger.error("CustomerOrder查无此订单 order_num =" + item.order_num)
                    return
            elif item.order_num.startswith('CL'):
                customer_order = Clearance.objects.get(clearance_num=item.order_num, del_flag=False)
            elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_OUTBOUND_ORDER):
                customer_order = WarehouseOutboundOrder.objects.get(order_num=item.order_num, del_flag=False)
            elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_INBOUND_ORDER):
                customer_order = WarehouseInboundOrder.objects.get(order_num=item.order_num, del_flag=False)
            # 兼容增值服务单
            elif item.order_num.startswith('VAS'):
                logger.info(f'进来了1')
                customer_order = VasOrder.objects.get(vas_order_num=item.order_num, del_flag=False)
            # 兼容退货单
            elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + 'RETURN'):
                logger.info(f'进来了2')
                customer_order = ReturnOrder.objects.get(order_num=item.order_num, del_flag=False)

            company_list = ['manage.tangus.cn']
            try:
                if settings.DOMAIN_URL in company_list:
                    logger.info(f'进来了0{customer_order}, {item}')
                    start_row = write_detail_new(adjust_number, customer_order, item, start_col, start_row, wss)
                else:
                    start_row = write_detail(adjust_number, customer_order, item, start_col, start_row, wss)
            except Exception as e:
                logger.error(traceback.format_exc())
                save_invoice_err(invoice.id, f'写入详情时异常-->{e}')
                logger.error(f'写入详情时异常-->{e}')
                return

        wb.save(new_filename)
        invoice.is_debit_detail = True
        invoice.debit_detail_url = settings.DOMAIN_URL + '/media/bill/' + file_name
        invoice.save()
        save_invoice_err(invoice.id, "")
        logger.info('end write detail to excel！')

    logger.info('------------------end handler_generate_debit_detail_file_common-------------------')


# 生成清关账单
def handler_generate_customs_clearance_debit_detail(invoice, remark = False):

    logger.info('---start handler_generate_customs_clearance_debit_detail---')
    # 获取当前发票
    if invoice:
        # 获取当前发票下的账单
        debits_details = AccountReceivable.objects.filter(debit_num__invoice=invoice, debit_num__del_flag=False,
                                                          del_flag=False)

        logger.info('handler_generate_customs_clearance_debit_detail debit count is' + str(debits_details.count()))

        file_prefix = ''
        if remark:
            bill_file = settings.DEBIT_DIR_ALL + file_prefix + 'clearance_vasorder_bills_details.xlsx'
        else:
            bill_file = settings.DEBIT_DIR_ALL + file_prefix + 'clearance_bills_details.xlsx'
        file_name = 'Clearance-Bill-Details-' + invoice.invoice_num + '-' + datetime.now().strftime(
            "%Y%m%d%H%M%S") + '.xlsx'
        new_filename = settings.STATIC_MEDIA_DIR + 'bill/' + file_name

        # bill_file = '/Users/<USER>/PycharmProjects/alita/media/clearance_bills_details.xlsx'
        # new_filename = f'/Users/<USER>/PycharmProjects/alita/media/{file_name}'

        try:
            copyfile(bill_file, new_filename)
        except Exception as e:
            logger.error(traceback.format_exc())
            save_invoice_err(invoice.id, f'未有账单模版-->{e}')
            logger.error(f'未有账单模版-->{e}')
            return

        wb = openpyxl.load_workbook(new_filename)
        ws = wb.worksheets[0]
        style = xlwt.XFStyle()
        lineal = xlwt.Alignment()
        lineal.horz = 0x02
        lineal.vert = 0x01
        style.alignment = lineal

        # 设置公司中文名
        # ws.cell(1, 1, settings.COMPANY_NAME)
        # 设置账单日期为开票的时间
        ws.cell(2, 8, invoice.create_date.strftime("%Y-%m-%d"))
        # 设置客户名称
        ws.cell(4, 2, invoice.customer.name)
        # 设置发票号
        ws.cell(4, 7, invoice.invoice_num)
        # 设置账期区间
        ws.cell(8, 2, invoice.debit_time_range)
        # 设置合计
        ws.cell(8, 7, invoice.amount)
        # 设置币种
        ws.cell(8, 9, invoice.currency)

        wss = wb.worksheets[1]
        # 第二张表写账单明细
        szx_start_row = 7
        start_row = 2 + szx_start_row
        start_col = 1
        # 循环添加账单
        for item in debits_details:
            # 写入客户订单号
            logger.info(f'handler_generate_customs_clearance_debit_detail {start_row} {start_col + 1} {item.customer_orderNum}')
            wss.cell(start_row, start_col + 1, str(item.customer_orderNum))
            # 获取客户订单
            customer_order = ''
            adjust_number = None
            if item.adjust_number and item.adjust_number.startswith('AI'):
                adjust_number = item.adjust_number if item.adjust_number.startswith('AI') else item.order_num
                customer_order = DebitAdjust.objects.get(debit_adjust_num=adjust_number, del_flag=False)
            elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CLEARANCE_ORDER_MARK):
                try:
                    customer_order = CustomsClearanceOrder.objects.get(order_num=item.order_num, del_flag=False)
                except CustomsClearanceOrder.DoesNotExist:
                    save_invoice_err(invoice.id, "CustomerOrder查无此订单 order_num =" + item.order_num)
                    logger.error("CustomerOrder查无此订单 order_num =" + item.order_num)
                    return
            # 兼容增值服务单
            elif item.order_num.startswith('VAS'):
                customer_order = VasOrder.objects.get(vas_order_num=item.order_num, del_flag=False)
            else:
                logger.error(f"非清关订单{item.order_num}")

            company_list = ['manage.tangus.cn']
            try:
                if settings.DOMAIN_URL in company_list:
                    start_row = write_detail_new(adjust_number, customer_order, item, start_col, start_row, wss, remark)
                else:
                    start_row = write_detail(adjust_number, customer_order, item, start_col, start_row, wss)
            except Exception as e:
                logger.error(traceback.format_exc())
                save_invoice_err(invoice.id, f'写入详情时异常-->{e}')
                logger.error(f'写入详情时异常-->{e}')
                return

        wb.save(new_filename)
        invoice.is_debit_detail = True
        invoice.debit_detail_url = settings.DOMAIN_URL + '/media/bill/' + file_name
        invoice.save()
        save_invoice_err(invoice.id, "")

    logger.info('------------------end handler_generate_customs_clearance_debit_detail-------------------')


# 生成账单明细new(生成账单汇总明细)
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_generate_debit_detail_file(self, remark=None, invoice_id=None):
    logger.info(f'--------------start handler_generate_debit_detail_file new--------------')
    # 获取当前发票
    if invoice_id:
        invoices = Invoice.objects.filter(id=invoice_id, del_flag=False)
    else:
        invoices = Invoice.objects.filter(is_debit_detail=False, del_flag=False).order_by('-id')[:10]
    for invoice in invoices:
        if invoice is not None:
            if settings.SYSTEM_ORDER_MARK in ['MZ', 'YQF']:
                print('invoice.product.type-->', invoice.product, invoice.product and invoice.product.type)
                if invoice.product:
                    if invoice.product.type == 'TR' and settings.SYSTEM_ORDER_MARK == 'MZ':
                        create_transport_order_debit_detail(invoice)
                    elif invoice.product.type == 'TR' and settings.SYSTEM_ORDER_MARK in ['YQF', 'FX']:
                        # create_transport_order_debit_detail(invoice)
                        create_debit_detail_yqf(invoice)
                    elif invoice.product.type == 'PC':
                        create_parcel_order_debit_detail(invoice)
                    else:
                        handler_generate_debit_detail_file_common(invoice)
                else:
                    # logger.info(f"账单汇总{invoice.invoice_num}不属于运输产品或小包产品")
                    handler_generate_debit_detail_file_common(invoice)
            elif remark and settings.SYSTEM_MARK in ['TANGUS']:
                logger.info(f'是否到')
                handler_generate_customs_clearance_debit_detail(invoice, remark)
            elif invoice.product and invoice.product.type == 'CL':
                handler_generate_customs_clearance_debit_detail(invoice)
            elif settings.SYSTEM_ORDER_MARK in ['CLT', 'FX']:
                # 凯乐通账单生成
                # settle_bill_service = SettleBillService()
                settle_bill_service = CltBillService()
                settle_bill_service.generate_klt_settle_bill(invoice)
            else:
                handler_generate_debit_detail_file_common(invoice)
    logger.info('------------------end handler_generate_debit_detail_file new-------------------')


# 生成运输账单明细
def create_transport_order_debit_detail(invoice):
    logger.info('运输账单明细开始统计Invoice number: ' + invoice.invoice_num)
    # 获取当前发票下的账单
    debits_details = AccountReceivable.objects.filter(debit_num__invoice=invoice, debit_num__del_flag=False,
                                                      del_flag=False)
    logger.info(f'{invoice.invoice_num} End Get Debits Details， debit count is' + str(debits_details.count()))
    file_prefix = ''
    bill_file = settings.DEBIT_DIR_ALL + file_prefix + 'bills_details_transport.xlsx'
    file_name = 'Bill-Details-' + invoice.invoice_num + '-' + datetime.now().strftime(
        "%Y%m%d%H%M%S") + '.xlsx'
    new_filename = settings.STATIC_MEDIA_DIR + 'bill/' + file_name
    copyfile(bill_file, new_filename)
    wb = openpyxl.load_workbook(new_filename)
    ws = wb.worksheets[0]
    style = xlwt.XFStyle()
    lineal = xlwt.Alignment()
    lineal.horz = 0x02
    lineal.vert = 0x01
    style.alignment = lineal

    # # 在第一页写入账单汇总
    # font = Font(name='Calibri', bold=True)
    # ws.cell(11, 1, '费用名称').font = font
    # ws.cell(11, 2, '数量').font = font
    # ws.cell(11, 3, '合计').font = font

    # 明细sheet
    # wss = wb.worksheets[1]
    invoice_details_df = pd.DataFrame(columns=['费用名称', '数量', '合计'], dtype=object)
    # 铭志项目
    for item in debits_details:
        invoice_details_df.loc[len(invoice_details_df.index)] = get_invoice_detail(item)
    invoice_details_df = invoice_details_df.groupby('费用名称').agg({'数量': 'sum', '合计': 'sum'}).reset_index()
    print('invoice_details_df-->\n', invoice_details_df)
    # 写账单明细
    sql_df = write_detail_transport(invoice, wb.worksheets)

    # 第一个sheet汇总数据
    # 客户名称
    ws.cell(2, 3, invoice.customer.name)
    # 客户编码
    ws.cell(2, 6, invoice.customer.short_name)
    if sql_df is None or sql_df.empty:
        logger.error(f'{invoice.invoice_num} 没有查询到运费数据')
    else:
        # 总订单数
        ws.cell(3, 3, len(sql_df))
        # 总计费重
        ws.cell(3, 6, sql_df['收货计费重'].sum())
        # ws.cell(3, 6, sql_df[6].sum())
    freight_indices = invoice_details_df[invoice_details_df['费用名称'] == '运费'].index.tolist()
    freight = 0
    if freight_indices:
        index_value = freight_indices[0]
        try:
            freight = invoice_details_df.loc[index_value, '合计']
        except KeyError:
            logger.info(f'{invoice.invoice_num}账单汇总的费用项中没有运费！')
    pick_up_charge_indices = invoice_details_df[invoice_details_df['费用名称'] == '提货费'].index.tolist()
    pick_up_charge = 0
    if pick_up_charge_indices:
        index_value = pick_up_charge_indices[0]
        try:
            pick_up_charge = invoice_details_df.loc[index_value, '合计']
        except KeyError:
            logger.info(f'{invoice.invoice_num}账单汇总的费用项中没有提货费！')
    # 基础运费总额
    ws.cell(5, 4, freight)
    # 提货费
    ws.cell(6, 4, pick_up_charge)
    # 附加费总额: 总费用 - 运费 - 提货费
    additional = invoice.amount - freight - pick_up_charge
    ws.cell(7, 4, additional)
    # 总费用
    ws.cell(8, 4, invoice.amount)
    wb.save(new_filename)
    invoice.is_debit_detail = True
    invoice.debit_detail_url = settings.DOMAIN_URL + '/media/bill/' + file_name
    invoice.save()
    logger.info(f'transport {invoice.invoice_num} end write detail to excel！')


# 生成YQF账单明细
def create_debit_detail_yqf(invoice: Invoice):
    logger.info('yqf账单明细开始统计Invoice number: ' + invoice.invoice_num)
    # debits = Debit.objects.filter(invoice=invoice, del_flag=False)

    # 获取当前发票下的账单
    # debits_details = AccountReceivable.objects.filter(debit_num__invoice=invoice, debit_num__del_flag=False,
    #                                                   del_flag=False)
    # freight = debits_details.aggregate(total=Sum('account_amount'))['total'] or 0
    freight = Decimal(invoice.amount).quantize(Decimal('0.00'))

    file_prefix = ''
    bill_file = settings.DEBIT_DIR_ALL + file_prefix + 'bills_details_transport.xlsx'
    # file_name = 'Bill-Details-' + invoice.invoice_num + '-' + datetime.now().strftime(
    #     "%Y%m%d%H%M%S") + '.xlsx'
    attr_period_month = invoice.attr_period_month or datetime.now()
    file_name = f'{invoice.customer.name}-{attr_period_month.strftime("%Y%m%d")}-{freight}.xlsx'
    new_filename = settings.STATIC_MEDIA_DIR + 'bill/' + file_name
    copyfile(bill_file, new_filename)
    wb = openpyxl.load_workbook(new_filename)
    style = xlwt.XFStyle()
    lineal = xlwt.Alignment()
    lineal.horz = 0x02
    lineal.vert = 0x01
    style.alignment = lineal

    fba_debits_details_export = FbaDebitsDetailsExport(wb, invoice)

    # 写入账单头部
    fba_debits_details_export.yqf_write_headers()

    # 明细sheet
    # wss = wb.worksheets[1]
    # invoice_details_df = pd.DataFrame(columns=['费用名称', '数量', '合计'], dtype=object)
    # # 铭志项目
    # for item in debits_details:
    #     invoice_details_df.loc[len(invoice_details_df.index)] = get_invoice_detail(item)
    # invoice_details_df = invoice_details_df.groupby('费用名称').agg({'数量': 'sum', '合计': 'sum'}).reset_index()
    # print('invoice_details_df-->\n', invoice_details_df)

    # 写入账单明细
    # write_detail_transport_yqf(invoice, wb.worksheets)
    fba_debits_details_export.yqf_write_details(add_border=True)

    # 写入账单尾部
    fba_debits_details_export.yqf_write_tail()

    wb.save(new_filename)
    invoice.is_debit_detail = True
    invoice.debit_detail_url = settings.DOMAIN_URL + '/media/bill/' + file_name
    invoice.save()
    logger.info(f'transport {invoice.invoice_num} end write detail to excel！')


# 生成小包账单明细
def create_parcel_order_debit_detail(invoice):
    logger.info('小包账单明细开始统计Invoice number: ' + invoice.invoice_num)
    # 获取当前发票下的账单
    debits_details = AccountReceivable.objects.filter(debit_num__invoice=invoice, debit_num__del_flag=False,
                                                      del_flag=False)
    logger.info(f'{invoice.invoice_num} End Get Debits Details， debit count is ' + str(debits_details.count()))
    file_prefix = ''
    bill_file = settings.DEBIT_DIR_ALL + file_prefix + 'bills_details_packet.xlsx'
    file_name = 'Bill-Details-' + invoice.invoice_num + '-' + datetime.now().strftime(
        "%Y%m%d%H%M%S") + '.xlsx'
    new_filename = settings.STATIC_MEDIA_DIR + 'bill/' + file_name
    copyfile(bill_file, new_filename)
    wb = openpyxl.load_workbook(new_filename)
    ws = wb.worksheets[0]
    style = xlwt.XFStyle()
    lineal = xlwt.Alignment()
    lineal.horz = 0x02
    lineal.vert = 0x01
    style.alignment = lineal
    # 账单汇总sheet
    # 公司中文名
    # ws.cell(1, 1, settings.COMPANY_NAME)
    ws.cell(1, 1, '铭志国际物流有限公司')
    # 账单日期为开票的时间
    ws.cell(2, 8, invoice.create_date.strftime("%Y-%m-%d"))
    # 客户名称
    ws.cell(4, 2, invoice.customer.name)
    # 发票号
    ws.cell(4, 7, invoice.invoice_num)
    # 账期区间
    ws.cell(8, 2, invoice.debit_time_range)
    # 本期合计
    ws.cell(8, 7, invoice.amount)
    # 合计未付金额
    # # invoices = Invoice.objects.filter(customer=invoice.customer, product__type='PC', del_flag=False)
    # invoices = Invoice.objects.filter(customer=invoice.customer, del_flag=False)
    # invoices_data = invoices.values('customer').annotate(amount=Sum('amount'),
    #                                                      balance=Sum('balance'))
    # if invoices_data:
    #     unpaid = invoices_data[0]['balance']
    #     # 过往未付金额
    #     ws.cell(10, 7, -invoice.amount + unpaid)
    #     # 合计未付款金额
    #     ws.cell(12, 7, -invoice.amount)
    # 币种
    ws.cell(8, 9, invoice.currency)

    # 在第一页写入账单汇总
    # font = Font(name='Calibri', bold=True)
    # ws.cell(11, 1, '费用名称').font = font
    # ws.cell(11, 2, '数量').font = font
    # ws.cell(11, 3, '合计').font = font

    # 明细sheet
    wss = wb.worksheets[1]
    # invoice_details_df = pd.DataFrame(columns=['费用名称', '数量', '合计'], dtype=object)
    # # 循环添加账单
    # for item in debits_details:
    #     invoice_details_df.loc[len(invoice_details_df.index)] = get_invoice_detail(item)
    # invoice_details_df = invoice_details_df.groupby('费用名称').agg({'数量': 'sum', '合计': 'sum'}).reset_index()
    # for index, row in invoice_details_df.iterrows():
    #     ws.cell(12 + index, 1, row['费用名称'])
    #     ws.cell(12 + index, 2, row['数量'])
    #     ws.cell(12 + index, 3, row['合计'])

    # 第二张表写账单明细
    sql_df = write_detail_parcel_order(invoice, wss)
    # line_num = 8 + len(sql_df) + 5
    # # 添加收款信息
    # wss.cell(line_num, 1, '我司收款信息如下：')
    # wss.cell(line_num + 1, 1, '银行开户名')
    # wss.cell(line_num + 1, 2, '银行开户行')
    # wss.cell(line_num + 1, 3, '银行账号')
    # wss.cell(line_num + 2, 1, '铭志国际物流有限公司上海分公司')
    # wss.cell(line_num + 2, 3, '<EMAIL>')
    # wss.cell(line_num + 3, 1, '铭志国际物流有限公司')
    # wss.cell(line_num + 3, 2, '江苏常熟农村商业银行股份有限公司莫城支行')
    # wss.cell(line_num + 3, 3, '101230001020758314')
    # wss.cell(line_num + 4, 1, '铭志国际物流有限公司上海分公司')
    # wss.cell(line_num + 4, 2, '上海农村商业银行股份有限公司爱博家园支行	')
    # wss.cell(line_num + 4, 3, '50131000840431368')
    # wss.cell(line_num + 5, 1, '铭志国际物流有限公司义乌分公司')
    # wss.cell(line_num + 5, 2, '浙江稠州商业银行股份有限公司义乌后宅支行')
    # wss.cell(line_num + 5, 3, '15630012010090015092')
    # # 设置单元格合并居中
    # wss.merge_cells(f'A{line_num}:C{line_num}')
    # align = Alignment(horizontal='center', vertical='center')
    # wss[f'A{line_num}'].alignment = align
    # wss[f'A{line_num}'].font = Font(name='宋体', size=12, bold=True, color='0000FF')
    # # 设置单元格居中
    # for column in range(ord('A'), ord('F')):
    #     wss[chr(column) + str(line_num + 1)].alignment = Alignment(horizontal="center", vertical="center")
    #     wss[chr(column) + str(line_num + 1)].font = Font(name='宋体', size=10, bold=True)
    # border_style = Border(
    #     left=Side(border_style='thin', color='000000'),
    #     right=Side(border_style='thin', color='000000'),
    #     top=Side(border_style='thin', color='000000'),
    #     bottom=Side(border_style='thin', color='000000')
    # )
    # # 设置边框线
    # for row in wss.iter_rows(min_row=line_num+1, max_row=line_num+5, min_col=1, max_col=3):
    #     for cell in row:
    #         cell.border = border_style

    wb.save(new_filename)
    invoice.is_debit_detail = True
    invoice.debit_detail_url = settings.DOMAIN_URL + '/media/bill/' + file_name
    invoice.save()
    logger.info(f'packet {invoice.invoice_num} end write detail to excel！')


def get_invoice_detail(item: AccountReceivable):
    if item.order_num.startswith('AI') or item.adjust_number.startswith('AI'):
        adjust_number = item.adjust_number if item.adjust_number.startswith('AI') else item.order_num
    else:
        adjust_number = None

    charge_in = None
    if is_parcel_customer_order(item.order_num) and not item.is_adjust:
        charge_in_list = ParcelOrderChargeIn.objects.filter(customer_order_num__order_num=item.order_num,
                                                            currency_type=item.origin_currency,
                                                            charge=item.charge_name,
                                                            customer=item.debit_num.customer,
                                                            charge_total=item.origin_amount, del_flag=False)
        if charge_in_list.count() > 0:
            charge_in = charge_in_list.first()
    elif item.order_num.startswith(
        settings.SYSTEM_ORDER_MARK + settings.CLEARANCE_ORDER_MARK) and not item.is_adjust:
        charge_in_list = CustomsClearanceOrderChargeIn.objects.filter(customer_order_num__order_num=item.order_num,
                                                                      currency_type=item.origin_currency,
                                                                      charge=item.charge_name,
                                                                      customer=item.debit_num.customer,
                                                                      charge_total=item.origin_amount, del_flag=False)
        if charge_in_list.count() > 0:
            charge_in = charge_in_list.first()
    elif (item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK) or
          item.order_num.startswith('ZJ' + settings.CUSTOMER_ORDER_MARK)) and not item.is_adjust:
        customer_order_queryset = CustomerOrderChargeIn.objects.filter(
            customer_order_num__order_num=item.order_num,
            currency_type=item.origin_currency,
            charge=item.charge_name,
            customer=item.debit_num.customer,
            charge_total=item.origin_amount, del_flag=False)
        if customer_order_queryset.count() != 0:
            charge_in = customer_order_queryset.first()
    elif item.adjust_number.startswith('AI') or item.order_num.startswith('AI'):
        # try:
        charge_in = DebitAdjustDetail.objects.filter(debit_adjust_id__debit_adjust_num=adjust_number,
                                                     currency_type=item.origin_currency,
                                                     order_num=item.order_num, charge=item.charge_name,
                                                     customer=item.customer, track_num=item.track_num,
                                                     customer_orderNum=item.customer_orderNum,
                                                     charge_total=item.origin_amount, del_flag=False).first()
        # except (ObjectDoesNotExist, MultipleObjectsReturned):
        #     raise ParamError(f'收入调整单明细未查到或者有重复, 调整单号: {adjust_number}, 业务单据号: {item.order_num}, '
        #                      f'客户订单号: {item.customer_orderNum}, 费用名称: {item.charge_name}, '
        #                      f'合计金额: {item.origin_amount}')

    elif item.order_num.startswith('CL') and not item.is_adjust:
        charge_in = ClearanceChargeIn.objects.filter(customer_order_num__clearance_num=item.order_num,
                                                     currency_type=item.origin_currency,
                                                     charge=item.charge_name, customer=item.customer,
                                                     charge_total=item.origin_amount, del_flag=False).first()
    elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_OUTBOUND_ORDER) and not item.is_adjust:
        charge_in = OutboundOrderChargeIn.objects.filter(customer_order_num__order_num=item.order_num,
                                                         currency_type=item.origin_currency,
                                                         charge=item.charge_name, customer=item.customer,
                                                         charge_total=item.origin_amount, del_flag=False).first()
    elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_INBOUND_ORDER) and not item.is_adjust:
        charge_in = InboundOrderChargeIn.objects.filter(customer_order_num__order_num=item.order_num,
                                                        currency_type=item.origin_currency,
                                                        charge=item.charge_name, customer=item.customer,
                                                        charge_total=item.origin_amount, del_flag=False).first()

    # 在sheet1中写入收款明细
    # if charge_in:
    #     ws.cell(11 + start_row, 1, item.charge_name)
    #     ws.cell(11 + start_row, 2, charge_in.charge_count)
    #     ws.cell(11 + start_row, 3, charge_in.charge_total)
    if charge_in:
        return charge_in.charge.name, charge_in.charge_count, charge_in.charge_total
    else:
        return None, 0, 0


def write_detail(adjust_number, customer_order, item, start_col, start_row, wss):
    col_index_buyer_countrycode = start_col + 5
    col_index_buyer_postcode = start_col + 6
    col_index_carton = start_col + 7
    col_index_weight = start_col + 8
    col_index_charged_weight = start_col + 9
    col_index_price = start_col + 10
    col_index_quantity = start_col + 11
    col_index_fee_name = start_col + 12
    col_index_amount = start_col + 13
    col_index_currency = start_col + 14
    col_index_remark = start_col + 15
    col_index_zone_value = start_col + 16
    # 前4项
    wss.cell(start_row, start_col, item.debit_num.order_num)
    wss.cell(start_row, start_col + 2, item.debit_num.debit_date)
    # 清关单，track_num 存了 mawb
    wss.cell(start_row, start_col + 3, item.debit_num.track_num)
    wss.cell(start_row, start_col + 4, item.debit_num.product_code)
    charge_in = None
    # 小包订单的邮编字段不一样
    wss.cell(start_row, col_index_currency, item.debit_num.currency)

    if item.adjust_number and item.adjust_number.startswith('AI'):
        wss.cell(start_row, col_index_buyer_countrycode, '')
        wss.cell(start_row, col_index_buyer_postcode, '')
        wss.cell(start_row, col_index_carton, '')
        wss.cell(start_row, col_index_weight, '')
        wss.cell(start_row, col_index_charged_weight, '')

        if item.adjust_number.startswith('AI'):
            adjust_number = item.adjust_number
        elif item.order_num.startswith('AI'):
            adjust_number = item.order_num
        try:
            logger.info('adjust_number =' + str(adjust_number) + ', item =' + str(item.__dict__))
            charge_in = DebitAdjustDetail.objects.get(debit_adjust_id__debit_adjust_num=adjust_number,
                                                      currency_type=item.origin_currency,
                                                      order_num=item.order_num, charge=item.charge_name,
                                                      customer=item.customer, track_num=item.track_num,
                                                      customer_orderNum=item.customer_orderNum,
                                                      charge_total=item.origin_amount, del_flag=False)
            wss.cell(start_row, start_col + 1, charge_in.customer_orderNum)
            wss.cell(start_row, start_col + 3, charge_in.track_num)
            wss.cell(start_row, col_index_price, charge_in.charge_rate)
            wss.cell(start_row, col_index_quantity, charge_in.charge_count)
            wss.cell(start_row, col_index_fee_name, charge_in.charge.name)
            wss.cell(start_row, col_index_amount, charge_in.account_charge)
            wss.cell(start_row, col_index_remark, charge_in.remark)
            # 重写跟踪号
            # wss.cell(start_row, start_col + 3, charge_in.order_num)
        except DebitAdjustDetail.DoesNotExist:
            wss.cell(start_row, col_index_price, '找不到对应明细')
            wss.cell(start_row, col_index_quantity, '找不到对应明细')
            wss.cell(start_row, col_index_fee_name, '找不到对应明细')
            wss.cell(start_row, col_index_amount, '找不到对应明细')
            wss.cell(start_row, col_index_remark, '')
            # 重写跟踪号
            wss.cell(start_row, start_col + 3, '找不到对应明细')
        except Exception as e:
            logger.info(f'adjust_number {adjust_number} 收入调整单明细获取对应明细时异常{e}')
            raise e
    elif is_parcel_customer_order(item.order_num) and not item.is_adjust:
        wss.cell(start_row, col_index_buyer_countrycode, customer_order.buyer_country_code)
        wss.cell(start_row, col_index_buyer_postcode, customer_order.buyer_postcode)
        wss.cell(start_row, col_index_carton, '1')
        wss.cell(start_row, col_index_weight, customer_order.weight)
        wss.cell(start_row, col_index_charged_weight, '')
        wss.cell(start_row, col_index_zone_value, customer_order.zone_value)
        # 获取该条付款明细对应的订单下的明细
        logger.info('--item->' + item.order_num)
        charge_in_list = ParcelOrderChargeIn.objects.filter(customer_order_num__order_num=item.order_num,
                                                            currency_type=item.origin_currency,
                                                            charge=item.charge_name,
                                                            customer=item.debit_num.customer,
                                                            charge_total=item.origin_amount, del_flag=False)
        if charge_in_list.count() > 0:
            charge_in = charge_in_list.first()
            wss.cell(start_row, col_index_price, charge_in.charge_rate)
            wss.cell(start_row, col_index_quantity, charge_in.charge_count)
            wss.cell(start_row, col_index_fee_name, charge_in.charge.name)
            wss.cell(start_row, col_index_amount, charge_in.account_charge)
            wss.cell(start_row, col_index_remark, charge_in.remark)
        else:
            wss.cell(start_row, col_index_price, '找不到对应明细')
            wss.cell(start_row, col_index_quantity, '找不到对应明细')
            wss.cell(start_row, col_index_fee_name, '找不到对应明细')
            wss.cell(start_row, col_index_amount, '找不到对应明细')
            wss.cell(start_row, col_index_remark, '')

    # 清关单
    elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CLEARANCE_ORDER_MARK) and not item.is_adjust:
        # receiver = [x.address_num for x in
        #             CustomsClearanceAddress.objects.filter(del_flag=False, customer_order_num=customer_order,
        #                                                    address_type='receiver')]
        customs_clearance_address_queryset = CustomsClearanceAddress.objects.filter(
            del_flag=False,
            customer_order_num=customer_order,
            address_type='receiver'
        )
        receiver_address_num = [x.address_num for x in customs_clearance_address_queryset]
        country_code = [x.country_code for x in customs_clearance_address_queryset]
        wss.cell(start_row, col_index_buyer_countrycode, country_code)
        wss.cell(start_row, col_index_buyer_postcode, str(receiver_address_num))
        wss.cell(start_row, col_index_carton, customer_order.carton)
        wss.cell(start_row, col_index_weight, customer_order.weight)
        wss.cell(start_row, col_index_charged_weight, '')
        # 获取该条付款明细对应的订单下的明细
        logger.info('--item->' + item.order_num)
        charge_in_list = CustomsClearanceOrderChargeIn.objects.filter(customer_order_num__order_num=item.order_num,
                                                                      currency_type=item.origin_currency,
                                                                      charge=item.charge_name,
                                                                      customer=item.debit_num.customer,
                                                                      charge_total=item.origin_amount, del_flag=False)
        if charge_in_list.count() > 0:

            charge_in = charge_in_list.first()
            wss.cell(start_row, col_index_price, charge_in.charge_rate)
            wss.cell(start_row, col_index_quantity, charge_in.charge_count)
            wss.cell(start_row, col_index_fee_name, charge_in.charge.name)
            wss.cell(start_row, col_index_amount, charge_in.account_charge)
            wss.cell(start_row, col_index_remark, charge_in.remark)
        else:
            wss.cell(start_row, col_index_price, '找不到对应明细')
            wss.cell(start_row, col_index_quantity, '找不到对应明细')
            wss.cell(start_row, col_index_fee_name, '找不到对应明细')
            wss.cell(start_row, col_index_amount, '找不到对应明细')
            wss.cell(start_row, col_index_remark, '')

    elif (item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK) or
          item.order_num.startswith('ZJ' + settings.CUSTOMER_ORDER_MARK)) and not item.is_adjust:
        wss.cell(start_row, col_index_buyer_countrycode, customer_order.buyer_country_code)
        wss.cell(start_row, col_index_buyer_postcode, customer_order.buyer_postcode)
        wss.cell(start_row, col_index_carton, customer_order.carton)
        wss.cell(start_row, col_index_weight, customer_order.weight)
        wss.cell(start_row, col_index_charged_weight, customer_order.charge_weight)
        customer_order_queryset = CustomerOrderChargeIn.objects.filter(
            customer_order_num__order_num=item.order_num,
            currency_type=item.origin_currency,
            charge=item.charge_name,
            customer=item.debit_num.customer,
            charge_total=item.origin_amount, del_flag=False)
        if customer_order_queryset.count() != 0:
            charge_in = customer_order_queryset.first()
            wss.cell(start_row, col_index_price, charge_in.charge_rate)
            wss.cell(start_row, col_index_quantity, charge_in.charge_count)
            wss.cell(start_row, col_index_fee_name, charge_in.charge.name)
            wss.cell(start_row, col_index_amount, charge_in.account_charge)
            wss.cell(start_row, col_index_remark, charge_in.remark)
        else:
            wss.cell(start_row, col_index_price, '找不到对应明细')
            wss.cell(start_row, col_index_quantity, '找不到对应明细')
            wss.cell(start_row, col_index_fee_name, '找不到对应明细')
            wss.cell(start_row, col_index_amount, '找不到对应明细')
            wss.cell(start_row, col_index_remark, '')
    elif item.order_num.startswith('CL') and not item.is_adjust:
        wss.cell(start_row, col_index_buyer_countrycode, '')
        wss.cell(start_row, col_index_buyer_postcode, '')
        wss.cell(start_row, col_index_carton, customer_order.carton)
        wss.cell(start_row, col_index_weight, customer_order.weight)
        wss.cell(start_row, col_index_charged_weight, '')
        try:
            logger.info(
                'order_num->' + item.order_num + ', ' + item.origin_currency + ','
                + str(item.charge_name.id) + ',' + str(item.customer.id) + ',' + str(item.origin_amount))
            charge_in = ClearanceChargeIn.objects.get(customer_order_num__clearance_num=item.order_num,
                                                      currency_type=item.origin_currency,
                                                      charge=item.charge_name, customer=item.customer,
                                                      charge_total=item.origin_amount, del_flag=False)
            wss.cell(start_row, col_index_price, charge_in.charge_rate)
            wss.cell(start_row, col_index_quantity, charge_in.charge_count)
            wss.cell(start_row, col_index_fee_name, charge_in.charge.name)
            wss.cell(start_row, col_index_amount, charge_in.account_charge)
            wss.cell(start_row, col_index_remark, charge_in.remark)
        except ClearanceChargeIn.DoesNotExist:
            wss.cell(start_row, col_index_price, '找不到对应明细')
            wss.cell(start_row, col_index_quantity, '找不到对应明细')
            wss.cell(start_row, col_index_fee_name, '找不到对应明细')
            wss.cell(start_row, col_index_amount, '找不到对应明细')
            wss.cell(start_row, col_index_remark, '')
        except Exception as e:
            logger.info(f'order_num {item.order_num} 进口报关单收入获取对应明细时异常{e}')
            raise e
    elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_OUTBOUND_ORDER) and not item.is_adjust:
        wss.cell(start_row, col_index_buyer_countrycode, customer_order.buyer_country_code)
        wss.cell(start_row, col_index_buyer_postcode, customer_order.buyer_postcode)
        wss.cell(start_row, col_index_carton, '1')
        wss.cell(start_row, col_index_weight, customer_order.weight)
        wss.cell(start_row, col_index_charged_weight, '')
        logger.info('---item.order_num--->' + item.order_num)
        try:
            charge_in = OutboundOrderChargeIn.objects.get(customer_order_num__order_num=item.order_num,
                                                          currency_type=item.origin_currency,
                                                          charge=item.charge_name, customer=item.customer,
                                                          charge_total=item.origin_amount, del_flag=False)
            wss.cell(start_row, col_index_price, charge_in.charge_rate)
            wss.cell(start_row, col_index_quantity, charge_in.charge_count)
            wss.cell(start_row, col_index_fee_name, charge_in.charge.name)
            wss.cell(start_row, col_index_amount, charge_in.account_charge)
            wss.cell(start_row, col_index_remark, charge_in.remark)
        except ClearanceChargeIn.DoesNotExist:
            wss.cell(start_row, col_index_price, '找不到对应明细')
            wss.cell(start_row, col_index_quantity, '找不到对应明细')
            wss.cell(start_row, col_index_fee_name, '找不到对应明细')
            wss.cell(start_row, col_index_amount, '找不到对应明细')
            wss.cell(start_row, col_index_remark, '')
    elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_INBOUND_ORDER) and not item.is_adjust:
        wss.cell(start_row, col_index_carton, customer_order.carton)
        wss.cell(start_row, col_index_weight, customer_order.weight)
        try:
            charge_in = InboundOrderChargeIn.objects.get(customer_order_num__order_num=item.order_num,
                                                         currency_type=item.origin_currency,
                                                         charge=item.charge_name, customer=item.customer,
                                                         charge_total=item.origin_amount, del_flag=False)
            wss.cell(start_row, col_index_price, charge_in.charge_rate)
            wss.cell(start_row, col_index_quantity, charge_in.charge_count)
            wss.cell(start_row, col_index_fee_name, charge_in.charge.name)
            wss.cell(start_row, col_index_amount, charge_in.account_charge)
            wss.cell(start_row, col_index_remark, charge_in.remark)
        except ClearanceChargeIn.DoesNotExist:
            wss.cell(start_row, col_index_price, '找不到对应明细')
            wss.cell(start_row, col_index_quantity, '找不到对应明细')
            wss.cell(start_row, col_index_fee_name, '找不到对应明细')
            wss.cell(start_row, col_index_amount, '找不到对应明细')
            wss.cell(start_row, col_index_remark, '')
        except Exception as e:
            logger.info(f'order_num {item.order_num} 入库单收入明细获取对应明细时异常{e}')
            raise e
    # 设置单元格居中
    wss['A' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['B' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['C' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['D' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['E' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['F' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['G' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['H' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['I' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['J' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['K' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['L' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['M' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['N' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    start_row += 1
    return start_row


# FBA订单写账单汇总
def write_detail_transport(invoice, ws):
    logger.info(f'{invoice.invoice_num} write_detail_transport start')
    # 写入基础运费sheet
    query_sql_sms = f"""
    select
    日期,
    销售产品,
    客户单号,
    订单号,
    仓库代码,
    客户代码,
    收货计费重,
    国家,
    件数,
    邮编,
    sum(单价) as 单价,
    sum(运费) as 运费,
    sum(提货费) as 提货费,
    sum(总运费) - sum(运费) - sum(提货费) as 附加费,
    sum(总运费) as 总运费,
    中文品名
    from (
    select
    DATE(settle_accountreceivable.account_time) as 日期,
    -- settle_accountreceivable.order_num as 订单号,
    pms_product.name as 销售产品,
    settle_debit.customer_order as 客户单号,
    settle_debit.order_num as 订单号,
    settle_accountreceivable.warehouse_code as 仓库代码,
    company_company.short_name as 客户代码,
    settle_accountreceivable.buyer_postcode as 邮编,
    settle_accountreceivable.carton as 件数,
    settle_debit.confirm_charge_weight as 收货计费重,
    settle_accountreceivable.country_code as 国家,
    max(CASE WHEN info_charge.code = 'YF' THEN settle_accountreceivable.charge_rate ELSE 0 END) as 单价,
    max(CASE WHEN info_charge.code = 'YF' THEN settle_accountreceivable.account_amount ELSE 0 END) as 运费,
    max(CASE WHEN info_charge.code = 'THF' THEN settle_accountreceivable.account_amount ELSE 0 END) as 提货费,
    sum(settle_accountreceivable.account_amount) as 总运费,
    info_charge.code as charge_code,
    settle_accountreceivable.declared_nameCN as 中文品名
    from settle_accountreceivable
    join settle_debit on settle_debit.id = settle_accountreceivable.debit_num_id and settle_debit.del_flag = false
    join settle_invoice on settle_invoice.id = settle_debit.invoice_id and settle_invoice.del_flag = false
    left join company_company on company_company.id = settle_accountreceivable.customer_id
    join pms_product on pms_product.id = settle_accountreceivable.product_id
    join info_charge on info_charge.id = settle_accountreceivable.charge_name_id
    left join order_customerorder on order_customerorder.order_num = settle_accountreceivable.order_num and order_customerorder.del_flag = false
    where settle_accountreceivable.del_flag = false
    and settle_invoice.id = {invoice.id}
    group by
    settle_accountreceivable.id
    ORDER BY settle_accountreceivable.id DESC
    ) as inner_table
    group by
    inner_table.日期,
    inner_table.销售产品,
    inner_table.客户单号,
    inner_table.订单号,
    inner_table.仓库代码,
    inner_table.客户代码,
    inner_table.收货计费重,
    inner_table.国家,
    inner_table.件数,
    inner_table.邮编,
    inner_table.中文品名
    """

    query_sql = f"""
        select
        日期,
        销售产品,
        客户单号,
        订单号,
        仓库代码,
        客户代码,
        收货计费重,
        国家,
        件数,
        邮编,
        sum(单价) as 单价,
        sum(运费) as 运费,
        SUM(inner_table.charge_total) - sum(运费) as 附加费,
        SUM(inner_table.charge_total) as 总运费,
        中文品名
        from (
        WITH first_parcel_item AS (
          SELECT
            order_customerorder.id as customerorder_id,
              MIN(declared_nameCN) as declared_nameCN
          FROM
            order_parcelitem
            join order_parcel on order_parcel.id = order_parcelitem.parcel_num_id
            join order_customerorder on order_customerorder.id = order_parcel.customer_order_id
          GROUP BY
            order_customerorder.id
        )
        select
        order_customerorder.id as oid,
        order_customerorderchargein.charge_total as charge_total,
        DATE(settle_debit.debit_date) as 日期,
        -- order_customerorder.order_num as 订单号,
        settle_debit.order_num as 订单号,
        case
            when company_address.address_num = '' or company_address.address_num is NULL then order_customerorder.buyer_address_num
            else company_address.address_num
        end as 仓库代码,
        company_company.short_name as 客户代码,
        -- order_customerorder.ref_num as 客户单号,
        settle_debit.customer_order as 客户单号,
        order_customerorder.tracking_num as 跟踪号,
        settle_debit.product_code as 销售产品,
        case
            when company_address.postcode = '' or company_address.postcode is NULL then order_customerorder.buyer_postcode
            else company_address.postcode
        end as 邮编,
        order_customerorder.carton as 件数,
        order_customerorder.confirm_charge_weight as 收货计费重,
        company_address.country_code as 国家,
        max(CASE WHEN info_charge.name = '运费' THEN order_customerorderchargein.charge_rate ELSE 0 END) as 单价,
        max(CASE WHEN info_charge.name = '运费' THEN order_customerorderchargein.charge_total ELSE 0 END) as 运费,
        info_charge.name as 费用名称,
        first_parcel_item.declared_nameCN as 中文品名
        from order_customerorderchargein
        join order_customerorder on order_customerorder.id = order_customerorderchargein.customer_order_num_id
        JOIN first_parcel_item ON order_customerorder.id = first_parcel_item.customerorder_id
        -- join settle_accountreceivable on settle_accountreceivable.charge_name_id = order_customerorderchargein.charge_id
        join settle_debit on settle_debit.customer_order = order_customerorder.ref_num
        join settle_accountreceivable on settle_debit.id = settle_accountreceivable.debit_num_id
        join settle_invoice on settle_invoice.id = settle_debit.invoice_id
        join info_charge on info_charge.id = order_customerorderchargein.charge_id
        join company_company on company_company.id = order_customerorder.customer_id
        left join company_address on company_address.id = order_customerorder.receiver_id
        where order_customerorderchargein.currency_type = settle_accountreceivable.origin_currency
        and order_customerorderchargein.customer_id = settle_debit.customer_id
        and order_customerorderchargein.charge_total = settle_accountreceivable.origin_amount
        and order_customerorderchargein.del_flag = false
        and order_customerorder.del_flag = false
        and settle_accountreceivable.del_flag = false
        and settle_debit.del_flag = false
        and settle_invoice.del_flag = false
        and info_charge.del_flag = false
        and settle_invoice.id = {invoice.id}
        group by
        order_customerorder.id,
        settle_debit.debit_date,
        settle_debit.order_num,
        order_customerorder.ref_num,
        order_customerorder.tracking_num,
        settle_debit.product_code,
        order_customerorder.buyer_postcode,
        order_customerorder.confirm_charge_weight,
        order_customerorderchargein.charge_rate,
        order_customerorderchargein.charge_total,
        settle_invoice.id,
        info_charge.name,
        first_parcel_item.declared_nameCN
        ) as inner_table
        group by
            inner_table.oid,
            inner_table.日期,
            inner_table.销售产品,
            inner_table.客户单号,
            inner_table.订单号,
            inner_table.仓库代码,
            inner_table.客户代码,
            inner_table.收货计费重,
            inner_table.国家,
            inner_table.件数,
            inner_table.邮编
        order by inner_table.oid desc
        """
    mysql_connect = MysqlConn()
    sql_data = mysql_connect.sql_execute(query_sql_sms)
    if not sql_data:
        mysql_connect.conn.close()
        logger.info(f'{invoice.invoice_num} 没有运费数据')
        return
    # 写入基础运费sheet
    write_field = ['日期', '销售产品', '客户单号', '订单号', '仓库代码', '客户代码', '收货计费重',
                   '国家', '件数', '邮编', '单价', '运费', '提货费', '附加费', '总运费', '中文品名']
    yf_sql_df = pd.DataFrame(sql_data, columns=write_field)
    # pd.read_sql报错了
    # sql_df = pd.read_sql(query_sql, mysql_connect.conn)
    # print('sql_df-->\n', sql_df)
    start_row = 2
    for index, row in yf_sql_df.iterrows():
        start_col = 1
        for column in range(len(write_field)):
            ws[1].cell(start_row, start_col, row[column])
            start_col += 1
        start_row += 1

    # 写入提货费sheet
    pick_up_charge_sql = f"""
    select
    DATE(settle_accountreceivable.account_time) as 日期,
    -- settle_accountreceivable.order_num as 订单号,
    settle_debit.order_num as 订单号,
    info_charge.name as 提货费类型,
    settle_accountreceivable.account_amount as 金额,
    settle_accountreceivable.origin_currency as 币种,
    settle_accountreceivable.remark as 备注
    from settle_accountreceivable
    join settle_debit on settle_debit.id = settle_accountreceivable.debit_num_id
    join settle_invoice on settle_invoice.id = settle_debit.invoice_id
    join company_company on company_company.id = settle_accountreceivable.customer_id
    join pms_product on pms_product.id = settle_accountreceivable.product_id
    join info_charge on info_charge.id = settle_accountreceivable.charge_name_id
    -- left join settle_debitadjustdetail on settle_accountreceivable.order_num = settle_debitadjustdetail.order_num
    -- left join settle_debitadjust on settle_debitadjustdetail.debit_adjust_id_id = settle_debitadjust.id
    where settle_accountreceivable.del_flag = false
    and settle_debit.del_flag = false
    and settle_invoice.del_flag = false
    and info_charge.code = 'THF'
    and settle_invoice.id = {invoice.id}
    group by
    settle_accountreceivable.order_num,
    settle_accountreceivable.id
    ORDER BY settle_accountreceivable.order_num DESC
    """

    sql_data = mysql_connect.sql_execute(pick_up_charge_sql)
    if not sql_data:
        # mysql_connect.conn.close()
        logger.info(f'{invoice.invoice_num} 没有提货费数据')
        # return sql_df
    # 写入附加费sheet
    write_field = ['日期', '订单号', '提货费类型', '金额', '币种', '备注']
    pick_up_charge_sql_df = pd.DataFrame(sql_data, columns=write_field)
    # additional_sql_df = pd.read_sql(additional_sql, mysql_connect.conn)
    # print('additional_sql_df-->\n', additional_sql_df)
    start_row = 2
    for index, row in pick_up_charge_sql_df.iterrows():
        start_col = 1
        for column in range(len(write_field)):
            ws[2].cell(start_row, start_col, row[column])
            start_col += 1
        start_row += 1

    # 写入附加费sheet
    additional_sql_sms = f"""
    select
    DATE(settle_accountreceivable.account_time) as 日期,
    -- settle_accountreceivable.order_num as 订单号,
    settle_debit.order_num as 订单号,
    info_charge.name as 附加费类型,
    settle_accountreceivable.account_amount as 金额,
    settle_accountreceivable.origin_currency as 币种,
    settle_accountreceivable.remark as 备注
    from settle_accountreceivable
    join settle_debit on settle_debit.id = settle_accountreceivable.debit_num_id
    join settle_invoice on settle_invoice.id = settle_debit.invoice_id
    join company_company on company_company.id = settle_accountreceivable.customer_id
    join pms_product on pms_product.id = settle_accountreceivable.product_id
    join info_charge on info_charge.id = settle_accountreceivable.charge_name_id
    -- left join settle_debitadjustdetail on settle_accountreceivable.order_num = settle_debitadjustdetail.order_num
    -- left join settle_debitadjust on settle_debitadjustdetail.debit_adjust_id_id = settle_debitadjust.id
    where settle_accountreceivable.del_flag = false
    and settle_debit.del_flag = false
    and settle_invoice.del_flag = false
    and info_charge.code not in ('YF', 'THF')
    and settle_invoice.id = {invoice.id}
    group by
    settle_accountreceivable.order_num,
    settle_accountreceivable.id
    ORDER BY settle_accountreceivable.order_num DESC
    """

    additional_sql = f"""
    select
        DATE(settle_debit.debit_date) as 日期,
        -- order_customerorder.order_num as 订单号,
        settle_debit.order_num as 订单号,
        info_charge.name as 附加费类型,
        order_customerorderchargein.charge_total as 金额,
        settle_debit.currency as 币种,
        order_customerorderchargein.remark as 备注
        from order_customerorderchargein
        join order_customerorder on order_customerorder.id = order_customerorderchargein.customer_order_num_id
        join settle_debit on settle_debit.customer_order = order_customerorder.ref_num
        -- join settle_accountreceivable on settle_accountreceivable.charge_name_id = order_customerorderchargein.charge_id
        join settle_accountreceivable on settle_debit.id = settle_accountreceivable.debit_num_id
        join settle_invoice on settle_invoice.id = settle_debit.invoice_id
        join info_charge on info_charge.id = order_customerorderchargein.charge_id
        join company_company on company_company.id = order_customerorder.customer_id
        where order_customerorderchargein.currency_type = settle_accountreceivable.origin_currency
        and order_customerorderchargein.customer_id = settle_debit.customer_id
        and order_customerorderchargein.charge_total = settle_accountreceivable.origin_amount
        and order_customerorderchargein.del_flag = false
        and order_customerorder.del_flag = false
        and settle_accountreceivable.del_flag = false
        and settle_debit.del_flag = false
        and settle_invoice.del_flag = false
        and info_charge.del_flag = false
        and info_charge.code != 'YF'
        and settle_invoice.id = {invoice.id}
        ORDER BY order_customerorder.id DESC
        """

    sql_data = mysql_connect.sql_execute(additional_sql_sms)
    if not sql_data:
        # mysql_connect.conn.close()
        logger.info(f'{invoice.invoice_num} 没有附加费数据')
        # return sql_df
    # 写入附加费sheet
    write_field = ['日期', '订单号', '附加费类型', '金额', '币种', '备注']
    additional_sql_df = pd.DataFrame(sql_data, columns=write_field)
    # additional_sql_df = pd.read_sql(additional_sql, mysql_connect.conn)
    # print('additional_sql_df-->\n', additional_sql_df)
    start_row = 2
    for index, row in additional_sql_df.iterrows():
        start_col = 1
        for column in range(len(write_field)):
            ws[3].cell(start_row, start_col, row[column])
            start_col += 1
        start_row += 1

    mysql_connect.conn.close()
    # 设置单元格居中
    # for column in range(ord('A'), ord('N')):
    #     wss[chr(column) + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    logger.info(f'{invoice.invoice_num} write_detail_transport end')
    return yf_sql_df


# def write_detail_transport_yqf(invoice, ws):
#     写入基础运费sheet
#     write_field = ['日期', '销售产品', '客户单号', '订单号', '仓库代码', '客户代码', '收货计费重',
#                    '国家', '件数', '邮编', '单价', '运费', '提货费', '附加费', '总运费', '中文品名']
#     yf_sql_df = pd.DataFrame(sql_data, columns=write_field)
#     pd.read_sql报错了
#     sql_df = pd.read_sql(query_sql, mysql_connect.conn)
#     print('sql_df-->\n', sql_df)



def save_invoice_err(invoice_id, msg):
    Invoice.objects.filter(id=invoice_id).update(err_msg=msg)


# 小包订单写账单汇总
def write_detail_parcel_order(invoice, wss):
    logger.info(f'{invoice.invoice_num} write_detail_parcel_order start')
    query_sql_sms = f"""
    select
    日期,
    订单号,
    客户订单号,
    邮政单号,
    产品,
    邮编,
    件数,
    sum(计费重) as 计费重,
    sum(单价) as 单价,
    sum(运费) as 运费,
    sum(总费用) - sum(运费) as 运杂费,
    sum(总费用) as 总费用,
    中文品名
    from (
    select
    settle_accountreceivable.account_time as 日期,
    settle_debit.order_num as 订单号,
    settle_debit.customer_order as 客户订单号,
    settle_accountreceivable.label_billid as 邮政单号,
    pms_product.name as 产品,
    settle_accountreceivable.buyer_postcode as 邮编,
    1 as 件数,
    max(CASE WHEN info_charge.name = '运费' THEN settle_accountreceivable.charge_count ELSE 0 END) as 计费重,
    max(CASE WHEN info_charge.name = '运费' THEN settle_accountreceivable.charge_rate ELSE 0 END) as 单价,
    max(CASE WHEN info_charge.name = '运费' THEN settle_accountreceivable.account_amount ELSE 0 END) as 运费,
    settle_accountreceivable.account_amount as 总费用,
    info_charge.name as 费用名称, -- 费用名称
    settle_accountreceivable.declared_nameCN as 中文品名
    from settle_accountreceivable
    join settle_debit on settle_debit.id = settle_accountreceivable.debit_num_id
    join settle_invoice on settle_invoice.id = settle_debit.invoice_id
    join company_company on company_company.id = settle_accountreceivable.customer_id
    join pms_product on pms_product.id = settle_accountreceivable.product_id
    join info_charge on info_charge.id = settle_accountreceivable.charge_name_id
    where settle_accountreceivable.del_flag = false
    and settle_debit.del_flag = false
    and settle_invoice.del_flag = false
    and settle_invoice.id = {invoice.id}
    group by
    settle_accountreceivable.id
    ORDER BY settle_accountreceivable.id DESC
    -- ORDER BY settle_debit.order_num DESC
    ) as inner_table
    group by
    inner_table.日期,
    inner_table.订单号,
    inner_table.客户订单号,
    inner_table.邮政单号,
    inner_table.产品,
    inner_table.邮编,
    inner_table.中文品名
    """

    query_sql = f"""
    select
        日期,
        订单号,
        客户订单号,
        邮政单号,
        产品,
        邮编,
        件数,
        sum(计费重) as 计费重,
        sum(单价) as 单价,
        sum(运费) as 运费,
        SUM(inner_table.charge_total) - sum(运费) as 运杂费,
        SUM(inner_table.charge_total) as 总费用,
        中文品名
        from (
        WITH first_parcel_item AS (
          SELECT
            order_parcelcustomerorder.id as parcelcustomerorder_id,
              MIN(declared_nameCN) as declared_nameCN
          FROM
            order_parcelorderitem
            join order_parcelorderparcel on order_parcelorderparcel.id = order_parcelorderitem.parcel_num_id
            join order_parcelcustomerorder on order_parcelcustomerorder.id = order_parcelorderparcel.customer_order_id
          GROUP BY
            order_parcelcustomerorder.id
        )
        select
        order_parcelcustomerorder.id as oid,
        order_parcelorderchargein.charge_total as charge_total,
        settle_debit.debit_date as 日期,
        order_parcelcustomerorder.order_num as 订单号,
        order_parcelcustomerorder.customer_order_num as 客户订单号,
        order_parcelcustomerorder.label_billid as 邮政单号,
        settle_debit.product_code as 产品,
        order_parcelcustomerorder.buyer_postcode as 邮编,
        1 as 件数,
        -- order_parcelcustomerorder.charge_weight as 计费重,
        MAX(CASE WHEN info_charge.name = '运费' THEN order_parcelorderchargein.charge_count ELSE 0 END) as 计费重,
        -- 假设有多个运费也只取第一个
        MAX(CASE WHEN info_charge.name = '运费' THEN order_parcelorderchargein.charge_rate ELSE 0 END) as 单价,
        MAX(CASE WHEN info_charge.name = '运费' THEN order_parcelorderchargein.charge_total ELSE 0 END) as 运费,
        info_charge.name as 费用名称,
        first_parcel_item.declared_nameCN as 中文品名
        from order_parcelorderchargein
        join order_parcelcustomerorder on order_parcelcustomerorder.id = order_parcelorderchargein.customer_order_num_id
        join settle_debit on settle_debit.customer_order = order_parcelcustomerorder.customer_order_num
        join settle_invoice on settle_invoice.id = settle_debit.invoice_id
        join info_charge on info_charge.id = order_parcelorderchargein.charge_id
        JOIN first_parcel_item ON order_parcelcustomerorder.id = first_parcel_item.parcelcustomerorder_id
        where order_parcelorderchargein.del_flag = false
        and order_parcelcustomerorder.del_flag = false
        and settle_debit.del_flag = false
        and settle_invoice.del_flag = false
        and info_charge.del_flag = false
        and settle_invoice.id = {invoice.id}
        group by
        order_parcelcustomerorder.id,
        order_parcelorderchargein.charge_total,
        settle_debit.debit_date,
        order_parcelcustomerorder.order_num,
        order_parcelcustomerorder.customer_order_num,
        order_parcelcustomerorder.tracking_num,
        settle_debit.product_code,
        order_parcelcustomerorder.buyer_postcode,
        info_charge.name,
        first_parcel_item.declared_nameCN
        ) as inner_table
    group by
        inner_table.oid,
        inner_table.日期,
        inner_table.订单号,
        inner_table.客户订单号,
        inner_table.邮政单号,
        inner_table.产品,
        inner_table.邮编,
        inner_table.件数
        -- inner_table.计费重
    order by inner_table.oid desc
    """

    mysql_connect = MysqlConn()
    # sql_df = pd.read_sql(query_sql, mysql_connect.conn)
    sql_data = mysql_connect.sql_execute(query_sql_sms)
    if not sql_data:
        mysql_connect.conn.close()
        save_invoice_err(invoice.id, f'{invoice.invoice_num} 没有费用数据')
        logger.info(f'{invoice.invoice_num} 没有费用数据')
        return
    write_field = ['日期', '订单号', '客户订单号', '邮政单号', '产品', '邮编', '件数',
                   '计费重', '单价', '运费', '运杂费', '总费用', '中文品名']
    sql_df = pd.DataFrame(sql_data, columns=write_field)
    # print('sql_df-->\n', sql_df)
    start_row = 9
    for index, row in sql_df.iterrows():
        start_col = 1
        for column in range(len(write_field)):
            wss.cell(start_row, start_col, row[column])
            start_col += 1
        start_row += 1

    mysql_connect.conn.close()
    save_invoice_err(invoice.id, '')
    logger.info(f'{invoice.invoice_num} write_detail_parcel_order end')
    return sql_df


# 兼容了清关
def write_detail_new(adjust_number, customer_order, item, start_col, start_row, wss, remark = False):
    # 前4项
    wss.cell(start_row, start_col, item.debit_num.order_num)
    wss.cell(start_row, start_col + 2, item.debit_num.debit_date)
    # 清关单，track_num 存了 mawb
    wss.cell(start_row, start_col + 3, item.debit_num.track_num)
    wss.cell(start_row, start_col + 4, item.debit_num.mawb)
    wss.cell(start_row, start_col + 5, item.debit_num.product_code)
    charge_in = None
    # 小包订单的邮编字段不一样
    wss.cell(start_row, start_col + 16, item.debit_num.currency)

    if item.adjust_number and item.adjust_number.startswith('AI') :
        wss.cell(start_row, start_col + 6, '')
        wss.cell(start_row, start_col + 7, '')
        wss.cell(start_row, start_col + 8, '')
        wss.cell(start_row, start_col + 9, '')
        wss.cell(start_row, start_col + 10, '')
        wss.cell(start_row, start_col + 11, '')

        adjust_number = item.adjust_number
        try:
            logger.info('adjust_number =' + str(adjust_number) + ', item =' + str(item.__dict__))
            charge_in = DebitAdjustDetail.objects.get(debit_adjust_id__debit_adjust_num=adjust_number,
                                                      currency_type=item.origin_currency,
                                                      charge=item.charge_name,
                                                      customer=item.customer,
                                                      track_num=item.track_num,
                                                      ocean_order_num=item.ocean_order_num,
                                                      charge_total=item.origin_amount, del_flag=False)

            # wss.cell(start_row, start_col + 1, charge_in.customer_orderNum or '')
            wss.cell(start_row, start_col + 3, charge_in.track_num)
            wss.cell(start_row, start_col + 12, charge_in.charge_rate)
            wss.cell(start_row, start_col + 13, charge_in.charge_count)
            wss.cell(start_row, start_col + 14, charge_in.charge.name)
            wss.cell(start_row, start_col + 15, charge_in.account_charge)
            wss.cell(start_row, start_col + 17, charge_in.remark)
            # 重写跟踪号
            # wss.cell(start_row, start_col + 3, charge_in.order_num)
        except DebitAdjustDetail.DoesNotExist:
            wss.cell(start_row, start_col + 12, '找不到对应明细')
            wss.cell(start_row, start_col + 13, '找不到对应明细')
            wss.cell(start_row, start_col + 14, '找不到对应明细')
            wss.cell(start_row, start_col + 15, '找不到对应明细')
            wss.cell(start_row, start_col + 17, '')
            # 重写跟踪号
            wss.cell(start_row, start_col + 3, '找不到对应明细')
        except Exception as e:
            logger.info(f'adjust_number {adjust_number} 收入调整单明细获取对应明细时异常{e}')
            raise e
    elif is_parcel_customer_order(item.order_num) and not item.is_adjust:
        wss.cell(start_row, start_col + 6, customer_order.buyer_country_code)
        wss.cell(start_row, start_col + 7, customer_order.buyer_postcode)
        wss.cell(start_row, start_col + 8, '1')
        wss.cell(start_row, start_col + 9, '1')
        wss.cell(start_row, start_col + 10, customer_order.weight)
        wss.cell(start_row, start_col + 11, '')
        wss.cell(start_row, start_col + 18, customer_order.zone_value)
        # 获取该条付款明细对应的订单下的明细
        logger.info('--item->' + item.order_num)
        charge_in_list = ParcelOrderChargeIn.objects.filter(customer_order_num__order_num=item.order_num,
                                                            currency_type=item.origin_currency,
                                                            charge=item.charge_name,
                                                            customer=item.debit_num.customer,
                                                            charge_total=item.origin_amount, del_flag=False)
        if charge_in_list.count() > 0:
            charge_in = charge_in_list.first()
            wss.cell(start_row, start_col + 11, charge_in.charge_weight)
            wss.cell(start_row, start_col + 12, charge_in.charge_rate)
            wss.cell(start_row, start_col + 13, charge_in.charge_count)
            wss.cell(start_row, start_col + 14, charge_in.charge.name)
            wss.cell(start_row, start_col + 15, charge_in.account_charge)
            wss.cell(start_row, start_col + 17, charge_in.remark)
        else:
            wss.cell(start_row, start_col + 11, '找不到对应明细')
            wss.cell(start_row, start_col + 12, '找不到对应明细')
            wss.cell(start_row, start_col + 13, '找不到对应明细')
            wss.cell(start_row, start_col + 14, '找不到对应明细')
            wss.cell(start_row, start_col + 15, '找不到对应明细')
            wss.cell(start_row, start_col + 17, '')

    # 清关单
    elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CLEARANCE_ORDER_MARK) and not item.is_adjust:
        logger.info(f'进来了13')
        customs_clearance_address_queryset = CustomsClearanceAddress.objects.filter(del_flag=False,
                                                                                    customer_order_num=customer_order,
                                                                                    address_type='receiver')
        receiver_postcode = [x.postcode for x in customs_clearance_address_queryset]
        country_code = [x.country_code for x in customs_clearance_address_queryset]
        wss.cell(start_row, start_col + 6, ','.join(country_code))
        wss.cell(start_row, start_col + 7, ','.join(receiver_postcode))
        wss.cell(start_row, start_col + 8, customer_order.pre_carton)
        wss.cell(start_row, start_col + 9, customer_order.carton)
        wss.cell(start_row, start_col + 10, customer_order.pre_weight)
        wss.cell(start_row, start_col + 11, customer_order.weight)
        # 获取该条付款明细对应的订单下的明细
        logger.info('--item->' + item.order_num)
        charge_in_list = CustomsClearanceOrderChargeIn.objects.filter(customer_order_num__order_num=item.order_num,
                                                                      currency_type=item.origin_currency,
                                                                      charge=item.charge_name,
                                                                      customer=item.debit_num.customer,
                                                                      charge_total=item.origin_amount, del_flag=False)
        if charge_in_list.count() > 0:
            charge_in = charge_in_list.first()
            wss.cell(start_row, start_col + 12, charge_in.charge_rate) # 单价
            wss.cell(start_row, start_col + 13, customer_order.package_num) #实际包裹数
            wss.cell(start_row, start_col + 14, charge_in.charge.name)
            wss.cell(start_row, start_col + 15, charge_in.account_charge)
            wss.cell(start_row, start_col + 17, charge_in.remark)
        else:
            wss.cell(start_row, start_col + 12, '找不到对应明细')
            wss.cell(start_row, start_col + 13, '找不到对应明细')
            wss.cell(start_row, start_col + 14, '找不到对应明细')
            wss.cell(start_row, start_col + 15, '找不到对应明细')
            wss.cell(start_row, start_col + 17, '')

    # 运输
    elif (item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK)) and not item.is_adjust:
        wss.cell(start_row, start_col + 6, customer_order.buyer_country_code)
        wss.cell(start_row, start_col + 7, customer_order.buyer_postcode)
        wss.cell(start_row, start_col + 8, '1')
        wss.cell(start_row, start_col + 9, customer_order.carton)
        wss.cell(start_row, start_col + 10, customer_order.weight)
        wss.cell(start_row, start_col + 11, customer_order.charge_weight)
        customer_order_queryset = CustomerOrderChargeIn.objects.filter(
            customer_order_num__order_num=item.order_num,
            currency_type=item.origin_currency,
            charge=item.charge_name,
            customer=item.debit_num.customer,
            charge_total=item.origin_amount, del_flag=False)
        if customer_order_queryset.count() != 0:
            charge_in = customer_order_queryset.first()
            wss.cell(start_row, start_col + 12, charge_in.charge_rate)
            wss.cell(start_row, start_col + 13, charge_in.charge_count)
            wss.cell(start_row, start_col + 14, charge_in.charge.name)
            wss.cell(start_row, start_col + 15, charge_in.account_charge)
            wss.cell(start_row, start_col + 17, charge_in.remark)
        else:
            wss.cell(start_row, start_col + 12, '找不到对应明细')
            wss.cell(start_row, start_col + 13, '找不到对应明细')
            wss.cell(start_row, start_col + 14, '找不到对应明细')
            wss.cell(start_row, start_col + 15, '找不到对应明细')
            wss.cell(start_row, start_col + 17, '')

    elif item.order_num.startswith('CL') and not item.is_adjust:
        wss.cell(start_row, start_col + 6, '')
        wss.cell(start_row, start_col + 7, '')
        wss.cell(start_row, start_col + 8, '')
        wss.cell(start_row, start_col + 9, customer_order.carton)
        wss.cell(start_row, start_col + 10, customer_order.weight)
        wss.cell(start_row, start_col + 11, '')
        try:
            logger.info('order_num->' + item.order_num + ', ' + item.origin_currency + ','
                + str(item.charge_name.id) + ',' + str(item.customer.id) + ',' + str(item.origin_amount))
            charge_in = ClearanceChargeIn.objects.get(customer_order_num__clearance_num=item.order_num,
                                                      currency_type=item.origin_currency,
                                                      charge=item.charge_name, customer=item.customer,
                                                      charge_total=item.origin_amount, del_flag=False)
            wss.cell(start_row, start_col + 12, charge_in.charge_rate)
            wss.cell(start_row, start_col + 13, charge_in.charge_count)
            wss.cell(start_row, start_col + 14, charge_in.charge.name)
            wss.cell(start_row, start_col + 15, charge_in.account_charge)
            wss.cell(start_row, start_col + 17, charge_in.remark)
        except ClearanceChargeIn.DoesNotExist:
            wss.cell(start_row, start_col + 12, '找不到对应明细')
            wss.cell(start_row, start_col + 13, '找不到对应明细')
            wss.cell(start_row, start_col + 14, '找不到对应明细')
            wss.cell(start_row, start_col + 15, '找不到对应明细')
            wss.cell(start_row, start_col + 17, '')
        except Exception as e:
            logger.info(f'order_num {item.order_num} 进口报关单收入获取对应明细时异常{e}')
            raise e
    elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_OUTBOUND_ORDER) and not item.is_adjust:
        wss.cell(start_row, start_col + 6, customer_order.buyer_country_code)
        wss.cell(start_row, start_col + 7, customer_order.buyer_postcode)
        wss.cell(start_row, start_col + 8, '1')
        wss.cell(start_row, start_col + 9, '1')
        wss.cell(start_row, start_col + 10, customer_order.weight)
        wss.cell(start_row, start_col + 11, '')
        logger.info('---item.order_num--->' + item.order_num)
        try:
            charge_in = OutboundOrderChargeIn.objects.get(customer_order_num__order_num=item.order_num,
                                                          currency_type=item.origin_currency,
                                                          charge=item.charge_name, customer=item.customer,
                                                          charge_total=item.origin_amount, del_flag=False)
            wss.cell(start_row, start_col + 12, charge_in.charge_rate)
            wss.cell(start_row, start_col + 13, charge_in.charge_count)
            wss.cell(start_row, start_col + 14, charge_in.charge.name)
            wss.cell(start_row, start_col + 15, charge_in.account_charge)
            wss.cell(start_row, start_col + 17, charge_in.remark)
        except ClearanceChargeIn.DoesNotExist:
            wss.cell(start_row, start_col + 12, '找不到对应明细')
            wss.cell(start_row, start_col + 13, '找不到对应明细')
            wss.cell(start_row, start_col + 14, '找不到对应明细')
            wss.cell(start_row, start_col + 15, '找不到对应明细')
            wss.cell(start_row, start_col + 17, '')
        except Exception as e:
            logger.info(f'order_num {item.order_num} 出库单收入明细获取对应明细时异常{e}')
            raise e
    elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_INBOUND_ORDER) and not item.is_adjust:
        wss.cell(start_row, start_col + 9, customer_order.carton)
        wss.cell(start_row, start_col + 10, customer_order.weight)
        try:
            charge_in = InboundOrderChargeIn.objects.get(customer_order_num__order_num=item.order_num,
                                                         currency_type=item.origin_currency,
                                                         charge=item.charge_name, customer=item.customer,
                                                         charge_total=item.origin_amount, del_flag=False)
            wss.cell(start_row, start_col + 12, charge_in.charge_rate)
            wss.cell(start_row, start_col + 13, charge_in.charge_count)
            wss.cell(start_row, start_col + 14, charge_in.charge.name)
            wss.cell(start_row, start_col + 15, charge_in.account_charge)
            wss.cell(start_row, start_col + 17, charge_in.remark)
        except ClearanceChargeIn.DoesNotExist:
            wss.cell(start_row, start_col + 12, '找不到对应明细')
            wss.cell(start_row, start_col + 13, '找不到对应明细')
            wss.cell(start_row, start_col + 14, '找不到对应明细')
            wss.cell(start_row, start_col + 15, '找不到对应明细')
            wss.cell(start_row, start_col + 17, '')
        except Exception as e:
            logger.info(f'order_num {item.order_num} 入库单收入明细获取对应明细时异常{e}')
            raise e
    elif item.order_num.startswith('VAS'):
        logger.info(f'进来了21')
        if remark:
            wss.cell(start_row, start_col + 18, customer_order.remark)
        else:
            wss.cell(start_row, start_col + 17, customer_order.remark)
        wss.cell(start_row, start_col + 1, customer_order.business_order_num)
        try:
            charge_in = VasOrderChargeIn.objects.get(vas_order_num__vas_order_num=item.order_num,
                                                         currency_type=item.origin_currency,
                                                         charge=item.charge_name, customer=item.customer,
                                                         charge_total=item.origin_amount, del_flag=False)
            wss.cell(start_row, start_col + 12, charge_in.charge_rate)
            wss.cell(start_row, start_col + 13, charge_in.charge_count)
            wss.cell(start_row, start_col + 14, charge_in.charge.name)
            wss.cell(start_row, start_col + 15, charge_in.account_charge)
        except VasOrderChargeIn.DoesNotExist:
            wss.cell(start_row, start_col + 12, '找不到对应明细')
            wss.cell(start_row, start_col + 13, '找不到对应明细')
            wss.cell(start_row, start_col + 14, '找不到对应明细')
            wss.cell(start_row, start_col + 15, '找不到对应明细')
        except Exception as e:
            logger.info(f'order_num {item.order_num} 增值服务单收入明细获取对应明细时异常{e}')
            raise e
    elif item.order_num.startswith(settings.SYSTEM_ORDER_MARK + 'RETURN') and not item.is_adjust:
        logger.info(f'进来了22')
        try:
            charge_in = ReturnOrderChargeIn.objects.get(customer_order_num__order_num=item.order_num,
                                                         currency_type=item.origin_currency,
                                                         charge=item.charge_name, customer=item.customer,
                                                         charge_total=item.origin_amount, del_flag=False)
            wss.cell(start_row, start_col + 12, charge_in.charge_rate)
            wss.cell(start_row, start_col + 13, charge_in.charge_count)
            wss.cell(start_row, start_col + 14, charge_in.charge.name)
            wss.cell(start_row, start_col + 15, charge_in.account_charge)
            wss.cell(start_row, start_col + 17, charge_in.remark)
        except ReturnOrderChargeIn.DoesNotExist:
            wss.cell(start_row, start_col + 12, '找不到对应明细')
            wss.cell(start_row, start_col + 13, '找不到对应明细')
            wss.cell(start_row, start_col + 14, '找不到对应明细')
            wss.cell(start_row, start_col + 15, '找不到对应明细')
            wss.cell(start_row, start_col + 17, '')
        except Exception as e:
            logger.info(f'order_num {item.order_num} 退货单收入明细获取对应明细时异常{e}')
            raise e


    # 设置单元格居中
    wss['A' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['B' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['C' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['D' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['E' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['F' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['G' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['H' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['I' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['J' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['K' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['L' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['M' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['N' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['O' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    wss['P' + str(start_row)].alignment = Alignment(horizontal="center", vertical="center")
    start_row += 1
    return start_row


# # 周结自动开具发票
# @app.task(bind=True, base=QueueOnce)
# def handler_create_invoice_week(self):
#     logger.info('--------------start handler_create_invoice_week--------------')
#     if settings.DOMAIN_URL == 'manage.zhengfx.com':
#         now = datetime.datetime.now()
#         last_week_start = (now - timedelta(days=now.weekday() + 7)).strftime('%Y-%m-%d')
#         last_week_end = (now - timedelta(days=now.weekday() + 1)).strftime('%Y-%m-%d')
#
#         last_week_debits = Debit.objects.filter(debit_date__range=[last_week_start, last_week_end], del_flag=False,
#                                                 product_code__isnull=False, customer__debit_date='1',
#                                                 is_invoiced=False)
#
#         group_week_debits = last_week_debits.values_list('customer', 'currency', 'product_code').annotate(
#             total=Sum('amount'))
#         for item in group_week_debits:
#             # 获取上周该客户该币种该产品的账单
#             queryset = last_week_debits.filter(customer=item[0], currency=item[1], product_code=item[2])
#             invoice = Invoice()
#             invoice.amount = item[3] or 0
#             # 自动设置付款金额和币种
#             invoice.pay_amount = invoice.amount
#             invoice.balance = invoice.amount
#             invoice.pay_balance = invoice.amount
#             invoice.customer = Company.objects.get(id=item[0])
#             invoice.save()
#             invoice.invoice_num = 'IV' + create_order_num(invoice.id)
#             invoice.create_by = UserProfile.objects.get(id=1)
#             invoice.currency = item[1]
#             # 自动设置付款金额和币种
#             invoice.pay_currency = item[1]
#             invoice.pay_rate = 1
#             if not settings.MULTI_CURRENCY:
#                 # 单币种自动设置核销币种核销金额核销汇率
#                 invoice.pay_currency = settings.CURRENT_CURRENCY
#                 invoice.pay_amount = item[3] or 0
#                 invoice.pay_balance = invoice.pay_amount
#                 invoice.pay_rate = 1
#             # 账单id列表下所有的明细
#             account_receivable = AccountReceivable.objects.filter(debit_num__in=[x.id for x in queryset],
#                                                                   del_flag=False).order_by('account_time')
#             invoice.debit_time_range = account_receivable.last().account_time.strftime(
#                 '%Y%m%d') + '-' + account_receivable.first().account_time.strftime(
#                 '%Y%m%d')
#             # 设置产品编码
#             invoice.product_code = item[2]
#             invoice.save()
#             queryset.update(invoice_id=invoice.id, is_invoiced=True)
#     logger.info('------------------end handler_create_invoice_week-------------------')
#
#
# # 月结自动开具发票
# @app.task(bind=True, base=QueueOnce)
# def handler_create_invoice_month(self):
#     logger.info('--------------start handler_create_invoice_month--------------')
#     if settings.DOMAIN_URL == 'manage.zhengfx.com':
#         fist_day = datetime.date(datetime.date.today().year, datetime.date.today().month - 1, 1)
#         last_day = datetime.date(datetime.date.today().year, datetime.date.today().month, 1) - datetime.timedelta(1)
#         last_month_debits = Debit.objects.filter(debit_date__range=[fist_day, last_day], del_flag=False,
#                                                  product_code__isnull=False, customer__debit_date='2',
#                                                  is_invoiced=False)
#
#         group_month_debits = last_month_debits.values_list('customer', 'currency', 'product_code').annotate(
#             total=Sum('amount'))
#         for item in group_month_debits:
#             # 获取上周该客户该币种该产品的账单
#             queryset = last_month_debits.filter(customer=item[0], currency=item[1], product_code=item[2])
#             invoice = Invoice()
#             invoice.amount = item[3] or 0
#             # 自动设置付款金额和币种
#             invoice.pay_amount = invoice.amount
#             invoice.balance = invoice.amount
#             invoice.pay_balance = invoice.amount
#             invoice.customer = Company.objects.get(id=item[0])
#             invoice.save()
#             invoice.invoice_num = 'IV' + create_order_num(invoice.id)
#             invoice.create_by = UserProfile.objects.get(id=1)
#             invoice.currency = item[1]
#             # 自动设置付款金额和币种
#             invoice.pay_currency = item[1]
#             invoice.pay_rate = 1
#             if not settings.MULTI_CURRENCY:
#                 # 单币种自动设置核销币种核销金额核销汇率
#                 invoice.pay_currency = settings.CURRENT_CURRENCY
#                 invoice.pay_amount = item[3] or 0
#                 invoice.pay_balance = invoice.pay_amount
#                 invoice.pay_rate = 1
#             # 账单id列表下所有的明细
#             account_receivable = AccountReceivable.objects.filter(debit_num__in=[x.id for x in queryset],
#                                                                   del_flag=False).order_by('account_time')
#             invoice.debit_time_range = account_receivable.last().account_time.strftime(
#                 '%Y%m%d') + '-' + account_receivable.first().account_time.strftime(
#                 '%Y%m%d')
#             # 设置产品编码
#             invoice.product_code = item[2]
#             invoice.save()
#             queryset.update(invoice_id=invoice.id, is_invoiced=True)
#     logger.info('------------------end handler_create_invoice_month-------------------')


# QueueOnce是利用redis分布式锁，防止同一重复执行
# @app.task(base=QueueOnce,once={'graceful': True})
# def testCelery(arg):
#     logger.info("------------->hi---->")
#     time.sleep(20)
#     logger.info(arg)

# 更新时效
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_clearance_aging(self):
    logger.info('--------------start handler_clearance_aging--------------')
    # 获取未统计完成的时效的进口报关单
    queryset = Clearance.objects.filter(aging_status='0', actual_arrivals_date__isnull=False, del_flag=False)
    for item in queryset:
        if item.trans_status == 'HO':
            # 移交派送了，用派送日期计算
            item.aging_status = '1'
            diff = calc_day_diff(str(item.actual_arrivals_date), str(item.delivery_date))
        else:
            # 未派送，用当前日期计算
            diff = calc_day_diff(str(item.actual_arrivals_date))
        item.aging = diff if diff >= 0 else 0
        item.save()
    logger.info('--------------end handler_clearance_aging--------------')


# 订单导入校验产品
def order_import_verification_product_name(user, product_name=None, product_code=None):
    if not product_name and not product_code:
        raise ParamError('没有产品名称或产品编码', ErrorCode.PARAM_ERROR)
    if product_name:
        product = Product.objects.filter(name=product_name, del_flag=False).last()
        if not product:
            raise ParamError(f'系统中找不到此产品名称『{product_name}』', ErrorCode.PARAM_ERROR)
    else:
        product = Product.objects.filter(code=product_code, del_flag=False).last()
        if not product:
            raise ParamError(f'系统中找不到此产品编码『{product_code}』', ErrorCode.PARAM_ERROR)
    if product.status == 'OFF':
        raise ParamError(f'此产品已被停用, 产品: {product}', ErrorCode.PARAM_ERROR)
    if not user.is_staff and not product.is_open:
        raise ParamError(f'此产品没有对外开放, 产品: {product}', ErrorCode.PARAM_ERROR)
    return product


def order_import_verification_is_clearance(is_clearance):
    if is_clearance not in ['是', '否']:
        raise ParamError('是否报关为必填项', ErrorCode.PARAM_ERROR)


def verification_send_post_code(post_code):
    if not post_code:
        raise ParamError('发件人为必填项', ErrorCode.PARAM_ERROR)


# 公共函数: 异步处理上传文件(异步导入文件)(订单异步任务)
@app.task(bind=True, base=QueueOnce, max_retries=0, once={'graceful': True, 'timeout': 60 * 10})
def common_upload_order_handle(self, upload_task_id=None):
    logger.info(f'common_upload_order_handle start: {upload_task_id}')
    
    if upload_task_id:
        upload_task_queryset = OrderSyncUploadTask.objects.filter(id=upload_task_id, del_flag=False)
    else:
        upload_task_queryset = OrderSyncUploadTask.objects.filter(status='Waiting', del_flag=False)
    
    for upload_task in upload_task_queryset:
        logger.info(f'common_upload_order_handle start, taskId: {upload_task.id}, file_name: {upload_task.file_name}, '
                    f'user: {upload_task.create_by}, count: {upload_task_queryset.count()}')

        key = f'upload_order_handle_{upload_task.id}'
        cache_upload_task_id = cache.get(key)
        if cache_upload_task_id:
            continue
        else:
            cache.set(key, "1", 600)

        start_time = datetime.now()
        try:
            if upload_task.task_type == 'UploadOrderExcel':
                if upload_task.order_type == 'PC':
                    parcel_customer_order_upload_handle(upload_task.id)
                elif upload_task.order_type == 'CPC':
                    parcel_customer_order_upload_handle_result(upload_task.id)
                elif upload_task.order_type == 'TR' and upload_task.goods_type == 'COM':
                    # fba_customer_order_upload_handle(upload_task.id)
                    fba_customer_order_upload_handle_async.apply_async(
                        args=[upload_task.id],
                        queue=f'label_queue_10',
                        routing_key=f'label_queue_10'
                    )
                    continue
                elif upload_task.order_type == 'TR' and upload_task.goods_type == 'SINGLE':
                    # fba_customer_order_single_upload_handle(upload_task.id)
                    fba_customer_order_upload_handle_async.apply_async(
                        args=[upload_task.id],
                        queue=f'label_queue_10',
                        routing_key=f'label_queue_10'
                    )
                    continue
                elif upload_task.order_type == 'TR' and upload_task.goods_type == 'MULTI':
                    fba_single_multi_orders_upload_handle(upload_task.id)
                elif upload_task.order_type == 'TR' and upload_task.goods_type == 'ZRH':
                    # fba_customer_order_upload_handle_zrh(upload_task.id)
                    fba_customer_order_upload_handle_async.apply_async(
                        args=[upload_task.id],
                        queue=f'label_queue_10',
                        routing_key=f'label_queue_10'
                    )
                    continue
                else:
                    raise Exception(f'找不到对应的任务, 单据类型: {upload_task.order_type}, '
                                    f'任务类型: {upload_task.task_type}, 商品类型: {upload_task.goods_type}')
            elif upload_task.task_type == 'UploadParcelVoucher':
                if upload_task.order_type == 'TR':
                    analysis_tr_voucher_handle(upload_task.id)
                else:
                    raise Exception(f'找不到对应的任务, 单据类型: {upload_task.order_type}, '
                                    f'任务类型: {upload_task.task_type}, 商品类型: {upload_task.goods_type}')
            elif upload_task.task_type == 'BatchWeighting':
                if upload_task.order_type == 'BP':
                    batch_big_parcel_weighting(upload_task.id)
                elif upload_task.order_type == 'PC':
                    batch_small_parcel_weighting(upload_task.id)
                else:
                    raise Exception(f'找不到对应的任务, 单据类型: {upload_task.order_type}, '
                                    f'任务类型: {upload_task.task_type}, 商品类型: {upload_task.goods_type}')
            # 导入fbm订单
            elif upload_task.task_type == 'UploadFBMOrderExcel':
                # fbm_customer_order_upload_handle(upload_task.id)
                fba_customer_order_upload_handle_async.apply_async(
                    args=[upload_task.id],
                    queue=f'label_queue_10',
                    routing_key=f'label_queue_10'
                )
                continue
            elif upload_task.task_type == 'ChangedUploadOrderExcel':
                if upload_task.order_type == 'PC':
                    changed_parcel_customer_order_upload_handle(upload_task.id)
                else:
                    raise Exception(f'找不到对应的任务, 单据类型: {upload_task.order_type}, '
                                    f'任务类型: {upload_task.task_type}, 商品类型: {upload_task.goods_type}')
            else:
                raise Exception(f'找不到对应的任务, 单据类型: {upload_task.order_type}, '
                                f'任务类型: {upload_task.task_type}, 商品类型: {upload_task.goods_type}')
            # 再重新获取一次任务, 避免出现代码鬼打墙(数据未保存)
            upload_task_sec = OrderSyncUploadTask.objects.get(id=upload_task.id)
            upload_task_sec.status = 'Success'
            upload_task_sec.save()
        except Exception as e:
            upload_task_sec = OrderSyncUploadTask.objects.get(id=upload_task.id)
            logger.info(traceback.format_exc())
            upload_task_sec.task_desc = str(e)[:2000]
            upload_task_sec.status = 'Failure'
            upload_task_sec.save()
        finally:
            upload_task_sec = OrderSyncUploadTask.objects.get(id=upload_task.id)
            datetime_now = datetime.now()
            upload_task_sec.execution_time = datetime_now - start_time
            upload_task_sec.update_date = datetime_now
            upload_task_sec.save()

    logger.info(f'common_upload_order_handle end')


@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True, 'keys': ['upload_task_id'],
                                                          'timeout': 600})
@transaction.atomic
def fba_customer_order_upload_handle_async(self, upload_task_id):
    logger.info(f'fba_customer_order_upload_handle_async start: {upload_task_id}')
    start_time = datetime.now()
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    try:
        if upload_task.task_type == 'UploadOrderExcel':
            if upload_task.order_type == 'TR' and upload_task.goods_type == 'COM':
                fba_customer_order_upload_handle(upload_task_id)
            elif upload_task.order_type == 'TR' and upload_task.goods_type == 'SINGLE':
                fba_customer_order_single_upload_handle(upload_task_id)
            elif upload_task.order_type == 'TR' and upload_task.goods_type == 'ZRH':
                fba_customer_order_upload_handle_zrh(upload_task_id)
        elif upload_task.task_type == 'UploadFBMOrderExcel':
            fbm_customer_order_upload_handle(upload_task.id)
        # 再重新获取一次任务, 避免出现代码鬼打墙(数据未保存)
        upload_task_sec = OrderSyncUploadTask.objects.get(id=upload_task.id)
        upload_task_sec.status = 'Success'
        upload_task_sec.save()
    except Exception as e:
        upload_task_sec = OrderSyncUploadTask.objects.get(id=upload_task.id)
        logger.info(traceback.format_exc())
        upload_task_sec.task_desc = str(e)[:2000]
        upload_task_sec.status = 'Failure'
        upload_task_sec.save()
    finally:
        upload_task_sec = OrderSyncUploadTask.objects.get(id=upload_task.id)
        datetime_now = datetime.now()
        upload_task_sec.execution_time = datetime_now - start_time
        upload_task_sec.update_date = datetime_now
        upload_task_sec.save()

    logger.info(f'fba_customer_order_upload_handle_async end: {upload_task_id}, {upload_task_sec.status}')


# 异步处理导入fba订单excel(fba订单导入)(异步导入订单)(异步上传fba订单)(main)
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True, 'keys': ['upload_task_id'],
                                                          'timeout': 600})
@transaction.atomic
def fba_customer_order_upload_handle(self, upload_task_id, overwrite=False):
    """
    :param self:
    :param upload_task_id:
    :param overwrite: 是否覆盖更新，用于详情页导入更新订单；默认False，保持原逻辑。
    :return:
    """
    logger.info(f'fba_customer_order_upload_handle start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    # excel_path = "D:\\xlsx_SsZEXBt.xlsx"
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(excel_path)
    upload_task.save()
    user = upload_task.create_by

    logger.info(f'fba_customer_order_upload_handle开始读文件: {upload_task.id}')
    workbook = openpyxl.load_workbook(excel_path, data_only=True)
    logger.info(f'fba_customer_order_upload_handle成功读文件: {upload_task.id}')
    # 获取第一张表
    table = workbook.worksheets[0]
    # 行数
    line_number = table.max_row

    # ref_num = get_excel_cell(3, 'b', table)
    # upload_fba_order_key = f'upload_fba_order_{ref_num}'
    # upload_fba_order = cache.get(upload_fba_order_key)
    # if upload_fba_order:
    #     raise ParamError(f'客户订单号正在上传: {ref_num}', ErrorCode.PARAM_ERROR)
    # else:
    #     cache.set(upload_fba_order_key, "1", 600)

    try:
        # 校验是否报关填写
        is_clearance = get_excel_cell(9, 'i', table)
        order_import_verification_is_clearance(is_clearance)

        # 获取产品
        product_name = get_excel_cell(9, 'b', table)
        product = order_import_verification_product_name(user, product_name=product_name)

        import_service = FbaOrderImportService(table, user)
        company = import_service.get_order_customer()

        warehouse_address = get_excel_cell(4, 'i', table)
        private_address = get_excel_cell(6, 'i', table)
        buyer_address = import_service.get_order_receiver(warehouse_address, private_address)
        if warehouse_address:
            receiver = buyer_address
        else:
            receiver = None

        shipper_address = import_service.get_order_shipper(product)

        # 检查是否是更新现有订单
        existing_order = None
        if upload_task.order_num:
            try:
                existing_order = CustomerOrder.objects.get(order_num=upload_task.order_num, del_flag=False)
                # 清理现有数据前先获取订单信息
                logger.info(f'Updating existing order: {existing_order.order_num}')
            except CustomerOrder.DoesNotExist:
                logger.warning(f'Order with number {upload_task.order_num} not found, creating new order')

        # 创建或更新订单
        # 如果是更新订单且需要清理数据，在创建新内容前清理
        if existing_order and overwrite:
            clean_fba_order_before_import(existing_order, user, overwrite=overwrite)
            customer_order = import_service.update_order(existing_order, company, product, receiver, buyer_address,
                                                         shipper_address, upload_task)
        else:
            customer_order = import_service.create_order(company, product, receiver, buyer_address,
                                                         shipper_address, upload_task)

        upload_task.order_num = customer_order.order_num
        upload_task.save()

        with open(excel_path, 'rb') as file:
            excel_content = file.read()
        # 使用 ContentFile 将文件内容转换为 Django 可识别的文件对象
        excel_file = ContentFile(excel_content, name=upload_task.file_name)
        # 保存到附件中
        OrderAttachment.objects.create(name=upload_task.file_name, url=excel_file, customerOrder=customer_order,
                                       **get_update_params_by_user(user, True))

        # 获取表头
        header_map = {
            # 包裹
            '箱号/FBA编号': ['parcel_num', 'str'],
            'Shipment Id': ['shipment_id', 'str'],
            'Reference Id': ['reference_id', 'str'],
            '材积CM(长*宽*高)': ['parcel_size', 'str'],
            '重量KGS': ['parcel_weight', 'Decimal'],

            # 商品
            '海关编码HSCODE': ['customs_code', 'str'],
            'sku': ['item_code', 'str'],
            '中文品名': ['declared_nameCN', 'str'],
            '英文品名': ['declared_nameEN', 'str'],
            '数量': ['item_qty', 'int'],
            '单价(USD)': ['declared_price', 'Decimal'],
            '品牌': ['brand', 'str'],
            '规格型号': ['model', 'str'],
            '材质': ['texture', 'str'],
            '用途': ['use', 'str'],
            '产品图片': ['item_picture', 'Img'],
        }

        # 读取第11行的表头数据
        header_list = [cell.value.replace('\n', '') for cell in table[11] if cell.value]
        # 获取表头每一列对应的列索引
        header_index = {header_list[i]: i + 1 for i in range(len(header_list))}

        diff_header_excel = set(header_index.keys()) - set(header_map.keys())
        if diff_header_excel:
            if '箱数' in diff_header_excel:
                raise ParamError(f'普通订单表格中有多余的"箱数"字段, 请确定导入的是普通订单', ErrorCode.PARAM_ERROR)
            else:
                raise ParamError(f'"{", ".join(diff_header_excel)}"字段不是表格内的规范字段, 请下载使用标准模板导入', ErrorCode.PARAM_ERROR)
        diff_header_standard = set(header_map.keys()) - set(header_index.keys())
        if diff_header_standard:
            raise ParamError(f'"{", ".join(diff_header_standard)}"字段不存在, 请下载使用标准模板导入', ErrorCode.PARAM_ERROR)

        excel_header_map = {}
        for column, index in header_index.items():
            if column == '箱数':
                raise ParamError(f'普通订单表格中有多余的箱数字段, 请确定导入的是普通订单, 不是单一商品订单',
                                 ErrorCode.PARAM_ERROR)
            if column in header_map:
                excel_header_map[header_map[column][0]] = [index, header_map[column][1]]

        get_cell = GetExcelCellData(excel_header_map, table)

        # 商品数据的第一行(从1开始)
        first_item_row = 12
        # 对包裹和商品同时重复做判断
        # parcel_item_df = {}
        parcel_item_df = pd.DataFrame(columns=['parcel', 'parcel_item'], dtype=object)
        parcel_item_ctn = 0

        # 判断文件标题箱数（xxx）和导入的行数（xxx）不匹配
        # 箱数
        box_num = get_excel_cell(3, 'f', table)
        parcel_num_arr = []
        for row in range(first_item_row, line_number + 1):
            parcel_num = get_cell.get_cell_by_header(row, 'parcel_num')
            print('parcel_num-->', parcel_num)
            if parcel_num:
                parcel_num_arr.append(str(parcel_num).strip())
            else:
                break

        parcel_num_arr = list(set(parcel_num_arr))
        if box_num != len(parcel_num_arr):
            raise ParamError(f'文件标题箱数（{box_num}）和导入的箱数（{len(parcel_num_arr)}）不匹配', ErrorCode.PARAM_ERROR)

        # 包裹新增
        # 获取工作表上的所有图片
        # all_images = table._images
        all_images = []
        with zipfile.ZipFile(excel_path, 'r') as z:
            # 遍历 XL/Media 和 XL/Drawings 目录中的所有文件
            print('z.namelist()-->', z.namelist())
            for file in z.namelist():
                if file.startswith('xl/media/') and file.lstrip('xl/media/'):
                    # 读取文件内容到内存中
                    # with z.open(file) as f:
                    f = z.open(file)
                    all_images.append(f)

        parcel_num_original_map = {}
        reference_ids = set()
        shipment_ids = set()
        # 遍历商品行数据
        for row in range(first_item_row, line_number + 1):
            parcel_num_original = get_cell.get_cell_by_header(row, 'parcel_num')
            if not parcel_num_original:
                # continue
                break
            # 如果是仓库地址, 则需要校验包裹号
            if receiver is not None:
                sys_parcel_num = create_sys_parcel_num(customer_order)
                parcel_num = parcel_num_original
                judge_parcel_num_rule(parcel_num, customer_order, raise_error=True)
            # 如果是私人地址, 则将系统包裹号作为包裹号
            else:
                # 相同的初始包裹, 则不能直接生成包裹号, 那样不支持一个包裹号对应多个商品
                if parcel_num_original in parcel_num_original_map:
                    sys_parcel_num = parcel_num_original_map[parcel_num_original]
                else:
                    sys_parcel_num = create_sys_parcel_num(customer_order)
                    parcel_num_original_map[parcel_num_original] = sys_parcel_num
                parcel_num = sys_parcel_num

            # 壹起飞校验reference_id必填
            if settings.SYSTEM_ORDER_MARK in ['YQF', 'FX']:
                reference_id = get_cell.get_cell_by_header(row, 'reference_id',
                                                           allow_null=False).replace(" ", "").replace("\n", "")
            else:
                reference_id = get_cell.get_cell_by_header(row, 'reference_id').replace(" ", "").replace("\n", "")
            shipment_id = get_cell.get_cell_by_header(row, 'shipment_id').replace(" ", "").replace("\n", "")
            if reference_id:
                reference_ids.add(reference_id)
            if shipment_id:
                shipment_ids.add(shipment_id)

            parcel = import_service.create_parcel(get_cell, row, parcel_num, sys_parcel_num, customer_order,
                                                  reference_id, shipment_id)

            parcel_item = import_service.create_parcel_item(get_cell, row, parcel, parcel_item_df)

            # 保存商品图片
            print('all_images0-->', all_images)
            all_image_ids = []
            image_id = get_cell.get_cell_by_header(row, 'item_picture')
            print('image_id0-->', image_id)
            if image_id:
                if image_id not in all_image_ids:
                    all_image_ids.append(image_id)
                img_index = all_image_ids.index(image_id)
                if img_index < len(all_images) - 1:
                    if settings.SYSTEM_ORDER_MARK == 'YQF':
                        img_data = all_images[img_index]
                    else:
                        # 第一张图是mz的logo, 从第二张图开始取
                        img_data = all_images[img_index + 1]
                    image_name = f"{parcel_num}_{row}.jpg"
                    # django_file = File(current_image, name=file.split('/')[-1])
                    django_file = File(img_data, name=image_name)
                    parcel_item.item_picture.save(django_file.name, django_file, save=True)
                    parcel_item.save()

            """
            first_image_index = 0
            for image in all_images:
                if image.anchor._from.row + 1 == row:
                    break
                first_image_index += 1
            print('first_image_index-->', first_image_index)
            image_row = row - first_item_row + first_image_index
            target_image = all_images[image_row] if image_row < len(all_images) else None
            if target_image:
                img_data = target_image._data()
                image_name = f"{parcel_num}_{row}.jpg"
                parcel_item.item_picture.save(image_name, ContentFile(img_data))
                parcel_item.save()
            """

            parcel_item_ctn += 1
        if parcel_item_ctn == 0:
            raise ParamError(f'未导入任何数据, 请检查是否有数据或是否与已有包裹号重复', ErrorCode.PARAM_ERROR)
        # raise Exception('668')

        summary_predict_parcels_data(customer_order)

        check_customer_order_shipment_id(customer_order)
        # 生成订单的reference_id
        customer_order.reference_id = create_order_reference_id(reference_ids)
        customer_order.shipment_id = create_order_reference_id(shipment_ids)
        customer_order.save()
        # 判断非fba仓库/偏远地区
        common_judge_fba_and_remote(customer_order)
        workbook.close()
    except Exception as e:
        # cache.delete(upload_fba_order_key)
        raise ParamError(e)

    # if os.path.exists(excel_path):
    #     os.remove(excel_path)
    logger.info(f'fba_customer_order_upload_handle end: {upload_task.id}')


# zrh异步处理导入fba订单excel(fba订单导入)(异步导入订单)(异步上传fba订单)(main)
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True, 'keys': ['upload_task_id'],
                                                          'timeout': 600})
@transaction.atomic
def fba_customer_order_upload_handle_zrh(self, upload_task_id, overwrite=False):
    """
    :param self:
    :param upload_task_id:
    :param overwrite: 是否覆盖更新，用于详情页导入更新订单；默认False，保持原逻辑。
    :return:
    """
    logger.info(f'fba_customer_order_upload_handle_zrh start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    # excel_path = "D:\\xlsx_SsZEXBt.xlsx"
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(excel_path)
    upload_task.save()
    user = upload_task.create_by

    logger.info(f'fba_customer_order_upload_handle_zrh开始读文件: {upload_task.id}')
    workbook = openpyxl.load_workbook(excel_path, data_only=True)
    logger.info(f'fba_customer_order_upload_handle_zrh成功读文件: {upload_task.id}')
    # 获取第一张表
    table = workbook.worksheets[0]
    # 行数
    line_number = table.max_row

    ref_num = get_excel_cell(3, 'b', table)
    upload_fba_order_key = f'upload_fba_order_{ref_num}'
    upload_fba_order = cache.get(upload_fba_order_key)
    if upload_fba_order:
        raise ParamError(f'客户订单号正在上传: {ref_num}', ErrorCode.PARAM_ERROR)
    else:
        cache.set(upload_fba_order_key, "1", 600)

    try:
        # 校验是否报关填写
        is_clearance = get_excel_cell(9, 'k', table)
        order_import_verification_is_clearance(is_clearance)

        # 获取产品
        product_name = get_excel_cell(9, 'b', table)
        product = order_import_verification_product_name(user, product_name=product_name)

        import_service = FbaOrderImportService(table, user)
        company = import_service.get_order_customer()

        warehouse_address = get_excel_cell(4, 'k', table)
        private_address = get_excel_cell(6, 'k', table)
        buyer_address = import_service.get_order_receiver(warehouse_address, private_address)
        if warehouse_address:
            receiver = buyer_address
        else:
            receiver = None

        shipper_address = import_service.get_order_shipper(product)

        # 检查是否是更新现有订单
        existing_order = None
        if upload_task.order_num:
            try:
                existing_order = CustomerOrder.objects.get(order_num=upload_task.order_num, del_flag=False)
                # 清理现有数据前先获取订单信息
                logger.info(f'Updating existing order: {existing_order.order_num}')
            except CustomerOrder.DoesNotExist:
                logger.warning(f'Order with number {upload_task.order_num} not found, creating new order')

        # 创建或更新订单
        # 如果是更新订单且需要清理数据，在创建新内容前清理
        if existing_order and overwrite:
            clean_fba_order_before_import(existing_order, user, overwrite=overwrite)
            customer_order = import_service.update_order_zrh(existing_order, company, product, receiver, buyer_address,
                                                         shipper_address, upload_task)
        else:
            customer_order = import_service.create_order_zrh(company, product, receiver, buyer_address,
                                                         shipper_address, upload_task)

        upload_task.order_num = customer_order.order_num
        upload_task.save()

        with open(excel_path, 'rb') as file:
            excel_content = file.read()
        # 使用 ContentFile 将文件内容转换为 Django 可识别的文件对象
        excel_file = ContentFile(excel_content, name=upload_task.file_name)
        # 保存到附件中
        OrderAttachment.objects.create(name=upload_task.file_name, url=excel_file, customerOrder=customer_order,
                                       **get_update_params_by_user(user, True))

        # 获取表头
        header_map = {
            # 包裹
            '箱号/FBA编号': ['parcel_num', 'str'],
            'Shipment Id': ['shipment_id', 'str'],
            'Reference Id': ['reference_id', 'str'],
            '材积CM(长*宽*高)': ['parcel_size', 'str'],
            '重量KGS': ['parcel_weight', 'Decimal'],

            # 商品
            '海关编码HSCODE': ['customs_code', 'str'],
            'sku': ['item_code', 'str'],
            '产品毛重': ['item_weight', 'str'],
            '产品尺寸': ['item_size', 'str'],
            '中文品名': ['declared_nameCN', 'str'],
            '英文品名': ['declared_nameEN', 'str'],
            '数量': ['item_qty', 'int'],
            '单价(USD)': ['declared_price', 'Decimal'],
            '品牌': ['brand', 'str'],
            '规格型号': ['model', 'str'],
            '材质': ['texture', 'str'],
            '用途': ['use', 'str'],
            '销售链接*(FBA自税/递延必填）': ['sku_url', 'str'],
            '产品图片': ['item_picture', 'Img'],
        }

        # 读取第11行的表头数据
        header_list = [cell.value.replace('\n', '') for cell in table[11] if cell.value]
        # 获取表头每一列对应的列索引
        header_index = {header_list[i]: i + 1 for i in range(len(header_list))}

        diff_header_excel = set(header_index.keys()) - set(header_map.keys())
        if diff_header_excel:
            if '箱数' in diff_header_excel:
                raise ParamError(f'普通订单表格中有多余的"箱数"字段, 请确定导入的是普通订单', ErrorCode.PARAM_ERROR)
            else:
                raise ParamError(f'"{", ".join(diff_header_excel)}"字段不是表格内的规范字段, 请下载使用标准模板导入', ErrorCode.PARAM_ERROR)
        diff_header_standard = set(header_map.keys()) - set(header_index.keys())
        if diff_header_standard:
            raise ParamError(f'"{", ".join(diff_header_standard)}"字段不存在, 请下载使用标准模板导入', ErrorCode.PARAM_ERROR)

        excel_header_map = {}
        for column, index in header_index.items():
            if column == '箱数':
                raise ParamError(f'普通订单表格中有多余的箱数字段, 请确定导入的是普通订单, 不是单一商品订单',
                                 ErrorCode.PARAM_ERROR)
            if column in header_map:
                excel_header_map[header_map[column][0]] = [index, header_map[column][1]]

        get_cell = GetExcelCellData(excel_header_map, table)

        # 商品数据的第一行(从1开始)
        first_item_row = 12
        # 对包裹和商品同时重复做判断
        # parcel_item_df = {}
        parcel_item_df = pd.DataFrame(columns=['parcel', 'parcel_item'], dtype=object)
        parcel_item_ctn = 0

        # 判断文件标题箱数（xxx）和导入的行数（xxx）不匹配
        # 箱数
        box_num = get_excel_cell(3, 'f', table)
        parcel_num_arr = []
        for row in range(first_item_row, line_number + 1):
            parcel_num = get_cell.get_cell_by_header(row, 'parcel_num')
            print('parcel_num-->', parcel_num)
            if parcel_num:
                parcel_num_arr.append(str(parcel_num).strip())
            else:
                break

        parcel_num_arr = list(set(parcel_num_arr))
        if box_num != len(parcel_num_arr):
            raise ParamError(f'文件标题箱数（{box_num}）和导入的箱数（{len(parcel_num_arr)}）不匹配', ErrorCode.PARAM_ERROR)

        # 包裹新增
        # 获取工作表上的所有图片
        # all_images = table._images
        all_images = []
        with zipfile.ZipFile(excel_path, 'r') as z:
            # 遍历 XL/Media 和 XL/Drawings 目录中的所有文件
            print('z.namelist()-->', z.namelist())
            for file in z.namelist():
                if file.startswith('xl/media/') and file.lstrip('xl/media/'):
                    # 读取文件内容到内存中
                    # with z.open(file) as f:
                    f = z.open(file)
                    all_images.append(f)

        parcel_num_original_map = {}
        reference_ids = set()
        shipment_ids = set()
        # 遍历商品行数据
        for row in range(first_item_row, line_number + 1):
            parcel_num_original = get_cell.get_cell_by_header(row, 'parcel_num')
            if not parcel_num_original:
                # continue
                break
            # 如果是仓库地址, 则需要校验包裹号
            if receiver is not None:
                sys_parcel_num = create_sys_parcel_num(customer_order)
                parcel_num = parcel_num_original
                judge_parcel_num_rule(parcel_num, customer_order, raise_error=True)
            # 如果是私人地址, 则将系统包裹号作为包裹号
            else:
                # 相同的初始包裹, 则不能直接生成包裹号, 那样不支持一个包裹号对应多个商品
                if parcel_num_original in parcel_num_original_map:
                    sys_parcel_num = parcel_num_original_map[parcel_num_original]
                else:
                    sys_parcel_num = create_sys_parcel_num(customer_order)
                    parcel_num_original_map[parcel_num_original] = sys_parcel_num
                parcel_num = sys_parcel_num

            reference_id = get_cell.get_cell_by_header(row, 'reference_id').replace(" ", "").replace("\n", "")
            shipment_id = get_cell.get_cell_by_header(row, 'shipment_id').replace(" ", "").replace("\n", "")
            if reference_id:
                reference_ids.add(reference_id)
            if shipment_id:
                shipment_ids.add(shipment_id)

            parcel = import_service.create_parcel(get_cell, row, parcel_num, sys_parcel_num, customer_order,
                                                  reference_id, shipment_id)

            parcel_item = import_service.create_parcel_item_zrh(get_cell, row, parcel, parcel_item_df)

            # 保存商品图片
            print('all_images0-->', all_images)
            all_image_ids = []
            image_id = get_cell.get_cell_by_header(row, 'item_picture')
            print('image_id0-->', image_id)
            if image_id:
                if image_id not in all_image_ids:
                    all_image_ids.append(image_id)
                img_index = all_image_ids.index(image_id)
                if img_index < len(all_images) - 1:
                    if settings.SYSTEM_ORDER_MARK == 'YQF':
                        img_data = all_images[img_index]
                    else:
                        # 第一张图是mz的logo, 从第二张图开始取
                        img_data = all_images[img_index + 1]
                    image_name = f"{parcel_num}_{row}.jpg"
                    # django_file = File(current_image, name=file.split('/')[-1])
                    django_file = File(img_data, name=image_name)
                    parcel_item.item_picture.save(django_file.name, django_file, save=True)
                    parcel_item.save()

            """
            first_image_index = 0
            for image in all_images:
                if image.anchor._from.row + 1 == row:
                    break
                first_image_index += 1
            print('first_image_index-->', first_image_index)
            image_row = row - first_item_row + first_image_index
            target_image = all_images[image_row] if image_row < len(all_images) else None
            if target_image:
                img_data = target_image._data()
                image_name = f"{parcel_num}_{row}.jpg"
                parcel_item.item_picture.save(image_name, ContentFile(img_data))
                parcel_item.save()
            """

            parcel_item_ctn += 1
        if parcel_item_ctn == 0:
            raise ParamError(f'未导入任何数据, 请检查是否有数据或是否与已有包裹号重复', ErrorCode.PARAM_ERROR)
        # raise Exception('668')

        summary_predict_parcels_data(customer_order)

        check_customer_order_shipment_id(customer_order)
        # 生成订单的reference_id
        customer_order.reference_id = create_order_reference_id(reference_ids)
        customer_order.shipment_id = create_order_reference_id(shipment_ids)
        customer_order.save()
        # 判断非fba仓库/偏远地区
        common_judge_fba_and_remote(customer_order)
        workbook.close()
    except ParamError as e:
        cache.delete(upload_fba_order_key)
        raise ParamError(e)

    # if os.path.exists(excel_path):
    #     os.remove(excel_path)
    logger.info(f'fba_customer_order_upload_handle end: {upload_task.id}')


@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True, 'keys': ['upload_task_id'],
                                                          'timeout': 600})
def parcel_customer_order_upload_handle_result(self, upload_task_id):
    """异步处理导入小包订单excel(保留每个订单的执行结果)"""
    logger.info(f'parcel_customer_order_upload_handle_result start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(excel_path)
    upload_task.save()
    user = upload_task.create_by
    if not user:
        raise Exception('用户不存在, 请检查')
    is_cms = not user.is_staff

    parcel_order_nums = []
    logger.info(f'parcel_customer_order_upload_handle_result开始读文件: {upload_task.id}')
    # 打开excel
    wb = xlrd_compat.open_workbook(filename=excel_path)
    logger.info(f'parcel_customer_order_upload_handle_result成功读文件: {upload_task.id}')

    # 获取第一张表
    table = wb.sheets()[0]
    nrows = table.nrows  # 行数
    # print('nrows-->', nrows, type(nrows))

    # 客户下标: 是客户小包就不需要填客户编码, 表格会少一列
    if is_cms:
        customer_index = 0
    else:
        customer_index = 1

    customer_title = table.cell_value(0, 1)
    if 'Customer' in customer_title and customer_index == 0:
        raise ParamError(f'模版不正确,客户请使用客户端下载的模版', ErrorCode.PARAM_ERROR)
    if 'Customer' not in customer_title and customer_index == 1:
        raise ParamError(f'模版不正确,缺少客户编码列', ErrorCode.PARAM_ERROR)

    order_list = []
    parcel_no = []
    for index in range(1, nrows):
        try:
            customer_order_num = str(table.cell_value(index, 0)).strip().replace('.0', '')
        except Exception:
            raise ValueError(f'客户单号填写不正确；第{index}行')

        error_msg = ''
        result_status = 'Success'
        try:
            order_data = assemble_params_item(table, index, customer_index, order_list, parcel_no, user)
            upload_parcel_customer_order_item(order_data, user)
        except Exception as e:
            result_status = 'Failure'
            error_msg = f'读取第{index}行异常: {str(e)}'[:499]

        OrderSyncUploadTaskResult.objects.update_or_create(
            task_id=upload_task,
            customer_order_num=customer_order_num,
            defaults={
                'remark': error_msg,
                'status': result_status,
                'update_by': user,
                'update_date': datetime.now()
            }
        )

    upload_task.task_desc = ','.join(parcel_order_nums)[:2000]
    upload_task.save()
    logger.info(f'parcel_customer_order_upload_handle_result end: {upload_task.id}')

# 异步处理导入小包订单excel(客户小包订单/订单小包订单)
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True, 'keys': ['upload_task_id'],
                                                          'timeout': 600})
@transaction.atomic
def changed_parcel_customer_order_upload_handle(self, upload_task_id):
    """用于批量更改小包清关信息"""
    logger.info(f'changed parcel_customer_order_upload_handle start: {upload_task_id}')
    # todo 待实现
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    # user = UserProfile.objects.get(id=upload_task.create_by)
    excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(excel_path)
    upload_task.save()
    user = upload_task.create_by
    if not user:
        raise Exception('用户不存在, 请检查')
    is_cms = not user.is_staff

    parcel_order_nums = []
    logger.info(f'changed parcel_customer_order_upload_handle开始读文件: {upload_task.id}')
    # 打开excel
    wb = xlrd_compat.open_workbook(filename=excel_path)
    logger.info(f'changed parcel_customer_order_upload_handle成功读文件: {upload_task.id}')
    # 获取第一张表
    table = wb.sheets()[0]
    nrows = table.nrows  # 行数
    # print('nrows-->', nrows, type(nrows))

    upload_changed_parcel_customer_order(nrows, table, user)

    upload_task.task_desc = ','.join(parcel_order_nums)[:2000]
    upload_task.save()
    logger.info(f'changed parcel_customer_order_upload_handle end: {upload_task.id}')


# 异步处理导入小包订单excel(客户小包订单/订单小包订单)
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True, 'keys': ['upload_task_id'],
                                                          'timeout': 600})
@transaction.atomic
def parcel_customer_order_upload_handle(self, upload_task_id):
    logger.info(f'parcel_customer_order_upload_handle start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    # user = UserProfile.objects.get(id=upload_task.create_by)
    excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(excel_path)
    upload_task.save()
    user = upload_task.create_by
    if not user:
        raise Exception('用户不存在, 请检查')
    is_cms = not user.is_staff

    parcel_order_nums = []
    logger.info(f'parcel_customer_order_upload_handle开始读文件: {upload_task.id}')
    # 打开excel
    wb = xlrd_compat.open_workbook(filename=excel_path)
    logger.info(f'parcel_customer_order_upload_handle成功读文件: {upload_task.id}')
    # 获取第一张表
    table = wb.sheets()[0]
    nrows = table.nrows  # 行数
    # print('nrows-->', nrows, type(nrows))

    # 客户下标: 是客户小包就不需要填客户编码, 表格会少一列
    if is_cms:
        customer_index = 0
    else:
        customer_index = 1

    upload_parcel_customer_order(customer_index, nrows, table, user)

    upload_task.task_desc = ','.join(parcel_order_nums)[:2000]
    upload_task.save()
    logger.info(f'parcel_customer_order_upload_handle end: {upload_task.id}')


# 异步处理fba导入单一商品订单excel(单一商品订单导入)(main)
@app.task(bind=True, max_retries=1, base=QueueOnce, once={'graceful': True, 'keys': ['upload_task_id'],
                                                          'timeout': 600})
@transaction.atomic
def fba_customer_order_single_upload_handle(self, upload_task_id, overwrite=False):
    """
    :param self:
    :param upload_task_id:
    :param overwrite: 增加覆盖更新的功能
    :return:
    """
    logger.info(f'fba_customer_order_single_upload_handle start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    # excel_path = "D:\\xlsx_SsZEXBt.xlsx"
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(excel_path)
    upload_task.save()
    user = upload_task.create_by
    if not user:
        raise Exception('用户不存在, 请检查')

    # 读取 Excel
    logger.info(f'fba_customer_order_single_upload_handle开始读文件: {upload_task.id}')
    workbook = openpyxl.load_workbook(excel_path, data_only=True)
    logger.info(f'fba_customer_order_single_upload_handle成功读文件: {upload_task.id}')
    # 获取第一张表
    table = workbook.worksheets[0]
    # 行数
    line_number = table.max_row

    # 校验是否报关填写
    is_clearance = get_excel_cell(9, 'i', table)
    order_import_verification_is_clearance(is_clearance)

    # 获取产品
    product_name = get_excel_cell(9, 'b', table)
    product = order_import_verification_product_name(user, product_name=product_name)

    import_service = FbaOrderImportService(table, user)
    company = import_service.get_order_customer(check_permission=False)

    warehouse_address = get_excel_cell(4, 'i', table)
    private_address = get_excel_cell(6, 'i', table)
    buyer_address = import_service.get_order_receiver(warehouse_address, private_address)
    if warehouse_address:
        receiver = buyer_address
    else:
        receiver = None

    shipper_address = import_service.get_order_shipper(product)

    # 检查是否是更新现有订单
    existing_order = None
    if upload_task.order_num:
        try:
            existing_order = CustomerOrder.objects.get(order_num=upload_task.order_num, del_flag=False)
            # 清理现有数据前先获取订单信息
            logger.info(f'Updating existing order: {existing_order.order_num}')
        except CustomerOrder.DoesNotExist:
            logger.warning(f'Order with number {upload_task.order_num} not found, creating new order')

    # 创建或更新订单
    # 如果是更新订单且需要清理数据，在创建新内容前清理
    if existing_order and overwrite:
        clean_fba_order_before_import(existing_order, user, overwrite=overwrite)
        customer_order = import_service.update_order(existing_order, company, product, receiver, buyer_address, shipper_address, upload_task)
    else:
        customer_order = import_service.create_order(company, product, receiver, buyer_address, shipper_address, upload_task)

    upload_task.order_num = customer_order.order_num
    upload_task.save()

    with open(excel_path, 'rb') as file:
        excel_content = file.read()
    # 使用 ContentFile 将文件内容转换为 Django 可识别的文件对象
    excel_file = ContentFile(excel_content, name=upload_task.file_name)
    # 保存到附件中
    OrderAttachment.objects.create(name=upload_task.file_name, url=excel_file, customerOrder=customer_order,
                                   **get_update_params_by_user(user, True))
    # 获取包裹和商品数据
    header_map = {
        # 包裹
        '箱号/FBA编号': ['parcel_num_pre', 'str'],
        'Shipment Id': ['shipment_id', 'str'],
        'Reference Id': ['reference_id', 'str'],
        '箱数': ['parcel_count', 'int'],
        '材积CM(长*宽*高)': ['parcel_size', 'str'],
        '重量KGS': ['parcel_weight', 'Decimal'],

        # 商品
        '海关编码HSCODE': ['customs_code', 'str'],
        'sku': ['item_code', 'str'],
        '中文品名': ['declared_nameCN', 'str'],
        '英文品名': ['declared_nameEN', 'str'],
        '数量': ['item_qty', 'int'],
        '单价(USD)': ['declared_price', 'Decimal'],
        '品牌': ['brand', 'str'],
        '规格型号': ['model', 'str'],
        '材质': ['texture', 'str'],
        '用途': ['use', 'str'],
        '产品图片': ['item_picture', 'Img'],
    }
    # 读取第一行的表头数据
    header_list = [cell.value.replace('\n', '') for cell in table[11] if cell.value]
    # 获取表头每一列对应的列索引
    header_index = {header_list[i]: i + 1 for i in range(len(header_list))}

    diff_header_excel = set(header_index.keys()) - set(header_map.keys())
    if diff_header_excel:
        raise ParamError(f'"{", ".join(diff_header_excel)}"字段不是表格内的规范字段, 请下载使用标准模板导入', ErrorCode.PARAM_ERROR)
    diff_header_standard = set(header_map.keys()) - set(header_index.keys())
    if diff_header_standard:
        if '箱数' in diff_header_standard:
            raise ParamError(f'单一商品订单表格中缺少"箱数", 请确定导入的是单一商品订单模板', ErrorCode.PARAM_ERROR)
        else:
            raise ParamError(f'"{", ".join(diff_header_standard)}"字段不存在, 请下载使用标准模板导入', ErrorCode.PARAM_ERROR)

    excel_header_map = {}
    for column, index in header_index.items():
        if column in header_map:
            excel_header_map[header_map[column][0]] = [index, header_map[column][1]]
    get_cell = GetExcelCellData(excel_header_map, table)

    # 包裹新增
    # 获取工作表上的所有图片
    # all_images = table._images
    all_images = []
    with zipfile.ZipFile(excel_path, 'r') as z:
        # 遍历 XL/Media 和 XL/Drawings 目录中的所有文件
        for file in z.namelist():
            if file.startswith('xl/media/') and file.lstrip('xl/media/'):
                # 读取文件内容到内存中
                # with z.open(file) as f:
                f = z.open(file)
                all_images.append(f)

    # 商品数据的第一行(从1开始)
    first_item_row = 12
    parcel_item_df = pd.DataFrame(columns=['parcel', 'parcel_item'], dtype=object)
    parcel_item_ctn = 0

    # 判断文件标题箱数（xxx）和导入的行数（xxx）不匹配
    # 箱数
    box_num = get_excel_cell(3, 'f', table)
    total_parcel_count = 0
    for row in range(first_item_row, line_number + 1):
        parcel_num_pre = get_cell.get_cell_by_header(row, 'parcel_num_pre')
        if parcel_num_pre:
            total_parcel_count += get_cell.get_cell_by_header(row, 'parcel_count')
        else:
            break

    if box_num != total_parcel_count:
        raise ParamError(f'文件标题箱数（{box_num}）和导入的箱数（{total_parcel_count}）不匹配', ErrorCode.PARAM_ERROR)

    reference_ids = set()
    shipment_ids = set()
    # parcel_count = 0
    # ctn_num_map = defaultdict(int)
    current_parcel_prefix_map = {}
    # 遍历商品行数据
    for index, row in enumerate(range(first_item_row, line_number + 1), start=1):
        parcel_num_pre = get_cell.get_cell_by_header(row, 'parcel_num_pre')
        if not parcel_num_pre:
            break
        parcel_num_pre = str(parcel_num_pre).strip()
        # 如果不在当前excel的前缀列表中, 则需要检测
        if parcel_num_pre not in current_parcel_prefix_map:
            if Parcel.objects.filter(parcel_num__startswith=parcel_num_pre, del_flag=False).exists():
                raise ParamError(f'包裹号前缀 {parcel_num_pre} 重复, 请重新填写包裹号', ErrorCode.PARAM_ERROR)
            current_parcel_prefix_map[parcel_num_pre] = 1
        # parcel_num = '1'
        # if parcel_count == 0:
        #     parcel_count = get_excel_cell(row, 'c', table)
        #     parcel_num = get_parcel_num_single_init(parcel_num_pre)

        print('all_images-->', all_images)
        all_image_ids = []
        image_id = get_cell.get_cell_by_header(row, 'item_picture')
        img_data = None
        if image_id:
            if image_id not in all_image_ids:
                all_image_ids.append(image_id)
            img_index = all_image_ids.index(image_id)
            if img_index < len(all_images) - 1:
                if settings.SYSTEM_ORDER_MARK == 'YQF':
                    img_data = all_images[img_index]
                else:
                    # 第一张图是mz的logo, 从第二张图开始取
                    img_data = all_images[img_index + 1]

        parcel_count = get_cell.get_cell_by_header(row, 'parcel_count')
        # parcel_suffix = 1
        for inner_index in range(parcel_count):
            # if inner_index != 0:
            #     parcel_num = get_parcel_num_single(parcel_num_pre, int(parcel_num[-6:]))
            sys_parcel_num = create_sys_parcel_num(customer_order)
            # 如果存在FBA仓库代码, 则需要校验包裹号
            if receiver is not None:
                parcel_num = parcel_num_pre + 'U' + str(current_parcel_prefix_map[parcel_num_pre]).rjust(6, '0')
                judge_parcel_num_rule(parcel_num, customer_order, raise_error=True)
            else:
                parcel_num = sys_parcel_num

            # 壹起飞校验reference_id必填
            if settings.SYSTEM_ORDER_MARK == 'YQF':
                reference_id = get_cell.get_cell_by_header(row, 'reference_id',
                                                           allow_null=False).replace(" ", "").replace("\n", "")
            else:
                reference_id = get_cell.get_cell_by_header(row, 'reference_id').replace(" ", "").replace("\n", "")
            shipment_id = get_cell.get_cell_by_header(row, 'shipment_id').replace(" ", "").replace("\n", "")
            if reference_id:
                reference_ids.add(reference_id)
            if shipment_id:
                shipment_ids.add(shipment_id)

            parcel = import_service.create_parcel(get_cell, row, parcel_num, sys_parcel_num, customer_order,
                                                  reference_id, shipment_id)

            parcel_item = import_service.create_parcel_item(get_cell, row, parcel, parcel_item_df)

            # if img_data:
            #     image_name = f"{parcel_num}_{row}.jpg"
            #     parcel_item.item_picture.save(image_name, ContentFile(img_data))
            #     parcel_item.save()
            if img_data:
                image_name = f"{parcel_num}_{row}.jpg"
                # django_file = File(current_image, name=file.split('/')[-1])
                django_file = File(img_data, name=image_name)
                parcel_item.item_picture.save(django_file.name, django_file, save=True)
                parcel_item.save()

            parcel_item_ctn += 1
            # ctn_num_map[parcel_num_pre] += 1
            current_parcel_prefix_map[parcel_num_pre] += 1
        # parcel_count = 0

    if parcel_item_ctn == 0:
        raise ParamError('未导入任何数据, 请检查是否有数据或是否与已有包裹号重复', ErrorCode.PARAM_ERROR)

    summary_predict_parcels_data(customer_order)

    check_customer_order_shipment_id(customer_order)

    customer_order.reference_id = create_order_reference_id(reference_ids)
    customer_order.shipment_id = create_order_reference_id(shipment_ids)
    customer_order.save()
    common_judge_fba_and_remote(customer_order)
    workbook.close()
    # logger.info(f'单一商品运输订单导入成功: {customer_order.order_num}')
    logger.info(f'fba_customer_order_single_upload_handle end: {upload_task.id}')


# 单一商品多订单导入(导入单一商品多订单)(单一商品多票导入)
@app.task(bind=True, max_retries=1, base=QueueOnce, once={'graceful': True, 'keys': ['upload_task_id'],
                                                          'timeout': 600})
@transaction.atomic
def fba_single_multi_orders_upload_handle(self, upload_task_id):
    logger.info(f'fba_single_multi_orders_upload_handle start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(excel_path)
    upload_task.save()
    user = upload_task.create_by
    if not user:
        raise Exception('用户不存在, 请检查')

    # 读取 Excel
    logger.info(f'fba_customer_order_single_upload_handle开始读文件: {upload_task.id}')
    workbook = openpyxl.load_workbook(excel_path)
    logger.info(f'fba_customer_order_single_upload_handle成功读文件: {upload_task.id}')
    # 获取第一张表
    table = workbook.worksheets[0]
    # 行数
    line_number = table.max_row

    # product = order_import_verification_product_name(user, product_name=product_name)

    import_service = MultiFbaOrderImportService(table, user)
    company = import_service.get_order_customer(check_permission=False)

    # upload_task.order_num = customer_order.order_num
    # upload_task.save()

    # 获取包裹和商品数据
    header_map = {
        # 订单
        '订单号/入仓号': ['ref_num', 'str'],
        '产品渠道': ['product_code', 'str'],
        '发件人': ['shipper_name', 'str'],
        # '地址类型': ['address_type', 'str'],
        '仓库代码': ['warehouse_address', 'str'],
        '地址（私人地址必填）': ['private_address', 'str'],
        # 包裹
        '箱号/FBA编号': ['parcel_num_pre', 'str'],
        'Shipment Id': ['shipment_id', 'str'],
        'Reference Id': ['reference_id', 'str'],
        '箱数': ['parcel_count', 'int'],
        '材积CM(长*宽*高)': ['parcel_size', 'str'],
        '单箱重量KGS': ['parcel_weight', 'Decimal'],
        # 商品
        '海关编码': ['customs_code', 'str'],
        'SKU': ['item_code', 'str'],
        '中文品名': ['declared_nameCN', 'str'],
        '英文品名': ['declared_nameEN', 'str'],
        '单箱数量': ['item_qty', 'int'],
        '商品单重(KG)': ['item_weight', 'int'],
        '单价USD': ['declared_price', 'Decimal'],
        '品牌': ['brand', 'str'],
        '规格型号': ['model', 'str'],
        '材质中英文': ['texture', 'str'],
        '用途中英文': ['use', 'str'],
        '产品图片1': ['item_picture', 'Img'],
        '是否带电': ['is_electric', 'str'],
        '是否带磁': ['is_magnetic', 'str'],
        # '是否是液体': ['is_liquid', 'str'],
        # '是否粉末': ['is_powder', 'str'],
    }
    # 读取第一行的表头数据
    header_list = [cell.value.replace('\n', '') for cell in table[1] if cell.value]
    # 获取表头每一列对应的列索引
    header_index = {header_list[i]: i + 1 for i in range(len(header_list))}

    # diff_header_excel = set(header_index.keys()) - set(header_map.keys())
    # if diff_header_excel:
    #     raise ParamError(f'"{", ".join(diff_header_excel)}"字段不是表格内的规范字段, 请下载使用标准模板导入', ErrorCode.PARAM_ERROR)
    # diff_header_standard = set(header_map.keys()) - set(header_index.keys())
    # if diff_header_standard:
    #     if '箱数' in diff_header_standard:
    #         raise ParamError(f'单一商品订单表格中缺少"箱数", 请确定导入的是单一商品订单模板', ErrorCode.PARAM_ERROR)
    #     else:
    #         raise ParamError(f'"{", ".join(diff_header_standard)}"字段不存在, 请下载使用标准模板导入', ErrorCode.PARAM_ERROR)

    excel_header_map = {}
    for column, index in header_index.items():
        if column in header_map:
            excel_header_map[header_map[column][0]] = [index, header_map[column][1]]
    get_cell = GetExcelCellData(excel_header_map, table)

    # 包裹新增
    # 获取工作表上的所有图片
    # all_images = table._images
    all_images = []
    with zipfile.ZipFile(excel_path, 'r') as z:
        # 遍历 XL/Media 和 XL/Drawings 目录中的所有文件
        for file in z.namelist():
            if file.startswith('xl/media/') and file.lstrip('xl/media/'):
                # 读取文件内容到内存中
                # with z.open(file) as f:
                f = z.open(file)
                all_images.append(f)
    print('all_images-->', all_images)

    # 商品数据的第一行(从1开始)
    first_item_row = 2
    parcel_item_df = pd.DataFrame(columns=['parcel', 'parcel_item'], dtype=object)
    parcel_item_ctn = 0

    reference_ids = set()
    shipment_ids = set()

    current_parcel_prefix_map = {}
    customer_order_map = {}
    # 遍历商品行数据
    for index, row in enumerate(range(first_item_row, line_number + 1), start=1):
        # 读取订单数据

        product_code = get_cell.get_cell_by_header(row, 'product_code')
        product = order_import_verification_product_name(user, product_code=product_code)

        shipper_name = get_cell.get_cell_by_header(row, 'shipper_name')
        shipper_address = import_service.get_order_shipper(shipper_name, product)

        warehouse_address = get_cell.get_cell_by_header(row, 'warehouse_address')
        private_address = get_cell.get_cell_by_header(row, 'private_address')
        buyer_address = import_service.get_order_receiver(warehouse_address, private_address)
        if warehouse_address:
            receiver = buyer_address
        else:
            receiver = None

        ref_num = get_cell.get_cell_by_header(row, 'ref_num')
        customer_order = import_service.create_order(ref_num, company, product, receiver, buyer_address,
                                                     shipper_address, upload_task, customer_order_map)

        if ref_num not in customer_order_map:
            customer_order_map[ref_num] = customer_order

        # 读取包裹数据
        parcel_num_pre = get_cell.get_cell_by_header(row, 'parcel_num_pre')
        if not parcel_num_pre:
            break
        parcel_num_pre = str(parcel_num_pre).strip()
        # 如果不在当前excel的前缀列表中, 则需要检测
        if parcel_num_pre not in current_parcel_prefix_map:
            if Parcel.objects.filter(parcel_num__startswith=parcel_num_pre, del_flag=False).exists():
                raise ParamError(f'包裹号前缀 {parcel_num_pre} 重复, 请重新填写包裹号', ErrorCode.PARAM_ERROR)
            current_parcel_prefix_map[parcel_num_pre] = 1

        all_image_ids = []
        image_id = get_cell.get_cell_by_header(row, 'item_picture')
        print('image_id-->', image_id)
        img_data = None
        if image_id:
            if image_id not in all_image_ids:
                all_image_ids.append(image_id)
            img_index = all_image_ids.index(image_id)
            if img_index < len(all_images) - 1:
                img_data = all_images[img_index]

        parcel_count = get_cell.get_cell_by_header(row, 'parcel_count')
        for inner_index in range(parcel_count):
            sys_parcel_num = create_sys_parcel_num(customer_order)
            # 如果存在FBA仓库代码, 则需要校验包裹号
            if receiver is not None:
                parcel_num = parcel_num_pre + 'U' + str(current_parcel_prefix_map[parcel_num_pre]).rjust(6, '0')
                judge_parcel_num_rule(parcel_num, customer_order, raise_error=True)
            else:
                parcel_num = sys_parcel_num

            # 壹起飞校验reference_id必填
            if settings.SYSTEM_ORDER_MARK == 'YQF':
                reference_id = get_cell.get_cell_by_header(row, 'reference_id',
                                                           allow_null=False).replace(" ", "").replace("\n", "")
            else:
                reference_id = get_cell.get_cell_by_header(row, 'reference_id').replace(" ", "").replace("\n", "")
            shipment_id = get_cell.get_cell_by_header(row, 'shipment_id').replace(" ", "").replace("\n", "")
            if reference_id:
                reference_ids.add(reference_id)
            if shipment_id:
                shipment_ids.add(shipment_id)

            parcel = import_service.create_parcel(get_cell, row, parcel_num, sys_parcel_num, customer_order,
                                                  reference_id, shipment_id)

            parcel_item = import_service.create_parcel_item(get_cell, row, parcel, parcel_item_df)

            print('has img_data?-->', img_data)
            if img_data:
                image_name = f"{parcel_num}_{row}.jpg"
                django_file = File(img_data, name=image_name)
                parcel_item.item_picture.save(django_file.name, django_file, save=True)
                parcel_item.save()

            parcel_item_ctn += 1
            current_parcel_prefix_map[parcel_num_pre] += 1

        if parcel_item_ctn == 0:
            raise ParamError('未导入任何数据, 请检查是否有数据或是否与已有包裹号重复', ErrorCode.PARAM_ERROR)

        summary_predict_parcels_data(customer_order)

        check_customer_order_shipment_id(customer_order)

        customer_order.reference_id = create_order_reference_id(reference_ids)
        customer_order.shipment_id = create_order_reference_id(shipment_ids)
        customer_order.save()
        common_judge_fba_and_remote(customer_order)
    workbook.close()
    logger.info(f'fba_single_multi_orders_upload_handle end: {upload_task.id}')


# 异步导入fbm订单(异步fbm订单导入)(异步上传fbm订单)(main)
@app.task(bind=True, max_retries=1, base=QueueOnce, once={'graceful': True, 'keys': ['upload_task_id'],
                                                          'timeout': 600})
@transaction.atomic
def fbm_customer_order_upload_handle(self, upload_task_id):
    logger.info(f'fbm_customer_order_upload_handle start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    # excel_path = r"D:\data\klt_order1.xlsx"
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(excel_path)
    upload_task.save()
    user = upload_task.create_by

    logger.info(f'fbm_customer_order_upload_handle开始读文件: {upload_task.id}')
    workbook = openpyxl.load_workbook(excel_path)
    logger.info(f'fbm_customer_order_upload_handle成功读文件: {upload_task.id}')
    # 获取第一张表
    table = workbook.worksheets[0]

    # 从table解析数据
    order_data = FbmOrderImportService.obtain_fbm_data_from_table(table)
    workbook.close()

    product_code = order_data.product_code
    product = order_import_verification_product_code(product_code, user)

    # 校验数据
    FbmOrderImportService.validate_fbm_data(order_data)

    customer_order = FbmOrderImportService().process_order(order_data, user)

    upload_task.order_num = customer_order.order_num
    upload_task.save()

    with open(excel_path, 'rb') as file:
        excel_content = file.read()
    # 使用 ContentFile 将文件内容转换为 Django 可识别的文件对象
    excel_file = ContentFile(excel_content, name=upload_task.file_name)
    # 保存到附件中
    OrderAttachment.objects.create(name=upload_task.file_name, url=excel_file, customerOrder=customer_order,
                                   **get_update_params_by_user(user, True))
    logger.info(f'fbm_customer_order_upload_handle end: {upload_task.id}')

    return True


# 导入并解析FBA订单包裹面单
@transaction.atomic
# @app.task(base=QueueOnce)
def analysis_tr_voucher_handle(upload_task_id):
    logger.info(f'upload_and_analysis_voucher_handle start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    file_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(file_path)
    upload_task.save()
    # label_file = open(file_path, 'rb')
    # print('label_file-->', label_file, type(label_file))
    # handle_uploaded_file(file_path, label_file)
    # label_file.close()

    output_dir = calculate_md5(file_path)
    path_output_dir = os.path.join(os.path.dirname(file_path), output_dir)
    read_and_split_pdf_label(file_path, path_output_dir)
    logger.info(f'analysis_tr_voucher_handle分割文件完成: {output_dir}, {os.listdir(path_output_dir)}')
    # print('path_output_dir-->', os.listdir(path_output_dir))

    parcel_nums = []
    # 遍历目录下的所有文件
    for filename in os.listdir(path_output_dir):
        # 对于已经处理过的pdf: 非output_开头的文件则跳过
        if not filename.startswith('output_'):
            continue
        inner_file_path = os.path.join(path_output_dir, filename)
        if os.path.isfile(inner_file_path):  # 确保是文件而不是子目录
            # 在这里进行你的处理逻辑，比如打印文件名
            print(f'处理文件: {filename}')
            sub_lable_file = path_output_dir + '/' + filename
            barcodes = identify_pdf_barcode(sub_lable_file)
            for barcode in barcodes:
                if len(barcode) == 34:
                    # 兼容FEDEX 短跟踪号查询
                    parcel_queryset = Parcel.objects.filter(tracking_num__in=[barcode, barcode[22:34]],
                                                            del_flag=False)
                else:
                    parcel_queryset = Parcel.objects.filter(tracking_num=barcode, del_flag=False)
                if parcel_queryset.exists():
                    parcel = parcel_queryset.first()
                    new_name = path_output_dir + '/' + barcode + '.pdf'
                    if os.path.isfile(sub_lable_file) and os.path.isfile(new_name):
                        os.remove(new_name)
                    os.rename(sub_lable_file, new_name)
                    parcel_queryset.update(voucher=new_name.replace(settings.STATIC_MEDIA_DIR, ''),
                                           update_date=datetime.now())
                    parcel_nums.extend(parcel_queryset.values_list('parcel_num', flat=True))
                    logger.info(f'包裹管理上传面单绑定跟踪号, 包裹号: {parcel.parcel_num}')
    upload_task.task_desc = ','.join(parcel_nums)[:2000]
    upload_task.save()
    logger.info(f'upload_and_analysis_voucher_handle end: {upload_task.id}')


# 批量大包签入(批量大包称重)
@transaction.atomic
def batch_big_parcel_weighting(upload_task_id):
    logger.info(f'batch_big_parcel_weighting start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    file_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(file_path)
    upload_task.save()
    user = upload_task.create_by

    # 打开excel
    # wb = xlrd.open_workbook(filename=None, file_contents=excel.read())
    wb = xlrd_compat.open_workbook(filename=file_path)

    # 获取第一张表
    table = wb.sheets()[0]
    nrows = table.nrows  # 行数
    data_list = []
    parcel_nums = []
    for row_index in range(1, nrows):
        row_values = table.row_values(row_index)
        # 打印每一行的值
        bulk_order_num = row_values[0]
        # 强制更新
        # need_update = request.data['update']
        need_update = row_values[5]
        # print("need_update", need_update)
        try:
            big_parcel = BigParcel.objects.get(parcel_num=bulk_order_num, del_flag=False)
        except BigParcel.DoesNotExist:
            big_parcel = None
        handler_data = {'bulk_order_num': bulk_order_num, 'bulk_weight': row_values[1],
                        'bulk_length': row_values[2], 'bulk_width': row_values[3],
                        'bulk_high': row_values[4], 'need_update': row_values[5]}

        if big_parcel is None:
            handler_data['status'] = '未找到大包订单,请确认'
            data_list.append(handler_data)
            continue
        elif not need_update and big_parcel.is_weighing:
            # 不需要更新然而找的大包单是已经核重的
            handler_data['status'] = '该大包单已经核重，如需更新数据，请勾选更新后再次尝试！'
            data_list.append(handler_data)
            continue
        handler_data['status'] = '处理成功'
        data_list.append(handler_data)
        parcel_nums.append(bulk_order_num)
    print(data_list, 'data_list')
    datetime_now = datetime.now()
    for item in data_list:
        print(item)
        big_parcel = BigParcel.objects.get(parcel_num=item['bulk_order_num'], del_flag=False)
        big_parcel.is_weighing = True
        big_parcel.parcel_weight = item['bulk_weight']
        big_parcel.parcel_length = item['bulk_length'] or big_parcel.parcel_length
        big_parcel.parcel_width = item['bulk_width'] or big_parcel.parcel_width
        big_parcel.parcel_height = item['bulk_high'] or big_parcel.parcel_height
        big_parcel.update_by = user
        big_parcel.update_date = datetime_now
        big_parcel.save()

    # 打开现有的 Excel 文件
    sheet_name = '大包批量核重数据'
    # 写入标题行
    titles = ['货箱号', '重量', '长', '宽', '高', '强制更新', '状态']
    excel_name = os.path.basename(file_path)
    save_bulk_model_obj(data_list, excel_name, sheet_name, titles, 'LARGE', 'WEIGHING')

    upload_task.task_desc = ','.join(parcel_nums)[:2000]
    upload_task.save()
    logger.info(f'batch_big_parcel_weighting end: {upload_task.id}')


# 批量小包签入(批量小包称重)
@transaction.atomic
def batch_small_parcel_weighting(upload_task_id):
    logger.info(f'batch_small_parcel_weighting start: {upload_task_id}')
    upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
    file_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
    upload_task.status = 'Processed'
    upload_task.file_md5 = calculate_md5(file_path)
    upload_task.save()
    user = upload_task.create_by

    # 打开excel
    wb = xlrd_compat.open_workbook(filename=file_path)
    # 获取第一张表
    table = wb.sheets()[0]
    nrows = table.nrows  # 行数

    data_list = []
    for row_index in range(1, nrows):
        row_values = table.row_values(row_index)
        order_num = row_values[0]
        parcel_weight = row_values[1]
        parcel_length = row_values[2]
        parcel_width = row_values[3]
        parcel_height = row_values[4]
        if row_values[5] and str(row_values[5]).strip() == '是':
            need_update = True
        else:
            need_update = False

        logger.info('check_weight order_num =' + order_num)
        handler_data = {'bulk_order_num': order_num, 'bulk_weight': row_values[1], 'bulk_length': row_values[2],
                        'bulk_width': row_values[3], 'bulk_high': row_values[4], 'need_update': row_values[5]}

        parcel_order = get_parcel_order_by_order_num(order_num)
        if not parcel_order:
            handler_data['status'] = '未找到小包订单,请确认'
            data_list.append(handler_data)
            continue
        if parcel_order.order_status == 'VO':
            handler_data['status'] = '作废订单，请拦截！'
            data_list.append(handler_data)
            continue
        if not need_update and parcel_order.is_weighing:
            handler_data['status'] = '非强制更新，不允许重复称重'
            data_list.append(handler_data)
            continue
        if parcel_order.intercept_mark:
            handler_data['status'] = '拦截订单，请联系客服处理'
            data_list.append(handler_data)
            continue

        validate_res = validate_weighing_and_packing(parcel_order)
        if validate_res:
            handler_data['status'] = '获取面单已经超过15天，禁止操作'
            data_list.append(handler_data)
            continue

        try:
            handler_parcel_weighting(need_update, parcel_height, parcel_length, parcel_order, parcel_weight, parcel_width, user)
            handler_data['status'] = '处理成功'
        except Exception as e:
            handler_data['status'] = str(e)
        data_list.append(handler_data)

    # 打开现有的 Excel 文件
    sheet_name = '小包批量核重数据'
    excel_name = os.path.basename(file_path)
    # 写入标题行
    titles = ['运单号', '重量', '长', '宽', '高', '强制更新(是/否)', '状态']
    save_bulk_model_obj(data_list, excel_name, sheet_name, titles, 'SMALL')

    logger.info(f'batch_small_parcel_weighting end: {upload_task.id}')


# 订单异步任务(定时任务)
@app.task(bind=True, base=QueueOnce)
# def common_order_async_task_handle(task_func, task_id, *args, **kwargs):
def common_order_async_task_handle(self):
    logger.info(f'common_order_async_task_handle start')
    start_time = datetime.now()
    # 写在try里面, 避免脏读, 因为handle_func中也有OrderSyncUploadTask操作
    # task = OrderAsyncTask.objects.get(id=task_id)
    tasks = OrderAsyncTask.objects.filter(Q(start_time=None) | Q(start_time__lte=start_time),
                                          status='Waiting', del_flag=False)
    for task in tasks:
        task_id = task.id
        logger.info(f'common_order_async_task_handle start: 任务id: {task_id}')
        try:
            # globals_dict = globals()
            # if task_func in globals_dict:
            #     # 通过字符串索引调用函数
            #     handle_func = globals_dict[task_func]
            #     handle_func(task, *args, **kwargs)
            if task.order_type == 'TR' and task.task_type == 'BL':
                async_billing_customer_order(task)
            elif task.order_type == 'TR' and task.task_type == 'OrderStatus':
                async_customer_order_status(task)
            elif task.task_type == 'CostFinish':
                async_cost_finish(task, is_share=False)
            else:
                raise Exception(f'找不到对应的单据类型: {task.order_type}和任务类型: {task.task_type}')
            task.status = 'Success'
            task.task_desc = 'Success'
            logger.info(f'common_order_async_task_handle任务执行成功, id: {task_id}')
        except Exception as e:
            logger.info(traceback.format_exc())
            task.task_desc = str(e)[:2000]
            # task.task_desc = str(traceback.format_exc())[:2000]
            task.status = 'Failure'
            logger.error(f'common_order_async_task_handle任务执行失败, id: {task_id}, 报错:\n{str(traceback.format_exc())}')
        finally:
            datetime_now = datetime.now()
            task.execution_time = datetime_now - start_time
            task.update_date = datetime_now
            task.save()

# 异步对订单拦截状态从一起飞同步到铭志
def async_customer_order_status(task):
    task.status = 'Processed'
    task.save()

    if settings.SYSTEM_ORDER_MARK != 'YQF':
        return

    order = CustomerOrder.objects.get(order_num=task.order_num, del_flag=False)
    if not order:
        return
    url = 'http://cshm.mz56.com' + '/api/customerOrders/accept_cancel_intercept_data/'
    # url = 'http://127.0.0.1:8000' + '/api/customerOrders/accept_cancel_intercept_data/'
    result = request_server({'data': [order.order_num], 'operate_type': task.task_desc}, url,
                            {'Content-Type': 'application/json'})
    if not result:
        raise ParamError('订单操作推送供应商失败', ErrorCode.PARAM_ERROR)
    if result.get('code') == 200:
        logger.info(f'{task.task_desc} 推送数据成功！')
    else:
        error_msg = result.get('msg', '') or result.get('detail', '')
        raise Exception(f'订单操作推送供应商失败: {error_msg}')


# 异步对订单做应收应付计费(异步计费/异步计价)
def async_billing_customer_order(task):
    task.status = 'Processed'
    task.save()
    user = task.create_by
    try:
        if task.order_type == 'TR':
            order = CustomerOrder.objects.get(order_num=task.order_num, del_flag=False)
        elif task.order_type == 'PC':
            order = ParcelCustomerOrder.objects.get(order_num=task.order_num, del_flag=False)
        else:
            raise Exception(f'异步计费任务的类型错误: {task.order_type}')
    except (ObjectDoesNotExist, MultipleObjectsReturned):
        raise Exception(f'异步计费任务未找到唯一的单据, 单号: {task.order_num}, 类型: {task.order_type}')

    # try:
    #     user = UserProfile.objects.get(id=user_id)
    # except (ObjectDoesNotExist, MultipleObjectsReturned):
    #     raise ParamError(f'async_billing_customer_order用户认证失败', ErrorCode.PARAM_ERROR)

    billing_customer_order(order, user)


# wms计算库龄任务(定时任务)
@app.task(bind=True, base=QueueOnce)
def handle_inventory_age_task(self):
    logger.info('start handler_inventory_age_task')

    if settings.SYSTEM_ORDER_MARK not in ['TEST']:
        return

    start_time = datetime.now()

    # 获取当前日期
    current_date = datetime.datetime.now()
    calculate_age_date = current_date.strftime('%Y-%m-%d')

    # 查询仓租单当天是否已经生成,生成则先删除后重新生成
    #查询当前仓库在库库存大于0的全部批次库存,同时基于
    inventory_batch_list = InventoryBatch.objects.filter(del_flag=False,instock_qty__gt=0).values('warehouse', 'customer', 'sku', 'batch_no').annotate(total_instock_qty=sum('instock_qty'))
    # 封装库龄数据
    if inventory_batch_list:
        inventory_age_list = []
        for inv in inventory_batch_list:
            # 基于批次库存计算库龄数据并保存
            inventory_age = InventoryAge()
            inventory_age.calculate_age_date = calculate_age_date
            inventory_age.warehouse = inv.warehouse
            inventory_age.customer = inv.customer
            inventory_age.sku_code = inv.sku_code
            inventory_age.declared_name_cn = inv.declared_name_cn
            inventory_age.declared_name_en = inv.declared_name_en
            inventory_age.putaway_date = inv.putaway_date
            inventory_age.instock_qty = inv.instock_qty
            inventory_age.storage_age = calc_day_diff(inv.putaway_date, calculate_age_date) # 计算库龄
            inventory_age_list.append(inventory_age)

        if inventory_age_list:
            # 批量插入
            InventoryAge.objects.bulk_create(inventory_age_list)

        logger.info(f"计算库龄日期:{calculate_age_date} 生成{len(inventory_batch_list)}条库龄数据成功")
    else:
        logger.info(f"计算库龄日期:{calculate_age_date}没有查询到库存记录,无法生成库龄!")


# wms生成仓租单任务(定时任务)
@app.task(bind=True, base=QueueOnce)
def handle_rent_order_task(self):
    logger.info('start handler_rent_order_task')

    if settings.SYSTEM_ORDER_MARK not in ['TEST']:
        return

    # 获取当前日期
    current_date = datetime.datetime.now()
    calculate_age_date = current_date.strftime('%Y-%m-%d')

    # 查询仓租单当天是否已经生成,如果有直接返回
    num = RentOrderDetails.objects.filter(storage_date=calculate_age_date,del_flag=False).count()
    if num > 0:
        logger.error(f'仓租日期:{calculate_age_date}的仓租单已经生成,不允许重复计算')
        return

    # 查询当天全部商品库龄
    inventory_age_list = InventoryAge.objects.filter(del_flag=False,instock_qty__gt=0,calculate_age_date=calculate_age_date).all()
    # 基于库龄数据生成【仓租单明细】
    if inventory_age_list is not None and len(inventory_age_list) > 0:
        # 计算应收仓租单
        calc_rent_order(calculate_age_date,inventory_age_list,"receivable")
        # 计算应付仓租单
        calc_rent_order(calculate_age_date,inventory_age_list,"payable")


# 基于计费类型计算生成仓租单和仓租明细
@transaction.atomic
def calc_rent_order(calculate_age_date,inventory_age_list,fee_type):
    # 开始时间
    start_time = datetime.now()
    logger.info(f'仓租日期:{calculate_age_date},{fee_type}仓租单,开始计算')
    rent_order_details_list = []
    for inv_age in inventory_age_list:
        rent_order_details = RentOrderDetails()
        rent_order_details.storage_date = calculate_age_date # 仓租日期全部和计算日期保持一致
        # rent_order_details.storage_no = storage_no
        rent_order_details.customer = inv_age.customer
        rent_order_details.warehouse = inv_age.warehouse
        rent_order_details.sku_code = inv_age.sku_code
        rent_order_details.declared_name_cn = inv_age.declared_name_cn
        rent_order_details.declared_name_en = inv_age.declared_name_en
        rent_order_details.putaway_date = inv_age.putaway_date
        rent_order_details.surplus_qty = inv_age.instock_qty
        rent_order_details.reality_storage_age = inv_age.storage_age
        rent_order_details.rent_free_storage_age = inv_age.storage_age  # 暂时设计免租期逻辑,后续可以扩展
        rent_order_details.charged_volume = inv_age.sku.volume * inv_age.instock_qty  # 计费体积 = 商品体积 * 在库库存数

        # 区间计费/CBM (单价),根据每个商品的实际库龄去仓租计费规则中匹配
        rent_charged_config = RentChargesConfig.objects.filter(warehouse=inv_age.warehouse, customer=inv_age.customer,fee_type=fee_type) \
            .filter(storage_age_begin__lte=inv_age.storage_age, storage_age_end__gte=inv_age.storage_age).first()
        if rent_charged_config:
            rent_order_details.range_charged = rent_charged_config.fee_amount
        else:
            logger.info(f"仓租日期:{calculate_age_date},{fee_type}仓租单,客户{inv_age.customer}的商品：{inv_age.sku_code}库龄：{inv_age.storage_age}没有配置仓租计费区间规则,无法计算,直接跳过")
            continue

        # 总金额
        rent_order_details.amount = rent_order_details.range_charged * rent_order_details.charged_volume
        rent_order_details.fee_type = fee_type
        rent_order_details.currency = "CNY"

        # 计算仓租明细过程数据
        rent_order_details.remark = f"总金额:{rent_order_details.amount} = 区间计费/CBM:{rent_order_details.range_charged} * 计费体积(CBM):{rent_order_details.charged_volume} "

        # 仓租明细放入数组
        rent_order_details_list.append(rent_order_details)

    # 批量插入仓租明细
    RentOrderDetails.objects.bulk_create(rent_order_details_list)
    logger.info(f'仓租日期:{calculate_age_date},{fee_type}仓租单明细批量插入成功:{rent_order_details_list.count()}条')

    # 根据仓租单明细生成当天的仓租单
    rent_order_list = RentOrderDetails.objects.values_list("storage_date","warehouse", "customer","currency").annotate(
        item_qty=sum("surplus_qty"), total_volume=sum("charged_volume"), fee_amount=sum("amount"))
    if rent_order_list and len(rent_order_list) > 0:
        for order in rent_order_list:
            # 仓租单号
            storage_no = "RO" + datetime.date()
            rent_order = RentOrder()
            rent_order.storage_no = storage_no # 仓租单号
            rent_order.storage_date = calculate_age_date # 仓租日期全部和计算日期保持一致
            rent_order.warehouse = order.warehouse
            rent_order.customer = order.customer
            rent_order.item_qty = order.item_qty
            rent_order.total_volume = order.total_volume
            rent_order.currency = order.currency
            rent_order.fee_type = fee_type
            rent_order.create_date = datetime.now()
            # rent_order.update_by = user
            rent_order.update_date = datetime.now()

            # 仓租单落库
            rent_order.save()
            logger.info(f'仓租日期:{calculate_age_date},{fee_type}仓租单:{storage_no}插入成功')

            # 更新仓租单明细表的仓租单号
            RentOrderDetails.objects.filter(warehouse=rent_order.warehouse, storage_date=rent_order.storage_date, customer=rent_order.customer).update(storage_no=storage_no)
            logger.info(f'仓租日期:{calculate_age_date},{fee_type}仓租单:{storage_no}明细的仓租单号更新成功')

    # 结束时间
    end_time = datetime.now()
    # 计算时间差
    time_difference = end_time - start_time
    logger.info(f'仓租日期:{calculate_age_date},{fee_type}仓租单,计算结束,总耗时:{time_difference.total_seconds()}')


# 异步对海运提单/进口报关单/卡派单做成本确认和成本分摊(自动成本确认, 自动成本分摊)
def async_cost_finish(task, is_share=False):
    task.status = 'Processed'
    task.save()
    user = task.create_by
    order, charge_out_queryset, main_order_queryset = get_order_with_order_num(task.order_num,
                                                                               task.order_type,
                                                                               charge_type='cost',
                                                                               get_main_order=True)
    order_str = ORDER_MANE_MAP[task.order_type]
    # 异步成本确认
    order_cost_confirm(order.id, order_str, user)

    # 异步成本分摊
    if is_share:
        if not main_order_queryset.exists():
            raise Exception(f'单据{order_str}: {task.order_num} 未找到分摊的主体订单')
        fail_orders = 0
        # 再重新获取一次单据, 避免出现代码鬼打墙(数据未保存)
        order, charge_out_queryset, main_order_queryset = get_order_with_order_num(task.order_num,
                                                                                   task.order_type,
                                                                                   charge_type='cost',
                                                                                   get_main_order=True)
        if task.order_type == 'TO':
            logger.info(f'开始分摊{order_str}: {task.order_num} 的成本')
            fail_orders += common_order_cost_share(order, main_order_queryset,
                                                   charge_out_queryset, user)
            logger.info(f'单据{order_str}: {task.order_num} 的成本分摊完成')
        elif task.order_type in ['OC', 'CLI']:
            # customer_order = relate_ocean.customer_order_num
            logger.info(f'开始分摊{order_str}: {task.order_num} 的成本')
            stowage_map = {i.customer_order_num_id: i for i in main_order_queryset}
            # customer_orders = [i.customer_order_num for i in main_order_queryset]
            customer_order_nums = main_order_queryset.values_list('customer_order_num', flat=True)
            customer_orders = CustomerOrder.objects.filter(id__in=customer_order_nums, del_flag=False)
            fail_orders += common_order_cost_share(order, customer_orders,
                                                   charge_out_queryset, user, stowage_map=stowage_map)
            logger.info(f'单据{order_str}: {task.order_num} 的成本分摊完成')


# 产品异步任务(产品定时任务)(main)
@app.task(bind=True, base=QueueOnce)
def common_product_async_task_handle(self):
    logger.info(f'common_product_async_task_handle start')
    start_time = datetime.now()
    # 写在try里面, 避免脏读, 因为handle_func中也有OrderSyncUploadTask操作
    # task = ProductAsyncTask.objects.get(id=task_id)
    tasks = ProductAsyncTask.objects.filter(Q(start_time=None) | Q(start_time__lte=start_time),
                                            status='Waiting', del_flag=False)
    for task in tasks:
        task_id = task.id
        logger.info(f'common_product_async_task_handle start: 任务id: {task_id}')
        try:
            if task.order_type == 'ProductSalesPrice' and task.task_type == 'TransProtocolPrice':
                async_product_sales_price_trans_protocol_price(task)
            else:
                raise Exception(f'找不到对应的单据类型: {task.order_type}或任务类型: {task.task_type}')
            task.status = 'Success'
            task.task_desc = 'Success'
            logger.info(f'common_product_async_task_handle任务执行成功, id: {task_id}')
        except Exception as e:
            logger.info(traceback.format_exc())
            task.task_desc = str(e)[:2000]
            # task.task_desc = str(traceback.format_exc())[:2000]
            task.status = 'Failure'
            logger.error(f'common_product_async_task_handle任务执行失败, id: {task_id}, 报错:\n{str(traceback.format_exc())}')
        finally:
            datetime_now = datetime.now()
            task.execution_time = datetime_now - start_time
            task.update_date = datetime_now
            task.save()


def async_product_sales_price_trans_protocol_price(task):
    task.status = 'Processed'
    task.save()
    user = task.create_by
    try:
        if task.order_type == 'ProductSalesPrice':
            product_sales_price = ProductSalesPrice.objects.get(id=task.order_id, del_flag=False)
            product_sales_price_trans_protocol_price(user, product_sales_price)
        else:
            raise Exception(f'异步任务的类型错误: {task.order_type}')
    except (ObjectDoesNotExist, MultipleObjectsReturned):
        raise Exception(f'产品销售定价未找到唯一的id, id: {task.order_id}, 类型: {task.order_type}')


# dmas异步任务(dmas异步推送消息)(main)
@app.task(bind=True, base=QueueOnce)
def dmas_async_task_handle(self):
    logger.info(f'dmas_async_task_handle start')
    start_time = datetime.now()
    tasks = DmasMsgSendTask.objects.filter(Q(start_time=None) | Q(start_time__lte=start_time),
                                           status='Waiting', del_flag=False)
    for task in tasks:
        task_id = task.id
        logger.info(f'dmas_async_task_handle start: 任务id: {task_id}')
        try:
            if task.business_type == 'mz' and task.push_type == 'customer_code':
                dmas_push_message_scheduled_mz(task)
            else:
                raise Exception(f'未知的业务类型: {task.business_type}或推送类型: {task.push_type}')
            task.status = 'Success'
            task.task_desc = 'Success'
            logger.info(f'dmas_async_task_handle任务执行成功, id: {task_id}')
        except Exception as e:
            logger.info(traceback.format_exc())
            task.task_desc = str(e)[:2000]
            # task.task_desc = str(traceback.format_exc())[:2000]
            task.status = 'Failure'
            logger.error(f'dmas_async_task_handle任务执行失败, id: {task_id}, 报错:\n{str(traceback.format_exc())}')
        finally:
            datetime_now = datetime.now()
            task.update_date = datetime_now
            task.save()


def dmas_push_message_scheduled_mz(task: DmasMsgSendTask):
    task.status = 'Processed'
    task.save()
    user = task.create_by
    send_dmas_msg_by_customer(task.message, customer_code=task.customer_code)


# 对接rm manifest接口
@app.task(bind=True, base=QueueOnce)
@transaction.atomic
def call_rm_manifest_interface(self):
    logger.info('start call_rm_manifest_interface')

    if settings.SYSTEM_MARK not in ['ZJ', 'ZHS']:
        return

    # 获取当前日期
    current_date = datetime.now()
    # 获取 account_id 和 location_id
    rm_manifest_querysets = RmManifestConfig.objects.filter(del_flag=False, is_enable=True)
    if not rm_manifest_querysets.exists():
        return

    for rm_manifest in rm_manifest_querysets:
        url = rm_manifest.manifest_url
        carrier_code = rm_manifest.carrier_code

        full_url = f'{url}/{carrier_code}'
        location_id = rm_manifest.shipping_location_id
        account_id = rm_manifest.shipping_account_id
        client_id = rm_manifest.client_id
        client_secret = rm_manifest.client_secret
        if not location_id or not account_id:
            continue

        payload = {
            "ShippingLocationId": location_id,
            "ShippingAccountId": account_id,
            "Async": False
        }
        try:
            res = manifests_shipment(full_url, client_id, client_secret, payload)
        except Exception as e:
            logger.info(f'RM manifest接口调用异常: {e}')
            rm_manifest.execute_result = str(e)
            rm_manifest.execute_time = current_date
            rm_manifest.save()
            continue

        if res.status_code != 200:
            logger.info(f'RM manifest接口调用失败: {res.text}')
            rm_manifest.execute_result = res.text
            rm_manifest.execute_time = current_date
            rm_manifest.save()
        else:
            logger.info(f'RM manifest接口调用成功')
            rm_manifest.execute_result = res.text
            rm_manifest.execute_time = current_date
            rm_manifest.save()

            # 保存manifest文件
            manifest_res_lis = res.json()
            i = 1
            for manifest_res in manifest_res_lis:
                manifest_image_data = manifest_res['ManifestImage']
                label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) +  (datetime.now().strftime("%Y_%m_%d_%H")) + '_' + rm_manifest.name + '_' + str(i) + ".pdf"
                upload_file(label_url, manifest_image_data)
                RmManifestResFile.objects.create(
                    rm_manifest_config=rm_manifest,
                    file_url=label_url
                )
                i+=1

    logger.info('end call_rm_manifest_interface')

def parse_export_filters(filter_json):
    """
    解析导出任务的过滤器JSON

    Args:
        filter_json (str): 过滤器JSON字符串

    Returns:
        dict: 解析后的过滤器字典

    Raises:
        json.JSONDecodeError: JSON解析失败
        TypeError: 数据类型错误
    """
    if not filter_json or filter_json.strip() == "":
        return {}

    filter_prop = json.loads(filter_json)
    temp_filter = {}

    for key, value in filter_prop.items():
        # 处理日期/时间字段
        if "date" in key.lower() or "time" in key.lower():
            if "," in str(value):
                # 日期范围查询
                temp_filter[f"{key}__range"] = value.split(",")
            else:
                # 单个日期查询
                temp_filter[key] = value

        # 处理布尔值字段
        elif str(value).lower() == "true":
            temp_filter[key] = True
        elif str(value).lower() == "false":
            temp_filter[key] = False

        # 处理其他字段
        else:
            temp_filter[key] = value

    return temp_filter


def get_export_queryset(export_type, temp_filter):
    """
    根据导出类型获取对应的查询集和状态过滤器

    Args:
        export_type (str): 导出类型
        temp_filter (dict): 过滤器

    Returns:
        tuple: (queryset, status_filter)
    """
    if export_type == "1":
        status_filter = {"1": "待收款", "2": "收款完成", "3": "已取消"}
        queryset = AccountReceivable.objects.filter(**temp_filter, del_flag=False)
    elif export_type == "2":
        status_filter = {
            "1": "待提交",
            "2": "待经理审核",
            "3": "待财务审核",
            "5": "待支付",
            "6": "已支付",
            "7": "已取消",
            "8": "部分付款",
        }
        queryset = AccountPayable.objects.filter(**temp_filter, del_flag=False)
    elif export_type == "3":
        status_filter = {"1": "待收款", "2": "收款完成", "3": "已取消", "4": "部分收款"}
        queryset = Debit.objects.filter(**temp_filter, del_flag=False)
    else:
        return None, None

    return queryset, status_filter


def prefetch_related_data(queryset, rows_prop):
    """
    预加载关联数据，避免N+1查询问题

    Args:
        queryset: Django查询集
        rows_prop (list): 属性列表

    Returns:
        queryset: 优化后的查询集
    """
    select_related_fields = []
    prefetch_related_fields = []

    for prop in rows_prop:
        if prop.endswith("_name"):
            base_field = prop[:-5]  # 去掉 '_name' 后缀
            if prop in ["customer_name", "supplier_name"]:
                select_related_fields.append(base_field)
            elif prop in ["charge_name_name", "debit_num_name", "payment_num_name"]:
                select_related_fields.append(base_field)

    if select_related_fields:
        queryset = queryset.select_related(*select_related_fields)

    return queryset


def batch_process_export_data(queryset, rows_prop, status_filter, batch_size=1000):
    """
    批量处理导出数据，减少内存占用

    Args:
        queryset: Django查询集
        rows_prop (list): 属性列表
        status_filter (dict): 状态过滤器

    Yields:
        list: 每行数据
    """
    # 预加载关联数据
    queryset = prefetch_related_data(queryset, rows_prop)

    # 批量处理数据
    for batch_start in range(0, queryset.count(), batch_size):
        batch_end = batch_start + batch_size
        batch_data = queryset[batch_start:batch_end]

        for data in batch_data:
            prop_val = []
            query_dict = model_to_dict(data)
            query_dict["update_date"] = data.update_date.strftime("%Y-%m-%d")

            for prop in rows_prop:
                if prop.endswith("_name"):
                    id = query_dict[prop[0:-5]]
                    if id is not None:
                        if prop == "customer_name" or prop == "supplier_name":
                            # 使用预加载的数据
                            related_obj = getattr(data, prop[0:-5])
                            prop_val.append(related_obj.name if related_obj else "")
                        elif prop == "charge_name_name":
                            related_obj = getattr(data, "charge_name")
                            prop_val.append(related_obj.name if related_obj else "")
                        elif prop == "debit_num_name":
                            related_obj = getattr(data, "debit_num")
                            prop_val.append(
                                related_obj.debit_num if related_obj else ""
                            )
                        elif prop == "payment_num_name":
                            related_obj = getattr(data, "payment_num")
                            prop_val.append(related_obj.pay_num if related_obj else "")
                    else:
                        prop_val.append("")
                elif "status" in prop:
                    prop_val.append(status_filter.get(str(query_dict[prop]), ""))
                else:
                    # 处理布尔值，转换为"是"、"否"
                    value = query_dict[prop]
                    if isinstance(value, bool):
                        prop_val.append("是" if value else "否")
                    else:
                        prop_val.append(value)

            yield prop_val
