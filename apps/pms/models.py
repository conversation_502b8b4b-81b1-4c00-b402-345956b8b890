from django.db import models

# Create your models here.
from common.models import BaseEntity
from company.models import Company, SupplierButt, TrackSupplier, Address, Supplier
from info.models import Charge, TrackCode
from rbac.models import Organization
from django.conf import settings


# 产品表
class Product(BaseEntity):
    # 产品物流类型
    TYPE = [
        ('TR', '运输'),
        ('CL', '清关'),
        ('PC', '小包'),
        ('IC', '海外仓入库单'),
        ('OC', '海外仓出库单'),
        ('RO', '海外仓退货单'),
        ('BP', '大包'),
        ('IS', '保险'),
        ('ST', '空运'),
        ('OT', '海运'),
        ('TO', '卡派单'),
        ('IF', '海外仓入库中转单'),
        ('OF', '海外仓出库中转单'),
    ]

    SERVICE_LEAVE = [
        ('1', '优先'),
        ('2', '标准'),
        ('3', '经济'),
        ('4', '特惠'),
    ]

    LABEL_TYPE = [
        ('WML', '无面单'),
        ('WC', '尾程'),
        ('WA', '尾程+称重计费'),
        ('ZX', '中性'),
        ('ZW', '中性+称重换单'),
        ('YZW', '中性+称重换单+二次换单'),
        ('WCW', '尾程+二次换单'),  # 针对子产品二次拉单的情况
        ('YW', '邮政+二次换单'),
        ('HW', '中性+华磊(邮政)+称重换单'),
        ('CPW', '客户推送面单'),
        ('WWWC', '称重+等待确认+尾程'),  # 业务背景：称重不触发尾程，等待确认状态，然后手动再触发尾程--用于更改小包清关信息 确定状态之后再触发尾程
        # 韩进项目
        ('DD', '端到端'),
        ('AD', '平台产品'),
        ('FD', '分段服务'),
        ('SL', '解决方案'),
    ]
    BUSINESS_TYPE = [
        ('WW', '仓内作业'),
        ('AD', '账号打单'),
    ]

    STRATEGY_TYPE = [
        ('COST_LOW', '成本最低'),
        ('MULTI_CHANNEL', '多渠道'),
        ('MC_COST_LOW', '多渠道成本最低'),
    ]

    ORDER_TYPE = [
        ('C', '推客户单号'),
        ('S', '推系统单号'),
    ]

    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    PRICING_TYPE = [
        ('ProductSalesPrice', '产品销售定价'),
        ('RevenuePriceVersion', '收入价格版本'),
    ]

    # 换号方式
    REPLACE_NUM_MODE = [
        ('IC', '库内-实时换号'),
        ('FC', '预报-异步换号'),
    ]

    # 授权模式
    AUTH_MODE = [
        ('B', '黑名单'),
        ('W', '白名单'),
    ]

    name = models.CharField(max_length=100, verbose_name='产品名称')
    code = models.CharField(max_length=100, verbose_name='产品编码', null=True, blank=True)
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='产品物流类型', default='TR')
    sub_type = models.CharField(max_length=10, verbose_name='产品子类', null=True, blank=True)
    label_type = models.CharField(max_length=10, choices=LABEL_TYPE, verbose_name='产品类型', default='WC')
    service_level = models.CharField(max_length=2, choices=SERVICE_LEAVE, verbose_name='服务等级', default='1')
    classification = models.CharField(verbose_name='货物性质', null=True, blank=True, max_length=100)
    extended_field = models.TextField(verbose_name='产品时效说明', blank=True, null=True)
    ship_desc = models.TextField(verbose_name='产品走货说明', blank=True, null=True)
    replace_no_mode = models.CharField(max_length=3, choices=REPLACE_NUM_MODE, verbose_name='换号方式', default='IC')
    is_cancel = models.BooleanField(verbose_name='是否允许取消', default=False)
    auth_mode = models.CharField(max_length=3, choices=AUTH_MODE, verbose_name='产品授权模式', default='W')
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE)

    revenue_lock = models.BooleanField(verbose_name='收入确认', blank=True, default=False)
    cost_lock = models.BooleanField(verbose_name='成本确认', blank=True, default=False)
    is_valuation = models.BooleanField(verbose_name='是否收入计价', default=False)
    is_cost_valuation = models.BooleanField(verbose_name='是否成本计价', default=False)
    is_virtual = models.BooleanField(verbose_name='是否虚拟产品', default=False)
    strategy_type = models.CharField(max_length=30, choices=STRATEGY_TYPE, verbose_name='策略类型', null=True,
                                     blank=True)
    relate_products = models.CharField(max_length=200, verbose_name='关联产品id集', blank=True, null=True)
    is_open = models.BooleanField(verbose_name='是否对外开放', default=False)
    address_num = models.CharField(max_length=100, verbose_name='仓库编码', blank=True, null=True)
    charge_weight_rate = models.DecimalField(max_digits=10, decimal_places=0, verbose_name='计费重转换率', null=True,
                                             blank=True)
    round_of_factor = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='计费进位', null=True,
                                          blank=True)
    push_order_type = models.CharField(max_length=10, verbose_name='推送单号类型', choices=ORDER_TYPE, default='S')
    pricing_type = models.CharField(max_length=48, choices=PRICING_TYPE, verbose_name='定价方式', null=True, blank=True)
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    business_type = models.CharField(max_length=10, choices=BUSINESS_TYPE, verbose_name='业务类型', default='AD')
    is_upload_label = models.BooleanField(verbose_name='是否允许客户上传面单', default=False, null=True, blank=True)
    display_sort = models.IntegerField(null=True, blank=True, default=0, verbose_name="展示排序")
    is_address_validation = models.BooleanField(verbose_name='是否允许地址校验', default=False, null=True, blank=True)  # 验证商业住址、私人住址、未知

    class Meta:
        verbose_name_plural = '产品'
        verbose_name = '产品'
        ordering = ['-id']

    def __str__(self):
        return self.name


# 产品组合
class ProductCombination(BaseEntity):
    TRIGGER_TYPE = [
        ('ORDER', '下单触发'),
        ('REPLACE_ORDER', '换单触发'),
    ]

    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='product_combination')
    sub_product = models.ForeignKey(Product, verbose_name='子产品', null=True, blank=True, on_delete=models.SET_NULL,
                                    related_name='sub_product_combination')
    trigger_type = models.CharField(max_length=30, choices=TRIGGER_TYPE, verbose_name='触发类型', default='ORDER')
    country_code = models.CharField(max_length=10, verbose_name='国家编码', blank=True, null=True)

    class Meta:
        verbose_name_plural = '产品组合'
        verbose_name = '产品组合'
        ordering = ['-id']

    def __str__(self):
        return f'{self.product}'


# 产品税号
class ProductTax(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='中文品名', blank=True, null=True)
    name_en = models.CharField(max_length=100, verbose_name='英文品名', blank=True, null=True)
    export_tax_rebate = models.CharField(max_length=10, verbose_name='出口退税', blank=True, null=True)
    import_tariffs = models.CharField(max_length=10, verbose_name='进口退税', blank=True, null=True)
    cn_tariff_code = models.CharField(max_length=50, verbose_name='cn税则编码', blank=True, null=True)
    uk_tariff_code = models.CharField(max_length=50, verbose_name='uk税则编码', blank=True, null=True)
    us_tariff_code = models.CharField(max_length=50, verbose_name='us税则编码', blank=True, null=True)
    au_tariff_code = models.CharField(max_length=50, verbose_name='au税则编码', blank=True, null=True)
    de_tariff_code = models.CharField(max_length=50, verbose_name='de税则编码', blank=True, null=True)

    class Meta:
        verbose_name_plural = '产品税号'
        verbose_name = '产品税号'
        ordering = ['-id']

    def __str__(self):
        return self.name


# 产品轨迹配置
class ProductTrackCode(BaseEntity):
    aging = models.IntegerField(verbose_name='时效')
    sort = models.IntegerField(verbose_name='排序')
    track_code = models.ForeignKey(TrackCode, on_delete=models.DO_NOTHING, null=True)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, null=True, related_name='product_track_code')


# 服务（资源）
class Service(BaseEntity):
    TYPE = [
        ('A', '整单下单'),
        ('S', '拆单包下单'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    is_default = models.BooleanField(verbose_name='是否默认服务', default=False)
    trackSupplier = models.ForeignKey(TrackSupplier, on_delete=models.DO_NOTHING, null=True, blank=True,
                                      verbose_name='轨迹供应商')
    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='product_service')
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='服务类型', default='A')
    is_print = models.BooleanField(verbose_name='是否获取面单', default=False)
    is_manifest = models.BooleanField(verbose_name='是否推送mainfest', default=False)
    is_confirm = models.BooleanField(verbose_name='是否手动确认', default=False)
    extend_field = models.TextField(verbose_name='扩展字段', null=True, blank=True)

    name = models.CharField(max_length=100, verbose_name='配送资源名称')
    delivery_code = models.CharField(max_length=100, verbose_name='配送资源编码', null=True, blank=True)
    code = models.CharField(max_length=100, verbose_name='服务编码', blank=True, null=True)
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    butt_code = models.ForeignKey(SupplierButt, verbose_name='对接编码', null=True, blank=True,
                                  on_delete=models.SET_NULL)
    courier_code = models.CharField(max_length=100, verbose_name='尾程物流商编码', blank=True, null=True)
    status = models.CharField(max_length=10, choices=STATUS_TYPE, verbose_name='状态', default='DR', blank=True,
                              null=True)

    class Meta:
        verbose_name_plural = '服务'
        verbose_name = '服务'

    def __str__(self):
        return self.name


# 产品分区 (分区主表)
class ProductZone(BaseEntity):
    TYPE = [
        ('Buyer', '买家'),
        ('Seller', '卖家'),
        ('product', '产品'),
        ('service', '资源'),
    ]
    ZONE_TYPE = [

        ('SZ', '服务国家分区'),
        ('CZ', '应收计费分区'),
        ('PZ', '偏远分区'),
        ('NZ', '不可达分区'),

        ('CS', '渠道派送分区'),
        ('EF', '应付计费分区'),
        ('PF', '资源偏远分区'),
        ('NF', '资源不可达分区'),

    ]
    ZONE_MODEL = [
        ('NR', '普通分区'),
        ('PZ', '偏远分区'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    name = models.CharField(max_length=100, verbose_name='分区名称', blank=True, null=True)
    code = models.CharField(max_length=100, verbose_name='分区编码', blank=True, null=True)
    country_code = models.CharField(max_length=100, verbose_name='国家编码', blank=True, null=True)
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='类型', default='Buyer')
    zone_type = models.CharField(max_length=3, choices=ZONE_TYPE, verbose_name='分区类型', default='SZ')
    zone_model = models.CharField(max_length=3, choices=ZONE_MODEL, verbose_name='分区模式', default='NR', blank=True, null=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE, null=True,
                              blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '产品分区'
        verbose_name_plural = '产品分区'
        indexes = [
            models.Index(fields=['country_code', 'del_flag']),
            models.Index(fields=['name', 'del_flag']),
        ]

    def __str__(self):
        return self.name


# 产品分区邮编(分区明细)
class ProductZonePostCode(BaseEntity):
    TYPE = [
        ('AC', '精准邮编'),
        ('IS', '区间段(用~分割)'),
    ]

    product_zone = models.ForeignKey(ProductZone, related_name='product_local', verbose_name='产品分区',
                                     on_delete=models.DO_NOTHING, null=True)
    country_code = models.CharField(max_length=100, verbose_name='国家编码', blank=True, null=True)
    post_code = models.CharField(max_length=100, verbose_name='邮编')
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='类型', default='AC')

    name = models.CharField(max_length=100, verbose_name='分区名称', blank=True, null=True)
    code = models.CharField(max_length=100, verbose_name='分区编码', blank=True, null=True)
    state_code = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True)
    city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '产品分区邮编'
        verbose_name_plural = '产品分区邮编'
        indexes = [
            models.Index(fields=['product_zone', 'del_flag']),
            models.Index(fields=['product_zone', 'post_code', 'del_flag']),
            models.Index(fields=['product_zone', 'type', 'post_code', 'del_flag']),
        ]

    def __str__(self):
        return f'{self.product_zone}'


# 分拣设备配置
class SortingEquipment(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    status = models.CharField(max_length=10, choices=STATUS_TYPE, verbose_name='状态', default='DR')
    name = models.CharField(max_length=50, verbose_name='分拣设备名称', blank=True, null=True)
    code = models.CharField(max_length=30, verbose_name='分拣设备代码', blank=True, null=True)
    warehouse = models.ForeignKey(Organization, verbose_name='所属仓库', on_delete=models.DO_NOTHING, null=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '分拣设备配置'
        verbose_name_plural = '分拣设备配置'

    def __str__(self):
        return f'{self.name}: {self.code}'


# 分拣设备格口
class SortingEquipmentGrid(BaseEntity):
    sorting_equipment = models.ForeignKey(SortingEquipment, verbose_name='分拣设备',
                                          related_name="sorting_equipment_grids",
                                          on_delete=models.DO_NOTHING, null=True, blank=True)
    equipment_grid = models.CharField(max_length=10, verbose_name='设备格口', blank=True, null=True)
    group_grid = models.CharField(max_length=10, verbose_name='新设备组格口', blank=True, null=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '分拣设备格口'
        verbose_name_plural = '分拣设备格口'

    def __str__(self):
        return f'{self.sorting_equipment}: {self.equipment_grid}'


# 分拣分区
class SortingZone(BaseEntity):
    TYPE = [
        ('A', '禁止完成组包'),
        ('B', '界面提示确认'),
    ]

    name = models.CharField(max_length=50, verbose_name='分拣分区名称', blank=True, null=True)
    code = models.CharField(max_length=30, verbose_name='分拣分区ID', blank=True, null=True)
    custom_code = models.CharField(max_length=30, verbose_name='分拣分区业务自定义代码', blank=True, null=True)
    bag_label_template = models.CharField(max_length=50, verbose_name='袋标模版', blank=True, null=True)
    bag_num_rule = models.CharField(max_length=10, verbose_name='袋号生成规则前缀', blank=True, null=True)
    date_rule = models.CharField(max_length=10, verbose_name='日期规则', blank=True, null=True)
    serial_number = models.IntegerField(verbose_name='序列号位数规则', blank=True, null=True)
    bag_print_num = models.IntegerField(verbose_name='袋标打印份数', blank=True, null=True)
    bag_min_weight = models.DecimalField(max_digits=10, decimal_places=0, verbose_name='组包最小重量', null=True,
                                         blank=True)
    bag_max_weight = models.DecimalField(max_digits=10, decimal_places=0, verbose_name='组包最大重量', null=True,
                                         blank=True)
    allow_weight_error_min = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='允许最小重量误差',
                                                 null=True, blank=True)
    allow_weight_error_max = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='允许最大重量误差',
                                                 null=True, blank=True)
    discontent_weight = models.CharField(max_length=3, choices=TYPE, verbose_name='不满足重量操作模式', default='B')
    inject_port = models.CharField(max_length=10, verbose_name='注入口岸', blank=True, null=True)
    flight_port = models.CharField(max_length=10, verbose_name='航班口岸', blank=True, null=True)
    use_logistics_rule = models.CharField(max_length=100, verbose_name='使用物料规则', blank=True, null=True)
    operation_instructions = models.CharField(max_length=300, verbose_name='操作提示说明', blank=True, null=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '分拣分区'
        verbose_name_plural = '分拣分区'

    def __str__(self):
        return f'{self.name}: {self.code}'


# 线路
class ProductLine(BaseEntity):
    TYPE = [
        ('A', '自营线路'),
        ('B', '外采线路'),
        ('C', '帐号打单'),
        ('D', '邮政线路'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    BUSINESS_TYPE = [
        ('WW', '仓内作业'),
        ('AD', '账号打单'),
    ]
    name = models.CharField(max_length=100, verbose_name='线路名称', blank=True, null=True)
    code = models.CharField(max_length=100, verbose_name='线路编码', blank=True, null=True)
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='线路类型', default='A')
    status = models.CharField(max_length=10, choices=STATUS_TYPE, verbose_name='状态', default='DR')
    delivery_service = models.ForeignKey(Service, verbose_name='配送资源', related_name='line_delivery_service',
                                         on_delete=models.DO_NOTHING, null=True)
    classification = models.CharField(verbose_name='货物性质', max_length=100, blank=True, null=True)
    business_type = models.CharField(max_length=10, choices=BUSINESS_TYPE, verbose_name='业务类型', default='AD')

    class Meta:
        ordering = ['-id']
        verbose_name = '线路'
        verbose_name_plural = '线路'

    def __str__(self):
        return f'{self.name}: {self.code}'


# 分拣计划配置
class SortingPlanSetting(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    status = models.CharField(max_length=10, choices=STATUS_TYPE, verbose_name='状态', default='DR')

    name = models.CharField(max_length=50, verbose_name='分拣计划名称', blank=True, null=True)
    code = models.CharField(max_length=30, verbose_name='分拣计划代码', blank=True, null=True)
    sorting_equipment = models.ForeignKey(SortingEquipment, verbose_name='分拣设备',
                                          on_delete=models.DO_NOTHING, null=True, blank=True)
    warehouse = models.ForeignKey(Organization, verbose_name='所属仓库', on_delete=models.DO_NOTHING, null=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '分拣计划配置'
        verbose_name_plural = '分拣计划配置'

    def __str__(self):
        return f'{self.name}: {self.code}'


# 分拣计划明细
class SortingPlanDetail(BaseEntity):
    sorting_plan_setting = models.ForeignKey(SortingPlanSetting, verbose_name='分拣计划配置',
                                             related_name="sorting_plan_details",
                                             on_delete=models.DO_NOTHING, null=True, blank=True)

    grid = models.CharField(max_length=50, verbose_name='设备逻辑格口', blank=True, null=True)
    line = models.ForeignKey(ProductLine, verbose_name='线路',
                             on_delete=models.DO_NOTHING, null=True, blank=True)
    sorting_zone = models.ForeignKey(SortingZone, verbose_name='分拣分区',
                                     on_delete=models.DO_NOTHING, null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '分拣计划明细'
        verbose_name_plural = '分拣计划明细'

    def __str__(self):
        return f'{self.sorting_plan_setting}: {self.grid}'


# 分拣方案设置
class LineSortingSetting(BaseEntity):
    line = models.ForeignKey(ProductLine, verbose_name='线路', related_name="line_sorting_settings",
                             on_delete=models.DO_NOTHING, null=True, blank=True)

    product_zone = models.ForeignKey(ProductZone, verbose_name='派送分区',
                                     on_delete=models.DO_NOTHING, null=True, blank=True)

    sorting_zone = models.ForeignKey(SortingZone, verbose_name='分拣分区',
                                     on_delete=models.DO_NOTHING, null=True, blank=True)

    post_code_name = models.ForeignKey(ProductZonePostCode, verbose_name='服务分区邮编',
                                       related_name="post_code_names_sort",
                                       on_delete=models.DO_NOTHING, null=True, blank=True)
    country_code = models.CharField(max_length=100, verbose_name='国家编码', blank=True, null=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '分拣方案设置'
        verbose_name_plural = '分拣方案设置'

    def __str__(self):
        return f'LineSortingSetting: {self.line}'


# 线路重量修正
class LineWeightRevision(BaseEntity):
    line = models.ForeignKey(ProductLine, verbose_name='线路', related_name="line_weight_revisions",
                             on_delete=models.DO_NOTHING, null=True, blank=True)

    start_weight = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='开始重量', null=True, blank=True)
    end_weight = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='结束重量', null=True, blank=True)
    revision_weight = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='修正重量', null=True,
                                          blank=True)
    revision_length = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='修正长', null=True, blank=True)
    revision_width = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='修正宽', null=True, blank=True)
    revision_height = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='修正高', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '线路重量修正'
        verbose_name_plural = '线路重量修正'

    def __str__(self):
        return f'LineWeightRevision: {self.line}'


# 线路面单修正
class LineLabelRevision(BaseEntity):
    line = models.ForeignKey(ProductLine, verbose_name='线路', related_name="line_label_revisions",
                             on_delete=models.DO_NOTHING, null=True, blank=True)

    x_coordinate = models.IntegerField(verbose_name='x坐标(mm)', null=True, blank=True)
    y_coordinate = models.IntegerField(verbose_name='y坐标(mm)', null=True, blank=True)
    coverage_size_height = models.IntegerField(verbose_name='覆盖尺寸高(mm)', null=True, blank=True)
    coverage_size_width = models.IntegerField(verbose_name='覆盖尺寸宽(mm)', null=True, blank=True)
    font_szie = models.IntegerField(verbose_name='字体大小(px)', null=True, blank=True)

    TYPE = [
        ('A', '客户单号'),
        ('B', '固定值'),
    ]
    filling_type = models.CharField(max_length=10, verbose_name='填充类型', null=True, blank=True)
    filling_value = models.CharField(max_length=10, verbose_name='填充值', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '线路面单修正'
        verbose_name_plural = '线路面单修正'

    def __str__(self):
        return f'LineLabelRevision: {self.line}'


# 线路SLA刻画
class LineSlaPlan(BaseEntity):
    line = models.ForeignKey(ProductLine, verbose_name='线路', related_name="line_sla_plan",
                             on_delete=models.DO_NOTHING, null=True, blank=True)

    sla_time = models.IntegerField(verbose_name='时效(小时)', null=True, blank=True)
    start_node = models.CharField(max_length=10, verbose_name='开始订单节点', null=True, blank=True)
    end_node = models.CharField(max_length=10, verbose_name='结束订单节点', null=True, blank=True)
    TYPE = [
        ('A', '标准SLA刻画'),
        ('B', '自定义SLA刻画'),
    ]
    type = models.CharField(max_length=2, verbose_name='SLA刻画类型', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '线路SLA刻画'
        verbose_name_plural = '线路SLA刻画'

    def __str__(self):
        return f'LineSlaPlan: {self.line}'


# 产品路线 (路由)
class ProductRoute(BaseEntity):
    ROUTE_TYPE = [
        ('COMMON', '普通路线'),
        ('PROTOCOL_ROUTE', '协议路线'),
    ]
    product = models.ForeignKey(Product, verbose_name='产品', related_name='product_route', on_delete=models.DO_NOTHING,
                                null=True)
    start_zone = models.ForeignKey(ProductZone, verbose_name='产品起始分区', related_name="route_start_zone_id",
                                   on_delete=models.DO_NOTHING, null=True)
    end_zone = models.ForeignKey(ProductZone, verbose_name='产品终点分区', related_name="route_end_zone_id",
                                 on_delete=models.DO_NOTHING, null=True, blank=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    zone_value = models.CharField(max_length=48, verbose_name='分区值', null=True, blank=True)
    route_type = models.CharField(max_length=32, verbose_name='路线类型', choices=ROUTE_TYPE, default='COMMON')

    TYPE = [
        ('weight', '实重'),
        ('chargeWeight', '计费重'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    name = models.CharField(max_length=100, verbose_name='路由方案名称', blank=True, null=True)
    code = models.CharField(max_length=100, verbose_name='路由方案编码', blank=True, null=True)
    warehouse = models.ForeignKey(Address, verbose_name='生效仓库', on_delete=models.DO_NOTHING, null=True)
    router_warehouse = models.ForeignKey(Organization, verbose_name='组织架构', on_delete=models.DO_NOTHING, null=True)
    type = models.CharField(max_length=20, choices=TYPE, verbose_name='类型', default='weight')
    is_cost_priority = models.BooleanField(verbose_name="是否成本优先", default=False, blank=True, null=True)
    status = models.CharField(max_length=10, choices=STATUS_TYPE, verbose_name='状态', default='DR')

    class Meta:
        ordering = ['-id']
        verbose_name = '产品路线'
        verbose_name_plural = '产品路线'
        indexes = [
            models.Index(fields=['product', 'start_zone', 'end_zone', 'del_flag']),
        ]

    def __str__(self):
        return f'{self.product}: {self.start_zone}: {self.end_zone}'


# 产品路由明细
class ProductRouteDetail(BaseEntity):
    code = models.CharField(max_length=100, verbose_name='路由明细编码', blank=True, null=True)
    product_route = models.ForeignKey(ProductRoute, verbose_name='产品路由', related_name='product_route_details',
                                      on_delete=models.DO_NOTHING,
                                      null=True)
    sz_zone = models.ForeignKey(ProductZone, verbose_name='服务分区', related_name="route_detail_sz_zones",
                                on_delete=models.DO_NOTHING, null=True, blank=True)
    post_code_name = models.ForeignKey(ProductZonePostCode, verbose_name='服务分区邮编', related_name="post_code_names",
                                       on_delete=models.DO_NOTHING, null=True, blank=True)
    country_code = models.CharField(max_length=100, verbose_name='国家编码', blank=True, null=True)

    weight_among = models.CharField(max_length=100, verbose_name='重量区间', null=True, blank=True)
    price_among = models.CharField(max_length=100, verbose_name='商品价值区间', null=True, blank=True)
    parcel_size_among = models.CharField(max_length=100, verbose_name='包裹尺寸区间', null=True, blank=True)

    line = models.ForeignKey(ProductLine, verbose_name='服务线路', related_name="route_detail_line",
                             on_delete=models.DO_NOTHING, null=True, blank=True)

    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    status = models.CharField(max_length=10, choices=STATUS_TYPE, verbose_name='状态', default='DR')

    class Meta:
        # ordering = ['-id']
        verbose_name = '产品路由明细'
        verbose_name_plural = '产品路由明细'

    def __str__(self):
        return f'{self.product_route}:{self.sz_zone}'


# 计费规则
class ChargingRule(BaseEntity):
    CHARGING_MODE = [
        ('PR', '预报重量计费'),
        ('CW', '仓内重量计费'),
        ('QD', '预报重仓库重取大计费'),
    ]

    CHARGING_NODE = [
        ('PR', '预报'),
        ('IN', '入库'),
        ('ZB', '组包'),
        ('FY', '发运'),
        ('JH', '交航'),
        ('QF', '起飞'),
        ('LD', '落地'),
        ('CF', '清关完成'),
        ('MD', '末端上网'),
        ('PS', '派送完成'),
    ]

    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='charging_rules')
    charging_mode = models.CharField(max_length=3, choices=CHARGING_MODE, verbose_name='计费模式', default='PR')
    charging_node = models.CharField(max_length=3, choices=CHARGING_NODE, verbose_name='计费节点', default='PR')

    class Meta:
        ordering = ['-id']
        verbose_name = '计费规则'
        verbose_name_plural = '计费规则'

    def __str__(self):
        return f'{self.product}'


# 产品收费项 （计费重量规则）
class ProductCharge(BaseEntity):
    UNIT = [
        ('weight', '重量'),
        ('volume', '体积'),
        ('chargeWeight', '计费重'),
        ('confirmChargeWeight', '确认计费重'),
        ('carton', '件数'),
        ('SKUCarton', 'SKU预报件数'),
        ('SKUShelfCarton', 'SKU上架件数'),
        ('SKUCategories', 'SKU 种类数量'),
        ('oneSKUWeight', '单件sku重量'),
        ('newSkuQuantity', 'SKU 新品数量'),
        ('oneParcelSKUCarton', '单件包裹sku件数'),
        ('mix', '混合'),
        ('combine', '组合收费'),
        ('boxWeight', '箱数重量'),
        ('confirmChargeVolume', '确认计费体积'),
        ('halfDisposal', '半抛'),
        ('numberOfStrokes', '打板数'),
        ('numberOfTrucks', '用车数'),
        ('bubble', '泡比'),
        ('parcel_weight', '包裹重量'),
        ('actual_weight', '包裹实际重量'),
        ('parcel_confirm_weight', '包裹确认计费重'),
        ('20GP', '20GP整柜'),
        ('40GP', '40GP整柜'),
        ('40HQ', '40HQ整柜'),
        ('45HQ', '45HQ整柜'),
        ('palletQty', '托盘数'),
        ('ladingWeight', '清关提单重量'),
    ]

    BELONG = [
        ('default', '默认'),
        ('income', '收入'),
        ('cost', '成本'),
    ]

    name = models.CharField(max_length=300, verbose_name='收费项名称', null=True, blank=True)
    product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True,
                                related_name='product_charge')
    charge_desc = models.CharField(max_length=200, verbose_name='计费说明', null=True, blank=True)
    round_of_factor = models.CharField(max_length=10, verbose_name='计费进位单位', null=True, blank=True)
    more_charge_weight = models.DecimalField(max_digits=16, decimal_places=6, verbose_name='包裹最大限重', default=0)
    less_charge_weight = models.DecimalField(max_digits=16, decimal_places=6, verbose_name='整单最小限重', default=0)
    relate_charges = models.CharField(max_length=200, verbose_name='组合费用', blank=True, null=True)
    charge_combine_rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='组合收费比例', default=0)
    # 产品详情中的产品收费项的是否拦截去掉(不开启), 默认不拦截, 如果需要拦截就去产品拦截规则中单独配
    is_intercept = models.BooleanField(verbose_name="是否拦截", default=False, blank=True, null=True)

    country_code = models.CharField(max_length=3, verbose_name='收件国家', blank=True, null=True)
    charge = models.ForeignKey(Charge, verbose_name='费用项', on_delete=models.DO_NOTHING, null=True)
    charge_unit = models.CharField(max_length=64, choices=UNIT, verbose_name='计费单位', default='weight')
    charge_weight_rate = models.DecimalField(max_digits=10, decimal_places=0, verbose_name='计费重转换率', default=0)
    bubble_ratio = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='泡比系数', default=0)

    attribution_expenses = models.CharField(max_length=64, choices=BELONG, verbose_name='归属费用', default='default', null=True, blank=True)

    class Meta:
        verbose_name_plural = '产品收费项'
        verbose_name = '产品收费项'
        ordering = ['id']
        # unique_together = (('product', 'name', 'del_flag'),)

    def __str__(self):
        return f"({self.name}|{self.id})"


class ReceivingRule(BaseEntity):
    TYPE = [
        ('R', '拒收规则'),
        ('C', '国家约束'),
        ('F', '附加费规则')
    ]

    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='receiving_rules')
    type = models.CharField(max_length=2, choices=TYPE, verbose_name='规则类型', default='R')
    constraint_rules = models.CharField(max_length=1000, verbose_name='约束规则', null=True, blank=True)
    country_code = models.CharField(max_length=4, verbose_name='国家编码', null=True, blank=True)
    charge = models.ForeignKey(ProductCharge, verbose_name='收费项', null=True, blank=True, on_delete=models.SET_NULL,
                               related_name='receiving_charge')

    class Meta:
        ordering = ['-id']
        verbose_name = '收货规则'
        verbose_name_plural = '收货规则'

    def __str__(self):
        return f'{self.constraint_rules}'


# 产品附加收费规则
class ProductExtraChargeRule(BaseEntity):
    charge = models.ForeignKey(ProductCharge, verbose_name='产品收费项', on_delete=models.DO_NOTHING,
                               null=True, related_name='product_extra_charge_rule')
    left_bracket = models.CharField(max_length=255, verbose_name='左括号')
    criteria_code = models.CharField(max_length=255, verbose_name='计费要素')
    match_method = models.CharField(max_length=255, verbose_name='条件')
    condition_value = models.CharField(max_length=255, verbose_name='值')
    criteria_type = models.CharField(max_length=255, verbose_name='值类型')
    right_bracket = models.CharField(max_length=255, verbose_name='右括号')
    logical_operator = models.CharField(max_length=255, verbose_name='关系')
    serial_code = models.CharField(max_length=255, verbose_name='顺序')
    receiving_rule = models.ForeignKey(ReceivingRule, verbose_name='收货规则', null=True, blank=True, on_delete=models.SET_NULL,
                               related_name='receiving_rule_extra_charge_rule')

    class Meta:
        verbose_name = '适用条件'
        verbose_name_plural = '适用条件'

    def __str__(self):
        return f'ID: {self.id}, Criteria Code: {self.criteria_code}'

# 适用条件模板
class ProductExtraChargeRuleTemplate(BaseEntity):
    name = models.CharField(max_length=255, verbose_name='模板名称', null=True, blank=True,default='template')
    left_bracket = models.CharField(max_length=255, verbose_name='左括号',null=True, blank=True)
    criteria_code = models.CharField(max_length=255, verbose_name='计费要素',null=True, blank=True)
    match_method = models.CharField(max_length=255, verbose_name='条件',null=True, blank=True)
    condition_value = models.CharField(max_length=255, verbose_name='值',null=True, blank=True)
    # criteria_type = models.CharField(max_length=255, verbose_name='值类型',null=True, blank=True)
    right_bracket = models.CharField(max_length=255, verbose_name='右括号',null=True, blank=True)
    logical_operator = models.CharField(max_length=255, verbose_name='关系', null=True, blank=True)
    # serial_code = models.CharField(max_length=255, verbose_name='顺序', null=True, blank=True)

    class Meta:
        verbose_name = '适用条件模板'
        verbose_name_plural = '适用条件模板'

    def __str__(self):
        return f'ID: {self.id}, Criteria Code: {self.criteria_code}'

# 产品折扣
class ProductDiscount(BaseEntity):
    TYPE = [
        ('P', '指定产品'),
        ('A', '所有产品'),
    ]

    type = models.CharField(max_length=10, choices=TYPE, verbose_name='折扣类型', default='P')
    product = models.ForeignKey(Product, verbose_name='指定产品', on_delete=models.DO_NOTHING, null=True,
                                related_name='product_discount')

    customer = models.ForeignKey(Company, verbose_name='归属客户', on_delete=models.DO_NOTHING, null=True)

    rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='折扣率')

    class Meta:
        verbose_name_plural = '产品折扣'
        verbose_name = '产品折扣'
        ordering = ['-id']

    def __str__(self):
        return self.type


# 旧：产品客户限制 新：黑白名单
class ProductLimitUser(BaseEntity):
    TYPE = [
        ('B', '黑名单'),
        ('W', '白名单'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    product = models.ForeignKey(Product, verbose_name='指定产品', on_delete=models.DO_NOTHING, null=True,
                                related_name='product_limit_user')

    customer = models.ForeignKey(Company, verbose_name='归属客户', on_delete=models.DO_NOTHING, null=True)

    type = models.CharField(max_length=2, choices=TYPE, verbose_name='类型', default='W')
    country_code = models.CharField(max_length=3, verbose_name='国家编码', null=True, blank=True)

    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE, null=True,
                              blank=True)

    class Meta:
        verbose_name_plural = '产品客户限制(黑白名单)'
        verbose_name = '产品客户限制(黑白名单)'
        ordering = ['-id']

    def __str__(self):
        return f'{self.product}'


# 产品基本限制(订单校验)
class ProductBasicRestriction(BaseEntity):
    TYPE = [
        ('A', '属性限制'),
        ('B', '邮编限制'),
        ('C', '扩展限制'),
    ]
    BELONG_TYPE = [
        ('A', '订单'),
        ('B', '包裹'),
        ('C', '收件人'),
        ('D', '发件人'),
        ('E', '商品'),
        ('F', '海外退件地址'),
    ]
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='属性类型', default='A')
    belong_type = models.CharField(max_length=10, choices=BELONG_TYPE, verbose_name='所属类型', default='A')
    product = models.ForeignKey(Product, verbose_name='指定产品', on_delete=models.DO_NOTHING, null=True,
                                related_name='product_basic_restriction')
    prefix_word = models.TextField(verbose_name='前缀词', null=True, blank=True)
    product_zone = models.ForeignKey(ProductZone, related_name='product_zone', verbose_name='产品分区限制, 废弃',
                                     on_delete=models.DO_NOTHING, null=True, blank=True)
    product_zone_limit = models.CharField(max_length=255, verbose_name='产品分区限制', null=True, blank=True)
    censored_word = models.TextField(verbose_name='屏蔽词', null=True, blank=True)
    suffix_word = models.CharField(max_length=2500, verbose_name='后缀词', null=True, blank=True)
    allow_character = models.CharField(max_length=2500, verbose_name='允许字符', null=True, blank=True)
    encoding = models.CharField(max_length=255, verbose_name="限制字段", null=True, blank=True)
    checksum_type = models.CharField(max_length=255, verbose_name="限制字段中文名", null=True, blank=True)
    field_length = models.CharField(max_length=255, verbose_name="字段长度", null=True, blank=True)
    is_required = models.BooleanField(verbose_name="是否必填", default=False)
    chinese = models.BooleanField(verbose_name="中文", default=False)
    english = models.BooleanField(verbose_name="英文", default=False)
    number = models.BooleanField(verbose_name="数字", default=False)
    contain_alp_num = models.BooleanField(verbose_name="包含数字和英文", default=False)
    restriction_postcode = models.BooleanField(verbose_name="邮编限制", default=False)
    insert_trick = models.BooleanField(verbose_name="条件", default=False)
    is_show = models.BooleanField(verbose_name="是否显示", default=True)
    default_value = models.CharField(max_length=500, verbose_name='默认值', null=True, blank=True)
    limit_effect_country = models.CharField(max_length=500, verbose_name='限制生效国家', null=True, blank=True)

    regular_expression = models.CharField(max_length=500, verbose_name='正则表达', null=True, blank=True)
    abnormal_prompt = models.CharField(max_length=500, verbose_name='异常提示', null=True, blank=True)
    data_source = models.CharField(max_length=64, verbose_name="数据来源(第三方校验接口)", null=True, blank=True)

    class Meta:
        verbose_name_plural = '产品基本限制'
        verbose_name = '产品基本限制'
        ordering = ['-id']

    def __str__(self):
        return f'{self.product}'


# 产品基本限制配置
class ProductBasicRestrictionProfile(BaseEntity):
    TYPE = [
        ('A', '属性限制'),
        ('B', '邮编限制'),
        ('C', '扩展限制'),
    ]
    BELONG_TYPE = [
        ('A', '订单'),
        ('B', '包裹'),
        ('C', '收件人'),
        ('D', '发件人'),
        ('E', '商品'),
        ('F', '海外退件地址'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='属性类型', default='A')
    belong_type = models.CharField(max_length=10, choices=BELONG_TYPE, verbose_name='所属类型', default='A')
    encoding = models.CharField(max_length=255, verbose_name="限制字段", null=True, blank=True)
    checksum_type = models.CharField(max_length=255, verbose_name="限制字段中文名", null=True, blank=True)
    data_source = models.CharField(max_length=64, verbose_name="数据来源(第三方校验接口)", null=True, blank=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE, null=True,
                              blank=True)

    class Meta:
        verbose_name_plural = '产品基本限制配置'
        verbose_name = '产品基本限制配置'
        ordering = ['-id']

    def __str__(self):
        return self.encoding


# 产品协议方案
class ProtocolProject(BaseEntity):
    STATUS_TYPE = [
        ('ON', '生效'),
        ('OFF', '失效'),
        ('AF', '审核中'),
        ('RE', '审核不通过'),
    ]
    status = models.CharField(max_length=10, verbose_name='状态', default='ON')
    code = models.CharField(max_length=50, verbose_name='协议方案编码', blank=True, null=True)
    name = models.CharField(max_length=50, verbose_name='协议方案名称', blank=True, null=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)
    reviewer = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name='审核人', on_delete=models.SET_NULL, null=True,
                                 blank=True)
    audit_date = models.DateTimeField(verbose_name='审核时间', null=True, blank=True)

    class Meta:
        verbose_name_plural = '产品协议方案'
        verbose_name = '产品协议方案'
        ordering = ['-id']

    def __str__(self):
        return self.code


# 协议方案客户
class ProtocolProjectCustomer(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '停用'),
    ]
    status = models.CharField(max_length=10, verbose_name='状态', default='ON')
    customer = models.ForeignKey(Company, verbose_name='客户', on_delete=models.DO_NOTHING, null=True)
    protocol_project = models.ForeignKey(ProtocolProject, verbose_name='协议方案', null=True, blank=True,
                                         on_delete=models.SET_NULL, related_name="protocol_project_customer")

    class Meta:
        verbose_name_plural = '协议方案客户'
        verbose_name = '协议方案客户'
        ordering = ['-id']

    def __str__(self):
        return f'{self.protocol_project}'


# 收入价格版本
class ProductRevenueVersion(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
        ('KRW', 'KRW'),
        ('MXN', 'MXN'),
    ]

    PRICE_TYPE = [
        ('A', '公布价'),
        ('B', '协议价'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    version_name = models.CharField(max_length=100, verbose_name='版本名称', null=True, blank=True)
    product = models.ForeignKey(Product, verbose_name='产品', null=True, on_delete=models.DO_NOTHING)
    product_charge = models.ForeignKey(ProductCharge, verbose_name='产品费用项', on_delete=models.DO_NOTHING, null=True,
                                       blank=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    # end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)
    currency = models.CharField(max_length=3, verbose_name='币种', null=True, blank=True)
    price_type = models.CharField(max_length=2, verbose_name='价格类型', choices=PRICE_TYPE, default='A')
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE, null=True,
                              blank=True)

    class Meta:
        verbose_name_plural = '收入价格版本'
        verbose_name = '收入价格版本'
        ordering = ['-id']

    def __str__(self):
        return f"({self.version_name}| id:{self.id})"


# 收入价格明细
class ProductRevenueVersionLine(BaseEntity):
    TYPE = [
        ('ZJ', '总价'),
        ('DJ', '单价'),
        ('TZJ', '递增价'),
    ]
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
        ('KRW', 'KRW'),
        ('MXN', 'MXN'),
    ]
    SOURCE_TYPE = [
        ('S', '系统添加'),
        ('A', '手动录入'),
    ]
    ZONE_TYPE = [
        ('SZ', '服务国家分区'),
        ('CZ', '应收计费分区'),
        ('PZ', '偏远分区'),
        ('NZ', '不可达分区'),
    ]

    price_version = models.ForeignKey(ProductRevenueVersion, verbose_name='收入价格版本', on_delete=models.DO_NOTHING,
                                      null=True)
    zone_value = models.CharField(max_length=48, verbose_name='分区值', null=True, blank=True)
    zone = models.ForeignKey(ProductZonePostCode, verbose_name='分区', on_delete=models.DO_NOTHING, null=True,
                             blank=True)
    charge_type = models.CharField(max_length=10, choices=TYPE, verbose_name='计费方式', default='ZJ')
    currency = models.CharField(max_length=100, verbose_name='币种')
    rank_start = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级起点')
    rank_end = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级终点')
    price = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='价格')
    base_price = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='基础价格', default=0, null=True,
                                     blank=True)
    increased_rank = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='递增等级', default=0, null=True,
                                         blank=True)
    # 计费进位,取值限制为0.1,0.5,1中的一个
    round_of_factor = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='计费进位', default=0,
                                          null=True, blank=True)
    data_source = models.CharField(max_length=2, choices=SOURCE_TYPE, verbose_name='数据来源', default="A")

    charge = models.ForeignKey(ProductCharge, verbose_name='费用项', on_delete=models.DO_NOTHING, null=True, blank=True)
    zone_type = models.CharField(max_length=3, verbose_name='分区类型', choices=ZONE_TYPE, null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '收入价格明细'
        verbose_name_plural = '收入价格明细'

    def __str__(self):
        return f'{self.price_version}'


# 协议方案产品
class ProtocolProjectProduct(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '停用'),
    ]
    status = models.CharField(max_length=10, verbose_name='状态', default='ON')
    product_revenue_version = models.ForeignKey(ProductRevenueVersion, verbose_name='协议方案价格',
                                                on_delete=models.SET_NULL, null=True,
                                                related_name='protocol_project_version')
    protocol_project = models.ForeignKey(ProtocolProject, verbose_name='协议方案', null=True, blank=True,
                                         on_delete=models.SET_NULL, related_name='protocol_project_product')

    class Meta:
        verbose_name_plural = '协议方案产品'
        verbose_name = '协议方案产品'
        ordering = ['-id']

    def __str__(self):
        return f'{self.product_revenue_version}'


# 欧洲预报
class EuropeanForecast(BaseEntity):
    CURRENCY = [
        ('PREPARING', '准备中'),
        ('TRANSITING', '运输中'),
        ('ARRIVED', '已到港'),
        ('CLEAREING', '清关中'),
        ('CHECK', '查验'),
        ('CLEARED', '清关完成'),
        ('CARDED', '已卡派'),
        ('HANDED', '已交供应商'),
    ]

    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户')
    waybill_number = models.CharField(max_length=50, null=True, blank=True, verbose_name='提/运单号')
    transferoOrder_number = models.CharField(max_length=50, null=True, blank=True, verbose_name='转单号')
    goods_status = models.CharField(max_length=50, choices=CURRENCY, null=True, blank=True, verbose_name='货物状态')
    carton = models.IntegerField(null=True, blank=True, verbose_name='件数')
    weight = models.DecimalField(decimal_places=2, max_digits=10, null=True, blank=True, verbose_name='重量')
    volume = models.DecimalField(decimal_places=2, max_digits=10, null=True, blank=True, verbose_name='体积')
    undertaking = models.DecimalField(decimal_places=2, max_digits=10, null=True, blank=True, verbose_name='托数')
    airline_num = models.CharField(max_length=50, null=True, blank=True, verbose_name='航班号')
    box_num = models.CharField(max_length=50, null=True, blank=True, verbose_name='柜号', default='')
    expected_arrival_date = models.DateField(verbose_name='预计到达日期', null=True, blank=True)
    actual_arrival_date = models.DateField(verbose_name='实际到达日期', null=True, blank=True)
    expected_leave_date = models.DateField(verbose_name='预计离港日期', null=True, blank=True)
    actual_leave_date = models.DateField(verbose_name='实际离港日期', null=True, blank=True)
    clearance_date = models.DateField(verbose_name='清关日期', null=True, blank=True)
    reservation_number = models.CharField(max_length=50, null=True, blank=True, verbose_name='预约号')
    connect_date = models.DateField(verbose_name='交供应商日期', null=True, blank=True)
    aging = models.CharField(max_length=50, null=True, blank=True, verbose_name='时效')
    clearance_email = models.EmailField(null=True, blank=True, verbose_name='清关行邮箱')
    tax_email = models.EmailField(null=True, blank=True, verbose_name='抄税公司油箱')
    main_order = models.FileField(upload_to='uploads/', blank=True, verbose_name='主单')
    tax_order = models.FileField(upload_to='uploads/', blank=True, verbose_name='税单')
    pack_order = models.FileField(upload_to='uploads/', blank=True, verbose_name='箱单发票1')
    pack_order1 = models.FileField(upload_to='uploads/', blank=True, verbose_name='箱单发票2')
    pack_order2 = models.FileField(upload_to='uploads/', blank=True, verbose_name='箱单发票3')

    class Meta:
        verbose_name_plural = '欧洲预报'
        verbose_name = '欧洲预报'

    def __str__(self):
        return '欧洲预报'


# 美国预报
class AmericaForecast(BaseEntity):
    CURRENCY = [
        ('PREPARING', '准备中'),
        ('TRANSITING', '运输中'),
        ('ARRIVED', '已到港'),
        ('CLEAREING', '清关中'),
        ('CHECK', '查验'),
        ('CLEARED', '清关完成'),
        ('CARDED', '已卡派'),
        ('HANDED', '已交供应商'),
    ]

    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户')
    waybill_number = models.CharField(max_length=50, null=True, blank=True, verbose_name='提/运单号')
    transferoOrder_number = models.CharField(max_length=50, null=True, blank=True, verbose_name='转单号')
    goods_status = models.CharField(max_length=50, choices=CURRENCY, null=True, blank=True, verbose_name='货物状态')
    carton = models.IntegerField(null=True, blank=True, verbose_name='件数')
    weight = models.DecimalField(decimal_places=2, max_digits=10, null=True, blank=True, verbose_name='重量')
    volume = models.DecimalField(decimal_places=2, max_digits=10, null=True, blank=True, verbose_name='体积')
    undertaking = models.DecimalField(decimal_places=2, max_digits=10, null=True, blank=True, verbose_name='托数')
    airline_num = models.CharField(max_length=50, null=True, blank=True, verbose_name='航班号')
    box_num = models.CharField(max_length=50, null=True, blank=True, verbose_name='柜号', default='')
    expected_arrival_date = models.DateField(verbose_name='预计到达日期', null=True, blank=True)
    actual_arrival_date = models.DateField(verbose_name='实际到达日期', null=True, blank=True)
    expected_leave_date = models.DateField(verbose_name='预计离港日期', null=True, blank=True)
    actual_leave_date = models.DateField(verbose_name='实际离港日期', null=True, blank=True)
    clearance_date = models.DateField(verbose_name='清关日期', null=True, blank=True)
    reservation_number = models.CharField(max_length=50, null=True, blank=True, verbose_name='预约号')
    connect_date = models.DateField(verbose_name='交供应商日期', null=True, blank=True)
    aging = models.CharField(max_length=50, null=True, blank=True, verbose_name='时效')
    clearance_email = models.EmailField(null=True, blank=True, verbose_name='清关行邮箱')
    tax_email = models.EmailField(null=True, blank=True, verbose_name='抄税公司油箱')
    main_order = models.FileField(upload_to='uploads/', blank=True, verbose_name='主单')
    tax_order = models.FileField(upload_to='uploads/', blank=True, verbose_name='税单')
    pack_order = models.FileField(upload_to='uploads/', blank=True, verbose_name='箱单发票1')
    pack_order1 = models.FileField(upload_to='uploads/', blank=True, verbose_name='箱单发票2')
    pack_order2 = models.FileField(upload_to='uploads/', blank=True, verbose_name='箱单发票3')

    class Meta:
        verbose_name_plural = '美国预报'
        verbose_name = '美国预报'

    def __str__(self):
        return '美国预报'


# TZ邮编分区
class TzPostCodeZone(BaseEntity):
    """
    TZ邮编分区
    """
    country_code = models.CharField(max_length=100, verbose_name='国家编码', null=True, blank=True)
    zone_value = models.CharField(max_length=48, verbose_name='分区值', default='')
    post_code = models.CharField(max_length=100, verbose_name='邮编', default='')

    class Meta:
        verbose_name_plural = 'TZ邮编分区'
        verbose_name = 'TZ邮编分区'
        ordering = ['-id']

    def __str__(self):
        return self.country_code


# 产品销售定价表
class ProductSalesPrice(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '停用'),
    ]

    price_version = models.CharField(max_length=50, verbose_name='价格版本', null=True, blank=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='ON')
    products = models.CharField(max_length=500, verbose_name='产品', null=True, blank=True)
    product = models.ForeignKey(Product, verbose_name='产品', null=True, on_delete=models.DO_NOTHING)
    saler = models.CharField(max_length=50, verbose_name='销售', null=True, blank=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    # 因为由产品销售定价表创建协议价对应的产品协议方案需要有结束时间, 所以这里加结束时间
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)

    class Meta:
        verbose_name_plural = '产品销售定价'
        verbose_name = '产品销售定价'
        ordering = ['-id']

    def __str__(self):
        return self.price_version or ''


# 产品销售定价客户表
class ProductSalesPriceCustomer(BaseEntity):
    customer = models.ForeignKey(Company, verbose_name='客户', on_delete=models.DO_NOTHING, null=True)
    product_sales_price = models.ForeignKey(ProductSalesPrice, verbose_name='产品销售定价', null=True, blank=True,
                                            on_delete=models.SET_NULL, related_name="product_sales_price_customer")

    class Meta:
        verbose_name_plural = '产品销售定价归属客户'
        verbose_name = '产品销售定价归属客户'
        ordering = ['-id']

    def __str__(self):
        return self.customer.name if self.customer else ''


class ProductSalesPriceWarehouse(BaseEntity):
    product_sales_price = models.ForeignKey(ProductSalesPrice, verbose_name='产品销售定价', null=True, blank=True,
                                            on_delete=models.SET_NULL, related_name="product_sales_price_warehouse")
    warehouse = models.ForeignKey(Address, verbose_name='发货仓库', on_delete=models.DO_NOTHING, null=True)
    end_zone = models.ForeignKey(ProductZone, verbose_name='产品终点分区', on_delete=models.DO_NOTHING, null=True)
    rank_start = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级起点', null=True)
    rank_end = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级终点', null=True)
    receive_goods_price = models.CharField(max_length=100, verbose_name='收货价', null=True, blank=True)

    class Meta:
        verbose_name_plural = '产品销售定价仓库'
        verbose_name = '产品销售定价仓库'
        ordering = ['-id']

    def __str__(self):
        return f'{self.warehouse}'


class ProductSalesPriceStrategy(BaseEntity):
    STATUS = [
        ('DR', '草稿'),
        ('WO', '待生成'),
        ('FC', '生成完成'),
        ('VO', '作废'),
    ]

    status = models.CharField(max_length=10, verbose_name='状态', default='DR')
    product_ids = models.CharField(max_length=500, verbose_name='产品集合', null=True, blank=True)
    customer_ids = models.CharField(max_length=2000, verbose_name='客户集合', null=True, blank=True)
    warehouse_ids = models.CharField(max_length=200, verbose_name='仓库集合', null=True, blank=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)
    variable_price = models.CharField(max_length=100, verbose_name='浮动价', null=True, blank=True)

    class Meta:
        verbose_name_plural = '产品销售定价策略'
        verbose_name = '产品销售定价策略'
        ordering = ['-id']

    def __str__(self):
        return f'{self.id},{self.product_ids}' or ''


class SupplierService(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '停用'),
    ]

    status = models.CharField(max_length=10, verbose_name='状态', choices=STATUS_TYPE, default='ON')
    name = models.CharField(max_length=100, verbose_name='服务名称')
    code = models.CharField(max_length=100, verbose_name='服务编码')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    charge = models.ForeignKey(Charge, verbose_name='费用项', on_delete=models.DO_NOTHING, null=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)

    class Meta:
        verbose_name_plural = '供应商服务'
        verbose_name = '供应商服务'
        indexes = [
            models.Index(fields=['code', 'del_flag']),
        ]

    def __str__(self):
        return self.name


class SupplierServicePriceLine(BaseEntity):
    TYPE = [
        ('ZJ', '总价'),
        ('DJ', '单价'),
    ]
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
        ('MXN', 'MXN'),
    ]

    supplier_service = models.ForeignKey(SupplierService, verbose_name='供应商服务', related_name='supplierService',
                                         on_delete=models.DO_NOTHING, null=True)
    charge_type = models.CharField(max_length=10, choices=TYPE, verbose_name='计费方式', default='ZJ')
    currency = models.CharField(max_length=10, verbose_name='币种')
    rank_start = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级起点')
    rank_end = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级终点')
    price = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='价格')

    class Meta:
        verbose_name_plural = '供应商服务价格明细'
        verbose_name = '供应商服务价格明细'
        ordering = ['-id']
        indexes = [
            models.Index(fields=['supplier_service', 'del_flag']),
        ]

    def __str__(self):
        return self.supplier_service.code


class TrackRule(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '停用'),
    ]
    status = models.CharField(max_length=10, verbose_name='状态', default='ON')
    product = models.ForeignKey(Product, verbose_name='产品', null=True, on_delete=models.DO_NOTHING)
    service = models.ForeignKey(Service, verbose_name='服务', on_delete=models.DO_NOTHING, null=True)
    courier_code = models.CharField(max_length=100, verbose_name='尾程物流商编码', blank=True, null=True)

    class Meta:
        verbose_name_plural = '产品轨迹规则'
        verbose_name = '产品轨迹规则'
        ordering = ['-id']

    def __str__(self):
        return f'{self.product}'


class TrackBlockingWord(BaseEntity):
    block_word = models.CharField(max_length=100, verbose_name='屏蔽词', blank=True, null=True)
    track_rule = models.ForeignKey(TrackRule, verbose_name='产品轨迹规则', null=True, blank=True,
                                   on_delete=models.SET_NULL, related_name='track_blocking_word_list')

    class Meta:
        verbose_name_plural = '产品轨迹屏蔽词'
        verbose_name = '产品轨迹屏蔽词'
        ordering = ['-id']

    def __str__(self):
        return f'{self.track_rule}'


class InsuranceProduct(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='产品名称')
    code = models.CharField(max_length=100, verbose_name='产品编码', unique=True)
    revenue_lock = models.BooleanField(verbose_name='收入确认', blank=True, default=False)
    cost_lock = models.BooleanField(verbose_name='成本确认', blank=True, default=False)
    is_valuation = models.BooleanField(verbose_name='是否收入计价', default=False)
    is_cost_valuation = models.BooleanField(verbose_name='是否成本计价', default=False)
    butt_code = models.ForeignKey(SupplierButt, verbose_name='对接编码', null=True, blank=True,
                                  on_delete=models.SET_NULL)
    external_channel_code = models.CharField(max_length=50, verbose_name="保险产品编码", null=True, blank=True)
    insurance_company_no = models.CharField(max_length=50, verbose_name="保司编码", null=True, blank=True)
    insurance_options_code = models.CharField(max_length=100, verbose_name="保司方案代码", null=True, blank=True)
    insurance_project_code = models.CharField(max_length=100, verbose_name="保司方案代码", null=True, blank=True)
    extend_field = models.TextField(verbose_name='扩展字段', null=True, blank=True)

    class Meta:
        verbose_name_plural = '保险产品'
        verbose_name = '保险产品'
        ordering = ['-id']

    def __str__(self):
        return self.name


# 价格版本
class InsuranceProudctVersion(BaseEntity):
    PRICE_TYPE = [
        ('REVENUE', '收入'),
        ('COST', '成本'),
    ]
    version_name = models.CharField(max_length=100, verbose_name='版本名称')
    product = models.ForeignKey(InsuranceProduct, verbose_name='产品', related_name='product_versions', null=True,
                                on_delete=models.DO_NOTHING)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    price_type = models.CharField(max_length=10, verbose_name='价格类型', choices=PRICE_TYPE, default='REVENUE')

    class Meta:
        verbose_name_plural = '保险价格版本'
        verbose_name = '保险价格版本'
        ordering = ['-id']

    def __str__(self):
        return self.version_name


# 价格明细
class InsuranceVersionLine(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
        ('KRW', 'KRW'),
        ('MXN', 'MXN'),
    ]

    TYPE = [
        ('ZJ', '总价'),
        ('DJ', '单价'),
        ('HZJ', '货值价'),
    ]

    price_version = models.ForeignKey(InsuranceProudctVersion, verbose_name='价格版本', related_name='version_lines',
                                      on_delete=models.DO_NOTHING, null=True)
    currency = models.CharField(max_length=100, verbose_name='币种')
    charge_type = models.CharField(max_length=10, choices=TYPE, verbose_name='计费方式', default='ZJ')
    rank_start = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级起点')
    rank_end = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级终点')
    price = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='价格', null=True, blank=True)
    rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='费率')
    rate_coefficient = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='费率系数', default=0)
    # 计费进位,取值限制为0.1,0.5,1中的一个
    round_of_factor = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='计费进位', default=0)

    class Meta:
        ordering = ['-id']
        verbose_name = '保险价格明细'
        verbose_name_plural = '保险价格明细'

    def __str__(self):
        return f'{self.price_version}'


class ProductAsyncTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('Waiting', '等待中'),
        ('Processed', '处理中'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]
    ORDER_TYPE = [
        ('SalesPriceTemplate', '销售定价模板'),
        ('ProductSalesPrice', '产品销售定价'),
    ]
    TASK_TYPE = [
        ('TransProductSalesPrice', '销售定价模板转销售定价'),
        ('TransProtocolPrice', '销售定价转协议价'),
    ]
    order_id = models.IntegerField(verbose_name='单据id', null=True, blank=True)
    order_num = models.CharField(max_length=64, verbose_name='业务单据号', null=True, db_index=True)
    customer_orderNum = models.CharField(max_length=64, verbose_name='客户订单号', null=True, blank=True)
    order_type = models.CharField(max_length=64, choices=ORDER_TYPE, verbose_name='单据类型', null=True, blank=True)
    task_type = models.CharField(max_length=64, choices=TASK_TYPE, verbose_name='任务类型', null=True, blank=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True, default=None)
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='任务状态')
    task_desc = models.CharField(max_length=2048, verbose_name='任务描述', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True)
    execution_time = models.DurationField(verbose_name='处理用时', null=True, blank=True)

    class Meta:
        verbose_name_plural = '产品异步任务表'
        verbose_name = '产品异步任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


# 卡派用车规则
class TruckServiceRules(BaseEntity):
    USE_TYPE = [
        ('weight', '重量'),
        ('volume', '体积'),
        ('bubble', '泡比')
    ]

    version_name = models.CharField(max_length=64, verbose_name='版本名称')
    # zone_value = models.CharField(max_length=48, verbose_name='分区值', null=True, blank=True)
    use_type = models.CharField(max_length=32, choices=USE_TYPE, verbose_name='用车方式', default='bubble')
    # currency = models.CharField(max_length=100, verbose_name='币种')
    rank_start = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级起点')
    rank_end = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级终点', null=True, blank=True)
    strokes_num = models.IntegerField(verbose_name='打板数', null=True, blank=True)
    truck_num = models.IntegerField(verbose_name='用车数', null=True, blank=True)
    round_of_factor = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='计费进位', default=1)

    class Meta:
        ordering = ['-id']
        verbose_name = '卡派用车规则'
        verbose_name_plural = '卡派用车规则'

    def __str__(self):
        return f'{self.version_name}'


# 卡派头程规则
class TruckFirstVesselRules(BaseEntity):
    PRICING_RULE = [
        ('weight', '重量'),
        ('volume', '体积'),
        ('bubble', '泡比')
    ]

    version_name = models.CharField(max_length=64, verbose_name='版本名称')
    pricing_rule = models.CharField(max_length=32, choices=PRICING_RULE, verbose_name='定价规则', default='bubble')
    charge = models.ForeignKey(Charge, verbose_name='费用项', on_delete=models.DO_NOTHING, null=True)
    pricing_base = models.IntegerField(verbose_name='定价基数', null=True, blank=True)
    cabinet_capacity = models.IntegerField(verbose_name='柜子容量', null=True, blank=True)
    discount_factor = models.DecimalField(verbose_name='打折系数', max_digits=10, decimal_places=2)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '卡派头程规则'
        verbose_name_plural = '卡派头程规则'

    def __str__(self):
        return f'{self.version_name}'


class LabelRule(BaseEntity):
    TYPE = [
        ('CU', '产品自定义面单'),
        ('CH', '使用渠道面单'),
    ]

    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='label_rules')
    type = models.CharField(max_length=2, choices=TYPE, verbose_name='面单类型', default='CH')
    template_file = models.CharField(max_length=1000, verbose_name='面单模版', null=True, blank=True)
    label_length = models.IntegerField(verbose_name='可打印尺寸长', null=True, blank=True)
    label_width = models.IntegerField(verbose_name='可打印尺寸宽', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '面单规则'
        verbose_name_plural = '面单规则'

    def __str__(self):
        return f'{self.template_file}'


class OrderNumRule(BaseEntity):
    SOURCE_TYPE = [
        ('A', 'API获取'),
        ('C', '渠道字段'),
    ]
    API_TYPE = [
        ('QZ', '强制取消订单'),
        ('GJ', '轨迹推送'),
    ]

    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='order_num_rules')
    is_show_track_no = models.BooleanField(verbose_name='是否展示尾程单号', default=False)
    is_show_ems_no = models.BooleanField(verbose_name='是否展示邮政单号', default=False)
    is_forced_cancel = models.BooleanField(verbose_name='是否强制取消订单', default=False)
    is_track_push = models.BooleanField(verbose_name='是否轨迹推送', default=False)
    ems_no_source = models.CharField(max_length=10, choices=SOURCE_TYPE, verbose_name='邮政单号来源', null=True,
                                     blank=True)
    api_configuration = models.CharField(max_length=10, choices=API_TYPE, verbose_name='API其他配置', null=True,
                                         blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '单号规则'
        verbose_name_plural = '单号规则'

    def __str__(self):
        return f'{self.product}'


class ProductApiSetting(BaseEntity):
    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='product_api_settings')
    field_name = models.CharField(max_length=50, verbose_name='字段名称', null=True, blank=True)
    field_value = models.CharField(max_length=500, verbose_name='字段值', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '单号规则api配置'
        verbose_name_plural = '单号规则api配置'

    def __str__(self):
        return f'{self.product}:{self.field_name}'


class AddressRule(BaseEntity):
    ADDRESS_TYPE = [
        ('SD', '发件地址'),
        ('OV', '境外退货地址'),
    ]

    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='address_rules')
    address = models.ForeignKey(Address, verbose_name='地址', on_delete=models.DO_NOTHING, null=True)
    customer = models.ForeignKey(Company, verbose_name='客户', on_delete=models.DO_NOTHING, null=True)
    address_type = models.CharField(max_length=3, choices=ADDRESS_TYPE, verbose_name='地址类型', null=True, blank=True,
                                    default='N')
    is_general = models.BooleanField(verbose_name='是否通用', default=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '地址规则'
        verbose_name_plural = '地址规则'

    def __str__(self):
        return f'{self.address}'


# 分区设置
class ZoneSetting(BaseEntity):
    ZONE_TYPE = [
        ('SZ', '服务国家分区'),
        ('CZ', '应收计费分区'),
        ('PZ', '偏远分区'),
        ('NZ', '不可达分区'),
    ]

    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='zone_settings')
    zone_type = models.CharField(max_length=3, choices=ZONE_TYPE, verbose_name='分区类型', default='SZ')
    zone = models.ForeignKey(ProductZone, verbose_name='分区', on_delete=models.DO_NOTHING, null=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '分区设置'
        verbose_name_plural = '分区设置'

    def __str__(self):
        return f'{self.product}'


# 邮编校验地址配置表
class ProductPostCodeCheck(BaseEntity):
    country_code = models.CharField(max_length=100, verbose_name='国家编码', blank=True, null=True)
    state_code = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True)
    city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True)
    post_code = models.CharField(max_length=100, verbose_name='邮编')

    class Meta:
        ordering = ['-id']
        verbose_name = '邮编校验地址设置'
        verbose_name_plural = '邮编校验地址设置'
        indexes = [
            models.Index(fields=['post_code', 'del_flag']),
            models.Index(fields=['country_code', 'post_code', 'del_flag']),
            models.Index(fields=['country_code', 'state_code', 'post_code', 'del_flag']),
        ]

    def __str__(self):
        return f'{self.post_code}'


# 资源-附加费规则
class ServiceReceivingRule(BaseEntity):
    TYPE = [
        ('C', '国家约束'),
        ('F', '附加费规则')
    ]

    service = models.ForeignKey(Service, verbose_name='服务资源', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='service_receiving_rules')
    type = models.CharField(max_length=2, choices=TYPE, verbose_name='规则类型', default='F')
    constraint_rules = models.CharField(max_length=1000, verbose_name='约束规则', null=True, blank=True)
    country_code = models.CharField(max_length=4, verbose_name='国家编码', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '资源附加费规则'
        verbose_name_plural = '资源附加费规则'

    def __str__(self):
        return f'{self.constraint_rules}'


# 资源-面单规则
class ServiceLabelRule(BaseEntity):
    TYPE = [
        ('CU', '自定义面单'),
        ('CH', '使用渠道面单'),
    ]

    service = models.ForeignKey(Service, verbose_name='资源', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='service_label_rules')
    type = models.CharField(max_length=2, choices=TYPE, verbose_name='面单类型', default='CH')
    template_file = models.CharField(max_length=1000, verbose_name='面单模版', null=True, blank=True)
    label_length = models.IntegerField(verbose_name='可打印尺寸长', null=True, blank=True)
    label_width = models.IntegerField(verbose_name='可打印尺寸宽', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '资源面单规则'
        verbose_name_plural = '资源面单规则'

    def __str__(self):
        return f'{self.template_file}'


class ServiceOrderNumRule(BaseEntity):
    SOURCE_TYPE = [
        ('A', '下游API获取'),
        ('B', '系统生成'),
        ('C', '客户api预报推送'),
        ('D', '使用运单号'),
        ('E', '使用运单号并API推送下游'),
    ]

    REQUEST_TYPE = [
        ('YO', '运单号'),
        ('EO', '邮政单号'),
        ('CO', '客户单号'),
        ('KO', '客户编码+客户单号'),
    ]

    API_TYPE = [
        ('QZ', '强制取消订单'),
        ('GJ', '轨迹推送'),
    ]

    service = models.ForeignKey(Service, verbose_name='资源', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='service_order_num_rules')
    no_source = models.CharField(max_length=10, choices=SOURCE_TYPE, verbose_name='派送单号来源', null=True, blank=True)
    request_num = models.CharField(max_length=10, choices=REQUEST_TYPE, verbose_name='下游请求参考号', null=True,
                                   blank=True)
    api_configuration = models.CharField(max_length=10, choices=API_TYPE, verbose_name='API其他配置', null=True,
                                         blank=True)
    is_forced_cancel = models.BooleanField(verbose_name='是否强制取消订单', default=False)
    is_track_push = models.BooleanField(verbose_name='是否轨迹推送', default=False)
    is_async = models.BooleanField(verbose_name='是否异步换号', default=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '资源单号规则'
        verbose_name_plural = '资源单号规则'

    def __str__(self):
        return f'{self.service}'


class ServiceTrackConfig(BaseEntity):
    """资源轨迹配置"""

    TRACK_TYPE = [
        (1, '供应商接口'),
        (2, '17TRACK'),
        (3, '718TRACK'),
        (4, '轨迹API'),
    ]

    REQUEST_TYPE = [
        (1, '派送单号'),
        (2, '预报下游单号'),
        (3, '邮政单号'),
        (4, '供应商中性单号'),
    ]

    service = models.ForeignKey(Service, verbose_name='资源', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='service_track_configs')
    # 除供应商接口之外的轨迹配置
    track_supplier = models.ForeignKey(TrackSupplier, verbose_name='轨迹供API', null=True, blank=True,
                                       on_delete=models.SET_NULL, related_name='track_supplier_service_track_config')

    track_type = models.SmallIntegerField(choices=TRACK_TYPE, verbose_name='轨迹拉取类型', null=True, blank=True)
    request_num = models.SmallIntegerField(choices=REQUEST_TYPE, verbose_name='抓取轨迹单号类型', null=True, blank=True)
    node = models.CharField(max_length=60, verbose_name='生成轨迹任务节点', null=True, blank=True)
    pull_interval = models.FloatField(verbose_name='抓取时间间隔（小时）', null=True, blank=True)
    max_number_times = models.IntegerField(verbose_name='单节点抓取上限(同一轨迹节点重复抓取次数上限)', null=True, blank=True)

    # 派送完成后抓取轨迹
    interval = models.FloatField(verbose_name='间隔', null=True, blank=True)
    number_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True,
                                       help_text='派送完成达到重复抓取次数，任务完成')

    class Meta:
        ordering = ['-id']
        verbose_name = '资源轨迹配置'
        verbose_name_plural = '资源轨迹配置'

    def __str__(self):
        return f'{self.service}'


class ServiceApiSetting(BaseEntity):
    service = models.ForeignKey(Service, verbose_name='资源', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='service_api_settings')
    field_name = models.CharField(max_length=50, verbose_name='字段名称', null=True, blank=True)
    field_value = models.CharField(max_length=500, verbose_name='字段值', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '单号规则api配置'
        verbose_name_plural = '单号规则api配置'

    def __str__(self):
        return f'{self.service}:{self.field_name}'


class ServiceAddressRule(BaseEntity):
    ADDRESS_TYPE = [
        ('SD', '发件地址'),
        ('OV', '境外退货地址'),
        ('GN', '国内退货地址'),
    ]

    service = models.ForeignKey(Service, verbose_name='资源', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='service_address_rules')
    address = models.ForeignKey(Address, verbose_name='地址', on_delete=models.DO_NOTHING, null=True)
    customer = models.ForeignKey(Company, verbose_name='客户', on_delete=models.DO_NOTHING, null=True)
    address_type = models.CharField(max_length=3, choices=ADDRESS_TYPE, verbose_name='地址类型', null=True, blank=True,
                                    default='N')
    is_general = models.BooleanField(verbose_name='是否通用', default=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '地址规则'
        verbose_name_plural = '地址规则'

    def __str__(self):
        return f'{self.address}'


# 资源-订单校验
class ServiceBasicRestriction(BaseEntity):
    TYPE = [
        ('A', '属性限制'),
        ('B', '邮编限制'),
        ('C', '扩展限制'),
    ]
    BELONG_TYPE = [
        ('A', '订单'),
        ('B', '包裹'),
        ('C', '收件人'),
        ('D', '发件人'),
        ('E', '商品'),
        ('F', '海外退件地址'),
    ]
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='属性类型', default='A')
    belong_type = models.CharField(max_length=10, choices=BELONG_TYPE, verbose_name='所属类型', default='A')
    service = models.ForeignKey(Service, verbose_name='资源', on_delete=models.DO_NOTHING, null=True,
                                related_name='service_basic_restrictions')
    prefix_word = models.TextField(verbose_name='前缀词', null=True, blank=True)
    censored_word = models.TextField(verbose_name='屏蔽词', null=True, blank=True)
    suffix_word = models.CharField(max_length=2500, verbose_name='后缀词', null=True, blank=True)
    encoding = models.CharField(max_length=255, verbose_name="限制字段", null=True, blank=True)
    checksum_type = models.CharField(max_length=255, verbose_name="限制字段中文名", null=True, blank=True)
    field_length = models.CharField(max_length=255, verbose_name="字段长度", null=True, blank=True)
    is_required = models.BooleanField(verbose_name="是否必填", default=False)
    chinese = models.BooleanField(verbose_name="中文", default=False)
    english = models.BooleanField(verbose_name="英文", default=False)
    number = models.BooleanField(verbose_name="数字", default=False)
    restriction_postcode = models.BooleanField(verbose_name="邮编限制", default=False)
    insert_trick = models.BooleanField(verbose_name="条件", default=False)
    is_show = models.BooleanField(verbose_name="是否显示", default=True)
    default_value = models.CharField(max_length=500, verbose_name='默认值', null=True, blank=True)
    limit_effect_country = models.CharField(max_length=500, verbose_name='限制生效国家', null=True, blank=True)
    regular_expression = models.CharField(max_length=500, verbose_name='正则表达', null=True, blank=True)
    abnormal_prompt = models.CharField(max_length=500, verbose_name='异常提示', null=True, blank=True)

    class Meta:
        verbose_name_plural = '资源基本限制'
        verbose_name = '资源基本限制'
        ordering = ['-id']

    def __str__(self):
        return f'{self.service}:{self.encoding}'


class ServiceZoneSetting(BaseEntity):
    ZONE_TYPE = [
        ('CS', '渠道派送分区'),
        ('EF', '应付计费分区'),
        ('PF', '偏远分区'),
        ('NF', '不可达分区'),
    ]

    service = models.ForeignKey(Service, verbose_name='资源', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='service_zone_settings')
    zone_type = models.CharField(max_length=3, choices=ZONE_TYPE, verbose_name='分区类型', default='SZ')
    zone = models.ForeignKey(ProductZone, verbose_name='分区', on_delete=models.DO_NOTHING, null=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '分区设置'
        verbose_name_plural = '分区设置'

    def __str__(self):
        return f'{self.service}:{self.zone}'


class ServiceChargingRule(BaseEntity):
    CHARGING_MODE = [
        ('PR', '预报重量计费'),
        ('CW', '仓内重量计费'),
        ('QD', '预报重仓库重取大计费'),
    ]

    CHARGING_NODE = [
        ('PR', '预报'),
        ('IN', '入库'),
        ('ZB', '组包'),
        ('FY', '发运'),
        ('JH', '交航'),
        ('QF', '起飞'),
        ('LD', '落地'),
        ('CF', '清关完成'),
        ('MD', '末端上网'),
        ('PS', '派送完成'),
    ]

    service = models.ForeignKey(Service, verbose_name='资源', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='service_charging_rules')
    charging_mode = models.CharField(max_length=3, choices=CHARGING_MODE, verbose_name='计费模式', default='PR')
    charging_node = models.CharField(max_length=3, choices=CHARGING_NODE, verbose_name='计费节点', default='PR')

    class Meta:
        ordering = ['-id']
        verbose_name = '资源-计费规则'
        verbose_name_plural = '资源-计费规则'

    def __str__(self):
        return f'{self.service}:'


# 资源计费规则
class ServiceCharge(BaseEntity):
    UNIT = [
        ('weight', '重量'),
        ('volume', '体积'),
        ('chargeWeight', '计费重'),
        ('confirmChargeWeight', '确认计费重'),
        ('carton', '件数'),
        ('SKUCarton', 'SKU预报件数'),
        ('SKUShelfCarton', 'SKU上架件数'),
        ('SKUCategories', 'SKU 种类数量'),
        ('oneSKUWeight', '单件sku重量'),
        ('newSkuQuantity', 'SKU 新品数量'),
        ('oneParcelSKUCarton', '单件包裹sku件数'),
        ('mix', '混合'),
        ('combine', '组合收费'),
        ('boxWeight', '箱数重量'),
        ('confirmChargeVolume', '确认计费体积'),
        ('halfDisposal', '半抛'),
        ('numberOfStrokes', '打板数'),
        ('numberOfTrucks', '用车数'),
        ('bubble', '泡比'),
    ]

    name = models.CharField(max_length=300, verbose_name='收费项名称', null=True, blank=True)
    service = models.ForeignKey(Service, verbose_name='资源', on_delete=models.DO_NOTHING, null=True,
                                related_name='service_charges')
    charge_desc = models.CharField(max_length=200, verbose_name='计费说明', null=True, blank=True)
    round_of_factor = models.CharField(max_length=10, verbose_name='计费进位单位', null=True, blank=True)
    more_charge_weight = models.DecimalField(max_digits=16, decimal_places=6, verbose_name='包裹最大限重', default=0)
    less_charge_weight = models.DecimalField(max_digits=16, decimal_places=6, verbose_name='整单最小限重', default=0)
    relate_charges = models.CharField(max_length=200, verbose_name='组合费用', blank=True, null=True)
    charge_combine_rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='组合收费比例', default=0)
    is_intercept = models.BooleanField(verbose_name="是否拦截", default=False, blank=True, null=True)

    country_code = models.CharField(max_length=3, verbose_name='收件国家', blank=True, null=True)
    charge = models.ForeignKey(Charge, verbose_name='费用项', on_delete=models.DO_NOTHING, null=True)
    charge_unit = models.CharField(max_length=64, choices=UNIT, verbose_name='计费单位', default='weight')
    charge_weight_rate = models.DecimalField(max_digits=10, decimal_places=0, verbose_name='计费重转换率', default=0)
    bubble_ratio = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='泡比系数', default=0)

    class Meta:
        verbose_name_plural = '资源计费规则'
        verbose_name = '资源计费规则'
        ordering = ['id']

    def __str__(self):
        return self.name


# 资源-默认数据
class ServiceDefaultValue(BaseEntity):
    TYPE = [
        ('A', '属性限制'),
        ('B', '邮编限制'),
        ('C', '扩展限制'),
    ]
    BELONG_TYPE = [
        ('A', '订单'),
        ('B', '包裹'),
        ('C', '收件人'),
        ('D', '发件人'),
        ('E', '商品'),
        ('F', '海外退件地址'),
    ]
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='属性类型', default='A')
    belong_type = models.CharField(max_length=10, choices=BELONG_TYPE, verbose_name='所属类型', default='A')
    service = models.ForeignKey(Service, verbose_name='资源', on_delete=models.DO_NOTHING, null=True,
                                related_name='service_default_values')
    encoding = models.CharField(max_length=255, verbose_name="字段字段", null=True, blank=True)
    checksum_type = models.CharField(max_length=255, verbose_name="字段中文", null=True, blank=True)
    default_value = models.CharField(max_length=500, verbose_name='默认值', null=True, blank=True)
    limit_effect_country = models.CharField(max_length=50, verbose_name='限制生效国家', null=True, blank=True)
    regular_expression = models.CharField(max_length=500, verbose_name='正则表达', null=True, blank=True)
    abnormal_prompt = models.CharField(max_length=100, verbose_name='异常提示', null=True, blank=True)

    class Meta:
        verbose_name_plural = '资源默认数据'
        verbose_name = '资源默认数据'
        ordering = ['-id']


# 成本价格版本
class ProductCostVersion(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
        ('KRW', 'KRW'),
        ('MXN', 'MXN'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    product = models.ForeignKey(Product, verbose_name='产品', null=True, on_delete=models.DO_NOTHING)
    product_charge = models.ForeignKey(ProductCharge, verbose_name='产品费用项', on_delete=models.DO_NOTHING, null=True)

    service = models.ForeignKey(Service, verbose_name='服务资源', null=True, on_delete=models.DO_NOTHING)
    service_charge = models.ForeignKey(ServiceCharge, verbose_name='服务费用项', on_delete=models.DO_NOTHING, null=True,
                                       blank=True)

    version_name = models.CharField(max_length=100, verbose_name='分区名称')
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    # end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)
    currency = models.CharField(max_length=100, verbose_name='币种', null=True, blank=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE, null=True,
                              blank=True)

    class Meta:
        verbose_name_plural = '成本价格版本'
        verbose_name = '成本价格版本'
        ordering = ['-id']

    def __str__(self):
        return self.version_name


# 成本价格明细
class ProductCostVersionLine(BaseEntity):
    TYPE = [
        ('ZJ', '总价'),
        ('DJ', '单价'),
        ('TZJ', '递增价'),
    ]
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
        ('KRW', 'KRW'),
        ('MXN', 'MXN'),
    ]
    ZONE_TYPE = [
        ('CS', '渠道派送分区'),
        ('EF', '应付计费分区'),
        ('PF', '资源偏远分区'),
        ('NF', '资源不可达分区'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    price_version = models.ForeignKey(ProductCostVersion, verbose_name='成本价格版本', on_delete=models.DO_NOTHING,
                                      null=True)
    zone_value = models.CharField(max_length=48, verbose_name='分区值', null=True, blank=True)
    zone = models.ForeignKey(ProductZonePostCode, verbose_name='分区', on_delete=models.DO_NOTHING, null=True,
                             blank=True)
    charge_type = models.CharField(max_length=10, choices=TYPE, verbose_name='计费方式', default='ZJ')
    currency = models.CharField(max_length=100, verbose_name='币种')
    rank_start = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级起点')
    rank_end = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级终点')
    price = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='价格')
    base_price = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='基础价格', default=0, null=True,
                                     blank=True)
    increased_rank = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='递增等级', default=0, null=True,
                                         blank=True)

    # 计费进位,取值限制为0.1,0.5,1中的一个
    round_of_factor = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='计费进位', default=0,
                                          null=True, blank=True)

    charge = models.ForeignKey(ServiceCharge, verbose_name='费用项', on_delete=models.DO_NOTHING, null=True, blank=True)
    zone_type = models.CharField(max_length=3, verbose_name='分区类型', choices=ZONE_TYPE, null=True, blank=True)
    status = models.CharField(max_length=10, choices=STATUS_TYPE, verbose_name='状态', default='DR', blank=True,
                              null=True)

    class Meta:
        verbose_name_plural = '成本价格明细'
        verbose_name = '成本价格明细'
        ordering = ['-id']

    def __str__(self):
        return self.price_version.version_name


class ProductInterceptRule(BaseEntity):
    RULE_TYPE = [
        ('length', '长度限制'),
        ('weight', '重量限制'),
        ('area_restrict', '地区限制'),
    ]
    # 拦截规则主表
    intercept_rule_name = models.CharField(max_length=100, verbose_name='规则名称', null=True, blank=True)
    rule_type = models.CharField(max_length=32, choices=RULE_TYPE, verbose_name='规则类型', null=True, blank=True)
    is_marked = models.BooleanField(verbose_name='是否标记', default=False)
    is_intercept = models.BooleanField(verbose_name='是否拦截', default=False)
    is_enable = models.BooleanField(verbose_name='是否启用', default=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)
    products = models.ManyToManyField(Product, verbose_name='产品')

    class Meta:
        verbose_name_plural = '产品拦截规则'
        verbose_name = '产品拦截规则'
        ordering = ['-id']

    def __str__(self):
        return self.intercept_rule_name or ''


# 产品拦截规则明细
class ProductInterceptRuleDetail(BaseEntity):
    product_intercept_rule = models.ForeignKey(ProductInterceptRule, verbose_name='拦截规则',
                                               on_delete=models.DO_NOTHING, null=True, related_name='intercept_rules')
    left_bracket = models.CharField(max_length=255, verbose_name='左括号')
    criteria_code = models.CharField(max_length=255, verbose_name='计费要素')
    match_method = models.CharField(max_length=255, verbose_name='条件')
    condition_value = models.CharField(max_length=255, verbose_name='值')
    criteria_type = models.CharField(max_length=255, verbose_name='值类型')
    right_bracket = models.CharField(max_length=255, verbose_name='右括号')
    logical_operator = models.CharField(max_length=255, verbose_name='关系')
    serial_code = models.CharField(max_length=255, verbose_name='顺序')

    class Meta:
        verbose_name = '拦截规则明细'
        verbose_name_plural = '拦截规则明细'

    def __str__(self):
        return f'ID: {self.id}, Interception Code: {self.criteria_code}'


# 批量创建多个产品价格版本以及明细的操作任务
class MulProductPriceVersionTask(BaseEntity):
    STATUS = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]
    TYPE = [
        ('revenue', '收入价格版本任务'),
        ('cost', '成本价格版本'),
    ]
    ref_no = models.CharField(max_length=255, verbose_name='任务编号', null=True, blank=True, db_index=True)
    status = models.CharField(max_length=255, verbose_name='状态', null=True, blank=True,choices=STATUS,default='pending')
    type = models.CharField(max_length=255, verbose_name='任务类型', null=True, blank=True,choices=TYPE,default='revenue')
    class Meta:
        verbose_name_plural = '批量创建多个产品价格版本以及明细的操作任务'
        verbose_name = '批量创建多个产品价格版本以及明细的操作任务'
        ordering = ['-id']

class MulProductPriceVersionTaskProduct(BaseEntity):
    STATUS = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]
    task = models.ForeignKey(MulProductPriceVersionTask, verbose_name='任务', on_delete=models.DO_NOTHING, null=True, related_name='product')
    product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True, related_name='product')
    status = models.CharField(max_length=255, verbose_name='状态', null=True, blank=True,choices=STATUS,default='pending')
    next_run_time = models.IntegerField(verbose_name='下次处理时间戳', null=True, blank=True,default=0)
    run_count = models.IntegerField(verbose_name='处理次数', null=True, blank=True,default=0)

    class Meta:
        verbose_name_plural = '多产品创建价格版本任务的产品'
        verbose_name = '多产品创建价格版本任务的产品'
        ordering = ['-id']

class MulProductPriceVersion(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
        ('KRW', 'KRW'),
        ('MXN', 'MXN'),
    ]

    PRICE_TYPE = [
        ('A', '公布价'),
        ('B', '协议价'),
    ]

    version_name = models.CharField(max_length=100, verbose_name='版本名称', null=True, blank=True)
    product_charge_name = models.CharField(max_length=100, verbose_name='产品费用项名称', null=True, blank=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    currency = models.CharField(max_length=3, verbose_name='币种', null=True, blank=True)
    price_type = models.CharField(max_length=2, verbose_name='价格类型', choices=PRICE_TYPE, default='A')
    task = models.ForeignKey(MulProductPriceVersionTask, verbose_name='任务', on_delete=models.DO_NOTHING, null=True, related_name='price_version')
  

    class Meta:
        verbose_name_plural = '多产品创建价格版本任务的价格版本'
        verbose_name = '多产品创建价格版本任务的价格版本'
        ordering = ['-id']

    def __str__(self):
        return self.version_name

# 价格明细
class MulProductPriceVersionLine(BaseEntity):
    TYPE = [
        ('ZJ', '总价'),
        ('DJ', '单价'),
        ('TZJ', '递增价'),
    ]
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
        ('KRW', 'KRW'),
        ('MXN', 'MXN'),
    ]
    SOURCE_TYPE = [
        ('S', '系统添加'),
        ('A', '手动录入'),
    ]
    ZONE_TYPE = [
        ('SZ', '服务国家分区'),
        ('CZ', '应收计费分区'),
        ('PZ', '偏远分区'),
        ('NZ', '不可达分区'),
    ]

    price_version = models.ForeignKey(
        MulProductPriceVersion, 
        verbose_name='价格版本', 
        on_delete=models.DO_NOTHING,
        null=True,
        related_name='price_version_line'
    )
    zone_value = models.CharField(max_length=48, verbose_name='分区值', null=True, blank=True)
    charge_type = models.CharField(max_length=10, choices=TYPE, verbose_name='计费方式', default='ZJ')
    currency = models.CharField(max_length=100, verbose_name='币种')
    rank_start = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级起点')
    rank_end = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='等级终点')
    price = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='价格')
    base_price = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='基础价格', default=0, null=True,
                                     blank=True)
    increased_rank = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='递增等级', default=0, null=True,
                                         blank=True)
    # 计费进位,取值限制为0.1,0.5,1中的一个
    round_of_factor = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='计费进位', default=0,
                                          null=True, blank=True)
    data_source = models.CharField(max_length=2, choices=SOURCE_TYPE, verbose_name='数据来源', default="A")


    class Meta:
        ordering = ['-id']
        verbose_name = '多产品创建价格版本任务的价格明细'
        verbose_name_plural = '多产品创建价格版本任务的价格明细'

    def __str__(self):
        return f'{self.price_version}'