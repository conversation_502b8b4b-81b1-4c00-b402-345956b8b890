from datetime import datetime

from django.db import transaction
from rest_framework import serializers

from common.error import ParamError, ErrorCode
from common.order_num_gen_rule import generate_order_num_commom
from common.serializer import DeletedFilterListSerializer
from common.tools import get_update_params
from company.models import Address
from info.models import Charge
from pms.models import Service, ServiceReceivingRule, ServiceLabelRule, ServiceOrderNumRule, ServiceAddressRule, \
    ServiceZoneSetting, ServiceChargingRule, ServiceCharge, ServiceBasicRestriction, ServiceDefaultValue, \
    ServiceApiSetting, ServiceTrackConfig


class ServiceTrackConfigSerializer(serializers.ModelSerializer):
    """资源轨迹配置"""
    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceTrackConfig
        fields = '__all__'


class ServiceApiSettingSerializer(serializers.ModelSerializer):

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceApiSetting
        fields = '__all__'


class ServiceReceivingRulesSerializer(serializers.ModelSerializer):

    service_name = serializers.CharField(source='service.name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)
    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceReceivingRule
        fields = '__all__'


class ServiceLabelRulesSerializer(serializers.ModelSerializer):
    service_name = serializers.CharField(source='service.name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceLabelRule
        fields = '__all__'

class ServiceOrderNumRuleSerializer(serializers.ModelSerializer):
    service_name = serializers.CharField(source='service.name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)
    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceOrderNumRule
        fields = '__all__'


class ServiceAddressRuleSerializer(serializers.ModelSerializer):
    service_name = serializers.CharField(source='service.name', required=False)
    address_num = serializers.CharField(source='address.address_num', required=False)
    customer_name = serializers.CharField(source='customer.name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceAddressRule
        fields = '__all__'


class ServiceZoneSettingSerializer(serializers.ModelSerializer):
    service_name = serializers.CharField(source='service.name', required=False)
    zone_zone_type = serializers.CharField(source='zone.zone_type', required=False)
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceZoneSetting
        fields = '__all__'


class ServiceChargingRuleSerializer(serializers.ModelSerializer):
    '''
    计费规则序列化
    '''

    service_name = serializers.CharField(source='service.name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceChargingRule
        fields = '__all__'


class ServiceChargeSerializer(serializers.ModelSerializer):
    '''
    产品收费项序列化
    '''

    service_name = serializers.CharField(source='service.name', required=False)
    charge_name = serializers.CharField(source='charge.name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceCharge
        fields = '__all__'


class ServiceBasicRestrictionSerializer(serializers.ModelSerializer):

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceBasicRestriction
        fields = '__all__'

class ServiceDefaultValueSerializer(serializers.ModelSerializer):

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ServiceDefaultValue
        fields = '__all__'

class ServiceSerializer(serializers.ModelSerializer):
    '''
    服务序列化
    '''
    product_name = serializers.CharField(source='product.name', required=False)
    supplier_name = serializers.CharField(source='supplier.name', required=False)
    butt_code_name = serializers.CharField(source='butt_code.code', required=False)
    trackSupplier_name = serializers.CharField(source='trackSupplier.name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = Service
        fields = '__all__'


class ServiceDetailSerializer(serializers.ModelSerializer):
    '''
    服务列表序列化
    '''
    product_name = serializers.CharField(source='product.name', required=False)
    supplier_name = serializers.CharField(source='supplier.name', required=False)
    butt_code_name = serializers.CharField(source='butt_code.code', required=False)
    trackSupplier_name = serializers.CharField(source='trackSupplier.name', required=False)
    service_receiving_rules = ServiceReceivingRulesSerializer(many=True, required=False)
    service_label_rules = ServiceLabelRulesSerializer(many=True, required=False)
    service_order_num_rules = ServiceOrderNumRuleSerializer(many=True, required=False)
    service_address_rules = ServiceAddressRuleSerializer(many=True, required=False)
    service_zone_settings = ServiceZoneSettingSerializer(many=True, required=False)
    service_charging_rules = ServiceChargingRuleSerializer(many=True, required=False)
    service_charges = ServiceChargeSerializer(many=True, required=False)
    service_basic_restrictions = ServiceBasicRestrictionSerializer(many=True, required=False)
    service_default_values = ServiceDefaultValueSerializer(many=True, required=False)
    service_api_settings = ServiceApiSettingSerializer(many=True, required=False)
    service_track_configs = ServiceTrackConfigSerializer(many=True, required=False)
    create_by_name = serializers.CharField(source='create_by.name', required=False, read_only=True, allow_blank=True, allow_null=True)
    update_by_name = serializers.CharField(source='update_by.name', required=False, read_only=True, allow_blank=True, allow_null=True)

    class Meta:
        model = Service
        fields = '__all__'

    @transaction.atomic
    def create(self, validated_data):
        request = self.context['request']
        service_api_settings = validated_data.pop('service_api_settings', [])
        receiving_rules = validated_data.pop('service_receiving_rules', [])
        label_rules = validated_data.pop('service_label_rules', [])
        order_num_rules = validated_data.pop('service_order_num_rules', [])
        address_rules = validated_data.pop('service_address_rules', [])
        service_basic_restrictions = validated_data.pop('service_basic_restrictions', [])
        zone_settings = validated_data.pop('service_zone_settings', [])
        charging_rules = validated_data.pop('service_charging_rules', [])
        service_default_values = validated_data.pop('service_default_values', [])
        service_track_configs = validated_data.pop('service_track_configs', [])
        service_charges = request.data.get('service_charges', [])
        validated_data.pop('service_charges', [])

        service = Service.objects.create(**validated_data)

        if not validated_data.get('delivery_code', None):
            service.delivery_code = generate_order_num_commom('D4', service.id, 4)
        service.save()

        pms_charge(service, service_charges, request)
        pms_receive_rule(service, receiving_rules, request)
        pms_label_rule(service, label_rules, request)
        pms_order_num_rule(service, order_num_rules, request)
        pms_address_rule(service, address_rules, request)
        pms_order_verify_rule(service, service_basic_restrictions, request)
        pms_zone_setting(service, zone_settings, request)
        pms_charging_rule(service, charging_rules, request)
        pms_service_default_value(service, service_default_values, request)
        pms_service_api_setting(service, service_api_settings, request)
        pms_service_track_configs(service, service_track_configs, request)

        return service

    @transaction.atomic
    def update(self, instance, validated_data):
        request = self.context['request']

        receiving_rules = validated_data.pop('service_receiving_rules', [])
        label_rules = validated_data.pop('service_label_rules', [])
        order_num_rules = validated_data.pop('service_order_num_rules', [])
        address_rules = validated_data.pop('service_address_rules', [])
        product_basic_restrictions = validated_data.pop('service_basic_restrictions', [])
        zone_settings = validated_data.pop('service_zone_settings', [])
        charging_rules = validated_data.pop('service_charging_rules', [])
        service_charges = request.data.get('service_charges', [])
        validated_data.pop('service_charges', [])
        service_default_values = validated_data.pop('service_default_values', [])
        service_api_settings = validated_data.pop('service_api_settings', [])
        service_track_configs = validated_data.pop('service_track_configs', [])

        pms_receive_rule(instance, receiving_rules, request, instance, False)
        pms_label_rule(instance, label_rules, request, instance, False)
        pms_order_num_rule(instance, order_num_rules, request, instance, False)
        pms_order_verify_rule(instance, product_basic_restrictions, request, instance, False)
        pms_zone_setting(instance, zone_settings, request, instance, False)
        pms_address_rule(instance, address_rules, request, instance, False)
        pms_charging_rule(instance, charging_rules, request, instance, False)
        pms_charge(instance, service_charges, request, instance, False)
        pms_service_default_value(instance, service_default_values, request, instance, False)
        pms_service_api_setting(instance, service_api_settings, request, instance, False)
        pms_service_track_configs(instance, service_track_configs, request, instance, False)

        # 更新主记录的所有字段属性
        for field in validated_data:
            setattr(instance, field, validated_data.get(field, getattr(instance, field)))

        instance.update_by = request.user
        instance.update_date = datetime.now()
        instance.save()

        return instance


@transaction.atomic
def pms_service_api_setting(service, data, request, instance=None, is_add=True):
    if is_add:
        for item in data:
            ServiceApiSetting.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_api_settings.all()}

        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_api_settings.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceApiSetting.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()


@transaction.atomic
def pms_service_track_configs(service, data, request, instance=None, is_add=True):
    """资源轨迹供应商配置"""
    butt_code = service.butt_code

    def is_supplier_api(data_item):
        """校验轨迹选择供应商接口有没有设置供应商API"""
        # 选择供应商接口必须选择下游API获取，选择API接口
        if item.get('track_type', None) == 1:
            if not butt_code:
                raise ParamError('轨迹配置: 供应商接口不存在,请确认API配置是否有配置API信息', ErrorCode.PARAM_ERROR)

    if is_add:
        for item in data:
            is_supplier_api(item)
            ServiceTrackConfig.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_track_configs.all()}

        # 根据ID来查找需要更新的记录
        for item in data:
            is_supplier_api(item)
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_track_configs.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceTrackConfig.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()


@transaction.atomic
def pms_charging_rule(service, data, request, instance=None, is_add=True):
    if is_add:
        for item in data:
            ServiceChargingRule.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_charging_rules.all()}

        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_charging_rules.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceChargingRule.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()


# 创建分区规则
@transaction.atomic
def pms_zone_setting(service, data, request, instance=None, is_add=True):
    if is_add:
        for item in data:
            ServiceZoneSetting.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_zone_settings.all()}

        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_zone_settings.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceZoneSetting.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()


# 创建订单校验规则
@transaction.atomic
def pms_order_verify_rule(service, data, request, instance=None, is_add=True):
    if is_add:
        for item in data:
            ServiceBasicRestriction.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_basic_restrictions.all()}
        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_basic_restrictions.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceBasicRestriction.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()

@transaction.atomic
def pms_order_num_rule(service, data, request, instance=None, is_add=True):
    if is_add:
        for item in data:
            ServiceOrderNumRule.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_order_num_rules.all()}

        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_order_num_rules.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceOrderNumRule.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()# 创建产品收获规则

# 创建产品地址规则
@transaction.atomic
def pms_address_rule(service, data, request, instance=None, is_add=True):
    if is_add:
        for item in data:
            ServiceAddressRule.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_address_rules.all()}

        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_address_rules.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceAddressRule.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()

# 创建产品收获规则
@transaction.atomic
def pms_receive_rule(service, data, request, instance=None, is_add=True):
    if is_add:
        for item in data:
            ServiceReceivingRule.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_receiving_rules.all()}

        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_receiving_rules.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceReceivingRule.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()


# 创建面单规则
@transaction.atomic
def pms_label_rule(service, data, request, instance=None, is_add=True):
    if is_add:
        for item in data:
            ServiceLabelRule.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_label_rules.all()}

        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_label_rules.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceLabelRule.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()

# 创建基础默认规则
@transaction.atomic
def pms_service_default_value(service, data, request, instance=None, is_add=True):
    if is_add:
        for item in data:
            ServiceDefaultValue.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = {item.id: item for item in instance.service_default_values.all()}

        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                instance.service_default_values.create(**item)
            # 有ID的，将记录内容更新掉
            elif instance_data.get(item_id, None) is not None:
                instance_item = instance_data.pop(item_id)
                ServiceDefaultValue.objects.filter(id=instance_item.id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        for item in instance_data.values():
            item.del_flag = True
            item.save()

@transaction.atomic
def pms_charge(service, data, request, instance=None, is_add=True):

    if is_add:
        for item in data:
            name = item.get('name', None)
            if not name:
                name = item.get('charge_name', '')
                item['name'] = name
            ServiceCharge.objects.create(service=service, **item, **get_update_params(request, True))
    else:
        # 获取当instance的所有子表记录
        instance_data = [x.id for x in ServiceCharge.objects.filter(service=service, del_flag=False)]

        # 根据ID来查找需要更新的记录
        for item in data:
            item_id = item.get("id", None)
            name = item.get('name', None)
            if not name:
                name = item.get('charge_name', '')
                item['name'] = name
            # 没有ID的创建新的记录
            if item_id is None:
                item.pop('charge_name', None)
                item['charge'] = Charge.objects.get(id=item['charge'], del_flag=False)

                ServiceCharge.objects.create(service=service, **item, del_flag=False, **get_update_params(request, True))
            # 有ID的，将记录内容更新掉
            elif item_id in instance_data:
                instance_data.remove(item_id)
                item.pop('service_name', None)
                item.pop('charge_name', None)

                ServiceCharge.objects.filter(id=item_id).update(**item)

        # 针对还保留在列表里的记录，全部删除
        ServiceCharge.objects.filter(service=service, del_flag=False, id__in=instance_data).update(del_flag=True)


