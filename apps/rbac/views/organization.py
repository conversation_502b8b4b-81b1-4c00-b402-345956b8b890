# @Time    : 2019/1/12 21:03
# <AUTHOR> <PERSON><PERSON><PERSON>
from rest_framework.decorators import action
from rest_framework.viewsets import ModelViewSet

from common.utils.custom_viewset_base import CustomViewBase
from ..models import Organization
from ..serializers.organization_serializers import OrganizationSerializer, OrganizationUserTreeSerializer
from common.custom import CommonPagination, RbacPermission, TreeAPIView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework_jwt.authentication import JSONWebTokenAuthentication
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils.translation import gettext_lazy as _
from alita.logger import logger


class OrganizationViewSet(CustomViewBase):
    '''
    组织机构：增删改查
    '''
    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    pagination_class = CommonPagination
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ('name', )
    ordering_fields = ('id',)
    filterset_fields = ('type', )
    authentication_classes = (JSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    def list(self, request, *args, **kwargs):
        # 重写list方法，获取树状结构数据
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            # serializer = self.get_serializer(page, many=True)
            serializer = self.get_serializer(queryset, many=True)
            tree_dict = {}
            tree_data = []
            try:
                for item in serializer.data:
                    tree_dict[item['id']] = item
                for i in tree_dict:
                    tree_dict[i]['children'] = []
                    if tree_dict[i]['pid']:
                        pid = tree_dict[i]['pid']
                        parent = tree_dict[pid]
                        parent['children'].append(tree_dict[i])
                    else:
                        tree_data.append(tree_dict[i])
                return self.get_paginated_response(tree_data)
            except KeyError:
                logger.info(f'异常{serializer.data}')
                return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(methods=['GET'], detail=False)
    def get_department_list(self, request, *args, **kwargs):
        queryset = Organization.objects.filter(type='department', del_flag=False)
        serializer = self.get_serializer(queryset, many=True)
        return Response(data={'code': 200, 'data': serializer.data, 'msg': 'success'})

    def update(self, request, *args, **kwargs):
        # 继续执行默认的更新逻辑
        return super().update(request, *args, **kwargs)

    @action(detail=False, methods=['post'])
    def bulk_delete(self, request):
        ids = request.data.get('ids', [])
        if not ids:
            return Response({"error": "No IDs provided"}, status=400)

        deleted, _ = self.queryset.filter(id__in=ids).delete()
        return Response({"deleted": deleted}, status=204)

    @action(methods=['GET'], detail=False)
    def get_warehouse(self, request):
        queryset = Organization.objects.filter(type='warehouse', del_flag=False).all()

        if not queryset:
            return Response(data={'code': 400, 'msg': _('数据不存在')})

        serializer = OrganizationSerializer(queryset, many=True)

        return Response(data={
            'code': 200,
            'data': serializer.data,
            'msg': 'success'
        })


class OrganizationTreeView(TreeAPIView):
    '''
    组织架构树
    '''
    queryset = Organization.objects.all()


class OrganizationUserTreeView(APIView):
    '''
    组织架构关联用户树
    '''
    authentication_classes = (JSONWebTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def get(self, request, format=None):
        organizations = Organization.objects.all()
        serializer = OrganizationUserTreeSerializer(organizations, many=True)
        tree_dict = {}
        tree_data = []
        for item in serializer.data:
            new_item = {
                'id': 'o' + str(item['id']),
                'label': item['label'],
                'pid': item['pid'],
                'children': item['children']
            }
            tree_dict[item['id']] = new_item
        for i in tree_dict:
            if tree_dict[i]['pid']:
                pid = tree_dict[i]['pid']
                parent = tree_dict[pid]
                parent['children'].append(tree_dict[i])
            else:
                tree_data.append(tree_dict[i])
        return Response(tree_data)
