import json
from typing import List
import os
# 处理取消小包订单
import re
from datetime import datetime
import random
from decimal import Decimal, ROUND_DOWN
from dataclasses import dataclass
from urllib import request
from django.db.models import Sum, Q, F, Prefetch, Subquery

from PyPDF2 import PdfFileMerger
from django.conf import settings
from django.forms import model_to_dict
from django.db import transaction
from django.db.models import Q, Sum
from django.core.cache import cache
from alita.logger import logger
import re

from account.views.account import deduction_account
from common.error import ParamError, ErrorCode
from common.order_num_gen_rule import generate_order_num_commom
from common.service.account_service import refund_account
from common.utils.object import upper_first

from common.service.product_attribute_limit import check_order_data, check_parcel_data, add_create_order_track_info, \
    check_prop_limit, get_productBasicRestriction_defalut_value, get_productBasicRestriction, process_gb_postcode, \
    check_data, check_data_v2
from common.string import now_to_timestamp
from common.tools import gen_parcel_order_num, restrict_charge_item, optimize_weight, get_update_params_by_user, \
    set_multistep_parcel_track, parcel_out_warehouse
from common.tools import num_to_str, get_update_params, gen_parcel_order_num
from common.utils.product import get_sub_product_list
from company.models import SupplierButtAccount, SupplierButt, Company, Address
from order.integration.call_service.service import call_last_mile_service
from order.integration.integrationInterface import LabelOrderVo, cancel_label, confirm_ship, scanform, FlightInfo
from order.integration.util.abstract_func import create_order_label_task
from order.models import ParcelOrderLabelTask, ParcelOrderChargeIn, ParcelOrderLabel, ParcelOrderItem, \
    ParcelCustomerOrder, ParcelOrderParcel, ParcelOrderChargeOut, ParcelTrack, ParcelOrderExtend, ParcelOrderAddress
from common.service.pms_service import add_revenue, add_cost
from common.tools import gen_parcel_order_num, restrict_charge_item
from common.utils.gen_mode_key_util import gen_mode_key_by_shunt
from common.utils.get_currency_rate import get_currency_rate
from company.models import SupplierButtAccount, SupplierButt
from alita.logger import logger
from order.integration.integrationInterface import LabelOrderVo, cancel_label, confirm_ship, get_label
from order.models import ParcelOrderLabelTask, ParcelOrderChargeIn, ParcelOrderLabel, ParcelOrderItem, ParcelTrack, \
    ParcelOrderParcel, ParcelOrderChargeOut, CancelParcelCustomerOrderLabelTask
from pms.models import Product, Service, ProductBasicRestriction, ProductLimitUser, ReceivingRule, ProductRoute, \
    ProductRouteDetail, ServiceReceivingRule, ServiceBasicRestriction, ServiceDefaultValue, ServiceZoneSetting, \
    AddressRule, LineSortingSetting, ZoneSetting, ProductZonePostCode, ServiceApiSetting

from order.integration.postkeeperIntegrationService import PostkeeperIntegrationService
from order.integration.customerOrderIntegrationService import CustomerOrderIntegrationService
from order.integration.parcelCustomerOrderIntegrationService import ParcelCustomerOrderIntegrationService
from order.integration.parcelCustomerOrderIntegrationService import ParcelCustomerOrderIntegrationService
from order.integration.idealIntegrationService import IdealIntegrationService
from order.integration.deUpsIntegrationService import DeUpsIntegrationService
from order.integration.oblIntegrationService import OblIntegrationService
from order.integration.ideal2IntegrationService import Ideal2IntegrationService
from order.integration.ideal3IntegrationService import Ideal3IntegrationService
from order.integration.hsIntegrationService import HsIntegrationService
from order.integration.megaIntegrationService import MegaIntegrationService
from order.integration.kwtIntegrationService import KwtIntegrationService
from order.integration.px4IntegrationService import Px4IntegrationService
from order.integration.px4LmaIntegrationService import Px4LmaIntegrationService
from order.integration.hltIntegrationService import HltIntegrationService
from order.integration.anNengIntegrationService import AnNengIntegrationService
from order.integration.hf2IntegrationService import Hf2IntegrationService
from order.integration.zsdIntegrationService import ZsdIntegrationService
from order.integration.jnIntegrationService import JnIntegrationService
from order.integration.yhIntegrationService import YhIntegrationService
from order.integration.ninjaVanIntegrationService import NinjaVanIntegrationService
from order.integration.gsIntegrationService import GsIntegrationService
from order.integration.lcsIntegrationService import LcsIntegrationService
from order.integration.thzIntegrationService import ThzIntegrationService
from order.integration.mapleIntegrationService import MapleIntegrationService
from order.integration.dhlIntegrationService import DhlIntegrationService
from order.integration.pospeipIntegrationService import PospeipIntegrationService
from order.integration.k5IntegrationService import K5IntegrationService
from order.integration.zrIntegrationService import ZrIntegrationService
from order.integration.hallIntegrationService import HallIntegrationService
from order.integration.shopLineIntegrationService import ShopLineIntegrationService
from order.integration.dhlShippingIntegrationService import DhlShippingIntegrationService
from order.integration.dhlIntlShippingIntegrationService import DhlIntlShippingIntegrationService
from order.integration.shipHuBxIntegrationService import ShipHuBxIntegrationService
from order.integration.jaIntegrationService import JaIntegrationService
from order.integration.jdIntegrationService import JdIntegrationService
from order.integration.shipNitroIntegrationService import ShipNitroIntegrationService
from order.integration.hlIntegrationService import HlIntegrationService
from order.integration.emsZjIntegrationService import EmsZjIntegrationService
from order.integration.didaIntegrationService import DidaIntegrationService
from order.integration.olwIntegrationService import OlwIntegrationService
from order.integration.shaoKeIntegrationService import ShaoKeIntegrationService
from order.integration.dpexIntegrationService import DpexIntegrationService
from order.integration.spxIntegrationService import SpxIntegrationService
from order.integration.htyIntegrationService import HtyIntegrationService
from order.integration.ycIntegrationService import YcIntegrationService
from order.integration.dhlIntlEconomyIntegrationService import DhlIntlEconomyIntegrationService
from order.integration.cainiaoCdPacketIntegrationService import CainiaoCdPacketIntegrationService
from order.integration.cainiaoFullPacketIntegrationService import CainiaoFullPacketIntegrationService
from order.integration.hyIntegrationService import HyIntegrationService
from order.integration.gweIntegrationService import GweIntegrationService
from order.integration.gwe2IntegrationServeice import Gwe2IntegrationService
from order.integration.hermesIntegrationService import HermesIntegrationService
from order.integration.octopusIntegrationService import OctopusIntegrationService
from order.integration.ibilabIntegrationService import IbilabIntegrationService
from order.integration.yldFedexIntegrationService import YldFedexIntegrationService
from order.integration.yldUPSIntegrationService import YldUPSIntegrationService
from order.integration.ruiDianIntegrationService import RuiDianIntegrationService
from order.integration.cainiaoOverseaIntegrationService import CainiaoOverseaIntegrationService
from order.integration.dgdIntegrationService import DgdIntegrationService
from order.integration.ddIntegrationService import DdIntegrationService
from order.integration.shipberIntegrationService import ShipberIntegrationService
from order.integration.saichengIntegrationService import SaichengIntegrationService
from order.integration.maegmantIntegrationService import MaegmantIntegrationService
from order.integration.multicourierIntegrationService import MulticourierIntegrationService
from order.integration.jerryIntegrationService import JerryIntegrationService
from order.integration.jyIntegrationService import JyIntegrationService
from order.integration.gpsIntegrationService import GPSIntegrationService
from order.integration.maerskIntegrationService import MaerskIntegrationService
from order.integration.haiTunIntegrationService import HaiTunIntegrationService
from order.integration.yzIntegrationService import YzIntegrationService
from order.integration.rlabelIntegrationService import RLabelIntegrationService
from order.integration.colisIntegrationService import ColisIntegrationService
from order.integration.winitLmaIntegrationService import WinitLmaIntegrationService
from order.integration.yicIntegrationService import YicIntegrationService
from order.integration.zxLabelIntegrationService import ZxLabelIntegrationService
from order.integration.worldTechIntegrationService import WorldTechIntegrationService
from order.integration.sengiIntegrationService import SengiIntegrationService
from order.integration.skyeShipIntegrationService import SkyeShipIntegrationService
from order.integration.feikeIntegrationService import FeiKeIntegrationService
from order.integration.earlybirdIntegrationService import EarlyBirdIntegrationService
from order.integration.pacticIntegrationService import PacticIntegrationService
from order.integration.yodelIntegrationService import YodelIntegrationService
from order.integration.scurriIntegrationService import ScurriIntegrationService
from order.integration.hanJinIntegrationService import HanJinIntegrationService
from order.integration.asendiaIntegrationService import AsendiaIntegrationService
from order.integration.tongDaDpdIntegrationService import TongDaDpdIntegrationService
from order.integration.dhlNlIntegrationService import DhlNlIntegrationService
from order.integration.boFengService import BoFengIntegrationService
from order.integration.royalMailIntegrationService import RoyalMailIntegrationService
from order.integration.yidaService import YiDaIntegrationService
from order.integration.eboIntegrationService import EboIntegrationService
from order.integration.jingDongSmallBagIntegrationService import JingDongSmallBagIntegrationService
from order.integration.evriIntegrationService import EvriIntegrationService
from order.integration.shaoKeDHLIntegrationService import ShaokeDHLIntegrationService
from order.integration.meiShangAnXinIntegrationService import MeiShangAnXinIntegrationService
from order.integration.amazonShippingOneClickService import AmazonShippingOneClickService
from order.integration.sfService import SFIntegrationService
from order.integration.sfExportEcommerceService import SFExportEcommerceIntegrationService
from order.integration.anPostIntegrationService import AnPostIntegrationService
from order.integration.omsClientIntegrationService import OmsClientIntegrationService
from order.integration.fedExIntegrationService import FedExIntegrationService

from pms.serializers.product import ProductBasicRestrictionSerializer
from pms.util.calc import postcode_belong_to_product_zone
from pms.util.product_zone import get_zone_code_v2
from common.utils.arr_util import arr_to_str
from common.utils.response import fail_response, success_response
from rest_framework.response import Response
from rest_framework import status
from info.models import Dict
import time

import track

@transaction.atomic
def fail_orders(ids, user, request):
    queryset = ParcelCustomerOrder.objects.filter(id__in=ids, is_revenue_lock=False, is_cost_lock=False,
                                                  del_flag=False)
    if len(ids) != queryset.count():
        request.data['code'] = 400
        request.data['msg'] = '请选择未进行收入确认和成本确认的订单操作'
        return Response(data=request.data, status=status.HTTP_200_OK)

    success_message = []
    fail_message = []

    for id in ids:
        parcel_customer_orders = ParcelCustomerOrder.objects.filter(id=id, del_flag=False)
        if parcel_customer_orders.count() == 0:
            success_message.append('订单不存在 id=' + str(id))
            continue

        customer_order = parcel_customer_orders.first()

        order_num = customer_order.order_num
        if customer_order.order_status == 'VO':
            success_message.append(order_num)
            continue

        handler_cancel_label(customer_order, order_num, success_message, fail_message, user)

    if len(success_message) > 0 and not fail_message:
        return success_response(request, arr_to_str(success_message) + '作废成功')
    if len(fail_message) > 0 and not success_message:
        return fail_response(request, arr_to_str(fail_message))

    return success_response(request, arr_to_str(success_message) + '作废成功,' + arr_to_str(fail_message))


def review_order():
    """审核小包单是否收取取消费"""
    pass
    # parcel_orders = ParcelOrderExtend.objects.filter(
    #     customer_order=customer_orders.first().id, del_flag=False
    # ).select_related('customer_order')
    # confirm_progress_list = [order_.confirm_progress for order_ in parcel_orders]
    # if not confirm_progress_list:
    #     return Response(data={'msg': '订单不存在', 'code': 404}, status=status.HTTP_200_OK)
    # if confirm_progress_list[0] == '2':
    #     cancel_type = request.data.get('cancel_type', None)
    #     if not cancel_type:
    #         return Response(data={'msg': '订单已确认取消类型不能为空', 'code': 404}, status=status.HTTP_200_OK)
    #     try:
    #         # 获取取消订单收费项
    #         charge_cancel = Charge.objects.get(del_flag=0, status='on', code='F009')
    #     except Charge.DoesNotExist:
    #         raise ParamError('找不到取消订单收费项', ErrorCode.PARAM_ERROR)
    #     # 更新订单扩展表状态
    #     parcel_orders.update(
    #         confirm_progress=4,
    #         cancel_types=1,
    #         confirm_types=1,
    #         update_by=user,
    #         update_date=datetime.now(),
    #         cancel_type_api=cancel_type
    #     )
    #     # 遍历每个订单处理费用
    #     for parcel_order in parcel_orders:
    #         customer_order = parcel_order.customer_order
    #         # 获取该订单所有费用项
    #         charge_ins = ParcelOrderChargeIn.objects.filter(
    #             customer_order_num=parcel_order.id, del_flag=False
    #         )
    #         refund_account(parcel_order.customer_order, user, ParcelOrderChargeIn)
    #         # 计算总费用（处理空值）
    #         total_charge = charge_ins.aggregate(
    #             total=Sum('charge_total')
    #         )['total'] or 0
    #         total_charge = float(total_charge) * 0.1
    #         if charge_ins.exists():
    #             currency_type = charge_ins.first().currency_type
    #         else:
    #             raise ParamError('币种不存在', ErrorCode.DATA_NOT_VALID)
    #         if int(cancel_type) == 1:
    #             # 创建新费用项
    #             ParcelOrderChargeIn.objects.create(
    #                 charge=charge_cancel,
    #                 charge_rate=total_charge,
    #                 charge_count=1,
    #                 charge_total=total_charge,
    #                 currency_type=currency_type,
    #                 current_exchange=charge_ins.first().current_exchange,
    #                 account_charge=total_charge,
    #                 customer=charge_ins.first().customer,
    #                 is_system=True,
    #                 customer_order_num=customer_order,
    #                 charging_time=datetime.now(),
    #                 charge_value=total_charge,
    #                 published_account_charge=total_charge,
    #                 is_share=charge_ins.first().is_share,
    #                 share_charge_id=charge_ins.first().share_charge_id
    #             )
    #
    #             deduction_account(parcel_order.customer_order, user, ParcelOrderChargeIn)
    #
    #         else:
    #             parcel_orders = ParcelOrderExtend.objects.filter(
    #                 customer_order_id__in=customer_orders.first().id,
    #                 del_flag=False).exclude(confirm_progress=3)
    #             if parcel_orders.exists():
    #                 parcel_orders.update(confirm_progress=3, update_by=user, update_date=datetime.now())
    #             else:
    #                 raise ParamError('已经是申请状态', ErrorCode.DATA_NOT_VALID)
    #         # 删除旧费用项
    #         charge_ins.update(
    #             del_flag=True,
    #             update_by=user,
    #             update_date=datetime.now()
    #         )
    #     fail_orders([customer_orders.first().id], user, request)
    #     data = {
    #         'msg': order_num + '作废成功',
    #         'code': 200,
    #         'order_num': order_num,
    #         'customer_order_num': customer_order_num
    #     }
    #     return do_response_common(msg='Success', code=200, data=data)
    # elif confirm_progress_list[0] == '1':
    #     for parcel_order in parcel_orders:
    #         # 获取该订单所有费用项
    #         charge_ins = ParcelOrderChargeIn.objects.filter(
    #             customer_order_num=parcel_order.id, del_flag=False
    #         )
    #         charge_ins.update(
    #             del_flag=True,
    #             update_by=user,
    #             update_date=datetime.now()
    #         )
    #         fail_orders([customer_orders.first().id], user, request)
    #         data = {
    #             'msg': order_num + '作废成功',
    #             'code': 200,
    #             'order_num': order_num,
    #             'customer_order_num': customer_order_num
    #         }
    #         return do_response_common(msg='Success', code=200, data=data)
    # else:
    #     return Response(data={'msg': '订单已取消或者在取消中', 'code': 404}, status=status.HTTP_200_OK)


@transaction.atomic
def update_order_status(customer_order) -> bool:
    product_basic_restriction = ProductBasicRestriction.objects.filter(
        encoding='is_allowed_nullification',
        belong_type='A',
        type='C',
        product=customer_order.product,
        del_flag=False
    ).last()
    if product_basic_restriction:
        if product_basic_restriction.insert_trick:
            customer_order.order_status = 'BC'
            customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            customer_order.remark = '作废中'
            customer_order.save()
            return False

    customer_order.order_status = 'VO'
    customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    customer_order.remark = '取消订单'
    customer_order.save()
    return True


@transaction.atomic
def handler_cancel_label(customer_order, order_num, success_message, fail_message, user):
    if customer_order.order_status in ['VO', 'BC', 'CS']:
        success_message.append(order_num)
        return

    if customer_order.order_status == 'DR':
        customer_order.order_status = 'VO'
        customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        customer_order.remark = '取消订单(DR)'
        customer_order.save()
        success_message.append(order_num)
        return

    if customer_order.tracking_num and (
            customer_order.tracking_num == customer_order.label_billid or customer_order.order_num == customer_order.tracking_num):
        is_enable_refund = update_order_status(customer_order)
        if is_enable_refund:
            # 退款给客户
            refund_account(customer_order, user, ParcelOrderChargeIn)
        success_message.append(order_num)
        return

    order_label_task_list = ParcelOrderLabelTask.objects.filter(~(Q(status='VO') | Q(product__code='ZX_LABEL')),
                                                                order_num=customer_order.id,
                                                                del_flag=False)
    # 无下单任务，直接作废，并退款
    if order_label_task_list.count() == 0:
        is_enable_refund = update_order_status(customer_order)
        if is_enable_refund:
            # 退款给客户
            refund_account(customer_order, user, ParcelOrderChargeIn)
        success_message.append(order_num)
        return

    cancel_flag = False
    for order_label_task in order_label_task_list:
        key = 'handler_label_' + order_num
        val = cache.get(key)
        if val:
            fail_message.append('订单处理中暂不能作废' + str(order_num))
            continue

        key = 'handler_get_label_' + order_num
        val = cache.get(key)
        if val:
            fail_message.append('订单获取面单处理中暂不能作废' + str(order_num))
            continue

        if order_label_task.status == "UnHandled" and order_label_task.handle_times >= 121:
            order_label_task.status = 'VO'
            order_label_task.label_desc = '已作废,如已获取面单，请及时通知供应商'
            order_label_task.update_by = user
            order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            order_label_task.save()
            cancel_flag = True
            success_message.append(order_num)
            continue

        if settings.SYSTEM_VERSION == 'V2':
            success_flag, message, cancel_result = handle_order_cancellation_v2(order_label_task, customer_order,
                                                                             order_num, user)
        else:
            success_flag, message, cancel_result = handle_order_cancellation(order_label_task, customer_order,
                                                                             order_num, user)
        if success_flag:
            success_message.append(message)
            cancel_flag = cancel_result
        elif message:
            fail_message.append(message)
        
        if not cancel_flag:
            continue

    if cancel_flag:
        # 作废订单
        is_enable_refund = update_order_status(customer_order)
        if is_enable_refund:
            # 退款给客户
            refund_account(customer_order, user, ParcelOrderChargeIn)


@transaction.atomic
def handler_get_label(customer_order, user):
    """获取面单"""

    if not customer_order.tracking_num:
        raise ParamError('物流单号不存在', ErrorCode.PARAM_ERROR)

    order_label_task = ParcelOrderLabelTask.objects.filter(~Q(status='VO'), order_num=customer_order.id,
                                                                del_flag=False).first()

    parcel_order_parcel_list = ParcelOrderParcel.objects.filter(customer_order=customer_order).all()

    parcel_order_item_list = ParcelOrderItem.objects.filter(
        parcel_num__in=parcel_order_parcel_list.values_list('id', flat=True)).all()


    product = customer_order.product
    service = customer_order.service

    supplier_butt = service.butt_code
    api_setting_queryset = ServiceApiSetting.objects.filter(service=service, del_flag=False)
    logger.info(f'api_setting_queryset: {api_setting_queryset.values()}')
    settings_dict = {item.field_name: item.field_value for item in api_setting_queryset}
    class_name = supplier_butt.class_name
    class_name = upper_first(class_name)


    label_order_vo = LabelOrderVo()
    label_order_vo.orderLabelTask = order_label_task
    label_order_vo.service = service
    label_order_vo.customerOrder = customer_order
    label_order_vo.supplier_butt = supplier_butt
    label_order_vo.product = product
    label_order_vo.parcelList = parcel_order_parcel_list
    label_order_vo.parcelItemList = parcel_order_item_list
    label_order_vo.service_dict = settings_dict
    # 通过反射实例化对象
    obj = call_last_mile_service(class_name)
    result = get_label(obj, label_order_vo)
    return result


@transaction.atomic
def handler_force_cancel_order(customer_order, order_num, success_message, fail_message, user):
    if customer_order.order_status in ['VO', 'BC', 'CS']:
        success_message.append(order_num)
        return
    customer_order.remark = '强制取消订单'
    if customer_order.order_status == 'DR':
        customer_order.order_status = 'VO'
        customer_order.remark = '强制作废订单(DR)'
        customer_order.update_by = user
        customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        customer_order.save()
        success_message.append(order_num)
        return

    order_label_task_list = ParcelOrderLabelTask.objects.filter(~Q(status='VO'), order_num=customer_order.id,
                                                                del_flag=False)
    # 无下单任务，直接作废，并退款
    if order_label_task_list.count() == 0:
        is_enable_refund = update_order_status(customer_order)
        if is_enable_refund:
            # 退款给客户
            refund_account(customer_order, user, ParcelOrderChargeIn)
        success_message.append(order_num)
        return

    for order_label_task in order_label_task_list:
        key = 'handler_label_' + order_num
        val = cache.get(key)
        if val:
            fail_message.append('订单处理中暂不能作废' + str(order_num))
            continue

        key = 'handler_get_label_' + order_num
        val = cache.get(key)
        if val:
            fail_message.append('订单获取面单处理中暂不能作废' + str(order_num))
            continue

        order_label_task.status = 'VO'
        order_label_task.label_desc = '强制作废,如已获取面单请及时通知供应商'
        order_label_task.update_by = user
        order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label_task.save()                
        if not order_label_task.third_order_no and not val:
            pass
        else:
            ParcelOrderLabel.objects.filter(order_num=customer_order, del_flag=False) \
                .update(del_flag=True, update_by=user, update_date=datetime.now())

    # 作废订单
    is_enable_refund = update_order_status(customer_order)
    if is_enable_refund:
        # 退款给客户
        refund_account(customer_order, user, ParcelOrderChargeIn)
    success_message.append(order_num)

def create_cancel_order_task_with_orders(orders: List[ParcelCustomerOrder], source='UNKONW'):
    '''
    创建取消订单任务
    :param customer_order:
    :param user:
    :return:
    '''
    obj = Dict.objects.filter(
            label='取消操作是否添加取消任务',
            type='add_cancel_order_task_when_canceling', 
            del_flag=False
    ).last()
    if not obj:
        logger.info('没有开启取消操作自动添加取消任务')
        return
    if not orders:
           return
    for order in orders:
        if not CancelParcelCustomerOrderLabelTask.objects.filter(order=order).exists():
            CancelParcelCustomerOrderLabelTask.objects.create(
                order=order, 
                order_num=order.order_num,
                order_create_date=order.create_date,
                next_run_time=int(time.time()) + 300,
                source=source,
                customer_order_num=order.customer_order_num,
                tracking_num=order.tracking_num,
            )
        else:
            logger.info(f'订单 {order.order_num} 已存在二次取消任务，不再重复创建')


@transaction.atomic
def handler_confirm_ship(parcel_customer_orders, request, results, order_num, fail_results=None):
    """确认发货/确认订单"""
    if fail_results is None:
        fail_results = []

    if parcel_customer_orders.count() == 0:
        fail_results.append(str(order_num) + '订单不存在')
        return

    parcel_customer_order = parcel_customer_orders.first()
    order_num = parcel_customer_order.order_num

    if settings.SYSTEM_VERSION != 'V2':
        if parcel_customer_order.is_confirm_ship:
            results.append(order_num)
            return

        order_label_task_list = ParcelOrderLabelTask.objects.filter(status='ConfirmLabel',
                                                                    order_num=parcel_customer_order,
                                                                    del_flag=False)
        if order_label_task_list.exists():
            parcel_customer_order.is_confirm_ship = True
            parcel_customer_order.update_by = request.user
            parcel_customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            parcel_customer_order.save()
            results.append(order_num)
            return

        order_label_task_list = ParcelOrderLabelTask.objects.filter(status='Success',
                                                                    order_num=parcel_customer_order,
                                                                    del_flag=False)
        if not order_label_task_list.exists():
            fail_results.append(order_num + '未成功获取面单')
            return

        order_label_task = order_label_task_list[0]

        product = parcel_customer_order.real_product or parcel_customer_order.product
        # if not parcel_customer_order.service:
        service_list = Service.objects.filter(product=product.id, del_flag=False)
        if service_list.count() == 0:
            raise ParamError('产品[' + product.code + ']未配置服务', ErrorCode.PARAM_ERROR)

        service = service_list.first()
        # else:
        #     service = parcel_customer_order.service

    else:
        order_label_task = None
        product = parcel_customer_order.real_product or parcel_customer_order.product
        if not parcel_customer_order.service:
            raise ParamError('订单未配置资源', ErrorCode.PARAM_ERROR)
        else:
            service = parcel_customer_order.service

    if settings.SYSTEM_VERSION == 'V2':
        supplier_butt = service.butt_code
        api_setting_queryset = ServiceApiSetting.objects.filter(service=service, del_flag=False)
        logger.info(f'api_setting_queryset: {api_setting_queryset.values()}')
        settings_dict = {item.field_name: item.field_value for item in api_setting_queryset}
        class_name = supplier_butt.class_name
        class_name = upper_first(class_name)
        supplier_account = None
    else:
        settings_dict = None
        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        # if not supplier_butt.is_support_cancel:
        #     raise ParamError('产品[' + product.code + ']不支持确认发货', ErrorCode.PARAM_ERROR)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id,
                                                                   del_flag=False)
        supplier_account = supplier_account_list[0]
        class_name = supplier_butt.class_name
    label_order_vo = LabelOrderVo()
    label_order_vo.orderLabelTask = order_label_task
    label_order_vo.supplierAccount = supplier_account
    label_order_vo.service = service
    label_order_vo.customerOrder = parcel_customer_order
    label_order_vo.supplier_butt = supplier_butt
    label_order_vo.product = product
    label_order_vo.parcel_related_name = 'parcel'
    label_order_vo.parcelItem_related_name = 'parcelItem'
    label_order_vo.service_dict = settings_dict
    # 通过反射实例化对象
    obj = globals()[class_name]()
    result = confirm_ship(obj, label_order_vo)
    if settings.SYSTEM_VERSION == 'V2':
        create_user = parcel_customer_order.create_by
        if int(result['code']) == 200:
            parcel_customer_order.is_confirm_ship = True
            parcel_customer_order.order_status = 'CONFIRMED'
            parcel_customer_order.update_by = request.user
            parcel_customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            parcel_customer_order.save()

            logger.info(f'api_confirm_ship_or_update:{order_num} -->{result}')

            set_multistep_parcel_track(order_num, 'CONFIRMED-000', datetime.now(), create_user, '')
            # 订单变更已确认
            ParcelOrderExtend.objects.filter(customer_order=parcel_customer_order,
                                             del_flag=False).update(confirm_types=1, confirm_progress=2)
        else:
            set_multistep_parcel_track(order_num, 'CONFIRMED-999', datetime.now(), create_user, '')
        return result
    else:
        if result['code'] == '0':
            parcel_customer_order.is_confirm_ship = True
            parcel_customer_order.update_by = request.user
            parcel_customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            parcel_customer_order.save()
            order_label_task.label_desc = '确认发货'
            order_label_task.update_by = request.user
            order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            order_label_task.save()
            results.append(order_num)
        else:
            fail_results.append(f'{order_num}={result["msg"]}')


def handler_scan_form_v2(parcel_customer_orders, request_data, scan_form_type='api'):
    """
    scanform v2 版本
    scan_form_type: api、web
    """
    if settings.SYSTEM_VERSION == 'V2':
        if scan_form_type == 'api':
            net_weight = request_data.get('netWeightD')
            if not net_weight:
                raise ParamError(f'Please enter netWeightD: {net_weight}', ErrorCode.PARAM_ERROR)
            weight = request_data.get('weightD')
            if not weight:
                raise ParamError(f'Please enter weightD: {weight}', ErrorCode.PARAM_ERROR)
        else:
            net_weight = parcel_customer_orders.aggregate(total=Sum('weighing_weight'))['total']
            weight = parcel_customer_orders.aggregate(total=Sum('weight'))['total']

        # 封装 scan_form_data
        scan_form_data = {
            'HandOverInfo': None,
            "flightInfo": None,
            "net_weight": net_weight,
            "total_weight": weight,
            "order_nums": request_data.get('order_nums', '')
        }
    else:
        scan_form_data = {
            "order_nums": request_data.get('order_nums', '')
        }

    handoverinfo = request_data.get('handoverinfo')
    if handoverinfo:
        scan_form_data['HandOverInfo'] = ParcelOrderAddress(**handoverinfo)

    flightinfo = request_data.get('flightinfo')
    if flightinfo:
        scan_form_data['flightInfo'] = FlightInfo(
            mawbNo=flightinfo['mawbNo'],
            flightNo=flightinfo['flightNo'],
            etd=flightinfo['etd'],
            eta=flightinfo['eta'],
            fromPortCode=flightinfo['fromPortCode'],
            transitPortCode=flightinfo['transitPortCode'],
            toPortCode=flightinfo['toPortCode']
        )

    if parcel_customer_orders.count() == 0:
        raise ParamError(f'订单不存在', ErrorCode.PARAM_ERROR)

    # product_count = parcel_customer_orders.values('product').distinct().count()
    # if product_count > 1:
    #     raise ParamError(f'scanform订单集不能存在多个产品', ErrorCode.PARAM_ERROR)

    # warehouse_count = parcel_customer_orders.values('warehouse_code').distinct().count()
    # if warehouse_count > 1:
    #     raise ParamError(f'scanform订单集不能存在多个仓库', ErrorCode.PARAM_ERROR)

    tracking_nums = []
    for parcel_customer_order in parcel_customer_orders:
        tracking_nums.append(parcel_customer_order.tracking_num)

    parcel_customer_order = parcel_customer_orders.first()
    product = parcel_customer_order.product
    service = parcel_customer_order.service

    if not service:
        service_list = Service.objects.filter(product=product.id, del_flag=False)
        if service_list.count() == 0:
            raise ParamError('产品[' + product.code + ']未配置服务', ErrorCode.PARAM_ERROR)

        service = service_list.first()

    if settings.SYSTEM_VERSION == 'V2':
        api_setting_queryset = ServiceApiSetting.objects.filter(service=service, del_flag=False)
        logger.info(f'api_setting_queryset: {api_setting_queryset.values()}')
        settings_dict = {item.field_name: item.field_value for item in api_setting_queryset}
        class_name = service.butt_code.class_name
        class_name = upper_first(class_name)
        supplier_account = None
    else:
        settings_dict = None
        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(
            vendor_id=supplier_butt.vendor_id.id,
            del_flag=False
        )
        supplier_account = supplier_account_list[0]
        class_name = supplier_butt.class_name

    label_order_vo = LabelOrderVo()
    label_order_vo.supplierAccount = supplier_account
    label_order_vo.customerOrders = parcel_customer_orders
    label_order_vo.customerOrder = parcel_customer_order
    label_order_vo.parcel_related_name = 'parcel'
    label_order_vo.parcelItem_related_name = 'parcelItem'
    label_order_vo.product = product
    label_order_vo.service = service
    label_order_vo.is_unpack = False
    label_order_vo.service_dict = settings_dict or None
    label_order_vo.scan_form_data = scan_form_data
    label_order_vo.tracking_nums = tracking_nums
    label_order_vo.order_nums = tracking_nums
    # 通过反射实例化对象
    obj = globals()[class_name]()
    result = scanform(obj, label_order_vo)
    return result


# 优化重量
def optimize_weight(parcel_weight=None, product=None):
    if not product or not parcel_weight:
        return parcel_weight

    weight = (float(parcel_weight) * 1000)

    is_optimize_weight_queryset = ProductBasicRestriction.objects.filter(product=product, encoding='is_optimize_weight',
                                                                         del_flag=False)
    if is_optimize_weight_queryset.count() > 0:
        current_time = datetime.now()
        target_time = datetime(2023, 12, 18, 0, 0, 0)
        # 当前时间小于2023-12-18才能超过 则不运行
        if weight > 113.41 and weight <= 226.80 and is_optimize_weight_queryset and current_time < target_time:
            return float(random.randint(84, 110)) / 1000
        if weight > 226.81 and weight <= 340.19 and is_optimize_weight_queryset:
            return float(random.randint(196, 220)) / 1000
        if weight > 340.20 and weight <= 453.31 and is_optimize_weight_queryset:
            return float(random.randint(308, 330)) / 1000
        if weight > 453.32 and weight <= 700 and is_optimize_weight_queryset:
            return float(random.randint(420, 440)) / 1000

    # 根据比例 10%
    is_optimize_weight_rate_queryset = ProductBasicRestriction.objects.filter(product=product,
                                                                              encoding='is_optimize_weight_rate',
                                                                              del_flag=False)

    if is_optimize_weight_rate_queryset.count() > 0:
        return float((Decimal(parcel_weight) * Decimal('0.90')).quantize(Decimal('0.000'), ROUND_DOWN))

    return parcel_weight


# 113.41到226.80g的订单随机改到 84-110g
# 226.81g到340.19g的订单随机改到 196-220g
# 340.20g到453.31g 的订单随机改到 308-330g
# 453.32g到700g的订单随机改到  420-440g

def get_sku_and_num(parcel_order):
    parcel_item_queryset = ParcelOrderItem.objects.filter(parcel_num__customer_order=parcel_order,
                                                          del_flag=False)
    sku_and_num = []
    i = 0
    for parcel_item in parcel_item_queryset:
        if i > 4:
            break
        if parcel_item.item_code:
            item_code = str(parcel_item.item_qty) + ' * ' + parcel_item.item_code
            if len(item_code) > 60:
                item_code = item_code[0:60]
            sku_and_num.append(item_code)
        i += 1
    return sku_and_num


# 限制客户
def handler_product_limit_user(customer, product):
    if not customer or not product:
        return
    product_limit_user_queryset = ProductLimitUser.objects.filter(product=product, del_flag=False)
    if product_limit_user_queryset:
        product_limit_user_list = product_limit_user_queryset.filter(customer=customer)
        if product_limit_user_list.count() == 0:
            raise ParamError('该产品限制客户' + str(product.code) + '无法使用，请联系客服', ErrorCode.PARAM_ERROR)


# 组装参数
def assemble_barcode_params(parcel_order):
    """

        product_code='女士连衣裙 黑色 L 200g',
        sku_name='T-001 女士连衣裙 黑色 L 200g',
        qty='6',
        product_name='美国电商小包 特惠-E',
    @param parcel_order:
    @return:
    """

    sku_and_num = get_sku_and_num(parcel_order)
    address_num = ''
    if parcel_order.warehouse_code:
        address_num = parcel_order.warehouse_code.address_num

    parcel_item_queryset = ParcelOrderItem.objects.filter(
        parcel_num__customer_order=parcel_order,
        del_flag=False
    )
    sku_name = declared_name_cn = declared_name_en = ''
    if parcel_item_queryset:
        # 取前2个
        parcel_items = parcel_item_queryset[:2]
        # 中文品名
        declared_name_cn = ' '.join([item.declared_nameCN or '' for item in parcel_items])
        declared_name_en = ' '.join([item.declared_nameEN or '' for item in parcel_items])
        # SKU
        # sku_name = ''.join([item.declared_nameEN + '*' + str(item.item_qty) for item in parcel_items])
        sku_name = parcel_item_queryset.first().item_code

    # 扩展属性 是否显示
    label_ext_show = get_productBasicRestriction_defalut_value(parcel_order.product, 'label_ext_show',
                                                               parcel_order.buyer_country_code)

    order = {
        "order_num": parcel_order.order_num,
        "product_code": parcel_order.product.name,
        "pices": 1,
        "weight": parcel_order.weight,
        "buyer_address_num": '',
        'sku_and_num': sku_and_num,
        'address_num': address_num,
        'customer_order_num': parcel_order.customer_order_num,
        'tracking_num': parcel_order.tracking_num,
        'buyer_name': parcel_order.buyer_name or '',
        'buyer_address_one': parcel_order.buyer_address_one or '',
        'buyer_city_code': parcel_order.buyer_city_code or '',
        'buyer_state': parcel_order.buyer_state or '',
        'buyer_country_code': parcel_order.buyer_country_code or '',
        'buyer_postcode': parcel_order.buyer_postcode,
        'sku_name': sku_name or '',
        'declared_name_cn': declared_name_cn or '',
        'declared_name_en': declared_name_en or '',
        'ext_show': label_ext_show,
    }
    return order


def assemble_params_item(table, index, customer_index, order_list, parcel_no, user=None):
    """
    assemble_params 拼接参数
    customer_index 是否是客户下单
    """
    customer_order_num = str(table.cell_value(index, 0)).strip().replace('.0', '')
    logger.info(f'customer_order_num {customer_order_num}')

    # 使用列表推导式查找匹配的字典
    order_data = next((d for d in order_list if customer_order_num in d.values()), None)
    parcel_num = str(table.cell_value(index, 14 + customer_index)).strip()
    # 获取上一个的包裹编号
    is_parcel_num = False
    try:
        parcel_last = order_data.get('parcel')[-1].get("parcel_num")
        logger.info(f'上一次包裹号 {parcel_last}')
        logger.info(f'本次包裹号 {parcel_num}')
        if parcel_num == parcel_last:
            is_parcel_num = True
    except:
        is_parcel_num = False

    # 创建商品
    item_length = table.cell_value(index, 29 + customer_index) or 1
    check_is_decimal(item_length, f"第{index + 1}行Length(CM)单元格的值不是数字类型,如{item_length}")
    item_width = table.cell_value(index, 30 + customer_index) or 1
    check_is_decimal(item_width, f"第{index + 1}行Width(CM)单元格的值不是数字类型,如{item_width}")
    item_height = table.cell_value(index, 31 + customer_index) or 1
    check_is_decimal(item_height, f"第{index + 1}行Heigth(CM)单元格的值不是数字类型,如{item_height}")
    item_weight = table.cell_value(index, 32 + customer_index) or 0.001
    check_is_decimal(item_weight, f"第{index + 1}行Weight(KG)单元格的值不是数字类型,如{item_weight}")

    parcel_item = {
        'item_code': str(table.cell_value(index, 20 + customer_index) or '').strip(),  # 物品号
        'item_name': str(table.cell_value(index, 21 + customer_index) or '').strip(),  # 物品名称
        'item_qty': table.cell_value(index, 22 + customer_index) or 1,  # 数量
        'sale_price': table.cell_value(index, 23 + customer_index) or 1,  # 销售价格
        'sale_currency': str(table.cell_value(index, 24 + customer_index) or 'USD').strip(),  # 报价币种
        'declared_price': table.cell_value(index, 25 + customer_index) or 1,  # 申报价格
        'declared_currency': str(table.cell_value(index, 26 + customer_index) or '').strip(),  # 申报币种
        'declared_nameCN': str(table.cell_value(index, 27 + customer_index) or '').strip(),  # 中文申报品名
        'declared_nameEN': str(table.cell_value(index, 28 + customer_index) or '').strip(),  # 英文申报品名
        'item_length': Decimal(item_length),  # 长
        'item_width': Decimal(item_width),  # 宽
        'item_height': Decimal(item_height),  # 高
        'item_weight': Decimal(item_weight),  # 重量
        'item_volume': Decimal(item_length) * Decimal(item_width) * Decimal(item_height) / 1000000,  # 体积
        'texture': str(table.cell_value(index, 33 + customer_index) or '').strip(),  # SKU Quality
        'use': str(table.cell_value(index, 34 + customer_index) or '').strip(),  # SKU Usage
        'brand': str(table.cell_value(index, 35 + customer_index) or '').strip(),  # SKU Brand
        'model': str(table.cell_value(index, 36 + customer_index) or '').strip(),  # SKU Model
        'customs_code': str(table.cell_value(index, 37 + customer_index) or '').strip(),  # 海关编码
    }

    if not order_data:

        parcel_length = table.cell_value(index, 16 + customer_index)
        check_is_decimal(parcel_length, f"第{index + 1}行Parcel Length(CM)单元格的值不是数字类型,如{parcel_length}")

        parcel_width = table.cell_value(index, 17 + customer_index)
        check_is_decimal(parcel_width, f"第{index + 1}行Parcel Width(CM)单元格的值不是数字类型,如{parcel_width}")

        parcel_height = table.cell_value(index, 18 + customer_index)
        check_is_decimal(parcel_height, f"第{index + 1}行Parcel Height(CM)单元格的值不是数字类型,如{parcel_height}")

        parcel_weight = table.cell_value(index, 19 + customer_index)
        check_is_decimal(parcel_weight, f"第{index + 1}行Parcel Weight(KG)单元格的值不是数字类型,如{parcel_weight}")
        parcel_weight = round(Decimal(parcel_weight), 4)

        volume = Decimal(parcel_length) * Decimal(parcel_width) * Decimal(parcel_height) * Decimal(0.000001)
        volume = round(volume, 4)

        parcel = {
            'parcel_num': parcel_num,  # 包裹号
            'parcel_item': [parcel_item],
            'parcel_desc': str(table.cell_value(index, 15 + customer_index)).strip(),  # 包裹描述
            'parcel_length': Decimal(parcel_length),  # 长
            'parcel_width': Decimal(parcel_width),  # 宽
            'parcel_height': Decimal(parcel_height),  # 高
            'parcel_weight': parcel_weight,  # 重量
            'parcel_volume': volume,
        }
        num1 = (Decimal(volume) / Decimal(6000))
        if num1 > Decimal(parcel_weight):
            charge_weight = num1
        else:
            charge_weight = Decimal(parcel_weight)
        logger.info(f'解析小包excel参数，获取小包单计费重：{charge_weight}')
        order_data = {
            'order_status': 'WO',
            'customer_order_num': customer_order_num,
            'product_code': str(table.cell_value(index, 1 + customer_index)).strip(),
            'warehouse_code': str(table.cell_value(index, 2 + customer_index)).strip(),
            'buyer_company_name': check_none(table.cell_value(index, 3 + customer_index)),  # 公司名字
            'buyer_name': check_none(table.cell_value(index, 4 + customer_index)),  # 收件人
            'buyer_address_one': check_none(table.cell_value(index, 5 + customer_index)),  # 地址1
            'buyer_address_two': check_none(table.cell_value(index, 6 + customer_index)),  # 地址2
            'buyer_house_num': num_to_str(table, index, 7 + customer_index),  # 门牌号
            'buyer_city': check_none(table.cell_value(index, 8 + customer_index)),  # 收件人城市
            'buyer_city_code': check_none(table.cell_value(index, 8 + customer_index)),  # 收件人城市Code
            'buyer_state': check_none(table.cell_value(index, 9 + customer_index)),  # 收件人州
            'buyer_postcode': str(table.cell_value(index, 10 + customer_index)).strip().replace('.0', ''),  # 邮编
            'buyer_country': check_none(table.cell_value(index, 11 + customer_index)),  # 收件人国家
            'buyer_country_code': check_none(table.cell_value(index, 11 + customer_index)),  # 收件人国家
            'buyer_mail': check_none(table.cell_value(index, 12 + customer_index)),  # 收件人邮箱
            'buyer_phone': str(table.cell_value(index, 13 + customer_index)).strip().replace('.0', ''),  # 收件人电话
            'weight': parcel_weight,  # 重量就为包裹的重量
            'volume': volume,  # 体积
            'charge_weight': charge_weight,
            'ioss_num': str(table.cell_value(index, 38 + customer_index) or '').strip().replace('.0', ''),  # IOSS
            'buyer_tax': str(table.cell_value(index, 39 + customer_index) or '').strip().replace('.0', ''),  # tax
            'parcel': [parcel]
        }

        if customer_index == 1:
            order_data['customer_code'] = str(table.cell_value(index, 1)).strip().replace('.0', '')
        else:
            order_data['customer_code'] = user.company

        order_list.append(order_data)
    else:
        if is_parcel_num:
            logger.info(f'进入相同包裹2')
            parcel = order_data['parcel']

            parcel_item_arr = parcel[-1]['parcel_item']
            parcel_item_arr.append(parcel_item)
        else:

            # 支持多包裹的情况
            parcel_length = table.cell_value(index, 16 + customer_index)
            check_is_decimal(parcel_length, f"第{index + 1}行Parcel Length(CM)单元格的值不是数字类型,如{parcel_length}")

            parcel_width = table.cell_value(index, 17 + customer_index)
            check_is_decimal(parcel_width, f"第{index + 1}行Parcel Width(CM)单元格的值不是数字类型,如{parcel_width}")

            parcel_height = table.cell_value(index, 18 + customer_index)
            check_is_decimal(parcel_height, f"第{index + 1}行Parcel Height(CM)单元格的值不是数字类型,如{parcel_height}")

            parcel_weight = table.cell_value(index, 19 + customer_index)
            check_is_decimal(parcel_weight, f"第{index + 1}行Parcel Weight(KG)单元格的值不是数字类型,如{parcel_weight}")
            parcel_weight = round(Decimal(parcel_weight), 4)

            volume = Decimal(parcel_length) * Decimal(parcel_width) * Decimal(parcel_height) * Decimal(0.000001)
            volume = round(volume, 4)

            new_parcel = {
                'parcel_num': parcel_num,  # 包裹号
                'parcel_item': [parcel_item],
                'parcel_desc': str(table.cell_value(index, 15 + customer_index)).strip(),  # 包裹描述
                'parcel_length': Decimal(parcel_length),  # 长
                'parcel_width': Decimal(parcel_width),  # 宽
                'parcel_height': Decimal(parcel_height),  # 高
                'parcel_weight': parcel_weight,  # 重量
                'parcel_volume': volume,
            }

            order_data['parcel'].append(new_parcel)
    logger.info(f'-----order_data测试书局: {order_data}')
    return order_data


# 组装execl order参数
def assemble_params(nrows, table, customer_index=0, user=None):
    '''
    customer_index 是否是客户下单
    '''
    customer_title = table.cell_value(0, 1)
    if 'Customer' in customer_title and customer_index == 0:
        raise ParamError(f'模版不正确,客户请使用客户端下载的模版', ErrorCode.PARAM_ERROR)
    if 'Customer' not in customer_title and customer_index == 1:
        raise ParamError(f'模版不正确,缺少客户编码列', ErrorCode.PARAM_ERROR)

    order_list = []
    parcel_no = []
    for index in range(1, nrows):
        assemble_params_item(table, index, customer_index, order_list, parcel_no, user)

    return order_list


def check_is_decimal(number, error_massage):
    try:
        Decimal(number)
    except:
        raise ParamError(error_massage, ErrorCode.PARAM_ERROR)


def check_none(cell_value):
    if not cell_value:
        return ''

    return str(cell_value).strip()


class ProductData:
    def __init__(self, order_params, product_id):
        self.data = {**order_params, "product": product_id}


def upload_parcel_customer_order_item(order_data, user):
    """上传小包，解析excel"""
    logger.info(f'测试order_data专用{order_data}')
    parcel_params = order_data.pop('parcel')
    # parcel_item_params = parcel_params.pop('parcel_item')

    customer_order_num = order_data['customer_order_num']
    if customer_order_num:
        old_order_queryset = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                                customer_order_num=customer_order_num,
                                                                del_flag=False)
        if old_order_queryset.count() > 0:
            raise ParamError('客户订单号(' + str(customer_order_num) + ')不能重复, 对应订单号:' +
                             old_order_queryset.first().order_num, ErrorCode.PARAM_ERROR)

    # 判断客户
    customer_code = order_data.pop('customer_code')
    logger.info(f'customer--->>{customer_code}')
    customer_queryset = Company.objects.filter(short_name=customer_code, del_flag=False)
    if not customer_queryset.exists():
        raise ParamError(f'未找到此客户信息{customer_code}', ErrorCode.PARAM_ERROR)
    order_data['customer'] = customer_queryset.first()

    # 获取产品
    product_code_val = order_data.pop('product_code')
    product_queryset = Product.objects.filter(code=product_code_val, del_flag=False, type='PC')
    if product_queryset.count() == 0:
        raise ParamError(f'产品{product_code_val}不存在', ErrorCode.PARAM_ERROR)

    product = product_queryset.first()
    order_data['product'] = product
    service_list = Service.objects.filter(product=product.id, del_flag=False, is_default=True)
    if service_list.count() == 0:
        raise ParamError(f'产品[{product.code}]未配置服务', ErrorCode.PARAM_ERROR)

    service = service_list.first()
    order_data['service'] = service

    # 获取仓库
    warehouse_code = order_data.pop('warehouse_code')
    if product.address_num:
        address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
        if address_queryset.count() == 0:
            raise ParamError(f'产品{product.code}里配置的仓库地址编码[{product.address_num}]不存在，请检查',
                             ErrorCode.PARAM_ERROR)
        order_data['warehouse_code'] = address_queryset.first()

    elif warehouse_code and str(warehouse_code).strip():
        warehouse_code_val = str(warehouse_code).strip()
        address_queryset = Address.objects.filter(address_num=warehouse_code_val, address_type='SP',
                                                  del_flag=False)
        if address_queryset.count() == 0:
            raise ParamError(f'客户单号:{customer_order_num},仓库地址编码[{warehouse_code_val}]不存在，请检查',
                             ErrorCode.PARAM_ERROR)
        order_data['warehouse_code'] = address_queryset.first()
    else:
        raise ParamError(f'客户单号:{customer_order_num},仓库地址编码不存在，请检查',
                         ErrorCode.PARAM_ERROR)

    # 插入订单
    parcel_customer_order = ParcelCustomerOrder.objects.create(**order_data,
                                                               **get_update_params_by_user(user, True))
    # 生成订单号
    gen_parcel_order_num(parcel_customer_order)

    # 字段检查 和插入轨迹
    if product:

        if get_productBasicRestriction(product, 'process_gb_postcode', parcel_customer_order.buyer_country_code):
            parcel_customer_order.buyer_postcode = process_gb_postcode(parcel_customer_order.buyer_postcode)

        # 是否合并地址1和地址2
        if get_productBasicRestriction(product, 'is_merge_address_one_two', parcel_customer_order.buyer_country_code) \
                and parcel_customer_order.buyer_address_two:
            parcel_customer_order.buyer_address_one += ' ' + parcel_customer_order.buyer_address_two
            parcel_customer_order.buyer_address_two = ''

        check_order_data(order_data, product)
        check_parcel_data(parcel_params, product, order_data['buyer_country_code'])
        add_create_order_track_info(product, parcel_customer_order.order_num)
        # product_data = ProductData(order_data, get(product, 'id'))
        # create_order_check(product_data, parcel_customer_order, "POST")

    parcel_customer_order.save()
    logger.info(f'测试parcel_params专用2{parcel_params}')
    # 添加包裹
    for parcel_p in parcel_params:
        parcel_item_params = parcel_p.pop('parcel_item')
        parcel_order_parcel = ParcelOrderParcel.objects.create(**parcel_p,
                                                               customer_order=parcel_customer_order,
                                                               **get_update_params_by_user(user, True))
        # 创建包裹里商品
        for parcel_item_param in parcel_item_params:
            ParcelOrderItem.objects.create(**parcel_item_param,
                                           parcel_num=parcel_order_parcel,
                                           **get_update_params_by_user(user, True))

    #
    product = parcel_customer_order.product
    if product.label_type == 'WC':

        if not parcel_customer_order.warehouse_code and product and (
                product.is_valuation or product.is_cost_valuation):
            raise ParamError('计价异常:未配置发货地址', ErrorCode.PARAM_ERROR)

        # 是否收入计价
        if product and product.is_valuation:
            ParcelOrderChargeIn.objects.filter(customer_order_num=parcel_customer_order, del_flag=False,
                                               is_system=True).update(del_flag=True)
            add_revenue(parcel_customer_order, user, ParcelOrderChargeIn)
        # 是否成本计价
        if product and product.is_cost_valuation:
            ParcelOrderChargeOut.objects.filter(customer_order_num=parcel_customer_order, del_flag=False,
                                                is_system=True).update(del_flag=True)
            add_cost(parcel_customer_order, user, ParcelOrderChargeOut)

        # 从客户账号扣钱
        if product.is_valuation:
            deduction_account(parcel_customer_order, user, ParcelOrderChargeIn)

        # 创建面单定时任务
        create_order_label_task(parcel_customer_order, user, ParcelOrderLabelTask, ParcelOrderParcel)

    # elif product.label_type == 'ZW':
    elif product.label_type in ('ZW', 'WWWC'):
        parcel_customer_order.tracking_num = parcel_customer_order.order_num

    parcel_customer_order.order_status = 'WO'
    parcel_customer_order.update_date = datetime.now()
    parcel_customer_order.update_by = user
    parcel_customer_order.save()


# 上传小包，解析excel
def upload_parcel_customer_order(customer_index, nrows, table, user):
    order_data_list = assemble_params(nrows, table, customer_index, user)
    for order_data in order_data_list:
        upload_parcel_customer_order_item(order_data, user)

def upload_changed_parcel_customer_order(nrows, table, user):
    """处理批量更改小包清关信息函数 - 优化版本"""
    
    # 第一步：按订单号归类导入数据
    order_data_dict = {}
    
    for index in range(1, nrows):
        parcel_order_num = table.cell_value(index, 0) or ''  # 订单号
        if not parcel_order_num:
            continue
            
        # 如果订单号不在字典中，初始化为空列表
        if parcel_order_num not in order_data_dict:
            order_data_dict[parcel_order_num] = []
        
        # 收集当前行的商品数据
        item_data = {
            'declared_name_cn': table.cell_value(index, 1) or '',  # 中文品名
            'declared_name_en': table.cell_value(index, 2) or '',  # 英文品名
            'customs_code': table.cell_value(index, 3) or '',  # 海关编码
            'goods_quantity': table.cell_value(index, 4) or '',  # 商品数量
            'declared_price': table.cell_value(index, 5) or '',  # 申报价值
            'declared_currency': table.cell_value(index, 6) or '',  # 申报币种
            'goods_weight': table.cell_value(index, 7) or '',  # 商品重量
            'ioss_num': table.cell_value(index, 8) or ''  # ioss num
        }
        
        order_data_dict[parcel_order_num].append(item_data)
    
    # 第二步：批量处理每个订单
    for order_num, items_data in order_data_dict.items():
        try:
            # 一次性查询订单信息
            parcel_customer_order = ParcelCustomerOrder.objects.filter(
                order_num=order_num, 
                del_flag=False
            ).first()
            
            if not parcel_customer_order:
                logger.warning(f"未找到订单号为 {order_num} 的小包订单")
                continue

            # 处理IOSS更新（取第一条数据的ioss_num）
            first_item_data = items_data[0]
            if first_item_data['ioss_num']:
                parcel_order_extend = parcel_customer_order.parcelOrderExtends.filter(del_flag=False).first()
                if parcel_order_extend:
                    parcel_order_extend.label_ioss_num = first_item_data['ioss_num']
                    parcel_order_extend.update_by = user
                    parcel_order_extend.is_changed_customs_clearance = True
                    parcel_order_extend.save()
                else:
                    logger.warning(f"订单号 {order_num} 未找到对应的扩展表记录")

            # 一次性查询该订单下的所有包裹
            parcels = ParcelOrderParcel.objects.filter(
                customer_order=parcel_customer_order,
                del_flag=False
            )

            # 一次性查询该订单下的所有商品（按ID排序保证顺序一致）
            parcel_items = ParcelOrderItem.objects.filter(
                parcel_num__in=parcels,
                del_flag=False
            ).order_by('id')

            if not parcel_items.exists():
                logger.warning(f"订单号 {order_num} 下未找到商品信息")
                continue

            # 第三步：按顺序一一对应更新
            # 取较小的数量进行更新
            update_count = min(len(items_data), parcel_items.count())
            
            for i in range(update_count):
                item_data = items_data[i]
                parcel_item = parcel_items[i]
                updated = False
                
                # 更新各个字段
                if item_data['declared_name_cn']:
                    parcel_item.label_declared_nameCN = item_data['declared_name_cn']
                    updated = True
                    
                if item_data['declared_name_en']:
                    parcel_item.label_declared_nameEN = item_data['declared_name_en']
                    updated = True
                    
                if item_data['customs_code']:
                    parcel_item.label_customs_code = item_data['customs_code']
                    updated = True
                    
                if item_data['goods_quantity']:
                    try:
                        parcel_item.label_item_qty = int(item_data['goods_quantity'])
                        updated = True
                    except (ValueError, TypeError):
                        logger.warning(f"订单号 {order_num} 第{i+1}个商品数量格式错误: {item_data['goods_quantity']}")
                        
                if item_data['declared_price']:
                    try:
                        parcel_item.label_declared_price = Decimal(str(item_data['declared_price']))
                        updated = True
                    except (ValueError, TypeError):
                        logger.warning(f"订单号 {order_num} 第{i+1}个商品申报价格格式错误: {item_data['declared_price']}")
                        
                if item_data['declared_currency']:
                    parcel_item.label_declared_currency = item_data['declared_currency']
                    updated = True
                    
                if item_data['goods_weight']:
                    try:
                        parcel_item.label_item_weight = Decimal(str(item_data['goods_weight']))
                        updated = True
                    except (ValueError, TypeError):
                        logger.warning(f"订单号 {order_num} 第{i+1}个商品重量格式错误: {item_data['goods_weight']}")

                if updated:
                    parcel_item.update_by = user
                    parcel_item.save()

            logger.info(f"成功更新订单号 {order_num} 的清关信息，更新了 {update_count} 个商品")

        except Exception as e:
            logger.error(f"更新订单号 {order_num} 的清关信息时发生错误: {str(e)}")
            continue

    logger.info("批量更改小包清关信息处理完成")

# 添加打单定时任务 ,HLYZ: 华磊邮政, ZX_LABEL:中性面单
def add_label_task_by_product_type(customer_order, user, product_code='HLYZ'):
    product = Product.objects.filter(code=product_code, del_flag=False).first()
    if not product:
        raise ParamError(f'未找到产品编码对应的产品, 编码: {product_code}', ErrorCode.PARAM_ERROR)
    order_label_task_list = ParcelOrderLabelTask.objects.filter(~Q(status='VO'),
                                                                order_num=customer_order.id,
                                                                del_flag=False,
                                                                product=product)
    if order_label_task_list.count() == 0:
        order_label_task = ParcelOrderLabelTask()
        order_label_task.order_num = customer_order
        order_label_task.status = 'UnHandled'
        order_label_task.mode_key = gen_mode_key_by_shunt(product.id)
        order_label_task.handle_times = '0'
        order_label_task.create_by = user
        order_label_task.product = product
        order_label_task.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label_task.save()


# 组装发货清单证明参数
def assemble_delivery_certificate_params(parcel_order):
    """
    @param parcel_order:
    @return:
    """
    tracking_num = parcel_order.tracking_num
    name = parcel_order.buyer_name
    address = parcel_order.buyer_address_one
    full_address = parcel_order.buyer_address_one or "" + ' - ' + parcel_order.buyer_address_two or ""
    phone = parcel_order.buyer_phone
    post_code = parcel_order.buyer_postcode
    order_num = parcel_order.order_num
    customer_order_num = parcel_order.customer_order_num
    buyer_country = parcel_order.buyer_country
    ship_date = parcel_order.ship_date
    parcel_track = ParcelTrack.objects.filter(order_num=parcel_order.order_num,
                                              track_code="OL022-000").first()
    if parcel_track:
        delivery_datetime = parcel_track.actual_time.strftime("%Y-%m-%d %H:%M:%S")
    else:
        delivery_datetime = ""

    parcel_item_queryset = ParcelOrderItem.objects.filter(
        parcel_num__customer_order=parcel_order,
        del_flag=False
    )
    sku_name = ''
    if parcel_item_queryset:
        # 取前2个
        parcel_items = parcel_item_queryset[:2]
        sku_name = ''.join([item.declared_nameEN for item in parcel_items])

    package_weight = str(round(float(parcel_order.weight), 2))

    # ASCAN
    outbound_time = parcel_order.outbound_time
    if not outbound_time:
        outbound_track = ParcelTrack.objects.filter(del_flag=False, order_num=parcel_order.order_num,
                                                    system_code='outbound').first()
        if outbound_track:
            outbound_time = outbound_track.actual_time

    if outbound_time:
        outbound_time = outbound_time.strftime("%Y-%m-%d")

    return {
        "delivery_datetime": delivery_datetime,
        "ship_date": ship_date,
        "customer_order_num": customer_order_num,
        "full_address": full_address,
        "buyer_country": buyer_country,
        "order_num": order_num,
        "delivery_date": outbound_time,
        "tracking_num": tracking_num or '',
        "name": name or '',
        "address": address or '',
        "phone": phone or '',
        "post_code": post_code or '',
        "sku_name": sku_name or '',
        "package_weight": package_weight
    }


def handler_create_order(customer_order, customer_order_charge_in_list_data, customer_order_charge_out_list_data,
                         parcel_item_data, user, validated_data, product_id):
    # 字段检查
    product_obj = Product.objects.get(id=product_id, del_flag=False)
    if product_obj:

        if get_productBasicRestriction(product_obj, 'process_gb_postcode', customer_order.buyer_country_code):
            customer_order.buyer_postcode = process_gb_postcode(customer_order.buyer_postcode)

        # 是否合并地址1和地址2
        if get_productBasicRestriction(product_obj, 'is_merge_address_one_two', customer_order.buyer_country_code) \
                and customer_order.buyer_address_two:
            customer_order.buyer_address_one += ' ' + customer_order.buyer_address_two
            customer_order.buyer_address_two = ''

        check_order_data(validated_data, product_obj)
        check_parcel_data(parcel_item_data, product_obj, customer_order.country_code)
        add_create_order_track_info(product_obj, customer_order.order_num)
        check_prop_limit(customer_order, product_obj)

    handler_product_limit_user(customer_order.customer, product_obj)

    # 设置系统自动生成单据号码
    customer_order.save()
    gen_parcel_order_num(customer_order)
    customer_order.save()
    if product_obj:
        add_create_order_track_info(product_obj, customer_order.order_num)
    logger.info(f'----order_num-->>{customer_order.order_num}')

    # 创建包裹和包裹明细
    if parcel_item_data is not None:
        for item in parcel_item_data:
            # 判断是否已经存在了这个包裹
            if ParcelOrderParcel.objects.filter(customer_order=customer_order,
                                                parcel_num=item['parcel_num']).count() == 0:
                # 生成新的包裹
                parcel = save_parcel_order_parcel(customer_order, item)
            else:
                parcel = ParcelOrderParcel.objects.get(customer_order=customer_order, del_flag=False,
                                                       parcel_num=item['parcel_num'])

            # 生成包裹里面的商品
            save_parcel_item(item, parcel)

    product = customer_order.product
    express_type = product.label_type if product else None
    # 中性称重换单的产品订单不在更新订单的时候计费
    if express_type not in ['ZW', 'HW', 'WWWC']:
        # 创建收入记录
        save_customer_order_charge_in(customer_order, customer_order_charge_in_list_data, user)
        # 创建成本记录
        save_customer_order_charge_out(customer_order, customer_order_charge_out_list_data, user)

    if customer_order.order_status == 'WO' and product.label_type not in ['ZW', 'HW', 'WA', 'WWWC']:

        if not customer_order.warehouse_code and product and (product.is_valuation or product.is_cost_valuation):
            raise ParamError('计价异常:未配置发货地址', ErrorCode.PARAM_ERROR)

        if product and product.is_valuation:
            add_revenue(customer_order, user, ParcelOrderChargeIn)
        if product and product.is_cost_valuation:
            add_cost(customer_order, user, ParcelOrderChargeOut)

    customer_order.create_by = user
    customer_order.update_by = user
    customer_order.create_date = datetime.now()
    customer_order.update_date = datetime.now()

    parcel_list = ParcelOrderParcel.objects.filter(customer_order=customer_order, del_flag=False)
    # 统计体积
    volumn = 0
    for parcel in parcel_list:
        volumn += parcel.parcel_volume
    customer_order.volume = volumn

    customer_order.save()
    return customer_order


# def save_parcel_order_parcel(customer_order, item):
#     parcel = ParcelOrderParcel()
#     parcel.parcel_num = item['parcel_num']
#     parcel.parcel_length = item['parcel_length'] or 0
#     parcel.parcel_width = item['parcel_width'] or 0
#     parcel.parcel_height = item['parcel_height'] or 0
#     parcel.parcel_weight = item['parcel_weight'] or 0
#     parcel.is_electronic = item.get('is_electronic', 0)
#     parcel.label_weight = optimize_weight(parcel.parcel_weight, customer_order.product)
#
#     parcel.parcel_qty = 1
#     parcel.remark = item.get('remark', None)
#     if (item['parcel_width'] or 0) != 0 and (item['parcel_length'] or 0) != 0 and (item['parcel_height'] or 0) != 0:
#         parcel.parcel_volume = Decimal(item['parcel_length']) * Decimal(item['parcel_width']) * Decimal(
#             item['parcel_height']) / 1000000
#     else:
#         parcel.parcel_volume = 0
#     parcel.customer_order = customer_order
#     parcel.save()
#     return parcel


# def save_parcel_item(item, parcel):
    # if not item.get('item_qty', None):
    #     raise ParamError('商品数量不为空', ErrorCode.PARAM_ERROR)
    # if not item.get('item_weight', None):
    #     raise ParamError('商品重量不为空', ErrorCode.PARAM_ERROR)
    # parcel_item = ParcelOrderItem()
    # parcel_item.parcel_num = parcel
    # parcel_item.item_code = item.get('item_code', None)
    # parcel_item.declared_nameCN = item.get('declared_nameCN', None)
    # parcel_item.declared_nameEN = item.get('declared_nameEN', None)
    # parcel_item.declared_price = item.get('declared_price', 1) or 1
    # parcel_item.item_qty = item.get('item_qty', 1) or 1
    # parcel_item.item_weight = item.get('item_weight', 0.05) or 0.05
    # parcel_item.texture = item.get('texture', None)
    # parcel_item.item_size = item.get('item_size', None)
    # parcel_item.use = item.get('use', None)
    # parcel_item.brand = item.get('brand', None)
    # parcel_item.model = item.get('model', None)
    # parcel_item.customs_code = item.get('customs_code', None)
    # parcel_item.save()


def save_customer_order_charge_in(customer_order, customer_order_charge_in_list_data, user):
    restrict_charge_item(customer_order_charge_in_list_data, '收入')
    for customer_order_charge_in_data in customer_order_charge_in_list_data:
        customer_order_charge_in_data.pop('customer_order_num', None)
        customer_order_charge_in = ParcelOrderChargeIn.objects.create(customer_order_num=customer_order,
                                                                      **customer_order_charge_in_data)
        customer_order_charge_in.charge_total = (customer_order_charge_in.charge_rate or 0) * (
                customer_order_charge_in.charge_count or 0)

        # 通用获取汇率
        current_exchange = get_currency_rate(customer_order_charge_in.currency_type,
                                             [customer_order_charge_in.currency_type])
        # 通过汇率计算记账金额
        customer_order_charge_in.account_charge = customer_order_charge_in.charge_total * current_exchange
        customer_order_charge_in.current_exchange = current_exchange

        if not customer_order_charge_in.customer:
            customer_order_charge_in.customer = customer_order.customer or None
        customer_order_charge_in.create_by = user
        customer_order_charge_in.update_by = user
        customer_order_charge_in.create_date = datetime.now()
        customer_order_charge_in.update_date = datetime.now()
        customer_order_charge_in.save()


def save_customer_order_charge_out(customer_order, customer_order_charge_out_list_data, user):
    restrict_charge_item(customer_order_charge_out_list_data, '成本')
    for customer_order_charge_out_data in customer_order_charge_out_list_data:
        customer_order_charge_out_data.pop('customer_order_num', None)
        customer_order_charge_out = ParcelOrderChargeOut.objects.create(customer_order_num=customer_order,
                                                                        **customer_order_charge_out_data)
        customer_order_charge_out.charge_total = (customer_order_charge_out.charge_rate or 0) * (
                customer_order_charge_out.charge_count or 0)

        # 通用获取汇率
        current_exchange = get_currency_rate(customer_order_charge_out.currency_type,
                                             [customer_order_charge_out.currency_type])
        # 通过汇率计算记账金额
        customer_order_charge_out.account_charge = customer_order_charge_out.charge_total * current_exchange
        customer_order_charge_out.current_exchange = current_exchange
        customer_order_charge_out.create_by = user
        customer_order_charge_out.update_by = user
        customer_order_charge_out.create_date = datetime.now()
        customer_order_charge_out.update_date = datetime.now()
        customer_order_charge_out.save()


# 组装小包扩展信息
def assemble_order_extends(customer_order, parcel_order_extends, tax_info, request):
    # 拓展属性；是否下单即确认等
    order_extend_objs = ParcelOrderExtend.objects.filter(customer_order=customer_order, del_flag=False)
    if order_extend_objs.exists():
        order_extend_objs.update(**parcel_order_extends, **get_update_params(request))
        order_extend_obj = order_extend_objs.last()
    else:
        order_extend_obj = ParcelOrderExtend.objects.create(customer_order=customer_order, **parcel_order_extends)

    # 税号
    # tax_info = order_dist.get('taxInfo', {})
    if tax_info and settings.SYSTEM_VERSION == 'V2':
        tax_type = tax_info.get('tax_type', None)
        if tax_type:
            order_extend_obj.tax_type = str(tax_type).upper()
        order_extend_obj.tax_no = tax_info.get('tax_no')
        order_extend_obj.tax_pay_mode = tax_info.get('tax_pay_mode')
        order_extend_obj.tax_company = tax_info.get('tax_company')
        order_extend_obj.tax_country = tax_info.get('tax_country')
        order_extend_obj.tax_address = tax_info.get('tax_address')
        order_extend_obj.save()
        # 兼容旧的税号
        if tax_type == 'IOSS':
            customer_order.ioss_num = tax_info.get('tax_no')
            customer_order.save(update_fields=['ioss_num'])
    else:
        # 兼容新版税号
        order_extend_obj.tax_type = 'IOSS'
        order_extend_obj.tax_no = customer_order.ioss_num
        order_extend_obj.save()
    return order_extend_obj


def assemble_recipient_info(customer_order,recipient_info, request):
    # 收件人地址
    # recipient_info = request.data.get('recipientInfo', {})
    if recipient_info and settings.SYSTEM_VERSION == 'V2':

        rc_address_obj_queryset = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='RC',
                                                                    del_flag=False)
        if rc_address_obj_queryset.exists():
            recipient_info.pop('update_by', None)
            recipient_info.pop('update_date', None)
            rc_address_obj_queryset.update(**recipient_info, **get_update_params(request))
            rc_address_obj = rc_address_obj_queryset.first()
        else:
            recipient_info.pop('update_by', None)
            recipient_info.pop('update_date', None)
            rc_address_obj = ParcelOrderAddress.create_address(customer_order=customer_order, address_type='RC',
                                                               **recipient_info)

        # 兼容旧版收件人地址
        customer_order.buyer_name = rc_address_obj.contact_name
        customer_order.buyer_company_name = rc_address_obj.company_name
        customer_order.buyer_country = rc_address_obj.country_code
        customer_order.buyer_country_code = rc_address_obj.country_code
        customer_order.buyer_state = rc_address_obj.state_code
        customer_order.buyer_city = rc_address_obj.city_code
        customer_order.buyer_address_one = rc_address_obj.address_one
        customer_order.buyer_address_two = rc_address_obj.address_two
        customer_order.buyer_house_num = rc_address_obj.house_no
        customer_order.buyer_postcode = rc_address_obj.postcode
        customer_order.buyer_mail = rc_address_obj.contact_email
        customer_order.buyer_phone = rc_address_obj.contact_phone
        customer_order.save()
    else:
        # 兼容旧版本API
        recipient_info = {
            'contact_name': customer_order.buyer_name,
            'company_name': customer_order.buyer_company_name,
            'country_code': customer_order.buyer_country,
            'state_code': customer_order.buyer_state,
            'city_code': customer_order.buyer_city,
            'address_one': customer_order.buyer_address_one,
            'address_two': customer_order.buyer_address_two,
            'house_no': customer_order.buyer_house_num,
            'postcode': customer_order.buyer_postcode,
            'contact_email': customer_order.buyer_mail,
            'contact_phone': customer_order.buyer_phone,
        }
        rc_address_obj_queryset = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='RC',
                                                                    del_flag=False)
        if rc_address_obj_queryset.exists():
            rc_address_obj_queryset.update(**recipient_info, **get_update_params(request))
            rc_address_obj = rc_address_obj_queryset.first()
        else:
            rc_address_obj = ParcelOrderAddress.create_address(customer_order=customer_order, address_type='RC',
                                                               **recipient_info)

    return rc_address_obj


def assemble_address_info(customer_order, request, request_fields, address_type):
    address_info = request.data.get(request_fields, {})
    if address_info:
        ParcelOrderAddress.create_address(customer_order=customer_order, address_type=address_type, **address_info)


# 封装发件地址
def assemble_shipper_info(customer_order, warehouse, request):
    shipper_info_data = {
        'address_num': warehouse.address_num,
        'contact_name': warehouse.contact_name,
        'contact_phone': warehouse.contact_phone,
        'contact_email': warehouse.contact_email,
        'country_code': warehouse.country_code,
        'company_name': warehouse.company_name,
        'state_code': warehouse.state_code,
        'city_code': warehouse.city_code,
        'address_one': warehouse.address_one,
        'address_two': warehouse.address_two,
        'house_no': warehouse.house_no,
        'postcode': warehouse.postcode
    }

    shipper_address_queryset = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='SP',
                                                                 del_flag=False)
    if shipper_address_queryset.exists():
        shipper_address_queryset.update(**shipper_info_data, **get_update_params(request))
    else:
        ParcelOrderAddress.create_address(customer_order=customer_order, address_type='SP', **shipper_info_data)


# 封装旧系统发货地址信息
def assemble_old_shipper_info(customer_order, shipper_info, request):
    shipper_info.pop('update_by', None)
    shipper_info.pop('update_date', None)
    shipper_info.pop('address_name', None)
    sp_address_queryset = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='SP',
                                                            del_flag=False)
    if sp_address_queryset.exists():
        sp_address_queryset.update(**shipper_info, **get_update_params(request))
        sp_address_obj = sp_address_queryset.first()
    else:
        sp_address_obj = ParcelOrderAddress.create_address(customer_order=customer_order,
                                                           address_type='SP',
                                                           **shipper_info)

    save_customer_order_shipper_address(customer_order, sp_address_obj)


def save_customer_order_shipper_address(customer_order, sp_address_obj):
    customer_order.contact_name = sp_address_obj.contact_name
    customer_order.contact_email = sp_address_obj.contact_email
    customer_order.contact_phone = sp_address_obj.contact_phone
    customer_order.country_code = sp_address_obj.country_code
    customer_order.state_code = sp_address_obj.state_code
    customer_order.city_code = sp_address_obj.city_code
    customer_order.postcode = sp_address_obj.postcode
    customer_order.house_no = sp_address_obj.house_no
    customer_order.address_one = sp_address_obj.address_one
    customer_order.address_two = sp_address_obj.address_two
    customer_order.company_name = sp_address_obj.company_name
    customer_order.save()


def handler_shipper_info(customer, customer_order, product, request, warehouse, shipper_info):
    address_rule = AddressRule.objects.filter(
        product=product,
        customer=customer,
        address_type='SD',
        del_flag=False
    ).first()
    # 如果没有找到特定客户的地址规则，则查找通用地址规则
    if not address_rule:
        address_rule = AddressRule.objects.filter(
            product=product,
            is_general=True,
            address_type='SD',
            del_flag=False
        ).first()
    if address_rule:
        warehouse = address_rule.address

    if not warehouse:
        # 发件人地址
        # shipper_info = request.data.get('shipperInfo', {})
        if shipper_info:
            assemble_old_shipper_info(customer_order, shipper_info, request)
    else:
        save_customer_order_shipper_address(customer_order, warehouse)
        assemble_shipper_info(customer_order, warehouse, request)


# 封装境外地址
def handler_return_oversea_address_info(customer, customer_order, product):
    # 判断客户所属
    return_address_rule = AddressRule.objects.filter(
        product=product,
        customer=customer,
        address_type='OV',
        del_flag=False
    ).first()

    if not return_address_rule:
        # 判断是否有通用
        return_address_rule = AddressRule.objects.filter(
            product=product,
            is_general=True,
            address_type='OV',
            del_flag=False
        ).first()

    if not return_address_rule or not return_address_rule.address:
        return

    ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='OV', del_flag=False). \
        update(del_flag=True)

    address = return_address_rule.address
    address_dict = model_to_dict(address)
    address_dict.pop('id', None)
    address_dict.pop('create_by', None)
    address_dict.pop('update_by', None)
    address_dict['create_date'] = datetime.now()
    parcel_order_address_fields = [field.name for field in ParcelOrderAddress._meta.fields]
    return_address_info = {key: value for key, value in address_dict.items() if
                           key in parcel_order_address_fields}
    ParcelOrderAddress.create_address(customer_order=customer_order, **return_address_info)


# 验证处理收货规则
def check_receiving_rule(buyer_address, product_obj):
    receiving_rule_queryset = ReceivingRule.objects.filter(product=product_obj, del_flag=False)
    # 验证收货规则（国家约束）
    country_receiving_rule_queryset = receiving_rule_queryset.filter(type='C', country_code=buyer_address.country_code)
    for country_receiving_rule in country_receiving_rule_queryset:
        if country_receiving_rule.constraint_rules:
            pass
            # raise ParamError(f'检查国家限制', ErrorCode.PARAM_ERROR)
    # 验证收货规则（通用约束）
    receiving_rule_queryset = receiving_rule_queryset.filter(type='R')
    for receiving_rule in receiving_rule_queryset:
        if receiving_rule.constraint_rules:
            pass
            # raise ParamError(f'检查通用约束', ErrorCode.PARAM_ERROR)
    # 验证收货规则（附加费约束）？
    pass


# 检查资源
def check_service_receiving_rule(buyer_address, service):
    receiving_rule_queryset = ServiceReceivingRule.objects.filter(service=service, del_flag=False)
    # 验证收货规则（国家约束）
    country_receiving_rule_queryset = receiving_rule_queryset.filter(type='C', country_code=buyer_address.country_code)
    for country_receiving_rule in country_receiving_rule_queryset:
        if country_receiving_rule.constraint_rules:
            pass
            # raise ParamError(f'检查国家限制', ErrorCode.PARAM_ERROR)
    # 验证收货规则（通用约束）
    receiving_rule_queryset = receiving_rule_queryset.filter(type='R')
    for receiving_rule in receiving_rule_queryset:
        if receiving_rule.constraint_rules:
            pass
            # raise ParamError(f'检查通用约束', ErrorCode.PARAM_ERROR)
    # 验证收货规则（附加费约束）？
    pass


# 获取线路和渠道资源
def get_product_line_and_service(customer_order, product, buyer_address):
    product_line, zone = get_product_line_and_zone(buyer_address, customer_order, product)

    service = product_line.delivery_service

    return product_line, service, zone


def get_product_line_and_zone(buyer_address, customer_order, product):
    print(f'buyer_address =>{buyer_address}')
    # 找路由
    product_route_queryset = ProductRoute.objects.filter(product=product, status='ON', del_flag=False)
    # 找路由明细
    product_route_detail_queryset = ProductRouteDetail.objects.filter(product_route__in=product_route_queryset,
                                                                      del_flag=False)
    if not product_route_queryset.exists():
        raise ParamError(f'未配置路由，请联系客服', ErrorCode.PARAM_ERROR)
    parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=customer_order, del_flag=False)
    # total_parcel_weight = sum(float(parcel.parcel_weight) for parcel in parcel_queryset)
    parcel = parcel_queryset.first()
    total_parcel_weight = parcel.parcel_weight
    parcel_item_queryset = ParcelOrderItem.objects.filter(parcel_num__in=parcel_queryset, del_flag=False)
    total_price = sum(float(parcel_item.declared_price or 0) for parcel_item in parcel_item_queryset)
    product_line = None
    zone = None
    for product_route_detail in product_route_detail_queryset:

        logger.info(f'product_route_detail=>{product_route_detail}')

        if product_route_detail.weight_among:
            weight_among_arr = str(product_route_detail.weight_among).split('~')
            logger.info(f'total_parcel_weight:{total_parcel_weight}, weight_among_arr={weight_among_arr}')
            if float(total_parcel_weight) < float(weight_among_arr[0]) or float(
                    total_parcel_weight) > float(weight_among_arr[1]):
                continue

        if product_route_detail.parcel_size_among:
            parce_size_among_arr = str(product_route_detail.parcel_size_among).split('~')
            start_size_arr = str(parce_size_among_arr[0]).split('*')
            end_size_arr = str(parce_size_among_arr[1]).split('*')

            logger.info(
                f'parcel_size_among:{product_route_detail.parcel_size_among}, parcel_length={parcel.parcel_length}, parcel_weight={parcel.parcel_weight} , parcel_height={parcel.parcel_height}')
            if float(parcel.parcel_length) < float(start_size_arr[0]) or float(parcel.parcel_length) > float(
                    end_size_arr[0]) or float(parcel.parcel_weight) < float(start_size_arr[1]) or float(
                parcel.parcel_weight) > float(end_size_arr[1]) or float(parcel.parcel_height) < float(
                start_size_arr[2]) or float(parcel.parcel_height) > float(end_size_arr[2]):
                continue

        if product_route_detail.price_among:
            price_among_arr = str(product_route_detail.price_among).split('~')
            logger.info(f'total_price:{total_price}, price_among_arr={price_among_arr}')
            if float(total_price) < float(price_among_arr[0]) or float(
                    total_price) > float(price_among_arr[1]):
                continue

        end_dist_zone = get_zone_code_v2(customer_order, buyer_address.country_code, buyer_address.postcode,
                                         product_route_detail.post_code_name)
        if not end_dist_zone:
            continue

        product_line = product_route_detail.line
        zone = product_route_detail.post_code_name
    if not product_line:
        raise ParamError(f'收件人国家/邮编无服务,请联系客服', ErrorCode.PARAM_ERROR)
    return product_line, zone


# 验证订单产品
def check_order_data_by_product(order_data, product, buyer_address, parcel_data, parcel_item_data, address_data):
    """
    address_data: {'buyer_address_data': {}, 'sender_address_data': {}}
    """

    # 打印出来address_data.get('returninfo')
    logger.info(f'address_data.get("returninfo")=>{address_data.get("returninfo")}')
    check_receiving_rule(buyer_address, product)
    # 订单
    basic_restriction_queryset = ProductBasicRestriction.objects.filter(product=product,
                                                                        belong_type='A',
                                                                        type__in=['A', 'B'], del_flag=False)
    for basic_restriction in basic_restriction_queryset:
        check_data_v2(basic_restriction, order_data)

    # 收件人地址
    basic_restriction_queryset = ProductBasicRestriction.objects.filter(product=product,
                                                                        belong_type='C',
                                                                        type__in=['A', 'B'], del_flag=False)
    for basic_restriction in basic_restriction_queryset:
        check_data_v2(basic_restriction, address_data['buyer_address_data'])

    # 发件人地址
    basic_restriction_queryset = ProductBasicRestriction.objects.filter(product=product,
                                                                        belong_type='D',
                                                                        type__in=['A', 'B'], del_flag=False)
    for basic_restriction in basic_restriction_queryset:
        check_data_v2(basic_restriction, address_data['sender_address_data'])

    # 海外退件地址
    if address_data.get('returninfo'):
        basic_restriction_queryset = ProductBasicRestriction.objects.filter(product=product,
                                                                            belong_type='F',
                                                                            type__in=['A', 'B'], del_flag=False)
        for basic_restriction in basic_restriction_queryset:
            check_data_v2(basic_restriction, address_data['returninfo'])

    # 包裹
    if parcel_data:
        basic_restriction_queryset = ProductBasicRestriction.objects.filter(product=product, belong_type='B',
                                                                            type__in=['A', 'B'], del_flag=False)

        for basic_restriction in basic_restriction_queryset:
            for prrcel in parcel_data:
                check_data_v2(basic_restriction, prrcel)
    # 商品
    if parcel_item_data:
        basic_restriction_queryset = ProductBasicRestriction.objects.filter(product=product, belong_type='E',
                                                                            type__in=['A', 'B'], del_flag=False)

        for basic_restriction in basic_restriction_queryset:
            for prrcel_item in parcel_item_data:
                check_data_v2(basic_restriction, prrcel_item)


# 验证订单资源
def check_order_data_by_service(order_data, service, buyer_address, parcel_data, parcel_item_data, address_data):
    """
    address_data: {'buyer_address_data': {}, 'sender_address_data': {}}
    """
    # 验证收货规则
    check_service_receiving_rule(buyer_address, service)

    # 订单
    basic_restriction_queryset = ServiceBasicRestriction.objects.filter(service=service, belong_type='A',
                                                                        type__in=['A', 'B'], del_flag=False)
    for basic_restriction in basic_restriction_queryset:
        check_data_v2(basic_restriction, order_data)

    # 收件人
    basic_restriction_queryset = ServiceBasicRestriction.objects.filter(service=service, belong_type='C',
                                                                        type__in=['A', 'B'], del_flag=False)
    for basic_restriction in basic_restriction_queryset:
        check_data_v2(basic_restriction, address_data['buyer_address_data'])

    # 发件人
    basic_restriction_queryset = ServiceBasicRestriction.objects.filter(service=service, belong_type='D',
                                                                        type__in=['A', 'B'], del_flag=False)
    for basic_restriction in basic_restriction_queryset:
        check_data_v2(basic_restriction, address_data['sender_address_data'])

    # 包裹
    if parcel_data:
        basic_restriction_queryset = ServiceBasicRestriction.objects.filter(service=service, belong_type='B',
                                                                            type__in=['A', 'B'], del_flag=False)

        for basic_restriction in basic_restriction_queryset:
            for prrcel in parcel_data:
                check_data_v2(basic_restriction, prrcel)
    # 商品
    if parcel_item_data:
        basic_restriction_queryset = ServiceBasicRestriction.objects.filter(service=service, belong_type='E',
                                                                            type__in=['A', 'B'], del_flag=False)

        for basic_restriction in basic_restriction_queryset:
            for prrcel_item in parcel_item_data:
                check_data_v2(basic_restriction, prrcel_item)


# 设置订单默认值
def set_order_data_default_value(customer_order, service):
    # 订单
    service_default_value_queryset = ServiceDefaultValue.objects.filter(service=service, belong_type='A',
                                                                        type__in=['A', 'B'], del_flag=False)

    # 使用setattr更新model实例的属性
    for service_default_value in service_default_value_queryset:
        setattr(customer_order, service_default_value.encoding, service_default_value.default_value)

    # 保存更改
    customer_order.save()


# 组装包裹信息
def assemble_parcel_item(customer_order, parcel_data, parcel_item_data, classification=None):
    logger.info(f'debug xxxxxxxxxxx   单号：{customer_order} 包裹数据：{parcel_data} 包裹item数据：{parcel_item_data}')
    # 合并没有传包裹号的包裹
    default_parcel_num = None
    if parcel_item_data:
        idx = 1
        for item in parcel_item_data:
            if not item.get('classification') and classification:
                item['classification'] = classification

            if not item.get('parcel_num') and settings.SYSTEM_ORDER_MARK + settings.PARCEL_ORDER_MARK in ['HGE']:
                if not default_parcel_num:
                    item['parcel_num'] = generate_order_num_commom(customer_order.order_num + '-', idx, 3)
                    idx = idx + 1
                    default_parcel_num = item['parcel_num']
                else:
                    item['parcel_num'] = default_parcel_num

            elif not item.get('parcel_num'):
                item['parcel_num'] = f'P{now_to_timestamp(17)}'

            # 判断是否已经存在了这个包裹
            if ParcelOrderParcel.objects.filter(customer_order=customer_order, del_flag=False,
                                                parcel_num=item['parcel_num']).count() == 0:
                # 生成新的包裹
                parcel = save_parcel_order_parcel(customer_order, item)
            else:
                parcel = ParcelOrderParcel.objects.get(customer_order=customer_order, del_flag=False,
                                                       parcel_num=item['parcel_num'])

            # 生成包裹里面的商品
            save_parcel_item(item, parcel)
    else:
        if parcel_data:
            # 创建包裹和包裹明细
            idx = 1
            for parcel_item_data in parcel_data:

                if not parcel_item_data.get(
                        'parcel_num') and settings.SYSTEM_ORDER_MARK + settings.PARCEL_ORDER_MARK in ['HGE']:
                    parcel_item_data['parcel_num'] = generate_order_num_commom(customer_order.order_num + '-', idx, 3)
                    idx = idx + 1
                elif not parcel_item_data.get('parcel_num'):
                    parcel_item_data['parcel_num'] = f'P{now_to_timestamp(17)}'

                # 判断是否已经存在了这个包裹
                if ParcelOrderParcel.objects.filter(customer_order=customer_order, del_flag=False,
                                                    parcel_num=parcel_item_data['parcel_num']).count() == 0:
                    # 生成新的包裹
                    parcel = save_parcel_order_parcel(customer_order, parcel_item_data)
                else:
                    parcel = ParcelOrderParcel.objects.get(customer_order=customer_order, del_flag=False,
                                                           parcel_num=parcel_item_data['parcel_num'])

                if parcel_item_data.get("parcelItem", None) is not None:
                    for item in parcel_item_data["parcelItem"]:
                        # 生成包裹里面的商品
                        save_parcel_item(item, parcel)


# 保存包裹商品
def save_parcel_item(item, parcel):
    declare_parce = item.get('declared_price', 1)
    if not declare_parce:
        declare_parce = 1

    declared_currency = item.get('declared_currency', 'USD')
    if not declared_currency:
        declared_currency = 'USD'

    parcel_item = ParcelOrderItem()
    parcel_item.parcel_num = parcel
    parcel_item.item_code = item.get('item_code', None)
    parcel_item.declared_nameCN = item.get('declared_nameCN', None)
    parcel_item.declared_nameEN = item.get('declared_nameEN', None)
    parcel_item.item_en_desc = item.get('item_en_desc', parcel_item.declared_nameEN)
    parcel_item.item_desc = item.get('item_desc', parcel_item.declared_nameCN)

    parcel_item.declared_currency = declared_currency
    parcel_item.declared_price = declare_parce

    parcel_item.item_qty = item.get('item_qty', 1) or 1
    parcel_item.item_weight = item.get('item_weight', 0.01) or 0.01
    parcel_item.net_weight = parcel_item.item_weight
    parcel_item.texture = item.get('texture', None)
    parcel_item.item_size = item.get('item_size', None)
    parcel_item.use = item.get('use', None)
    parcel_item.brand = item.get('brand', None)
    parcel_item.model = item.get('model', None)
    parcel_item.customs_code = item.get('customs_code', None)
    parcel_item.sku_url = item.get('sku_url', None)
    parcel_item.distribution_remark = item.get('distribution_remark', None)
    parcel_item.origin_country = item.get('origin_country', None)
    parcel_item.in_HScode = item.get('in_HScode', None)
    parcel_item.out_HScode = item.get('out_HScode', None)
    parcel_item.weight_unit = item.get('weight_unit') or "kg"
    parcel_item.size_unit = item.get('size_unit') or "cm"

    if item.get('out_declared_price', None):
        parcel_item.out_declared_price = item.get('out_declared_price')
    if item.get('out_declared_currency', None):
        parcel_item.out_declared_currency = item.get('out_declared_currency')
    parcel_item.save()


def save_parcel_order_parcel(customer_order, item):
    parcel = ParcelOrderParcel()
    parcel.parcel_num = item['parcel_num']
    length = item['parcel_length'] or 0
    width = item['parcel_width'] or 0
    height = item['parcel_height'] or 0

    parcel_size_unit = item.get('parcel_size_unit') or 'cm'

    # 长宽高重排
    size_arr = [Decimal(length), Decimal(width), Decimal(height)]
    size_arr.sort(reverse=True)
    length = size_arr[0]
    width = size_arr[1]
    height = size_arr[2]
    parcel.parcel_length = length
    parcel.parcel_width = width
    parcel.parcel_height = height

    parcel.parcel_size_unit = parcel_size_unit

    parcel_weight_unit = item.get('parcel_weight_unit') or 'kg'
    parcel.parcel_weight_unit = parcel_weight_unit
    parcel.parcel_weight = item['parcel_weight'] or 0
    parcel.is_electronic = item.get('is_electronic', 0)
    parcel.classification = item.get('classification', '0')
    label_weight = item.get('label_weight', 0)
    if float(label_weight) == 0:
        parcel.label_weight = optimize_weight(parcel.parcel_weight, customer_order.product)
    else:
        parcel.label_weight = label_weight

    parcel.parcel_qty = 1
    parcel.remark = item.get('remark', None)
    if (item['parcel_width'] or 0) != 0 and (item['parcel_length'] or 0) != 0 and (
            item['parcel_height'] or 0) != 0:
        # 如果单位是inch，则直接计算
        if parcel_size_unit and parcel_size_unit.upper() == 'INCH':
            parcel.parcel_volume = Decimal(item['parcel_length']) * \
                                   Decimal(item['parcel_width']) * \
                                   Decimal(item['parcel_height'])
        else:
            parcel.parcel_volume = Decimal(item['parcel_length']) * \
                                   Decimal(item['parcel_width']) * \
                                   Decimal(item['parcel_height']) / 1000000

    else:
        parcel.parcel_volume = 0

    # 货物属性：是否带电等
    if item.get('classification'):
        parcel.classification = item['classification']
        # 带电
        if item['classification'] == '1':
            parcel.is_electronic = True
    # 包裹类型
    if item.get('parcel_type'):
        parcel.parcel_type = item['parcel_type']
    parcel.customer_order = customer_order
    parcel.save()
    return parcel


def get_label_deal(request, parcel_customer_orders):
    '''
    处理面单接口
    :param request:
    :param parcel_customer_orders:
    :return:
    '''
    # 初始化PDF合并器和最终文件路径
    pdf_merger = PdfFileMerger()
    final_file_url = None
    single_file_mode = False  # 标记是否为单文件模式
    finePath = settings.STATIC_MEDIA_DIR
    fileUrl = 'http://' + settings.DOMAIN_URL + '/media/'

    for parcel_customer_order in parcel_customer_orders:
        product = parcel_customer_order.product
        order_num = str(parcel_customer_order.id)  # 当前订单号
        logger.info(f'处理订单 {order_num}, 产品类型: {product.label_type}')

        if product.is_virtual and product.strategy_type in ['MULTI_CHANNEL', 'MC_COST_LOW']:
            sub_product_list = get_sub_product_list(parcel_customer_order, product)
            orderLabelList = ParcelOrderLabel.objects.filter(
                order_num=order_num,  # 只查询当前订单的面单
                product__in=sub_product_list,
                del_flag=False
            )

        elif product.label_type in ['WC', 'WA'] or parcel_customer_order.is_weighing:
            orderLabelList = ParcelOrderLabel.objects.filter(
                ~Q(product__code='ZX_LABEL'),
                order_num=order_num,  # 只查询当前订单的面单
                del_flag=False
            ).order_by('sort_code', 'id')

        elif product.label_type == 'YW' and not parcel_customer_order.is_weighing:
            orderLabelList = ParcelOrderLabel.objects.filter(
                order_num=order_num,  # 只查询当前订单的面单
                del_flag=False
            )
            if parcel_customer_order.tracking_num and parcel_customer_order.tracking_num != parcel_customer_order.label_billid:
                orderLabelList = orderLabelList.filter(is_secondary=True)

        elif product.label_type == 'HW' and not parcel_customer_order.is_weighing:
            hlyz_product = Product.objects.filter(code='HLYZ', del_flag=False).first()
            orderLabelList = ParcelOrderLabel.objects.filter(
                order_num=order_num,  # 只查询当前订单的面单
                product=hlyz_product,
                del_flag=False
            )
        elif product.label_type in ['FD', 'DD']:
            orderLabelList = ParcelOrderLabel.objects.filter(order_num=order_num, del_flag=False)
        else:
            raise ParamError(f'面单类型{product.label_type}不存在或未称重', ErrorCode.PARAM_ERROR)

        # 处理当前订单的面单
        if len(orderLabelList) == 0:
            raise ParamError(f'订单{order_num}查无面单', ErrorCode.PARAM_ERROR)
        elif len(orderLabelList) == 1:
            orderLabel = orderLabelList[0]
            if len(parcel_customer_orders) == 1:  # 只有一个订单时直接返回
                final_file_url = fileUrl + orderLabel.label_url
                single_file_mode = True
            else:
                pdf_merger.append(settings.STATIC_MEDIA_DIR + orderLabel.label_url)
        else:
            # 多个面单合并为一个PDF
            for orderLabel in orderLabelList:
                pdf_merger.append(settings.STATIC_MEDIA_DIR + orderLabel.label_url)

        # 更新下载次数
        ParcelCustomerOrder.objects.filter(id=parcel_customer_order.id).update(
            download_num=F('download_num') + 1,
            update_date=datetime.now()
        )

    # 处理最终输出
    if single_file_mode:
        pass  # 已经设置final_file_url
    elif len(pdf_merger.pages) > 0:
        # 生成合并后的文件名
        fineName = 'merged_' + datetime.now().strftime("%Y%m%d%H%M%S") + '.pdf'
        finePath += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
        final_file_url = fileUrl + 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName

        # 确保目录存在
        os.makedirs(os.path.dirname(finePath), exist_ok=True)

        # 写入合并后的PDF
        with open(finePath, 'wb') as f:
            pdf_merger.write(f)
        pdf_merger.close()

    request.data['data'] = final_file_url
    return success_response(request, '')


def handle_order_cancellation(order_label_task, customer_order, order_num, user):
    """
    处理订单取消的逻辑 V1
    Args:
        order_label_task: 订单标签任务对象
        customer_order: 客户订单对象
        order_num: 订单号
        user: 用户对象
    Returns:
        tuple: (success_flag, message, cancel_flag)
        - success_flag: 是否成功
        - message: 成功或失败消息
        - cancel_flag: 取消标志
    """
    # 兼容无服务的订单
    product = order_label_task.product
    service = Service.objects.filter(product=product.id, del_flag=False).first()
    if not service:
        logger.info(f'订单号{order_num}产品[{product.code}]未配置服务')
        return False, f'订单号{order_num}产品[{product.code}]未配置服务', False

    # 如果是虚拟产品和成本最低的，用真实产品
    if product.is_virtual:
        product = customer_order.real_product
        service = Service.objects.filter(product=product, del_flag=False).first()

    if not service or not service.butt_code:
        logger.info(f'订单号{order_num}的产品[{product.code}]不支持取消')
        return False, f'订单号{order_num}的产品[{product.code}]不支持取消', False

    supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
    if not supplier_butt.is_support_cancel:
        #  产品配置了 允许取消中，供应商配置了不允许取消，把单的状态变成作废中
        product_basic_restriction = ProductBasicRestriction.objects.filter(
            encoding='is_allowed_nullification',
            belong_type='A',
            type='C',
            product=customer_order.product,
            del_flag=False
        ).last()
        if product_basic_restriction:
            if product_basic_restriction.insert_trick:
                customer_order.order_status = 'BC'
                customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                customer_order.remark = '作废中'
                customer_order.save()
                return False, None, False
        logger.info(f'订单号{order_num}的产品[{product.code}]不支持取消')
        return False, f'订单号{order_num}的产品[{product.code}]不支持取消', False

    supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id,
                                                               del_flag=False)
    supplier_account = supplier_account_list.first()
    class_name = supplier_butt.class_name
    label_order_vo = LabelOrderVo()
    label_order_vo.orderLabelTask = order_label_task
    label_order_vo.supplierAccount = supplier_account
    label_order_vo.customerOrder=customer_order
    label_order_vo.service = service
    label_order_vo.product = product
    # 通过反射实例化对象
    obj = globals()[class_name]()
    result = cancel_label(obj, label_order_vo)
    
    if result['code'] == '0':
        order_label_task.status = 'VO'
        order_label_task.label_desc = '面单已作废'
        order_label_task.update_by = user
        order_label_task.update_date = datetime.now()
        order_label_task.save()

        ParcelOrderLabel.objects.filter(order_num=customer_order, del_flag=False) \
            .update(del_flag=True, update_by=user, update_date=datetime.now())
        return True, order_num, True
    elif result['code'] == '400':
        logger.info(f'订单{order_num}作废失败{str(result["msg"])}')
        return False, f'订单{order_num}作废失败{str(result["msg"])}', False
    else:
        logger.info(f'订单{order_num}作废失败')
        return False, f'订单{order_num}作废失败', False


def handle_order_cancellation_v2(order_label_task, customer_order, order_num, user):
    """
    处理订单取消的逻辑 V2
    Args:
        order_label_task: 订单标签任务对象
        customer_order: 客户订单对象
        order_num: 订单号
        user: 用户对象
    Returns:
        tuple: (success_flag, message, cancel_flag)
        - success_flag: 是否成功
        - message: 成功或失败消息
        - cancel_flag: 取消标志
    """

    service = order_label_task.service
    api_setting_queryset = ServiceApiSetting.objects.filter(service=service, del_flag=False)
    logger.info(f'api_setting_queryset: {api_setting_queryset.values()}')
    settings_dict = {item.field_name: item.field_value for item in api_setting_queryset}
    class_name = service.butt_code.class_name
    class_name = upper_first(class_name)

    label_order_vo = LabelOrderVo()
    label_order_vo.orderLabelTask = order_label_task
    label_order_vo.customerOrder = customer_order
    label_order_vo.product = customer_order.product
    label_order_vo.service = customer_order.service
    label_order_vo.supplier_butt = ''
    label_order_vo.is_unpack = False

    label_order_vo.service_dict = settings_dict

    logger.info("className =" + str(class_name))
    # 通过反射实例化对象
    obj = globals()[class_name]()
    result = cancel_label(obj, label_order_vo)

    if result['code'] == '0':
        order_label_task.status = 'VO'
        order_label_task.label_desc = '面单已作废'
        order_label_task.update_by = user
        order_label_task.update_date = datetime.now()
        order_label_task.save()

        ParcelOrderLabel.objects.filter(order_num=customer_order, del_flag=False) \
            .update(del_flag=True, update_by=user, update_date=datetime.now())
        return True, order_num, True
    elif result['code'] == '400':
        return False, f'订单{order_num}作废失败{str(result["msg"])}', False
    else:
        return False, f'订单{order_num}作废失败', False
