from datetime import datetime

from django.db import transaction
from django.db.models import Q

from alita.logger import logger
from order.integration.util.emsCnUtil import LadingBillInfo, ems_receiver_lading
from order.models import MasterOrder, CustomerOrder, ParcelOutboundOrder, BigParcel, ParcelCustomerOrder, \
    MasterOrderToEms, MasterOrderPushTask
from company.models import OceanPort


def get_org_map_by_code(code):
    map_data = {
        # '330100': ('31000902', '杭州国际(商业)集拼中心仓'),
        '330100': ('31120168', '杭州市商业渠道集拼仓'),
        '330200': ('31500033', '中国邮政集团有限公司宁波市国际公司直属跨请营业部'),
        # '330300': ('32500513', '中国邮政集团有限公司温州市国际公司鹿城营业部'),
        '330300': ('32500190', '中国邮政集团有限公司浙江省温州市国际业务分公司'),
        '330400': ('31401903', '嘉兴市国际公司直属秀洲营业部'),
        '330500': ('31302306', '中国邮政集团有限公司湖州市国际客户揽投部'),
        '330600': ('31206503', '中国邮政集团有限公司浙江省绍兴市国际分公司商业渠道集拼仓'),
        # '330700': ('32100066', '中国邮政集团有限公司金华市国际公司直属商企营业部'),
        '330700': ('32100163', '金华市国际业务分公司'),
        '330800': ('32400013', '国际客户直属跨境揽投部'),
        '331100': ('32300060', '中国邮政集团有限公司丽水市国际客户市本级揽投部'),
        '331000': ('31800013', '台州市国际客户揽投部'),
        '330900': ('31602163', '舟山市行业客户揽投部'),
        '330782': ('32200049', '中国邮政集团有限公司浙江省义乌市国际公司商业渠道集拼中心仓'),
    }
    return map_data.get(code, (None, None))


@transaction.atomic
def add_push_ems_data(master_order_num, user, date):

    if not date:
        date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    master_order_queryset = MasterOrder.objects.filter(order_num=master_order_num, del_flag=False)
    if not master_order_queryset.exists():
        return

    master_order = master_order_queryset.first()

    customer_order_queryset = CustomerOrder.objects.filter(master_num=master_order, del_flag=False)

    parcel_outbound_order_list = ParcelOutboundOrder.objects.filter(customer_order__in=customer_order_queryset,
                                                                    del_flag=False)
    if parcel_outbound_order_list.count() == 0:
        logger.info(f'master_order_num={master_order_num} 无出库单数据')
        return

    big_parcel_queryset = BigParcel.objects.filter(parcel_outbound_order__in=parcel_outbound_order_list,
                                                   del_flag=False)
    if big_parcel_queryset.count() == 0:
        logger.info(f'master_order_num={master_order_num} 无出大包单数据')
        return

    master_order_to_ems_arr = []
    for big_parcel in big_parcel_queryset:
        parcel_order_list = ParcelCustomerOrder.objects.filter(big_parcel=big_parcel,
                                                               del_flag=False).filter(
            Q(label_billid__isnull=False) & ~Q(label_billid='')
        )
        if not parcel_order_list.exists():
            continue
        for parcel_order in parcel_order_list:
            # weight = int(float(parcel_order.weighing_weight or 0.1) * 100)
            weight = str(parcel_order.weighing_weight)
            orgCode, orgName = get_org_map_by_code(parcel_order.subsidiary_organ_code or '330700')

            to_ems_queryset = MasterOrderToEms.objects.filter(mail_no=parcel_order.label_billid,
                                                              bag_id=big_parcel.parcel_num,
                                                              master_order=master_order,
                                                              is_push_finish=False, del_flag=False)
            if to_ems_queryset.exists():
                continue

            master_order_to_ems = {
                'mail_no': parcel_order.label_billid,
                'weight': weight,
                'dest_country': parcel_order.buyer_country_code,
                'envelop_time': date,
                'bag_id': big_parcel.parcel_num,
                'master_order': master_order.order_num,
                'airline_num': master_order.airline_num,
                'departure': master_order.departure,
                'destination': master_order.destination,
                'org_code': orgCode,
                'org_name': orgName,
                'source_type': 'order',
                'create_by': user,
                'create_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }
            master_order_to_ems_arr.append(master_order_to_ems)

    if not master_order_to_ems_arr:
        return

    for master_order_to_ems in master_order_to_ems_arr:
        MasterOrderToEms.objects.create(**master_order_to_ems)

    master_order_nums = list({item['master_order']: item for item in master_order_to_ems_arr}.values())
    print(master_order_nums)
    for master_order in master_order_nums:
        master_order_push_task_params = {
            'master_order_no': master_order['master_order'],
            'create_by': user,
            'create_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        }
        MasterOrderPushTask.objects.create(**master_order_push_task_params)


def handler_master_order_push_task(master_order_push_task):
    master_order_no = master_order_push_task.master_order_no

    master_order_to_ems_queryset = MasterOrderToEms.objects.filter(master_order=master_order_no,is_push_finish=False, del_flag=False)
    if not master_order_to_ems_queryset.exists():
        master_order_push_task.status = 'NOHandled'
        master_order_push_task.remark = '无数据'
        master_order_push_task.save()

    logger.info(f'handler_push_ems_receiver_lading {master_order_no}')

    mailbag_arr = []
    for master_order_to_tms in master_order_to_ems_queryset:
        bag_id = master_order_to_tms.bag_id
        target_bag = next((bag for bag in mailbag_arr if bag['bagId'] == bag_id), None)
        weight = int(float(master_order_to_tms.weight or 0.1) * 100)
        if not target_bag:
            mailbag = {
                'bagId': bag_id,
                'envelopTime': master_order_to_tms.envelop_time,
                'destCountry': master_order_to_tms.dest_country,
                'mail': [{
                    'mailNo': master_order_to_tms.mail_no,
                    'bubbleWeight': weight,
                    'postWeight': weight,
                    'destCountry': master_order_to_tms.dest_country
                }]
            }
            mailbag_arr.append(mailbag)
        else:
            mail_info = target_bag['mail']
            mail_info.append({
                'mailNo': master_order_to_tms.mail_no,
                'bubbleWeight': weight,
                'postWeight': weight,
                'destCountry': master_order_to_tms.dest_country
            })

    master_order = master_order_to_ems_queryset.first()
    ladingBillInfo = LadingBillInfo()
    ladingBillInfo.ladingBillNo = master_order.master_order
    ladingBillInfo.flightNumber = master_order.airline_num
    ladingBillInfo.departPortCode = master_order.departure
    ladingBillInfo.unloadPortCode = master_order.destination
    ladingBillInfo.clearCustType = '3'
    ladingBillInfo.bussnessType = '1'
    ladingBillInfo.systemType = '1'
    ladingBillInfo.orgCode = master_order.org_code
    ladingBillInfo.orgName = master_order.org_name
    ladingBillInfo.mailBag = mailbag_arr

    result = ems_receiver_lading(ladingBillInfo)
    master_order_to_ems_queryset.update(request_date=datetime.now())
    if 'message' in result.keys() and result['message'] == 'success':
        master_order_push_task.status = 'Success'
        master_order_push_task.remark = 'success'
        master_order_to_ems_queryset.update(is_push_finish=True, update_date=datetime.now(), remark='success')
    else:
        if 'errorMsg' in result.keys():
            master_order_push_task.remark = result['errorMsg']
        master_order_push_task.handle_times += 1
        master_order_to_ems_queryset.update(update_date=datetime.now(), remark=str(result['errorMsg']))

    master_order_push_task.save()


def assemble_barcode_params_for_master_order(master_order):
    ref_num = master_order.get("warehouse_receipt_number") or master_order.get("ref_num", "")
    order_num = master_order.get("order_num") or ""
    branch_order_num = ''
    carrier_code = master_order.get("carrier_code") or ""
    airline_num = master_order.get("airline_num") or ""
    departure = master_order.get("departure") or ""
    plan_leave_date = (
        master_order.get("plan_leave_date").strftime("%Y-%m-%d")
        if master_order.get("plan_leave_date")
        else ""
    )

    destination = master_order.get("destination", "")
    plan_arrivals_date = (
        master_order.get("plan_arrivals_date").strftime("%Y-%m-%d")
        if master_order.get("plan_arrivals_date")
        else ""
    )

    customer_carton = master_order.get("customer_carton") or ""
    customer_weight = master_order.get("customer_weight") or ""
    customer_volume = master_order.get("customer_volume") or ""

    return {
        "ref_num": ref_num,  # 入仓单号
        "order_num": order_num,  # 主单号
        "branch_order_num": branch_order_num,  # 分单号（暂时无该字段，默认为空）
        "carrier_code": carrier_code,  # 航空公司编码
        "airline_num": airline_num,  # 航班号
        "departure":departure,  # 起飞机场
        "plan_leave_date": plan_leave_date,  # 计划起飞时间
        "destination": destination,  # 落地机场
        "plan_arrivals_date": plan_arrivals_date,  # 计划落地时间
        "customer_carton": customer_carton,  # 提单件数
        "customer_weight": customer_weight,  # 提单毛重
        "customer_volume": customer_volume,  # 提单体积
        "create_date":datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }