import os
from collections import defaultdict
from datetime import datetime

from django.conf import settings
from django.db import transaction
from django.db.models import Q
from django.core.files.base import File, ContentFile

from alita.logger import logger
from common.common_const import ShopTypeEnum
from common.error import ParamError, ErrorCode
from common.tools import get_excel_cell, GetExcelCellData, create_sys_parcel_num, get_update_params_by_user, \
    gen_order_num, summary_predict_parcels_data, create_order_reference_id, save_address, change_order_status, \
    check_product_route
from company.models import Address, Company
from order.integration.schemas.customer_order_schema import CustomerOrderFbmExcelSchema, \
    CustomerOrderFbmParcelExcelSchema
from order.models import OcShipment, CustomerOrder, Parcel, ParcelItem, OrderAttachment
from pms.models import Product
from rbac.models import UserProfile


class FbaOrderImportService:
    """
    FBA订单导入公共处理方法(FBA订单异步导入)(异步导入FBA订单)
    """
    def __init__(self, table, user):
        self.table = table
        self.user = user

    # 获取客户
    def get_order_customer(self, check_permission=True):
        company = None
        is_cms = not self.user.is_staff
        if is_cms:
            company = self.user.company
        else:
            company_short_name = get_excel_cell(9, 'f', self.table)
            if company_short_name:
                company_short_name = str(company_short_name).strip()
                company = Company.objects.filter(short_name=company_short_name, is_customer=True,
                                                 del_flag=False).first()
            if company is None:
                raise ParamError(f'未查到客户编码 {company_short_name}', ErrorCode.PARAM_ERROR)

        if company and company.status == 'OFF':
            raise ParamError(f'客户: {company.name}已被停用', ErrorCode.PARAM_ERROR)

        # 权限检查
        if check_permission:
            user_profile = UserProfile.objects.filter(username=self.user.username).first()
            department = user_profile.department
            if department and department.relate_customer and company.id not in list(
                    map(int, department.relate_customer.split(','))):
                raise ParamError(f'没有权限导入: 用户的部门没有关联此订单客户 {company.name}', ErrorCode.PARAM_ERROR)
        return company

    # 获取发件人数据
    def get_order_shipper(self, product):
        postcode = get_excel_cell(8, 'b', self.table)
        shipper_name = get_excel_cell(6, 'f', self.table)

        if not shipper_name:
            raise ParamError(f'地址编码必填', ErrorCode.PARAM_ERROR)

        logger.info(f'地址编码-->{shipper_name}')
        logger.info(f"发件人-->{get_excel_cell(4, 'b', self.table)}")
        shipper_address = {}

        if shipper_name:
            address_queryset = Address.objects.filter(address_num=shipper_name, address_type='SP', del_flag=False)
            if not address_queryset.exists():
                raise ParamError(f'发件人地址编码不存在, 地址编码: {shipper_name}', ErrorCode.PARAM_ERROR)
            shipper_address = {
                # 发件人
                'shipper': address_queryset.first(),
                'contact_name': shipper_name,
                'company_name': get_excel_cell(5, 'b', self.table),
                'address_one': get_excel_cell(6, 'b', self.table),
                'city_code': get_excel_cell(7, 'b', self.table),
                'state_code': get_excel_cell(7, 'f', self.table),
                'postcode': postcode,
                'contact_phone': get_excel_cell(8, 'f', self.table),
                'country_code': 'CN'
            }
        # 如果没有发件人 默认用产品(现在不会走到这里, 暂不删)
        else:
            if product.address_num:
                address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
                if address_queryset:
                    seller_address = address_queryset.first()
                    shipper_address = {
                        # 发件人
                        'shipper': seller_address,
                        'contact_name': seller_address.contact_name,
                        'company_name': seller_address.company_name,
                        'address_one': seller_address.address_one,
                        'city_code': seller_address.city_code,
                        'state_code': seller_address.state_code,
                        'postcode': seller_address.postcode,
                        'contact_phone': seller_address.contact_phone,
                        'country_code': 'CN'
                    }
        if not shipper_address:
            raise ParamError(f'没有发件人信息, 地址编码: {shipper_name}', ErrorCode.PARAM_ERROR)
        return shipper_address

    # 获取收件人数据
    def get_order_receiver(self, warehouse_address, private_address):
        if not warehouse_address and not private_address:
            raise ParamError(f'仓库地址和私人地址都不存在', ErrorCode.PARAM_ERROR)

        # 仓库地址优先读取系统地址, 若系统地址不存在, 则读取表格地址保存到系统
        if warehouse_address:
            buyer_address_num = str(warehouse_address).strip()
            buyer_addresses = Address.objects.filter(address_num=warehouse_address, del_flag=False)
            if buyer_addresses:
                buyer_address = buyer_addresses.first()
            else:
                buyer_address = save_address(buyer_address_num, self.table)
            # receiver = buyer_address
        # 私人地址直接读取表格数据, 不读取系统地址
        else:
            # receiver = None
            buyer_address = save_address(private_address, self.table, is_warehouse_address=False)
        # print('buyer_address-->', buyer_address)
        return buyer_address

    # 创建订单
    def create_order(self, company, product, receiver, buyer_address, shipper_address, upload_task):
        ref_num = get_excel_cell(3, 'b', self.table)
        if CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=ref_num, del_flag=False).exists():
            raise ParamError(f'客户订单号不能重复： {ref_num}', ErrorCode.PARAM_ERROR)

        is_customs_declaration_str = get_excel_cell(9, 'i', self.table)
        is_customs_declaration = True if is_customs_declaration_str == '是' else False
        arrival_date = get_excel_cell(9, 'm', self.table)
        print('arrival_date-->', arrival_date, type(arrival_date))
        if settings.SYSTEM_ORDER_MARK == 'YQF':
            if arrival_date:
                # arrival_date = datetime.strptime(arrival_date_str, '%Y-%m-%d').date()
                if arrival_date.date() < datetime.today().date():
                    raise ParamError(f'预计到货日期不能早于今天: {arrival_date.date()}', ErrorCode.PARAM_ERROR)
            else:
                raise ParamError(f'未填写预计到货日期: {arrival_date}', ErrorCode.PARAM_ERROR)

        order_params = {
            'customer': company,
            'saler': company.saler_name if company else None,
            'product': product,
            'ref_num': ref_num,
            'is_customs_declaration': is_customs_declaration,
            # 收件人, 私人地址不填外键
            'receiver': receiver,
            'buyer_address_num': buyer_address.address_num,
            'buyer_name': buyer_address.contact_name,
            'buyer_company_name': get_excel_cell(6, 'm', self.table) or buyer_address.company_name,
            'buyer_country_code': buyer_address.country_code,
            'buyer_postcode': buyer_address.postcode,
            'buyer_address_one': buyer_address.address_one,
            'buyer_city_code': buyer_address.city_code,
            'buyer_state': buyer_address.state_code,
            'buyer_phone': buyer_address.contact_phone,
            # 其他
            'order_remark': get_excel_cell(10, 'b', self.table),
            'arrival_date': arrival_date,
        }
        customer_order = CustomerOrder.objects.create(**order_params, **shipper_address,
                                                      **get_update_params_by_user(self.user, True))
        gen_order_num(customer_order)
        change_order_status(customer_order, 'WO', self.user)

        # 校验产品路线
        check_product_route(customer_order)

        # if customer_order.receiver:
        #     address_type = get_excel_cell(4, 'i', self.table)
        #     delivery_address_type_map = {value: key for key, value in dict(Address.DELIVERY_ADDRESS_TYPE).items()}
        #     customer_order.receiver.save_fields(delivery_address_type=delivery_address_type_map.get(address_type))

        return customer_order

    def create_order_zrh(self, company, product, receiver, buyer_address, shipper_address, upload_task):
        ref_num = get_excel_cell(3, 'b', self.table)
        if CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=ref_num, del_flag=False).exists():
            raise ParamError(f'客户订单号不能重复： {ref_num}', ErrorCode.PARAM_ERROR)

        is_customs_declaration_str = get_excel_cell(9, 'k', self.table)
        is_customs_declaration = True if is_customs_declaration_str == '是' else False
        arrival_date = get_excel_cell(9, 'o', self.table)
        print('arrival_date-->', arrival_date, type(arrival_date))
        if settings.SYSTEM_ORDER_MARK == 'YQF':
            if arrival_date:
                # arrival_date = datetime.strptime(arrival_date_str, '%Y-%m-%d').date()
                if arrival_date.date() < datetime.today().date():
                    raise ParamError(f'预计到货日期不能早于今天: {arrival_date.date()}', ErrorCode.PARAM_ERROR)
            else:
                raise ParamError(f'未填写预计到货日期: {arrival_date}', ErrorCode.PARAM_ERROR)

        order_params = {
            'customer': company,
            'saler': company.saler_name if company else None,
            'product': product,
            'ref_num': ref_num,
            'is_customs_declaration': is_customs_declaration,
            # 收件人, 私人地址不填外键
            'receiver': receiver,
            'buyer_address_num': buyer_address.address_num,
            'buyer_name': buyer_address.contact_name,
            'buyer_company_name': get_excel_cell(6, 'o', self.table) or buyer_address.company_name,
            'buyer_country_code': buyer_address.country_code,
            'buyer_postcode': buyer_address.postcode,
            'buyer_address_one': buyer_address.address_one,
            'buyer_city_code': buyer_address.city_code,
            'buyer_state': buyer_address.state_code,
            'buyer_phone': buyer_address.contact_phone,
            # 其他
            'order_remark': get_excel_cell(10, 'b', self.table),
            'arrival_date': arrival_date,
            'vat_num': get_excel_cell(10, 'f', self.table),
            'eori_num': get_excel_cell(10, 'k', self.table)
        }
        customer_order = CustomerOrder.objects.create(**order_params, **shipper_address,
                                                      **get_update_params_by_user(self.user, True))
        gen_order_num(customer_order)
        change_order_status(customer_order, 'WO', self.user)

        # 校验产品路线
        # check_product_route(customer_order)

        # if customer_order.receiver:
        #     address_type = get_excel_cell(4, 'i', self.table)
        #     delivery_address_type_map = {value: key for key, value in dict(Address.DELIVERY_ADDRESS_TYPE).items()}
        #     customer_order.receiver.save_fields(delivery_address_type=delivery_address_type_map.get(address_type))

        return customer_order

    def update_order(self, customer_order, company, product, receiver, buyer_address, shipper_address, upload_task):
        """
        更新订单信息
        """
        ref_num = get_excel_cell(3, 'b', self.table)
        # 检查是否存在重复的ref_num（排除当前订单）
        if CustomerOrder.objects.filter(~Q(id=customer_order.id), ~Q(order_status='VO'), ref_num=ref_num,
                                        del_flag=False).exists():
            raise ParamError(f'客户订单号不能重复： {ref_num}', ErrorCode.PARAM_ERROR)

        is_customs_declaration_str = get_excel_cell(9, 'i', self.table)
        is_customs_declaration = True if is_customs_declaration_str == '是' else False

        # Update order details
        customer_order.customer = company
        customer_order.saler = company.saler_name if company else None
        customer_order.product = product
        customer_order.ref_num = ref_num
        customer_order.is_customs_declaration = is_customs_declaration

        # 更新收件人
        customer_order.receiver = receiver
        customer_order.buyer_address_num = buyer_address.address_num
        customer_order.buyer_name = buyer_address.contact_name
        customer_order.buyer_company_name = get_excel_cell(6, 'm', self.table) or buyer_address.company_name
        customer_order.buyer_country_code = buyer_address.country_code
        customer_order.buyer_postcode = buyer_address.postcode
        customer_order.buyer_address_one = buyer_address.address_one
        customer_order.buyer_city_code = buyer_address.city_code
        customer_order.buyer_state = buyer_address.state_code
        customer_order.buyer_phone = buyer_address.contact_phone

        # 更新发件人
        for key, value in shipper_address.items():
            setattr(customer_order, key, value)

        # 更新备注
        customer_order.order_remark = get_excel_cell(10, 'b', self.table)

        # 更新预计到货日期
        customer_order.arrival_date = get_excel_cell(9, 'm', self.table) or customer_order.arrival_date

        customer_order.update_by = self.user
        customer_order.save()

        # 更新的excel保存到附件
        excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
        with open(excel_path, 'rb') as file:
            excel_content = file.read()
        # 使用 ContentFile 将文件内容转换为 Django 可识别的文件对象
        excel_file = ContentFile(excel_content, name=upload_task.file_name)
        OrderAttachment.objects.create(name=upload_task.file_name, url=excel_file, customerOrder=customer_order,
                                       **get_update_params_by_user(self.user, True))

        return customer_order

    def update_order_zrh(self, customer_order, company, product, receiver, buyer_address, shipper_address, upload_task):
        """
        更新订单信息
        """
        ref_num = get_excel_cell(3, 'b', self.table)
        # 检查是否存在重复的ref_num（排除当前订单）
        if CustomerOrder.objects.filter(~Q(id=customer_order.id), ~Q(order_status='VO'), ref_num=ref_num,
                                        del_flag=False).exists():
            raise ParamError(f'客户订单号不能重复： {ref_num}', ErrorCode.PARAM_ERROR)

        is_customs_declaration_str = get_excel_cell(9, 'k', self.table)
        is_customs_declaration = True if is_customs_declaration_str == '是' else False

        # Update order details
        customer_order.customer = company
        customer_order.saler = company.saler_name if company else None
        customer_order.product = product
        customer_order.ref_num = ref_num
        customer_order.is_customs_declaration = is_customs_declaration

        # 更新收件人
        customer_order.receiver = receiver
        customer_order.buyer_address_num = buyer_address.address_num
        customer_order.buyer_name = buyer_address.contact_name
        customer_order.buyer_company_name = get_excel_cell(6, 'o', self.table) or buyer_address.company_name
        customer_order.buyer_country_code = buyer_address.country_code
        customer_order.buyer_postcode = buyer_address.postcode
        customer_order.buyer_address_one = buyer_address.address_one
        customer_order.buyer_city_code = buyer_address.city_code
        customer_order.buyer_state = buyer_address.state_code
        customer_order.buyer_phone = buyer_address.contact_phone

        # 更新发件人
        for key, value in shipper_address.items():
            setattr(customer_order, key, value)

        # 更新备注
        customer_order.order_remark = get_excel_cell(10, 'b', self.table)
        customer_order.vat_num = get_excel_cell(10, 'f', self.table)
        customer_order.eori_num = get_excel_cell(10, 'k', self.table)

        # 更新预计到货日期
        customer_order.arrival_date = get_excel_cell(9, 'o', self.table) or customer_order.arrival_date

        customer_order.update_by = self.user
        customer_order.save()

        # 更新的excel保存到附件
        excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
        with open(excel_path, 'rb') as file:
            excel_content = file.read()
        # 使用 ContentFile 将文件内容转换为 Django 可识别的文件对象
        excel_file = ContentFile(excel_content, name=upload_task.file_name)
        OrderAttachment.objects.create(name=upload_task.file_name, url=excel_file, customerOrder=customer_order,
                                       **get_update_params_by_user(self.user, True))

        return customer_order

    # 创建包裹
    def create_parcel(self, get_cell, row, parcel_num, sys_parcel_num, customer_order, reference_id, shipment_id):
        exist_parcels = Parcel.objects.filter(parcel_num=parcel_num, customer_order=customer_order, del_flag=False)
        if exist_parcels.exists():
            # 存在包裹, 只新增ParcelItem
            parcel = exist_parcels.first()
        else:
            # 不存在包裹, 创建Parcel并新增ParcelItem
            same_parcel_num_parcels = Parcel.objects.filter(parcel_num=parcel_num, del_flag=False)
            if same_parcel_num_parcels.count() > 0:
                raise ParamError(f'包裹号 {parcel_num} 已存在, 不允许重复', ErrorCode.PARAM_ERROR)

            parcel_size = get_cell.get_cell_by_header(row, 'parcel_size')
            if not parcel_size:
                raise ParamError(f"第{row}行第d列(材积CM(长*宽*高))必填，请检查{parcel_size}", ErrorCode.PARAM_ERROR)

            if len(parcel_size.split('*')) != 3:
                raise ParamError(f"第{row}行第d列(材积CM(长*宽*高))中的值格式不正确，请检查{parcel_size}", ErrorCode.PARAM_ERROR)

            parcel_length, parcel_width, parcel_height = parcel_size.split('*')
            try:
                parcel_volume = float(parcel_length) * float(parcel_width) * float(parcel_height) / 1000000
            except ValueError:
                raise ParamError(f"第{row}行第d列(材积CM(长*宽*高))中的值格式不正确，请填写小数, 不要带单位", ErrorCode.PARAM_ERROR)

            # 新增包裹次序
            # parcel_order = Parcel.objects.filter(customer_order=customer_order).count()
            parcel_params = {
                'parcel_num': parcel_num,
                'reference_id': reference_id,
                'shipment_id': shipment_id,
                'parcel_length': parcel_length,
                'parcel_width': parcel_width,
                'parcel_height': parcel_height,
                'parcel_weight': get_cell.get_cell_by_header(row, 'parcel_weight'),
                'parcel_volume': parcel_volume,
                'customer_order': customer_order,
                'sys_parcel_num': sys_parcel_num
            }
            # parcel_params['sku'] = get_excel_cell(x, 'f', table)
            parcel = Parcel.objects.create(**parcel_params, **get_update_params_by_user(self.user, True))
        return parcel

    # 创建商品
    def create_parcel_item(self, get_cell, row, parcel, parcel_item_df):
        sku_params = {
            'customs_code': get_cell.get_cell_by_header(row, 'customs_code'),
            'item_code': get_cell.get_cell_by_header(row, 'item_code'),
            'item_name': get_cell.get_cell_by_header(row, 'declared_nameCN'),
            'item_qty': get_cell.get_cell_by_header(row, 'item_qty'),
            'declared_nameCN': get_cell.get_cell_by_header(row, 'declared_nameCN'),
            'declared_nameEN': get_cell.get_cell_by_header(row, 'declared_nameEN'),
            'declared_price': get_cell.get_cell_by_header(row, 'declared_price'),
            'brand': get_cell.get_cell_by_header(row, 'brand'),
            'model': get_cell.get_cell_by_header(row, 'model'),
            'texture': get_cell.get_cell_by_header(row, 'texture'),
            'use': get_cell.get_cell_by_header(row, 'use'),
            'parcel_num': parcel
        }
        # same_parcel_item = parcel_item_df[(parcel_item_df['parcel'] == parcel_num) &
        #                                   (parcel_item_df['parcel_item'] == sku_params['item_code'])]
        # 相同包裹号下的相同商品号不导入, 目前不加这个限制
        # if not same_parcel_item.empty:
        #     continue
        if sku_params['item_code'].strip():
            parcel_item_df.loc[len(parcel_item_df.index)] = [parcel.parcel_num, sku_params['item_code'].strip()]

        same_parcel_item = parcel_item_df[parcel_item_df.duplicated(subset=['parcel', 'parcel_item'])]
        if not same_parcel_item.empty:
            raise ParamError(f'导入失败, 相同包裹号『{parcel.parcel_num}』'
                             f'下的商品号『{sku_params["item_code"]}』重复', ErrorCode.PARAM_ERROR)
        parcel_item = ParcelItem.objects.create(**sku_params, **get_update_params_by_user(self.user, True))
        return parcel_item

    # 创建商品
    def create_parcel_item_zrh(self, get_cell, row, parcel, parcel_item_df):
        parcel_size = get_cell.get_cell_by_header(row, 'parcel_size')
        if not parcel_size:
            raise ParamError(f"第{row}行第d列(材积CM(长*宽*高))必填，请检查{parcel_size}", ErrorCode.PARAM_ERROR)

        if len(parcel_size.split('*')) != 3:
            raise ParamError(f"第{row}行第d列(材积CM(长*宽*高))中的值格式不正确，请检查{parcel_size}",
                             ErrorCode.PARAM_ERROR)

        parcel_item_length, parcel_item_width, parcel_item_height = parcel_size.split('*')
        sku_params = {
            'customs_code': get_cell.get_cell_by_header(row, 'customs_code'),
            'item_code': get_cell.get_cell_by_header(row, 'item_code'),
            'item_weight': get_cell.get_cell_by_header(row, 'item_weight'),
            'item_length': parcel_item_length,
            'item_width': parcel_item_width,
            'item_height': parcel_item_height,
            'item_name': get_cell.get_cell_by_header(row, 'declared_nameCN'),
            'item_qty': get_cell.get_cell_by_header(row, 'item_qty'),
            'declared_nameCN': get_cell.get_cell_by_header(row, 'declared_nameCN'),
            'declared_nameEN': get_cell.get_cell_by_header(row, 'declared_nameEN'),
            'declared_price': get_cell.get_cell_by_header(row, 'declared_price'),
            'brand': get_cell.get_cell_by_header(row, 'brand'),
            'model': get_cell.get_cell_by_header(row, 'model'),
            'texture': get_cell.get_cell_by_header(row, 'texture'),
            'use': get_cell.get_cell_by_header(row, 'use'),
            'sku_url': get_cell.get_cell_by_header(row, 'sku_url'),
            'parcel_num': parcel
        }
        # same_parcel_item = parcel_item_df[(parcel_item_df['parcel'] == parcel_num) &
        #                                   (parcel_item_df['parcel_item'] == sku_params['item_code'])]
        # 相同包裹号下的相同商品号不导入, 目前不加这个限制
        # if not same_parcel_item.empty:
        #     continue
        if sku_params['item_code'].strip():
            parcel_item_df.loc[len(parcel_item_df.index)] = [parcel.parcel_num, sku_params['item_code'].strip()]

        same_parcel_item = parcel_item_df[parcel_item_df.duplicated(subset=['parcel', 'parcel_item'])]
        if not same_parcel_item.empty:
            raise ParamError(f'导入失败, 相同包裹号『{parcel.parcel_num}』'
                             f'下的商品号『{sku_params["item_code"]}』重复', ErrorCode.PARAM_ERROR)
        parcel_item = ParcelItem.objects.create(**sku_params, **get_update_params_by_user(self.user, True))
        return parcel_item


class MultiFbaOrderImportService:
    """
    FBA多订单导入(FBA多订单异步导入)(异步导入FBA多订单)
    """
    def __init__(self, table, user):
        self.table = table
        self.user = user
        self.now_time = datetime.now()

    # 获取客户
    def get_order_customer(self, check_permission=True):
        company = None
        is_cms = not self.user.is_staff
        if is_cms:
            company = self.user.company

        if company and company.status == 'OFF':
            raise ParamError(f'客户: {company.name}已被停用', ErrorCode.PARAM_ERROR)

        # 权限检查
        if check_permission:
            user_profile = UserProfile.objects.filter(username=self.user.username).first()
            department = user_profile.department
            if department and department.relate_customer and company.id not in list(
                    map(int, department.relate_customer.split(','))):
                raise ParamError(f'没有权限导入: 用户的部门没有关联此订单客户 {company.name}', ErrorCode.PARAM_ERROR)
        return company

    # 获取发件人数据
    def get_order_shipper(self, address_num, product):
        # 如果没有发件人 默认用产品的
        shipper_address = {}
        if address_num:
            address = Address.objects.filter(address_num=address_num, address_type='SP', del_flag=False).last()
            if address:
                shipper_address = {
                    'shipper': address,
                    'contact_name': address.contact_name,
                    'company_name': address.company_name,
                    'address_one': address.address_one,
                    'city_code': address.city_code,
                    'state_code': address.state_code,
                    'postcode': address.postcode,
                    'contact_phone': address.contact_phone,
                    'country_code': address.country_code or 'CN'
                }
            else:
                raise ParamError(f'未找到发件人地址: {address_num}', ErrorCode.PARAM_ERROR)

        else:
            if product.address_num:
                address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
                if address_queryset:
                    seller_address = address_queryset.first()
                    shipper_address = {
                        # 发件人
                        'shipper': seller_address,
                        'contact_name': seller_address.contact_name,
                        'company_name': seller_address.company_name,
                        'address_one': seller_address.address_one,
                        'city_code': seller_address.city_code,
                        'state_code': seller_address.state_code,
                        'postcode': seller_address.postcode,
                        'contact_phone': seller_address.contact_phone,
                        'country_code': 'CN'
                    }
        return shipper_address

    # 获取收件人数据
    def get_order_receiver(self, warehouse_address, private_address):
        if not warehouse_address and not private_address:
            raise ParamError(f'仓库地址和私人地址都不存在', ErrorCode.PARAM_ERROR)

        # 仓库地址优先读取系统地址, 若系统地址不存在, 则读取表格地址保存到系统
        if warehouse_address:
            buyer_address_num = str(warehouse_address).strip()
            buyer_addresses = Address.objects.filter(address_num=warehouse_address, del_flag=False)
            if buyer_addresses:
                buyer_address = buyer_addresses.first()
            else:
                buyer_address = save_address(buyer_address_num, self.table)
            # receiver = buyer_address
        # 私人地址直接读取表格数据, 不读取系统地址
        else:
            # receiver = None
            buyer_address = save_address(private_address, self.table, is_warehouse_address=False)
        # print('buyer_address-->', buyer_address)
        return buyer_address

    # 创建订单
    def create_order(self, ref_num, company, product, receiver, buyer_address, shipper_address, upload_task,
                     customer_order_map):

        duplicate_orders = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=ref_num, del_flag=False)
        if ref_num in customer_order_map:
            return customer_order_map.get(ref_num)
        elif duplicate_orders.exists():
            raise ParamError(f'客户订单号不能重复： {ref_num}', ErrorCode.PARAM_ERROR)

        is_customs_declaration_str = get_excel_cell(9, 'i', self.table)
        is_customs_declaration = True if is_customs_declaration_str == '是' else False

        order_params = {
            'customer': company,
            'saler': company.saler_name if company else None,
            'product': product,
            'ref_num': ref_num,
            'is_customs_declaration': is_customs_declaration,
            # 收件人, 私人地址不填外键
            'receiver': receiver,
            'buyer_address_num': buyer_address.address_num,
            'buyer_name': buyer_address.contact_name,
            'buyer_company_name': buyer_address.company_name,
            'buyer_country_code': buyer_address.country_code,
            'buyer_postcode': buyer_address.postcode,
            'buyer_address_one': buyer_address.address_one,
            'buyer_city_code': buyer_address.city_code,
            'buyer_state': buyer_address.state_code,
            'buyer_phone': buyer_address.contact_phone,
            # 其他
            # 'order_remark': get_excel_cell(10, 'b', self.table),
        }
        customer_order = CustomerOrder.objects.create(**order_params, **shipper_address,
                                                      **get_update_params_by_user(self.user, True,
                                                                                  datetime_now=datetime.now()))
        gen_order_num(customer_order)
        change_order_status(customer_order, 'WO', self.user)

        # 校验产品路线
        check_product_route(customer_order)

        # 保存到附件
        excel_path = os.path.join(settings.STATIC_MEDIA_DIR, str(upload_task.url))
        with open(excel_path, 'rb') as file:
            excel_content = file.read()
        # 使用 ContentFile 将文件内容转换为 Django 可识别的文件对象
        excel_file = ContentFile(excel_content, name=upload_task.file_name)
        OrderAttachment.objects.create(name=upload_task.file_name, url=excel_file, customerOrder=customer_order,
                                       **get_update_params_by_user(self.user, True,
                                                                   datetime_now=datetime.now()))

        return customer_order

    # 创建包裹
    def create_parcel(self, get_cell, row, parcel_num, sys_parcel_num, customer_order, reference_id, shipment_id):
        exist_parcels = Parcel.objects.filter(parcel_num=parcel_num, customer_order=customer_order, del_flag=False)
        if exist_parcels.exists():
            # 存在包裹, 只新增ParcelItem
            parcel = exist_parcels.first()
        else:
            # 不存在包裹, 创建Parcel并新增ParcelItem
            same_parcel_num_parcels = Parcel.objects.filter(parcel_num=parcel_num, del_flag=False)
            if same_parcel_num_parcels.count() > 0:
                raise ParamError(f'包裹号 {parcel_num} 已存在, 不允许重复', ErrorCode.PARAM_ERROR)

            parcel_size = get_cell.get_cell_by_header(row, 'parcel_size')
            if not parcel_size:
                raise ParamError(f"第{row}行第d列(材积CM(长*宽*高))必填，请检查{parcel_size}", ErrorCode.PARAM_ERROR)

            if len(parcel_size.split('*')) != 3:
                raise ParamError(f"第{row}行第d列(材积CM(长*宽*高))中的值格式不正确，请检查{parcel_size}", ErrorCode.PARAM_ERROR)

            parcel_length, parcel_width, parcel_height = parcel_size.split('*')
            try:
                parcel_volume = float(parcel_length) * float(parcel_width) * float(parcel_height) / 1000000
            except ValueError:
                raise ParamError(f"第{row}行第d列(材积CM(长*宽*高))中的值格式不正确，请填写小数, 不要带单位", ErrorCode.PARAM_ERROR)

            # 新增包裹次序
            # parcel_order = Parcel.objects.filter(customer_order=customer_order).count()
            parcel_params = {
                'parcel_num': parcel_num,
                'reference_id': reference_id,
                'shipment_id': shipment_id,
                'parcel_length': parcel_length,
                'parcel_width': parcel_width,
                'parcel_height': parcel_height,
                'parcel_weight': get_cell.get_cell_by_header(row, 'parcel_weight'),
                'parcel_volume': parcel_volume,
                'customer_order': customer_order,
                'sys_parcel_num': sys_parcel_num
            }
            # parcel_params['sku'] = get_excel_cell(x, 'f', table)
            parcel = Parcel.objects.create(**parcel_params, **get_update_params_by_user(
                self.user, True,
                datetime_now=datetime.now()))
        return parcel

    # 创建商品
    def create_parcel_item(self, get_cell, row, parcel, parcel_item_df):
        sku_params = {
            'customs_code': get_cell.get_cell_by_header(row, 'customs_code'),
            'item_code': get_cell.get_cell_by_header(row, 'item_code'),
            'item_name': get_cell.get_cell_by_header(row, 'declared_nameCN'),
            'item_qty': get_cell.get_cell_by_header(row, 'item_qty'),
            'declared_nameCN': get_cell.get_cell_by_header(row, 'declared_nameCN'),
            'declared_nameEN': get_cell.get_cell_by_header(row, 'declared_nameEN'),
            'declared_price': get_cell.get_cell_by_header(row, 'declared_price'),
            'brand': get_cell.get_cell_by_header(row, 'brand'),
            'model': get_cell.get_cell_by_header(row, 'model'),
            'texture': get_cell.get_cell_by_header(row, 'texture'),
            'use': get_cell.get_cell_by_header(row, 'use'),
            'parcel_num': parcel
        }
        # same_parcel_item = parcel_item_df[(parcel_item_df['parcel'] == parcel_num) &
        #                                   (parcel_item_df['parcel_item'] == sku_params['item_code'])]
        # 相同包裹号下的相同商品号不导入, 目前不加这个限制
        # if not same_parcel_item.empty:
        #     continue
        if sku_params['item_code'].strip():
            parcel_item_df.loc[len(parcel_item_df.index)] = [parcel.parcel_num, sku_params['item_code'].strip()]

        same_parcel_item = parcel_item_df[parcel_item_df.duplicated(subset=['parcel', 'parcel_item'])]
        if not same_parcel_item.empty:
            raise ParamError(f'导入失败, 相同包裹号『{parcel.parcel_num}』'
                             f'下的商品号『{sku_params["item_code"]}』重复', ErrorCode.PARAM_ERROR)
        parcel_item = ParcelItem.objects.create(**sku_params, **get_update_params_by_user(
            self.user, True,
            datetime_now=datetime.now()))
        return parcel_item


def clean_fba_order_before_import(customer_order, user, overwrite=True):
    """
    导入覆盖前，清理订单数据
    清理订单数据，删除所有包裹SKU、所有包裹、订单信息
    """
    if not overwrite:
        logger.info(f'Skipping cleanup for FBA order: {customer_order.order_num} (overwrite=False)')
        return

    logger.info(f'Cleaning FBA order before import: {customer_order.order_num}')

    # 获取所有包裹
    parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False)

    # 删除包裹SKU
    for parcel in parcels:
        ParcelItem.objects.filter(parcel_num=parcel, del_flag=False).update(
            del_flag=True,
        )

    # 删除所有包裹
    parcels.update(
        del_flag=True,
    )

    # 重新设置订单
    customer_order.reference_id = None
    customer_order.shipment_id = None
    customer_order.save()

    logger.info(f'Successfully cleaned FBA order: {customer_order.order_num}')
