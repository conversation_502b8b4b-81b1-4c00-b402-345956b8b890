import base64
import calendar
import io
import json
import os
import random
import re
import traceback
import types
import urllib.request as urllib
from collections import OrderedDict, defaultdict
from datetime import datetime, timedelta, date
from functools import reduce
from pprint import pprint
import time
from decimal import Decimal, ROUND_HALF_UP, InvalidOperation
from typing import List, Union, Optional, Tuple
from decimal import Decimal
import openpyxl
from barcode.writer import ImageWriter
from dateutil import rrule
from dateutil.relativedelta import relativedelta
from django.http import HttpResponse
from django.utils import timezone
from openpyxl.drawing.image import Image
from openpyxl.reader.excel import load_workbook
from openpyxl.styles import Alignment, PatternFill, Font
from PIL import Image as PILImage
from pydash import get

import barcode

from django.core.mail import EmailMessage
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist, MultipleObjectsReturned
from django.core.files.base import ContentFile
from django.core.mail import EmailMultiAlternatives
from django.db import transaction, models, IntegrityError
from django.db.models import Q, Sum, F, QuerySet, Max, Min, Case, When, Value, Prefetch
from django.forms import model_to_dict
from openpyxl import Workbook
from rest_framework import status, serializers
from rest_framework.request import Request
from rest_framework.response import Response
from simpleeval import simple_eval
from openpyxl.styles import Alignment
from django.db.models import Field

from account.models import Account, AccountDetail
from alita.logger import logger
from common.common_const import CostShareTypeEnum, ShopTypeEnum
from common.common_parameter import TRACK17_CARRIER_CODE_MAP, CUSTOMER_ORDER_STATUS_MAP
from common.error import ErrorCode, ParamError
from common.excel_tools import set_data
from common.order_num_gen_rule import create_order_num, create_parcel_order_num_new, generate_order_num_commom, \
    is_parcel_customer_order
from common.service.customer_fbm_order_export_service import ExportFbmOrderExcelSchema
from common.service.customer_order_service import get_shipment_id
from common.service.pms_service import add_revenue, add_cost, add_cost_by_product
from common.service.track_service import add_parcel_track_bi
from common.sort_code_util import convert_sort_code_num, generate_shipment_sort_code_new
from common.string import calculate_mod36_checksum
from common.utils.amazon_ship_track_response_util import format_utc_date_from_datetime
from common.utils.barcode_gen import create_barcode_for_fba_order, create_box_barcode_for_fbm_order, create_box_barcode_for_fbm_order_with_size
from common.utils.custom_json_response import JResponse
from common.utils.custom_pagination import PostBodyPagination
from common.utils.dmas_util import send_fba_track_dmas
from common.utils.gen_mode_key_util import gen_mode_key_by_shunt
from common.utils.get_currency_rate import get_currency_rate
from common.utils.product import get_replace_order_product
from common.utils.response import fail_response, fail_response_common, success_response_common
from common.utils.barcode_gen import overwrite_pdf_with_fitz
from common.service.product_attribute_limit import get_attribute_extend_limitation_default_value

from company.models import Company, Address, SupplierButt, SupplierButtAccount, TrackNodeAddress
from crm.models import ProductBubbleDiscounts, BubbleDiscounts, WeightCommission, ProductWeightCommission, \
    OrderCommissionDiscounts, ProductSaleCostVersion, ProductSaleCostVersionLine, OrderCommissionExport, \
    CustomerOrderSaleCost, CommissionDiscountsBlack
from cs.models import VasOrder, VasOrderChargeOut, VasOrderChargeIn
from info.models import AccountingInterval, AirportCode, Exchange, TrackCode, Dict, Charge, ServiceClass
from oms.models import InboundOrderChargeIn, InboundOrderChargeOut, OutboundOrderChargeIn, OutboundOrderChargeOut, \
    WarehouseInboundOrder, WarehouseOutboundOrder, ReturnOrder, ReturnOrderChargeIn, ReturnOrderChargeOut, \
    WarehouseOutboundTransferOrder, OutboundTransferChargeIn, OutboundTransferChargeOut, WarehouseInboundTransferOrder, \
    InboundTransferChargeIn, InboundTransferChargeOut
from order.integration.schemas.customer_order_schema import FBMBoxLabelSchema, ExportBoxOrder, \
    ExportBoxInboundOrderSchema, ExportBoxDetailSchema, ExportShipmentExcelSchema
# from order.integration.util.abstract_func import add_warehouse_code, create_order_label_task
from order.integration.util.clearanceOutUtil import request_server
from order.integration.util.commonUtil import DecimalEncoder
from order.integration.util.hzhUtil import push_track
from order.models import BigParcel, BigPickRecord, Clearance, ClearanceAttachment, ClearanceChargeIn, \
    ClearanceChargeOut, CustomerOrder, CustomerOrderChargeIn, CustomerOrderChargeOut, CustomsClearanceOrder, \
    CustomsClearanceOrderChargeIn, CustomsClearanceOrderChargeOut, CustomsClearanceRelateTruck, Parcel, \
    ParcelCustomerOrder, ParcelOrderChargeIn, ParcelOrderChargeOut, ParcelOutboundOrder, ParcelTrack, Track, \
    MasterOrder, MasterOrderChargeIn, MasterOrderChargeOut, OceanOrder, OceanOrderChargeOut, CollectOrder, \
    CollectOrderChargeOut, ClearanceOut, TruckOrder, ClearanceOutChargeOut, TruckOrderChargeOut, \
    CustomerOrderRelateOcean, ParcelOrderParcel, ParcelOrderItem, ParcelItem, CustomerParcelTrack, OrderSyncUploadTask, \
    OrderAsyncTask, RelateStorePosition, CollectOrderChargeIn, BulkParcelWeightRecord, PickRecord, ParcelOrderLabelTask, \
    OceanOrderTrack, OcShipment, TakeStockJobDetail, OrderFieldChangeLog, \
    CombineBillingOrderChargeIn, ClearanceOutSyncTask, LabelChangeConfig, CustomerOrderRelateTruck
from pms.models import ProductTrackCode, Service, ProductRevenueVersion, ProtocolProject, Product, ProductCharge, \
    ProductRevenueVersionLine, ProductCostVersion, ProductCostVersionLine, ProductBasicRestriction, \
    ProtocolProjectCustomer, ProductSalesPrice, ProtocolProjectProduct, ProductSalesPriceCustomer, ProductRoute, \
    ProductSalesPriceWarehouse, ZoneSetting, ProductZonePostCode, ServiceZoneSetting, ServiceCharge, ProductZone
from pms.util.calc import round_value, build_order_calc_vo, get_charge_weight_by_product_charge, \
    get_charge_weight_by_order, check_product_intercept_rule, OrderChargeCalcResultVo
from pms.util.product_zone import get_zone_postcode_list, get_zone
from pms.util.revenue_calc import adjust_weight_revenue_calc
from rbac.models import UserProfile
from settle.models import Debit, AccountReceivable, AccountPayable, Invoice, ReceiptInvoice, ReceiptDebit, Receipt, \
    DebitAdjust, DebitAdjustDetail, ReconciliationDetails, FinanceOperateChangeLog
from common.error import ParamError, ErrorCode
from pms.serializers.product_revenue_version import handler_protocol_project_product
from alita.settings.base import MEDIA_URL
from order.models import ParcelOrderLabel
# from common.utils.barcode_gen import create_barcodes_for_order

# 获取创建参数创建账单
from wechat.message.wxUtil import get_order_message_bw
from wechat.models import WechatPushMessage

from django_redis import get_redis_connection

redis = get_redis_connection("default")

ORDER_STATUS = {
    'DR': '草稿',
    'WO': '等待作业',
    'PDC': '已预报',
    'ITP': '已拦截',
    'PW': '已部分入仓',
    'AW': '已全部入仓',
    'CWED': '已确认入仓数据',
    'OW': '已出国内仓',
    'TF': '转运',
    'SF': '已签收',
    'VO': '作废',
    'FC': '完成'
}


class TrackCodeError(Exception):
    """轨迹代码相关错误"""
    pass


class TrackTimeOrderError(Exception):
    """轨迹时间顺序错误"""
    pass


class ParcelSerializer(serializers.ModelSerializer):
    class Meta:
        model = Parcel
        fields = '__all__'


def save_parcel(customer_order, item, valuation_label=True, parcel: Parcel = None, user=None):
    """
    封装保存包裹
    """
    parcel_flag = False
    if parcel:
        parcel_flag = True
        parcel.del_flag = False
        # 保存包裹的修改记录
        serializer = ParcelSerializer(data=item)
        # 验证数据
        serializer.is_valid()
        validated_data = serializer.validated_data
        # print('validated_data??66-->', validated_data)
        OrderFieldChangeLog.record(parcel, validated_data, 'FBA', user, instance_id=parcel.customer_order.id,
                                   do_not_record=['remark'])
        old_shipment_id = parcel.shipment_id
        # 修改货件管理的货件号
        oc_shipments = OcShipment.objects.filter(customer_order_num=customer_order,
                                                 shipment_id=old_shipment_id, del_flag=False)
        oc_shipments.update(shipment_id=item.get('shipment_id'))
    else:
        parcel = Parcel()
        # 修改或创建货件管理
        oc_shipments = OcShipment.objects.filter(customer_order_num=customer_order,
                                                 shipment_id=item.get('shipment_id'), del_flag=False)
        if oc_shipments.exists():
            oc_shipment = oc_shipments.first()
            oc_shipments.update(parcel_qty=(oc_shipment.parcel_qty or 0) + 1)
        else:
            shipment_params = {
                "customer_order_num": customer_order,
                "shipment_id": item.get('shipment_id'),
                "reference_id": item.get('reference_id'),
                "customer": customer_order.customer,
                "parcel_qty": 1,
            }
            OcShipment.objects.create(**shipment_params)
    parcel.parcel_num = item['parcel_num']
    parcel.reference_id = item.get('reference_id')
    parcel.shipment_id = item.get('shipment_id')
    parcel.shop_type = item.get('shop_type')
    # parcel.parcel_unit = item.get('parcel_unit')
    parcel.parcel_length = item['parcel_length'] or 0
    parcel.parcel_width = item['parcel_width'] or 0
    parcel.parcel_height = item['parcel_height'] or 0
    parcel.parcel_weight = item['parcel_weight'] or 0
    parcel.label_weight = item.get('label_weight') or 0
    parcel.label_length = item.get('label_length') or 0
    parcel.label_width = item.get('label_width') or 0
    parcel.label_height = item.get('label_height') or 0
    if valuation_label:
        parcel.label_weight = item.get('label_weight') or parcel.parcel_weight
        parcel.label_length = item.get('label_length') or parcel.parcel_length
        parcel.label_width = item.get('label_width') or parcel.parcel_width
        parcel.label_height = item.get('label_height') or parcel.parcel_height
    parcel.actual_length = item.get('actual_length') or 0
    parcel.actual_width = item.get('actual_width') or 0
    parcel.actual_height = item.get('actual_height') or 0
    parcel.actual_weight = item.get('actual_weight') or 0
    parcel.remark = item.get('remark') or None
    parcel.customer_sys_parcel_num = item.get('customer_sys_parcel_num', parcel.customer_sys_parcel_num)
    parcel.parcel_qty = 1
    # 前端不会编辑系统包裹号和转单号
    if not parcel_flag:
        parcel.tracking_num = item.get('tracking_num')
        parcel.sys_parcel_num = item.get('sys_parcel_num')
        parcel.is_weighing = item.get('is_weighing', False)
    if (item['parcel_width'] or 0) != 0 and (item['parcel_length'] or 0) != 0 and (item['parcel_height'] or 0) != 0:
        parcel.parcel_volume = Decimal(item['parcel_length']) * Decimal(item['parcel_width']) * Decimal(
            item['parcel_height']) / 1000000
    else:
        parcel.parcel_volume = 0
    if (item.get('actual_width') or 0) != 0 and (item.get('actual_length') or 0) != 0 and (
            item.get('actual_height') or 0) != 0:
        parcel.actual_volume = Decimal(item.get('actual_length')) * Decimal(item.get('actual_width')) * Decimal(
            item.get('actual_height')) / 1000000
    else:
        parcel.actual_volume = 0
    parcel.customer_order = customer_order
    parcel.order_type = customer_order.order_type
    parcel.save()
    return parcel


def save_shipments(customer_order, parcel_item_data):
    """
    保存货件信息
    @param customer_order:
    @param parcel_item_data:
    @return:
    """

    # 检查 parcel_item_data 是否为空
    if not parcel_item_data:
        return

    # 根据 shipment_id 分组
    shipment_package_infos_dict = defaultdict(list)
    for package_info in parcel_item_data:
        if not package_info.get('shipment_id'):
            continue  # 如果 shipment_id 不存在，跳过该包裹
        shipment_package_infos_dict[package_info['shipment_id']].append(package_info)

    # 处理每个货件
    for shipment_id, package_infos in shipment_package_infos_dict.items():
        if not package_infos:
            continue  # 如果没有包裹信息，跳过该货件

        # 获取第一个包裹信息作为参考
        package_info = package_infos[0]

        # 检查必要的字段是否存在
        required_fields = ['reference_id', 'shop_type']
        if not all(field in package_info for field in required_fields):
            continue  # 如果缺少必要字段，跳过该货件

        # 计算包裹数量
        parcel_qty = sum(package_info.get('parcel_qty', 1) for package_info in package_infos)

        # 准备货件参数
        shipment_params = {
            "customer_order_num": customer_order,
            "shipment_id": shipment_id,
            "reference_id": package_info['reference_id'],
            "customer": customer_order.customer,
            "parcel_qty": parcel_qty,
            "shop_type": ShopTypeEnum.convert_shop_type(package_info['shop_type']),
        }

        # 创建货件
        OcShipment.objects.create(**shipment_params)


def save_parcel_item(item, parcel):
    """
    封装保存包裹商品
    :param item:
    :param parcel:
    """
    parcel_item = ParcelItem()
    parcel_item.parcel_num = parcel
    parcel_item.item_code = item['item_code']
    parcel_item.declared_nameCN = item['declared_nameCN']
    parcel_item.declared_nameEN = item['declared_nameEN']
    parcel_item.declared_price = item['declared_price'] or None
    parcel_item.item_qty = item.get('item_qty') or None
    parcel_item.item_weight = item.get('item_weight') or None
    parcel_item.texture = item.get('texture')
    parcel_item.item_size = item.get('item_size')
    parcel_item.use = item.get('use')
    parcel_item.brand = item.get('brand')
    parcel_item.model = item.get('model')
    parcel_item.customs_code = item.get('customs_code')
    parcel_item.tax_rate = item.get('tax_rate')
    parcel_item.fba_no = item.get('fba_no')
    parcel_item.fba_track_code = item.get('fba_track_code')
    parcel_item.box_qty = item.get('box_qty')
    parcel_item.combined_parcel_num = item.get('combined_parcel_num')
    # 由于shipment_id和包裹的数据冲突, 所以暂时不保存
    # parcel_item.shipment_id = item.get('shipment_id')
    is_electric = item.get('is_electric')
    is_magnetic = item.get('is_magnetic')
    if str(is_electric).lower() == 'true' or is_electric == '是':
        parcel_item.is_electric = True
    if str(is_magnetic).lower() == 'true' or is_magnetic == '是':
        parcel_item.is_magnetic = True
    parcel_item.custom_clearance = item.get('custom_clearance')
    # http://127.0.0.1:8000/api/customerOrders/1495/static/alita/media/ParcelItem/2024/09/11/FBA18DK4F9MXU000001_12_QxATsi5.jpg
    item_picture = item.get('item_picture', None)
    print('save_parcel_item item_picture1-->', item_picture)
    if item_picture:
        if '/media/' in item_picture:
            print('save_parcel_item id-->', parcel_item.id)
            parcel_item.item_picture = item_picture.split('/media/')[-1]
        else:
            parcel_item.item_picture = item_picture
    parcel_item.save()
    # 保存base64商品图片
    save_parcel_item_picture(item, parcel_item)


def save_parcel_item_picture(item, parcel_item):
    file_base64 = item.get('picture_base64')
    picture_url = item.get('picture_url')

    if not file_base64 and not picture_url:
        logger.warning('未提供图片数据或图片链接')
        return

    file_name = f'{parcel_item.parcel_num.parcel_num}_{parcel_item.id}.png'
    picture_url_path = "ParcelItem/" + (datetime.now().strftime("%Y/%m/%d/")) + file_name
    decoded_file_name = os.path.join(settings.MEDIA_ROOT, picture_url_path)
    file_dir = os.path.dirname(decoded_file_name)

    if not os.path.exists(file_dir):
        os.makedirs(file_dir)

    if file_base64:
        # 处理 base64 图片数据
        with open(decoded_file_name, "wb") as code:
            code.write(base64.b64decode(file_base64))
    elif picture_url:
        # 处理图片链接
        try:
            with urllib.urlopen(picture_url) as response:
                with open(decoded_file_name, "wb") as file:
                    file.write(response.read())
        except Exception as e:
            logger.error(f'下载图片链接失败: {picture_url}, 错误信息: {str(e)}')
            return

    parcel_item.item_picture = picture_url_path
    parcel_item.save()
    logger.info(f'成功保存商品图片, 包裹号: {parcel_item.parcel_num.parcel_num}, '
                f'商品号: {parcel_item.item_code}, 图片路径: {picture_url_path}')


@transaction.atomic
def create_debit(params):
    logger.info('start publish create debit .....')
    debit = Debit.objects.create(**params)
    debit.debit_num = 'D' + create_order_num(debit.id)
    logger.info('debit_num is ' + debit.debit_num)
    debit.save()
    logger.info('end publish create debit .....')
    return debit


# 获取创建参数创建收款明细
@transaction.atomic
def create_account_receivable(params):
    logger.info('start publish create account_receivable .....')
    account_receivable = AccountReceivable.objects.create(**params)
    account_receivable.ar_num = 'AR' + create_order_num(account_receivable.id)
    logger.info('AR Number is ' + account_receivable.ar_num)
    account_receivable.save()
    logger.info('end publish create account_receivable .....')


# 获取创建参数创建账单
@transaction.atomic
def create_account_payable(params):
    logger.info('start publish create account_payable .....' + str(params['order_num']))
    logger.info('params .....' + str(params))
    # 判断付款明细是否已存在
    account_payable_queryset = AccountPayable.objects.filter(order_num=params['order_num'],
                                                             charge_fee_id=params['charge_fee_id'],
                                                             del_flag=False)
    if account_payable_queryset.count() > 0:
        raise ParamError('付款明细已存在!', ErrorCode.PARAM_ERROR)
    account_payable = AccountPayable.objects.create(**params)
    account_payable.cr_num = 'CR' + create_order_num(account_payable.id)
    logger.info('CR Number is ' + account_payable.cr_num)
    account_payable.save()
    logger.info('end publish create account_payable .....' + str(params['order_num']))


# 获取收入确认和成本确认的收入或者成本
@transaction.atomic
def get_amount_form_charge(charge_queryset):
    # 此处判断收入和成本的币种，同一个币种则不用进行汇率转换，不同币种换算为人民币
    amount = 0
    if charge_queryset.count() != 0:
        currency_count = charge_queryset.values("currency_type").distinct().count()
        if currency_count != 1:
            # 进行汇率换算为CNY， 本身是CNY就不需要进行换算
            for item in charge_queryset:
                if item.currency_type == 'CNY':
                    amount += item.account_charge
                else:
                    # 单币种不需要进行汇率换算，生成成本的时候已经生成
                    if settings.MULTI_CURRENCY:
                        rate = get_exchange_rate(item.currency_type)
                        amount += item.account_charge * rate
                    else:
                        amount += item.account_charge
        else:
            amount = charge_queryset.aggregate(total=Sum('account_charge'))['total'] or 0
    return amount


# 获取收入确认和成本确认的收入或者成本, 区分币种(不同币种不放在合计到一起)
@transaction.atomic
def get_currency_amount_form_charge(charge_queryset):
    amount = {}
    if charge_queryset.count() != 0:
        # currency_types = charge_queryset.values("currency_type").distinct()
        for charge in charge_queryset:
            if charge.currency_type in amount:
                amount[charge.currency_type] += charge.charge_total
            else:
                amount[charge.currency_type] = charge.charge_total
    return amount


# 收入和成本都确认了计算毛利和毛利币种
@transaction.atomic
def check_gross_correct(order, order_type):
    logger.info('check_gross_correct---->>>')
    # 获取收入确认和成本确认
    if order_type == 'ParcelCustomerOrder':
        order_queryset = ParcelCustomerOrder.objects.filter(id=order.id)
        charge_in = ParcelOrderChargeIn.objects.filter(customer_order_num=order.id, del_flag=False)
        charge_out = ParcelOrderChargeOut.objects.filter(customer_order_num=order.id, del_flag=False)
        # account_time = order.order_time
    elif order_type == 'CustomerOrder':
        order_queryset = CustomerOrder.objects.filter(id=order.id)
        charge_in = CustomerOrderChargeIn.objects.filter(customer_order_num=order.id, del_flag=False)
        charge_out = CustomerOrderChargeOut.objects.filter(customer_order_num=order.id, del_flag=False)
        # account_time = order.arrival_date
    elif order_type == 'Clearance':
        order_queryset = Clearance.objects.filter(id=order.id)
        charge_in = ClearanceChargeIn.objects.filter(customer_order_num=order.id, del_flag=False)
        charge_out = ClearanceChargeOut.objects.filter(customer_order_num=order.id, del_flag=False)
        # account_time = order.opera_date
    elif order_type == 'Outbound':
        order_queryset = WarehouseOutboundOrder.objects.filter(id=order.id)
        charge_in = OutboundOrderChargeIn.objects.filter(customer_order_num=order.id, del_flag=False)
        charge_out = OutboundOrderChargeOut.objects.filter(customer_order_num=order.id, del_flag=False)
        # account_time = order.order_time
    elif order_type == 'Inbound':
        order_queryset = WarehouseInboundOrder.objects.filter(id=order.id)
        charge_in = InboundOrderChargeIn.objects.filter(customer_order_num=order.id, del_flag=False)
        charge_out = InboundOrderChargeOut.objects.filter(customer_order_num=order.id, del_flag=False)
    elif order_type == 'OMSReturnOrder':
        order_queryset = ReturnOrder.objects.filter(id=order.id)
        charge_in = ReturnOrderChargeIn.objects.filter(customer_order_num=order.id, del_flag=False)
        charge_out = ReturnOrderChargeOut.objects.filter(customer_order_num=order.id, del_flag=False)
    elif order_type == 'CustomsClearanceOrder':
        order_queryset = CustomsClearanceOrder.objects.filter(id=order.id)
        charge_in = CustomsClearanceOrderChargeIn.objects.filter(customer_order_num=order.id, del_flag=False)
        charge_out = CustomsClearanceOrderChargeOut.objects.filter(customer_order_num=order.id, del_flag=False)
    elif order_type == 'MasterOrder':
        order_queryset = MasterOrder.objects.filter(id=order.id)
        charge_in = MasterOrderChargeIn.objects.filter(customer_order_num=order.id, del_flag=False)
        charge_out = MasterOrderChargeOut.objects.filter(customer_order_num=order.id, del_flag=False)
    elif order_type == 'VasOrder':
        order_queryset = VasOrder.objects.filter(id=order.id)
        charge_in = VasOrderChargeIn.objects.filter(vas_order_num=order.id, del_flag=False)
        charge_out = VasOrderChargeOut.objects.filter(vas_order_num=order.id, del_flag=False)
    else:
        raise ParamError(f'没有此单据类型: {order_type}', ErrorCode.PARAM_ERROR)

    # 收入的币种
    charge_in_currency = charge_in.values("currency_type").distinct().count()
    # 成本的币种
    charge_out_currency = charge_out.values("currency_type").distinct().count()

    income = order_queryset.first().income
    cost = order_queryset.first().cost

    if charge_in_currency == 1 and charge_out_currency == 1 and charge_in.first().currency_type \
            != charge_out.first().currency_type:
        # if settings.MULTI_CURRENCY:
        # 应付和应收币种只有一个并且币种都不同， 计算毛利直接计算
        rate_in = get_exchange_rate(charge_in.first().currency_type)
        rate_out = get_exchange_rate(charge_out.first().currency_type)
        # else:
        #     rate_in = 1
        #     rate_out = 1
        income = 0
        for item in charge_in:
            income += item.charge_total * rate_in
        cost = 0
        for item in charge_out:
            cost += item.charge_total * rate_out
    elif charge_in_currency == 1 and charge_out_currency > 1:
        # 成本币种大于1(成本已换算为CNY)，收入币种等于1并且收入币种不为CNY，重新计算收入
        income = 0
        for item in charge_in:
            if item.currency_type != 'CNY':
                # if settings.MULTI_CURRENCY:
                #     rate_in = get_exchange_rate(item.currency_type)
                # else:
                rate_in = get_exchange_rate(item.currency_type)
                income += item.charge_total * rate_in
            else:
                income += item.charge_total
    elif charge_in_currency > 1 and charge_out_currency == 1:
        # 收入币种大于1(收入已换算为CNY)，成本币种等于1并且成本币种不为CNY，重新计算成本
        cost = 0
        for item in charge_out:
            if item.currency_type != 'CNY':
                # if settings.MULTI_CURRENCY:
                #     rate_out = get_exchange_rate(item.currency_type)
                # else:
                rate_out = get_exchange_rate(item.currency_type)
                cost += item.charge_total * rate_out
            else:
                cost += item.charge_total
    if charge_in_currency == 1 and charge_out_currency == 1 and charge_in.first().currency_type == \
            charge_out.first().currency_type and settings.MULTI_CURRENCY:
        gross_currency = charge_out.first().currency_type
    elif charge_out_currency == 1 and settings.MULTI_CURRENCY:
        gross_currency = charge_out.first().currency_type
    else:
        gross_currency = 'CNY'
    gross_profit = income - cost

    update_params = {
        'income': income,
        'cost': cost,
        'gross_profit': gross_profit,
        'gross_currency': gross_currency,
        # 'account_time': get_and_check_time(account_time, order_type)
    }
    order_queryset.update(**update_params)


# 收入确认和成本确认后，更新订单收入、成本、毛利
@transaction.atomic
def update_order_gross_profit(currency_orders, order_type, charge_queryset, operation_type='revenue', time=None):
    logger.info('start update order gross profit ..... :' + operation_type)
    # 重新获取order
    # if order_type == 'ParcelCustomerOrder':
    #     order_update_filter = ParcelCustomerOrder.objects.filter(id=id)
    # elif order_type == 'CustomerOrder':
    #     order_update_filter = CustomerOrder.objects.filter(id=id)
    # elif order_type == 'Clearance':
    #     order_update_filter = Clearance.objects.filter(id=id)
    # elif order_type == 'Outbound':
    #     order_update_filter = WarehouseOutboundOrder.objects.filter(id=id)
    # elif order_type == 'Inbound':
    #     order_update_filter = WarehouseInboundOrder.objects.filter(id=id)
    # elif order_type == 'CustomsClearanceOrder':
    #     order_update_filter = CustomsClearanceOrder.objects.filter(id=id)
    # currency_order = order_update_filter.first()
    currency_order = currency_orders.first()

    logger.info(f'start update order gross profit currency_order {currency_order}')

    # 获取收入或者成本的金额、特别注意，小包单和运输单的成本明细需要把分摊的费用也计入
    amount = get_amount_form_charge(charge_queryset)

    logger.info(f'start update order gross profit amount {amount}')

    if operation_type == 'revenue':
        # 收入确认
        # if currency_order.is_cost_lock:
        #     order_status = 'FC'
        # else:
        #     if order_type == 'Clearance' or order_type == 'CustomsClearanceOrder':
        #         order_status = currency_order.clear_status
        #     else:
        #         order_status = currency_order.order_status
        update_params = {
            'income': amount,
            'is_revenue_lock': True,
            # 'order_status': order_status,
            'account_time': time
        }
    else:
        # 成本确认
        # if currency_order.is_revenue_lock:
        #     order_status = 'FC'
        # else:
        #     if order_type == 'Clearance' or order_type == 'CustomsClearanceOrder':
        #         order_status = currency_order.clear_status
        #     else:
        #         order_status = currency_order.order_status
        update_params = {
            'cost': amount,
            'is_cost_lock': True,
            # 'order_status': order_status,
            'pay_account_time': time
        }
    if order_type == 'Clearance' or order_type == 'CustomsClearanceOrder':
        old_status = currency_order.clear_status
        # update_params.pop('order_status')
        # update_params['clear_status'] = order_status
        update_params['account_time'] = datetime.now().strftime("%Y-%m-%d")
    # 更新订单的状态、收入、成本、
    currency_orders.update(**update_params)
    # 更新后获取最新实例
    currency_order.refresh_from_db()
    if hasattr(currency_order, 'is_revenue_lock'):
        logger.info(f'currency_order {currency_order} is_revenue_lock: {currency_order.is_revenue_lock}')
    if hasattr(currency_order, 'is_cost_lock'):
        logger.info(f'currency_order {currency_order} is_cost_lock: {currency_order.is_cost_lock}')
    # 重新更新收入、成本、毛利、毛利币种
    # if order_status == 'FC':
    if ((operation_type == 'cost' and hasattr(currency_order, 'is_revenue_lock') and currency_order.is_revenue_lock)
            or (operation_type == 'revenue' and hasattr(currency_order,
                                                        'is_cost_lock') and currency_order.is_cost_lock)):
        check_gross_correct(currency_order, order_type)
    # 进口报关单得完成还需要加上是否有ASCAN时间，有才变成完成FC
    # if order_type == 'Clearance' and currency_order.delivery_finish_date is None:
    #     currency_order.clear_status = old_status
    logger.info('end update order gross profit ..... :' + operation_type)


# 获取账单日期记账日期并进行校验所在月份是否锁定
@transaction.atomic
def get_and_check_time(time, order_type=None):
    if not time:
        err_tip = '订单类型未知'
        if order_type == 'ParcelCustomerOrder':
            err_tip = '请填写下单日期!'
        elif order_type == 'CustomerOrder':
            err_tip = '请填写实际到货日期!'
        elif order_type == 'Clearance':
            err_tip = '请填写操作日期!'
        elif order_type == 'Outbound':
            err_tip = '请填写下单日期!'
        elif order_type == 'Inbound':
            err_tip = '请填写到货日期!'
        elif order_type == 'OMSReturnOrder':
            err_tip = '请填写到货日期!'
        elif order_type == 'CustomsClearanceOrder':
            err_tip = '请填写操作日期!'
        elif order_type == 'MasterOrder':
            err_tip = '请填写实际离港日期!'
        elif order_type == 'OceanOrder':
            err_tip = '请填写实际离港日期!'
        elif order_type == 'CollectOrder':
            err_tip = '请填写创建日期!'
        elif order_type == 'ClearanceOut':
            err_tip = '请填写创建日期!'
        elif order_type == 'TruckOrder':
            err_tip = '请填写创建日期!'
        elif order_type == 'VasOrder':
            err_tip = '请填写创建日期!'
        raise ParamError(err_tip, ErrorCode.PARAM_ERROR)
    flag = True
    lock_moth = AccountingInterval.objects.filter(del_flag=False, is_lock=True)
    transfer_time = datetime.date(datetime.strptime(time.strftime('%Y-%m-%d'), '%Y-%m-%d'))
    for item in lock_moth:
        if item.start_month <= transfer_time <= item.end_month:
            flag = False
    if flag:
        verify_time = time
    else:
        verify_time = datetime.now().strftime("%Y-%m-%d")
    return verify_time


# 获取传入日期是否锁定
@transaction.atomic
def is_lock_time(time):
    if not time:
        return False
    flag = False
    lock_moth = AccountingInterval.objects.filter(del_flag=False, is_lock=True)
    transfer_time = datetime.date(datetime.strptime(time.strftime('%Y-%m-%d'), '%Y-%m-%d'))
    for item in lock_moth:
        if item.start_month <= transfer_time <= item.end_month:
            flag = True
    if flag:
        # 锁定了
        return True
    else:
        # 锁定了
        return False


# 判断传入需要解锁的订单是否已经锁帐
@transaction.atomic
def is_lock_order(order, type='revenue'):
    if type == 'revenue':
        time = order.account_time
    else:
        time = order.pay_account_time
    if time is not None:
        flag = is_lock_time(order.account_time)
        if flag:
            try:
                order_num = order.order_num
            except:
                order_num = order.clearance_num
            raise ParamError('该订单号：%s的记账日期已经锁定，无法进行解锁操作！' % order_num, ErrorCode.PARAM_ERROR)


# 获取发票的创建参数
@transaction.atomic
def get_debit_params(order_type, current_order, currency, amount, customer, user):
    params = {
        'customer': customer,
        'amount': amount,
        'create_by': user,
        'update_by': user,
        'create_date': datetime.now(),
        'update_date': datetime.now(),
    }
    # 设置币种
    if settings.MULTI_CURRENCY:
        params['currency'] = currency
    else:
        params['currency'] = settings.CURRENT_CURRENCY
    if order_type == 'ParcelCustomerOrder':
        # 上面默认设置为小包的参数
        params['customer_order'] = current_order.customer_order_num
        if current_order.product and current_order.product.label_type == 'WC':
            params['debit_date'] = current_order.order_time
        else:
            params['debit_date'] = current_order.inbound_time
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['track_num'] = current_order.tracking_num
        params['order_num'] = current_order.order_num
        params['charge_weight'] = current_order.charge_weight
        params['confirm_charge_weight'] = current_order.confirm_charge_weight
    elif order_type == 'CustomerOrder':
        params['customer_order'] = current_order.ref_num
        params['debit_date'] = current_order.actual_arrival_date or current_order.check_in_time
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['track_num'] = current_order.tracking_num
        params['order_num'] = current_order.order_num
        params['charge_weight'] = current_order.charge_weight
        params['confirm_charge_weight'] = current_order.confirm_charge_weight
        params['confirm_volume'] = current_order.confirm_volume
        # 取订单配置的第一个提单的柜号
        relate_ocean_order = CustomerOrderRelateOcean.objects.filter(customer_order_num=current_order,
                                                                     del_flag=False).first()
        if relate_ocean_order:
            params['container_no'] = relate_ocean_order.oceanOrder.container_no
    elif order_type == 'Clearance':
        if current_order.master_order_num:
            track_num = current_order.master_order_num
        elif current_order.ocean_order_num:
            track_num = current_order.ocean_order_num
        else:
            track_num = current_order.clearance_num
        params['customer_order'] = current_order.clearance_num
        params['debit_date'] = current_order.opera_date
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['track_num'] = track_num
        params['order_num'] = current_order.clearance_num
    elif order_type == 'Outbound':
        params['customer_order'] = current_order.customer_order_num
        params['debit_date'] = current_order.order_time
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['track_num'] = current_order.tracking_num
        params['order_num'] = current_order.order_num
    elif order_type == 'Inbound':
        params['customer_order'] = current_order.ref_num
        params['debit_date'] = current_order.arrival_date
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['track_num'] = current_order.tracking_num
        params['order_num'] = current_order.order_num
    elif order_type == 'OutboundTransfer':
        params['customer_order'] = current_order.customer_order_num
        params['debit_date'] = current_order.order_time
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        # params['track_num'] = current_order.tracking_num
        params['order_num'] = current_order.order_num
    elif order_type == 'InboundTransfer':
        params['customer_order'] = current_order.ref_num
        params['debit_date'] = current_order.arrival_date
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        # params['track_num'] = current_order.tracking_num
        params['order_num'] = current_order.order_num
    elif order_type == 'OMSReturnOrder':
        params['customer_order'] = current_order.customer_order_num
        params['debit_date'] = current_order.actual_arrival_date or current_order.estimated_arrival_date
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['track_num'] = current_order.tracking_num
        params['order_num'] = current_order.order_num
    elif order_type == 'CustomsClearanceOrder':
        params['customer_order'] = current_order.order_num
        params['debit_date'] = current_order.clearance_date
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['track_num'] = current_order.mawb
        params['mawb'] = current_order.mawb
        params['order_num'] = current_order.order_num
    elif order_type == 'CollectOrder':
        params['customer_order'] = current_order.order_num
        params['debit_date'] = current_order.create_date
        params['product_code'] = None
        params['product'] = None
        params['track_num'] = None
        params['order_num'] = current_order.order_num
    # 获取检验后的开票时间
    params['debit_date'] = get_and_check_time(params['debit_date'], order_type)
    return params


# 获取收款明细的创建参数
@transaction.atomic
def get_receivable_params(order_type, current_order, charge_in, debit, user):
    params = {
        'customer': debit.customer,
        'charge_name': charge_in.charge,
        'origin_amount': charge_in.charge_total,
        'origin_balance': charge_in.charge_total,
        'origin_currency': charge_in.currency_type,
        'account_amount': charge_in.account_charge,
        'account_balance': charge_in.account_charge,
        # 额外添加设置字段
        'charge_rate': charge_in.charge_rate,
        'charge_count': charge_in.charge_count,
        'current_exchange': charge_in.current_exchange,
        'debit_num': debit,
        'create_by': user,
        'update_by': user,
        'create_date': datetime.now(),
        'update_date': datetime.now(),
        'del_flag': False,
    }
    if order_type == 'ParcelCustomerOrder':
        # 默认设置为小包的参数
        params['order_num'] = current_order.order_num
        params['customer_orderNum'] = current_order.customer_order_num
        params['product_code'] = current_order.product.name
        product = current_order.product
        params['product'] = product
        # 账单中需要从订单上同步的数据
        params['tracking_num'] = current_order.tracking_num
        params['buyer_postcode'] = current_order.buyer_postcode
        params['carton'] = 1
        params['declared_nameCN'] = get_order_declared_name_cn(order_type, current_order)
        params['label_billid'] = current_order.label_billid
        # 记录计费值
        params['charge_value'] = charge_in.charge_value
        params['charge_weight'] = charge_in.charge_weight
        if product.label_type in ['HW', 'ZW', 'WWWC']:
            params['account_time'] = current_order.inbound_time
        else:
            params['account_time'] = current_order.order_time

    elif order_type == 'CustomerOrder':
        params['order_num'] = current_order.order_num
        params['customer_orderNum'] = current_order.ref_num
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        if settings.SYSTEM_ORDER_MARK in ['CLT', 'FX']:
            params['account_time'] = current_order.actual_arrival_date or current_order.check_in_time
        else:
            params['account_time'] = current_order.actual_arrival_date
        params['warehouse_code'] = current_order.receiver.address_num if current_order.receiver else \
            current_order.buyer_address_num
        params['country_code'] = current_order.receiver.country_code if current_order.receiver else \
            current_order.buyer_country_code
        params['carton'] = current_order.carton
        params['buyer_postcode'] = current_order.receiver.postcode if current_order.receiver else None
        params['declared_nameCN'] = get_order_declared_name_cn(order_type, current_order)
        params['collect_order_num'] = current_order.collect_num.order_num if current_order.collect_num else None
    elif order_type == 'Clearance':
        if current_order.master_order_num:
            track_num = current_order.master_order_num
        elif current_order.ocean_order_num:
            track_num = current_order.ocean_order_num
        else:
            track_num = current_order.clearance_num
        params['order_num'] = current_order.clearance_num
        params['customer_orderNum'] = track_num
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['account_time'] = current_order.opera_date
    elif order_type == 'Outbound':
        # 出库订单
        params['order_num'] = current_order.order_num
        params['customer_orderNum'] = current_order.customer_order_num
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['account_time'] = current_order.order_time
        params['is_rebate_order'] = charge_in.is_rebate_order
        params['owe_mark'] = charge_in.owe_mark
    elif order_type == 'Inbound':
        # 入库订单
        params['order_num'] = current_order.order_num
        params['customer_orderNum'] = current_order.ref_num
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['account_time'] = current_order.arrival_date
    elif order_type == 'OutboundTransfer':
        # 出库订单
        params['order_num'] = current_order.order_num
        params['customer_orderNum'] = current_order.customer_order_num
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['account_time'] = current_order.order_time
        params['is_rebate_order'] = charge_in.is_rebate_order
        params['owe_mark'] = charge_in.owe_mark
    elif order_type == 'InboundTransfer':
        # 入库订单
        params['order_num'] = current_order.order_num
        params['customer_orderNum'] = current_order.ref_num
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['account_time'] = current_order.arrival_date
    elif order_type == 'OMSReturnOrder':
        # oms退货单
        params['order_num'] = current_order.order_num
        params['customer_orderNum'] = current_order.customer_order_num
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['account_time'] = current_order.actual_arrival_date or current_order.estimated_arrival_date

    elif order_type == 'CustomsClearanceOrder':
        # 清关订单
        params['order_num'] = current_order.order_num
        params['customer_orderNum'] = current_order.mawb
        params['product_code'] = current_order.product.name
        params['product'] = current_order.product
        params['account_time'] = current_order.clearance_date
    elif order_type == 'CollectOrder':
        params['order_num'] = current_order.order_num
        params['customer_orderNum'] = current_order.order_num
        params['product_code'] = None
        params['product'] = None
        params['account_time'] = current_order.create_date
        params['collect_order_num'] = current_order.order_num
    # 获取检验后的开票时间
    params['account_time'] = get_and_check_time(params['account_time'], order_type)
    return params


# 获取付款明细的创建参数
@transaction.atomic
def get_payable_params(order_type, current_order, charge_out, user):
    if not charge_out.supplier:
        raise ParamError(f'成本没有选择供应商: {charge_out.charge}', ErrorCode.PARAM_ERROR)
    params = {
        'order_num': getattr(current_order, 'order_num', None),
        'charge_name': charge_out.charge,
        'charge_fee_id': charge_out.id,
        'supplier': charge_out.supplier,
        'origin_amount': charge_out.charge_total,
        'account_amount': charge_out.account_charge,
        'origin_balance': charge_out.charge_total,
        'account_balance': charge_out.account_charge,
        'origin_currency': charge_out.currency_type,
        'create_by': user,
        'update_by': user,
        'create_date': datetime.now(),
        'update_date': datetime.now(),
        # 'product': current_order.product,
        'account_time': getattr(current_order, 'create_date', None),
        'pieces': getattr(current_order, 'carton', 0) or 0
    }
    # 根据 order_type 动态添加 weight 字段
    if order_type != "VasOrder":
        params['weight'] = current_order.weight or 0

    if order_type == 'ParcelCustomerOrder':
        # 默默人设置为小包的参数
        params['third_orderNo'] = current_order.third_orderNo
        params['customer_orderNum'] = current_order.customer_order_num
        params['track_num'] = current_order.tracking_num
        params['product'] = current_order.product
        params['account_time'] = current_order.order_time
        params['pieces'] = 1
        # 添加分区
        params['zone_value'] = current_order.zone_value
    elif order_type == 'CustomerOrder':
        params['third_orderNo'] = current_order.third_orderNo
        params['customer_orderNum'] = current_order.ref_num
        params['track_num'] = current_order.tracking_num
        params['product'] = current_order.product
        if settings.SYSTEM_ORDER_MARK in ['CLT']:
            params['account_time'] = current_order.actual_arrival_date or current_order.check_in_time
        else:
            params['account_time'] = current_order.actual_arrival_date
        # 添加分区
        params['zone_value'] = current_order.zone_value
    elif order_type == 'Clearance':
        # current_order = Clearance.objects.get(id=1)
        if current_order.master_order_num:
            customer_order_num = current_order.master_order_num
        elif current_order.ocean_order_num:
            customer_order_num = current_order.ocean_order_num
        else:
            customer_order_num = current_order.clearance_num
        params['customer_orderNum'] = customer_order_num
        params['track_num'] = current_order.container_no or customer_order_num
        params['order_num'] = current_order.clearance_num
        params['product'] = current_order.product
        params['account_time'] = current_order.opera_date
        params['container_no'] = current_order.container_no
        params['ocean_order_num'] = current_order.ocean_order_num
    elif order_type == 'Outbound':
        params['third_orderNo'] = current_order.third_orderNo
        params['customer_orderNum'] = current_order.customer_order_num
        params['track_num'] = current_order.tracking_num
        params['product'] = current_order.product
        params['account_time'] = current_order.order_time
        params['pieces'] = 1
        # 添加分区
        params['zone_value'] = current_order.zone_value
    elif order_type == 'Inbound':
        params['third_orderNo'] = current_order.third_orderNo
        params['customer_orderNum'] = current_order.ref_num
        params['track_num'] = current_order.tracking_num
        params['product'] = current_order.product
        params['account_time'] = current_order.arrival_date
        # 添加分区
        params['zone_value'] = current_order.zone_value
    elif order_type == 'OMSReturnOrder':
        params['third_orderNo'] = current_order.third_orderNo
        params['customer_orderNum'] = current_order.customer_order_num
        params['track_num'] = current_order.tracking_num
        params['product'] = current_order.product
        params['account_time'] = current_order.actual_arrival_date or current_order.estimated_arrival_date
        # 添加分区
        params['zone_value'] = current_order.zone_value
    elif order_type == 'CustomsClearanceOrder':
        params['third_orderNo'] = current_order.container_no
        params['customer_orderNum'] = current_order.mawb
        params['track_num'] = current_order.container_no
        params['mawb'] = current_order.mawb
        params['product'] = current_order.product
        params['account_time'] = current_order.clearance_date
        # params['zone_value'] = current_order.zone_value
        params['container_no'] = current_order.container_no
    elif order_type == 'TruckOrder':
        params['order_num'] = current_order.truck_order_num
        # params['product'] = current_order.product
        params['account_time'] = getattr(current_order, 'create_date', None)
        params['ocean_order_num'] = current_order.ocean_order_num
        ocean_orders = OceanOrder.objects.filter(order_num=current_order.ocean_order_num, del_flag=False)
        if ocean_orders:
            params['container_no'] = ocean_orders.first().container_no
    elif order_type == 'OceanOrder':
        params['container_no'] = current_order.container_no
        params['ocean_order_num'] = current_order.order_num
        params['account_time'] = current_order.actual_leave_date
    elif order_type == 'VasOrder':
        params['order_num'] = current_order.vas_order_num
        params['account_time'] = getattr(current_order, 'create_date', None)

    # 获取检验后的开票时间
    params['account_time'] = get_and_check_time(params['account_time'], order_type)
    return params


# 避免数据脏读, 重新获取订单
def get_order_by_id(primary_key, order_type):
    logger.info('start get_order_by_id....')
    first_order = None
    currency_order_filter = None
    if order_type == 'ParcelCustomerOrder':
        # 小包订单
        currency_order_filter = ParcelCustomerOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'CustomerOrder':
        # 客户订单
        currency_order_filter = CustomerOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'Clearance':
        # 进口报关单
        currency_order_filter = Clearance.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'Outbound':
        # 出库订单
        currency_order_filter = WarehouseOutboundOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'Inbound':
        # 入库订单
        currency_order_filter = WarehouseInboundOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'OMSReturnOrder':
        # oms 退货单
        currency_order_filter = ReturnOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'CustomsClearanceOrder':
        # 清关订单
        currency_order_filter = CustomsClearanceOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'MasterOrder':
        currency_order_filter = MasterOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'OceanOrder':
        currency_order_filter = OceanOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'CollectOrder':
        currency_order_filter = CollectOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'ClearanceOut':
        currency_order_filter = ClearanceOut.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
    elif order_type == 'TruckOrder':
        currency_order_filter = TruckOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()

    logger.info('end get get_order_by_id....')
    return first_order, currency_order_filter


# 获取需要成本收入确认的订单以及订单下面的收入明细或者成本明细
@transaction.atomic
def get_order_and_queryset(primary_key, order_type, operation_type='revenue'):
    logger.info('start get order and queryset....')
    currency_order_filter = None
    charge_queryset_share = None
    charge_queryset = None
    if order_type == 'ParcelCustomerOrder':
        # 小包订单
        currency_order_filter = ParcelCustomerOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = ParcelOrderChargeIn.objects.filter(customer_order_num=first_order.id,
                                                                 del_flag=False)
            charge_queryset_share = charge_queryset
        else:
            charge_queryset = ParcelOrderChargeOut.objects.filter(del_flag=False, customer_order_num=first_order.id)
            charge_queryset_share = charge_queryset.filter(is_share=False)
    elif order_type == 'CustomerOrder':
        # 客户订单
        currency_order_filter = CustomerOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = CustomerOrderChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
            charge_queryset_share = charge_queryset
        else:
            charge_queryset = CustomerOrderChargeOut.objects.filter(del_flag=False, customer_order_num=first_order.id)
            charge_queryset_share = charge_queryset.filter(is_share=False)
    elif order_type == 'Clearance':
        # 进口报关单
        currency_order_filter = Clearance.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = ClearanceChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)

        else:
            charge_queryset = ClearanceChargeOut.objects.filter(del_flag=False, customer_order_num=first_order.id)
        charge_queryset_share = charge_queryset
    elif order_type == 'Outbound':
        # 出库订单
        currency_order_filter = WarehouseOutboundOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = OutboundOrderChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
        else:
            charge_queryset = OutboundOrderChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'Inbound':
        # 入库订单
        currency_order_filter = WarehouseInboundOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = InboundOrderChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
        else:
            charge_queryset = InboundOrderChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'OutboundTransfer':
        # 出库订单
        currency_order_filter = WarehouseOutboundTransferOrder.objects.filter(id=primary_key, del_flag=False)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = OutboundTransferChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
        else:
            charge_queryset = OutboundTransferChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'InboundTransfer':
        # 入库订单
        currency_order_filter = WarehouseInboundTransferOrder.objects.filter(id=primary_key, del_flag=False)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = InboundTransferChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
        else:
            charge_queryset = InboundTransferChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'OutboundTransfer':
        # 出库订单
        currency_order_filter = WarehouseOutboundTransferOrder.objects.filter(id=primary_key, del_flag=False)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = OutboundTransferChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
        else:
            charge_queryset = OutboundTransferChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'InboundTransfer':
        # 入库订单
        currency_order_filter = WarehouseInboundTransferOrder.objects.filter(id=primary_key, del_flag=False)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = InboundTransferChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
        else:
            charge_queryset = InboundTransferChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'OMSReturnOrder':
        # oms 退货单
        currency_order_filter = ReturnOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = ReturnOrderChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
            # 如果存在重派费但订单未重派,则排除重派费用
            if not first_order.is_redispatch:
                charge_queryset = charge_queryset.exclude(charge__name__contains='重派费')
                
        else:
            charge_queryset = ReturnOrderChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
            # 如果存在销毁费但订单未销毁,则排除销毁费用  
            if not first_order.is_destroy:
                charge_queryset = charge_queryset.exclude(charge__name__contains='销毁费')

        charge_queryset_share = charge_queryset
    elif order_type == 'CustomsClearanceOrder':
        # 清关订单
        currency_order_filter = CustomsClearanceOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = CustomsClearanceOrderChargeIn.objects.filter(customer_order_num=first_order.id,
                                                                           del_flag=False)
        else:
            charge_queryset = CustomsClearanceOrderChargeOut.objects.filter(customer_order_num=first_order.id,
                                                                            del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'MasterOrder':
        currency_order_filter = MasterOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = MasterOrderChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
        else:
            charge_queryset = MasterOrderChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'OceanOrder':
        currency_order_filter = OceanOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            raise ParamError('海运单没有收入确认！', ErrorCode.PARAM_ERROR)
        else:
            charge_queryset = OceanOrderChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'CollectOrder':
        currency_order_filter = CollectOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            charge_queryset = CollectOrderChargeIn.objects.filter(customer_order_num=first_order.id, del_flag=False)
        else:
            charge_queryset = CollectOrderChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'ClearanceOut':
        currency_order_filter = ClearanceOut.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            raise ParamError('出口报关单没有收入确认！', ErrorCode.PARAM_ERROR)
        else:
            charge_queryset = ClearanceOutChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'TruckOrder':
        currency_order_filter = TruckOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            raise ParamError('卡派单没有收入确认！', ErrorCode.PARAM_ERROR)
        else:
            charge_queryset = TruckOrderChargeOut.objects.filter(customer_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset
    elif order_type == 'VasOrder':
        currency_order_filter = VasOrder.objects.filter(id=primary_key)
        first_order = currency_order_filter.first()
        if operation_type == 'revenue':
            raise ParamError('增值服务单没有收入确认！', ErrorCode.PARAM_ERROR)
        else:
            charge_queryset = VasOrderChargeOut.objects.filter(vas_order_num=first_order.id, del_flag=False)
        charge_queryset_share = charge_queryset

    logger.info('end get order and queryset....')
    return [currency_order_filter, charge_queryset_share, charge_queryset]


# 发送单个附件邮件
def send_file(subject, content, to_mails, filename):
    from_email = settings.EMAIL_HOST_USER
    msg = EmailMultiAlternatives(subject, content, from_email, to_mails)
    msg.attach_file(filename)
    msg.send()


# 发送多个附件邮件
def send_multiple_file(subject, content, to_mails, filename):
    from_email = settings.EMAIL_HOST_USER
    msg = EmailMultiAlternatives(subject, content, from_email, to_mails)
    for file in filename:
        msg.attach_file(file)
    msg.send()


# 发送文本类型的邮件
def send_str_email(subject, content, to_mails, html_content=None):
    from_email = settings.EMAIL_HOST_USER
    msg = EmailMultiAlternatives(subject, content, from_email, to_mails)
    if html_content:
        msg.attach_alternative(html_content, "text/html")

    msg.send()

def send_email_with_attach(subject, content, to_mails, attach_body, attach_file_name=None):
    """
    直接转换base64编码作为附件发送邮件
    :param subject: 主题
    :param content: 内容
    :param to_mails: 收件人 list
    :param attach_body: base64.b64decode(xxxxxxxxx)
    :param attach_file_name: 附件文件名  eg: label.pdf
    :return:
    """
    from_email = settings.EMAIL_HOST_USER
    email = EmailMessage(
        subject=subject,
        body=content,
        from_email=from_email,
        to=to_mails,
    )
    email.attach(attach_file_name, content=attach_body, mimetype='application/octet-stream')
    email.send()


# 公共添加轨迹方法, 若是修改订单轨迹, 则同时会修改订单状态
@transaction.atomic
def set_customer_track(order_type, date, id, code, qty, request):
    # 空运主单号、航班号、海运提单号、柜号
    master_order_num = ''
    airline_num = ''
    ocean_order_num = ''
    container_no = ''
    clear_type = None
    if not qty or (qty.isdecimal() and not int(qty) and order_type != 'PC'):
        raise ParamError('件数需要为大于0的数字, 请填写件数！', ErrorCode.PARAM_ERROR)
    product = None
    if order_type == 'TR':
        model_class = 'CustomerOrder'
        order = CustomerOrder.objects.get(id=id)
        order_num = order.order_num
        product = order.product
        expected_qty = order.pre_carton
        # 通过轨迹代码修改订单状态
        change_order_status(order, code, user=request.user, qty=qty)

    elif order_type == 'PC':
        model_class = 'ParcelCustomerOrder'
        order = ParcelCustomerOrder.objects.get(id=id)
        order_num = order.order_num
        product = order.product
        expected_qty = 1
        qty = 1
    elif order_type == 'CL':
        model_class = 'Clearance'
        order = Clearance.objects.get(id=id)
        order_num = order.clearance_num
        product = order.product
        expected_qty = order.carton
        clear_type = order.clear_type
        if clear_type == 'MS':
            master_order_num = order.master_order_num
            airline_num = order.airline_num
        else:
            ocean_order_num = order.ocean_order_num
            container_no = order.container_no
    elif order_type == 'CC':
        model_class = 'CustomsClearanceOrder'
        order = CustomsClearanceOrder.objects.get(id=id)
        order_num = order.clearance_num
        product = order.product
        expected_qty = order.carton
        clear_type = order.clear_type
        if clear_type == 'MS':
            master_order_num = order.master_order_num
            airline_num = order.airline_num
        else:
            ocean_order_num = order.ocean_order_num
            container_no = order.container_no
    # elif order_type == 'OW':
    #     pass
    else:
        model_class = 'CustomerOrder'
        order = CustomerOrder.objects.get(id=id)
        order_num = order.order_num
        expected_qty = order.carton

    # 获取当前配置的产品轨迹code
    product_codes = ProductTrackCode.objects.filter(product=product, del_flag=False, track_code__code=code)
    if not product_codes:
        raise ParamError('该产品未配置当前轨迹代码，请检查！', ErrorCode.PARAM_ERROR)
    product_code = product_codes.first()

    # 获取该订单已经产生的最后一条轨迹和对应的产品轨迹
    last_track = Track.objects.filter(order_num=order_num, del_flag=False).last()
    if last_track is not None and last_track.track_code == 'FC':
        raise ParamError('订单已经完成，不可操作！', ErrorCode.PARAM_ERROR)

    # 判断是否当前轨迹code是否第一次设置
    current_code_track = Track.objects.filter(order_id=id, track_code=code, del_flag=False)
    if current_code_track.count() != 0:
        expected_time = current_code_track.first().expected_time
    else:
        expected_time = (last_track.actual_time + timedelta(days=product_code.aging)).strftime('%Y-%m-%d') \
            if last_track else None

    track_location, track_remark = get_track_location_remark(order, code)
    node_address = request.data.get('nodeAddress', {})
    address_name_en = node_address.get('address_name_en')
    logger.info(f'订单轨迹更改address_name_en1: {address_name_en}')

    params = {
        'order_num': order_num,
        'order_id': id,
        'track_code': product_code.track_code.code,
        'track_name': product_code.track_code.name,
        'expected_time': expected_time,
        'actual_time': date,
        'product': product,
        'qty': qty,
        'expected_qty': expected_qty,
        'master_order_num': master_order_num,
        'airline_num': airline_num,
        'ocean_order_num': ocean_order_num,
        'container_no': container_no,
        'clear_type': clear_type,
        'remark': track_remark,
        'location': track_location,
        'location_en': address_name_en,
    }
    Track.objects.create(**params, **get_update_params(request, True))
    user = UserProfile()
    user.id = 1
    # 发送wx消息
    if order_type == 'TR' and order_num.startswith('BSID'):
        save_push_message_bw(order.customer.id, '客户', order_num, product_code.track_code.name,
                             order.ref_num, product.name, '', order.pre_carton, order.pre_weight, order.pre_volume,
                             user)

    if order_type == 'PC' and order_num.startswith('CQJYD'):
        push_track(order.customer_order_num, order.tracking_num, product_code.track_code.name,
                   product_code.track_code.code)
    logger.info(f'1修改公共单轨迹{order_num}由{last_track.track_code if last_track else None}改为{code}')


# 公共添加轨迹方法,无产品
@transaction.atomic
def set_customer_track2(order_type, date, id, qty, request, code, name):
    # 空运主单号、航班号、海运提单号、柜号
    master_order_num = ''
    airline_num = ''
    ocean_order_num = ''
    container_no = ''
    clear_type = None
    if order_type == 'TR':
        order = CustomerOrder.objects.get(id=id)
        order_num = order.order_num
        product = order.product
        expected_qty = order.pre_carton
    elif order_type == 'PC':
        order = ParcelCustomerOrder.objects.get(id=id)
        order_num = order.order_num
        product = order.product
        expected_qty = 1
        qty = 1
    elif order_type == 'CL':
        order = Clearance.objects.get(id=id)
        order_num = order.clearance_num
        product = order.product
        expected_qty = order.carton
        clear_type = order.clear_type
        if clear_type == 'MS':
            master_order_num = order.master_order_num
            airline_num = order.airline_num
        else:
            ocean_order_num = order.ocean_order_num
            container_no = order.container_no
    elif order_type == 'CC':
        order = CustomsClearanceOrder.objects.get(id=id)
        order_num = order.clearance_num
        product = order.product
        expected_qty = order.carton
        clear_type = order.clear_type
        if clear_type == 'MS':
            master_order_num = order.master_order_num
            airline_num = order.airline_num
        else:
            ocean_order_num = order.ocean_order_num
            container_no = order.container_no
    # elif order_type == 'OW':
    #     pass
    else:
        order = CustomerOrder.objects.get(id=id)
        order_num = order.order_num
        product = order.product
        expected_qty = order.carton

    # 获取该订单已经产生的最后一条轨迹和对应的产品轨迹
    last_track = Track.objects.filter(order_num=order_num, del_flag=False).last()
    if last_track is not None and last_track.track_code == 'FC':
        raise ParamError('订单已经完成，不可操作！', ErrorCode.PARAM_ERROR)

    # 判断是否当前轨迹code是否第一次设置
    current_code_track = Track.objects.filter(order_id=id, track_code=code, del_flag=False)
    if current_code_track.count() != 0:
        expected_time = current_code_track.first().expected_time
    else:
        expected_time = datetime.now()

    track_location, track_remark = get_track_location_remark(order, code)
    node_address = request.data.get('nodeAddress', {})
    address_name_en = node_address.get('address_name_en')
    logger.info(f'订单轨迹更改address_name_en2: {address_name_en}')

    params = {
        'order_num': order_num,
        'order_id': id,
        'track_code': code,
        'track_name': name,
        'expected_time': expected_time,
        'actual_time': date,
        'product': product,
        'qty': qty,
        'expected_qty': expected_qty,
        'master_order_num': master_order_num,
        'airline_num': airline_num,
        'ocean_order_num': ocean_order_num,
        'container_no': container_no,
        'clear_type': clear_type,
        'location': track_location,
        'location_en': address_name_en,
    }
    Track.objects.create(**get_update_params(request, True), **params)
    user = UserProfile()
    user.id = 1
    # 发送wx消息
    if order_type == 'TR' and order_num.startswith('BSID'):
        save_push_message_bw(order.customer.id, '客户', order_num, name,
                             order.ref_num, product.name, '', order.pre_carton, order.pre_weight, order.pre_volume,
                             user)

    if order_type == 'PC' and order_num.startswith('CQJYD'):
        push_track(order.customer_order_num, order.tracking_num, name, code)
    logger.info(f'2修改公共单{order_num}轨迹由{last_track.track_code if last_track else None}改为{code}')


# 添加小包轨迹方法
@transaction.atomic
def set_parcel_track(parcel_order_id, code, date, user, location=None, courier_code='', remark='', is_concat_remark=False):
    order = ParcelCustomerOrder.objects.get(id=parcel_order_id)
    order_num = order.order_num

    # 判断是否当前轨迹code是否第一次设置
    current_code_track = ParcelTrack.objects.filter(order_num=order_num, system_code=code, del_flag=False)
    if current_code_track.exists():
        return

    if settings.SYSTEM_MARK in ['MZ']:

        track_code = TrackCode.objects.filter(code=code, del_flag=False).first()
        if not track_code:
            raise ParamError(f'未配置系统轨迹:{code},请检查！', ErrorCode.PARAM_ERROR)

        track_location, track_remark = get_track_location_remark(order, code)

        params = {
            'order_num': order_num,
            'system_code': track_code.system_code or '',
            'track_code': track_code.code,
            'track_sub_code': track_code.sub_code or '',
            'track_name': track_code.name or '',
            'actual_time': date,
            'push_status': 'WAIT_PUSH',
            'location': track_location,
            'remark': track_remark,
            'mode_key': random.randint(1, 2),
            'courier_code': courier_code,
        }
        ParcelTrack.objects.create(**params, **get_update_params_by_user(user, True))

    else:

        # 查询轨迹配置表
        track_code_all_queryset = TrackCode.objects.filter(affiliated_track='T', del_flag=False)
        if not track_code_all_queryset.exists():
            raise ParamError(f'未配置轨迹配置,请检查', ErrorCode.PARAM_ERROR)

        # 配置轨迹配置
        track_code_queryset = track_code_all_queryset.filter(system_code=code)
        if not track_code_queryset.exists():
            raise ParamError(f'未配置系统轨迹:{code},请检查！', ErrorCode.PARAM_ERROR)
        track_code = track_code_queryset.first()

        parcel_track_queryset = ParcelTrack.objects.filter(order_num=order_num, del_flag=False).order_by('actual_time')
        for parcel_track in parcel_track_queryset:
            other_track_code = track_code_all_queryset.filter(system_code=parcel_track.system_code).first()
            if isinstance(date, str):
                date = date.replace("T", " ")
                actual_time = datetime.strptime(date, "%Y-%m-%d %H:%M:%S")
            else:
                actual_time = date

            if other_track_code and other_track_code.sort and track_code.sort and \
                    ((other_track_code.sort < track_code.sort and parcel_track.actual_time > actual_time) or
                     (other_track_code.sort > track_code.sort and parcel_track.actual_time < actual_time)):
                raise ParamError(
                    f'订单{parcel_track.order_num}轨迹顺序时间有误{track_code.name}时间{date}后于{parcel_track.track_name}的时间{parcel_track.actual_time}',
                    ErrorCode.PARAM_ERROR)
        if is_concat_remark:
            track_remark = track_code.nameEn + ' ' + remark
        else:
            track_remark = track_code.nameEn or remark
        params = {
            'order_num': order_num,
            'system_code': track_code.system_code,
            'track_code': track_code.code,
            'track_sub_code': track_code.sub_code,
            'track_name': track_code.name,
            'actual_time': date,
            'push_status': 'WAIT_PUSH',
            'location': location,
            # 'remark': track_code.nameEn or remark,
            'remark': track_remark,
            'mode_key': random.randint(1, 2),
            'courier_code': courier_code,
        }

        ParcelTrack.objects.create(**params, **get_update_params_by_user(user, True))

        # 加入bi统计
        add_parcel_track_bi(order, date, location, track_code, params['remark'])


@transaction.atomic
def set_multistep_parcel_track(order_num: str, code: str, track_date, user, location: Optional[str] = None,
                               courier_code: str = '', remark: str = '', timezone_offset: Optional[str] = None):
    """
    设置多级轨迹code

    Args:
        order_num: 订单号
        code: 轨迹代码
        track_date: 轨迹时间
        user: 操作用户
        location: 位置信息
        courier_code: 尾程物流商编码
        remark: 备注
        timezone_offset: 时区偏移（例如：'+08:00'、'-05:00'）
    """
    try:
        # 检查轨迹是否已存在
        if _track_already_exists(order_num, code):
            logger.info(f"轨迹代码 {code} 已存在于订单 {order_num}")
            return

        # 3. 解析轨迹代码
        base_code, event_codes = _parse_track_code(code)

        # 4. 验证轨迹配置
        track_code = _validate_and_get_track_config(base_code, event_codes)

        # 6. 验证轨迹时间顺序
        actual_time = _parse_datetime(track_date)
        # _validate_track_time_order(order_num, track_code, actual_time)

        # 7. 创建轨迹记录
        track_params = _build_track_params(
            order_num, track_code, event_codes,
            actual_time, location, courier_code, remark, user, timezone_offset
        )

        track = ParcelTrack.objects.create(**track_params)

        # # 8. 添加BI统计
        # add_parcel_track_bi(order, actual_time, location, track_code, track_params['remark'])

        logger.info(f"成功创建轨迹记录: 订单={order_num}, 代码={code}")
        return track

    except (TrackCodeError, TrackTimeOrderError) as e:
        logger.error(f"轨迹设置失败: {str(e)}")
        # raise ParamError(str(e), ErrorCode.PARAM_ERROR)
    except Exception as e:
        logger.error(f"设置轨迹时发生未知错误: {str(e)}", exc_info=True)
        # raise ParamError(f'设置轨迹失败: {str(e)}', ErrorCode.PARAM_ERROR)

@transaction.atomic
def delete_multistep_parcel_track(order_num: str, code: str, user):
    """
    删除多级轨迹code

    Args:
        order_num: 订单号
        code: 轨迹代码
        user: 操作用户
    """
    try:
        # 检查轨迹是否存在
        if not _track_already_exists(order_num, code):
            logger.info(f"轨迹代码 {code} 不存在于订单 {order_num}")
            return 0

        # 解析轨迹代码
        base_code, event_codes = _parse_track_code(code)

        # 构建完整的轨迹代码（与设置时保持一致）
        if event_codes:
            full_track_code = base_code + '-' + '-'.join(event_codes)
        else:
            full_track_code = code

        # 查找要删除的轨迹记录
        tracks_to_delete = ParcelTrack.objects.filter(
            order_num=order_num,
            track_code=full_track_code,
            del_flag=False
        )

        if not tracks_to_delete.exists():
            logger.warning(f"未找到要删除的轨迹: 订单={order_num}, 代码={code}")
            return 0

        # 执行软删除
        deleted_count = tracks_to_delete.update(
            del_flag=True,
            **get_update_params_by_user(user, False)
        )

        logger.info(f"成功删除轨迹记录: 订单={order_num}, 代码={code}, 删除数量={deleted_count}")
        return deleted_count

    except Exception as e:
        logger.error(f"删除轨迹时发生未知错误: 订单={order_num}, 代码={code}, 错误={str(e)}", exc_info=True)
        return 0


def _track_already_exists(order_num: str, code: str) -> bool:
    """检查轨迹是否已存在"""
    return ParcelTrack.objects.filter(
        order_num=order_num,
        track_code=code,
        del_flag=False
    ).exists()


def _parse_track_code(code: str) -> Tuple[str, List[str]]:
    """
    解析轨迹代码

    Returns:
        (base_code, event_codes): 基础代码和事件代码列表
    """
    parts = str(code).split('-')
    base_code = parts[0]
    event_codes = parts[1:] if len(parts) > 1 else []
    return base_code, event_codes


def _validate_and_get_track_config(base_code: str, event_codes: List[str]) -> 'TrackCode':
    """验证并获取轨迹配置"""
    # 获取所有轨迹配置（优化：一次查询获取所有需要的配置）
    track_configs = TrackCode.objects.filter(
        del_flag=False
    ).select_related()

    # 验证基础轨迹配置
    main_track_configs = track_configs.filter(affiliated_track='T')
    if not main_track_configs.exists():
        raise TrackCodeError('未配置轨迹配置,请检查')

    # 查找匹配的轨迹代码
    track_code = main_track_configs.filter(
        Q(system_code=base_code) | Q(code=base_code)
    ).first()

    if not track_code:
        raise TrackCodeError(f'未配置系统轨迹:{base_code},请检查！')

    # 验证事件代码
    if event_codes:
        _validate_event_codes(track_configs, event_codes)

    return track_code


def _validate_event_codes(track_configs, event_codes: List[str]):
    """验证事件代码"""
    if not event_codes:
        return

    # 验证节点状态
    node_status = track_configs.filter(
        affiliated_track='ONS',
        code=event_codes[0]
    ).first()

    if not node_status:
        raise TrackCodeError(f'轨迹节点状态{event_codes[0]}未配置,请检查')

    # 验证异常事件（如果存在多个事件代码）
    if len(event_codes) > 1:
        event_code = track_configs.filter(
            affiliated_track='NEE',
            code=event_codes[-1]
        ).first()

        if not event_code:
            raise TrackCodeError(f'轨迹异常事件{event_codes[-1]}未配置,请检查')


def _parse_datetime(date) -> datetime:
    """解析日期时间"""
    if isinstance(date, str):
        date = date.replace("T", " ")
        return datetime.strptime(date, "%Y-%m-%d %H:%M:%S")
    return date


def _validate_track_time_order(order_num: str, track_code: 'TrackCode', actual_time: datetime):
    """验证轨迹时间顺序"""
    if not track_code.sort:
        return  # 如果没有排序值，跳过验证

    # 优化：使用select_related减少查询次数
    existing_tracks = ParcelTrack.objects.filter(
        order_num=order_num,
        del_flag=False
    ).select_related().order_by('actual_time')

    # 获取所有轨迹配置的映射（优化：减少数据库查询）
    track_code_map = {
        tc.code: tc for tc in TrackCode.objects.filter(
            affiliated_track='T',
            del_flag=False
        ).only('code', 'sort', 'name')
    }

    for existing_track in existing_tracks:
        base_existing_code = str(existing_track.track_code).split('-')[0]
        other_track_code = track_code_map.get(base_existing_code)

        if not other_track_code or not other_track_code.sort:
            continue

        # 检查时间顺序逻辑
        time_order_violated = (
                (other_track_code.sort < track_code.sort and existing_track.actual_time > actual_time) or
                (other_track_code.sort > track_code.sort and existing_track.actual_time < actual_time)
        )

        if time_order_violated:
            raise TrackTimeOrderError(
                f'订单{order_num}轨迹顺序时间有误: {track_code.name}时间{actual_time}'
                f'与{existing_track.track_name}时间{existing_track.actual_time}冲突'
            )


def _build_track_params(
        order_num: str,
        track_code: 'TrackCode',
        event_codes: List[str],
        actual_time: datetime,
        location: Optional[str],
        courier_code: str,
        remark: str,
        user,
        timezone_offset: Optional[str] = None
) -> dict:
    """构建轨迹参数"""
    # 构建完整的轨迹代码
    if event_codes:
        full_track_code = track_code.code + '-' + '-'.join(event_codes)
    else:
        full_track_code = track_code.code

    if not timezone_offset:
        timezone_offset = getattr(settings, 'TIME_ZONE_OFFSET', '') or ''

    return {
        'order_num': order_num,
        'system_code': track_code.system_code,
        'track_code': full_track_code,
        'track_sub_code': track_code.sub_code,
        'track_name': track_code.name,
        'actual_time': actual_time,
        'push_status': 'WAIT_PUSH',
        'location': location or '',
        'remark': track_code.nameEn or remark or '',
        'mode_key': random.randint(1, 2),
        'courier_code': courier_code,
        'timezone_offset': timezone_offset,
        **get_update_params_by_user(user, True)
    }


# FBA订单添加轨迹方法(只添加轨迹)
@transaction.atomic
def set_customer_track_fba(customer_order, track_code, request_user, date=None, track_name=None, raise_no_track=False,
                           track_remark=None, track_location=None):
    if date is None:
        date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    if not track_name:
        # 使用 select_related 一次性加载关联的 track_code 对象
        product_codes = ProductTrackCode.objects.filter(
            product=customer_order.product,
            del_flag=False,
            track_code__code=track_code
        )

        if product_codes.exists():
            product_code = product_codes.first()
            track_name = product_code.track_code.name if product_code.track_code else None
        else:
            track_codes = TrackCode.objects.filter(
                code=track_code,
                affiliated_track='P',
                del_flag=False,
            )
            if track_codes.exists():
                track_code_obj = track_codes.first()
                track_name = track_code_obj.name
            else:
                if raise_no_track:
                    raise ParamError(f'订单{customer_order.order_num}的产品{customer_order.product}未配置轨迹代码: '
                                     f'{track_code}，请检查！', ErrorCode.PARAM_ERROR)
                else:
                    logger.error(f'订单{customer_order.order_num}的产品{customer_order.product}未配置轨迹代码: '
                                 f'{track_code}，请检查！')

    if not track_remark:
        location, track_remark = get_track_location_remark(customer_order, track_code)
        if not track_location:
            track_location = location

    address_name_en = None
    if track_location:
        track_node_address = TrackNodeAddress.objects.filter(address_name=track_location,
                                                             del_flag=False).last()
        if track_node_address:
            address_name_en = track_node_address.address_name_en

    logger.info(f'订单轨迹更改address_name_en3: {address_name_en}')

    track_params = {
        'track_code': track_code,
        'track_name': track_name,
        'order_num': customer_order.order_num,
        'order_id': customer_order.id,
        'actual_time': date,
        'expected_qty': customer_order.pre_carton,
        'expected_time': date,
        'remark': track_remark,
        'location': track_location,
        'location_en': address_name_en,
    }
    Track.objects.create(**track_params, **get_update_params_by_user(request_user, True))

    logger.info(
        f'修改订单轨迹{customer_order.order_num}改为{track_code}')
    return '订单轨迹添加成功', 0


# FBM订单添加轨迹
@transaction.atomic
def set_customer_track_fbm(
        customer_order: CustomerOrder,
        order_status: str,
        request_user,
        tracking_time=None
):
    track_map = {
        "WO": ["submitted", "已提交"],
        "VC": ["approved", "已审核"],
        "IW": ["warehousing", "已入仓"],
        "IWC": ["in_warehouse_confirm", "已入仓数据确认"],
        "OWH": ["out_warehouse", "已出仓"],
        "DE": ["declared", "已报关"],
        "SO": ["steel_off", "已离港/已起飞"],
        "AR": ["arrived", "已到港/已降落"],
        "CC": ["customs_cleared", "已清关"],
        "IWW": ["overseas_warehousing_arrived", "已到达海外仓"],
        "OOD": ["extracted", "派送中"],
        "PSF": ["Partially_signed_for", "部分签收"],
        "FC": ["signed", "已签收"]
    }

    if order_status not in track_map:
        raise ParamError(f'订单状态{order_status}不支持', ErrorCode.PARAM_ERROR)

    track_info = track_map.get(order_status)

    tracking_time = tracking_time or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    track = Track()
    track.create_by = request_user
    track.update_by = request_user
    track.track_code = track_info[0]
    track.track_name = track_info[-1]
    track.order_num = customer_order.order_num
    track.order_id = customer_order.id
    track.actual_time = tracking_time
    track.expected_qty = customer_order.pre_carton
    track.expected_time = tracking_time
    track.remark = ''
    track.location = ''
    track.save()
    return True


# 获取轨迹参数, 方便后续批量添加轨迹
def get_customer_track_fba_object(customer_order, track_code, request, date=None, track_name=None, track_remark=None,
                                  track_location=None, track_location_en=None):
    if date is None:
        date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    track_params = {
        'create_by': request.user,
        'update_by': request.user,
        'track_code': track_code,
        'track_name': track_name,
        'order_num': customer_order.order_num,
        'order_id': customer_order.id,
        'actual_time': date,
        'expected_qty': customer_order.pre_carton,
        'expected_time': date,
        'remark': track_remark,
        'location': track_location,
        'location_en': track_location_en,
    }
    track_object = Track(**track_params)
    logger.info(f'get_customer_track_fba_params: {track_params}')
    return track_object


# 更新空/海/卡派单轨迹(添加轨迹)
def update_transport_order_track(transport_order, transport_order_track_model, foreign_key, user, code,
                                 date=datetime.now(), track_name=None, track_remark=None, track_location=None,
                                 track_map=None):
    if isinstance(transport_order, CustomerOrder):
        model_class = 'CustomerOrder'
        affiliated_track = 'P'
    elif isinstance(transport_order, OceanOrder):
        model_class = 'OceanOrder'
        affiliated_track = 'C'
    elif isinstance(transport_order, MasterOrder):
        model_class = 'MasterOrder'
        affiliated_track = 'M'
    elif isinstance(transport_order, TruckOrder):
        model_class = 'TruckOrder'
        affiliated_track = 'TO'
    elif isinstance(transport_order, CollectOrder):
        model_class = 'CollectOrder'
        affiliated_track = 'CO'
    elif isinstance(transport_order, ParcelCustomerOrder):
        model_class = 'ParcelCustomerOrder'
        affiliated_track = 'P'
    elif isinstance(transport_order, ClearanceOut):
        model_class = 'ClearanceOut'
        affiliated_track = 'CLO'
    else:
        raise ParamError(f'未知的单据类型', ErrorCode.PARAM_ERROR)

    if not track_name:
        if track_map:
            track_name = track_map.get(code)
        else:
            track_code_queryset = TrackCode.objects.filter(code=code, affiliated_track=affiliated_track, del_flag=False)
            if track_code_queryset.exists():
                track_name = track_code_queryset.first().name
            else:
                raise ParamError(f'未配置所属类型为 {affiliated_track}的轨迹代码 {code}', ErrorCode.PARAM_ERROR)

    if not track_remark:
        location, track_remark = get_track_location_remark(transport_order, code)
        if not track_location:
            track_location = location

    # transport_order_track = transport_order_track_model()
    # transport_order_track.track_code = code
    # transport_order_track.track_name = track_name
    # transport_order_track.operation_time = date
    # transport_order_track.remark = track_remark
    # transport_order_track.location = track_location
    # setattr(transport_order_track, foreign_key, transport_order)
    # transport_order_track.save()

    track_params = {
        'track_code': code,
        'track_name': track_name,
        'operation_time': date,
        'remark': track_remark,
        'location': track_location,
    }

    transport_order_track_model.objects.create(**track_params, **{foreign_key: transport_order},
                                              **get_update_params_by_user(user, True))
    logger.info(f'修改运输单轨迹, 运单: {transport_order}, 轨迹: {code}, 轨迹说明: {track_name}, 时间: {date}')


# 同步出口报关信息
def sync_clearance_out_info(transport_order: ClearanceOut, clear_status):
    # 报关完成的才进行同步信息
    if not isinstance(transport_order, ClearanceOut) or clear_status != 'FC':
        return
    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'FX']:
        # raise ParamError('此功能仅在供应商服务器使用', ErrorCode.PARAM_ERROR)
        print('此功能仅在供应商服务器使用')
        return

    if not transport_order.customer_clearance_num:
        return

    # 通过出口报关单获取柜号,启运港,目的港,船名,航次信息
    update_data = {}  # 更新的几个字段信息
    ocean_order = transport_order.ocean_order_id
    if ocean_order:
        container_no = ocean_order.container_no
        departure = ocean_order.departure
        destination = ocean_order.destination
        discharge = ocean_order.discharge
        vessel = ocean_order.vessel
        voyage_num = ocean_order.voyage_num
        update_data.update({
            'container_no': container_no,
            'departure': departure,
            'destination': destination,
            'discharge': discharge,
            'vessel': vessel,
            'voyage_num': voyage_num,
        })

    # 通过获取放行资料信息
    release_attachments = transport_order.release_attachments.all()
    attachment_list = []

    for attachment in release_attachments:
        file_url = attachment.url
        if file_url:
            file_params = {
                'file': os.path.basename(file_url.name),
                'base64': base64.b64encode(file_url.file.read()).decode('ascii')
            }
            attachment_list.append(file_params)

    push_data = {
        # 'clearance_num': transport_order.clearance_num,  # 报关单号
        'clearance_num': transport_order.customer_clearance_num,  # 客户报关单号
        'attachment_list': attachment_list,
        'update_data': update_data
    }
    if update_data:
        push_data.update({'update_data': update_data})
    # print("需要同步的信息-----------------", push_data)

    json_str = json.dumps(push_data, sort_keys=True, ensure_ascii=False, separators=(',', ':'),
                          cls=DecimalEncoder)
    json_data = json.loads(json_str)

    # 从mz同步到yqf
    url = 'http://cshm.yiqifei56.com' + '/api/clearanceOutOrders/accept_sync_clearance_out_info/'
    # url = 'http://127.0.0.1:8000' + '/api/clearanceOutOrders/accept_sync_clearance_out_info/'
    result = request_server({'data': json_data}, url, {'Content-Type': 'application/json'})

    if not result:
        raise ParamError('出口报关信息推送失败', ErrorCode.PARAM_ERROR)

    if result.get('code') != 200:
        error_msg = result.get('msg', '') or result.get('detail', '')
        raise ParamError(f'订单确认数据推送失败: {error_msg}', ErrorCode.PARAM_ERROR)

    logger.info(f'同步出口报关信息成功, 报关单号--------------------------: {transport_order.clearance_num}')


# 获取空/海/卡派单轨迹数据, 方便后续批量更新
def get_transport_order_tracks(transport_order, ocean_order_track, foreign_key, user, code, track_name,
                               date, track_remark=None, track_location=None):
    model_class = ''
    if isinstance(transport_order, CustomerOrder):
        model_class = 'CustomerOrder'
    elif isinstance(transport_order, OceanOrder):
        model_class = 'OceanOrder'
    elif isinstance(transport_order, TruckOrder):
        model_class = 'TruckOrder'
    elif isinstance(transport_order, ParcelCustomerOrder):
        model_class = 'ParcelCustomerOrder'
    elif isinstance(transport_order, MasterOrder):
        model_class = 'MasterOrder'
    if not track_remark:
        track_location, track_remark = get_track_location_remark(transport_order, code)
    track_params = {
        'create_by': user,
        'update_by': user,
        'track_code': code,
        'track_name': track_name,
        'operation_time': date,
        'remark': track_remark,
        'location': track_location,
        foreign_key: transport_order
    }
    track_object = ocean_order_track(**track_params)
    logger.info(f'get_transport_order_track: {track_params}')
    return track_object


# 更新空/海/卡派单时同步更新关联的订单轨迹: 未使用, 用 set_customer_track_fba 代替
def sync_update_order_track(user, track_code, track_name, customer_order, date):
    track = Track()
    track.create_by = user
    track.update_by = user
    track.track_code = track_code
    track.track_name = track_name
    track.order_num = customer_order.order_num
    track.order_id = customer_order.id
    track.actual_time = date
    track.save()
    logger.info(f'修改订单轨迹{customer_order.order_num}改为{track_code}, 时间:{track.actual_time}')


# 获取轨迹描述
def get_track_location_remark(instance, track_code, raise_err=False, track_time=datetime.now()):
    track_location = None
    track_remark = None
    if not instance:
        return track_location, track_remark
    """
    # 新增亚马逊轨迹节点的预计送达时间
    if track_code == 'DEL':
        estimated_delivery_date = format_utc_date_from_datetime(track_time)
    elif track_code == 'BOO':  # 已约仓之后会有预计送达时间，这个节点取这个时间即可。
        estimated_delivery_date = arrival_date
    else:
        estimated_delivery_date = cal_estimated_delivery_date(product_code, track_code, track_time)
    """

    if isinstance(instance, CustomerOrder):
        warehouse_address = instance.shipper.address_name if instance.shipper else instance.address_num
        track_location = warehouse_address
        if track_code == "AW":
            track_remark = f'转运中心已收货, 货物已抵达{warehouse_address}'
            track_location = warehouse_address
        elif track_code in ['PDC', 'PW', 'AW', 'CWED', 'OW']:
            track_location = instance.country_code
    elif isinstance(instance, OceanOrder):
        # 获取地点
        if track_code in ['ALC', 'CBR', 'AOB', 'DEC', 'DEP', 'SLL']:
            # todo_x: 港口加一个英文港口字段
            track_location = instance.departure  # 启运港
        elif track_code in ['ARR', 'CC', 'ADS', 'VER', 'REL', 'PL', 'ADL', 'ASP']:
            track_location = instance.destination  # 目的港
        elif track_code in ['PL']:
            track_location = instance.receiver.country_code if instance.receiver else None  # 收件人国家

        if track_code == "ALC":
            # 已装柜
            # 去掉{date}, 因为轨迹的操作时间有这个时间, 展示轨迹的时候就不会显示重复的时间了
            # track_remark = f'{date}已装柜, 预计{instance.estimated_time_departure}离港, ' \
            #                          f'{instance.vessel}, {instance.voyage_num}'
            track_remark = f'已装柜, 预计{instance.estimated_time_departure}离港, ' \
                           f'{instance.vessel}, {instance.voyage_num}'
        if track_code == "DEC":
            track_remark = f'已报关'
        elif track_code == "CBR":
            # 甩柜
            track_remark = f'船期更新, 预计{instance.estimated_time_departure}离港, ' \
                           f'{instance.vessel}, {instance.voyage_num}'
        elif track_code == "DEC":
            # 已报关
            track_remark = f'已报关'
        elif track_code == "AOB":
            # 已上船
            track_remark = f'离港'
        elif track_code == "DEP":
            # 已离港
            track_remark = f'{instance.actual_leave_date}已离港，预计{instance.estimated_time_arrival}到港'
        elif track_code == "SLL":
            # 船司晚开
            track_remark = f'船期延误, 预计{instance.estimated_time_departure}离港'
        # elif track_code == "SDY":
        #     track_remark = f'船期延误，预计{instance.estimated_time_arrival}到港'
        elif track_code == "ARR":
            # 已到港
            track_remark = f'已到港'
        elif track_code == "SAL":
            # 船司晚到
            track_remark = f'船期延误, 预计{instance.estimated_time_arrival}到港'
        elif track_code == "CC":
            # 已清关
            track_remark = f'已清关'
        elif track_code == "ADS":
            # 已卸船
            track_remark = f'已卸船'
        elif track_code == "VER":
            # 已查验
            track_remark = f'已查验'
        elif track_code == "REL":
            # 查验放行
            track_remark = f'查验放行'
        elif track_code == "PL":
            # 已提柜
            track_remark = f'已提柜'
        elif track_code == "ASP":
            # 已拆柜: 必须在已清关和已预约之间
            track_remark = '已拆柜, 等待交仓'
        elif track_code == "ADL":
            # 已交运
            track_remark = f'已交运'
        elif track_code == "WT":
            # 等待上铁路
            # warehouse_address = instance.receiver.address_name if instance.receiver else None
            track_remark = f'已卸船, 等待上火车发往芝加哥'
        elif track_code == "OT":
            # 已上铁路火车
            warehouse_address = instance.receiver.address_name if instance.receiver else None
            # track_remark = f'已上火车, 发往{warehouse_address}'
            track_remark = f'已上火车, 发往芝加哥'
        elif track_code == "NC":
            # 通知查验
            track_remark = f'通知查验, 查验方式: 人工查验'
        elif track_code == "TCIS":
            # 提柜进查验站
            track_remark = f'提柜进查验站, 排队查验中'
        elif track_code == "CAR":
            # 查验放行
            track_remark = f'查验已放行, 等待回装提柜中'
        elif track_code == "RTC":
            # 已还柜
            track_remark = f'已还柜'
        elif track_code == "TA":
            # 铁路已到达
            track_remark = f'铁路已到达芝加哥'
    elif isinstance(instance, TruckOrder):
        if track_code == "BOO":
            # 已预约
            track_remark = f'预计{instance.book_warehouse_date}送仓'
            track_location = instance.start_destination  # 始发站
            if instance.ocean_order_num:
                ocean_order = OceanOrder.objects.filter(order_num=instance.ocean_order_num, del_flag=False).first()
                track_location = ocean_order and ocean_order.destination
            if raise_err and not track_location:
                if instance.ocean_order_num:
                    raise ParamError(f'卡派单绑定的提单 {instance.ocean_order_num} 上的目的港必填', ErrorCode.PARAM_ERROR)
                else:
                    raise ParamError(f'卡派单未绑定提单, 无法修改轨迹', ErrorCode.PARAM_ERROR)

        elif track_code == "CBE":
            # 延约-重新预约中
            track_remark = f'延约, 更新预计{instance.book_warehouse_date}送仓'
            track_location = instance.start_destination  # 始发站
        elif track_code == "DEL":
            # 已送达
            track_remark = f'已送仓, POD已上传'
            track_location = instance.arrive_destination  # 目的站
            truck_order_relate_order = CustomerOrderRelateTruck.objects.filter(
                truck_order=instance,
                del_flag=False).first()
            customer_order = truck_order_relate_order and truck_order_relate_order.customer_order_num
            if customer_order:
                track_location = customer_order.receiver and customer_order.receiver.address_name
            print('track_location是个啥-->', track_location)
            if raise_err and not track_location:
                if customer_order:
                    if customer_order.receiver:
                        raise ParamError(f'卡派单绑定的订单 {customer_order.order_num} 上的收件人地址编码必填',
                                         ErrorCode.PARAM_ERROR)
                    else:
                        raise ParamError('私人地址', ErrorCode.PARAM_ERROR)
                else:
                    raise ParamError(f'卡派单未配置订单, 无法修改轨迹', ErrorCode.PARAM_ERROR)

            # customer_orders = CustomerOrder.objects.filter(truck_customer_order_ref__truck_order=instance,
            #                                                truck_customer_order_ref__del_flag=False,
            #                                                del_flag=False)
            # customer_orders.update(estimated_delivery_date=format_utc_date_from_datetime(track_time))

    elif isinstance(instance, (ParcelCustomerOrder, MasterOrder)):
        # 小包单和空运单共用一套轨迹
        if track_code == "ROC":
            track_remark = f'到达操作中心CN'
            track_location = 'CN'
        elif track_code == "EOC":
            track_remark = f'离开操作中心CN'
            track_location = 'CN'
        elif track_code in ["ACT", 'DEC', 'FD', 'FL', 'UCC', 'CC', 'ROOC', 'DTW']:
            destination = ''
            departure = ''
            if isinstance(instance, ParcelCustomerOrder):
                # 小包配置大包单, 大包配置出货单, 出货配置运输单, 运输配置海运单, 海运单找到启运港
                if instance.big_parcel and instance.big_parcel.parcel_outbound_order and \
                        instance.big_parcel.parcel_outbound_order.customer_order:
                    customer_order = instance.big_parcel.parcel_outbound_order.customer_order
                    # 默认会配置空运单
                    if customer_order.master_num:
                        destination = customer_order.master_num.destination
                        departure = customer_order.master_num.departure
                    else:
                        # 检查是否配置海运单/海运优先轨迹
                        order_ocean_relate = CustomerOrderRelateOcean.objects.filter(customer_order_num=customer_order,
                                                                                     del_flag=False)
                        if order_ocean_relate.exists():
                            ocean_order = order_ocean_relate.first().oceanOrder
                            destination = ocean_order.destination
                            departure = ocean_order.departure
                        elif customer_order.ocean_num:
                            destination = customer_order.ocean_num.destination
                            departure = customer_order.ocean_num.departure
            elif isinstance(instance, MasterOrder):
                destination = instance.destination
                departure = instance.departure
            destination = destination if destination else ''
            departure = departure if departure else ''
            airport_code_queryset = AirportCode.objects.filter(airport_code=destination, del_flag=False)
            location = ''
            if airport_code_queryset.exists():
                location = airport_code_queryset.first().airport_en_name
            # 获取地点
            if track_code in ['ACT', 'DEC', 'FD']:
                track_location = departure
            elif track_code in ['FL', 'ACW', 'UCC', 'CC']:
                track_location = destination
            elif track_code in ['ROOC', 'PL', 'DTW']:
                track_location = location
            elif track_code in ['outbound', 'inbound', 'inbox']:
                track_location = 'CN'

            if track_code == 'ACT':
                track_remark = f'到达航司货站{departure}'
            elif track_code == 'DEC':
                track_remark = f'报关完成{departure}'
            elif track_code == 'FD':
                track_remark = f'航班起飞{departure}'
            elif track_code == 'FL':
                track_remark = f'航班落地{destination}'
            elif track_code == 'ACW':
                track_remark = f'已到监管仓{destination}'
            elif track_code == 'UCC':
                track_remark = f'清关中{destination}'
            elif track_code == 'CC':
                track_remark = f'清关完成{destination}'
            elif track_code == "ROOC":
                track_remark = f'到达海外操作中心{location}'
            elif track_code == "PL":
                track_remark = f'已提柜'
            elif track_code == "DTW":
                track_remark = f'已交运USPS操作中心{location}'
    logger.info(f'单据的轨迹描述: {instance}, track_code: {track_code}, track_remark: {track_remark}, track_location: {track_location}')
    return track_location, track_remark


# 根据轨迹修改FBA订单状态
def change_order_status(customer_order: CustomerOrder, track_code, user=None, qty=0, date_time=None):
    status_map = dict(CustomerOrder.ORDER_STATUS)
    if not status_map.get(track_code):
        logger.info(f'订单中没有此状态, 不做任何修改: {track_code}')
        return
    # 根据订单获取对应产品配置的轨迹基础数据
    try:
        qty = int(qty)
    except ValueError as e:
        return f'件数: {qty} 请填写数字', 1
    old_status = customer_order.order_status
    forecast = ['WO', 'PDC']
    finish = ['FC', 'SF']
    configured_track = []
    # if customer_order.product:
    #     product_track_codes = customer_order.product.product_track_code.all()
    #     if product_track_codes.count() > 0:
    #         for product_track_code in product_track_codes:
    #             if product_track_code.track_code and not product_track_code.del_flag:
    #                 configured_track.append(product_track_code.track_code.code)

    if track_code == 'ITP':
        if customer_order.is_intercept:
            modify_data = {'order_status': '取消拦截'}
        else:
            modify_data = {'order_status': '拦截'}
    else:
        modify_data = {'order_status': track_code}

    OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', user)

    if customer_order.product:
        configured_track = ProductTrackCode.objects.filter(
            product=customer_order.product,
            product__del_flag=False,
            del_flag=False
        ).values_list('track_code__code', flat=True)

    # 修改轨迹为['CWED', 'OW', 'DEP', 'TF', 'SF']需要判断是否有 'AW' 轨迹, 如果没有, 报错
    # if track_code in get_order_status_mapping(['CWED', 'OW', 'DEP', 'TF', 'SF']):
    #     if settings.SYSTEM_ORDER_MARK == 'MZ':
    #         aw_tracks = Track.objects.filter(order_id=customer_order.id, track_code='AW', del_flag=False)
    #         if not aw_tracks.exists():
    #             raise ParamError(f'订单没有已入仓轨迹, 请先做入仓: {customer_order.order_num}',
    #                              ErrorCode.PARAM_ERROR)

    if track_code in ['PW', 'AW', 'CWED', 'OW', 'TF']:
        # 已部分入仓, 已全部入仓, 已确认入仓数据, 已出国内仓, 转运
        customer_order.save_fields(order_status=track_code)
        # 对于已部分入仓和已全部入仓, 且传参件数, 则把件数更新到订单上
        if track_code in ['PW', 'AW', 'IW'] and qty > 0:
            customer_order.save_fields(carton=qty)
        if track_code in ['AW', 'IW']:
            customer_order.save_fields(actual_arrival_date=datetime.now())
    elif track_code in forecast:
        # 已预报(等待作业)
        if configured_track:
            # FBA订单为已预报, 其他订单改为等待作业
            cur_status = list(set(configured_track) & set(forecast))
            customer_order.save_fields(order_status=cur_status[0] if cur_status else 'WO')
        else:
            customer_order.save_fields(order_status='WO')
    elif track_code == 'VO':
        # 作废
        # if customer_order.order_status not in forecast:
        #     return f'订单『{customer_order.order_num}』的状态不为已预报, 无法作废', 1
        customer_order.save_fields(order_status=track_code)
    elif track_code == 'ITP':
        # 已拦截
        if customer_order.order_status in ['OW', 'TF', 'SF', 'FC'] and not customer_order.is_intercept:
            return f'订单『{customer_order.order_num}』状态为' \
                   f'{status_map.get(customer_order.order_status)}, 无法拦截', 1
        customer_order.save_fields(is_intercept=not customer_order.is_intercept)
    elif track_code in finish:
        # 已签收(完成)
        if configured_track:
            # 订单状态都设置为产品的第一个轨迹对应的代码, FBA订单为已预报, 其他订单改为等待作业
            cur_status = list(set(configured_track) & set(finish))
            customer_order.save_fields(order_status=cur_status[0] if cur_status else 'FC')

        else:
            customer_order.save_fields(order_status='FC')
    elif track_code in CUSTOMER_ORDER_STATUS_MAP.keys():
        customer_order.save_fields(order_status=track_code)
        # 已离港
        if track_code == 'SO':
            customer_order.save_fields(actual_leave_date=datetime.strptime(date_time, '%Y-%m-%d %H:%M:%S').date())
        # 已到港
        elif track_code == 'AR':
            customer_order.save_fields(actual_arrivals_date=datetime.strptime(date_time, '%Y-%m-%d %H:%M:%S').date())
        # 已到海外仓
        elif track_code == 'IWW':
            customer_order.save_fields(actual_arrived_wh_date=datetime.strptime(date_time, '%Y-%m-%d %H:%M:%S').date())

    else:
        # customer_order.order_status = 'WO'
        # raise ParamError(f'不能将订单状态改为未知状态: {track_code}', ErrorCode.PARAM_ERROR)
        logger.info(f'不能将订单状态改为此状态: {track_code}')
        return
    if user is not None:
        customer_order.save_fields(update_by=user)
    customer_order.save_fields(update_date=datetime.now())
    logger.info(f'修改FBA订单状态{customer_order.order_num}由{old_status}改为{customer_order.order_status}')
    return '订单状态设置成功', 0


# 根据轨迹修改小包单状态
def change_parcel_order_status(order, track_code, user=None):
    old_status = order.order_status
    if track_code in dict(ParcelCustomerOrder.STATUS):
        order.order_status = track_code
    elif track_code == 'ROC':
        order.order_status = 'INBOUND'
    elif track_code == 'EOC':
        order.order_status = 'OUTBOUND'
    elif track_code in ['ACT', 'DEC', 'FD', 'FL', 'UCC', 'CC', 'ROOC', 'DTW']:
        order.order_status = 'TF'
    if user is not None:
        order.update_by = user
    order.update_date = datetime.now()
    order.save()
    logger.info(f'修改小包单状态{order.customer_order_num}由{old_status}改为{order.order_status}')


def save_push_message_bw(company_id, company_type, order_num, track_status, fba_id, product_name, remark, carton,
                         weight, volumn, adminUser, is_send=False):
    # user = UserProfile()
    # user.id = 1
    push_message = WechatPushMessage()
    push_message.company_id = company_id
    push_message.company_type = company_type
    push_message.order_num = order_num
    json_str = get_order_message_bw(order_num, track_status, fba_id, product_name,
                                    str(carton) + '件' + str(weight) + 'kg' + str(volumn) + 'm³', remark)
    push_message.message_json = json_str
    template_id = '-o7WJV_MAPw8IHK4jquVGHmW0cZR7ZjlscpmHjwS0DQ'
    push_message.template_id = template_id
    push_message.content = remark
    push_message.user_id = adminUser.id
    push_message.is_send = is_send
    push_message.status = '0'
    push_message.create_by = adminUser
    push_message.update_by = adminUser
    push_message.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    push_message.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    push_message.save()


# 生成小包单号
def gen_parcel_order_num(parcel_customer_order):
    if settings.SYSTEM_ORDER_MARK in ['MZ'] or (settings.SYSTEM_ORDER_MARK + settings.PARCEL_ORDER_MARK) in ['ZHPHD']:
        order_num = create_parcel_order_num_new()
        for i in range(1, 100):
            old_order_queryset = ParcelCustomerOrder.objects.filter(order_num=order_num, del_flag=False)
            if old_order_queryset.count() == 0:
                break
            else:
                order_num = create_parcel_order_num_new()
        parcel_customer_order.order_num = order_num
    elif settings.SYSTEM_ORDER_MARK in {'HGE', 'HG'}:
        date_str = datetime.now().strftime("%Y%m%d")
        date_str = str(date_str)[2:]
        prefix = settings.SYSTEM_ORDER_MARK + settings.PARCEL_ORDER_MARK + date_str
        redis_seq_key = f"sequence:{date_str}"

        # 初始化计数器：只初始化一次
        if not redis.exists(redis_seq_key):
            order_count = ParcelCustomerOrder.objects.filter(
                create_date__date=date.today()
            ).count()
            redis.set(redis_seq_key, order_count)

        # 尝试生成唯一单号
        for i in range(1000):
            seq = redis.incr(redis_seq_key)
            pre_order_num = generate_order_num_commom(prefix, seq, 6)
            order_num = pre_order_num + calculate_mod36_checksum(pre_order_num)

            lock_key = f"order_lock:{order_num}"
            # 加分布式锁，3 秒过期，避免死锁
            locked = redis.set(lock_key, 1, nx=True, ex=3)
            if not locked:
                continue  # 被别人占用，重试

            old_order_queryset = ParcelCustomerOrder.objects.filter(order_num=order_num, del_flag=False)
            if not old_order_queryset.exists():
                parcel_customer_order.order_num = order_num
                break

        if not parcel_customer_order.order_num:
            raise ValueError('单号重复')
    else:
        parcel_customer_order.order_num = settings.SYSTEM_ORDER_MARK + settings.PARCEL_ORDER_MARK + create_order_num(
            parcel_customer_order.id)


# 运输单号生成
def create_order_num_new(order_num_prefix=None, order_type='customer_order'):
    # 年月日
    now = datetime.now().strftime("%Y%m%d")

    six_digit_random = random.randint(10000, 99999)

    if order_num_prefix is None:
        if order_type == 'customer_order':
            order_num_prefix = settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK
        elif order_type == 'clearance_out':
            order_num_prefix = settings.CLEARANCE_OUT_MARK + settings.CUSTOMER_ORDER_MARK
        else:
            raise ParamError(f'未知的单据类型: {order_type}', ErrorCode.PARAM_ERROR)

    return order_num_prefix + str(now)[2:] + str(six_digit_random)


# 生成单号
def gen_order_num(customer_order, order_num_prefix=None):
    # 已经有单号了, 不需要重复生成单号
    if customer_order.order_num:
        return
    if settings.SYSTEM_ORDER_MARK in ['MZ', 'CLT']:
        order_num = create_order_num_new(order_num_prefix=order_num_prefix)
        for i in range(1, 100):
            old_order_queryset = type(customer_order).objects.filter(order_num=order_num, del_flag=False)
            if old_order_queryset.count() == 0:
                break
            else:
                order_num = create_order_num_new(order_num_prefix=order_num_prefix)
        customer_order.order_num = order_num
    else:
        if order_num_prefix is None:
            order_num_prefix = settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK
        customer_order.order_num = order_num_prefix + create_order_num(customer_order.id)
    customer_order.save()


# # 生成出口报关单号
# def gen_clearance_out_order_num(out_order, order_num_prefix=None):
#     if settings.SYSTEM_ORDER_MARK in ['MZ', 'CLT']:
#         order_num = create_order_num_new(order_num_prefix=order_num_prefix)
#
#         for i in range(1, 100):
#             old_order_queryset = type(out_order).objects.filter(order_num=order_num, del_flag=False)
#             if old_order_queryset.count() == 0:
#                 break
#             else:
#                 order_num = create_order_num_new(order_num_prefix=order_num_prefix)
#         out_order.clearance_num = order_num
#     else:
#         if order_num_prefix is None:
#             order_num_prefix = settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK
#         out_order.clearance_num = order_num_prefix + create_order_num(out_order.id)
#     out_order.save()


# 批量搜索
def get_multi_search(self, request, serializer=None, foreign_key=None, select_related_fields=False):
    search_data = {}
    filter_data = {}
    foreign_key = foreign_key if foreign_key else {}
    # print('get_multi_search request.data-->', request.data)
    multi_data = request.data.pop('multiData', {})
    if len(multi_data) > 1000:
        raise ParamError('多项查询的数据不能超过1000条', ErrorCode.PARAM_ERROR)
    filter_prop = request.data.pop('filterProp', {})
    for item in filter_prop:
        if item.startswith('$'):
            filter_data[item.lstrip('$')] = filter_prop[item]
            continue
        if item.find('date') >= 0 or item.find('time') >= 0:
            params = filter_prop.get(item, None)
            if params.find(',') >= 0:
                filter_data[item + '__range'] = params.split(',')
            else:
                filter_data[item] = params
        elif filter_prop[item] == 'true':
            filter_data[item] = True
        elif filter_prop[item] == 'false':
            filter_data[item] = False
        else:
            filter_data[item] = filter_prop[item]
        if item in foreign_key:
            filter_data[item] = filter_data[foreign_key[item]]
    print('filter_data0-->', filter_data)
    filter_queryset = self.queryset.filter(**filter_data, del_flag=False)
    for item in multi_data:
        if item in foreign_key:
            search_data[foreign_key[item] + '__in'] = multi_data[item]
        else:
            search_data[item + '__in'] = multi_data[item]
    print('search_data0-->', search_data)
    queryset = filter_queryset.filter(**search_data).distinct()
    print('queryset0-->', queryset)
    count = queryset.count()
    if select_related_fields:
        queryset = serializer.setup_eager_loading(queryset)

    if serializer is None:
        data = self.get_serializer(queryset, many=True).data
    else:
        data = serializer(queryset, many=True).data
    request.data['count'] = count
    request.data['data'] = data

    # page = self.paginate_queryset(queryset)
    # print('page-->', page)
    # if page is not None:
    #     if serializer is None:
    #         serializer_data = self.get_serializer(page, many=True)
    #     else:
    #         serializer_data = serializer(page, many=True)
    #     return self.get_paginated_response(serializer_data.data)
    #
    # serializer = self.get_serializer(queryset, many=True)
    # return JResponse(data=serializer.data, code=200, msg="success", status=status.HTTP_200_OK)

    paginator = PostBodyPagination()
    page = paginator.paginate_queryset(queryset, request)
    # page = self.paginate_queryset(queryset)
    print('page-->', page)
    if page is not None:
        if serializer is None:
            serializer_data = self.get_serializer(page, many=True)
        else:
            serializer_data = serializer(page, many=True)
        return paginator.get_paginated_response(serializer_data.data)

    serializer = self.get_serializer(queryset, many=True)
    return JResponse(data=serializer.data, code=200, msg="success", status=status.HTTP_200_OK)

    # # 手动分页
    # paginator = PostBodyPagination()
    # paginated_queryset = paginator.paginate_queryset(queryset, request)
    #
    # # 序列化分页后的数据
    # serializer = self.get_serializer(paginated_queryset, many=True)
    #
    # # 返回分页响应
    # return paginator.get_paginated_response(serializer.data)


def get_multi_search_no_foreign_key(self, request, self_model=None, model_class=None):
    search_data = {}
    multi_data = request.data.pop('multiData', {})
    if len(multi_data) > 1000:
        raise ParamError('多项查询的数据不能超过1000条', ErrorCode.PARAM_ERROR)
    for item in multi_data:
        search_data[item + '__in'] = multi_data[item]
    filter_queryset = model_class.objects.filter(**search_data, del_flag=False)
    if filter_queryset.exists():
        order_num_li = filter_queryset.values_list('order_num', flat=True)
    else:
        raise ParamError('未查询到数据！', ErrorCode.PARAM_ERROR)
    self_filter_queryset = self_model.objects.filter(order_num__in=order_num_li, del_flag=False)
    count = self_filter_queryset.count()
    request.data['count'] = count
    paginator = PostBodyPagination()
    page = paginator.paginate_queryset(self_filter_queryset, request)
    serializer_data = self.get_serializer(page, many=True)
    return paginator.get_paginated_response(serializer_data.data)
    # serializer = self.get_serializer(self_filter_queryset, many=True)
    # return JResponse(data=serializer.data, code=200, msg="success", status=status.HTTP_200_OK)


def param_to_queryset(item, value):
    filter_data = {}
    if item.startswith('$'):
        filter_data[item.lstrip('$')] = value
        return filter_data

    if item.find('date') >= 0 or item.find('time') >= 0:
        params = value
        if params.find(',') >= 0:
            filter_data[item + '__range'] = params.split(',')
        else:
            filter_data[item] = params
    elif value == 'true':
        filter_data[item] = True
    elif value == 'false':
        filter_data[item] = False
    else:
        filter_data[item] = value

    return filter_data


def params_query(queryset, model, request, search_fields=(), except_params=None):
    if except_params is None:
        except_params = []
    except_params.append('search')
    query_params = request.query_params
    search = query_params.get('search', '').strip()
    if search and search_fields:
        search_queries = [Q(**{field + '__icontains': search}) for field in search_fields]
        queryset = queryset.filter(reduce(lambda x, y: x | y, search_queries))
        # print('queryset1-->', queryset.query)
    field_names = [field.name for field in model._meta.get_fields()]
    params = {}
    for item in query_params:
        item = item.strip()
        if item in field_names and item not in except_params:
            is_bool_field = isinstance(model._meta.get_field(item), models.BooleanField)
            if is_bool_field:
                params[item] = True if query_params.get(item) == 'true' else False
            elif item.find('date') >= 0 or item.find('time') >= 0:
                date = query_params.get(item, None)
                if date.find(',') >= 0:
                    params[item + '__range'] = date.split(',')
                else:
                    params[item] = query_params[item]
            else:
                params[item] = query_params[item]
    # print('params6-->', params)
    queryset = queryset.filter(**params)
    # print('queryset2-->', queryset.query)
    return queryset


# 公共收入确认(生成账单)
@transaction.atomic
def order_revenue_confirm(order_id, order_type, user=None):
    order_and_charge = get_order_and_queryset(order_id, order_type, 'revenue')
    # if not order_and_charge[1].exists():
    #     logger.error(f'没有查询到数据,结束此处循环')
    #     return
    current_order = order_and_charge[0].first()
    if current_order.is_revenue_lock:
        logger.error(f'单据: {current_order}已做成本确认')
        return
    logger.info(order_type + '：start_parcel_order_revenue : id=' + str(current_order.id))
    # 获取该客户单下的收入明细
    charge_queryset = order_and_charge[1]
    # 获取该客户单收入明细里客户的个数
    customers = charge_queryset.values('customer').distinct()
    print('customers-->', customers)
    # 没有明细，时间默认当前
    params = {}
    params['account_time'] = datetime.now()
    for cus in customers:
        if not cus['customer']:
            raise ParamError('收入没有付款方, 请检查', ErrorCode.PARAM_ERROR)
        # 获取这个客户下的币种数目
        currency_list_queryset = charge_queryset.filter(customer=cus['customer'], del_flag=False)
        currency_list = currency_list_queryset.values('currency_type').distinct()

        # 获取客户
        customer = Company.objects.get(pk=cus['customer'])

        if settings.MULTI_CURRENCY:
            if len(currency_list) == 0:
                params['account_time'] = datetime.now()
            # 系统为多币种，如果同一客户多个收入币种就需要开多个账单
            for item in currency_list:
                # 获取当前客户当前币种的queryset
                currency_charge_in_queryset = charge_queryset.filter(customer=cus['customer'], del_flag=False,
                                                                     currency_type=item['currency_type'])
                amount = currency_charge_in_queryset.aggregate(total=Sum('account_charge'))['total'] or 0
                currency = item['currency_type']
                # 获取账单参数
                params = get_debit_params(order_type, current_order, currency, amount, customer, user)
                debit = create_debit(params)

                # 获取收款明细参数
                for charge_in in currency_charge_in_queryset:
                    params = get_receivable_params(order_type, current_order, charge_in, debit, user)
                    create_account_receivable(params)
        else:
            # 获取当前客户下的明细
            currency_charge_in_queryset = charge_queryset.filter(customer=cus['customer'], del_flag=False)
            amount = currency_charge_in_queryset.aggregate(total=Sum('account_charge'))['total'] or 0

            currency = '自动设置为系统的币种'
            params = get_debit_params(order_type, current_order, currency, amount, customer, user)
            debit = create_debit(params)

            # 产生Debit明细项目
            for charge_in in currency_charge_in_queryset:
                params = get_receivable_params(order_type, current_order, charge_in, debit, user)
                create_account_receivable(params)

    # 更新订单的收入、成本、毛利、状态
    update_order_gross_profit(order_and_charge[0], order_type, order_and_charge[2], 'revenue', params['account_time'])
    logger.info(order_type + '：end_parcel_order_revenue : id=' + str(current_order.id))


# 公共成本确认
@transaction.atomic
def order_cost_confirm(primary_key, order_type, user=None):
    order_and_charge = get_order_and_queryset(primary_key, order_type, 'cost')
    # if not order_and_charge[1].exists():
    #     logger.error(f'没有查询到数据,结束此处循环')
    #     return
    current_order = order_and_charge[0].first()
    if current_order.is_cost_lock:
        logger.error(f'单据: {current_order}已做成本确认')
        return
    # logger.info(order_type + ': start_order_cost: id=' + str(current_order.id))
    logger.info(f'开始成本确认, order_type: {order_type}, order_id: {current_order.id}')
    # 进行成本确认
    charge_queryset = order_and_charge[1]

    for charge_out in charge_queryset:
        # 如果要修改account_time, 需要修改的第一处
        params = get_payable_params(order_type, current_order, charge_out, user)
        # 创建付款明细
        create_account_payable(params)
    account_time = datetime.now()
    # 如果要修改account_time, 需要修改的第二处
    if charge_queryset.count() == 0:
        if order_type == 'ParcelCustomerOrder':
            account_time = current_order.order_time
        elif order_type == 'CustomerOrder':
            if settings.SYSTEM_ORDER_MARK in ['CLT']:
                account_time = current_order.actual_arrival_date or current_order.check_in_time
            else:
                account_time = current_order.actual_arrival_date
        elif order_type == 'Clearance':
            account_time = current_order.opera_date
        elif order_type == 'Outbound':
            account_time = current_order.order_time
        elif order_type == 'Inbound':
            account_time = current_order.arrival_date
        elif order_type == 'OMSReturnOrder':
            account_time = current_order.actual_arrival_date or current_order.estimated_arrival_date
        elif order_type == 'CustomsClearanceOrder':
            account_time = current_order.clearance_date
        elif order_type == 'MasterOrder':
            account_time = current_order.actual_leave_date
        elif order_type == 'OceanOrder':
            # 海运提单的成本确认 只能在离港后确认，先把这个限制放开掉
            # 暂时改为下单日期
            account_time = current_order.actual_leave_date
            # account_time = current_order.create_date
        elif order_type == 'CollectOrder':
            account_time = current_order.create_date
        elif order_type == 'ClearanceOut':
            account_time = current_order.create_date
        elif order_type == 'TruckOrder':
            account_time = current_order.create_date
        elif order_type == 'VasOrder':
            account_time = current_order.create_date
    account_time = get_and_check_time(account_time, order_type)

    # 更新订单的收入、成本、毛利、状态
    update_order_gross_profit(order_and_charge[0], order_type, order_and_charge[2], 'cost', account_time)
    logger.info(order_type + ': end_order_cost: id=' + str(current_order.id))


# 获取指定年份月份的第一天和最后一天的时间
@transaction.atomic
def get_current_month_start_and_end(date):
    """
    年份 date格式: 2017-09-08
    :param date:
    :return: 本月第一天日期和本月最后一天日期
    """
    if date.count('-') != 2:
        raise ValueError('- is error')
    year, month = str(date).split('-')[0], str(date).split('-')[1]
    end = calendar.monthrange(int(year), int(month))[1]
    start_date = '%s-%s-01 00:00:00' % (year, month)
    end_date = '%s-%s-%s 23:59:59' % (year, month, end)
    return [start_date, end_date]


# 获取时间范围内的所有月份列表
def get_month_range_list(start_month):
    """
    从开始日期到结束日期查询存在的月份列表，除去本月的数据
    :param start_month:
    :param end_month:
    :return:
    """
    if len(str(datetime.datetime.now().month)) == 1:
        end_month = str(datetime.datetime.now().year) + '-0' + str(datetime.datetime.now().month)
    else:
        end_month = str(datetime.datetime.now().year) + '-' + str(datetime.datetime.now().month)
    start_time = datetime.datetime.strptime(start_month, "%Y-%m")
    end_time = datetime.datetime.strptime(end_month, "%Y-%m")
    month_count = rrule.rrule(rrule.MONTHLY, dtstart=start_time, until=end_time).count()
    now_month = datetime.datetime.strptime(str(datetime.datetime.now())[:7], "%Y-%m")
    if start_time == now_month == end_time:
        return []
    else:
        month_list = []
        for x in range(month_count):
            year, month = [int(y) for y in str(start_time)[:7].split("-")]
            month = month + x
            if month > 12:
                year += 1
                month -= 12
            elif month < 1:
                year -= 1
                month += 12
            year, month = str(year), str(month)
            if len(month) == 1:
                month = "0" + month
            month_list.append(year + "-" + month + '-01')
        if str(now_month)[:7] in month_list:
            month_list.remove(str(now_month)[:7])
        return month_list


# 删除文件
def remove_file(path, base_dir=settings.STATIC_MEDIA_DIR):
    if len(str(path)) != 0:
        url = base_dir + str(path)
        if os.path.exists(url):
            os.remove(url)


# 获取所关联主单或者提单是不是完成状态
def get_order_is_finish(order_type, queryset):
    # 获取订单
    if order_type == 'CustomerOrder':
        filter_queryset = queryset.filter(Q(master_num__isnull=False) | Q(ocean_num__isnull=False))
        status_list = [(x.master_num or x.ocean_num).order_status for x in filter_queryset]
    elif order_type == 'Clearance':
        filter_queryset = queryset.filter(Q(master_order_id__isnull=False) | Q(ocean_order_id__isnull=False))
        status_list = [(x.master_order_id or x.ocean_order_id).order_status for x in filter_queryset]
    elif order_type == 'CustomsClearanceOrder':
        filter_queryset = CustomsClearanceRelateTruck.objects.filter(order_num__in=queryset)
        status_list = [(x.truck_order).status for x in filter_queryset]
    flag = True
    for item in status_list:
        if item != 'FC':
            flag = False
    return flag


# action获取通用设置更新人和更新时间
def get_update_params(request, is_create=False, datetime_now=datetime.now()):
    user = request.user
    if isinstance(user, AnonymousUser):
        user = None
    params = {
        'update_by': user,
        'update_date': datetime_now
    }
    if is_create:
        params['create_by'] = user
        params['create_date'] = datetime_now
    return params


def get_update_params_by_user(user, is_create=False, datetime_now=datetime.now()):
    params = {
        'update_by': user,
        'update_date': datetime_now
    }
    if is_create:
        params['create_by'] = user
        params['create_date'] = datetime_now
    return params


# 获取表格单元格的类型并返回字符类型
def get_cell_str(c_type, cell_value):
    if c_type == 2:
        cell = str(int(cell_value)).strip()
    else:
        cell = str(cell_value)
    return cell


# 获取目标币种转CNY的汇率
@transaction.atomic
def get_exchange_rate(current, target='CNY'):
    if not current:
        raise ParamError('请填写当前币种！', ErrorCode.PARAM_ERROR)
    exchange = Exchange.objects.filter(current_currency=current, target_currency=target, del_flag=False)
    if exchange.count() == 0:
        logger.info('current---->>' + current + ', target--->' + target)
        raise ParamError('系统找不到当前汇率记录！', ErrorCode.PARAM_ERROR)
    else:
        return exchange.last().multiplying_rate


# 根据产品获取默认服务
def get_service_from_product(product: Product, raise_none=False):
    service = None
    if product is not None:
        service = Service.objects.filter(product=product, del_flag=False, is_default=True).last()
        # except Service.DoesNotExist:
    if service is None and raise_none:
        raise ParamError(f'产品找不到默认服务, 产品: {product}', ErrorCode.PARAM_ERROR)
    else:
        return service


# 进口报关单根据海运空运获取新的文件名
def get_new_name(order, file_name=''):
    if order.clear_type == 'MS' and not order.master_order_num:
        raise ParamError('请填写空运主单号', ErrorCode.PARAM_ERROR)
    elif order.clear_type == 'OC' and not order.container_no:
        raise ParamError('请填写柜号', ErrorCode.PARAM_ERROR)
    else:
        prefix = order.master_order_num if order.clear_type == 'MS' else order.container_no
        if file_name == 'main_file':
            # 发票箱单
            name = prefix + '-I&P'
        elif file_name == 'cabin_file':
            # 报关清单
            name = prefix + '-LIST'
        elif file_name == 'tax_file':
            # 税单
            name = prefix + '-DUTY'
        elif file_name == 'master_ocean_file':
            # 主单/提单
            name = prefix + '-MAWB'
        elif file_name == 'check_file':
            # ISF
            name = prefix + '-ISF'
        elif file_name == 'bill_file':
            # 电放提单
            name = prefix + '-AWB'
        elif file_name == 'dispatch_file':
            # 派送清单
            name = prefix + '-DL'
        elif file_name == 'awb_file':
            # awb
            name = prefix + '-awb'
        elif file_name == 'sop_file':
            # awb
            name = prefix + '-sop'
        elif file_name == 'manifest_file':
            # manifest
            name = prefix + '-manifest'
        elif file_name == 'attach':
            # 附件
            nums = ClearanceAttachment.objects.filter(clearanceOrder=order, del_flag=False).count()
            name = prefix + '-F' + (nums if nums > 9 else '0' + str(nums))
        else:
            raise ParamError('未知的文件类型，请检查！', ErrorCode.PARAM_ERROR)
        return name


# 进口报关单获取文件名和后缀名
@transaction.atomic
def get_file_suffix(path='', new_name=''):
    if path:
        try:
            suffix = os.path.splitext(path)[1]
            old_file_name = os.path.splitext(path)[0]
            suffix_path = old_file_name.replace(old_file_name.split(('/'))[-1], '')
            origin_file_name = old_file_name.split(('/'))[-1]

            if new_name:
                # 需要重命名
                try:
                    os.rename(path, suffix_path + new_name + suffix)
                except Exception as r:
                    raise ParamError(r, ErrorCode.PARAM_ERROR)
            # 返回 后缀名、路径、原文件名、新路径
            # 新字段值
            field = suffix_path.replace(settings.STATIC_MEDIA_DIR, '')
            return [suffix, suffix_path, origin_file_name, field + new_name + suffix]
        except Exception as e:
            raise ParamError(e, ErrorCode.PARAM_ERROR)
    else:
        raise ParamError('请传入文件路径名！', ErrorCode.PARAM_ERROR)


def num_to_str(table, row_index, column_index):
    """
    excel num转str，去掉.0
    :param table:
    :param row:
    :param column_index:
    :return:
    """
    # ctype : 0 empty,1 string, 2 number, 3 date, 4 boolean, 5 error
    cell = table.cell(row_index, column_index)
    cell_value = table.cell_value(row_index, column_index)
    if not cell_value:
        return
    if cell.ctype == 2 and int(cell_value) == cell_value:
        cell_value = int(cell_value)

    return str(cell_value)


# 更新包裹轨迹
def set_parcel_track_info(code, order, request, product, date):
    if not date:
        date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # 启运港新增轨迹: 到达航司货站, 报关完成, 航班起飞
    if code in ['inbound', 'outbound', 'domesticTransfer', 'exportDeclaration', 'departurePort'] \
            and settings.SYSTEM_ORDER_MARK in ['ZH']:
        location = 'CN'
    elif code in ['inbound', 'outbound', 'domesticTransfer', 'exportDeclaration', 'departurePort', 'ACT', 'DEC', 'FD']:
        location = order.departure
    else:
        location = order.destination
    airport_code_queryset = AirportCode.objects.filter(airport_code=location, del_flag=False)
    if airport_code_queryset.count() > 0:
        location = airport_code_queryset.first().airport_en_name

    parcel_outbound_order_list = ParcelOutboundOrder.objects.filter(customer_order=order, del_flag=False)
    if parcel_outbound_order_list.count() == 0:
        return
    big_parcel_queryset = BigParcel.objects.filter(parcel_outbound_order__in=parcel_outbound_order_list,
                                                   del_flag=False)
    parcel_customer_order_list = ParcelCustomerOrder.objects.filter(big_parcel__in=big_parcel_queryset,
                                                                    del_flag=False)
    for parcel_customer_order in parcel_customer_order_list:
        set_parcel_track(parcel_customer_order.id, code, date, request.user, location)
        change_parcel_order_status(parcel_customer_order, code, user=request.user)


# 修改海运优先轨迹(会将轨迹同步到订单上)
def change_first_ocean_order_track(order_query: OceanOrder, track_code, date, user, track_name=None, track_remark=None,
                                   track_location=None):
    # if not track_name:
    #     track_code_queryset = TrackCode.objects.filter(code=track_code, affiliated_track='C', del_flag=False)
    #     if track_code_queryset.count() > 0:
    #         track_name = track_code_queryset.first().name
    # 已离港/已到港则添加实际离港/到港时间
    # date_now = datetime.strptime(date, '%Y-%m-%d %H:%M:%S')
    if track_code == 'DEP':
        order_query.actual_leave_date = datetime.strptime(date, '%Y-%m-%d %H:%M:%S').date()
        order_query.save()
    if track_code == 'ARR':
        order_query.actual_arrivals_date = datetime.strptime(date, '%Y-%m-%d %H:%M:%S').date()
        order_query.save()

    # 添加海运优先轨迹
    update_transport_order_track(order_query, OceanOrderTrack, 'ocean_order_num', user, track_code,
                                 date=date, track_name=track_name, track_remark=track_remark,
                                 track_location=track_location)

    # 存在于状态列表则修改状态
    if track_code in dict(OceanOrder.ORDER_STATUS).keys():
        order_query.order_status = track_code
        order_query.save()
    customer_orders = CustomerOrder.objects.filter(ocean_num=order_query, del_flag=False)
    # 添加客户订单轨迹
    for order in customer_orders:
        if order.first_track:
            # 海运提单已离港则修改订单状态为转运
            if track_code == "DEP":
                change_order_status(order, "TF", user=user)
            set_customer_track_fba(order, track_code, user, date=date, track_name=track_name,
                                   track_remark=track_remark, track_location=track_location)

            send_fba_track_dmas(order, track_code, date, track_remark, user=user)


# 绑定海运优先单到订单上
def bind_ocean_order_and_customer_order(ocean_order, customer_order_queryset, user):
    # customer_order = customer_order_queryset.first()

    # total_freight_num = 0
    # for oceanorder in oceanorders:
    #     total_freight_num += int(oceanorder.get('freight_num', 0))
    # if total_freight_num > (customer_order.carton or 0):
    #     return fail_response(request, '配载件数不能超过整单件数')

    update_order_params = {
        'ocean_num': ocean_order,
        'departure': ocean_order.departure,
        'destination': ocean_order.destination,
        'ocean_number': ocean_order.order_num,
        'expected_leave_date': ocean_order.estimated_time_departure,
        'expected_arrivals_date': ocean_order.estimated_time_arrival,
        'actual_leave_date': ocean_order.actual_leave_date,
        'actual_arrivals_date': ocean_order.actual_arrivals_date,
        # 配置船名航次
        # 'vessel': ocean_order.vessel,
        # 'voyage_num': ocean_order.voyage_num,
        'first_track': True
    }
    # CustomerOrder.objects.filter(id=customer_order.id, del_flag=False).update(**update_order_params,
    #                                                                           **get_update_params(request))
    modify_data = {'ocean_num': ocean_order}
    for customer_order in customer_order_queryset:
        OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', user)
    customer_order_queryset.update(**update_order_params, **get_update_params_by_user(user))
    # 订单绑定海运优先轨迹时, 如果海运优先轨迹的船名和航次不为空, 则将优先轨迹上的船名航次同步到订单上
    for customer_order in customer_order_queryset:
        customer_order.vessel = ocean_order.vessel or customer_order.vessel
        customer_order.voyage_num = ocean_order.voyage_num or customer_order.voyage_num
        customer_order.save()
    # 清楚配载信息
    # CustomerOrderRelateOcean.objects.filter(customer_order_num=customer_order, del_flag=False).update(del_flag=True,
    #                                                                                                   update_by=user,
    #                                                                                                   update_date=datetime.now())
    # 重新配载
    # for oceanorder in oceanorders:
    #     ocean_order = OceanOrder.objects.get(id=oceanorder['id'])
    #     freight_num = oceanorder.get('freight_num', 0)
    #     # 只要有订单配载了，就是配载中状态 ==> 如果是第一件 就更改状态
    #     customer_set = CustomerOrderRelateOcean.objects.filter(oceanOrder=ocean_order, del_flag=False).count()
    #     if customer_set == 0:
    #         ocean_order.order_status = 'LOA'
    #         ocean_order.save()
    ocean_order.order_status = 'LOA'
    ocean_order.save()

    # # 添加配载数量
    # relate_ocean_queryset = CustomerOrderRelateOcean.objects.filter(customer_order_num=customer_order,
    #                                                                 oceanOrder=ocean_order, del_flag=False)
    # if relate_ocean_queryset:
    #     customerOrderRelateOcean = relate_ocean_queryset[0]
    # else:
    #     customerOrderRelateOcean = CustomerOrderRelateOcean()
    #     customerOrderRelateOcean.customer_order_num = customer_order
    #     customerOrderRelateOcean.oceanOrder = ocean_order
    #
    # if not freight_num:
    #     freight_num = customer_order.carton or customer_order.pre_carton or 0
    # customerOrderRelateOcean.freight_num = freight_num
    # customerOrderRelateOcean.create_by = user
    # customerOrderRelateOcean.create_date = datetime.now()
    # customerOrderRelateOcean.save()

    # 汇总海运提单下订单的件重体
    customer_orders = CustomerOrder.objects.filter(ocean_num=ocean_order, del_flag=False)
    ocean_order.carton = customer_orders.aggregate(total=Sum('carton'))['total'] or 0
    ocean_order.weight = customer_orders.aggregate(total=Sum('weight'))['total'] or 0
    ocean_order.volume = customer_orders.aggregate(total=Sum('volume'))['total'] or 0
    ocean_order.charge_weight = customer_orders.aggregate(total=Sum('charge_weight'))['total'] or 0
    ocean_order.save()
    logger.info(f'海运优先单绑定订单, 海运优先单: {ocean_order.order_num}, '
                f'订单: {", ".join(customer_order_queryset.values_list("order_num", flat=True))}')


# 创建/修改海运单/海运优先轨迹单时同步船名航次到订单上
def follow_ocean_order_sync_info(bind_ocean, current_ocean_order: OceanOrder):
    # 海运优先单
    if current_ocean_order.is_first:
        # bind_ocean = validated_data.get("bind_ocean")
        if bind_ocean:
            # 如果海运优先轨迹单绑定海运单, 则将海运单的船名航次同步到海运优先轨迹单上
            ocean_order = OceanOrder.objects.filter(order_num=bind_ocean, del_flag=False).first()
            if ocean_order:
                current_ocean_order.vessel = ocean_order.vessel
                current_ocean_order.voyage_num = ocean_order.voyage_num
                current_ocean_order.update_date = datetime.now()
                current_ocean_order.save()

        if current_ocean_order.vessel or current_ocean_order.voyage_num:
            # 如果海运优先轨迹单上存在船名和航次, 在同步到对应的订单上
            update_order_params = {
                # 'departure': current_ocean_order.departure,
                # 'destination': current_ocean_order.destination,
                # 'ocean_number': current_ocean_order.order_num,
                'expected_leave_date': current_ocean_order.estimated_time_departure,
                'expected_arrivals_date': current_ocean_order.estimated_time_arrival,
                'actual_leave_date': current_ocean_order.actual_leave_date,
                'actual_arrivals_date': current_ocean_order.actual_arrivals_date,
                'vessel': current_ocean_order.vessel,
                'voyage_num': current_ocean_order.voyage_num,
            }
            CustomerOrder.objects.filter(ocean_num=current_ocean_order, del_flag=False
                                         ).update(**update_order_params,
                                                  update_date=datetime.now())
    # 海运提单
    else:
        # 修改海运优先单
        # 修改海运单时，如果海运单绑定的客户订单有优先轨迹，改了船名航次，则将船名航次更新到 优先轨迹上，同时更新海运单绑定的客户订单
        first_ocean_orders = OceanOrder.objects.filter(bind_ocean=current_ocean_order, del_flag=False)
        # 若有海运优先单跟踪了此海运单, 则更新海运优先, 再更新订单
        if first_ocean_orders.exists():
            first_ocean_orders.update(vessel=current_ocean_order.vessel, voyage_num=current_ocean_order.voyage_num)
            for first_ocean_order in first_ocean_orders:
                # 更新关联了海运优先的订单
                customer_orders = CustomerOrder.objects.filter(ocean_num=first_ocean_order, del_flag=False)
                customer_orders.update(vessel=current_ocean_order.vessel, voyage_num=current_ocean_order.voyage_num)
        # 若没有海运优先跟踪这个海运单, 则判断全局字典中是否开启海运优先, 未开启才更新订单
        else:
            is_open_first_track = Dict.objects.filter(label='OpenFirstTrack', value='1', del_flag=False)
            if not is_open_first_track.exists():
                relate_order_queryset = CustomerOrderRelateOcean.objects.filter(
                    oceanOrder__order_num=current_ocean_order,
                    del_flag=False)
                for relate_order in relate_order_queryset:
                    if relate_order.customer_order_num:
                        relate_order.customer_order_num.vessel = current_ocean_order.vessel
                        relate_order.customer_order_num.voyage_num = current_ocean_order.voyage_num
                        relate_order.customer_order_num.expected_leave_date = current_ocean_order.estimated_time_departure
                        relate_order.customer_order_num.expected_arrivals_date = current_ocean_order.estimated_time_arrival
                        relate_order.customer_order_num.actual_leave_date = current_ocean_order.actual_leave_date
                        relate_order.customer_order_num.actual_arrivals_date = current_ocean_order.actual_arrivals_date
                        relate_order.customer_order_num.save()

        # 修改出口报关单
        clearance_outs = ClearanceOut.objects.filter(ocean_order_id=current_ocean_order, del_flag=False)
        if clearance_outs.exists():
            # 更新出口报关单的: 柜号, 启运港, 目的港, 卸货港, 船名, 航次
            clearance_outs.update(
                container_no=current_ocean_order.container_no,
                departure=current_ocean_order.departure,
                destination=current_ocean_order.destination,
                discharge=current_ocean_order.discharge,
                vessel=current_ocean_order.vessel,
                voyage_num=current_ocean_order.voyage_num,
            )


# 组装修改海运单轨迹同步修改订单轨迹和状态数据
def get_ocean_order_change_customer_order_tracks(ocean_order, code, request, date, track_name, track_remark,
                                                 track_location, track_location_en=None):
    customer_order_tracks = []
    # customer_orders = CustomerOrder.objects.filter(ocean_num=item.id, del_flag=False)
    relate_ocean_queryset = CustomerOrderRelateOcean.objects.filter(oceanOrder=ocean_order, del_flag=False)
    # 判断是否在全局开启虚拟轨迹, 若开启, 则海运单的轨迹更改不会同步到订单上
    is_open_first_track = Dict.objects.filter(label='OpenFirstTrack', value='1', del_flag=False)
    track_code = code
    if not is_open_first_track.exists():
        for relate_ocean in relate_ocean_queryset:
            order = relate_ocean.customer_order_num
            # sync_update_order_track(request.user, code, track_name, order, date)

            # 凯乐通海运提单修改轨迹对应修改订单状态
            if settings.SYSTEM_ORDER_MARK in ['CLT', 'FX']:
                # 已报关
                if code == 'DEC':
                    change_order_status(order, 'DE', user=request.user, date_time=date)
                # 已离港
                elif code == "DEP":
                    change_order_status(order, 'SO', user=request.user, date_time=date)
                # 已到港
                elif code == 'ARR':
                    change_order_status(order, 'AR', user=request.user, date_time=date)
                # 已清关
                elif code == 'CC':
                    change_order_status(order, 'CC', user=request.user, date_time=date)
                # 已出仓
                elif code == 'YCC':
                    change_order_status(order, 'OWH', user=request.user, date_time=date)
                # 已到仓
                elif code == 'YDC':
                    track_code = 'IWW'
                    change_order_status(order, 'IWW', user=request.user, date_time=date)
            else:
                # 已离港
                if code == "DEP":
                    change_order_status(order, 'TF', user=request.user)

            track_params = get_customer_track_fba_object(
                order, track_code, request, date=date,
                track_name=track_name,
                track_remark=track_remark,
                track_location=track_location,
                track_location_en=track_location_en,
            )
            customer_order_tracks.append(track_params)

            send_fba_track_dmas(order, track_code, date, track_remark, user=request.user)

    return customer_order_tracks


# openpyxl根据坐标获取excel表格单元格数据, x从1开始, y从A开始
def get_excel_cell(x, y, sheet, string=False, allow_formula=False):
    # value = sheet.cell_value(x-1, ord(y.lower()) - ord('a'))
    value = sheet.cell(x, ord(y.lower()) - ord('a') + 1).value
    # 如果单元格数据是公式, 则报错
    # cell = sheet.cell(x, ord(y.lower()) - ord('a') + 1)
    # if cell.data_type == 'f' and not allow_formula:
    #     raise ParamError(f'第{x}行, 第{y}列是公式: {value}, 无法读取数据', ErrorCode.PARAM_ERROR)
    if value:
        if string:
            value = str(value).strip()
        elif isinstance(value, str):
            value = value.strip()
    return value


class GetExcelCellData:
    def __init__(self, excel_header_map, sheet):
        self.excel_header_map = excel_header_map
        self.sheet = sheet

    # openpyxl根据表头索引获取excel表格单元格数据, x从1开始, y从A开始
    def get_cell_by_header(self, row, field, allow_null=True, allow_formula=False):
        # print('self.excel_header_map-->', self.excel_header_map)
        if field not in self.excel_header_map:
            raise ParamError(f'字段: {field} 不在表头map中, 请重新下载表格模板', ErrorCode.PARAM_ERROR)
            # return
        column, value_type = self.excel_header_map[field]
        value = self.sheet.cell(row, column).value
        # 如果单元格数据是公式, 则报错
        cell = self.sheet.cell(row, column)
        if cell.data_type == 'f' and not allow_formula and value_type.lower() != 'img':
            raise ParamError(f'第{row}行, 第{index_to_excel_column(column - 1)}列是公式: {value}, 无法读取数据',
                             ErrorCode.PARAM_ERROR)
        value_type_map = {
            'str': '字符串',
            'int': '整型',
            'Decimal': '小数',
            'Img': '图片'
        }
        try:
            if value_type.lower() == 'str':
                value = str(value or '').strip()
                if not allow_null and not value:
                    raise TypeError
            elif value_type.lower() == 'int':
                if allow_null:
                    value = int(value or 0)
                else:
                    value = int(value)
            elif value_type == 'Decimal':
                if allow_null:
                    value = float(value or 0)
                else:
                    value = float(value)
            elif value_type == 'Img':
                # f =_xlfn.DISPIMG("ID_BD704E38711F47C9910685C40D913920",1)
                print('value-->', value, type(value))
                if value is None:
                    return None
                img_id = re.findall(r'DISPIMG\("(.*?)",\d+\)', str(value))
                if img_id:
                    value = img_id[0]
                else:
                    value = None
        except ValueError:
            raise ParamError(f'第{row}行第{chr(ord("A") + column - 1)}列: {value} 格式错误, 期望格式: '
                             f'{value_type_map.get(value_type, value_type)}', ErrorCode.PARAM_ERROR)
        except TypeError:
            raise ParamError(f'第{row}行第{chr(ord("A") + column - 1)}列: 不允许为空, 请检查', ErrorCode.PARAM_ERROR)
        return value


def print_fba_label_common(customer_orders):
    orders = []
    for customer_order in customer_orders:
        order = []
        customer_order_num = customer_order.order_num
        # shipment_id = customer_order_num[:-7] if len(customer_order_num) == 19 else 'Unknown'
        parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
        if parcels.count() == 0:
            continue
        for parcel in parcels:
            if not parcel:
                continue
            # print('parcel-->', parcel)
            parcel_map = {
                'parcel_num': parcel.parcel_num,
                'sys_parcel_num': parcel.sys_parcel_num,
                'warehouse_code': customer_order.receiver.address_num if customer_order.receiver
                else customer_order.buyer_address_num,
                'buyer_postcode': customer_order.buyer_postcode,
                # 'product': customer_order.product.code,
                'product': customer_order.product.name,
                'shipmentId': get_shipment_id(parcel.parcel_num),
                'order_num': customer_order_num,
                'ref_num': customer_order.ref_num,
                'place': 'MADE IN CHINA'
            }
            order.append(parcel_map)
        orders.append(order)

    # 一个订单一个文件
    result_list = []
    for order in orders:
        base64_data = create_barcode_for_fba_order([order])
        file_name = f'{order[0]["ref_num"]}_{order[0]["product"]}_{order[0]["warehouse_code"]}_{len(order)}件'
        result_list.append({
            "file_name": file_name,
            "base64_data": base64_data
        })

    return result_list, 0


def print_truck_label_common(customer_orders):
    """
    打印卡派面单
    """
    from common.utils.barcode_gen import create_barcode_for_truck_order
    
    orders = []
    for customer_order in customer_orders:
        order = []
        customer_order_num = customer_order.order_num
        parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
        if parcels.count() == 0:
            continue
            
        for parcel in parcels:
            if not parcel:
                continue
                
            # 根据图片要求生成对应的数据
            parcel_map = {
                'order_num': customer_order_num,
                'parcel_count': parcels.count(),  # 件数
                'postal_code': customer_order.buyer_postcode,  # 邮编
                # 订单号后5位
                'tracking_num': customer_order_num[-5:] if len(customer_order_num) >= 5 else customer_order_num,
            }
            order.append(parcel_map)
        orders.append(order)
    
    if not orders:
        return None, 1
        
    # 一个订单一个文件
    result_list = []
    for order in orders:
        base64_data = create_barcode_for_truck_order(order)
        if not base64_data:
            continue
        file_name = f'{order[0]["order_num"]}_{order[0]["postal_code"]}_{len(order)}件'
        result_list.append({
            "file_name": file_name,
            "base64_data": base64_data
        })
    
    if not result_list:
        return None, 2
        
    return result_list, 0


def export_inbound_order_common(customer_orders):
    """
    导出进仓单
    """

    orders = {}
    for customer_order in customer_orders:
        parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
        if parcels.count() == 0:
            continue

        export_order = ExportBoxInboundOrderSchema()
        export_order.order_num = customer_order.order_num
        export_order.barcode = customer_order.order_num
        export_order.total_box_qty = customer_order.pre_carton or 0

        # parcels 根据shipment_id分组

        shipment_parcel_map = defaultdict(list)
        for parcel in parcels:
            shipment_parcel_map[parcel.shipment_id].append(parcel)

        box_detail_list = []
        for shipment_id, parcel_list in shipment_parcel_map.items():
            box_detail = ExportBoxDetailSchema()
            # 箱数
            box_detail.shipping_mark = shipment_id
            box_detail.number = sum(list(map(lambda p: p.parcel_qty, parcel_list)))
            parcel_volume = sum(p.parcel_volume for p in parcel_list)
            parcel_weight = sum(p.parcel_weight for p in parcel_list)
            box_detail.volume = parcel_volume
            box_detail.weight = parcel_weight
            box_detail_list.append(box_detail)

        # 只保留前21条数据
        if len(box_detail_list) > 21:
            box_detail_list = box_detail_list[:21]
        export_order.box_details = box_detail_list
        orders[customer_order.order_num] = export_order

    result = {}
    for order_num, export_order in orders.items():
        result[order_num] = generate_inbound_order_excel(export_order)

    return result


def generate_inbound_order_excel(data: ExportBoxInboundOrderSchema):
    # 加载模板
    template_path = settings.STATIC_MEDIA_DIR + '/templates/template_inbound_order.xlsx'
    workbook = load_workbook(template_path)
    sheet = workbook.active

    # 写入订单号
    sheet.cell(row=4, column=3, value=data.order_num)

    # 写入总箱数
    sheet.cell(row=2, column=8, value=f"总箱数：{data.total_box_qty}")

    # 生成条码图片并插入到Excel中
    barcode_image = generate_barcode_image(data.barcode, narrow=0.35, height=4.0)

    img = Image(barcode_image)

    # 获取C列的宽度和第5行的高度
    cell_width_pixels = sheet.column_dimensions['C'].width * 8  # Excel中列宽转换为像素
    cell_height_pixels = sheet.row_dimensions[5].height + 10  # 行高已经是像素

    # 将像素转换为EMU
    img.width = min(barcode_image.width, cell_width_pixels)
    img.height = min(barcode_image.height, cell_height_pixels)

    # 设置图片锚点
    img.anchor = 'C5'  # 插入到C5单元格
    sheet.add_image(img)

    # 写入合计
    total_number = sum(detail.number for detail in data.box_details)
    sheet.cell(row=25, column=7, value=total_number)

    # 定义数据开始的行索引 假设数据从第四行开始
    start_row_index = 4

    # 写入数据
    for i, detail in enumerate(data.box_details):
        sheet.cell(row=start_row_index + i, column=5, value=i + 1)
        sheet.cell(row=start_row_index + i, column=6, value=detail.shipping_mark)
        sheet.cell(row=start_row_index + i, column=7, value=detail.number)
        sheet.cell(row=start_row_index + i, column=8, value=detail.volume)
        sheet.cell(row=start_row_index + i, column=9, value=detail.weight)

    # 返回工作簿的字节流
    output = io.BytesIO()
    workbook.save(output)
    output.seek(0)
    base64_encoded = base64.b64encode(output.getvalue()).decode('utf-8')
    return base64_encoded


def generate_barcode_image(content, narrow=None, height=None, orientation=0):
    """
    生成条形码图片
    @param content:  条形码内容
    @param narrow: 窄条形码宽度
    @param height: 条形码高度
    @param orientation:  条形码方向
    @return:
    """
    if not content:
        return

    # 精细度
    dpi = 360
    # module宽度
    module_width = narrow if narrow is not None else 1.0 / dpi

    # 创建条形码对象
    code128 = barcode.get_barcode_class('code128')
    options = {
        'module_width': module_width,
        'write_text': False,  # 不写入文本
        'dpi': dpi,
        'orientation': orientation
    }
    if height is not None:
        options['module_height'] = height

    # 生成条形码
    barcode_instance = code128(content, writer=ImageWriter())
    buffer = io.BytesIO()
    barcode_instance.write(buffer, options=options)

    # 将生成的条形码图像写入输出流
    buffer.seek(0)
    return PILImage.open(buffer)


def export_box_order_common(customer_orders):
    """
    导出箱单
    """
    orders = {}
    for customer_order in customer_orders:
        box_orders = []

        parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
        if parcels.count() == 0:
            continue

        parcel_items = ParcelItem.objects.filter(parcel_num__in=parcels, del_flag=False)
        if parcel_items.count() == 0:
            continue

        shipments = OcShipment.objects.filter(customer_order_num=customer_order, del_flag=False)
        shipment_map = {shipment.shipment_id: shipment for shipment in shipments}
        parcel_num_map = {parcel.parcel_num: parcel for parcel in parcels}

        parcel_item_map = {}
        for parcel_item in parcel_items:
            if parcel_item.parcel_num.parcel_num not in parcel_item_map:
                parcel_item_map[parcel_item.parcel_num.parcel_num] = [parcel_item]
            else:
                parcel_item_map[parcel_item.parcel_num.parcel_num].append(parcel_item)

        for parcel in parcels:
            oc_parcel = parcel_num_map.get(parcel.parcel_num)
            shipment = shipment_map.get(parcel.shipment_id)
            if parcel.parcel_num and oc_parcel and oc_parcel.id != parcel.id:
                parcel.parcel_qty = 0

            parcel_item_list = parcel_item_map.get(parcel.parcel_num)
            if parcel_item_list:
                i = 0
                for parcel_item in parcel_item_list:
                    if i > 0:
                        parcel.parcel_qty = 0
                    box_order = write_export_box_order(customer_order, parcel, parcel_item, shipment)
                    box_orders.append(box_order)
                    i += 1

            # elif parcel.shipment_id:
            #     box_order = write_export_box_order(customer_order, parcel, None, shipment)
            #     box_orders.append(box_order)

            orders[customer_order.order_num] = box_orders

    # by 单生成excel

    result_map = dict()
    for order_num, box_orders in orders.items():
        result_map[order_num] = generate_box_excel(box_orders)

    return result_map


def generate_box_excel(data_list: List[ExportBoxOrder]):
    """
    生成箱单Excel
    @param data_list:
    @return: base64编码的excel文件
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "Sheet1"

    # Define the headers based on the provided @Excel annotations
    headers = [
        "Envio/Shipment  ID*",
        "Seller/Reference  ID*",
        "店铺类型*",
        "图片",
        "英文品名*",
        "中文品名*",
        "材质*",
        "用途*",
        "品牌*",
        "型号",
        "单个单价*（USD）",
        "总个数*",
        "海关编码*",
        "单箱长(CM)*",
        "单箱宽(CM)*",
        "单箱高(CM)*",
        "单箱重量(KG)*",
        "箱数*",
        "合箱箱号",
        "报关方式*",
        "是否带电",
        "是否带磁",
        "总货值（USD）",
        "总体积(CBM)",
        "总重量(KG)",
        "目的仓代码",
        "订单号",
        "提单号",
        "柜号",
        "分拣码"
    ]
    ws.append(headers)

    data_list = [data.to_dict() for data in data_list]
    for row in data_list:
        row_data = []
        for key in headers:
            if key == '图片':
                # Placeholder for image handling
                row_data.append(None)
            else:
                # Map the keys from the data to the headers
                mapped_key = {
                    "Envio/Shipment  ID*": "shipment_id",
                    "Seller/Reference  ID*": "reference_id",
                    "店铺类型*": "shop_type",
                    "英文品名*": "declared_name_en",
                    "中文品名*": "declared_name_cn",
                    "材质*": "texture",
                    "用途*": "item_use",
                    "品牌*": "brand",
                    "型号": "model",
                    "单个单价*（USD）": "declared_price",
                    "总个数*": "item_qty",
                    "海关编码*": "customs_code",
                    "单箱长(CM)*": "parcel_length",
                    "单箱宽(CM)*": "parcel_width",
                    "单箱高(CM)*": "parcel_height",
                    "单箱重量(KG)*": "parcel_weight",
                    "箱数*": "parcel_qty",
                    "合箱箱号": "parcel_num",
                    "报关方式*": "value_added",
                    "是否带电": "is_electrified",
                    "是否带磁": "is_magnetic",
                    "总货值（USD）": "total_price",
                    "总体积(CBM)": "total_volume",
                    "总重量(KG)": "total_weight",
                    "目的仓代码": "warehouse_code",
                    "订单号": "order_num",
                    "提单号": "ocean_num",
                    "柜号": "container_no",
                    "分拣码": "sort_code"
                }.get(key, key)
                row_data.append(row.get(mapped_key, None))
        ws.append(row_data)

    # Adjust column widths and set alignment
    for column_cells in ws.columns:
        length = max(len(str(cell.value)) for cell in column_cells)
        ws.column_dimensions[column_cells[0].column_letter].width = length + 2
        for cell in column_cells:
            cell.alignment = Alignment(horizontal='left', vertical='top')

    # 设置第一行的高度、加粗和灰色背景
    ws.row_dimensions[1].height = 30  # 第一行高度不变
    for cell in ws[1]:
        cell.font = Font(bold=True)  # 加粗
        cell.fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色背景

    # 设置其他行的高度为60
    for row in ws.iter_rows(min_row=2):  # 从第二行开始
        ws.row_dimensions[row[0].row].height = 60

    # Handle images separately after data is inserted
    for idx, row in enumerate(data_list, start=2):  # Start from 2 to account for header
        if row.get('image'):
            img = Image(row['image'])
            img.height = 60
            img.width = 60  # Adjust width if necessary to maintain aspect ratio
            ws.add_image(img, f'D{idx}')  # Assuming 'image' is always in the 4th column (D)
            # Adjust column D width to fit the image
            # Adjust divisor if necessary
            ws.column_dimensions['D'].width = max(ws.column_dimensions['D'].width, img.width / 6.5)

    # Save the workbook to a BytesIO object
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)  # Reset the stream position to the beginning

    # Convert the BytesIO object to base64
    base64_encoded = base64.b64encode(output.getvalue()).decode('utf-8')
    return base64_encoded


def generate_fbm_order_excel(data_list: List[ExportFbmOrderExcelSchema]):
    wb = Workbook()
    ws = wb.active
    ws.title = "Sheet1"

    # Define the headers based on the provided @Excel annotations
    headers = [
        "Envio/Shipment  ID*",
        "Seller/Reference  ID*",
        "店铺类型*",
        "图片",
        "英文品名*",
        "中文品名*",
        "材质*",
        "用途*",
        "品牌*",
        "型号",
        "单个单价*（USD）",
        "总个数*",
        "海关编码*",
        "单箱长(CM)*",
        "单箱宽(CM)*",
        "单箱高(CM)*",
        "单箱重量(KG)*",
        "箱数*",
        "合箱箱号",
        "报关方式*",
        "是否带电",
        "是否带磁",
        "总货值（USD）",
        "总体积(CBM)",
        "总重量(KG)",
        "订单号",
        "客户单号",
        "下单产品名称",
        "国内交货仓库",
        "平台仓代码",
        "平台仓库地址",
        "收货人邮编",
        "收货人",
        "收货人电话",
        "收货人省/州",
        "收货人城市",
        "收货详细地址",
        "是否购买保险*",
        "分拣码",
        "客户",
    ]
    ws.append(headers)
    data_list = [data.to_dict() for data in data_list]
    print('data_list233-->', data_list)
    for row in data_list:
        row_data = []
        for key in headers:
            if key == '图片':
                # Placeholder for image handling
                row_data.append(None)
            else:
                # Map the keys from the data to the headers
                mapped_key = {
                    "Envio/Shipment  ID*": "shipment_id",
                    "Seller/Reference  ID*": "reference_id",
                    "店铺类型*": "shop_type",
                    "英文品名*": "declared_name_en",
                    "中文品名*": "declared_name_cn",
                    "材质*": "texture",
                    "用途*": "item_use",
                    "品牌*": "brand",
                    "型号": "model",
                    "单个单价*（USD）": "declared_price",
                    "总个数*": "item_qty",
                    "海关编码*": "customs_code",
                    "单箱长(CM)*": "parcel_length",
                    "单箱宽(CM)*": "parcel_width",
                    "单箱高(CM)*": "parcel_height",
                    "单箱重量(KG)*": "parcel_weight",
                    "箱数*": "parcel_qty",
                    "合箱箱号": "parcel_num",
                    "报关方式*": "value_added",
                    "是否带电": "is_electrified",
                    "是否带磁": "is_magnetic",
                    "总货值（USD）": "total_price",
                    "总体积(CBM)": "total_volume",
                    "总重量(KG)": "total_weight",
                    "订单号": "order_num",
                    "客户订单号": "customer_num",
                    "下单产品名称": "product_name",
                    "国内交货仓库": "address_num",
                    "平台仓代码": "buyer_address_num",
                    "平台仓库地址": "warehouse_address",
                    "收货人邮编": "buyer_postcode",
                    "收货人": "buyer_name",
                    "收货人电话": "buyer_phone",
                    "收货人省/州": "buyer_state",
                    "收货人城市": "buyer_city_code",
                    "收货详细地址": "buyer_address",
                    "是否购买保险*": "is_insurance",
                    "分拣码": "sort_code",
                    "客户": "customer_name",
                }.get(key, key)
                row_data.append(row.get(mapped_key, None))
        ws.append(row_data)

    # Adjust column widths and set alignment
    for column_cells in ws.columns:
        length = max(len(str(cell.value)) for cell in column_cells)
        ws.column_dimensions[column_cells[0].column_letter].width = length + 2
        for cell in column_cells:
            cell.alignment = Alignment(horizontal='left', vertical='top')

    # 设置第一行的高度、加粗和灰色背景
    ws.row_dimensions[1].height = 30  # 第一行高度不变
    for cell in ws[1]:
        cell.font = Font(bold=True)  # 加粗
        cell.fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色背景

    # 设置其他行的高度为60
    for row in ws.iter_rows(min_row=2):  # 从第二行开始
        ws.row_dimensions[row[0].row].height = 60

    # Handle images separately after data is inserted
    for idx, row in enumerate(data_list, start=2):  # Start from 2 to account for header
        if row.get('image'):
            # img = Image(row['image'])
            # print('image2-->', img, type(img))
            # img.height = 60
            # img.width = 60  # Adjust width if necessary to maintain aspect ratio
            # ws.add_image(img, f'D{idx}')  # Assuming 'image' is always in the 4th column (D)
            # # Adjust column D width to fit the image
            # ws.column_dimensions['D'].width = max(ws.column_dimensions['D'].width,
            #                                       img.width / 6.5)  # Adjust divisor if necessary

            # 读取图片由openpyxl 的 Image对象, 换成PIL 的 Image对象, 避免报错 mime = mimetypes.types_map[True][ext] KeyError: '.webp'
            # 方案一: 将图片格式转成png格式: ...
            # 方案二: 使用PIL Image读取图片:
            image_path = row['image']
            logger.info(f'为什么图片路径不存在: {image_path}')
            # 使用 PIL 读取图片
            pil_img = PILImage.open(image_path)

            # 调整图片大小
            pil_img = pil_img.resize((60, 60))  # 调整为 60x60 像素

            # 将 PIL 图片转换为 openpyxl 的 Image 对象
            image_stream = io.BytesIO()  # 创建一个内存流
            pil_img.save(image_stream, format='PNG')  # 将 PIL 图片保存为 PNG 格式到内存流
            image_stream.seek(0)  # 将流指针重置到开头

            # 加载为 openpyxl 的 Image 对象
            img = Image(image_stream)

            # 打印图片信息
            print('image2-->', img, type(img))

            # 插入图片到 Excel
            ws.add_image(img, f'D{idx}')  # 假设图片总是插入到第 4 列 (D)

            # 调整列宽以适应图片
            ws.column_dimensions['D'].width = max(ws.column_dimensions['D'].width, 60 / 6.5)

    return wb


def generate_shipment_excel(data_list: List[ExportShipmentExcelSchema]):
    wb = Workbook()
    ws = wb.active
    ws.title = "Sheet1"
    # Define the headers based on the provided @Excel annotations 货件号	店铺ID	店铺类型
    headers = [
        "原货件号",
        "店铺ID",
        "店铺类型",
        "订单号",
        "订单状态",
        "货件状态",
        "货件箱数",
        "已预约时间",
        "ETD/实际开船",
        "ETA/实际到港",
        "海外到仓日期",
        "最早可约时间",
        "客户",
        "海外仓",
        "渠道",
        "异常标签",
    ]
    ws.append(headers)

    data_list = [data.to_dict() for data in data_list]
    for row in data_list:
        row_data = []
        for key in headers:
            # Map the keys from the data to the headers
            mapped_key = {
                "原货件号": "shipment_id",
                "店铺ID": "reference_id",
                "店铺类型": "shop_type",
                "订单号": "order_num",
                "订单状态": "order_status",
                "货件状态": "status",
                "货件箱数": "parcel_qty",
                "已预约时间": "scheduled_time",
                "ETD/实际开船": "actual_leave_date",
                "ETA/实际到港": "actual_arrivals_date",
                "海外到仓日期": "actual_arrived_wh_date",
                "最早可约时间": "earliest_available_time",
                "客户": "customer_name",
                "海外仓": "ocean_warehouse",
                "渠道": "product_name",
                "异常标签": "abnormal_tags",
            }.get(key, key)
            row_data.append(row.get(mapped_key, None))

        ws.append(row_data)

    # Adjust column widths and set alignment
    min_width = 10  # Set minimum width
    for column_cells in ws.columns:
        length = max(len(str(cell.value)) for cell in column_cells)
        width = max(length + 2, min_width)  # Ensure min width is maintained
        ws.column_dimensions[column_cells[0].column_letter].width = width
        for cell in column_cells:
            cell.alignment = Alignment(horizontal='left', vertical='top')

    # 设置第一行的高度、加粗和灰色背景
    ws.row_dimensions[1].height = 30  # 第一行高度不变
    for cell in ws[1]:
        cell.font = Font(bold=True)  # 加粗
        cell.fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色背景

    # 设置其他行的高度为60
    for row in ws.iter_rows(min_row=2):  # 从第二行开始
        ws.row_dimensions[row[0].row].height = 60

    return wb


def write_export_box_order(
        order: CustomerOrder,
        parcel: Parcel,
        oc_parcel_item: ParcelItem = None,
        shipment: OcShipment = None
) -> ExportBoxOrder:
    box_qty = oc_parcel_item.box_qty or 0
    export_box_order = ExportBoxOrder()
    export_box_order.shipment_id = parcel.shipment_id
    export_box_order.reference_id = parcel.reference_id
    export_box_order.shop_type = ShopTypeEnum.translate_shop_type(parcel.shop_type)

    export_box_order.parcel_length = parcel.parcel_length
    export_box_order.parcel_width = parcel.parcel_width
    export_box_order.parcel_height = parcel.parcel_height
    export_box_order.parcel_weight = parcel.parcel_weight
    export_box_order.parcel_qty = box_qty
    export_box_order.parcel_num = oc_parcel_item.combined_parcel_num or ''

    if float(parcel.parcel_length) > 0 and float(parcel.parcel_width) > 0 and float(parcel.parcel_height) > 0:
        volume = parcel.parcel_length * parcel.parcel_width * parcel.parcel_height / 1000000
    else:
        volume = 0

    export_box_order.total_volume = volume * box_qty
    if parcel.parcel_weight is not None:
        export_box_order.total_weight = parcel.parcel_weight * box_qty
    export_box_order.warehouse_code = order.buyer_address_num
    export_box_order.order_num = order.order_num
    export_box_order.ocean_num = order.ocean_number
    # export_box_order.container_no = order.container_no

    if oc_parcel_item:

        if oc_parcel_item.item_picture:
            export_box_order.image = oc_parcel_item.item_picture.file.name

        export_box_order.declared_name_en = oc_parcel_item.declared_nameEN
        export_box_order.declared_name_cn = oc_parcel_item.declared_nameCN
        export_box_order.texture = oc_parcel_item.texture
        export_box_order.item_use = oc_parcel_item.use
        export_box_order.brand = oc_parcel_item.brand
        export_box_order.model = oc_parcel_item.model
        if oc_parcel_item.declared_price is not None:
            export_box_order.declared_price = str(oc_parcel_item.declared_price)
        if oc_parcel_item.item_qty is not None:
            export_box_order.item_qty = str(oc_parcel_item.item_qty)
        export_box_order.customs_code = oc_parcel_item.customs_code
        export_box_order.value_added = oc_parcel_item.custom_clearance
        export_box_order.is_electrified = oc_parcel_item.is_electric
        export_box_order.is_magnetic = oc_parcel_item.is_magnetic
        if oc_parcel_item.declared_price is not None:
            export_box_order.total_price = oc_parcel_item.declared_price * oc_parcel_item.item_qty

    if shipment is not None and shipment.sort_code:
        export_box_order.sort_code = shipment.sort_code

    return export_box_order


def print_fbm_box_label_common(customer_orders: List[CustomerOrder], select_shipment_ids: list = None,
                               label_size: str = "100*112"):
    """
    打印FBM 箱唛标签
    @param customer_orders: 客户订单列表
    @param select_shipment_ids: 选择的货件ID列表
    @param label_size: 面单尺寸，支持 "100*100" 或 "100*112"
    """
    orders = []

    for customer_order in customer_orders:
        shipment_params = []
        product = customer_order.product

        # 如果传入了选中的货件号，则只打印所选货件号的
        if isinstance(select_shipment_ids, list) and select_shipment_ids:
            oc_shipments = customer_order.shipment_customer_order_ref.filter(del_flag=False,
                                                                             shipment_id__in=select_shipment_ids)
        else:
            oc_shipments = customer_order.shipment_customer_order_ref.filter(del_flag=False)

        for oc_shipment in oc_shipments:
            warehouse_code = customer_order.buyer_address_num or ''
            # delivery_address_type = 'PA'
            if customer_order.receiver and customer_order.receiver.address_num:
                warehouse_code = customer_order.receiver.address_num
                # delivery_address_type = customer_order.receiver.delivery_address_type

            params = {
                "shipment_id": oc_shipment.shipment_id if oc_shipment.shipment_id else customer_order.order_num,
                "buyer_postcode": customer_order.buyer_postcode,
                "product_code": product.code,
                "box_no": str(oc_shipment.parcel_qty),
                "order_num": customer_order.order_num,
                "warehouse_code": warehouse_code,
                # "delivery_address_type": delivery_address_type
            }

            sort_code = oc_shipment.sort_code
            if not sort_code:
                sort_no, sort_code = generate_shipment_sort_code_new()
                # sort_no = generate_shipment_sort_code()
                # year_str = datetime.now().strftime('%Y')
                # sort_code = convert_sort_code_num(int(sort_no), year_str)
                oc_shipment.sort_no = sort_no
                oc_shipment.sort_code = sort_code
                oc_shipment.sort_time = datetime.now()
                oc_shipment.save()

            params["sort_code"] = sort_code
            params["sort_no"] = str(oc_shipment.sort_no) if oc_shipment.sort_no is not None else ""
            shipment_params.append(FBMBoxLabelSchema.from_dict(params))
        if not shipment_params:
            raise ParamError(f'订单: {customer_order.order_num}没有任何货件')
        orders.append(shipment_params)
    print('orders-->', orders)
    # 一个订单一个文件
    result_list = []
    for order_list in orders:
        out = io.BytesIO()
        try:
            create_box_barcode_for_fbm_order_with_size(order_list, out, label_size)
            base64_data = base64.b64encode(out.getvalue()).decode('ascii')
            result_list.append({
                "order_num": order_list[0].order_num,
                "base64_data": base64_data
            })
        finally:
            out.close()

    return result_list


def generate_shipment_sort_code() -> int:
    """
    生成分拣顺序号
    @return:

    """

    # 获取当月第一天的日期 2024-08-01 00:00:00
    first_day = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    # 获取当月最后一天的日期 2024-08-31 23:59:59
    last_day = (first_day.replace(day=calendar.monthrange(first_day.year, first_day.month)[1]) +
                relativedelta(hour=23, minute=59, second=59))

    shipment_key = f'shipment_key:{first_day.strftime("%Y%m%d")}'

    # django_redis.cache.RedisCache
    sort_no = cache.get(shipment_key, default=0)
    if sort_no == 0:
        # 查询当月最大的分拣顺序号
        max_sort_no = OcShipment.objects.filter(sort_time__range=[first_day, last_day], sort_no__gt=0).aggregate(
            max_sort=Max('sort_no'))['max_sort'] or 0
        current_sort_no = max_sort_no + 1
        # 初始化顺序号 缓存60day
        cache.set(shipment_key, current_sort_no, 60 * 60 * 24 * 60)
    else:
        current_sort_no = cache.incr(shipment_key)

    return current_sort_no


# 汇总包裹预计件重体到运输单上(汇总包裹预计件重体)
def summary_predict_parcels_data(customer_order):
    logger.info(f'汇总包裹预计件重体: {customer_order}')
    parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
    if not parcels.exists():
        return
    parcel_pre_sum_data = parcels.aggregate(
        parcel_qty=Sum('parcel_qty'),
        parcel_weight=Sum('parcel_weight'),
        parcel_volume=Sum('parcel_volume')
    )
    customer_order.save_fields(
        pre_carton=parcel_pre_sum_data['parcel_qty'] or 0,
        pre_weight=parcel_pre_sum_data['parcel_weight'] or 0,
        pre_volume=parcel_pre_sum_data['parcel_volume'] or 0,
    )
    # customer_order.charge_trans = 6000
    # temp_weight = (Decimal(customer_order.pre_volume) / Decimal(customer_order.charge_trans))
    # if temp_weight > Decimal(customer_order.pre_weight):
    #     customer_order.charge_weight = temp_weight
    # else:
    #     customer_order.charge_weight = Decimal(customer_order.pre_weight)


# 汇总包裹的实际件重体到订单上去, 不是按包裹里的商品计算, 是按包裹计算(汇总包裹实际件重体)
def summary_parcels_info(customer_order: CustomerOrder):
    logger.info(f'汇总包裹实际件重体: {customer_order}')
    parcels = customer_order.parcel.filter(del_flag=False)
    parcel_actual_sum_data = parcels.aggregate(
        actual_weight=Sum('actual_weight'),
        actual_volume=Sum('actual_volume')
    )
    customer_order.save_fields(
        carton=parcels.filter(is_weighing=True).count(),
        weight=parcel_actual_sum_data['actual_weight'] or 0,
        volume=parcel_actual_sum_data['actual_volume'] or 0,
    )
    logger.info(f'汇总包裹件重体到订单上, 订单号: {customer_order.order_num}, 件数: {customer_order.carton}, '
                f'重量: {customer_order.weight}, 体积: {customer_order.volume}')


# 校验产品路线
def check_product_route(customer_order: CustomerOrder):
    # 产品路线校验（在订单创建完成后进行，不影响创建流程）
    if customer_order.product and customer_order.product.is_valuation and customer_order.receiver and \
            customer_order.receiver.delivery_address_type in ['FBA', 'FBASatellite']:
        # 构建订单计算VO对象
        from common.service.pms_service import build_order_calc_vo
        order_calc_vo = build_order_calc_vo(customer_order, is_recharge=False)

        # 获取起点分区
        start_dist_vo = order_calc_vo.startDistVo
        start_dist_zones = get_zone(customer_order, customer_order.product, 'Seller', start_dist_vo.countryCode, start_dist_vo.postCode)
        if not start_dist_zones:
            raise ParamError(f'订单创建时产品路线校验失败 - 起点分区未找到: 订单号:{customer_order.order_num}, 国家:{start_dist_vo.countryCode}, 邮编:{start_dist_vo.postCode}', ErrorCode.PARAM_ERROR)
        # 获取终点分区
        end_dist_vo = order_calc_vo.endDistVo
        end_dist_zones = get_zone(customer_order, customer_order.product, 'Buyer', end_dist_vo.countryCode, end_dist_vo.postCode)
        if not end_dist_zones:
            raise ParamError(f'订单创建时产品路线校验失败 - 终点分区未找到: 订单号:{customer_order.order_num}, 国家:{end_dist_vo.countryCode}, 邮编:{end_dist_vo.postCode}', ErrorCode.PARAM_ERROR)

        # 获取产品路线
        if start_dist_zones and end_dist_zones:
            from pms.util.revenue_calc import get_product_route
            product_routes = get_product_route(customer_order.product, start_dist_zones, end_dist_zones, order_calc_vo.calcDate)
            if len(product_routes) == 0:
                raise ParamError(f'订单创建时产品路线校验失败 - 路线不存在: 订单号:{customer_order.order_num}, 起点分区:{[x.name for x in start_dist_zones]}, 终点分区:{[x.name for x in end_dist_zones]}', ErrorCode.PARAM_ERROR)
            else:
                logger.info(f'订单创建时产品路线校验成功: 订单号:{customer_order.order_num}, 路线分区值:{product_routes[0].zone_value}')


# 统一异常返回
def respond_with_error(request: Request, msg: str, code: int = 400,
                       big_parcel_record: BigPickRecord = None) -> Response:
    # 记录FBA错误信息
    if big_parcel_record is not None:
        big_parcel_record.error_msg = msg
        big_parcel_record.save()
    request.data['msg'] = msg
    request.data['code'] = code
    return Response(data=request.data, status=status.HTTP_200_OK)


# 统一处理base64
def read_and_encode_base64(file_path: str) -> str:
    with open(file_path, 'rb') as f:
        base64_data = base64.b64encode(f.read())
        return base64_data.decode()


# 账单汇总公共函数
def billing_summary(queryset, request, invoice=None):
    """
    :param queryset: Debit queryset
    :param request:
    :param invoice: 是否创建账单汇总, 如果不是新建则为 invoice 对象
    :return:
    """
    p_amount = queryset.aggregate(total=Sum('amount'))['total'] or 0
    if invoice is None:
        for query in queryset:
            query.pay_balance = query.amount
            if query.pay_rate is None:
                query.pay_rate = 1
            # 核销余额 = 付款余额 / 汇率
            query.balance = Decimal(query.pay_balance) / Decimal(query.pay_rate or 1)
            query.save()
        params = {
            'amount': p_amount,
            'pay_amount': p_amount,
            'balance': p_amount,
            'pay_balance': p_amount,
            'customer': queryset.first().customer,
            'currency': queryset.first().currency,
            'pay_currency': queryset.first().currency,
            'pay_rate': 1,
            'product_code': queryset.first().product_code,
            'product': queryset.first().product
        }
        invoice = Invoice.objects.create(**params, **get_update_params(request, True))
        # invoice.amount = queryset.aggregate(total=Sum('amount'))['total'] or 0
        invoice.invoice_num = 'IV' + create_order_num(invoice.id)
        if not settings.MULTI_CURRENCY:
            # 单币种自动设置核销币种核销金额核销汇率
            invoice.pay_currency = settings.CURRENT_CURRENCY
            invoice.pay_balance = invoice.pay_amount
            invoice.pay_rate = 1
    else:
        # 修改合计和核销余额
        invoice.amount = p_amount
        invoice.balance = p_amount
    # 账单id列表下所有的明细
    account_receivable = AccountReceivable.objects.filter(debit_num__in=[x.id for x in queryset],
                                                          del_flag=False).order_by('-account_time')
    invoice.debit_time_range = account_receivable.last().account_time.strftime(
        '%Y%m%d')[2:] + '-' + account_receivable.first().account_time.strftime(
        '%Y%m%d')[2:]
    # 设置产品编码，不能直接拿第一条，可能是调整单取不到产品
    invoice.save()
    queryset.update(invoice_id=invoice.id, is_invoiced=True, **get_update_params(request))


# 核销账单汇总公共方法
def invoice_charge_off_common(receipt_obj, invoices, request):
    # 进账单余额
    total_balance = Decimal(receipt_obj.balance)

    deal_count = 0
    undeal_invoices = []
    deal_invoice_nums = []
    part_deal_invoice_nums = []
    part_deal_invoices = ''
    invoices = invoices.filter(del_flag=False)
    for invoice in invoices:
        # record_balance = total_balance
        debit_query = Debit.objects.filter(invoice=invoice, del_flag=False).order_by('amount')
        # total_balance 是账单核销后剩余的金额
        code, res, receipt_invoice, invoice_charge_balance = invoice_charge_off(request, invoice, total_balance,
                                                                                receipt_obj)
        total_balance -= invoice_charge_balance
        undeal_debit, rest_debit, receipt_debits, _ = debit_charge_off(request, debit_query,
                                                                       invoice_charge_balance,
                                                                       receipt_obj)
        # code, res = invoice_charge_off(request, invoice, record_balance - total_balance, receipt_obj)
        # 由于可能有一个进账单多次核销相同账单汇总下的账单, 取消核销的时候不知道取消哪个, 所以需要添加一层关联关系
        for receipt_debit in receipt_debits:
            receipt_debit.receipt_invoice = receipt_invoice
            receipt_debit.save()

        action_description = ""  # 账单汇总核销描述

        print("action_description------------", action_description)
        if code == -1:
            undeal_invoices.append(res)
        elif code == 0:
            part_deal_invoices = res
            part_deal_invoice_nums.append(invoice.invoice_num)  # 部分核销
            action_description = f'部分核销了账单汇总: {invoice.invoice_num}'
            print("22222222222222222222------------", action_description)
        else:
            deal_count += 1
            deal_invoice_nums.append(invoice.invoice_num)  # 全部核销
            action_description = f'核销了账单汇总: {invoice.invoice_num}'
            print("3333333333333333333333------------", action_description)

        if action_description:
            # 写入操作记录
            FinanceOperateChangeLog.record(user=request.user,
                                           order_type='Invoice',
                                           obj_id=invoice.id,  # 账单汇总
                                           obj_num=invoice.invoice_num,  # 进账单交易号
                                           action_type='settle',
                                           action_description=action_description)
    # 更新进账单余额
    receipt_obj.balance = total_balance
    receipt_obj.update_by = get_update_params(request)['update_by']
    receipt_obj.update_date = get_update_params(request)['update_date']
    receipt_obj.save()

    # 有核销过的, 才记录日志------------------------------
    if deal_invoice_nums or part_deal_invoices:
        action_description = ""
        if deal_invoice_nums:
            action_description += f'核销了账单汇总: {", ".join(deal_invoice_nums)}'
        if part_deal_invoice_nums:
            action_description += f', 部分核销了账单汇总: {", ".join(part_deal_invoice_nums)}'

        # 写入操作记录
        FinanceOperateChangeLog.record(user=request.user,
                                       order_type='Receipt',
                                       obj_id=receipt_obj.id,  # 进账单id
                                       obj_num=receipt_obj.transaction_num,  # 进账单交易号
                                       action_type='settle',  # 操作类型: 核销
                                       action_description=action_description.strip(', '))  # 操作描述

    if len(undeal_invoices) == 0 and not part_deal_invoices:
        msg = f'全部核销完成, 核销账单汇总: {deal_count}单, 进账单剩余核销余额: {str(total_balance)}'
        info = 'success'
    # elif len(undeal_invoices) != 0:
    #     msg = f'金额不足, 全部核销: {deal_count}单, 部分核销: {part_deal_invoices}, 未核销: {", ".join(undeal_invoices)}'
    #     info = 'warning'
    else:
        # msg = '未知问题'
        msg = f'金额不足, 全部核销: {deal_count}单, 部分核销: {part_deal_invoices or "0单"}, ' \
              f'未核销: {", ".join(undeal_invoices) or "0单"}'
        info = 'warning'
        msg = f'全部核销完成, 核销账单汇总: {deal_count}单, 进账单剩余核销余额: {str(total_balance)}'

    return info, msg


# 核销账单汇总(单个账单汇总)
def invoice_charge_off(request, invoice: Invoice, total_balance, receipt, is_charge_invoice=True):
    """
    :param invoice: 账单汇总
    :param total_balance: 核销金额
    :param request:
    :param receipt: 进账单
    :param is_charge_invoice: 是否是核销的账单汇总
    """
    total_balance_temp = total_balance
    logger.info(f'开始核销账单汇总{invoice.invoice_num}, 进账单id: {receipt.id}, 账单汇总初始金额: {invoice.amount}, '
                f'账单汇总核销余额: {invoice.balance}, 进账单初始金额: {receipt.amount}, '
                f'进账单核销余额: {receipt.balance}, 此次核销金额: {total_balance}')
    # 账单汇总待核销金额
    charge_balance = Decimal(invoice.pay_balance)
    msg = ''

    # 分情况
    if total_balance - charge_balance > 0:
        # 全部核销
        if is_charge_invoice:
            total_balance -= charge_balance
            rest_balance = charge_balance

            invoice.invoice_status = 2
            invoice.is_verification = True
            invoice.balance = 0
            invoice.pay_balance = 0
            invoice.new_verification = True
            invoice.receive_date = receipt.account_date
            invoice.update_by = get_update_params(request)['update_by']
            invoice.update_date = get_update_params(request)['update_date']
            res_code = 1
            # msg = f'全部核销, 进账单核销余额: {total_balance}'
        # 部分核销
        else:
            difference_value = charge_balance - total_balance
            print('difference_value是个啥-->', difference_value)
            # 这里进行转换余额, 页面所见核销余额为转换后的余额
            invoice.invoice_status = 4
            invoice.pay_balance = difference_value
            invoice.new_verification = True
            invoice.balance = Decimal(difference_value) / Decimal(invoice.pay_rate or 1)
            rest_balance = total_balance
            res_code = 0
            total_balance = 0
            msg = invoice.invoice_num
    # 全部核销
    elif total_balance - charge_balance == 0:
        total_balance -= charge_balance
        rest_balance = charge_balance

        invoice.invoice_status = 2
        invoice.is_verification = True
        invoice.balance = 0
        invoice.pay_balance = 0
        invoice.new_verification = True
        invoice.receive_date = receipt.account_date
        invoice.update_by = get_update_params(request)['update_by']
        invoice.update_date = get_update_params(request)['update_date']
        res_code = 1
        # msg = f'全部核销, 进账单核销余额: {total_balance}'
    # 部分核销
    else:
        # todo_x: 进账单的金额比账单汇总的少, 会有什么问题, 进账单金额为0, 会有什么问题
        difference_value = charge_balance - total_balance
        # 这里进行转换余额, 页面所见核销余额为转换后的余额
        invoice.invoice_status = 4
        invoice.pay_balance = difference_value
        invoice.new_verification = True
        invoice.balance = Decimal(difference_value) / Decimal(invoice.pay_rate or 1)
        rest_balance = total_balance
        total_balance = 0
        res_code = 0
        # msg = '账单汇总' + invoice.invoice_num + '核销了' + str(
        #     Decimal(total_balance) / Decimal(invoice.pay_rate)) + '剩余' + str(
        #     Decimal(difference_value) / Decimal(invoice.pay_rate)) + ', 进账单剩余核销余额为：0'
        msg = invoice.invoice_num
    invoice.save()

    # if total_balance <= 0:
    #     # 未核销
    #     return res_code, invoice.invoice_num, None, 0
    # elif total_balance - charge_balance < 0:
    #     # 部分收款
    #     difference_value = charge_balance - total_balance
    #     # 这里进行转换余额, 页面所见核销余额为转换后的余额
    #     invoice.invoice_status = 4
    #     invoice.pay_balance = difference_value
    #     invoice.new_verification = True
    #     invoice.balance = Decimal(difference_value) / Decimal(invoice.pay_rate or 1)
    #     rest_balance = total_balance
    #     res_code = 0
    #     total_balance = 0
    #     # msg = '账单汇总' + invoice.invoice_num + '核销了' + str(
    #     #     Decimal(total_balance) / Decimal(invoice.pay_rate)) + '剩余' + str(
    #     #     Decimal(difference_value) / Decimal(invoice.pay_rate)) + ', 进账单剩余核销余额为：0'
    #     msg = invoice.invoice_num
    # else:
    #     total_balance -= charge_balance
    #     # 全部收款
    #     rest_balance = charge_balance
    #
    #     invoice.invoice_status = 2
    #     invoice.is_verification = True
    #     invoice.balance = 0
    #     invoice.pay_balance = 0
    #     invoice.new_verification = True
    #     invoice.receive_date = receipt.account_date
    #     invoice.update_by = get_update_params(request)['update_by']
    #     invoice.update_date = get_update_params(request)['update_date']
    #     res_code = 1
    #     # msg = f'全部核销, 进账单核销余额: {total_balance}'
    # invoice.save()

    # 关联进账单
    receipt_invoice = ReceiptInvoice.objects.create(invoice=invoice, receipt=receipt,
                                                    rest_balance=rest_balance, charge_balance=rest_balance,
                                                    deal_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                                    **get_update_params(request, True))
    logger.info(f'结束核销账单汇总{invoice.invoice_num}, 进账单id: {receipt.id}, 账单汇总初始金额: {invoice.amount}, '
                f'账单汇总核销余额: {invoice.balance}, 进账单初始金额: {receipt.amount}, '
                f'进账单核销余额: {receipt.balance}(还未扣减), 此次核销金额: {charge_balance - invoice.pay_balance}')
    # return res_code, msg, receipt_invoice, charge_balance - invoice.pay_balance
    return res_code, msg, receipt_invoice, total_balance_temp - total_balance


# 核销账单(多个账单)
def debit_charge_off(request, debit_query, total_balance, receipt, is_charge_debit=False):
    total_balance_temp = total_balance
    # 未能核销账单汇总
    undeal_debit = []
    # 未能核销完全的付款单
    rest_debit = ''
    receipt_debits = []
    fully_charged_debits = []  # 全部核销的账单号
    partially_charged_debits = []  # 部分核销的账单号
    for item in debit_query:
        # 对于已经核销的账单应该跳过, 不应该报错
        # if not item.pay_balance:
        #     raise ParamError(f'账单{item.debit_num}的付款余额不足，请检查！', ErrorCode.PARAM_ERROR)
        item_balance = Decimal(item.pay_balance)
        logger.info(f'开始核销账单{item.debit_num}, 进账单id: {receipt.id}, 账单初始金额: {item.amount}, '
                    f'账单核销余额: {item.balance}, 进账单初始金额: {receipt.amount}, '
                    f'进账单核销余额: {receipt.balance}, 此次核销金额: {total_balance}')
        # 兼容没有付款余额的账单
        if item_balance == 0:
            continue
        if total_balance == 0:
            # 没钱了，把没处理的账单加进去
            undeal_debit.append(item.debit_num)
            break
        elif total_balance - item_balance < 0:
            # 部分收款
            difference_value = item_balance - total_balance  # 0.74 = 176 - 175.26
            # 这里进行转换余额
            # 页面所见核销余额为转换后的余额
            item.debit_status = 4
            item.pay_balance = difference_value
            # item.new_verification = True
            item.balance = Decimal(difference_value) / Decimal(item.pay_rate or 1)
            item.save()

            # rest_debit = '账单' + item.debit_num + '核销了' + str(
            #     Decimal(total_balance) / Decimal(item.pay_rate or 1)) + '剩余' + str(
            #     Decimal(difference_value) / Decimal(item.pay_rate or 1)) + ', 进账单剩余核销余额为：0'

            rest_debit = f'账单: {item.debit_num}核销了{Decimal(total_balance) / Decimal(item.pay_rate or 1)}, ' \
                         f'剩余: {Decimal(difference_value) / Decimal(item.pay_rate or 1)}, 进账单剩余核销余额为: 0'

            rest_balance = total_balance  # 175.26

            # 进账单不足以核销账单汇总，进账单核销余额为0，账单汇总核销掉能核销的金额即进账单的核销余额
            total_balance = 0
            # 后面还要关联账单核销明细, 不加break
            partially_charged_debits.append(item.debit_num)  # 记录部分核销的账单号
        else:
            # 全部收款
            # 多对多把账单汇总关联进去进账单
            rest_balance = item_balance
            total_balance -= item_balance
            item.debit_status = 2
            item.is_verification = True
            item.balance = 0
            item.pay_balance = 0
            # item.new_verification = True
            item.receive_time = receipt.account_date
            item.update_by = get_update_params(request)['update_by']
            item.update_date = get_update_params(request)['update_date']
            item.save()
            fully_charged_debits.append(item.debit_num)  # 记录全部核销的账单号

            AccountReceivable.objects.filter(del_flag=False, debit_num=item.id).update(
                receive_status=2,
                receive_time=receipt.account_date, **get_update_params(request))
        print('rest_balance-->', rest_balance)
        if rest_balance is not None:
            # 关联进账单
            receipt_debit = ReceiptDebit.objects.create(debit=item, receipt=receipt,
                                                        rest_balance=rest_balance, charge_balance=rest_balance,
                                                        deal_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                                        **get_update_params(request, True))
            receipt_debits.append(receipt_debit)
        logger.info(f'结束核销账单{item.debit_num}, 进账单id: {receipt.id}, 账单初始金额: {item.amount}, '
                    f'账单核销余额: {item.balance}, 进账单初始金额: {receipt.amount}, '
                    f'进账单核销余额 = 此次核销金额剩余: {total_balance}')

    if is_charge_debit and (fully_charged_debits or partially_charged_debits):
        # 写入操作日志记录
        operate_descr = ""
        if fully_charged_debits:
            operate_descr += f'核销了账单: {", ".join(fully_charged_debits)}'
        if partially_charged_debits:
            operate_descr += f', 部分核销了账单: {", ".join(partially_charged_debits)}'

        # 写入操作记录
        FinanceOperateChangeLog.record(user=request.user,
                                       order_type='Receipt',
                                       obj_id=receipt.id,  # 进账单id
                                       obj_num=receipt.transaction_num,  # 进账单交易号
                                       action_type='settle',  # 操作类型: 核销
                                       action_description=operate_descr.strip(', '))  # 操作描述
    return undeal_debit, rest_debit, receipt_debits, total_balance_temp - total_balance


# 取消账单汇总核销方法
def repeal_invoice_charge_off(receipt_invoice_id, receipt_id, request):
    """
    :param receipt_invoice_id: 账单汇总核销记录id
    :param receipt_id: 进账单id
    :param request:
    :return:
    """
    # 获取账单汇总核销记录
    try:
        receipt_invoice = ReceiptInvoice.objects.get(id=receipt_invoice_id, del_flag=False)
    except ReceiptInvoice.DoesNotExist:
        raise ParamError('未查询到账单汇总核销记录!', ErrorCode.PARAM_ERROR)
    # 获取进账单
    receipt = Receipt.objects.get(id=receipt_id, del_flag=False)
    # 获取当前账单汇总和账单
    invoice = receipt_invoice.invoice
    debits = Debit.objects.filter(invoice=invoice, del_flag=False).order_by('amount')
    update_by = get_update_params(request)['update_by']
    update_date = get_update_params(request)['update_date']

    init_balance = receipt_invoice.rest_balance or 0
    msg = f'撤销账单汇总核销成功, 返还金额: {init_balance}'
    logger.info(
        f'1开始撤销账单汇总核销, 进账单id: {receipt_id}, 核销记录id: {receipt_invoice.id}, 账单汇总id: {receipt_invoice.invoice_id}, '
        f'账单汇总初始金额: {invoice.amount}, 账单汇总核销余额: {invoice.balance}, '
        f'进账单初始金额: {receipt.amount}, 进账单核销余额: {receipt.balance}, 此次返还金额: {init_balance}')
    # 进账单动作
    receipt.balance += Decimal(init_balance)
    receipt.update_by = update_by
    receipt.update_date = update_date
    receipt.save()

    action_description = f'取消核销了账单汇总: {invoice.invoice_num}'
    # 写入取消核销账单汇总的操作记录------------------------------------
    FinanceOperateChangeLog.record(user=request.user,
                                   order_type='Receipt',
                                   obj_id=receipt.id,  # 进账单id
                                   obj_num=receipt.transaction_num,  # 进账单交易号
                                   action_type='cancel_settle',  # 操作类型: 取消核销
                                   action_description=action_description)  # 操作描述

    # 写入取消核销账单汇总的操作记录------------------------------------
    FinanceOperateChangeLog.record(user=request.user,
                                   order_type='Invoice',
                                   obj_id=invoice.id,  # 账单汇总id
                                   obj_num=invoice.invoice_num,  # 账单汇总流水
                                   action_type='cancel_settle',  # 操作类型: 取消核销
                                   action_description=action_description)  # 操作描述
    # 账单汇总动作
    invoice.receive_date = None
    invoice.pay_balance += Decimal(init_balance)
    invoice.balance += Decimal(init_balance) / Decimal(invoice.pay_rate or 1)
    invoice.update_by = update_by
    invoice.update_date = update_date
    if invoice.pay_balance == invoice.pay_amount:
        # 撤销了全部金额
        invoice.invoice_status = 1
        invoice.new_verification = False
    else:
        # 撤销了部分金额
        invoice.invoice_status = 4
    invoice.save()

    # 账单汇总核销记录动作
    # receipt_invoice.charge_balance = 0
    receipt_invoice.rest_balance = 0
    receipt_invoice.update_by = update_by
    receipt_invoice.update_date = update_date
    receipt_invoice.del_flag = True
    receipt_invoice.save()

    receipt_debits = ReceiptDebit.objects.filter(debit__in=debits, receipt=receipt, receipt_invoice=receipt_invoice,
                                                 del_flag=False)
    # 兼容以前的旧数据
    if not receipt_debits.exists():
        receipt_debits = ReceiptDebit.objects.filter(debit__in=debits, receipt=receipt, del_flag=False)
    # 账单动作
    # for debit in debits:
    for receipt_debit in receipt_debits:
        print('receipt_debit1-->', receipt_debit.id, receipt_debits)
        # if init_balance == 0:
        #     raise ParamError(f'存在bug! 账单汇总金额返还完毕但账单金额还未返还完毕, 账单核销记录: {receipt_debit.id}',
        #                      ErrorCode.PARAM_ERROR)
        debit = receipt_debit.debit
        # 有可能有多个进账单核销过这个账单, 所以不能把账单金额全部返还
        # refund = debit.amount - debit.balance
        refund = receipt_debit.rest_balance
        logger.info(
            f'1开始撤销账单核销, 进账单id: {receipt_id}, 核销记录id: {receipt_debit.id}, 账单id: {receipt_debit.debit_id}, '
            f'账单初始金额: {debit.amount}, 账单核销余额: {debit.balance}, 此次返还金额: {refund}')
        debit.update_by = update_by
        debit.update_date = update_date
        debit.receive_time = None
        # # 撤销了全部金额
        # if init_balance >= refund:
        #     init_balance -= refund
        #     debit.balance += refund
        #     debit.pay_balance += refund * (debit.pay_rate or 1)
        #     # receipt_invoice.rest_balance -= refund
        #     debit.debit_status = 1
        #     debit.save()
        #     logger.info(f'2撤销账单核销全部金额, 核销记录id: {receipt_invoice.id}, 账单id: {debit.id}, 返还金额: {refund}, '
        #                 f'账单初始付款余额: {debit.pay_balance}, 初始核销余额: {debit.balance}')
        # else:
        #     # 撤销了部分金额
        #     refund = init_balance
        #     init_balance = 0
        #     debit.balance += refund
        #     debit.pay_balance += refund * (debit.pay_rate or 1)
        #     # receipt_invoice.rest_balance = 0
        #     debit.debit_status = 4
        #     debit.save()
        #     logger.info(f'2撤销账单核销部分金额, 核销记录id: {receipt_invoice.id}, 账单id: {debit.id}, 返还金额: {refund}, '
        #                 f'账单初始付款余额: {debit.pay_balance}, 初始核销余额: {debit.balance}')
        #     # break

        init_balance -= refund
        debit.balance += refund
        debit.pay_balance += refund * (debit.pay_rate or 1)
        if debit.balance == debit.amount:
            # 撤销了全部金额
            debit.debit_status = 1
        else:
            # 撤销了部分金额
            debit.debit_status = 4
        # receipt_invoice.rest_balance -= refund
        debit.save()

        # 账单核销记录动作
        # receipt_debit.charge_balance = 0
        receipt_debit.rest_balance = 0
        receipt_debit.update_by = update_by
        receipt_debit.update_date = update_date
        receipt_debit.del_flag = True
        receipt_debit.save()
        logger.info(
            f'1结束撤销账单核销, 进账单id: {receipt_id}, 核销记录id: {receipt_debit.id}, 账单id: {receipt_debit.debit_id}, '
            f'账单初始金额: {debit.amount}, 账单核销余额: {debit.balance}, 此次返还金额: {refund}')
    print('receipt_invoice.rest_balance-->', receipt_invoice.rest_balance, receipt_invoice.charge_balance)

    # # 账单核销记录动作
    # receipt_debits = ReceiptDebit.objects.filter(debit__in=debits, receipt=receipt, del_flag=False)
    # for receipt_debit in receipt_debits:
    #     print('receipt_debit-->', receipt_debit, init_balance, receipt_debit.rest_balance)
    #     if init_balance >= receipt_debit.rest_balance:
    #         receipt_debit.rest_balance = 0
    #         receipt_debit.rest_balance = 0
    #         init_balance -= receipt_debit.rest_balance
    #     else:
    #         receipt_debit.rest_balance -= init_balance
    #         receipt_debit.rest_balance -= init_balance
    #         init_balance = 0
    #     receipt_debit.del_flag = True
    #     receipt_debit.update_by = update_by
    #     receipt_debit.update_date = update_date
    #     receipt_debit.save()
    logger.info(
        f'1结束撤销账单汇总核销, 进账单id: {receipt_id}, 核销记录id: {receipt_invoice.id}, 账单汇总id: {receipt_invoice.invoice_id}, '
        f'账单汇总初始金额: {invoice.amount}, 账单汇总核销余额: {invoice.balance}, '
        f'进账单初始金额: {receipt.amount}, 进账单核销余额: {receipt.balance}, 此次返还金额剩余: {init_balance}')
    return msg


# 取消账单核销方法
def repeal_debit_charge_off(receipt_debit_id, receipt_id, request):
    """
    :param receipt_debit_id: 账单核销记录id
    :param receipt_id: 进账单id
    :param request:
    :return:
    """
    # 获取账单核销记录
    try:
        receipt_debit = ReceiptDebit.objects.get(id=receipt_debit_id, del_flag=False)
    except ReceiptDebit.DoesNotExist:
        raise ParamError('未查询到账单核销记录!', ErrorCode.PARAM_ERROR)
    # 获取进账单
    receipt = Receipt.objects.get(id=receipt_id, del_flag=False)
    # 获取当前账单和账单汇总
    debits = [receipt_debit.debit]
    invoice = receipt_debit.debit.invoice
    update_by = get_update_params(request)['update_by']
    update_date = get_update_params(request)['update_date']

    init_balance = receipt_debit.rest_balance or 0
    msg = f'撤销账单核销成功, 返还金额: {init_balance}'
    receipt_invoice = ReceiptInvoice.objects.filter(invoice=invoice, receipt=receipt, del_flag=False).first()
    logger.info(
        f'2开始撤销账单汇总核销, 进账单id: {receipt_id}, 核销记录id: {receipt_invoice.id}, 账单汇总id: {receipt_invoice.invoice_id}, '
        f'账单汇总初始金额: {invoice.amount}, 账单汇总核销余额: {invoice.balance}, '
        f'进账单初始金额: {receipt.amount}, 进账单核销余额: {receipt.balance}, 此次返还金额: {init_balance}')

    # 进账单动作
    receipt.balance += Decimal(init_balance)
    receipt.update_by = update_by
    receipt.update_date = update_date
    receipt.save()

    # 账单汇总动作
    invoice.pay_balance += Decimal(init_balance)
    invoice.balance += Decimal(init_balance) / Decimal(invoice.pay_rate or 1)
    invoice.update_by = update_by
    invoice.update_date = update_date
    if invoice.pay_balance == invoice.pay_amount:
        # 撤销了全部金额
        invoice.invoice_status = 1
        invoice.new_verification = False
        action_description = f'取消核销了账单汇总: {invoice.invoice_num}'
    else:
        # 撤销了部分金额
        invoice.invoice_status = 4
        action_description = f'部分取消核销了账单汇总: {invoice.invoice_num}'
    invoice.save()

    # 写入取消核销账单汇总的操作记录------------------------------------
    FinanceOperateChangeLog.record(user=request.user,
                                   order_type='Receipt',
                                   obj_id=receipt.id,  # 进账单id
                                   obj_num=receipt.transaction_num,  # 进账单交易号
                                   action_type='cancel_settle',  # 操作类型: 取消核销
                                   action_description=action_description)  # 操作描述

    # 写入取消核销账单汇总的操作记录------------------------------------
    FinanceOperateChangeLog.record(user=request.user,
                                   order_type='Invoice',
                                   obj_id=invoice.id,  # 账单汇总id
                                   obj_num=invoice.invoice_num,  # 账单汇总流水
                                   action_type='cancel_settle',  # 操作类型: 取消核销
                                   action_description=action_description)  # 操作描述

    # 账单汇总核销记录动作
    # 可能存在核销了一个正金额一个负金额正好金额为0, 导致没有生成账单汇总核销记录
    if receipt_invoice:
        # if init_balance >= receipt_invoice.rest_balance:
        #     receipt_invoice.rest_balance = 0
        #     receipt_invoice.del_flag = True
        # else:
        receipt_invoice.rest_balance -= init_balance
        receipt_invoice.update_by = update_by
        receipt_invoice.update_date = update_date
        receipt_invoice.save()

    # 账单动作
    receipt_debits = ReceiptDebit.objects.filter(debit__in=debits, receipt=receipt, receipt_invoice=receipt_invoice,
                                                 del_flag=False)
    # 兼容以前的旧数据
    if not receipt_debits.exists():
        receipt_debits = ReceiptDebit.objects.filter(debit__in=debits, receipt=receipt, del_flag=False)

    full_cancel_list = []
    part_cancel_list = []
    for receipt_debit in receipt_debits:
        print('receipt_debit2-->', receipt_debit.id, receipt_debits)
        # if init_balance == 0:
        #     raise ParamError(f'存在bug! 账单汇总金额返还完毕但账单金额还未返还完毕, 账单核销记录: {receipt_debit.id}',
        #                      ErrorCode.PARAM_ERROR)
        debit = receipt_debit.debit
        # refund = debit.amount - debit.balance
        # receipt_debit = ReceiptDebit.objects.filter(debit=debit, receipt=receipt, del_flag=False).first()
        # if not receipt_debit:
        #     raise ParamError(f'未查询到账单核销记录, 账单: {debit.debit_num}', ErrorCode.PARAM_ERROR)
        refund = receipt_debit.rest_balance
        logger.info(
            f'2开始撤销账单核销, 进账单id: {receipt_id}, 核销记录id: {receipt_debit.id}, 账单id: {receipt_debit.debit_id}, '
            f'账单初始金额: {debit.amount}, 账单核销余额: {debit.balance}, 此次返还金额: {refund}')
        debit.receive_time = None
        debit.update_by = update_by
        debit.update_date = update_date
        # # 撤销了全部金额
        # if init_balance >= refund:
        #     receipt_debit.del_flag = True
        #     init_balance -= refund
        #     debit.balance += refund
        #     debit.pay_balance += refund * (debit.pay_rate or 1)
        #     receipt_debit.rest_balance -= refund
        #     debit.debit_status = 1
        #     debit.save()
        #     logger.info(f'2撤销账单核销全部金额, 核销记录id: {receipt_debit.id}, 账单id: {debit.id}, 返还金额: {refund}, '
        #                 f'账单初始付款余额: {debit.pay_balance}, 初始核销余额: {debit.balance}')
        # else:
        #     refund = init_balance
        #     init_balance = 0
        #     # 撤销了部分金额
        #     debit.balance += refund
        #     debit.pay_balance += refund * (debit.pay_rate or 1)
        #     receipt_debit.rest_balance = 0
        #     debit.debit_status = 4
        #     debit.save()
        #     logger.info(f'2撤销账单核销部分金额, 核销记录id: {receipt_debit.id}, 账单id: {debit.id}, 返还金额: {refund}, '
        #                 f'账单初始付款余额: {debit.pay_balance}, 初始核销余额: {debit.balance}')
        #     # break
        init_balance -= refund
        debit.balance += refund
        debit.pay_balance += refund * (debit.pay_rate or 1)
        if debit.balance == debit.amount:
            # 撤销了全部金额
            debit.debit_status = 1
            full_cancel_list.append(debit.debit_num)
        else:
            # 撤销了部分金额
            debit.debit_status = 4
            part_cancel_list.append(debit.debit_num)
        # receipt_invoice.rest_balance -= refund
        debit.debit_status = 1
        debit.save()

        # 账单核销记录动作
        # receipt_debit.charge_balance = 0
        receipt_debit.rest_balance = 0
        receipt_debit.update_by = update_by
        receipt_debit.update_date = update_date
        receipt_debit.del_flag = True
        receipt_debit.save()
        logger.info(
            f'2结束撤销账单核销, 进账单id: {receipt_id}, 核销记录id: {receipt_debit.id}, 账单id: {receipt_debit.debit_id}, '
            f'账单初始金额: {debit.amount}, 账单核销余额: {debit.balance}, 此次返还金额: {refund}')
    print('receipt_debit.rest_balance-->', receipt_debit.rest_balance, receipt_debit.charge_balance)

    # 账单有核销过的, 写入操作记录
    if full_cancel_list or part_cancel_list:
        if full_cancel_list:
            action_description = f'取消核销了账单: {", ".join(full_cancel_list)}'
        if part_cancel_list:
            action_description = f', 部分取消核销了账单: {", ".join(part_cancel_list)}'
        # 写入取消核销账单的操作记录------------------------------------
        FinanceOperateChangeLog.record(user=request.user,
                                       order_type='Receipt',
                                       obj_id=receipt.id,  # 进账单id
                                       obj_num=receipt.transaction_num,  # 进账单交易号
                                       action_type='cancel_settle',  # 操作类型: 取消核销
                                       action_description=action_description.strip(', '))  # 操作描述

    # 如果所有账单汇总下所有账单取消核销, 则删除账单汇总核销
    receipt_debits = ReceiptDebit.objects.filter(receipt=receipt, receipt_invoice=receipt_invoice,
                                                 del_flag=False)
    if not receipt_debits.exists() and receipt_invoice.rest_balance == 0:
        receipt_invoice.del_flag = True
        receipt_invoice.save()

    # # 账单核销记录动作
    # receipt_debits = ReceiptDebit.objects.filter(debit__in=debits, receipt=receipt, del_flag=False)
    # for receipt_debit in receipt_debits:
    #     print('receipt_debit-->', receipt_debit, init_balance, receipt_debit.rest_balance)
    #     # if init_balance >= receipt_debit.rest_balance:
    #     #     receipt_debit.rest_balance = 0
    #     #     init_balance -= receipt_debit.rest_balance
    #     receipt_debit.rest_balance = 0
    #     receipt_debit.rest_balance = 0
    #     receipt_debit.del_flag = True
    #     receipt_debit.update_by = update_by
    #     receipt_debit.update_date = update_date
    #     receipt_debit.save()
    logger.info(
        f'2结束撤销账单汇总核销, 进账单id: {receipt_id}, 核销记录id: {receipt_invoice.id}, 账单汇总id: {receipt_invoice.invoice_id}, '
        f'账单汇总初始金额: {invoice.amount}, 账单汇总核销余额: {invoice.balance}, '
        f'进账单初始金额: {receipt.amount}, 进账单核销余额: {receipt.balance}, 此次返还金额剩余: {init_balance}')
    return msg


# 导入收入价格版本excel
def upload_income_version_excel(request, wb, table_index=0):
    # 获取第一张表
    table = wb.worksheets[table_index]
    row_count = min_row = 2
    max_column = 6
    for row in table.iter_rows(min_row=min_row, values_only=True):

        row_count += 1
        for column in range(max_column + 1):
            logger.info(f'读取到第{row_count}行第{index_to_excel_column(column)}列值: {row[column]}')
            cell = table.cell(row=row_count, column=column + 1)
            if cell.data_type == 'f':
                raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列是公式: {row[column]}, '
                                 f'无法读取数据', ErrorCode.PARAM_ERROR)

        val1 = row[0]  # 产品编码
        val2 = row[1]  # 产品费用项名称
        val3 = row[2]  # 版本名称
        val4 = row[3]  # 开始时间
        val5 = row[4]  # 币种
        val6 = row[5]  # 协议方案编码
        val7 = row[6]  # 备注

        # 忽略空值
        if val1 is None or val1 == "":
            continue  # 跳过空值

        if not val1 or not val2 or not val3 or not val4:
            raise ParamError('请填写产品编码、产品费用项名称、版本名称、开始时间', ErrorCode.PARAM_ERROR)

        # 处理时间类型
        if isinstance(val4, datetime):
            val4 = val4.date()

        if ProductRevenueVersion.objects.filter(version_name=val3, del_flag=False).count() != 0:
            raise ParamError('已经有对应的版本名称：%s' % val3, ErrorCode.PARAM_ERROR)

        # 获取产品
        try:
            product = Product.objects.get(code=val1, del_flag=False)
        except Product.DoesNotExist:
            raise ParamError('未找到对应的产品:%s，请检查！' % val1, ErrorCode.PARAM_ERROR)

        # 获取产品费用项
        try:
            product_charge = ProductCharge.objects.get(product=product, name=val2, del_flag=False,
                                                       attribution_expenses__in=['default', 'income'])
        except ProductCharge.DoesNotExist:
            raise ParamError('未找到对应的产品:%s下的费用项:%s，请检查！' % (product.code, val2), ErrorCode.PARAM_ERROR)

        params = {
            'product': product,
            'product_charge': product_charge,
            'version_name': val3,
            'start_time': val4,
            'currency': str(val5).strip(),
            'remark': val7,
        }
        # 获取协议方案
        if val6:
            protocol_project_queryset = ProtocolProject.objects.filter(code=str(val6).strip(), del_flag=False)
            if protocol_project_queryset.count() == 0:
                raise ParamError('未找到对应的协议方案:%s，请检查！' % val6, ErrorCode.PARAM_ERROR)
            params['price_type'] = 'B'
            protocol_project = protocol_project_queryset.first()
            product_revenue_version = ProductRevenueVersion.objects.create(**params, **get_update_params(request, True))
            handler_protocol_project_product(product_revenue_version, params['price_type'],
                                             protocol_project.id, request.user)
        else:
            params['price_type'] = 'A'
            product_revenue_version = ProductRevenueVersion.objects.create(**params, **get_update_params(request, True))
        logger.info(
            f'成功导入收入价格版本, 产品编码: {val1} 、产品费用项名称: {val2} 、版本名称: {val3} 、开始时间: {val4}')

# 根据指定列名确定起始行
def detect_data_start_row(table, col_name1, col_name2):
    """
    检测 Excel 文件的数据起始行（基于列名匹配）
    """
    header_row = None
    logger.info(f'开始检测数据起始行, 检测列名1: {col_name1}, 检测列名2: {col_name2}')
    for row_idx, row in enumerate(table.iter_rows(values_only=True), start=1):
        if row and any(cell is not None for cell in row):  # 至少有一个非空单元格
            # 检查是否包含特定列名（如 "分区"）
            if col_name1 in str(row[0]) or col_name2 in str(row[0]):  # 适应同一个地方两个导入模板，第一列可能是分区或分区名称
                header_row = row_idx
                break
    return header_row + 1 if header_row else 1  # 数据起始行 = 标题行 + 1


# 导入收入价格明细excel
def upload_income_detail_excel(request, wb, table_index=0):
    table = wb.worksheets[table_index]
    if settings.SYSTEM_VERSION != 'V2':
        row_count = min_row = 2
        max_column = 10
        for row in table.iter_rows(min_row=min_row, values_only=True):

            row_count += 1
            for column in range(max_column + 1):
                logger.info(f'读取到第{row_count}行第{index_to_excel_column(column)}列值: {row[column]}')
                # 如果单元格数据是公式, 则报错
                cell = table.cell(row=row_count, column=column + 1)
                if cell.data_type == 'f':
                    raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列是公式: {row[column]}, '
                                     f'无法读取数据', ErrorCode.PARAM_ERROR)

            val1 = row[0]  # 价格版本名称
            print('val1----------------->', val1)
            val2 = str(row[1]) if row[1] else ''  # 分区值
            val3 = row[2]  # 计费方式
            val4 = row[3]  # 币种
            val5 = row[4]  # 等级起点
            val6 = row[5]  # 等级终点
            val7 = row[6]  # 价格
            val8 = row[7]  # 基础价格
            val9 = row[8]  # 递增区间
            val10 = row[9]  # 四舍五入因子
            val11 = row[10]  # 备注

            # 忽略空值
            if val1 is None or val1 == "":
                continue  # 跳过空值

            if not val1 or not val3 or not val4:
                raise ParamError('请填写收入价格版本名称、计费方式、币种', ErrorCode.PARAM_ERROR)

            # 获取价格版本名称
            try:
                price_version = ProductRevenueVersion.objects.get(version_name=str(val1).strip(), del_flag=False)
            except ProductRevenueVersion.DoesNotExist:
                raise ParamError('未找到对应的收入价格版本:%s，请检查！' % val1, ErrorCode.PARAM_ERROR)

            if val3 == '总价':
                charge_type = 'ZJ'
            elif val3 == '单价':
                charge_type = 'DJ'
            elif val3 == '递增价':
                charge_type = 'TZJ'
            else:
                raise ParamError('计费方式：%s不正确' % val3, ErrorCode.PARAM_ERROR)

            query_params = {
                'price_version': price_version,
                'zone_value': val2,
                'charge_type': charge_type,
                'currency': str(val4).strip(),
                'rank_start': safe_decimal(val5),
                'rank_end': safe_decimal(val6),
                'price': safe_decimal(val7),
                'base_price': safe_decimal(val8, Decimal('0')),
                'increased_rank': safe_decimal(val9, Decimal('0')),
                'round_of_factor': safe_decimal(val10, Decimal('0')),
                'del_flag': False
            }

            if settings.SYSTEM_VERSION == 'V2':
                product = price_version.product

                zone_settting = ZoneSetting.objects.filter(product=product, del_flag=False, zone_type='CZ').first()
                if not zone_settting:
                    raise ParamError(f'价格版本对应产品({product.name})未设置应收付费分区,请设置',
                                     ErrorCode.PARAM_ERROR)

                zone_postcode = ProductZonePostCode.objects.filter(product_zone=zone_settting.zone, code=str(val2),
                                                                   del_flag=False).first()
                if not zone_postcode:
                    raise ParamError(f'价格版本对应产品({product.name})未设置应收付费分区明细,请设置',
                                     ErrorCode.PARAM_ERROR)
                query_params['zone'] = zone_postcode

            defaults_params = {**query_params, 'remark': val11, **get_update_params(request, True)}

            # 移除所有值为 None 的键
            defaults_params = {k: v for k, v in defaults_params.items() if v is not None}
            # 由于存在重复数据, 所以暂时不使用 update_or_create 方法
            # ProductRevenueVersionLine.objects.update_or_create(defaults=defaults_params, **query_params)
            duplicate_data = ProductRevenueVersionLine.objects.filter(**query_params)
            first_duplicate_data = duplicate_data.first()
            if duplicate_data.exists():
                duplicate_data.update(del_flag=True)
                first_duplicate_data.del_flag = False
                first_duplicate_data.save()
            else:
                ProductRevenueVersionLine.objects.create(**defaults_params)

            logger.info(f'成功导入收入价格明细, 价格表版本: {val1} 、分区值: {val2} 、价格类型: {val3} 、'
                        f'等级起点: {val5}、等级终点: {val6}')
    elif settings.SYSTEM_VERSION == 'V2':
        cost_version_id = request.data.get('cost_version_id')
        
        # 两阶段覆盖导入：第一阶段 - 数据收集和验证
        logger.info(f'开始两阶段覆盖导入，价格版本ID: {cost_version_id}')
        validated_data_list = []  # 存储所有验证通过的数据
        
        row_count = min_row = detect_data_start_row(table, '分区', '分区名称')
        print(f'起始行导入，{row_count}')
        max_column = 8
        
        # 第一阶段：完整验证所有数据，不写入数据库
        for row in table.iter_rows(min_row=min_row, values_only=True):

            row_count += 1
            for column in range(max_column + 1):
                logger.info(f'读取到第{row_count}行第{index_to_excel_column(column)}列值: {row[column]}')
                cell = table.cell(row=row_count, column=column + 1)
                if cell.data_type == 'f':
                    raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列是公式: {row[column]}, '
                                     f'无法读取数据', ErrorCode.PARAM_ERROR)
            val1 = row[0]  # 分区 或 分区名称
            val2 = row[1]  # 分区方式
            val3 = row[2]  # 计费方式
            val4 = row[3]  # 等级起点
            val5 = row[4]  # 等级终点
            val6 = row[5]  # 费用项
            val7 = row[6]  # 币种
            val8 = row[7]  # 金额
            val9 = row[8]  # 备注
            
            # 检查必填字段（不能为空或None）
            required_fields = [val1, val2, val3, val6, val7]  # 分区、分区方式、计费方式、费用项、币种
            if any(field is None or field == '' for field in required_fields):
                continue

            # 检查数值字段（可以为0但不能为None）
            numeric_fields = [val4, val5, val8]  # 等级起点、等级终点、金额
            if any(field is None for field in numeric_fields):
                continue
                
            # 获取价格版本名称
            try:
                price_version = ProductRevenueVersion.objects.get(id=cost_version_id, del_flag=False)
            except ProductRevenueVersion.DoesNotExist:
                raise ParamError('未找到对应的收入价格版本:%s，请检查！' % cost_version_id, ErrorCode.PARAM_ERROR)
                
            # 获取费用项
            product_charges = ProductCharge.objects.filter(
                name=val6, 
                product=price_version.product,  # 添加产品关联
                del_flag=False,
                attribution_expenses__in=['default', 'income']
            )
            if not product_charges:
                raise ParamError('查不到收费项%s' % val6, ErrorCode.PARAM_ERROR)

            if not product_charges.exists():
                raise ParamError('未找到产品(%s)下对应的费用项:%s，请检查！' % (price_version.product.name, val6), ErrorCode.PARAM_ERROR)

            if val3 == '一口价':
                charge_type = 'ZJ'
            elif val3 == '单价':
                charge_type = 'DJ'
            elif val3 == '递增价':
                charge_type = 'TZJ'
            else:
                raise ParamError('计费方式：%s不正确' % val3, ErrorCode.PARAM_ERROR)

            if val2 in ['计费分区', '应收计费分区']:
                val_l = 'CZ'
            elif val2 == '偏远分区':
                val_l = 'PZ'
            else:
                raise ParamError('分区方式：%s不正确' % val2, ErrorCode.PARAM_ERROR)
                
            query_params = {
                'zone_value': val1,
                'charge_type': charge_type,
                'currency': str(val7),
                'rank_start': safe_decimal(val4),
                'rank_end': safe_decimal(val5),
                'price': safe_decimal(val8),
                'charge': product_charges.first(),
                'price_version_id': cost_version_id,
                'zone_type': val_l,
                'del_flag': False
            }

            product = price_version.product
            product_id = price_version.product_id

            zone_settting = ZoneSetting.objects.filter(product=product, del_flag=False, zone_type=val_l).first()

            if not zone_settting:
                raise ParamError(f'价格版本对应产品({product.name})未设置{val_l},请设置',
                                 ErrorCode.PARAM_ERROR)
                                 
            # val1 = row[0]  # 分区 或 分区名称
            if val1 == '分区':
                zone_postcode = ProductZonePostCode.objects.filter(product_zone=zone_settting.zone, code=str(val1),
                                                                   del_flag=False).first()
            else:
                zone_postcode = ProductZonePostCode.objects.filter(product_zone=zone_settting.zone, name=str(val1),
                                                                   del_flag=False).first()

            if not zone_postcode:
                raise ParamError(f'价格版本对应产品({product.name})未设置{val_l}明细,请设置',
                                 ErrorCode.PARAM_ERROR)
            query_params['zone'] = zone_postcode

            defaults_params = {**query_params, 'remark': val9, **get_update_params(request, True)}

            # 移除所有值为 None 的键
            defaults_params = {k: v for k, v in defaults_params.items() if v is not None}
            
            # 第一阶段：只收集验证通过的数据，不写入数据库
            validated_data_list.append(defaults_params)
            
            logger.info(f'验证通过第{row_count}行数据, 分区: {val1} 、币种: {val7} 、'
                        f'等级起点: {val4}、等级终点: {val5}')
        
        # 第二阶段：原子性覆盖操作
        logger.info(f'第一阶段完成，共验证通过{len(validated_data_list)}条数据，开始第二阶段原子性覆盖')
        
        from django.db import transaction
        with transaction.atomic():
            # 在事务中先删除原有数据
            deleted_count = ProductRevenueVersionLine.objects.filter(
                price_version_id=cost_version_id, 
                del_flag=False
            ).update(del_flag=True)
            logger.info(f'事务中已删除价格版本({cost_version_id})下的{deleted_count}条原有记录')
            
            # 在事务中批量创建所有新数据
            new_records = []
            for data in validated_data_list:
                new_records.append(ProductRevenueVersionLine(**data))
            
            ProductRevenueVersionLine.objects.bulk_create(new_records)
            logger.info(f'事务中已批量创建{len(new_records)}条新记录')
        
        logger.info(f'覆盖导入成功完成，价格版本({cost_version_id})数据已完全更新')


# 导入产品销售定价成本价格版本excel
def upload_product_sale_cost_version(request, wb, table_index=0):
    # 获取第一张表
    table = wb.worksheets[table_index]
    for row in table.iter_rows(min_row=2, values_only=True):
        val1 = row[0]  # 产品编码
        val2 = row[1]  # 产品费用项编码
        val3 = row[2]  # 版本名称
        val4 = row[3]  # 开始时间
        val5 = row[4]  # 币种
        val6 = row[5]  # 备注

        if not val1 or not val2 or not val3 or not val4:
            raise ParamError('请填写产品编码、产品费用项编码、版本名称、开始时间、币种', ErrorCode.PARAM_ERROR)

        if isinstance(val4, datetime):
            val4 = val4.date()

        if ProductSaleCostVersion.objects.filter(version_name=val3, del_flag=False).count() != 0:
            raise ParamError('已经有对应的版本名称：%s' % val3, ErrorCode.PARAM_ERROR)

        # 获取产品
        try:
            product = Product.objects.get(code=val1, del_flag=False)
        except Product.DoesNotExist:
            raise ParamError('未找到对应的产品:%s，请检查！' % val1, ErrorCode.PARAM_ERROR)

        # 获取产品费用项
        try:
            product_charge = ProductCharge.objects.get(product=product, name=val2, del_flag=False,
                                                       attribution_expenses__in=['default', 'cost'])
        except ProductCharge.DoesNotExist:
            raise ParamError('未找到对应的产品:%s下的费用项:%s，请检查！' % (product.code, val2), ErrorCode.PARAM_ERROR)

        params = {'product': product, 'product_charge': product_charge, 'version_name': val3, 'start_time': val4,
                  'currency': str(val5).strip(), 'remark': val6}

        ProductSaleCostVersion.objects.create(**params, **get_update_params(request, True))


# 导入销售成本价格明细excel
def upload_product_sale_cost_version_line(request, wb, table_index=0):
    table = wb.worksheets[table_index]
    for row in table.iter_rows(min_row=2, values_only=True):
        val1 = row[0]  # 销售成本价格版本名称
        val2 = str(row[1]) if row[1] else ''  # 分区值
        val3 = row[2]  # 计费方式
        val4 = row[3]  # 币种
        val5 = row[4]  # 等级起点
        val6 = row[5]  # 等级终点
        val7 = row[6]  # 价格
        val8 = row[7]  # 基础价格
        val9 = row[8]  # 递增区间
        val10 = row[9]  # 四舍五入因子
        val11 = row[10]  # 备注

        if not val1 or not val3 or not val4:
            raise ParamError('请填写销售成本价格版本名称、计费方式、币种', ErrorCode.PARAM_ERROR)

        # 获取价格版本名称
        try:
            price_version = ProductSaleCostVersion.objects.get(version_name=str(val1).strip(), del_flag=False)
        except ProductSaleCostVersion.DoesNotExist:
            raise ParamError('未找到对应的收入价格版本:%s，请检查！' % val1, ErrorCode.PARAM_ERROR)

        if val3 == '总价':
            charge_type = 'ZJ'
        elif val3 == '单价':
            charge_type = 'DJ'
        elif val3 == '递增价':
            charge_type = 'TZJ'
        else:
            raise ParamError('计费方式：%s不正确' % val3, ErrorCode.PARAM_ERROR)
        params = {
            'price_version': price_version,
            'zone_value': val2,
            'charge_type': charge_type,
            'currency': str(val4).strip(),
            'rank_start': safe_decimal(val5),
            'rank_end': safe_decimal(val6),
            'price': safe_decimal(val7),
            'base_price': safe_decimal(val8, Decimal('0')),
            'increased_rank': safe_decimal(val9, Decimal('0')),
            'round_of_factor': safe_decimal(val10, Decimal('0')),
            'remark': val11,
        }

        # 移除所有值为 None 的键
        params = {k: v for k, v in params.items() if v is not None}

        ProductSaleCostVersionLine.objects.create(**params, **get_update_params(request, True))


def safe_decimal(value, default=None):
    if value in (None, ''):
        return default
    try:
        return Decimal(str(value))
    except InvalidOperation:
        return default


# 导入成本价格版本excel
def upload_cost_version_excel(request, wb, table_index=0):
    table = wb.worksheets[table_index]
    row_count = min_row = 2
    max_column = 6
    for row in table.iter_rows(min_row=min_row, values_only=True):

        row_count += 1
        for column in range(max_column + 1):
            logger.info(f'读取到第{row_count}行第{index_to_excel_column(column)}列值: {row[column]}')
            cell = table.cell(row=row_count, column=column + 1)
            if cell.data_type == 'f':
                raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列是公式: {row[column]}, '
                                 f'无法读取数据', ErrorCode.PARAM_ERROR)
        val1 = row[0]  # 产品编码
        val2 = row[1]  # 产品费用项编码
        val3 = row[2]  # 版本名称
        val4 = row[3]  # 开始时间
        val5 = row[4]  # 币种
        val6 = row[5]  # 备注

        if not val1 or not val2 or not val3 or not val4:
            raise ParamError('请填写产品编码、产品费用项编码、版本名称、开始时间、币种', ErrorCode.PARAM_ERROR)

        if isinstance(val4, datetime):
            val4 = val4.date()

        if ProductRevenueVersion.objects.filter(version_name=val3, del_flag=False).count() != 0:
            raise ParamError('已经有对应的版本名称：%s' % val3, ErrorCode.PARAM_ERROR)

        # 获取产品
        try:
            product = Product.objects.get(code=val1, del_flag=False)
        except Product.DoesNotExist:
            raise ParamError('未找到对应的产品:%s，请检查！' % val1, ErrorCode.PARAM_ERROR)

        # 获取产品费用项
        try:
            product_charge = ProductCharge.objects.get(product=product, name=val2, del_flag=False,
                                                       attribution_expenses__in=['default', 'cost'])
        except ProductCharge.DoesNotExist:
            raise ParamError('未找到对应的产品:%s下的费用项:%s，请检查！' % (product.code, val2), ErrorCode.PARAM_ERROR)

        params = {
            'product': product,
            'product_charge': product_charge,
            'version_name': val3,
            'start_time': val4,
            'currency': str(val5).strip(),
            'remark': val6,
        }
        ProductCostVersion.objects.create(**params, **get_update_params(request, True))


# 导入成本价格明细excel
def upload_cost_detail_excel(request, wb, table_index=0):
    table = wb.worksheets[table_index]
    if settings.SYSTEM_VERSION == 'V2':
        cost_version_id = request.data.get('cost_version_id')
        row_count = min_row = 2
        max_column = 8
        for row in table.iter_rows(min_row=min_row, values_only=True):

            row_count += 1
            for column in range(max_column + 1):
                logger.info(f'读取到第{row_count}行第{index_to_excel_column(column)}列值: {row[column]}')
                cell = table.cell(row=row_count, column=column + 1)
                if cell.data_type == 'f':
                    raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列是公式: {row[column]}, '
                                     f'无法读取数据', ErrorCode.PARAM_ERROR)
            val1 = row[0]  # 分区
            val2 = row[1]  # 分区方式
            val3 = row[2]  # 计费方式
            val4 = row[3]  # 等级起点
            val5 = row[4]  # 等级终点
            val6 = row[5]  # 费用项
            val7 = row[6]  # 币种
            val8 = row[7]  # 金额
            val9 = row[8]  # 备注

            if not val1 or not val3 or not val7 or not val8:
                raise ParamError('请填写成本价格版本分区、计费方式、币种、金额', ErrorCode.PARAM_ERROR)
            # 获取价格版本名称
            try:
                price_version = ProductCostVersion.objects.get(pk=cost_version_id, del_flag=False)
            except ProductCostVersion.DoesNotExist:
                raise ParamError('未找到对应的成本价格版本:%s，请检查！' % (cost_version_id), ErrorCode.PARAM_ERROR)

            service = price_version.service

            # 获取费用项
            try:
                service_charge = ServiceCharge.objects.get(service=service, name=val6, del_flag=False)
            except ServiceCharge.DoesNotExist:
                raise ParamError(f'未找到对应资源{service.name}的费用项:{val6}，请检查！', ErrorCode.PARAM_ERROR)

            if val3 == '总价' or val3 == '一口价':
                charge_type = 'ZJ'
            elif val3 == '单价':
                charge_type = 'DJ'
            elif val3 == '递增价':
                charge_type = 'TZJ'
            else:
                raise ParamError('计费方式：%s不正确' % (val3), ErrorCode.PARAM_ERROR)

            if val2 == '计费分区':
                zone_type = 'EF'
            elif val2 == '偏远分区':
                zone_type = 'PF'
            else:
                raise ParamError('分区类型：%s不正确' % (val2), ErrorCode.PARAM_ERROR)

            zone_settting = ServiceZoneSetting.objects.filter(service=service, del_flag=False,
                                                              zone_type=zone_type).first()
            if not zone_settting:
                raise ParamError(f'价格版本对应资源({service.name})未设置应付计费分区/偏远分区,请设置',
                                 ErrorCode.PARAM_ERROR)

            zone_postcode = ProductZonePostCode.objects.filter(product_zone=zone_settting.zone, code=str(val1),
                                                               del_flag=False).first()
            if not zone_postcode:
                raise ParamError(f'价格版本对应资源({service.name})未设置应付计费分区明细,请设置',
                                 ErrorCode.PARAM_ERROR)

            params = {
                'price_version': price_version,
                'zone': zone_postcode,
                'zone_type': zone_type,
                'zone_value': val1,
                'charge_type': charge_type,
                'rank_start': safe_decimal(val4),
                'rank_end': safe_decimal(val5),
                'charge': service_charge,
                'currency': str(val7).strip(),
                'price': safe_decimal(val8),
                'remark': val9,
            }

            # 移除所有值为 None 的键
            params = {k: v for k, v in params.items() if v is not None}

            ProductCostVersionLine.objects.create(**params, **get_update_params(request, True))
    else:
        row_count = min_row = 2
        max_column = 10
        for row in table.iter_rows(min_row=min_row, values_only=True):
            # 检查是否为空行（所有单元格都为空）
            if all(cell is None or str(cell).strip() == '' for cell in row):
                continue

            row_count += 1
            for column in range(max_column + 1):
                logger.info(f'读取到第{row_count}行第{index_to_excel_column(column)}列值: {row[column]}')
                cell = table.cell(row=row_count, column=column + 1)
                if cell.data_type == 'f':
                    raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列是公式: {row[column]}, '
                                     f'无法读取数据', ErrorCode.PARAM_ERROR)
            val1 = row[0]  # 成本价格版本名称
            val2 = str(row[1]) if row[1] else ''  # 分区值
            val3 = row[2]  # 计费方式
            val4 = row[3]  # 币种
            val5 = row[4]  # 等级起点
            val6 = row[5]  # 等级终点
            val7 = row[6]  # 价格
            val8 = row[7]  # 基础价格
            val9 = row[8]  # 递增区间
            val10 = row[9]  # 四舍五入因子
            val11 = row[10]  # 备注
            if not val1 or not val3 or not val4:
                raise ParamError('请填写成本价格版本名称、计费方式、币种', ErrorCode.PARAM_ERROR)

            # 获取价格版本名称
            try:
                price_version = ProductCostVersion.objects.get(version_name=str(val1).strip(), del_flag=False)
            except ProductCostVersion.DoesNotExist:
                raise ParamError('未找到对应的成本价格版本:%s，请检查！' % (val1), ErrorCode.PARAM_ERROR)

            if val3 == '总价':
                charge_type = 'ZJ'
            elif val3 == '单价':
                charge_type = 'DJ'
            elif val3 == '递增价':
                charge_type = 'TZJ'
            else:
                raise ParamError('计费方式：%s不正确' % (val3), ErrorCode.PARAM_ERROR)
            params = {
                'price_version': price_version,
                'zone_value': val2,
                'charge_type': charge_type,
                'currency': str(val4).strip(),
                'rank_start': safe_decimal(val5),
                'rank_end': safe_decimal(val6),
                'price': safe_decimal(val7),
                'base_price': safe_decimal(val8, Decimal('0')),
                'increased_rank': safe_decimal(val9, Decimal('0')),
                'round_of_factor': safe_decimal(val10, Decimal('0')),
                'remark': val11,
            }

            # 移除所有值为 None 的键
            params = {k: v for k, v in params.items() if v is not None}

            ProductCostVersionLine.objects.create(**params, **get_update_params(request, True))


def model_create_error_trap(e, model):
    # 唯一性冲突（Unique Constraint Violation）
    err_match = re.search(r"Duplicate entry '(.*?)' for key '(.*?)'", str(e))
    if err_match:
        duplicate_value = err_match.group(1)
        field_name = err_match.group(2)
        # 尝试处理复合字段名
        field_name_tail = field_name.split('.')[-1]
        if hasattr(model, field_name_tail):
            verbose_name = model._meta.get_field(field_name_tail).verbose_name
        else:
            verbose_name = field_name_tail
        raise ParamError(f"{verbose_name}: {duplicate_value} 已存在, 不能重复", ErrorCode.PARAM_ERROR)

    # 非空约束（NOT NULL Constraint Violation）
    # django.db.utils.IntegrityError: (1048, "Column 'amount' cannot be null")
    err_match = re.search(r"Column '(.*?)' cannot be null", str(e))
    if err_match:
        field_name = err_match.group(1)
        field_name_tail = field_name.split('.')[-1]
        if hasattr(model, field_name_tail):
            verbose_name = model._meta.get_field(field_name_tail).verbose_name
        else:
            verbose_name = field_name_tail
        raise ParamError(f"{verbose_name}: 该字段不能为空", ErrorCode.PARAM_ERROR)

    # 其他未知错误
    raise ParamError(f"其他错误: {str(traceback.format_exc())}", ErrorCode.PARAM_ERROR)


# 列索引转换成excel的列号
def index_to_excel_column(index):
    result = ""
    while index >= 0:
        # 计算当前位对应的字母
        remainder = index % 26
        result = chr(65 + remainder) + result
        # 更新索引值
        index = (index // 26) - 1
    return result


# 通用导入一维表格方法
def common_upload_excel(request, model, header_map, unique_config_map=None, relevance_field_map=None,
                        attachment_map=None, calc_fields=None, default_fields=None, workbook=None,
                        repetition_restrict=False, data_check=None, sheet_name=None):
    """
    :param request:
    :param model: 导入的模型类
    :param header_map: 导入的表头map, 例如
            header_map = [
                {'field_name': '订单号', 'field': 'customer_order_num', 'type': 'foreign_key',
                 'foreign_key_map': {'field': 'order_num', 'model': CustomerOrder}},
                {'field_name': '派送费金额', 'field': 'charge_total', 'type': 'Str'}
            ]
    :param unique_config_map: 导入的唯一键
    :param relevance_field_map: 导入的关联字段
    :param attachment_map: 保存导入的表格到模型类中
    :param calc_fields: 导入计算字段
    :param default_fields: 导入默认字段(给定默认值)
    # :param file_object: 不读取excel表格, 而是使用给定的文件对象
    :param workbook: 不读取excel表格, 而是使用给定的workbook
    :param repetition_restrict: 重复限制, 查询录入的数据是否有已存在的对象, 若有则不新增
    :param data_check: 数据校验, 若有数据校验, 则判断数据后返回状态: update(覆盖)/add(新增)/pass(无操作,不保存数据)
    :param sheet_name: 限制sheet
    :return:
    """
    logger.info(f'common_upload_excel start')
    unique_config_map = unique_config_map or {}
    relevance_field_map = relevance_field_map or {}
    calc_fields = calc_fields or {}
    default_fields = default_fields or {}
    # field_name_map = {i['field_name']: {'field': i['field'], 'type': i['type']} for i in header_map}
    field_name_map = {}
    foreign_key_query_map_list = []
    for item in header_map:
        field_name = item.pop('field_name')
        field_name_map[field_name] = item
        if item.get('type') == 'foreign_key_query':
            foreign_key_query_map_list.append(item)

    excel = request.FILES.get('file')
    if not workbook:
        # 将文件内容读取到内存中
        file_content = excel.read()
        # 使用 io.BytesIO 将内容包装成文件对象
        file_object = io.BytesIO(file_content)
        # 读取 Excel
        workbook = openpyxl.load_workbook(file_object, read_only=True)

    if sheet_name:
        try:
            sheet = workbook[sheet_name]
        except KeyError:
            # 捕获 KeyError 异常，表明指定的工作表名称不存在
            raise ParamError(f"Sheet {sheet_name} 不存在。", ErrorCode.PARAM_ERROR)
    else:
        sheet = workbook.worksheets[0]

    # 获取行数
    # max_row = sheet.max_row
    # print('max_row-->', max_row)

    excel_header_map = {}
    # many_to_many_field_map = {}
    # 读取表头
    print('time4.5-->', datetime.now())
    for row in sheet.iter_rows(max_row=1, values_only=True):
        for index, field_name in enumerate(row):
            print('row[index]-->', row[index], type(row[index]))
            if row[index] is None:
                break
            if row[index] in field_name_map:
                # if field_name_map[row[index]].get('type') == 'many_to_many_foreign_key':
                #     many_to_many_field_map[field_name] = index
                if field_name not in excel_header_map:
                    excel_header_map[field_name] = index
        break
    logger.info(f'common_upload_excel, excel_header_map: {excel_header_map}')
    print('time5-->', datetime.now())

    # model_object_ids = []
    row_count = 2
    for row in sheet.iter_rows(min_row=2, values_only=True):
        logger.info(f'读取行数据, 读取到第{row_count}行数据: {row}')
        print('time6-->', datetime.now())
        row_over = False
        # 当前行全部为空值
        # 去除many_to_many_foreign_key后所有的字段值
        list_row = []
        for excel_header_field_index in excel_header_map.values():
            list_row.append(row[excel_header_field_index])
        if not any(list_row):
            break
        params = {}
        foreign_key_object = {}
        many_to_many_map = {}
        # ignore_params = {}
        for field_name, column in excel_header_map.items():
            logger.info(f'读取到第{row_count}行第{index_to_excel_column(column)}列值: {row[column]}')
            field = field_name_map[field_name]['field']
            field_type = field_name_map[field_name].get('type', 'Str')
            try:
                # 如果单元格数据是公式, 则报错
                cell = sheet.cell(row=row_count, column=column + 1)
                if cell.data_type == 'f':
                    raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列是公式: {row[column]}, '
                                     f'无法读取数据', ErrorCode.PARAM_ERROR)
                if field_type.lower() == 'str':
                    field_value = None if row[column] is None else str(row[column]).strip()
                    length = field_name_map[field_name].get('length')
                    if length and len(field_value) > length:
                        raise ParamError(
                            f'第{row_count}行, 第{index_to_excel_column(column)}列, 字符串长度不允许超过: {length}',
                            ErrorCode.PARAM_ERROR)
                elif field_type.lower() == 'decimal':
                    if row[column]:
                        decimal_places = field_name_map[field_name].get('decimal_places')
                        if decimal_places is not None:
                            field_value = Decimal(row[column]).quantize(Decimal(f"0.{'0' * decimal_places}"),
                                                                        rounding=ROUND_HALF_UP)
                        else:
                            field_value = Decimal(row[column])
                    else:
                        field_value = None
                elif field_type.lower() == 'date':
                    # field_value = row[column]
                    if isinstance(row[column], datetime):
                        field_value = row[column]
                    elif isinstance(row[column], int):
                        field_value = datetime(1899, 12, 30) + timedelta(days=row[column])
                    else:
                        field_value = datetime.strptime(row[column], '%Y-%m-%d').date() if row[column] else None
                elif field_type.lower() == 'datetime':
                    if isinstance(row[column], datetime):
                        field_value = row[column]
                    else:
                        field_value = datetime.strptime(row[column], '%Y-%m-%d %H:%M:%S') if row[column] else None
                elif field_type.lower() == 'foreign_key':
                    foreign_key_map = field_name_map[field_name]['foreign_key_map']
                    print('foreign_key_map1-->', foreign_key_map)
                    # foreign_key_filed = list(foreign_key_map[field].keys())[0]
                    foreign_key_filed = foreign_key_map['field']
                    query_map = {foreign_key_filed: row[column]}
                    # print('query_map-->', query_map)
                    # foreign_key_model = list(foreign_key_map[field].values())[0]
                    foreign_key_model = foreign_key_map['model']
                    foreign_query = foreign_key_model.objects.filter(**query_map, del_flag=False)
                    if foreign_query.exists():
                        field_value = foreign_key_object[field] = foreign_query.last()
                        print(f'读取到外键的值, {foreign_key_filed}: {field_value}')
                    else:
                        foreign_key_type = foreign_key_map.get('type')
                        if foreign_key_type == 'auto_create':
                            create_method = foreign_key_map.get('create_method')
                            foreign_key_create_object = create_method(row[column])
                            field_value = foreign_key_object[field] = foreign_key_create_object
                        else:
                            table_name = foreign_key_model._meta.verbose_name if foreign_key_model._meta else \
                                foreign_key_model.__name__
                            verbose_name = foreign_key_model._meta.get_field(foreign_key_filed).verbose_name
                            not_found = field_name_map[field_name].get('not_found')
                            if not_found == 'pass':
                                row_over = True
                                continue
                            else:
                                raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列, '
                                                 f'{table_name}中未找到对应的{verbose_name}: {row[column]}',
                                                 ErrorCode.PARAM_ERROR)
                                # field_value = foreign_key_object[field] = None
                elif field_type.lower() == 'many_to_many_foreign_key':
                    # 查询/创建多对多关系
                    many_to_many_map[field] = field_name_map[field_name]['many_to_many_map']
                    target_model = many_to_many_map[field].get('target_model')
                    target_model_search_key = many_to_many_map[field].get('target_model_search_key')
                    target_query = target_model.objects.filter(**{target_model_search_key: row[column]},
                                                               del_flag=False).last()
                    if not target_query:
                        raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列, '
                                         f'未找到对应的{field_name_map[field_name]}: {row[column]}',
                                         ErrorCode.PARAM_ERROR)
                    many_to_many_map[field]['target_query'] = target_query
                    continue
                elif field_type.lower() == 'channelmap':
                    channel_map = field_name_map[field_name]['channel_map']
                    channel_map_trans = {v: k for k, v in dict(channel_map).items()}
                    field_value = channel_map_trans.get(row[column], row[column])
                elif field_type.lower() == 'ignore':
                    # ignore_params[field] = row[column]
                    continue
                else:
                    field_value = row[column]
            except Exception as e:
                raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列数据异常, '
                                 f'异常数据: {row[column]}, 错误: {e}', ErrorCode.PARAM_ERROR)
            params[field] = field_value
        if row_over:
            continue

        for calc_field, value in calc_fields.items():
            value = value.replace(' ', '')
            calc_list = re.split(r'([+-/*])', value)
            formula = ''
            for item in calc_list:
                if item in ['+', '-', '*', '/']:
                    formula += item
                else:
                    formula += str(params.get(item))
            print('formula-->', formula)
            params[calc_field] = eval(formula)

        for relevance_field, relevance_foreign_key_map in relevance_field_map.items():
            relevance_foreign_key = relevance_foreign_key_map.get('foreign_key')
            relevance_value = relevance_foreign_key_map.get('mapping')
            relevance_foreign_key_model_map = relevance_foreign_key_map.get('_model_map_')
            # 如果本身有值, 则不查外键, 本身没有值, 才查外键
            if params.get(relevance_field) is None:
                if relevance_foreign_key_model_map:
                    foreign_key_model = relevance_foreign_key_model_map['model']
                    foreign_key_key = relevance_foreign_key_model_map['key']
                    if params.get(relevance_foreign_key):
                        relevance_query = foreign_key_model.objects.filter(
                            **{foreign_key_key: params.get(relevance_foreign_key)}, del_flag=False
                        ).last()
                        if relevance_query:
                            params[relevance_field] = getattr(relevance_query, relevance_value)
                else:
                    params[relevance_field] = getattr(foreign_key_object.get(relevance_foreign_key), relevance_value)

        # 处理 foreign_key_query
        print('params-->', params)
        # for params_item in params:
        for foreign_key_query_item in foreign_key_query_map_list:
            foreign_key_map = foreign_key_query_item['foreign_key_map']
            field = foreign_key_query_item['field']
            print('foreign_key_map2-->', foreign_key_map)
            # foreign_key_filed = list(foreign_key_map[field].keys())[0]
            foreign_key_filed = foreign_key_map['field']
            query_item = {}
            if isinstance(foreign_key_filed, list):
                for filed_item in foreign_key_filed:
                    query_item[filed_item['inner_field']] = params.pop(filed_item['inner_field'])
            print('query_item-->', query_item)
            if not query_item:
                raise ParamError(f'未找到外键查询值', ErrorCode.PARAM_ERROR)
            foreign_key_model = foreign_key_map['model']
            foreign_query = foreign_key_model.objects.filter(**query_item, del_flag=False)
            if not foreign_query.exists():
                table_name = foreign_key_model._meta.verbose_name if foreign_key_model._meta else \
                    foreign_key_model.__name__
                verbose_name = foreign_key_model._meta.get_field(foreign_key_filed).verbose_name
                raise ParamError(f'{table_name}中未找到对应的{verbose_name}: {query_item}', ErrorCode.PARAM_ERROR)
            # field_value = foreign_key_object[field] = foreign_query.first()
            params[field] = foreign_query.last()

        logger.info(f'读取到第{row_count}行参数: {params}')

        model_object = None
        # unique_map = {'update': True, 'fields': ['order_num', 'parcel_num']}
        # 唯一索引, 若unique_map中的字段联合查询存在重复数据, 则默认对查出来的数据进行更新而不新增
        if unique_config_map and unique_config_map['fields']:
            unique_list = unique_config_map['fields']
            unique_map = {i: params.get(i) for i in unique_list}
            old_queryset = model.objects.filter(**unique_map, del_flag=False)
            if old_queryset.exists():  # 需要更新时才更新
                update_bool = unique_config_map.get('update', False)
                if update_bool:
                    old_queryset.update(**params, **get_update_params(request, False))
                    model_object = old_queryset.last()
                else:
                    raise ParamError(f'{model._meta.verbose_name}中已存在对应的数据{list(unique_map.values())}')

        # 重复限制
        if repetition_restrict:
            old_queryset = model.objects.filter(**params, del_flag=False)
            print('old_queryset-->', old_queryset)
            if old_queryset.exists:
                model_object = old_queryset.last()
            else:
                pass

        if not model_object:
            # model_object = model.objects.create(**params, **get_update_params(request, True))
            model_object = model(**params, **get_update_params(request, True))

        for default_field, default_value in default_fields.items():
            # params[default_field] = default_value
            if default_field == '_common_fields_map_':
                common_fields = default_value['fields']
                common_func = default_value['func']
                common_result = common_func(model_object)
                for index, common_field in enumerate(common_fields):
                    print('setattr?-->', model_object, common_field, common_result[index])
                    setattr(model_object, common_field, common_result[index])
            elif isinstance(default_value, types.FunctionType):
                setattr(model_object, default_field, default_value(model_object))
                # model_object.save(update_fields=[default_field])
            else:
                setattr(model_object, default_field, default_value)
                # model_object.save(update_fields=[default_field])
        # model_object.save()
        # model_object_ids.append(model_object.id)

        try:
            if data_check:
                check_result, old_queryset_check = data_check(model_object)
                logger.info(f'data_check-->{check_result}, {old_queryset_check}')
                if check_result == 'update':
                    old_queryset_check.update(**params, **get_update_params(request, False))
                elif check_result == 'add':
                    model_object.save()
                else:
                    pass
            else:
                model_object.save()

            print('model_object------------------->', model_object.__dict__)

        except IntegrityError as e:
            model_create_error_trap(e, model)

        # 创建多对多关系表
        if many_to_many_map:
            print('many_to_many_map-->', many_to_many_map)
            for field_name, column in excel_header_map.items():
                field_type = field_name_map[field_name].get('type', 'Str')
                if field_type == 'many_to_many_foreign_key':
                    field = field_name_map[field_name]['field']
                    middle_model = many_to_many_map[field].get('middle_model')
                    main_model_foreign_key = many_to_many_map[field].get('main_model_foreign_key')
                    target_model_foreign_key = many_to_many_map[field].get('target_model_foreign_key')
                    target_query = many_to_many_map[field].get('target_query')
                    exists_relation = middle_model.objects.filter(**{main_model_foreign_key: model_object},
                                                                  **{target_model_foreign_key: target_query},
                                                                  del_flag=False)
                    if not exists_relation.exists():
                        middle_model.objects.create(**{main_model_foreign_key: model_object},
                                                    **{target_model_foreign_key: target_query},
                                                    **get_update_params(request, is_create=True))

        # print('model_object-->', model_object)
        if attachment_map:
            if attachment_map.get('is_save_first'):
                if row_count == 1:
                    # 保存附件到第一个对象中
                    foreign_key = {attachment_map['foreign_key']: model_object}
                    attachment_map['model'].objects.create(name=excel, url=excel, **foreign_key,
                                                           **get_update_params(request, True))
            else:
                # 保存附件到每一个对象中
                foreign_key = {attachment_map['foreign_key']: model_object}
                attachment_map['model'].objects.create(name=excel, url=excel, **foreign_key,
                                                       **get_update_params(request, True))
        row_count += 1

    # 添加多对多字段值
    # row_count = 1
    # for row in sheet.iter_rows(min_row=2, values_only=True):
    #     list_row = []
    #     for many_to_many_field_index in many_to_many_field_map.values():
    #         list_row.append(row[many_to_many_field_index])
    #     if not any(list_row):
    #         break
    #     for field_name, column in many_to_many_field_map.items():
    #         # 创建多对多关系
    #         many_to_many_map = field_name_map[field_name]['many_to_many_map']
    #         middle_model = many_to_many_map.get('middle_model')
    #         target_model = many_to_many_map.get('target_model')
    #         main_model_foreign_key = many_to_many_map.get('main_model_foreign_key')
    #         target_model_foreign_key = many_to_many_map.get('target_model_foreign_key')
    #         target_model_search_key = many_to_many_map.get('target_model_search_key')
    #         target_query = target_model.objects.filter(**{target_model_search_key: row[column]},
    #                                                    del_flag=False).first()
    #         for model_object_id in model_object_ids:
    #             old_queryset = model.objects.filter(id=model_object_id, del_flag=False)
    #
    #             if not target_query:
    #                 continue
    #             exists_relation = middle_model.objects.filter(**{main_model_foreign_key: old_queryset.first()},
    #                                                           **{target_model_foreign_key: target_query},
    #                                                           del_flag=False)
    #             if exists_relation.exists():
    #                 continue
    #             else:
    #                 middle_model.objects.create(**{main_model_foreign_key: old_queryset.first()},
    #                                             **{target_model_foreign_key: target_query},
    #                                             **get_update_params(request, is_create=True))
    #             row_count += 1

    workbook.close()
    logger.info(f'common_upload_excel end')


def common_upload_many_sheet_excel(request, sheet_configs):
    """
    处理多 sheet Excel 数据导入，遍历配置并调用 common_upload_excel。
    :param request: 请求对象，包含上传的 Excel 文件
    :param sheet_configs: 包含所有 sheet 配置的列表
    :return: Response 对象
    """
    # 获取上传文件
    excel = request.FILES.get('file')
    if not excel:
        raise ParamError('未上传excel文件')

    # 读取文件内容
    file_content = excel.read()
    # 遍历配置，处理每个 sheet
    for config in sheet_configs:
        # 读取文件内容
        workbook = openpyxl.load_workbook(io.BytesIO(file_content), read_only=True)
        sheet_name = config.get('sheet_name')
        model = config.get('model')
        header_map = config.get('header_map')
        calc_fields = config.get('calc_fields', {})
        unique_config_map = config.get('unique_config_map', {})
        relevance_field_map = config.get('relevance_field_map', {})
        attachment_map = config.get('attachment_map', {})
        default_fields = config.get('default_fields', {})
        repetition_restrict = config.get('repetition_restrict', False)

        # 检查 sheet 是否存在
        if sheet_name not in workbook.sheetnames:
            workbook.close()
            raise ParamError(f'未找到 {sheet_name} 表')

        # 调用 common_upload_excel，捕获异常
        common_upload_excel(
            request=request,
            model=model,
            header_map=header_map,
            unique_config_map=unique_config_map,
            relevance_field_map=relevance_field_map,
            attachment_map=attachment_map,
            calc_fields=calc_fields,
            default_fields=default_fields,
            workbook=workbook,
            repetition_restrict=repetition_restrict,
            sheet_name=sheet_name,
        )

    return Response({'code': 200, 'msg': '所有 sheet 导入成功'}, status=200)


# 通用导入excel绑定关系方法
def common_upload_excel_bind(request, model, header_map, foreign_key_map=None):
    foreign_key_map = foreign_key_map or {}

    excel = request.FILES.get('file')
    # 将文件内容读取到内存中
    file_content = excel.read()
    # 使用 io.BytesIO 将内容包装成文件对象
    file_object = io.BytesIO(file_content)
    # 读取 Excel
    workbook = openpyxl.load_workbook(file_object, read_only=True)
    # 获取第一张表
    sheet = workbook.worksheets[0]

    excel_header_map = {}
    # 读取表头
    for row in sheet.iter_rows(max_row=1, values_only=True):
        for index, field in enumerate(row):
            if not row[index]:
                break
            if row[index] in header_map and field not in excel_header_map:
                excel_header_map[field] = index
        break

    row_count = 0
    for row in sheet.iter_rows(min_row=2, values_only=True):
        # print('row-->', row)
        params = {}
        foreign_key_object = {}
        main_query = None
        for field_name, column in excel_header_map.items():
            field, field_type = header_map[field_name]
            if field_type == 'primary_key':
                field_value = str(row[column]).strip()
                main_query = model.objects.filter(**{field_name: field_value}, del_flag=False).first()
                if not main_query:
                    table_name = model._meta.verbose_name if model._meta else model.__name__
                    raise ParamError(f'未查询到当前{table_name}: {field_value}', ErrorCode.PARAM_ERROR)
            elif field_type == 'foreign_key':
                print('foreign_key_map3-->', foreign_key_map)
                foreign_key_filed = foreign_key_map[field]['map_field']
                query_map = {foreign_key_filed: row[column]}
                foreign_key_model = foreign_key_map[field]['model']
                foreign_object = foreign_key_model.objects.filter(**query_map, del_flag=False)
                if not foreign_object.exists():
                    table_name = foreign_key_model._meta.verbose_name if foreign_key_model._meta else \
                        foreign_key_model.__name__
                    verbose_name = foreign_key_model._meta.get_field(foreign_key_filed).verbose_name
                    raise ParamError(f'{table_name}中未找到对应的{verbose_name}: {row[column]}', ErrorCode.PARAM_ERROR)
                field_value = foreign_key_object[field] = foreign_object.first()
                params[field] = field_value
            else:
                raise ParamError(f'不允许的字段类型: {field_type}', ErrorCode.PARAM_ERROR)
        if main_query is None:
            table_name = model._meta.verbose_name if model._meta else model.__name__
            raise ParamError(f'未查询到当前{table_name}', ErrorCode.PARAM_ERROR)
        if params:
            main_query.update(**params, **get_update_params(request, False))
        row_count += 1


# 通用导出一维表格方法
def common_export_excel(headers, data, export_type=None):
    """
    导出Excel表格的函数

    :param headers: 表头列表，例如 ['Name', 'Age', 'City']
    :param data: 数据列表，例如 [['Alice', 30, 'New York'], ['Bob', 25, 'Los Angeles']]
    :param export_type: 导出方式，可以是文件路径（如 'output.xlsx'）或 'stream' 表示返回二进制数据流
    :return: 如果 export_type 是 'stream'，返回二进制数据流；否则返回 None
    """
    logger.info(f'common_export_excel start')
    # 创建一个新的工作簿和工作表
    workbook = openpyxl.Workbook()
    sheet = workbook.active

    # 写入表头
    sheet.append(headers)

    # 写入数据
    for row in data:
        sheet.append(row)

    # output = None
    response = None

    # 根据导出方式处理
    if export_type == 'stream':
        # 将工作簿保存到内存中的二进制流
        # output = io.BytesIO()
        response = HttpResponse(content_type='application/msexcel')
        workbook.save(response)
        # workbook.save(output)
        # output.seek(0)  # 将文件指针移到开头
    else:
        path = export_type
        if not export_type:
            # 保存到指定路径
            path = settings.DEBIT_DIR_ALL + 'export_data.xlsx'
        workbook.save(path)
    logger.info(f'common_export_excel end')
    return response


# 判断邮编是否包含偏远地区
def check_purpose_code_is_remote(product, warehouse):
    if not warehouse:
        return False
    purpose_codes_query = get_zone_postcode_list(
        customer_order=None,
        product=product,
        country_code=warehouse.country_code,
        zone_type='Buyer'
    ).filter(del_flag=False).values_list('product_zone__name', flat=True).distinct()

    # 将 purpose_code 转换为列表存入字典中
    order_data = {'purpose_code': list(purpose_codes_query)}
    # conditions = "purpose_code in 偏远"
    conditions = "'purpose_code' in '偏远'"
    try:
        result = simple_eval(conditions, names=order_data)
    except SyntaxError:
        raise ParamError(f'产品收费项收费规则的适用条件语法错误, 错误规则: {conditions}')
    print("判断是否为偏远地区:", conditions, "is", result)
    return result


# 自动生成订单的reference_id
def create_order_reference_id(reference_ids):
    logger.info(f'reference_ids-->, {reference_ids}')
    # 通过所有商品的reference_id汇总成集合, 用逗号隔开生成订单reference_id
    order_reference_id = ''
    for index, reference_id in enumerate(reference_ids):
        new_order_reference_id = order_reference_id
        if index == 0:
            new_order_reference_id = str(reference_id)
        else:
            new_order_reference_id += ',' + str(reference_id)
        if len(new_order_reference_id) > 256:
            break
        else:
            order_reference_id = new_order_reference_id
    return order_reference_id


# 校验包裹号
def judge_parcel_num_rule(parcel_num, customer_order: CustomerOrder, raise_error=False):
    # 如果收件人的地址的平台目的仓选择FBA, 则校验包裹号
    if customer_order.receiver and customer_order.receiver.delivery_address_type == 'FBA':
        # print('2-->', parcel_num)
        # 包裹号需要以FBA开头，结尾是U+6位数字
        parcel_num = str(parcel_num)
        if parcel_num.startswith('FBA') and len(parcel_num) >= 7 and parcel_num[-7] == 'U' and \
                parcel_num[-6:].isdecimal():
            return True
        else:
            if raise_error:
                raise ParamError(f'包裹号 {parcel_num} 错误, 请输入正确的fba包裹号: FBA号+U+6位数字',
                                 ErrorCode.PARAM_ERROR)
            else:
                return False
    return True


# 自动生成系统包裹号
def create_sys_parcel_num(customer_order):
    # 若包裹号在标准内(结尾是U+6位数字), 则根据包裹号生成包裹系统唯一码sys_parcel_num(FBA号+包裹号的结尾), 否则按包裹次序生成唯一码
    # parcel_num = str(parcel_num)
    # if parcel_num[-7:].startswith('U') and parcel_num[-6:].isdecimal():
    #     sys_parcel_num = f"{customer_order.order_num}-{parcel_num[-7:]}"
    # else:

    # 因为包裹号结尾部分可能重复, 所以不使用包裹号结尾生成系统包裹号
    parcel_order = Parcel.objects.filter(customer_order=customer_order, del_flag=False)

    if parcel_order.exists():
        serial_number = parcel_order.count() + 1
    else:
        serial_number = 1
    sys_parcel_num = f"{customer_order.order_num}-{str(serial_number).rjust(5, '0')}"

    while True:
        if Parcel.objects.filter(sys_parcel_num=sys_parcel_num, del_flag=False).exists():
            serial_number += 1
            sys_parcel_num = f"{customer_order.order_num}-{str(serial_number).rjust(5, '0')}"
        else:
            break
    return sys_parcel_num


# 获取单一商品初始包裹号
def get_parcel_num_single_init(parcel_num_pre):
    parcel_num = parcel_num_pre + 'U000001'
    suffix = 1
    last_parcel_order = Parcel.objects.filter(parcel_num__startswith=parcel_num_pre, del_flag=False).last()
    if last_parcel_order is not None:
        suffix_temp = last_parcel_order.parcel_num[-6:]
        if suffix_temp.isdecimal():
            suffix = int(suffix_temp) + 1
            parcel_num = parcel_num_pre + 'U' + str(suffix).rjust(6, '0')
    while True:
        if Parcel.objects.filter(parcel_num=parcel_num, del_flag=False).exists():
            suffix += 1
            parcel_num = parcel_num_pre + 'U' + str(suffix).rjust(6, '0')
        else:
            break
    print(f'生成单一商品初始包裹号: {parcel_num}')
    return parcel_num


# 获取单一商品包裹号
def get_parcel_num_single(parcel_num_pre, suffix):
    parcel_num = parcel_num_pre + 'U' + str(suffix + 1).rjust(6, '0')
    while True:
        if Parcel.objects.filter(parcel_num=parcel_num, del_flag=False).exists():
            suffix += 1
            parcel_num = parcel_num_pre + 'U' + str(suffix).rjust(6, '0')
        else:
            break
    print(f'生成单一商品包裹号: {parcel_num}')
    return parcel_num


# 出仓校验(包裹)
def outbound_verify_parcel(parcel, request):
    # 校验包裹状态 ==>  FBA号 或者 中性箱号
    if not parcel:
        return respond_with_error(request, "未找到该包裹")
    if not parcel.is_weighing:
        return respond_with_error(request, "未入仓包裹，请先入仓")
    if parcel.out_warehouse:
        return respond_with_error(request, "该包裹已出仓，请勿重复出仓")
    if parcel.intercept_mark:
        return respond_with_error(request, "拦截订单不可出仓，请联系客服处理")
    # 校验订单状态
    if parcel.customer_order.order_status == 'VO':
        return respond_with_error(request, "作废订单，请拦截")
    if parcel.customer_order.is_intercept:
        return respond_with_error(request, "订单被拦截, 无法出仓")
    return 0


# 序列化器中模型类一对多外键数据的创建
def foreign_key_data_create(related_data, model, foreign_key, instance, user):
    for data in related_data:
        new_related_object = model.objects.create(**data)
        setattr(new_related_object, foreign_key, instance)
        new_related_object.create_by = user
        new_related_object.save()


# 序列化器中模型类一对多外键数据的修改
def foreign_key_data_update(related_data, related_model, foreign_key, instance, user):
    related_model.objects.filter(**{foreign_key: instance}).update(del_flag=True)
    for data in related_data:
        if data.get('id'):
            new_related_queryset = related_model.objects.filter(id=data.get('id'))
            data['update_by'] = user
            data['update_date'] = datetime.now()
            data['del_flag'] = False
            new_related_queryset.update(**data)
        else:
            new_related_object = related_model.objects.create(**data)
            setattr(new_related_object, foreign_key, instance)
            new_related_object.create_by = user
            new_related_object.save()


# 成本解锁公共函数
@transaction.atomic
def cost_unlock_common(queryset, request, judge_lock_account=False, set_order_status=None,
                       order_num='order_num'):
    # share_cost_order = queryset.filter(is_share_cost=True)
    fail_orders = 0
    share_account_payables = []
    payment_account_payables = []
    for query in queryset:
        if hasattr(query, 'is_share_cost') and query.is_share_cost:
            share_account_payables.append(getattr(query, order_num))
            # raise ParamError(f'请先取消单据的成本分摊: {getattr(query, order_num)}', ErrorCode.PARAM_ERROR)
            continue
        # if AccountPayable.objects.filter(payment_num__isnull=False, is_adjust=False,
        #                                  order_num=getattr(query, order_num), del_flag=False).count() > 0:
        if judge_lock_account:
            is_lock_order(query, 'cost')
        account_payables = AccountPayable.objects.filter(order_num=getattr(query, order_num), is_adjust=False)
        error_payables = account_payables.filter(is_payment=True)
        if error_payables.exists():
            payment_account_payables.append(getattr(query, order_num))
            continue
        account_payables.update(del_flag=True)
        query.is_cost_lock = False
        query.account_time = None
        query.gross_profit = None
        query.gross_currency = None
        query.cost = None
        query.update_by = get_update_params(request)['update_by']
        query.update_date = get_update_params(request)['update_date']
        if set_order_status is not None:
            query.order_status = set_order_status
        query.save()
    msgs = []
    if share_account_payables:
        msgs.append(f'已成本分摊单号({len(share_account_payables)}个): {", ".join(share_account_payables)}')
    if payment_account_payables:
        msgs.append(f'已生成付款单单号({len(payment_account_payables)}个): {", ".join(payment_account_payables)}')
    if any([share_account_payables, payment_account_payables]):
        msg = '成本解锁部分失败, ' + ', '.join(msgs)
    else:
        msg = '成本解锁成功'
    return fail_orders, msg


# 成本分摊: 按重量/体积分摊到FBA订单/小包单上
def cost_share_to_order_common(main_order, divided_orders, charge_out, user, charge_model, share_type='charge_weight',
                               stowage_map=None):
    """
    @param main_order: 被分摊的单据
    @param divided_orders: 分摊到的单据
    @param charge_out:
    @param user:
    @param charge_model:
    @param share_type:
    @param stowage_map: 订单可能会部分配载到海运单上, 所以按计费重分摊则是按订单配载到海运单上的配载计费重分摊, 按确认计费重或体积分摊同理;
        进出口报关单直接关联的是海运单, 所以进出口报关单的分摊和海运提单相同, 按真实配载分摊
    @return:
    """
    share_type_map = dict(Charge.ALLOCATION_TYPE)
    # share_type in ['charge_weight', 'confirm_charge_weight', 'volume']
    if isinstance(main_order, TruckOrder):
        main_order_num = main_order.truck_order_num
    elif isinstance(main_order, (Clearance, ClearanceOut)):
        main_order_num = main_order.clearance_num
    else:
        main_order_num = main_order.order_num
    # 总金额
    charge_total = charge_out.charge_total

    share_rate_map = {}
    # 进出口报关单和海运提单的成本分摊需要用配载计费重/配载确认计费重/配载体积分摊
    if stowage_map:
        total_share_data = 0
        for customer_order_id, relate_ocean in stowage_map.items():
            if not relate_ocean.customer_order_num.carton:
                raise ParamError(f'订单: {relate_ocean.customer_order_num.order_num} 的件数为0, 不能分摊成本',
                                 ErrorCode.PARAM_ERROR)
            if getattr(relate_ocean.customer_order_num, share_type) is None:
                share_type_format = dict(Charge.ALLOCATION_TYPE).get(share_type)
                raise ParamError(f'订单: {relate_ocean.customer_order_num.order_num} 的{share_type_format}为空, '
                                 f'不能分摊成本', ErrorCode.PARAM_ERROR)
            order_share_data = getattr(relate_ocean.customer_order_num, share_type) * \
                               relate_ocean.freight_num / relate_ocean.customer_order_num.carton
            share_rate_map[customer_order_id] = order_share_data
            total_share_data += order_share_data
            logger.info(
                f'订单/小包单: {relate_ocean.customer_order_num.order_num} 的配载分摊基数为: {order_share_data}')
    else:
        # total_share_data = divided_orders.aggregate(total=Sum(share_type))['total'] or 0
        # total_share_data = sum([getattr(divided_order, share_type) for divided_order in divided_orders
        #                         if getattr(divided_order, share_type)])
        total_share_data = 0
        for divided_order in divided_orders:
            order_share_data = getattr(divided_order, share_type)
            if order_share_data is None:
                raise ParamError(f'订单{divided_order.order_num}没有{share_type_map.get(share_type)}',
                                 ErrorCode.PARAM_ERROR)
            share_rate_map[divided_order.id] = order_share_data
            total_share_data += order_share_data
            logger.info(f'订单/小包单: {divided_order.order_num} 的分摊基数为: {order_share_data}')
    logger.info(f'主单: {main_order_num} 的分摊总数为: {total_share_data}')
    if total_share_data <= 0:
        raise ParamError(f'成本分摊失败: 订单的总{share_type_map.get(share_type)}不大于0', ErrorCode.PARAM_ERROR)
        # return False
    # 计算分摊比例
    # share_rate_map = {}
    # for divided_order in divided_orders:
    #     order_share_data = getattr(divided_order, share_type)
    #     share_rate_map[divided_order.id] = order_share_data / total_share_data
    #     print('分摊比例: ', order_share_data, total_share_data)

    # 按比例计算每个parcel_customer_order的计费金额
    for divided_order in divided_orders:
        share_rate = share_rate_map.get(divided_order.id) / total_share_data
        # share_rate = getattr(divided_order, share_type) / total_share_data
        share_amount = (charge_total * share_rate).quantize(Decimal('0.01'), rounding='ROUND_HALF_UP')
        charge_count = (charge_out.charge_count * share_rate).quantize(Decimal('0.000001'), rounding='ROUND_HALF_UP')

        # 如果有历史分摊的，先删除 fix:删除对应费用的分摊信息
        charge_queryset = charge_model.objects.filter(
            del_flag=False,
            is_share=True,
            customer_order_num=divided_order.id,
            charge=charge_out.charge,
            share_charge_id=main_order_num
        )
        if charge_queryset.count() > 0:
            charge_queryset.update(
                del_flag=True
            )

        charge_model.objects.create(
            charge=charge_out.charge,
            customer_order_num=divided_order,
            charge_rate=charge_out.charge_rate,
            charge_count=charge_count,
            charge_total=share_amount,
            currency_type=charge_out.currency_type,
            current_exchange=charge_out.current_exchange,
            account_charge=share_amount,
            supplier=charge_out.supplier,
            is_share=True,
            is_system=True,
            base_price=charge_out.base_price,
            share_charge_id=main_order_num,
            **get_update_params_by_user(user, True)
        )
        logger.info(
            f"运输单{main_order_num}下的订单/小包单{divided_order.order_num}的{share_type}分摊成本为{share_amount}")
    return True


# 成本分摊: 平均分摊到FBA订单/小包单上
def cost_share_to_order_average(main_order, customer_orders, charge_out, user, charge_model):
    print('cost_share_to_order_average-->')
    if isinstance(main_order, TruckOrder):
        main_order_num = main_order.truck_order_num
    elif isinstance(main_order, (Clearance, ClearanceOut)):
        main_order_num = main_order.clearance_num
    else:
        main_order_num = main_order.order_num
    # 总金额
    transport_charge_total = charge_out.charge_total
    logger.info(f'主单: {main_order_num} 的分摊总数为: {len(customer_orders)}')
    # 计算每个parcel_customer_order的计费金额
    for customer_order in customer_orders:
        share_amount = round(transport_charge_total / len(customer_orders), 2)

        charge_queryset = charge_model.objects.filter(
            ~Q(is_share=True),
            customer_order_num=customer_order,
            charge=charge_out.charge,
            del_flag=False
        )
        if charge_queryset.exists():
            raise ParamError(
                f'{customer_order.order_num}订单的成本已存在{charge_out.charge.name}计费项, 不能分摊重复的计费项',
                ErrorCode.PARAM_ERROR)
        charge_queryset = charge_model.objects.filter(
            charge=charge_out.charge,
            customer_order_num=customer_order,
            share_charge_id=main_order_num,
            is_share=True,
            del_flag=False
        )
        if charge_queryset.exists():
            charge_queryset.update(del_flag=True)

        charge_model.objects.create(
            charge=charge_out.charge,
            customer_order_num=customer_order,
            charge_rate=charge_out.charge_rate,
            charge_count=1,
            charge_total=share_amount,
            currency_type=charge_out.currency_type,
            current_exchange=charge_out.current_exchange,
            account_charge=share_amount,
            supplier=charge_out.supplier,
            is_share=True,
            is_system=True,
            base_price=charge_out.base_price,
            share_charge_id=main_order_num,
            **get_update_params_by_user(user, True)
        )
        # main_order.is_share_revenue = True
        # main_order.save()
        logger.info(f"运输单{main_order_num}下的订单/小包单{customer_order.order_num}的按订单分摊成本为{share_amount}")
    return True


# 成本分摊: 按计费重分摊到小包单上(弃用)
def cost_share_to_packet_by_weight(
        main_order_num: str,
        parcel_customer_orders: List[ParcelCustomerOrder],
        charge_out: Union[
            CustomerOrderChargeOut, MasterOrderChargeOut,
            OceanOrderChargeOut, TruckOrderChargeOut, CollectOrderChargeOut, ClearanceChargeOut
        ],
        user
):
    """
    按计费重分摊到小包单上
    @param main_order_num: 被分摊的单据号
    @param parcel_customer_orders:
    @param charge_out:
    @param user:
    @return:
    """
    # 总金额
    charge_total = charge_out.charge_total

    # 计算parcel_customer_orders的总重量
    total_weight = sum([parcel_customer_order.charge_weight for parcel_customer_order in parcel_customer_orders
                        if parcel_customer_order.charge_weight])
    if total_weight <= 0:
        return False

    # 计算每个parcel_customer_order的计费重量比例
    parcel_weight_rate_map = {}
    for parcel_customer_order in parcel_customer_orders:
        if parcel_customer_order.charge_weight:
            parcel_weight_rate_map[parcel_customer_order.id] = parcel_customer_order.charge_weight / total_weight
        else:
            parcel_weight_rate_map[parcel_customer_order.id] = 0

    # 按比例计算每个parcel_customer_order的计费金额
    for parcel_customer_order in parcel_customer_orders:
        share_rate = parcel_weight_rate_map.get(parcel_customer_order.id)
        share_amount = charge_total * share_rate

        # 如果有历史分摊的，先删除 fix:删除对应费用的分摊信息
        charge_queryset = ParcelOrderChargeOut.objects.filter(
            del_flag=False,
            is_share=True,
            customer_order_num=parcel_customer_order.id,
            charge=charge_out.charge,
            share_charge_id=main_order_num
        )
        if charge_queryset.count() > 0:
            charge_queryset.update(
                del_flag=True
            )

        ParcelOrderChargeOut.objects.create(
            charge=charge_out.charge,
            customer_order_num=parcel_customer_order,
            charge_rate=charge_out.charge_rate,
            charge_count=share_rate * charge_out.charge_count,
            charge_total=share_amount,
            currency_type=charge_out.currency_type,
            current_exchange=charge_out.current_exchange,
            account_charge=share_amount,
            supplier=charge_out.supplier,
            is_share=True,
            is_system=True,
            base_price=charge_out.base_price,
            share_charge_id=main_order_num,
            **get_update_params_by_user(user, True)
        )
        logger.info(f"单据{main_order_num}下的小包单{parcel_customer_order.order_num}的计费重分摊成本为{share_amount}")
    return True


# 成本分摊: 按体积分摊到小包单上(弃用)
def cost_share_to_packet_by_volume(
        main_order_num: str,
        parcel_customer_orders: List[ParcelCustomerOrder],
        charge_out: Union[
            CustomerOrderChargeOut, MasterOrderChargeOut,
            OceanOrderChargeOut, TruckOrderChargeOut, CollectOrderChargeOut, ClearanceChargeOut
        ],
        user
):
    """
    按体积分摊到小包单上
    @param main_order_num: 订单号
    @param parcel_customer_orders:
    @param charge_out:
    @param user:
    @return:
    """
    # 总金额
    charge_total = charge_out.charge_total

    # 计算parcel_customer_orders的总重量
    total_volume = sum([parcel_customer_order.volume for parcel_customer_order in parcel_customer_orders
                        if parcel_customer_order.volume])
    if total_volume <= 0:
        return False

    # 计算每个parcel_customer_order的体积比例
    parcel_volume_rate_map = {}
    for parcel_customer_order in parcel_customer_orders:
        if parcel_customer_order.volume:
            parcel_volume_rate_map[parcel_customer_order.id] = parcel_customer_order.volume / total_volume
        else:
            parcel_volume_rate_map[parcel_customer_order.id] = 0

    # 按比例计算每个parcel_customer_order的计费金额
    for parcel_customer_order in parcel_customer_orders:
        share_rate = parcel_volume_rate_map.get(parcel_customer_order.id)
        share_amount = charge_total * share_rate

        # 如果有历史分摊的，先删除 fix:删除对应费用的分摊信息
        charge_queryset = ParcelOrderChargeOut.objects.filter(
            del_flag=False,
            is_share=True,
            customer_order_num=parcel_customer_order.id,
            charge=charge_out.charge,
            share_charge_id=main_order_num
        )
        if charge_queryset.count() > 0:
            charge_queryset.update(
                del_flag=True
            )

        ParcelOrderChargeOut.objects.create(
            charge=charge_out.charge,
            customer_order_num=parcel_customer_order,
            charge_rate=charge_out.charge_rate,
            charge_count=share_rate * charge_out.charge_count,
            charge_total=share_amount,
            currency_type=charge_out.currency_type,
            current_exchange=charge_out.current_exchange,
            account_charge=share_amount,
            supplier=charge_out.supplier,
            is_share=True,
            is_system=True,
            base_price=charge_out.base_price,
            share_charge_id=main_order_num,
            **get_update_params_by_user(user, True)
        )
        logger.info(
            f"单据{main_order_num}下的小包单{parcel_customer_order.order_num}包裹的体积分摊成本为{share_amount}")
    return True


# 成本分摊: 按订单分摊到小包单上(弃用)
def cost_share_to_packet_by_order(
        main_order_num: str,
        parcel_customer_orders: List[ParcelCustomerOrder],
        customer_order_charge: Union[CustomerOrderChargeOut, OceanOrderChargeOut, TruckOrderChargeOut,
        CollectOrderChargeOut, ClearanceChargeOut],
        user
):
    # 总金额
    charge_total = customer_order_charge.charge_total

    # 计算每个parcel_customer_order的计费金额
    for parcel_customer_order in parcel_customer_orders:
        share_amount = round(charge_total / len(parcel_customer_orders), 2)

        charge_queryset = ParcelOrderChargeOut.objects.filter(
            del_flag=False,
            is_share=True,
            customer_order_num=parcel_customer_order.id,
            share_charge_id=main_order_num
        )
        if charge_queryset.count() > 0:
            charge_queryset.update(
                del_flag=True
            )

        ParcelOrderChargeOut.objects.create(
            charge=customer_order_charge.charge,
            customer_order_num=parcel_customer_order,
            charge_rate=customer_order_charge.charge_rate,
            charge_count=1,
            charge_total=share_amount,
            currency_type=customer_order_charge.currency_type,
            current_exchange=customer_order_charge.current_exchange,
            account_charge=share_amount,
            supplier=customer_order_charge.supplier,
            is_share=True,
            is_system=True,
            base_price=customer_order_charge.base_price,
            share_charge_id=main_order_num,
            **get_update_params_by_user(user, True)
        )
        logger.info(
            f"单据{main_order_num}下的小包单{parcel_customer_order.order_num}包裹的按订单分摊成本为{share_amount}")
    return True


# 收入分摊: 按重量/体积分摊到FBA订单/小包单上
def revenue_share_to_order_common(main_order, divided_orders, charge_in, user, charge_model,
                                  share_type='charge_weight'):
    share_type_map = dict(Charge.ALLOCATION_TYPE)
    # share_type in ['charge_weight', 'confirm_charge_weight', 'volume']
    print('revenue_share_by_weight-->')
    if isinstance(main_order, TruckOrder):
        main_order_num = main_order.truck_order_num
    elif isinstance(main_order, (Clearance, ClearanceOut)):
        main_order_num = main_order.clearance_num
    else:
        main_order_num = main_order.order_num
    # 总金额
    transport_charge_total = charge_in.charge_total

    total_share_data = divided_orders.aggregate(total=Sum(share_type))['total'] or 0
    if total_share_data <= 0:
        raise ParamError(f'收入分摊失败: 订单的总{share_type_map.get(share_type)}不大于0', ErrorCode.PARAM_ERROR)
        # return False

    # 计算每个订单的分摊比例
    share_rate_map = {}
    for divided_order in divided_orders:
        order_share_data = getattr(divided_order, share_type)
        if order_share_data is None:
            raise ParamError(f'订单{divided_order.order_num}没有{share_type_map.get(share_type)}',
                             ErrorCode.PARAM_ERROR)
        share_rate_map[divided_order.id] = order_share_data
        logger.info(f'订单/小包单: {divided_order.order_num} 的分摊基数为: {order_share_data} / {total_share_data}')

    logger.info(f'主单: {main_order_num} 的分摊总数为: {total_share_data}')

    # 按比例计算每个单的计费金额
    for divided_order in divided_orders:
        share_rate = share_rate_map[divided_order.id] / total_share_data
        share_amount = (transport_charge_total * share_rate).quantize(Decimal('0.01'), rounding='ROUND_HALF_UP')
        charge_count = (charge_in.charge_count * share_rate).quantize(Decimal('0.000001'), rounding='ROUND_HALF_UP')

        # charge_queryset = charge_model.objects.filter(
        #     ~Q(is_share=True),
        #     customer_order_num=divided_order,
        #     charge=charge_in.charge,
        #     del_flag=False
        # )
        # if charge_queryset.exists():
        #     raise ParamError(
        #         f'{divided_order.order_num}订单的收入已存在{charge_in.charge.name}计费项, 不能分摊重复的计费项',
        #         ErrorCode.PARAM_ERROR)
        # 如果有历史分摊的，先删除 fix:删除对应费用的分摊信息
        charge_queryset = charge_model.objects.filter(
            is_share=True,
            customer_order_num=divided_order,
            charge=charge_in.charge,
            share_charge_id=main_order_num,
            del_flag=False
        )
        if charge_queryset.exists():
            charge_queryset.update(
                del_flag=True
            )

        share_charge = charge_model.objects.create(
            charge=charge_in.charge,
            customer_order_num=divided_order,
            charge_rate=charge_in.charge_rate,
            charge_count=charge_count,
            charge_total=share_amount,
            currency_type=charge_in.currency_type,
            current_exchange=charge_in.current_exchange,
            account_charge=share_amount,
            customer=divided_order.customer,
            is_share=True,
            is_system=True,
            base_price=charge_in.base_price,
            share_charge_id=main_order_num,
            **get_update_params_by_user(user, True)
        )
        # if divided_order.is_revenue_lock:
        #     debit_date = getattr(divided_order, 'check_in_time', None) or getattr(main_order, 'plan_visit_time', None)
        #     collect_order_revenue_share_adjust(share_charge, user, debit_date=debit_date)
        logger.info(
            f"运输单{main_order_num}下的订单/小包单{divided_order.order_num}的{share_type}分摊收入为{share_amount}")
    return True


# 收入分摊: 平均分摊到FBA订单上
def revenue_share_to_order_average(main_order, divided_orders, charge_in, user, charge_model):
    print('average_share_to_order-->')
    if isinstance(main_order, TruckOrder):
        main_order_num = main_order.truck_order_num
    elif isinstance(main_order, (Clearance, ClearanceOut)):
        main_order_num = main_order.clearance_num
    else:
        main_order_num = main_order.order_num
    # 总金额
    transport_charge_total = charge_in.charge_total

    logger.info(f'主单: {main_order_num} 的分摊总数为: {len(divided_orders)}')

    # 计算分摊比例
    for divided_order in divided_orders:
        share_amount = round(transport_charge_total / len(divided_orders), 2)

        # charge_queryset = charge_model.objects.filter(
        #     ~Q(is_share=True),
        #     customer_order_num=divided_order,
        #     charge=charge_in.charge,
        #     del_flag=False
        # )
        # if charge_queryset.exists():
        #     raise ParamError(
        #         f'{divided_order.order_num}订单的收入已存在{charge_in.charge.name}计费项, 不能分摊重复的计费项',
        #         ErrorCode.PARAM_ERROR)
        charge_queryset = charge_model.objects.filter(
            charge=charge_in.charge,
            customer_order_num=divided_order,
            share_charge_id=main_order_num,
            is_share=True,
            del_flag=False
        )
        if charge_queryset.exists():
            charge_queryset.update(del_flag=True)

        share_charge = charge_model.objects.create(
            charge=charge_in.charge,
            customer_order_num=divided_order,
            charge_rate=charge_in.charge_rate,
            charge_count=1,
            charge_total=share_amount,
            currency_type=charge_in.currency_type,
            current_exchange=charge_in.current_exchange,
            account_charge=share_amount,
            customer=divided_order.customer,
            is_share=True,
            is_system=True,
            base_price=charge_in.base_price,
            share_charge_id=main_order_num,
            **get_update_params_by_user(user, True)
        )
        # if divided_order.is_revenue_lock:
        #     debit_date = getattr(divided_order, 'check_in_time', None) or getattr(main_order, 'plan_visit_time', None)
        #     collect_order_revenue_share_adjust(share_charge, user, debit_date=debit_date)
        logger.info(f"运输单{main_order_num}下的订单/小包单{divided_order.order_num}的按订单分摊收入为{share_amount}")
    return True


# FBA订单的成本分摊
# def customer_order_cost_share(queryset, request):
#     fail_orders = 0
#     for query in queryset:
#         # 查询关联的小包单
#         parcel_customer_orders = ParcelCustomerOrder.objects.filter(
#             big_parcel__parcel_outbound_order__customer_order=query.id,
#             del_flag=False
#         )
#         if parcel_customer_orders.count() == 0:
#             fail_orders += 1
#             continue
#
#         # 获取订单下的成本明细
#         customer_order_charges = CustomerOrderChargeOut.objects.filter(customer_order_num=query, del_flag=False)
#         if customer_order_charges.count() == 0:
#             fail_orders += 1
#             continue
#
#         for customer_order_charge in customer_order_charges:
#             # 获取费用对应的分摊类型
#             share_type = customer_order_charge.charge.share_type
#             if share_type == 'weight':
#                 # 按计费重分摊
#                 result = cost_share_by_weight(query.order_num, parcel_customer_orders, customer_order_charge,
#                                               request.user)
#                 if not result:
#                     fail_orders += 1
#                     continue
#             elif share_type == 'volume':
#                 fail_orders += 1
#                 result = cost_share_by_volume(query.order_num, parcel_customer_orders, customer_order_charge,
#                                               request.user)
#                 if not result:
#                     fail_orders += 1
#             elif share_type == 'order':
#                 fail_orders += 1
#                 result = cost_share_by_order(query.order_num, parcel_customer_orders, customer_order_charge, request)
#                 if not result:
#                     fail_orders += 1
#             else:
#                 continue
#     return fail_orders


# 公共成本分摊: 分摊到FBA订单上
def common_order_cost_share(main_order, customer_orders, charge_out_queryset, user, stowage_map=None):
    if len(customer_orders) == 0:
        raise ParamError(f"单据{main_order}下无关联的订单", ErrorCode.PARAM_ERROR)
    cost_lock_orders = customer_orders.filter(is_cost_lock=True)
    if cost_lock_orders.exists():
        raise ParamError(
            f'不允许分摊到已成本确认的订单: {", ".join(cost_lock_orders.values_list("order_num", flat=True))}',
            ErrorCode.PARAM_ERROR)
    logger.info(f'common_order_cost_share开始成本分摊 : {customer_orders}')
    # 单据下的成本明细，逐个分摊
    for charge_out in charge_out_queryset:
        # 获取费用对应的分摊类型
        share_type = charge_out.charge.share_type
        if share_type in ['weight', 'charge_weight', 'confirm_charge_weight', 'volume', 'confirm_volume']:
            # 按计费重分摊
            cost_share_to_order_common(main_order, customer_orders, charge_out, user, CustomerOrderChargeOut,
                                       share_type=share_type, stowage_map=stowage_map)
        elif share_type == 'order':
            cost_share_to_order_average(main_order, customer_orders, charge_out, user, CustomerOrderChargeOut)
        else:
            continue
    main_order.is_share_cost = True
    main_order.save()
    return 0


# 公共取消成本分摊: 分摊到FBA订单上
def common_order_cost_share_cancel(main_order, customer_orders):
    # charge_model in [CustomerOrderChargeOut, CustomerOrderChargeIn]
    if isinstance(main_order, TruckOrder):
        main_order_num = main_order.truck_order_num
    elif isinstance(main_order, (Clearance, ClearanceOut)):
        main_order_num = main_order.clearance_num
    else:
        main_order_num = main_order.order_num
    cancel_count = 0
    if len(customer_orders) == 0:
        logger.error(f"单据{main_order_num}下无关联的订单")
    else:
        for customer_order in customer_orders:
            # 有历史分摊的，先删除
            charge_queryset = CustomerOrderChargeOut.objects.filter(
                del_flag=False,
                is_share=True,
                share_charge_id=main_order_num,
                customer_order_num=customer_order
            )
            if charge_queryset.exists():
                charge_queryset.update(del_flag=True)
                cancel_count += 1
    main_order.is_share_cost = False
    main_order.save()
    return cancel_count


# 公共成本分摊: 分摊到小包单上
def common_packet_cost_share(main_order, customer_order, charge_out_queryset, user):
    if isinstance(main_order, TruckOrder):
        main_order_num = main_order.truck_order_num
    elif isinstance(main_order, (Clearance, ClearanceOut)):
        main_order_num = main_order.clearance_num
    else:
        main_order_num = main_order.order_num
    # 分摊到小包单上, 查询关联的小包单
    parcel_customer_orders = ParcelCustomerOrder.objects.filter(
        big_parcel__parcel_outbound_order__customer_order__in=customer_order,
        del_flag=False
    )
    cost_lock_orders = parcel_customer_orders.filter(is_cost_lock=True)
    if cost_lock_orders.exists():
        raise ParamError(
            f'不允许分摊到已成本确认的小包单: {", ".join(cost_lock_orders.values_list("order_num", flat=True))}',
            ErrorCode.PARAM_ERROR)

    if parcel_customer_orders.count() == 0:
        logger.error(f"单据{main_order_num}下无关联的小包单")
        return 1

    # 揽收单下的成本明细，逐个分摊
    for charge_out in charge_out_queryset:
        # 获取费用对应的分摊类型
        share_type = charge_out.charge.share_type
        if share_type == 'weight':
            # 按计费重量
            cost_share_to_order_common(main_order, parcel_customer_orders, charge_out, user, ParcelOrderChargeOut,
                                       share_type='weight')
        elif share_type == 'charge_weight':
            # 按计费重分摊
            cost_share_to_order_common(main_order, parcel_customer_orders, charge_out, user, ParcelOrderChargeOut,
                                       share_type='charge_weight')
        elif share_type == 'volume':
            cost_share_to_order_common(main_order, parcel_customer_orders, charge_out, user, ParcelOrderChargeOut,
                                       share_type='volume')
        elif share_type == 'order':
            cost_share_to_order_average(main_order, parcel_customer_orders, charge_out, user,
                                        ParcelOrderChargeOut)
        else:
            continue
    main_order.is_share_cost = True
    main_order.save()
    return 0


# 公共取消成本分摊: 分摊到小包单上
def common_packet_cost_share_cancel(main_order, customer_order):
    if isinstance(main_order, TruckOrder):
        main_order_num = main_order.truck_order_num
    elif isinstance(main_order, (Clearance, ClearanceOut)):
        main_order_num = main_order.clearance_num
    else:
        main_order_num = main_order.order_num
    # 查询关联的小包单
    parcel_customer_orders = ParcelCustomerOrder.objects.filter(
        big_parcel__parcel_outbound_order__customer_order=customer_order,
        del_flag=False
    )

    if parcel_customer_orders.count() == 0:
        logger.error(f"单据{main_order_num}下无关联的小包单")
        return 1

    cancel_count = 0
    for parcel_customer_order in parcel_customer_orders:
        # 有历史分摊的，先删除
        charge_queryset = ParcelOrderChargeOut.objects.filter(
            del_flag=False,
            is_share=True,
            share_charge_id=main_order_num,
            customer_order_num=parcel_customer_order
        )
        if charge_queryset.exists():
            charge_queryset.update(del_flag=True)
            cancel_count += 1
    main_order.is_share_cost = False
    main_order.save()
    logger.error(f"单据{main_order_num}取消分摊到小包单完成")
    return cancel_count


def del_old_charge(customer_orders, charge_in, charge_model):
    # charge_in = CombineBillingOrderChargeIn.objects.get(id=0)
    old_charge = charge_model.objects.filter(customer_order_num__in=customer_orders,
                                             charge=charge_in.charge,
                                             customer=charge_in.customer, del_flag=False)
    if old_charge.exists():
        old_charge.update(del_flag=True)


# 公共收入分摊: 分摊到FBA订单上
def common_order_revenue_share(main_order, customer_orders, charge_in_queryset, user, is_del_old_charge=False):
    # 揽收单的收入分摊到已收入确认的订单上时, 生成收入调整单并自动通过审核, 调用 collect_order_revenue_share_adjust
    revenue_lock_orders = customer_orders.filter(is_revenue_lock=True)
    if revenue_lock_orders.exists():
        raise ParamError(
            f'不允许分摊到已收入确认的订单: {", ".join(revenue_lock_orders.values_list("order_num", flat=True))[:100]}',
            ErrorCode.PARAM_ERROR)
    # 主单下的成本明细，逐个分摊
    for charge_in in charge_in_queryset:
        if is_del_old_charge:
            del_old_charge(customer_orders, charge_in, CustomerOrderChargeIn)
        # 获取费用对应的分摊类型
        share_type = charge_in.charge.share_type
        if share_type in ['weight', 'charge_weight', 'confirm_charge_weight', 'volume', 'confirm_volume']:
            revenue_share_to_order_common(main_order, customer_orders, charge_in, user, CustomerOrderChargeIn,
                                          share_type=share_type)
        elif share_type == 'order':
            revenue_share_to_order_average(main_order, customer_orders, charge_in, user, CustomerOrderChargeIn)
        else:
            continue
    main_order.is_share_revenue = True
    main_order.save()


# 公共取消收入分摊: 分摊到FBA订单上
def common_order_revenue_share_cancel(main_order, customer_orders):
    # charge_model in [CustomerOrderChargeOut, CustomerOrderChargeIn]
    if isinstance(main_order, TruckOrder):
        main_order_num = main_order.truck_order_num
    elif isinstance(main_order, (Clearance, ClearanceOut)):
        main_order_num = main_order.clearance_num
    else:
        main_order_num = main_order.order_num
    if len(customer_orders) == 0:
        logger.error(f"单据{main_order_num}下无关联的订单")
        return 1

    cancel_count = 0
    for customer_order in customer_orders:
        # 有历史分摊的，先删除
        charge_queryset = CustomerOrderChargeIn.objects.filter(
            del_flag=False,
            is_share=True,
            share_charge_id=main_order_num,
            customer_order_num=customer_order
        )
        if charge_queryset.exists():
            charge_queryset.update(del_flag=True)
            cancel_count += 1
    main_order.is_share_revenue = False
    main_order.save()
    return cancel_count


# 生成调整单相关数据
def create_debit_adjust_and_detail(
        adjust_params: dict,
        adjust_detail_params_list: list,
        user: UserProfile,
        currency: str
):
    debit_adjust = DebitAdjust.objects.create(**adjust_params, **get_update_params_by_user(user, True))
    debit_adjust.debit_adjust_num = 'AI' + create_order_num(debit_adjust.id)
    debit_adjust.save()
    # ####################生成调整单明细
    # 通用获取汇率
    current_exchange = get_currency_rate(currency, [currency])
    for adjust_detail_params in adjust_detail_params_list:
        account_charge = adjust_detail_params['charge_total'] * current_exchange
        adjust_detail_params['current_exchange'] = current_exchange
        adjust_detail_params['currency_type'] = currency
        adjust_detail_params['account_charge'] = account_charge
        adjust_detail_params['debit_adjust_id'] = debit_adjust
        adjust_detail_params['track_num'] = adjust_params.get('track_num')
        adjust_detail_params['ocean_order_num'] = adjust_params.get('ocean_order_num')
        adjust_detail_params['container_no'] = adjust_params.get('container_no')
        debit_adjust_detail = DebitAdjustDetail.objects.create(**adjust_detail_params,
                                                               **get_update_params_by_user(user, True))
    return debit_adjust, debit_adjust_detail
    ###############################################


# 公共生成收入调整单
def create_revenue_adjust_common(reconciliation_detail: ReconciliationDetails, user, is_update_detail=True,
                                 debit_date=datetime.now(), service_fee_percentage=0):
    try:
        charge = Charge.objects.get(name=reconciliation_detail.charge)
    except Charge.DoesNotExist:
        charge = None

    customer_order = get_order_by_order_num(reconciliation_detail.order_num)
    if isinstance(customer_order, ParcelCustomerOrder):
        # 如果是作废中的单，生成两个调整明细：退费和取消费/手续费单
        if customer_order.order_status == 'BC' and charge and (
                '运费' in charge.name or '派送费' in charge.name
        ) and service_fee_percentage > 0:
            debit_adjust, debit_adjust_detail_list = parcel_customer_order_invalidated(
                customer_order, reconciliation_detail, charge, user, is_update_detail=is_update_detail,
                debit_date=debit_date, service_fee_percentage=service_fee_percentage)
            return debit_adjust, debit_adjust_detail_list

    debit_adjust, debit_adjust_detail = create_adjust_and_adjust_detail(customer_order, reconciliation_detail,
                                                                        charge, user,
                                                                        is_update_detail=True,
                                                                        debit_date=datetime.now())
    if customer_order and debit_adjust == "combine" and debit_adjust_detail == "combine":
        debit_adjust, debit_adjust_detail = create_adjust_and_adjust_detail_combine(customer_order,
                                                                                    reconciliation_detail,
                                                                                    charge, user,
                                                                                    is_update_detail=True,
                                                                                    debit_date=datetime.now())

    return debit_adjust, debit_adjust_detail


def parcel_customer_order_invalidated(customer_order, reconciliation_detail, charge, user, is_update_detail=True,
                                      debit_date=datetime.now(), service_fee_percentage=0):
    debit_num = reconciliation_detail.reconciliation and reconciliation_detail.reconciliation.debit_num
    # 获取需要退费的金额
    charge_in_list = ParcelOrderChargeIn.objects.filter(customer_order_num=customer_order, del_flag=False)
    if charge_in_list.count() == 0:
        raise ParamError(f'作废中的订单未找到收入明细, 订单: {customer_order.order_num}, '
                         f'费用: {reconciliation_detail.charge}, 币种: {reconciliation_detail.currency}, '
                         f'客户: {reconciliation_detail.customer}', ErrorCode.PARAM_ERROR)
    total_fee = 0
    currency = None
    for charge_in in charge_in_list:
        total_fee += charge_in.charge_total
        if not currency:
            currency = charge_in.currency_type

    # 生成退费调整单，退给客户的，所以是负数
    diff_amount = total_fee * -1

    cancel_detail_params = {
        'charge_rate': diff_amount,
        'charge_count': 1,
        'charge_total': diff_amount,
        'customer': reconciliation_detail.customer,
        'order_num': reconciliation_detail.order_num,
        'charge': charge,
    }
    # 获取取消手续费金额
    service_fee_diff_amount = service_fee_percentage * total_fee * Decimal(0.01)
    service_detail_params = {
        'charge_rate': service_fee_diff_amount,
        'charge_count': 1,
        'charge_total': service_fee_diff_amount,
        'customer': reconciliation_detail.customer,
        'order_num': reconciliation_detail.order_num,
        'charge': charge,
    }
    adjust_params = {
        'customer': reconciliation_detail.customer,
        # 退费是负数
        'amount': diff_amount + service_fee_diff_amount,
        'debit_date': debit_date,
        'debit_num': debit_num,
        'track_num': reconciliation_detail.track_num,
        'ocean_order_num': reconciliation_detail.ocean_order_num,
        'container_no': reconciliation_detail.container_no
    }
    debit_adjust, debit_adjust_detail_list = create_debit_adjust_and_detail(
        adjust_params,
        [cancel_detail_params, service_detail_params],
        user,
        currency or reconciliation_detail.currency,
    )
    # 更新对账明细
    if is_update_detail:
        reconciliation_detail.is_income_adjusted = True
        reconciliation_detail.adjust_income_num = debit_adjust.debit_adjust_num
        reconciliation_detail.save()
    return debit_adjust, debit_adjust_detail_list


def create_adjust_and_adjust_detail(customer_order, reconciliation_detail: ReconciliationDetails,
                                    charge, user, is_update_detail=True,
                                    debit_date=datetime.now()):
    debit_num = reconciliation_detail.reconciliation and reconciliation_detail.reconciliation.debit_num
    # 实收
    official_receipts = reconciliation_detail.s_amount
    # 应收
    receivable = reconciliation_detail.amount

    # 重量和分区都不一致
    if (reconciliation_detail.zone_value != "" and reconciliation_detail.s_zone_value != "" and
            reconciliation_detail.zone_value != reconciliation_detail.s_zone_value and reconciliation_detail.weight != 0
            and reconciliation_detail.s_weight != 0 and
            reconciliation_detail.weight != reconciliation_detail.s_weight):
        # 客户订单和小包单才重新计算价格差异
        if isinstance(customer_order, (ParcelCustomerOrder, CustomerOrder)):
            # 获取计费的费用项
            product_charge = ProductCharge.objects.filter(~Q(charge_unit='combine'),
                                                          charge__name=reconciliation_detail.charge,
                                                          charge_unit__in=['weight', 'chargeWeight'],
                                                          attribution_expenses__in=['default', 'income'],
                                                          product=customer_order.product,
                                                          del_flag=False).first()
            if product_charge:
                # 获取单据对应的费用项的收入以及收入价格版本, 按旧的收入价格版本和新的重量重新计算一遍价格
                if isinstance(customer_order, ParcelCustomerOrder):
                    charge_in_model = ParcelOrderChargeIn
                else:
                    charge_in_model = CustomerOrderChargeIn
                charge_ins = charge_in_model.objects.filter(customer_order_num=customer_order,
                                                            charge__name=reconciliation_detail.charge,
                                                            currency_type=reconciliation_detail.currency,
                                                            customer=reconciliation_detail.customer,
                                                            del_flag=False)
                if not charge_ins.exists():
                    raise ParamError(f'未找到订单收入明细, 订单: {customer_order.order_num}, '
                                     f'费用: {reconciliation_detail.charge}, 币种: {reconciliation_detail.currency}, '
                                     f'客户: {reconciliation_detail.customer}', ErrorCode.PARAM_ERROR)
                charge_in = charge_ins.first()
                price_version = charge_in.price_version
                official_receipts = charge_in.charge_total
                logger.info(f"公共生成收入调整单, 开始重新计算收入, 订单号: {customer_order.order_num}, "
                            f"收入价格版本: {price_version}, 旧的收入合计: {official_receipts}")
                order_calc_vo = build_order_calc_vo(customer_order, True)

                revenue_results = adjust_weight_revenue_calc(order_calc_vo, customer_order.product,
                                                             product_charges=[product_charge],
                                                             adjust_weight=reconciliation_detail.weight,
                                                             price_version=price_version,
                                                             adjust_zone=reconciliation_detail.zone_value,
                                                             )
                revenue_result = revenue_results[0]

                logger.info(f"公共生成收入调整单, 订单号: {customer_order.order_num}, "
                            f"费用项: {reconciliation_detail.charge}, 重新计算后的收费: {revenue_result.result_fee}")
                receivable = revenue_result.result_fee
            else:
                return "combine", "combine"
    # 重量不一致
    elif reconciliation_detail.weight != 0 and reconciliation_detail.s_weight != 0 and \
            reconciliation_detail.weight != reconciliation_detail.s_weight:
        print('customer_order-->', customer_order)
        # 客户订单和小包单才重新计算价格差异
        if isinstance(customer_order, (ParcelCustomerOrder, CustomerOrder)):
            # 获取计费的费用项
            product_charge = ProductCharge.objects.filter(~Q(charge_unit='combine'),
                                                          charge__name=reconciliation_detail.charge,
                                                          charge_unit__in=['weight', 'chargeWeight'],
                                                          attribution_expenses__in=['default', 'income'],
                                                          product=customer_order.product,
                                                          del_flag=False).first()
            if product_charge:
                # 获取单据对应的费用项的收入以及收入价格版本, 按旧的收入价格版本和新的重量重新计算一遍价格
                if isinstance(customer_order, ParcelCustomerOrder):
                    charge_in_model = ParcelOrderChargeIn
                else:
                    charge_in_model = CustomerOrderChargeIn
                charge_ins = charge_in_model.objects.filter(customer_order_num=customer_order,
                                                            charge__name=reconciliation_detail.charge,
                                                            currency_type=reconciliation_detail.currency,
                                                            customer=reconciliation_detail.customer,
                                                            del_flag=False)
                if not charge_ins.exists():
                    raise ParamError(f'未找到订单收入明细, 订单: {customer_order.order_num}, '
                                     f'费用: {reconciliation_detail.charge}, 币种: {reconciliation_detail.currency}, '
                                     f'客户: {reconciliation_detail.customer}', ErrorCode.PARAM_ERROR)
                charge_in = charge_ins.first()
                price_version = charge_in.price_version
                official_receipts = charge_in.charge_total
                logger.info(f"公共生成收入调整单, 开始重新计算收入, 订单号: {customer_order.order_num}, "
                            f"收入价格版本: {price_version}, 旧的收入合计: {official_receipts}")
                order_calc_vo = build_order_calc_vo(customer_order, True)

                revenue_results = adjust_weight_revenue_calc(order_calc_vo, customer_order.product,
                                                             product_charges=[product_charge],
                                                             adjust_weight=reconciliation_detail.weight,
                                                             price_version=price_version)
                revenue_result = revenue_results[0]

                logger.info(f"公共生成收入调整单, 订单号: {customer_order.order_num}, "
                            f"费用项: {reconciliation_detail.charge}, 重新计算后的收费: {revenue_result.result_fee}")
                receivable = revenue_result.result_fee
            else:
                return "combine", "combine"

    # 分区不一致
    elif (reconciliation_detail.zone_value != "" and reconciliation_detail.s_zone_value != "" and
          reconciliation_detail.zone_value != reconciliation_detail.s_zone_value):
        # 客户订单和小包单才重新计算价格差异
        if isinstance(customer_order, (ParcelCustomerOrder, CustomerOrder)):
            # 获取计费的费用项
            product_charge = ProductCharge.objects.filter(~Q(charge_unit='combine'),
                                                          charge__name=reconciliation_detail.charge,
                                                          charge_unit__in=['weight', 'chargeWeight'],
                                                          attribution_expenses__in=['default', 'income'],
                                                          product=customer_order.product,
                                                          del_flag=False).first()
            if product_charge:
                # 获取单据对应的费用项的收入以及收入价格版本, 按旧的收入价格版本和新的重量重新计算一遍价格
                if isinstance(customer_order, ParcelCustomerOrder):
                    charge_in_model = ParcelOrderChargeIn
                else:
                    charge_in_model = CustomerOrderChargeIn
                charge_ins = charge_in_model.objects.filter(customer_order_num=customer_order,
                                                            charge__name=reconciliation_detail.charge,
                                                            currency_type=reconciliation_detail.currency,
                                                            customer=reconciliation_detail.customer,
                                                            del_flag=False)
                if not charge_ins.exists():
                    raise ParamError(f'未找到订单收入明细, 订单: {customer_order.order_num}, '
                                     f'费用: {reconciliation_detail.charge}, 币种: {reconciliation_detail.currency}, '
                                     f'客户: {reconciliation_detail.customer}', ErrorCode.PARAM_ERROR)
                charge_in = charge_ins.first()
                price_version = charge_in.price_version
                official_receipts = charge_in.charge_total
                logger.info(f"公共生成收入调整单, 开始重新计算收入, 订单号: {customer_order.order_num}, "
                            f"收入价格版本: {price_version}, 旧的收入合计: {official_receipts}")
                order_calc_vo = build_order_calc_vo(customer_order, True)

                revenue_results = adjust_weight_revenue_calc(order_calc_vo, customer_order.product,
                                                             product_charges=[product_charge],
                                                             price_version=price_version,
                                                             adjust_zone=reconciliation_detail.zone_value,
                                                             )
                revenue_result = revenue_results[0]

                logger.info(f"公共生成收入调整单, 订单号: {customer_order.order_num}, "
                            f"费用项: {reconciliation_detail.charge}, 重新计算后的收费: {revenue_result.result_fee}")
                receivable = revenue_result.result_fee
            else:
                return "combine", "combine"

    logger.info(f"系统金额: {official_receipts}, "
                f"重新计算后的金额: {receivable}")
    diff_amount = receivable - official_receipts

    logger.info(f"计算结果33: {diff_amount}, ")

    # 生成调整单
    params = {
        'customer': reconciliation_detail.customer,
        'product': reconciliation_detail.product,
        'amount': diff_amount,
        'debit_date': debit_date,
        'debit_num': debit_num,
        'track_num': reconciliation_detail.track_num,
        'ocean_order_num': reconciliation_detail.ocean_order_num,
        'container_no': reconciliation_detail.container_no
    }
    debit_adjust = DebitAdjust.objects.create(**params, **get_update_params_by_user(user, True))
    debit_adjust.save_fields(debit_adjust_num='AI' + create_order_num(debit_adjust.id))
    # 生成调整单明细
    # 通用获取汇率
    current_exchange = get_currency_rate(reconciliation_detail.currency, [reconciliation_detail.currency])
    account_charge = diff_amount * current_exchange
    params = {
        'debit_adjust_id': debit_adjust,
        'charge': charge,
        'charge_rate': diff_amount,
        'charge_count': 1,
        'charge_total': diff_amount,
        'currency_type': reconciliation_detail.currency,
        'account_charge': account_charge,
        'current_exchange': current_exchange,
        'customer': reconciliation_detail.customer,
        'order_num': reconciliation_detail.order_num,
        'track_num': reconciliation_detail.track_num,
        'ocean_order_num': reconciliation_detail.ocean_order_num,
        'container_no': reconciliation_detail.container_no,
    }
    debit_adjust_detail = DebitAdjustDetail.objects.create(**params, **get_update_params_by_user(user, True))
    # 更新对账明细
    if is_update_detail:
        reconciliation_detail.is_income_adjusted = True
        reconciliation_detail.adjust_income_num = debit_adjust.debit_adjust_num
        # 更新对账明细的收入相关数据
        reconciliation_detail.income_amount = official_receipts
        reconciliation_detail.calc_amount = receivable
        reconciliation_detail._income_amount = receivable - official_receipts
        reconciliation_detail.save()

    return debit_adjust, debit_adjust_detail


def create_adjust_and_adjust_detail_combine(customer_order, reconciliation_detail: ReconciliationDetails,
                                            charge, user, is_update_detail=True,
                                            debit_date=datetime.now()):
    debit_num = reconciliation_detail.reconciliation and reconciliation_detail.reconciliation.debit_num
    # 获取计费的费用项
    product_combine_charge = ProductCharge.objects.filter(charge_unit='combine',
                                                          charge__name=reconciliation_detail.charge,
                                                          attribution_expenses__in=['default', 'income'],
                                                          product=customer_order.product,
                                                          del_flag=False).first()
    if product_combine_charge:

        if isinstance(customer_order, ParcelCustomerOrder):
            charge_in_model = ParcelOrderChargeIn
        else:
            charge_in_model = CustomerOrderChargeIn
        charge_ins = charge_in_model.objects.filter(customer_order_num=customer_order,
                                                    charge__name=reconciliation_detail.charge,
                                                    currency_type=reconciliation_detail.currency,
                                                    customer=reconciliation_detail.customer,
                                                    del_flag=False)
        if not charge_ins.exists():
            raise ParamError(f'未找到订单收入明细, 订单: {customer_order.order_num}, '
                             f'费用: {reconciliation_detail.charge}, 币种: {reconciliation_detail.currency}, '
                             f'客户: {reconciliation_detail.customer}', ErrorCode.PARAM_ERROR)
        charge_in = charge_ins.first()
        price_version = charge_in.price_version
        charge_in_charge_total = charge_in.charge_total

        charge_results = get_charge_results_after_adjust(customer_order, user, reconciliation_detail, product_combine_charge)
        result_charge_total = calc_charge_combine_after_adjust(charge_results, product_combine_charge)
        # 找到当前对账明细对应的费用
        logger.info(f"公共生成收入调整单, 订单号: {customer_order.order_num}, "
                    f"费用项: {reconciliation_detail.charge}, 重新计算后的收费: {result_charge_total}")
        s_amount = result_charge_total
    else:
        return None, None

    diff_amount = s_amount - charge_in_charge_total

    # 生成调整单
    params = {
        'customer': reconciliation_detail.customer,
        'product': reconciliation_detail.product,
        'amount': diff_amount,
        'debit_date': debit_date,
        'debit_num': debit_num,
        'track_num': reconciliation_detail.track_num,
        'ocean_order_num': reconciliation_detail.ocean_order_num,
        'container_no': reconciliation_detail.container_no
    }
    debit_adjust = DebitAdjust.objects.create(**params, **get_update_params_by_user(user, True))
    debit_adjust.save_fields(debit_adjust_num='AI' + create_order_num(debit_adjust.id))
    # 生成调整单明细
    # 通用获取汇率
    current_exchange = get_currency_rate(reconciliation_detail.currency, [reconciliation_detail.currency])
    account_charge = diff_amount * current_exchange
    params = {
        'debit_adjust_id': debit_adjust,
        'charge': charge,
        'charge_rate': diff_amount,
        'charge_count': 1,
        'charge_total': diff_amount,
        'currency_type': reconciliation_detail.currency,
        'account_charge': account_charge,
        'current_exchange': current_exchange,
        'customer': reconciliation_detail.customer,
        'order_num': reconciliation_detail.order_num,
        'track_num': reconciliation_detail.track_num,
        'ocean_order_num': reconciliation_detail.ocean_order_num,
        'container_no': reconciliation_detail.container_no,
    }
    debit_adjust_detail = DebitAdjustDetail.objects.create(**params, **get_update_params_by_user(user, True))
    # 更新对账明细
    if is_update_detail:
        reconciliation_detail.is_income_adjusted = True
        reconciliation_detail.adjust_income_num = debit_adjust.debit_adjust_num
        # 更新对账明细的收入相关数据
        reconciliation_detail.income_amount = charge_in_charge_total
        reconciliation_detail.calc_amount = s_amount
        reconciliation_detail._income_amount = s_amount - charge_in_charge_total
        reconciliation_detail.save()

    return debit_adjust, debit_adjust_detail


# 计算调整后的组合费用
def calc_charge_combine_after_adjust(charge_results, product_combine_charge: ProductCharge):
    print('charge_results---->', charge_results)
    # product_combine_charges = ProductCharge.objects.filter(charge_unit='combine', product=product, del_flag=False)
    if product_combine_charge:
        logger.info(f'开始计算组合费用项, 主费用项: {product_combine_charge.name}')
        if not product_combine_charge.charge_combine_rate or not product_combine_charge.relate_charges:
            return
        relate_charges = product_combine_charge.relate_charges.split(',')
        charge_total = 0
        for charge_result in charge_results:
            logger.info(f'开始计算组合费用项, 所有费用项: {relate_charges}, 计费子费用项: {charge_result.name}, '
                        f'计费总价: {charge_result.result_fee}')
            if charge_result.name in relate_charges:
                charge_total += charge_result.result_fee

        if charge_total == 0:
            return 0

        result_charge_total = charge_total * product_combine_charge.charge_combine_rate / 100
        logger.info(
            f'开始计算组合费用项, 组合费用项: {product_combine_charge.name}, 乘收费比例前的合计: {charge_total},'
            f' 费用合计: {result_charge_total}')
        return result_charge_total


# 获取调整后的计费
def get_charge_results_after_adjust(customer_order, user, reconciliation_detail, product_combine_charge):
    charge_results = []
    # 获取计费的费用项
    product_charges = ProductCharge.objects.filter(~Q(charge_unit='combine'), attribution_expenses__in=['default', 'income'],
                                                   product=customer_order.product, del_flag=False)

    relate_charges = product_combine_charge.relate_charges.split(',')
    logger.info(f'{[product_charge.charge for product_charge in product_charges]}我到这里21')
    for product_charge in product_charges:
        logger.info(f'{product_charge.name}计费名称')
        # 从订单收入中查询计费合计
        if isinstance(customer_order, ParcelCustomerOrder):
            charge_in_model = ParcelOrderChargeIn
        else:
            charge_in_model = CustomerOrderChargeIn
        if product_charge.name in relate_charges:
            charge_ins = charge_in_model.objects.filter(customer_order_num=customer_order,
                                                        charge__name=product_charge.charge,
                                                        currency_type=reconciliation_detail.currency,
                                                        customer=reconciliation_detail.customer,
                                                        del_flag=False)
            msg = f'获取调整后的计费, 未找到订单收入明细, 订单: {customer_order.order_num}, ' \
                  f'费用: {product_charge.charge}, 币种: {reconciliation_detail.currency}, ' \
                  f'客户: {reconciliation_detail.customer}'
            logger.info(msg)
            result = OrderChargeCalcResultVo()
            if charge_ins.exists():
                charge_in = charge_ins.first()

                # 从已生成的调整单里查询计费合计
                adjust_detail_params = {
                    'charge': product_charge.charge,
                    'customer': reconciliation_detail.customer,
                    'order_num': reconciliation_detail.order_num,
                    'track_num': reconciliation_detail.track_num,
                }

                logger.info(f'字典{adjust_detail_params}')
                debit_adjust_detail = DebitAdjustDetail.objects.filter(**adjust_detail_params).exclude(
                Q(debit_adjust_id__debit_status=4) | Q(debit_adjust_id__debit_status=3)).first()
                if debit_adjust_detail:
                    logger.info(f'我到这里')
                    charge_in_charge_total = charge_in.charge_total
                    diff_amount = debit_adjust_detail.charge_total
                    s_amount = charge_in_charge_total + diff_amount # 重新计算后的金额
                    logger.info(f'费用{charge_in_charge_total}---{diff_amount}')
                    result.result_fee = s_amount
                    result.name = product_charge.name
                    logger.info(f'结果1111: {result.result_fee}')
                else:
                    logger.info(f'我到那里')
                    result.result_fee = charge_in.charge_total
                    result.name = product_charge.name
                    logger.info(f'结果2222: {result.result_fee}')
                charge_results.append(result)
            # 如果小包收入中没有该费用类型,则默认合计为0
            else:
                logger.info(f'我到外面')
                result.result_fee = 0
                result.name = product_charge.name
                logger.info(f'结果333: {result.result_fee}')
                charge_results.append(result)

    return charge_results


# 收入调整单审核通过公共函数
def debit_adjust_approve_common(debit_adjust: DebitAdjust, user, debit_adjust_detail=None, debit_date=datetime.now()):
    # 推送生成账单和账单明细
    product_code = debit_adjust.product.name if debit_adjust.product else None
    print('obj.track_num-->', debit_adjust.track_num)
    # 生成一个账单
    params = {
        'customer': debit_adjust.customer,
        'debit_status': 1,
        'product_code': product_code,
        'product': debit_adjust.product,
        'order_num': debit_adjust.debit_adjust_num,
        'amount': debit_adjust.amount,
        'debit_date': debit_date,
        'is_adjust': True,
        'adjust_number': debit_adjust.debit_adjust_num,
        'track_num': debit_adjust.track_num,
        'container_no': debit_adjust.container_no,
    }
    debit = Debit.objects.create(**params, **get_update_params_by_user(user, True))
    debit.debit_num = 'D' + create_order_num(debit.id)
    if debit_adjust_detail:
        account_receivable_queryset = [debit_adjust_detail]
        # 设置账单的币种
        debit.currency = debit_adjust_detail.currency_type
        # debit.track_num = reconciliation_detail.track_num
        debit.customer_order = debit_adjust_detail.customer_orderNum
    else:
        # 获取调整单下的调整明细
        account_receivable_queryset = DebitAdjustDetail.objects.filter(debit_adjust_id=debit_adjust.id, del_flag=False)
        # 设置账单的币种
        debit.currency = account_receivable_queryset.first().currency_type
        # debit.track_num = account_receivable_queryset.first().track_num
        debit.customer_order = account_receivable_queryset.first().customer_orderNum
        # 如果收入调整单明细中没有客户订单号
        if not debit.customer_order:
            logger.info(f'测试通过')
            try:
                ls = ReconciliationDetails.objects.filter(track_num=account_receivable_queryset.first().track_num, del_flag=False).first()
                debit.customer_order = ls.customer_orderNum
            except:
                debit.customer_order = None

    for account_obj in account_receivable_queryset:
        print('account_obj-->', account_obj, account_obj.charge)
        # 生成收款明细
        params = {
            'charge_name': account_obj.charge,
            'customer': account_obj.customer,
            'order_num': account_obj.order_num,
            'product_code': product_code,
            'product': debit_adjust.product,
            'customer_orderNum': account_obj.customer_orderNum,
            'track_num': account_obj.track_num,
            'origin_amount': account_obj.charge_total,
            'origin_balance': account_obj.charge_total,
            'account_amount': account_obj.account_charge,
            'account_balance': account_obj.account_charge,
            'account_time': debit_date,
            'is_adjust': True,
            'adjust_number': debit_adjust.debit_adjust_num,
            'debit_num': debit,
            'origin_currency': account_obj.currency_type,
            'ocean_order_num': account_obj.ocean_order_num,
            'container_no': account_obj.container_no,
        }
        account_receivable = AccountReceivable.objects.create(**params, **get_update_params_by_user(user, True))
        account_receivable.ar_num = 'AR' + create_order_num(account_receivable.id)
        account_receivable.save()
        # 将作废中的订单状态修改成作废成功
        customer_order = get_order_by_order_num(account_obj.order_num)
        # 会退费，所以CF的也更新为作废成功
        if isinstance(customer_order, ParcelCustomerOrder) and customer_order.order_status in ['BC', 'CF']:
            customer_order.order_status = 'CS'
            customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            customer_order.remark = '收入调整单审核通过,作废成功'
            customer_order.save()

    debit.amount = debit_adjust.amount
    debit.save()

    if settings.IS_DEDUCTION_ACCOUNT and debit_adjust.customer and debit_adjust.customer.is_wallet_manage:
        deduction_account_adjust(debit_adjust, user)

    # 修改调整单状态为审核完成
    debit_adjust.debit_status = 3
    debit_adjust.debit_date = datetime.now()
    debit_adjust.save()


@transaction.atomic
def deduction_account_adjust(adjust_order, user):
    """
    调整单扣减账户
    :param adjust_order:
    :param user:
    :return:
    """
    debit_adjust_num = adjust_order.debit_adjust_num
    account_receivable_queryset = DebitAdjustDetail.objects.filter(debit_adjust_id=adjust_order.id, del_flag=False)
    if account_receivable_queryset.count() == 0:
        raise ParamError('调整单' + debit_adjust_num + '无调整明细')

    total_fee = 0
    currency = None
    for charge_in in account_receivable_queryset:
        total_fee += charge_in.charge_total
        if not currency:
            currency = charge_in.currency_type

    customer_account_queryset = Account.objects.filter(customer=adjust_order.customer, currency=currency,
                                                       del_flag=False)
    if customer_account_queryset.count() == 0:
        raise ParamError(f'该客户此币种{currency}账户不存在')

    customer_account = Account.objects.select_for_update().get(id=customer_account_queryset.first().id,
                                                               del_flag=False)

    if customer_account.usable_balance - total_fee < 0:
        logger.info(str(customer_account.customer) + '帐户余额不足,剩余' + str(customer_account.usable_balance))
        raise ParamError('帐户余额不足,剩余' + str(customer_account.usable_balance), ErrorCode.PARAM_ERROR)

    balance = customer_account.usable_balance

    Account.objects.filter(id=customer_account.id).update(usable_balance=F('usable_balance') - total_fee,
                                                          update_date=datetime.now(), update_by=user)

    for charge_in in account_receivable_queryset:
        balance = balance - charge_in.charge_total
        transaction_type = 'deduction' if charge_in.charge_total > 0 else 'refund'
        params = {
            'account': customer_account,
            'transaction_type': transaction_type,
            'customer_order_num': charge_in.customer_orderNum,
            'amount': -charge_in.charge_total,
            'currency': currency,
            'transaction_no': debit_adjust_num,
            'transaction_unique_no': 'DE' + create_order_num(charge_in.id),
            'transaction_charge_code': charge_in.charge.code,
            'transaction_charge_name': charge_in.charge.name,
            'exchange_rate': 1,
            'account_currency': currency,
            'account_charge_amount': -charge_in.charge_total,
            'account_balance': balance,
            'ref_no': charge_in.order_num,
        }
        account_detail = AccountDetail.objects.create(**params)
        account_detail.create_by = user
        account_detail.update_by = user
        account_detail.create_date = datetime.now()
        account_detail.update_date = datetime.now()
        account_detail.save()


# 揽收单的收入分摊到已收入确认的订单上时, 生成收入调整单并自动通过审核
def collect_order_revenue_share_adjust(share_charge, user, debit_date=None):
    debit_date = debit_date if debit_date else datetime.now()
    reconciliation_detail = ReconciliationDetails()
    reconciliation_detail.customer = share_charge.customer
    reconciliation_detail._amount = -share_charge.charge_total
    # reconciliation_detail.reconciliation = None
    reconciliation_detail.order_num = getattr(share_charge.customer_order_num, 'order_num', None)
    reconciliation_detail.track_num = share_charge.share_charge_id
    reconciliation_detail.charge = share_charge.charge
    reconciliation_detail.currency = share_charge.currency_type
    # 创建收入调整单
    debit_adjust, debit_adjust_detail = create_revenue_adjust_common(
        reconciliation_detail, user, is_update_detail=False, debit_date=debit_date)

    debit_adjust_approve_common(debit_adjust, user, debit_adjust_detail=debit_adjust_detail,
                                debit_date=debit_date)


# 通用创建单据的成本明细
def create_order_cost(charges, charge_out_model, foreign_key, instance, user, restrict_type='common'):
    if restrict_type == 'common':
        restrict_charge_item(charges, '成本')
    elif restrict_type == 'currency':
        restrict_charge_item_and_currency(charges, 'charge_out')
    for charge in charges:
        charge_out_object = charge_out_model.objects.create(**{foreign_key: instance}, **charge)
        # setattr(charge_out_object, foreign_key, instance)
        charge_out_object.charge_total = (charge_out_object.charge_rate or 0) * (
                charge_out_object.charge_count or 0)
        # 通用获取汇率
        current_exchange = get_currency_rate(charge_out_object.currency_type, [charge_out_object.currency_type])
        charge_out_object.account_charge = charge_out_object.charge_total * current_exchange
        charge_out_object.create_by = user
        charge_out_object.save()


# 通用修改单据的收入明细
def update_order_revenue(charges, charge_model, foreign_key, instance, user, restrict_type='common'):
    if restrict_type == 'common':
        restrict_charge_item(charges, '收入')
    elif restrict_type == 'currency':
        restrict_charge_item_and_currency(charges, 'charge_in')
    charge_model.objects.filter(**{foreign_key: instance}).update(del_flag=True)
    for charge in charges:
        print('charge_data1-->', charge, {foreign_key: instance})
        if charge.get('id'):
            new_related_queryset = charge_model.objects.filter(id=charge.get('id'))
            charge['update_by'] = user
            charge['update_date'] = datetime.now()
            charge['del_flag'] = False
            new_related_queryset.update(**charge)
            charge_object = charge_model.objects.get(id=charge.get('id'))
        else:
            charge_object = charge_model.objects.create(**{foreign_key: instance}, **charge)
            charge_object.create_by = user

        # 计算费用合计和记账金额
        charge_object.charge_total = (charge_object.charge_rate or 0) * (
                charge_object.charge_count or 0)
        # 通用获取汇率
        current_exchange = get_currency_rate(charge_object.currency_type,
                                             [charge_object.currency_type])
        charge_object.account_charge = charge_object.charge_total * current_exchange
        # fix: AttributeError: 'xxx' object has no attribute 'customer'
        if not charge_object.customer and hasattr(instance, 'customer'):
            charge_object.customer = getattr(instance, 'customer') or None
        charge_object.save()


# 通用修改单据的成本明细
def update_order_cost(charges, charge_model, foreign_key, instance, user, restrict_type='common'):
    if restrict_type == 'common':
        restrict_charge_item(charges, '成本')
    elif restrict_type == 'currency':
        restrict_charge_item_and_currency(charges, 'charge_out')
    charge_model.objects.filter(**{foreign_key: instance}).update(del_flag=True)
    for charge in charges:
        print('charge_data2-->', charge, {foreign_key: instance})
        if charge.get('id'):
            new_related_queryset = charge_model.objects.filter(id=charge.get('id'))
            charge['update_by'] = user
            charge['update_date'] = datetime.now()
            charge['del_flag'] = False
            new_related_queryset.update(**charge)
            charge_object = charge_model.objects.get(id=charge.get('id'))
        else:
            charge_object = charge_model.objects.create(**{foreign_key: instance}, **charge)
            charge_object.create_by = user

        # 计算费用合计和记账金额
        charge_object.charge_total = (charge_object.charge_rate or 0) * (
                charge_object.charge_count or 0)
        # 通用获取汇率
        current_exchange = get_currency_rate(charge_object.currency_type,
                                             [charge_object.currency_type])
        charge_object.account_charge = charge_object.charge_total * current_exchange
        charge_object.save()


# FBA包裹出仓获取海运提单
def out_warehouse_get_ocean_num(parcel, ocean_num_id, request):
    print('out_warehouse_get_ocean_num-->', parcel, ocean_num_id, request)
    relate_customer_orders = CustomerOrderRelateOcean.objects.filter(customer_order_num=parcel.customer_order,
                                                                     del_flag=False)
    # 出仓时尽量让操作用户无感
    if relate_customer_orders.count() == 0:
        return respond_with_error(request, "未查到关联的海运提单"), 1
    elif relate_customer_orders.count() == 1:
        customer_order_relate_ocean = relate_customer_orders.first()
        if ocean_num_id:
            if int(ocean_num_id) != customer_order_relate_ocean.oceanOrder.id:
                # ocean_order = OceanOrder.objects.get(id=ocean_num_id)
                # return respond_with_error(request,
                #                           f"包裹{parcel.parcel_num}不在{ocean_order.order_num}提单内, 不允许出库"), 1
                return respond_with_error(request, f"海运单已切换, 请扫描箱号后回车重新选择海运提单!", code=402), 1

            res, code = check_ocean_order_out_of_storage(request, parcel, ocean_num_id)
            if code == 1:
                return res, 1

        # 多对多中间表数据只有一个, 则默认取这个, 不论是否传海运单id
        ocean_order = customer_order_relate_ocean.oceanOrder
    else:
        if ocean_num_id:
            res, code = check_ocean_order_out_of_storage(request, parcel, ocean_num_id)
            if code == 1:
                return res, 1

            if int(ocean_num_id) not in relate_customer_orders.values_list('oceanOrder_id', flat=True):
                return respond_with_error(request, f"海运单已切换, 请扫描箱号后回车重新选择海运提单.", code=402), 1
            try:
                ocean_order = OceanOrder.objects.get(id=ocean_num_id)
            except OceanOrder.DoesNotExist:
                return respond_with_error(request, f"海运单号id异常, 请联系管理员"), 1
        else:
            return respond_with_error(request, f"海运单已切换, 请扫描箱号后回车重新选择海运单"), 1
    return (relate_customer_orders, ocean_order), 0


def check_ocean_order_out_of_storage(request, parcel, ocean_num_id):
    customer_order = parcel.customer_order

    # current_ocean_orders = CustomerOrderRelateOcean.objects.filter(oceanOrder_id=ocean_num_id,
    #                                                                del_flag=False)
    # if current_ocean_orders:
    #     if int(parcel.customer_order.id) not in current_ocean_orders.values_list('customer_order_num_id',
    #                                                                              flat=True):
    #         ocean_order = OceanOrder.objects.get(id=ocean_num_id)
    #         return respond_with_error(request, f"包裹{parcel.parcel_num}不在{ocean_order.order_num}提单内, 不允许出库"), 1

    # 这个海运提单下的 所有FBA订单
    relate_ocean_orders = CustomerOrderRelateOcean.objects.filter(oceanOrder_id=ocean_num_id, del_flag=False)
    for check_customer in relate_ocean_orders:
        if check_customer.out_warehouse_num != 0 and check_customer.customer_order_num != customer_order \
                and check_customer.freight_num - check_customer.out_warehouse_num > 0:
            return respond_with_error(request,
                                      f"订单{check_customer.customer_order_num.order_num}还存在未出库"
                                      f"的箱数{check_customer.freight_num - check_customer.out_warehouse_num}"
                                      f"个，请先将该订单全部箱子出库."), 1
    return None, 0


# 获取海运提单相关数据
def get_ocean_order_relate_data(ocean_order):
    relate_ocean_orders = CustomerOrderRelateOcean.objects.filter(oceanOrder=ocean_order, del_flag=False)
    # 配载件数：查询当前海运提单下面 每个订单的配载数 的和
    parcels_out_count = relate_ocean_orders.aggregate(Sum('freight_num'))['freight_num__sum'] or 0
    # 出仓件数
    out_warehouse_count = relate_ocean_orders.aggregate(Sum('out_warehouse_num'))['out_warehouse_num__sum'] or 0
    # 出仓票数
    relate_ocean_out_count = CustomerOrderRelateOcean.objects.filter(~Q(freight_num=0),
                                                                     oceanOrder=ocean_order,
                                                                     freight_num=F('out_warehouse_num'),
                                                                     del_flag=False).count()
    # 配载票数
    set_ocean = relate_ocean_orders.count()
    return parcels_out_count, out_warehouse_count, relate_ocean_out_count, set_ocean


# 将订单下的未称重包裹的实际长宽高重量体积都置为0
def set_parcel_size_zero(customer_order):
    parcels = Parcel.objects.filter(customer_order=customer_order, is_weighing=False, del_flag=False)
    # parcels.update(actual_length=0, actual_width=0, actual_height=0, actual_weight=0, actual_volume=0)
    parcels.update(remark='强制入参删除未过机包裹号', del_flag=True)


# 一个订单下的计费项不允许重复(收入/成本), 避免重复计费项
def restrict_charge_item(charges, charge_type):
    print('charges-->', charges)
    charge_map = {}
    for charge in charges:
        # print('charge-->', type(charge_in), charge_in)
        if isinstance(charge, OrderedDict):
            charge_code = charge.get('charge').code
            charge_name = charge.get('charge').name
        else:
            charge_code = charge.get('charge_code')
            charge_name = charge.get('charge_name')
        if charge_code not in charge_map:
            charge_map[charge_code] = charge_name
        else:
            raise ParamError(f'订单下的{charge_type}计费项: {charge_name} 重复', ErrorCode.PARAM_ERROR)


def restrict_charge_item_and_currency(charges, charge_type):
    # print('charges-->', charges)
    charge_map = {}
    for charge in charges:
        supplier_name = ''
        # print('charge-->', type(charge), charge)
        if isinstance(charge, OrderedDict):
            charge_code = charge.get('charge').code
            charge_name = charge.get('charge').name
            currency_type = charge.get('currency_type')
            if charge_type == 'charge_in':
                charge_judge = f'{charge_code}_{currency_type}'
            else:
                supplier_name = charge.get('supplier').name
                charge_judge = f'{charge_code}_{currency_type}_{supplier_name}'
        else:
            charge_code = charge.get('charge_code')
            charge_name = charge.get('charge_name')
            currency_type = charge.get('currency_type')
            if charge_type == 'charge_in':
                charge_judge = f'{charge_code}_{currency_type}'
            else:
                supplier_name = charge.get('supplier_name')
                charge_judge = f'{charge_code}_{currency_type}_{supplier_name}'
        if charge_judge not in charge_map:
            charge_map[charge_judge] = charge_name
        else:
            charge_type_format = '成本' if charge_type == 'charge_out' else '收入'
            raise ParamError(f'订单下的{charge_type_format}计费项: {charge_name}({currency_type} {supplier_name}) 重复',
                             ErrorCode.PARAM_ERROR)


# 一个订单下的计费项不允许重复(收入/成本), 避免重复计费项
def restrict_charge_item_and_supplier(charges, charge_type):
    logger.info(f'restrict_charge_item_and_supplier目前的计费项: {charges}')
    charge_map = {}
    for charge in charges:
        supplier_name = ''
        # print('charge000-->', type(charge), charge)
        if isinstance(charge, OrderedDict):
            charge_code = charge.get('charge').code
            charge_name = charge.get('charge').name
            if charge_type == '收入':
                charge_judge = f'{charge_code}'
            else:
                supplier_name = charge.get('supplier').name
                charge_judge = f'{charge_code}_{supplier_name}'
        else:
            charge_code = charge.get('charge_code')
            charge_name = charge.get('charge_name')
            if charge_type == '收入':
                charge_judge = f'{charge_code}'
            else:
                supplier_name = charge.get('supplier_name')
                charge_judge = f'{charge_code}_{supplier_name}'
        # print('charge_map-->', charge_map)
        if charge_judge not in charge_map:
            charge_map[charge_judge] = charge_name
        else:
            # charge_type_format = '成本' if charge_type == 'charge_out' else '收入'
            supplier_name_format = '' if charge_type == '收入' else f'({supplier_name})'
            raise ParamError(f'订单下的{charge_type}计费项: {charge_name}{supplier_name_format} 重复',
                             ErrorCode.PARAM_ERROR)


# 通过订单获取订单下包裹中第一个商品的中文品名
def get_order_declared_name_cn(order_type, current_order):
    if order_type == 'ParcelCustomerOrder':
        parcels = ParcelOrderParcel.objects.filter(customer_order=current_order, del_flag=False)
        if not parcels.exists():
            return None
        parcel_items = ParcelOrderItem.objects.filter(parcel_num=parcels.first(), del_flag=False)
    elif order_type == 'CustomerOrder':
        parcels = Parcel.objects.filter(customer_order=current_order, del_flag=False)
        if not parcels.exists():
            return None
        parcel_items = ParcelItem.objects.filter(parcel_num=parcels.first(), del_flag=False)
    else:
        return None
    if parcel_items.exists():
        return parcel_items.first().declared_nameCN


# 下发17track任务
def set_17track_task(parcel: Parcel):
    if CustomerParcelTrack.objects.filter(parcel_num=parcel.parcel_num, track_code='PL',
                                          del_flag=False).exists():
        return
    order_track = Track.objects.filter(order_num=parcel.customer_order.order_num, track_code='PL',
                                       del_flag=False).first()
    pl_date = order_track.actual_time if order_track else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    track_task = {
        'parcel_num': parcel.parcel_num,
        'track_code': 'PL',
        'track_name': '已提柜',
        'actual_time': pl_date,
        'remark': '初始轨迹',
        'location': '',
        'push_status': 'WAIT_PUSH'
    }
    logger.info(f'下发17track任务: {parcel.parcel_num}')
    CustomerParcelTrack.objects.create(**track_task)


# 获取通过供应商编码获取17track对应的物流商编码
def get_17track_courier_code(code):
    return TRACK17_CARRIER_CODE_MAP.get(code)


# 保存excel中的仓库(买家地址)
def save_address(buyer_address_num, table, is_warehouse_address=True):
    # buyer_address = Address()
    # buyer_address.address_num = buyer_address_num
    # buyer_address.address_name = buyer_address_num
    # buyer_address.contact_name = get_excel_cell(3, 'i', table)
    # buyer_address.contact_phone = get_excel_cell(8, 'i', table)
    # buyer_address.country_code = get_excel_cell(5, 'i', table)
    # buyer_address.state_code = get_excel_cell(7, 'n', table)
    # buyer_address.city_code = get_excel_cell(7, 'i', table)
    # buyer_address.postcode = get_excel_cell(5, 'n', table)
    # buyer_address.address_one = get_excel_cell(6, 'i', table)
    # buyer_address.company_name = get_excel_cell(4, 'i', table)
    # buyer_address.save()
    # return buyer_address

    # 更换模板
    buyer_address = Address()
    buyer_address.address_num = buyer_address_num
    buyer_address.address_name = buyer_address_num
    buyer_address.contact_name = get_excel_cell(6, 'i', table)
    buyer_address.contact_phone = get_excel_cell(8, 'o', table)
    buyer_address.country_code = get_excel_cell(7, 'i', table)
    buyer_address.state_code = get_excel_cell(7, 'm', table)
    buyer_address.city_code = get_excel_cell(8, 'i', table)
    buyer_address.postcode = get_excel_cell(8, 'm', table)
    buyer_address.address_one = get_excel_cell(7, 'o', table)
    buyer_address.company_name = get_excel_cell(6, 'o', table)
    buyer_address.address_type = 'RC'  # 设为收件人
    if is_warehouse_address:
        buyer_address.save()
    return buyer_address


# 保存api下单的仓库地址
def save_api_create_address(request, customer_order):
    # 仓库代码(收件人)
    warehouse_address = request.data.get('warehouse_address', None)
    if warehouse_address:
        buyer_address = Address.objects.filter(address_num=warehouse_address, del_flag=False).first()
        if buyer_address:
            # customer_order.receiver = receiver_address
            buyer_address_params = {
                # 'receiver': buyer_address if warehouse_address else None,
                'receiver': buyer_address,
                'buyer_address_num': buyer_address.address_num,
                'buyer_name': buyer_address.contact_name,
                'buyer_mail': buyer_address.contact_email,
                'buyer_phone': buyer_address.contact_phone,
                'buyer_country_code': buyer_address.country_code,
                'buyer_state': buyer_address.state_code,
                'buyer_city_code': buyer_address.city_code,
                'buyer_postcode': buyer_address.postcode,
                'buyer_house_num': buyer_address.house_no,
                'buyer_address_one': buyer_address.address_one,
                'buyer_address_two': buyer_address.address_two,
                'buyer_company_name': buyer_address.company_name,
            }
            for field in buyer_address_params:
                setattr(customer_order, field, buyer_address_params.get(field))
            # return buyer_address_params
        else:
            raise ParamError(f'找不到仓库地址: {warehouse_address}', ErrorCode.PARAM_ERROR)
    # customer_order.save()


# 根据地址外键保存订单的发件人和收件人信息
def save_shipper_and_receiver_by_address(order, address, address_type='receiver'):
    common_address_params = {
        'address_num': address.address_num,
        'name': address.contact_name,
        'mail': address.contact_email,
        'phone': address.contact_phone,
        'country_code': address.country_code,
        'state': address.state_code,
        'city_code': address.city_code,
        'postcode': address.postcode,
        'house_num': address.house_no,
        'address_one': address.address_one,
        'address_two': address.address_two,
        'company_name': address.company_name,
    }
    # shipper or receiver
    if address_type == 'receiver':
        save_address_params = {f'buyer_{key}': value for key, value in common_address_params.items()}
        save_address_params['receiver'] = address
    else:
        save_address_params = common_address_params
        save_address_params['shipper'] = address
    for field in save_address_params:
        setattr(order, field, save_address_params[field])
    order.save()


# 复制订单的收发件人数据给新的单据(注意字段需要保持一致才能成功赋值)
def copy_customer_order_address(customer_order: CustomerOrder, new_order, address_type='receiver'):
    common_address_params = ['address_num', 'contact_name', 'contact_email', 'contact_phone', 'country_code',
                             'state_code', 'city_code', 'postcode', 'house_no', 'address_one', 'address_two',
                             'company_name', ]
    # shipper or receiver
    if address_type == 'receiver':
        save_address_params = [f'buyer_{field}' for field in common_address_params]
        save_address_params.append('receiver')
    else:
        save_address_params = common_address_params
        save_address_params.append('shipper')
    print('save_address_params-->', save_address_params)

    for field in save_address_params:
        print('hasattr save_address_params-->', new_order, field, hasattr(new_order, field))
        if hasattr(new_order, field):
            setattr(new_order, field, getattr(customer_order, field))
    new_order.save()


# 解析前端的日期, 格式: Mon May 20 2024 00:00:00 GMT+0800 (GMT+08:00)
def parse_custom_date(date_str):
    # date_str = "Mon May 20 2024 00:00:00 GMT+0800 (GMT+08:00)"
    parts = re.split(r'\s+', date_str.strip())
    time_zone = parts[-1]
    formatted_date_str = ' '.join(parts[:len(parts) - 2])
    dt = datetime.strptime(formatted_date_str, '%a %b %d %Y %H:%M:%S')
    return dt, time_zone


# 对订单做应收应付计费
@transaction.atomic
def billing_customer_order(customer_order, user):
    start_time = datetime.now()
    logger.info(f"{customer_order.order_num}计费start")
    # 是否收入计价
    product = customer_order.product
    if product and product.is_valuation:
        CustomerOrderChargeIn.objects.filter(customer_order_num=customer_order, del_flag=False,
                                             is_system=True).update(del_flag=True)
        add_revenue(customer_order, user, CustomerOrderChargeIn, is_recharge=True)

    # 是否成本计价
    if product and product.is_cost_valuation:
        CustomerOrderChargeOut.objects.filter(customer_order_num=customer_order, del_flag=False,
                                              is_system=True).update(del_flag=True)
        add_cost(customer_order, user, CustomerOrderChargeOut, is_recharge=True)

    # customer_order.save()
    logger.info(f"{customer_order.order_num}计费end: {datetime.now() - start_time}")


# 包裹出仓公共方法
def parcel_out_warehouse(parcel, ocean_num_id, request):
    verify_res = outbound_verify_parcel(parcel, request)
    if verify_res != 0:
        return verify_res
    logger.info(f'parcel_out_warehouse开始: {parcel.parcel_num}')

    # sys_parcel_num_list = parcel.customer_order.parcel.all().values_list('sys_parcel_num', flat=True)

    customer_order = parcel.customer_order
    # relate_customer_order = CustomerOrderRelateOcean.objects.filter(customer_order_num=customer_order,
    #                                                                 del_flag=False)
    # 获取出仓海运提单
    res, err = out_warehouse_get_ocean_num(parcel, ocean_num_id, request)
    if err == 1:
        return res
    else:
        relate_customer_order, ocean_order = res

    ocean_num_id = ocean_order.id

    # 校验配载
    if not ocean_order:
        return respond_with_error(request, f"该箱还未做配载，订单号: {customer_order.order_num}, "
                                           f"提单号: {ocean_order.order_num}")
    # 提单状态必须是配载完成
    logger.info(f"扫描出仓-后端检索-海运提单号：{ocean_order.order_num}, 状态：{ocean_order.order_status}, "
                f"包裹号: {parcel.parcel_num}")
    if ocean_order.order_status not in ["LOC", "OUS"]:
        return respond_with_error(request, f"提单: {ocean_order.order_num} 尚未配载完成")

    # 这个海运提单下的 所有FBA订单
    not_out_warehouse_orders = []
    relate_ocean_orders = CustomerOrderRelateOcean.objects.filter(oceanOrder=ocean_order, del_flag=False)
    for check_customer in relate_ocean_orders:
        # 查询订单件数不等于所关联的海运单的出仓件数即可
        customer_order_inner = check_customer.customer_order_num
        # not_out_parcels = customer_order_inner.parcel.filter(out_warehouse=False, del_flag=False)
        customer_order_relate_ocean = CustomerOrderRelateOcean.objects.filter(customer_order_num=customer_order_inner,
                                                                              del_flag=False)
        all_out_warehouse_num = customer_order_relate_ocean.aggregate(total=Sum('out_warehouse_num'))['total']

        # if not_out_parcels.exists():
        if all_out_warehouse_num != customer_order_inner.carton:
            not_out_warehouse_orders.append(customer_order_inner.order_num)
        if check_customer.out_warehouse_num != 0 and customer_order_inner != customer_order \
                and check_customer.freight_num - check_customer.out_warehouse_num > 0:
            return respond_with_error(request,
                                      f"订单{customer_order_inner.order_num}还存在未出库的箱数"
                                      f"{check_customer.freight_num - check_customer.out_warehouse_num}"
                                      f"个，请先将该订单全部箱子出库")
    logger.info(f'parcel_out_warehouse1: {parcel.parcel_num}')

    # 当前海运提单中的第一箱出库，就将状态改为出库中
    if ocean_order.order_status == "LOC":
        ocean_order.order_status = 'OUS'
        ocean_order.save()

    # 出仓时 包裹绑定海运提单
    parcel.out_warehouse = True
    parcel.ocean_order = ocean_order
    parcel.save()

    # 当前订单的入仓件数和出仓件数
    current_parcel_list = customer_order.parcel.filter(del_flag=False)
    in_warehouse = current_parcel_list.count()
    out_warehouse = current_parcel_list.filter(out_warehouse=True).count()

    logger.info(f'parcel_out_warehouse2: {parcel.parcel_num}')

    # 订单状态：全部出仓
    if in_warehouse == out_warehouse:
        change_order_status(customer_order, "OW", user=request.user)
        ocean_order.actual_loading_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        unbind_store_position(customer_order)

    logger.info(f'parcel_out_warehouse3: {parcel.parcel_num}')

    # 避免并发情况下不同的包裹出库对同一个出仓数进行+1
    # relate_query = relate_customer_order.first()
    relate_query = CustomerOrderRelateOcean.objects.select_for_update().get(
        id=relate_customer_order.first().id,
        del_flag=False)

    logger.info(f'parcel_out_warehouse4: {parcel.parcel_num}')

    # 出仓件数：+1
    relate_query.out_warehouse_num += 1
    relate_query.save()

    # 配载件数和出仓件数
    # 配载件数：查询当前海运提单下面 每个订单的配载数 的和
    parcels_out_count = relate_ocean_orders.aggregate(Sum('freight_num'))['freight_num__sum'] or 0
    out_warehouse_count = relate_ocean_orders.aggregate(Sum('out_warehouse_num'))['out_warehouse_num__sum'] or 0
    # 出仓票数
    relate_ocean_out = CustomerOrderRelateOcean.objects.filter(~Q(freight_num=0),
                                                               oceanOrder=ocean_order,
                                                               freight_num=F('out_warehouse_num'),
                                                               del_flag=False)
    not_in_warehouse_parcel = current_parcel_list.filter(~Q(is_weighing=True)).values_list('parcel_num', flat=True)
    not_out_warehouse_parcel = current_parcel_list.filter(~Q(out_warehouse=True)).values_list('parcel_num', flat=True)
    logger.info(f'parcel_out_warehouse5: {parcel.parcel_num}')

    # 出仓记录
    ex_warehouse_record = {
        'parcel_num': parcel.parcel_num,
        'order_num': customer_order.order_num,
        'type': 'EX_WAREHOUSE',
        'ocean_order': ocean_order,
        'remark': f'FBA包裹出仓, 当前出仓件数: {relate_query.out_warehouse_num}, 实际出仓件数: {out_warehouse_count}'
    }
    BigPickRecord.objects.create(**ex_warehouse_record, **get_update_params(request, True))

    res_data = {
        'sum_order_num': parcel.parcel_num,  # 包裹号
        'ocean_num_id': ocean_num_id,
        'all_out': in_warehouse == out_warehouse,
        'master_order': {
            'master_order_num': ocean_order.order_num,  # 海运提单号
            # 'pre_carton': current_parcel_list.count(),  # 预计出仓数量
            # 'in_warehouse': customer_order.carton,  # 入仓票数
            # 'out_warehouse': out_warehouse_count,  # 出仓票数

            'set_warehouse': parcels_out_count,  # 配载件数
            'out_warehouse_num': out_warehouse_count,  # 出仓件数

            # 配载票数：查询当前海运提单下面的订单数量
            'set_ocean': relate_ocean_orders.count(),  # 配载票数
            'out_warehouse_qty': relate_ocean_out.count(),  # 出仓票数

            'client_name': customer_order.customer.short_name if customer_order.customer else None,  # 客户姓名
            # 'not_scanned': not_weighing + not_warehouse,
            'not_in_warehouse': len(not_in_warehouse_parcel),  # 未入仓件数
            'not_in_warehouse_parcel': not_in_warehouse_parcel,  # 未入仓包裹列表
            'not_out_warehouse': len(not_out_warehouse_parcel),  # 未出仓件数
            'not_out_warehouse_parcel': not_out_warehouse_parcel,  # 未出仓包裹号列表
            'not_out_warehouse_orders': not_out_warehouse_orders,  # 海运单下包含未出仓包裹的订单号列表
            'scanned_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }
    }
    logger.info(f'出仓成功: {parcel.parcel_num}, 订单号: {parcel.customer_order.order_num}, '
                f'海运单号: {ocean_order.order_num}, 配载件数: {parcels_out_count}, 出仓件数: {out_warehouse_count}')
    request.data['tracking_num'] = False
    request.data['data'] = res_data
    request.data['msg'] = '出仓成功！'
    request.data['code'] = 200
    logger.info(f'parcel_out_warehouse结束: {parcel.parcel_num}')
    return Response(data=request.data, status=status.HTTP_200_OK)


# 修改订单状态
def update_order_status(transport_order, checkpoint_date, order_status):
    transport_order.order_status = order_status
    transport_order.update_date = checkpoint_date
    transport_order.save()


# 合并单元格
def merge_cells(ws):
    max_row = ws.max_row
    max_col = ws.max_column

    for col in range(1, max_col + 1):
        values_in_col = set()

        for row in range(1, max_row + 1):
            cell_value = ws.cell(row=row, column=col).value
            if cell_value is not None:
                values_in_col.add(cell_value)

        for value in values_in_col:
            rows_to_merge = [row for row in range(1, max_row + 1) if ws.cell(row=row, column=col).value == value]
            if len(rows_to_merge) > 1:
                ws.merge_cells(start_row=rows_to_merge[0], start_column=col, end_row=rows_to_merge[-1], end_column=col)
                # 设置合并后的单元格文本居中
                ws.cell(row=rows_to_merge[0], column=col).alignment = Alignment(horizontal='center')


# 对订单的计费重, 确认计费重, 确认计费体积取整
def charge_weight_round(customer_order: CustomerOrder):
    # 对计费重取整
    product = customer_order.product
    if product and product.round_of_factor:
        if customer_order.charge_weight:
            customer_order.save_fields(charge_weight=round_value(customer_order.charge_weight,
                                                                 product.round_of_factor))
    # 对确认计费重取整
    confirm_charge_weight_carry = Dict.objects.filter(label='confirm_charge_weight_carry',
                                                      del_flag=False).last()
    if confirm_charge_weight_carry:
        if customer_order.confirm_charge_weight:
            customer_order.save_fields(confirm_charge_weight=round_value(customer_order.confirm_charge_weight,
                                                                         confirm_charge_weight_carry.value))
    # 对确认计费体积取整
    confirm_charge_volume_carry = Dict.objects.filter(label='confirm_charge_volume_carry',
                                                      del_flag=False).last()
    if confirm_charge_volume_carry:
        if customer_order.confirm_volume:
            customer_order.save_fields(confirm_volume=round_value(customer_order.confirm_volume,
                                                                  confirm_charge_volume_carry.value))
    logger.info(f'对订单的计费重, 确认计费重, 确认计费体积取整: {customer_order.charge_weight}, '
                f'{customer_order.confirm_charge_weight}, {customer_order.confirm_volume}')


def unbind_store_position(customer_order):
    relate_store_queryset = RelateStorePosition.objects.filter(customer_order=customer_order, del_flag=False)
    datetime_now = datetime.now()
    relate_store_queryset.update(del_flag=True, unbind_time=datetime_now, remark='包裹全部出仓解绑')


# 保存上传文件
def save_bulk_model_obj(data_list, excel_name, sheet_name, titles, types='SMALL', record_type='WEIGHING'):
    wb = Workbook()
    worksheet = wb.create_sheet(sheet_name, 0)
    for col_num, title in enumerate(titles, 1):
        worksheet.cell(row=1, column=col_num).value = title
    # 写入数据
    for row_num, row_data in enumerate(data_list, 2):
        for col_num, col_value in enumerate(row_data.values(), 1):
            worksheet.cell(row=row_num, column=col_num).value = col_value
    # 创建一个BytesIO对象，用于存储Excel文件内容
    file_obj = io.BytesIO()
    # 将Excel文件保存到BytesIO对象中
    wb.save(file_obj)
    file_obj.seek(0)
    wb.close()
    content = file_obj.getvalue()
    file_obj.close()
    # 创建一个ContentFile对象，将BytesIO对象的内容包装成可存储的文件对象
    content_file = ContentFile(content)
    # 保存文件到数据库
    bulk_model_obj = BulkParcelWeightRecord()
    bulk_model_obj.create_at = datetime.now()
    bulk_model_obj.types = types
    bulk_model_obj.record_type = record_type
    bulk_model_obj.status = 'successful'
    bulk_model_obj.save()
    # 保存excel路径，excel 名到数据库
    # new_name = str(bulk_model_obj.id) + '-' + str(excel.name)
    new_name = str(bulk_model_obj.id) + '-' + str(excel_name)
    logger.info(f'保存上传文件: {new_name}')
    bulk_model_obj.excel_file.save(new_name, content_file)
    bulk_model_obj.excel_file_name = new_name
    bulk_model_obj.save()


# 获取计费重
def get_charge_weight(parcel_weight, charge_weight_rate, volume):
    charge_weight = 0
    if volume > 0 and charge_weight_rate and charge_weight_rate > 0:
        charge_weight = volume * Decimal(1000000) / Decimal(charge_weight_rate)
    if charge_weight > Decimal(parcel_weight):
        parcel_weight = charge_weight
    return parcel_weight


# 获取体积
def get_volume(parcel_length, parcel_width, parcel_height, volume):
    if float(parcel_length) > 0 and float(parcel_width) > 0 and float(parcel_height) > 0:
        return Decimal(parcel_length) * Decimal(parcel_width) * Decimal(parcel_height) / Decimal(1000000)
    return 0


# 优化重量
def optimize_weight(parcel_weight=None, product=None):
    if not product or not parcel_weight:
        return parcel_weight

    weight = (float(parcel_weight) * 1000)

    is_optimize_weight_queryset = ProductBasicRestriction.objects.filter(product=product, encoding='is_optimize_weight',
                                                                         del_flag=False)
    if is_optimize_weight_queryset.count() > 0:
        current_time = datetime.now()
        target_time = datetime(2023, 12, 18, 0, 0, 0)
        # 当前时间小于2023-12-18才能超过 则不运行
        if 113.41 < weight <= 226.80 and is_optimize_weight_queryset and current_time < target_time:
            return float(random.randint(84, 110)) / 1000
        if 226.81 < weight <= 340.19 and is_optimize_weight_queryset:
            return float(random.randint(196, 220)) / 1000
        if 340.20 < weight <= 453.31 and is_optimize_weight_queryset:
            return float(random.randint(308, 330)) / 1000
        if 453.32 < weight <= 700 and is_optimize_weight_queryset:
            return float(random.randint(420, 440)) / 1000

    # 根据比例10%
    is_optimize_weight_rate_queryset = ProductBasicRestriction.objects.filter(product=product,
                                                                              encoding='is_optimize_weight_rate',
                                                                              del_flag=False)

    if is_optimize_weight_rate_queryset.count() > 0:
        return float(Decimal(parcel_weight * 0.90).quantize(Decimal('0.00')))

    return parcel_weight


@transaction.atomic
def deduction_account(customer_order, user, charge_in_obj):
    """
    扣减账户
    :param customer_order:
    :param user:
    :return:
    """
    charge_in_list = charge_in_obj.objects.filter(customer_order_num=customer_order, del_flag=False)
    if charge_in_list.count() == 0:
        raise ParamError('订单' + customer_order.order_num + '无收入明细')

    total_fee = 0
    currency = None
    for charge_in in charge_in_list:
        total_fee += charge_in.charge_total
        if not currency:
            currency = charge_in.currency_type

    logger.info(f'customer--->>{customer_order.customer}, currency={currency}')
    customer_account_queryset = Account.objects.filter(customer=customer_order.customer, currency=currency,
                                                       del_flag=False)
    if customer_account_queryset.count() == 0:
        raise ParamError('帐户不存在，请联系管理员')
    customer_account = Account.objects.select_for_update().get(id=customer_account_queryset.first().id,
                                                               del_flag=False)

    logger.info(f'customer_account-->{customer_account}')
    # 是否钱包管理 不使用钱包管理的客户不扣废
    if not customer_account.customer.is_wallet_manage:
        logger.info(str(customer_account.customer) + '未启用钱包服务，不用扣款')
        return

    if customer_account.usable_balance - total_fee < 0:
        logger.info(str(customer_account.customer) + '帐户余额不足,剩余' + str(customer_account.usable_balance))
        raise ParamError(f'{str(customer_account.customer)}帐户余额不足,剩余' + str(customer_account.usable_balance),
                         ErrorCode.PARAM_ERROR)

    balance = customer_account.usable_balance

    Account.objects.filter(id=customer_account.id).update(usable_balance=F('usable_balance') - total_fee,
                                                          update_date=datetime.now(), update_by=user)

    order_dict = model_to_dict(customer_order)
    if 'customer_order_num' in order_dict.keys():
        customer_order_num = customer_order.customer_order_num
    elif 'ref_num' in order_dict.keys():
        customer_order_num = customer_order.ref_num
    else:
        customer_order_num = customer_order.order_num

    for charge_in in charge_in_list:
        balance = balance - charge_in.charge_total
        params = {
            'account': customer_account,
            'transaction_type': 'deduction',
            'customer_order_num': customer_order_num,
            'amount': -charge_in.charge_total,
            'currency': currency,
            'transaction_no': customer_order.order_num,
            'transaction_unique_no': 'DE' + create_order_num(charge_in.id),
            'transaction_charge_code': charge_in.charge.code,
            'transaction_charge_name': charge_in.charge.name,
            'exchange_rate': 1,
            'account_currency': currency,
            'account_charge_amount': -charge_in.charge_total,
            'account_balance': balance
        }
        account_detail = AccountDetail.objects.create(**params)
        account_detail.create_by = user
        account_detail.update_by = user
        account_detail.create_date = datetime.now()
        account_detail.update_date = datetime.now()
        # account_detail.remark = '补收1105'
        account_detail.save()


# 根据单号查询获取小包订单
def get_parcel_order_by_order_num(order_num):
    parcel_order = None
    try:
        if is_parcel_customer_order(order_num):
            if '_' in order_num:
                order_num = order_num.split('_')[0]
            parcel_order = ParcelCustomerOrder.objects.get(order_num=order_num, del_flag=False)

        elif len(order_num) == 30:
            parcel_order = ParcelCustomerOrder.objects.get(tracking_num=order_num[8:30], del_flag=False)
        elif len(order_num) == 34 and str(order_num).startswith('4'):
            parcel_order = ParcelCustomerOrder.objects.get(tracking_num=order_num[8:34], del_flag=False)
        elif len(order_num) == 26 and str(order_num).startswith('9'):
            parcel_order_queryset = ParcelCustomerOrder.objects.filter(tracking_num=order_num, del_flag=False)
            if not parcel_order_queryset.exists():
                parcel_order = ParcelCustomerOrder.objects.get(tracking_num__endswith=order_num, del_flag=False)
            else:
                parcel_order = parcel_order_queryset.first()
        elif order_num.startswith('%') and len(order_num) == 28:
            parcel_order = ParcelCustomerOrder.objects.get(tracking_num=order_num[8:22], del_flag=False)
            # parcel_order_queryset = ParcelCustomerOrder.objects.filter(tracking_num__endswith=order_num[8:22], del_flag=False)
            # print(parcel_order_queryset.query)
            # if parcel_order_queryset.exists():
            #     parcel_order = parcel_order_queryset.first()

        elif order_num.startswith('JGB') and len(order_num) > 50:
            parcel_order = ParcelCustomerOrder.objects.get(tracking_num=order_num[10:31], del_flag=False)
        elif len(order_num) > 30:
            parcel_order = ParcelCustomerOrder.objects.get(tracking_num=order_num, del_flag=False)

        if not parcel_order:
            parcel_order = ParcelCustomerOrder.objects.get(~Q(order_status='VO'),
                                                           Q(order_num=order_num) | Q(tracking_num=order_num) | Q(
                                                               customer_order_num=order_num) | Q(
                                                               label_billid=order_num),
                                                           del_flag=False)
    except ParcelCustomerOrder.DoesNotExist:

        if not parcel_order:
            try:
                parcel_order = ParcelCustomerOrder.objects.get(~Q(order_status='VO'),
                                                               Q(order_num=order_num) | Q(tracking_num=order_num) | Q(
                                                                   customer_order_num=order_num) | Q(
                                                                   label_billid=order_num),
                                                               del_flag=False)
            except ParcelCustomerOrder.DoesNotExist:
                parcel_order = None

    return parcel_order


def check_order_status(code, order):
    if code == 'DDSC' and order.order_status not in ['DR']:
        raise ParamError(f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置订单创建生成',
                         ErrorCode.PARAM_ERROR)
    elif code == 'PDC' and order.order_status not in ['DR', 'WO']:
        raise ParamError(
            f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置{ORDER_STATUS[code]}',
            ErrorCode.PARAM_ERROR)
    elif code == 'ITP' and order.order_status in ['ITP', 'VO']:
        raise ParamError(
            f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置{ORDER_STATUS[code]}',
            ErrorCode.PARAM_ERROR)
    elif code == 'PW' and order.order_status not in ['DR', 'WO', 'PDC', 'PW']:
        raise ParamError(
            f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置{ORDER_STATUS[code]}',
            ErrorCode.PARAM_ERROR)
    elif code == 'AW' and order.order_status not in ['WO', 'PDC', 'PW']:
        raise ParamError(
            f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置{ORDER_STATUS[code]}',
            ErrorCode.PARAM_ERROR)
    elif code == 'CWED' and order.order_status not in ['AW']:
        raise ParamError(
            f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置{ORDER_STATUS[code]}',
            ErrorCode.PARAM_ERROR)
    elif code == 'TF' and order.order_status not in ['AW']:
        raise ParamError(
            f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置{ORDER_STATUS[code]}',
            ErrorCode.PARAM_ERROR)
    elif code == 'DEP' and order.order_status not in ['TF', 'OW']:
        raise ParamError(f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置已离港',
                         ErrorCode.PARAM_ERROR)
    elif code == 'ARR' and order.order_status not in ['TF', 'OW']:
        raise ParamError(f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置已到港',
                         ErrorCode.PARAM_ERROR)
    elif code == 'CC' and order.order_status not in ['TF', 'OW']:
        raise ParamError(f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置已清关',
                         ErrorCode.PARAM_ERROR)
    elif code == 'REL' and order.order_status not in ['TF', 'OW']:
        raise ParamError(f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置已放行',
                         ErrorCode.PARAM_ERROR)
    elif code == 'SF' and order.order_status not in ['TF', 'OW']:
        raise ParamError(f'订单{order.order_num},状态:{ORDER_STATUS[order.order_status]}，不能设置已签收',
                         ErrorCode.PARAM_ERROR)

    # 处于全部入仓之前的订单不能越过全部入仓改为之后的状态
    if order.order_status in ['DR', 'WO', 'PDC', 'PW'] and code not in ['DR', 'WO', 'PDC', 'PW', 'AW']:
        raise ParamError(f'处于全部入仓之前的订单不能越过全部入仓改为之后的状态', ErrorCode.PARAM_ERROR)


def convert_fbm_track_code_by(order_status: str):
    """
    转换订单状态为FBM的轨迹编码
    @param order_status:
    @return:
    """

    if order_status == 'VC':
        return 'VC', 'approved', '已审核'
    elif order_status == 'IW':
        return 'IW', 'warehousing', '已到货'
    elif order_status == 'IWC':
        return 'IWC', 'in_warehouse_confirm', '已确认入仓数据'
    elif order_status == 'OWH':
        return 'OWH', 'out_warehouse', '已出仓'
    elif order_status == 'DE':
        return 'DE', 'declared', '已报关'
    elif order_status == 'SO':
        return 'SO', 'steel_off', '已离港/已起飞'
    elif order_status == 'AR':
        return 'AR', 'arrived', '已到港/已降落'
    elif order_status == 'CC':
        return 'CC', 'customs_cleared', '已清关'
    elif order_status == 'IWW':
        return 'IWW', 'IWW', '已到达海外仓'
    elif order_status == 'OOD':
        return 'OOD', 'extracted', '派送中'
    elif order_status == 'SF':
        return 'SF', 'signed', '已签收'
    else:
        return None, None, None


def validate_fbm_order_status(order_status: str, target_order_status: str):
    """
    验证订单状态是否可以转换为目标订单状态
    @param order_status: 当前订单状态
    @param target_order_status: 目标状态
    @return:
    """
    # 当前【已提交】下一个状态是【已审核】
    if order_status == 'WO' and target_order_status != 'VC':
        raise ParamError("当前【已提交】下一个状态是【已审核】", ErrorCode.PARAM_ERROR)

    # 当前【已审核】，下一个状态为【已到货】
    if order_status == 'VC' and target_order_status != 'IW':
        raise ParamError("当前【已审核】，下一个状态为【已到货】", ErrorCode.PARAM_ERROR)

    # 当前【已到货】，下一个状态为【入仓数据确认】
    if order_status == 'IW' and target_order_status != 'IWC':
        raise ParamError("当前【已到货】，下一个状态为【入仓数据确认】", ErrorCode.PARAM_ERROR)

    # 当前【入仓数据确认】，下一个状态为【已出仓】
    if order_status == 'IWC' and target_order_status != 'OWH':
        raise ParamError("当前【入仓数据确认】，下一个状态为【已出仓】", ErrorCode.PARAM_ERROR)

    # 当前【已出仓】，下一个状态为【已报关】
    if order_status == 'OWH' and target_order_status != 'DE':
        raise ParamError("当前【已出仓】，下一个状态为【已报关】", ErrorCode.PARAM_ERROR)

    # 当前【已报关】，下一个状态为【已离港/已起飞】
    if order_status == 'DE' and target_order_status != 'SO':
        raise ParamError("当前【已报关】，下一个状态为【已离港/已起飞】", ErrorCode.PARAM_ERROR)

    # 当前【已离港/已起飞】，下一个状态为【已到港/已降落】
    if order_status == 'SO' and target_order_status != 'AR':
        raise ParamError("当前【已离港/已起飞】，下一个状态为【已到港/已降落】", ErrorCode.PARAM_ERROR)

    # 当前【已到港/已降落】，下一个状态为【已清关】
    if order_status == 'AR' and target_order_status != 'CC':
        raise ParamError("当前【已到港/已降落】，下一个状态为【已清关】", ErrorCode.PARAM_ERROR)

    # 当前【已清关】，下一个状态为【已到达海外仓】
    if order_status == 'CC' and target_order_status != 'IWW':
        raise ParamError("当前【已清关】，下一个状态为【已到达海外仓】", ErrorCode.PARAM_ERROR)

    # 当前【已到达海外仓】，下一个状态为【派送中】
    if order_status == 'IWW' and target_order_status != 'OOD':
        raise ParamError("当前【已到达海外仓】，下一个状态为【派送中】", ErrorCode.PARAM_ERROR)

    # 当前【派送中】，下一个状态为【已签收】
    if order_status == 'OOD' and target_order_status != 'SF':
        raise ParamError("当前【派送中】，下一个状态为【已签收】", ErrorCode.PARAM_ERROR)

    return None


def get_fbm_order_next_status(order_status: str):
    """
    获取订单下一个状态
    @param order_status: 当前订单状态
    @return:
    """
    # 当前【已提交】下一个状态是【已审核】
    if order_status == 'WO':
        return 'VC'
    # 当前【已审核】，下一个状态为【已到货】
    elif order_status == 'VC':
        return 'IW'
    # 当前【已到货】，下一个状态为【入仓数据确认】
    elif order_status == 'IW':
        return 'IWC'
    # 当前【入仓数据确认】，下一个状态为【已出仓】
    elif order_status == 'IWC':
        return 'OWH'
    # 当前【已出仓】，下一个状态为【已报关】
    elif order_status == 'OWH':
        return 'DE'
    # 当前【已报关】，下一个状态为【已离港/已起飞】
    elif order_status == 'DE':
        return 'SO'
    # 当前【已离港/已起飞】，下一个状态为【已到港/已降落】
    elif order_status == 'SO':
        return 'AR'
    # 当前【已到港/已降落】，下一个状态为【已清关】
    elif order_status == 'AR':
        return 'CC'
    # 当前【已清关】，下一个状态为【已到达海外仓】
    elif order_status == 'CC':
        return 'IWW'
    # 当前【已到达海外仓】，下一个状态为【派送中】
    elif order_status == 'IWW':
        return 'OOD'
    # 当前【派送中】，下一个状态为【已签收】
    elif order_status == 'OOD':
        return 'SF'
    else:
        return None


# 计算两个日期相差天数，自定义函数名，和两个日期的变量名。
def calc_day_diff(date1, date2=None):
    date1 = time.strptime(date1, "%Y-%m-%d")
    date2 = time.strptime(datetime.now().strftime("%Y-%m-%d") if date2 is None else date2, "%Y-%m-%d")
    date1 = datetime(date1[0], date1[1], date1[2])
    date2 = datetime(date2[0], date2[1], date2[2])
    # 返回两个变量相差的值，就是相差天数
    return (date2 - date1).days


def get_order_with_order_num(order_num, order_type, charge_type='cost', get_main_order=False):
    charge_queryset = None
    main_order_queryset = None
    try:
        if order_type == 'TR':
            # 客户订单
            currency_order_filter = CustomerOrder.objects.filter(order_num=order_num, del_flag=False)
            order = currency_order_filter.first()
            if charge_type == 'revenue':
                charge_queryset = CustomerOrderChargeIn.objects.filter(customer_order_num=order, del_flag=False)
            elif charge_type == 'cost':
                charge_queryset = CustomerOrderChargeOut.objects.filter(del_flag=False, customer_order_num=order)
        elif order_type == 'OC':
            order = OceanOrder.objects.get(order_num=order_num, del_flag=False)
            if charge_type == 'revenue':
                raise ParamError('海运单没有收入确认！', ErrorCode.PARAM_ERROR)
            elif charge_type == 'cost':
                charge_queryset = OceanOrderChargeOut.objects.filter(customer_order_num=order, del_flag=False)
            if get_main_order:
                main_order_queryset = CustomerOrderRelateOcean.objects.filter(oceanOrder=order, del_flag=False)
        elif order_type == 'MS':
            currency_order_filter = MasterOrder.objects.filter(order_num=order_num, del_flag=False)
            order = currency_order_filter.first()
            if charge_type == 'revenue':
                charge_queryset = MasterOrderChargeIn.objects.filter(customer_order_num=order, del_flag=False)
            elif charge_type == 'cost':
                charge_queryset = MasterOrderChargeOut.objects.filter(customer_order_num=order, del_flag=False)
        elif order_type == 'CLI':
            order = Clearance.objects.get(clearance_num=order_num, del_flag=False)
            if charge_type == 'revenue':
                charge_queryset = ClearanceChargeIn.objects.filter(customer_order_num=order, del_flag=False)
            elif charge_type == 'cost':
                charge_queryset = ClearanceChargeOut.objects.filter(customer_order_num=order, del_flag=False)
            ocean_order = order.ocean_order_id
            if not ocean_order:
                raise ParamError(f'进口报关单成本确认出错: 报关单号: {order_num} 没有绑定的海运提单',
                                 ErrorCode.PARAM_ERROR)
            if get_main_order:
                main_order_queryset = CustomerOrderRelateOcean.objects.filter(oceanOrder=ocean_order, del_flag=False)
        elif order_type == 'TO':
            order = TruckOrder.objects.get(truck_order_num=order_num, del_flag=False)
            if charge_type == 'revenue':
                raise ParamError('卡派单没有收入确认！', ErrorCode.PARAM_ERROR)
            elif charge_type == 'cost':
                charge_queryset = TruckOrderChargeOut.objects.filter(customer_order_num=order, del_flag=False)
            if get_main_order:
                main_order_queryset = CustomerOrder.objects.filter(truck_order_id=order, del_flag=False)
        # elif order_type == 'CollectOrder':
        #     currency_order_filter = CollectOrder.objects.filter(order_num=order_num, del_flag=False)
        #     order = currency_order_filter.first()
        #     if charge_type == 'revenue':
        #         charge_queryset = CollectOrderChargeIn.objects.filter(customer_order_num=order, del_flag=False)
        #     elif charge_type == 'cost':
        #         charge_queryset = CollectOrderChargeOut.objects.filter(customer_order_num=order,
        #                                                                del_flag=False)
        elif order_type == 'CLO':
            currency_order_filter = ClearanceOut.objects.filter(clearance_num=order_num)
            order = currency_order_filter.first()
            if charge_type == 'revenue':
                raise ParamError('出口报关单没有收入确认！', ErrorCode.PARAM_ERROR)
            elif charge_type == 'cost':
                charge_queryset = ClearanceOutChargeOut.objects.filter(customer_order_num=order,
                                                                       del_flag=False)
        else:
            raise ParamError(f'async_cost_finish任务的类型错误: {order_type}', ErrorCode.PARAM_ERROR)
        return order, charge_queryset, main_order_queryset
    except (ObjectDoesNotExist, MultipleObjectsReturned):
        raise ParamError(f'async_cost_finish未找到唯一的单据, 单号: {order_num}, 类型: {order_type}',
                         ErrorCode.PARAM_ERROR)


# 根据单据号查找对应的订单
def get_order_by_order_num(order_num):
    logger.info(f'get_order_by_order_num---->{order_num}')
    if not order_num:
        return None

    order = None
    if is_parcel_customer_order(order_num):
        # 小包
        try:
            order = ParcelCustomerOrder.objects.get(order_num=order_num,
                                                    del_flag=False)
        except ParcelCustomerOrder.DoesNotExist:
            pass
    elif order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK) or \
            order_num.startswith('ZJ' + settings.CUSTOMER_ORDER_MARK):
        # 客户订单
        try:
            order = CustomerOrder.objects.get(order_num=order_num, del_flag=False)
        except CustomerOrder.DoesNotExist:
            pass
    elif order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CLEARANCE_ORDER_MARK):
        # 清关单
        try:
            order = CustomsClearanceOrder.objects.get(order_num=order_num, del_flag=False)
        except CustomsClearanceOrder.DoesNotExist:
            pass
    elif order_num.startswith('CLI'):
        # 进口报关单
        try:
            order = Clearance.objects.get(clearance_num=order_num, del_flag=False)
        except Clearance.DoesNotExist:
            pass
    elif order_num.startswith('TR'):
        # 卡派单
        try:
            order = TruckOrder.objects.get(truck_order_num=order_num, del_flag=False)
        except TruckOrder.DoesNotExist:
            pass
    else:
        # 海运提单
        try:
            order = OceanOrder.objects.get(order_num=order_num, del_flag=False)
        except OceanOrder.DoesNotExist:
            pass
        # 小包单根据转单号查找
        # 转单号不符合上面小包单的判断规则, 这里额外加判断
        if order is None:
            try:
                order = ParcelCustomerOrder.objects.get(tracking_num=order_num, del_flag=False)
            except ObjectDoesNotExist:
                pass
    return order


# 汇总件重体到配载单据上: 订单配置单据时汇总订单的件重体到此单据上
def summary_order_weight_and_size(transport_order, order_foreign_key, summary_confirm_data=False):
    if not transport_order:
        return
    filter_params = {order_foreign_key: transport_order}
    customer_orders = CustomerOrder.objects.filter(~Q(order_status='VO'), **filter_params, del_flag=False)
    summary_field = ['carton', 'weight', 'volume']
    summary_aggregate_field = [Sum('carton'), Sum('weight'), Sum('volume')]
    if summary_confirm_data:
        summary_field.extend(['charge_weight', 'confirm_charge_weight', 'confirm_volume'])
        summary_aggregate_field.extend([Sum('charge_weight'), Sum('confirm_charge_weight'), Sum('confirm_volume')])
    print('summary_aggregate_field-->', summary_aggregate_field)
    stowage_summary = customer_orders.aggregate(*summary_aggregate_field)
    for field in summary_field:
        setattr(transport_order, field, stowage_summary[field + '__sum'] or 0)
    transport_order.save()


# 汇总件重体到多对多配载的单据上(海运单和卡派单通用): 订单配置单据时汇总订单的件重体到此单据上
def summary_order_weight_and_size_many_to_many(transport_order, middle_order_model, middle_transport_order_key):
    """
    :param transport_order: OceanOrder / TruckOrder
    :param middle_order_model: CustomerOrderRelateOcean / CustomerOrderRelateTruck
    :param middle_transport_order_key: oceanOrder / truck_order
    """
    if not transport_order:
        return
    filter_params = {middle_transport_order_key: transport_order}
    relate_orders = middle_order_model.objects.filter(**filter_params, del_flag=False)
    # customer_order_ids = relate_orders.values_list('customer_order_num', flat=True)
    # customer_orders = CustomerOrder.objects.filter(~Q(order_status='VO'), id__in=customer_order_ids, del_flag=False)
    summary_field = ['carton', 'weight', 'volume']
    summary_aggregate_field = {'carton': Sum('freight_num'), 'weight': Sum('allocate_weight'),
                               'volume': Sum('allocate_volume')}
    print('summary_aggregate_field-->', summary_aggregate_field)
    # stowage_summary = customer_orders.aggregate(*summary_aggregate_field)
    stowage_summary = relate_orders.aggregate(**summary_aggregate_field)
    for field in summary_field:
        setattr(transport_order, field, stowage_summary[field] or 0)
    transport_order.save()


# 客户订单限制(2024-12-11改为12kg限制了)
def customer_order_restrict(customer_order: CustomerOrder):
    # 查询产品是否有订单计费重最小10kg限制, 若有限制则限制计费重最小为10kg
    product_basic_restriction = ProductBasicRestriction.objects.filter(encoding='is_weight_least_10',
                                                                       belong_type='A',
                                                                       type='C',
                                                                       product=customer_order.product,
                                                                       del_flag=False).last()
    if product_basic_restriction:
        is_weight_least_10 = product_basic_restriction.insert_trick
        restrict_value = product_basic_restriction.default_value or 12
        if is_weight_least_10 and (customer_order.charge_weight or 0) < restrict_value:
            customer_order.save_fields(charge_weight=restrict_value)


def get_fba_parcel_weight(parcel):
    customer_order = parcel.customer_order
    volume = get_volume(parcel.actual_length, parcel.actual_width, parcel.actual_height, 0)
    product = customer_order.product
    if product and product.charge_weight_rate and not customer_order.charge_trans:
        charge_trans = product.charge_weight_rate
    else:
        charge_trans = customer_order.charge_trans
    num1 = (Decimal(volume) * Decimal(1000000) / Decimal(charge_trans or 6000))
    if num1 > Decimal(parcel.actual_weight):
        result = num1
    else:
        result = Decimal(parcel.actual_weight)
    print('result-->', result)
    return result


# 包裹10kg限制(2024-12-11改为12kg限制了)
def parcel_ten_kg_restrict(parcel):
    # 查询产品是否有订单计费重最小10kg限制, 若有限制则限制计费重最小为10kg
    product_basic_restriction = ProductBasicRestriction.objects.filter(encoding='single_box_is_weight_least_10',
                                                                       type='C',
                                                                       product=parcel.customer_order.product,
                                                                       del_flag=False).last()
    if product_basic_restriction:
        single_box_is_weight_least_10 = product_basic_restriction.insert_trick
        # print('single_box_is_weight_least_10-->', single_box_is_weight_least_10)
        restrict_value = product_basic_restriction.default_value or 12
        if single_box_is_weight_least_10 and get_fba_parcel_weight(parcel) < restrict_value:
            print('fba_parcel_order_obj.actual_weight-->', parcel.actual_weight)
            parcel.save_fields(
                actual_weight=restrict_value,
                is_ten_carry=True,
            )
        else:
            parcel.save_fields(is_ten_carry=False)


def is_number(s):
    if not s:
        return False
    return bool(re.match(r'^-?\d+(\.\d+)?$', s))


def is_integer(s):
    if not s:
        return False
    return bool(re.match(r'^-?\d+$', s))


def parcel_data_check(parcel):
    parcel_size_weight_restrict(parcel)
    check_product_intercept_rule(parcel.customer_order, parcel=parcel, check_type='weighting')


# 包裹尺寸和重量限制标记(仅标记不拦截)
def parcel_size_weight_restrict(parcel):
    restrict_fields = ['parcel_over_length', 'actual_weight']
    # restrict_fields = {'actual_weight': 22, 'actual_length': 120, 'actual_width': 75, 'perimeter': 260}
    customer_order = parcel.customer_order
    # for parcel in customer_order.parcel.filter(del_flag=False):

    # print('actual_weight-->', parcel.actual_weight)
    for restrict_field in restrict_fields:
        product_basic_restriction = ProductBasicRestriction.objects.filter(encoding=restrict_field,
                                                                           type='C',
                                                                           belong_type='B',
                                                                           product=customer_order.product,
                                                                           del_flag=False).first()
        if product_basic_restriction and product_basic_restriction.insert_trick:
            judge_result = False
            # default_value = limit
            # exists_default_value = product_basic_restriction.default_value
            # if is_number(exists_default_value) or is_integer(exists_default_value):
            #     default_value = Decimal(product_basic_restriction.default_value)
            if restrict_field == 'parcel_over_length':
                perimeter = parcel.actual_length + (parcel.actual_width + parcel.actual_height) * 2
                if parcel.actual_length > 120 or parcel.actual_weight > 75 or perimeter > 260:
                    parcel.save_fields(is_overlength=True)
                    customer_order.save_fields(is_overlength=True)
                    judge_result = True
            elif restrict_field == 'actual_weight':
                if getattr(parcel, restrict_field) > 22:
                    parcel.save_fields(is_overweight=True)
                    customer_order.save_fields(is_overweight=True)
                    judge_result = True
            else:
                continue
            logger.info(f'有扩展属性限制, 限制包裹字段: {restrict_field}, 判断结果: {judge_result}')


# 包裹尺寸限制和重量限制_old
def parcel_size_weight_restrict_old(customer_order):
    restrict_fields = {'actual_weight': 22, 'actual_length': 120, 'actual_width': 75, 'perimeter': 260}
    for parcel in customer_order.parcel.filter(del_flag=False):
        print('actual_weight-->', parcel.actual_weight)
        for restrict_field, limit in restrict_fields.items():
            product_basic_restriction = ProductBasicRestriction.objects.filter(encoding=restrict_field,
                                                                               type='C',
                                                                               belong_type='B',
                                                                               product=customer_order.product,
                                                                               del_flag=False).first()
            if product_basic_restriction and product_basic_restriction.insert_trick:
                judge_result = False
                default_value = limit
                exists_default_value = product_basic_restriction.default_value
                if is_number(exists_default_value) or is_integer(exists_default_value):
                    default_value = Decimal(product_basic_restriction.default_value)
                if restrict_field == 'actual_weight':
                    if getattr(parcel, restrict_field) > default_value:
                        parcel.is_overweight = True
                        customer_order.is_overweight = True
                        judge_result = True
                elif restrict_field == 'perimeter':
                    perimeter = parcel.actual_length + (parcel.actual_width + parcel.actual_height) * 2
                    if perimeter > default_value:
                        parcel.is_overlength = True
                        customer_order.is_overlength = True
                        judge_result = True
                else:
                    if getattr(parcel, restrict_field) > default_value:
                        parcel.is_overlength = True
                        customer_order.is_overlength = True
                        judge_result = True
                logger.info(
                    f'有扩展属性限制, 限制包裹字段: {restrict_field}, 数值: {default_value}, 判断结果: {judge_result}')
                parcel.save()
                customer_order.save()


@transaction.atomic
def repair_account(customer_order, user, total_fee, currency, account):
    """
    补收取
    :param customer_order:
    :param user:
    :return:
    """

    customer_account_queryset = Account.objects.filter(customer=customer_order.customer, currency=currency,
                                                       del_flag=False)
    if customer_account_queryset.count() == 0:
        raise ParamError('帐户不存在，请联系管理员')

    customer_account = Account.objects.select_for_update().get(id=customer_account_queryset.first().id,
                                                               del_flag=False)

    if customer_account.usable_balance - total_fee < 0:
        logger.info(str(customer_account.customer) + '帐户余额不足,剩余' + str(customer_account.usable_balance))
        raise ParamError(
            f'{str(customer_account.customer)}帐户余额不足,剩余' + str(customer_account.usable_balance),
            ErrorCode.PARAM_ERROR)

    balance = customer_account.usable_balance

    Account.objects.filter(id=customer_account.id).update(usable_balance=F('usable_balance') - total_fee,
                                                          update_date=datetime.now(), update_by=user)

    params = {
        'account': customer_account,
        'transaction_type': 'deduction',
        'customer_order_num': customer_order.order_num,
        'amount': -total_fee,
        'currency': currency,
        'transaction_no': customer_order.order_num,
        'transaction_unique_no': 'DE' + create_order_num(account.id),
        'transaction_charge_code': account.transaction_charge_code,
        'transaction_charge_name': account.transaction_charge_name,
        'exchange_rate': 1,
        'account_currency': currency,
        'account_charge_amount': -total_fee,
        'account_balance': balance - total_fee,
        'remark': '补收1105'
    }
    account_detail = AccountDetail.objects.create(**params)
    account_detail.create_by = user
    account_detail.update_by = user
    account_detail.create_date = datetime.now()
    account_detail.update_date = datetime.now()
    account_detail.save()


# 获取供营商配置信息
def get_service_class_url(service_class_code):
    service_class = ServiceClass.objects.filter(code=service_class_code, del_flag=False).first()
    if not service_class:
        error_msg = f'没有创建 {service_class_code} 服务类'
        logger.error(error_msg)
        return 1, error_msg
    supplier_butt = SupplierButt.objects.filter(class_name=service_class.code, del_flag=False).first()
    if not supplier_butt:
        error_msg = f'供应商服务对接没有关联 {service_class_code} 服务类'
        logger.error(error_msg)
        return 1, error_msg
    supplier_accounts = SupplierButtAccount.objects.filter(vendor_id__code=supplier_butt.vendor_id.code,
                                                           type='SYNC',
                                                           del_flag=False)

    if not supplier_accounts.exists():
        error_msg = f'没有供应商为: {supplier_butt.vendor_id.code}的同步订单的账户信息'
        logger.error(error_msg)
        return 1, error_msg

    supplier_account = supplier_accounts.first()
    return 0, supplier_account


# 计算泡比优惠
def calc_bubble_discounts(customer_order):
    if hasattr(customer_order, 'order_num'):
        order_num = customer_order.order_num
    elif hasattr(customer_order, 'inquiry_num'):
        order_num = customer_order.inquiry_num
    else:
        order_num = customer_order.truck_order_num
    commission_revenue = [None, None]
    product = customer_order.product
    # 泡比 = 实重 / 体积
    if not customer_order.volume:
        return commission_revenue
    # 泡比优惠黑名单不计算费用, 返回0
    commission_discounts_black = None
    if hasattr(customer_order, 'customer'):
        customer = customer_order.customer
        commission_discounts_black = CommissionDiscountsBlack.objects.filter(customer=customer, del_flag=False).first()
    if commission_discounts_black and commission_discounts_black.bubble_discounts_status == 'ON':
        return commission_revenue

    bubble = customer_order.weight / customer_order.volume
    logger.info(f'计算泡比优惠, 订单: {order_num}, 产品: {product.name}, 泡比: {bubble}')
    check_in_time = get(customer_order, 'check_in_time', datetime.now())
    product_bubble_discounts_queryset = ProductBubbleDiscounts.objects.filter(product=product, del_flag=False)
    if product_bubble_discounts_queryset.exists() and check_in_time:
        apply_types = ['commission', 'revenue']
        for product_bubble_discounts in product_bubble_discounts_queryset:
            for index, apply_type in enumerate(apply_types):
                bubble_discounts = BubbleDiscounts.objects.filter(product_bubble_discounts=product_bubble_discounts,
                                                                  start_time__lte=check_in_time,
                                                                  end_time__gt=check_in_time,
                                                                  apply_type=apply_type,
                                                                  del_flag=False).last()
                if bubble_discounts:
                    logger.info(
                        f'比对泡比优惠价格区间: {bubble_discounts.rank_start}, {bubble}, {bubble_discounts.rank_end}')
                    if bubble_discounts.rank_start <= bubble < bubble_discounts.rank_end:
                        logger.info(f'找到符合的泡比优惠规则, 订单: {order_num}, 产品: {product.name}, '
                                    f'泡比: {bubble}, 优惠价格: {bubble_discounts.price}')
                        commission_revenue[index] = bubble_discounts
        logger.info(f'查询泡比优惠规则, 订单: {order_num}, 产品: {product.name}, '
                    f'泡比: {commission_revenue}, 签入时间: {check_in_time}')
        return commission_revenue
        # logger.error(f'泡比优惠规则里没有符合此单的规则, 订单: {order_num}, 产品: {product.name}, '
        #              f'泡比: {bubble}, 签入时间: {check_in_time}')
    logger.info(f'此单产品没有泡比优惠规则, 订单: {order_num}, 产品: {product.name}')
    return commission_revenue


# 计算重量提成
def calc_weight_commission(customer_order):
    if hasattr(customer_order, 'order_num'):
        order_num = customer_order.order_num
    elif hasattr(customer_order, 'inquiry_num'):
        order_num = customer_order.inquiry_num
    else:
        order_num = customer_order.truck_order_num
    # 计费重等计费单位字段转换
    charge_unit_trans = {
        'chargeWeight': 'charge_weight',
        'confirmChargeWeight': 'confirm_charge_weight',
        'confirmChargeVolume': 'confirm_volume',
    }
    # 重量提成黑名单不计算费用, 返回0
    commission_discounts_black = None
    if hasattr(customer_order, 'customer'):
        customer = customer_order.customer
        commission_discounts_black = CommissionDiscountsBlack.objects.filter(customer=customer, del_flag=False).first()
    if commission_discounts_black and commission_discounts_black.weight_commission_status == 'ON':
        return 0, None
    # calc_unit = {v: k for k, v in dict(WeightCommission.UNIT).items()}
    product = customer_order.product
    check_in_time = get(customer_order, 'check_in_time', datetime.now())
    product_weight_commission_queryset = ProductWeightCommission.objects.filter(product=product, del_flag=False)
    if product_weight_commission_queryset.exists() and check_in_time:
        for product_weight_commission in product_weight_commission_queryset:
            # print('product_weight_commission-->', product_weight_commission, check_in_time)
            weight_commission = WeightCommission.objects.filter(product_weight_commission=product_weight_commission,
                                                                start_time__lte=check_in_time,
                                                                end_time__gt=check_in_time,
                                                                del_flag=False).last()
            if weight_commission:
                charge_unit = charge_unit_trans.get(weight_commission.charge_unit, weight_commission.charge_unit)
                if hasattr(customer_order, charge_unit):
                    if getattr(customer_order, charge_unit) is None:
                        charge_unit_char = dict(WeightCommission.UNIT).get(weight_commission.charge_unit)
                        raise ParamError(f'计算重量提成: 订单没有计费重, 订单: {order_num}, 计费重: {charge_unit_char}',
                                         ErrorCode.PARAM_ERROR)
                    price = getattr(customer_order, charge_unit) * weight_commission.commission_rate
                    logger.info(f'找到符合的重量提成规则, 订单: {order_num}, 产品: {product.name}, '
                                f'{charge_unit}: {getattr(customer_order, charge_unit)}, '
                                f'提成率: {weight_commission.commission_rate}, 提成价格: {price}')
                    return price, weight_commission
                else:
                    raise ParamError(f'订单 {order_num}没有属性 {charge_unit}', ErrorCode.PARAM_ERROR)
        logger.error(f'重量提成规则里没有符合此单的规则, 订单: {order_num}, 产品: {product.name}, '
                     f'签入时间: {check_in_time}')
        return None, None
    logger.info(f'此单产品没有重量提成规则, 订单: {order_num}, 产品: {product.name}')
    return None, None


# 创建订单的收入计价
def create_order_charge_in(customer_order, order_charge_in_obj, charge_code, price, charge_count, currency, user):
    charge = Charge.objects.filter(code=charge_code, del_flag=False).first()
    if not charge:
        raise ParamError(f'费用代码不存在: {charge_code}', ErrorCode.PARAM_ERROR)
    # order_charge_in_obj = CustomerOrderChargeIn
    charge_in_old = order_charge_in_obj.objects.filter(customer_order_num=customer_order,
                                                       charge=charge, del_flag=False, is_system=True).first()
    # charge_in_old.update(del_flag=True)
    if charge_in_old:
        charge_in = charge_in_old
    else:
        charge_in = order_charge_in_obj()
    if hasattr(customer_order, 'customer'):
        charge_in.customer = customer_order.customer
    charge_in.is_system = True
    charge_in.charge = charge
    charge_in.charge_rate = price
    # 默认使用单价 * 数量 = 合计
    charge_in.charge_total = price * charge_count
    charge_in.charge_count = charge_count
    charge_in.base_price = None
    charge_in.currency_type = currency
    charge_in.customer_order_num = customer_order
    # 通用获取汇率
    current_exchange = get_currency_rate(currency, [currency])
    logger.info(f'current_exchange汇率: {current_exchange}')
    charge_in.current_exchange = current_exchange
    charge_in.account_charge = price * charge_count * current_exchange
    charge_in.create_by = user
    charge_in.update_by = user
    charge_in.create_date = datetime.now()
    charge_in.update_date = datetime.now()
    logger.info(f'创建订单的收入计价: {charge_in.__dict__}')

    # if revenue_result.price_version:
    #     charge_in.price_version = revenue_result.price_version
    # charge_in.charge_weight = revenue_result.charge_weight

    charge_in.save()


# 获取计费数据
def get_charge_unit_data(product_charge, customer_order):
    if product_charge.charge_unit == 'confirmChargeWeight':
        return customer_order.confirm_charge_weight
    elif product_charge.charge_unit == 'confirmChargeVolume':
        return customer_order.confirm_volume
    elif product_charge.charge_unit == 'bubble':
        if not customer_order.volume:
            logger.info(f'计算泡比体积不能为0, 订单: {get(customer_order, "order_num")}')
            return None
        return customer_order.weight / customer_order.volume
    elif hasattr(customer_order, product_charge.charge_unit):
        return getattr(customer_order, product_charge.charge_unit)
    else:
        logger.info(f'未查询到正确的计费单位, 订单: {get(customer_order, "order_num")}, '
                    f'计费单位: {product_charge.charge_unit}')
        return None


# 计算并保存泡比优惠和重量提成
@transaction.atomic
def save_crm_price(customer_order, order_charge_in_obj, user=None, is_profit=False):
    if hasattr(customer_order, 'order_num'):
        order_num = customer_order.order_num
    elif hasattr(customer_order, 'inquiry_num'):
        order_num = customer_order.inquiry_num
    else:
        order_num = customer_order.truck_order_num
    # 泡比优惠
    commission_bubble_discounts, revenue_bubble_discounts = calc_bubble_discounts(customer_order)
    logger.info(
        f'计算订单泡比优惠, 订单: {order_num}, 泡比优惠: 提成: {commission_bubble_discounts}, 收入: {revenue_bubble_discounts}')
    if revenue_bubble_discounts is not None:
        # order_bubble_discounts_queryset = OrderCommissionDiscounts.objects.filter(customer_order=customer_order,
        #                                                                           discounts_type='bubble_discounts',
        #                                                                           del_flag=False)
        # if order_bubble_discounts_queryset.exists():
        #     order_bubble_discounts_queryset.update(price=discounts_price, is_summarizing=False)
        # else:
        #     OrderCommissionDiscounts.objects.create(customer_order=customer_order, price=discounts_price,
        #                                             discounts_type='bubble_discounts',
        #                                             is_summarizing=False)
        discounts_price = revenue_bubble_discounts.price
        charge_count = get_charge_unit_data(revenue_bubble_discounts, customer_order)
        if is_profit:
            real_discounts_price = discounts_price
        else:
            real_discounts_price = -discounts_price
        try:
            create_order_charge_in(customer_order, order_charge_in_obj, 'PBYH', real_discounts_price, charge_count,
                                   revenue_bubble_discounts.currency, user)
        except ParamError as e:
            if '费用代码不存在' in str(e):
                raise ParamError(f'请创建 "泡比优惠": "PBYH" 业务费用项', ErrorCode.PARAM_ERROR)


# 删除已经生成的泡比优惠和重量提成
@transaction.atomic
def delete_crm_price(customer_order):
    # 泡比优惠
    # order_bubble_discounts_queryset = OrderCommissionDiscounts.objects.filter(customer_order=customer_order,
    #                                                                           discounts_type='bubble_discounts',
    #                                                                           del_flag=False)
    customer_order_charge_in = CustomerOrderChargeIn.objects.filter(customer_order=customer_order,
                                                                    charge__code='ZLTC',
                                                                    del_flag=False)

    if customer_order_charge_in.exists():
        customer_order_charge_in.update(del_flag=True)
    # 重量提成
    # order_weight_commission_queryset = OrderCommissionDiscounts.objects.filter(customer_order=customer_order,
    #                                                                            discounts_type='weight_commission',
    #                                                                            del_flag=False)
    customer_order_charge_in = CustomerOrderChargeIn.objects.filter(customer_order=customer_order,
                                                                    charge__code='PBYH',
                                                                    del_flag=False)
    if customer_order_charge_in.exists():
        customer_order_charge_in.update(del_flag=True)


# 产品销售定价转协议价(main)(同步创建协议价, 系统不走销售定价, 先走协议价)
# @transaction.atomic
def product_sales_price_trans_protocol_price(user, product_sales_price):
    logger.info(f'产品销售定价转协议价开始, 销售定价版本: {product_sales_price.price_version}, '
                f'销售定价id: {product_sales_price.id}')
    # product_sales_price = ProductSalesPrice()
    product = product_sales_price.product
    product_revenue_version_name = product_sales_price.price_version

    # 查询产品销售定价客户
    product_sales_price_customers = ProductSalesPriceCustomer.objects.filter(
        product_sales_price=product_sales_price, del_flag=False)
    # if not product_sales_price_customers.exists():
    #     raise ParamError(f'产品销售定价没有客户, 销售定价版本 {product_sales_price.price_version}',
    #                      ErrorCode.PARAM_ERROR)

    # 查询产品销售定价仓库
    product_sales_price_warehouses = ProductSalesPriceWarehouse.objects.filter(
        product_sales_price=product_sales_price, del_flag=False)
    print('product_sales_price_warehouses-->', product_sales_price_warehouses)
    # if not product_sales_price_warehouses.exists():
    #     raise ParamError(f'产品销售定价没有产品, 销售定价版本 {product_sales_price.price_version}',
    #                      ErrorCode.PARAM_ERROR)

    product_sales_price_end_time = product_sales_price.end_time
    if not product_sales_price_end_time:
        current_year = datetime.now().year
        current_century_start = (current_year // 100) * 100
        current_century_end = current_century_start + 99
        product_sales_price_end_time = datetime(current_century_end, 1, 1)

    product_charge_queryset = ProductCharge.objects.filter(~Q(charge_unit='combine'),
                                                           product=product,
                                                           charge__code='YF', del_flag=False)
    if not product_charge_queryset.exists:
        raise ParamError(f'销售定价的产品: {product}下的收费项没有配置运费, 请先配置运费', ErrorCode.PARAM_ERROR)

    for product_charge in product_charge_queryset:
        print('product_charge-->', product_charge)
        # 查询或创建收入价格版本(协议价)
        product_revenue_versions = ProductRevenueVersion.objects.filter(
            product=product,
            product_charge=product_charge,
            version_name=product_revenue_version_name,
            # start_time=product_sales_price.start_time,
            currency='CNY',
            price_type='B',
            del_flag=False
        )
        # ).order_by('-start_time').first()
        if product_revenue_versions.exists():
            print('product_revenue_versions-->', product_revenue_versions)
            product_revenue_version = product_revenue_versions.first()
            product_revenue_versions.update(del_flag=True)
            product_revenue_version.save_fields(del_flag=False, start_time=product_sales_price.start_time)
            print('product_revenue_version-->', product_revenue_version, product_revenue_version.del_flag)
        else:
            product_revenue_version = ProductRevenueVersion.objects.create(
                product=product,
                product_charge=product_charge,
                version_name=product_revenue_version_name,
                start_time=product_sales_price.start_time,
                currency='CNY',
                price_type='B',
                **get_update_params_by_user(user, True)
            )

        # 创建/更新协议价
        # 查询或创建产品协议方案
        exists_protocol_projects = ProtocolProject.objects.filter(
            # protocol_project_customer__customer=customer,
            protocol_project_product__product_revenue_version=product_revenue_version,
            # start_time=product_sales_price.start_time,
            # end_time=product_sales_price_end_time,
            # del_flag=False
        )
        logger.info(f'查询并删除旧的协议价: {exists_protocol_projects}')
        # 更新产品销售定价的开始时间和结束时间时, 将时间不一致的产品协议方案删掉
        exists_protocol_projects.update(del_flag=True)
        protocol_projects = exists_protocol_projects.filter(start_time=product_sales_price.start_time,
                                                            end_time=product_sales_price_end_time)
        if protocol_projects.exists():
            protocol_project = protocol_projects.first()
            protocol_project.del_flag = False
            protocol_project.save()
        else:
            protocol_project = ProtocolProject.objects.create(
                start_time=product_sales_price.start_time,
                end_time=product_sales_price_end_time,
                **get_update_params_by_user(user, True)
            )
            protocol_project.code = protocol_project.name = 'PPC' + create_order_num(protocol_project.id, 9)
            protocol_project.save()

        ProtocolProjectCustomer.objects.filter(protocol_project=protocol_project,
                                               del_flag=False).update(del_flag=True)
        ProtocolProjectProduct.objects.filter(protocol_project=protocol_project,
                                              del_flag=False).update(del_flag=True)
        for product_sales_price_customer in product_sales_price_customers:
            customer = product_sales_price_customer.customer
            # product_revenue_version_name = f'{product}{datetime.now().strftime("%Y%m%d")}_SYS'
            # product_revenue_versions = []

            # 查询或创建协议方案客户
            exists_protocol_project_customers = ProtocolProjectCustomer.objects.filter(
                protocol_project=protocol_project, customer=customer)
            print('exists_protocol_project_customers-->', exists_protocol_project_customers)
            if exists_protocol_project_customers.exists():
                exists_protocol_project_customer = exists_protocol_project_customers.first()
                exists_protocol_project_customer.del_flag = False
                exists_protocol_project_customer.save()
            else:
                ProtocolProjectCustomer.objects.create(
                    protocol_project=protocol_project, customer=customer,
                    **get_update_params_by_user(user, True))

            # 查询或创建协议方案产品
            exists_protocol_project_products = ProtocolProjectProduct.objects.filter(
                product_revenue_version=product_revenue_version, protocol_project=protocol_project)
            print('exists_protocol_project_products-->', exists_protocol_project_products)
            if exists_protocol_project_products.exists():
                exists_protocol_project_product = exists_protocol_project_products.first()
                exists_protocol_project_product.del_flag = False
                exists_protocol_project_product.save()
            else:
                ProtocolProjectProduct.objects.create(
                    product_revenue_version=product_revenue_version, protocol_project=protocol_project,
                    **get_update_params_by_user(user, True))

        protocol_create_revenue_version_line(product, user, product_revenue_version, product_sales_price,
                                             product_sales_price_warehouses)

    logger.info(f'产品销售定价转协议价成功, 销售定价版本: {product_sales_price.price_version}, '
                f'销售定价id: {product_sales_price.id}')


# 协议价创建收入价格明细
def protocol_create_revenue_version_line_old(product, user, product_revenue_version, product_sales_price,
                                             product_sales_price_warehouses):
    # 把旧的收入价格明细先全部删掉
    old_version_line = ProductRevenueVersionLine.objects.filter(price_version=product_revenue_version,
                                                                # zone_value=product_route.zone_value,
                                                                charge_type='DJ',
                                                                currency='CNY',
                                                                data_source='S',  # S代表产品销售定价同步创建
                                                                del_flag=False)
    if old_version_line.exists():
        old_version_line.delete()

    for price_warehouse in product_sales_price_warehouses:
        logger.info(f'查询到产品销售定价仓库: {price_warehouse.warehouse}')
        # # 根据发货仓库查找发件人, 获取发件人国家, 进而查找起点分区
        # product_zone = ProductZone.objects.filter(
        #     country_code=price_warehouse.warehouse.country_code,
        #     type='Seller',
        #     del_flag=False
        # )
        # # 搜索或创建产品路线
        # exists_product_route = ProductRoute.objects.filter(
        #     product=product, del_flag=False
        # ).first()

        # 获取起点分区
        start_dist_zones = get_zone(None, product, 'Seller',
                                    price_warehouse.warehouse.country_code,
                                    price_warehouse.warehouse.postcode)
        if not start_dist_zones:
            error_msg = f'销售定价转协议价失败: 未找到起点分区, 发货仓库: {price_warehouse.warehouse.address_num}, ' \
                        f'国家: {price_warehouse.warehouse.country_code}, ' \
                        f'邮编: {price_warehouse.warehouse.postcode}'
            logger.error(error_msg)
            raise ParamError(error_msg, ErrorCode.PARAM_ERROR)

        start_zones = [x.name for x in start_dist_zones]
        logger.info(f'产品销售定价转协议价, 收入-路线起点分区: {start_zones}')

        # 查询产品路线
        for start_dist_zone in start_dist_zones:
            # for end_dist_zone in end_dist_zones:
            product_route = ProductRoute.objects.filter(
                product=product,
                start_zone=start_dist_zone,
                end_zone=price_warehouse.end_zone,
                start_time__lte=product_sales_price.start_time,
                del_flag=False
            ).order_by('-start_time', '-id').first()
            if not product_route:
                # # 查找最大的分区值并+1得到新的分区值
                # product_routes = ProductRoute.objects.filter(product=product, del_flag=False)
                # zone_values = product_routes.values_list('zone_value', flat=True)
                # zone_values = [int(i) for i in zone_values if i and i.isdecimal()]
                # new_zone_value = str(max(zone_values) + 1)
                # product_route = ProductRoute.objects.create(
                #     product=product,
                #     start_zone=start_dist_zone,
                #     end_zone=price_warehouse.end_zone,
                #     start_time=product_sales_price.start_time,
                #     zone_value=new_zone_value,
                #     **get_update_params_by_user(user, True)
                # )
                raise ParamError(f'销售定价的产品: {product}没有匹配的产品路线, 起点分区: {start_dist_zone}, '
                                 f'终点分区: {price_warehouse.end_zone}, 开始时间: {product_sales_price.start_time}',
                                 ErrorCode.PARAM_ERROR)
            logger.info(f'查询产品路线, 起点分区: {start_dist_zone}, 终点分区: {price_warehouse.end_zone}')

            # 创建收入价格明细(把产品销售定价的收货价作为收入价格明细的价格)
            # zone_value = product_routes[0].zone_value
            print('product_route.zone_value-->', product_route.zone_value)

            # old_version_line = ProductRevenueVersionLine.objects.filter(price_version=product_revenue_version,
            #                                                             zone_value=product_route.zone_value,
            #                                                             charge_type='DJ',
            #                                                             currency='CNY',
            #                                                             rank_start=price_warehouse.rank_start,
            #                                                             rank_end=price_warehouse.rank_end,
            #                                                             # price=price_warehouse.receive_goods_price,
            #                                                             data_source='S',  # S代表产品销售定价同步创建
            #                                                             del_flag=False)

            version_line = ProductRevenueVersionLine.objects.create(price_version=product_revenue_version,
                                                                    zone_value=product_route.zone_value,
                                                                    charge_type='DJ',
                                                                    currency='CNY',
                                                                    rank_start=price_warehouse.rank_start,
                                                                    rank_end=price_warehouse.rank_end,
                                                                    price=price_warehouse.receive_goods_price,
                                                                    data_source='S',  # S代表产品销售定价同步创建
                                                                    **get_update_params_by_user(user, True))


# 协议价创建收入价格明细
def protocol_create_revenue_version_line(product, user, product_revenue_version, product_sales_price,
                                         product_sales_price_warehouses):
    # 把旧的收入价格明细先全部删掉
    old_version_line = ProductRevenueVersionLine.objects.filter(price_version=product_revenue_version,
                                                                # zone_value=product_route.zone_value,
                                                                charge_type='DJ',
                                                                currency='CNY',
                                                                data_source='S',  # S代表产品销售定价同步创建
                                                                del_flag=False)
    if old_version_line.exists():
        old_version_line.delete()

    for price_warehouse in product_sales_price_warehouses:
        logger.info(f'查询到产品销售定价仓库: {price_warehouse.warehouse}')

        # 获取起点分区
        start_dist_zones = get_zone(None, product, 'Seller',
                                    price_warehouse.warehouse.country_code,
                                    price_warehouse.warehouse.postcode)
        if not start_dist_zones:
            error_msg = f'销售定价转协议价失败: 未找到起点分区, 发货仓库: {price_warehouse.warehouse.address_num}, ' \
                        f'国家: {price_warehouse.warehouse.country_code}, ' \
                        f'邮编: {price_warehouse.warehouse.postcode}'
            logger.error(error_msg)
            raise ParamError(error_msg, ErrorCode.PARAM_ERROR)

        start_zones = [x.name for x in start_dist_zones]
        logger.info(f'产品销售定价转协议价, 收入-路线起点分区: {start_zones}')

        # 查询或创建产品协议路线
        for start_dist_zone in start_dist_zones:
            product_route = ProductRoute.objects.filter(
                product=product,
                start_zone=start_dist_zone,
                end_zone=price_warehouse.end_zone,
                start_time__lte=product_sales_price.start_time,
                del_flag=False,
                # route_type='PROTOCOL_ROUTE',
            ).order_by('-start_time', '-id').first()
            if not product_route:
                # 目前存在bug, 由于不确定发货仓库所在的路线需不需要新增, 所以暂时不新增产品路线
                # 而且如果用户也添加了相同的路线, 则不会创建路线, 并且把所有该路线下创建的收入价格明细删掉了
                # product_route = ProductRoute.objects.create(
                #     product=product,
                #     start_zone=start_dist_zone,
                #     end_zone=price_warehouse.end_zone,
                #     start_time=product_sales_price.start_time,
                #     zone_value=f'PROTOCOL_{price_warehouse.end_zone}',
                #     route_type='PROTOCOL_ROUTE',
                #     **get_update_params_by_user(user, True)
                # )
                raise ParamError(f'销售定价的产品: {product}没有匹配的产品路线, 起点分区: {start_dist_zone}, '
                                 f'终点分区: {price_warehouse.end_zone}, 开始时间: {product_sales_price.start_time}',
                                 ErrorCode.PARAM_ERROR)
            logger.info(f'查询或创建产品路线, 起点分区: {start_dist_zone}, 终点分区: {price_warehouse.end_zone}, '
                        f'分区值: PROTOCOL_{price_warehouse.end_zone}')

            # 创建收入价格明细(把产品销售定价的收货价作为收入价格明细的价格)
            # zone_value = product_routes[0].zone_value
            print('product_route.zone_value-->', product_route.zone_value)
            version_line = ProductRevenueVersionLine.objects.create(price_version=product_revenue_version,
                                                                    zone_value=product_route.zone_value,
                                                                    charge_type='DJ',
                                                                    currency='CNY',
                                                                    rank_start=price_warehouse.rank_start,
                                                                    rank_end=price_warehouse.rank_end,
                                                                    price=price_warehouse.receive_goods_price,
                                                                    data_source='S',  # S代表产品销售定价同步创建
                                                                    **get_update_params_by_user(user, True))


# 客户订单状态映射函数
def get_order_status_mapping(order_status):
    status_mapping = []
    if isinstance(order_status, str):
        for fbm_code, fba_code in CUSTOMER_ORDER_STATUS_MAP.items():
            if fbm_code == order_status or fba_code == order_status:
                if settings.SYSTEM_ORDER_MARK in ['CLT']:
                    # status_mapping.update([fbm_code, fba_code])
                    status_mapping.append(fbm_code)
                elif settings.SYSTEM_ORDER_MARK in ['FX']:
                    status_mapping.append(fbm_code)
                    status_mapping.append(fba_code)
                else:
                    print('fba_code-->', fba_code)
                    status_mapping.append(fba_code)
    elif isinstance(order_status, list):
        for fbm_code, fba_code in CUSTOMER_ORDER_STATUS_MAP.items():
            # print('1??-->', fbm_code, fba_code)
            for status_code in order_status:
                # print('2??-->', status_code)
                if fbm_code == status_code or fba_code == status_code:
                    if settings.SYSTEM_ORDER_MARK in ['CLT']:
                        # status_mapping.update([fbm_code, fba_code])
                        status_mapping.append(fbm_code)
                    elif settings.SYSTEM_ORDER_MARK in ['FX']:
                        status_mapping.append(fbm_code)
                        status_mapping.append(fba_code)
                    else:
                        print('fba_code-->', fba_code)
                        status_mapping.append(fba_code)
    else:
        raise ParamError('参数错误', ErrorCode.PARAM_ERROR)
    return list(OrderedDict.fromkeys(status_mapping))


# 计算销售定价的成本价
@transaction.atomic
def calc_sales_cost_price(customer_order, user, is_raise_err=False):
    if settings.SYSTEM_ORDER_MARK in ['MZ', 'YQF', 'FX']:
        charge_in = CustomerOrderSaleCost.objects.filter(customer_order_num=customer_order,
                                                         del_flag=False, data_source='S')
        charge_in.delete()
        try:
            # print('111-->')
            add_revenue(customer_order, user, CustomerOrderSaleCost, is_recharge=True,
                        calc_type='sales_cost')
        except ParamError as e:
            if is_raise_err:
                raise ParamError(e, ErrorCode.PARAM_ERROR)
            else:
                charge_in = CustomerOrderSaleCost()
                charge_in.customer = customer_order.customer
                charge_in.is_system = True
                charge_in.customer_order_num = customer_order
                charge_in.create_by = user
                charge_in.update_by = user
                charge_in.create_date = datetime.now()
                charge_in.update_date = datetime.now()
                charge_in.valuation_detail = str(e)
                charge_in.save()


@transaction.atomic
def calculate_mark_sleep(sleep_threshold_days=30):
    """
        计算客户是否超过指定天数未发货，若是则标记为休眠，未超过标记为非休眠。

        params:
            sleep_threshold_days (int): 未发货天数阈值，默认为30天。

        return:
            tuple: (sleep_count, active_count) 被标记为休眠和非休眠的客户数量。
    """
    current_date = timezone.now().date()
    threshold_date = current_date - timedelta(days=sleep_threshold_days)

    # 查询所有客户及其最近的 actual_arrival_date
    companies = Company.objects.filter(
        is_customer=True,
        del_flag=False
    ).annotate(
        latest_arrival_date=Max('order_customer__actual_arrival_date')
    )

    # 使用条件表达式创建比较日期字段
    companies = companies.annotate(
        comparison_date=Case(
            # 当有最后发货日期时，使用该日期
            When(latest_arrival_date__isnull=False, then=F('latest_arrival_date')),
            # 默认值
            default=F('create_date'),
            output_field=models.DateField()
        )
    )

    # 需要标记为休眠的客户（无订单或最新日期早于阈值）
    sleep_companies = companies.filter(
        comparison_date__lt=threshold_date
    ).exclude(is_sleep=True)  # 排除已标记为休眠的

    # 需要标记为非休眠的客户（有订单且最新日期晚于阈值）
    active_companies = companies.filter(
        comparison_date__gte=threshold_date
    ).exclude(is_sleep=False)  # 排除已标记为非休眠的

    sleep_count = sleep_companies.count()
    active_count = active_companies.count()

    # 批量更新
    if sleep_count > 0:
        sleep_companies.update(is_sleep=True)

    if active_count > 0:
        active_companies.update(is_sleep=False)

    return sleep_count, active_count


# 生成提成导出数据(计算销售提成main)(计算提成导出)(计算提成明细)
@transaction.atomic
def generate_royalty_derivation(order_commission_export: OrderCommissionExport):
    # order_commission_export = OrderCommissionExport.objects.get(id=1)
    # customer_order = CustomerOrder.objects.get(id=1)
    customer_order = order_commission_export.customer_order
    if not customer_order.is_revenue_lock:
        raise ParamError(f'收入确认之后才能计算提成', ErrorCode.PARAM_ERROR)

    bubble = None
    if customer_order.weight and customer_order.volume:
        bubble = customer_order.weight / customer_order.volume

    # 计算泡比优惠
    commission_bubble_discounts, revenue_bubble_discounts = calc_bubble_discounts(customer_order)
    logger.info(f'计算订单泡比优惠, 订单号: {customer_order.order_num}, '
                f'泡比优惠: 提成: {commission_bubble_discounts}, 收入: {revenue_bubble_discounts}')
    commission_bubble_discounts_price = (commission_bubble_discounts.price if commission_bubble_discounts else 0) or 0
    revenue_bubble_discounts_price = (revenue_bubble_discounts.price if revenue_bubble_discounts else 0) or 0

    # 获取计费的费用项
    product_charge = ProductCharge.objects.filter(product=customer_order.product, charge__code='YF',
                                                  del_flag=False).last()
    if not product_charge:
        raise ParamError(f'没有运费收费项', ErrorCode.PARAM_ERROR)
    charge_unit = get_charge_weight_by_order(product_charge, customer_order, raise_error=True)

    # 计算重量提成
    weight_commission_price, weight_commission = calc_weight_commission(customer_order)
    logger.info(f'订单: {customer_order.order_num}, 重量提成价格: {weight_commission_price}')

    customer_order_charge_in = CustomerOrderChargeIn.objects.filter(customer_order_num=customer_order, del_flag=False)
    freights = customer_order_charge_in.filter(
        charge__code='YF',
        charge__del_flag=False)
    sales_price = freights.aggregate(Sum('charge_total'))['charge_total__sum'] or 0
    # customer_order_sale_cost = CustomerOrderSaleCost.objects.filter(customer_order_num=customer_order,
    #                                                                 del_flag=False).last()
    # 计算成本价
    # if not customer_order_sale_cost:
    # calc_sales_cost_price(customer_order, order_commission_export.create_by)

    calc_sales_cost_price(customer_order, order_commission_export.create_by)
    customer_order_sale_cost = CustomerOrderSaleCost.objects.filter(customer_order_num=customer_order,
                                                                    del_flag=False).last()

    freight = freights.first()
    cost_price = revenue_price = 0
    
    # 成本单价逻辑：优先使用OrderCommissionExport的cost_price，如果没有则使用计算结果
    if order_commission_export.cost_price:
        cost_price = order_commission_export.cost_price
        logger.info(f'订单: {customer_order.order_num}, 使用OrderCommissionExport预设的cost_price: {cost_price}')
    else:
        # 使用销售成本计算的结果
        if order_commission_export.status in ['0', '1']:
            if customer_order_sale_cost and customer_order_sale_cost.price_version:
                cost_price = customer_order_sale_cost.charge_rate or 0
                logger.info(f'订单: {customer_order.order_num}, 使用销售成本价格版本计算的cost_price: {cost_price}')
            else:
                if freight:
                    cost_price = freight.published_price or freight.charge_price or 0
                    logger.info(f'订单: {customer_order.order_num}, 使用运费记录的cost_price: {cost_price}')
    
    # # 如果使用了预设的cost_price，需要更新销售成本记录以保持数据一致性(这个逻辑不确定是否需要)
    # if order_commission_export.cost_price and customer_order_sale_cost:
    #     customer_order_sale_cost.charge_rate = order_commission_export.cost_price
    #     customer_order_sale_cost.charge_total = order_commission_export.cost_price * charge_unit
    #     customer_order_sale_cost.account_charge = order_commission_export.cost_price * charge_unit
    #     customer_order_sale_cost.save()
    #     logger.info(f'订单: {customer_order.order_num}, 已更新销售成本记录使用预设cost_price: {order_commission_export.cost_price}')
    if freight:
        revenue_price = freight.charge_rate
    additional_charge = customer_order_charge_in.filter(
        charge__code='CPFJ', charge__del_flag=False).aggregate(Sum('charge_total'))['charge_total__sum'] or 0

    collect_order_revenue_share = collect_order_cost_share = 0
    if customer_order.collect_num_id:
        collect_order = customer_order.collect_num_id
        collect_order_charge_in = CollectOrderChargeIn.objects.filter(customer_order_num=collect_order,
                                                                      del_flag=False)
        collect_order_charge_in_price = collect_order_charge_in.aggregate(Sum('charge_total'))['charge_total__sum'] or 0
        collect_order_relate_customer_order = CustomerOrder.objects.filter(~Q(order_status='VO'),
                                                                           collect_num_id=collect_order,
                                                                           del_flag=False)
        collect_order_charge_weight = \
            collect_order_relate_customer_order.aggregate(Sum('charge_weight'))['charge_weight__sum'] or 0
        collect_order_revenue_share = \
            collect_order_charge_in_price * customer_order.charge_weight / collect_order_charge_weight

        collect_order_charge_out = CollectOrderChargeOut.objects.filter(customer_order_num=collect_order,
                                                                        del_flag=False)
        collect_order_charge_out_price = \
            collect_order_charge_out.aggregate(Sum('charge_total'))['charge_total__sum'] or 0
        collect_order_cost_share = \
            collect_order_charge_out_price * customer_order.charge_weight / collect_order_charge_weight

    debit = Debit.objects.filter(order_num=customer_order.order_num, del_flag=False).first()
    debit_status = None
    receive_time = None
    if debit:
        debit_status = dict(Debit.DEBIT_STATUS).get(debit.debit_status)
        receive_time = debit.receive_time
    debit_date = None
    if customer_order.customer:
        debit_date = dict(Company.DEBIT).get(customer_order.customer.debit_date)

    order_discounts_price = customer_order_charge_in.filter(
        charge__code='PBYH', charge__del_flag=False).aggregate(Sum('charge_total'))['charge_total__sum'] or 0
    # 销售卖价 + 产品附加费 - 成本单价 * 客户确认计费重 + 泡比优惠 * 客户确认计费重 + ifnull(揽收收入分摊, 0) - ifnull(揽收成本分摊, 0) - 赔偿金额 as 毛利,
    # 毛利 * 0.3 as 毛利提成,
    gross_margin = sales_price + additional_charge - cost_price * charge_unit + \
                   commission_bubble_discounts_price * charge_unit + collect_order_revenue_share - collect_order_cost_share
    # yqf需要 + 订单泡比优惠
    if settings.SYSTEM_ORDER_MARK == 'YQF':
        gross_margin += order_discounts_price

    deduction = order_commission_export.deduction or 0
    gross_margin_commission = gross_margin * Decimal(0.3) - deduction


    # yqf需要展示总提成
    # 毛利提成 + 重量提成 as 提成

    generate_data = {
        # 'order_num': customer_order.order_num,
        # 'saler': customer_order.saler,
        # 'customer': customer_order.customer.name if customer_order.customer else None,
        # 'product': customer_order.product.name if customer_order.product else None,
        # 'buyer_postcode': customer_order.buyer_postcode,
        # 'buyer_address_num': customer_order.buyer_address_num,
        # 'check_in_time': customer_order.check_in_time,
        # 'confirm_charge_weight': customer_order.confirm_charge_weight,
        # 'confirm_volume': customer_order.confirm_volume,
        # 'weight': customer_order.weight,
        'customer_order': customer_order,
        'bubble': bubble,
        # 泡比优惠
        'bubble_discounts_price': commission_bubble_discounts_price,
        # 重量提成
        'weight_commission_price': weight_commission_price,
        # 销售卖价
        'sales_price': sales_price,
        'collect_order_revenue_share': collect_order_revenue_share,
        'collect_order_cost_share': collect_order_cost_share,
        # 赔偿金额
        'compensation': 0,
        # 成本单价
        'cost_price': cost_price,
        # 收入单价
        'revenue_price': revenue_price,
        # 产品附加费
        'additional_charge': additional_charge,
        # 销售成本
        'sale_cost': cost_price * charge_unit,
        'gross_margin': gross_margin,
        'gross_margin_commission': gross_margin_commission,
        'debit_status': debit_status,
        'debit_date': debit_date,
        'receive_time': receive_time,
        # 'remark': customer_order.remark,
        'is_calc': True,
        'calc_result': 'Success',
        'update_date': datetime.now(),
        'gross_commission': (gross_margin_commission or 0) + (weight_commission_price or 0)
    }
    # order_commission_export = OrderCommissionExport.objects.filter(customer_order=customer_order, del_flag=False)
    # if order_commission_export.exists():
    #     order_commission_export.update(del_flag=False)
    # OrderCommissionExport.objects.create(**generate_data,
    #                                      **get_update_params_by_user(customer_order.update_by, is_create=True))
    for field in generate_data:
        setattr(order_commission_export, field, generate_data.get(field, getattr(order_commission_export, field)))
    logger.info(f'订单销售提成计算成功: {customer_order.order_num}')


# 订单导入校验产品
def order_import_verification_product_code(product_code, user):
    if not product_code:
        raise ParamError('未选择产品编码', ErrorCode.PARAM_ERROR)
    product = Product.objects.filter(code=product_code, del_flag=False).first()
    print('啊? product-->', product)
    if product is None:
        raise ParamError(f'产品编码『{product_code}』系统找不到此产品', ErrorCode.PARAM_ERROR)
    if product.status == 'OFF':
        raise ParamError(f'此产品已被停用, 产品: {product}', ErrorCode.PARAM_ERROR)
    if not user.is_staff and not product.is_open:
        raise ParamError(f'此产品没有对外开放, 产品: {product}', ErrorCode.PARAM_ERROR)
    return product


# 全部入仓时判断是否有销售价卡
def judge_has_sales_price(customer_order):
    # 对订单的产品销售定价和收入价格版本开始时间进行判断, 确定销售价卡
    product_sales_price_queryset = ProductSalesPrice.objects.filter(product=customer_order.product,
                                                                    del_flag=False)
    product_sales_price_customers = ProductSalesPriceCustomer.objects.filter(
        customer=customer_order.customer, product_sales_price__in=product_sales_price_queryset, del_flag=False)
    latest_product_sales_price_customer = product_sales_price_customers.order_by(
        '-product_sales_price__start_time').first()
    product_revenue_version = ProductRevenueVersion.objects.filter(
        product=customer_order.product, del_flag=False,
        price_type='A').order_by('-start_time').first()
    # print('what-->', latest_product_sales_price_customer, product_revenue_version)
    if latest_product_sales_price_customer and product_revenue_version:
        latest_product_sales_price = latest_product_sales_price_customer.product_sales_price
        price_card_judge_sign = False
        # 产品销售定价的开始时间 > 收入价格版本的开始时间, 则设定价卡时间为: 产品销售定价的开始时间
        if latest_product_sales_price.start_time and product_revenue_version.start_time:
            if latest_product_sales_price.start_time > product_revenue_version.start_time:
                customer_order.save_fields(sales_price_card_time=latest_product_sales_price.start_time)
                price_card_judge_sign = True
        logger.info(
            f'订单{customer_order.order_num}判断销售价卡, 销售定价版本: '
            f'{latest_product_sales_price_customer.product_sales_price}, 收入价格版本: {product_revenue_version}, '
            f'判断结果: {price_card_judge_sign}')
    else:
        logger.info(
            f'订单{customer_order.order_num}判断销售价卡, 没有产品销售定价归属客户 或 没有收入价格版本')


def validate_weighing_and_packing(parcel_order):
    """
    已经获取了面单，15天后，对称重、装箱、换标操作做校验
    :param parcel_order: 小包
    :return:
    """
    # 判断是否获取面单已经超过15天，如果超过15天则报错
    # 获取小包的产品是否配置了校验属性
    basic_restriction_obj = parcel_order.product.product_basic_restriction.filter(type='C', belong_type='A',
                                                                                  insert_trick=1,
                                                                                  encoding='validate_weighing_and_packing')
    if basic_restriction_obj.exists():
        if parcel_order.parcelOrderLabels.filter(del_flag=False).exclude(tracking_no=parcel_order.order_num).exists():
            to_day = datetime.now().date()
            parcel_order_obj = parcel_order.parcelOrderLabels.filter(del_flag=False).exclude(tracking_no=parcel_order.order_num).order_by(
                'create_date').first()  # 获取最早的时间（一般情况下只会有一个面单）
            create_date = parcel_order_obj.create_date.date()
            diff_days = (to_day - create_date).days
            if diff_days > 15:
                return Response(data={'msg': '获取面单已经超过15天，禁止操作！', 'code': 400}, status=status.HTTP_200_OK)


def update_order_data_for_cancel_force(request, ids, user_profile=None):
    user = user_profile or request.user
    # fba取消订单逻辑封装 [强制作废订单]
    queryset = CustomerOrder.objects.filter(~Q(order_status='VO'), id__in=ids,
                                            is_revenue_lock=False,
                                            is_cost_lock=False)

    if queryset.count() != len(ids):
        raise ParamError('请选择未作废, 未收入/成本确认的订单操作！', ErrorCode.PARAM_ERROR)
    else:
        # 更改状态、取消与主单和分单的外键关联
        for item in queryset:
            change_order_status(item, 'VO', user)
            item.master_num = None
            item.house_num = None
            item.update_by = get_update_params(request)['update_by']
            item.update_date = get_update_params(request)['update_date']
            item.save()

            # 将订单下的所有包裹的del_flag置为true, (包裹号全局唯一, 但作废的订单下的包裹号不计算在内, 所以要置1)
            Parcel.objects.filter(customer_order=item, del_flag=False).update(remark='cancel_order', del_flag=True)


def update_order_data_for_recovery_order(request, ids, user_profile=None):
    # 恢复订单 [取消作废]
    user = user_profile or request.user
    queryset = CustomerOrder.objects.filter(id__in=ids, order_status='VO')
    if queryset.count() != len(ids):
        raise ParamError(f'请选择作废状态的客户订单！', ErrorCode.PARAM_ERROR)
    else:
        # 更改状态、取消与主单和分单的外键关联
        for item in queryset:
            same_customer_ref_num = CustomerOrder.objects.filter(Q(ref_num=item.ref_num) &
                                                                 ~Q(order_status='VO') &
                                                                 Q(del_flag=False))
            if same_customer_ref_num.count() > 0:
                raise ParamError(f'已存在客户订单号为『{item.ref_num}』的订单, 无法恢复！', ErrorCode.PARAM_ERROR)
            same_customer_parcels = item.parcel.filter(del_flag=False).values("parcel_num")
            for parcel in same_customer_parcels:
                same_parcel = Parcel.objects.filter(~Q(customer_order__order_status='VO'),
                                                    customer_order__customer=item.customer,
                                                    parcel_num=parcel['parcel_num'], del_flag=False)
                if same_parcel.count() > 0:
                    raise ParamError(f'客户『{item.customer}』, 已存在包裹号『{parcel["parcel_num"]}』, 无法恢复！',
                                     ErrorCode.PARAM_ERROR)
            # item.order_status = 'WO'
            change_order_status(item, 'WO', user)
            # item.master_num = None
            # item.house_num = None
            item.update_by = user
            item.update_date = datetime.now()
            item.save()

            # 将对应轨迹系统的订单取消轨迹删除
            # Track.objects.filter(track_code='CL').update(del_flag=True)
            # 将订单下的所有cancel_order的包裹的del_flag置为0
            parcels = Parcel.objects.filter(customer_order=item, remark='cancel_order', del_flag=True)
            for parcel in parcels:
                if Parcel.objects.filter(~Q(remark='cancel_order'), parcel_num=parcel.parcel_num,
                                         del_flag=False).count() > 0:
                    raise ParamError(f'已存在 {parcel.parcel_num} 包裹, 无法恢复订单', ErrorCode.PARAM_ERROR)
            parcels.update(del_flag=False, remark=None, **get_update_params(request))


def update_order_data_for_customer_order_intercept(request, ids, user_profile=None):
    # 拦截订单
    user = user_profile or request.user
    queryset = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
    for item in queryset:
        if item.is_intercept:
            raise ParamError(f'订单『{item.order_num}』是拦截状态, 不需要拦截', ErrorCode.PARAM_ERROR)
        set_customer_track_fba(item, 'ITP', user)
        # if err == 1:
        #     return fail_response(request, msg)
        msg, err = change_order_status(item, 'ITP', user)
        if err == 1:
            raise ParamError(msg, ErrorCode.PARAM_ERROR)


def update_order_data_for_cancel_customer_order_intercept(request, ids, user_profile=None):
    # 取消拦截订单
    user = user_profile or request.user
    queryset = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
    for item in queryset:
        if not item.is_intercept:
            raise ParamError(f'订单『{item.order_num}』不是拦截状态, 不需要取消拦截', ErrorCode.PARAM_ERROR)
        set_customer_track_fba(item, item.order_status, user)
        # if err == 1:
        #     return fail_response(request, msg)
        msg, err = change_order_status(item, 'ITP', user)
        if err == 1:
            raise ParamError(msg, ErrorCode.PARAM_ERROR)


# 将有逗号的多个文件名正确的分割成文件名列表
def split_filenames(filename_str):
    if not filename_str:
        return []
    filenames = []
    current_filename = ""
    parts = filename_str.split(',')

    for part in parts:
        if current_filename:
            # 尝试拼接当前部分到现有的文件名
            temp_filename = current_filename + ',' + part
            if '.' in temp_filename and temp_filename.rsplit('.', 1)[1]:
                # 如果拼接后的文件名有有效的后缀，将其作为一个完整的文件名
                filenames.append(temp_filename)
                current_filename = ""
            else:
                # 否则，继续拼接
                current_filename = temp_filename
        else:
            if '.' in part and part.rsplit('.', 1)[1]:
                # 如果当前部分有有效的后缀，将其作为一个完整的文件名
                filenames.append(part)
            else:
                # 否则，开始构建新的文件名
                current_filename = part

    # 处理最后一个可能未完成的文件名
    if current_filename:
        filenames.append(current_filename)

    """
    # 测试示例
    filename_str1 = "aaa.txt,bbb.zip"
    filename_str2 = "test,cmd.xlsx,ccc.txt"

    print(split_filenames(filename_str1))
    print(split_filenames(filename_str2))
    
    # 结果
    ['aaa.txt', 'bbb.zip']
    ['test,cmd.xlsx', 'ccc.txt']
    """
    return filenames


def change_label_common_func(service, product, customer_order, order_label, order_num, order_label_changed, order_label_task, log_flag=''):
    """
    更改面单公共方法
    :param service: Service  对象
    :param product: Product 对象
    :param customer_order: ParcelCustomerOrder  对象
    :param order_label: 面单表对象
    :param order_label_changed: 更改后的面单表对象
    :param order_label_task: 面单任务表对象
    :param order_num: 单号
    :log_flag: 日志标识
    :return:
    """
    # 更改面单逻辑
    # todo 待验证测试
    service_code = service.code
    product_code = product.code
    warehouse_code = customer_order.warehouse_code.address_num
    logger.info(f'{log_flag}--service_code：{service_code}')
    logger.info(f'{log_flag}--product_code：{product_code}')
    logger.info(f'{log_flag}--warehouse_code：{warehouse_code}')
    label_change_config = LabelChangeConfig.objects.filter(service_code=service_code.strip(),
                                                           product_code=product_code.strip(),
                                                           warehouse_code=warehouse_code.strip(),
                                                           is_enable=True,
                                                           del_flag=False)
    if not label_change_config.exists():
        return False

    label_change_config_detail = label_change_config.first().label_config_detail.all()
    if not label_change_config_detail.exists():
        return False

    change_rule_li = [{'x_coordinate': detail.x_coordinate, 'y_coordinate': detail.y_coordinate,
                       'content_height': detail.content_height, 'content_width': detail.content_width,
                       'content': detail.content.replace('\\n', '\n'), 'font_size': detail.font_size} for detail
                      in
                      label_change_config_detail]
    old_label_url = order_label.label_url
    change_path_li = old_label_url.split('/')[:-1]
    change_path_li.append(order_num + '_A' + '.pdf')
    changed_label = '/'.join(change_path_li)
    logger.info(f'{log_flag}--预览更改面单更改后的面单label路径：{changed_label}')
    file_path = settings.STATIC_MEDIA_DIR
    old_file_name = file_path + old_label_url
    changed_file_path = file_path + changed_label
    logger.info(f'{log_flag}--预览更改面单更改后的面单文件路径：{changed_file_path}')

    try:
        overwrite_pdf_with_fitz(old_file_name, changed_file_path, change_rule_li)
        order_label.del_flag = True
        order_label.save()
        order_label_changed.order_num = customer_order
        order_label_changed.tracking_no = order_label.tracking_no
        # order_label_changed.third_order_no = label_info['sk_no']  # 暂不传此值
        order_label_changed.label_url = changed_label
        order_label_changed.create_by = order_label_task.create_by
        order_label_changed.create_date = datetime.now()
        order_label_changed.product = order_label_task.product
        order_label_changed.is_change_label = True
        order_label_changed.save()
    except Exception as e:
        logger.info(f'{log_flag}--预览更改面单更改后的面单失败：{e}')
        logger.info(traceback.format_exc())
        raise


def judge_is_vender_no_for_tracking_num(order_label, parcel_customer_order):
    """
    判断产品中的扩展属性 是否有 is_vender_no 并进行相应的逻辑操作：
    详情见代码注释和逻辑
    :param order_label: 小包面单对象
    :param parcel_customer_order: 小包单对象
    :return: tracking_num
    """
    # 业务背景：以京东（美国站）为例，返回的跟踪单号是京东的（即供应商单号），
    # 并不是真正的尾程单号。此处根据扩展属性来决定返回不返回给客户的京东的单号，如果不满足则返回 zhihe单号
    # 直到获取到真正的尾程单号并将其返回
    is_vender_no = False
    extend_res = get_attribute_extend_limitation_default_value(order_label.product, 'is_vender_no')

    # 如果没有is_vender_no 扩展属性，那么保持原有逻辑 取order_label.tracking_no跟踪号
    if not extend_res:
        tracking_num = order_label.tracking_no
    else:
        # 如果扩展属性is_vender_no存在 并且 小包单号跟踪号不为空，并且不以 ZHPHD开头, 则就是真正的跟踪单号
        tmp_tracking_num = parcel_customer_order.tracking_num
        if tmp_tracking_num and not tmp_tracking_num.startswith('ZHPHD'):
            tracking_num = tmp_tracking_num
        else:  # 否则就返回zhihe的面单
            tracking_num = parcel_customer_order.order_num
        is_vender_no = True
    return tracking_num, is_vender_no


# 根据发货仓库获取起点分区: 通过发货仓库邮编查产品分区邮编, 获取产品分区
def get_start_zone_by_delivery_warehouse(shipper: Address):
    if not shipper:
        return
    product_zone_post_code = ProductZonePostCode.objects.filter(
        # 这里不规范, ProductZonePostCode 数据源表没有 country_code 数据
        # country_code=shipper.country_code,
        post_code=shipper.postcode,
        product_zone__type='Seller',
        del_flag=False
    ).last()

    if product_zone_post_code:
        product_zone = product_zone_post_code.product_zone
        return product_zone
    else:
        return


# def generate_zx_label_for_api_label(parcel_customer_order, label_size):
#     """
#     智禾相关
#     :param parcel_customer_order:
#     :param label_size:
#     :return:
#     """
#     from common.service.parcel_order import assemble_barcode_params
#     file_url_str = ''
#     base64_str = ''
#     product = Product.objects.filter(code='ZX_LABEL', del_flag=False).first()
#     order_label_task_list = ParcelOrderLabelTask.objects.filter(product=product,
#                                                                 order_num=parcel_customer_order,
#                                                                 del_flag=False).order_by('-id')
#     if order_label_task_list.exists():
#         order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_customer_order,
#                                                            product=product,
#                                                            del_flag=False)
#         if len(order_label_list) == 0:
#             return file_url_str, base64_str
#
#         label_path = settings.STATIC_MEDIA_DIR
#         file_url = 'http://' + settings.DOMAIN_URL + '/media/'
#         order_label = order_label_list[0]  # 智禾只有一个单号
#         file_url_str = file_url + order_label.label_url
#         label_path_str = label_path + order_label.label_url
#
#         with open(label_path_str, 'rb') as f:
#             base64_data = base64.b64encode(f.read())
#             base64_str = base64_data.decode()
#         return file_url_str, base64_str
#     else:
#         # 兼容旧的无面单任务的
#         label_list = []
#         orders = []
#         orders.append(assemble_barcode_params(parcel_customer_order))
#         base64_str = create_barcodes_for_order(orders, label_size)
#         return file_url_str, base64_str
