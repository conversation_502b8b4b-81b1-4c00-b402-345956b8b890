import os
import base64
import hashlib
import json
import os.path
import time
from datetime import datetime, timedelta

import django
import requests

profile = os.environ.get('PROJECT_SETTINGS', 'test5')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'alita.settings.%s' % profile)
django.setup()

from django.conf import settings
from django.core.files.storage import FileSystemStorage

from info.models import Dict
from order.models import DmasMsgSendTask
from common.utils.logger_util import alita_logger_info
from alita.logger import logger

from django.core.files.base import ContentFile


def save_image_file(img_base64):
    # 确保 base64 数据是字符串格式
    if isinstance(img_base64, bytes):
        img_base64 = img_base64.decode('utf-8')

    # 分离 base64 头部和实际数据（如果有）
    if ',' in img_base64:
        header, img_base64 = img_base64.split(',', 1)

    # 解码 base64 数据
    img_data = base64.b64decode(img_base64)

    # 创建文件内容对象
    file = ContentFile(img_data)

    # 创建保存路径
    path = os.path.join(settings.STATIC_MEDIA_DIR, 'dmasFile')
    # path = os.path.join(r'E:\1_sound_code\Alita\static\alita\media\\', 'dmasFile')
    # path = os.path.join(r'/Users/<USER>/Desktop/chen/1_sound_code/Alita/static/alita/media', 'dmasFile')
    os.makedirs(path, exist_ok=True)  # 确保目录存在

    # 自定义保存路径（按日期组织）
    date_path = datetime.now().strftime("%Y/%m/%d/")
    file_url = os.path.join(path, date_path)
    os.makedirs(file_url, exist_ok=True)  # 创建日期目录

    # 生成文件名（可以根据需要修改）
    filename = f"image_{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
    # filename = 'image_20250526185038.png'

    # 使用 FileSystemStorage 保存文件
    fs = FileSystemStorage(location=file_url)
    saved_filename = fs.save(filename, file)

    uploaded_file_url = os.path.join(file_url, saved_filename)
    logger.info(f'uploaded_file_url-->{uploaded_file_url}')

    # full_file_url = 'http://' + settings.DOMAIN_URL + '/media/dmasFile/' + date_path + filename
    full_file_url = f'http://{settings.DOMAIN_URL}/media/dmasFile/{date_path}{filename}'
    logger.info(f'full_file_url-->{full_file_url}')

    return full_file_url


def send_dmas_message(msg, message_group=None, send_type='sync', at_list=None):
    """
    # 注意: 只能向企业微信群里发送消息
    :param msg:
    :param message_group: 普信中流砥柱/铭志每日财务报表群/dmas测试群
    :param send_type: async是异步, 走的AI, sync是同步, 不走AI
    :param at_list: @群成员
    """
    if message_group is None:
        message_group = ["dmas测试群"]
    if at_list is None:
        at_list = []
    base_url = 'http://dmas.puxinc.com/'
    robot_id = 'dmas_local_00001'
    url = f'{base_url}api/v2/wecom/sendMessage?robotId={robot_id}&sendType={send_type}'
    params = {
        "list": [
            {
                "groupNameList": message_group,
                "atList": at_list,
                "receivedContent": msg
            }
        ]
    }
    result = request_server(params, url, {'Content-Type': 'application/json'})

    if not result:
        error_msg = f'发送dmas消息异常: 没有任何返回'
        logger.info(error_msg)
        return 1, error_msg

    if result.get('code') == 200:
        logger.info(f'发送dmas消息成功, 消息: {msg}')
        return 0, None
    else:
        error_msg = result.get('msg')
        logger.info(f'发送dmas消息失败: {error_msg}')
        return 1, error_msg


# 暂时不能用
def get_dmas_token():
    # url = 'http://dmas.puxinc.com/aWhWOpCbKj2gqmIG/authtoken/tokenproxy/'
    url = 'http://dmas.puxinc.com/api/v2/auth-token/'
    result = request_server({'username': 'admin', 'password': 'px2025888@'}, url, {'Content-Type': 'application/json'})
    if not result:
        logger.info(f'发送dmas消息异常: 没有任何返回')
        return
    # print('result------------->', result)
    logger.info(f'获取到dmas token: {result.get("token")}')
    return result.get('token')


def send_dmas_msg_by_customer(msg, customer_code=None, business_type='mz', at_list=None, token=None):
    """
    :param msg:
    :param customer_code: 客户编码
    :param business_type: 业务类型, 默认mz
    :param at_list:
    :param token:
    """
    # http://dmas.puxinc.com/api/v2/business/cs/send_message_by_customer
    if at_list is None:
        at_list = []
    if token is None:
        token = get_dmas_token()
    base_url = 'http://dmas.puxinc.com/'
    url = f'{base_url}api/v2/business/cs/send_message_by_customer'
    params = {
        "detail_type": "COMMON",
        # "atList": at_list,
        "customer_code": customer_code,
        "business_type": business_type,
        "content": msg
    }
    result = request_server(params, url, {'Content-Type': 'application/json', 'Authorization': f'Token {token}'})

    if not result:
        error_msg = f'发送dmas消息异常: 没有任何返回'
        logger.info(error_msg)
        return 1, error_msg

    if result.get('code') == 200:
        logger.info(f'发送dmas消息成功, 消息: {msg}')
        return 0, None
    else:
        error_msg = result.get('msg')
        logger.info(f'发送dmas消息失败: {error_msg}')
        return 1, error_msg


def send_dmas_base64_image(img_base64=None, file_url=None, send_msg='推送消息', message_group=None,
                           at_list=None, at_msg=''):
    """
    :param img_base64: img_base64 和 file_url 二选一
    :param file_url:
    :param send_msg:
    :param at_list:
    :param at_msg:
    :param message_group: 普信中流砥柱/铭志每日财务报表群/dmas测试群
    """
    if message_group is None:
        message_group = ["dmas测试群"]
    if at_list is None:
        at_list = []
    if settings.SYSTEM_ORDER_MARK == 'MZ':
        time.sleep(20)

    # base64_to_file(img_base64, send_msg)
    # return
    base_url = 'http://dmas.puxinc.com/'
    robot_id = 'dmas_local_00001'
    url = f'{base_url}api/v2/digital/rpa/sendNews?robotId={robot_id}'
    params = {
        "groupNameList": message_group,
        # 发送图片目前不支持@成员, 所以需要单独发送消息去@
        # "atList": at_list,
        "fileType": "image",
        # "extraText": send_msg
    }

    if send_msg:
        params['extraText'] = send_msg

    if img_base64:
        print_log = False
        binary_data = base64.b64decode(img_base64)
        # 计算哈希值（以 SHA-256 为例）
        hash_object = hashlib.sha256(binary_data)
        hash_value = hash_object.hexdigest()
        params.update({
            "objectName": hash_value,
            "fileBase64": img_base64,
        })
    else:
        print_log = True
        filename = os.path.basename(file_url)
        name_without_ext = os.path.splitext(filename)[0]
        params.update({
            "objectName": name_without_ext,
            "file_url": file_url,
        })

    result = request_server(params, url, {'Content-Type': 'application/json'}, print_log=print_log)

    if not result:
        error_msg = f'发送dmas图片异常: 没有任何返回'
        logger.info(error_msg)
        return 1, error_msg

    if result.get('code') == 200:
        logger.info(f'发送dmas图片成功')
    else:
        error_msg = result.get('msg')
        logger.info(f'发送dmas图片失败: {error_msg}')
        return 1, error_msg

    if at_list:
        code, error_msg = send_dmas_message(at_msg, message_group=message_group, at_list=at_list)
        return code, error_msg


def send_dmas_base64_txt(txt_base64, send_msg='推送消息', message_group=None):
    """
    :param txt_base64:
    :param send_msg:
    :param message_group: 普信中流砥柱/铭志每日财务报表群/dmas测试群
    """
    base_url = 'http://dmas.puxinc.com/'
    robot_id = 'dmas_local_00001'
    url = f'{base_url}api/v2/digital/rpa/sendNews?robotId={robot_id}'
    if message_group is None:
        message_group = ["dmas测试群"]
    if settings.SYSTEM_ORDER_MARK == 'MZ':
        time.sleep(20)
    binary_data = base64.b64decode(txt_base64)
    # 计算哈希值（以 SHA-256 为例）
    hash_object = hashlib.sha256(binary_data)
    hash_value = hash_object.hexdigest()
    params = {
        "groupNameList": message_group,
        "objectName": hash_value,
        "fileBase64": txt_base64,
        "fileType": "text",
        "extraText": send_msg
    }
    result = request_server(params, url, {'Content-Type': 'application/json'}, print_log=False)

    if not result:
        error_msg = f'发送dmas文档异常: 没有任何返回'
        logger.info(error_msg)
        return 1, error_msg

    if result.get('code') == 200:
        logger.info(f'发送dmas文档成功')
        return 0, None
    else:
        error_msg = result.get('msg')
        logger.info(f'发送dmas文档失败: {error_msg}')
        return 1, error_msg


def request_server(post_data, url, headers, order_num=None, print_log=True):
    """
    请求
    :param post_data:
    :param url:
    :param headers:
    :return:
    """
    print(json.dumps(post_data))
    print(url)
    operate_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    if print_log:
        alita_logger_info(f'dmas util post请求url={url} 入参: ', str(post_data), operate_time,
                          order_num or post_data.get('order_num', None))
    r = requests.post(url, json=post_data, headers=headers)
    if print_log:
        alita_logger_info(f'dmas util post请求url={url} 出参: ', r.text, operate_time,
                          order_num or post_data.get('order_num', None))
    print('requests.post-->', r, r.text)
    return r.json()


def get_next_17_oclock():
    # 获取当前日期和时间
    now = datetime.now()
    # 创建今天 17:00:00 的时间对象
    today_17 = now.replace(hour=17, minute=0, second=0, microsecond=0)

    if now < now.replace(hour=16, minute=30, second=0, microsecond=0):
        # 如果当前时间在今天 17 点之前，返回今天 17 点
        return today_17
    else:
        # 如果当前时间在今天 17 点之后，返回明天 17 点
        tomorrow = now + timedelta(days=1)
        tomorrow_17 = tomorrow.replace(hour=17, minute=0, second=0, microsecond=0)
        return tomorrow_17


# 创建dmas消息推送异步任务
def common_dmas_async_task(order_id, message, business_type, push_type, customer_code=None, user=None, start_time=None):
    logger.info(f'创建dmas消息推送异步任务, 单据id: {order_id}, 业务类型: {business_type}, 推送类型: {push_type}, '
                f'客户编码: {customer_code}, 消息内容: {message}')
    exists_tasks = DmasMsgSendTask.objects.filter(order_id=order_id,
                                                  business_type=business_type,
                                                  push_type=push_type,
                                                  message=message,
                                                  del_flag=False)
    # 如果任务存在则改为未执行, 需要重新执行
    if exists_tasks:
        exists_tasks.update(status='Waiting', update_by=user)
    else:
        task_params = {
            'order_id': order_id,
            'business_type': business_type,
            'push_type': push_type,
            'customer_code': customer_code,
            'message': message,
            'status': 'Waiting',
            'start_time': start_time,
        }
        dmas_task = DmasMsgSendTask.objects.create(**task_params, create_by=user)
    logger.info(f'创建dmas消息推送异步任务完成, 单据id: {order_id}, 业务类型: {business_type}, 推送类型: {push_type}, '
                f'客户编码: {customer_code}, 消息内容: {message}')


# dmas推送fba订单轨迹
def send_fba_track_dmas(customer_order, track_code, actual_time, track_remark, user=None):
    # 是否开启dmas推送订单轨迹
    is_notification = Dict.objects.filter(label='FbaDmasNotification', value='1', del_flag=False)
    logger.info(f'send_fba_track_dmas start: {is_notification.exists()}')
    if not is_notification.exists():
        return
    if not customer_order.customer:
        return
    order_num = customer_order.ref_num
    customer_code = customer_order.customer.short_name
    if track_code in ['ARR', 'CC', 'ADS', 'VER', 'REL', 'PL', 'ASP', 'ADL',
                      'WT', 'OT', 'NC', 'TCIS', 'CAR', 'RTC', 'TA', 'SF']:
        logger.info(f'send_fba_track_dmas开始发送轨迹, 订单: {order_num}, 客户: {customer_code}, 轨迹代码: {track_code}, '
                    f'轨迹描述: {track_remark}')
        # msg = f"订单「{order_num}」最新轨迹：{actual_time}（{track_remark.replace('None', '')}）"
        msg = f"订单 {order_num} 最新轨迹：{actual_time}（{track_remark.replace('None', '')}）"
        # send_dmas_message(msg, message_group=[f'{customer_code}_客户通知群'], at_list=None, customer_code=customer_code)
        is_open_dmas_track = Dict.objects.filter(label='OpenDmasAsyncTask', value='1', del_flag=False)
        if is_open_dmas_track.exists():
            common_dmas_async_task(customer_order.id, msg, 'mz', 'customer_code', customer_code=customer_code,
                                   user=user, start_time=get_next_17_oclock())
        else:
            send_dmas_msg_by_customer(msg, customer_code=customer_code)
    else:
        logger.info(f'send_fba_track_dmas未发送轨迹, 订单: {order_num}, 轨迹代码: {track_code}, 轨迹描述: {track_remark}')


if __name__ == '__main__':
    # send_dmas_message('测试磁盘空间消息: ZH磁盘空间70%', ['普信IT支持群'], 'sync')
    # send_dmas_msg_by_customer('这是一条客户测试消息', customer_code='0600429')
    send_dmas_base64_image(file_url='http://cshm.mz56.com/media/dmasFile/2025/07/15/image_20250715101055.png',
                           # send_msg=f'美国海运时效报表 {start_day.strftime("%m%d")}-{end_day.strftime("%m%d")}',
                           send_msg=f'',
                           message_group=['普信IT支持群'], at_list=['王子斌Eli', '玄冬'],
                           at_msg=f'美国海运时效报表', )
    # print(settings.STATIC_MEDIA_DIR)
