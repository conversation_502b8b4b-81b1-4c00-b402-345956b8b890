import base64
import io
import os
import platform
import time
from datetime import datetime, timedelta
from decimal import Decimal
from django.utils import timezone

import django
import numpy as np
import openpyxl
from PIL import Image
from django.conf import settings
from django.db.models import Q, Sum
from matplotlib import pyplot as plt, patheffects
from scipy.interpolate import make_interp_spline

from account.models import Account
from oms.models import ReturnOrder
from order.utils.ocean_order import judge_zone_affiliation
from pms.models import Product, ProductZone

profile = os.environ.get('PROJECT_SETTINGS', 'test')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'alita.settings.%s' % profile)
django.setup()


from alita.logger import logger
from common.utils.dmas_util import send_dmas_message, send_dmas_base64_image
from order.models import CustomerOrder, Track, CustomerOrderRelateOcean, OceanOrderTrack, CustomerOrderChargeIn, \
    CustomerOrderChargeOut, TruckOrder, TruckOrderTrack


def write_a_column_excel(column_data, column_title, excel_title):
    workbook = openpyxl.Workbook()
    # 选择默认的工作表 (通常是第一个工作表)
    sheet = workbook.active
    # 添加标题
    sheet['A1'] = column_title
    # 将订单号列表写入工作表
    for index, data in enumerate(column_data):
        # 从第二行开始写入订单号 (index + 2)
        sheet.cell(row=index + 2, column=1, value=data)
    # 保存工作簿
    workbook.save(f'{excel_title}.xlsx')


# 统计fba订单未收入确认和未成本确认
def fba_order_not_revenue_confirm(write_excel=False):
    task_start_time = datetime.now().date() + timedelta(days=7)

    # 订单全部入仓7天后未收入确认
    in_warehouse_before = ['DR', 'WO', 'PDC', 'ITP', 'PW', 'VO']
    customer_orders = CustomerOrder.objects.filter(~Q(order_status__in=in_warehouse_before), is_revenue_lock=False,
                                                   del_flag=False)
    tracks = Track.objects.filter(track_code='AW', order_id__in=customer_orders.values_list('id', flat=True),
                                  create_date__lt=task_start_time, del_flag=False)
    not_revenue_orders = CustomerOrder.objects.filter(id__in=tracks.values_list('order_id', flat=True), del_flag=False)
    not_revenue_orders_list = not_revenue_orders.values_list('order_num', flat=True)
    not_revenue_orders_msg = '\n'.join(not_revenue_orders_list[:20])
    suffix = '\n......' if not_revenue_orders.count() > 20 else ''
    res_message = f'订单全部入仓7天后未收入确认（{not_revenue_orders.count()}条）: \n{not_revenue_orders_msg}{suffix}'
    logger.info(res_message)
    send_dmas_message(res_message, message_group=['铭志每日财务报表群'])
    if write_excel:
        write_a_column_excel(not_revenue_orders_list, '订单号', '未收入确认订单号')

    # 订单已离港7天后未成本确认
    depart_from_port_before = ['DR', 'WO', 'PDC', 'ITP', 'PW', 'VO', 'AW', 'CWED', 'OW']
    customer_orders = CustomerOrder.objects.filter(~Q(order_status__in=depart_from_port_before), is_cost_lock=False,
                                                   del_flag=False)
    ocean_orders = CustomerOrderRelateOcean.objects.filter(
        customer_order_num__in=customer_orders.values_list('id', flat=True), del_flag=False)
    ocean_order_tracks = OceanOrderTrack.objects.filter(
        track_code='DEP',
        ocean_order_num_id__in=ocean_orders.values_list('oceanOrder_id', flat=True),
        create_date__lt=task_start_time, del_flag=False)
    not_cost_relate_orders = CustomerOrderRelateOcean.objects.filter(
        oceanOrder__in=ocean_order_tracks.values_list('ocean_order_num_id', flat=True), del_flag=False)
    not_cost_orders = CustomerOrder.objects.filter(
        id__in=not_cost_relate_orders.values_list('customer_order_num_id', flat=True), del_flag=False)
    not_cost_orders_list = not_cost_orders.values_list('order_num', flat=True)
    not_cost_orders_msg = '\n'.join(not_cost_orders_list[:20])
    suffix = '\n......' if not_cost_orders.count() > 20 else ''
    res_message = f'订单已离港7天后未成本确认（{not_cost_orders.count()}条）: \n{not_cost_orders_msg}{suffix}'
    logger.info(res_message)
    send_dmas_message(res_message, message_group=['铭志每日财务报表群'])
    if write_excel:
        write_a_column_excel(not_cost_orders_list, '订单号', '未成本确认订单号')

    # 海运单已离港7天后未成本分摊
    ocean_order_tracks = OceanOrderTrack.objects.filter(
        track_code='DEP',
        create_date__lt=task_start_time,
        ocean_order_num__is_share_cost=False,
        del_flag=False)
    not_cost_share_ocean_orders = ocean_order_tracks.values_list('ocean_order_num__order_num', flat=True)
    not_cost_share_ocean_orders_msg = '\n'.join(not_cost_share_ocean_orders[:20])
    suffix = '\n......' if ocean_order_tracks.count() > 20 else ''
    res_message = f'海运单已离港7天后未成本分摊（{ocean_order_tracks.count()}条）: ' \
                  f'\n{not_cost_share_ocean_orders_msg}{suffix}'
    logger.info(res_message)
    send_dmas_message(res_message, message_group=['铭志每日财务报表群'])
    if write_excel:
        write_a_column_excel(not_cost_share_ocean_orders, '海运单号', '未成本分摊海运单号')


# 统计fba订单的收入和成本
def statistics_revenue_and_cost(start_day, end_day, order_type='FBA'):
    daily_costs = {}
    daily_revenues = {}

    for i in range((end_day - start_day).days):
        day = start_day + timedelta(days=i)
        start_of_a_day = day.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_a_day = day.replace(hour=23, minute=59, second=59, microsecond=999999)

        customer_orders = CustomerOrder.objects.filter(
            create_date__gte=start_of_a_day,
            create_date__lte=end_of_a_day,
            order_type=order_type,
            del_flag=False
        )
        cost_detail = customer_orders.aggregate(income_total=Sum('income'), cost_total=Sum('cost'))
        daily_costs[day.strftime('%Y-%m-%d')] = cost_detail['cost_total'] or 0
        daily_revenues[day.strftime('%Y-%m-%d')] = cost_detail['income_total'] or 0
        # daily_costs[day.strftime('%Y-%m-%d')] = (i + 1) / 2
        # daily_revenues[day.strftime('%Y-%m-%d')] = (i + 1)
    logger.info(f'settle_statistics_dmas: {daily_revenues}, {daily_costs}')
    img_base64 = draw_histogram_chart_double(daily_revenues, daily_costs, '确认收入', '确认成本',
                                             '日期', '金额/元', '确认收入与确认成本统计图', separate=False)
    send_dmas_base64_image(img_base64, send_msg='订单的确认收入和支出', message_group=['铭志每日财务报表群'])


# 统计fba订单收入计费和成本计费
def statistics_charge_in_and_out(start_day, end_day, order_type='FBA'):
    daily_costs = {}
    daily_revenues = {}

    for i in range((end_day - start_day).days):
        day = start_day + timedelta(days=i)
        start_of_a_day = day.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_a_day = day.replace(hour=23, minute=59, second=59, microsecond=999999)

        charge_outs = CustomerOrderChargeOut.objects.filter(
            customer_order_num__create_date__gte=start_of_a_day,
            customer_order_num__create_date__lte=end_of_a_day,
            customer_order_num__order_type=order_type,
            customer_order_num__del_flag=False,
            del_flag=False
        )
        charge_ins = CustomerOrderChargeIn.objects.filter(
            customer_order_num__create_date__gte=start_of_a_day,
            customer_order_num__create_date__lte=end_of_a_day,
            customer_order_num__order_type=order_type,
            customer_order_num__del_flag=False,
            del_flag=False
        )

        daily_costs[day.strftime('%Y-%m-%d')] = charge_outs.aggregate(charge_total=Sum('charge_total'))[
                                                    'charge_total'] or 0
        daily_revenues[day.strftime('%Y-%m-%d')] = charge_ins.aggregate(charge_total=Sum('charge_total'))[
                                                       'charge_total'] or 0
        # daily_costs[day.strftime('%Y-%m-%d')] = (i + 1) / 2
        # daily_revenues[day.strftime('%Y-%m-%d')] = (i + 1)
    logger.info(f'settle_statistics_dmas: {daily_revenues}, {daily_costs}')
    img_base64 = draw_histogram_chart_double(daily_revenues, daily_costs, '计费收入', '计费成本',
                                             '日期', '金额/元', '计费收入与计费成本统计图', separate=False)
    send_dmas_base64_image(img_base64, send_msg='订单的计费收入和支出', message_group=['铭志每日财务报表群'])


# 统计fba订单的下单量
def place_order_count(start_day, end_day, order_type='FBA'):
    print('start_day, end_day-->', start_day, end_day)
    daily_customer_orders = {}
    # daily_parcel_customer_orders = {}
    daily_customer_orders_volume = {}

    for i in range((end_day - start_day).days):
        day = start_day + timedelta(days=i)
        start_of_day = day.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = day.replace(hour=23, minute=59, second=59, microsecond=999999)

        customer_orders = CustomerOrder.objects.filter(
            create_date__gte=start_of_day,
            create_date__lte=end_of_day,
            order_type=order_type,
            del_flag=False
        )
        # parcel_customer_orders = ParcelCustomerOrder.objects.filter(
        #     create_date__gte=start_of_day,
        #     create_date__lte=end_of_day,
        #     del_flag=False
        # )

        daily_customer_orders[day.strftime('%Y-%m-%d')] = customer_orders.count() or 0
        # daily_parcel_customer_orders[day.strftime('%Y-%m-%d')] = parcel_customer_orders.count() or 0
        customer_order_data = customer_orders.aggregate(all_volume=Sum('volume'))
        if customer_order_data:
            daily_customer_orders_volume[day.strftime('%Y-%m-%d')] = customer_order_data['all_volume'] or 0
        else:
            daily_customer_orders_volume[day.strftime('%Y-%m-%d')] = 0
        # daily_customer_orders[day.strftime('%Y-%m-%d')] = (i + 1) / 2
        # daily_customer_orders_volume[day.strftime('%Y-%m-%d')] = (i + 1)

    img_base64 = draw_histogram_and_line_chart(daily_customer_orders, daily_customer_orders_volume, '订单数量',
                                               '订单体积', '日期', '下单量', '下单体积/方', '下单统计')
    send_dmas_base64_image(img_base64, send_msg='订单的下单量', message_group=['铭志每日财务报表群'])


# def pick_up_aging(query_products, start_day, end_day, order_type='FBA'):
#     product_ocean_order_aging = {'志尊达': [12, 12, 12.0], '志速达': [13, 18, 14.5],
#                                  '普船海派': [14, 22, 16.3], '志速达卡派': [13, 20, 15.2],
#                                  '普船卡派（洛杉矶）': [14, 29, 20.8],
#                                  '普船拼箱（洛杉矶）': [16, 23, 20.3], '芝加哥专线': [20, 30, 24.8]}
#     img_base64 = draw_pick_up_aging(product_ocean_order_aging, '', '时效/天', '开船到提柜时效')
#     # send_dmas_base64_image(img_base64, send_msg='订单的下单量', message_group=['铭志每日财务报表群'])
#     return img_base64


# 开船到提柜时效(已离港到已提柜)
def pick_up_aging(query_products, start_day, end_day, order_type='FBA'):
    logger.info(f'pick_up_aging start, {start_day}, {end_day}')
    product_ocean_order_aging = {}

    for product_code in query_products:
        product = Product.objects.filter(code=product_code, del_flag=False).last()
        customer_orders = CustomerOrder.objects.filter(
            create_date__gte=start_day,
            create_date__lte=end_day,
            product=product,
            order_type=order_type,
            del_flag=False
        )
        min_aging = timedelta(days=365 * 100)  # 初始化为一个很大的天数，确保任何实际时效都小于它
        statistics_this_aging_days = []
        # 最小 - 初始化为零 timedelta
        max_aging = timedelta(days=0)  # 初始化为零时长
        sum_aging_days = 0  # 用于存储总天数
        aging_count = 0
        for customer_order in customer_orders:
            # 注意这里您使用了 order_num 进行过滤，如果 Track 模型是通过 order_id 关联 CustomerOrder，
            # 您可能需要改回 order_id=customer_order.id
            aging_dep = Track.objects.filter(track_code='DEP', order_num=customer_order.order_num,
                                             del_flag=False).last()
            aging_pl = Track.objects.filter(track_code='PL', order_num=customer_order.order_num,
                                            del_flag=False).first()
            logger.info(f'有没有值?-->, {aging_dep}, {aging_pl}')
            if aging_dep and aging_pl:
                aging = aging_pl.actual_time.date() - aging_dep.actual_time.date()  # aging 是一个 timedelta 对象
                min_aging = min(aging, min_aging)
                max_aging = max(aging, max_aging)
                this_aging_days = aging.total_seconds() / (24 * 3600)
                statistics_this_aging_days.append(this_aging_days)
                sum_aging_days += this_aging_days
                aging_count += 1

        # 将最小和最大时效转换为天数（浮点数）
        # 如果 min_aging 仍然是初始大值，则保持无限大
        min_aging_days = min_aging.days
        max_aging_days = max_aging.days

        # 计算平均时效
        average_aging_days = sum_aging_days / aging_count if aging_count > 0 else None

        # 计算平均时效天数，并精确到天（四舍五入）
        average_aging_days_rounded = round(average_aging_days, 1) if average_aging_days is not None else None

        let_20 = len([i for i in statistics_this_aging_days if i <= 20])
        gt_20 = len([i for i in statistics_this_aging_days if i > 20])

        logger.info(f'{product.name}20天以内的轨迹数量: {let_20}, 20天以上的轨迹数量: {gt_20}, 20天以上轨迹占比{gt_20 / (gt_20 + let_20)}')

        # product_ocean_order_aging[product] 的值以天为单位（最大和最小是浮点数，平均是整数或浮点数根据您的需求）
        # 考虑到绘图函数可能需要浮点数，我们将平均值也转换为浮点数，保留小数点后一位，与图片样式一致
        # product_ocean_order_aging[product] = [
        product_ocean_order_aging[product.name] = [
            min_aging_days,  # 最小天数 (float)
            max_aging_days,  # 最大天数 (float)
            float(average_aging_days_rounded) if average_aging_days_rounded is not None else None  # 平均天数 (float)
        ]

    if not product_ocean_order_aging:
        return None
    logger.info(f'pick_up_aging data: {product_ocean_order_aging}')
    if isinstance(start_day, str):
        start_day_format = datetime.strptime(start_day, "%Y-%m-%d").strftime("%m-%d")
        end_day_format = datetime.strptime(end_day, "%Y-%m-%d").strftime("%m-%d")
    else:
        start_day_format = start_day.strftime("%m%d")
        end_day_format = end_day.strftime("%m%d")
    statistics_date = f'{start_day_format}-{end_day_format}'
    img_base64 = draw_pick_up_aging(product_ocean_order_aging, '', '时效/天', title='开船到提柜时效',
                                    statistics_date=statistics_date)
    # send_dmas_base64_image(img_base64, send_msg='订单的下单量', message_group=['铭志每日财务报表群'])
    logger.info(f'pick_up_aging end, {start_day}, {end_day}')
    return img_base64


# def arrive_overseas_warehouse_aging(channel, product_ocean_order_aging, most_max_aging_items, order_type='FBA',
#                                     is_draw_big_title=False):
#     img_base64 = draw_arrive_overseas_warehouse_aging(product_ocean_order_aging, most_max_aging_items, channel, '',
#                                                       '时效/天', is_draw_big_title=is_draw_big_title)
#     logger.info(f'arrive_overseas_warehouse_aging end, {channel}')
#     return img_base64


# 提柜到入仓平均时效(已提柜到已到海外仓)
def arrive_overseas_warehouse_aging(channel, warehouse_codes, start_day, end_day, order_type='FBA', is_draw_big_title=False):
    logger.info(f'arrive_overseas_warehouse_aging start, {channel}, {start_day}, {end_day}')
    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'FX']:
        return

    product_ocean_order_aging = {}

    most_max_aging = []
    for warehouse_code in warehouse_codes:
        customer_orders = CustomerOrder.objects.filter(
            create_date__gte=start_day,
            create_date__lte=end_day,
            receiver__address_num=warehouse_code,
            order_type=order_type,
            del_flag=False
        )
        max_aging = timedelta(days=0)  # 初始化为零时长
        sum_aging_days = 0  # 用于存储总天数
        aging_count = 0
        for customer_order in customer_orders:
            # 注意这里您使用了 order_num 进行过滤，如果 Track 模型是通过 order_id 关联 CustomerOrder，
            # 您可能需要改回 order_id=customer_order.id
            aging_dep = Track.objects.filter(track_code='DEP', order_num=customer_order.order_num,
                                             del_flag=False).last()
            truck_orders = TruckOrder.objects.filter(truckOrderRelateList__customer_order_num=customer_order,
                                                     del_flag=False)
            aging_pl = TruckOrderTrack.objects.filter(track_code='DEL', truckOrder__in=truck_orders,
                                                      del_flag=False).first()
            logger.info(f'{channel}获取轨迹数据, 订单: {customer_order.order_num}, '
                        f'已离港轨迹id: {aging_dep and aging_dep.id}, 已送达轨迹id: {aging_pl and aging_pl.id}')
            if aging_dep and aging_pl:
                aging = aging_pl.operation_time.date() - aging_dep.actual_time.date()  # aging 是一个 timedelta 对象
                max_aging = max(aging, max_aging)
                this_aging_days = aging.total_seconds() / (24 * 3600)  # 将 timedelta 转换为天数进行累加
                sum_aging_days += this_aging_days
                aging_count += 1

        # 将最小和最大时效转换为天数（浮点数）
        # 如果 min_aging 仍然是初始大值，则保持无限大
        # max_aging_days = max_aging.days
        most_max_aging.append(max_aging)

        # 计算平均时效（以天为单位）
        average_aging_days = sum_aging_days / aging_count if aging_count > 0 else None

        # 精确到天（四舍五入）
        average_aging_days_rounded = round(average_aging_days, 1) if average_aging_days is not None else None

        # product_ocean_order_aging[product] 的值以天为单位（最大和最小是浮点数，平均是整数或浮点数根据您的需求）
        # 考虑到绘图函数可能需要浮点数，我们将平均值也转换为浮点数，保留小数点后一位，与图片样式一致
        product_ocean_order_aging[warehouse_code] = \
            float(average_aging_days_rounded) if average_aging_days_rounded is not None else None  # 平均天数 (float)
    if not product_ocean_order_aging:
        return None
    most_max_aging_items = max(most_max_aging)
    logger.info(f'arrive_overseas_warehouse_aging data: {most_max_aging_items}, {product_ocean_order_aging}')
    img_base64 = draw_arrive_overseas_warehouse_aging(product_ocean_order_aging, most_max_aging_items, channel, '',
                                                      '时效/天', is_draw_big_title=is_draw_big_title)
    logger.info(f'arrive_overseas_warehouse_aging end, {channel}, {start_day}, {end_day}')
    return img_base64


# 提柜到入仓平均时效(已提柜到已到海外仓)
def arrive_overseas_warehouse_aging_new(channel, start_day, end_day, order_type='FBA',
                                        is_draw_big_title=False):
    logger.info(f'arrive_overseas_warehouse_aging start, {channel}, {start_day}, {end_day}')
    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'FX']:
        return

    title = channel.get('title')
    product_code = channel.get('product')
    warehouse_codes = channel.get('end_zone')

    # product = Product.objects.filter(code=product_code, del_flag=False).last()

    product_ocean_order_aging = {}
    most_max_aging = []
    for warehouse_code in warehouse_codes:
        if warehouse_code in ['美东', '美中', '美西']:
            all_product_zones = ProductZone.objects.filter(type='Buyer', del_flag=False)
            warehouse_code_list = []
            for product_zone in all_product_zones:
                is_belong_to = judge_zone_affiliation(product_zone.name, warehouse_code)
                if is_belong_to:
                    warehouse_code_list.append(product_zone.name)
            logger.info(f'warehouse_code_list-->{warehouse_code_list}')
            customer_orders = CustomerOrder.objects.filter(
                create_date__gte=start_day,
                create_date__lte=end_day,
                product__code=product_code,
                receiver__address_num__in=warehouse_code_list,
                order_type=order_type,
                del_flag=False
            )
        else:
            customer_orders = CustomerOrder.objects.filter(
                create_date__gte=start_day,
                create_date__lte=end_day,
                product__code=product_code,
                receiver__address_num=warehouse_code,
                order_type=order_type,
                del_flag=False
            )
        max_aging = timedelta(days=0)  # 初始化为零时长
        sum_aging_days = 0  # 用于存储总秒数
        aging_count = 0
        for customer_order in customer_orders:
            # 注意这里您使用了 order_num 进行过滤，如果 Track 模型是通过 order_id 关联 CustomerOrder，
            # 您可能需要改回 order_id=customer_order.id
            aging_dep = Track.objects.filter(track_code='DEP', order_num=customer_order.order_num,
                                             del_flag=False).last()
            truck_orders = TruckOrder.objects.filter(truckOrderRelateList__customer_order_num=customer_order,
                                                     del_flag=False)
            aging_pl = TruckOrderTrack.objects.filter(track_code='DEL', truckOrder__in=truck_orders,
                                                      del_flag=False).first()
            # if not aging_pl:
            #     aging_pl = Track.objects.filter(track_code__in=['SF', 'FC', 'Delivered'],
            #                                     order_num=customer_order.order_num,
            #                                     del_flag=False).first()
            logger.info(f'{product_code}获取轨迹数据, 订单: {customer_order.order_num}, '
                        f'已离港轨迹id: {aging_dep and aging_dep.id}, 已送达轨迹id: {aging_pl and aging_pl.id}')
            if aging_dep and aging_pl:
                aging = aging_pl.operation_time.date() - aging_dep.actual_time.date()  # aging 是一个 timedelta 对象
                max_aging = max(aging, max_aging)
                this_aging_days = aging.total_seconds() / (24 * 3600)  # 将 timedelta 转换为总秒数进行累加
                sum_aging_days += this_aging_days
                aging_count += 1

        # 将最小和最大时效转换为天数（浮点数）
        # 如果 min_aging 仍然是初始大值，则保持无限大
        # max_aging_days = max_aging.days
        most_max_aging.append(max_aging)

        # 计算平均时效（以秒为单位）
        average_aging_days = sum_aging_days / aging_count if aging_count > 0 else None

        # 计算平均时效天数，并精确到天（四舍五入）
        average_aging_days_rounded = round(average_aging_days / (24 * 3600),
                                           1) if average_aging_days is not None else None

        # product_ocean_order_aging[product] 的值以天为单位（最大和最小是浮点数，平均是整数或浮点数根据您的需求）
        # 考虑到绘图函数可能需要浮点数，我们将平均值也转换为浮点数，保留小数点后一位，与图片样式一致
        product_ocean_order_aging[warehouse_code] = \
            float(average_aging_days_rounded) if average_aging_days_rounded is not None else None  # 平均天数 (float)
    if not product_ocean_order_aging:
        return None
    most_max_aging_items = max(most_max_aging)
    logger.info(f'arrive_overseas_warehouse_aging data: {most_max_aging_items}, {product_ocean_order_aging}')
    img_base64 = draw_arrive_overseas_warehouse_aging(product_ocean_order_aging, most_max_aging_items, title, '',
                                                      '时效/天', is_draw_big_title=is_draw_big_title)
    logger.info(f'arrive_overseas_warehouse_aging end, {channel}, {start_day}, {end_day}')
    return img_base64


# 绘制单柱柱状图
def draw_histogram_chart(data, data_title, x_label, y_label, head_title):
    days = list(data.keys())
    data_values = list(data.values())

    x = np.arange(len(days))

    fig = plt.figure(dpi=150)
    # 设置中文字体（如果需要替换为其他字体，请更改字体路径）
    # 若没有则需要安装字体
    plt.rcParams['font.family'] = 'SimSun'  # 设置字体
    plt.bar(x, data_values, width=0.4, color='#63daad', label=data_title)

    # 在每个条形上标示数值
    for i, v in enumerate(data_values):
        if isinstance(v, int):
            plt.text(i, v, f'{v}', ha='center', va='bottom', fontsize=9)
        else:
            plt.text(i, v, f'{v:.2f}', ha='center', va='bottom', fontsize=9)

    plt.xlabel(' '.join(x_label))
    plt.ylabel('\n'.join(y_label), rotation=0, labelpad=20)
    plt.title(head_title)
    plt.xticks(x, days, rotation=45, ha='right')  # 设置横轴标签并旋转45度
    plt.legend()

    # 保存图表到内存
    buf = io.BytesIO()
    plt.tight_layout()  # 自适应布局
    plt.savefig(buf, format='png')
    plt.close()
    buf.seek(0)

    # 将图表转换为 base64 编码
    img_base64 = base64.b64encode(buf.read()).decode('utf-8')
    print(img_base64 + '\n')
    return img_base64


# 绘制双柱柱状图(横轴相同的两组数据)
def draw_histogram_chart_double(more_data, less_data, more_title, less_title, x_label, y_label, head_title,
                                separate=True):
    """
    :param more_data:
    :param less_data:
    :param more_title:
    :param less_title:
    :param x_label:
    :param y_label:
    :param head_title:
    :param separate: 两个柱体是否拆开, True: 拆开, False: 重叠
    """
    days = list(more_data.keys())
    more_values = list(more_data.values())
    less_values = list(less_data.values())

    x = np.arange(len(days))

    figsize_x = max(6.4, len(days) - 2)
    fig = plt.figure(figsize=(figsize_x, 4.8), dpi=150)
    # 设置中文字体（如果需要替换为其他字体，请更改字体路径）
    # 若没有则需要安装字体
    plt.rcParams['font.family'] = 'SimSun'  # 设置字体
    if separate:
        # plt.bar(x + 0.2, more_values, width=0.4, color='#5aaef4', label=more_title)
        plt.bar(x + 0.2, more_values, color='#5aaef4', label=more_title)
        plt.bar(x - 0.2, less_values, color='#63daad', label=less_title)
    else:
        plt.bar(x, more_values, color='#5aaef4', label=more_title)
        plt.bar(x, less_values, color='#63daad', label=less_title)

    # 在每个条形上标示数值
    for i, v in enumerate(more_values):
        if separate:
            i_x = i + 0.2
        else:
            i_x = i
        if isinstance(v, int):
            plt.text(i_x, v, f'{v}', ha='center', va='bottom', fontsize=9)
        else:
            plt.text(i_x, v, f'{v:.2f}', ha='center', va='bottom', fontsize=9)
    for i, v in enumerate(less_values):
        if separate:
            i_x = i - 0.2
        else:
            i_x = i
        if isinstance(v, int):
            plt.text(i_x, v, f'{v}', ha='center', va='bottom', fontsize=9)
        else:
            plt.text(i_x, v, f'{v:.2f}', ha='center', va='bottom', fontsize=9)

    plt.xlabel(' '.join(x_label))
    plt.ylabel('\n'.join(y_label), y=0.4, rotation=0, labelpad=20)
    plt.title(head_title)
    plt.xticks(x, days, rotation=45, ha='right')  # 设置横轴标签并旋转45度
    plt.legend()

    # 保存图表到内存
    buf = io.BytesIO()
    plt.tight_layout()  # 自适应布局
    plt.savefig(buf, format='png')
    plt.close()
    buf.seek(0)

    # 将图表转换为 base64 编码
    img_base64 = base64.b64encode(buf.read()).decode('utf-8')
    print(img_base64)
    return img_base64


# 绘制柱状图 + 折线图(共用x轴)
def draw_histogram_and_line_chart(y1_data, y2_data, y1_title, y2_title, x_label, y1_label, y2_label, head_title):
    days = list(y1_data.keys())
    y1_values = list(y1_data.values())
    y2_values = list(y2_data.values())

    x = np.arange(len(days))

    # 设置中文字体（如果需要替换为其他字体，请更改字体路径）
    # 若没有则需要安装字体
    if platform.system() == 'Darwin':
        plt.rcParams['font.sans-serif'] = ['Heiti TC']  # 设置中文字体
    else:
        plt.rcParams['font.family'] = 'SimSun'  # 设置字体

    # 创建柱状图
    figsize_x = max(6.4, len(days) - 2)
    fig, ax1 = plt.subplots(figsize=(figsize_x, 4.8), dpi=150)
    ax1.bar(x, y1_values, color='#5aaef4', label=y1_title)

    # 在每个条形上标示数值
    for i, v in enumerate(y1_values):
        if isinstance(v, int):
            ax1.text(i, v, f'{v}', color='#2b2b2b', ha='center', va='bottom', fontsize=9)
        else:
            ax1.text(i, v, f'{v:.2f}', color='#2b2b2b', ha='center', va='bottom', fontsize=9)

    # 创建折线图
    # ax2 = ax1.gca().twinx()  # 创建第二个 y 轴
    ax2 = ax1.twinx()  # 创建第二个 y 轴
    ax2.plot(x, y2_values, color='#ee6666', marker='o', label=y2_title)

    # 在折线上标示数值
    for i, v in enumerate(y2_values):
        if isinstance(v, int):
            ax2.text(i, v - Decimal('0.06') * max(y2_values), f'{v}', color='#c03a90', ha='center', va='bottom',
                     fontsize=9)
        else:
            ax2.text(i, v - Decimal('0.06') * max(y2_values), f'{v:.2f}', color='#c03a90', ha='center', va='bottom',
                     fontsize=9)

    # 设置标签和标题
    ax1.set_xlabel(' '.join(x_label))
    ax1.set_ylabel('\n'.join(y1_label), y=0.4, rotation=0, labelpad=20)
    ax2.set_ylabel('\n'.join(y2_label), y=0.6, rotation=0, labelpad=20)
    ax1.set_title(head_title)
    ax1.set_xticks(x)
    ax1.set_xticklabels(days, rotation=45, ha='right')  # 设置横轴标签并旋转45度

    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    # 保存图表到内存
    buf = io.BytesIO()
    plt.tight_layout()  # 自适应布局
    plt.savefig(buf, format='png')
    plt.close()
    buf.seek(0)

    # 将图表转换为 base64 编码
    img_base64 = base64.b64encode(buf.read()).decode('utf-8')
    print(img_base64)
    return img_base64


# 绘制开船到提柜提柜时效(展示三个值: 最大值和最小值共轴, 平均值用曲线绘制)
def draw_pick_up_aging(product_ocean_order_aging, x_label="产品", y_label="时效(天)", title="开船到提柜时效",
                       statistics_date=None, big_title='美国海运时效表'):
    """
    根据产品时效数据绘制组合图表 (柱状图+平滑曲线图)，增加四周留白空间。
    """
    print(f'product_ocean_order_aging-->{product_ocean_order_aging}')
    # 准备数据
    products = list(product_ocean_order_aging.keys())
    min_agings = [data[0] for data in product_ocean_order_aging.values()]
    max_agings = [data[1] for data in product_ocean_order_aging.values()]
    avg_agings = [data[2] for data in product_ocean_order_aging.values()]

    # 过滤无效数据
    filtered_data = [(p, min_a, max_a, avg_a) for p, min_a, max_a, avg_a in
                     zip(products, min_agings, max_agings, avg_agings)
                     if avg_a is not None and avg_a != float('inf')]

    if not filtered_data:
        return ""

    products, min_agings, max_agings, avg_agings = zip(*filtered_data)
    x = np.arange(len(products))  # x轴刻度位置

    # 设置图表样式
    plt.style.use('default')
    if platform.system() == 'Darwin':
        plt.rcParams['font.sans-serif'] = ['Heiti TC']  # 设置中文字体
    else:
        plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # 创建更大的画布和更多的留白
    fig, ax = plt.subplots(figsize=(24, 18))  # 修改为与 draw_arrive_overseas_warehouse_aging 相同的尺寸

    # 设置背景色
    fig.patch.set_facecolor('#E8F0FB')  # 设置图表背景色
    ax.set_facecolor('#E8F0FB')

    # 设置边框样式
    for spine in ['top', 'right', 'left']:
        ax.spines[spine].set_visible(False)
    ax.spines['bottom'].set_visible(True)

    # 设置颜色
    max_color = '#4774CC'  # 深蓝色
    min_color = '#B6C7E8'  # 浅蓝色
    avg_color = '#F98021'  # 橙色

    # 绘制柱状图 (MAX和MIN)
    width = 0.3  # 修改柱状图宽度与 draw_arrive_overseas_warehouse_aging 一致
    rects1 = ax.bar(x, max_agings, width, label='MAX', color=max_color)
    rects2 = ax.bar(x, min_agings, width, label='MIN', color=min_color)

    # 创建平滑曲线
    if len(x) > 1:  # 只有多于1个数据点才能平滑
        # 生成更密集的x值
        x_new = np.linspace(x.min(), x.max(), 300)
        # 使用三次样条插值
        spl = make_interp_spline(x, avg_agings, k=3)  # k=3表示三次样条
        avg_smooth = spl(x_new)
        # 绘制平滑曲线
        line = ax.plot(x_new, avg_smooth, label='平均值', color=avg_color,
                       linestyle='-', linewidth=2.5)
        # 在原始数据点上添加标记
        ax.scatter(x, avg_agings, color=avg_color, s=60, zorder=5)
    else:
        # 只有一个数据点时直接绘制点
        ax.scatter(x, avg_agings, label='平均值', color=avg_color, s=60)

    # 添加数据标签
    for i, (min_a, max_a, avg_a) in enumerate(zip(min_agings, max_agings, avg_agings)):
        ax.text(x[i], avg_a + 0.5, f'{avg_a:.1f}天', ha='center', va='bottom', fontsize=22)

    # 设置图表标题和轴标签
    ax.set_title(title, fontsize=50, y=1.2)  # 修改字体大小与 draw_arrive_overseas_warehouse_aging 一致

    title_obj = ax.title
    title_obj.set_path_effects([
        patheffects.withStroke(linewidth=2, foreground="black"),  # 修改线宽与 draw_arrive_overseas_warehouse_aging 一致
        patheffects.Normal()
    ])

    if statistics_date:
        subtitle_obj = ax.text(
            0, 1.5,
            statistics_date,
            transform=ax.transAxes,  # 使用轴坐标
            ha='left',  # 水平居中
            va='bottom',  # 垂直对齐
            fontsize=70,  # 比主标题小一些
            fontweight='bold',
            color='#1F50F4'
        )
        # 为副标题添加相同的加粗效果
        subtitle_obj.set_path_effects([
            patheffects.withStroke(linewidth=3, foreground="#1F50F4"),  # 线宽可以比主标题细一点
            patheffects.Normal()
        ])

    subtitle_obj = ax.text(
        0, 1.38,
        big_title,
        transform=ax.transAxes,  # 使用轴坐标
        ha='left',  # 水平居中
        va='bottom',  # 垂直对齐
        fontsize=80,  # 比主标题小一些
        fontweight='bold'
    )
    # 为副标题添加相同的加粗效果
    subtitle_obj.set_path_effects([
        patheffects.withStroke(linewidth=3, foreground="black"),  # 线宽可以比主标题细一点
        patheffects.Normal()
    ])

    ax.set_xticks(x)
    ax.set_xticklabels(products, rotation=45, ha="right",
                       fontsize=25)  # 修改字体大小与 draw_arrive_overseas_warehouse_aging 一致

    # 调整Y轴范围
    y_min = max(0, min(min_agings) * 0.9)  # 确保最小值不小于0
    y_max = max(max_agings) * 1.1

    # 动态确定刻度间隔
    range = y_max - y_min
    if range < 10:
        interval = 1
    elif range < 20:
        interval = 2
    elif range < 50:
        interval = 5
    else:
        interval = 10

    # 生成均匀的刻度
    y_ticks = np.arange(np.floor(y_min / interval) * interval, np.ceil(y_max / interval) * interval + interval,
                        interval)

    ax.set_ylim(y_min, y_max)

    # 设置Y轴刻度
    ax.set_yticks(y_ticks)
    ax.set_yticklabels([f'{int(y)}天' for y in y_ticks], fontsize=22)  # 修改字体大小与 draw_arrive_overseas_warehouse_aging 一致

    # 设置网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # 设置图例位置和样式
    handles, labels = ax.get_legend_handles_labels()
    # 重新排序图例：MAX, MIN, 平均值
    order = [0, 1, 2]  # 原始顺序
    handles = [handles[i] for i in order]
    labels = [labels[i] for i in order]
    legend = ax.legend(handles, labels, loc='upper center',
                       bbox_to_anchor=(0.5, -0.25),  # 向下移动图例
                       ncol=3, fontsize=25)
    # 设置图例背景色
    legend.get_frame().set_facecolor('#E8F0FB')
    legend.get_frame().set_edgecolor('none')  # 移除图例边框

    # 增加四周留白（重点调整左右和上方留白）
    plt.subplots_adjust(
        left=0.2,  # 左边距 (显著增加)
        right=0.8,  # 右边距 (显著增加)
        top=0.9,  # 上边距 (增加)
        bottom=0.2  # 下边距
    )

    # 将图表保存到内存缓冲区
    buf = io.BytesIO()
    plt.savefig(buf, format='png', dpi=120, bbox_inches='tight', pad_inches=1.0)  # 增加pad_inches到1.0
    buf.seek(0)

    # 将缓冲区内容编码为base64
    img_base64 = base64.b64encode(buf.getvalue()).decode('utf-8')

    # 关闭图表，释放内存
    plt.close(fig)

    # print(f'开船到提柜时效, img_base64-->\n{img_base64}\n')

    return img_base64


# 绘制提柜到入仓平均时效(展示一个值: 平均值, 另外最大值+n作为背景柱体)
def draw_arrive_overseas_warehouse_aging(product_ocean_order_aging, most_max_aging_items, channel,
                                         x_label="产品", y_label="时效(天)", is_draw_big_title=None):
    """
    根据产品时效数据绘制组合图表 (柱状图+平滑曲线图)，增加四周留白空间。

    Args:
        product_ocean_order_aging: 字典，键为产品名称，值为 [min_aging, max_aging, average_aging]。
        most_max_aging_items:
        channel: 小标题(渠道)。
        x_label: X轴标签。
        y_label: Y轴标签。
        is_draw_big_title: 图表标题。

    Returns:
        图表的base64编码字符串。
    """
    print(f'product_ocean_order_aging-->{product_ocean_order_aging}, {most_max_aging_items}')
    # 准备数据
    warehouse_codes = list(product_ocean_order_aging.keys())
    avg_agings = list(product_ocean_order_aging.values())
    most_max_aging_day = (round((most_max_aging_items + timedelta(days=5)) / timedelta(days=5)) *
                          timedelta(days=5)).days
    print('avg_agings-------->', avg_agings)
    max_agings = [most_max_aging_day for _ in range(len(warehouse_codes))]

    # 过滤无效数据
    filtered_data = [(w, avg_a, max_aging) for w, avg_a, max_aging in
                     zip(warehouse_codes, avg_agings, max_agings)
                     if avg_a is not None]

    if not filtered_data:
        return ""

    warehouse_codes, avg_agings, max_agings = zip(*filtered_data)
    x = np.arange(len(warehouse_codes))  # x轴刻度位置

    # 设置图表样式
    plt.style.use('default')
    if platform.system() == 'Darwin':
        plt.rcParams['font.sans-serif'] = ['Heiti TC']  # 设置中文字体
    else:
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # 创建更大的画布和更多的留白
    fig, ax = plt.subplots(figsize=(24, 12))  # 进一步增加画布尺寸

    # 设置背景色
    fig.patch.set_facecolor('#E8F0FB')  # 设置图表背景色
    ax.set_facecolor('#E8F0FB')  # 设置绘图区域背景色

    # 设置边框样式
    for spine in ['top', 'right', 'left']:
        ax.spines[spine].set_visible(False)
    ax.spines['bottom'].set_visible(True)

    # 设置颜色
    max_color = '#4774CC'  # 深蓝色
    min_color = '#B6C7E8'  # 浅蓝色
    avg_color = '#F98021'  # 橙色

    # 绘制柱状图 (MAX和MIN)
    width = 0.5  # 柱状图宽度
    rects2 = ax.bar(x, max_agings, width, label='MAX', color=min_color)
    rects1 = ax.bar(x, avg_agings, width, label='平均值', color=max_color)

    # 添加数据标签
    for i, avg_a in enumerate(avg_agings):
        ax.text(x[i], avg_a + 0.5, f'{avg_a:.1f}', ha='center', va='bottom', fontsize=17)

    # 设置图表标题
    if is_draw_big_title:
        ax.set_title(is_draw_big_title, fontsize=50, y=1.2, ha='center')
        # ax.set_title(title, fontsize=50, pad=20, y=1.2)

    title_obj = ax.title
    title_obj.set_path_effects([
        patheffects.withStroke(linewidth=2, foreground="black"),
        patheffects.Normal()
    ])

    # 添加副标题（同样加粗效果）
    subtitle = channel  # 替换为你的副标题内容
    subtitle_obj = ax.text(
        0.5, 1.1,  # x=0.5居中，y=1.05表示在主标题下方
        subtitle,
        transform=ax.transAxes,  # 使用轴坐标
        ha='center',  # 水平居中
        va='bottom',  # 垂直对齐
        fontsize=30,  # 比主标题小一些
        fontweight='bold'
    )

    # 为副标题添加相同的加粗效果
    subtitle_obj.set_path_effects([
        patheffects.withStroke(linewidth=1.5, foreground="black"),  # 线宽可以比主标题细一点
        patheffects.Normal()
    ])

    # ax.set_xlabel(x_label, fontsize=17, labelpad=20)  # 增加label与轴的距离
    ax.set_xticks(x)
    ax.set_xticklabels(warehouse_codes, rotation=45, ha="right", fontsize=20)

    # 调整Y轴范围
    y_min = max(0, min(avg_agings) * 0.9)  # 确保最小值不小于0
    y_max = max(max_agings) * 1.1

    # 动态确定刻度间隔
    range_y = y_max - y_min
    if range_y < 10:
        interval = 1
    elif range_y < 20:
        interval = 2
    elif range_y < 50:
        interval = 5
    else:
        interval = 10

    # 生成均匀的刻度
    y_ticks = np.arange(np.floor(y_min / interval) * interval, np.ceil(y_max / interval) * interval + interval,
                        interval)

    ax.set_ylim(y_min, y_max)

    # 设置Y轴刻度
    ax.set_yticks(y_ticks)
    ax.set_yticklabels([f'{int(y)}天' for y in y_ticks], fontsize=22)

    # 设置网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # # 设置图例位置和样式
    # handles, labels = ax.get_legend_handles_labels()
    # # 重新排序图例：MAX, MIN, 平均值
    # legend = ax.legend(handles, labels, loc='upper center',
    #                    bbox_to_anchor=(0.5, -0.25),  # 向下移动图例
    #                    ncol=3, fontsize=17)
    # # 设置图例背景色
    # legend.get_frame().set_facecolor('#E8F0FB')
    # legend.get_frame().set_edgecolor('none')  # 移除图例边框

    # 增加四周留白（重点调整左右和上方留白）
    plt.subplots_adjust(
        left=0.2,  # 左边距 (显著增加)
        right=0.8,  # 右边距 (显著增加)
        top=0.9,  # 上边距 (增加)
        bottom=0.2  # 下边距
    )

    # 将图表保存到内存缓冲区
    buf = io.BytesIO()
    plt.savefig(buf, format='png', dpi=120, bbox_inches='tight', pad_inches=1.0)  # 增加pad_inches到1.0
    buf.seek(0)

    # 将缓冲区内容编码为base64
    img_base64 = base64.b64encode(buf.getvalue()).decode('utf-8')

    # 关闭图表，释放内存
    plt.close(fig)

    # print(f'提柜到入仓时效, img_base64-->\n{img_base64}\n')

    return img_base64


# 将两张宽度相同的base64编码的图片上下紧密拼接（无留白）
def combine_multiple_plots_vertically(img_base64_list):
    """
    将多张base64编码的图片上下紧密拼接（无留白）

    Args:
        img_base64_list: 可变参数，传入多张图片的base64编码

    Returns:
        合并后的base64编码图片
    """
    # 解码所有图片
    images = []
    for img_base64 in img_base64_list:
        img_data = base64.b64decode(img_base64)
        img = Image.open(io.BytesIO(img_data))
        images.append(img)

    # 计算总高度和最大宽度
    total_height = sum(img.height for img in images)
    max_width = max(img.width for img in images)

    # 创建新画布
    combined = Image.new('RGB', (max_width, total_height))

    # 逐个粘贴图片
    y_offset = 0
    for img in images:
        x_offset = (max_width - img.width) // 2  # 水平居中
        combined.paste(img, (x_offset, y_offset))
        y_offset += img.height  # 更新垂直偏移量

    # 转换为base64
    buf = io.BytesIO()
    combined.save(buf, format='PNG')
    return base64.b64encode(buf.getvalue()).decode('utf-8')


# 将距离退件单截止重派日期小于等于3天的单子提醒客户处理
def return_order_confirm():
    """
    发送退货单重派截止日期提醒（距离截止日期<=3天的单子）
    1. 查询所有未删除且截止日期在3天内的退货单
    2. 格式化提醒消息
    3. 发送通知到指定群组
    """
    three_days_later = timezone.now().date() + timedelta(days=3)

    # 查询符合条件的退货单（未删除且截止日期在3天内）
    urgent_return_orders = ReturnOrder.objects.filter(
        ~Q(order_status__in=['CP', 'XH']),
        del_flag=False,
        reassignment_deadline_date__isnull=False,
        reassignment_deadline_date__lte=three_days_later,
        reassignment_deadline_date__gte=timezone.now().date()  # 确保还没过期
    ).order_by('reassignment_deadline_date')

    if urgent_return_orders.exists():
        order_count = urgent_return_orders.count()
        order_samples = "\n".join(
            f"{order.tracking_num} - 客户 {order.customer} - 截止日期: {order.reassignment_deadline_date.strftime('%Y-%m-%d')}"
            for order in urgent_return_orders[:20]  # 最多显示20条样例
        )

        message = (
            f"【退货单重派提醒】\n"
            f"共有 {order_count} 个退货单将在3天内截止重派:\n"
            f"{order_samples}\n"
            f"请及时处理！"
        )

        logger.info(f"发现 {order_count} 个即将截止的退货单，已发送提醒")
        send_dmas_message(message, message_group=['tangus退件单提醒群'])

    else:
        logger.info("当前没有即将截止的退货单需要提醒")


def account_balance_alert():
    """
    发送账户余额预警提醒（余额在0-2000之间的账户）
    1. 查询所有未删除且余额在0-2000之间的账户
    2. 格式化提醒消息
    3. 发送通知到指定群组
    """
    # 查询符合条件的账户
    low_balance_accounts = Account.objects.filter(
        del_flag=False,
        usable_balance__gt=0,
        usable_balance__lt=2000
    ).order_by('usable_balance')

    if low_balance_accounts.exists():
        account_count = low_balance_accounts.count()
        account_samples = "\n".join(
            f"客户: {account.customer} - 当前余额: {account.usable_balance:.2f}"
            for account in low_balance_accounts
        )

        message = (
            f"【账户余额不足预警】\n"
            f"共有 {account_count} 个账户余额不足2000:\n"
            f"{account_samples}\n"
            f"请及时提醒客户充值！"
        )

        logger.info(f"发现 {account_count} 个余额不足的账户，已发送提醒")
        send_dmas_message(message, message_group=['tangus账户余额预警群'])

    else:
        logger.info("当前没有余额不足需要提醒的账户")

if __name__ == '__main__':
    def base64_to_file(base64_code, file_name='111.jpg'):
        with open(file_name, "wb") as code:
            code.write(base64.b64decode(base64_code))

    product_ocean_order_aging1 = {'志尊达': [12, 12, 12.0], '志速达': [12, 20, 14.1], '普船海派': [17, 19, 17.2], '志速达卡派': [14, 19, 16.5], '普船卡派（洛杉矶）': [14, 19, 17.5], '普船拼箱（洛杉矶）': [14, 19, 17.2], '芝加哥专线': [24, 25, 24.0]}
    img_base1 = draw_pick_up_aging(product_ocean_order_aging1, '', '时效/天', '开船到提柜时效')

    product_ocean_order_aging2 = {'IUSJ': None, 'IUSP': None, 'IUSQ': None, 'LAX9': 16.0, 'SBD1': None, 'PSC2': 22.3, 'MQJ1': None, 'ONT8': 15.0, 'GYR2': 21.3, 'GYR3': 25.0, 'I袗袧3': None, 'IND9': 23.0, 'LAS1': 20.5, 'VGT2': 24.3, 'SMF3': 21.0, 'MEM1': 19.0, 'MDW2': None, 'LGB8': 17.5, 'RFD2': None, 'FTW1': None, 'SCK4': None, 'MIT2': 22.5, 'GEU2': 14.0}
    img_base2 = draw_arrive_overseas_warehouse_aging(product_ocean_order_aging2, timedelta(26), '', '时效/天', '提柜到入仓平均时效')

    combined_img = combine_multiple_plots_vertically([img_base1, img_base2])
    base64_to_file(combined_img)
