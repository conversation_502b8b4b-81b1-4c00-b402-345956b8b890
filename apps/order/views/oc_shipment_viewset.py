import traceback
from copy import deepcopy
from datetime import timedelta

from django.conf import settings
from django.core.cache import cache
from django.db import transaction
from django.db.models.functions import Coalesce
from django.http import HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.decorators import action
from django.db.models import Sum, Prefetch, Q, Value, DecimalField
from django.db.models import OuterRef, Subquery
from django.utils.datetime_safe import datetime

from alita.logger import logger
from common.error import ParamError, ErrorCode
from common.requestLock import lock_request
from common.service.shipment_case_service import ShipmentCaseService
from common.service.shipment_export_service import ShipmentExportService
from common.tools import generate_shipment_excel, get_multi_search, param_to_queryset, parse_custom_date
from common.utils.custom_json_response import JResponse

from common.utils.custom_viewset_base import CustomViewBase
from common.utils.outbound_instruct_util import calc_exceed_time_storage_charges
from common.utils.response import fail_response, success_response, success_response_common, fail_response_common
from company.models import Address
from cs.models import AbnormalFeedback, CaseShipment, VasOrder
from oms.models import ExceedTimeStorageCharges

from order.models import OcShipment, CustomerOrderRelateOcean, CustomerOrder, OutboundInstruct, Parcel, Track, \
    OutboundInstructDetail, OrderFieldChangeLog, OcShipmentLabel
from common.custom import RbacPermission
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework_jwt.authentication import JSONWebTokenAuthentication

from order.serializers.oc_shipment_serializer import OcShipmentSerializer, OcShipmentAndDetailSerializer, \
    ExceedTimeStorageChargesSerializer, OcShipmentLabelSerializer
from order.serializers.outbound_instruct_serializer import validate_appointment_time
from order.utils.oc_shipment_utils import storage_charges_revenue_confirm
from settle.models import Debit, AccountReceivable


class OcShipmentViewSet(CustomViewBase):
    """
    货件管理：增删改查
    """

    queryset = OcShipment.objects.all()

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ('=shipment_num', '=shipment_id', '=customer_order_num__order_num', '=reference_id')
    # 日期范围查询未生效原因: 不要在这里加 customer_order_num__actual_arrived_wh_date, 因为父类的get_queryset中读取并执行了查询, 如果在这里定义,
    # DRF识别不了前端传过来的查询 customer_order_num__actual_arrived_wh_date=2024-05-01,2025-06-24
    filterset_fields = ('shipment_id', 'reference_id', 'shop_type', 'status', 'customer',
                        'customer_order_num__receiver',
                        # 'customer_order_num__customerOrderRelateList__oceanOrder__order_num',
                        # 'customer_order_num__customerOrderRelateList__oceanOrder__container_no',
                        'special_require',
                        'customer_order_num__customerOrderRelateList__oceanOrder__airline_short_name')
    ordering_fields = ('id',)
    authentication_classes = (JSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    def get_serializer_class(self):
        if self.action == "list":
            return OcShipmentSerializer
        return OcShipmentAndDetailSerializer

    def get_queryset(self):
        if self.action == "list":
            # 0226 新需求,调整列表，以前部分查询需要去掉
            # 货件列表查询需要关联的对象有：订单、轨迹、出仓指令、增值单、包裹下的SKU数量、工单case、异常反馈等
            queryset = super().get_queryset().select_related("customer_order_num")

            status = self.request.query_params.get('status')
            if settings.SYSTEM_ORDER_MARK in ['CLT'] and not status:
                queryset = queryset.filter(~Q(status='VO'))

            # 获取订单下的包裹的SKU PCS
            # parcel_items_sum = Parcel.objects.filter(
            #     customer_order_id=OuterRef('customer_order_num_id')
            # ).annotate(
            #     items_qty_sum=Sum('parcelItem__item_qty')
            # ).values('items_qty_sum')

            # 获取最新轨迹 Track 的order_num有索引
            # latest_track = Track.objects.filter(
            #     order_num=OuterRef('customer_order_num__order_num'),
            #     del_flag=False,
            # ).order_by('-actual_time')
            #
            # queryset = queryset.annotate(
            #     newest_track_time=Subquery(
            #         latest_track.values('actual_time')[:1]
            #     ),
            #     newest_track_name=Subquery(
            #         latest_track.values('track_name')[:1]
            #     ),
            #     total_item_qty=Subquery(
            #         parcel_items_sum[:1]
            #     )
            # )

            # 获取货件下的工单
            queryset = queryset.prefetch_related(
                Prefetch(
                    'case_shipment_ref',
                    queryset=CaseShipment.objects.select_related('case').filter(del_flag=False).only('case__case_num',
                                                                                                     'pk'),
                    to_attr='case_shipments'
                )
            )

            # 获取出仓指令
            # queryset = queryset.prefetch_related(
            #     Prefetch(
            #         'outbound_instruct_shipment_ref',
            #         queryset=OutboundInstruct.objects.filter(del_flag=False),
            #         to_attr='outbound_instructs'
            #     )
            # )

            # 获取货件相关的出仓指令
            queryset = queryset.prefetch_related(
                Prefetch(
                    'outbound_instruct_detail_shipment_ref',  # 通过反向关系获取 OutboundInstructDetail
                    queryset=OutboundInstructDetail.objects.select_related('outbound_instruct').filter(
                        Q(del_flag=False) & ~Q(outbound_instruct__status='VO')
                    ),
                    to_attr='outbound_instruct_details'
                )
            )

            # 获取增值单
            queryset = queryset.prefetch_related(
                Prefetch(
                    'vas_shipment_ref',
                    queryset=VasOrder.objects.filter(del_flag=False).select_related('charge'),
                    to_attr='vas_orders'
                )
            )

            # 关联异常反馈
            queryset = queryset.prefetch_related(
                Prefetch(
                    'abnormal_feedback_shipment_ref',
                    queryset=AbnormalFeedback.objects.filter(del_flag=False)
                    .select_related('abnormal_type'),
                    # .select_related('abnormal_tag'),
                    to_attr='abnormal_feedbacks'
                )
            )

            return queryset

        return super().get_queryset()

    def list(self, request, *args, **kwargs):

        order_status = request.query_params.get('order_status')
        order_num = request.query_params.get('order_num')
        ref_num = request.query_params.get('ref_num')
        ocean_num = request.query_params.get('ocean_num')
        abnormal_type = request.query_params.get('abnormal_type')
        abnormal_tags = request.query_params.get('abnormal_tags')
        address_id = request.query_params.get('address_num')
        all_parcel_volume = request.query_params.get('all_parcel_volume')
        sort_code = request.query_params.get('sort_code')
        if ocean_num:
            relate_order = CustomerOrderRelateOcean.objects.filter(oceanOrder__order_num=ocean_num, del_flag=False)
            customer_order_ids = relate_order.values_list('customer_order_num_id', flat=True)
            # self.queryset = self.queryset.filter(customer_order_num__ocean_number=ocean_num, del_flag=False)
            print('customer_order_ids-->', customer_order_ids)
            self.queryset = self.queryset.filter(customer_order_num_id__in=customer_order_ids, del_flag=False)

        if ref_num:
            self.queryset = self.queryset.filter(customer_order_num__ref_num=ref_num, del_flag=False)

        if order_num:
            self.queryset = self.queryset.filter(customer_order_num__order_num=order_num, del_flag=False)

        if order_status:
            self.queryset = self.queryset.filter(customer_order_num__order_status=order_status, del_flag=False)
        if address_id:
            address_obj = Address.objects.filter(id=address_id).first()
            if address_obj:
                self.queryset = self.queryset.filter(address_num=address_obj.address_num, del_flag=False)
        if sort_code:
            self.queryset = self.queryset.filter(sort_code=sort_code, del_flag=False)

        if abnormal_type is not None:
            abnormal_feedbacks = AbnormalFeedback.objects.filter(
                abnormal_type__type_name=abnormal_type,
                del_flag=False,
            ).order_by('-id')[:1000]
            if not abnormal_feedbacks:
                return success_response(request, "success!")

            customer_orders = abnormal_feedbacks.values_list('customer_order_num__id', flat=True)
            self.queryset = self.queryset.filter(customer_order_num__id__in=customer_orders, del_flag=False)

        if abnormal_tags is not None:
            abnormal_feedbacks = AbnormalFeedback.objects.filter(
                abnormal_tag__type_name=abnormal_tags,
                del_flag=False,
            ).order_by('-id')[:1000]
            if not abnormal_feedbacks:
                return success_response(request, "success!")

            customer_orders = abnormal_feedbacks.values_list('customer_order_num__id', flat=True)
            self.queryset = self.queryset.filter(customer_order_num__id__in=customer_orders, del_flag=False)

        # 非外键关联的列表页数据渲染
        # 新增逻辑：all_parcel_volume包裹总体积过滤
        # 检查 all_parcel_volume 是否为 1 或 2（字符串或整数形式)
        if all_parcel_volume in [1, 2, '1', '2']:
            # 动态计算包裹总体积
            # 构建子查询 total_volume_subquery，用于计算每个货件下所有包裹的实际体积总和

            total_volume_subquery = Parcel.objects.filter(
                shipment_id=OuterRef('shipment_id'),            # 引用外层 OcShipment 的 shipment_id
                customer_order=OuterRef('customer_order_num__id'),  # 引用外层 OcShipment 关联的客户订单 ID
                del_flag=False                                  # 只筛选未被删除的包裹记录
            ).values('shipment_id').annotate(                   # 按 shipment_id 分组
                total=Sum('actual_volume', output_field=DecimalField())  # 计算包裹实际体积总和，并指定输出字段类型为 DecimalField
            ).values('total')                                   # 提取 total 字段供后续使用

            # 将计算字段 temp_total_volume 添加到主查询集 self.queryset 中
            # 使用 Coalesce 处理空值情况（即没有包裹时返回 0）
            self.queryset = self.queryset.annotate(
                temp_total_volume=Coalesce(
                    Subquery(total_volume_subquery, output_field=DecimalField()),  # 执行子查询并指定输出类型
                    Value(0, output_field=DecimalField())       # 如果子查询结果为空，则默认为 0（Decimal 类型）
                )
            )

            # 根据参数 all_parcel_volume 的值进一步过滤查询集
            if all_parcel_volume in [1, '1']:
                # 筛选包裹总体积等于 0 的货件
                self.queryset = self.queryset.filter(temp_total_volume=0)
            elif all_parcel_volume in [2, '2']:
                # 筛选包裹总体积大于 0 的货件
                self.queryset = self.queryset.filter(temp_total_volume__gt=0)

        return super().list(request, *args, **kwargs)

    # # 批量搜索
    # @action(methods=['POST'], detail=False)
    # def multi_search(self, request):
    #     return get_multi_search(self, request, serializer=OcShipmentSerializer)
    #     return success_response(request, "success!")

    # 批量搜索(批量查询)
    @action(methods=['POST'], detail=False)
    def multi_search(self, request):
        # {"multiData":{"truck_order_num":["100000126","342342"]},"filterProp":{"order_status":"WO"}}
        multi_data = request.data.get('multiData', {})
        filter_prop = request.data.get('filterProp', {})

        filter_params = {}
        for key, value in filter_prop.items():
            filter_params.update(param_to_queryset(key, value))

        queryset = self.get_queryset().filter(**filter_params).distinct()
        if multi_data:
            for key, value in multi_data.items():
                if not value:
                    continue
                param = {}
                if key == 'order_num':
                    param['customer_order_num__order_num__in'] = value
                elif key == 'ref_num':
                    param['customer_order_num__ref_num__in'] = value
                elif key == 'ocean_num':
                    param['customer_order_num__ocean_number__in'] = value
                elif key == 'tracking_num':
                    param['customer_order_num__tracking_num__in'] = value
                elif key == 'container_no':
                    relate_order = CustomerOrderRelateOcean.objects.filter(oceanOrder__container_no__in=value,
                                                                           del_flag=False)
                    print('relate_order-->', relate_order, value)
                    customer_orders = relate_order.values_list('customer_order_num__id', flat=True)
                    param['customer_order_num__id__in'] = customer_orders
                else:
                    param[key + '__in'] = value
                queryset = queryset.filter(**param).distinct()

        if settings.SYSTEM_ORDER_MARK in ['CLT']:
            queryset = queryset.filter(~Q(status='VO'))

        count = queryset.count()
        data = OcShipmentSerializer(queryset, many=True).data

        # request.data['count'] = count
        # request.data['data'] = data

        # return success_response(request, "success!")
        return success_response_common(data=data)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_update(self, request):
        """
        批量修改
        """

        ids = request.data['ids']
        user = request.user
        req_data = request.data
        shipment_list = OcShipment.objects.filter(
            id__in=ids,
            del_flag=False,
        )
        if len(shipment_list) != len(ids):
            return fail_response(request, '请选择有效的货件')

        for shipment in shipment_list:
            # 需要更新的字段信息
            modify_data = {}

            if req_data.get('status') and shipment.status != 'VO':
                modify_data['status'] = req_data['status']

            if req_data.get('white_list_time'):
                modify_data['white_list_time'] = req_data['white_list_time']

            # if req_data.get('scheduled_time'):
            #     scheduled_time = req_data['scheduled_time']
            #     validate_appointment_time(datetime.strptime(scheduled_time, "%Y-%m-%dT%H:%M:%S"))
            #     modify_data['scheduled_time'] = scheduled_time
            #
            #     # 货件管理修改预约时间同步修改出库指令的预约时间
            #     shipment.outbound_instruct_shipment_ref.filter(~Q(status='VO'), del_flag=False).update(
            #         appointment_time=scheduled_time)

            if req_data.get('expiration_time'):
                modify_data['expiration_time'] = req_data['expiration_time']

            if req_data.get('shop_type'):
                modify_data['shop_type'] = req_data['shop_type']

            if req_data.get('etaw'):
                modify_data['etaw'] = req_data['etaw']

            if req_data.get('earliest_available_time'):
                modify_data['earliest_available_time'] = req_data['earliest_available_time']

            # 增加操作记录
            OrderFieldChangeLog.record(shipment, modify_data, 'OcShipment', user)

            for key, value in modify_data.items():
                setattr(shipment, key, value)
            shipment.save()

        return success_response(request, 'success')

    # 修改预约时间(客户需求, 不在货件管理改预约时间--弃用)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def modify_scheduled_time(self, request):
        oc_shipment_id = request.data['id']
        scheduled_time = request.data['modifyVal']
        validate_appointment_time(datetime.strptime(scheduled_time, "%Y-%m-%dT%H:%M:%S"))
        shipment = OcShipment.objects.get(id=oc_shipment_id)
        shipment.scheduled_time = scheduled_time
        shipment.save()

        # 货件管理修改预约时间同步修改出库指令的预约时间
        shipment.outbound_instruct_shipment_ref.filter(~Q(status='VO'), del_flag=False).update(
            appointment_time=scheduled_time)

        return success_response_common(msg='修改成功！')

    @action(methods=['POST'], detail=False)
    def batch_new_case(self, request):
        """
        新建工单
        @param request:
        @return:
        """

        request_data = request.data
        case_content = request_data.get('case_content')
        case_type = request_data.get('case_type')
        ids = request_data.get('ids')
        if not ids:
            return fail_response(request, '请选择有效的货件')

        shipment_list = OcShipment.objects.filter(
            id__in=ids,
            del_flag=False,
        )
        if len(shipment_list) != len(ids):
            return fail_response(request, '请选择有效的货件')

        shipment_case_service = ShipmentCaseService()
        shipment_case_service.generate_shipment_case(
            shipment_list=shipment_list,
            case_content=case_content,
            case_type=case_type,
            user=request.user,
        )

        return success_response(request, 'success')

    # 获取下拉字典
    @action(methods=['GET'], detail=False)
    def get_dict(self, request):
        query = request.query_params.get('q')
        if not query:
            # 分页倒序获取前10条数据
            queryset = self.get_queryset().filter(
                ~Q(status='VO'),
            ).order_by('-id')[:10]
            serializer = self.get_serializer(queryset, many=True)
            shipments = serializer.data
        else:
            # 模糊查询
            queryset = self.get_queryset().filter(
                ~Q(status='VO'),
                shipment_id__istartswith=query
            ).order_by('-id')[:100]
            serializer = self.get_serializer(queryset, many=True)
            shipments = serializer.data

        data_list = []
        if shipments:
            for shipment in shipments:
                data_list.append({
                    'shipment_id': shipment.get('shipment_id'),
                    'reference_id': shipment.get('reference_id'),
                    'shop_type': shipment.get('shop_type'),
                    'parcel_qty': shipment.get('parcel_qty'),
                    'value': shipment.get('id'),
                    'label': shipment.get('shipment_id')
                })

        return success_response_common(data=data_list)

    @action(methods=['POST'], detail=False)
    def export_shipment(self, request):

        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError('请选择货件号', ErrorCode.PARAM_ERROR)

        shipments = self.get_queryset().filter(id__in=ids, del_flag=False)

        # 获取数据
        shipment_service = ShipmentExportService()
        data_list = shipment_service.export_shipments(shipments=shipments)

        # 生成excel
        wb = generate_shipment_excel(data_list)
        response = HttpResponse(content_type='application/msexcel')
        sheet_name = f'shipment_{str(int(datetime.now().timestamp()))}'
        response['Content-Disposition'] = 'attachment;filename=%s.xlsx' % sheet_name
        wb.save(response)
        return response


class ExceedTimeStorageChargesViewSetOld(CustomViewBase):
    """
    超期仓租页面old
    """
    queryset = Parcel.objects.all()
    serializer_class = ExceedTimeStorageChargesSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ('shipment_id', 'customer_order__order_num', 'customer_order__ref_num')
    filterset_fields = ('shipment_id', 'customer_order__order_num', 'customer_order__ref_num',
                        'customer_order__ocean_warehouse__address_num', 'customer_order__customer')
    ordering_fields = ('id', )
    authentication_classes = (JSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    def list(self, request, *args, **kwargs):
        old_shipment_id = self.request.query_params.get('old_shipment_id')
        if old_shipment_id:
            oc_shipments = OcShipment.objects.filter(old_shipment_id=old_shipment_id, del_flag=False)
            if oc_shipments.exists():
                self.queryset = self.queryset.filter(shipment_id__in=oc_shipments.values_list('shipment_id', flat=True))
            else:
                self.queryset = self.queryset.none()

        shipment_status = self.request.query_params.get('shipment_status')
        if shipment_status:
            oc_shipments = OcShipment.objects.filter(status=shipment_status, del_flag=False)
            if oc_shipments.exists():
                self.queryset = self.queryset.filter(shipment_id__in=oc_shipments.values_list('shipment_id', flat=True))
            else:
                self.queryset = self.queryset.none()

        airline_short_name = self.request.query_params.get('airline_short_name')
        if airline_short_name:
            customer_order_relate_oceans = CustomerOrderRelateOcean.objects.filter(
                oceanOrder__airline_short_name=airline_short_name,
                del_flag=False)
            self.queryset = self.queryset.filter(
                customer_order__customerOrderRelateList__in=customer_order_relate_oceans)

        outbound_num = self.request.query_params.get('outbound_num')
        if outbound_num:
            oc_shipments = OcShipment.objects.filter(outbound_instruct_shipment_ref__outbound_num=outbound_num,
                                                     del_flag=False)
            if oc_shipments.exists():
                self.queryset = self.queryset.filter(shipment_id__in=oc_shipments.values_list('shipment_id', flat=True))
            else:
                self.queryset = self.queryset.none()

        appointment_time = self.request.query_params.get('$appointment_time')
        if appointment_time:
            oc_shipments = OcShipment.objects.filter(
                outbound_instruct_shipment_ref__appointment_time__range=appointment_time.split(','),
                del_flag=False)
            if oc_shipments.exists():
                self.queryset = self.queryset.filter(shipment_id__in=oc_shipments.values_list('shipment_id', flat=True))
            else:
                self.queryset = self.queryset.none()

        debit_create_date = self.request.query_params.get('$debit_create_date')
        if debit_create_date:
            pass
            # booking_time_parse = parse_custom_date(debit_create_date)[0]
            # booking_time_range = [booking_time_parse, booking_time_parse + timedelta(days=7)]
            # print('booking_time_range-->', booking_time_range)
            # self.queryset = self.queryset.filter(booking_time__range=booking_time_range)
            # debit = Debit.objects.filter(order_num=obj.customer_order.order_num, del_flag=False).first()

        return super().list(request, *args, **kwargs)

    # 批量搜索(批量查询)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def multi_search(self, request):
        outbound_num = request.data.get('multiData', {}).get('outbound_num')
        if outbound_num:
            oc_shipments = OcShipment.objects.filter(outbound_instruct_shipment_ref__outbound_num__in=outbound_num,
                                                     del_flag=False)
            if oc_shipments.exists():
                self.queryset = self.queryset.filter(shipment_id__in=oc_shipments.values_list('shipment_id', flat=True))
            else:
                self.queryset = self.queryset.none()

        customer_order__order_num = request.data.get('multiData', {}).get('customer_order__order_num')
        if customer_order__order_num:
            self.queryset = self.queryset.filter(customer_order__order_num__in=customer_order__order_num)
            print('customer_order__order_num-->', customer_order__order_num, self.queryset)

        shipment_id = request.data.get('multiData', {}).get('shipment_id')
        if shipment_id:
            self.queryset = self.queryset.filter(shipment_id__in=shipment_id)

        old_shipment_id = request.data.get('multiData', {}).get('old_shipment_id')
        if old_shipment_id:
            oc_shipments = OcShipment.objects.filter(old_shipment_id=old_shipment_id, del_flag=False)
            if oc_shipments.exists():
                self.queryset = self.queryset.filter(shipment_id__in=oc_shipments.values_list('shipment_id', flat=True))
            else:
                self.queryset = self.queryset.none()
        # return get_multi_search(self, request, serializer=ExceedTimeStorageChargesSerializer)
        return super().list(request)


class ExceedTimeStorageChargesViewSet(CustomViewBase):
    """
    超期仓租页面
    """
    queryset = ExceedTimeStorageCharges.objects.all()
    serializer_class = ExceedTimeStorageChargesSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ('parcel_num__parcel_num', 'shipment_id', 'order_num', )
    filterset_fields = ('shipment_id', 'order_num', 'ocean_warehouse__address_num', 'customer', 'is_revenue_lock',
                        'airline_short_name', 'shipment_status', 'appointment_time', 'debit_date',
                        'actual_arrived_wh_date')
    ordering_fields = ('id', )
    authentication_classes = (JSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    def list(self, request, *args, **kwargs):
        parcel_volume_is_null = self.request.query_params.get('parcel_volume_is_null')
        logger.info(f'parcel_volume_is_null-->{parcel_volume_is_null}, {type(parcel_volume_is_null)}')
        if parcel_volume_is_null == '1':
            self.queryset = self.queryset.filter(Q(parcel_volume__isnull=True) | Q(parcel_volume=0))
        elif parcel_volume_is_null == '2':
            self.queryset = self.queryset.filter(Q(parcel_volume__isnull=False) & ~Q(parcel_volume=0))
        return super().list(request, *args, **kwargs)

    # 批量搜索(批量查询)
    @action(methods=['POST'], detail=False)
    def multi_search(self, request):
        return get_multi_search(self, request, serializer=ExceedTimeStorageChargesSerializer)

    # 重新计算仓租
    @action(methods=['POST'], detail=False)
    def calc_exceed_time_storage_charges(self, request):
        ids = request.data['ids']
        exceed_time_storage_charges_queryset = ExceedTimeStorageCharges.objects.filter(id__in=ids, del_flag=False)
        for exceed_time_storage_charges in exceed_time_storage_charges_queryset:
            parcel = exceed_time_storage_charges.parcel_num
            # outbound_instruct = OutboundInstruct.objects.filter(outbound_num=exceed_time_storage_charges.outbound_num,
            #                                                     del_flag=False).last()
            calc_exceed_time_storage_charges(parcel)

        return success_response_common('计算成功')

    # 收入确认(生成账单)
    @lock_request
    @action(methods=['POST'], detail=False)
    def revenue_order(self, request):
        ids = request.data['ids']
        queryset = ExceedTimeStorageCharges.objects.filter(id__in=ids, del_flag=False)

        not_qualify = queryset.filter(is_revenue_lock=True)
        if not_qualify.exists():
            error_order_nums = not_qualify.values_list('order_num', flat=True)
            return fail_response_common(msg=f'请选择未进行收入确认的单据, 已确认的单据: {", ".join(error_order_nums)}')

        errors_msg = []
        for current_order in queryset:
            # 增加redis，防止重复执行
            key = f'exceed_time_storage_charges_{current_order.id}'
            try:
                if cache.get(key):
                    continue
                cache.set(key, 'running', timeout=60 * 60)
                storage_charges_revenue_confirm(current_order.id, request.user)

            except ParamError as e:
                logger.error(traceback.format_exc())
                errors_msg.append(str(e))
            finally:
                cache.delete(key)

        if errors_msg:
            msg = f'部分收入确认成功，部分收入确认异常，异常如 {errors_msg}'
        else:
            msg = '收入确认完成，账单生成成功。'
        return success_response_common(msg=msg)

    # 收入解锁
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def order_unlock(self, request):
        ids = request.data['ids']
        queryset = ExceedTimeStorageCharges.objects.filter(id__in=ids, del_flag=False)
        fail_orders = 0
        for current_order in queryset:
            parcel = current_order.parcel_num
            if Debit.objects.filter(order_num=parcel.parcel_num, is_invoiced=True, del_flag=False,
                                    is_adjust=False).exists():
                fail_orders += 1
            else:
                # is_lock_order(customer_order, 'revenue')
                Debit.objects.filter(order_num=parcel.parcel_num, is_adjust=False).update(del_flag=True)
                AccountReceivable.objects.filter(order_num=parcel.parcel_num, is_adjust=False).update(
                    del_flag=True)
                current_order.save_fields(is_revenue_lock=False,
                                          income=None,
                                          account_time=None,
                                          update_by=request.user,
                                          update_date=datetime.now())
        if fail_orders > 0:
            msg = f'部分收入解锁成功！{fail_orders} 个单据解锁失败，请确保收入所开具的账单还未生成发票'
            return success_response(request, msg)
        else:
            return success_response(request, '客户订单收入解锁成功')


class OcShipmentLabelViewSet(CustomViewBase):
    """
    货件操作标签：增删改查
    """
    queryset = OcShipmentLabel.objects.all()
    serializer_class = OcShipmentLabelSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ('=customer_order_num', '=oc_shipment')
    filterset_fields = ('status', 'operate_tag')
    ordering_fields = ('id',)
    authentication_classes = (JSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)
