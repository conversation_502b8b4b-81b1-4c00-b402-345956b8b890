import base64
import io
import json
import os
import time
import traceback
from copy import deepcopy
from datetime import datetime, timedelta
from decimal import Decimal
from shutil import copyfile
import xmltodict

import openpyxl
import pandas as pd
import xlrd
from PyPDF2 import PdfFileMerger
from celery_once import AlreadyQueued
from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.core.files.base import ContentFile
from django.db import transaction
from django.db.models import Q, Sum, Count
from django.forms import model_to_dict
from django.http import HttpResponse
from django.utils.decorators import method_decorator
from django.contrib.auth import authenticate
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font
from rest_framework.decorators import action
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.permissions import IsAuthenticated

from account.views.account import deduction_account
from alita.logger import logger
from alita.settings.base import MEDIA_URL
from common.async_tasks import common_order_async_task
from common.common_const import FBM_TRACK_CODE_LIST
from common.custom import RbacPermission, AlitaJSONWebTokenAuthentication
from common.error import ErrorCode, ParamError
from common.excel_tools import set_data
from common.requestLock import lock_request, lock_request_common
from common.service.account_service import refund_account
from common.service.customer_fbm_order_export_service import CustomerFbmOrderExportService
from common.service.customer_fbm_order_service import CustomerFbmOrderService
from common.service.customer_order_service import save_shiper_address
from common.service.fba_order_import_service import clean_fba_order_before_import
from common.service.fba_order_import_update_service import FBAOrderImportUpdateService
from common.service.fbm_order_import_service import FbmOrderImportService
from common.service.ocean_service import summarize_ocean_data
from common.order_num_gen_rule import create_order_num
from common.service.truck_order_service import TruckOrderService
from common.tools import change_order_status, create_order_reference_id, create_sys_parcel_num, gen_order_num, \
    get_excel_cell, get_multi_search, get_update_params, is_lock_order, judge_parcel_num_rule, order_cost_confirm, \
    order_revenue_confirm, print_fba_label_common, print_truck_label_common, send_file, send_str_email, set_customer_track, \
    set_customer_track_fba, set_parcel_track_info, cost_unlock_common, set_parcel_size_zero, \
    get_track_location_remark, params_query, \
    summary_predict_parcels_data, charge_weight_round, \
    summary_parcels_info, check_order_status, bind_ocean_order_and_customer_order, common_packet_cost_share_cancel, \
    common_packet_cost_share, print_fbm_box_label_common, export_box_order_common, export_inbound_order_common, \
    convert_fbm_track_code_by, \
    validate_fbm_order_status, summary_order_weight_and_size, common_upload_excel, customer_order_restrict, \
    save_crm_price, generate_fbm_order_excel, \
    get_order_status_mapping, order_import_verification_product_code, \
    calc_sales_cost_price, get_order_by_id, get_fbm_order_next_status, get_file_suffix, remove_file, \
    save_api_create_address, copy_customer_order_address, update_order_data_for_cancel_force, \
    update_order_data_for_recovery_order, update_order_data_for_customer_order_intercept, \
    update_order_data_for_cancel_customer_order_intercept, index_to_excel_column
from common.upload_tools import sync_upload_file_common
from common.utils.arr_util import arr_to_str
from common.utils.barcode_gen import create_barcodes_for_order2, create_barcodes_for_order3, openpyxl_to_pdf
from common.utils.custom_viewset_base import CustomViewBase
from common.utils.data_verify import verify_ocean_order_batch_stowage, check_combine_billing_ocean_order
from common.utils.decorators import request_frequency_limit
from common.utils.gen_mode_key_util import gen_moke_key_random
from common.utils.response import *
from company.models import Address, Company, SupplierButt, SupplierButtAccount
from crm.models import OrderCommissionExport
from cs.models import AbnormalFeedback, AbnormalTag, CustomerOrderAbnormal
from info.models import Charge, Dict, TrackCode
from order.integration.customerOrderIntegrationService import create_order_sync_tasks
from order.integration.integrationInterface import LabelOrderVo, cancel_label, confirm_ship
from order.integration.pedderIntergrationService import PedderIntegrationService
from order.integration.util.abstract_func import create_order_label_task, handler_cancel_label
from order.integration.util.commonUtil import convert_dict, DecimalEncoder
from order.models import BigParcel, Clearance, ClearanceOut, CollectOrder, CustomerOrder, CustomerOrderChargeIn, \
    CustomerOrderChargeOut, CustomerOrderRelateOcean, HouseOrder, Insurance, KuaJingBao, MasterOrder, OceanOrder, \
    OrderAttachment, OrderLabel, OrderLabelTask, Parcel, ParcelCustomerOrder, ParcelItem, ParcelOutboundOrder, \
    ParcelSize, Track, TruckOrder, OrderSyncTask, OcShipment, OutboundInstruct, \
    OrderFieldChangeLog, CombineBillingOrder, CustomerOrderRelateTruck
from order.serializers.customer_order import CustomerOrderAndDetatilSerializer, CustomerOrderSerializer, \
    handler_create_order, calc_bubble_and_charge_weight, CustomerOrderChargeInSerializer, \
    CustomerOrderChargeOutSerializer, handler_update_order
from order.serializers.parcel_customer_order import add_cost, add_revenue
from order.serializers.track import TrackSerializer
from common.utils.customer_order_utils import cancel_order_common, sync_cancel_intercept_data, \
    sync_complete_warehousing_data
from common.utils.amazon_ship_track_response_util import gene_xml_res_data, gene_xml_un_authorized_res_data, \
    gene_xml_invalid_tracking_num, gene_xml_invalid_api_version, gene_xml_root, gene_xml_service_not_available, \
    gene_xml_not_valid, gene_xml_not_well_formed, assemble_amazon_ship_track_data_dict
from order.utils.customer_order_track_utils import save_node_address
from order.utils.data_check import check_config_combine_billing_order
from order.utils.outboundinstruct_utils import update_oc_shipment_outbound_instruct_data
from order.utils.truck_order_utils import batch_create_truck_order, batch_create_truck_order_get_ocean_order
from pms.models import Product, ProductTrackCode, Service, ProductZone, ProductRoute
from pms.serializers.product import ProductTrackCodeSerializer
from pms.util.calc import DistCalcVo, OrderCalcVo, common_judge_fba_and_remote, check_customer_order_shipment_id
from pms.util.calc_tz import handler_calc_weight
from pms.util.revenue_calc import revenue_calc
from rbac.models import UserProfile
from settle.models import AccountReceivable, Debit
from apps.order.tasks import sync_customer_order_task, sync_modify_remark_to_mz
from order.integration.util.customerOrderUtil import CreateCustomerOrder, CreateParcel, request_server
from task.tasks import async_billing_customer_order, fbm_customer_order_upload_handle, fba_customer_order_upload_handle, \
    fba_customer_order_single_upload_handle

from rest_framework_xml.parsers import XMLParser
from rest_framework_xml.renderers import XMLRenderer


class CustomerOrderViewSet(CustomViewBase):
    """
    客户订单管理：增删改查
    """
    # 针对外键的表进行预加载
    queryset = CustomerOrder.objects.all().select_related('customer', 'product', 'shipper', 'receiver')
    # 针对1对多和多对多的预加载
    queryset = queryset.prefetch_related('orderLabelTasks', 'orderSyncTasks', 'truck_order_id')

    serializer_class = CustomerOrderSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = (
        'order_num', 'ref_num', '=parcel__parcel_num', '=airline_num', '=destination', '=ref_num',
        '=third_orderNo', '=saler', '=ocean_number'
        # 查询内容太多导致查询时间长, 目前就只查这些保证效率
        # '=parcel__tracking_num', '=master_num__order_num', '=ocean_num__order_num', '=house_num__order_num',
        # '=master_number', '=receiver__address_num', '=collect_num__order_num', '=parcel__reference_id',
        # '=parcel__shipment_id'
    )
    filterset_fields = (
        'order_status', 'customer', 'shipper', 'receiver', 'master_num', 'house_num', 'is_send_debit',
        'is_revenue_lock', 'is_cost_lock', 'is_arrival_notice', 'is_customs_declaration', 'order_type',
        'saler', 'warehouse_type', 'logistics_planning', 'order_num', 'ref_num', 'collect_num__order_num',
        'ocean_number', 'third_orderNo', 'buyer_address_num', 'custom_clearance', 'create_by',
        'shipment_customer_order_ref__shipment_id'
    )
    ordering_fields = ('id',)
    authentication_classes = (AlitaJSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    # renderer_classes = [XMLRenderer]
    # parser_classes = [XMLParser]

    def get_queryset(self):
        # filterset_fields 中的 order_status 改到这里筛选, 适用于CLT全部数据中不显示作废数据
        if self.action == "list":
            order_status = self.request.query_params.get('order_status')
            if settings.SYSTEM_ORDER_MARK in ['CLT'] and not order_status:
                self.queryset = self.queryset.filter(~Q(order_status='VO'))

        # 根据部门查看部门数据
        user = self.request.user
        department = user.department
        if not user.is_superuser and department:
            if department.relate_customer:
                customer_arr = department.relate_customer.split(',')
            else:
                customer_arr = []
            self.queryset = self.queryset.filter(customer__in=list(map(int, customer_arr)))
        return super().get_queryset()

    def get_serializer_class(self):
        if self.action == "list":
            return CustomerOrderSerializer
        return CustomerOrderAndDetatilSerializer

    @extend_schema(
        summary="运输单-列表",
        description='order_type: TR 运输单；',
        tags=["运营端-运输"],
    )
    def list(self, request, *args, **kwargs):
        not_status_param = request.query_params.get('not_status')
        is_config_order_param = request.query_params.get('is_config_order')
        saler_id = request.query_params.get('saler')
        tags_order_status = request.query_params.get('tags_order_status')
        exclude_status = request.query_params.get('exclude_status')
        is_sales_price_card = request.query_params.get('is_sales_price_card')
        is_intercept = request.query_params.get('is_intercept')
        ocean_num = request.query_params.get('ocean_num')
        # first_ocean_num = request.query_params.get('ocean_num__order_num')
        is_sync_yqf = request.query_params.get('is_sync_yqf')
        receiver_name = request.query_params.get('receiver_name')
        container_no = request.query_params.get('container_no')
        reference_id = request.query_params.get('reference_id')
        abnormal_type = request.query_params.get('abnormal_type')
        abnormal_tags = request.query_params.get('abnormal_tags')

        if ocean_num:
            relate_order = CustomerOrderRelateOcean.objects.filter(oceanOrder__order_num=ocean_num, del_flag=False)
            customer_orders = relate_order.values_list('customer_order_num__id', flat=True)
            self.queryset = self.queryset.filter(id__in=customer_orders, del_flag=False)

        if container_no:
            relate_order = CustomerOrderRelateOcean.objects.filter(oceanOrder__container_no=container_no,
                                                                   del_flag=False)
            customer_orders = relate_order.values_list('customer_order_num__id', flat=True)
            self.queryset = self.queryset.filter(id__in=customer_orders, del_flag=False)

        # if first_ocean_num:
        #     self.queryset = self.queryset.filter(ocean_num__order_num=first_ocean_num, del_flag=False)

        if exclude_status and not request.query_params.get('order_status') and not tags_order_status:
            self.queryset = self.queryset.exclude(order_status=exclude_status)

        if is_sync_yqf == 'UnSync':
            self.queryset = self.queryset.filter(~Q(id__in=OrderSyncTask.objects.values('order_num_id')))
        elif is_sync_yqf in ['UnHandled', 'Success', 'Failure', 'VO']:
            self.queryset = self.queryset.filter(orderSyncTasks__task_type='PUSH_ORDER',
                                                 orderSyncTasks__status=is_sync_yqf,
                                                 del_flag=False)
        if reference_id:
            relate_parcel_id_list = Parcel.objects.filter(
                reference_id=reference_id,
                del_flag=False
            ).values_list('customer_order__id', flat=True)
            self.queryset = self.queryset.filter(id__in=relate_parcel_id_list, del_flag=False)

        user = request.user
        # queryset = self.queryset

        # tangus需求，暂时先注释
        # if not_status_param:
        #     self.queryset = self.queryset.exclude(order_status='FC')

        if is_config_order_param:
            is_config_order = is_config_order_param.lower() == 'true'
            if is_config_order:
                conditions = Q(ocean_num__isnull=not is_config_order) | \
                             Q(ocean_number__isnull=not is_config_order) | \
                             Q(master_num__isnull=not is_config_order) | \
                             Q(master_number__isnull=not is_config_order)
            else:
                conditions = Q(ocean_num__isnull=not is_config_order) & \
                             Q(ocean_number__isnull=not is_config_order) & \
                             Q(master_num__isnull=not is_config_order) & \
                             Q(master_number__isnull=not is_config_order)
            self.queryset = self.queryset.filter(conditions)

        if user.post == '销售' and not saler_id:
            self.queryset = self.queryset.filter(saler=user.name)

        # elif saler_id:
        #     user_profile = UserProfile.objects.filter(id=saler_id).first()
        #     if user_profile:
        #         self.queryset = self.queryset.filter(saler=user_profile.name)

        # 产品 支持多选查询
        # 这里删除了 filterset_fields 中的product字段，因为会优先走filterset_fields的查询
        # 或者在前段修改提交的参数，如：products
        products = request.query_params.get('product')
        if products is not None and products != '':
            product_ids = [int(x) for x in products.split(',') if x.strip()]
            self.queryset = self.queryset.filter(product_id__in=product_ids)

        address_nums = request.query_params.get('address_num')
        if address_nums is not None and address_nums != '':
            address_ids = [int(x) for x in address_nums.split(',') if x.strip()]
            self.queryset = self.queryset.filter(receiver_id__in=address_ids)

        # 接受tags的查询
        if tags_order_status:
            self.queryset = self.queryset.filter(order_status=tags_order_status)

        query_map = {}
        if is_sales_price_card:
            if is_sales_price_card.lower() == 'true':
                query_map['sales_price_card_time__isnull'] = False
                # self.queryset = self.queryset.filter(sales_price_card_time__isnull=False)
            elif is_sales_price_card.lower() == 'false':
                query_map['sales_price_card_time__isnull'] = True
                # self.queryset = self.queryset.filter(sales_price_card_time__isnull=True)

        if is_intercept:
            if is_intercept.lower() == 'true':
                query_map['is_intercept'] = True
            elif is_intercept.lower() == 'false':
                query_map['is_intercept'] = False

        if receiver_name:
            self.queryset = self.queryset.filter(
                Q(receiver__address_num=receiver_name) | Q(buyer_address_num=receiver_name))

        if abnormal_type is not None:
            abnormal_feedbacks = AbnormalFeedback.objects.filter(
                abnormal_type__type_name=abnormal_type,
                del_flag=False,
            ).order_by('-id')[:1000]
            if not abnormal_feedbacks:
                return success_response(request, "success!")

            customer_orders = abnormal_feedbacks.values_list('customer_order_num__id', flat=True)
            self.queryset = self.queryset.filter(customer_order_num__id__in=customer_orders, del_flag=False)

        if abnormal_tags is not None:
            abnormal_feedbacks = AbnormalFeedback.objects.filter(
                abnormal_tag__type_name=abnormal_tags,
                del_flag=False,
            ).order_by('-id')[:1000]
            if not abnormal_feedbacks:
                return success_response(request, "success!")

            customer_orders = abnormal_feedbacks.values_list('customer_order_num__id', flat=True)
            self.queryset = self.queryset.filter(customer_order_num__id__in=customer_orders, del_flag=False)
        logger.info(f'query_map-->, {query_map}')
        self.queryset = self.queryset.filter(**query_map)

        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        if not request.user.is_staff and request.user.company is not None and self.get_object().customer != request.user.company:
            # 客户
            raise ParamError('您无权查看当前信息详情', ErrorCode.PARAM_ERROR)
        return super().retrieve(request, *args, **kwargs)

    # FBA订单轨迹查询(FBA列表页轨迹查询)
    @action(methods=['POST'], detail=False)
    def get_customer_order_track(self, request):
        order_id = request.data.get('id')
        if order_id is None:
            return fail_response_common(msg='没有订单id')

        order = CustomerOrder.objects.filter(id=order_id, del_flag=False).last()
        if not order:
            return fail_response_common(msg=f'未查询到订单, id: {order_id}')

        tracks = Track.objects.filter(order_num=order.order_num, del_flag=False).order_by('-actual_time')

        if not tracks.exists():
            return fail_response_common(msg='订单无轨迹数据')

        if order.receiver:
            receiver_name = order.receiver.address_num
        else:
            receiver_name = order.buyer_address_num

        data = {
            'id': order.id,
            'order_status': order.order_status,
            'order_type': order.order_type,
            'order_num': order.order_num,
            'ref_num': order.ref_num,
            'country_code': order.country_code,
            'buyer_country_code': order.buyer_country_code,
            'product_name': order.product.name,
            'receiver_name': receiver_name,
            'buyer_postcode': order.buyer_postcode,
            'carton': order.carton,
            'tracks': TrackSerializer(tracks, many=True).data,
        }

        return success_response_common(data=data)

    # FBA订单费用查询(FBA列表页费用快捷录入)
    @action(methods=['POST'], detail=False)
    def get_customer_order_charge(self, request):
        order_id = request.data.get('id')
        if order_id is None:
            return fail_response_common(msg='没有订单id')

        charge_ins = CustomerOrderChargeIn.objects.filter(customer_order_num_id=order_id, del_flag=False)
        charge_outs = CustomerOrderChargeOut.objects.filter(customer_order_num_id=order_id, del_flag=False)

        order = CustomerOrder.objects.filter(id=order_id, del_flag=False).last()
        if not order:
            return fail_response_common(msg=f'未查询到订单, id: {order_id}')

        data = {
            'customerOrderChargeIns': CustomerOrderChargeInSerializer(charge_ins, many=True).data,
            'customerOrderChargeOuts': CustomerOrderChargeOutSerializer(charge_outs, many=True,
                                                                        context={'request': request}).data,
        }

        return success_response_common(data=data)

    # 导出订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def export_order(self, request):
        # target_url = 'https://bi.aliyuncs.com/token3rd/offline/view/pc.htm?pageId=6e0b7d7b-2ada-4236-b719-c7baf5c8e59f&accessToken=5b334a5b674f215cdc2a48802bdf5b36'
        target_url = 'https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=abd2b1cf-22c1-405d-b7fa-f19acb55680a&accessToken=********************************&dd_orientation=auto'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # 导出客户确认
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def export_client_confirmation(self, request):
        # target_url = 'https://bi.aliyuncs.com/token3rd/offline/view/pc.htm?pageId=b8295a00-ca99-43a3-b873-65c93ef42c66&accessToken=b588e0b487946dbb838a9cc759f275fb'
        target_url = 'https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=412ac946-3323-4d0f-a850-69b998d6ed1d&accessToken=044bd16b49576fa51991703cfd1250d6&dd_orientation=auto'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # 导出打单数据
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def export_order_data(self, request):
        # target_url = 'https://bi.aliyuncs.com/token3rd/offline/view/pc.htm?pageId=f20f2ae7-f00c-43a8-9822-539ceda382cc&accessToken=95be29fb644dc0b3e6fbbb2366a75235'
        target_url = 'https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=1516cf2f-9a65-4df5-ab96-ecfe1e3f54fb&accessToken=a71779b26973cfd3eaa8369771fcc6f7&dd_orientation=auto'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # 导出 转单号
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def export_tracking_data(self, request):
        # quick bi 高级版中没有自助取数，这里替换成仪表盘
        # 自助取数
        # target_url = 'https://bi.aliyuncs.com/token3rd/offline/view/pc.htm?pageId=09662834-e32d-46c8-ac54-2cb523238af9&accessToken=9c3aa7db450bb7a846e2097f6ed67947'
        # 仪表板
        target_url = 'https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=033609c3-a031-4627-ad5a-6bfa10b4539b&accessToken=d93843acfdb1cada3d9a2459ece71029&dd_orientation=auto'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # 导出 预录单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def export_pre_recorded_order(self, request):
        target_url = 'https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=7d788b75-f2e7-49d4-aeae-19dee20972a6&accessToken=5b9d5b6e6cba2093be6b712223811ef1&dd_orientation=auto'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # 导出 销售BI表
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def export_saler_data(self, request):
        target_url = 'https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=42badf32-7f2c-4a63-a47f-49c15f470152&accessToken=2bd67e68a4d1d3c5b6a3105f65ca72e9&dd_orientation=auto'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # 配仓数据汇总(配载BI)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_ocean_bi(self, request):
        target_url = 'https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=ce6d122a-ef9f-4793-8775-b2c3194c6925&accessToken=11339ede0247a2e7669c1cfaa2a3c8c9&dd_orientation=auto'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # 配仓单导出数据
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_ocean_export(self, request):
        target_url = 'https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=91376e38-c35d-47d8-863c-dae899db947d&accessToken=a53490d332b2b5db6b25a7c49199e5f7&dd_orientation=auto'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # 导出 FBA订单各仓时效
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def export_timeliness_warehouse_fba(self, request):
        target_url = 'https://bi.aliyuncs.com/token3rd/offline/view/pc.htm?pageId=31bdf72d-e50e-4af5-8574-32f258f26d1f&accessToken=4779fd7a83b41c5ded30b09c0e5a4f86'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # 导出 小包时效
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def export_timeliness_warehouse_parcel(self, request):
        target_url = 'https://bi.aliyuncs.com/token3rd/offline/view/pc.htm?pageId=8a50f214-fe65-4af4-ad71-549959d18e59&accessToken=1fb95ab2062b53ad2bee1cf3e95903aa'
        return Response(data={"url": target_url, "code": 200}, status=status.HTTP_200_OK)

    # # 修改备注
    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def modify_remark(self, request):
    #     id = request.data['id']
    #     remark = request.data['modifyVal']
    #     CustomerOrder.objects.filter(id=id).update(remark=remark)
    #     data = {'msg': '修改成功！', 'code': 200}
    #     return Response(data=data, status=status.HTTP_200_OK)

    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def test1(self, request):
    #     product_id = 1
    #     result = check_purpose_code_is_remote(product, customer_order.receiver)

    # 修改转单号
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def modify_tracking_num(self, request):
        id = request.data['id']
        remark = request.data['modifyVal']
        CustomerOrder.objects.filter(id=id).update(tracking_num=remark, update_by=request.user,
                                                   update_date=datetime.now())
        data = {'msg': '修改成功！', 'code': 200}
        return Response(data=data, status=status.HTTP_200_OK)

    # 配置空运主单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_master_num(self, request):
        ids = request.data['ids']
        masterorder = request.data.get('selectData', [])
        queryset = CustomerOrder.objects.filter(id__in=ids)
        # 获取主单完成的主单数量
        FC_queryset = queryset.filter(master_num__isnull=False)
        FC_num = 0
        for item in FC_queryset:
            if item.order_status == 'FC' or item.order_status == 'VO':
                FC_num += 1
        if FC_num != 0:
            return fail_response(request, '请检查所选客户订单所关联空运主单的状态是未完成的状态')
        intercept_num = 0
        for item in queryset:
            if item.is_intercept:
                intercept_num += 1
        if intercept_num != 0:
            return fail_response(request, '请检查所有订单都未被被拦截')
        if len(masterorder) == 0:
            modify_data = {'master_num': None}
            for order in queryset:
                OrderFieldChangeLog.record(order, modify_data, 'FBA', request.user)
            queryset.update(master_num=None, airline_num=None, master_number=None)
            return success_response(request, '取消与空运主单关联成功')
        else:
            modify_data = {'master_num': masterorder[0]['id']}
            for order in queryset:
                OrderFieldChangeLog.record(order, modify_data, 'FBA', request.user)
            master_order = MasterOrder.objects.get(id=masterorder[0]['id'])
            queryset.update(master_num=master_order, airline_num=master_order.airline_num,
                            actual_leave_date=master_order.actual_leave_date, master_number=master_order.order_num,
                            actual_arrivals_date=master_order.actual_arrivals_date, departure=master_order.departure,
                            destination=master_order.destination, **get_update_params(request))

            # 给客户单插入一条 '集货仓收货' 轨迹
            # for customer_order in queryset:
            #     set_customer_track([customer_order.id], 'AA')

            return success_response(request, '配置成功')

    # 配置海运提单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_ocean_num(self, request):
        user = request.user
        ids = request.data['ids']
        is_part_set = request.data['is_part_set']
        oceanorders = request.data.get('selectData', [])
        is_rebinding = request.data.get('is_rebinding', False)
        is_open_first_track = Dict.objects.filter(label='OpenFirstTrack', value='1', del_flag=False)

        if not ids:
            raise ParamError('请选择要配载的订单', ErrorCode.PARAM_ERROR)

        if not oceanorders:
            raise ParamError('请选择要配载的海运提单', ErrorCode.PARAM_ERROR)

        # 是否部分配载
        if is_part_set:
            if len(ids) > 1:
                raise ParamError('部分配载不能选择多个订单配载，只能选择一个订单', ErrorCode.PARAM_ERROR)
            customer_order_queryset = CustomerOrder.objects.filter(id=ids[0], del_flag=False)
            customer_order = customer_order_queryset.first()

            verify_ocean_order_batch_stowage(customer_order)
            check_combine_billing_ocean_order(customer_order)
            if customer_order.is_intercept:
                raise ParamError(f'被拦截订单{customer_order.order_num}不能配载', ErrorCode.PARAM_ERROR)
            if customer_order.order_status == 'VO':
                raise ParamError(f'已作废订单{customer_order.order_num}不能配载', ErrorCode.PARAM_ERROR)

            # 查询订单对应的海运单是否存在, 若在relate_order中, 则统计总配载件数不加上这个海运单的已配载件数, 后续会更新这个海运单的配载件数
            relate_order = CustomerOrderRelateOcean.objects.filter(customer_order_num=customer_order, del_flag=False)
            ocean_order_ids = relate_order.values_list('oceanOrder_id', flat=True)
            # 使用客户进口商的订单不允许配载到BCO的海运提单中
            ocean_orders = OceanOrder.objects.filter(id__in=ocean_order_ids, del_flag=False)
            if customer_order.is_importer_clearance and ocean_orders.filter(is_bco=True).exists():
                error_ocean_orders = ocean_orders.filter(is_bco=True).values_list('order_num', flat=True)
                raise ParamError(f'订单使用进口商清关, 不允许配载到BCO海运提单中, 订单: {customer_order.order_num}, '
                                 f'提单: {", ".join(error_ocean_orders)}', ErrorCode.PARAM_ERROR)

            select_oceanorder_ids = [i.get('id') for i in oceanorders]
            under_stowage = total_freight_num = 0
            under_stowage_count = relate_order.filter(~Q(oceanOrder_id__in=select_oceanorder_ids)
                                                      ).aggregate(total=Sum('freight_num'))['total'] or 0
            for oceanorder in oceanorders:
                print('ocean_order_ids-->', oceanorder.get('id'), ocean_order_ids)
                total_freight_num += int(oceanorder.get('freight_num', 0))
                # if int(oceanorder.get('id')) not in ocean_order_ids:
                #     under_stowage += int(oceanorder.get('freight_num', 0))
            if total_freight_num == 0:
                raise ParamError(f'配载件数要大于0', ErrorCode.PARAM_ERROR)
            if total_freight_num > (customer_order.carton or 0):
                raise ParamError(f'配载件数不能超过整单件数', ErrorCode.PARAM_ERROR)
            # total_obj = relate_order.aggregate(sum_freight_num=Sum('freight_num'))
            # print('wen-->', total_freight_num, total_obj['sum_freight_num'])
            # if (under_stowage + (total_obj['sum_freight_num'] or 0)) > (customer_order.carton or 0):
            print('under_stowage_count-->', under_stowage_count, total_freight_num)
            if (under_stowage_count + total_freight_num) > (customer_order.carton or 0):
                raise ParamError(f'配载件数+已配载数量不能超过整单件数', ErrorCode.PARAM_ERROR)

            # 默认取第一个海运提单的信息更新到订单
            oceanorder = oceanorders[0]
            ocean_order = OceanOrder.objects.get(id=oceanorder['id'])
            ocean_info = {}
            # 更新ETD和ETA时间
            # if is_open_first_track.exists():
            ocean_info = {
                'expected_leave_date': ocean_order.estimated_time_departure,
                'expected_arrivals_date': ocean_order.estimated_time_arrival
            }
            update_order_params = {
                # 配海运提单改为新增多对多关系了, 不需要用外键, 配优先轨迹海运单还在用外键
                # 'ocean_num': ocean_order,
                'ocean_number': ocean_order.order_num,
                'departure': ocean_order.departure,
                'destination': ocean_order.destination,
                # 'actual_leave_date': ocean_order.actual_leave_date,
                # 'actual_arrivals_date': ocean_order.actual_arrivals_date,
                **ocean_info
            }
            modify_data = {'ocean_number': ocean_order.order_num}
            OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)
            customer_order_queryset.update(**update_order_params, **get_update_params(request))

            # 配载
            for oceanorder in oceanorders:
                ocean_order = OceanOrder.objects.get(id=oceanorder['id'])
                freight_num = int(oceanorder.get('freight_num', 0))
                # 只要有订单配载了，就是配载中状态 ==> 如果是第一件 就更改状态
                customer_set = CustomerOrderRelateOcean.objects.filter(oceanOrder=ocean_order, del_flag=False).count()
                if customer_set == 0:
                    ocean_order.order_status = 'LOA'
                    ocean_order.save()

                # 添加配载数量
                relate_ocean_queryset = CustomerOrderRelateOcean.objects.filter(customer_order_num=customer_order,
                                                                                oceanOrder=ocean_order, del_flag=False)
                if relate_ocean_queryset:
                    customerOrderRelateOcean = relate_ocean_queryset[0]
                    # customerOrderRelateOcean.freight_num += freight_num
                    customerOrderRelateOcean.freight_num = freight_num
                else:
                    customerOrderRelateOcean = CustomerOrderRelateOcean()
                    customerOrderRelateOcean.customer_order_num = customer_order
                    customerOrderRelateOcean.oceanOrder = ocean_order
                    customerOrderRelateOcean.freight_num = freight_num

                customerOrderRelateOcean.create_by = user
                customerOrderRelateOcean.create_date = datetime.now()
                customerOrderRelateOcean.save()
                customer_order.update_date = datetime.now()
                customer_order.save()

                summarize_ocean_data(ocean_order)
                logger.info(f'部分配载成功, 海运单号: {ocean_order.order_num}, 订单号: {customer_order.order_num}, '
                            f'订单件数: {customer_order.carton}, 配载件数: {freight_num}')

        else:
            if len(oceanorders) > 1:
                return fail_response(request, '整单配载只能配载一个海运单')
            customer_order_queryset = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
            if not oceanorders:
                # 软删订单关联的真实轨迹海运单
                for customer_order in customer_order_queryset:
                    incidence_relation = CustomerOrderRelateOcean.objects.filter(customer_order_num=customer_order,
                                                                                 del_flag=False)
                    modify_data = {'ocean_number': None}
                    print('customer_order.ocean_number-->', customer_order.ocean_number)
                    OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

                    # customer_order.ocean_number = None
                    customer_order.save()
                    for relation in incidence_relation:
                        relation.oceanOrder.carton = 0
                        relation.oceanOrder.order_status = 'WO'
                        relation.oceanOrder.save()
                        if relation.oceanOrder.is_first is False:
                            relation.del_flag = True
                            relation.save()
                return success_response(request, '取消订单配置的海运提单成功')

            oceanorder = oceanorders[0]
            ocean_order = OceanOrder.objects.get(id=oceanorder['id'])

            # 使用客户进口商的订单不允许配载到BCO的海运提单中
            if customer_order_queryset.filter(is_importer_clearance=True) and ocean_order.is_bco:
                error_customer_orders = customer_order_queryset.filter(
                    is_importer_clearance=True).values_list('order_num', flat=True)
                raise ParamError(
                    f'订单使用进口商清关, 不允许配载到BCO海运提单中, 订单: {", ".join(error_customer_orders)}, '
                    f'提单: {ocean_order.order_num}', ErrorCode.PARAM_ERROR)

            # 是判断一下，哪些状态才可以配载？
            out_warehouse_num = 0
            for customer_order in customer_order_queryset:
                if customer_order.order_status == 'VO':
                    raise ParamError(f'已作废订单{customer_order.order_num}不能配载', ErrorCode.PARAM_ERROR)
                if customer_order.is_intercept:
                    raise ParamError(f'被拦截订单{customer_order.order_num}不能配载', ErrorCode.PARAM_ERROR)
                # 允许订单件数为0, 预配载
                # if not customer_order.carton:
                #     raise ParamError(f'订单{customer_order.order_num}还没有件数, 不能配载, 请先做大包签入',
                #                      ErrorCode.PARAM_ERROR)
                check_combine_billing_ocean_order(customer_order)

                relate_ocean_queryset = CustomerOrderRelateOcean.objects.filter(customer_order_num=customer_order,
                                                                                del_flag=False)
                if relate_ocean_queryset.count() > 1:
                    raise ParamError(f'订单{customer_order.order_num} 已部分配载，不能全部配载', ErrorCode.PARAM_ERROR)

                if relate_ocean_queryset.count() == 1:
                    relate_ocean = relate_ocean_queryset.first()
                    if relate_ocean.oceanOrder != ocean_order:
                        if is_rebinding:
                            out_warehouse_num = relate_ocean.out_warehouse_num
                            relate_ocean_queryset.update(del_flag=True, **get_update_params(request))
                        else:
                            raise ParamError(
                                f'订单{customer_order.order_num} 已配载{relate_ocean.oceanOrder.order_num}，'
                                f'未勾选重新绑定不能再全部配载', ErrorCode.PARAM_ERROR)
                    else:
                        continue
                # 只要有订单配载了，就是配载中状态 ==> 如果是第一件 就更改状态
                if relate_ocean_queryset.count() == 0:
                    ocean_order.order_status = 'LOA'
                    ocean_order.save()
                ocean_info = {}
                # 更新ETD和ETA时间
                # if is_open_first_track.exists():
                ocean_info = {
                    'expected_leave_date': ocean_order.estimated_time_departure,
                    'expected_arrivals_date': ocean_order.estimated_time_arrival
                }
                update_order_params = {
                    # 'ocean_num': ocean_order,
                    'ocean_number': ocean_order.order_num,
                    'departure': ocean_order.departure,
                    'destination': ocean_order.destination,
                    # 'actual_leave_date': ocean_order.actual_leave_date,
                    # 'actual_arrivals_date': ocean_order.actual_arrivals_date,
                    **ocean_info
                }
                # 将海运单的数据更新到订单里
                modify_data = {'ocean_number': ocean_order.order_num}
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)
                customer_order.update_date = datetime.now()
                customer_order.save()
                orders = CustomerOrder.objects.filter(id=customer_order.id, del_flag=False)
                orders.update(**update_order_params, **get_update_params(request))

                customerOrderRelateOcean = CustomerOrderRelateOcean()
                customerOrderRelateOcean.customer_order_num = customer_order
                customerOrderRelateOcean.oceanOrder = ocean_order
                customerOrderRelateOcean.freight_num = customer_order.carton or 0
                # 继承之前绑定海运单的已出仓件数
                customerOrderRelateOcean.out_warehouse_num = out_warehouse_num
                if out_warehouse_num > 0:
                    ocean_order.order_status = 'OUS'
                    ocean_order.save()
                customerOrderRelateOcean.create_by = user
                customerOrderRelateOcean.create_date = datetime.now()
                customerOrderRelateOcean.save()

                logger.info(f'全部配载成功, 海运单号: {ocean_order.order_num}, 订单号: {customer_order.order_num}, '
                            f'配载件数: {customer_order.carton}')

            summarize_ocean_data(ocean_order)

        # 校验配载海运提单, 打异常标签
        verify_set_ocean_num(request, ids, oceanorders)

        return success_response(request, '配置成功')

    # 配置海运优先提单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_ocean_first(self, request):
        user = request.user
        ids = request.data['ids']

        customer_order_queryset = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        # 获取主单完成的主单数量
        FC_queryset = customer_order_queryset.filter(ocean_num__isnull=False)
        FC_num = 0
        for item in FC_queryset:
            if item.order_status == 'FC' or item.order_status == 'VO':
                FC_num += 1
        if FC_num != 0:
            return fail_response(request, '请检查所选客户订单所关联海运提单的状态是未完成的状态')

        oceanorders = request.data.get('selectData', [])
        # 取消与海运优先的关联
        if len(oceanorders) == 0:
            modify_data = {'ocean_num': None}
            for customer_order in customer_order_queryset:
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)
            customer_order_queryset.update(ocean_num=None, ocean_number=None, vessel=None,
                                           voyage_num=None, first_track=False)
            return success_response(request, '取消与海运优先的关联成功')

        oceanorder = oceanorders[0]
        freight_num = oceanorder.get('freight_num', None)
        if not freight_num and len(oceanorders) > 1:
            return fail_response(request, '一个订单不能配载多个海运优先单')

        ocean_order = OceanOrder.objects.get(id=oceanorder['id'])
        modify_data = {'ocean_num': ocean_order}
        for customer_order in customer_order_queryset:
            OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)
        # 进行绑定
        bind_ocean_order_and_customer_order(ocean_order, customer_order_queryset, user)

        return success_response(request, '配置成功')

    # 配置分单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_house_num(self, request):
        ids = request.data['ids']
        houseorder = request.data.get('selectData', [])
        queryset = CustomerOrder.objects.filter(id__in=ids)
        intercept_num = 0

        for item in queryset:
            if item.is_intercept:
                intercept_num += 1
        if intercept_num != 0:
            return fail_response(request, '请检查所有订单都未被被拦截')
        if len(houseorder) == 0:
            modify_data = {'house_num': None}
            # 修改前添加操作记录
            for customer_order in queryset:
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

            queryset.update(house_num=None, **get_update_params(request))
            msg = '取消与分单关联成功'
        else:
            house_order = HouseOrder.objects.get(id=houseorder[0]['id'])
            modify_data = {
                'house_num': house_order,
                'airline_num': house_order.airline_num,
                'actual_leave_date': house_order.actual_leave_date,
                'actual_arrivals_date': house_order.actual_arrivals_date,
                'departure': house_order.departure,
                'destination': house_order.destination
            }
            # 修改前添加操作记录
            for customer_order in queryset:
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

            queryset.update(**modify_data, **get_update_params(request))
            msg = '配置成功'

        return success_response(request, msg)

    # 配置卡派单(配置卡车单)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_truck_num(self, request):
        """
        配置卡派单(配置卡车单) - 支持部分配载和全部配载

        校验：
        - 忽略拦截订单，作废订单
        - 部分配载时校验配载总件数不超过订单件数
        - 校验已配载件数+新配载件数不超过订单总件数

        :param request: 请求数据包含ids(订单IDs), selectData(选择的卡派单), is_part_set(是否部分配载)
        :return: 处理结果
        """
        user = request.user
        ids = request.data['ids']
        selected_truck_orders = request.data.get('selectData', [])
        is_part_set = request.data.get('is_part_set')
        is_rebinding = request.data.get('is_rebinding', False)

        # 检查订单状态 - 过滤掉已拦截和作废订单
        customer_order_queryset = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        for customer_order in customer_order_queryset:
            if customer_order.is_intercept:
                raise ParamError(f'被拦截订单{customer_order.order_num}不能配载', ErrorCode.PARAM_ERROR)
            if customer_order.order_status == 'VO':
                raise ParamError(f'已作废订单{customer_order.order_num}不能配载', ErrorCode.PARAM_ERROR)

        # 取消关联 - 如果没有选择卡派单，则解除关联
        if len(selected_truck_orders) == 0:
            for customer_order in customer_order_queryset:
                # 记录字段变更
                modify_data = {'truck_order_id': None}
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

                # 删除关联记录
                relate_trucks = CustomerOrderRelateTruck.objects.filter(
                    customer_order_num=customer_order, del_flag=False)
                relate_trucks.update(del_flag=True, **get_update_params(request))

            # 更新客户订单的卡派单字段
            customer_order_queryset.update(truck_order_id=None, **get_update_params(request))
            return success_response(request, '取消订单配置的卡派单成功')

        # 检查选择的卡派单是否存在作废状态
        selected_truck_order_ids = [truck['id'] for truck in selected_truck_orders]
        selected_truck_order_qs = TruckOrder.objects.filter(
            id__in=selected_truck_order_ids, del_flag=False)

        if selected_truck_order_qs.filter(order_status='VO').exists():
            raise ParamError('已作废的卡派单不能配置', ErrorCode.PARAM_ERROR)

        truck_order_service = TruckOrderService()
        # 处理部分配载逻辑
        if is_part_set:
            if len(ids) > 1:
                raise ParamError('部分配载不能选择多个订单配载，只能选择一个订单', ErrorCode.PARAM_ERROR)

            customer_order = customer_order_queryset.first()

            # 查询订单对应的卡派单关联记录
            relate_trucks = CustomerOrderRelateTruck.objects.filter(
                customer_order_num=customer_order, del_flag=False)
            truck_order_ids = relate_trucks.values_list('truck_order_id', flat=True)

            # 计算当前已配载件数（不包括本次选择的卡派单）
            # select_truck_order_ids = [i.get('id') for i in selected_truck_orders]
            under_stowage_count = relate_trucks.filter(
                ~Q(truck_order_id__in=selected_truck_order_ids)
            ).aggregate(total=Sum('freight_num'))['total'] or 0

            # 计算本次新增配载总件数
            total_freight_num = 0
            for truck_order_data in selected_truck_orders:
                total_freight_num += int(truck_order_data.get('freight_num', 0))

            # 校验配载件数
            if total_freight_num == 0:
                raise ParamError('配载件数要大于0', ErrorCode.PARAM_ERROR)
            if total_freight_num > (customer_order.carton or 0):
                raise ParamError('配载件数不能超过整单件数', ErrorCode.PARAM_ERROR)
            if (under_stowage_count + total_freight_num) > (customer_order.carton or 0):
                raise ParamError('配载件数+已配载数量不能超过整单件数', ErrorCode.PARAM_ERROR)

            # 更新订单的卡派单关联信息，默认使用第一个卡派单
            truck_order_first = TruckOrder.objects.get(id=selected_truck_orders[0]['id'])
            update_order_params = {
                'truck_order_id': truck_order_first,
                'truck_num': truck_order_first.truck_order_num
            }

            # 记录字段变更
            modify_data = {'truck_order_id': truck_order_first}
            OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

            # 更新订单信息
            customer_order_queryset.update(**update_order_params, **get_update_params(request))

            # 处理每个卡派单的配载
            for truck_order_data in selected_truck_orders:
                truck_order = TruckOrder.objects.get(id=truck_order_data['id'])
                freight_num = int(truck_order_data.get('freight_num', 0))

                # 查找或创建关联记录
                relate_truck_qs = CustomerOrderRelateTruck.objects.filter(
                    customer_order_num=customer_order,
                    truck_order=truck_order,
                    del_flag=False
                )

                if relate_truck_qs.exists():
                    # 更新现有关联记录
                    customer_order_relate_truck = relate_truck_qs.first()
                    customer_order_relate_truck.freight_num = freight_num
                else:
                    # 创建新关联记录
                    customer_order_relate_truck = CustomerOrderRelateTruck()
                    customer_order_relate_truck.customer_order_num = customer_order
                    customer_order_relate_truck.truck_order = truck_order
                    customer_order_relate_truck.freight_num = freight_num

                # 配置重量和体积（按比例分配）
                if customer_order.carton and customer_order.carton > 0:
                    ratio = freight_num / customer_order.carton
                    customer_order_relate_truck.allocate_weight = float((customer_order.weight or 0)) * ratio
                    customer_order_relate_truck.allocate_volume = float((customer_order.volume or 0)) * ratio

                # 保存关联记录
                customer_order_relate_truck.create_by = user
                customer_order_relate_truck.create_date = datetime.now()
                customer_order_relate_truck.save()

                # 更新卡派单的件重体总数
                truck_order_service.update_truck_order_totals(truck_order)

                logger.info(
                    f'部分配载成功, 卡派单号: {truck_order.truck_order_num}, 订单号: {customer_order.order_num}, '
                    f'订单件数: {customer_order.carton}, 配载件数: {freight_num}')

        else:
            # 全部配载逻辑
            if len(selected_truck_orders) > 1:
                raise ParamError('整单配载只能配载一个卡派单', ErrorCode.PARAM_ERROR)

            truck_order = TruckOrder.objects.get(id=selected_truck_orders[0]['id'])

            for customer_order in customer_order_queryset:
                # 检查是否已经有部分配载
                relate_truck_qs = CustomerOrderRelateTruck.objects.filter(
                    customer_order_num=customer_order, del_flag=False)

                if relate_truck_qs.count() > 1:
                    raise ParamError(f'订单{customer_order.order_num} 已部分配载，不能全部配载', ErrorCode.PARAM_ERROR)

                if relate_truck_qs.count() == 1:
                    relate_truck = relate_truck_qs.first()
                    if relate_truck.truck_order != truck_order:
                        if is_rebinding:
                            # 重新绑定: 删除旧关联
                            relate_truck_qs.update(del_flag=True, **get_update_params(request))
                        else:
                            raise ParamError(
                                f'订单{customer_order.order_num} 已配载到卡派单 {relate_truck.truck_order.truck_order_num}，'
                                f'未勾选重新绑定不能再全部配载', ErrorCode.PARAM_ERROR)
                    else:
                        # 已经配载到同一卡派单，跳过
                        continue

                # 更新订单的卡派单关联
                update_order_params = {
                    'truck_order_id': truck_order,
                    'truck_num': truck_order.truck_order_num
                }

                # 记录字段变更
                modify_data = {'truck_order_id': truck_order}
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

                # 更新订单
                CustomerOrder.objects.filter(id=customer_order.id).update(
                    **update_order_params, **get_update_params(request))

                # 创建关联记录
                customer_order_relate_truck = CustomerOrderRelateTruck()
                customer_order_relate_truck.customer_order_num = customer_order
                customer_order_relate_truck.truck_order = truck_order
                customer_order_relate_truck.freight_num = customer_order.carton or 0
                customer_order_relate_truck.allocate_weight = customer_order.weight or 0
                customer_order_relate_truck.allocate_volume = customer_order.volume or 0
                customer_order_relate_truck.create_by = user
                customer_order_relate_truck.create_date = datetime.now()
                customer_order_relate_truck.save()

                logger.info(
                    f'全部配载成功, 卡派单号: {truck_order.truck_order_num}, 订单号: {customer_order.order_num}, '
                    f'配载件数: {customer_order.carton}')

            # 更新卡派单的件重体总数
            truck_order_service.update_truck_order_totals(truck_order)

        return success_response(request, '配置成功')

    # 批量生成并配置卡派单(批量创建卡派单)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_set_truck_num(self, request):
        ids = request.data.get('ids', [])
        customer_orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        if not customer_orders.exists():
            raise ParamError(f'没有查询到订单', ErrorCode.PARAM_ERROR)

        # 现在允许配置
        # # X如果已绑定卡派单, 则不允许批量配置卡派单
        # relate_orders = CustomerOrderRelateTruck.objects.filter(customer_order_num__in=customer_orders,
        #                                                         del_flag=False)
        # if relate_orders.exists():
        #     has_binding_orders = relate_orders.values_list('customer_order_num__order_num', flat=True)
        #     raise ParamError(f'订单已绑定卡派单, 请解绑后再批量配置, 订单号: {", ".join(has_binding_orders)}',
        #                      ErrorCode.PARAM_ERROR)

        # 如果有转单号, 则配置到一个卡派单中, 没有转单号则走原来的逻辑
        has_tracking_num = customer_orders.filter(tracking_num__isnull=False)
        no_tracking_num = customer_orders.filter(tracking_num__isnull=True)

        truck_order_nums = []
        receivers, ocean_order_id = batch_create_truck_order_get_ocean_order(has_tracking_num)
        if ocean_order_id:
            ocean_order = OceanOrder.objects.get(id=ocean_order_id)
            truck_order_params = {
                'ocean_order_num': ocean_order.order_num,
                # 'arrive_destination': arrive_destination,
            }
            truck_order_num = batch_create_truck_order(request, truck_order_params, has_tracking_num, ocean_order)
            if truck_order_num:
                truck_order_nums.append(truck_order_num)

        receivers, ocean_order_id = batch_create_truck_order_get_ocean_order(no_tracking_num)
        if ocean_order_id:
            # logger.info(f'batch_set_truck_num ocean_order_id--> {ocean_order_id}, {type(ocean_order_id)}')
            ocean_order = OceanOrder.objects.get(id=ocean_order_id)
            # conform_queryset = CustomerOrder.objects.filter(~Q(order_status='VO'), id__in=ids)
            # receivers = customer_orders.values_list('receiver', flat=True)
            logger.info(f'批量配置卡派单, 收件人: {receivers}')
            for receiver in receivers:
                # 按收件人(仓库地址)生成卡派单
                # if isinstance(receiver, int):
                #     arrive_destination = Address.objects.get(id=receiver).address_num
                #     current_customer_orders = customer_orders.filter(receiver_id=receiver)
                # else:
                arrive_destination = receiver
                current_customer_orders = customer_orders.filter(buyer_address_num=receiver)
                truck_order_params = {
                    'ocean_order_num': ocean_order.order_num,
                    'arrive_destination': arrive_destination,
                }
                truck_order_num = batch_create_truck_order(request, truck_order_params, current_customer_orders,
                                                           ocean_order)
                if truck_order_num:
                    truck_order_nums.append(truck_order_num)
                # current_customer_orders.update(truck_order_id=truck_order, **get_update_params(request))
        return success_response(request, f'批量生成并配置卡派单成功, 生成的卡派单号: {truck_order_nums}')

    # 配置揽收单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_collect_num(self, request):
        ids = request.data['ids']
        collect_order = request.data.get('selectData', [])
        customer_orders = CustomerOrder.objects.filter(id__in=ids)
        conform_queryset = CustomerOrder.objects.filter(~Q(is_intercept=True), ~Q(order_status='VO'), id__in=ids)
        if len(customer_orders) != len(conform_queryset):
            return fail_response(request, '请检查所有订单都未作废或未拦截')
        if len(collect_order) == 0:
            # customer_orders.update(collect_num=None, **get_update_params(request))
            for customer_order in customer_orders:
                modify_data = {'collect_num': None}
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

                collect_order = customer_order.collect_num
                customer_order.collect_num = None
                customer_order.update_by = get_update_params(request)['update_by']
                customer_order.update_date = get_update_params(request)['update_date']
                customer_order.save()
                summary_order_weight_and_size(collect_order, 'collect_num')
            msg = '取消与揽收单关联成功'
        else:
            fail_result = []
            # 取第一个揽收单, 只配第一个揽收单
            first_collect_order = CollectOrder.objects.get(id=collect_order[0]['id'])
            for customer_order in customer_orders:
                if first_collect_order.customer and customer_order.customer != first_collect_order.customer:
                    fail_result.append(customer_order.order_num)
                else:
                    modify_data = {'collect_num': first_collect_order}
                    OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

                    customer_order.collect_num = first_collect_order
                    customer_order.save()
            summary_order_weight_and_size(first_collect_order, 'collect_num')
            # queryset.update(collect_num=cur_order, **get_update_params(request))
            if fail_result:
                msg = f'部分配置失败(订单的客户和揽收单客户不相同), 失败单号: {", ".join(fail_result)}'
                return fail_response(request, msg)
            else:
                msg = '配置成功'
        return success_response(request, msg)

    # 批量创建并配置揽收单(批量创建揽收单/批量配置揽收单)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_set_collect_order(self, request):
        ids = request.data.get('ids', [])
        customer_orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        if not customer_orders.exists():
            raise ParamError(f'没有查询到订单', ErrorCode.PARAM_ERROR)
        # 如果订单都是同一个客户, 则把客户加到揽收单上
        customers = customer_orders.values_list('customer', flat=True).distinct()
        customer_id = None
        if len(customers) == 1:
            customer_id = customers[0]

        create_params = {
            'customer_id': customer_id,
            'pre_carton': customer_orders.aggregate(total=Sum('pre_carton'))['total'],
            'pre_weight': customer_orders.aggregate(total=Sum('pre_weight'))['total'],
            'pre_volume': customer_orders.aggregate(total=Sum('pre_volume'))['total'],
            'carton': customer_orders.aggregate(total=Sum('carton'))['total'],
            'weight': customer_orders.aggregate(total=Sum('weight'))['total'],
            'volume': customer_orders.aggregate(total=Sum('volume'))['total'],
        }
        collect_order = CollectOrder.objects.create(**create_params, **get_update_params(request, True))
        gen_order_num(collect_order, order_num_prefix='COL')
        # collect_order.order_num = 'COL' + create_order_num(collect_order.id)
        collect_order.save()
        modify_data = {'collect_num': collect_order}
        for customer_order in customer_orders:
            OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)
        customer_orders.update(collect_num=collect_order, **get_update_params(request))
        return success_response(request, f'批量创建并配置揽收单成功, 生成的揽收单号: {collect_order.order_num}')

    # 配置出口报关单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_clearance_out_num(self, request):
        ids = request.data['ids']
        other_orders = request.data.get('selectData', [])
        if len(other_orders) > 1:
            return fail_response(request, '只能配置一个出口报关单')
        customer_orders = CustomerOrder.objects.filter(id__in=ids)
        intercept_customer_orders = customer_orders.filter(is_intercept=True)
        if intercept_customer_orders.exists():
            intercept_customer_order_nums = intercept_customer_orders.values_list('order_num', flat=True)
            return fail_response(request, f'订单被拦截, 无法配置: {", ".join(intercept_customer_order_nums)}')
        customer_orders_customer = customer_orders.values_list('customer', flat=True).distinct()
        if len(customer_orders_customer) > 1:
            return fail_response(request, f'请选择同一个客户的订单')
        if len(other_orders) == 0:
            clearance_out = customer_orders.first().clearance_out
            modify_data = {'clearance_out': None}
            for customer_order in customer_orders:
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)
            customer_orders.update(clearance_out=None, **get_update_params(request))
            msg = '取消与出口报关单关联成功'
        else:
            clearance_out = ClearanceOut.objects.get(id=other_orders[0]['id'])
            if customer_orders_customer and customer_orders_customer[0] != clearance_out.customer_id:
                return fail_response(request, f'订单客户与配置的出口报关单客户不一致')
            if clearance_out.clear_status in ['VO', 'FC']:
                return fail_response(request, '不能配置作废/完成的出口报关单')
            modify_data = {'clearance_out': clearance_out}
            for customer_order in customer_orders:
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)
            customer_orders.update(clearance_out=clearance_out, is_customs_declaration=True,
                                   **get_update_params(request))
            msg = '配置成功'
        summary_order_weight_and_size(clearance_out, 'clearance_out')
        return success_response(request, msg)

    # 配置合并计费单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_combine_billing_order(self, request):
        ids = request.data['ids']
        combine_billing_order_ids = request.data.get('selectData', [])
        customer_orders = CustomerOrder.objects.filter(id__in=ids)
        conform_queryset = CustomerOrder.objects.filter(~Q(is_intercept=True), ~Q(order_status='VO'), id__in=ids)
        if len(customer_orders) != len(conform_queryset):
            return fail_response_common(msg='请检查所有订单都未作废或未拦截')
        if len(combine_billing_order_ids) == 0:
            for customer_order in customer_orders:
                modify_data = {'combine_billing_order': None}
                OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

                combine_billing_order = customer_order.combine_billing_order
                customer_order.combine_billing_order = None
                customer_order.update_by = get_update_params(request)['update_by']
                customer_order.update_date = get_update_params(request)['update_date']
                customer_order.save()
                summary_order_weight_and_size(combine_billing_order, 'combine_billing_order')
            msg = '取消与合并计费单关联成功'
        else:
            fail_result = []
            # 取第一个合并计费单, 只配第一个合并计费单
            combine_billing_order = CombineBillingOrder.objects.get(id=combine_billing_order_ids[0]['id'])

            check_config_combine_billing_order(customer_orders)

            first_customer_order = customer_orders.first()
            customer = first_customer_order.customer
            product = first_customer_order.product
            shipper = first_customer_order.shipper
            receiver = first_customer_order.receiver
            if combine_billing_order.customer and combine_billing_order.customer != customer:
                return fail_response_common(f'订单的客户与合并计费单的客户需要保持一致, 订单客户: {customer.name}, '
                                            f'计费单客户: {combine_billing_order.customer.name}')
            elif not combine_billing_order.customer:
                combine_billing_order.customer = customer
                combine_billing_order.save()
            if combine_billing_order.product and combine_billing_order.product != product:
                return fail_response_common(f'订单的客户与合并计费单的客户需要保持一致, 订单产品: {product.name}, '
                                            f'计费单产品: {combine_billing_order.product.name}')
            elif not combine_billing_order.product:
                combine_billing_order.product = product
                combine_billing_order.save()
            if combine_billing_order.shipper and combine_billing_order.shipper != shipper:
                return fail_response_common(
                    f'订单的发件人与合并计费单的发件人需要保持一致, 订单发件人: {shipper.address_num}, '
                    f'计费单发件人: {combine_billing_order.shipper.address_num}')
            elif not combine_billing_order.shipper:
                combine_billing_order.shipper = shipper
                combine_billing_order.save()
            if combine_billing_order.receiver and combine_billing_order.receiver != receiver:
                return fail_response_common(
                    f'订单的收件人与合并计费单的收件人需要保持一致, 订单收件人: {receiver.address_num}, '
                    f'计费单收件人: {combine_billing_order.receiver.address_num}')
            elif not combine_billing_order.receiver:
                copy_customer_order_address(first_customer_order, combine_billing_order, address_type='receiver')

            for customer_order in customer_orders:
                if combine_billing_order.customer and customer_order.customer != combine_billing_order.customer:
                    fail_result.append(customer_order.order_num)
                else:
                    modify_data = {'combine_billing_order': combine_billing_order}
                    OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

                    customer_order.combine_billing_order = combine_billing_order
                    customer_order.save()
            summary_order_weight_and_size(combine_billing_order, 'combine_billing_order', summary_confirm_data=True)
            # 更新签入时间(取订单中最早的签入时间)
            has_check_in_time = customer_orders.filter(check_in_time__isnull=False).order_by('check_in_time').first()
            combine_billing_order.check_in_time = has_check_in_time and has_check_in_time.check_in_time
            combine_billing_order.save()

            # queryset.update(combine_billing_order=cur_order, **get_update_params(request))
            if fail_result:
                msg = f'部分配置失败(订单的客户和合并计费单客户不相同), 失败单号: {", ".join(fail_result)}'
                return fail_response_common(msg)
            else:
                msg = '配置成功'
        return success_response_common(msg=msg)

    # 批量创建并配置合并计费单(批量创建合并计费单/批量配置合并计费单)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_set_combine_billing_order(self, request):
        ids = request.data.get('ids', [])
        if len(ids) < 2:
            return fail_response_common('至少选择两个订单创建合并计费单')
        customer_orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        if not customer_orders.exists():
            raise ParamError(f'没有查询到订单', ErrorCode.PARAM_ERROR)
        check_config_combine_billing_order(customer_orders)
        # 如果订单都是同一个客户, 则把客户加到合并计费单上
        first_customer_order = customer_orders.first()
        has_check_in_time = customer_orders.filter(check_in_time__isnull=False).order_by('check_in_time').first()

        create_params = {
            'customer': first_customer_order.customer,
            'product': first_customer_order.product,
            'check_in_time': has_check_in_time and has_check_in_time.check_in_time,
            'pre_carton': customer_orders.aggregate(total=Sum('pre_carton'))['total'],
            'pre_weight': customer_orders.aggregate(total=Sum('pre_weight'))['total'],
            'pre_volume': customer_orders.aggregate(total=Sum('pre_volume'))['total'],
            'carton': customer_orders.aggregate(total=Sum('carton'))['total'],
            'weight': customer_orders.aggregate(total=Sum('weight'))['total'],
            'volume': customer_orders.aggregate(total=Sum('volume'))['total'],
            'charge_weight': customer_orders.aggregate(total=Sum('charge_weight'))['total'],
            'confirm_charge_weight': customer_orders.aggregate(total=Sum('confirm_charge_weight'))['total'],
        }
        combine_billing_order = CombineBillingOrder.objects.create(**create_params, **get_update_params(request, True))
        gen_order_num(combine_billing_order, order_num_prefix='CB')
        copy_customer_order_address(first_customer_order, combine_billing_order, address_type='shipper')
        copy_customer_order_address(first_customer_order, combine_billing_order, address_type='receiver')
        modify_data = {'combine_billing_order': combine_billing_order}
        for customer_order in customer_orders:
            OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)
        customer_orders.update(combine_billing_order=combine_billing_order, **get_update_params(request))
        return success_response_common(
            msg=f'批量创建并配置合并计费单成功, 生成的合并计费单: {combine_billing_order.order_num}')

    # 获取所有未完成的客户订单
    @action(methods=['GET'], detail=False)
    def all_notFC_order(self, request):
        # queryset = CustomerOrder.objects.filter(~Q(order_status__in=['FC', 'VO']), del_flag=False).order_by('-id')
        queryset = CustomerOrder.objects.filter(~Q(order_status__in=['VO']), del_flag=False).order_by('-id')
        search_fields = ('order_num',)
        queryset = params_query(queryset, CustomerOrder, request, search_fields=search_fields, except_params=['size'])
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = CustomerOrderSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        else:
            serialized_data = CustomerOrderSerializer(queryset, many=True)
            request.data['data'] = serialized_data
            return success_response(request, 'success')

    # 发送账单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def send_debit(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids)
        content = 'Please check your debit note.'
        try:
            m_file = settings.DEBIT_DIR + 'air_debit.xlsx'
            for customer_order in queryset:
                subject = 'Debit-' + customer_order.order_num
                to_mails = Company.objects.get(pk=customer_order.customer.id).debit_email.split(';')
                # to_mails = Company.objects.get(pk=customer_order.customer.id).debit_email.split(',')
                new_filename = settings.DEBIT_DIR + 'Debit-' + customer_order.order_num + '.xlsx'
                copyfile(m_file, new_filename)
                wb = openpyxl.load_workbook(new_filename)
                ws = wb.worksheets[0]
                main_row_num = 6
                main_col_num = 2
                row_step = 1
                col_step = 3
                # 系统订单号
                ws.cell(main_row_num, main_col_num, customer_order.order_num)
                # 客户订单号
                ws.cell(main_row_num, main_col_num + col_step, customer_order.ref_num or '')
                # Excel换行
                main_row_num += row_step
                # 客户名称
                ws.cell(main_row_num, main_col_num, customer_order.customer.name)
                # 航班日期
                # ws.cell(main_row_num, main_col_num + col_step, customer_order.airline_date)
                # Excel换行
                main_row_num += row_step
                # 货物口名和货物信息
                ws.cell(main_row_num, main_col_num, customer_order.goods_nameCN)
                ws.cell(main_row_num, main_col_num + col_step, '%s CTNS %s KGS %s CBM' % (
                    customer_order.carton, customer_order.weight, customer_order.volume))
                # Excel换行
                main_row_num += row_step
                # 当前产品是FBA时，不显示主单号和航班号
                if customer_order.master_num is not None:
                    ws.cell(main_row_num, main_col_num, customer_order.master_num.order_num)
                    ws.cell(main_row_num, main_col_num + col_step, customer_order.airline_num)

                # 如果订单有跟踪号，发送账单时带上。
                if customer_order.tracking_num is not None:
                    ws.cell(main_row_num, main_col_num, customer_order.tracking_num)

                # Excel 换行
                main_row_num += row_step
                ws.cell(main_row_num, main_col_num, customer_order.destination)
                # 费用明细表
                charges = CustomerOrderChargeIn.objects.filter(customer_order_num=customer_order.pk, del_flag=False)
                row_num = 13
                col_num = 1
                for charge in charges:
                    ws.cell(row_num, col_num, charge.charge.name)
                    ws.cell(row_num, col_num + 1, charge.charge_rate)
                    ws.cell(row_num, col_num + 2, charge.charge_count)
                    ws.cell(row_num, col_num + 3, charge.currency_type)
                    ws.cell(row_num, col_num + 4, charge.charge_total)
                    ws.cell(row_num, col_num + 5, charge.account_charge)
                    row_num += 1
                ws['F27'] = charges.aggregate(total=Sum('account_charge'))['total'] or 0
                wb.save(new_filename)
                # 发送邮件, 如果多个用分号隔开则循环发送
                send_file(subject, content, to_mails, new_filename)

                # 标识账单已经发送
                customer_order.is_send_debit = True
                customer_order.save()
        except ValueError:
            return fail_500_response(request, 'Failed')

        return success_response(request, 'success')

    # 收入确认(生成账单): 把oms中的收入明细数据读到财务中来
    @lock_request
    @action(methods=['POST'], detail=False)
    @method_decorator(request_frequency_limit)
    def revenue_order(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids)

        not_qualify = queryset.filter(order_status__in=['DR', 'WO', 'PDC', 'PW', 'VO', 'VC'],
                                      is_revenue_lock=False)
        if not_qualify.exists():
            error_order_nums = not_qualify.values_list('order_num', flat=True)
            return fail_response(request,
                                 f'请选择未进行收入确认并且全部入仓后的客户订单, 异常如: {", ".join(error_order_nums)}')

        errors_msg = []
        for customer_order in queryset:
            # 增加redis，防止重复执行
            key = f'_customer_order_finish_order_{customer_order.id}'
            try:
                if cache.get(key):
                    continue
                cache.set(key, 'running', timeout=60 * 60)
                order_revenue_confirm(customer_order.id, 'CustomerOrder', request.user)

                # 重新获取订单
                customer_order = get_order_by_id(customer_order.id, 'CustomerOrder')[0]
                # 计算销售定价的成本价
                calc_sales_cost_price(customer_order, request.user)
                # 录入到订单提成明细, 每天定时任务计算提成
                order_commission_export = OrderCommissionExport.objects.filter(
                    customer_order=customer_order, del_flag=False)
                if order_commission_export.exists():
                    order_commission_export.update(is_calc=False)
                else:
                    OrderCommissionExport.objects.create(customer_order=customer_order)

            except ParamError as e:
                logger.error(traceback.format_exc())
                errors_msg.append(str(e))
            finally:
                cache.delete(key)

        if errors_msg:
            request.data['msg'] = f'部分收入确认成功，部分收入确认异常，异常如 {errors_msg}'
        else:
            request.data['msg'] = '收入确认完成，账单生成成功。'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 收入解锁
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def order_unlock(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids)
        fail_orders = 0
        for customer_order in queryset:
            if Debit.objects.filter(order_num=customer_order.order_num, is_invoiced=True, del_flag=False,
                                    is_adjust=False).count() > 0:
                fail_orders += 1
            else:
                is_lock_order(customer_order, 'revenue')
                Debit.objects.filter(order_num=customer_order.order_num, is_adjust=False).update(del_flag=True)
                AccountReceivable.objects.filter(order_num=customer_order.order_num, is_adjust=False).update(
                    del_flag=True)
                customer_order.is_revenue_lock = False
                customer_order.account_time = None
                # customer_order.order_status = 'WO'
                customer_order.income = None
                customer_order.gross_profit = None
                customer_order.gross_currency = None
                customer_order.update_by = get_update_params(request)['update_by']
                customer_order.update_date = get_update_params(request)['update_date']
                customer_order.save()
                # 删除已经生成的泡比优惠 和 重量提成
                # delete_crm_price(customer_order)
        if fail_orders > 0:
            msg = '部分客户订单收入解锁成功！%s 个订单解锁失败，请确保收入所开具的账单还未生成发票' % fail_orders
            return success_response(request, msg)
        else:
            return success_response(request, '客户订单收入解锁成功')

    # 成本确认
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_finish(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids)
        not_qualify = queryset.filter(order_status__in=['DR', 'WO', 'PDC', 'PW', 'VO', 'VC'],
                                      is_cost_lock=False)
        if not_qualify.exists():
            error_order_nums = not_qualify.values_list('order_num', flat=True)
            return fail_response(request,
                                 f'请选择未进行成本确认并且全部入仓后的客户订单, 异常如: {", ".join(error_order_nums)}')

        # 判断所关联的主单或提单是否为完成状态
        for item in queryset:
            order_cost_confirm(item.id, 'CustomerOrder', request.user)
        return success_response(request, '成本确认成功！')

    # 成本解锁
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_unlock(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids)
        # fail_orders = 0
        # for customer_order in queryset:
        #     if AccountPayable.objects.filter(payment_num__isnull=False, is_adjust=False,
        #                                      order_num=customer_order.order_num, del_flag=False).count() > 0:
        #         fail_orders += 1
        #         continue
        #     else:
        #         is_lock_order(customer_order, 'cost')
        #         AccountPayable.objects.filter(order_num=customer_order.order_num, is_adjust=False).update(del_flag=True)
        #         customer_order.is_cost_lock = False
        #         customer_order.account_time = None
        #         # customer_order.order_status = 'WO'
        #         customer_order.gross_profit = None
        #         customer_order.gross_currency = None
        #         customer_order.cost = None
        #         customer_order.update_by = get_update_params(request)['update_by']
        #         customer_order.update_date = get_update_params(request)['update_date']
        #         customer_order.save()
        fail_orders, msg = cost_unlock_common(queryset, request, judge_lock_account=True)
        return success_response(request, msg)

    # 订单成本分摊
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_share(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids)

        fail_orders = 0
        # fail_orders = customer_order_cost_share(queryset, request)
        for customer_order in queryset:
            charge_out_queryset = CustomerOrderChargeOut.objects.filter(customer_order_num=customer_order,
                                                                        del_flag=False)
            if charge_out_queryset.count() == 0:
                fail_orders += 1
                continue
            fail_orders += common_packet_cost_share(customer_order, customer_order,
                                                    charge_out_queryset, request.user)
        if fail_orders > 0:
            msg = f'订单成本分摊部分失败, 失败个数: {fail_orders}！'
            return success_response(request, msg)
        else:
            return success_response(request, '客户订单成本分摊成功')

    # 订单取消成本分摊
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_share_cancel(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        for obj in queryset:
            # 获取成本明细
            # charge_out_queryset = CustomerOrderChargeOut.objects.filter(customer_order_num=obj, del_flag=False)
            # if charge_out_queryset.count() == 0:
            #     continue

            # # 获取对应的订单
            # customer_order_queryset = CustomerOrder.objects.filter(collect_num=obj, del_flag=False)
            # if customer_order_queryset.count() == 0:
            #     continue

            # for customer_order in customer_order_queryset:
            logger.info(f'开始取消订单 {obj.order_num} 的分摊成本')
            common_packet_cost_share_cancel(obj, obj)
            logger.info(f'取消订单 {obj.order_num} 的分摊成本完成')

        return success_response(request, '取消成本分摊成功')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def create_label(self, request):
        """
        创建下面单接口
        :param request:
        :return:
        """
        ids = request.data['ids']
        customer_orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False, carton__gte=0)

        if customer_orders.count() == 0:
            raise ParamError('没有符合规划的订单可以获取面单。', ErrorCode.PARAM_ERROR)

        for customer_order in customer_orders:
            create_order_label_task(customer_order, request.user, OrderLabelTask, Parcel)

        return success_response(request, 'success')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def create_sync_order_info(self, request):
        """
        创建订单同步任务(最初需求: 同步到 一起飞, 目前需求: 同步到供应商服务器)(同步至供应商)(同步订单至供应商)
        """
        ids = request.data['ids']
        # customer_orders = CustomerOrder.objects.filter(id__in=ids, is_sync_yqf=False, del_flag=False)
        # tasks = OrderSyncTask.objects.filter(Q(status='Success'),
        #                                      order_num_id__in=ids, del_flag=False)
        #
        # if tasks.count() == len(ids):
        #     raise ParamError('没有需要同步的订单, 请检查订单是否已同步', ErrorCode.PARAM_ERROR)

        for customer_order_id in ids:
            # order_label_task_list = OrderSyncTask.objects.filter(Q(status__in=['HandledBy3rdNo', 'Success']),
            #                                                      order_num=customer_order.id,
            #                                                      del_flag=False)
            # 存在任务, 就下发, 不存在任务, 就创建然后下发
            old_tasks = OrderSyncTask.objects.filter(order_num_id=customer_order_id, task_type='PUSH_ORDER',
                                                     del_flag=False)
            if old_tasks.exists():
                old_task = old_tasks.first()
                # 对于执行成功和已提交的任务不做任何处理
                if old_task.status in ['Success', 'UnHandled']:
                    old_task.handle_times = 0
                    old_task.save()
                    # continue
                elif old_task.status in ['HandledBy3rdNo', 'Failure']:
                    old_task.status = 'UnHandled'
                    old_task.handle_times = 0
                    old_task.save()
                    # continue
                # customer_order = old_tasks.first().order_num
                mode_key = old_task.mode_key
            else:
                # if order_label_task_list.count() > 0:
                #     raise ParamError('不要重复同步' + customer_order.order_num, ErrorCode.PARAM_ERROR)
                # else:
                order_label_task = OrderSyncTask()
                order_label_task.order_num = CustomerOrder.objects.get(id=customer_order_id)
                order_label_task.status = 'UnHandled'
                order_label_task.mode_key = mode_key = gen_moke_key_random()
                order_label_task.handle_times = '0'
                order_label_task.create_by = request.user
                order_label_task.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                order_label_task.save()
            try:
                # sync_customer_order_task.delay(mode_key, customer_order_id=customer_order_id)
                sync_customer_order_task.apply_async(
                    args=[mode_key],
                    kwargs={
                        'customer_order_id': customer_order_id,
                    },
                    queue=f'label_queue_2',
                    routing_key=f'label_queue_2'
                )
            except AlreadyQueued:
                return success_response_common('任务已存在队列中, 请不要重复提交')
            # sync_customer_order_task(str(1), customer_order_id=customer_order_id)
        return success_response_common('任务已下发')

    # 手动创建同步订单收入的任务
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def create_sync_order_revenue(self, request):
        ids = request.data['ids']
        judge_orders = CustomerOrder.objects.filter(id__in=ids, is_revenue_lock=False, del_flag=False)
        if judge_orders.exists():
            return fail_response(request,
                                 f'订单尚未收入确认: {", ".join(judge_orders.values_list("order_num", flat=True))}')
        not_create_task = []
        for customer_order_id in ids:
            old_tasks = OrderSyncTask.objects.filter(order_num_id=customer_order_id, task_type='PUSH_ORDER',
                                                     status='Success', del_flag=False)
            if old_tasks.exists():
                old_task = old_tasks.first()
                create_order_sync_tasks(old_task, is_judge_repetition=True, task_types=['PUSH_REVENUE'])
            else:
                customer_order = CustomerOrder.objects.filter(id=customer_order_id, del_flag=False).first()
                if customer_order:
                    not_create_task.append(customer_order.order_num)
        if not_create_task:
            return fail_response(request, f'{len(not_create_task)}个同步收入的任务未创建成功, '
                                          f'因为还未同步订单至供应商: {", ".join(not_create_task)}')
        else:
            return success_response(request, '任务已下发')

    # 获取当前订单的保险信息
    @transaction.atomic
    @action(methods=['GET'], detail=False)
    def get_currency_insurance(self, request):
        """
        创建保险单单接口
        :param request:
        :return:
        """
        order_id = request.query_params['id']
        order_queryset = CustomerOrder.objects.filter(id=order_id, del_flag=False, is_insurance=True)
        if order_queryset.count() == 1:
            order = order_queryset.first()
            kuajing = KuaJingBao.objects.get(order_num=order.order_num, del_flag=False)
            # if kuajing.status == 'FC':
            #     raise ParamError('该订单已经投保完成了，不能进行修改！', ErrorCode.PARAM_ERROR)
            insurance = Insurance.objects.get(order=order)
            request.data = {
                'trackingNo': insurance.trackingNo,
                'productCode': insurance.productCode,
                'insuredName': insurance.insuredName,
                'cargoValue': insurance.cargoValue,
                'freight': insurance.freight,
                'freightCurrencyCode': insurance.freightCurrencyCode,
                'baseAmountWay': insurance.baseAmountWay,
                'ratio': insurance.ratio,
                'currencyCode': insurance.currencyCode,
                'chargeableWeight': insurance.chargeableWeight,
                'remark': insurance.remark,
                'transportModeCode': insurance.transportModeCode,
                'transportTool': insurance.transportTool,
                'blNo': insurance.blNo,
                'deliverywayCode': insurance.deliverywayCode,
                'expressCompanyCode': insurance.expressCompanyCode,
                'expressNo': insurance.expressNo,
                'shipmentId': insurance.shipmentId,
                'packingCode': insurance.packingCode,
                'packingQuantity': insurance.packingQuantity,
                'cargoDesc': insurance.cargoDesc,
                'cargoCategoryCode': insurance.cargoCategoryCode,
                'departureDate': insurance.departureDate,
                'departureCountryCode': insurance.departureCountryCode,
                'departureAddress': insurance.departureAddress,
                'destType': insurance.destType,
                'destinationCountryCode': insurance.destinationCountryCode,
                'destinationAddress': insurance.destinationAddress,
                'shelf': insurance.shelf,
                'shelfName': insurance.shelfName,
                'unrestRisk': insurance.unrestRisk,
                'certificateType': insurance.certificateType,
                'certificateNo': insurance.certificateNo,
                'mobile': insurance.mobile,
                'contactAddress': insurance.contactAddress,
                # 'amendmentReason': insurance.amendmentReason,
            }
        else:
            raise ParamError('该订单未进行投保服务，请再次确认！', ErrorCode.PARAM_ERROR)

        return success_response(request, 'success')

    # # 提交保险单
    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def create_insurance(self, request):
    #     """
    #     创建保险单单接口
    #     :param request:
    #     :return:
    #     """
    #     request.data.pop('date')
    #     request.data.pop('amount')
    #     request.data.pop('selectData')
    #     request.data.pop('selectActionVal')
    #     request.data.pop('label')
    #     order_id = request.data.pop('ids')[0]
    #     order_queryset = CustomerOrder.objects.filter(id=order_id, del_flag=False, is_insurance=False,
    #                                                   order_status__in=['WO'])
    #     if order_queryset.count() == 1:
    #         order = order_queryset.first()
    #         Insurance.objects.create(**request.data, order=order, **get_update_params(request, True))
    #         # 创建跨境煲保险单
    #         KuaJingBao.objects.create(order_num=order.order_num, request_data=str(request.data),
    #                                   **get_update_params(request, True))
    #         order_queryset.update(is_insurance=True)
    #         # 测试
    #         # handler_kua_jing_order()
    #     else:
    #         raise ParamError('该订单已经创建了对应保险单，您可以进行结果的查询或者保单信息的更改！', ErrorCode.PARAM_ERROR)
    #
    #     return success_response(request, 'success')

    # # 查询保险单
    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def get_insurance(self, request):
    #     """
    #     查询保险单单接口
    #     :param request:
    #     :return:
    #     """
    #     order_id = request.data.pop('ids')[0]
    #     customer_order = CustomerOrder.objects.get(id=order_id)
    #     try:
    #         insurance = Insurance.objects.get(order=customer_order, del_flag=False)
    #         kuajingbao = KuaJingBao.objects.get(order_num=insurance.order.order_num)
    #         if kuajingbao.status == 'FC':
    #             url = 'http://testapi.kjingbao.com/cbi/policy/v1/query'
    #             times = datetime.now().strftime("%Y%m%d%H%M%S")
    #             md5_str = ('100015%sqQYr8Nza9Ugq3rX7' % (times))
    #             sign = hashlib.md5(md5_str.encode('utf-8')).hexdigest().lower()
    #             data = {
    #                 'trackingNo': insurance.trackingNo
    #             }
    #             post_data = {
    #                 'secretId': '100015',
    #                 'sign': sign,
    #                 'timestamp': times,
    #                 'data': json.dumps(data, ensure_ascii=False, separators=(',', ':'), cls=DecimalEncoder)
    #             }
    #             print("调用第三方接口" + url + ",入参:" + str(post_data))
    #             post_data = json.dumps(post_data, ensure_ascii=False, separators=(',', ':'), cls=DecimalEncoder)
    #             r = requests.post(url, data=post_data, headers={'Content-Type': 'application/json'})
    #             print(r.json())
    #             request.data['content'] = r.json()['content']
    #         else:
    #             raise ParamError(kuajingbao.response_data, ErrorCode.PARAM_ERROR)
    #     except Insurance.DoesNotExist:
    #         raise ParamError('该订单尚未创建保险单！', ErrorCode.PARAM_ERROR)
    #
    #     return success_response(request, 'success')

    @action(methods=['POST'], detail=False)
    def cancel_label(self, request):
        '''
        取消面单接口
        :param request:
        :return:
        '''
        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError('订单Id必填', ErrorCode.PARAM_ERROR)

        results = []
        for id in ids:
            handler_cancel_label(id, request, results, CustomerOrder, OrderLabelTask, OrderLabel,
                                 CustomerOrderChargeOut, Parcel)

        logger.info('resutls =' + str(results))
        return success_response(request, str(results))

    # 确认发货
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def confirm_ship(self, request):
        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError('订单Id必填', ErrorCode.PARAM_ERROR)

        results = []
        for id in ids:
            customer_orders = CustomerOrder.objects.filter(id=id, del_flag=False)
            if len(customer_orders) == 0:
                raise ParamError('订单不存在 id=' + str(id), ErrorCode.PARAM_ERROR)

            customer_order = customer_orders[0]
            order_label_task_list = OrderLabelTask.objects.filter(status='Success',
                                                                  order_num=customer_order,
                                                                  del_flag=False)
            if len(order_label_task_list) == 0:
                raise ParamError('无处理成功的订单 id=' + str(id), ErrorCode.PARAM_ERROR)

            order_label_task = order_label_task_list[0]

            print("--productId-->" + str(customer_order.product.id))
            product = Product.objects.get(id=customer_order.product.id)
            if not customer_order.service:
                service_list = Service.objects.filter(product=product.id, del_flag=False)
                if service_list.count() == 0:
                    raise ParamError('产品[' + product.code + ']未配置服务', ErrorCode.PARAM_ERROR)

                service = service_list.first()
            else:
                service = customer_order.service

            supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
            if not supplier_butt.is_support_cancel:
                raise ParamError('产品[' + product.code + ']不支持取消', ErrorCode.PARAM_ERROR)
            supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id,
                                                                       del_flag=False)
            supplier_account = supplier_account_list[0]

            class_name = supplier_butt.class_name

            label_order_vo = LabelOrderVo()
            label_order_vo.orderLabelTask = order_label_task
            label_order_vo.supplierAccount = supplier_account
            label_order_vo.service = service

            # 通过反射实例化对象
            obj = globals()[class_name]()
            result = confirm_ship(obj, label_order_vo)

            if result['code'] == '0':
                order_label_task.label_desc = '确认发货'
                order_label_task.update_by = request.user
                order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                order_label_task.save()
                results.append(customer_order.order_num)

        logger.info('resutls =' + str(results))
        return success_response(request, str(results))

    # 下载面单
    @action(methods=['POST'], detail=False)
    def get_label(self, request):
        '''
        下面面单接口
        :param request:
        :return:
        '''
        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError('订单Id必填', ErrorCode.PARAM_ERROR)

        customer_orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        order_num_list = [str(customer_order.id) for customer_order in customer_orders]

        customer_order = customer_orders[0]

        orderLabelList = OrderLabel.objects.filter(order_num__in=order_num_list, del_flag=False)

        finePath = settings.STATIC_MEDIA_DIR
        fileUrl = 'http://' + settings.DOMAIN_URL + '/media/'
        if len(orderLabelList) == 0:
            raise ParamError('查无面单', ErrorCode.PARAM_ERROR)
        elif len(orderLabelList) == 1:
            orderLabel = orderLabelList[0]
            # fineName = customer_order.order_num + '.pdf'
            finePath += orderLabel.label_url
            fileUrl += orderLabel.label_url
        else:
            pdf_merger = PdfFileMerger()

            fineName = customer_order.order_num + '_m.pdf'
            finePath += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            fileUrl += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            dir = os.path.dirname(finePath)
            if not os.path.exists(dir):
                os.makedirs(dir)
            i = 0
            for orderLabel in orderLabelList:
                logger.info(customer_order.order_num + " pdf page=>" + str(i))
                pdf_merger.merge(i, settings.STATIC_MEDIA_DIR + orderLabel.label_url)

                i += 1

            pdf_merger.write(finePath)
            pdf_merger.close()

        # 下载面单次数加1
        for item in customer_orders:
            download_num = item.download_num or 0
            item.download_num = download_num + 1
            item.save()

        request.data['data'] = fileUrl
        return success_response(request, '')

    @action(methods=['POST'], detail=False)
    def api_orders_label(self, request):
        order_nums = request.data.get('order_nums')
        if not order_nums:
            raise ParamError('订单号必填', ErrorCode.PARAM_ERROR)
        customer_orders = CustomerOrder.objects.filter(order_num__in=order_nums, del_flag=False)
        if customer_orders.count() != len(order_nums):
            raise ParamError(f'有部分订单号不存在：' + ','.join(order_nums), ErrorCode.PARAM_ERROR)
        result = []
        for customer_order in customer_orders:
            pdf_base, err = print_fba_label_common([customer_order])
            if err == 1:
                return fail_response(request, '未找到订单下面的包裹！')
            label_dict = {
                "label": '',
                "label_base64": pdf_base
            }
            result.append(dict(
                order_num=customer_order.order_num,
                label_list=[label_dict]
            ))
        # pdf_data = base64.b64decode(pdf_base)
        # pdf_merger = PdfFileMerger()
        # pdf_merger.write(io.BytesIO(pdf_data))
        # pdf_merger.close()
        request.data['data'] = result
        return success_response(request, '获取面单成功')

    # 获取面单接口
    @action(methods=['POST'], detail=False)
    def api_label(self, request):
        order_num = request.data.get('order_num')
        if not order_num:
            raise ParamError('订单号必填', ErrorCode.PARAM_ERROR)
        customer_orders = CustomerOrder.objects.filter(order_num=order_num, del_flag=False)
        if not customer_orders.exists():
            raise ParamError('查无此订单' + str(order_num), ErrorCode.PARAM_ERROR)
        label_list = []
        for customer_order in customer_orders:
            pdf_base, err = print_fba_label_common([customer_order])
            if err == 1:
                return fail_response(request, '未找到订单下面的包裹！')
            label_dict = {
                "label": '',
                "label_base64": pdf_base
            }
            label_list.append(label_dict)
        # pdf_data = base64.b64decode(pdf_base)
        # pdf_merger = PdfFileMerger()
        # pdf_merger.write(io.BytesIO(pdf_data))
        # pdf_merger.close()
        request.data['data'] = label_list
        return success_response(request, '获取面单成功')

    # 导出fbm订单
    @action(methods=['POST'], detail=False)
    def export_fbm_order(self, request):

        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError('请选择订单', ErrorCode.PARAM_ERROR)

        customer_orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False)

        # 获取数据
        customer_fbm_order_export_service = CustomerFbmOrderExportService()
        data_list = customer_fbm_order_export_service.export(orders=customer_orders)

        # 生成excel
        wb = generate_fbm_order_excel(data_list)
        response = HttpResponse(content_type='application/msexcel')
        sheet_name = f'order_{str(int(datetime.now().timestamp()))}'
        response['Content-Disposition'] = 'attachment;filename=%s.xlsx' % sheet_name
        wb.save(response)
        return response

    @action(methods=['POST'], detail=False)
    def download_excel(self, request):
        '''
        导出订单数据
        :param request:
        :return:
        '''
        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError('订单必选', ErrorCode.PARAM_ERROR)

        wb = Workbook()
        # 创建一个sheet
        sheet_name = '订单数据'
        w = wb.create_sheet(sheet_name, 0)

        title_dict = ['订单号', '客户订单号', '包裹跟踪号']
        i = 1
        for title in title_dict:
            w.cell(row=1, column=i).value = title
            s = chr(i + 64) + str(1)
            w[s].alignment = Alignment(horizontal='center', vertical='center')
            w[s].font = Font(size=18, bold=True)
            w.column_dimensions[chr(i + 64)].width = 35
            i += 1

        excel_row = 2
        for id in ids:
            customerOrder = CustomerOrder.objects.get(id=id)
            orderLabelList = OrderLabel.objects.filter(order_num=customerOrder)
            if orderLabelList.count() > 0:
                for orderLabel in orderLabelList:
                    w.cell(row=excel_row, column=1).value = customerOrder.order_num
                    self.setStyle(excel_row, w, 1)
                    w.cell(row=excel_row, column=2).value = customerOrder.ref_num
                    self.setStyle(excel_row, w, 2)
                    w.cell(row=excel_row, column=3).value = orderLabel.tracking_no
                    self.setStyle(excel_row, w, 3)
            else:
                w.cell(row=excel_row, column=1).value = customerOrder.order_num
                self.setStyle(excel_row, w, 1)
                w.cell(row=excel_row, column=2).value = customerOrder.ref_num
                self.setStyle(excel_row, w, 2)
            excel_row += 1

        response = HttpResponse(content_type='application/msexcel')
        response['Content-Disposition'] = 'attachment;filename=%s.xlsx' % sheet_name
        wb.save(response)
        return response

    # 设置excel字体
    def setStyle(self, excel_row, w, excel_col):
        s = chr(excel_col + 64) + str(excel_row)
        w[s].alignment = Alignment(horizontal='center', vertical='center')
        w[s].font = Font(size=14)

    # 轨迹更改
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_track(self, request):
        ids = request.data['ids']
        orders = CustomerOrder.objects.filter(id__in=ids)
        code = request.data['selectActionVal']
        node_address = request.data.get('nodeAddress', {})
        save_node_address(node_address, code)
        date = request.data.get('date') or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        qty = request.data['amount']
        not_check_status = request.data.get('not_check_status')

        for order in orders:
            if not not_check_status:
                check_order_status(code, order)
            if code in get_order_status_mapping('AW'):
                all_warehousing_constraint(order, request.user, code=code)
            else:
                set_customer_track('TR', date, order.id, code, qty, request)
                set_parcel_track_info(code, order, request, order.product, date)
        return success_response(request, '轨迹更新成功！')

    @action(methods=['POST'], detail=False)
    def change_track_stowage_ocean(self, request):
        user = request.user
        ids = request.data['ids']
        customer_orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        track_code = request.data['selectActionVal']
        date = request.data.get('date') or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        qty = request.data['amount']
        # 添加客户订单轨迹
        for customer_order in customer_orders:
            # if customer_order.first_track:
            track_name = ''
            track_code_queryset = TrackCode.objects.filter(code=track_code, affiliated_track='C', del_flag=False)
            if track_code_queryset.exists():
                track_name = track_code_queryset.first().name
            first_ocean_order = customer_order.ocean_num
            if not first_ocean_order:
                raise ParamError(f'订单未配置海运优先轨迹, 订单: {customer_order.order_num}', ErrorCode.PARAM_ERROR)
            location, track_remark = get_track_location_remark(first_ocean_order, track_code)
            # 海运提单已离港则修改订单状态为转运
            if track_code == "DEP":
                change_order_status(customer_order, "TF", user=user)
            set_customer_track_fba(customer_order, track_code, user, date=date, track_name=track_name,
                                   track_remark=track_remark, track_location=location)
        return success_response_common()

    # 获取当前订单的产品下的轨迹code
    @transaction.atomic
    @action(methods=['GET'], detail=False)
    def get_product_track_code(self, request):
        # todo_c: 目前前端批量修改轨迹更改也只会修改第一个选择的订单, 待优化
        order_id = request.query_params['id']
        order_type = request.query_params.get('order_type')
        if order_type == 'stowageOcean' and settings.SYSTEM_ORDER_MARK in ['MZ', 'YQF', 'FX']:
            track_code_queryset = TrackCode.objects.filter(affiliated_track='C', del_flag=False)
            if track_code_queryset.exists():
                track_code_data = []
                for track_code in track_code_queryset:
                    track_code_data.append({'track_code_code': track_code.code, 'track_code_name': track_code.name})
                request.data['data'] = track_code_data
                return success_response(request, 'success')
            else:
                raise ParamError('系统中未配置海运轨迹代码', ErrorCode.PARAM_ERROR)
        order = CustomerOrder.objects.get(id=order_id)
        if order.product is None:
            raise ParamError('该订单暂未配置产品', ErrorCode.PARAM_ERROR)
        else:
            product_track_codes = ProductTrackCode.objects.filter(product=order.product, del_flag=False)
            if product_track_codes.count() == 0:
                raise ParamError('该产品暂未配置任何轨迹代码！', ErrorCode.PARAM_ERROR)
            json_data = ProductTrackCodeSerializer(product_track_codes, many=True).data
            tracks = Track.objects.filter(order_id=order_id, del_flag=False).order_by('-id')
            current_track = None
            if tracks.count() > 0:
                current_track = tracks.first().track_code
            request.data['current_track'] = current_track
            request.data['data'] = json_data
        return success_response(request, 'success')

    # 作废订单(订单作废)
    @transaction.atomic
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def cancel_order(self, request):
        ids = request.data.get('ids')
        order_num = request.data.get('order_num')
        # if ids:
        #     queryset = CustomerOrder.objects.filter(id__in=ids, order_status__in=['WO', 'PDC', 'PW', 'AW'],
        #                                             is_revenue_lock=False, is_cost_lock=False, del_flag=False)
        #     if queryset.count() != len(ids):
        #         return fail_response(request,
        #                              '请选择状态为[等待作业, 已预报, 部分入仓, 全部入仓]并且未进行收入确认和成本确认的客户订单！')
        # else:
        #     queryset = CustomerOrder.objects.filter(order_num=order_num, order_status__in=['WO', 'PDC', 'PW', 'AW'],
        #                                             is_revenue_lock=False, is_cost_lock=False, del_flag=False)
        #     if not queryset.exists():
        #         return fail_response(request,
        #                              '请选择状态为[等待作业, 已预报, 部分入仓, 全部入仓]并且未进行收入确认和成本确认的客户订单！')
        #
        # # 更改状态、取消与主单和分单的外键关联
        # for item in queryset:
        #     # item.order_status = 'VO'
        #     change_order_status(item, 'VO', request.user)
        #     item.master_num = None
        #     item.house_num = None
        #     item.update_by = get_update_params(request)['update_by']
        #     item.update_date = get_update_params(request)['update_date']
        #     item.save()
        #     # 将订单下的所有包裹的del_flag置为true, (包裹号全局唯一, 但作废的订单下的包裹号不计算在内, 所以要置1)
        #     Parcel.objects.filter(customer_order=item, del_flag=False).update(remark='cancel_order', del_flag=True)

        cancel_order_common(request, ids)
        is_open_sync_supplier_intercept = Dict.objects.filter(label='is_open_sync_supplier_intercept', value='1',
                                                              del_flag=False)
        if is_open_sync_supplier_intercept.exists():
            sync_cancel_intercept_data(ids, 'cancel_order')

        return success_response_common('作废订单成功!')

    # 强制作废订单(不判断订单的状态)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cancel_order_force(self, request):
        ids = request.data['ids']
        # queryset = CustomerOrder.objects.filter(~Q(order_status='VO'), id__in=ids,
        #                                         is_revenue_lock=False,
        #                                         is_cost_lock=False)
        #
        # if queryset.count() != len(ids):
        #     return fail_response(request, '请选择未作废, 未收入/成本确认的订单操作！')
        # else:
        #     # 更改状态、取消与主单和分单的外键关联
        #     for item in queryset:
        #         change_order_status(item, 'VO', request.user)
        #         item.master_num = None
        #         item.house_num = None
        #         item.update_by = get_update_params(request)['update_by']
        #         item.update_date = get_update_params(request)['update_date']
        #         item.save()
        #
        #         # 将订单下的所有包裹的del_flag置为true, (包裹号全局唯一, 但作废的订单下的包裹号不计算在内, 所以要置1)
        #         Parcel.objects.filter(customer_order=item, del_flag=False).update(remark='cancel_order', del_flag=True)

        update_order_data_for_cancel_force(request, ids)
        is_open_sync_supplier_intercept = Dict.objects.filter(label='is_open_sync_supplier_intercept', value='1',
                                                              del_flag=False)
        if is_open_sync_supplier_intercept.exists():
            sync_cancel_intercept_data(ids, 'cancel_order_force')

        return success_response_common('作废强制订单成功!')

    # 作废fbm订单(fbm订单作废)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cancel_fbm_order(self, request):
        ids = request.data['ids']
        user = request.user
        queryset = CustomerOrder.objects.filter(id__in=ids, order_status__in=['WO', 'DR'])

        if queryset.count() != len(ids):
            return fail_response(request, '当前状态不支持废弃！')

        # 更改状态、取消与主单和分单的外键关联
        for item in queryset:
            # 增加操作记录
            modify_data = {'master_num': None, 'house_num': None}
            OrderFieldChangeLog.record(item, modify_data, 'FBA', request.user)

            # item.order_status = 'VO'
            change_order_status(item, 'VO', request.user)  # 内部已经记录status日志修改
            item.master_num = None
            item.house_num = None
            item.update_by = get_update_params(request)['update_by']
            item.update_date = get_update_params(request)['update_date']
            item.save()

            # 订单下的货件号也作废
            oc_shipments = OcShipment.objects.filter(customer_order_num=item, del_flag=False)
            oc_shipments.update(status='VO')
            # 将对应轨迹系统的订单轨迹删除
            # track_queryset = Track.objects.filter(customer_order_num=item.id)
            # track_queryset.update(del_flag=True)

            track = Track()
            # track_queryset.update(del_flag=True)
            track.track_code = 'CL'
            track.customer_order_num = item
            track.update_by = get_update_params(request)['update_by']
            track.update_date = get_update_params(request)['update_date']
            track.actual_time = get_update_params(request)['update_date']
            track.create_by = get_update_params(request, True)['update_by']
            track.create_date = get_update_params(request, True)['update_date']
            track.save()
            # 给客户订单添加  '取消订单'  轨迹
            # set_customer_track([track.id], 'CL')
            Parcel.objects.filter(customer_order=item, del_flag=False).update(remark='cancel_order', del_flag=True)

        return success_response(request, '作废订单成功!')


    # 复制fbm订单(原单维持作废状态, 新单copy原单信息, ???查询会存在多个相同的单号)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def copy_fbm_order(self, request):
        order_id = request.data.get('id')
        order_status = request.data.get('order_status')

        if not order_id:
            raise ParamError('请选择要复制的订单！', ErrorCode.PARAM_ERROR)

        if order_status != 'VO':
            raise ParamError('请选择作废的订单进行复制', ErrorCode.PARAM_ERROR)

        original_order = CustomerOrder.objects.get(id=order_id, order_status='VO')

        if not original_order:
            raise ParamError('所选订单不存在', ErrorCode.PARAM_ERROR)

        order_num = original_order.order_num

        temp_queryset = CustomerOrder.objects.filter(~Q(order_status='VO'), order_num=order_num, del_flag=False)

        if temp_queryset.exists():
            raise ParamError(f'当前单号已存在非作废状态的订单: {order_num}', ErrorCode.PARAM_ERROR)

        update_params = get_update_params(request=request, is_create=True)

        # 使用 model_to_dict 创建订单字典
        order_dict = model_to_dict(
            original_order,
            exclude=['id', 'create_by', 'create_date', 'update_by', 'update_date', 'del_flag', 'order_status']
        )
        # 转换外键字段
        order_dict = convert_foreign_keys(original_order, order_dict)

        # 更新需要修改的字段
        order_dict.update({
            'order_status': 'DR',  # 新订单状态设为草稿
        })

        # 创建新订单对象
        new_order = CustomerOrder.objects.create(**order_dict, **update_params)
        # # 生成订单号
        # gen_order_num(new_order)

        # 查询订单下所有货件号并新建
        oc_shipments = OcShipment.objects.filter(customer_order_num=original_order, status='VO', del_flag=False)
        for oc_shipment in oc_shipments:
            oc_shipment_dict = model_to_dict(oc_shipment, exclude=['id', 'create_by', 'create_date', 'update_by',
                                                                   'update_date', 'del_flag', 'remark',
                                                                   'customer_order_num'])
            oc_shipment_dict = convert_foreign_keys(oc_shipment, oc_shipment_dict)
            oc_shipment_dict.update({
                'customer_order_num': new_order,
            })

            new_oc_shipment = OcShipment.objects.create(**oc_shipment_dict, **update_params)

            # 查询原订单所属货件号下标记作废订单的已删除包裹, 并新建
            parcels = Parcel.objects.filter(customer_order=original_order,
                                            shipment_id=oc_shipment.shipment_id,
                                            remark='cancel_order',
                                            del_flag=True)

            for parcel in parcels:
                parcel_dict = model_to_dict(parcel, exclude=['id', 'create_by', 'create_date', 'update_by',
                                                             'update_date', 'del_flag', 'remark', 'customer_order',
                                                             'shipment_id'])
                parcel_dict = convert_foreign_keys(parcel, parcel_dict)

                parcel_dict.update({
                    'customer_order': new_order,
                    'shipment_id': new_oc_shipment.shipment_id,
                })
                new_parcel = Parcel.objects.create(**parcel_dict, **update_params)

                # 复制包裹内商品并新建
                parcel_items = ParcelItem.objects.filter(parcel_num=parcel,
                                                         # shipment_id=oc_shipment.shipment_id,
                                                         del_flag=False)

                for item in parcel_items:
                    item_dict = model_to_dict(item, exclude=['id', 'create_by', 'create_date', 'update_by',
                                                             'update_date', 'del_flag', 'remark', 'parcel_num',
                                                             'shipment_id'])
                    item_dict = convert_foreign_keys(item, item_dict)
                    item_dict.update({
                        'shipment_id': new_oc_shipment.shipment_id,
                        'parcel_num': new_parcel,
                    })
                    ParcelItem.objects.create(**item_dict, **update_params)

        return success_response_common(
            data={'id': new_order.id},
            msg='复制订单成功!'
        )

    # 恢复订单(取消作废)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def recovery_order(self, request):
        ids = request.data['ids']
        # queryset = CustomerOrder.objects.filter(id__in=ids, order_status='VO')
        # if queryset.count() != len(ids):
        #     return fail_response(request, '请选择作废状态的客户订单！')
        # else:
        #     # 更改状态、取消与主单和分单的外键关联
        #     for item in queryset:
        #         same_customer_ref_num = CustomerOrder.objects.filter(Q(ref_num=item.ref_num) &
        #                                                              ~Q(order_status='VO') &
        #                                                              Q(del_flag=False))
        #         if same_customer_ref_num.count() > 0:
        #             return fail_response(request, f'已存在客户订单号为『{item.ref_num}』的订单, 无法恢复！')
        #         same_customer_parcels = item.parcel.filter(del_flag=False).values("parcel_num")
        #         for parcel in same_customer_parcels:
        #             same_parcel = Parcel.objects.filter(~Q(customer_order__order_status='VO'),
        #                                                 customer_order__customer=item.customer,
        #                                                 parcel_num=parcel['parcel_num'], del_flag=False)
        #             if same_parcel.count() > 0:
        #                 return fail_response(request,
        #                                      f'客户『{item.customer}』, 已存在包裹号『{parcel["parcel_num"]}』, 无法恢复！')
        #         # item.order_status = 'WO'
        #         change_order_status(item, 'WO', request.user)
        #         # item.master_num = None
        #         # item.house_num = None
        #         item.update_by = get_update_params(request)['update_by']
        #         item.update_date = get_update_params(request)['update_date']
        #         item.save()
        #
        #         # 将对应轨迹系统的订单取消轨迹删除
        #         # Track.objects.filter(track_code='CL').update(del_flag=True)
        #         # 将订单下的所有cancel_order的包裹的del_flag置为0
        #         parcels = Parcel.objects.filter(customer_order=item, remark='cancel_order', del_flag=True)
        #         for parcel in parcels:
        #             if Parcel.objects.filter(~Q(remark='cancel_order'), parcel_num=parcel.parcel_num,
        #                                      del_flag=False).count() > 0:
        #                 raise ParamError(f'已存在 {parcel.parcel_num} 包裹, 无法恢复订单', ErrorCode.PARAM_ERROR)
        #         parcels.update(del_flag=False, remark=None, **get_update_params(request))

        update_order_data_for_recovery_order(request, ids)
        is_open_sync_supplier_intercept = Dict.objects.filter(label='is_open_sync_supplier_intercept', value='1',
                                                              del_flag=False)
        if is_open_sync_supplier_intercept.exists():
            # try:
            #     sync_cancel_intercept_data(ids, 'customer_order_cancel_order_cancel')
            # except ParamError as e:
            #     return success_response_common(f'操作成功, 但未推送至供应商, 原因: {e}')
            sync_cancel_intercept_data(ids, 'customer_order_cancel_order_cancel')

        return success_response(request, "恢复订单成功！")

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def recall_order(self, request):
        """
        撤回到草稿
        """
        ids = request.data['ids']
        user = request.user
        customer_order_list = CustomerOrder.objects.filter(
            id__in=ids,
            del_flag=False,
            order_status__in=['WO', 'VC']
        )
        if len(customer_order_list) != len(ids):
            return fail_response(request, '请选择[已提交][已审核]的订单')

        for customer_order in customer_order_list:
            # 增加操作记录
            modify_data = {'order_status': 'DR'}
            OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

            customer_order.order_status = 'DR'
            customer_order.save()
            logger.info(f'订单{customer_order.order_num}撤销成功')

        return success_response(request, 'success')

    # 强制全部入仓
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def complete_warehousing(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids, order_status='PW', del_flag=False)
        if queryset.count() != len(ids):
            return fail_response(request, '请选择状态为『已部分入仓』的客户订单进行确认！')
        else:
            for item in queryset:
                all_warehousing_constraint(item, request.user)

            return success_response(request, '全部入仓成功！')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def submit_fbm_order(self, request):
        """
        提交FBM订单
        :param request:
        :return:
        """
        ids = request.data['ids']
        user = request.user
        customer_order_list = CustomerOrder.objects.filter(id__in=ids, del_flag=False, order_status='DR')
        if customer_order_list.count() != len(ids):
            return fail_response(request, '请选择草稿状态的订单')

        customer_fbm_order_service = CustomerFbmOrderService()
        track_time = datetime.now()
        for customer_order in customer_order_list:
            # customer_order 还未被修改前备份原始对象
            original_obj = deepcopy(customer_order)
            customer_fbm_order_service.submit_order(
                order_data=customer_order,
                user=user,
                is_client=False,
                track_time=track_time
            )
            # 增加操作记录
            change_fields = ['order_status']
            OrderFieldChangeLog.record_instance_changes(old_instance=original_obj,
                                                        new_instance=customer_order,
                                                        fields_list=change_fields,
                                                        order_type='FBA',
                                                        user=user)

        return success_response(request, 'success')

    # 确认入仓数据
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def confirm_warehouse_data(self, request):
        ids = request.data.get('ids')
        if not ids:
            ids = [request.data.get('id')]
        queryset = CustomerOrder.objects.filter(id__in=ids, order_status='AW', del_flag=False)
        if queryset.count() != len(ids):
            return fail_response(request, '请选择状态为『已全部入仓』的客户订单进行确认！')
        else:
            for item in queryset:
                set_customer_track_fba(item, 'CWED', request.user)
                change_order_status(item, 'CWED', request.user)
                set_parcel_track_info('CWED', item, request, item.product, datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            return success_response(request, '确认入仓数据成功！')

    # 重新计价
    @lock_request
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def revaluation(self, request):
        ids = request.data['ids']
        revaluation_status = ['AW', 'CWED', 'OW', 'TF', 'SF', 'FC']
        revaluation_status_map = get_order_status_mapping(revaluation_status)
        print('revaluation_status_map-->', revaluation_status_map)
        queryset = CustomerOrder.objects.filter(id__in=ids, order_status__in=revaluation_status_map,
                                                del_flag=False)
        if queryset.count() != len(ids):
            return fail_response(request, '请选择状态为已全部入仓后的客户订单重新计价！')

        is_revenue_lock_orders = CustomerOrder.objects.filter(id__in=ids, is_revenue_lock=True, del_flag=False)
        if is_revenue_lock_orders.exists():
            order_nums = [x.order_num for x in is_revenue_lock_orders]
            return fail_response(request, f'订单 {order_nums} 已收入确认, 无法重新计价！')
        is_cost_lock_orders = CustomerOrder.objects.filter(id__in=ids, is_cost_lock=True, del_flag=False)

        if is_cost_lock_orders.exists():
            order_nums = [x.order_num for x in is_cost_lock_orders]
            return fail_response(request, f'订单 {order_nums} 已成本确认, 无法重新计价！')

        is_not_check_in_times = CustomerOrder.objects.filter(id__in=ids, check_in_time__isnull=True, del_flag=False)
        if is_not_check_in_times.exists():
            order_nums = [x.order_num for x in is_not_check_in_times]
            return fail_response(request, f'订单 {order_nums} 没有签入时间, 无法重新计价！')

        for customer_order in queryset:
            product = customer_order.product

            charge_in = CustomerOrderChargeIn.objects.filter(~Q(is_share=True),
                                                             customer_order_num=customer_order,
                                                             del_flag=False, is_system=True)
            charge_in.update(del_flag=True)
            charge_out = CustomerOrderChargeOut.objects.filter(~Q(is_share=True),
                                                               customer_order_num=customer_order,
                                                               del_flag=False, is_system=True)
            charge_out.update(del_flag=True)

            # 计费重和确认计费重取整
            charge_weight_round(customer_order)

            if product.is_valuation:
                add_revenue(customer_order, request.user, CustomerOrderChargeIn, is_recharge=True)
                # 计算销售定价的成本价
                # add_revenue(customer_order, request.user, CustomerOrderSaleCost, is_recharge=True, sales_cost=True)

                # 查询fba运输单是否有相同的费用项
                charge_duplicates = (
                    CustomerOrderChargeIn.objects
                    .filter(customer_order_num=customer_order, del_flag=False)
                    .values('charge')
                    .annotate(charge_count=Count('id'))
                    .filter(charge_count__gt=1)
                )

                # print('charge_duplicates.query-------------->', charge_duplicates.query)
                if charge_duplicates.exists():
                    # 提取所有重复的 charge_id
                    duplicate_charge_ids = [item['charge'] for item in charge_duplicates]

                    # 查询对应的费用名称
                    duplicate_charge_names = list(
                        Charge.objects.filter(id__in=duplicate_charge_ids).values_list('name', flat=True)
                    )

                    # 拼接提示信息
                    error_msg = f'订单下的收入计费项: {", ".join(duplicate_charge_names)} 重复'

                    raise ParamError(error_msg, ErrorCode.PARAM_ERROR)

            if product.is_cost_valuation:
                add_cost(customer_order, request.user, CustomerOrderChargeOut, is_recharge=True)
            # 计算泡比优惠放到订单收入计费里
            save_crm_price(customer_order, CustomerOrderChargeIn, user=request.user)
        return success_response(request, '重新计价成功！')

    # 订单拦截(拦截订单)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def customer_order_intercept(self, request):
        ids = request.data['ids']
        # queryset = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        # for item in queryset:
        #     if item.is_intercept:
        #         return fail_response(request, f'订单『{item.order_num}』是拦截状态, 不需要拦截')
        #     set_customer_track_fba(item, 'ITP', request.user)
        #     # if err == 1:
        #     #     return fail_response(request, msg)
        #     msg, err = change_order_status(item, 'ITP', request.user)
        #     if err == 1:
        #         return fail_response(request, msg)

        update_order_data_for_customer_order_intercept(request, ids)
        is_open_sync_supplier_intercept = Dict.objects.filter(label='is_open_sync_supplier_intercept', value='1',
                                                              del_flag=False)

        logger.info(f'执行拦截同步吗: {is_open_sync_supplier_intercept.exists()}')
        if is_open_sync_supplier_intercept.exists():
            sync_cancel_intercept_data(ids, 'customer_order_intercept', user=request.user)

        return success_response(request, '订单拦截成功！')

    # 订单取消拦截
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cancel_customer_order_intercept(self, request):
        ids = request.data['ids']
        # queryset = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        # for item in queryset:
        #     if not item.is_intercept:
        #         return fail_response(request, f'订单『{item.order_num}』不是拦截状态, 不需要取消拦截')
        #     set_customer_track_fba(item, item.order_status, request.user)
        #     # if err == 1:
        #     #     return fail_response(request, msg)
        #     msg, err = change_order_status(item, 'ITP', request.user)
        #     if err == 1:
        #         return fail_response(request, msg)

        update_order_data_for_cancel_customer_order_intercept(request, ids)
        is_open_sync_supplier_intercept = Dict.objects.filter(label='is_open_sync_supplier_intercept', value='1',
                                                              del_flag=False)
        if is_open_sync_supplier_intercept.exists():
            sync_cancel_intercept_data(ids, 'customer_order_intercept_cancel')

        return success_response(request, '订单取消拦截成功！')

    # 包裹拦截
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def parcel_intercept(self, request):
        pk = request.data['id']
        try:
            parcel = Parcel.objects.get(id=pk, del_flag=False)
        except Parcel.DoesNotExist:
            return fail_response(request, f'包裹找不到')
        if parcel.intercept_mark:
            return fail_response(request, f'包裹『{parcel.parcel_num}』是拦截状态, 不需要拦截')
        if parcel.customer_order:
            parcels = Parcel.objects.filter(customer_order=parcel.customer_order, del_flag=False).all()
            intercept = False
            for cur_parcel in parcels:
                if cur_parcel.intercept_mark:
                    intercept = True
                    break
            # 如果之前没有包裹被拦截, 则需要修改订单轨迹和状态
            if not intercept:
                set_customer_track_fba(parcel.customer_order, 'ITP', request.user)
                change_order_status(parcel.customer_order, 'ITP', request.user)
            parcel.intercept_mark = True
            parcel.save()
        return success_response(request, '包裹拦截成功！')

    # 包裹取消拦截
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def parcel_cancel_intercept(self, request):
        pk = request.data['id']
        try:
            parcel = Parcel.objects.get(id=pk, del_flag=False)
        except Parcel.DoesNotExist:
            return fail_response(request, f'包裹找不到')
        if not parcel.intercept_mark:
            return fail_response(request, f'包裹『{parcel.parcel_num}』不是拦截状态, 不需要取消拦截')
        if parcel.customer_order:
            parcel.intercept_mark = False
            parcel.save()
            parcels = Parcel.objects.filter(customer_order=parcel.customer_order, del_flag=False).all()
            intercept = False
            for cur_parcel in parcels:
                if cur_parcel.intercept_mark:
                    intercept = True
                    break
            # 如果所有包裹都没有被拦截, 则需要修改订单轨迹和状态
            if not intercept:
                set_customer_track_fba(parcel.customer_order, parcel.customer_order.order_status, request.user)
                change_order_status(parcel.customer_order, 'ITP', request.user)
        return success_response(request, '包裹取消拦截成功！')

    # 完成订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def finish_order(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids, order_status__in=['CWED', 'OW', 'TF', 'SF', 'FC'],
                                                del_flag=False)
        if queryset.count() != len(ids):
            return fail_response(request, '已确认入仓数据之后才可以进行收入确认！')
        else:
            # 更改状态、取消与主单和分单的外键关联
            for item in queryset:
                # item.order_status = 'FC'
                set_customer_track_fba(item, 'FC', request.user)
                change_order_status(item, 'FC', request.user)
                item.update_by = get_update_params(request)['update_by']
                item.update_date = get_update_params(request)['update_date']
                item.save()

            return success_response(request, '完成订单成功!')

    # 修改备注
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def modify_remark(self, request):
        id = request.data['id']
        remark = request.data['modifyVal']
        customer_order = CustomerOrder.objects.get(id=id)

        if not customer_order:
            raise ParamError('订单找不到')

        origin_remark = customer_order.remark
        customer_order.save_fields(remark=remark)

        # 异步调用推送备注给铭志
        if origin_remark != remark:
            # sync_modify_remark_to_mz.delay(customer_order.order_num, remark)
            sync_modify_remark_to_mz.apply_async(
                args=[customer_order.order_num, remark],
                queue=f'label_queue_1',
                routing_key=f'label_queue_1'
            )

        data = {'msg': '修改成功！', 'code': 200}
        return Response(data=data, status=status.HTTP_200_OK)

    # 修改确认计费重
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def modify_confirm_charge_weight(self, request):
        order_id = request.data['id']
        confirm_charge_weight = request.data['modifyVal'] or 0
        order = CustomerOrder.objects.get(id=order_id, del_flag=False)
        if order.is_revenue_lock:
            raise ParamError('订单已收入确认, 不允许修改确认计费重', ErrorCode.PARAM_ERROR)
        modify_data = {
            'confirm_charge_weight': float(confirm_charge_weight),
        }
        OrderFieldChangeLog.record(order, modify_data, 'FBA', request.user)
        order.confirm_charge_weight = float(confirm_charge_weight)
        order.update_by = request.user
        order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order.save()

        data = {'msg': '修改成功！', 'code': 200}
        return Response(data=data, status=status.HTTP_200_OK)

    # 修改确认体积
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def modify_confirm_charge_volume(self, request):
        order_id = request.data['id']
        confirm_volume = request.data['modifyVal'] or 0
        orders = CustomerOrder.objects.filter(id=order_id, del_flag=False)
        order = orders.first()
        if order.is_revenue_lock:
            raise ParamError('订单已收入确认, 不允许修改确认计费体积', ErrorCode.PARAM_ERROR)
        if settings.SYSTEM_ORDER_MARK == 'CLT' and order.order_status in ['AR', 'CC', 'IWW', 'OOD', 'PSF', 'FC', 'SF']:
            raise ParamError('订单已离港, 不允许修改确认计费体积', ErrorCode.PARAM_ERROR)
        modify_data = {
            'confirm_volume': float(confirm_volume),
        }
        OrderFieldChangeLog.record(order, modify_data, 'FBA', request.user)
        orders.update(
            confirm_volume=float(confirm_volume),
            update_by=request.user,
            update_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        data = {'msg': '修改成功！', 'code': 200}
        return Response(data=data, status=status.HTTP_200_OK)

    # 修改fbm计费重
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def modify_fbm_charge_weight(self, request):
        order_id = request.data['id']
        charge_weight = request.data['modifyVal'] or 0
        order = CustomerOrder.objects.get(id=order_id, order_type='FBA', del_flag=False)
        if order.is_revenue_lock:
            raise ParamError('订单已收入确认, 不允许修改计费重', ErrorCode.PARAM_ERROR)
        modify_data = {
            'charge_weight': float(charge_weight),
        }
        OrderFieldChangeLog.record(order, modify_data, 'FBA', request.user)
        order.charge_weight = float(charge_weight)
        order.update_by = request.user
        order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order.save()

        data = {'msg': '修改成功！', 'code': 200}
        return Response(data=data, status=status.HTTP_200_OK)

    # 恢复订单(已弃用)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def recovery_order_old(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids, order_status__in=['VO', 'FC'])
        if queryset.count() != len(ids):
            return fail_response(request, '请选择非草稿或非待作业订单！')
        else:
            # 更改状态、取消与主单和分单的外键关联
            for item in queryset:
                item.order_status = 'WO'
                item.update_by = get_update_params(request)['update_by']
                item.update_date = get_update_params(request)['update_date']
                item.save()

                # 将对应轨迹系统的订单取消轨迹删除
                Track.objects.filter(track_code='CL').update(del_flag=True)
            return success_response(request, "恢复订单成功！")

    # 到货通知
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def arrival_notice(self, request):
        ids = request.data['ids']
        queryset = CustomerOrder.objects.filter(id__in=ids)

        for customer_order in queryset:

            # 自动计算打单重量
            if settings.IS_AUTO_CALC_LABEL_WEIGHT:
                handler_calc_weight(customer_order.order_num, request.user)

            # 有实际件数重量体积的发送邮件
            if customer_order.carton is not None and customer_order.weight is not None and \
                    customer_order.volume is not None:
                # 发送的主题
                subject = 'Arrival Notice'
                # 发送的邮箱
                # to_mails = Company.objects.get(pk=customer_order.customer.id).debit_email.split(';')
                if customer_order.customer and customer_order.customer.debit_email:
                    to_mails = customer_order.customer.debit_email.split(';')
                else:
                    raise ParamError(f'客户没有配置账单邮箱邮箱, 客户: {customer_order.customer}',
                                     ErrorCode.PARAM_ERROR)
                # 发送的内容
                content = '尊敬的用户:\r\n\r\n贵公司订舱的一票%s %s航班到%s的货物，已经到达我司仓库。\r\n入仓号为:%s' % (
                    customer_order.arrival_date, customer_order.airline_num, customer_order.receiver,
                    customer_order.order_num)
                if customer_order.tracking_num is not None:
                    content += '转单号为：%s' % customer_order.tracking_num
                expected = '\r\n预计到货件/重/体为:%dCTN(S)/%.2fKG(S)/%.2fCBM' % (
                    customer_order.pre_carton, customer_order.pre_weight, customer_order.pre_volume)
                actual = '\r\n实际到货件/重/体为:%dCTN(S)/%.2fKG(S)/%.2fCBM' % (
                    customer_order.carton, customer_order.weight, customer_order.volume)
                content = content + expected + actual + '\r\n\r\n详细尺寸明细如下（长*宽*高*件数  重量*件数）:\r\n'

                # 获取客户订单下的包裹并列出长宽高,根据尺寸的模式来获取尺寸。
                if customer_order.parceType:
                    parcels = Parcel.objects.filter(customer_order=customer_order.id, del_flag=False)
                    for item in parcels:
                        content = content + '%.1f*%.1f*%.1f*%d  %.1f*%d' % (
                            item.parcel_length, item.parcel_width, item.parcel_height, item.parcel_qty,
                            item.parcel_weight, item.parcel_qty) + '\r\n'
                else:
                    parcel_sizes = ParcelSize.objects.filter(customer_order=customer_order.id, del_flag=False)
                    for item in parcel_sizes:
                        content = content + '%.1f*%.1f*%.1f*%d  %.1f*%d' % (
                            item.parcel_length, item.parcel_width, item.parcel_height, item.parcel_qty,
                            item.parcel_weight, item.parcel_qty) + '\r\n'

                content = content + '\r\n订舱和实际到货误差超过3%,如有异议，请在半小时内同我司客服联系。\r\n超过1小时不确认，我司将视为贵公司默认。'

                # 发送邮件, 如果多个用分号隔开则循环发送
                send_str_email(subject, content, to_mails)

                # 标识账单已经发送
                customer_order.is_arrival_notice = True
                customer_order.save()

                # 插入一条轨迹
                product_codes = ProductTrackCode.objects.filter(product=customer_order.product,
                                                                del_flag=False).order_by('sort')
                if product_codes.count() > 0:
                    date = datetime.now().strftime('%Y-%m-%d')
                    set_customer_track('TR', date, customer_order.id, product_codes.first().track_code, 0, request)

        return success_response(request, "成功发送到货通知")

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_handle_times(self, request):
        """
        修改抓单任务的处理次数
        :param request:
        :return:
        """
        OrderLabelTask.objects.filter(id=request.data['id']).update(handle_times=request.data['num'])
        return success_response(request, "修改成功！")

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_sync_task_status(self, request):
        # print('data-->', request.data)
        if request.data['value'] != 'UnHandled':
            raise ParamError('任务状态只能改为未处理', ErrorCode.PARAM_ERROR)
        OrderSyncTask.objects.filter(id=request.data['id']).update(status=request.data['value'])
        return success_response(request, "修改成功！")

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_sync_task_handle_times(self, request):
        if not str(request.data['value']).isdecimal():
            raise ParamError('处理次数必须填写数字', ErrorCode.PARAM_ERROR)
        OrderSyncTask.objects.filter(id=request.data['id']).update(handle_times=request.data['value'])
        return success_response(request, "修改成功！")

    # @action(methods=['POST'], detail=False)
    # def get_order(self, request):
    #     """
    #     根据poNo获取面单是否存在系统
    #     :param request:
    #     :return:
    #     """
    #     poNo = request.data['poNo']
    #     if poNo is (None or ''):
    #         return fail_response(request, "po号必填")
    #
    #     count = CustomerOrder.objects.filter(po_no=poNo, del_flag=False).count()
    #     if count > 0:
    #         request.data['count'] = count
    #         return success_response(request, "已存在数据")
    #
    #     request.data['count'] = 0
    #     return success_response(request, "")

    @lock_request_common(['ref_num'], customer_restrict=True)
    @transaction.atomic
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def api_create(self, request):
        """
        通过api下单
        :param request:
        :return:
        """
        user = request.user
        parce_type = request.data.get('parceType') or True

        # 序列化
        serializer = CustomerOrderAndDetatilSerializer(data=request.data)
        # 验证数据
        serializer.is_valid()

        # validated_data = serializer.data
        # validated_data = serializer.validated_data
        validated_data = serializer.excluded_readonly_data
        logger.info(f'api订单创建-->入参1: {validated_data}')
        validated_data.pop('tracks', [])
        validated_data.pop('customerOrderRelateList', [])
        validated_data.pop('relative_debitAdjustDetails', [])
        validated_data.pop('masterOrderOut', [])
        validated_data.pop('oceanOrderOut', [])
        validated_data.pop('truck_order_pod', None)
        validated_data.pop('ocean_num_name', None)
        validated_data.pop('truck_order_num', None)
        validated_data.pop('orderAsyncTask', [])
        validated_data.pop('fields_change_logs', None)
        validated_data.pop('is_sync_yqf', None)
        validated_data.pop('shipments', None)
        validated_data.pop('shipper', None)
        customer_order_charge_in_list_data = validated_data.pop('customerOrderChargeIns', {})
        customer_order_charge_out_list_data = validated_data.pop('customerOrderChargeOuts', {})

        address_num = request.data.get('shipper', None)
        # if not address_num:
        #     raise ParamError('发件人地址(shipper)参数必填', ErrorCode.PARAM_ERROR)

        serializer.check_customer_status(user.company)
        # 去掉客户订单号首尾空白符
        validated_data['ref_num'] = (validated_data.get('ref_num') or '').strip()

        ref_num = validated_data.get('ref_num')
        old_order = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=ref_num,
                                                 customer=user.company,
                                                 del_flag=False)
        if old_order.count() > 0:
            raise ParamError(f'客户订单号不能重复: {ref_num}', ErrorCode.PARAM_ERROR)
        logger.info(f'1validated_data获取创建时间: {validated_data.get("create_date")}, '
                    f'获取拦截状态: {validated_data.get("is_intercept")}')
        customer_order = CustomerOrder.objects.create(**validated_data)
        customer_order.parceType = parce_type

        parcel_item_data = request.data.get('parcelItem')
        if not parcel_item_data:
            raise ParamError('包裹信息必填', ErrorCode.PARAM_ERROR)

        # parce_type = validated_data.get('parceType') or True

        if not customer_order.customer:
            company = user.company
            logger.info('canmapy =' + str(company))
            if company:
                customer_order.customer = company
                customer_order.saler = company.saler_name

        logger.info("ref_num =" + str(customer_order.ref_num) + ",customer" + str(customer_order.customer))

        service_code = request.data.get('service_code', None)
        product = order_import_verification_product_code(service_code, user)

        customer_order.product = product
        service_list = Service.objects.filter(product=product, del_flag=False, is_default=True)
        customer_order.service = service_list.first()

        if address_num:
            address_num = address_num.strip()
            address_queryset = Address.objects.filter(address_num=address_num, del_flag=False)
            if address_queryset.exists():
                save_shiper_address(address_queryset.first(), customer_order)
        else:
            # 兼容对方下单接口未来得及改的情况
            if product.address_num:
                address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
                if address_queryset.exists():
                    save_shiper_address(address_queryset.first(), customer_order)

        if not customer_order.shipper:
            raise ParamError(f'未填写发件人地址(shipper)参数, 并且未配置产品{product.name}的仓库', ErrorCode.PARAM_ERROR)

        # # 默认用产品设置地址，后客户传入地址
        # if product.address_num:
        #     address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
        #     if address_queryset.exists():
        #         save_shiper_address(address_queryset.first(), customer_order)
        # else:
        #     # 兼容warehouse_code
        #     address_num = request.data.get('shipper', None)
        #     if address_num:
        #         address_queryset = Address.objects.filter(address_num=address_num, del_flag=False)
        #         if address_queryset.exists():
        #             save_shiper_address(address_queryset.first(), customer_order)

        # customer_order.order_status = 'WO'
        order_status = request.data.get('order_status', 'WO')
        change_order_status(customer_order, order_status, request.user)

        # # 仓库代码(收件人)
        # warehouse_address = request.data.get('warehouse_address', None)
        # if warehouse_address:
        #     receiver_address = Address.objects.filter(address_num=warehouse_address, del_flag=False).first()
        #     if receiver_address:
        #         customer_order.receiver = receiver_address
        #     else:
        #         raise ParamError(f'找不到仓库地址: {warehouse_address}', ErrorCode.PARAM_ERROR)
        # customer_order.save()

        # 保存仓库收件人(如果能查到系统数据则会用系统数据覆盖收件人信息)
        save_api_create_address(request, customer_order)

        handler_create_order(customer_order, customer_order_charge_in_list_data,
                             customer_order_charge_out_list_data, parce_type, parcel_item_data, user,
                             is_summery_pre_data=True)

        # 从客户账号扣钱
        # if customer_order.product and customer_order.product.is_valuation:
        #     deduction_account(customer_order, user, CustomerOrderChargeIn)

        # create_order_label_task(customer_order, user, OrderLabelTask, Parcel)
        request.data['order_num'] = customer_order.order_num
        # 返回费用给客户
        # charge_in_list = CustomerOrderChargeIn.objects.filter(customer_order_num=customer_order, del_flag=False)
        # if charge_in_list.count() > 0:
        #     orderFee = 0
        #     currency = ''
        #     for charge_in in charge_in_list:
        #         orderFee += charge_in.charge_total
        #         if currency == '':
        #             currency = charge_in.currency_type
        #     request.data['orderFee'] = orderFee
        #     request.data['currency'] = currency
        # else:
        #     request.data['orderFee'] = 0
        #     request.data['currency'] = ''

        logger.info(f'2validated_data获取创建时间: {customer_order.create_date}, '
                    f'获取拦截状态: {customer_order.is_intercept}')

        return success_response(request, "Order successful")

    @lock_request_common(['ref_num'], customer_restrict=True)
    @transaction.atomic
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def api_update(self, request):
        """
        更新信息
        :param request:
        :return:
        """
        user = request.user
        parce_type = request.data.get('parceType') or True

        # 序列化
        serializer = CustomerOrderAndDetatilSerializer(data=request.data)
        # 验证数据
        serializer.is_valid()

        validated_data = serializer.data
        logger.info(f'api_update: {validated_data}')
        validated_data.pop('tracks', [])
        validated_data.pop('customerOrderRelateList', [])
        validated_data.pop('relative_debitAdjustDetails', [])
        validated_data.pop('masterOrderOut', [])
        validated_data.pop('oceanOrderOut', [])
        validated_data.pop('truck_order_pod', None)
        validated_data.pop('ocean_num_name', None)
        validated_data.pop('orderAsyncTask', [])
        validated_data.pop('fields_change_logs', None)
        validated_data.pop('is_sync_yqf', None)
        validated_data.pop('shipments', None)
        customer_order_charge_in_list_data = validated_data.pop('customerOrderChargeIns', {})
        customer_order_charge_out_list_data = validated_data.pop('customerOrderChargeOuts', {})

        serializer.check_customer_status(user.company)
        # 去掉客户订单号首尾空白符
        ref_num = (validated_data.get('ref_num') or '').strip()
        validated_data['ref_num'] = ref_num
        if not ref_num:
            raise ParamError('客户订单号不能为空', ErrorCode.PARAM_ERROR)

        # customer_order = CustomerOrder.objects.filter(order_num=ref_num, del_flag=False).first()
        customer_order = CustomerOrder.objects.filter(ref_num=ref_num, del_flag=False).first()
        if not customer_order:
            raise ParamError('订单未同步，请先同步订单', ErrorCode.PARAM_ERROR)

        customer_order.parceType = parce_type

        parcel_item_data = request.data.get('parcelItem')
        if not parcel_item_data:
            raise ParamError('包裹信息必填', ErrorCode.PARAM_ERROR)

        if not customer_order.customer:
            company = user.company
            logger.info('canmapy =' + str(company))
            if company:
                customer_order.customer = company
                customer_order.saler = company.saler_name

        logger.info("ref_num =" + str(customer_order.ref_num) + ",customer" + str(customer_order.customer))

        service_code = request.data.get('service_code', None)
        product = order_import_verification_product_code(service_code, user)

        customer_order.product = product
        service_list = Service.objects.filter(product=product, del_flag=False, is_default=True)
        customer_order.service = service_list.first()

        # 默认用产品设置地址，后客户传入地址
        if product.address_num:
            address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
            if address_queryset.exists():
                save_shiper_address(address_queryset.first(), customer_order)
        else:
            # 兼容warehouse_code
            address_num = request.data.get('shipper', None)
            if address_num:
                address_queryset = Address.objects.filter(address_num=address_num, del_flag=False)
                if address_queryset.exists():
                    save_shiper_address(address_queryset.first(), customer_order)

        # 保存仓库收件人(如果能查到系统数据则会用系统数据覆盖收件人信息)
        save_api_create_address(request, customer_order)

        # 导入更新同步到铭志前,把铭志得订单数据清除
        clean_fba_order_before_import(customer_order=customer_order, user=user, overwrite=True)

        handler_update_order(
            customer_order,
            customer_order_charge_in_list_data,
            customer_order_charge_out_list_data,
            parce_type,
            parcel_item_data,
            user,
            is_summery_pre_data=True
        )

        request.data['order_num'] = customer_order.order_num
        return success_response(request, "Order successful")

    # 通过api同步订单包裹的件重体数据
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_sync_order_data(self, request):
        order_num = request.data.get('order_num')
        if not order_num:
            raise ParamError('订单号是必填参数, 请检查', ErrorCode.PARAM_ERROR)
        # customer_order = CustomerOrder.objects.filter(order_num=order_num,
        customer_order = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=order_num,
                                                      del_flag=False).first()
        print('customer_order-->', customer_order)
        if not customer_order:
            raise ParamError(f'未查询到订单号: {order_num}', ErrorCode.PARAM_ERROR)
        parcels = customer_order.parcel.filter(del_flag=False)
        parcel_items = ParcelItem.objects.filter(parcel_num__in=parcels, del_flag=False)
        label_order_vo = LabelOrderVo()
        label_order_vo.customerOrder = customer_order
        order_dict = model_to_dict(customer_order)
        customer_order_dict = CreateCustomerOrder().__dict__
        customer_order_dict = convert_dict(customer_order_dict, order_dict)
        regroup_parcels = []
        for parcel_item in parcel_items:
            for parcel in parcels:
                if parcel == parcel_item.parcel_num:
                    order_parcel_dict = CreateParcel().__dict__
                    parcel_dict = model_to_dict(parcel)
                    order_parcel_dict = convert_dict(order_parcel_dict, parcel_dict)
                    print('order_parcel_dict-->', order_parcel_dict)
                    # parcel_item_dict = model_to_dict(parcel_item)
                    # order_parcel_dict = convert_dict(order_parcel_dict, parcel_item_dict)
                    order_parcel_dict['parcel_num'] = parcel.parcel_num
                    regroup_parcels.append(order_parcel_dict)
        customer_order_dict['parcelItem'] = regroup_parcels
        # # 将订单关联的卡派单中的pod文件转成base64传输
        # pod_file = None
        # if customer_order.pod_file:
        #     pod_file = customer_order.pod_file
        # else:
        #     if customer_order.truck_order_id:
        #         pod_file = customer_order.truck_order_id.transport_file
        # if pod_file:
        #     pod_file_content = base64.b64encode(pod_file.file.read()).decode('ascii')
        #     # pod_file_content = base64.b64encode(pod_file.file.read())
        #     customer_order_dict['pod_file'] = os.path.basename(pod_file.name)
        #     customer_order_dict['pod_file_base64'] = pod_file_content
        print('customer_order_dict-->\n', customer_order_dict)
        json_str = json.dumps(customer_order_dict, sort_keys=True, ensure_ascii=False, separators=(',', ':'),
                              cls=DecimalEncoder)
        json_data = json.loads(json_str)
        return Response(data={'code': 200, 'data': json_data, 'msg': 'success'}, status=status.HTTP_200_OK)

    # 壹起飞通过api接受铭志同步的件重体数据
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_accept_order_data(self, request):
        data = request.data.get('data')
        order_num = data.pop('ref_num', None)

        if not order_num:
            raise ParamError('订单号是必填参数, 请检查', ErrorCode.PARAM_ERROR)

        parcel_item_data = data.get('parcelItem')
        if not parcel_item_data:
            raise ParamError('铭志传参包裹信息不存在', ErrorCode.PARAM_ERROR)

        customer_order_dict = CreateCustomerOrder().__dict__
        customer_order_dict = convert_dict(customer_order_dict, data)
        order_status = customer_order_dict.get('order_status')

        customer_orders = CustomerOrder.objects.filter(order_num=order_num, del_flag=False)
        # customer_orders = CustomerOrder.objects.filter(ref_num=order_num, del_flag=False)
        # 如果订单已确认数据, 则不更新确认计费重和确认体积
        first_customer_order = customer_orders.first()

        if first_customer_order.confirm_charge_weight:
            customer_order_dict.pop('confirm_charge_weight', None)
        if first_customer_order.confirm_volume:
            customer_order_dict.pop('confirm_volume', None)
        if first_customer_order.check_in_time:
            customer_order_dict.pop('check_in_time', None)
        customer_order_dict.pop('remark', None)
        customer_orders.update(**customer_order_dict, update_date=datetime.now())

        parce_type = data.get('parceType') or True

        if parce_type:
            # 完整商品
            if parcel_item_data:
                for item in parcel_item_data:
                    print('item??-->', item)
                    exists_parcel = Parcel.objects.filter(customer_order=first_customer_order,
                                                          parcel_num=item['parcel_num'], del_flag=False).first()
                    if not exists_parcel:
                        print(
                            f'拉取订单件重体和状态等数据: 本地不存在订单号: {first_customer_order.order_num}, '
                            f'包裹号: {item["parcel_num"]}的包裹')
                        return
                    order_parcel_dict = CreateParcel().__dict__

                    order_parcel_dict = convert_dict(order_parcel_dict, item)
                    print('order_parcel_dict6-->', order_parcel_dict)
                    Parcel.objects.filter(id=exists_parcel.id).update(**order_parcel_dict,
                                                                      update_date=datetime.now())
        else:
            # 简易
            if parcel_item_data:
                for item in parcel_item_data:
                    exists_parcel = ParcelSize.objects.filter(customer_order=first_customer_order,
                                                              del_flag=False).first()
                    order_parcel_dict = CreateParcel().__dict__
                    order_parcel_dict = convert_dict(order_parcel_dict, item)
                    ParcelSize.objects.filter(id=exists_parcel.id).update(**order_parcel_dict,
                                                                          update_date=datetime.now())

        if order_status == 'AW':
            future_time = datetime.now() + timedelta(minutes=1)
            common_order_async_task(first_customer_order.order_num, 'TR', 'BL', first_customer_order.update_by,
                                    customer_order_num=first_customer_order.ref_num, start_time=future_time)
        logger.info(f'拉取订单件重体和状态等数据: {first_customer_order.order_num}已更新订单数据, 订单状态: {order_status}')
        return success_response_common('接收成功')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_sync_order_data_batch(self, request):
        order_nums = request.data.get('order_nums')
        if not order_nums:
            raise ParamError('订单号是必填参数, 请检查', ErrorCode.PARAM_ERROR)
        res_data = {}
        for order_num in order_nums:
            # customer_order = CustomerOrder.objects.filter(~Q(order_status='VO'), order_num=order_num,
            customer_order = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=order_num,
                                                          del_flag=False).first()
            print('customer_order-->', customer_order)
            if not customer_order:
                # raise ParamError(f'未查询到订单号: {order_num}', ErrorCode.PARAM_ERROR)
                logger.info(f'未查询到订单号: {order_num}')
                res_data[order_num] = '未查询到订单号'
                continue
            parcels = customer_order.parcel.filter(del_flag=False)
            parcel_items = ParcelItem.objects.filter(parcel_num__in=parcels, del_flag=False)
            label_order_vo = LabelOrderVo()
            label_order_vo.customerOrder = customer_order
            order_dict = model_to_dict(customer_order)
            customer_order_dict = CreateCustomerOrder().__dict__
            customer_order_dict = convert_dict(customer_order_dict, order_dict)
            regroup_parcels = []
            for parcel_item in parcel_items:
                for parcel in parcels:
                    if parcel == parcel_item.parcel_num:
                        order_parcel_dict = CreateParcel().__dict__
                        parcel_dict = model_to_dict(parcel)
                        order_parcel_dict = convert_dict(order_parcel_dict, parcel_dict)
                        print('order_parcel_dict-->', order_parcel_dict)
                        # parcel_item_dict = model_to_dict(parcel_item)
                        # order_parcel_dict = convert_dict(order_parcel_dict, parcel_item_dict)
                        order_parcel_dict['parcel_num'] = parcel.parcel_num
                        regroup_parcels.append(order_parcel_dict)
            customer_order_dict['parcelItem'] = regroup_parcels
            # # 将订单关联的卡派单中的pod文件转成base64传输
            # pod_file = None
            # if customer_order.pod_file:
            #     pod_file = customer_order.pod_file
            # else:
            #     if customer_order.truck_order_id:
            #         pod_file = customer_order.truck_order_id.transport_file
            # if pod_file:
            #     pod_file_content = base64.b64encode(pod_file.file.read()).decode('ascii')
            #     # pod_file_content = base64.b64encode(pod_file.file.read())
            #     customer_order_dict['pod_file'] = os.path.basename(pod_file.name)
            #     customer_order_dict['pod_file_base64'] = pod_file_content
            print('customer_order_dict-->\n', customer_order_dict)
            json_str = json.dumps(customer_order_dict, sort_keys=True, ensure_ascii=False, separators=(',', ':'),
                                  cls=DecimalEncoder)
            json_data = json.loads(json_str)
            # 组装返回数据
            res_data[order_num] = json_data

        return Response(data={'code': 200, 'data': res_data, 'msg': 'success'}, status=status.HTTP_200_OK)

    # 通过api同步订单签收时的卡派POD文件
    @action(methods=['POST'], detail=False)
    def api_sync_order_pod_file(self, request):
        order_nums = request.data.get('order_nums')
        if not order_nums:
            raise ParamError('订单号是必填参数, 请检查', ErrorCode.PARAM_ERROR)
        # customer_orders = CustomerOrder.objects.filter(order_num__in=order_nums,
        customer_orders = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num__in=order_nums,
                                                       del_flag=False)
        print('customer_order-->', customer_orders)
        if not customer_orders.exists():
            raise ParamError(f'未查询到给定订单号: {order_nums}', ErrorCode.PARAM_ERROR)
        order_pod_data = []
        for customer_order in customer_orders:
            customer_order_dict = {
                'order_num': customer_order.ref_num,
                'pod_file': None,
                'pod_file_base64': None,
            }
            # 将订单关联的卡派单中的pod文件转成base64传输
            pod_file = None
            if customer_order.pod_file:
                pod_file = customer_order.pod_file
            else:
                # if customer_order.truck_order_id:
                #     pod_file = customer_order.truck_order_id.transport_file
                relate_trucks = CustomerOrderRelateTruck.objects.filter(customer_order_num=customer_order,
                                                                        del_flag=False)
                if relate_trucks.exists():
                    truck_order = TruckOrder.objects.filter(Q(transport_file__isnull=False) & ~Q(transport_file=''),
                                                            id__in=relate_trucks.values_list('truck_order', flat=True),
                                                            del_flag=False).last()
                    if truck_order:
                        # request_url = self.context['request'].build_absolute_uri()
                        pod_file = truck_order.transport_file

            if pod_file:
                pod_file_content = base64.b64encode(pod_file.file.read()).decode('ascii')
                # pod_file_content = base64.b64encode(pod_file.file.read())
                customer_order_dict['pod_file'] = os.path.basename(pod_file.name)
                customer_order_dict['pod_file_base64'] = pod_file_content
            order_pod_data.append(customer_order_dict)
        json_str = json.dumps(order_pod_data, sort_keys=True, ensure_ascii=False, separators=(',', ':'),
                              cls=DecimalEncoder)
        json_data = json.loads(json_str)
        return Response(data={'code': 200, 'data': json_data, 'msg': 'success'}, status=status.HTTP_200_OK)

    # 壹起飞通过api接受铭志同步的pod_file数据
    @action(methods=['POST'], detail=False)
    def api_accept_order_pod_file(self, request):
        data = request.data.get('data', {})
        order_nums = data.get('ref_nums', None)
        pod_file = data.get('pod_file', None)

        if not order_nums:
            raise ParamError('订单号是必填参数, 请检查', ErrorCode.PARAM_ERROR)

        if not pod_file:
            raise ParamError('pod_file是必填参数, 请检查', ErrorCode.PARAM_ERROR)

        customer_orders = CustomerOrder.objects.filter(~Q(order_status='VO'), order_num__in=order_nums, del_flag=False)
        if not customer_orders.exists():
            raise ParamError(f'壹起飞查不到订单{order_nums}', ErrorCode.PARAM_ERROR)

        # 保存卡派pod文件
        pod_file_url = "files/" + (datetime.now().strftime("%Y/%m/%d/")) + pod_file
        # logger.info(f'拉取订单pod文件成功: {customer_order.order_num}接收pod文件pod_file_url: {pod_file_url}')
        decoded_file_name = MEDIA_URL + pod_file_url
        file_dir = os.path.dirname(decoded_file_name)
        if not os.path.exists(file_dir):
            os.makedirs(file_dir)

        pod_file_base64 = data.get('pod_file_base64')
        with open(decoded_file_name, "wb") as code:
            code.write(base64.b64decode(pod_file_base64))
        customer_orders.update(pod_file=pod_file_url)

        return success_response_common('接收成功')


    # 铭志接收壹起飞修改订单备注
    @action(methods=['POST'], detail=False)
    def api_accept_order_remark(self, request):
        data = request.data.get('data', {})
        ref_num = data.get('ref_num', None)
        remark = data.get('remark', None)

        logger.info(f'进入接收订单备注接口: data')

        if not ref_num:
            raise ParamError('订单号是必填参数, 请检查', ErrorCode.PARAM_ERROR)

        # customer_orders = CustomerOrder.objects.filter(order_num=ref_num, del_flag=False)
        customer_orders = CustomerOrder.objects.filter(ref_num=ref_num, del_flag=False)
        if not customer_orders.exists():
            raise ParamError(f'铭志查不到客户订单{ref_num}', ErrorCode.PARAM_ERROR)

        customer_orders.update(remark=remark)

        return success_response_common('接收备注成功')

    @action(methods=['POST'], detail=False)
    def api_sync_order_pod_file_batch(self, request):
        order_nums = request.data.get('order_nums')
        if not order_nums:
            raise ParamError('订单号是必填参数, 请检查', ErrorCode.PARAM_ERROR)
        # customer_orders = CustomerOrder.objects.filter(order_num__in=order_nums,
        customer_orders = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num__in=order_nums,
                                                       del_flag=False)
        print('customer_order-->', customer_orders)
        if not customer_orders.exists():
            raise ParamError(f'未查询到给定订单号: {order_nums}', ErrorCode.PARAM_ERROR)
        # order_pod_data = []
        res_data = {}
        for customer_order in customer_orders:
            customer_order_dict = {
                'order_num': customer_order.ref_num,
                'pod_file': None,
                'pod_file_base64': None,
            }
            # 将订单关联的卡派单中的pod文件转成base64传输
            pod_file = None
            if customer_order.pod_file:
                pod_file = customer_order.pod_file
            else:
                # if customer_order.truck_order_id:
                #     pod_file = customer_order.truck_order_id.transport_file
                relate_trucks = CustomerOrderRelateTruck.objects.filter(customer_order_num=customer_order,
                                                                        del_flag=False)
                if relate_trucks.exists():
                    truck_order = TruckOrder.objects.filter(Q(transport_file__isnull=False) & ~Q(transport_file=''),
                                                            id__in=relate_trucks.values_list('truck_order', flat=True),
                                                            del_flag=False).last()
                    if truck_order:
                        # request_url = self.context['request'].build_absolute_uri()
                        pod_file = truck_order.transport_file

            if pod_file:
                pod_file_content = base64.b64encode(pod_file.file.read()).decode('ascii')
                # pod_file_content = base64.b64encode(pod_file.file.read())
                customer_order_dict['pod_file'] = os.path.basename(pod_file.name)
                customer_order_dict['pod_file_base64'] = pod_file_content
            else:
                logger.info(f'订单「{customer_order.order_num}」没有卡派pod')
            # order_pod_data.append(customer_order_dict)

            res_data[customer_order.ref_num] = customer_order_dict
            # res_data[customer_order.order_num] = customer_order_dict

        json_str = json.dumps(res_data, sort_keys=True, ensure_ascii=False, separators=(',', ':'),
                              cls=DecimalEncoder)
        json_data = json.loads(json_str)
        return Response(data={'code': 200, 'data': json_data, 'msg': 'success'}, status=status.HTTP_200_OK)

    # api推送订单收入
    @lock_request_common(['order_num'])
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_set_order_revenue(self, request):
        order_num = request.data.get('order_num')
        # user = request.user
        if not order_num:
            return fail_response_common('订单号必填')

        customer_order_queryset = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=order_num, del_flag=False)
        if not customer_order_queryset.exists():
            return fail_response_common(f'未找到客户订单: {order_num}')

        customer_order = customer_order_queryset.first()
        if customer_order.is_revenue_lock:
            return fail_response_common(f'订单已收入确认, 请收入解锁后再同步')
        # yqf同步订单收入过来时把所有已存在的收入先删掉
        charge_in_queryset = CustomerOrderChargeIn.objects.filter(customer_order_num=customer_order,
                                                                  del_flag=False)
        charge_in_queryset.update(del_flag=True)

        revenue_data = request.data.get('revenue_data')
        if not revenue_data:
            return success_response_common('没有收入信息, 仅删掉此系统的所有已存在的收入')

        revenue_arr = []
        for revenue in revenue_data:
            charge_code = revenue.pop('charge_code')
            # charge_rate = revenue['charge_rate']
            # charge_total = revenue['charge_total']
            charge_name = revenue.pop('charge_name')
            currency_type = revenue['currency_type']
            charge_queryset = Charge.objects.filter(code=charge_code, del_flag=False)
            if not charge_queryset.exists():
                return fail_response_common(f'费用项不存在{charge_name}')

            charge = charge_queryset.first()
            # # 如果有重复费用, 则软删除, 然后新增费用, 费用yqf同步过来的为准
            # charge_in_queryset = CustomerOrderChargeIn.objects.filter(customer_order_num=customer_order, charge=charge,
            #                                                           # charge_rate=charge_rate,
            #                                                           # charge_total=charge_total,
            #                                                           currency_type=currency_type,
            #                                                           # data_source='P',
            #                                                           del_flag=False)
            # # 存在相同的费用则软删除
            # if charge_in_queryset.exists():
            #     charge_in_queryset.update(charge_rate=revenue['charge_rate'],
            #                               charge_count=revenue['charge_count'],
            #                               charge_total=revenue['charge_total'])
            #     continue

            revenue['charge'] = charge
            revenue_arr.append(revenue)
        for revenue in revenue_arr:
            CustomerOrderChargeIn.objects.create(**revenue,
                                                 customer=customer_order.customer,
                                                 data_source='P',
                                                 customer_order_num=customer_order,
                                                 **get_update_params(request, True))
        return success_response_common(f'推送成功')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_get_order_cost(self, request):
        order_num = request.data.get('order_num')
        user = request.user
        if not order_num:
            return fail_response(request, '订单号必填')

        customer_order_queryset = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=order_num, del_flag=False)
        if not customer_order_queryset.exists():
            return fail_response(request, f'未找到客户订单: {order_num}')

        customer_order = customer_order_queryset.first()
        if not customer_order.is_cost_lock:
            return fail_response(request, f'成本还未确认')

        charge_out_queryset = CustomerOrderChargeOut.objects.filter(customer_order_num=customer_order, del_flag=False)

        data = []
        for charge_out in charge_out_queryset:
            data.append({
                'charge_code': charge_out.charge.code,
                'charge_name': charge_out.charge.name,
                'charge_rate': charge_out.charge_rate,
                'charge_count': charge_out.charge_count,
                'charge_total': charge_out.charge_total,
                'currency_type': charge_out.currency_type,
                'current_exchange': charge_out.current_exchange,
                'account_charge': charge_out.account_charge,
            })

        return Response(data={'code': 200, 'data': data, 'msg': 'success'}, status=status.HTTP_200_OK)


    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_get_order_cost_batch(self, request):
        order_nums = request.data.get('order_nums')
        if not order_nums:
            return fail_response(request, '订单号必填')
        res_data = {}
        for order_num in order_nums:
            customer_order_queryset = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=order_num, del_flag=False)
            # customer_order_queryset = CustomerOrder.objects.filter(~Q(order_status='VO'), order_num=order_num, del_flag=False)
            if not customer_order_queryset.exists():
                # return fail_response(request, f'未找到客户订单: {order_num}')
                logger.info(f'未找到客户订单: {order_num}')
                res_data[order_num] = f'未找到客户订单: {order_num}'
                continue

            customer_order = customer_order_queryset.first()
            if not customer_order.is_cost_lock:
                # return fail_response(request, f'成本还未确认')
                logger.info(f'成本还未确认')
                res_data[order_num] = f'成本还未确认'
                continue

            charge_out_queryset = CustomerOrderChargeOut.objects.filter(customer_order_num=customer_order, del_flag=False)

            data = []
            for charge_out in charge_out_queryset:
                data.append({
                    'charge_code': charge_out.charge.code,
                    'charge_name': charge_out.charge.name,
                    'charge_rate': charge_out.charge_rate,
                    'charge_count': charge_out.charge_count,
                    'charge_total': charge_out.charge_total,
                    'currency_type': charge_out.currency_type,
                    'current_exchange': charge_out.current_exchange,
                    'account_charge': charge_out.account_charge,
                })

            res_data[order_num] = data

        return Response(data={'code': 200, 'data': res_data, 'msg': 'success'}, status=status.HTTP_200_OK)

    # 获取面单
    @action(methods=['POST'], detail=False)
    def api_get_label(self, request):
        '''
        下面面单接口
        :param request:
        :return:
        '''
        order_num = request.data['order_num']
        customer_orders = CustomerOrder.objects.filter(order_num=order_num, del_flag=False)
        if customer_orders.count() == 0:
            raise ParamError('查无此订单' + str(order_num), ErrorCode.PARAM_ERROR)

        customer_order = customer_orders.first()

        order_label_task_list = OrderLabelTask.objects.filter(order_num=customer_order, del_flag=False).order_by('-id')
        if order_label_task_list.count() == 0:
            raise ParamError('无面单任务，请联系客服', ErrorCode.PARAM_ERROR)

        order_label_task = order_label_task_list.first()
        if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
            if order_label_task.label_desc:
                raise ParamError(order_label_task.label_desc, ErrorCode.PARAM_ERROR)
            else:
                raise ParamError('面单还未生成，请稍后再试', ErrorCode.PARAM_ERROR)
        elif order_label_task.status == 'VO':
            raise ParamError('面单已作废', ErrorCode.PARAM_ERROR)

        order_label_list = OrderLabel.objects.filter(order_num=customer_order, del_flag=False)
        if len(order_label_list) == 0:
            raise ParamError('面单还未生成，请稍后再试', ErrorCode.PARAM_ERROR)

        label_path = settings.STATIC_MEDIA_DIR
        file_url = 'http://' + settings.DOMAIN_URL + '/media/'

        label_list = []

        for order_label in order_label_list:
            file_url_str = file_url + order_label.label_url
            label_path_str = label_path + order_label.label_url

            with open(label_path_str, 'rb') as f:
                base64_data = base64.b64encode(f.read())
                base64_str = base64_data.decode()

            label_dict = {
                "tracking_no": order_label.tracking_no,
                "label": file_url_str,
                "label_base64": base64_str
            }
            label_list.append(label_dict)

        request.data['label_infos'] = label_list

        if customer_order.tracking_num:
            request.data['main_tracking_no'] = customer_order.tracking_num
        else:
            main_tracking_no = order_label_list.first().tracking_no
            request.data['main_tracking_no'] = main_tracking_no
            customer_order.tracking_num = main_tracking_no

        # 下载面单次数加1
        download_num = customer_order.download_num or 0
        customer_order.download_num = download_num + 1
        customer_order.save()

        return success_response(request, '')

    @action(methods=['POST'], detail=False)
    def api_cancel_label(self, request):
        '''
        取消面单接口
        :param request:
        :return:
        '''
        order_num = request.data['order_num']
        customer_orders = CustomerOrder.objects.filter(order_num=order_num, del_flag=False)
        if customer_orders.count() == 0:
            raise ParamError('查无此订单' + str(order_num), ErrorCode.PARAM_ERROR)
        customer_order = customer_orders.first()

        if customer_order.is_cost_lock or customer_order.is_revenue_lock:
            return success_response(request, '收入或成本已确认，不允许作废')

        if customer_order.order_status == 'VO':
            return success_response(request, '作废成功')

        order_label_task_list = OrderLabelTask.objects.filter(~Q(status='VO'), order_num=customer_order.id,
                                                              del_flag=False)
        if len(order_label_task_list) == 0:
            raise ParamError('无可作废订单 id=' + str(order_num), ErrorCode.PARAM_ERROR)

        for order_label_task in order_label_task_list:
            user = request.user

            key = 'handler_label_' + customer_order.order_num
            val = cache.get(key)
            if val:
                raise ParamError('订单处理中暂不能作废' + str(order_num), ErrorCode.PARAM_ERROR)

            key = 'handler_get_label_' + customer_order.order_num
            val = cache.get(key)
            if val:
                raise ParamError('订单获取面单处理中暂不能作废' + str(order_num), ErrorCode.PARAM_ERROR)

            if not order_label_task.third_order_no and not val:
                order_label_task.status = 'VO'
                order_label_task.label_desc = '已作废,如已获取面单，请及时通知供应商'
                order_label_task.update_by = user
                order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                order_label_task.save()

                return self.handler_cancel_succ(customer_order, request, user)

            else:
                # 兼容无服务的订单
                product = Product.objects.get(id=customer_order.product.id)
                if not customer_order.service:
                    service_list = Service.objects.filter(product=product.id, del_flag=False)
                    if service_list.count() == 0:
                        raise ParamError('产品[' + product.code + ']未配置服务', ErrorCode.PARAM_ERROR)
                    service = service_list.first()
                else:
                    service = customer_order.service

                # 如果是虚拟产品，以最小成本的产品打单
                if product.is_virtual:
                    product = customer_order.real_product
                    service = Service.objects.filter(product=product, del_flag=False).first()

                supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
                if not supplier_butt.is_support_cancel:
                    raise ParamError('产品[' + product.code + ']不支持取消', ErrorCode.PARAM_ERROR)
                supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id,
                                                                           del_flag=False)
                supplier_account = supplier_account_list[0]
                class_name = supplier_butt.class_name
                label_order_vo = LabelOrderVo()
                label_order_vo.orderLabelTask = order_label_task
                label_order_vo.supplierAccount = supplier_account
                label_order_vo.service = service
                label_order_vo.customerOrder = customer_order
                # 通过反射实例化对象
                obj = globals()[class_name]()
                result = cancel_label(obj, label_order_vo)
                if result['code'] == '0':
                    order_label_task.status = 'VO'
                    order_label_task.label_desc = '面单已作废'
                    order_label_task.update_by = user
                    order_label_task.update_date = datetime.now()
                    order_label_task.save()

                    return self.handler_cancel_succ(customer_order, request, user)

                elif result['code'] == '400':
                    return fail_response(request, '订单' + customer_order.order_num + '作废失败' + str(result['msg']))
                else:
                    return fail_response(request, '订单' + customer_order.order_num + '作废失败')

    def handler_cancel_succ(self, customer_order, request, user):
        # 作废订单
        order_label_list = OrderLabel.objects.filter(order_num=customer_order, del_flag=False)
        if order_label_list.count() > 0:
            order_label_list.update(del_flag=True)
        # customer_order.order_status = 'VO'
        change_order_status(customer_order, 'VO', request.user)
        customer_order.update_date = datetime.now()
        customer_order.save()
        # 退款给客户
        refund_account(customer_order, user, CustomerOrderChargeIn)
        return success_response(request, '作废成功')

    # 批量搜索(批量查询)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def multi_search(self, request):
        # 由于self.get_serializer()获取到的序列化器是CustomerOrderAndDetatilSerializer, 里面部分内容和list序列化器不一致,
        # 有的字段值就不一样
        user = request.user
        # 根据部门查看部门数据
        department = user.department
        filter_prop = request.data.get('filterProp', {})
        order_type = filter_prop.get('orderType', None)
        if department and department.relate_customer:
            customer_arr = department.relate_customer.split(',')
            self.queryset = self.queryset.filter(customer__in=list(map(int, customer_arr)))
        if settings.SYSTEM_ORDER_MARK in ['CLT'] and order_type and order_type == 'FBM':
            self.queryset = self.queryset.filter(~Q(order_status='VO'))

        return get_multi_search(self, request, serializer=CustomerOrderSerializer)
        # return success_response(request, "success!")

    # 上传订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def upload_excel(self, request):
        user = request.user
        excel = request.FILES.get('file')
        # 打开excel
        wb = xlrd.open_workbook(filename=None, file_contents=excel.read())
        # 获取第一张表
        table = wb.sheets()[0]
        nrows = table.nrows  # 行数

        count = 1

        # 订单新增
        all_customer_orders = {}
        for index in range(2, nrows):

            nums = table.cell_value(index, 4 + count)
            for num in range(1, int(nums) + 1):
                ref_num = str(table.cell_value(index, 0 + count)).strip().replace('.0', '')
                if all_customer_orders.get(ref_num, None) is None:
                    order_params = {
                        'ref_num': ref_num,
                    }
                    if table.cell_value(index, 0):
                        try:
                            customer = Company.objects.get(short_name=str(table.cell_value(index, 0)).strip(),
                                                           is_customer=True,
                                                           del_flag=False)
                        except Company.DoesNotExist:
                            raise ParamError('客户编码：%s系统找不到！' % (table.cell_value(index, 0)),
                                             ErrorCode.PARAM_ERROR)
                    product = None
                    if table.cell_value(index, 1 + count):
                        try:
                            product = Product.objects.get(code=str(table.cell_value(index, 1 + count)).strip(),
                                                          type='TR', del_flag=False)
                        except Product.DoesNotExist:
                            raise ParamError('产品编码：%s系统找不到！' % (table.cell_value(index, 1 + count)),
                                             ErrorCode.PARAM_ERROR)
                    shipper = None
                    if table.cell_value(index, 2 + count):
                        try:
                            # 产品默认地址优先，后客户传入地址
                            if product and product.address_num:
                                address_num = product.address_num
                            else:
                                address_num = str(table.cell_value(index, 2 + count)).strip()
                            shipper = Address.objects.get(address_num=address_num, address_type='SP', del_flag=False)

                            order_params['address_num'] = shipper.address_num  # 地址编码
                            order_params['contact_name'] = shipper.contact_name  # 联系人
                            order_params['contact_email'] = shipper.contact_email  # 邮箱
                            order_params['contact_phone'] = shipper.contact_phone  # 电话
                            order_params['country_code'] = shipper.country_code  # 国家编码
                            order_params['state_code'] = shipper.state_code  # 省份(州)编码
                            order_params['city_code'] = shipper.city_code  # 城市编码
                            order_params['postcode'] = shipper.postcode  # 邮编
                            order_params['house_no'] = shipper.house_no  # 门牌号
                            order_params['address_one'] = shipper.address_one  # 地址行1
                            order_params['address_two'] = shipper.address_two  # 地址行2
                            order_params['company_name'] = shipper.company_name  # 公司名
                        except Address.DoesNotExist:
                            raise ParamError('Warehouse Code：%s系统找不到！' % (table.cell_value(index, 2 + count)),
                                             ErrorCode.PARAM_ERROR)
                    receiver = None
                    if table.cell_value(index, 3 + count):
                        try:
                            receiver = Address.objects.get(address_num=str(table.cell_value(index, 3 + count)).strip(),
                                                           address_type='RC', del_flag=False)
                            order_params['buyer_address_num'] = receiver.address_num  # 地址编码
                            order_params['buyer_name'] = receiver.contact_name  # 联系人
                            order_params['buyer_mail'] = receiver.contact_email  # 邮箱
                            order_params['buyer_phone'] = receiver.contact_phone  # 电话
                            order_params['buyer_country_code'] = receiver.country_code  # 国家编码
                            order_params['buyer_state'] = receiver.state_code  # 省份(州)编码
                            order_params['buyer_city_code'] = receiver.city_code  # 城市编码
                            order_params['buyer_postcode'] = receiver.postcode  # 邮编
                            order_params['buyer_house_num'] = receiver.house_no  # 门牌号
                            order_params['buyer_address_one'] = receiver.address_one  # 地址行1
                            order_params['buyer_address_two'] = receiver.address_two  # 地址行2
                            order_params['buyer_company_name'] = receiver.company_name  # 公司名
                        except Address.DoesNotExist:
                            raise ParamError('FBA Code：%s系统找不到！' % (table.cell_value(index, 3 + count)),
                                             ErrorCode.PARAM_ERROR)

                    order_params['customer'] = customer  # 客户
                    order_params['saler'] = customer.saler_name  # 销售负责人
                    order_params['product'] = product  # 产品编码
                    service_list = Service.objects.filter(product=product, del_flag=False, is_default=True)
                    if service_list.count() == 0:
                        raise ParamError('产品[' + product.code + ']未配置服务', ErrorCode.PARAM_ERROR)

                    order_params['service'] = service_list.first()  # 产品服务
                    order_params['shipper'] = shipper  # 发件人
                    order_params['receiver'] = receiver  # 收件人
                    customer_order = CustomerOrder.objects.create(**order_params, **get_update_params(request, True))
                    # customer_order.order_num = settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK + create_order_num(
                    #     customer_order.id, 5)
                    gen_order_num(customer_order)
                    customer_order.save()
                    all_customer_orders[str(ref_num).strip()] = customer_order
                    # 保存到附件中
                    OrderAttachment.objects.create(name=excel, url=excel, customerOrder=customer_order,
                                                   **get_update_params(request, True))

        all_order_parcel = {}
        # 包裹新增
        for index in range(2, nrows):
            nums = table.cell_value(index, 4 + count)
            for num in range(1, int(nums) + 1):
                ref_num = str(table.cell_value(index, 0 + count)).strip().replace('.0', '')
                parcel_num = ref_num + str(index) + str(num).strip()
                if all_order_parcel.get(parcel_num, None) is None:
                    parcel_params = {
                        'parcel_num': parcel_num,
                        'parcel_desc': '',
                        'parcel_length': table.cell_value(index, 5 + count),
                        'parcel_width': table.cell_value(index, 6 + count),
                        'parcel_height': table.cell_value(index, 7 + count),
                        'parcel_weight': table.cell_value(index, 8 + count),
                        'label_length': table.cell_value(index, 5 + count),
                        'label_width': table.cell_value(index, 6 + count),
                        'label_height': table.cell_value(index, 7 + count),
                        'label_weight': table.cell_value(index, 8 + count),
                        'parcel_volume': table.cell_value(index, 5 + count) * table.cell_value(
                            index, 6 + count) * table.cell_value(index, 7 + count) / 1000000,
                        'customer_order': all_customer_orders[ref_num]
                    }
                    parcel = Parcel.objects.create(**parcel_params, **get_update_params(request, True))
                    all_order_parcel[parcel_num] = parcel

        # 商品新增
        for index in range(2, nrows):
            nums = table.cell_value(index, 4 + count)
            ref_num = str(table.cell_value(index, 0 + count)).strip().replace('.0', '')
            for num in range(1, int(nums) + 1):
                parcel_num = ref_num + str(index) + str(num).strip()
                sku_params = {
                    'item_code': table.cell_value(index, 9 + count),
                    'item_name': table.cell_value(index, 13 + count),
                    'item_qty': table.cell_value(index, 10 + count),
                    'sale_price': 1,
                    'sale_currency': '',
                    'declared_price': table.cell_value(index, 11 + count),
                    'declared_currency': table.cell_value(index, 12 + count),
                    'declared_nameCN': table.cell_value(index, 13 + count),
                    'declared_nameEN': table.cell_value(index, 14 + count),
                    'item_size': '',
                    'texture': table.cell_value(index, 15 + count),
                    'use': table.cell_value(index, 16 + count),
                    'brand': table.cell_value(index, 17 + count),
                    'model': table.cell_value(index, 18 + count),
                    'customs_code': str(table.cell_value(index, 19 + count)).strip().replace('.0', ''),
                    'fba_no': table.cell_value(index, 21 + count),
                    'fba_track_code': table.cell_value(index, 22 + count),
                    'parcel_num': all_order_parcel[parcel_num]
                }
                ParcelItem.objects.create(**sku_params, **get_update_params(request, True))

        # 汇总预计数量，体检重
        for customer_order_key in all_customer_orders:
            print("=customer_order==>" + customer_order_key)
            customer_order = all_customer_orders[customer_order_key]
            parcel_queryset = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
            parcel_list = parcel_queryset.values('customer_order').annotate(label_weight=Sum('label_weight'),
                                                                            parcel_volume=Sum('parcel_volume'),
                                                                            parcel_qty=Sum('parcel_qty'))
            for parcel in parcel_list:
                customer_order.pre_carton = parcel['parcel_qty']
                customer_order.pre_weight = parcel['label_weight']
                customer_order.pre_volume = parcel['parcel_volume']
                # customer_order.charge_trans = 167
                customer_order.charge_trans = 6000
                # 计费重量 = 体积 / 计费转换乘率     和    重量相比较   谁大取谁
                num1 = (Decimal(customer_order.pre_volume) / Decimal(customer_order.charge_trans))
                if num1 > Decimal(customer_order.pre_weight):
                    customer_order.charge_weight = num1
                else:
                    customer_order.charge_weight = Decimal(customer_order.pre_weight)
                customer_order.save()

        # 用完记得删除释放资源
        wb.release_resources()
        del wb
        return Response(data={'code': 200, 'data': {}, 'msg': '导入成功'}, status=status.HTTP_200_OK)

    # 异步导入订单
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def upload_file_sync_fba(self, request):
        # 这里还是单文件上传, 不过前端允许一次性选择多个文件, 但其实是多次调用接口进行单个文件上传
        return sync_upload_file_common(request, 'TR', 'UploadOrderExcel')

    # 运输 - 智融航-FBA订单: 导入FBA订单(excel)
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def upload_file_sync_fba_zrh(self, request):
        # 这里还是单文件上传, 不过前端允许一次性选择多个文件, 但其实是多次调用接口进行单个文件上传
        print("upload_file_sync_fba_zrh")
        return sync_upload_file_common(request, 'TR', 'UploadOrderExcel', goods_type='ZRH')

    # 运输 - FBA订单: 导入FBA订单(excel) (改为异步, 弃用)
    @transaction.atomic
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def upload_excel_fba(self, request):
        excel = request.FILES.get('file')
        # 将文件内容读取到内存中
        file_content = excel.read()
        # 使用 io.BytesIO 将内容包装成文件对象
        file_object = io.BytesIO(file_content)
        # 读取 Excel
        workbook = openpyxl.load_workbook(file_object)
        # 获取第一张表
        table = workbook.worksheets[0]
        # 行数
        line_number = table.max_row

        # 订单新增
        ref_num = get_excel_cell(3, 'b', table)
        if CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=ref_num, del_flag=False).exists():
            raise ParamError(f'客户订单号不能重复： {ref_num}', ErrorCode.PARAM_ERROR)

        # 获取产品
        product_code = get_excel_cell(9, 'b', table)
        # product = Product.objects.filter(code=product_code, del_flag=False).first()
        product = Product.objects.filter(name=product_code, del_flag=False).first()
        if product is None:
            raise ParamError(f'产品编码『{product_code}』系统找不到或存在重复', ErrorCode.PARAM_ERROR)
        if product.status == 'OFF':
            raise ParamError('此产品服务已被停用' + str(product_code), ErrorCode.PARAM_ERROR)

        # 获取客户
        company = None
        company_short_name = get_excel_cell(9, 'f', table)
        if company_short_name:
            company_short_name = str(company_short_name).strip()
            company = Company.objects.filter(short_name=company_short_name, is_customer=True, del_flag=False).first()
            if company is None:
                raise ParamError(f'未查到客户编码 {company_short_name}', ErrorCode.PARAM_ERROR)

        if company and company.status == 'OFF':
            raise ParamError(f'公司: {company.name}已被停用', ErrorCode.PARAM_ERROR)

        # 权限检查
        user_profile = UserProfile.objects.filter(username=request.user.username).first()
        department = user_profile.department
        if department and department.relate_customer and company.id not in list(
                map(int, department.relate_customer.split(','))):
            raise ParamError(f'没有权限访问 {company.name}', ErrorCode.PARAM_ERROR)

        # 处理收件地址
        warehouse_address = get_excel_cell(4, 'i', table)
        private_address = get_excel_cell(6, 'i', table)
        buyer_address_num = warehouse_address or private_address
        if not buyer_address_num:
            raise ParamError(f'收件地址『{product_code}』不存在', ErrorCode.PARAM_ERROR)

        if buyer_address_num:
            buyer_address_num = str(buyer_address_num).strip()
            buyer_address_queryset = Address.objects.filter(address_num=buyer_address_num, del_flag=False)
            if buyer_address_queryset:
                buyer_address = buyer_address_queryset.first()
            else:
                if warehouse_address:
                    buyer_address = save_address(buyer_address_num, table)
                else:
                    buyer_address = save_address(buyer_address_num, table, is_warehouse_address=False)
            # print('buyer_address-->', buyer_address)
        else:
            raise ParamError(f'产品编码『{product_code}』系统找不到或存在重复', ErrorCode.PARAM_ERROR)

        # 如果没有发件人 默认用产品
        contact_name = get_excel_cell(4, 'b', table)
        shipper_address = {}
        if contact_name:
            postcode = get_excel_cell(8, 'b', table)
            if not postcode:
                raise ParamError(f'发件人邮编必填', ErrorCode.PARAM_ERROR)
            shipper_address = {
                # 发件人
                'contact_name': get_excel_cell(4, 'b', table),
                'company_name': get_excel_cell(5, 'b', table),
                'address_one': get_excel_cell(6, 'b', table),
                'city_code': get_excel_cell(7, 'b', table),
                'state_code': get_excel_cell(7, 'f', table),
                'postcode': postcode,
                'contact_phone': get_excel_cell(8, 'f', table),
                'country_code': 'CN'
            }
        else:
            if product.address_num:
                address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
                if address_queryset:
                    seller_address = address_queryset.first()
                    shipper_address = {
                        # 发件人
                        'shipper': seller_address,
                        'contact_name': seller_address.contact_name,
                        'company_name': seller_address.company_name,
                        'address_one': seller_address.address_one,
                        'city_code': seller_address.city_code,
                        'state_code': seller_address.state_code,
                        'postcode': seller_address.postcode,
                        'contact_phone': seller_address.contact_phone,
                        'country_code': 'CN'
                    }

        order_params = {
            'customer': company,
            'saler': company.saler_name if company else None,
            'product': product,
            'ref_num': ref_num,
            # 收件人, 私人地址不填外键
            'receiver': buyer_address if warehouse_address else None,
            'buyer_address_num': buyer_address.address_num,
            'buyer_name': buyer_address.contact_name,
            'buyer_company_name': buyer_address.company_name,
            'buyer_country_code': buyer_address.country_code,
            'buyer_postcode': buyer_address.postcode,
            'buyer_address_one': buyer_address.address_one,
            'buyer_city_code': buyer_address.city_code,
            'buyer_state': buyer_address.state_code,
            'buyer_phone': buyer_address.contact_phone,
            # 其他
            'order_remark': get_excel_cell(10, 'b', table),
        }
        customer_order = CustomerOrder.objects.create(**order_params, **shipper_address,
                                                      **get_update_params(request, True))

        gen_order_num(customer_order)
        customer_order.save()
        change_order_status(customer_order, 'WO', request.user)
        # 保存到附件中
        OrderAttachment.objects.create(name=excel, url=excel, customerOrder=customer_order,
                                       **get_update_params(request, True))

        # 包裹新增
        # 获取工作表上的所有图片
        all_images = table._images
        # 商品数据的第一行(从1开始)
        first_item_row = 12
        first_image_index = None
        # 对包裹和商品同时重复做判断
        # parcel_item_df = {}
        parcel_item_df = pd.DataFrame(columns=['parcel', 'parcel_item'], dtype=object)
        parcel_item_ctn = 0

        # 判断文件标题箱数（xxx）和导入的行数（xxx）不匹配
        # 箱数
        box_num = get_excel_cell(3, 'f', table)
        parcel_num_arr = []
        for x in range(first_item_row, line_number + 1):
            parcel_num = get_excel_cell(x, 'a', table)
            if parcel_num:
                parcel_num_arr.append(str(parcel_num).strip())
            else:
                break

        parcel_num_arr = list(set(parcel_num_arr))
        if box_num != len(parcel_num_arr):
            raise ParamError(f'文件标题箱数（{box_num}）和导入的箱数（{len(parcel_num_arr)}）不匹配', ErrorCode.PARAM_ERROR)

        reference_ids = set()
        # 遍历商品行数据
        for x in range(first_item_row, line_number + 1):
            parcel_num = get_excel_cell(x, 'a', table)
            if not parcel_num:
                break
            # 校验包裹号
            judge_parcel_num_rule(parcel_num, customer_order, raise_error=True)
            exist_parcels = Parcel.objects.filter(parcel_num=parcel_num, customer_order=customer_order, del_flag=False)

            reference_id = get_excel_cell(x, 'b', table)
            if reference_id:
                reference_ids.add(reference_id)

            if exist_parcels.count() > 0:
                # 存在包裹, 只新增ParcelItem
                parcel = exist_parcels[0]
            else:
                # 不存在包裹, 创建Parcel并新增ParcelItem
                same_parcel_num_parcels = Parcel.objects.filter(parcel_num=parcel_num, del_flag=False)
                if same_parcel_num_parcels.count() > 0:
                    raise ParamError(f'包裹号 {parcel_num} 已存在, 不允许重复', ErrorCode.PARAM_ERROR)

                parcel_size = get_excel_cell(x, 'c', table)
                if not parcel_size:
                    raise ParamError(f"第{x}行第c列(材积CM(长*宽*高))必填，请检查{parcel_size}", ErrorCode.PARAM_ERROR)

                if len(parcel_size.split('*')) != 3:
                    raise ParamError(f"第{x}行第c列(材积CM(长*宽*高))中的值格式不正确，请检查{parcel_size}",
                                     ErrorCode.PARAM_ERROR)

                parcel_length = parcel_size.split('*')[0]
                parcel_width = parcel_size.split('*')[1]
                parcel_height = parcel_size.split('*')[2]

                # 新增包裹次序
                # parcel_order = Parcel.objects.filter(customer_order=customer_order).count()
                parcel_params = {
                    'parcel_num': parcel_num,
                    'reference_id': reference_id,
                    'parcel_length': parcel_length,
                    'parcel_width': parcel_width,
                    'parcel_height': parcel_height,
                    'parcel_weight': get_excel_cell(x, 'd', table),
                    'parcel_volume': float(parcel_length) * float(parcel_width) * float(parcel_height) / 1000000,
                    'customer_order': customer_order,
                    'sys_parcel_num': create_sys_parcel_num(customer_order)
                }
                # parcel_params['sku'] = get_excel_cell(x, 'f', table)
                parcel = Parcel.objects.create(**parcel_params, **get_update_params(request, True))

            # 商品新增
            # 校验参数
            item_qty = get_excel_cell(x, 'i', table)
            if not isinstance(item_qty, (int, float)):
                print(f"单元格中的值不是数字类型 第{x}行第i列")
                raise ParamError(f"第{x}行第i列(数量)中的值不是数字类型 如{item_qty}", ErrorCode.PARAM_ERROR)

            sku_params = {
                'customs_code': get_excel_cell(x, 'e', table),
                'item_code': get_excel_cell(x, 'f', table),
                'item_name': get_excel_cell(x, 'g', table),
                'item_qty': item_qty,
                'declared_nameCN': get_excel_cell(x, 'g', table),
                'declared_nameEN': get_excel_cell(x, 'h', table),
                'declared_price': get_excel_cell(x, 'j', table),
                'brand': get_excel_cell(x, 'k', table),
                'model': get_excel_cell(x, 'l', table),
                'texture': get_excel_cell(x, 'm', table),
                'use': get_excel_cell(x, 'n', table),
                'parcel_num': parcel
            }
            same_parcel_item = parcel_item_df[(parcel_item_df['parcel'] == parcel_num) &
                                              (parcel_item_df['parcel_item'] == sku_params['item_code'])]
            if not same_parcel_item.empty:
                # 相同包裹号下的相同商品号不导入
                # print('重复数据-->', parcel_num, sku_params['item_code'])
                continue
            parcel_item_df.loc[len(parcel_item_df.index)] = [parcel_num, sku_params['item_code']]
            # same_parcel_item = parcel_item_df[parcel_item_df.duplicated(subset=['parcel', 'parcel_item'])]
            # if not same_parcel_item.empty:
            #     return Response(data={'code': 400, 'data': {}, 'msg': f'导入失败, 相同包裹号『{parcel_num}』'
            #                                                           f'下的商品号『{sku_params["item_code"]}』重复'},
            #                     status=status.HTTP_200_OK)
            parcel_item = ParcelItem.objects.create(**sku_params, **get_update_params(request, True))
            # 保存商品图片
            # parcel_item_picture =? table['O12'].value
            if first_image_index is None:
                first_image_index = 0
                for image in all_images:
                    if image.anchor._from.row + 1 == x:
                        break
                    first_image_index += 1
            image_row = x - first_item_row + first_image_index
            target_image = all_images[image_row] if image_row < len(all_images) else None
            if target_image:
                img_data = target_image._data()
                image_name = f"{parcel_num}_{x}.jpg"
                parcel_item.item_picture.save(image_name, ContentFile(img_data))
                parcel_item.save()
            parcel_item_ctn += 1
        if parcel_item_ctn == 0:
            return Response(
                data={'code': 400, 'data': {}, 'msg': '未导入任何数据, 请检查是否有数据或是否与已有包裹号重复'},
                status=status.HTTP_200_OK)

        summary_predict_parcels_data(customer_order)

        check_customer_order_shipment_id(customer_order)

        customer_order.reference_id = create_order_reference_id(reference_ids)
        customer_order.save()
        # 判断非fba仓库/偏远地区
        common_judge_fba_and_remote(customer_order)
        workbook.close()
        logger.info(f'运输订单导入成功: {customer_order.order_num}')
        return Response(data={'code': 200, 'data': {}, 'msg': '导入成功'}, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def upload_file_sync_fba_single(self, request):
        return sync_upload_file_common(request, 'TR', 'UploadOrderExcel', goods_type='SINGLE')

    # FBA多订单导入(异步)
    @transaction.atomic
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def upload_file_sync_fba_multi(self, request):
        return sync_upload_file_common(request, 'TR', 'UploadOrderExcel', goods_type='MULTI')

    # 运输 - FBA订单: 导入单一商品订单 (改为异步, 弃用)
    @transaction.atomic
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def upload_excel_fba_single(self, request):
        excel = request.FILES.get('file')
        # 将文件内容读取到内存中
        file_content = excel.read()
        # 使用 io.BytesIO 将内容包装成文件对象
        file_object = io.BytesIO(file_content)
        # 读取 Excel
        workbook = openpyxl.load_workbook(file_object)
        # 获取第一张表
        table = workbook.worksheets[0]
        # 行数
        line_number = table.max_row

        # 订单新增
        ref_num = get_excel_cell(3, 'b', table)
        if CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=ref_num, del_flag=False).exists():
            raise ParamError(f'客户订单号不能重复： {ref_num}', ErrorCode.PARAM_ERROR)

        # 获取产品
        product_code = get_excel_cell(9, 'b', table)
        # product = Product.objects.filter(code=product_code, del_flag=False).first()
        product = Product.objects.filter(name=product_code, del_flag=False).first()
        if product is None:
            raise ParamError(f'产品编码『{product_code}』系统找不到或存在重复', ErrorCode.PARAM_ERROR)
        if product.status == 'OFF':
            raise ParamError('此产品服务已被停用' + str(product_code), ErrorCode.PARAM_ERROR)

        # 获取客户
        company = None
        company_short_name = get_excel_cell(9, 'f', table)
        if company_short_name:
            company_short_name = str(company_short_name).strip()
            company = Company.objects.filter(short_name=company_short_name, is_customer=True, del_flag=False).first()
            if company is None:
                raise ParamError(f'未查到客户编码 {company_short_name}', ErrorCode.PARAM_ERROR)

        if company and company.status == 'OFF':
            raise ParamError(f'客户: {company.name}已被停用', ErrorCode.PARAM_ERROR)

        # 权限检查
        user_profile = UserProfile.objects.filter(username=request.user.username).first()
        department = user_profile.department
        if department and department.relate_customer and company.id not in list(
                map(int, department.relate_customer.split(','))):
            raise ParamError(f'没有权限访问 {company.name}', ErrorCode.PARAM_ERROR)

        # 处理收件地址
        warehouse_address = get_excel_cell(4, 'i', table)
        private_address = get_excel_cell(6, 'i', table)
        buyer_address_num = warehouse_address or private_address
        if not buyer_address_num:
            raise ParamError(f'收件地址『{product_code}』不存在', ErrorCode.PARAM_ERROR)

        # warehouse_address_sign = True if get_excel_cell(4, 'i', table) else False

        if buyer_address_num:
            buyer_address_num = str(buyer_address_num).strip()
            buyer_address_queryset = Address.objects.filter(address_num=buyer_address_num, del_flag=False)
            if buyer_address_queryset:
                buyer_address = buyer_address_queryset.first()
            else:
                if warehouse_address:
                    buyer_address = save_address(buyer_address_num, table)
                else:
                    buyer_address = save_address(buyer_address_num, table, is_warehouse_address=False)
            # print('buyer_address-->', buyer_address)
        else:
            raise ParamError(f'产品编码『{product_code}』系统找不到或存在重复', ErrorCode.PARAM_ERROR)

        # 如果没有发件人 默认用产品
        contact_name = get_excel_cell(4, 'b', table)
        shipper_address = {}
        if contact_name:
            postcode = get_excel_cell(8, 'b', table)
            if not postcode:
                raise ParamError(f'发件人邮编必填', ErrorCode.PARAM_ERROR)
            shipper_address = {
                # 发件人
                'contact_name': get_excel_cell(4, 'b', table),
                'company_name': get_excel_cell(5, 'b', table),
                'address_one': get_excel_cell(6, 'b', table),
                'city_code': get_excel_cell(7, 'b', table),
                'state_code': get_excel_cell(7, 'f', table),
                'postcode': postcode,
                'contact_phone': get_excel_cell(8, 'f', table),
                'country_code': 'CN'
            }
        else:
            if product.address_num:
                address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
                if address_queryset:
                    seller_address = address_queryset.first()
                    shipper_address = {
                        # 发件人
                        'shipper': seller_address,
                        'contact_name': seller_address.contact_name,
                        'company_name': seller_address.company_name,
                        'address_one': seller_address.address_one,
                        'city_code': seller_address.city_code,
                        'state_code': seller_address.state_code,
                        'postcode': seller_address.postcode,
                        'contact_phone': seller_address.contact_phone,
                        'country_code': 'CN'
                    }

        order_params = {
            'customer': company,
            'saler': company.saler_name if company else None,
            'product': product,
            'ref_num': ref_num,
            # 收件人
            'receiver': buyer_address if warehouse_address else None,
            'buyer_address_num': buyer_address.address_num,
            'buyer_name': buyer_address.contact_name,
            'buyer_company_name': buyer_address.company_name,
            'buyer_country_code': buyer_address.country_code,
            'buyer_postcode': buyer_address.postcode,
            'buyer_address_one': buyer_address.address_one,
            'buyer_city_code': buyer_address.city_code,
            'buyer_state': buyer_address.state_code,
            'buyer_phone': buyer_address.contact_phone,
            # 其他
            'order_remark': get_excel_cell(10, 'b', table),
        }
        customer_order = CustomerOrder.objects.create(**order_params, **shipper_address,
                                                      **get_update_params(request, True))

        gen_order_num(customer_order)
        customer_order.save()
        change_order_status(customer_order, 'PDC', request.user)
        # 保存到附件中
        OrderAttachment.objects.create(name=excel, url=excel, customerOrder=customer_order,
                                       **get_update_params(request, True))

        # 包裹新增
        # 获取工作表上的所有图片
        all_images = table._images

        # 商品数据的第一行(从1开始)
        first_item_row = 12
        parcel_item_df = pd.DataFrame(columns=['parcel', 'parcel_item'], dtype=object)
        parcel_item_ctn = 0

        # 判断文件标题箱数（xxx）和导入的行数（xxx）不匹配
        # 箱数
        box_num = get_excel_cell(3, 'f', table)
        parcel_count = 0
        for x in range(first_item_row, line_number + 1):
            parcel_num_pre = get_excel_cell(x, 'a', table)
            if parcel_num_pre:
                parcel_count += int(get_excel_cell(x, 'c', table))
            else:
                break

        if box_num != parcel_count:
            raise ParamError(f'文件标题箱数（{box_num}）和导入的箱数（{parcel_count}）不匹配', ErrorCode.PARAM_ERROR)

        reference_ids = set()
        # cur_count = 0
        # ctn_num_map = defaultdict(int)
        current_parcel_prefix_map = {}
        # 遍历商品行数据
        for index, x in enumerate(range(first_item_row, line_number + 1)):
            parcel_num_pre = get_excel_cell(x, 'a', table)
            if not parcel_num_pre:
                break
            parcel_num_pre = str(parcel_num_pre).strip()
            # 如果不在当前excel的前缀列表中, 则需要检测
            if parcel_num_pre not in current_parcel_prefix_map:
                if Parcel.objects.filter(parcel_num__startswith=parcel_num_pre, del_flag=False).exists():
                    raise ParamError(f'包裹号前缀 {parcel_num_pre} 重复, 请重新填写包裹号', ErrorCode.PARAM_ERROR)
                current_parcel_prefix_map[parcel_num_pre] = 1
            # parcel_num = '1'
            # if cur_count == 0:
            #     cur_count = get_excel_cell(x, 'c', table)
            #     parcel_num = get_parcel_num_single_init(parcel_num_pre)
            cur_count = int(get_excel_cell(x, 'c', table))
            # parcel_suffix = 1
            for inner_index in range(cur_count):
                # if inner_index != 0:
                #     parcel_num = get_parcel_num_single(parcel_num_pre, int(parcel_num[-6:]))
                parcel_num = parcel_num_pre + 'U' + str(current_parcel_prefix_map[parcel_num_pre]).rjust(6, '0')
                # 校验包裹号
                judge_parcel_num_rule(parcel_num, customer_order, raise_error=True)
                exist_parcels = Parcel.objects.filter(parcel_num=parcel_num, customer_order=customer_order,
                                                      del_flag=False)

                reference_id = get_excel_cell(x, 'b', table)
                if reference_id:
                    reference_ids.add(reference_id)

                if exist_parcels.exists():
                    # 存在包裹, 只新增ParcelItem
                    parcel = exist_parcels.first()
                else:
                    # 不存在包裹, 创建Parcel并新增ParcelItem
                    same_parcel_num_parcels = Parcel.objects.filter(parcel_num=parcel_num, del_flag=False)
                    if same_parcel_num_parcels.count() > 0:
                        raise ParamError(f'包裹号 {parcel_num} 已存在, 不允许重复', ErrorCode.PARAM_ERROR)

                    parcel_size = get_excel_cell(x, 'd', table)
                    if not parcel_size:
                        raise ParamError(f"第{x}行第c列(材积CM(长*宽*高))必填，请检查{parcel_size}",
                                         ErrorCode.PARAM_ERROR)

                    if len(parcel_size.split('*')) != 3:
                        raise ParamError(f"第{x}行第c列(材积CM(长*宽*高))中的值格式不正确，请检查{parcel_size}",
                                         ErrorCode.PARAM_ERROR)

                    parcel_length = parcel_size.split('*')[0]
                    parcel_width = parcel_size.split('*')[1]
                    parcel_height = parcel_size.split('*')[2]

                    # 新增包裹次序
                    # parcel_order = Parcel.objects.filter(customer_order=customer_order).count()
                    parcel_params = {
                        'parcel_num': parcel_num,
                        'reference_id': reference_id,
                        'parcel_length': parcel_length,
                        'parcel_width': parcel_width,
                        'parcel_height': parcel_height,
                        'parcel_weight': get_excel_cell(x, 'e', table),
                        'parcel_volume': float(parcel_length) * float(parcel_width) * float(parcel_height) / 1000000,
                        'customer_order': customer_order,
                        'sys_parcel_num': create_sys_parcel_num(customer_order)
                    }
                    # parcel_params['sku'] = get_excel_cell(x, 'f', table)
                    parcel = Parcel.objects.create(**parcel_params, **get_update_params(request, True))

                # 商品新增
                # 校验参数
                item_qty = get_excel_cell(x, 'j', table)
                if not isinstance(item_qty, (int, float)):
                    print(f"单元格中的值不是数字类型 第{x}行第i列")
                    raise ParamError(f"第{x}行第i列(数量)中的值不是数字类型 如{item_qty}", ErrorCode.PARAM_ERROR)

                sku_params = {
                    'customs_code': get_excel_cell(x, 'f', table),
                    'item_code': get_excel_cell(x, 'g', table),
                    'item_name': get_excel_cell(x, 'h', table),
                    'item_qty': item_qty,
                    'declared_nameCN': get_excel_cell(x, 'h', table),
                    'declared_nameEN': get_excel_cell(x, 'i', table),
                    'declared_price': get_excel_cell(x, 'k', table),
                    'brand': get_excel_cell(x, 'l', table),
                    'model': get_excel_cell(x, 'm', table),
                    'texture': get_excel_cell(x, 'n', table),
                    'use': get_excel_cell(x, 'o', table),
                    'parcel_num': parcel
                }
                same_parcel_item = parcel_item_df[(parcel_item_df['parcel'] == parcel_num) &
                                                  (parcel_item_df['parcel_item'] == sku_params['item_code'])]
                if not same_parcel_item.empty:
                    # 相同包裹号下的相同商品号不导入
                    continue
                parcel_item_df.loc[len(parcel_item_df.index)] = [parcel_num, sku_params['item_code']]
                parcel_item = ParcelItem.objects.create(**sku_params, **get_update_params(request, True))

                target_image = all_images[index] if index < len(all_images) else None
                if target_image:
                    img_data = target_image._data()
                    image_name = f"{parcel_num}_{x}.jpg"
                    parcel_item.item_picture.save(image_name, ContentFile(img_data))
                    parcel_item.save()
                parcel_item_ctn += 1
                # ctn_num_map[parcel_num_pre] += 1
                current_parcel_prefix_map[parcel_num_pre] += 1
            # cur_count = 0

        if parcel_item_ctn == 0:
            return Response(
                data={'code': 400, 'data': {}, 'msg': '未导入任何数据, 请检查是否有数据或是否与已有包裹号重复'},
                status=status.HTTP_200_OK)

        # 汇总预计数量，体检重
        parcel_queryset = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
        parcel_list = parcel_queryset.values('customer_order').annotate(
            parcel_weight=Sum('parcel_weight'),
            parcel_volume=Sum('parcel_volume'),
            parcel_qty=Sum('parcel_qty'))
        for parcel in parcel_list:
            customer_order.pre_carton = parcel['parcel_qty']
            customer_order.pre_weight = parcel['parcel_weight']
            customer_order.pre_volume = parcel['parcel_volume']
            customer_order.save()

        check_customer_order_shipment_id(customer_order)

        customer_order.reference_id = create_order_reference_id(reference_ids)
        customer_order.save()
        common_judge_fba_and_remote(customer_order)
        workbook.close()
        logger.info(f'单一商品运输订单导入成功: {customer_order.order_num}')
        return Response(data={'code': 200, 'data': {}, 'msg': '导入成功'}, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def print_label(self, request):
        """
        # 打印中性面单页签
        :param request:
        :return:
        """
        ids = request.data['ids']
        customer_order_list = CustomerOrder.objects.filter(~Q(product=None), id__in=ids, del_flag=False)

        if len(customer_order_list) != len(ids):
            return fail_response(request, '请检查所选客户订单都选择了产品！')
        orders = []
        for customer_order in customer_order_list:
            order = {
                "order_num": customer_order.order_num,
                "product_code": customer_order.product.name,
                "pices": customer_order.pre_carton,
                "weight": customer_order.pre_weight,
                "buyer_address_num": customer_order.buyer_address_num,
                "buyer_name": customer_order.buyer_name,
                "buyer_address_one": customer_order.buyer_address_one,
                "buyer_address_two": customer_order.buyer_address_two,
                "buyer_city_code": customer_order.buyer_city_code,
                "buyer_state": customer_order.buyer_state,
                "buyer_postcode": customer_order.buyer_postcode,
                "buyer_country_code": customer_order.buyer_country_code,
                "buyer_phone": customer_order.buyer_phone
            }
            orders.append(order)

        if settings.CUSTOMER_ORDER_MARK == 'QD':
            request.data['base64'] = create_barcodes_for_order3(orders)
        else:
            request.data['base64'] = create_barcodes_for_order2(orders)
        request.data['msg'] = '打印成功！'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def print_fba_label(self, request):
        """
        # 打印FBA中性面单
        """
        ids = request.data['ids']
        customer_orders = CustomerOrder.objects.filter(~Q(product=None), id__in=ids, del_flag=False)
        if len(customer_orders) != len(ids):
            return fail_response(request, '请检查所选客户订单都选择了产品！')

        result_list, err = print_fba_label_common(customer_orders)
        if err == 1:
            return fail_response(request, '未找到订单下面的包裹！')
        request.data['base64'] = result_list
        return success_response(request, 'FBA中性面单打印成功！')

    @action(methods=['POST'], detail=False)
    def print_truck_label(self, request):
        """
        # 打印卡派面单
        """
        ids = request.data['ids']
        customer_orders = CustomerOrder.objects.filter(~Q(product=None), id__in=ids, del_flag=False)
        if len(customer_orders) != len(ids):
            return fail_response(request, '请检查所选客户订单都选择了产品！')

        result_list, err = print_truck_label_common(customer_orders)
        if err == 1:
            return fail_response(request, '未找到订单下面的包裹！')
        elif err == 2:
            return fail_response(request, '订单号生成失败！')
        # print('print_truck_label result_list-->', result_list)
        request.data['base64'] = result_list
        return success_response(request, '卡派面单打印成功！')

    # 首页获取订单数量
    @transaction.atomic
    @action(methods=['GET'], detail=False)
    def home_data(self, request):
        clearance_params = {}
        customer_params = {}
        parcel_params = {}
        if not request.user.is_staff:
            # 客户或者供应商
            if request.user.company is None:
                # 没有配置
                raise ParamError('用户暂时未配置所属公司，请联系系统方！', ErrorCode.PARAM_ERROR)
            elif request.user.company.is_customer:
                # 是客户
                clearance_params['customer'] = request.user.company
                customer_params['customer'] = request.user.company
                parcel_params['customer'] = request.user.company
            elif request.user.company.is_supplier:
                # 是供应商
                clearance_params['supplier'] = request.user.company
                customer_params['service.supplier'] = request.user.company
                parcel_params['service.supplier'] = request.user.company

        # 是供应商
        # 区分客户跟供应商，客户显示客户的，供应商根据产品的服务带出来
        weeks = get_week_days()
        json_data = {
            'customer_order': [],
            'parcel_customer_order': [],
            'clearance_order': []
        }
        for item in weeks:
            t_num = CustomerOrder.objects.filter(arrival_date=item, del_flag=False, **customer_params).count()
            p_num = ParcelCustomerOrder.objects.filter(order_time__range=[item + ' 00:00:00', item + ' 23:59:59'],
                                                       del_flag=False, order_type='PC', **parcel_params).count()
            c_num = Clearance.objects.filter(opera_date=item, del_flag=False, **clearance_params).count()
            json_data['customer_order'].append(t_num)
            json_data['parcel_customer_order'].append(p_num)
            json_data['clearance_order'].append(c_num)
        json_data['total'] = [json_data['customer_order'][-1], json_data['parcel_customer_order'][-1],
                              json_data['clearance_order'][-1]]
        json_data['date'] = weeks
        request.data['data'] = json_data
        return success_response(request, 'success')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def submit_order(self, request):
        """
        提交订单
        :param request:
        :return:
        """
        ids = request.data['ids']
        user = request.user
        customer_order_list = CustomerOrder.objects.filter(id__in=ids, del_flag=False, order_status='DR')
        for customer_order in customer_order_list:

            if not customer_order.arrival_date:
                customer_order.arrival_date = datetime.now().strftime("%Y-%m-%d")

            # 计费重和确认计费重取整
            charge_weight_round(customer_order)

            # 是否收入计价
            if customer_order.product and customer_order.product.is_valuation:
                CustomerOrderChargeIn.objects.filter(customer_order_num=customer_order, del_flag=False,
                                                     is_system=True).update(
                    del_flag=True)
                add_revenue(customer_order, user, CustomerOrderChargeIn)
            # 是否成本计价
            if customer_order.product and customer_order.product.is_cost_valuation:
                CustomerOrderChargeOut.objects.filter(customer_order_num=customer_order, del_flag=False,
                                                      is_system=True).update(
                    del_flag=True)
                add_cost(customer_order, user, CustomerOrderChargeOut)

            # 从客户账号扣钱
            if customer_order.product and customer_order.product.is_valuation:
                deduction_account(customer_order, user, CustomerOrderChargeIn)

            # customer_order.order_status = 'WO'
            change_order_status(customer_order, 'WO', request.user)
            customer_order.update_date = datetime.now()
            customer_order.update_by = user
            customer_order.save()

        return success_response(request, 'success')

    # 预估费用
    @action(methods=['POST'], detail=False)
    def api_pre_fee(self, request):
        product_code = request.data['service_code']
        if not product_code:
            return fail_response(request, "请输入服务编码！")
        product_queryset = Product.objects.filter(code=product_code, del_flag=False)
        if product_queryset.count() == 0:
            return fail_response(request, "请输入查无此产品")

        product = product_queryset.first()

        sender_country = request.data['sender_country']
        sender_postcode = request.data['sender_postcode']

        receive_country = request.data['receive_country']
        receive_postcode = request.data['receive_postcode']

        parcels = request.data['parcels']

        parcelList = []
        for parcel in parcels:
            parcelPage = Parcel()
            parcelPage.parcel_num = ''
            parcelPage.parcel_weight = Decimal(parcel['parcel_weight'])
            parcelPage.parcel_length = Decimal(parcel['parcel_length'])
            parcelPage.parcel_width = Decimal(parcel['parcel_width'])
            parcelPage.parcel_height = Decimal(parcel['parcel_height'])
            parcelPage.parcel_volume = (
                                               parcelPage.parcel_length * parcelPage.parcel_width * parcelPage.parcel_height) / 1000000
            parcelList.append(parcelPage)

        order_calc_vo = OrderCalcVo()
        order_calc_vo.product = product

        start_dict_vo = DistCalcVo()
        start_dict_vo.countryCode = sender_country
        start_dict_vo.postCode = sender_postcode
        order_calc_vo.startDistVo = start_dict_vo
        end_dict_vo = DistCalcVo()
        end_dict_vo.countryCode = receive_country
        end_dict_vo.postCode = receive_postcode
        order_calc_vo.endDistVo = end_dict_vo
        order_calc_vo.parcels = parcelList
        # order_calc_vo.customer = 'test'
        order_calc_vo.calcDate = datetime.now()

        user = request.user
        if not user.is_staff:
            order_calc_vo.customer = user.company

        revenue_results = revenue_calc(order_calc_vo, product, True)

        result = []
        for revenue_result in revenue_results:
            result.append({
                'result_fee': revenue_result.result_fee,
                'currency': revenue_result.currency,
                'charge_name': revenue_result.charge_name,
            })

        data = {}
        data['data'] = result
        data['msg'] = ''
        data['code'] = 200

        return Response(data=data, status=status.HTTP_200_OK)

    # 预估费用, 不传产品
    @action(methods=['POST'], detail=False)
    def api_pre_fee_all_product(self, request):
        # ids = request.data['ids']
        user = request.user
        parcels = request.data['parcels']

        parcel_list = []
        for parcel in parcels:
            parcel_list.append({
                'weight': Decimal(parcel['parcel_weight']),
                'length': Decimal(parcel['parcel_length']),
                'width': Decimal(parcel['parcel_width']),
                'height': Decimal(parcel['parcel_height']),
                'volume': (Decimal(parcel['parcel_length']) * Decimal(parcel['parcel_width']) * Decimal(
                    parcel['parcel_height'])) / 1000000,
            })

        order_calc_vo = OrderCalcVo()

        # warehouse = parcel_customer_order.warehouse_code

        start_dict_vo = DistCalcVo()
        start_dict_vo.countryCode = request.data['sender_country']
        start_dict_vo.postCode = request.data['sender_postcode']
        order_calc_vo.startDistVo = start_dict_vo
        end_dict_vo = DistCalcVo()
        buyer_country_code = request.data['receive_country']
        end_dict_vo.countryCode = buyer_country_code
        end_dict_vo.postCode = request.data['receive_postcode']
        order_calc_vo.endDistVo = end_dict_vo
        order_calc_vo.parcels = parcel_list
        order_calc_vo.customer = user.company
        order_calc_vo.calcDate = datetime.now()

        product_zone_queryset = ProductZone.objects.filter(country_code=buyer_country_code, del_flag=False,
                                                           type='Buyer')
        if not product_zone_queryset.exists():
            return fail_response(request, f"此国家{buyer_country_code}未配置产品分区")

        product_route_queryset = ProductRoute.objects.filter(end_zone__in=product_zone_queryset, del_flag=False)
        products = [x.product.id for x in product_route_queryset if x.product]

        product_queryset = Product.objects.filter(id__in=products, type='TR', is_open=True, is_virtual=False,
                                                  del_flag=False)

        success_data = []
        fail_data = []
        for product in product_queryset:
            order_calc_vo.product = product
            try:
                revenue_results = revenue_calc(order_calc_vo, product)
            except Exception as e:
                fail_data.append({'product_name': product.name,
                                  'calc_result': str(e)})
                continue

            calc_result = {
                'product_name': product.name,
                # 'product_code': product.code,
                'total_fee': 0,
                'yf_fee': 0,
                'ryf_fee': 0,
                'pyf_fee': 0,
                'zz_fee': 0,
                'other_fee': 0,
            }
            for revenue_result in revenue_results:
                calc_result['currency'] = revenue_result.currency
                calc_result['total_fee'] = calc_result['total_fee'] + revenue_result.result_fee
                if revenue_result.charge_code in ['YF', 'PSF']:
                    calc_result['yf_fee'] = calc_result['yf_fee'] + revenue_result.result_fee
                elif revenue_result.charge_code == 'RYF':
                    calc_result['ryf_fee'] = calc_result['ryf_fee'] + revenue_result.result_fee
                elif revenue_result.charge_code == 'PYF':
                    calc_result['pyf_fee'] = calc_result['pyf_fee'] + revenue_result.result_fee
                elif revenue_result.charge_code == 'SRZZF':
                    calc_result['zz_fee'] = calc_result['zz_fee'] + revenue_result.result_fee
                else:
                    calc_result['other_fee'] = calc_result['other_fee'] + revenue_result.result_fee

            success_data.append(calc_result)

        res_data = {
            'success_data': success_data,
            'fail_data': fail_data,
            'msg': '',
            'code': 200
        }
        return Response(data=res_data, status=status.HTTP_200_OK)

    # 运输单中批量解绑出货单和大包单中的包裹信息
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_unbind_outbound_order(self, request):
        outbound_nums = request.data.get('outbound_num', [])
        user = request.user
        try:
            # 查询所有出货订单和相关的大包
            outbound_orders = ParcelOutboundOrder.objects.filter(outbound_num__in=outbound_nums, del_flag=False)
            big_parcels = BigParcel.objects.filter(parcel_outbound_order__in=outbound_orders, del_flag=False)
            # 解绑相关的包裹
            parcels = Parcel.objects.filter(parcel_num__in=big_parcels.values('parcel_num'), del_flag=False)
            parcels.update(del_flag=True, update_date=datetime.now(), update_by=user)

            # 更新出货订单
            outbound_orders.update(customer_order=None)

            # 返回响应
            return Response({'msg': '解绑成功', 'code': 200}, status=status.HTTP_200_OK)

        except ObjectDoesNotExist as e:
            # 记录日志
            logger.exception("出货订单号不存在")
            # 返回错误响应
            return Response({'msg': '出货订单号不存在', 'code': 400}, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            # 记录日志
            logger.exception("操作失败")

            # 返回错误响应
            return Response({'msg': '操作失败', 'code': 500}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # 获取订单状态(new)
    @action(methods=['GET'], detail=False)
    def get_order_status(self, request):
        order_status = []
        for code, label in CustomerOrder.ORDER_STATUS:
            if code not in ['DR', 'WO', 'FC']:
                order_status.append({'id': code, 'name': label})
        data = {'msg': '成功！', 'code': 200, 'data': order_status}
        return Response(data=data, status=status.HTTP_200_OK)

    # 获取订单状态map
    @action(methods=['GET'], detail=False)
    def get_order_status_map(self, request):
        order_status = {}
        for code, label in CustomerOrder.ORDER_STATUS:
            order_status[code] = label
        data = {'msg': '成功！', 'code': 200, 'data': order_status}
        return Response(data=data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def api_get_order(self, request):

        order_num = request.data['order_num']
        customer_orders = CustomerOrder.objects.filter(order_num=order_num, del_flag=False)
        if customer_orders.count() == 0:
            return fail_response(request, f'查无此订单{order_num}')
        customer_order = customer_orders.first()
        data = {
            'order_num': customer_order.order_num,
            'tracking_num': customer_order.tracking_num or '',
            'customer_order_num': customer_order.ref_num or '',
            'order_status': customer_order.order_status or '',
        }
        request.data['data'] = data

        return success_response(request, '获取成功')

    # 移除订单下的包裹
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def remove_parcel(self, request):
        # 这里无法区分删除完整订单还是简易订单, 目前默认删完整订单
        parcel_id = request.data.get('id')
        customer_order_id = request.data.get('customer_order_id')
        customer_order = CustomerOrder.objects.get(id=customer_order_id, del_flag=False)
        del_num = customer_order.order_num + '_' + str(int(round(time.time() * 1000000)))
        Parcel.objects.filter(id=parcel_id, del_flag=False).update(del_flag=True, remark=del_num)
        for good in ParcelItem.objects.filter(parcel_num=parcel_id, del_flag=False):
            good.del_flag = True
            good.remark = del_num
            good.save()
        # 汇总包裹的件数
        summary_predict_parcels_data(customer_order)
        summary_parcels_info(customer_order)
        request.data['msg'] = '移除成功'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 批量更新(修改确认计费重和产品)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_update_order(self, request):
        ids = request.data['ids']
        shipper_id = request.data.get('shipper')
        product_id = request.data.get('product')
        confirm_charge_weight = request.data.get('confirm_charge_weight')
        confirm_volume = request.data.get('confirm_volume')
        arrival_date = request.data.get('arrival_date')
        is_confirm_data = request.data.get('is_confirm_data')
        is_customs_declaration = request.data.get('is_customs_declaration')
        if len(ids) == 0:
            raise ParamError('订单id必填', ErrorCode.PARAM_ERROR)
        if not any([shipper_id, product_id, confirm_charge_weight, confirm_volume, is_confirm_data is not None,
                    is_customs_declaration is not None]):
            raise ParamError('未批量更新任何数据, 请设定修改值', ErrorCode.PARAM_ERROR)

        params = {
            'update_date': datetime.now(),
            'update_by': request.user
        }
        if shipper_id:
            shipper = Address.objects.get(id=shipper_id)
            params['shipper'] = shipper
            params['address_num'] = shipper.address_num
            params['contact_name'] = shipper.contact_name
            params['contact_email'] = shipper.contact_email
            params['contact_phone'] = shipper.contact_phone
            params['country_code'] = shipper.country_code
            params['state_code'] = shipper.state_code
            params['city_code'] = shipper.city_code
            params['postcode'] = shipper.postcode
            params['house_no'] = shipper.house_no
            params['address_one'] = shipper.address_one
            params['address_two'] = shipper.address_two
            params['company_name'] = shipper.company_name

        if product_id:
            product = Product.objects.get(id=product_id)
            service_query = Service.objects.filter(product=product, del_flag=False)
            params['product'] = product
            params['service'] = service_query.first()
        if confirm_charge_weight:
            params['confirm_charge_weight'] = confirm_charge_weight
        if confirm_volume:
            params['confirm_volume'] = confirm_volume

        if arrival_date:
            params['arrival_date'] = arrival_date

        # 批量更新是否报关件
        if is_customs_declaration is not None:
            params['is_customs_declaration'] = is_customs_declaration

        # if is_confirm_data is not None:
        #     params['is_confirm_data'] = is_confirm_data

        results = []
        update_order_ids = []
        for order_id in ids:
            orders = CustomerOrder.objects.filter(id=order_id, del_flag=False)
            if orders:
                order = orders.first()
                charge_in = CustomerOrderChargeIn.objects.filter(customer_order_num=order_id, del_flag=False)
                if charge_in and product_id:
                    results.append(order.order_num + '已有收入不能修改产品')
                    continue
                if order.is_revenue_lock and confirm_charge_weight:
                    results.append(order.order_num + '已收入确认不能修改确认计费重')
                    continue
                if order.is_revenue_lock and confirm_volume:
                    results.append(order.order_num + '已收入确认不能修改确认计费体积')
                    continue
                order_label_task = OrderLabelTask.objects.filter(order_num=order_id, del_flag=False,
                                                                 status__in=['UnHandled', 'HandledBy3rdNo'])
                if order_label_task:
                    results.append(order.order_num + '有打单任务请先关闭任务')
                    continue
                OrderFieldChangeLog.record(order, params, 'FBA', request.user)

                update_order_ids.append(order_id)

        if update_order_ids:
            CustomerOrder.objects.filter(id__in=update_order_ids, del_flag=False).update(**params)
        msg = arr_to_str(results) if results else '更新成功'
        return success_response(request, msg)

    # api接收确认数据
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def accept_confirm_data(self, request):
        print('request.data-->', request.data)
        data = request.data.get('data')
        if not data:
            return fail_response_common('没有收到任何数据')

        for confirm_data in data:
            order_num = confirm_data.get('order_num')
            confirm_params = confirm_data.get('confirm_params')
            if not order_num:
                return fail_response_common(f'订单号不存在: {order_num}')
            customer_order_queryset = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=order_num,
                                                                   del_flag=False)
            if not customer_order_queryset.exists():
                return fail_response_common(f'未找到客户订单号: {order_num}')
                # return fail_response_common(f'未找到订单: {order_num}')

            customer_order_queryset.update(**confirm_params, **get_update_params(request))
        return success_response_common('Success')

    # api接收确认数据
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def accept_cancel_intercept_data(self, request):
        print('request.data-->', request.data)
        data = request.data.get('data')
        operate_type = request.data.get('operate_type')
        if not data:
            return fail_response_common('没有收到任何数据')

        orders = CustomerOrder.objects.filter(ref_num__in=data, del_flag=False)
        if not orders.exists():
            return fail_response_common(f'未找到客户订单号: {data}')
        ids = list(orders.values_list('id', flat=True))
        user_profile = UserProfile.objects.get(username='admin')  # 默认是admin用户

        if operate_type == 'cancel_order':  # 作废订单
            cancel_order_common(request, ids, user=user_profile)
        elif operate_type == 'cancel_order_force':  # 强制作废订单
            update_order_data_for_cancel_force(request, ids, user_profile=user_profile)
        elif operate_type == 'customer_order_intercept':  # 拦截订单
            update_order_data_for_customer_order_intercept(request, ids, user_profile=user_profile)
        elif operate_type == 'customer_order_cancel_order_cancel':  # 取消作废订单
            update_order_data_for_recovery_order(request, ids, user_profile=user_profile)
        elif operate_type == 'customer_order_intercept_cancel':  # 取消订单拦截
            update_order_data_for_cancel_customer_order_intercept(request, ids, user_profile=user_profile)
        else:
            return fail_response_common(f'未找到 operate_type 类型')
        return success_response_common('Success')

    # api接收确认数据:[同步强制全部入仓]
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def accept_complete_warehousing_data(self, request):
        print('request.data-->', request.data)
        order_num = request.data.get('data')
        if not order_num:
            return fail_response_common('没有收到任何数据')
        customer_order_queryset = CustomerOrder.objects.filter(ref_num=order_num, del_flag=False)
        if not customer_order_queryset.exists():
            return fail_response_common(f'未找到客户订单号: {order_num}')
        user_profile = customer_order_queryset.first().create_by
        all_warehousing_constraint(customer_order_queryset.first(), user_profile)
        return success_response_common('Success')

    # 修改并推送确认数据到客户(yqf, 目前固定为yqf)(同步确认数据)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def sync_confirm_data(self, request):
        if settings.SYSTEM_ORDER_MARK not in ['MZ', 'FX']:
            raise ParamError('此功能仅在供应商服务器使用', ErrorCode.PARAM_ERROR)
        ids = request.data['ids']
        confirm_charge_weight = request.data.get('confirm_charge_weight')
        confirm_volume = request.data.get('confirm_volume')
        check_in_time = request.data.get('check_in_time')
        if len(ids) == 0:
            raise ParamError('订单id必填', ErrorCode.PARAM_ERROR)
        if not any([confirm_charge_weight, confirm_volume, check_in_time]):
            raise ParamError('未批量更新任何数据, 请设定修改值', ErrorCode.PARAM_ERROR)

        confirm_params = {
            'update_date': datetime.now(),
            'update_by': request.user
        }
        if confirm_charge_weight:
            confirm_params['confirm_charge_weight'] = confirm_charge_weight
        if confirm_volume:
            confirm_params['confirm_volume'] = confirm_volume
        if check_in_time:
            confirm_params['check_in_time'] = check_in_time
        # 默认会将订单是否确认数据改为是
        # params['is_confirm_data'] = True

        results = []
        update_order_ids = []
        update_order_nums = []
        for order_id in ids:
            orders = CustomerOrder.objects.filter(id=order_id, del_flag=False)
            if orders:
                order = orders.first()
                if order.is_revenue_lock and confirm_charge_weight:
                    results.append(order.order_num + '已收入确认不能修改确认计费重')
                    continue
                if order.is_revenue_lock and confirm_volume:
                    results.append(order.order_num + '已收入确认不能修改确认计费体积')
                    continue
                if order.is_revenue_lock and check_in_time:
                    results.append(order.order_num + '已收入确认不能修改签入时间')
                    continue
                order_label_task = OrderLabelTask.objects.filter(order_num=order_id, del_flag=False,
                                                                 status__in=['UnHandled', 'HandledBy3rdNo'])
                if order_label_task:
                    results.append(order.order_num + '有打单任务请先关闭任务')
                    continue
                OrderFieldChangeLog.record(order, confirm_params, 'FBA', request.user)

                update_order_ids.append(order_id)
                update_order_nums.append(order.ref_num)

        if update_order_ids:
            # 批量更新到供应商
            # code, res = get_service_class_url('CustomerOrderSyncService')
            # if code == 1:
            #     raise ParamError(res, ErrorCode.PARAM_ERROR)
            # else:
            #     supplier_account = res

            # 由于客户需求, 改成从mz同步到yqf
            # url = supplier_account.url + '/api/customerOrders/accept_confirm_data/'
            url = 'http://cshm.yiqifei56.com' + '/api/customerOrders/accept_confirm_data/'
            # url = 'http://127.0.0.1:8000' + '/api/customerOrders/accept_confirm_data/'

            push_data = []
            confirm_params.pop('update_date', None)
            confirm_params.pop('update_by', None)
            for update_order_num in update_order_nums:
                push_data.append({'order_num': update_order_num, 'confirm_params': confirm_params})

            result = request_server({'data': push_data}, url, {'Content-Type': 'application/json'})

            if not result:
                raise ParamError('订单确认数据推送失败', ErrorCode.PARAM_ERROR)

            if result.get('code') == 200:
                results.append('订单确认数据推送成功')
            else:
                error_msg = result.get('msg', '') or result.get('detail', '')
                raise ParamError(f'订单确认数据推送失败: {error_msg}', ErrorCode.PARAM_ERROR)
            # 最后再修改数据
            CustomerOrder.objects.filter(id__in=update_order_ids, del_flag=False).update(**confirm_params)

        msg = arr_to_str(results) if results else '更新并推送成功'
        return success_response_common(msg=msg)

    # 自定义POD导出
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def custom_pod_export(self, request):
        ids = request.data.get('ids')
        if not ids:
            raise ParamError('订单必选', ErrorCode.PARAM_ERROR)
        orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        unreceipted = orders.filter(~Q(order_status__in=['SF', 'FC']))
        if unreceipted.exists():
            error_message = {'detail': '所选订单状态必须为已签收'}
            return Response(error_message, status=status.HTTP_400_BAD_REQUEST)
        wb = openpyxl.Workbook()
        # 创建一个sheet
        sheet_name = 'POD数据'
        ws = wb.create_sheet(sheet_name, 0)
        # 要转成PDF单元格不好合并居中, 取消第一行操作
        # # 合并从A1到I1的单元格
        # ws.merge_cells('A1:I1')
        # # 在合并后的单元格中写入内容
        # cell = ws['A1']
        # cell.value = 'Shipment Information'
        # cell.alignment = Alignment(horizontal='center', vertical='center')
        title_dict = [
            "Serial number", "ARN", "PRO/Carrier\nReference\nNumber",
            "BOL/Vendor or Seller\nReference Number\nList (use , as\nseparator)",
            "Vendor\nName", "Pallet\nCount", "Carton\nCount", "Unit\nCount",
            "PO List (use , as\nseparator) *",
        ]
        title_row = 1
        # 填充标题
        ws.row_dimensions[title_row].height = 60
        for i, title in enumerate(title_dict):
            ws.cell(row=title_row, column=i + 1).value = title
            # 设置单元格格式
            cell = ws.cell(row=title_row, column=i + 1)
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.font = Font(size=12, bold=True)
            ws.column_dimensions[ws.cell(row=title_row, column=i + 1).column_letter].width = 20

        parcels = Parcel.objects.filter(customer_order__in=orders, del_flag=False)

        excel_row = title_row + 1
        shipment_ids = parcels.values_list('shipment_id', flat=True)
        shipment_ids = list(set(shipment_ids))
        for shipment_id in shipment_ids:
            current_parcels = Parcel.objects.filter(customer_order__in=orders, shipment_id=shipment_id,
                                                    del_flag=False)
            if not current_parcels.exists():
                continue
            print('current_parcels-->', current_parcels.count())
            ci = 1
            # 序号
            set_data(ci, excel_row - 2, excel_row, ws)
            ci += 1
            # ARN
            set_data(ci, None, excel_row, ws)
            ci += 1
            # PRO
            set_data(ci, shipment_id, excel_row, ws)
            ci += 1
            # BOL
            set_data(ci, shipment_id, excel_row, ws)
            ci += 1
            # Vendor Name
            set_data(ci, None, excel_row, ws)
            ci += 1
            # Pallet Count
            set_data(ci, 0, excel_row, ws)
            ci += 1
            # Carton Count
            # current_order = current_parcels.first().customer_order
            set_data(ci, current_parcels.count(), excel_row, ws)
            ci += 1
            # Unit Count
            parcel_items = ParcelItem.objects.filter(parcel_num__in=current_parcels, del_flag=False)
            # unit_count = parcel_items.aggregate(total=Sum('parcel_qty'))['total'] or 0
            unit_count = parcel_items.aggregate(total=Sum('item_qty'))['total'] or 0
            set_data(ci, unit_count, excel_row, ws)
            ci += 1
            # PO: reference_id
            set_data(ci, current_parcels.first().reference_id, excel_row, ws)
            ci += 1

            excel_row += 1

        # response = HttpResponse(content_type='application/msexcel')
        # response['Content-Disposition'] = 'attachment;filename=%s.xlsx' % sheet_name
        # wb.save(response)
        # return response

        # 最新需求要转成pdf导出
        pdf_buffer = openpyxl_to_pdf(wb)
        print('pdf_buffer-->', pdf_buffer, type(pdf_buffer))
        # 返回 PDF 文件
        response = HttpResponse(pdf_buffer, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="output.pdf"'
        return response

    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def upload_file_sync_fbm(self, request):
        """
        导入FBM订单excel文件
        """

        return sync_upload_file_common(request, 'TR', 'UploadFBMOrderExcel')

    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def import_update_fbm_order(self, request):
        """
        导入更新FBM订单excel文件
        """
        user = request.user
        order_id = request.data.get('id')
        if not order_id:
            raise ParamError('订单不能为空', ErrorCode.PARAM_ERROR)

        file_obj = request.FILES.get('file')
        if not file_obj:
            raise ParamError('文件不能为空', ErrorCode.PARAM_ERROR)

        customer_order = CustomerOrder.objects.filter(id=order_id, del_flag=False).first()
        if not customer_order:
            raise ParamError('订单不存在', ErrorCode.PARAM_ERROR)

        workbook = openpyxl.load_workbook(file_obj)
        # 获取第一张表
        table = workbook.worksheets[0]

        # 从table解析数据
        order_data = FbmOrderImportService.obtain_fbm_data_from_table(table)
        workbook.close()

        product_code = order_data.product_code
        product = order_import_verification_product_code(product_code, user)

        # 校验数据
        FbmOrderImportService.validate_fbm_data(order_data, is_update=True)

        # customer_order = FbmOrderImportService().update_fbm_order(
        #     order_data,
        #     user,
        #     order_id=order_id,
        # )

        # 处理订单
        FbmOrderImportService().process_order(order_data, customer_order.create_by, customer_order.id,
                                              keep_shipment_id=True)

        logger.info(f"{customer_order.order_num} import_update_fbm_order success")
        return Response(data={'code': 200, 'msg': '导入更新订单成功！'}, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def import_update_fba_order_normal(self, request):
        """
        导入更新FBA订单excel文件 -- 标准导入
        """

        fba_order_import_update_service = FBAOrderImportUpdateService()
        fba_order_import_update_service.import_update_fba_order(request, goods_type='COM')

        return Response(data={'code': 200, 'msg': '导入更新订单成功！'}, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def import_update_fba_order_single(self, request):
        """
        导入更新FBA订单excel文件  - 单一商品
        """

        fba_order_import_update_service = FBAOrderImportUpdateService()
        fba_order_import_update_service.import_update_fba_order(request, goods_type='SINGLE')

        return Response(data={'code': 200, 'msg': '导入更新订单成功！'}, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def print_box_label(self, request):
        """
        打印箱唛
        """
        ids = request.data['ids']
        select_data = request.data.get('sourceData', None)  # 选择的货件
        # label_size = request.data.get('label_size', '100*150')  # 面单尺寸，默认100*150
        # label_size = request.data.get('params', {}).get('label_size', '100*150')  # 面单尺寸，默认100*150

        # # 验证尺寸参数
        # if label_size not in ['100*100', '100*150']:
        #     return fail_response(request, '面单尺寸参数错误，只支持 100*100 或 100*150')

        # 检查这些订单状态(排除草稿和等待作业状态的订单)
        error_orders = CustomerOrder.objects.filter(order_status__in=['DR', 'WO', 'VO'], id__in=ids, del_flag=False)
        if error_orders.exists():
            error_order_nums = error_orders.values_list('order_num', flat=True)
            return fail_response(request, f'审核后才能下载箱唛, 订单: {", ".join(error_order_nums)}')

        # 检查订单是否有产品关联
        customer_orders = CustomerOrder.objects.filter(~Q(product=None), id__in=ids, del_flag=False)
        if len(customer_orders) != len(ids):
            return fail_response(request, '请检查所选客户订单都选择了产品！')

        select_shipment_ids = [i['shipment_id'] for i in select_data] if select_data else None

        result_list = print_fbm_box_label_common(customer_orders, select_shipment_ids, '100*112')

        request.data['data'] = list(map(
            lambda x: {'file_name': f"箱唛{x['order_num']}_{'100*112'}.pdf", 'base64': x['base64_data']}, result_list)
        )
        return success_response(request, '打印箱唛成功！')

    @action(methods=['POST'], detail=False)
    def print_box_label_100(self, request):
        """
        打印箱唛
        """
        ids = request.data['ids']
        select_data = request.data.get('sourceData', None)  # 选择的货件

        # 检查这些订单状态(排除草稿和等待作业状态的订单)
        error_orders = CustomerOrder.objects.filter(order_status__in=['DR', 'WO', 'VO'], id__in=ids, del_flag=False)
        if error_orders.exists():
            error_order_nums = error_orders.values_list('order_num', flat=True)
            return fail_response(request, f'审核后才能下载箱唛, 订单: {", ".join(error_order_nums)}')

        # 检查订单是否有产品关联
        customer_orders = CustomerOrder.objects.filter(~Q(product=None), id__in=ids, del_flag=False)
        if len(customer_orders) != len(ids):
            return fail_response(request, '请检查所选客户订单都选择了产品！')

        select_shipment_ids = [i['shipment_id'] for i in select_data] if select_data else None

        result_list = print_fbm_box_label_common(customer_orders, select_shipment_ids, '100*100')

        request.data['data'] = list(map(
            lambda x: {'file_name': f"箱唛{x['order_num']}_{'100*100'}.pdf", 'base64': x['base64_data']}, result_list)
        )
        return success_response(request, '打印箱唛成功！')

    @action(methods=['POST'], detail=False)
    def export_box_order(self, request):
        """
        导出箱单
        """
        ids = request.data['ids']
        customer_orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        if len(customer_orders) != len(ids):
            return fail_response(request, '请检查所选客户订单！')

        results = export_box_order_common(customer_orders)
        data_list = []
        for order_num, base64_data in results.items():
            file_name = f"boxOrder_{order_num}.xlsx"
            data_list.append({'file_name': file_name, 'base64': base64_data})

        request.data['data'] = data_list
        return success_response(request, '导出箱单成功！')

    @action(methods=['POST'], detail=False)
    def export_inbound_order(self, request):
        """
        生成入仓单Excel文件
        """
        ids = request.data['ids']
        customer_orders = CustomerOrder.objects.filter(
            ~Q(order_status__in=['DR', 'WO', 'VO']),
            id__in=ids,
            del_flag=False
        )

        if len(customer_orders) != len(ids):
            return fail_response(request, '订单未审核，无法下载进仓单')

        results = export_inbound_order_common(customer_orders)
        data_list = []
        for order_num, base64_data in results.items():
            file_name = f"{order_num}.xlsx"
            data_list.append({'file_name': file_name, 'base64': base64_data})

        request.data['data'] = data_list
        return success_response(request, '导出入仓单成功！')

    @action(methods=['POST'], detail=False)
    def print_inbound_box_label(self, request):
        """
        一键下载箱唛及入仓单
        """
        ids = request.data['ids']
        # select_data = request.data.get('sourceData', None)  # 选择的货件

        # 检查这些订单状态(排除草稿和等待作业状态的订单)
        error_orders = CustomerOrder.objects.filter(order_status__in=['DR', 'WO', 'VO'], id__in=ids, del_flag=False)
        if error_orders.exists():
            error_order_nums = error_orders.values_list('order_num', flat=True)
            return fail_response(request, f'审核后才能下载箱唛及入仓单, 订单: {", ".join(error_order_nums)}')

        # 检查订单是否有产品关联
        customer_orders = CustomerOrder.objects.filter(~Q(product=None), id__in=ids, del_flag=False)
        if len(customer_orders) != len(ids):
            return fail_response(request, '请检查所选客户订单都选择了产品！')

        data_list = []

        # 1. 生成箱唛PDF
        box_label_results = print_fbm_box_label_common(customer_orders=customer_orders)

        # 添加箱唛文件到返回列表
        for result in box_label_results:
            file_name = f"箱唛{result['order_num']}_100*112.pdf"
            data_list.append({'file_name': file_name, 'base64': result['base64_data']})

        # 2. 生成入仓单Excel
        inbound_results = export_inbound_order_common(customer_orders)

        # 添加入仓单文件到返回列表
        for order_num, base64_data in inbound_results.items():
            file_name = f"{order_num}.xlsx"
            data_list.append({'file_name': file_name, 'base64': base64_data})

        request.data['data'] = data_list
        return success_response(request, '一键下载箱唛及入仓单成功！')

    @transaction.atomic
    @action(methods=['GET'], detail=False)
    def get_fbm_track_code(self, request):
        """
        获取FBM订单轨迹
        @param request:
        @return:
        """

        request.data['data'] = FBM_TRACK_CODE_LIST
        return success_response(request, '获取成功')

    # fbm订单轨迹更改
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_fbm_track(self, request):
        """
        修改FBM轨迹
        @param request:
        @return:
        """
        ids = request.data['ids']
        user = request.user
        code = request.data['selectActionVal']
        track_time = request.data.get('date') or datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        target_order_status, track_code, track_name = convert_fbm_track_code_by(code)
        if not track_code:
            return fail_response(request, '请选择正确的轨迹！')

        orders = CustomerOrder.objects.filter(id__in=ids)
        for customer_order in orders:
            # customer_order 还未被修改前备份原始对象
            original_obj = deepcopy(customer_order)
            change_fields = ['order_status']  # 记录修改的字段列表

            # 校验状态
            # validate_fbm_order_status(customer_order.order_status, target_order_status)
            # 已离港时判断是否有确认体积
            if code == 'SO':
                if not customer_order.confirm_volume:
                    return fail_response(request, '没有确认体积, 请添加确认体积！')
            # 已到货时更新签入时间
            if code == 'IW':
                customer_order.check_in_time = track_time
                customer_order.save()
                change_fields.append('check_in_time')
            # 确认入仓自动计价
            elif code == 'IWC':
                # 异步计费
                future_time = datetime.now() + timedelta(seconds=10)
                is_open_auto_charge_price = Dict.objects.filter(label='is_open_auto_charge_price',
                                                                      value='1',
                                                                      del_flag=False)
                if is_open_auto_charge_price.exists():
                    common_order_async_task(customer_order.order_num, 'TR', 'BL', request.user,
                                            customer_order_num=customer_order.ref_num, start_time=future_time)

            # 已到海外仓
            elif code == 'IWW':
                customer_order.save_fields(
                    actual_arrived_wh_date=datetime.strptime(track_time, '%Y-%m-%d %H:%M:%S').date()
                )
                change_fields.append('actual_arrived_wh_date')
            customer_order.order_status = target_order_status
            if track_code == 'warehousing':
                customer_order.arrival_date = track_time
                if isinstance(track_time, str):
                    customer_order.arrival_date = datetime.strptime(track_time, '%Y-%m-%d %H:%M:%S').date()
                else:
                    customer_order.arrival_date = track_time
                change_fields.append('arrival_date')
            elif track_code == 'signed':
                customer_order.sign_time = track_time
                change_fields.append('sign_time')
            customer_order.save()

            # 增加操作记录
            OrderFieldChangeLog.record_instance_changes(old_instance=original_obj,
                                                        new_instance=customer_order,
                                                        fields_list=change_fields,
                                                        order_type='FBA',
                                                        user=user)
            # 新增轨迹
            params = {
                'order_num': customer_order.order_num,
                'order_id': customer_order.id,
                'track_code': track_code,
                'track_name': track_name,
                'actual_time': track_time,
                'product': customer_order.product,
                'remark': ""
            }
            Track.objects.create(**params)
            logger.info(f"修改订单{customer_order.order_num}的FBM轨迹为{track_name}，实际时间{track_time}")

        return success_response(request, '轨迹修改成功')

    # 改为下一个状态
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_fbm_next_track(self, request):
        """
        @param request:
        @return:
        """
        # return success_response_common()
        customer_order_id = request.data['id']
        track_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        customer_order = CustomerOrder.objects.get(id=customer_order_id)
        # 获取下一个轨迹状态
        next_code = get_fbm_order_next_status(customer_order.order_status)

        target_order_status, track_code, track_name = convert_fbm_track_code_by(next_code)
        if not track_code:
            return fail_response(request, '请选择正确的轨迹！')
        if next_code == 'IW':
            return fail_response(request, '不允许在详情页更新状态为已到货, 请在列表页修改')

        # 已离港时判断是否有确认体积
        if next_code == 'SO':
            if not customer_order.confirm_volume:
                return fail_response(request, '没有确认体积, 请添加确认体积！')
        # 已到货时更新签入时间
        if next_code == 'IW':
            customer_order.check_in_time = track_time
            customer_order.save()
        customer_order.order_status = target_order_status
        if track_code == 'warehousing':
            customer_order.arrival_date = track_time
            if isinstance(track_time, str):
                customer_order.arrival_date = datetime.strptime(track_time, '%Y-%m-%d %H:%M:%S').date()
            else:
                customer_order.arrival_date = track_time
        elif track_code == 'signed':
            customer_order.sign_time = track_time
        customer_order.save()

        # 新增轨迹
        params = {
            'order_num': customer_order.order_num,
            'order_id': customer_order.id,
            'track_code': track_code,
            'track_name': track_name,
            'actual_time': track_time,
            'product': customer_order.product,
            'remark': ""
        }
        Track.objects.create(**params)
        logger.info(f"修改订单{customer_order.order_num}的FBM轨迹为{track_name}，实际时间{track_time}")
        return success_response(request, '轨迹修改成功')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def submit_pre_order(self, request):
        """
        提交预约仓单
        @param request:
        @return:
        """

        order_id = request.data['order_id']
        try:
            customer_order = CustomerOrder.objects.get(id=order_id, del_flag=False)
        except CustomerOrder.DoesNotExist:
            return fail_response(request, '订单不存在！')

        warehouse_id = request.data['warehouse_id']
        pedder_integration_service = PedderIntegrationService()
        response = pedder_integration_service.create_order(customer_order, int(warehouse_id))
        if response.is_success():
            customer_order.external_order_num = response.data.pre_order_no
            customer_order.save()
            return success_response(request, '创建预约仓单成功！')

        return fail_response(request, response.msg)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def update_pre_order(self, request):
        """
        更新预约仓单
        @param request:
        @return:
        """

        order_id = request.data['order_id']
        try:
            customer_order = CustomerOrder.objects.get(id=order_id, del_flag=False)
        except CustomerOrder.DoesNotExist:
            return fail_response(request, '订单不存在！')

        warehouse_id = request.data['warehouse_id']
        pedder_integration_service = PedderIntegrationService()
        response = pedder_integration_service.update_order(customer_order, int(warehouse_id))
        if response.is_success():
            return success_response(request, '更新预约仓单成功！')

        return fail_response(request, response.msg)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def delete_pre_order(self, request):
        """
        删除预约仓单
        @param request:
        @return:
        """

        order_id = request.data['order_id']
        try:
            customer_order = CustomerOrder.objects.get(id=order_id, del_flag=False)
        except CustomerOrder.DoesNotExist:
            return fail_response(request, '订单不存在！')

        pedder_integration_service = PedderIntegrationService()
        response = pedder_integration_service.delete_order(customer_order)
        if response.get("code") == 10200:
            customer_order.external_order_num = ''
            customer_order.external_order_status = "X"
            customer_order.save()
            return success_response(request, '删除预约仓单成功！')

        return fail_response(request, response.msg)

    # 修改海外仓
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_oversea_warehouse(self, request):
        """
        修改海外仓地址
        联动修改货件号海外仓地址+出仓指令地址
        @param request:
        @return:
        """

        ids = request.data['ids']
        user = request.user
        address = request.data['address']
        if not address:
            return fail_response(request, '请选择正确的地址！')

        address_obj = Address.objects.filter(id=address).first()
        if not address_obj:
            return fail_response(request, '地址不存在！')

        address_num = address_obj.address_num
        customer_orders = CustomerOrder.objects.filter(id__in=ids, del_flag=False)
        if customer_orders.count() != len(ids):
            return fail_response(request, '请选择正确的订单！')

        for customer_order in customer_orders:
            # 增加操作记录
            modify_data = {'ocean_warehouse': address_obj}
            OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', user)

            customer_order.save_fields(ocean_warehouse=address_obj)
            # shipment_list = OcShipment.objects.filter(customer_order_num=customer_order, del_flag=False)
            # if not shipment_list:
            #     continue
            # for shipment in shipment_list:
            #     shipment.address_num = address_num
            #     shipment.save()
            #
            # shipment_ids = shipment_list.values_list('shipment_id', flat=True)
            # outbound_instructs = OutboundInstruct.objects.filter(
            #     oc_shipment__shipment_id__in=shipment_ids,
            #     del_flag=False
            # )
            # for outbound_instruct in outbound_instructs:
            #     outbound_instruct.address_num = address_num
            #     outbound_instruct.save()
            update_oc_shipment_outbound_instruct_data(customer_order)

        return success_response(request, '修改成功')

    # 批量配置并创建出口报关单(批量配置出口报关单)
    @action(methods=['POST'], detail=False)
    def conf_clearance_out(self, request):
        ids = request.data.get('ids', None)
        if not ids:
            return fail_response(request, '请选择订单')

        customer_orders = CustomerOrder.objects.filter(
            ~Q(order_status='VO'),
            clearance_out=None,
            id__in=ids,
            del_flag=False
        )
        if len(customer_orders) != len(ids):
            return fail_response(request, '请选择未配置出口报关单的未作废订单')
        customer = None
        for order in customer_orders:
            if not customer:
                customer = order.customer
            else:
                if customer != order.customer:
                    return fail_response(request, '请选择相同客户的订单')
        if len(ids) > 1:
            clear_type = 'MC'
        else:
            clear_type = 'AC'
        # data = dict(
        #     clear_type=clear_type,
        #     carton=0,
        #     weight=0,
        #     volume=0
        # )
        # for i in customer_orders:
        #     data['carton'] += i.carton or i.pre_carton or 0
        #     data['weight'] += i.weight or i.pre_weight or 0
        #     data['volume'] += i.volume or i.pre_volume or 0

        clearance = ClearanceOut()
        clearance.create_by = request.user
        clearance.update_by = request.user
        clearance.save()
        clearance.clearance_num = settings.CLEARANCE_OUT_MARK + settings.CUSTOMER_ORDER_MARK + \
                                  create_order_num(clearance.id)
        clearance.clear_type = clear_type
        clearance.customer = customer
        # for k, v in data.items():
        #     setattr(clearance, k, v)
        clearance.save()
        # for i in customer_orders:
        #     i.clearance_out = clearance
        #     i.save()

        # 增加操作记录
        for customer_order in customer_orders:
            modify_data = {'clearance_out': clearance, 'is_customs_declaration': True}
            OrderFieldChangeLog.record(customer_order, modify_data, 'FBA', request.user)

        customer_orders.update(clearance_out=clearance, is_customs_declaration=True)

        summary_order_weight_and_size(clearance, 'clearance_out')

        return success_response(request, 'success')

    # # 成本收入快捷录入(单独修改订单的费用)...modify_customer_order_charge方法在序列化器中和视图函数中的区别太大, 不能兼容, 弃用
    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def quick_entry_charge(self, request):
    #     modify_customer_order_charge(request)
    #     return success_response_common(data={'msg': 'OK'})

    # 批量导入订单收入
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def upload_file_order_revenue(self, request):
        header_map = [
            {'field_name': '订单号', 'field': 'customer_order_num', 'type': 'foreign_key',
             'foreign_key_map': {'field': 'order_num', 'model': CustomerOrder}},
            {'field_name': '费用名称', 'field': 'charge', 'type': 'foreign_key',
             'foreign_key_map': {'model': Charge, 'field': 'name'}},
            {'field_name': '单价', 'field': 'charge_rate', 'type': 'decimal', 'decimal_places': 2},
            {'field_name': '数量', 'field': 'charge_count', 'type': 'decimal', 'decimal_places': 3},
            {'field_name': '币种', 'field': 'currency_type', 'type': 'Str'},
            # {'field_name': '供应商编码', 'field': 'supplier', 'type': 'foreign_key',
            #  'foreign_key_map': {'field': 'short_name', 'model': Company}},
            # {'field_name': '备注', 'field': 'remark', 'type': 'Str'},
        ]
        calc_fields = {'charge_total': 'charge_rate * charge_count',  'account_charge': 'charge_total'}

        def data_check(instance: CustomerOrderChargeIn):
            old_charge_ins = CustomerOrderChargeIn.objects.filter(customer_order_num=instance.customer_order_num,
                                                                  charge=instance.charge, del_flag=False)
            if old_charge_ins.exists():
                return 'update', old_charge_ins
            else:
                return 'add', None

        def get_order_customer(instance: CustomerOrderChargeIn):
            return instance.customer_order_num and instance.customer_order_num.customer

        default_fields = {'customer': get_order_customer}

        common_upload_excel(request, CustomerOrderChargeIn, header_map, calc_fields=calc_fields,
                            default_fields=default_fields, data_check=data_check)
        return success_response_common(msg='导入成功')

    # 批量导入订单成本
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def upload_file_order_cost(self, request):
        header_map = [
            {'field_name': '订单号', 'field': 'customer_order_num', 'type': 'foreign_key',
             'foreign_key_map': {'field': 'order_num', 'model': CustomerOrder}},
            {'field_name': '费用名称', 'field': 'charge', 'type': 'foreign_key',
             'foreign_key_map': {'model': Charge, 'field': 'name'}},
            {'field_name': '单价', 'field': 'charge_rate', 'type': 'decimal', 'decimal_places': 2},
            {'field_name': '数量', 'field': 'charge_count', 'type': 'decimal', 'decimal_places': 3},
            {'field_name': '币种', 'field': 'currency_type', 'type': 'Str'},
            {'field_name': '供应商编码', 'field': 'supplier', 'type': 'foreign_key',
             'foreign_key_map': {'field': 'short_name', 'model': Company}},
        ]
        calc_fields = {'charge_total': 'charge_rate * charge_count',  'account_charge': 'charge_total'}

        def data_check(instance: CustomerOrderChargeOut):
            old_charge_outs = CustomerOrderChargeOut.objects.filter(customer_order_num=instance.customer_order_num,
                                                                    charge=instance.charge, del_flag=False)
            if old_charge_outs.exists():
                return 'update', old_charge_outs
            else:
                return 'add', None

        common_upload_excel(request, CustomerOrderChargeOut, header_map, calc_fields=calc_fields,
                            data_check=data_check, sheet_name='订单成本')
        return success_response_common(msg='导入成功')

    # # 批量导入订单派送费成本
    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def upload_file_order_cost(self, request):
    #     header_map = [
    #         {'field_name': '订单号', 'field': 'customer_order_num', 'type': 'foreign_key',
    #          'foreign_key_map': {'field': 'order_num', 'model': CustomerOrder}},
    #         {'field_name': '派送费金额', 'field': 'charge_total', 'type': 'Str'},
    #         {'field_name': '供应商编码', 'field': 'supplier', 'type': 'foreign_key',
    #          'foreign_key_map': {'field': 'short_name', 'model': Company}},
    #         {'field_name': '币种', 'field': 'currency_type', 'type': 'Str'}
    #     ]
    #     # 只导入派送费
    #     charge_name = '派送费'
    #     charge = Charge.objects.filter(name=charge_name, del_flag=False).first()
    #     if not charge:
    #         raise ParamError(f'未找到费用项: {charge_name}', ErrorCode.PARAM_ERROR)
    #     default_fields = {'charge': charge, 'charge_count': 1}
    #     calc_fields = {'charge_rate': 'charge_total', 'account_charge': 'charge_total'}
    #
    #     calc_fields = calc_fields or {}
    #     default_fields = default_fields or {}
    #     # field_name_map = {i['field_name']: {'field': i['field'], 'type': i['type']} for i in header_map}
    #     field_name_map = {}
    #     for item in header_map:
    #         field_name = item.get('field_name')
    #         field_name_map[field_name] = item
    #     print('time1-->', datetime.now())
    #     excel = request.FILES.get('file')
    #     # 将文件内容读取到内存中
    #     file_content = excel.read()
    #     # 使用 io.BytesIO 将内容包装成文件对象
    #     file_object = io.BytesIO(file_content)
    #     # 读取 Excel
    #     workbook = openpyxl.load_workbook(file_object, read_only=True)
    #     # 获取第一张表
    #     print('time1.5-->', datetime.now())
    #     sheet = workbook.worksheets[0]
    #     print('time2-->', datetime.now())
    #     # 获取行数
    #     # max_row = sheet.max_row
    #     # print('max_row-->', max_row)
    #
    #     column_data = []
    #     # 使用 iter_rows 逐行读取
    #     for row in sheet.iter_rows(min_row=2, max_col=1, values_only=True):
    #         if row[0]:
    #             column_data.append(row[0])  # 读取第一列的数据
    #
    #     print('time3-->', datetime.now(), column_data)
    #     logger.info(f'获取所有导入成本的订单: {", ".join(column_data)}')
    #     customer_orders = CustomerOrder.objects.filter(order_num__in=column_data, del_flag=False)
    #     exists_charge = CustomerOrderChargeOut.objects.filter(customer_order_num__in=customer_orders,
    #                                                           charge=charge, del_flag=False)
    #     if exists_charge.exists():
    #         error_order_nums = exists_charge.values_list('customer_order_num__order_num', flat=True)
    #         raise ParamError(f'订单中已存在{charge_name}: {", ".join(error_order_nums)}',
    #                          ErrorCode.PARAM_ERROR)
    #     cost_lock_orders = customer_orders.filter(is_cost_lock=True)
    #     if cost_lock_orders.exists():
    #         error_order_nums = cost_lock_orders.values_list('order_num', flat=True)
    #         raise ParamError(f'订单中已成本确认: {", ".join(error_order_nums)}',
    #                          ErrorCode.PARAM_ERROR)
    #
    #     print('time4-->', datetime.now())
    #     common_upload_excel(request, CustomerOrderChargeOut, header_map, calc_fields=calc_fields,
    #                         default_fields=default_fields, workbook=workbook)
    #     return success_response_common(msg='导入成功')

    # 批量导入FBA订单轨迹
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def upload_excel_order_track(self, request):
        # order_id, order_num, track_code, track_name, actual_time, location
        header_map = [
            {'field': 'order_num', 'field_name': '订单号', 'type': 'Str'},
            {'field': 'track_code', 'field_name': '轨迹代码', 'type': 'Str'},
            # {'field': 'track_code', 'field_name': '轨迹代码', 'type': 'foreign_key',
            #  'foreign_key_map': {'field': 'short_name', 'model': ProductTrackCode}},
            {'field': 'actual_time', 'field_name': '实际时间', 'type': 'DateTime'},
        ]
        # relevance_field_map = {
        #     # bind_ocean 绑定的是 OceanOrder.order_num, 取到model对象后, 获取vessel
        #     'order_id': {'_model_map_': {'model': CustomerOrder, 'key': 'order_num'}, 'foreign_key': 'order_num',
        #                  'mapping': 'id'},
        # }
        default_fields = {
            '_common_fields_map_': {
                'fields': ['order_id', 'product', 'track_name', 'location', 'remark'],
                'func': get_customer_order_track_related_info
            }
        }
        common_upload_excel(request, Track, header_map, default_fields=default_fields, repetition_restrict=True)
        return Response(data={'code': 200, 'data': {}, 'msg': '导入成功'}, status=status.HTTP_200_OK)

    # 批量导入新增/更新FBM测量数据
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def upload_excel_fbm_metrical_data(self, request):
        # todo_x
        # shipment_map = [
        #     {'field': 'order_num', 'field_name': '货件号', 'type': 'Str'},
        # ]
        parcel_size_map = [
            {'field': 'shipment_id', 'field_name': '货件号', 'type': 'Str'},
            {'field': 'parcel_num', 'field_name': '包裹号', 'type': 'Str'},
            {'field': 'actual_length', 'field_name': '长', 'type': 'Str'},
            {'field': 'actual_width', 'field_name': '宽', 'type': 'Str'},
            {'field': 'actual_height', 'field_name': '高', 'type': 'Str'},
            {'field': 'actual_weight', 'field_name': '实收单箱重', 'type': 'Decimal'},
            {'field': 'actual_volume', 'field_name': '实收单箱体积', 'type': 'Decimal'},
        ]
        parcel_map = [
            {'field': 'shipment_id', 'field_name': '货件号', 'type': 'Str'},
            {'field': 'parcel_num', 'field_name': '包裹号', 'type': 'Str'},
            {'field': 'actual_length', 'field_name': '长', 'type': 'Str'},
            {'field': 'actual_width', 'field_name': '宽', 'type': 'Str'},
            {'field': 'actual_height', 'field_name': '高', 'type': 'Str'},
            {'field': 'actual_weight', 'field_name': '实收单箱重', 'type': 'Decimal'},
            {'field': 'actual_volume', 'field_name': '实收单箱体积', 'type': 'Decimal'},
            {'field': 'parcel_confirm_weight', 'field_name': '确认计费重', 'type': 'Decimal'},
            {'field': 'parcel_confirm_volume', 'field_name': '确认计费体积', 'type': 'Decimal'},
        ]

        model_map = [
            {'map': parcel_size_map, 'model': ParcelSize},
            {'map': parcel_map, 'model': Parcel},
        ]

        excel = request.FILES.get('file')
        # 将文件内容读取到内存中
        file_content = excel.read()
        # 使用 io.BytesIO 将内容包装成文件对象
        file_object = io.BytesIO(file_content)
        # 读取 Excel
        workbook = openpyxl.load_workbook(file_object, read_only=True)
        # 获取第一张表
        sheet = workbook.worksheets[0]

        for table_item in model_map:
            field_name_map = {}
            table_map = table_item['map']
            for table_map_item in table_map:
                field_name = table_map_item.pop('field_name')
                field_name_map[field_name] = table_map_item
            table_item['field_name_map'] = field_name_map
            excel_header_map = {}
            # 读取表头
            for row in sheet.iter_rows(max_row=1, values_only=True):
                for index, field in enumerate(row):
                    print('读的啥-->', row[index])
                    if not row[index]:
                        break
                    if row[index] in field_name_map and field not in excel_header_map:
                        excel_header_map[field] = index
                break
            table_item['excel_header_map'] = excel_header_map
        print('model_map22-->', model_map)

        row_count = 1
        customer_order_map = {}
        for row in sheet.iter_rows(min_row=2, values_only=True):
            logger.info(f'first_ocean_order_bind time1--> {datetime.now()}')

            for table_item in model_map:
                # table_map = table_item['map']
                field_name_map = table_item['field_name_map']
                model = table_item['model']
                excel_header_map = table_item['excel_header_map']

                print('field_name_map-->', field_name_map)
                print('excel_header_map-->', excel_header_map)
                if not excel_header_map:
                    raise ParamError('未导入任何数据, 请检查', ErrorCode.PARAM_ERROR)

                # print('row-->', row)
                first_field = list(excel_header_map.keys())[0]
                column_index = excel_header_map[first_field]
                print('first_val-->', row, column_index)
                first_val = row[column_index]
                if not first_val:
                    break
                params = {}
                for field_name, column in excel_header_map.items():
                    logger.info(f'读取到第{row_count}行第{index_to_excel_column(column)}列值: {row[column]}')
                    field = field_name_map[field_name]['field']
                    field_type = field_name_map[field_name].get('type', 'Str')
                    # try:
                    # 如果单元格数据是公式, 则报错
                    cell = sheet.cell(row=row_count, column=column + 1)
                    if cell.data_type == 'f':
                        raise ParamError(f'第{row_count}行, 第{index_to_excel_column(column)}列是公式: {row[column]}, '
                                         f'无法读取数据', ErrorCode.PARAM_ERROR)
                    if field_type == 'Str':
                        field_value = None if row[column] is None else str(row[column]).strip()
                        length = field_name_map[field_name].get('length', 0)
                        if length and len(field_value) > length:
                            raise ParamError(
                                f'第{row_count}行, 第{index_to_excel_column(column)}列, 字符串长度不允许超过: {length}',
                                ErrorCode.PARAM_ERROR)
                    elif field_type == 'Decimal':
                        if row[column]:
                            field_value = Decimal(row[column])
                        else:
                            field_value = None
                    else:
                        field_value = row[column]
                    params[field] = field_value
                parcels = Parcel.objects.filter(parcel_num=params['parcel_num'],
                                                shipment_id=params['shipment_id'], del_flag=False)
                if not parcels.exists():
                    raise ParamError(f'未查询到当前包裹, 包裹号: {params["parcel_num"]}, '
                                     f'货件号: {params["shipment_id"]}', ErrorCode.PARAM_ERROR)
                if model == Parcel:
                    print('要更新的数据1-->', model, parcels, params)
                    parcel = parcels.first()
                    if parcel.customer_order.id not in customer_order_map:
                        customer_order_map[parcel.customer_order.id] = parcel.customer_order
                    parcel.save_fields(
                        actual_length=params['actual_length'] or parcel.actual_length,
                        actual_width=params['actual_width'] or parcel.actual_width,
                        actual_height=params['actual_height'] or parcel.actual_height,
                        actual_weight=params['actual_weight'] or parcel.actual_weight,
                        actual_volume=params['actual_volume'] or parcel.actual_volume,
                        parcel_confirm_weight=params['parcel_confirm_weight'] or parcel.parcel_confirm_weight,
                        parcel_confirm_volume=params['parcel_confirm_volume'] or parcel.parcel_confirm_volume,
                    )
                else:
                    model_queryset = model.objects.filter(parcel_num=params['parcel_num'], del_flag=False)

                    print('要更新的数据2-->', model, model_queryset, params)
                    if model_queryset.exists():
                        params.pop('parcel_num')
                        params.pop('shipment_id')
                        model_queryset.update(**params)
                    else:
                        # model = ParcelSize
                        params.pop('shipment_id')
                        model.objects.create(**params, customer_order=parcels.first().customer_order, parcel_qty=1,
                                             check_date=datetime.now().strftime("%Y-%m-%d"))
            row_count += 1

        if customer_order_map:
            for customer_order in customer_order_map.values():
                summary_predict_parcels_data(customer_order)
                summary_parcels_info(customer_order)

        return success_response_common('导入更新成功')

    @action(methods=['POST'], detail=False)
    def upload_file(self, request):
        order = CustomerOrder.objects.get(id=request.data['id'])
        pod_file = request.FILES.get('pod_file')

        if pod_file:
            order.pod_file = pod_file
            order.save()
            new_name = str(pod_file.name) + '-' + str(order.id)
            order.pod_file = get_file_suffix(settings.STATIC_MEDIA_DIR + str(order.pod_file), new_name)[3]

        order.save()
        return success_response_common(msg='上传成功')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def detele_file(self, request):
        order = CustomerOrder.objects.get(id=request.data['id'])
        file_name = request.data['fileName']

        if file_name == 'pod_file':
            # 发票和箱单
            remove_file(order.pod_file)
            order.pod_file = None

        order.update_by = get_update_params(request)['update_by']
        order.update_date = get_update_params(request)['update_date']
        order.save()
        return success_response_common(msg='删除成功')

    @action(methods=['POST'], detail=False)
    def download_pod_file(self, request):
        # 获取对象
        customer_order = CustomerOrder.objects.get(id=request.data['id'])
        pod_file = None
        if hasattr(customer_order, 'pod_file') and customer_order.pod_file:
            pod_file = customer_order.pod_file
        elif hasattr(customer_order, 'truck_order_id') and customer_order.truck_order_id:
            pod_file = customer_order.truck_order_id.transport_file
        # 检查是否存在 pod_file
        print('什么鬼?-->', pod_file)
        if pod_file:
            # 打开文件并返回 FileResponse
            # file = open(pod_file.path, 'rb')
            # response = FileResponse(file)
            # response['Content-Disposition'] = f'attachment; filename="{customer_order.pod_file.name}"'
            # file_url = request.build_absolute_uri(pod_file.url)
            file_url = pod_file.url
            print('file_url-->', file_url)
            return success_response_common(data={"file_url": file_url})
        else:
            return fail_response_common(msg='POD文件不存在')

    # ama ship track 接口
    @action(methods=['POST'], detail=False,
            renderer_classes=[XMLRenderer],
            parser_classes=[XMLParser])
    def ship_track(self, request):

        # 根据文档实例是按单个tracking_id 查询
        # 后续兼容多个amazon tracking_num
        # xml_data = request.body.decode('utf-8')
        # data_dict = xmltodict.parse(xml_data)
        # 后续兼容多个amazon tracking_num

        try:
            request_data = request.data
        except Exception as e:
            logger.info(f'amazon ship track xml数据解析异常: {str(e)}')
            logger.info(f'请求数据：{request.data}')
            res = gene_xml_not_well_formed('no tracking num') # todo 解析不出单号，但是要返回单号
            return HttpResponse(res, content_type='application/xml; charset=utf-8')

        try:
            username = request_data.get('Validation').get('UserID')
            password = request_data.get('Validation').get('Password')
            api_version = request_data.get('APIVersion')
            tracking_id = request_data.get('TrackingNumber')
        except Exception as e:
            logger.info(f'amazon ship track xml数据解析异常: {str(e)}')
            logger.info(f'请求数据：{request.data}')
            res = gene_xml_not_well_formed('no tracking num')
            return HttpResponse(res, content_type='application/xml; charset=utf-8')

        # api_version 判断 暂时 固定 4.0
        if str(api_version) != '4.0':
            res = gene_xml_invalid_api_version(tracking_id)
            return HttpResponse(res, content_type='application/xml; charset=utf-8')

        user = authenticate(request, username=username, password=password)
        # 鉴权失败
        if not user:
            res = gene_xml_un_authorized_res_data(tracking_id)
            return HttpResponse(res, content_type='application/xml; charset=utf-8')

        # 未传递 tracking_id
        if not tracking_id:
            res = gene_xml_invalid_tracking_num(tracking_id)
            return HttpResponse(res, content_type='application/xml; charset=utf-8')

        # 返回轨迹信息
        track_obj = Track.objects.filter(order_num=tracking_id, del_flag=False).order_by('-actual_time')
        if not track_obj.exists():
            res = gene_xml_invalid_tracking_num(tracking_id)
            return HttpResponse(res, content_type='application/xml; charset=utf-8')

        try:
            ship_track_data = assemble_amazon_ship_track_data_dict(track_obj, tracking_id)
        except Exception as e:
            logger.info(traceback.format_exc())
            logger.info(f'ship track data异常：{str(e)}')
            res = gene_xml_service_not_available(tracking_id)
            return HttpResponse(res, content_type='application/xml; charset=utf-8')

        logger.info(f'包装的ship track数据：{ship_track_data}')

        try:
            res = gene_xml_res_data(ship_track_data)
        except Exception as e:
            logger.info(traceback.format_exc())
            logger.info(f'ship track data异常：{str(e)}')
            res = gene_xml_service_not_available(tracking_id)
            return HttpResponse(res, content_type='application/xml; charset=utf-8')

        return HttpResponse(res, content_type='application/xml; charset=utf-8')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def accept_sync_sys_parcel_num(self, request):
        data = request.data['data']
        order_num = data.get('order_num')
        parcel_nums = data.get('parcel_nums')
        customer_order = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=order_num, del_flag=False).last()
        if not customer_order:
            raise ParamError(f'未找到客户订单号: {order_num}', ErrorCode.PARAM_ERROR)
        for parcel_num, sys_parcel_num in parcel_nums.items():
            parcel = Parcel.objects.filter(customer_order=customer_order, parcel_num=parcel_num, del_flag=False).last()
            if parcel:
                parcel.save_fields(customer_sys_parcel_num=sys_parcel_num)
        return success_response_common()


def get_week_days():
    # 先获得时间数组格式的日期
    weeks = []
    day = 7
    for index in range(0, day):
        day_ago = (datetime.now() - timedelta(days=index))
        str_time = day_ago.strftime("%Y-%m-%d")
        weeks.append(str_time)
    weeks.reverse()
    return weeks


def save_address(buyer_address_num, table, is_warehouse_address=True):
    # buyer_address = Address()
    # buyer_address.address_num = buyer_address_num
    # buyer_address.address_name = buyer_address_num
    # buyer_address.contact_name = get_excel_cell(3, 'i', table)
    # buyer_address.contact_phone = get_excel_cell(8, 'i', table)
    # buyer_address.country_code = get_excel_cell(5, 'i', table)
    # buyer_address.state_code = get_excel_cell(7, 'n', table)
    # buyer_address.city_code = get_excel_cell(7, 'i', table)
    # buyer_address.postcode = get_excel_cell(5, 'n', table)
    # buyer_address.address_one = get_excel_cell(6, 'i', table)
    # buyer_address.company_name = get_excel_cell(4, 'i', table)
    # buyer_address.save()
    # return buyer_address

    # 更换模板
    buyer_address = Address()
    buyer_address.address_num = buyer_address_num
    buyer_address.address_name = buyer_address_num
    buyer_address.contact_name = get_excel_cell(6, 'i', table)
    buyer_address.contact_phone = get_excel_cell(8, 'o', table)
    buyer_address.country_code = get_excel_cell(7, 'i', table)
    buyer_address.state_code = get_excel_cell(7, 'm', table)
    buyer_address.city_code = get_excel_cell(8, 'i', table)
    buyer_address.postcode = get_excel_cell(8, 'm', table)
    buyer_address.address_one = get_excel_cell(7, 'o', table)
    buyer_address.company_name = get_excel_cell(6, 'o', table)
    buyer_address.address_type = 'RC'  # 设为收件人
    if is_warehouse_address:
        buyer_address.save()
    return buyer_address


# FBA订单强制全部入仓公共函数
def all_warehousing_constraint(customer_order, user, code='AW'):
    date_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    track_location, track_remark = get_track_location_remark(customer_order, code)
    set_customer_track_fba(customer_order, code, user, date=date_now, track_remark=track_remark)
    change_order_status(customer_order, code, user)
    # 将订单下未称重的包裹长宽高都置为0
    set_parcel_size_zero(customer_order)

    summary_parcels_info(customer_order)
    # 计算计费重
    calc_bubble_and_charge_weight(customer_order)
    # 客户订单限制
    customer_order_restrict(customer_order)
    # 包裹尺寸重量限制(称重的时候已经限制过了, 这里不用做限制)
    # parcel_size_weight_restrict(customer_order)
    # 计费
    # async_billing_customer_order.delay(customer_order, user)
    future_time = datetime.now() + timedelta(minutes=1)
    common_order_async_task(customer_order.order_num, 'TR', 'BL', user, customer_order_num=customer_order.ref_num,
                            start_time=future_time)
    # 同步给mz
    is_open_sync_supplier_intercept = Dict.objects.filter(label='is_open_sync_supplier_intercept',
                                                          value='1', del_flag=False)
    if is_open_sync_supplier_intercept.exists():
        sync_complete_warehousing_data(customer_order.id)


def get_customer_order_track_related_info(instance):
    code = instance.track_code
    order_num = instance.order_num
    customer_order = CustomerOrder.objects.filter(order_num=order_num, del_flag=False).first()
    track_location, track_remark = get_track_location_remark(customer_order, code)
    if customer_order:
        product_code = ProductTrackCode.objects.filter(product=customer_order.product, del_flag=False,
                                                       track_code__code=code).first()
        track_name = None
        if product_code:
            track_name = product_code.track_code.name
        return customer_order.id, customer_order.product, track_name, track_location, track_remark
    else:
        return None, None, None, track_location, track_remark


def convert_foreign_keys(instance, data_dict):
    """
    将字典中的外键字段值从ID转换为实际的对象实例

    参数:
        instance: Django模型实例 (原始对象)
        data_dict: 通过model_to_dict生成的字典

    返回:
        更新后的字典，外键字段值替换为对象实例
    """
    # 获取模型的所有外键字段
    model = instance.__class__
    foreign_key_fields = [
        f.name for f in model._meta.get_fields()
        if (f.many_to_one or f.one_to_one) and f.concrete
    ]

    # 处理外键字段
    for field in foreign_key_fields:
        if field in data_dict and data_dict[field]:
            # 获取关联对象实例
            data_dict[field] = getattr(instance, field)

    return data_dict


def verify_set_ocean_num(request, ids, ocean_orders):
    """
    配置海运提单时校验(仅仅打异常标签):
    1. 所有订单都没有配出口报关单, 可以配置提单(无需打异常标)
    2. 存在订单没有配出口报关单分情况打异常标:
        所有订单是否配置同一出口报关单, 若不是, 则打异常标签: 报关异常, 订单未配置同一出口报关单XX,xx
        所有订单配置同一出口报关单, 需要选择当前出口报关单下的所有订单进行配置海运提单, 若没有全选,
        则打异常标签: 报关异常, 出口报关单XX下的订单XX,xx(未勾选的)未全部配载到此海运提单XX
    """

    is_verify_set_ocean_num = Dict.objects.filter(label='is_verify_set_ocean_num', value='1', del_flag=False)
    if not is_verify_set_ocean_num.exists():
        return

    logger.info('进入验证------------111')

    # 查询没有配置出口报关单的订单数量
    with_clearance_orders = CustomerOrder.objects.filter(clearance_out__isnull=False, id__in=ids, del_flag=False)
    # 所选订单配置了出口报关单，则分情况打异常标签
    if with_clearance_orders.exists():
        logger.info('进入验证------------222')
        abnormal_queryset = AbnormalTag.objects.filter(type_name='报关异常', del_flag=False)
        if not abnormal_queryset.exists():
            abnormal_obj = AbnormalTag.objects.create(type_name='报关异常',
                                                      **get_update_params(request=request, is_create=True))
        else:
            abnormal_obj = abnormal_queryset.first()

        # 获取所有不同的出口报关单 ID
        # unique_clearance_out_ids = with_clearance_orders.values_list('clearance_out_id', flat=True).distinct()
        unique_clearance_out_nums = with_clearance_orders.values_list('clearance_out__clearance_num',
                                                                      flat=True).distinct()
        # 如果未配置同一出口报关单，则打异常标签: 报关异常,订单xxx未配置同一出口报关单xxx
        if len(unique_clearance_out_nums) > 1:
            for abnormal_order in with_clearance_orders:
                content = f'订单未配置同一出口报关单{",".join(unique_clearance_out_nums)}'
                abnormal_tag_dic = {
                    # 'abnormal_tag': abnormal_obj,
                    'content': content,
                    'customer': abnormal_order.customer,
                    'customer_order_num': abnormal_order
                }

                new_abnormal_obj = CustomerOrderAbnormal.objects.create(
                    **abnormal_tag_dic,
                    **get_update_params(request=request, is_create=True))

                # 设置多对多关系
                new_abnormal_obj.abnormal_tag.add(abnormal_obj)
                # 设置系统自动生成单据号码
                new_abnormal_obj.order_num = 'AM' + create_order_num(new_abnormal_obj.id)
                new_abnormal_obj.save(update_fields=['order_num'])

        # 配置了同一出口报关单,校验这个出口报关单下的所有订单是否都被选中了,异常标签: 报关异常, 出口报关单xxx下的订单未全部配载到此海运单xxx
        else:
            unselected_order_nums = []
            clearance_out_num = unique_clearance_out_nums[0]
            # 找出出口报关单关联的但此次未被选中的订单
            unselected_orders = CustomerOrder.objects.filter(clearance_out__clearance_num=clearance_out_num,
                                                             del_flag=False).exclude(id__in=ids)
            if unselected_orders.exists():
                unselected_order_nums = unselected_orders.values_list('order_num', flat=True).distinct()

            if unselected_order_nums:
                order_nums_str = ','.join(unselected_order_nums)
                ocean_order_nums = [i['order_num'] for i in ocean_orders]
                for abnormal_order in with_clearance_orders:
                    content = f'出口报关单{clearance_out_num}下的订单{order_nums_str}未全部配载到海运单{",".join(ocean_order_nums)}'  # todo海运单几个?
                    abnormal_tag_dic = {
                        # 'abnormal_tag': abnormal_obj,
                        'content': content,
                        'customer': abnormal_order.customer,
                        'customer_order_num': abnormal_order
                    }

                    new_abnormal_obj = CustomerOrderAbnormal.objects.create(**abnormal_tag_dic,
                                                         **get_update_params(request=request, is_create=True))
                    # 设置多对多关系
                    new_abnormal_obj.abnormal_tag.add(abnormal_obj)
                    # 设置系统自动生成单据号码
                    new_abnormal_obj.order_num = 'AM' + create_order_num(new_abnormal_obj.id)
                    new_abnormal_obj.save(update_fields=['order_num'])

def check_revenue_or_cost_is_lock(check_num, check_type):
    """
    检查订单的收入或成本是否已锁定

    Args:
        instance: CustomerOrder实例

    Returns:
        str: 错误消息，如果未锁定则返回空字符串
    """
    if check_type == 'CollectOrder' and check_num:
        collect_order = CollectOrder.objects.get(order_num=check_num)
        if collect_order.is_revenue_lock:
            return True
        if collect_order.is_cost_lock:
            return True
    if check_type == 'truckorder' and check_num:
        truck_order = TruckOrder.objects.get(truck_order_num=check_num)
        if truck_order.is_cost_lock:
            return True
    if check_type == 'oceanorder' and check_num:
        ocean_order = OceanOrder.objects.get(order_num=check_num)
        if ocean_order.is_cost_lock:
            return True
    return False