import os
import traceback
import base64
from datetime import timedelta
from shutil import copyfile
import logging
from rest_framework.decorators import action

import openpyxl
from django.conf import settings
from django.core.checks import messages
from django.db import transaction
from django.db.models import Sum, F, Q, Case, When, DecimalField, Value, Count
from django.http import HttpResponse, FileResponse
from django.utils import timezone
from django.utils.datetime_safe import datetime
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from django.utils.timezone import make_aware

from alita.logger import logger
from common.error import ParamError, ErrorCode
from common.service.master_service import add_push_ems_data
from common.order_num_gen_rule import create_order_num
from common.tools import remove_file, get_update_params, set_parcel_track_info, \
    update_transport_order_track, order_cost_confirm, is_lock_order, cost_unlock_common, \
    set_customer_track_fba, params_query, parse_custom_date, \
    common_packet_cost_share, common_packet_cost_share_cancel, get_track_location_remark, send_file
from common.tools import send_email_with_attach, send_str_email
from common.utils.custom_viewset_base import CustomViewBase
from common.utils.response import fail_500_response, success_response, fail_response, success_response_common
from company.models import Company
from info.models import TrackCode
from order.integration.util.cainiaoUtil import airline_receive
from order.integration.util.iMileUtil import push_master_order_to_mail
from order.integration.util.gwe2Util import mps_bill_lading_create
from order.integration.util.asendiaUtil import asendia_async_close_manifest, get_asendia_async_close_manifest_result
from order.integration.util.px4_util import async_close_manifest_4px
from order.models import MasterOrder, MasterOrderChargeOut, CustomerOrder, CustomerOrderChargeOut, Clearance, Parcel, \
    ClearanceParcel, ParcelItem, ClearanceParcelItem, ClearanceOut, ClearanceOutParcel, ClearanceOutParcelItem, \
    MasterOrderTrack, Track, ClearanceInSku, ClearanceOutSku, MasterOrderPushTask, ParcelOutboundOrder, BigParcel, \
    BigParcelLabel, ParcelCustomerOrder, ParcelOrderAddress, MasterOrderAddress, MasterOrderChargeIn, ParcelTrack, MasterAttachment
from order.serializers.master_order import MasterOrderSerializer, MasterOrderAndDetatilSerializer
from common.custom import RbacPermission
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework_jwt.authentication import JSONWebTokenAuthentication

from pms.util.supplier_cost import cacl_cost
from settle.models import AccountPayable

from apps.order.integration.util.hzhUtil import AwbOrder, getTimestamp, awb_push
from export.models import TemplateType
from common.tools import set_multistep_parcel_track
from celery import shared_task
from apps.common.service.master_service import assemble_barcode_params_for_master_order
from apps.common.utils.barcode_gen import create_barcode_for_master_order
from apps.order.tasks import set_multistep_parcel_track_task, delete_multistep_parcel_track_task


def process_parcel_tracks_batch(master_order_num, node_code, node_type, track_datetime, user, location,
                                parcel_data_list, timezone_offset=None):
    """
    批量处理小包单轨迹
    """
    try:
        logger.info(
            f'批量处理小包单轨迹开始: 空运主单 {master_order_num}, 操作类型 {node_type}, 小包单数量 {len(parcel_data_list)}')

        success_count = 0
        error_count = 0

        for parcel_data in parcel_data_list:
            try:
                order_num = parcel_data['order_num']
                formatted_code = parcel_data['formatted_code']

                if node_type == '1':  # 写入轨迹
                    # 调用现有的轨迹写入函数
                    set_multistep_parcel_track_task.delay(order_num, formatted_code, user.id, location, timezone_offset)
                    success_count += 1

                elif node_type == '2':  # 删除轨迹
                    # 调用异步删除轨迹函数
                    delete_multistep_parcel_track_task.delay(order_num, formatted_code, user.id)
                    success_count += 1

            except Exception as e:
                error_count += 1
                logger.error(f"处理小包单 {order_num} 轨迹失败: {str(e)}")

        logger.info(f'批量处理小包单轨迹完成: 空运主单 {master_order_num}, 成功 {success_count}, 失败 {error_count}')
        return success_count

    except Exception as e:
        logger.error(f"批量处理小包单轨迹异常: {str(e)}")
        return 0


@extend_schema_view(
    list=extend_schema(
        summary="空运主单列表",
        description="列表接口",
        tags=["运输管理"],
    ),
    create=extend_schema(
        summary="空运主单创建",
        description="创建接口",
        tags=["运输管理"],
    ),
    update=extend_schema(
        summary="空运主单更新",
        description="更新接口",
        tags=["运输管理"],
    ),
)
class MasterOrderViewSet(CustomViewBase):
    '''
    主单管理：增删改查
    '''
    queryset = MasterOrder.objects.all()
    # serializer_class = MasterOrderSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ('order_num', 'airline_num', 'destination', 'departure', 'clearance_num', 'clearanceOut_num')
    filterset_fields = ('order_status', 'is_cost_lock', 'airline_num', 'order_num', 'departure', 'destination')
    ordering_fields = ('id',)
    authentication_classes = (JSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    def get_serializer_class(self):
        if self.action == "list":
            return MasterOrderSerializer
        return MasterOrderAndDetatilSerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        # 获取请求中的 startTime 和 endTime 参数
        start_time = self.request.query_params.get('startTime')
        end_time = self.request.query_params.get('endTime')
        order_num = self.request.query_params.get('order_num')
        orderStatus = self.request.query_params.get('orderStatus')

        # 支持order_status逗号分隔的多值查询
        if orderStatus:
            if "," in orderStatus:
                order_status_list = [x.strip() for x in orderStatus.split(',') if x.strip()]
                queryset = queryset.filter(order_status__in=order_status_list)
            else:
                queryset = queryset.filter(order_status=orderStatus)

        # 如果 order_num 存在且包含逗号
        if order_num:
            if "," in order_num:
                order_num_list = [x.strip() for x in order_num.split(',') if x.strip()]
                queryset = queryset.filter(order_num__in=order_num_list)
            else:
                queryset = queryset.filter(order_num=order_num)

        if start_time and end_time:
            try:
                # 将时间字符串转换为 datetime 对象
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                # 过滤 create_date 在指定时间范围内的记录
                queryset = queryset.filter(create_date__gte=start_time, create_date__lte=end_time)
            except ValueError:
                # 如果时间格式错误，可以选择返回一个空的 queryset 或者处理异常
                queryset = queryset.none()  # 或者可以抛出一个异常

        return queryset

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.del_flag = True
        instance.update_by = get_update_params(request)['update_by']
        instance.update_date = get_update_params(request)['update_date']
        instance.save()
        return success_response_common()

    @extend_schema(
        summary="空运主单-列表",
        description='order_type: TR 运输单；',
        tags=["运营端-运输"],
    )
    def list(self, request, *args, **kwargs):
        if request.query_params.get('$order_status'):
            self.queryset = self.queryset.filter(order_status__in=request.query_params.get('$order_status').split(','))

        # 筛选以周为单位的日期范围
        booking_time = request.query_params.get('$booking_time')
        if booking_time:
            booking_time_parse = parse_custom_date(booking_time)[0]
            booking_time_range = [booking_time_parse, booking_time_parse + timedelta(days=7)]
            self.queryset = self.queryset.filter(booking_time__range=booking_time_range)
        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=['POST'])
    def get_export_template(self, request):
        """获取导出模板模板"""
        data = {
            TemplateType.System: {
                'manifest': ['in_us_manifest', 'out_kr_manifest']
            },
            TemplateType.Business: {},
        }
        return success_response_common(data=data)

    # 获取所有未完成的主单
    @transaction.atomic
    @action(methods=['GET'], detail=False)
    def all_notFC_order(self, request):
        queryset = MasterOrder.objects.filter(
            ~Q(order_status__in=['FC', 'VO']),
            del_flag=False,
            # is_first=is_first
        ).order_by('-id')
        search_fields = ('order_num', )
        queryset = params_query(queryset, MasterOrder, request, search_fields=search_fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = MasterOrderSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        else:
            serialized_data = MasterOrderSerializer(queryset, many=True)
            request.data['data'] = serialized_data
            return success_response(request, 'success')

    # 主单完成
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def master_order_finish(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids)
        queryset = queryset.filter(order_status='WO')
        if queryset.count() == 0:
            request.data['msg'] = '请选择等待作业的主单'
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)
        for obj in queryset:
            # 获取主单下的费用
            charge_out_queryset = MasterOrderChargeOut.objects.filter(masterOrder_num=obj.id, del_flag=False)
            # 获取主单关联的所有客户订单
            customer_order_queryset = CustomerOrder.objects.filter(master_num=obj.id, del_flag=False)
            # 获取客户订单中没有计费重量没有的客户订单
            error_order_count = customer_order_queryset.filter(Q(charge_weight__isnull=True) | Q(charge_weight=0),
                                                               del_flag=False).count()
            # 判断改主单是否关联了客户订单，没有关联不允许完成
            if customer_order_queryset.count() == 0:
                request.data['msg'] = '您选择的主单暂未关联客户订单'
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)
            elif error_order_count > 0:
                request.data['msg'] = '请查检所有客户订单是否都已经有计费重量'
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)
            else:
                # 汇总所有客户订单的计费重
                total_charge_weight = customer_order_queryset.aggregate(total=Sum('charge_weight'))['total'] or 0
                for charge_out in charge_out_queryset:
                    # 记账金额
                    charge = charge_out.account_charge
                    charge_account = charge_out.account_charge
                    # 分摊主单的费用到客户订单的成本
                    for order in customer_order_queryset:
                        share_charge_out = CustomerOrderChargeOut()
                        # 费用
                        share_charge_out.charge = charge_out.charge

                        if charge_out.charge_count != 1:
                            # 单价 = 客户单计费重 / 客户单总的计费重  *  当前主单费用的记账金额 / 当前计费重量
                            share_charge_out.charge_rate = order.charge_weight / total_charge_weight * charge / order.charge_weight
                            # 数量 = 当前计费重量
                            share_charge_out.charge_count = order.charge_weight
                        else:
                            # 单价 = 客户单计费重 / 客户单总的计费重  *  当前主单费用的记账金额 / 当前计费重量
                            share_charge_out.charge_rate = order.charge_weight / total_charge_weight * charge
                            # 数量 = 当前计费重量
                            share_charge_out.charge_count = 1

                        # 记账金额 = 客户单计费重 / 客户单总的计费重  *  当前主单费用的记账金额
                        share_charge_out.account_charge = order.charge_weight / total_charge_weight * charge_account
                        # 合计 = 客户单计费重 / 客户单总的计费重  *  当前主单费用的记账金额
                        share_charge_out.charge_total = share_charge_out.charge_rate * share_charge_out.charge_count
                        # 币种
                        share_charge_out.currency_type = 'CNY'
                        # 供应商
                        share_charge_out.supplier = charge_out.supplier
                        # 是否分摊
                        share_charge_out.is_share = True
                        # 客户单id
                        share_charge_out.customer_order_num = order
                        # 分摊单号
                        share_charge_out.share_charge_id = '%s-%s' % (obj.order_num, charge_out.id)
                        share_charge_out.update_by = get_update_params(request)['update_by']
                        share_charge_out.update_date = get_update_params(request)['update_date']
                        share_charge_out.create_by = get_update_params(request, True)['create_by']
                        share_charge_out.create_date = get_update_params(request, True)['create_date']
                        share_charge_out.save()
                    # 生成付款明细
                    params = {
                        'order_num': obj.order_num,
                        'charge_name': charge_out.charge,
                        'supplier': charge_out.supplier,
                        'origin_amount': charge_out.charge_total,
                        'origin_balance': charge_out.charge_total,
                        'origin_currency': charge_out.currency_type,
                        'account_amount': charge_out.account_charge,
                        'account_balance': charge_out.account_charge,
                        'account_time': datetime.now(),
                    }
                    ap = AccountPayable.objects.create(**params, **get_update_params(request, True))
                    ap.cr_num = 'CR' + create_order_num(ap.id)
                    ap.save()
                # 合计主单的总支出
                obj.charge_total = charge_out_queryset.aggregate(total=Sum('account_charge'))['total'] or 0
                # 更新当前主单状态
                obj.order_status = 'FC'
                # 获取主单下面所有的客户订单汇总件数重量体积到主单下面
                obj.customer_carton = customer_order_queryset.aggregate(total=Sum('carton'))['total'] or 0
                obj.customer_weight = customer_order_queryset.aggregate(total=Sum('weight'))['total'] or 0
                obj.customer_volume = customer_order_queryset.aggregate(total=Sum('volume'))['total'] or 0
                obj.update_by = get_update_params(request)['update_by']
                obj.update_date = get_update_params(request)['update_date']
                obj.save()
        request.data['msg'] = 'success'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 状态回退
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def master_order_cancel(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids)
        fail_orders = 0
        queryset = queryset.filter(order_status='FC')

        for obj in queryset:
            # 获取改主单下的客户订单
            master_customer_orders = CustomerOrder.objects.filter(master_num=obj, del_flag=False)
            # 获取当前主单完成生成的付款明细
            accountPayable_queryset = AccountPayable.objects.filter(order_num=obj.order_num, del_flag=False)
            # 新增判断改主单下面的客户订单如果有已经进行成本确认的客户单的话不允许回退
            # 主单下生成的付款明细都没有生成付款单才能回退
            if accountPayable_queryset.filter(
                    payment_num__isnull=True).count() == accountPayable_queryset.count() and master_customer_orders.filter(
                is_cost_lock=False).count() == master_customer_orders.count():
                AccountPayable.objects.filter(order_num=obj.order_num).update(del_flag=True)
                charge_out_queryset = MasterOrderChargeOut.objects.filter(masterOrder_num=obj.id, del_flag=False)
                for charge_out in charge_out_queryset:
                    share_charge_id = '%s-%s' % (obj.order_num, charge_out.id)
                    CustomerOrderChargeOut.objects.filter(share_charge_id=share_charge_id).update(del_flag=True,
                                                                                                  is_system=False,
                                                                                                  **get_update_params(
                                                                                                      request))
                obj.update_by = get_update_params(request)['update_by']
                obj.update_date = get_update_params(request)['update_date']
                obj.order_status = 'WO'
                obj.save()
            else:
                fail_orders += 1
        if fail_orders > 0:
            request.data['msg'] = '%s个订单状态回退成功！%s个订单回退失败，请确保主单所生成的付款明细还未开具付款单,并且主单下的客户单都未进行成本确认' % (
                queryset.count() - fail_orders, fail_orders)
        else:
            request.data['msg'] = '所有订单状态回退成功'

        # request.data['msg'] = 'success'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 生成进口报关单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def create_clearance_order(self, request):
        user = request.user
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids)

        n = queryset.filter(clearance_num=None).count()
        if n != len(ids):
            request.data['msg'] = '请选择未生成进口报关单的主单'
            request.data['data'] = {}
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        for master in queryset:
            params = {
                'master_order_id': master,
                'master_order_num': master.order_num,
                'clear_type': 'MS',
                'destination': master.destination,
                'departure': master.departure,
                'actual_arrivals_date': master.actual_arrivals_date,
                'airline_num': master.airline_num,
                'actual_leave_date': master.actual_leave_date,
                'carton': master.carton or 0,
                'weight': master.weight or 0,
                'volume': master.volume or 0
            }
            clearance = Clearance.objects.create(**params, **get_update_params(request, True))
            clearance.clearance_num = 'CLI' + create_order_num(clearance.id)
            clearance.save()
            master.clearance_num = clearance.clearance_num
            master.save()
            # 获取当前主单下的所有客户订单并汇总收入   支出
            customerOrders = CustomerOrder.objects.filter(master_num=master.id, del_flag=False)
            # 获取客户单客户的个数
            customers = customerOrders.values('customer').distinct()
            if customers.count() == 1:
                company = Company.objects.get(id=customers.first()['customer'])
                clearance.customer = company
            for customer in customerOrders:
                clearance.total_charge_in += customer.income or 0
                clearance.total_charge_out += customer.cost or 0

                # 获取主单下面的客户单的包裹和商品    批量创建包裹和商品明细   并   关联到进口报关单
                parcels = Parcel.objects.filter(customer_order=customer.id, del_flag=False)
                for item in parcels:
                    params = {
                        'parcel_num': item.parcel_num,
                        'parcel_length': item.parcel_length,
                        'parcel_width': item.parcel_width,
                        'parcel_height': item.parcel_height,
                        'parcel_weight': item.parcel_weight,
                        'parcel_volume': item.parcel_volume,
                        'customer_order': customer.order_num,
                        'clearance_order': clearance,
                        'parcel_qty': item.parcel_qty
                    }
                    parcel = ClearanceParcel.objects.create(**params, **get_update_params(request, True))
                    parcel_items = ParcelItem.objects.filter(parcel_num=item.id, del_flag=False)
                    for p_item in parcel_items:
                        params = {
                            'parcel_num': parcel,
                            'item_code': p_item.item_code,
                            'declared_nameCN': p_item.declared_nameCN,
                            'declared_nameEN': p_item.declared_nameEN,
                            'declared_price': p_item.declared_price or None,
                            'item_qty': p_item.item_qty or None,
                            'item_weight': p_item.item_weight or None,
                            'texture': p_item.texture,
                            'item_size': p_item.item_size,
                            'use': p_item.use,
                            'brand': p_item.brand,
                            'model': p_item.model,
                            'customs_code': p_item.customs_code
                        }
                        ClearanceParcelItem.objects.create(**params, **get_update_params(request, True))

            clearance.save()
            # 这里生成商品的汇总
            skus = ClearanceParcelItem.objects.filter(parcel_num__clearance_order=clearance, del_flag=False)
            skus_queryset = skus.values_list('item_code', 'declared_nameCN', 'declared_nameEN').annotate(
                item_qty=Sum('item_qty'), total_weight=Sum('item_weight'))
            parcel_id = {}
            for item in skus_queryset:
                currency_sku = skus.filter(item_code=item[0], declared_nameCN=item[1], declared_nameEN=item[2]).first()
                currency_parcel = currency_sku.parcel_num
                # 体积
                volume = (currency_parcel.parcel_length or 0) * (currency_parcel.parcel_width or 0) * (
                        currency_parcel.parcel_height or 0) * 0.000001
                # 箱数
                if currency_parcel.id in parcel_id.keys():
                    parcel_qty = 0
                else:
                    parcel_qty = currency_parcel.parcel_qty
                    parcel_id[currency_parcel.id] = True
                params = {
                    'item_code': item[0],
                    'declared_nameCN': item[1],
                    'declared_nameEN': item[2],
                    'customs_code': currency_sku.customs_code,
                    'item_qty': item[3],
                    'declared_price': currency_sku.declared_price,
                    'total_price': (currency_sku.declared_price or 0) * (item[3] or 0),
                    'item_length': currency_parcel.parcel_length,
                    'item_width': currency_parcel.parcel_width,
                    'item_height': currency_parcel.parcel_height,
                    'total_weight': item[4],
                    'parcel_qty': parcel_qty,
                    'volume_weight': volume * (parcel_qty or 0) / 6000,
                    'use': currency_sku.use,
                    'brand': currency_sku.brand,
                    'model': currency_sku.model,
                    'texture': currency_sku.texture,
                    'clearance': clearance,
                    'create_by': user,
                    'update_by': user
                }
                ClearanceInSku.objects.create(**params)

        request.data['msg'] = 'success'
        request.data['data'] = {}
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 生成出口报关单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def create_clearance_out_order(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids)

        n = queryset.filter(clearanceOut_num=None).count()
        if n != len(ids):
            request.data['msg'] = '请选择未生成出口报关单的主单'
            request.data['data'] = {}
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        for master in queryset:
            params = {
                'master_order_id': master
            }
            clearance = ClearanceOut.objects.create(**params, **get_update_params(request, True))
            clearance.clearance_num = settings.CLEARANCE_OUT_MARK + settings.CUSTOMER_ORDER_MARK + create_order_num(clearance.id)
            clearance.save()
            master.clearanceOut_num = clearance.clearance_num
            master.save()
            # 获取当前主单下的所有客户订单 并  汇总收入   支出   件数  重量   体积
            customerOrders = CustomerOrder.objects.filter(master_num=master.id, del_flag=False)
            if not customerOrders.exists():
                raise ParamError(f'空运主单{master.order_num}未有关联的运输单', ErrorCode.PARAM_ERROR)

            for customer_order in customerOrders:
                clearance.total_charge_in += customer_order.income or 0
                clearance.total_charge_out += customer_order.cost or 0
                clearance.carton += customer_order.carton or 0
                clearance.weight += customer_order.weight or 0
                clearance.volume += customer_order.volume or 0

                # 获取主单下面的客户单的包裹和商品    批量创建包裹和商品明细   并   关联到进口报关单
                parcels = Parcel.objects.filter(customer_order=customer_order.id, del_flag=False)
                for item in parcels:
                    params = {
                        'parcel_num': item.parcel_num,
                        'parcel_length': item.parcel_length,
                        'parcel_width': item.parcel_width,
                        'parcel_height': item.parcel_height,
                        'parcel_weight': item.parcel_weight,
                        'parcel_volume': item.parcel_volume,
                        'customer_order': customer_order.order_num,
                        'clearance_out_order': clearance,
                        'parcel_qty': item.parcel_qty
                    }
                    parcel = ClearanceOutParcel.objects.create(**params, **get_update_params(request, True))
                    parcel_items = ParcelItem.objects.filter(parcel_num=item.id, del_flag=False)
                    for p_item in parcel_items:
                        params = {
                            'parcel_num': parcel,
                            'item_code': p_item.item_code,
                            'declared_nameCN': p_item.declared_nameCN,
                            'declared_nameEN': p_item.declared_nameEN,
                            'declared_price': p_item.declared_price or None,
                            'item_qty': p_item.item_qty or None,
                            'item_weight': p_item.item_weight or None,
                            'texture': p_item.texture,
                            'item_size': p_item.item_size,
                            'use': p_item.use,
                            'brand': p_item.brand,
                            'model': p_item.model,
                            'customs_code': p_item.customs_code,
                            'fba_no': p_item.fba_no
                        }
                        ClearanceOutParcelItem.objects.create(**params, **get_update_params(request, True))

            clearance.save()
            # 这里生成商品的汇总
            skus = ClearanceOutParcelItem.objects.filter(parcel_num__clearance_out_order=clearance, del_flag=False)

            skus_queryset = skus.values_list('item_code', 'declared_nameCN', 'declared_nameEN').annotate(
                item_qty=Sum('item_qty'), total_weight=Sum('item_weight'))
            parcel_id = {}
            for item in skus_queryset:
                currency_sku = skus.filter(item_code=item[0], declared_nameCN=item[1], declared_nameEN=item[2]).first()
                currency_parcel = currency_sku.parcel_num
                # 体积
                volume = (currency_parcel.parcel_length or 0) * (currency_parcel.parcel_width or 0) * (
                        currency_parcel.parcel_height or 0) * 0.000001
                # 箱数
                if currency_parcel.id in parcel_id.keys():
                    parcel_qty = 0
                else:
                    parcel_qty = currency_parcel.parcel_qty
                    parcel_id[currency_parcel.id] = True
                params = {
                    'item_code': item[0],
                    'declared_nameCN': item[1],
                    'declared_nameEN': item[2],
                    'customs_code': currency_sku.customs_code,
                    'item_qty': item[3],
                    'declared_price': currency_sku.declared_price,
                    'total_price': (currency_sku.declared_price or 0) * (item[3] or 0),
                    'item_length': currency_parcel.parcel_length,
                    'item_width': currency_parcel.parcel_width,
                    'item_height': currency_parcel.parcel_height,
                    'total_weight': item[4],
                    'parcel_qty': parcel_qty,
                    'volume_weight': volume * (parcel_qty or 0) / 6000,
                    'use': currency_sku.use,
                    'brand': currency_sku.brand,
                    'model': currency_sku.model,
                    'texture': currency_sku.texture,
                    'clearance': clearance,
                }
                ClearanceOutSku.objects.create(**params)

        request.data['msg'] = 'success'
        request.data['data'] = {}
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 轨迹更改
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_track(self, request):
        user = request.user
        ids = request.data['ids']
        code = request.data['selectActionVal']
        date = request.data['date']
        queryset = MasterOrder.objects.filter(id__in=ids, del_flag=False)

        track_name = ''
        track_code_queryset = TrackCode.objects.filter(code=code, affiliated_track='M', del_flag=False)
        if track_code_queryset.count() > 0:
            track_name = track_code_queryset.first().name

        for master_order in queryset:
            update_transport_order_track(master_order, MasterOrderTrack, 'master_order_num', request.user, code,
                                         date=date, track_name=track_name)
            # 修改空运主单状态
            if code in dict(MasterOrder.ORDER_STATUS):
                master_order.order_status = code
                master_order.save()
            customer_orders = CustomerOrder.objects.filter(master_num=master_order.id, del_flag=False)
            for order in customer_orders:
                # sync_update_order_track(request.user, code, track_name, order, date)
                set_customer_track_fba(order, code, request.user, date=date, track_name=track_name)
                set_parcel_track_info(code, order, request, order.product, date)

            # 如果是干线离开，添加推送ems任务
            if code == 'departurePort':
                add_push_ems_data(master_order.order_num, user, date)

        request.data['msg'] = '轨迹更新成功！'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 导出清单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def export_list(self, request):
        id = request.data['ids'][0]
        master_order = MasterOrder.objects.get(id=id, del_flag=False)
        queryset = CustomerOrder.objects.filter(master_number=master_order.order_num, del_flag=False)
        try:
            m_file = settings.STATIC_MEDIA_DIR + 'templates/template_master_or_ocean.xlsx'
            # m_file = 'C:/Users/<USER>/Desktop/workspace/static/alita/media/templates/template_master_or_ocean.xlsx'
            new_filename = settings.STATIC_MEDIA_DIR + 'masterAndOcean/' + master_order.order_num + '.xlsx'
            # new_filename = 'C:/Users/<USER>/Desktop/workspace/static/alita/media/templates/' + master_order.order_num + '.xlsx'
            if (os.path.exists(new_filename)):
                os.remove(new_filename)
            copyfile(m_file, new_filename)
            wb = openpyxl.load_workbook(new_filename)
            ws = wb.worksheets[0]
            # 运单号
            ws.cell(2, 1, 'MAWB航空主单号')
            ws.cell(2, 3, master_order.order_num)
            main_row_num = 4
            main_col_num = 1
            index = 1
            for order in queryset:
                ws.cell(main_row_num, main_col_num, index)
                ws.cell(main_row_num, main_col_num + 1, order.order_num)
                ws.cell(main_row_num, main_col_num + 2, '')
                ws.cell(main_row_num, main_col_num + 3, order.pre_carton)
                ws.cell(main_row_num, main_col_num + 4, order.pre_weight)
                ws.cell(main_row_num, main_col_num + 5, order.pre_volume)
                ws.cell(main_row_num, main_col_num + 6, order.buyer_postcode)
                # 商品合并
                str = ','.join([x.declared_nameCN for x in ParcelItem.objects.filter(del_flag=False, parcel_num__customer_order=order)])
                ws.cell(main_row_num, main_col_num + 7, str)
                # Excel换行
                main_row_num += 1
                index += 1
            wb.save(new_filename)
            response = HttpResponse(content_type='application/msexcel')
            response['Content-Disposition'] = 'attachment;filename=%s.xlsx' % order.order_num
            wb.save(response)
            return response
        except ValueError:
            return fail_500_response(request, 'Failed')

    # 作废订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cancel_order(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids, order_status__in=['SM', 'DR', 'WO'], del_flag=False)
        if queryset.count() != len(ids):
            request.data['msg'] = '请选择不是完成状态的主单进行操作！'
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)
        else:
            # 判断该主单下有没有成本或者客户订单
            fail_order = []
            # 更改主单的状态，主单下面的成本也进行逻辑删除
            for item in queryset:
                orderNum = CustomerOrder.objects.filter(master_num=item.id, del_flag=False).count()
                chargeNum = MasterOrderChargeOut.objects.filter(masterOrder_num=item.id, del_flag=False).count()
                if orderNum == 0 and chargeNum == 0:
                    item.order_status = 'VO'
                    item.update_by = get_update_params(request)['update_by']
                    item.update_date = get_update_params(request)['update_date']
                    item.save()
                    # 将对应轨迹系统的订单轨迹删除
                    track = Track()
                    track.track_code = 'CL'
                    track.master_order_num = item
                    track.save()
                    # 将主单下面的客户订单解除与主单的关联关系
                    # CustomerOrder.objects.filter(master_num=item.id, del_flag=False).update(master_num=None)
                    # 将主单下面的成本逻辑删除
                    # MasterOrderChargeOut.objects.filter(masterOrder_num=item.id, del_flag=False).update(del_flag=True)
                else:
                    fail_order.append(item.order_num)

            if len(fail_order) == 0:
                request.data['msg'] = '作废订单成功！'
            else:
                request.data['msg'] = '部分订单因为已添加成本和关联了客户订单未能作废成功，包括有' + ','.join(fail_order)
            request.data['code'] = 200
            return Response(data=request.data, status=status.HTTP_200_OK)

    # 移除客户订单与主单关联
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def remove_order(self, request):
        id = request.data['id']
        order = CustomerOrder.objects.get(id=id, del_flag=False)
        order.master_num = None
        order.update_by = get_update_params(request)['update_by']
        order.update_date = get_update_params(request)['update_date']
        order.save()
        request.data['msg'] = '移除成功'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 上传主单和舱单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def upload_file(self, request):
        # 获取主单
        main_order = MasterOrder.objects.get(id=request.data['id'])
        main_file = request.FILES.get('main_file')
        cabin_file = request.FILES.get('cabin_file')
        if main_file is not None:
            main_order.main_file = main_file
        if cabin_file is not None:
            main_order.cabin_file = cabin_file
        main_order.update_by = get_update_params(request)['update_by']
        main_order.update_date = get_update_params(request)['update_date']
        main_order.save()
        return Response(data={'msg': '上传成功', 'code': 200}, status=status.HTTP_200_OK)

    # 删除主单和舱单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def detele_file(self, request):
        # 获取主单
        main_order = MasterOrder.objects.get(id=request.data['id'])
        if settings.SYSTEM_VERSION == 'V2':
            remove_file(main_order.main_file)
            main_order.main_file = None
        else:
            fileName = request.data['fileName']
            if fileName == 'main_file':
                remove_file(main_order.main_file)
                main_order.main_file = None
            elif fileName == 'cabin_file':
                remove_file(main_order.cabin_file)
                main_order.cabin_file = None
        main_order.update_by = get_update_params(request)['update_by']
        main_order.update_date = get_update_params(request)['update_date']
        main_order.save()
        return Response(data={'msg': '删除成功', 'code': 200}, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def push_order(self, request):

        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids, order_status__in=['SM','WO','ML'], del_flag=False)
        if queryset.count() != len(ids):
            request.data['msg'] = '请选择不是草稿或作废状态的主单进行操作！'
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        for master_order in queryset:
            awb = AwbOrder()
            awb.awb_sn = master_order.order_num
            awb.departure_airport = master_order.departure
            awb.destination_airport = master_order.destination
            if master_order.transit_airport:
               awb.transit_airport = master_order.transit_airport.split(',')
            else:
               awb.transit_airport = ''

            if master_order.airline_num:
                awb.flight_number = master_order.airline_num.split(',')
            awb.departure_time = master_order.plan_leave_date
            awb.arrival_time = master_order.plan_arrivals_date
            awb.push_time = getTimestamp()
            result = awb_push(awb)
            if result['msg'] == 'success':
                master_order.is_push = True
                master_order.update_by = request.user
                master_order.update_date = datetime.now()
                master_order.save()
            else:
                request.data['msg'] = result['msg']
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)

        return Response(data={'msg': '空运主单成功', 'code': 200}, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def push_order_to_imile(self, request):

        ids = request.data['ids']
        master_order_queryset = MasterOrder.objects.filter(id__in=ids, order_status__in=['SM', 'WO', 'ML'], del_flag=False)
        if master_order_queryset.count() != len(ids):
            request.data['msg'] = '请选择不是草稿或作废状态的主单进行操作！'
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        for master_order in master_order_queryset:

            customer_order_queryset=CustomerOrder.objects.filter(master_num=master_order,del_flag=False)
            parcel_numbers = Parcel.objects.filter(
                customer_order__in=customer_order_queryset,
                del_flag=False
            ).values_list('parcel_num', flat=True)

            master_data = {
                'masterWaybillNo': master_order.order_num,
                'bagNumbers': parcel_numbers,
                'shippingWarehouseReceiveDate': '',
                'originalCountry': '',
                'destinationCountry': '',
                'originalPort': master_order.departure,
                'destinationPort': master_order.destination,
                'airline': '',
                'flightNo': master_order.airline_num,
                'etd': master_order.plan_leave_date.strftime("%Y-%m-%d %H:%M:%S"),
                'eta': master_order.plan_arrivals_date.strftime("%Y-%m-%d %H:%M:%S"),
                'totalShipment': str(master_order.carton),
                'grossWeight': str(float(master_order.weight)),
                'volumeWeight': str(float(master_order.weight)),
            }

            result = push_master_order_to_mail(master_data)

            if result['message'] == 'success':
                master_order.is_push = True
                master_order.update_by = request.user
                master_order.update_date = datetime.now()
                master_order.save()
            else:
                request.data['msg'] = result['message']
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)

        return Response(data={'msg': '空运主单成功', 'code': 200}, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def push_order_to_4px(self, request):
        """推送到4px"""
        ids = request.data['ids']
        master_order_queryset = MasterOrder.objects.filter(id__in=ids, order_status__in=['SM', 'WO', 'ML'], del_flag=False)
        if master_order_queryset.count() != len(ids):
            request.data['msg'] = '请选择不是草稿或作废状态的主单进行操作！'
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        for master_order in master_order_queryset:
            master_data = self.generate_master_data_4px(master_order)
            try:
                result = async_close_manifest_4px(master_data)
            except Exception as e:
                logger.info(traceback.format_exc())
                return Response(data={'msg': str(e), 'code': 200}, status=status.HTTP_200_OK)

            logger.info('推送主单到4px---返回结果：', result)

            if int(result['result']) == 1:
                master_order.is_push = True
                master_order.update_by = request.user
                master_order.update_date = datetime.now()
                master_order.save()
                subject = 'scanform推送--' + master_order.order_num
                url = result['data']['labelUrl']
                content = f'Please check your scanform 4px. url is {url}'
                to_mails = '<EMAIL>'.split(';')


                # 发送邮件, 如果多个用分号隔开则循环发送
                send_str_email(subject, content, to_mails, )
            else:
                request.data['msg'] = result['msg']
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)

        return Response(data={'msg': '空运主单成功', 'code': 200}, status=status.HTTP_200_OK)

    def get_parcel_for_master_order(self, master_order):
        customer_order_queryset = CustomerOrder.objects.filter(master_num=master_order, del_flag=False)
        parcel_numbers = Parcel.objects.filter(
            customer_order__in=customer_order_queryset,
            del_flag=False
        )
        bags_li = []
        for parcel_number in parcel_numbers:
            big_parcel_obj = self.get_big_parcel_for_parcel_number(parcel_number)
            small_bag_lis = self.get_small_parcel_for_big_parcel(big_parcel_obj)
            bags_li.append({
                'productCode': big_parcel_obj.product.code if big_parcel_obj else None,
                'bagNo': parcel_number.parcel_num,
                'bagWeight': parcel_number.parcel_weight,
                'qty': big_parcel_obj.parcel_qty if big_parcel_obj else None,
                'parcels': small_bag_lis
            })
        return bags_li

    def get_parcel_for_master_order_4px(self, master_order):
        customer_order_queryset = CustomerOrder.objects.filter(master_num=master_order, del_flag=False)
        parcel_numbers = Parcel.objects.filter(
            customer_order__in=customer_order_queryset,
            del_flag=False
        )
        logger.info(f'订单包裹{parcel_numbers}')
        bags_li = []
        for parcel_number in parcel_numbers:
            big_parcel_obj = self.get_big_parcel_for_parcel_number(parcel_number)
            logger.info(f'大包单包裹{big_parcel_obj}')
            small_bag_lis = self.get_small_parcel_for_big_parcel_4px(big_parcel_obj)
            logger.info(f'小包单跟踪号{small_bag_lis}')
            bags_li.extend(small_bag_lis)
        return bags_li

    def get_parcel_for_master_order_for_asendia(self, master_order):
        customer_order_queryset = CustomerOrder.objects.filter(master_num=master_order, del_flag=False)
        parcel_numbers = Parcel.objects.filter(
            customer_order__in=customer_order_queryset,
            del_flag=False
        )
        bags_li = []
        for parcel_number in parcel_numbers:
            big_parcel_obj = self.get_big_parcel_for_parcel_number(parcel_number)
            if big_parcel_obj and big_parcel_obj.product.code == 'ASENDIA-DB':
                bags_li.append({
                    'BagId': big_parcel_obj.bigParcelLabels.tracking_no or '',
                })
        return bags_li


    @staticmethod
    def get_big_parcel_for_parcel_number(parcel_number):
        return BigParcel.objects.filter(parcel_num=parcel_number.parcel_num).first()

    @staticmethod
    def get_small_parcel_for_big_parcel(big_parcel_obj):
        if big_parcel_obj:
            small_parcel_objs = ParcelCustomerOrder.objects.filter(big_parcel=big_parcel_obj)
            return [{'trackingNo': small_parcel_obj.tracking_num, 'parcelWeight': small_parcel_obj.weighing_weight}
                    for small_parcel_obj in small_parcel_objs]
        return []

    @staticmethod
    def get_small_parcel_for_big_parcel_4px(big_parcel_obj):
        if not big_parcel_obj:
            return []

        small_parcel_objs = ParcelCustomerOrder.objects.filter(
            big_parcel=big_parcel_obj
        ).values_list('tracking_num', flat=True)

        tracking_nums = list(small_parcel_objs)
        return tracking_nums if tracking_nums else []

    def generate_master_data(self, master_order):
        bags_li = self.get_parcel_for_master_order(master_order)
        return {
            'mawb': master_order.order_num,
            'flightNo': master_order.airline_num,
            'deliveryDate': datetime.now(),
            'etd': master_order.plan_leave_date.strftime("%Y-%m-%d %H:%M:%S"),
            'eta': master_order.plan_arrivals_date.strftime("%Y-%m-%d %H:%M:%S"),
            'origPort': master_order.departure,
            'destPort': master_order.destination,
            'countryCode': master_order.supplier.country_code,
            'bags': bags_li
        }

    def generate_master_data_4px(self, master_order):
        bags_li = self.get_parcel_for_master_order_4px(master_order)
        logger.info({'orderNoList': bags_li})
        return {'orderNoList': bags_li}

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def push_order_to_gwe2(self, request):
        ids = request.data['ids']
        master_order_queryset = MasterOrder.objects.filter(id__in=ids, order_status__in=['SM', 'WO', 'ML'], del_flag=False)
        if master_order_queryset.count() != len(ids):
            request.data['msg'] = '请选择不是草稿或作废状态的主单进行操作！'
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        for master_order in master_order_queryset:
            master_data = self.generate_master_data(master_order)
            result = mps_bill_lading_create(master_data)
            logger.info('推送主单到gwe2---返回结果：', result)
            if result['message'] == 'success':
                master_order.is_push = True
                master_order.update_by = request.user
                master_order.update_date = datetime.now()
                master_order.save()
            else:
                request.data['msg'] = result['message']
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)

        return Response(data={'msg': '空运主单成功', 'code': 200}, status=status.HTTP_200_OK)


    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def push_order_to_asendia(self, request):
        """
        此接口需要调用调用两个接口，先推送，后获取推送结果。
        :param request:
        :return:
        """
        ids = request.data['ids']
        master_order_queryset = MasterOrder.objects.filter(id__in=ids, order_status__in=['SM', 'WO', 'ML'], del_flag=False)
        # master_order_queryset = MasterOrder.objects.filter(id__in=ids, order_status__in=['SM', 'WO', 'ML', 'CC'], del_flag=False)
        if master_order_queryset.count() != len(ids):
            request.data['msg'] = '请选择不是草稿或作废状态的主单进行操作！'
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        for master_order in master_order_queryset:
            master_data = self.generate_master_data_for_asendia(master_order)
            try:
                result = asendia_async_close_manifest(master_data)
            except Exception as e:
                logger.info(traceback.format_exc())
                return Response(data={'msg': str(e), 'code': 200}, status=status.HTTP_200_OK)
            logger.info('推送主单到asendia---返回结果：', result)
            res_json = result.json()
            if res_json['Status'] != 'true':
                return Response(data={'msg': f"推送主单到asendia失败: {res_json['ErrorMessage']}", 'code': 200}, status=status.HTTP_200_OK)
            else:
                # 获取推送结果
                queue_id = res_json['QueueId']
                get_result_data = {
                    'QueueIds': [queue_id],
                }
                try:
                    get_result = get_asendia_async_close_manifest_result(get_result_data)
                except Exception as e:
                    logger.info(traceback.format_exc())
                    return Response(data={'msg': str(e), 'code': 200}, status=status.HTTP_200_OK)
                result_json = get_result.json()
                if result_json['Status'] != 'true':
                    return Response(data={'msg': f"获取推送结果失败: {result_json['ErrorMessage']}", 'code': 200}, status=status.HTTP_200_OK)

                # 此处是一条一条发送的，所以返回的结果也是一条
                message_res = result_json['Message'][0]
                if message_res['Status'] != 'finish':
                    logger.info(f'message_res推送主单到asendia---返回结果：{message_res}')
                    return Response(data={'msg': f"推送主单到asendia---返回结果失败: {message_res}", 'code': 200},
                                    status=status.HTTP_200_OK)
                message_res_result = message_res['Result']
                if message_res_result['Status'] != 'true':
                    logger.info(f'message_res_result推送主单到asendia---返回结果：{message_res_result}')
                    return Response(data={'msg': f"推送主单到asendia---返回结果失败: {message_res_result}", 'code': 200},
                                    status=status.HTTP_200_OK)

                # 发送邮件
                # todo 暂注释，后续根据业务情况来放开
                """
                manifest_document = message_res_result['ManifestDocument']
                subject = 'Asendia 推送主单'
                content = 'Asendia 推送主单 成功！'
                to_mails = ['manifest<<EMAIL>>']
                file_data = base64.b64decode(manifest_document)
                send_email_with_attach(subject, content, to_mails, file_data, attach_file_name='test.pdf')
                """

                master_order.is_push = True
                master_order.update_by = request.user
                master_order.update_date = datetime.now()
                master_order.save()
            # if result['message'] == 'success':
            #     master_order.is_push = True
            #     master_order.update_by = request.user
            #     master_order.update_date = datetime.now()
            #     master_order.save()
            # else:
            #     request.data['msg'] = result['message']
            #     request.data['code'] = 400
            #     return Response(data=request.data, status=status.HTTP_200_OK)

        return Response(data={'msg': '空运主单成功', 'code': 200}, status=status.HTTP_200_OK)

    @staticmethod
    def format_date(local_time):
        if not isinstance(local_time, datetime):
            local_time = datetime.combine(local_time, datetime.min.time())
        local_time = make_aware(local_time)
        utc_time = local_time.strftime('%Y-%m-%dT%H:%M:%S')
        return utc_time

    def generate_master_data_for_asendia(self, master_order):
        bags_li = self.get_parcel_for_master_order_for_asendia(master_order)

        return {
            'Mawb': master_order.order_num,
            'FlightNo': master_order.airline_num or '',
            'ETD': self.format_date(master_order.plan_leave_date),
            'ETA': self.format_date(master_order.plan_arrivals_date),
            'DeliveryDate': (master_order.plan_leave_date - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S"),
            'Despatchlists': bags_li
        }

    # 成本确认
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_finish(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids)
        if queryset.filter(Q(is_cost_lock=True) | Q(order_status__in=['DR', 'VO'])).count() == 0:
            for item in queryset:
                order_cost_confirm(item.id, 'MasterOrder', request.user)
        else:
            return fail_response(request, '已成本确认或草稿/作废单据不能进行成本确认！')
        return success_response(request, '成本确认成功！')

    # 成本解锁
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_unlock(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids)
        fail_orders, msg = cost_unlock_common(queryset, request)
        return success_response(request, msg)

    # 成本计价
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def calc_cost(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(~Q(order_status__in=['DR', 'VO'],), id__in=ids, del_flag=False)
        if queryset.count() != len(ids):
            return fail_response(request, '请选择状态为已全部入仓后的客户订单重新计价！')

        is_cost_lock_orders = MasterOrder.objects.filter(id__in=ids, is_cost_lock=True, del_flag=False)
        if is_cost_lock_orders.exists():
            return fail_response(request, f'订单 {is_cost_lock_orders.first().order_num} 已成本确认, 无法重新计价！')

        for customer_order in queryset:
            user = request.user

            MasterOrderChargeOut.objects.filter(customer_order_num=customer_order, del_flag=False, is_system=True).update(del_flag=True)

            cacl_cost(customer_order, user, MasterOrderChargeOut)

        return success_response(request, '重新计价成功！')

    # 空运单成本分摊
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_share(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids)
        fail_orders = 0
        for obj in queryset:
            # 获取主单下的费用
            charge_out_queryset = MasterOrderChargeOut.objects.filter(customer_order_num=obj.id, del_flag=False)
            # 获取主单关联的所有客户订单
            customer_order_queryset = CustomerOrder.objects.filter(master_num=obj.id, del_flag=False)
            if customer_order_queryset.count() == 0:
                fail_orders += 1
                continue

            if charge_out_queryset.count() == 0:
                fail_orders += 1
                continue

            # for customer_order in customer_order_queryset:
            logger.info(f'开始分摊空运单 {obj.order_num} 的成本')
            fail_orders += common_packet_cost_share(obj, customer_order_queryset,
                                                    charge_out_queryset, request.user)
            logger.info(f'空运 {obj.order_num} 的成本分摊完成')

        if fail_orders > 0:
            msg = f'订单成本分摊部分失败, 失败个数: {fail_orders}！'
            return success_response(request, msg)
        else:
            return success_response(request, '成本分摊成功')

    # 空运单取消成本分摊
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_share_cancel(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids, del_flag=False)
        for obj in queryset:
            # 获取成本明细
            # charge_out_queryset = MasterOrderChargeOut.objects.filter(customer_order_num=obj, del_flag=False)
            # if charge_out_queryset.count() == 0:
            #     continue

            # 获取对应的订单
            customer_order_queryset = CustomerOrder.objects.filter(master_num=obj, del_flag=False)
            if customer_order_queryset.count() == 0:
                continue

            for customer_order in customer_order_queryset:
                logger.info(f'开始取消空运单 {obj.order_num} 的分摊成本')
                common_packet_cost_share_cancel(obj, customer_order)
                logger.info(f'取消空运单 {obj.order_num} 的分摊成本完成')

        return success_response(request, '取消成本分摊成功')

    # 推送干线交航
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def push_airline_receive(self, request):
        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids, order_status__in=['SM', 'WO', 'ML'], del_flag=False)
        if queryset.count() != len(ids):
            request.data['msg'] = '请选择不是草稿或作废状态的主单进行操作！'
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        for master_order in queryset:
            big_bag_list = []
            customer_orders = CustomerOrder.objects.filter(master_num=master_order.id, del_flag=False)
            if not customer_orders:
                request.data['msg'] = '空运主单'+str(master_order.order_num)+'未有关联的运输单'
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)

            parcel_outbound_order_list = ParcelOutboundOrder.objects.filter(customer_order__in=customer_orders,
                                                                            del_flag=False)
            if parcel_outbound_order_list.count() == 0:
                request.data['msg'] = '空运主单' + str(master_order.order_num) + '未有关联的出库单'
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)
            big_parcel_queryset = BigParcel.objects.filter(parcel_outbound_order__in=parcel_outbound_order_list,
                                                           del_flag=False)

            if big_parcel_queryset.count() == 0:
                request.data['msg'] = '空运主单' + str(master_order.order_num) + '未有关联的大包单'
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)

            big_parcel_label_list = BigParcelLabel.objects.filter(order_num__in=big_parcel_queryset,
                                                                            del_flag=False)
            for big_parcel_label in big_parcel_label_list:
                big_bag_list.append(big_parcel_label.tracking_no)

            if len(big_bag_list) == 0:
                request.data['msg'] = '空运主单' + str(master_order.order_num) + '未有关联的大包单跟踪号'
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)

            # fileUrl = 'http://' + settings.DOMAIN_URL + '/media/'
            # pic_urls = fileUrl + str(master_order.main_file)
            base64_str = ''
            # if master_order.main_file:
            #     label_path = settings.STATIC_MEDIA_DIR
            #     label_path_str = label_path + str(master_order.main_file)
            #     with open(label_path_str, 'rb') as f:
            #         base64_data = base64.b64encode(f.read())
            #         base64_str = base64_data.decode()


            result = airline_receive(master_order.order_num, str(float(master_order.weight)), master_order.airline_num,
                                     format(master_order.plan_leave_date, 'Y-m-d') + ' 00:00:00',
                                     format(master_order.plan_arrivals_date, 'Y-m-d') + ' 00:00:00',
                                     master_order.departure, master_order.destination, big_bag_list, base64_str)

            if result and result['success'] == 'true':
                master_order.is_push = True
                master_order.update_by = request.user
                master_order.update_date = datetime.now()
                master_order.save()
            else:
                request.data['msg'] = result['errorMsg']
                request.data['code'] = 400
                return Response(data=request.data, status=status.HTTP_200_OK)

        return Response(data={'msg': '推送主单成功', 'code': 200}, status=status.HTTP_200_OK)

    # 推送干线到达
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def push_airline_arrive(self, request):

        ids = request.data['ids']
        queryset = MasterOrder.objects.filter(id__in=ids, order_status__in=['SM', 'WO', 'ML'], del_flag=False)
        if queryset.count() != len(ids):
            request.data['msg'] = '请选择不是草稿或作废状态的主单进行操作！'
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        for master_order in queryset:
            pass

        return Response(data={'msg': '空运主单成功', 'code': 200}, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def node_maintenance(self, request):
        # 定义状态转换顺序映射（基于业务流程）
        STATUS_FLOW = {
            'LOC': {'system_code': 'OL029', 'order': 1, 'name': '已配舱', 'next': 'DEC'},
            'DEC': {'system_code': 'OL030', 'order': 2, 'name': '已报关', 'next': 'HA', 'prev': 'LOC'},
            'HA': {'system_code': 'HOA', 'order': 3, 'name': '已交航', 'next': 'DEP', 'prev': 'DEC'},
            'DEP': {'system_code': 'DPO', 'order': 4, 'name': '已起飞', 'next': 'ARR', 'prev': 'HA'},
            'ARR': {'system_code': 'OL016', 'order': 5, 'name': '已落地', 'next': 'PL', 'prev': 'DEP'},
            'PL': {'system_code': 'CCP', 'order': 6, 'name': '已提取', 'next': 'CC', 'prev': 'ARR'},
            'CC': {'system_code': 'DPCC', 'order': 7, 'name': '已清关', 'next': 'AM', 'prev': 'PL'},
            'AM': {'system_code': 'HOLM', 'order': 8, 'name': '已交邮', 'next': 'LOA', 'prev': 'CC'},
            'LOA': {'system_code': 'LOA', 'order': 9, 'name': '已到达', 'prev': 'AM'}
        }

        # 定义ARR前后的节点分组（用于location判断）
        BEFORE_ARR_NODES = {'LOC', 'DEC', 'HA', 'DEP'}
        ARR_AND_AFTER_NODES = {'ARR', 'PL', 'CC', 'AM', 'LOA'}

        def get_location_by_node(master_order, node_code):
            """根据节点类型获取对应的location"""
            if node_code in BEFORE_ARR_NODES:
                return master_order.departure or 'CN'
            elif node_code in ARR_AND_AFTER_NODES:
                return master_order.destination or 'CN'
            else:
                return 'CN'

        def get_parcel_orders_by_master_order(master_order):
            """根据空运主单获取所有关联的小包单（通过大包单）"""
            parcel_orders = []

            # 空运主单 -> 大包单 -> 小包单
            big_parcels = BigParcel.objects.filter(
                master_order=master_order,
                del_flag=False
            ).select_related('master_order')

            for big_parcel in big_parcels:
                parcels = ParcelCustomerOrder.objects.filter(
                    big_parcel=big_parcel,
                    del_flag=False
                ).select_related('big_parcel')
                parcel_orders.extend(parcels)

            # 去重处理
            seen_ids = set()
            unique_parcel_orders = []
            for parcel in parcel_orders:
                if parcel.id not in seen_ids:
                    seen_ids.add(parcel.id)
                    unique_parcel_orders.append(parcel)

            return unique_parcel_orders

        def validate_status_transition(current_status, target_node, operation_type):
            """验证状态转换的合法性"""
            if operation_type == '1':  # 添加轨迹
                if current_status not in STATUS_FLOW:
                    return False, f"当前状态 {current_status} 不在允许的状态流程中"

                expected_next = STATUS_FLOW[current_status].get('next')
                if target_node != expected_next:
                    current_name = STATUS_FLOW[current_status]['name']
                    expected_next_name = STATUS_FLOW.get(expected_next, {}).get('name',
                                                                                expected_next) if expected_next else '无'
                    target_name = STATUS_FLOW.get(target_node, {}).get('name', target_node)
                    return False, f"当前状态 {current_name} 的下一个状态应该是 {expected_next_name}，不能跳转到 {target_name}"

                return True, "状态转换验证通过"

            elif operation_type == '2':  # 删除轨迹
                # 删除轨迹时，只需要验证目标节点是否在定义的状态流程中
                if target_node not in STATUS_FLOW:
                    return False, f"目标状态 {target_node} 不在允许的状态流程中"

                # 删除轨迹不需要验证当前状态，可以删除任何存在的轨迹
                return True, "删除验证通过"

            return False, "无效的操作类型"

        queryset = request.data['node']
        user = request.user
        success_count = 0
        total_parcels = 0
        error_messages = []

        for node in queryset:
            # 获取节点信息
            node_code = node.get('node')
            node_type = node.get('type')
            master_order_nums = node.get("master_order_num", [])
            date_str = node.get('date', '')
            timezone_offset = node.get('timezone_offset', '')
            remark = node.get('remark', '')
            # 如果是添加操作，必须传时间
            if node_type == '1' and (not date_str or not timezone_offset):
                error_messages.append(f"添加节点操作必须传入时间和时区")
                continue

            # 解析日期字符串为datetime对象
            try:
                if date_str:
                    track_datetime = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                else:
                    track_datetime = datetime.now()
            except ValueError as e:
                error_messages.append(f"日期格式错误: {date_str}，应为YYYY-MM-DD HH:MM:SS格式")
                continue

            # 处理每个空运主单
            for master_order_num in master_order_nums:
                try:
                    # 获取空运主单
                    master_order = MasterOrder.objects.filter(
                        order_num=master_order_num, del_flag=False
                    ).first()

                    if not master_order:
                        error_messages.append(f"未找到空运主单: {master_order_num}")
                        continue

                    # 验证状态转换的合法性
                    current_status = master_order.order_status
                    is_valid, validation_msg = validate_status_transition(current_status, node_code, node_type)

                    if not is_valid:
                        error_messages.append(f"空运主单 {master_order_num}: {validation_msg}")
                        continue

                    # 获取关联的小包单
                    parcel_orders = get_parcel_orders_by_master_order(master_order)

                    if not parcel_orders:
                        error_messages.append(f"空运主单 {master_order_num} 未找到关联的小包单")
                        continue

                    # 根据节点类型获取location
                    location = get_location_by_node(master_order, node_code)

                    # 收集需要处理的小包单数据
                    parcel_data_list = []
                    for parcel_order in parcel_orders:
                        try:
                            parcel_order_num = parcel_order.order_num

                            if node_type == '1':  # 写入轨迹
                                if node_code in STATUS_FLOW:
                                    system_code = STATUS_FLOW[node_code]['system_code']
                                    formatted_code = f"{system_code}-000"
                                else:
                                    formatted_code = f"{node_code}-000"

                                # 检查是否已存在该轨迹 - 使用 system_code 检查（不带 -000）
                                existing_track = ParcelTrack.objects.filter(
                                    order_num=parcel_order_num,
                                    system_code=system_code if node_code in STATUS_FLOW else node_code,
                                    del_flag=False
                                ).exists()

                                if not existing_track:
                                    parcel_data_list.append({
                                        'order_num': parcel_order_num,
                                        'formatted_code': formatted_code  # 创建时使用带 -000 的格式
                                    })

                            elif node_type == '2':  # 删除轨迹
                                if node_code in STATUS_FLOW:
                                    system_code = STATUS_FLOW[node_code]['system_code']
                                    # 对于删除操作，我们需要匹配正确的字段
                                    # 根据日志显示，system_code 字段存储的是不带 -000 的代码
                                    # track_code 字段存储的是带 -000 的代码
                                else:
                                    system_code = node_code

                                # 检查是否存在该轨迹 - 根据实际数据格式进行匹配
                                existing_tracks = ParcelTrack.objects.filter(
                                    order_num=parcel_order_num,
                                    system_code=system_code,  # 直接使用 system_code，不加 -000
                                    del_flag=False
                                )

                                # 调试信息：记录查询结果
                                logger.info(f"删除轨迹检查 - 小包单: {parcel_order_num}, system_code: {system_code}, 找到轨迹数量: {existing_tracks.count()}")
                                if existing_tracks.exists():
                                    for track in existing_tracks:
                                        logger.info(f"找到轨迹 - track_code: {track.track_code}, system_code: {track.system_code}")

                                if existing_tracks.exists():
                                    # 传递实际的 track_code 给删除函数
                                    track_code_to_delete = existing_tracks.first().track_code
                                    parcel_data_list.append({
                                        'order_num': parcel_order_num,
                                        'formatted_code': track_code_to_delete  # 使用实际的 track_code
                                    })
                                else:
                                    logger.info(f"未找到要删除的轨迹 - 小包单: {parcel_order_num}, system_code: {system_code}")

                        except Exception as e:
                            error_messages.append(f"处理小包单 {parcel_order_num} 时出错: {str(e)}")

                    # 批量异步处理小包单轨迹
                    parcel_success_count = 0
                    if parcel_data_list:
                        parcel_success_count = process_parcel_tracks_batch(
                            master_order_num, node_code, node_type, track_datetime,
                            user, location, parcel_data_list, timezone_offset
                        )

                    # 更新空运主单状态
                    if node_type == '1':  # 写入轨迹
                        # 根据节点代码更新对应的时间字段
                        if node_code == 'DEP':
                            master_order.ATD = track_datetime
                        elif node_code == 'ARR':
                            master_order.ATA = track_datetime

                        master_order.order_status = node_code
                        master_order.remark = remark
                        master_order.update_by = user
                        master_order.update_date = track_datetime
                        master_order.save()

                    elif node_type == '2':  # 删除轨迹
                        # 根据删除的节点，清空该节点及之后所有节点的时间字段
                        if node_code == 'DEP':
                            master_order.ATD = None
                            master_order.ATA = None  # 起飞删除了，落地也要清空
                        elif node_code == 'ARR':
                            master_order.ATA = None

                        # 回退到前一个状态
                        if node_code in STATUS_FLOW:
                            previous_status = STATUS_FLOW[node_code].get('prev')
                            if previous_status:
                                master_order.order_status = previous_status
                            else:
                                master_order.order_status = 'LOC'
                        else:
                            master_order.order_status = 'LOC'

                        master_order.update_by = user
                        master_order.update_date = track_datetime
                        master_order.save()

                    total_parcels += parcel_success_count
                    if parcel_success_count > 0:
                        success_count += 1

                except Exception as e:
                    error_messages.append(f"处理空运主单 {master_order_num} 时出错: {str(e)}")



        # 构造返回消息
        if error_messages:
            if success_count > 0:
                msg = f"节点维护部分成功，处理了 {success_count} 个空运主单的 {total_parcels} 个小包单。错误: {len(error_messages)} 个。前3个错误: {'; '.join(error_messages[:3])}"
                code = 206
            else:
                msg = f"节点维护失败，所有操作都有错误: {'; '.join(error_messages[:3])}"
                code = 400
        else:
            if success_count > 0:
                msg = f"节点维护成功，共处理 {success_count} 个空运主单的 {total_parcels} 个小包单"
                code = 200
            else:
                msg = "没有找到需要处理的小包单"
                code = 400

        response_data = {
            'msg': msg,
            'code': 200,
            'success_count': success_count,
            'total_parcels': total_parcels,
            'error_count': len(error_messages),
            'error_details': error_messages if error_messages else [],
            'status_code': code
        }

        return Response(data=response_data, status=status.HTTP_200_OK)

    # 空运主单节点状态切换
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def node_switch(self, request):
        user = request.user
        ids = request.data.get('ids')
        current_status = request.data.get('current_status')
        if ids:
            to_update = MasterOrder.objects.filter(id__in=ids, order_status=current_status)
            if not to_update:
                # 构建更新参数字典
                update_params = {
                    'order_status': current_status,
                    'update_by': user,
                    'update_date': datetime.now()
                }
                # 当 current_status == "RCS" 时，额外更新 cargo_release_time
                if current_status == "RCS":
                    update_params['cargo_release_time'] = datetime.now()

                MasterOrder.objects.filter(id__in=ids).update(**update_params)

                return Response(data={'code': 200, 'data': {}, 'msg': '更改成功'}, status=status.HTTP_200_OK)
        return Response(data={'code': 400, 'data': {}, 'msg': '请不要重复更改'},
                        status=status.HTTP_400_BAD_REQUEST)


    @action(detail=False, methods=['post'])
    def batch_delete_drafts(self, request):
        """
        批量删除草稿接口（支持部分删除）
        请求参数格式：{"ids": [1,2,3]}
        """
        ids = request.data.get('ids', [])

        # 参数校验
        if not isinstance(ids, list) or not ids:
            return Response({'msg': '参数格式错误或ID列表为空', 'code': 400}, status=status.HTTP_400_BAD_REQUEST)

        # 查询符合删除条件的记录
        valid_objs = self.get_queryset().filter(id__in=ids, order_status='DR')
        count = valid_objs.count()
        valid_ids = set(valid_objs.values_list('id', flat=True))
        invalid_ids = list(set(ids) - valid_ids)  # 计算无效ID

        # 根据是否存在有效记录返回不同响应
        if not valid_objs.exists():
            return Response({'msg': '没有符合条件的草稿记录', 'code': 400}, status=status.HTTP_400_BAD_REQUEST)

        # 执行删除操作
        valid_objs.update(del_flag=True)

        response_data = {
            'msg': f'删除成功,删除数量:{count}',
            'invalid_ids': invalid_ids,
            'deleted_count': valid_objs.count(),
            'code': 200
        }

        return Response(response_data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def search(self, request):
        """
        主单管理POST查询接口，支持多条件查询，单号支持空运主单/运输单号/入仓单号三字段模糊或批量命中
        """
        data = request.data
        queryset = MasterOrder.objects.filter(del_flag=False).order_by('-id')
        order_num = data.get('order_num')  # 只暴露一个字段，支持逗号分隔
        airline_num = data.get('airline_num')
        departure = data.get('departure')
        destination = data.get('destination')
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        order_status = data.get('order_status')
        carrier_code = data.get('carrier_code')
        page = data.get('page', 1)
        size = data.get('size', 10)

        # 单号批量查询，支持逗号分隔，最多100个
        if order_num:
            order_nums = [x.strip() for x in order_num.split(',') if x.strip()][:100]
            q = Q()
            for num in order_nums:
                q |= Q(order_num=num) | Q(ref_num=num) | Q(
                    warehouse_receipt_number=num)
            queryset = queryset.filter(q)

        if airline_num:
            queryset = queryset.filter(airline_num=airline_num)
        if departure:
            queryset = queryset.filter(departure=departure)
        if destination:
            queryset = queryset.filter(destination=destination)
        if order_status:
            queryset = queryset.filter(order_status=order_status)
        if carrier_code:
            queryset = queryset.filter(carrier_code=carrier_code)
        if start_time and end_time:
            try:
                from datetime import datetime
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                queryset = queryset.filter(create_date__gte=start_time, create_date__lte=end_time)
            except Exception:
                return fail_response(request, '时间格式错误，需为YYYY-MM-DD HH:MM:SS')

        #手动分页
        page = int(page)
        size = int(size)
        total = queryset.count()
        start = (page - 1) * size
        end = start + size
        results = queryset[start:end]
        serializer = MasterOrderSerializer(results, many=True)

        response_data = {
            'code': 200,
            'msg': '获取空运主单列表成功！',
            'count': total,
            'page': page,
            'size': size,
            'data': serializer.data
        }
        return Response(response_data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def details(self, request):
        """
        主单详情接口，入参为主单id，返回主单及其关联的地址、收入、支出信息
        请求参数: {"id": 1}
        """
        master_order_id = request.data.get('id')
        if not master_order_id:
            return Response({'msg': '缺少主单id', 'code': 400}, status=status.HTTP_400_BAD_REQUEST)

        try:
            master_order = MasterOrder.objects.get(id=master_order_id, del_flag=False)
        except MasterOrder.DoesNotExist:
            return Response({'msg': '主单不存在', 'code': 404}, status=status.HTTP_404_NOT_FOUND)

        # 主单基本信息
        result = MasterOrderSerializer(master_order).data

        senderForm = {}
        recipientForm = {}
        NotifyPartyForm = {}
        # 关联地址
        sender_ins = MasterOrderAddress.objects.filter(customer_order=master_order, address_type="SP", del_flag=False).first()
        recipient_ins = MasterOrderAddress.objects.filter(customer_order=master_order, address_type="RC", del_flag=False).first()
        notify_party_ins = MasterOrderAddress.objects.filter(customer_order=master_order, address_type="NP", del_flag=False).first()
        if sender_ins:
            senderForm = {
                "id": sender_ins.id,
                "country_code": sender_ins.country_code,
                "company_name": sender_ins.company_name,
                "state_code": sender_ins.state_code,
                "city_code": sender_ins.city_code,
                "postcode": sender_ins.postcode,
                "region_code": sender_ins.region_code,
                "street": sender_ins.street,
                "house_no": sender_ins.house_no,
                "address_one": sender_ins.address_one,
                "contact_phone": sender_ins.contact_phone,
                "contact_email": sender_ins.contact_email,
                "id_type": sender_ins.id_type,
                "id_number": sender_ins.id_number,
                "middle_name": sender_ins.middle_name,
                "last_name": sender_ins.last_name,
                "first_name": sender_ins.first_name
            }
        if recipient_ins:
            recipientForm = {
                "id": recipient_ins.id,
                "country_code": recipient_ins.country_code,
                "company_name": recipient_ins.company_name,
                "state_code": recipient_ins.state_code,
                "city_code": recipient_ins.city_code,
                "postcode": recipient_ins.postcode,
                "region_code": recipient_ins.region_code,
                "street": recipient_ins.street,
                "house_no": recipient_ins.house_no,
                "address_one": recipient_ins.address_one,
                "contact_phone": recipient_ins.contact_phone,
                "contact_email": recipient_ins.contact_email,
                "id_type": recipient_ins.id_type,
                "id_number": recipient_ins.id_number,
                "middle_name": recipient_ins.middle_name,
                "last_name": recipient_ins.last_name,
                "first_name": recipient_ins.first_name
            }
        if notify_party_ins:
            NotifyPartyForm = {
                "id": notify_party_ins.id,
                "country_code": notify_party_ins.country_code,
                "company_name": notify_party_ins.company_name,
                "state_code": notify_party_ins.state_code,
                "city_code": notify_party_ins.city_code,
                "postcode": notify_party_ins.postcode,
                "region_code": notify_party_ins.region_code,
                "street": notify_party_ins.street,
                "house_no": notify_party_ins.house_no,
                "address_one": notify_party_ins.address_one,
                "contact_phone": notify_party_ins.contact_phone,
                "contact_email": notify_party_ins.contact_email,
                "id_type": notify_party_ins.id_type,
                "id_number": notify_party_ins.id_number,
                "middle_name": notify_party_ins.middle_name,
                "last_name": notify_party_ins.last_name,
                "first_name": notify_party_ins.first_name
            }

        # 关联收入
        masterOrderChargeIns = []
        charge_in_set = MasterOrderChargeIn.objects.filter(customer_order_num=master_order, del_flag=False).all()
        if charge_in_set:
            for i in charge_in_set:
                in_dict = {}
                in_dict["id"] = i.id
                if i.charge:
                    in_dict["charge"] = i.charge.id
                else:
                    in_dict["charge"] = ""
                in_dict["currency_type"] = i.currency_type
                in_dict["charge_rate"] = i.charge_rate
                in_dict["charge_unit"] = i.charge_unit
                in_dict["remark"] = i.remark
                masterOrderChargeIns.append(in_dict)

        # 关联支出
        masterOrderChargeOuts = []
        charge_out_set = MasterOrderChargeOut.objects.filter(customer_order_num=master_order, del_flag=False).all()
        if charge_out_set:
            for o in charge_out_set:
                out_dict = {}
                out_dict["id"] = o.id
                if o.charge:
                    out_dict["charge"] = o.charge.id
                else:
                    out_dict["charge"] = ""
                out_dict["currency_type"] = o.currency_type
                out_dict["charge_rate"] = o.charge_rate
                out_dict["charge_unit"] = o.charge_unit
                out_dict["remark"] = o.remark
                masterOrderChargeOuts.append(out_dict)

        #附件信息
        attachment_info = []
        attachment_set = MasterAttachment.objects.filter(masterOrder=master_order, del_flag=False).all()
        if attachment_set:
            for a in attachment_set:
                a_dict = {}
                a_dict["id"] = a.id
                a_dict["name"] = a.name
                a_dict["url"] = a.url.url if a.url else ''
                attachment_info.append(a_dict)

        result["senderForm"] = senderForm
        result["recipientForm"] = recipientForm
        result["NotifyPartyForm"] = NotifyPartyForm
        result["masterOrderChargeIns"] = masterOrderChargeIns
        result["masterOrderChargeOuts"] = masterOrderChargeOuts
        result["attachment_info"] = attachment_info

        return Response({'msg': 'success', 'code': 200, 'data': result}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['POST'])
    def add_container_search(self, request):
        """
        添加容器----查询出要添加的容器数据
        支持通过 容器号：parcel_num（大包单）、 批次号：parcel_outbound_order（出货单）、
        product_code（产品编码）、service_code（尾程服务） 和 ship_time（发运时间） 进行查询
        使用并查询逻辑（AND）

        请求参数:
        - parcel_num: 包裹号（字符串或列表，支持逗号分隔）
        - parcel_outbound_order: 出货单号（字符串或列表，支持逗号分隔）
        - product_code: 产品编码（字符串，单个查询）
        - service_code: 尾程服务编码（字符串，单个查询）
        - start_time: 发货开始时间（字符串，格式：YYYY-MM-DD HH:MM:SS）
        - end_time: 发货结束时间（字符串，格式：YYYY-MM-DD HH:MM:SS）
        """
        try:
            # 获取查询参数
            parcel_nums = request.data.get('parcel_num', '')
            parcel_outbound_orders = request.data.get('parcel_outbound_order', '')
            product_codes = request.data.get('product_code', '')
            service_codes = request.data.get('service_code', '')
            start_time = request.data.get('start_time', '')
            end_time = request.data.get('end_time', '')

            # 分别构建查询条件
            parcel_num_q = Q()
            outbound_order_q = Q()
            product_code_q = Q()
            service_code_q = Q()
            ship_time_q = Q()

            # 处理 parcel_num 参数（支持多个查询）
            if parcel_nums:
                if isinstance(parcel_nums, str):
                    # 检查是否包含逗号，支持多值查询
                    if ',' in parcel_nums:
                        parcel_num_list = [num.strip() for num in parcel_nums.split(',') if num.strip()]
                        parcel_num_q = Q(parcel_num__in=parcel_num_list)
                    else:
                        parcel_num_q = Q(parcel_num=parcel_nums.strip())
                elif isinstance(parcel_nums, list):
                    parcel_num_q = Q(parcel_num__in=parcel_nums)

            # 处理 parcel_outbound_order 参数（支持多个查询）
            if parcel_outbound_orders:
                if isinstance(parcel_outbound_orders, str):
                    # 检查是否包含逗号，支持多值查询
                    if ',' in parcel_outbound_orders:
                        outbound_order_list = [order.strip() for order in parcel_outbound_orders.split(',') if
                                               order.strip()]
                        outbound_order_q = Q(parcel_outbound_order__outbound_num__in=outbound_order_list)
                    else:
                        outbound_order_q = Q(parcel_outbound_order__outbound_num=parcel_outbound_orders.strip())
                elif isinstance(parcel_outbound_orders, list):
                    outbound_order_q = Q(parcel_outbound_order__outbound_num__in=parcel_outbound_orders)

            # 处理 product_code 参数（单个查询）
            if product_codes:
                if isinstance(product_codes, str):
                    product_code_q = Q(product__code=product_codes.strip())
                elif isinstance(product_codes, list) and product_codes:
                    product_code_q = Q(product__code=product_codes[0])

            # 处理 service_code 参数（尾程服务，单个查询）
            # BigParcel -> ParcelCustomerOrder -> Service 的关系路径
            if service_codes:
                if isinstance(service_codes, str):
                    service_code_q = Q(big_parcel__service__code=service_codes.strip())
                elif isinstance(service_codes, list) and service_codes:
                    service_code_q = Q(big_parcel__service__code=service_codes[0])

            # 处理 ship_time 参数（时间区间查询）
            if start_time or end_time:
                from datetime import datetime

                try:
                    if start_time and end_time:
                        # 两个时间都提供，进行区间查询
                        start_datetime = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                        end_datetime = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                        ship_time_q = Q(parcel_outbound_order__ship_time__gte=start_datetime,
                                        parcel_outbound_order__ship_time__lte=end_datetime)
                    elif start_time:
                        # 只提供开始时间，查询大于等于开始时间的记录
                        start_datetime = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                        ship_time_q = Q(parcel_outbound_order__ship_time__gte=start_datetime)
                    elif end_time:
                        # 只提供结束时间，查询小于等于结束时间的记录
                        end_datetime = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                        ship_time_q = Q(parcel_outbound_order__ship_time__lte=end_datetime)
                except ValueError as e:
                    return fail_response(request, f'时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式：{str(e)}')

            # 使用并查询逻辑组合条件
            q_objects = Q()
            conditions = [parcel_num_q, outbound_order_q, product_code_q, service_code_q, ship_time_q]
            active_conditions = [cond for cond in conditions if cond]

            if active_conditions:
                q_objects = active_conditions[0]
                for cond in active_conditions[1:]:
                    q_objects &= cond

            # 如果没有查询条件，返回错误
            if not q_objects:
                return fail_response(request,
                                     '请提供 parcel_num、parcel_outbound_order、product_code、service_code 或 start_time/end_time 查询参数')

            # 查询 BigParcel 模型，计算体积
            queryset = BigParcel.objects.filter(q_objects, del_flag=False).annotate(
                # 计算体积：长 × 宽 × 高
                calculated_volume=Case(
                    When(
                        parcel_length__isnull=False,
                        parcel_width__isnull=False,
                        parcel_height__isnull=False,
                        then=F('parcel_length') * F('parcel_width') * F('parcel_height')
                    ),
                    default=Value(0),
                    output_field=DecimalField(max_digits=15, decimal_places=6)
                )
            ).select_related('parcel_outbound_order', 'product').prefetch_related('big_parcel')

            # 格式化返回数据
            result_data = []
            for big_parcel in queryset:
                # 获取关联的服务信息（通过ParcelCustomerOrder）
                service_codes = []
                service_names = []
                parcel_customer_orders = big_parcel.big_parcel.filter(del_flag=False)
                for pco in parcel_customer_orders:
                    if pco.service:
                        if pco.service.code and pco.service.code not in service_codes:
                            service_codes.append(pco.service.code)
                        if pco.service.name and pco.service.name not in service_names:
                            service_names.append(pco.service.name)

                result_data.append({
                    'parcel_num': big_parcel.parcel_num,
                    'parcel_outbound_order': big_parcel.parcel_outbound_order.outbound_num if big_parcel.parcel_outbound_order else None,
                    'parcel_qty': big_parcel.parcel_qty,
                    'product_code': big_parcel.product.code if big_parcel.product else None,
                    'product_name': big_parcel.product.name if big_parcel.product else None,
                    'audit_weight': big_parcel.audit_weight,
                    'calculated_volume': big_parcel.calculated_volume
                    # 'parcel_length': big_parcel.parcel_length,
                    # 'parcel_width': big_parcel.parcel_width,
                    # 'parcel_height': big_parcel.parcel_height,
                    # 'service_codes': service_codes,
                    # 'service_names': service_names,
                    # 'ship_time': big_parcel.parcel_outbound_order.ship_time if big_parcel.parcel_outbound_order else None
                })

            return success_response_common(data=result_data)

        except Exception as e:
            logger.error(f'查询大包单信息失败: {str(e)}')
            return fail_response(request, f'查询失败: {str(e)}')

    @action(detail=False, methods=['POST'])
    def complete_the_cabin_allocation(self, request):
        """
        完成配舱操作 - 将大包单绑定到空运主单

        请求参数:
        - parcel_num: 大包单号列表，形如：[parcel_num1, parcel_num2, parcel_num3,...]
        - master_order_id: 空运主单ID，单个ID值

        处理逻辑:
        将接收到的大包单号与空运主单ID进行绑定操作
        注意：只绑定未关联出货单的大包单记录，避免绑定已关联出货单的重复记录
        """
        try:
            # 获取请求参数
            parcel_nums = request.data.get('parcel_num', [])
            master_order_id = request.data.get('master_order_id')

            # 参数验证
            if not parcel_nums or not isinstance(parcel_nums, list):
                return fail_response(request, '大包单号列表 parcel_num 不能为空')

            if not master_order_id:
                return fail_response(request, '空运主单ID master_order_id 不能为空')

            # 查询有效的大包单（只查询未关联出货单的记录）
            big_parcels = BigParcel.objects.filter(
                parcel_num__in=parcel_nums,
                del_flag=False,
                parcel_outbound_order__isnull=True  # 只绑定未关联出货单的大包单
            )

            # 查询有效的空运主单
            try:
                master_order = MasterOrder.objects.get(
                    id=master_order_id,
                    del_flag=False
                )
            except MasterOrder.DoesNotExist:
                return fail_response(request, f'空运主单ID {master_order_id} 不存在或已删除')

            # 执行配舱绑定操作
            with transaction.atomic():
                current_time = datetime.now()

                # 配舱策略：将所有大包单绑定到指定的空运主单
                for big_parcel in big_parcels:
                    # 更新大包单的空运主单绑定
                    big_parcel.master_order = master_order
                    big_parcel.master_order_time = current_time
                    big_parcel.update_by = request.user
                    big_parcel.update_date = current_time
                    big_parcel.save()

                # 配舱成功后将空运主单状态改为封单
                master_order.seal_status = 'SEALED'
                master_order.update_by = request.user
                master_order.update_date = current_time
                master_order.save()

            return success_response_common(msg='配舱操作成功')

        except Exception as e:
            logger.error(f'配舱操作失败: {str(e)}')
            return fail_response(request, f'配舱操作失败: {str(e)}')

    @action(detail=False, methods=['POST'])
    def quick_complete_the_cabin_allocation(self, request):
        """
        快速配舱操作 - 将大包单绑定到空运主单或删除绑定（支持部分绑定）

        请求参数:
        - parcel_num: 大包单号，英文逗号分隔（与parcel_outbound_order二选一）
        - parcel_outbound_order: 出货单号，英文逗号分隔（与parcel_num二选一）
        - master_order_id: 空运主单ID，单个ID值
        - isDel: 是否删除绑定，默认False（绑定操作），True（删除绑定操作）

        处理逻辑:
        1. 如果传递大包单号，直接查询大包单
        2. 如果传递出货单号，通过出货单查询关联的大包单
        3. 找到几个就处理几个，不会因为部分不存在而报错
        4. 根据isDel参数决定是绑定还是删除绑定
        5. 无论通过哪种方式，最终都只绑定/解绑大包单到空运主单
        """
        try:
            # 获取请求参数
            parcel_nums = request.data.get('parcel_num', '')
            parcel_outbound_orders = request.data.get('parcel_outbound_order', '')
            master_order_id = request.data.get('master_order_id')
            isDel = request.data.get('isDel', False)

            # 参数验证
            if not master_order_id:
                return fail_response(request, '空运主单号不能为空')

            if not parcel_nums and not parcel_outbound_orders:
                return fail_response(request, '请提供大包单号或出货单号')

            if parcel_nums and parcel_outbound_orders:
                return fail_response(request, '大包单号和出货单号不能同时提供')

            # 查询有效的空运主单
            try:
                master_order = MasterOrder.objects.get(
                    id=master_order_id,
                    del_flag=False
                )
            except MasterOrder.DoesNotExist:
                return fail_response(request, f'空运主单ID {master_order_id} 不存在或已删除')

            big_parcels_to_bind = []

            # 处理大包单号参数
            if parcel_nums:
                # 解析逗号分隔的大包单号
                parcel_num_list = [num.strip() for num in parcel_nums.split(',') if num.strip()]
                if not parcel_num_list:
                    return fail_response(request, '大包单号列表不能为空')

                # 查询存在的大包单
                big_parcels = BigParcel.objects.filter(
                    parcel_num__in=parcel_num_list,
                    del_flag=False
                )
                big_parcels_to_bind = list(big_parcels)

            # 处理出货单号参数
            elif parcel_outbound_orders:
                # 解析逗号分隔的出货单号
                outbound_order_list = [order.strip() for order in parcel_outbound_orders.split(',') if order.strip()]
                if not outbound_order_list:
                    return fail_response(request, '出货单号列表不能为空')

                # 通过出货单号查询关联的大包单
                big_parcels = BigParcel.objects.filter(
                    parcel_outbound_order__outbound_num__in=outbound_order_list,
                    del_flag=False
                )
                big_parcels_to_bind = list(big_parcels)

            # 检查是否找到大包单
            if not big_parcels_to_bind:
                return fail_response(request, '未找到任何有效的大包单')

            # 执行快速配舱操作（绑定或删除绑定）
            with transaction.atomic():
                current_time = datetime.now()

                if isDel:
                    # 删除绑定操作 - 只处理大包单解绑
                    for big_parcel in big_parcels_to_bind:
                        big_parcel.master_order = None
                        big_parcel.master_order_time = None
                        big_parcel.update_by = request.user
                        big_parcel.update_date = current_time
                        big_parcel.save()
                    
                    operation_msg = '删除绑定操作成功'
                else:
                    # 绑定操作 - 只处理大包单绑定
                    for big_parcel in big_parcels_to_bind:
                        big_parcel.master_order = master_order
                        big_parcel.master_order_time = current_time
                        big_parcel.update_by = request.user
                        big_parcel.update_date = current_time
                        big_parcel.save()

                    # 配舱成功后将空运主单状态改为封单
                    master_order.seal_status = 'SEALED'
                    master_order.update_by = request.user
                    master_order.update_date = current_time
                    master_order.save()
                    
                    operation_msg = '快速配舱操作成功'

            return success_response_common(msg=operation_msg)

        except Exception as e:
            operation_type = '删除绑定' if request.data.get('isDel', False) else '快速配舱'
            logger.error(f'{operation_type}操作失败: {str(e)}')
            return fail_response(request, f'{operation_type}操作失败: {str(e)}')

    @action(detail=False, methods=['POST'])
    def change_seal_status(self, request):
        """
        解封或者封单操作 - 批量修改空运主单的封单状态

        请求参数:
        - master_order_id: 空运主单ID数组，形如：[1, 2, 3, 4, ...]
        - seal_status: 封单状态，UNSEALED（解封）或 SEALED（封单）

        处理逻辑:
        根据提供的空运主单ID列表，将对应主单的seal_status状态设置为指定值
        """
        try:
            # 获取请求参数
            master_order_ids = request.data.get('master_order_id', [])
            seal_status = request.data.get('seal_status', '')

            # 参数验证
            if not master_order_ids or not isinstance(master_order_ids, list):
                return fail_response(request, '空运主单ID列表 master_order_id 不能为空且必须为列表格式')

            if not seal_status:
                return fail_response(request, '封单状态 seal_status 不能为空')

            # 验证封单状态值
            valid_statuses = ['UNSEALED', 'SEALED']
            if seal_status not in valid_statuses:
                return fail_response(request, f'封单状态必须为 {valid_statuses} 中的一个')

            if seal_status == "SEALED":
                order_status = "SCF" #封单后状态更新为配舱确认
            else:
                order_status = "LOA" #解封后空运主单状态更新为配舱中

            # 查询有效的空运主单
            master_orders = MasterOrder.objects.filter(
                id__in=master_order_ids,
                del_flag=False
            )

            found_count = master_orders.count()
            if found_count == 0:
                return fail_response(request, '未找到任何有效的空运主单')

            # 执行批量状态更新操作
            with transaction.atomic():
                current_time = datetime.now()

                # 批量更新空运主单的封单状态
                updated_count = master_orders.update(
                    order_status=order_status,
                    seal_status=seal_status,
                    update_by=request.user,
                    update_date=current_time
                )

            # 根据操作类型返回不同的消息
            operation_name = '解封' if seal_status == 'UNSEALED' else '封单'

            if found_count == len(master_order_ids):
                message = f'{operation_name}操作成功，共处理 {updated_count} 个空运主单'
            else:
                missing_count = len(master_order_ids) - found_count
                message = f'{operation_name}操作完成，成功处理 {updated_count} 个，{missing_count} 个主单不存在或已删除'

            return success_response_common(msg=message)

        except Exception as e:
            logger.error(f'封单状态修改失败: {str(e)}')
            return fail_response(request, f'封单状态修改失败: {str(e)}')

    @action(detail=False, methods=['POST'])
    def clear_the_cabin_allocation(self, request):
        """
        清空配舱操作 - 清空空运主单与大包单的绑定关系（仅限解封状态）

        请求参数:
        - master_order_id: 空运主单ID数组，形如：[1, 2, 3, 4, ...]

        处理逻辑:
        根据提供的空运主单ID列表，将绑定到这些空运主单的大包单解除绑定关系
        注意：只有解封状态(seal_status='UNSEALED')的空运主单才可以执行清空操作
        """
        try:
            # 获取请求参数
            master_order_ids = request.data.get('master_order_id', [])

            # 参数验证
            if not master_order_ids or not isinstance(master_order_ids, list):
                return fail_response(request, '空运主单ID列表 master_order_id 不能为空且必须为列表格式')

            # 查询有效且解封状态的空运主单
            master_orders = MasterOrder.objects.filter(
                id__in=master_order_ids,
                del_flag=False,
                seal_status='UNSEALED'  # 只有解封状态的才可以操作清空
            )

            # 查询所有指定的空运主单（用于检查哪些不符合条件）
            all_master_orders = MasterOrder.objects.filter(
                id__in=master_order_ids,
                del_flag=False
            )

            found_count = master_orders.count()
            all_found_count = all_master_orders.count()

            if found_count == 0:
                if all_found_count > 0:
                    return fail_response(request, '未找到任何解封状态的空运主单，只有解封状态的主单才能执行清空配舱操作')
                else:
                    return fail_response(request, '未找到任何有效的空运主单')

            # 执行清空配舱操作
            with transaction.atomic():
                current_time = datetime.now()

                # 查询绑定到这些空运主单的大包单
                big_parcels_to_clear = BigParcel.objects.filter(
                    master_order__in=master_orders,
                    del_flag=False
                )

                cleared_count = 0
                if big_parcels_to_clear.exists():
                    # 清空大包单与空运主单的绑定关系
                    cleared_count = big_parcels_to_clear.update(
                        master_order=None,
                        master_order_time=None,
                        update_by=request.user,
                        update_date=current_time
                    )

            # 构建返回消息
            sealed_count = all_found_count - found_count  # 已封单的数量
            missing_count = len(master_order_ids) - all_found_count  # 不存在或已删除的数量

            message_parts = [
                f'清空配舱操作完成，成功处理 {found_count} 个解封状态的空运主单，解除 {cleared_count} 个大包单绑定']

            if sealed_count > 0:
                message_parts.append(f'{sealed_count} 个主单为封单状态无法清空')

            if missing_count > 0:
                message_parts.append(f'{missing_count} 个主单不存在或已删除')

            message = '，'.join(message_parts)

            return success_response_common(msg=message)

        except Exception as e:
            logger.error(f'清空配舱操作失败: {str(e)}')
            return fail_response(request, f'清空配舱操作失败: {str(e)}')

    @action(detail=False, methods=['POST'])
    def search_completed_cabin_allocation(self, request):
        """
        查询所有已配舱的数据
        根据空运主单ID查询出绑定关系，返回关联的大包单数据
        支持在结果中按大包单号和出库单号进行过滤搜索

        请求参数:
        - master_order_id: 空运主单ID（必传）
        - big_parcel_num: 大包单号（可选，支持逗号分隔的多值查询）
        - parcel_outbound_order: 出库单号（可选，支持逗号分隔的多值查询）
        - start_time: 配舱开始时间（可选，格式：YYYY-MM-DD HH:MM:SS）
        - end_time: 配舱结束时间（可选，格式：YYYY-MM-DD HH:MM:SS）
        """
        try:
            # 获取请求参数
            master_order_id = request.data.get('master_order_id')
            big_parcel_nums = request.data.get('parcel_num', '')
            parcel_outbound_orders = request.data.get('parcel_outbound_order', '')
            start_time = request.data.get('start_time', '')
            end_time = request.data.get('end_time', '')

            # 参数验证
            if not master_order_id:
                return fail_response(request, '空运主单ID master_order_id 不能为空')

            # 查询有效的空运主单
            try:
                master_order = MasterOrder.objects.get(
                    id=master_order_id,
                    del_flag=False
                )
            except MasterOrder.DoesNotExist:
                return fail_response(request, f'空运主单ID {master_order_id} 不存在或已删除')

            # 查询该空运主单关联的所有大包单（已配舱的数据）
            queryset = BigParcel.objects.filter(
                master_order=master_order,
                del_flag=False
            ).annotate(
                # 计算体积：长 × 宽 × 高
                calculated_volume=Case(
                    When(
                        parcel_length__isnull=False,
                        parcel_width__isnull=False,
                        parcel_height__isnull=False,
                        then=F('parcel_length') * F('parcel_width') * F('parcel_height')
                    ),
                    default=Value(0),
                    output_field=DecimalField(max_digits=15, decimal_places=6)
                )
            ).select_related('parcel_outbound_order', 'product').prefetch_related('big_parcel')

            # 在结果中支持按大包单号进行过滤搜索
            if big_parcel_nums:
                if isinstance(big_parcel_nums, str):
                    # 检查是否包含逗号，支持多值查询
                    if ',' in big_parcel_nums:
                        big_parcel_num_list = [num.strip() for num in big_parcel_nums.split(',') if num.strip()]
                        queryset = queryset.filter(parcel_num__in=big_parcel_num_list)
                    else:
                        queryset = queryset.filter(parcel_num=big_parcel_nums.strip())
                elif isinstance(big_parcel_nums, list):
                    queryset = queryset.filter(parcel_num__in=big_parcel_nums)

            # 在结果中支持按出库单号进行过滤搜索
            if parcel_outbound_orders:
                if isinstance(parcel_outbound_orders, str):
                    # 检查是否包含逗号，支持多值查询
                    if ',' in parcel_outbound_orders:
                        outbound_order_list = [order.strip() for order in parcel_outbound_orders.split(',') if
                                               order.strip()]
                        queryset = queryset.filter(parcel_outbound_order__outbound_num__in=outbound_order_list)
                    else:
                        queryset = queryset.filter(parcel_outbound_order__outbound_num=parcel_outbound_orders.strip())
                elif isinstance(parcel_outbound_orders, list):
                    queryset = queryset.filter(parcel_outbound_order__outbound_num__in=parcel_outbound_orders)

            # 在结果中支持按配舱时间进行过滤搜索
            if start_time or end_time:
                try:
                    if start_time and end_time:
                        # 两个时间都提供，进行区间查询
                        start_datetime = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                        end_datetime = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                        queryset = queryset.filter(master_order_time__gte=start_datetime,
                                                  master_order_time__lte=end_datetime)
                    elif start_time:
                        # 只提供开始时间，查询大于等于开始时间的记录
                        start_datetime = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                        queryset = queryset.filter(master_order_time__gte=start_datetime)
                    elif end_time:
                        # 只提供结束时间，查询小于等于结束时间的记录
                        end_datetime = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                        queryset = queryset.filter(master_order_time__lte=end_datetime)
                except ValueError as e:
                    return fail_response(request, f'时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式：{str(e)}')

            # 格式化返回数据，与 add_container_search 函数保持一致
            result_data = []
            audit_weight = 0
            calculated_volume = 0
            for big_parcel in queryset:
                # 获取关联的服务信息（通过ParcelCustomerOrder）
                service_codes = []
                service_names = []
                parcel_customer_orders = big_parcel.big_parcel.filter(del_flag=False)
                for pco in parcel_customer_orders:
                    if pco.service:
                        if pco.service.code and pco.service.code not in service_codes:
                            service_codes.append(pco.service.code)
                        if pco.service.name and pco.service.name not in service_names:
                            service_names.append(pco.service.name)

                result_data.append({
                    'parcel_num': big_parcel.parcel_num,
                    'parcel_outbound_order': big_parcel.parcel_outbound_order.outbound_num if big_parcel.parcel_outbound_order else None,
                    'parcel_qty': big_parcel.parcel_qty,
                    'product_code': big_parcel.product.code if big_parcel.product else None,
                    'product_name': big_parcel.product.name if big_parcel.product else None,
                    'audit_weight': big_parcel.audit_weight,
                    'calculated_volume': big_parcel.calculated_volume
                })
                audit_weight += big_parcel.audit_weight
                calculated_volume += big_parcel.calculated_volume

            return success_response_common(data={
                'results': result_data,
                'count': len(result_data),
                'audit_weight': audit_weight,
                'calculated_volume': calculated_volume
            })

        except Exception as e:
            logger.error(f'查询已配舱数据失败: {str(e)}')
            return fail_response(request, f'查询已配舱数据失败: {str(e)}')

    @action(detail=False, methods=['POST'])
    def search_making_cargo_plan(self, request):
        """
        查询所有配舱的数据
        根据空运主单ID查询出绑定关系，返回关联的大包单数据

        请求参数:
        - master_order_id: 空运主单ID（必传）
        """
        try:
            # 获取请求参数
            master_order_id = request.data.get('master_order_id')

            # 参数验证
            if not master_order_id:
                return fail_response(request, '空运主单号不能为空')

            # 查询有效的空运主单
            try:
                master_order = MasterOrder.objects.get(
                    id=master_order_id,
                    del_flag=False
                )
            except MasterOrder.DoesNotExist:
                return fail_response(request, f'空运主单好{master_order_id}不存在')

            # 查询该空运主单关联的所有大包单（已配舱的数据）
            queryset = BigParcel.objects.filter(
                master_order=master_order,
                del_flag=False
            ).select_related('parcel_outbound_order', 'product').prefetch_related('big_parcel')

            # 格式化返回数据,返回的数据字段是大包号码，小包数量，大包重量，大包尺寸，创建仓库，创建时间，创建人
            result_data = []
            for big_parcel in queryset:
                result_data.append({
                    'parcel_num': big_parcel.parcel_num,
                    'parcel_qty': big_parcel.parcel_qty,
                    'parcel_weight': big_parcel.parcel_weight or 0,
                    'parcel_size': f'{big_parcel.parcel_length or 0}*{big_parcel.parcel_width or 0 }*{big_parcel.parcel_height or 0}',
                    'create_warehouse': big_parcel.address,
                    'create_time': big_parcel.create_date,
                    'create_user': big_parcel.user_desc
                })

            return success_response_common(data=result_data)
        except Exception as e:
            logger.error(f'查询配舱数据失败: {str(e)}')
            return fail_response(request, f'查询配舱数据失败: {str(e)}')




    @action(detail=False, methods=['POST'])
    def remove_cabin_allocation(self, request):
        """
        删除配舱绑定关系

        请求参数:
        - master_order_id: 空运主单ID（必传，单个ID值）
        - parcel_num: 大包单号列表（必传，指定要删除绑定关系的大包单号列表）
        """
        try:
            # 获取请求参数
            master_order_id = request.data.get('master_order_id')
            parcel_nums = request.data.get('parcel_num', [])

            # 参数验证
            if not master_order_id:
                return fail_response(request, '空运主单ID master_order_id 不能为空')

            if not parcel_nums or not isinstance(parcel_nums, list):
                return fail_response(request, '大包单号列表 parcel_num 不能为空且必须为列表格式')

            # 查询有效的空运主单
            try:
                master_order = MasterOrder.objects.get(
                    id=master_order_id,
                    del_flag=False
                )
            except MasterOrder.DoesNotExist:
                return fail_response(request, f'空运主单ID {master_order_id} 不存在或已删除')

            # 查询指定的大包单，并验证它们是否绑定到该空运主单
            big_parcels = BigParcel.objects.filter(
                parcel_num__in=parcel_nums,
                master_order=master_order,
                del_flag=False
            )

            # 获取实际找到的大包单号
            found_parcel_nums = set(big_parcels.values_list('parcel_num', flat=True))
            missing_parcel_nums = set(parcel_nums) - found_parcel_nums

            # 检查是否有需要删除的绑定关系
            if not big_parcels.exists():
                if missing_parcel_nums:
                    return fail_response(request, f'以下大包单未绑定到该空运主单或不存在: {", ".join(missing_parcel_nums)}')
                else:
                    return fail_response(request, '未找到需要删除绑定关系的大包单')

            # 执行删除操作
            with transaction.atomic():
                current_time = datetime.now()

                cleared_count = big_parcels.update(
                    master_order=None,
                    master_order_time=None,
                    update_by=request.user,
                    update_date=current_time
                )

                # 检查该空运主单是否还有其他绑定的大包单
                remaining_parcels = BigParcel.objects.filter(
                    master_order=master_order, 
                    del_flag=False
                ).exists()

                # 如果所有绑定关系都被删除，将空运主单状态改为未封单
                if not remaining_parcels:
                    master_order.seal_status = 'UNSEALED'

                master_order.update_by = request.user
                master_order.update_date = current_time
                master_order.save()

            # 构建返回消息
            success_message = f'删除配舱绑定成功，删除了 {cleared_count} 个大包单绑定'
            
            if missing_parcel_nums:
                warning_message = f'，以下大包单未绑定到该空运主单: {", ".join(missing_parcel_nums)}'
                success_message += warning_message

            return success_response_common(msg=success_message, data={
                'cleared_count': cleared_count,
                'successfully_removed': list(found_parcel_nums),
                'not_found_or_not_bound': list(missing_parcel_nums)
            })

        except Exception as e:
            logger.error(f'删除配舱绑定失败: {str(e)}')
            return fail_response(request, f'删除配舱绑定失败: {str(e)}')


    @action(methods=['POST'], detail=False)
    def upload_attachment(self, request):
        """
        上传主单附件，限制最多10个
        入参: master_order_id, file(支持多文件), name(可选)
        """
        master_order_id = request.data.get('master_order_id')
        files = request.FILES.getlist('file')
        name = request.data.get('name')

        if not master_order_id:
            return Response({'msg': '主单ID必填', 'code': 400}, status=status.HTTP_400_BAD_REQUEST)
        try:
            master_order = MasterOrder.objects.get(id=master_order_id, del_flag=False)
        except MasterOrder.DoesNotExist:
            return Response({'msg': '主单不存在', 'code': 404}, status=status.HTTP_404_NOT_FOUND)

        if not files:
            return Response({'msg': '用户未上传附件', 'code': 200}, status=status.HTTP_200_OK)

        # 限制最多10个附件
        MAX_FILES = 10
        if len(files) > MAX_FILES:
            return Response(
                {'msg': f'一次最多只能上传{MAX_FILES}个附件', 'code': 400},
                status=status.HTTP_400_BAD_REQUEST
            )

        attachments = []
        for f in files:
            attachment = MasterAttachment.objects.create(
                masterOrder=master_order,
                url=f,
                name=name or f.name,
                **get_update_params(request, True)
            )
            attachments.append({
                'id': attachment.id,
                'name': attachment.name,
                'url': attachment.url.url if attachment.url else '',
            })
        return Response({'msg': '上传成功', 'code': 200, 'data': attachments}, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def delete_attachment(self, request):
        """
        删除主单附件
        入参: attachment_id
        """
        attachment_id = request.data.get('attachment_id')
        user = request.user
        if not attachment_id:
            return Response({'msg': '附件ID必填', 'code': 400}, status=status.HTTP_400_BAD_REQUEST)
        try:
            attachment = MasterAttachment.objects.get(id=attachment_id)
            if attachment.url:
                attachment.url.delete(save=False)
            attachment.del_flag = True
            attachment.update_by = user
            attachment.update_date = datetime.now()
            attachment.save()
            return Response({'msg': '删除成功', 'code': 200}, status=status.HTTP_200_OK)
        except MasterAttachment.DoesNotExist:
            return Response({'msg': '附件不存在', 'code': 404}, status=status.HTTP_404_NOT_FOUND)

    @action(methods=['GET'], detail=False)
    def download_attachment(self, request):
        """
        下载主单附件
        入参: attachment_id
        """
        attachment_id = request.query_params.get('attachment_id')
        if not attachment_id:
            return Response({'msg': '附件ID必填', 'code': 400}, status=status.HTTP_400_BAD_REQUEST)
        try:
            attachment = MasterAttachment.objects.get(id=attachment_id)
            if not attachment.url:
                return Response({'msg': '附件文件不存在', 'code': 404}, status=status.HTTP_404_NOT_FOUND)
            response = FileResponse(attachment.url.open('rb'), as_attachment=True,
                                    filename=attachment.name or attachment.url.name)
            return response
        except MasterAttachment.DoesNotExist:
            return Response({'msg': '附件不存在', 'code': 404}, status=status.HTTP_404_NOT_FOUND)


    @action(methods=['POST'],detail=False)
    def print_warehouse_entry_form(self, request):
        """
        # 打印空运主单入仓单
        :param request:
        :return:
        """
        ids = request.data['ids']
        fields = [
            'id',
            'warehouse_receipt_number',
            'ref_num',
            'order_num',
            'carrier_code',
            'airline_num',
            'departure',
            'plan_leave_date',
            'destination',
            'plan_arrivals_date',
            'customer_carton',
            'customer_weight',
            'customer_volume',
        ]
        master_order_list = list(MasterOrder.objects.filter(id__in=ids, del_flag=False).values(*fields))
        orders = [assemble_barcode_params_for_master_order(order) for order in master_order_list]

        request.data['base64'] = create_barcode_for_master_order(orders)
        request.data['data'] = orders
        request.data['msg'] = '打印成功！'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def restowages(self, request):
        """
        重新配舱接口
        要求传入的主单id对应的order_status必须都是LOC，如果有不是LOC的就提示报错信息，然后将状态更新为LOA，并清空配舱时间
        重新配舱逻辑：
        1、将原本已配舱的状态置为配舱中
        2、清空原本的配舱完结时间。
        """
        data = request.data
        ids = data.get('ids')

        if not ids or not isinstance(ids, list):
            return fail_response(request, '参数ids不能为空且必须为列表')

        # 查询这些id的主单
        master_orders = MasterOrder.objects.filter(id__in=ids, del_flag=False)

        if master_orders.count() != len(ids):
            return fail_response(request, '部分主单不存在，请检查ids')

        # 检查所有order_status是否为'LOC'
        non_loc_orders = master_orders.exclude(order_status='LOC')
        if non_loc_orders.exists():
            non_loc_nums = ', '.join(non_loc_orders.values_list('order_num', flat=True))
            return fail_response(request, f'以下主单状态非已配舱状态，不允许重新配舱: {non_loc_nums}')

        # 所有都是'LOC'，更新为'LOA'，并清空配舱时间
        master_orders.update(order_status='LOA', stowage_plan_time=None, update_by=request.user, update_date=datetime.now())

        response_data = {
            'code': 200,
            'msg': '重新配舱成功！'
        }
        return Response(response_data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def search_total(self, request):
        """
        主单管理POST查询接口，支持多条件查询，统计每个order_status的数量。
        支持的搜索条件：order_num (支持逗号分隔批量)、airline_num、departure、destination、start_time、end_time、order_status、carrier_code。
        返回每个order_status的数量字典。
        """
        data = request.data
        queryset = MasterOrder.objects.filter(del_flag=False).order_by('-id')

        order_num = data.get('order_num')
        airline_num = data.get('airline_num')
        departure = data.get('departure')
        destination = data.get('destination')
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        order_status = data.get('order_status')
        carrier_code = data.get('carrier_code')

        # 单号批量查询，支持逗号分隔，最多100个
        if order_num:
            order_nums = [x.strip() for x in order_num.split(',') if x.strip()][:100]
            q = Q()
            for num in order_nums:
                q |= Q(order_num=num) | Q(ref_num=num) | Q(warehouse_receipt_number=num)
            queryset = queryset.filter(q)

        if airline_num:
            queryset = queryset.filter(airline_num=airline_num)
        if departure:
            queryset = queryset.filter(departure=departure)
        if destination:
            queryset = queryset.filter(destination=destination)
        if order_status:
            queryset = queryset.filter(order_status=order_status)
        if carrier_code:
            queryset = queryset.filter(carrier_code=carrier_code)
        if start_time and end_time:
            try:
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                queryset = queryset.filter(create_date__gte=start_time, create_date__lte=end_time)
            except ValueError:
                return fail_response(request, '时间格式错误，需为YYYY-MM-DD HH:MM:SS')

        # 统计每个order_status的数量
        status_counts = queryset.values('order_status').annotate(count=Count('order_status')).order_by('order_status')

        # 转换为字典格式，例如 {'DR': 1, 'SM': 2, ...}
        res = {item['order_status']: item['count'] for item in status_counts}

        # 如果某些状态没有记录，可以可选地填充0（根据需求）
        all_statuses = [s[0] for s in MasterOrder.ORDER_STATUS]
        for status_code in all_statuses:
            if status_code not in res:
                res[status_code] = 0

        response_data = {
            'code': 200,
            'msg': '获取空运主单对应状态数量成功！',
            'data': res
        }
        return Response(response_data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def revoke_cargo_release(self, request):
        """
        撤销放舱接口
        入参ids（主单ID列表），撤销后将order_status RCS更新为DR，放舱时间(cargo_release_time)清空
        """
        data = request.data
        ids = data.get('ids')

        if not ids or not isinstance(ids, list):
            return fail_response(request, '参数ids不能为空且必须为列表')

        # 查询这些id的主单
        master_orders = MasterOrder.objects.filter(id__in=ids, del_flag=False)

        if master_orders.count() != len(ids):
            return fail_response(request, '部分主单不存在，请检查ids')

        # 检查所有order_status是否为'RCS' 已放舱
        non_rcs_orders = master_orders.exclude(order_status='RCS')
        if non_rcs_orders.exists():
            non_loc_nums = ', '.join(non_rcs_orders.values_list('order_num', flat=True))
            return fail_response(request, f'以下主单状态非已放舱状态，不允许撤销放舱: {non_loc_nums}')

        # 更新状态为'DR'并清空放舱时间
        master_orders.update(order_status='DR', cargo_release_time=None, update_by=request.user, update_date=datetime.now())

        response_data = {
            'code': 200,
            'msg': '撤销放舱成功！'
        }
        return Response(response_data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def completed(self, request):
        """
        完结接口
        入参ids（主单ID列表）
        封单=配舱确认 in 配舱中， 封单以后才能完结 完结了就去已配舱了
        """
        data = request.data
        ids = data.get('ids')

        if not ids or not isinstance(ids, list):
            return fail_response(request, '参数ids不能为空且必须为列表')

        # 查询这些id的主单
        master_orders = MasterOrder.objects.filter(id__in=ids, del_flag=False)

        if master_orders.count() != len(ids):
            return fail_response(request, '部分主单不存在，请检查ids')
        #需要先检查下，是否满足封单状态
        non_scf_orders = master_orders.exclude(order_status='SCF')
        if non_scf_orders.exists():
            non_loc_nums = ', '.join(non_scf_orders.values_list('order_num', flat=True))
            return fail_response(request, f'以下主单状态非封单状态，不允许完结: {non_loc_nums}')

        # 更新状态为已配舱，并写入配舱时间
        master_orders.update(order_status='LOC', stowage_plan_time=datetime.now(), update_by=request.user, update_date=datetime.now())

        response_data = {
            'code': 200,
            'msg': '已完结！'
        }
        return Response(response_data, status=status.HTTP_200_OK)
