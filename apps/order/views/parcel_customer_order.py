import re
import base64
import copy
import json
import logging
import traceback
from django.utils.translation import gettext_lazy as _

import django_filters
import requests
from django.forms import model_to_dict
from django.views.decorators.csrf import csrf_exempt

from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from pydash import get
import os
import string
from datetime import datetime, timedelta
import random
from decimal import Decimal, InvalidOperation
from io import BytesIO

import xlrd
from PyPDF2 import PdfFileMerger
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db import transaction
from django.db.models import Sum, Q, F, Prefetch, Subquery, Count
from django.http import HttpResponse, FileResponse, StreamingHttpResponse, Http404
from django.shortcuts import get_object_or_404

from django_filters.rest_framework import DjangoFilterBackend
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font
from rest_framework.permissions import IsAuthenticated

from account.views.account import deduction_account
from cms.serializers.parcel_customer_order import CMSParcelCustomerOrderAndDetailSerializer
from common.error import ParamError, ErrorCode
from common.excel_tools import set_data
from common.requestLock import lock_request, lock_request_common, lock_request2
from common.service.account_service import refund_account
from common.service.parcel_order import handler_cancel_label, handler_confirm_ship, \
    handler_product_limit_user, assemble_barcode_params, upload_parcel_customer_order, add_label_task_by_product_type, \
    assemble_delivery_certificate_params, handler_force_cancel_order, assemble_order_extends, \
    assemble_recipient_info, assemble_address_info, assemble_shipper_info, \
    get_product_line_and_service, check_order_data_by_product, check_order_data_by_service, \
    set_order_data_default_value, handler_return_oversea_address_info, handler_shipper_info, handler_scan_form_v2, \
    fail_orders, get_label_deal, review_order, create_cancel_order_task_with_orders

from common.tools import judge_is_vender_no_for_tracking_num, set_multistep_parcel_track
from common.utils.generate_zx_label_func import generate_zx_label_for_api_label

from common.service.pms_service import handler_virtual_calc, handler_virtual_calc_multi_channel, add_revenue_v2, \
    add_cost_v2
from common.service.pms_zone import get_product_calc_zone, get_service_calc_zone, get_service_unreachable_zone, \
    get_product_unreachable_zone
from common.string import calculate_mod36_checksum
from common.upload_tools import sync_upload_file_common
from common.utils.arr_util import arr_to_str
from common.utils.barcode_gen import create_barcodes_for_order, create_shipping_label, \
    batch_create_barcodes_for_big_parcelorder, create_label_general_view, create_tail_journey_waybill, \
    create_shipment_proof, create_proof_of_delivery
from common.utils.custom_json_response import JResponse
from common.utils.custom_pagination import LargeResultsSetPagination
from common.utils.custom_viewset_base import CustomViewBase
from common.utils.gen_mode_key_util import gen_mode_key_by_shunt, gen_mode_key2
from common.utils.object import upper_first
from common.utils.product import get_sub_product_list
from common.utils.response import fail_response, success_response, do_response_common, do_response
from common.utils.logger_util import StreamEnum, openobserve_client
from company.models import Address, SupplierButt, SupplierButtAccount, Company
from alita.logger import logger
from info.models import Charge, TrackCode, ServiceClass
from order.integration.amazonShippingOneClickService import AmazonShippingOneClickService
from order.integration.integrationInterface import LabelOrderVo, update_order
from order.integration.util.abstract_func import create_order_label_task, add_order_label_task_by_service
from order.integration.util import amazon_shipping_util
from order.models import ParcelCustomerOrder, ParcelOrderChargeIn, \
    ParcelOrderChargeOut, ParcelOrderParcel, ParcelOrderItem, ParcelOrderLabelTask, ParcelOrderLabel, BigParcel, \
    PickRecord, CustomsClearanceBigParcelOrder, CustomerOrder, Parcel, ParcelOrderAddress, \
    ParcelTrack, SyncWMSTasks, ParcelOutboundOrder, ParcelOrderExtend, OrderAPILog, ClientApiLog, OrderScanFormTask, \
    ParcelOrderLog
import time

from common.custom import RbacPermission, AlitaJSONWebTokenAuthentication
from rest_framework.filters import SearchFilter, OrderingFilter
from django_filters import rest_framework as filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status

from django.conf import settings
from common.order_num_gen_rule import create_order_num, generate_scan_form_bath_number
from common.tools import get_multi_search, order_revenue_confirm, order_cost_confirm, \
    get_update_params, set_parcel_track, is_lock_order, \
    params_query, change_parcel_order_status
from common.order_num_gen_rule import is_parcel_customer_order
from info.models import Dict
from order.serializers.parcel_customer_order import ParcelCustomerOrderSerializer, \
    ParcelCustomerOrderAndDetailSerializer, handler_create_order, add_revenue, add_cost, \
    ParcelCustomerOrderDetailSerializer, ParcelCustomerOrderListSerializer, ParcelOrderLabelTaskListSerializer, \
    SyncWMSTasksSerializer, LastMileAPILogSerializer, ClientApiLogSerializer, ParcelCustomerOrderTrajectorySerializer, \
    APIParcelOrderAddressSerializer, ParcelCustomerOrderListSerializerV2, ParcelOrderSerializer, \
    ParcelOrderAddressSerializer, ParcelOrderItemSerializer, ParcelOrderLogSerializer
from order.serializers.track import ParcelTrackSerializer
from order.tasks import intercept_parcel_order_task, handler_create_parcel_label_task, set_multistep_parcel_track_task
from order.views.big_parcel import save_bulk_model_obj
from pms.models import Product, Service, ProductTrackCode, ProductCombination, ProductBasicRestriction, \
    ProductLimitUser, ProductZone, ProductRoute, AddressRule, ProductRouteDetail, LineSortingSetting, ChargingRule, \
    ServiceChargingRule, ProductLine, ServiceApiSetting, ServiceOrderNumRule
from pms.serializers.product import ProductTrackCodeSerializer, ProductBasicRestrictionSerializer
from pms.serializers.product_sorting import LineSortingSettingSerializer, SortingZoneSerializer
from pms.util.calc import OrderCalcVo, DistCalcVo
from pms.util.cost_calc import cost_calc
from pms.util.product_zone import get_zone_code_v2
from pms.util.revenue_calc import revenue_calc, revenue_calc_v2
from settle.models import AccountPayable, Debit, AccountReceivable
from django.core.cache import cache
from drf_spectacular.utils import extend_schema, extend_schema_view
from order.integration.postkeeperIntegrationService import PostkeeperIntegrationService
from order.integration.customerOrderIntegrationService import CustomerOrderIntegrationService
from order.integration.parcelCustomerOrderIntegrationService import ParcelCustomerOrderIntegrationService
from order.integration.idealIntegrationService import IdealIntegrationService
from order.integration.deUpsIntegrationService import DeUpsIntegrationService
from order.integration.oblIntegrationService import OblIntegrationService
from order.integration.ideal2IntegrationService import Ideal2IntegrationService
from order.integration.ideal3IntegrationService import Ideal3IntegrationService
from order.integration.hsIntegrationService import HsIntegrationService
from order.integration.megaIntegrationService import MegaIntegrationService
from order.integration.kwtIntegrationService import KwtIntegrationService
from order.integration.px4IntegrationService import Px4IntegrationService
from order.integration.px4LmaIntegrationService import Px4LmaIntegrationService
from order.integration.hltIntegrationService import HltIntegrationService
from order.integration.anNengIntegrationService import AnNengIntegrationService
from order.integration.anNengSyncCreateIntegrationService import AnNengSyncCreateIntegrationService
from order.integration.hf2IntegrationService import Hf2IntegrationService
from order.integration.zsdIntegrationService import ZsdIntegrationService
from order.integration.jnIntegrationService import JnIntegrationService
from order.integration.yhIntegrationService import YhIntegrationService
from order.integration.ninjaVanIntegrationService import NinjaVanIntegrationService
from order.integration.gsIntegrationService import GsIntegrationService
from order.integration.lcsIntegrationService import LcsIntegrationService
from order.integration.thzIntegrationService import ThzIntegrationService
from order.integration.mapleIntegrationService import MapleIntegrationService
from order.integration.dhlIntegrationService import DhlIntegrationService
from order.integration.pospeipIntegrationService import PospeipIntegrationService
from order.integration.k5IntegrationService import K5IntegrationService
from order.integration.zrIntegrationService import ZrIntegrationService
from order.integration.hallIntegrationService import HallIntegrationService
from order.integration.shopLineIntegrationService import ShopLineIntegrationService
from order.integration.dhlShippingIntegrationService import DhlShippingIntegrationService
from order.integration.dhlIntlShippingIntegrationService import DhlIntlShippingIntegrationService
from order.integration.dhlIntlEconomyIntegrationService import DhlIntlEconomyIntegrationService
from order.integration.shipHuBxIntegrationService import ShipHuBxIntegrationService
from order.integration.jaIntegrationService import JaIntegrationService
from order.integration.jdIntegrationService import JdIntegrationService
from order.integration.shipNitroIntegrationService import ShipNitroIntegrationService
from order.integration.hlIntegrationService import HlIntegrationService
from order.integration.emsZjIntegrationService import EmsZjIntegrationService
from order.integration.didaIntegrationService import DidaIntegrationService
from order.integration.olwIntegrationService import OlwIntegrationService
from order.integration.shaoKeIntegrationService import ShaoKeIntegrationService
from order.integration.dpexIntegrationService import DpexIntegrationService
from order.integration.spxIntegrationService import SpxIntegrationService
from order.integration.htyIntegrationService import HtyIntegrationService
from order.integration.ycIntegrationService import YcIntegrationService
from order.integration.cainiaoCdPacketIntegrationService import CainiaoCdPacketIntegrationService
from order.integration.cainiaoFullPacketIntegrationService import CainiaoFullPacketIntegrationService
from order.integration.hyIntegrationService import HyIntegrationService
from order.integration.gweIntegrationService import GweIntegrationService
from order.integration.gwe2IntegrationServeice import Gwe2IntegrationService
from order.integration.hermesIntegrationService import HermesIntegrationService
from order.integration.octopusIntegrationService import OctopusIntegrationService
from order.integration.ibilabIntegrationService import IbilabIntegrationService
from order.integration.yldFedexIntegrationService import YldFedexIntegrationService
from order.integration.yldUPSIntegrationService import YldUPSIntegrationService
from order.integration.ruiDianIntegrationService import RuiDianIntegrationService
from order.integration.cainiaoOverseaIntegrationService import CainiaoOverseaIntegrationService
from order.integration.dgdIntegrationService import DgdIntegrationService
from order.integration.ddIntegrationService import DdIntegrationService
from order.integration.shipberIntegrationService import ShipberIntegrationService
from order.integration.saichengIntegrationService import SaichengIntegrationService
from order.integration.maegmantIntegrationService import MaegmantIntegrationService
from order.integration.multicourierIntegrationService import MulticourierIntegrationService
from order.integration.jerryIntegrationService import JerryIntegrationService
from order.integration.jyIntegrationService import JyIntegrationService
from order.integration.gpsIntegrationService import GPSIntegrationService
from order.integration.maerskIntegrationService import MaerskIntegrationService
from order.integration.haiTunIntegrationService import HaiTunIntegrationService
from order.integration.yzIntegrationService import YzIntegrationService
from order.integration.rlabelIntegrationService import RLabelIntegrationService
from order.integration.colisIntegrationService import ColisIntegrationService
from order.integration.winitLmaIntegrationService import WinitLmaIntegrationService
from order.integration.yicIntegrationService import YicIntegrationService
from order.integration.zxLabelIntegrationService import ZxLabelIntegrationService
from order.integration.worldTechIntegrationService import WorldTechIntegrationService
from order.integration.sengiIntegrationService import SengiIntegrationService
from order.integration.skyeShipIntegrationService import SkyeShipIntegrationService
from order.integration.feikeIntegrationService import FeiKeIntegrationService
from order.integration.earlybirdIntegrationService import EarlyBirdIntegrationService
from order.integration.pacticIntegrationService import PacticIntegrationService
from order.integration.yodelIntegrationService import YodelIntegrationService
from order.integration.scurriIntegrationService import ScurriIntegrationService
from order.integration.hanJinIntegrationService import HanJinIntegrationService
from order.integration.asendiaIntegrationService import AsendiaIntegrationService
from order.integration.tongDaDpdIntegrationService import TongDaDpdIntegrationService
from order.integration.dhlNlIntegrationService import DhlNlIntegrationService
from order.integration.boFengService import BoFengIntegrationService
from order.integration.royalMailIntegrationService import RoyalMailIntegrationService
from order.integration.yidaService import YiDaIntegrationService
from order.integration.eboIntegrationService import EboIntegrationService
from order.integration.jingDongSmallBagIntegrationService import JingDongSmallBagIntegrationService
from order.integration.evriIntegrationService import EvriIntegrationService
from order.integration.meiShangAnXinIntegrationService import MeiShangAnXinIntegrationService
from order.integration.sfService import SFIntegrationService
from order.integration.sfExportEcommerceService import SFExportEcommerceIntegrationService
from order.integration.anPostIntegrationService import AnPostIntegrationService
from order.integration.omsClientIntegrationService import OmsClientIntegrationService


from django.utils import timezone
from common.utils.file_util import upload_file, get_label_file_base64, get_label_file_url
from order.integration.integrationInterface import IntegrationInterface, get_order_label_obj, get_warehouse_code
from order.integration.specialAPI.integrate import call_special_api
from common.utils.fedex_address_validation import get_address_type_for_pricing

def create_outbound_num():
    """生成运单号（出货单号）"""
    now_date_str = datetime.now().strftime("%y%m%d")
    first_ = ParcelOutboundOrder.objects.filter(outbound_num__startswith=f'HGE{now_date_str}').order_by('-id').first()
    if first_ is not None:
        serial_number = str(int(first_.outbound_num[9:15]) + 1).zfill(6)
    else:
        serial_number = '000001'
    return f'HGE{now_date_str}{serial_number}' + calculate_mod36_checksum(f'HGE{now_date_str}{serial_number}')


class ParcelCustomerOrderFilter(filters.FilterSet):
    """自定义查询类"""
    # 创建时间
    create_date = filters.CharFilter(method='filter_create_date')
    # 应收计费时间
    parcel_customer_order_charge_in__charging_time = filters.CharFilter(
        method='filter_parcel_customer_order_charge_in__charging_time')
    # 计费方式
    # billing_method = filters.CharFilter(method='filter_billing_method')
    warehouse_code = filters.CharFilter(method='filter_warehouse_code')
    # 客户
    customer = filters.CharFilter(method='filter_customer')
    # 业务员
    saler = filters.CharFilter(method='filter_saler')
    # 结算专员
    settlement_name = filters.CharFilter(method='filter_settlement_name')
    # 邮政单号
    label_billid = filters.CharFilter(method='filter_label_billid')

    """==============================新小包单列表查询参数====================="""
    # 客户订单多选查询
    customer_order_num = filters.CharFilter(method='filter_customer_order_num')
    # 运单号多选查询
    order_num = filters.CharFilter(method='filter_order_num')
    # 派送单号多选查询
    tracking_num = filters.CharFilter(method='filter_tracking_num')
    # 大包单号多选查询
    parcel_num = filters.CharFilter(method='filter_parcel_num')
    # 出货批次号
    outbound_num = filters.CharFilter(method='filter_outbound_num')
    # 空运主单号
    master_order_num = filters.CharFilter(method='filter_master_order_num')
    # 产品多选查询
    product = filters.CharFilter(method='filter_product')
    # 供应商名称
    supplier = filters.CharFilter(method='filter_supplier')
    # 入库时间
    inbound_time = filters.CharFilter(method='filter_inbound_time')
    # 预报时间
    order_time = filters.CharFilter(method='filter_order_time')
    # 线路
    product_line = filters.CharFilter(method='filter_product_line')
    # 派送资源
    service_name = filters.CharFilter(method='filter_service_name')
    # 运单节点
    waybill_node = filters.CharFilter(method='filter_waybill_node')
    # 运单节点状态
    waybill_node_status = filters.CharFilter(method='filter_waybill_node_status')
    # 收货国家
    buyer_country = filters.CharFilter(method='filter_buyer_country')
    # 计费状态
    billing_status = filters.CharFilter(method='filter_billing_status')
    # 扣费状态
    deduction_status = filters.CharFilter(method='filter_deduction_status')
    # 是否已换面单
    is_change_waybill = filters.CharFilter(method='filter_is_change_waybill')
    # 运单状态
    order_status = filters.CharFilter(method='filter_order_status')
    # 大包关联
    big_parcel = filters.CharFilter(method='big_parcel_filter')
    # 状态查询
    order_type = filters.CharFilter(method='filter_order_type')

    def filter_order_type(self, queryset, name, value):
        return queryset.filter(order_type=value)

    def filter_order_status(self, queryset, name, value):
        return queryset.filter(order_status__in=value.split(','))

    def filter_saler(self, queryset, name, value):
        return queryset.filter(customer_id__in=value.split(','))

    def filter_is_change_waybill(self, queryset, name, value):
        return queryset.filter(is_change_waybill=value)

    def filter_waybill_node_status(self, queryset, name, value):
        return queryset.filter(waybill_node_status__in=value.split(','))

    def filter_service_name(self, queryset, name, value):
        return queryset.filter(service__name__in=value.split(','))

    def filter_product_line(self, queryset, name, value):
        return queryset.filter(product_line__name__in=value.split(','))

    def filter_order_time(self, queryset, name, value):
        return queryset.filter(order_time__range=value.split(','))

    def filter_inbound_time(self, queryset, name, value):
        return queryset.filter(inbound_time__range=value.split(','))

    def filter_master_order_num(self, queryset, name, value):
        return queryset.filter(
            big_parcel__parcel_outbound_order__customer_order__master_num__order_num__in=value.split(','))

    def filter_master_order_num(self, queryset, name, value):
        return queryset.filter(big_parcel__parcel_outbound_order__outbound_num__in=value.split(','))

    def filter_outbound_num(self, queryset, name, value):
        return queryset.filter(big_parcel__parcel_outbound_order__outbound_num__in=value.split(','))

    def filter_parcel_num(self, queryset, name, value):
        return queryset.filter(big_parcel__parcel_num__in=value.split(','))

    def filter_tracking_num(self, queryset, name, value):
        return queryset.filter(tracking_num__in=value.split(','))

    def filter_label_billid(self, queryset, name, value):
        return queryset.filter(label_billid__in=value.split(','))

    def filter_order_num(self, queryset, name, value):
        return queryset.filter(order_num__in=value.split(','))

    def filter_customer_order_num(self, queryset, name, value):
        return queryset.filter(customer_order_num__in=value.split(','))

    def filter_settlement_name(self, queryset, name, value):
        return queryset.filter(customer_id__in=value.split(','))

    def filter_customer(self, queryset, name, value):
        return queryset.filter(customer__id=value)

    def filter_warehouse_code(self, queryset, name, value):
        return queryset.filter(parcelOrderLabelTasks__warehouse_code__in=value.split(','))

    def filter_billing_status(self, queryset, name, value):
        return queryset.filter(parcelOrderExtends__billing_status=value)

    def filter_deduction_status(self, queryset, name, value):
        return queryset.filter(parcelOrderExtends__deduction_status=value)

    def filter_buyer_country(self, queryset, name, value):
        return queryset.filter(buyer_country__in=value.split(','))

    def filter_parcel_customer_order_charge_in__charging_time(self, queryset, name, value):
        return queryset.filter(parcel_customer_order_charge_in__charging_time__range=value.split(','))

    def filter_create_date(self, queryset, name, value):
        return queryset.filter(create_date__range=value.split(','))

    def filter_supplier(self, queryset, name, value):
        return queryset.filter(customer__name__in=value.split(','))

    def filter_product(self, queryset, name, value):
        """多订单号查询"""
        # 将逗号分隔的值拆分为列表,使用 __in 过滤多个值
        return queryset.filter(product__id__in=value.split(','))

    def big_parcel_filter(self, queryset, name, value):
        """大包单过滤
        value=False: 查询未关联大包单的订单
        value=True: 查询已关联大包单的订单
        """
        try:
            # 将字符串 'true'/'false' 转换为布尔值
            if isinstance(value, str):
                value = value.lower() == 'true'

            if value is False:
                return queryset.filter(big_parcel__isnull=True)
            elif value is True:
                return queryset.filter(big_parcel__isnull=False)
            return queryset
        except (ValueError, TypeError):
            return queryset.none()

    class Meta:
        model = ParcelCustomerOrder
        fields = ('product', 'is_revenue_lock', 'is_cost_lock', 'customer', 'order_status',
                  'is_weighing', 'intercept_mark', 'big_parcel__parcel_num', 'order_type',
                  'is_confirm_ship', 'third_orderNo', 'buyer_country_code', 'service__name',
                  'country_code', 'order_num', "intercept_status", "big_parcel__id", "big_parcel",)


class ParcelCustomerOrderViewSet(CustomViewBase):
    """
    客户订单管理：增删改查
    """
    # 针对外键的表进行预加载
    queryset = ParcelCustomerOrder.objects.all()

    # 针对1对多和多对多的预加载
    # queryset = queryset.prefetch_related('orderLabelTasks', 'truck_order_id',)

    serializer_class = ParcelCustomerOrderListSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ('=order_num', '=tracking_num', '=customer_order_num', '=label_billid',
                     '=big_parcel__parcel_num', '=big_parcel__parcel_outbound_order__outbound_num')
    filterset_class = ParcelCustomerOrderFilter  # 使用自定义 FilterSet
    ordering_fields = ('id',)
    authentication_classes = (AlitaJSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    def get_serializer_class(self):
        if self.action == "list":
            if settings.SYSTEM_VERSION == 'V2':
                return ParcelCustomerOrderListSerializerV2
            return ParcelCustomerOrderListSerializer
        return ParcelCustomerOrderAndDetailSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        # serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer = ParcelOrderSerializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def get_last_track(self, waybill_nodes):
        from django.db.models import F, Window
        from django.db.models.functions import FirstValue

        # 先获取每个订单的最新状态记录
        latest_status_subquery = ParcelTrack.objects.filter(
            order_num=ParcelTrack('order_num')
        ).order_by('-update_date').values('order_num')[:1]

        query = Q()
        for prefix in waybill_nodes:
            query |= Q(track_code__startswith=prefix)
        # latest_trackings = ParcelTrack.objects.annotate(
        #     latest_id=Window(
        #         expression=FirstValue('id'),
        #         partition_by=[F('order_num')],  # 按 order_id 分组
        #         order_by=F('id').desc()  # 按时间降序
        #     )
        # ).filter(query).values_list('order_num', flat=True).distinct()  # 只保留每个分组的第一条
        latest_trackings = Subquery(
            ParcelTrack.objects.annotate(
                latest_status=Subquery(latest_status_subquery)
            ).filter(query).values('order_num'))

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context

    def get_object(self):
        """自定义详情页根据不同字段查询"""
        queryset = self.filter_queryset(self.get_queryset())

        # Perform the lookup filtering.
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field

        assert lookup_url_kwarg in self.kwargs, (
                'Expected view %s to be called with a URL keyword argument '
                'named "%s". Fix your URL conf, or set the `.lookup_field` '
                'attribute on the view correctly.' %
                (self.__class__.__name__, lookup_url_kwarg)
        )

        if str(self.kwargs[lookup_url_kwarg]).isdigit():
            filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        else:
            filter_kwargs = {'order_num': self.kwargs[lookup_url_kwarg]}

        try:
            obj = get_object_or_404(queryset, **filter_kwargs)
        except Exception:
            try:
                filter_kwargs = {'tracking_num': self.kwargs[lookup_url_kwarg]}
                obj = get_object_or_404(queryset, **filter_kwargs)
            except Exception:
                raise ParamError(_('订单不存在'), ErrorCode.PARAM_ERROR)

        # May raise a permission denied
        self.check_object_permissions(self.request, obj)

        return obj

    def get_queryset(self):
        queryset = super().get_queryset()
        return ParcelCustomerOrderListSerializer.setup_eager_loading(queryset)

    def list(self, request, *args, **kwargs):
        # tab页切换
        tab_node = request.query_params.get('tab_node')
        # 未换单
        if tab_node == '2':
            self.queryset = self.queryset.filter(~Q(is_change_waybill=True) & ~Q(order_status='VO'))
        # 已换单
        if tab_node == '3':
            self.queryset = self.queryset.filter(Q(is_change_waybill=True) & ~Q(order_status='VO'))
        # 已作废
        if tab_node == '4':
            self.queryset = self.queryset.filter(order_status='VO')

        # 是否有跟踪号
        if request.query_params.get('has_trackingno') == 'N':
            self.queryset = self.queryset.filter(Q(tracking_num__isnull=True) | Q(tracking_num=''))
        elif request.query_params.get('has_trackingno') == 'Y':
            self.queryset = self.queryset.filter(~Q(tracking_num__isnull=True) & ~Q(tracking_num=''))
        if request.query_params.get('has_scanform') == 'N':
            self.queryset = self.queryset.filter(Q(parcelOrderExtends__scanform_task__isnull=True))
        elif request.query_params.get('has_scanform') == 'Y':
            self.queryset = self.queryset.filter(~Q(parcelOrderExtends__scanform_task__isnull=True))
        return super().list(request, *args, **kwargs)

    @action(methods=['GET'], detail=False)
    def get_ids(self, request, *args, **kwargs):
        ids = [i.id for i in self.filter_queryset(self.get_queryset()).all()]
        return JResponse(code=200, data=ids, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def add_carrier(self, request, *args, **kwargs):
        order_id = request.data.get('order_id')  # 小包单id
        third_party_tracking_number = request.data.get('third_party_tracking_number')  # 第三方配送单号
        third_party_carrier = request.data.get('third_party_carrier')  # 第三方配送承运商
        if not all([order_id, third_party_tracking_number, third_party_carrier]):
            raise ParamError(_('参数异常'), ErrorCode.PARAM_ERROR)
        pco = ParcelCustomerOrder.objects.filter(id=order_id).first()
        if not pco:
            raise ParamError(_('小包单不存在'), ErrorCode.PARAM_ERROR)
        # 判断是否存在可用的第三方配送单号,存在则逻辑删除
        poe = ParcelOrderExtend.objects.filter(customer_order_id=order_id, del_flag=0).first()
        if poe:
            poe.del_flag = 1
            poe.save()
        # 新增第三方配送单号信息
        ParcelOrderExtend.objects.create(
            customer_order=pco,
            third_party_tracking_number=third_party_tracking_number,
            third_party_carrier=third_party_carrier
        )
        return JResponse(code=200, msg=_('第三方配送单号增加成功'), status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def wms_sync_parcel_sorting(self, request, *args, **kwargs):
        """WMS同步分拣结果"""
        # 获取用户所在组织架构的location和timezone_offset
        user = request.user
        user_location = None
        user_timezone_offset = None
        if user.department:
            user_location = user.department.location
            user_timezone_offset = user.department.timezone_offset
            logger.info(f'用户组织架构信息 - location: {user_location}, timezone_offset: {user_timezone_offset}')
        order_num = request.data.get('order_num')
        user_desc = request.data.get('user_desc')
        company_desc = request.data.get('company_desc')
        objs = ParcelCustomerOrder.objects.filter(
            order_num=order_num, order_status__in=['WO', 'GL', 'CONFIRMED', 'AW', 'INBOUND', 'RL', 'SORTING']
        )
        objs.update(order_status='SORTING')
        # 添加分拣轨迹
        set_multistep_parcel_track(order_num, 'SORTING-000', datetime.now(), request.user, user_location, timezone_offset=user_timezone_offset)
        # 订单日志
        ParcelOrderLog.objects.create(
            order_num=order_num,
            user_desc=user_desc,
            company_desc=company_desc,
            remark='包裹分拣'
        )
        return JResponse(code=200, msg="Success", status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def wms_sync_cancel_weighting(self, request, *args, **kwargs):
        """WMS同步撤销称重入库"""
        order_num = request.data.get('order_num')
        user_desc = request.data.get('user_desc')
        company_desc = request.data.get('company_desc')
        is_weighing = request.data.get('is_weighing')
        weighing_weight = request.data.get('weighing_weight')

        # 回退状态
        ParcelCustomerOrder.objects.filter(order_num=order_num,  is_weighing=True).update(
            is_weighing=is_weighing, weighing_weight=weighing_weight, order_status='GL')

        # 删除入库轨迹
        ParcelTrack.objects.filter(order_num=order_num, system_code='INBOUND',
                                   del_flag=False).update(del_flag=True, remark='撤销称重入库')
        # 订单日志
        ParcelOrderLog.objects.create(
            order_num=order_num,
            user_desc=user_desc,
            company_desc=company_desc,
            remark='撤销称重入库'
        )
        return JResponse(code=200, msg="Success", status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def change_charging_time(self, request, *args, **kwargs):
        """更改计费时间"""
        ret_ids = []
        ids = request.data.get('ids')
        charging_time = request.data.get('charging_time')
        if not all([ids, charging_time]):
            raise ParamError(_('ids,计费时间为必填项'), ErrorCode.PARAM_ERROR)
        objs = ParcelCustomerOrder.objects.filter(id__in=ids)
        for obj in objs:
            try:
                ret = obj.parcel_customer_order_charge_in.get(customer_order_num_id=obj.id)
                if ret:
                    ret_ids.append(ret.id)
            except Exception as e:
                pass
        ParcelOrderChargeIn.objects.filter(id__in=ret_ids).update(charging_time=charging_time)
        return JResponse(code=200, msg=_('更改计费时间成功'), status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def specify_price(self, request, *args, **kwargs):
        """指定单价计费"""
        ret_ids = []
        ids = request.data.get('ids')
        charge_id = request.data.get('charge_id')
        account_charge = request.data.get('account_charge')
        currency_type = request.data.get('currency_type')
        if not all([charge_id, account_charge, currency_type]):
            raise ParamError(_('ids,费用项，金额，币种为必填项'), ErrorCode.PARAM_ERROR)
        objs = ParcelCustomerOrder.objects.filter(id__in=ids)
        for obj in objs:
            try:
                ret = obj.parcel_customer_order_charge_in.get(customer_order_num_id=obj.id)
                if ret:
                    ret_ids.append(ret.id)
            except Exception as e:
                pass

        ParcelOrderChargeIn.objects.filter(id__in=ret_ids).update(
            charging_time=datetime.now(),
            charge_id=charge_id,
            account_charge=account_charge,
            currency_type=currency_type
        )
        return JResponse(code=200, msg=_('指定单价计费成功'), status=status.HTTP_200_OK)

    @action(methods=['GET'], detail=False)
    def get_badges(self, request):
        """获取角标"""
        queryset = self.filter_queryset(self.get_queryset())
        data = queryset.aggregate(
            total=Count('id'),
            not_replaced=Count('id', filter=~Q(is_change_waybill=True) & ~Q(order_status='VO')),
            replaced=Count('id', filter=Q(is_change_waybill=True) & ~Q(order_status='VO')),
            cancel_count=Count('id', filter=Q(order_status='VO')),
        )
        return JResponse(data=data, code=200, msg="success", status=status.HTTP_200_OK)

    def retrieve(self, request, *args, **kwargs):
        if not request.user.is_staff and request.user.company is not None and self.get_object().customer != request.user.company:
            # 客户
            raise ParamError(_('您无权查看当前信息详情'), ErrorCode.PARAM_ERROR)
        return super().retrieve(request, *args, **kwargs)

    def _postcode(self, postcode, value, msg):
        buyer_postcode = str(postcode)
        prefix_word_patterns = re.split(r'[,，]', str(value))
        if any(buyer_postcode.startswith(i_prefix_word) for i_prefix_word in prefix_word_patterns):
            raise ValueError({'msg': f"{msg}: {value}", 'code': 400})

    @action(methods=['GET'], detail=False)
    def trajectory_list(self, request, *args, **kwargs):
        """小包单轨迹列表"""
        customer_order_num = request.query_params.get("customer_order_num")
        order_num = request.query_params.get("order_num")
        tracking_num = request.query_params.get("tracking_num")
        if customer_order_num:
            self.queryset = self.queryset.filter(customer_order_num=customer_order_num)
        if order_num:
            self.queryset = self.queryset.filter(order_num=order_num)
        if tracking_num:
            self.queryset = self.queryset.filter(tracking_num=tracking_num)
        queryset = self.queryset.filter(~Q(tracking_num__isnull=True) & ~Q(tracking_num=''))
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ParcelCustomerOrderTrajectorySerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return JResponse(data=serializer.data, code=200, msg="success", status=status.HTTP_200_OK)

    # 获取所有未完成的小包订单
    @action(methods=['GET'], detail=False)
    def all_notFC_order(self, request):
        queryset = ParcelCustomerOrder.objects.filter(~Q(order_status__in=['FC', 'VO']), del_flag=False).order_by('-id')
        search_fields = ('order_num',)
        queryset = params_query(queryset, ParcelCustomerOrder, request, search_fields=search_fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ParcelCustomerOrderSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        else:
            serialized_data = ParcelCustomerOrderSerializer(queryset, many=True)
            request.data['data'] = serialized_data
            return success_response(request, 'success')

    @extend_schema(
        request={
            "application/json": {
                "example": {
                    "types": "A",  # 拦截类型：A 表示申请截单（韩进拦截申请），INTC 表示拦截，UNINTC 表示取消拦截
                    "remark": "拦截原因说明",  # 拦截备注
                    "ids": [1, 2, 3],  # 需要拦截的订单 ID 列表
                }
            }
        },
        responses={
            status.HTTP_200_OK: OpenApiResponse(
                description="拦截操作成功",
                examples=[
                    OpenApiExample(
                        "成功示例",
                        value={
                            "msg": "订单拦截中，请等待处理结果。",
                            "code": 200,
                        },
                    )
                ],
            ),
            status.HTTP_404_NOT_FOUND: OpenApiResponse(
                description="拦截操作失败",
                examples=[
                    OpenApiExample(
                        "失败示例",
                        value={
                            "msg": "拦截失败，未匹配到拦截类型！",
                            "code": 404,
                        },
                    )
                ],
            ),
        },
        summary="小包列表-拦截订单-申请截单",
        description="""
        拦截订单操作接口：
        - types: 拦截类型，可选值为 A（韩进拦截申请）、INTC（拦截）、UNINTC（取消拦截）、B（韩进取消拦截）。
        - remark: 拦截备注，用于说明拦截原因。
        - ids: 需要拦截的订单 ID 列表。
        """,
        tags=["直发订单管理"],
    )
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def order_intercept(self, request):
        """
        新增韩进拦截申请： types = A
        @param request:
        @return:
        """

        print('request.data-->', request.data)
        types = request.data.get('types', 'INTC')
        remark = request.data.get('remark')
        ids = request.data['ids']
        # print(ids, types)
        if types == 'INTC':
            parcel_orders = ParcelCustomerOrder.objects.filter(id__in=ids, intercept_mark=False, del_flag=False)
            if len(ids) != parcel_orders.count():
                raise ParamError(_('请确认选择的订单都是未拦截的'), ErrorCode.PARAM_ERROR)
            parcel_orders.update(intercept_mark=True, remark=remark)
            msg = _('拦截成功！')
            code = 200
        elif types == 'UNINTC':
            parcel_orders = ParcelCustomerOrder.objects.filter(id__in=ids, intercept_mark=True, del_flag=False)
            if len(ids) != parcel_orders.count():
                raise ParamError(_('请确认选择的订单都是已拦截的'), ErrorCode.PARAM_ERROR)
            parcel_orders.update(intercept_mark=False)
            msg = _('取消成功！')
            code = 200
        elif types == 'A':
            parcel_orders = ParcelCustomerOrder.objects.filter(id__in=ids, intercept_mark=False, del_flag=False)
            if len(ids) != parcel_orders.count():
                raise ParamError(_('请确认选择的订单都是未拦截的'), ErrorCode.PARAM_ERROR)
            # 更新为有拦截记录，用于TAB页筛选，更改拦截状态为拦截中

            parcel_orders.update(is_intercept_record=True, remark=remark, intercept_status='I')
            for item in parcel_orders:
                intercept_parcel_order_task.delay(order_id=item.id)
            msg = _('订单拦截中，请等待处理结果。')
            code = 200
        elif types == 'B':
            parcel_orders = ParcelCustomerOrder.objects.filter(id__in=ids, intercept_mark=True, del_flag=False)
            if len(ids) != parcel_orders.count():
                raise ParamError(_('请确认选择的订单都是已拦截的'), ErrorCode.PARAM_ERROR)
            # 用于TAB页筛选，更改拦截状态为取消拦截
            parcel_orders.update(remark=remark, intercept_status='Q', intercept_mark=False)
            msg = _('订单已取消拦截。')
            code = 200
        else:
            msg = _('拦截失败，未匹配到拦截类型！')
            code = 404
        data = {'msg': msg, 'code': code}
        return Response(data=data, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_order_intercept(self, request):
        """对外api订单拦截"""
        remark = request.data.get('remark')
        order_num = request.data.get('order_num')
        customer_order_num = request.data.get('customer_order_num')
        user = request.user
        if order_num:
            orders = ParcelCustomerOrder.objects.filter(
                order_num=order_num, customer=user.company, del_flag=False, intercept_mark=False
            )
        elif customer_order_num:
            orders = ParcelCustomerOrder.objects.filter(
                ~Q(order_status='VO'), customer_order_num=customer_order_num, customer=user.company, del_flag=False
            )
        if orders.exists():
            orders.update(is_intercept_record=True, intercept_status='I')
            for item in orders:
                intercept_parcel_order_task.delay(order_id=item.id)
            data = {'msg': _('拦截成功！'), 'code': 200}
        else:
            data = {'msg': _('订单不存在或者已经是拦截状态'), 'code': 404}
        return Response(data=data, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def update_inbound_time(self, request):
        """更新小包单入库时间"""
        ids = request.data.get('ids')
        inbound_time = request.data.get('inbound_time')
        if ids and inbound_time:
            ParcelCustomerOrder.objects.filter(id__in=ids).update(inbound_time=inbound_time)
            data = {'msg': _('小包单入库时间更新成功！'), 'code': 200}
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {'msg': _('请先勾选小包单和入库时间！'), 'code': 400}
            return Response(data=data, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def update_bulking_date(self, request):
        """更新小包单组包时间"""
        ids = request.data.get('ids')
        bulking_date = request.data.get('bulking_date')
        if ids and bulking_date:
            ParcelCustomerOrder.objects.filter(id__in=ids).update(bulking_date=bulking_date)
            data = {'msg': _('小包单组包时间更新成功！'), 'code': 200}
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {'msg': _('请先勾选小包单和组包时间！'), 'code': 400}
            return Response(data=data, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_remarks(self, request):
        """批量备注"""
        ids = request.data.get('ids')
        order_remark = request.data.get('order_remark')
        if ids:
            ParcelCustomerOrder.objects.filter(id__in=ids).update(order_remark=order_remark)
            data = {'msg': _('批量备注成功！'), 'code': 200}
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {'msg': _('请先勾选小包单！'), 'code': 400}
            return Response(data=data, status=status.HTTP_200_OK)

    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def change_product(self, request):
    #     """变更产品"""
    #     product_id = request.data.get('product_id')
    #     ids = request.data.get('ids')
    #     # if ids and product_id:
    #     #     if not ParcelCustomerOrder.objects.filter(id__in=ids).exists():
    #     #         data = {'msg': '小包单不存在！', 'code': 400}
    #     #         return Response(data=data, status=status.HTTP_200_OK)
    #     #     if not Product.objects.filter(id=product_id).exists():
    #     #         data = {'msg': '产品不存在！', 'code': 400}
    #     #         return Response(data=data, status=status.HTTP_200_OK)
    #     #     ParcelCustomerOrder.objects.filter(id__in=ids).update(product_id=product_id)
    #     #     data = {'msg': '变更产品成功！', 'code': 200}
    #     #     return Response(data=data, status=status.HTTP_200_OK)
    #     # else:
    #     #     data = {'msg': '请先勾选小包单和产品数据！', 'code': 400}
    #     data = {'msg': '变更产品后未重新计费 TODO'}
    #     return Response(data=data, status=status.HTTP_200_OK)
    #
    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def batch_inbound(self, request):
    #     """小包单批量入库"""
    #     ids = request.data.get('ids')
    #     if ids:
    #         ParcelCustomerOrder.objects.filter(id__in=ids).update(order_status="INBOUND", inbound_time=datetime.now())
    #         data = {'msg': '小包单批量入库成功！', 'code': 200}
    #         return Response(data=data, status=status.HTTP_200_OK)
    #     else:
    #         data = {'msg': '请先勾选小包单数据！', 'code': 400}
    #         return Response(data=data, status=status.HTTP_200_OK)
    #
    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def batch_revocation(self, request):
    #     """小包单撤销入库"""
    #     ids = request.data.get('ids')
    #     if ids:
    #         ParcelCustomerOrder.objects.filter(id__in=ids).update(order_status="WO", inbound_time=None)
    #         data = {'msg': '小包单撤销入库成功！', 'code': 200}
    #         return Response(data=data, status=status.HTTP_200_OK)
    #     else:
    #         data = {'msg': '请先勾选小包单数据！', 'code': 400}
    #         return Response(data=data, status=status.HTTP_200_OK)
    #
    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def batch_despatch(self, request):
    #     """小包单批量发运"""
    #     ids = request.data.get('ids')
    #     if ids:
    #         pco = ParcelCustomerOrder.objects.filter(big_parcel=None, id__in=ids)
    #         if pco:
    #             data = {'msg': '所选数据存在未组包的小包单！', 'code': 400}
    #             return Response(data=data, status=status.HTTP_200_OK)
    #
    #         big_parcels = BigParcel.objects.filter(big_parcel__in=ids).all()
    #
    #         outbound_big_parcels = [big_parcel for big_parcel in big_parcels if
    #                                 big_parcel.parcel_outbound_order is not None]
    #         if len(outbound_big_parcels) == 0 and len(big_parcels) > 0:
    #             """批量发运"""
    #             outbound_num = create_outbound_num()
    #             instance = ParcelOutboundOrder.objects.create(outbound_num=outbound_num)
    #             big_parcels.update(parcel_outbound_order=instance.id)
    #             data = {'msg': '小包单批量发运成功！', 'code': 200}
    #             return Response(data=data, status=status.HTTP_200_OK)
    #         else:
    #             data = {'msg': '所选数据存在发运单！', 'code': 400}
    #             return Response(data=data, status=status.HTTP_200_OK)
    #     else:
    #         data = {'msg': '请先勾选小包单数据！', 'code': 400}
    #         return Response(data=data, status=status.HTTP_200_OK)
    #
    # @transaction.atomic
    # @action(methods=['POST'], detail=False)
    # def cancel_despatch(self, request):
    #     """撤销发运"""
    #     ids = request.data.get('ids')
    #     if ids:
    #         big_parcels = BigParcel.objects.filter(big_parcel__in=ids).all()
    #         outbound_big_parcels = [big_parcel for big_parcel in big_parcels if
    #                                 big_parcel.parcel_outbound_order is not None]
    #         if len(outbound_big_parcels) == 0:
    #             """批量发运"""
    #             data = {'msg': '所选数据还未发运，无需撤销发运！', 'code': 400}
    #             return Response(data=data, status=status.HTTP_200_OK)
    #         else:
    #             outbound_ids = [outbound.id for outbound in outbound_big_parcels]
    #             big_parcels.update(parcel_outbound_order=None)
    #             ParcelOutboundOrder.objects.filter(id__in=outbound_ids).delete()
    #             data = {'msg': '撤销发运成功！', 'code': 200}
    #             return Response(data=data, status=status.HTTP_200_OK)
    #     else:
    #         data = {'msg': '请先勾选小包单数据！', 'code': 400}
    #         return Response(data=data, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_cancel_order_intercept(self, request):
        """对外api订单取消拦截"""
        user = request.user
        order_num = request.data.get('order_num')
        customer_order_num = request.data.get('customer_order_num')
        if order_num:
            parcel_orders = ParcelCustomerOrder.objects.filter(order_num=order_num, customer=user.company,
                                                               del_flag=False)
        elif customer_order_num:
            parcel_orders = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                               customer_order_num=customer_order_num,
                                                               customer=user.company, del_flag=False)
        if parcel_orders.exists():
            parcel_orders.update(intercept_mark=False)
            msg = _('取消成功！')
            code = 200
        else:
            msg = _('取消拦截失败，订单号不存在')
            code = 404
        data = {'msg': msg, 'code': code}
        return Response(data=data, status=status.HTTP_200_OK)

    # 批量订单拦截
    @action(methods=['POST'], detail=False)
    def batch_order_intercept(self, request):
        user = request.user
        ids = request.data['ids']
        parcel_customer_order_queryset = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False,
                                                                            intercept_mark=False)
        if parcel_customer_order_queryset.count() != len(ids):
            return fail_response(request, _('请选择状态未被拦截的客户订单！'))

        parcel_customer_order_queryset.update(intercept_mark=True, update_by=user, update_date=datetime.now())
        msg = '拦截成功！'
        code = 200
        data = {'msg': msg, 'code': code}
        return Response(data=data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def batch_order_cancel_intercept(self, request):
        user = request.user
        ids = request.data['ids']
        parcel_customer_order_queryset = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False,
                                                                            intercept_mark=True)
        if parcel_customer_order_queryset.count() != len(ids):
            return fail_response(request, _('请选择状态被拦截的客户订单！'))

        parcel_customer_order_queryset.update(intercept_mark=False, update_by=user, update_date=datetime.now())
        msg = _('取消拦截成功！')
        code = 200
        data = {'msg': msg, 'code': code}
        return Response(data=data, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def scan_form_order(self, request):
        """
        韩进对已预报的单子进行scan form
        @param request:
        @return:
        """
        ids = request.data['ids']
        user = request.user
        parcel_orders = ParcelOrderExtend.objects.filter(customer_order_id__in=ids, del_flag=False)

        parcel_orders.update(confirm_progress=2, confirm_types=2, update_by=user, update_date=datetime.now())
        data = {'msg': _('确认成功！'), 'code': 200}
        return Response(data=data, status=status.HTTP_200_OK)

    # 修改备注
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def modify_remark(self, request):
        id = request.data['id']
        remark = request.data['modifyVal']
        ParcelCustomerOrder.objects.filter(id=id).update(remark=remark)
        data = {'msg': _('修改成功！'), 'code': 200}
        return Response(data=data, status=status.HTTP_200_OK)

    # 完成订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def finish_order(self, request):
        ids = request.data['ids']
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids, order_status='WO')

        if queryset.count() != len(ids):
            return fail_response(request, _('请选择状态为等待作业并且未进行收入确认和成本确认的的客户订单！'))
        else:
            # 更改状态、取消与主单和分单的外键关联
            for item in queryset:
                item.order_status = 'FC'
                item.update_by = get_update_params(request)['update_by']
                item.update_date = get_update_params(request)['update_date']
                item.save()

            return success_response(request, _('完成订单成功!'))

    # 恢复订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def recovery_order(self, request):
        ids = request.data['ids']
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids, order_status__in=['VO', 'FC'])
        if queryset.count() != len(ids):
            return fail_response(request, _('请选择非草稿或非待作业订单！'))
        else:
            # 更改状态、取消与主单和分单的外键关联
            for item in queryset:
                item.order_status = 'WO'
                item.update_by = get_update_params(request)['update_by']
                item.update_date = get_update_params(request)['update_date']
                item.save()

            return success_response(request, _("恢复订单成功！"))

    # 收入确认
    @lock_request
    @action(methods=['POST'], detail=False)
    def revenue_order(self, request):
        ids = request.data['ids']
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids)
        errors_msg = []
        if queryset.filter(~Q(order_status__in=['DR', 'VO', 'FC']), is_revenue_lock=False).count() == len(ids):

            for parcel_customer_order in queryset:

                currency_order = ParcelCustomerOrder.objects.get(id=parcel_customer_order.id)
                if currency_order.is_revenue_lock:
                    continue

                # 增加redis，防止重复执行
                key = f'_parcel_customer_order_finish_order_{currency_order.id}'
                try:
                    if cache.get(key):
                        continue
                    cache.set(key, 'running', timeout=60 * 60)
                    order_revenue_confirm(currency_order.id, 'ParcelCustomerOrder', request.user)
                except ParamError as e:
                    errors_msg.append(str(e))
                finally:
                    cache.delete(key)
        else:
            request.data['msg'] = _('请选择未进行收入确认并且状态不为草稿/作废/完成的订单')
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        if errors_msg:
            f_msg = _("部分更新成功，部分更新异常，异常如")
            request.data['msg'] = f'{f_msg} {errors_msg}'
        else:
            request.data['msg'] = _('客户订单状态更新完成，账单成功生成。')

        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 收入解锁
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def order_unlock(self, request):
        ids = request.data['ids']
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids)
        fail_orders = 0
        for customer_order in queryset:
            if Debit.objects.filter(order_num=customer_order.order_num, is_invoiced=True, del_flag=False,
                                    is_adjust=False).count() > 0:
                fail_orders += 1
            else:
                is_lock_order(customer_order, 'revenue')
                Debit.objects.filter(order_num=customer_order.order_num, is_adjust=False).update(del_flag=True)
                AccountReceivable.objects.filter(order_num=customer_order.order_num, is_adjust=False).update(
                    del_flag=True)
                customer_order.is_revenue_lock = False
                customer_order.account_time = None
                # customer_order.order_status = 'WO'
                customer_order.income = None
                customer_order.gross_profit = None
                customer_order.gross_currency = None
                customer_order.update_by = get_update_params(request)['update_by']
                customer_order.update_date = get_update_params(request)['update_date']
                customer_order.save()
        if fail_orders > 0:
            f_msg = _("个订单解锁失败,部分客户订单收入解锁成功,请确保收入所开具的账单还未生成发票")
            request.data['msg'] = f'{fail_orders}{f_msg}'
            # request.data[
            #     'msg'] = '部分客户订单收入解锁成功！%s 个订单解锁失败，请确保收入所开具的账单还未生成发票' % fail_orders
        else:
            request.data['msg'] = _('客户订单收入解锁成功')
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 成本确认
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_finish(self, request):
        ids = request.data['ids']
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids)
        if queryset.filter(is_cost_lock=False, order_status__in=['WO', 'INBOUND', 'OUTBOUND']).count() == len(ids):
            for currency_order in queryset:
                order_cost_confirm(currency_order.id, 'ParcelCustomerOrder', request.user)
        else:
            request.data['msg'] = _('请选择未进行成本确认并且状态不为作废和草稿的订单！')
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)
        request.data['msg'] = _('成本确认成功！')
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 成本解锁
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cost_unlock(self, request):
        ids = request.data['ids']
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids)
        fail_orders = 0
        for customer_order in queryset:
            if AccountPayable.objects.filter(payment_num__isnull=False, is_adjust=False,
                                             order_num=customer_order.order_num, del_flag=False).count() > 0:
                fail_orders += 1
                continue
            else:
                is_lock_order(customer_order, 'cost')
                AccountPayable.objects.filter(order_num=customer_order.order_num, is_adjust=False).update(del_flag=True)
                customer_order.is_cost_lock = False
                customer_order.account_time = None
                # customer_order.order_status = 'WO'
                customer_order.gross_profit = None
                customer_order.gross_currency = None
                customer_order.cost = None
                customer_order.update_by = get_update_params(request)['update_by']
                customer_order.update_date = get_update_params(request)['update_date']
                customer_order.save()
        if fail_orders > 0:
            f_msg = _('客户订单成本解锁成功！ 订单成本解锁失败:')
            request.data['msg'] = f"{f_msg} {fail_orders}"
        else:
            request.data['msg'] = _('客户订单成本解锁成功')
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # 异步上传小包订单
    @action(methods=['POST'], detail=False, permission_classes=[IsAuthenticated])
    def upload_excel_sync_parcel(self, request):
        return sync_upload_file_common(request, 'PC', 'UploadOrderExcel')

    # 上传订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def upload_excel(self, request):
        user = request.user
        excel = request.FILES.get('file')
        # 打开excel
        wb = xlrd.open_workbook(filename=None, file_contents=excel.read())
        # 获取第一张表
        table = wb.sheets()[0]
        nrows = table.nrows  # 行数

        # 客户下标
        customer_index = 1
        upload_parcel_customer_order(customer_index, nrows, table, user)

        # 用完记得删除释放资源
        wb.release_resources()
        del wb
        return Response(data={'code': 200, 'data': {}, 'msg': _('导入成功')}, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def get_order(self, request):
        """
        根据poNo获取面单是否存在系统
        :param request:
        :return:
        """
        poNo = request.data['poNo']
        if poNo is (None or ''):
            return fail_response(request, _("po号必填"))

        count = ParcelCustomerOrder.objects.filter(po_no=poNo, del_flag=False).count()
        if count > 0:
            request.data['count'] = count
            return success_response(request, _("已存在数据"))

        request.data['count'] = 0
        return success_response(request, "")

    # 获取订单数据
    @action(methods=['GET'], detail=False)
    def get_orders(self, request):

        product_codes = request.query_params.get('product_codes')
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        product_codes = str(product_codes).split(',')
        order_queryset = ParcelCustomerOrder.objects.values_list('order_num', flat=True).filter(
            product__code__in=product_codes,
            del_flag=False)[:10]

        request.data['order_nums'] = order_queryset

        return success_response(request, "")

    @action(methods=['GET'], detail=False)
    def get_order_detail(self, request):
        """
        根据poNo获取面单是否存在系统
        :param request:
        :return:
        """
        order_num = request.query_params.get('order_num')
        if not order_num:
            return fail_response(request, _("order_num必填"))

        order_queryset = ParcelCustomerOrder.objects.filter(order_num=order_num, del_flag=False)
        if not order_queryset:
            return fail_response(request, _("无订单信息"))

        serializer = ParcelCustomerOrderDetailSerializer(order_queryset.first(), many=False)

        return Response(serializer.data)

    # 作废订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def fail_order(self, request):
        ids = request.data['ids']
        user = request.user
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)
        create_cancel_order_task_with_orders(queryset, source='FAIL_ORDER')
        queryset = queryset.filter(is_revenue_lock=False, is_cost_lock=False)
        if len(ids) != queryset.count():
            request.data['code'] = 400
            request.data['msg'] = _('请选择未进行收入确认和成本确认的订单操作')
            return Response(data=request.data, status=status.HTTP_200_OK)

        success_message = []
        fail_message = []

        for id in ids:
            parcel_customer_orders = ParcelCustomerOrder.objects.filter(id=id, del_flag=False)
            if parcel_customer_orders.count() == 0:
                success_message.append(_('订单不存在 id=') + str(id))
                continue

            customer_order = parcel_customer_orders.first()


            order_num = customer_order.order_num
            if customer_order.order_status == 'VO':
                success_message.append(order_num)
                continue

            if settings.SYSTEM_VERSION == "V2":
                # 未生成面单且未确认发货的订单可以直接作废，无需审核
                if not customer_order.is_change_waybill and not customer_order.is_confirm_ship:
                    # 直接作废，无需审核
                    handler_cancel_label(customer_order, order_num, success_message, fail_message, user)
                else:
                    # 已生成面单或已确认发货的订单需要审核
                    review_order()
            else:
                handler_cancel_label(customer_order, order_num, success_message, fail_message, user)

        if len(success_message) > 0 and not fail_message:
            return success_response(request, arr_to_str(success_message) + _('作废成功'))
        if len(fail_message) > 0 and not success_message:
            return fail_response(request, arr_to_str(fail_message))

        return success_response(request, arr_to_str(success_message) + _('作废成功') + ',' + arr_to_str(fail_message))

    # 恢复订单(取消作废)
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def recovery_order(self, request):
        ids = request.data.get('ids')
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids, order_status='VO', del_flag=False)
        if len(ids) != queryset.count():
            request.data['code'] = 400
            request.data['msg'] = _('请选择未进行收入确认和成本确认的订单操作')
            return fail_response(request, _('请选择作废状态的订单！'))
        for order_id in ids:
            ParcelCustomerOrder.objects.filter(id=order_id, del_flag=False).update(order_status='WO')
        return success_response(request, _('取消作废成功'))

    # 强制作废订单 不作废供应商
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def force_cancel_order(self, request):
        ids = request.data['ids']
        user = request.user
        queryset = ParcelCustomerOrder.objects.filter(
            id__in=ids, 
            del_flag=False
        )
        create_cancel_order_task_with_orders(queryset, source='FORCE_FAIL_ORDER')
        queryset = queryset.filter(
            is_revenue_lock=False, 
            is_cost_lock=False
        )
        if len(ids) != queryset.count():
            request.data['code'] = 400
            f_msg = _("作废订单不能重复作废")
            request.data['msg'] = f'{f_msg}{[x.order_num for x in queryset]}'
            return Response(data=request.data, status=status.HTTP_200_OK)

        success_message = []
        fail_message = []

        for id in ids:
            parcel_customer_orders = ParcelCustomerOrder.objects.filter(id=id, del_flag=False)
            if parcel_customer_orders.count() == 0:
                success_message.append(_('订单不存在 id=') + str(id))
                continue

            customer_order = parcel_customer_orders.first()

            if customer_order.is_cost_lock or customer_order.is_revenue_lock:
                f_msg = _("订单")
                f_msg2 = _("收入或成本已确认，不允许作废!")
                return fail_message.append(f'{f_msg}{customer_order.order_num}{f_msg2}')

            order_num = customer_order.order_num
            if customer_order.order_status == 'VO':
                success_message.append(order_num)
                continue

            handler_force_cancel_order(customer_order, order_num, success_message, fail_message, user)

        if len(success_message) > 0 and not fail_message:
            return success_response(request, arr_to_str(success_message) + _('作废成功'))
        if len(fail_message) > 0 and not success_message:
            return fail_response(request, arr_to_str(fail_message))

        return success_response(request, arr_to_str(success_message) + _('作废成功') + ',' + arr_to_str(fail_message))

    # 创建面单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def create_label(self, request):
        """
        创建下面单接口
        :param request:
        :return:
        """
        ids = request.data['ids']
        parcel_customer_orders = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False, order_status='WO')

        if parcel_customer_orders.count() == 0:
            raise ParamError(_('没有符合规划的订单可以获取面单。'), ErrorCode.PARAM_ERROR)

        for parcel_customer_order in parcel_customer_orders:

            product = parcel_customer_order.product
            if product.label_type == 'HW' and not parcel_customer_order.is_weighing and not parcel_customer_order.label_billid:
                add_label_task_by_product_type(parcel_customer_order, request.user)
                continue

            if product.label_type == 'ZW' and not parcel_customer_order.is_weighing:
                raise ParamError(_('此产品不允许下单,需要称重下单') + parcel_customer_order.order_num,
                                 ErrorCode.PARAM_ERROR)

            order_label_task_list = ParcelOrderLabelTask.objects.filter(~Q(status='VO'), ~Q(product__code='ZX_LABEL'),
                                                                        order_num=parcel_customer_order.id,
                                                                        del_flag=False)
            if order_label_task_list.exists():
                raise ParamError(_('不要重复下单') + parcel_customer_order.order_num, ErrorCode.PARAM_ERROR)
            else:
                order_label_task = ParcelOrderLabelTask()
                order_label_task.order_num = parcel_customer_order
                order_label_task.status = 'UnHandled'
                # order_label_task.mode_key = random.randint(1, 10)
                order_label_task.mode_key = gen_mode_key_by_shunt(parcel_customer_order.product.id)
                order_label_task.handle_times = '0'
                order_label_task.product = product
                order_label_task.create_by = request.user
                order_label_task.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                order_label_task.save()

        return success_response(request, 'success')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def get_body(self, request):
        ids = request.data.get('ids')
        if not ids:
            return fail_response(request, _('请输入正确的参数'))
        if len(ids) != 1:
            return fail_response(request, _('每次仅可选择1个订单'))

        url = settings.OPENOBSERVE_HOST
        if not url:
            return fail_response(request, _('系统不支持查报文，请联系技术'))

        before_time = datetime.now() - timedelta(days=60)
        order = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False, order_time__gte=before_time).first()
        if not order:
            return fail_response(request, _('仅可获取60天内的订单'))
        user = settings.OPENOBSERVE_USER
        password = settings.OPENOBSERVE_PWD
        org = settings.OPENOBSERVE_ORG
        url = settings.OPENOBSERVE_HOST

        order_time = order.order_time
        if order.inbound_time:
            order_time = order.inbound_time

        start_time = int(order_time.timestamp() * 1000000)
        end_time = int((order_time.timestamp() + 43200) * 1000000)
        bas64encoded_creds = base64.b64encode(bytes(user + ":" + password, "utf-8")).decode("utf-8")
        headers = {'accept': 'application/json', 'Content-Type': 'application/json',
                   "Authorization": "Basic " + bas64encoded_creds}
        data = {
            "query": {
                "end_time": end_time,
                "sql": f"SELECT * FROM 'info' where order_num = '{order.order_num}' ORDER BY _timestamp DESC",
                "start_time": start_time
            },
        }
        logger.info(f'{url}/api/{org}/_search ,入参: {json.dumps(data)}')
        resp = requests.post(f'{url}/api/{org}/_search', data=json.dumps(data), headers=headers)
        try:
            hits = resp.json()['hits']
        except Exception as e:
            hits = []
        data = dict()
        data['code'] = 200
        data['msg'] = 'success'
        data['data'] = hits
        return Response(data=data, status=status.HTTP_200_OK)

    # 更新预报重量
    @action(methods=['POST'], detail=False)
    def api_update_order_weight(self, request):
        order_num = request.data['order_num']
        weight = request.data['weight']

        if not weight:
            raise ParamError(_('重量必填'), ErrorCode.PARAM_ERROR)

        user = request.user
        customer_orders = ParcelCustomerOrder.objects.filter(order_num=order_num, del_flag=False)
        if customer_orders.count() == 0:
            raise ParamError(_('查无此订单') + str(order_num), ErrorCode.PARAM_ERROR)

        customer_order = customer_orders.first()
        if customer_order.order_status == 'VO':
            f_msg = _("订单已称作废不允许修改")
            raise ParamError(_("{order_num} 订单已作废不允许修改").format(order_num=order_num), ErrorCode.PARAM_ERROR)

            # raise ParamError(f'{order_num}{f_msg}', ErrorCode.PARAM_ERROR)

        if customer_order.is_weighing:
            f_msg = _("订单已称重签入不允许修改")
            raise ParamError(f'{order_num}{f_msg}', ErrorCode.PARAM_ERROR)

        customer_orders.update(weight=weight, update_by=user, update_date=datetime.now())

        return success_response(request, "success")

    # api 更新订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_update_order(self, request):
        order_num = request.data['order_num']
        weight = request.data['weight']
        user = request.user
        customer_orders = ParcelCustomerOrder.objects.filter(order_num=order_num, del_flag=False)
        if customer_orders.count() == 0:
            raise ParamError(_('查无此订单') + str(order_num), ErrorCode.PARAM_ERROR)
        customer_order = customer_orders.first()

        # 兼容无服务的订单
        product = customer_order.product

        if not customer_order.service:
            service_list = Service.objects.filter(product=product.id, del_flag=False)
            service = service_list.first()
        else:
            service = customer_order.service

        # 如果是虚拟产品，以最小成本的产品打单
        if product.is_virtual:
            product = customer_order.real_product
            service = Service.objects.filter(product=product, del_flag=False).first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id,
                                                                   del_flag=False)
        supplier_account = supplier_account_list[0]
        class_name = supplier_butt.class_name
        label_order_vo = LabelOrderVo()
        label_order_vo.supplierAccount = supplier_account
        label_order_vo.service = service
        label_order_vo.weight = weight
        label_order_vo.customerOrder = customer_order
        label_order_vo.supplier_butt = supplier_butt
        label_order_vo.product = product
        # 通过反射实例化对象
        obj = globals()[class_name]()
        result = update_order(obj, label_order_vo)
        if result['code'] == '0':
            ParcelOrderParcel.objects.filter(customer_order=customer_order, del_flag=False) \
                .update(label_weight=weight, update_by=user, update_date=datetime.now())
            return success_response(request, "success")

        elif result['code'] == '400':
            return fail_response(request, order_num + _('订单修改失败') + str(result['msg']))
        else:
            return fail_response(request, order_num + _('订单修改失败'))

    # @view_exception_handler
    # api 确认发货
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_confirm_ship(self, request):
        """单个订单确认"""
        results = []
        fail_results = []
        if settings.SYSTEM_VERSION == 'V2':
            user = request.user
            order_num = request.data.get('order_num')
            parcel_customer_orders = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'), order_num=order_num,
                                                                        customer=user.company,
                                                                        del_flag=False)
            if not parcel_customer_orders.exists():
                parcel_customer_orders = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                                            customer_order_num=order_num,
                                                                            customer=user.company,
                                                                            del_flag=False)

            if not parcel_customer_orders.exists():
                raise ParamError(_('查无此订单'), ErrorCode.PARAM_ERROR)

            parcel_customer_order = parcel_customer_orders.first()

            if not parcel_customer_order.tracking_num:
                raise ParamError('没有运单号, 不能预报', ErrorCode.PARAM_ERROR)

            result = handler_confirm_ship(parcel_customer_orders, request, results, order_num, fail_results)

            if not result or result.get('code', None) is None:
                logger.info(f'api_confirm_ship:{order_num} 不支持订单确认')
                return fail_response(request, '不支持订单确认')

            if int(result['code']) == 200:
                return success_response(request, 'success')
            else:
                return do_response(request, 400, result.get('message'))
        else:
            order_nums = request.data['order_nums']
            if not order_nums:
                raise ParamError(_('订单号必填'), ErrorCode.PARAM_ERROR)

            for order_num in order_nums:
                parcel_customer_orders = ParcelCustomerOrder.objects.filter(order_num=order_num, del_flag=False)
                handler_confirm_ship(parcel_customer_orders, request, results, order_num, fail_results)

            request.data['data'] = results
            request.data['fail_data'] = fail_results
            return success_response(request, '')

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_scanform(self, request):
        reference_num = request.data.get('reference_num')  # 系统级别唯一单号
        order_nums = request.data.get('order_nums')
        customer_order_nums = request.data.get('customer_order_nums')
        tracking_num = request.data.get('tracking_num')
        if not any([order_nums, customer_order_nums]):
            return fail_response(request, _('订单号与客户订单号列表必填一项'))
        if not reference_num:
            reference_num = generate_scan_form_bath_number()

        if not reference_num:
            return fail_response(request, _("请求参考号不存在,请检查请求参数 %s") % reference_num)
        user = request.user
        if order_nums:
            # order_nums 支持三种类型的混合查询
            logger.info(f"order_nums支持三合一查询: {order_nums}")
            
            # 先分析每个输入单号的匹配情况
            all_matched_order_ids = set()  # 使用set避免重复
            
            for order_num in order_nums:
                logger.info(f"正在分析输入单号: {order_num}")
                
                matches = ParcelCustomerOrder.objects.filter(
                    Q(order_num=order_num) | Q(customer_order_num=order_num) | Q(tracking_num=order_num),
                    del_flag=False, customer=user.company
                )
                
                logger.info(f"  单号 {order_num} 匹配到 {matches.count()} 条记录:")
                
                for match in matches:
                    match_types = []
                    if match.order_num == order_num:
                        match_types.append('order_num')
                    if match.customer_order_num == order_num:
                        match_types.append('customer_order_num')
                    if match.tracking_num == order_num:
                        match_types.append('tracking_num')
                    
                    logger.info(f"    订单ID={match.id}, order_num={match.order_num}, "
                            f"customer_order_num={match.customer_order_num}, "
                            f"tracking_num={match.tracking_num}, "
                            f"匹配类型: {'/'.join(match_types)}")
                    
                    all_matched_order_ids.add(match.id)
            
            # 使用收集到的订单ID进行最终查询
            parcel_customer_orders = ParcelCustomerOrder.objects.filter(
                id__in=all_matched_order_ids,
                del_flag=False
            )
            
            logger.info(f"最终去重后的订单数量: {parcel_customer_orders.count()}")
            logger.info(f"订单ID列表: {list(all_matched_order_ids)}")
            
            for order in parcel_customer_orders:
                logger.info(f"最终订单: ID={order.id}, order_num={order.order_num}, "
                        f"customer_order_num={order.customer_order_num}, "
                        f"tracking_num={order.tracking_num}")

        elif customer_order_nums:
            # 保持原有的客户订单号查询逻辑
            parcel_customer_orders = ParcelCustomerOrder.objects.filter(
                ~Q(order_status='VO'),
                customer_order_num__in=customer_order_nums,
                customer=user.company,
                del_flag=False
            )
        elif tracking_num:
            # 保持原有的跟踪号查询逻辑
            parcel_customer_orders = ParcelCustomerOrder.objects.filter(
                tracking_num=tracking_num, 
                customer=user.company,
                del_flag=False
            )
        
        if parcel_customer_orders.count() == 0:
            return fail_response(request, _('未找到匹配的订单'))

        master_air_waybill_number = request.data.get('flightinfo', {}).get('mawbNo')
        handover_num = request.data.get('handoverinfo', {}).get('handover_num')

        items = parcel_customer_orders.count()

        failure_tasks = OrderScanFormTask.objects.filter(
            reference_num=reference_num, master_air_waybill_number=master_air_waybill_number,
            handover_num=handover_num, del_flag=False,
            status='Failure',
        )
        if failure_tasks.exists():
            # 失败任务重推
            failure_tasks.update(status='UnHandled', request_data=request.data, response_data=None, items=items)
            return Response(data={'code': 200, 'msg': _('重推成功'), 'data': {'reference_num': reference_num}},
                            status=status.HTTP_200_OK)

        if OrderScanFormTask.objects.filter(reference_num=reference_num,
                                            master_air_waybill_number=master_air_waybill_number,
                                            handover_num=handover_num, del_flag=False,
                                            status='Success').exists():
            # 已预报成功返回提示
            return Response(data={'code': 200, 'msg': _('预报成功, 请在查询接口获取详情'), 'data': {'reference_num': reference_num}},
                            status=status.HTTP_200_OK)

        if OrderScanFormTask.objects.filter(reference_num=reference_num, del_flag=False).exists():
            return fail_response(request, _("请求参考号已存在 %s ,请勿重复提交") % reference_num)

        scanform_task = OrderScanFormTask.objects.create(
            reference_num=reference_num,
            master_air_waybill_number=master_air_waybill_number,
            handover_num=handover_num,
            items=items,
            request_data=request.data,
            create_by=request.user,
            scan_form_type='api',
            mode_key=gen_mode_key2(1, 5)
        )
        for order in parcel_customer_orders:
            # 如果重复创建，第一次的任务就会覆盖掉
            ParcelOrderExtend.objects.filter(customer_order=order, del_flag=0).update(scanform_task=scanform_task)
        return Response(data={'code': 200, 'msg': _('处理中'), 'data': {'reference_num': reference_num}},
                        status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def api_get_scanform_result(self, request):
        """异步获取scanform结果"""
        reference_num = request.data.get('reference_num')
        if not reference_num:
            return fail_response(request, _('reference_num,请求参考号不存在,请检查请求参数'))  # 'reference_num,请求参考号不存在,请检查请求参数'
        res = OrderScanFormTask.objects.filter(
            reference_num=reference_num,
            scan_form_type='api',
            del_flag=False
        ).last()
        if not res:
            return fail_response(request, _('reference_num,请求参考号不存在,请检查请求参数'))
        if settings.SYSTEM_VERSION == 'V2':
            data = {
                'reference_num': reference_num,
                'mawbNo': res.master_air_waybill_number,
                'handover_num': res.handover_num,
                'status': res.status,
                'msg': res.remark,
                'data': res.response_data if res.response_data else {}
            }
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            msg = ''
            if res.response_data:
                msg = res.response_data.get('msg', '')
            data = {
                'reference_num': reference_num,
                'status': res.status,
                'msg': msg,
                'data': res.response_data if res.response_data else {}
            }
            return Response(data=data, status=status.HTTP_200_OK)

    # 确认发货
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def confirm_ship(self, request):
        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError(_('订单id必填'), ErrorCode.PARAM_ERROR)

        results = []
        fail_results = []
        for id in ids:
            parcel_customer_orders = ParcelCustomerOrder.objects.filter(id=id, del_flag=False)
            order_num = parcel_customer_orders.first().order_num
            handler_confirm_ship(parcel_customer_orders, request, results, order_num, fail_results)

        if results and not fail_results:
            return success_response(request, f': {arr_to_str(results)}')

        if fail_results and not results:
            return fail_response(request, _("确认异常订单 %s") % arr_to_str(fail_results))

        msg = _("确认成功订单 %s") % arr_to_str(results)
        msg += _("确认异常订单 %s") % arr_to_str(fail_results)
        return success_response(request, msg)

    # 批量更新订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_update_order(self, request):
        ids = request.data['ids']
        product_id = request.data.get('product')
        warehouse_code = request.data.get('warehouse_code')
        if len(ids) == 0:
            raise ParamError(_('订单id必填'), ErrorCode.PARAM_ERROR)
        if not product_id and not warehouse_code:
            raise ParamError(_('产品或仓库编码必须选一个'), ErrorCode.PARAM_ERROR)

        params = {
            'update_date': datetime.now(),
            'update_by': request.user
        }
        if product_id:
            product = Product.objects.get(id=product_id)
            service_query = Service.objects.filter(product=product, del_flag=False)
            params['product'] = product
            params['service'] = service_query.first()

        if warehouse_code:
            params['warehouse_code'] = warehouse_code

        results = []
        for id in ids:

            parcel_customer_orders = ParcelCustomerOrder.objects.filter(id=id, del_flag=False)
            if parcel_customer_orders:
                parcel_customer_order = parcel_customer_orders.first()
                charge_in = ParcelOrderChargeIn.objects.filter(customer_order_num=parcel_customer_order, del_flag=False)
                if charge_in:
                    results.append(parcel_customer_order.order_num + _('已有收入不能更改'))
                    continue
                order_label_task = ParcelOrderLabelTask.objects.filter(order_num=parcel_customer_order, del_flag=False,
                                                                       status__in=['UnHandled', 'HandledBy3rdNo'],
                                                                       handle_times__lt=121)
                if order_label_task:
                    results.append(parcel_customer_order.order_num + _('有打单任务请先关闭任务'))
                    continue

                if parcel_customer_order.tracking_num and parcel_customer_order.tracking_num != parcel_customer_order.label_billid and parcel_customer_order.order_num != parcel_customer_order.tracking_num:
                    results.append(parcel_customer_order.order_num + _('已有跟踪号不能更改'))
                    continue

                ParcelCustomerOrder.objects.filter(id=id, del_flag=False).update(**params)

        return success_response(request, arr_to_str(results))

    # 批量更新TAX
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_update_tax(self, request):
        ids = request.data['ids']
        buyer_tax = request.data.get('buyer_tax', None)
        ioss_num = request.data.get('ioss_num', None)
        buyer_mail = request.data.get('buyer_mail', None)
        buyer_phone = request.data.get('buyer_phone', None)
        if len(ids) == 0:
            raise ParamError(_('订单id必填'), ErrorCode.PARAM_ERROR)
        # if not buyer_tax and not ioss_num and not buyer_mail:
        #     raise ParamError('TAX或IOSS必须选一个', ErrorCode.PARAM_ERROR)

        params = {
            'update_date': datetime.now(),
            'update_by': request.user
        }

        if buyer_tax:
            params['buyer_tax'] = buyer_tax
        if ioss_num:
            params['ioss_num'] = ioss_num
        if buyer_mail:
            params['buyer_mail'] = buyer_mail
        if buyer_phone:
            params['buyer_phone'] = buyer_phone

        results = []
        for id in ids:

            parcel_customer_orders = ParcelCustomerOrder.objects.filter(id=id, del_flag=False)
            if parcel_customer_orders:
                parcel_customer_order = parcel_customer_orders.first()
                charge_in = ParcelOrderChargeIn.objects.filter(customer_order_num=parcel_customer_order,
                                                               del_flag=False)
                if charge_in:
                    results.append(parcel_customer_order.order_num + _('已有收入不能更改'))
                    continue
                order_label_task = ParcelOrderLabelTask.objects.filter(order_num=parcel_customer_order,
                                                                       del_flag=False,
                                                                       status__in=['UnHandled', 'HandledBy3rdNo'],
                                                                       handle_times__lt=121)
                if order_label_task:
                    results.append(parcel_customer_order.order_num + _('有打单任务请先关闭任务'))
                    continue

                # if parcel_customer_order.tracking_num and parcel_customer_order.tracking_num != parcel_customer_order.label_billid and parcel_customer_order.order_num != parcel_customer_order.tracking_num:
                #     results.append(parcel_customer_order.order_num + '已有跟踪号不能更改')
                #     continue

                ParcelCustomerOrder.objects.filter(id=id, del_flag=False).update(**params)

        return success_response(request, arr_to_str(results))

    # 取消面单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def cancel_label(self, request):
        '''
        取消面单接口
        :param request:
        :return:
        '''
        ids = request.data['ids']
        user = request.user
        if len(ids) == 0:
            raise ParamError(_('订单Id必填'), ErrorCode.PARAM_ERROR)

        success_message = []
        fail_message = []

        for id in ids:
            parcel_customer_orders = ParcelCustomerOrder.objects.filter(id=id, del_flag=False)
            if parcel_customer_orders.count() == 0:
                success_message.append(_('订单不存在 id=') + str(id))
                continue

            customer_order = parcel_customer_orders.first()
            create_cancel_order_task_with_orders([customer_order],source='CANCEL_LABEL')

            order_num = customer_order.order_num
            if customer_order.order_status == 'VO':
                success_message.append(order_num)
                continue

            handler_cancel_label(customer_order, order_num, success_message, fail_message, user)

        if len(success_message) > 0 and not fail_message:
            return success_response(request, arr_to_str(success_message) + '作废成功')
        if len(fail_message) > 0 and not success_message:
            return fail_response(request, arr_to_str(fail_message))

        return success_response(request, arr_to_str(success_message) + _('作废成功') + ',' + arr_to_str(fail_message))


    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def get_label(self, request):
        '''
        下面面单接口
        :param request:
        :return:
        '''
        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError(_('订单Id必填'), ErrorCode.PARAM_ERROR)

        parcel_customer_orders = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)

        return get_label_deal(request, parcel_customer_orders)


    # 获取 AG单号对应的面单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def get_ag_label(self, request):
        '''
        下载 ag面单
        :param request:
        :return:
        '''

        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError(_('订单Id必填'), ErrorCode.PARAM_ERROR)

        parcel_customer_orders = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)

        parcel_customer_order = parcel_customer_orders[0]
        order_ag_label_list = []
        for parcel_customer in parcel_customer_orders:
            parcel_order_labels = parcel_customer.parcelOrderLabels.filter(third_order_no__startswith='ZP')  # 只会有一条数据
            for parcel_order_label in parcel_order_labels:
                if parcel_order_label:
                    order_ag_label_list.append(parcel_order_label)

        finePath = settings.STATIC_MEDIA_DIR
        fileUrl = 'http://' + settings.DOMAIN_URL + '/media/'
        if len(order_ag_label_list) == 0:
            raise ParamError(_('查无面单'), ErrorCode.PARAM_ERROR)

        elif len(order_ag_label_list) == 1:
            orderLabel = order_ag_label_list[0]
            # fineName = parcel_customer_order.order_num + '.pdf'
            finePath += orderLabel.label_url
            fileUrl += orderLabel.label_url
        else:
            pdf_merger = PdfFileMerger()

            fineName = parcel_customer_order.order_num + '_m.pdf'
            finePath += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            fileUrl += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            dir = os.path.dirname(finePath)
            if not os.path.exists(dir):
                os.makedirs(dir)
            i = 0
            for orderLabel in order_ag_label_list:
                logger.info(f'label_url_all: {settings.STATIC_MEDIA_DIR + orderLabel.label_url}')
                pdf_merger.merge(i, settings.STATIC_MEDIA_DIR + orderLabel.label_url)
                i += 1
            pdf_merger.write(finePath)

        request.data['data'] = fileUrl
        return success_response(request, '')

    def get_label_list(self, product, parcel_customer_order, label_size=140):
        label_list = []
        if product.is_virtual and product.strategy_type in ['MULTI_CHANNEL', 'MC_COST_LOW']:

            sub_product_list = get_sub_product_list(parcel_customer_order, product)

            order_label_task_list = ParcelOrderLabelTask.objects.filter(order_num=parcel_customer_order,
                                                                        del_flag=False,
                                                                        product__in=sub_product_list).order_by('-id')

            if order_label_task_list.count() == 0:
                raise ParamError(_('无面单任务，请联系客服'), ErrorCode.PARAM_ERROR)

            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                if order_label_task.label_desc:
                    raise ParamError(order_label_task.label_desc, ErrorCode.PARAM_ERROR)
                else:
                    raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)
            elif order_label_task.status == 'VO':
                raise ParamError(_('面单已作废'), ErrorCode.PARAM_ERROR)

            order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_customer_order,
                                                               product__in=sub_product_list, del_flag=False)
            if len(order_label_list) == 0:
                raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)

            label_path = settings.STATIC_MEDIA_DIR
            file_url = 'http://' + settings.DOMAIN_URL + '/media/'

            label_list = []
            for order_label in order_label_list:
                tracking_num, is_vender_no = judge_is_vender_no_for_tracking_num(order_label, parcel_customer_order)
                file_url_str = file_url + order_label.label_url
                label_path_str = label_path + order_label.label_url

                with open(label_path_str, 'rb') as f:
                    base64_data = base64.b64encode(f.read())
                    base64_str = base64_data.decode()

                if is_vender_no:
                    if parcel_customer_order.tracking_num.startswith('ZHPHD'):  # 跟踪号是智禾单号并且扩展属性打开，则返回中性面单
                        file_url_str, base64_str = generate_zx_label_for_api_label(parcel_customer_order, label_size)
                    else:
                        file_url_str = ''
                        base64_str = ''

                label_dict = {
                    # "tracking_no": order_label.tracking_no,
                    "tracking_no": tracking_num,
                    "label": file_url_str,
                    "label_base64": base64_str,
                    'label_billid': parcel_customer_order.label_billid or ''
                }
                label_list.append(label_dict)

        elif product.label_type in ['FD', 'DD', 'SL', 'AD']:
            # 不允许客户上传面单 在去获取面单任务，否则直接获取面单
            if product.is_upload_label is False:
                order_label_task_list = ParcelOrderLabelTask.objects.filter(order_num=parcel_customer_order,
                                                                            del_flag=False).order_by('-id')
                if order_label_task_list.count() == 0:
                    raise ParamError(_('无面单任务，请联系客服'), ErrorCode.PARAM_ERROR)

                order_label_task = order_label_task_list.first()
                if order_label_task.status in ['HandledBy3rdNo', 'UnHandled', 'Failure']:
                    if order_label_task.label_desc:
                        # set_multistep_parcel_track_task.delay(parcel_customer_order.id, 'RL-999',
                        #                                       parcel_customer_order.create_by.id, '')
                        raise ParamError(order_label_task.label_desc, ErrorCode.PARAM_ERROR)
                    else:
                        raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)
                elif order_label_task.status == 'VO':
                    raise ParamError(_('面单已作废'), ErrorCode.PARAM_ERROR)

            order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_customer_order, del_flag=False)
            if len(order_label_list) == 0:
                raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)

            courier_code = parcel_customer_order.service.courier_code if parcel_customer_order.service else ''

            # 根据客户返回url或者base64格式的面单文件
            if parcel_customer_order.customer.type_waybill == 'url':
                label_list = []
                for order_label in order_label_list:
                    file_url_str = get_label_file_url(order_label.label_url)

                    label_dict = {
                        "tracking_no": order_label.tracking_no,
                        "label": file_url_str,
                        "label_base64": '',
                        'label_billid': parcel_customer_order.label_billid or '',
                        # 尾程派送商名称
                        "delivery_carrier": courier_code,
                    }
                    label_list.append(label_dict)
                    logger.info(f'tracking_no: {order_label.tracking_no}, label_url:{file_url_str}')
            else:
                label_list = []
                for order_label in order_label_list:
                    base64_str = get_label_file_base64(order_label.label_url)

                    label_dict = {
                        "tracking_no": order_label.tracking_no,
                        "label": '',
                        "label_base64": base64_str,
                        'label_billid': parcel_customer_order.label_billid or '',
                        'delivery_carrier': courier_code,
                    }
                    label_list.append(label_dict)

        elif product.label_type in ['WC', 'WA'] or parcel_customer_order.is_weighing:
            order_label_task_list = ParcelOrderLabelTask.objects.filter(order_num=parcel_customer_order,
                                                                        del_flag=False).order_by('-id')
            if order_label_task_list.count() == 0:
                raise ParamError(_('无面单任务，请联系客服'), ErrorCode.PARAM_ERROR)

            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                if order_label_task.label_desc:
                    raise ParamError(order_label_task.label_desc, ErrorCode.PARAM_ERROR)
                else:
                    raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)

            elif order_label_task.status == 'VO':
                raise ParamError(_('面单已作废'), ErrorCode.PARAM_ERROR)

            order_label_list = ParcelOrderLabel.objects.filter(~Q(product__code='ZX_LABEL'),
                                                               order_num=parcel_customer_order,
                                                               del_flag=False).order_by('sort_code', 'id')
            if len(order_label_list) == 0:
                raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)

            label_path = settings.STATIC_MEDIA_DIR
            file_url = 'http://' + settings.DOMAIN_URL + '/media/'

            label_list = []
            i = 0
            for order_label in order_label_list:
                tracking_num, is_vender_no = judge_is_vender_no_for_tracking_num(order_label, parcel_customer_order)
                file_url_str = file_url + order_label.label_url
                label_path_str = label_path + order_label.label_url

                logger.info(f'{i}--->{parcel_customer_order.order_num}, labep_path:{label_path_str}')
                i += 1

                with open(label_path_str, 'rb') as f:
                    base64_data = base64.b64encode(f.read())
                    base64_str = base64_data.decode()

                if is_vender_no:
                    if parcel_customer_order.tracking_num.startswith('ZHPHD'):  # 跟踪号是智禾单号并且扩展属性打开，则返回中性面单
                        file_url_str, base64_str = generate_zx_label_for_api_label(parcel_customer_order, label_size)
                    else:
                        file_url_str = ''
                        base64_str = ''

                label_dict = {
                    # "tracking_no": order_label.tracking_no,
                    "tracking_no": tracking_num,
                    "label": file_url_str,
                    "label_base64": base64_str,
                    'label_billid': parcel_customer_order.label_billid or ''
                }
                label_list.append(label_dict)

        elif product.label_type == 'YW':
            order_label_task_list = ParcelOrderLabelTask.objects.filter(order_num=parcel_customer_order,
                                                                        del_flag=False).order_by('-id')
            if order_label_task_list.count() == 0:
                raise ParamError(_('无面单任务，请联系客服'), ErrorCode.PARAM_ERROR)

            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                if order_label_task.label_desc:
                    raise ParamError(order_label_task.label_desc, ErrorCode.PARAM_ERROR)
                else:
                    raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)
            elif order_label_task.status == 'VO':
                raise ParamError(_('面单已作废'), ErrorCode.PARAM_ERROR)

            order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_customer_order, del_flag=False)
            if len(order_label_list) == 0:
                raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)

            if parcel_customer_order.tracking_num and parcel_customer_order.tracking_num != parcel_customer_order.label_billid:
                order_label_list = order_label_list.filter(is_secondary=True)

            label_path = settings.STATIC_MEDIA_DIR
            file_url = 'http://' + settings.DOMAIN_URL + '/media/'

            label_list = []
            for order_label in order_label_list:
                tracking_num, is_vender_no = judge_is_vender_no_for_tracking_num(order_label, parcel_customer_order)
                file_url_str = file_url + order_label.label_url
                label_path_str = label_path + order_label.label_url

                with open(label_path_str, 'rb') as f:
                    base64_data = base64.b64encode(f.read())
                    base64_str = base64_data.decode()

                if is_vender_no:
                    if parcel_customer_order.tracking_num.startswith('ZHPHD'):  # 跟踪号是智禾单号并且扩展属性打开，则返回中性面单
                        file_url_str, base64_str = generate_zx_label_for_api_label(parcel_customer_order, label_size)
                    else:
                        file_url_str = ''
                        base64_str = ''

                label_dict = {
                    # "tracking_no": order_label.tracking_no,
                    "tracking_no": tracking_num,
                    "label": file_url_str,
                    "label_base64": base64_str,
                    'label_billid': parcel_customer_order.label_billid or ''
                }
                label_list.append(label_dict)

        # 中性+换单，还未称重，返回中性面单
        elif product.label_type == 'ZW' or not parcel_customer_order.is_weighing:
            label_list = []
            orders = []
            orders.append(assemble_barcode_params(parcel_customer_order))
            base64_str = create_barcodes_for_order(orders, label_size)
            label_dict = {
                "tracking_no": parcel_customer_order.order_num,
                "tracking_num": parcel_customer_order.tracking_num,
                "label": '',
                "label_base64": base64_str,
                'label_billid': parcel_customer_order.label_billid or ''
            }
            label_list.append(label_dict)

        elif product.label_type == 'HW' and not parcel_customer_order.is_weighing:

            hlyz_product = Product.objects.filter(code='HLYZ', del_flag=False).first()

            order_label_task_list = ParcelOrderLabelTask.objects.filter(product=hlyz_product,
                                                                        order_num=parcel_customer_order,
                                                                        del_flag=False).order_by('-id')

            if order_label_task_list.count() == 0:
                raise ParamError(_('无面单任务，请联系客服'), ErrorCode.PARAM_ERROR)

            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                if order_label_task.label_desc:
                    raise ParamError(order_label_task.label_desc, ErrorCode.PARAM_ERROR)
                else:
                    raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)
            if order_label_task.status == 'VO':
                raise ParamError(_('面单已作废'), ErrorCode.PARAM_ERROR)

            order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_customer_order, del_flag=False)
            order_label_list = order_label_list.filter(product=hlyz_product)
            if len(order_label_list) == 0:
                raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)

            label_list = []
            orders = []
            orders_map = assemble_barcode_params(parcel_customer_order)
            # 兼容没有parcel_customer_order.tracking_num的情况
            if not parcel_customer_order.tracking_num:
                orders_map['tracking_num'] = order_label_list.first().tracking_no
            orders.append(orders_map)
            base64_str = create_barcodes_for_order(orders)
            # base64_str = create_barcodes_for_order_v1(orders)
            label_dict = {
                "tracking_no": parcel_customer_order.tracking_num or order_label_list.first().tracking_no,
                "label": '',
                "label_base64": base64_str
            }
            label_list.append(label_dict)

        return label_list

    @action(methods=['POST'], detail=False)
    def api_orders_label(self, request):
        '''
        下多面单接口
        :param request:
        :return:
        '''
        label_size = request.data.get('label_size', 140)
        order_nums = request.data.get('order_nums', [])
        if not order_nums:
            raise ParamError(_('订单号必填'), ErrorCode.PARAM_ERROR)
        order_nums_str = ','.join(order_nums)

        key = f'api_label_order_not_exists_{order_nums_str}'
        if cache.get(key):
            logger.info(f'{key}')
            raise ParamError(_("有部分订单号不存在") + order_nums_str, ErrorCode.PARAM_ERROR)

        customer_orders = ParcelCustomerOrder.objects.filter(order_num__in=order_nums, del_flag=False)
        if customer_orders.count() != len(order_nums):
            cache.set(key, "order not exists", 60 * 60 * 24)
            raise ParamError(_("有部分订单号不存在") + order_nums_str, ErrorCode.PARAM_ERROR)

        result = []
        for parcel_customer_order in customer_orders:
            product = parcel_customer_order.product
            label_list = self.get_label_list(product, parcel_customer_order, label_size)
            result.append(dict(
                order_num=parcel_customer_order.order_num,
                label_list=label_list
            ))

        customer_orders.update(download_num=F('download_num') + 1, update_date=datetime.now())

        request.data['data'] = result
        return success_response(request, '')

    @action(methods=['POST'], detail=False)
    def api_label(self, request):
        '''
        下面单接口
        :param request:
        :return:
        '''

        label_size = request.data.get('label_size', 140)
        order_num = request.data.get('order_num')
        customer_order_num = request.data.get('customer_order_num')
        # if not order_num:
        #     raise ParamError('订单号必填', ErrorCode.PARAM_ERROR)
        if not any([order_num, customer_order_num]):
            raise ParamError(_('订单号与客户订单号必填一项'), ErrorCode.PARAM_ERROR)

        key = f'api_label_order_not_exists_{order_num or customer_order_num}'
        # if cache.get(key):
        #     logger.info(f'{key}')
        #     raise ParamError(f'查无此订单{order_num}', ErrorCode.PARAM_ERROR)
        user = request.user
        if order_num:
            customer_orders = ParcelCustomerOrder.objects.filter(order_num=order_num, customer=user.company,
                                                                 del_flag=False)
        else:
            customer_orders = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                                 customer_order_num=customer_order_num,
                                                                 customer=user.company, del_flag=False)

        if not customer_orders.exists():
            if settings.SYSTEM_VERSION == 'V2':
                cache.set(key, "order not exists", 60)
            else:
                cache.set(key, "order not exists", 60 * 60 * 24)
            raise ParamError(_('查无此订单') + str(order_num or customer_order_num), ErrorCode.PARAM_ERROR)

        parcel_customer_order = customer_orders.first()
        product = parcel_customer_order.product

        label_list = self.get_label_list(product, parcel_customer_order, label_size)

        # if settings.SYSTEM_VERSION == 'V2':
        #     set_multistep_parcel_track_task.delay(parcel_customer_order.id, 'RL-000', user.id, '')

        # 下载面单次数加1
        download_num = parcel_customer_order.download_num or 0
        # 注意，小范围的修改，用update，否则会出现灵异事件，即并发下其它地方已改数据，此处又还是原来的写会去。
        # parcel_customer_order.download_num = download_num + 1
        # parcel_customer_order.save()
        ParcelCustomerOrder.objects.filter(id=parcel_customer_order.id).update(download_num=download_num + 1,
                                                                               update_date=datetime.now())

        if not order_num and customer_order_num:
            request.data['order_num'] = parcel_customer_order.order_num
        elif order_num and not customer_order_num:
            request.data['customer_order_num'] = parcel_customer_order.customer_order_num

        request.data['data'] = label_list
        return success_response(request, '')

    # 小包api下单接口
    @lock_request_common(['customer_order_num'], customer_restrict=True)
    @extend_schema(description='创建小包订单API')
    @action(methods=['POST'], detail=False)
    def api_create(self, request):

        user = request.user

        request.data['order_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 序列化
        serializer = ParcelCustomerOrderAndDetailSerializer(data=request.data)
        # 验证数据
        ret = serializer.is_valid()

        # validated_data = serializer.data
        # validated_data = serializer.validated_data
        validated_data = serializer.excluded_readonly_data

        customer_order_charge_in_list_data = validated_data.pop('parcel_customer_order_charge_in', {})
        customer_order_charge_out_list_data = validated_data.pop('parcel_customer_order_charge_out', {})
        parcel_order_extends = validated_data.pop('parcelOrderExtends', {})
        parcel_order_extends["is_confirm_label"] = validated_data.pop('is_confirm_label', False)
        parcel_order_extends["is_overseas_return"] = validated_data.pop('is_overseas_return', False)
        parcel_order_extends["is_signature"] = validated_data.pop('is_signature', False)
        is_signature = request.data.pop('is_signature', None)

        if is_signature is not None:
            parcel_order_extends["is_signature"] = is_signature
            validated_data.pop('is_signature', None)
        else:
            parcel_order_extends["is_signature"] = validated_data.pop('is_signature', False)

        # 签名类型
        signature_type = validated_data.pop('signature_type', None)
        logger.info(f'下单测试地址签名，{signature_type}-')
        signature_type = request.data.get('signature_type', None)
        logger.info(f'下单测试地址签名request，{signature_type}--')
        if signature_type is not None:
            parcel_order_extends["signature_type"] = signature_type
            # validated_data.pop('signature_type', None)

        tracks_data = validated_data.pop('tracks', {})
        operator = validated_data.pop('operator', {})
        tracking_number = validated_data.pop('tracking_number', {})
        tracking_label_url = request.data.pop('tracking_label_url', None)
        tracking_label_base64 = request.data.pop('tracking_label_base64', None)
        tracking_num = request.data.get('tracking_num', None)
        validated_data.pop('recipientInfo', None)
        validated_data.pop('shipperInfo', None)
        validated_data.pop('classification', None)
        validated_data.pop('pack_type', None)
        validated_data.pop('customer_code', None)
        validated_data.pop('taxInfo', None)
        validated_data.pop('parcel_order_log', None)
        validated_data.pop('confirm_type', None)

        if settings.SYSTEM_VERSION == 'V2':
            if not validated_data.get('weight'):
                validated_data['weight'] = 0

        parcel_item_data = request.data.get('parcelItem', None)
        customer_order_num = request.data.get('customer_order_num', None)
        if not user.company:
            raise ParamError(_('客户不存在,请联系客服'), ErrorCode.PARAM_ERROR)
        if not parcel_item_data:
            raise ParamError(_('包裹和商品信息必填'), ErrorCode.PARAM_ERROR)

        # 校验商品信息的币种
        for item in parcel_item_data:
            declared_currency = item.get('declared_currency')
            if not declared_currency:
                continue
            if not isinstance(item.get('declared_currency'), str):
                raise ParamError(_("包裹商品信息里面的币种只能为 字符类型"), ErrorCode.PARAM_ERROR)

        old_order_queryset = ParcelCustomerOrder.objects.filter(~Q(order_status__in=['VO', 'BC', 'CS']),
                                                                customer_order_num=customer_order_num,
                                                                customer=user.company,
                                                                del_flag=False)
        if old_order_queryset.exists():
            customer_order = old_order_queryset.first()
            f_msg = _("客户订单号不能重复, 对应订单号")
            msg = f'({customer_order_num}){f_msg}: {customer_order.order_num}'
            data = {
                "customer_order_num": customer_order.customer_order_num,
                "service_code": customer_order.product.code,
                "order_time": customer_order.order_time,
                "order_num": customer_order.order_num,
                "tracking_num": customer_order.tracking_num or '',
                "code": 301,
                "msg": msg,
                "detail": msg
            }
            return Response(data=data, status=status.HTTP_200_OK)

            # raise ParamError('客户订单号(' + str(
            #     customer_order_num) + ')不能重复, 对应订单号:' + old_order_queryset.first().order_num,
            #                  ErrorCode.PARAM_ERROR)
            # request.data['order_num'] = old_order_queryset.first().order_num
            # customer_order = old_order_queryset.first()
            # if settings.SYSTEM_VERSION == 'V2':
            #     data = {
            #         "customer_order_num": customer_order.customer_order_num,
            #         "service_code": customer_order.product.code,
            #         "weight": customer_order.weight,
            #         "order_remark": customer_order.remark or '',
            #         "order_time": customer_order.order_time,
            #         "order_num": customer_order.order_num,
            #         "tracking_num": customer_order.tracking_num or '',
            #         "code": 200,
            #         "msg": "success"
            #     }
            #     return Response(data=data, status=status.HTTP_200_OK)
            #
            # return success_response(request, "success")

        with transaction.atomic():

            customer_order = ParcelCustomerOrder.objects.create(**validated_data)

            customer = user.company
            customer_order.customer = customer
            # 收件地址类型校验
            # 组装产品
            service_code = request.data.get('service_code', None)
            if not service_code:
                raise ParamError(_('请填写产品编码(service_code)'), ErrorCode.PARAM_ERROR)
            product_list = Product.objects.filter(code=service_code, del_flag=False)
            if product_list.count() == 0:
                f_msg = _('无此产品服务')
                raise ParamError(f"{f_msg},{str(service_code)}", ErrorCode.PARAM_ERROR)
            product = product_list.first()
            if product.status == 'OFF':
                f_msg = _('此产品服务已被停用')
                raise ParamError(f"{f_msg},{str(service_code)}", ErrorCode.PARAM_ERROR)
            # FedEx地址类型校验
            if product.is_address_validation:
                recipient_info = request.data.get('recipientInfo', {})
                address_type = get_address_type_for_pricing(recipient_info)
                logger.info(f'计价地址校验：{address_type}')
                if address_type == 'residential':
                    address_type = 'P'
                else:
                    address_type = 'B'
                parcel_order_extends['address_type'] = address_type
            # 是否下单即确认等拓展属性
            tax_info = request.data.get('taxInfo', {})
            order_extend_obj = assemble_order_extends(customer_order, parcel_order_extends, tax_info, request)

            # 组装收件人信息
            recipient_info = request.data.get('recipientInfo', {})
            buyer_address = assemble_recipient_info(customer_order, recipient_info, request)
            if not customer_order.buyer_city_code:
                customer_order.buyer_city_code = customer_order.buyer_city
                validated_data['buyer_city_code'] = customer_order.buyer_city
            if not customer_order.buyer_country_code:
                customer_order.buyer_country_code = customer_order.buyer_country
                validated_data['buyer_country_code'] = customer_order.buyer_country

            # logger.info(model_to_dict(customer_order))

            # 组装交接地址
            assemble_address_info(customer_order, request, 'handoverinfo', 'TD')
            # 组装退件地址
            assemble_address_info(customer_order, request, 'returninfo', 'OV')
            # 组装揽收地址
            assemble_address_info(customer_order, request, 'pickupinfo', 'CA')

            # # 组装产品
            # service_code = request.data.get('service_code', None)
            # if not service_code:
            #     raise ParamError(_('请填写产品编码(service_code)'), ErrorCode.PARAM_ERROR)
            # product_list = Product.objects.filter(code=service_code, del_flag=False)
            # if product_list.count() == 0:
            #     f_msg = _('无此产品服务')
            #     raise ParamError(f"{f_msg},{str(service_code)}", ErrorCode.PARAM_ERROR)
            # product = product_list.first()
            # if product.status == 'OFF':
            #     f_msg = _('此产品服务已被停用')
            #     raise ParamError(f"{f_msg},{str(service_code)}", ErrorCode.PARAM_ERROR)
            customer_order.product = product

            # 发货地址
            warehouse = None
            if settings.SYSTEM_VERSION == 'V1':
                service_list = Service.objects.filter(product=product, del_flag=False, is_default=True)
                service = service_list.first()
                customer_order.service = service
                # 如果产品里有地址，就用产品的地址
                if product.address_num:
                    address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
                    if address_queryset.count() > 0:
                        warehouse = address_queryset.first()
                        customer_order.warehouse_code = warehouse

                if not warehouse:
                    # 兼容warehouse_code
                    address_num = request.data.get('address_num', None)
                    if address_num:
                        warehouse = Address.objects.filter(address_num=address_num, del_flag=False).first()
                        if warehouse:
                            customer_order.warehouse_code = warehouse

                if not warehouse:
                    raise ParamError(_('无发货地址,请跟客服确认'), ErrorCode.PARAM_ERROR)

                # 兼容新api地址
                assemble_shipper_info(customer_order, warehouse, request)

                # 限制客户
                handler_product_limit_user(customer_order.customer, product)

            elif settings.SYSTEM_VERSION == 'V2':
                # 判断是否有按客户发件地址
                shipper_info = request.data.get('shipperInfo', {})
                handler_shipper_info(customer, customer_order, product, request, warehouse, shipper_info)

                # 判断是否通用境外退件地址
                handler_return_oversea_address_info(customer, customer_order, product)

            customer_order.order_status = 'WO'

            try:
                handler_create_order(customer_order, customer_order_charge_in_list_data,
                                     customer_order_charge_out_list_data, parcel_item_data, user, validated_data,
                                     product.id)
            except Exception as e:
                logger.info(traceback.format_exc())
                raise ParamError(str(e), ErrorCode.PARAM_ERROR)

            request.data['order_num'] = customer_order.order_num

            if settings.SYSTEM_VERSION == 'V1':
                # 从客户账号扣钱
                product = customer_order.product

                if product.label_type == 'CPW':
                    # 客户推送面单类型，不生成抓单任务
                    customer_order.save()
                    if product and product.is_valuation:
                        deduction_account(customer_order, user, ParcelOrderChargeIn)

                elif product.label_type == 'HW':
                    add_label_task_by_product_type(customer_order, request.user)
                elif product.label_type == 'WA':
                    create_order_label_task(customer_order, user, ParcelOrderLabelTask, ParcelOrderParcel)
                elif product.label_type in ['ZW', 'YZW']:
                    add_label_task_by_product_type(customer_order, request.user, 'ZX_LABEL')
                    customer_order.tracking_num = customer_order.order_num
                    customer_order.save()
                elif product.label_type != 'ZW':
                    if product and product.is_valuation:
                        deduction_account(customer_order, user, ParcelOrderChargeIn)

                    create_order_label_task(customer_order, user, ParcelOrderLabelTask, ParcelOrderParcel)

                    # 返回费用给客户
                    charge_in_list = ParcelOrderChargeIn.objects.filter(customer_order_num=customer_order,
                                                                        del_flag=False)
                    if charge_in_list.count() > 0:
                        orderFee = 0
                        currency = ''
                        for charge_in in charge_in_list:
                            orderFee += charge_in.charge_total
                            if currency == '':
                                currency = charge_in.currency_type
                        request.data['orderFee'] = orderFee
                        request.data['currency'] = currency

                else:
                    customer_order.tracking_num = customer_order.order_num
                    customer_order.save()

            elif settings.SYSTEM_VERSION == 'V2':

                # 判断产品是否不可达分区
                unreachable_zone = get_product_unreachable_zone(customer_order, product, buyer_address.country_code,
                                                                buyer_address.postcode)
                if unreachable_zone:
                    f_msg = _("收货地址不可达,收货国家")
                    f_msg2 = _("收货人邮编")
                    raise ParamError(
                        f'{f_msg}:{buyer_address.country_code},{f_msg2}：{buyer_address.postcode}',
                        ErrorCode.PARAM_ERROR)

                # 发件人地址
                sender_address = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='SP',
                                                                   del_flag=False).last()

                # 海外退件地址
                return_address = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='OV',
                                                                   del_flag=False).last()

                # 组装参数
                order_data = model_to_dict(customer_order)
                order_data.update(model_to_dict(order_extend_obj))
                parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=customer_order, del_flag=False)
                parcel_data = [model_to_dict(x) for x in parcel_queryset]
                parcel_order_item_queryset = ParcelOrderItem.objects.filter(parcel_num__in=parcel_queryset,
                                                                            del_flag=False)
                parcel_item_data = [model_to_dict(x) for x in parcel_order_item_queryset]
                buyer_address_data = model_to_dict(buyer_address)
                sender_address_data = model_to_dict(sender_address)
                address_data = {
                    "buyer_address_data": buyer_address_data,
                    "sender_address_data": sender_address_data,
                }

                # 如果存在海外退件地址，则添加到 address_data 中
                if return_address:
                    address_data["returninfo"] = model_to_dict(return_address)

                total_declared_price = parcel_order_item_queryset.aggregate(total=Sum('declared_price'))['total']
                order_data.update(
                    {
                        'order_declared_price': float(total_declared_price) if total_declared_price else 0,
                        'tracking_num': tracking_num,
                        'tracking_label_url': tracking_label_url,
                        'tracking_label_base64': tracking_label_base64
                    },
                )

                product_line, service, zone = get_product_line_and_service(customer_order, product, buyer_address)

                # 判断资源是否不可达分区
                unreachable_service_zone = get_service_unreachable_zone(customer_order, service,
                                                                        buyer_address.country_code,
                                                                        buyer_address.postcode)
                if unreachable_service_zone:
                    f_msg1 = _("收货地址资源不可达,收货国家")
                    f_msg2 = _("收货人邮编")
                    raise ParamError(
                        f'{f_msg1}:{buyer_address.country_code},{f_msg2}：{buyer_address.postcode}',
                        ErrorCode.PARAM_ERROR)

                # 设置默认数据
                set_order_data_default_value(customer_order, service)

                # 检查产品数据验证
                check_order_data_by_product(order_data, product, buyer_address, parcel_data, parcel_item_data,
                                            address_data)

                # 检查资源数据验证
                check_order_data_by_service(order_data, service, buyer_address, parcel_data, parcel_item_data,
                                            address_data)

                customer_order.service = service
                customer_order.product_line = product_line
                customer_order.save()

                # 计费节点
                charging_rule = ChargingRule.objects.filter(product=product, del_flag=False).first()
                if charging_rule and charging_rule.charging_node == 'PR':
                    calc_zone = get_product_calc_zone(customer_order, product, buyer_address.country_code,
                                                      buyer_address.postcode)
                    add_revenue_v2(customer_order, user, ParcelOrderChargeIn, calc_zone, charging_rule.charging_node)
                    # deduction_account(customer_order, user, ParcelOrderChargeIn)
                    # 更新计费状态
                    order_extend_obj.billing_status = True
                    order_extend_obj.save(update_fields=['billing_status'])

                service_charging_rule = ServiceChargingRule.objects.filter(service=service, del_flag=False).first()
                if service_charging_rule and service_charging_rule.charging_node == 'PR':
                    calc_cost_zone = get_service_calc_zone(customer_order, service, buyer_address.country_code,
                                                           buyer_address.postcode)
                    if not calc_cost_zone:
                        raise ParamError(_('资源未配置应付分区，请联系客服'), ErrorCode.PARAM_ERROR)
                    add_cost_v2(customer_order, user, ParcelOrderChargeOut, calc_cost_zone, charging_rule.charging_node)

                # 创建同步任务
                # 仓内作业订单才同步WMS
                if customer_order.product.business_type == 'WW' and settings.SYNC_WMS_HOST:
                    SyncWMSTasks.create_small_order(
                        unique_id=customer_order.order_num,
                        params=json.dumps(CMSParcelCustomerOrderAndDetailSerializer(customer_order).data),
                    )

                # 预报节点换号
                if customer_order.product.replace_no_mode == 'FC':
                    # 客户上传面单，不请求下游
                    if not product.is_upload_label:
                        service_order_num_rule_obj = ServiceOrderNumRule.objects.filter(service=service,
                                                                                       del_flag=False).first()
                        if service_order_num_rule_obj.is_async:
                            order_label_task = add_order_label_task_by_service(customer_order, user, ParcelOrderLabelTask,
                                                                               service)
                        else:
                            # 同步换号
                            order_label_task = add_order_label_task_by_service(customer_order, user, ParcelOrderLabelTask,
                                                                               service)
                            handler_create_parcel_label_task(order_label_task.mode_key, order_label_task.id)
                            if not ParcelOrderLabelTask.objects.filter(
                                    pk=order_label_task.id, status__in=['HandledBy3rdNo', 'Success'],
                                    del_flag=False).exists():
                                task_obj = ParcelOrderLabelTask.objects.filter(pk=order_label_task.id,
                                                                               del_flag=False).last()
                                if task_obj:
                                    raise ParamError(task_obj.label_desc, ErrorCode.PARAM_ERROR)
                                raise ParamError('预报失败', ErrorCode.PARAM_ERROR)

                # 添加轨迹
                set_multistep_parcel_track_task.delay(customer_order.order_num, 'WO-000', request.user.id, '')
                # 更新节点状态
                customer_order.waybill_node_status = '000'  # 设置初始节点状态为000
                customer_order.is_change_waybill = False  # 设置初始换单状态为为换单
                customer_order.save(update_fields=['waybill_node_status', 'is_change_waybill'])

                if product.is_upload_label:
                    if ParcelCustomerOrder.objects.filter(~Q(order_status='VO'), ~Q(pk=customer_order.id),
                                                          tracking_num=tracking_num, del_flag=False).exists():
                        raise ParamError(_('请勿重复使用相同派送单号'), ErrorCode.PARAM_ERROR)

                    if tracking_label_url:
                        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + customer_order.order_num + "_" + (datetime.now().strftime("%Y_%m_%d_%H_%M_%S")) + ".pdf"
                        upload_file(label_url, tracking_label_url, 'url')
                        order_num = customer_order.order_num
                        order_label = get_order_label_obj(order_num)
                        order_label.order_num = customer_order
                        order_label.tracking_no = tracking_num
                        order_label.label_url = label_url
                        order_label.create_by = user
                        order_label.create_date = datetime.now()
                        order_label.product = product
                        order_label.save()

                    if tracking_label_base64:
                        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + customer_order.order_num + "_" + (datetime.now().strftime("%Y_%m_%d_%H_%M_%S")) + ".pdf"
                        upload_file(label_url, tracking_label_base64)
                        order_num = customer_order.order_num
                        order_label = get_order_label_obj(order_num)
                        order_label.order_num = customer_order
                        order_label.tracking_no = tracking_num
                        order_label.label_url = label_url
                        order_label.create_by = user
                        order_label.create_date = datetime.now()
                        order_label.product = product
                        order_label.save()

                    customer_order.order_status = 'GL'
                    # 是否换单状态
                    customer_order.is_change_waybill = True
                    customer_order.tracking_num = tracking_num
                    customer_order.save(update_fields=['tracking_num', 'order_status', 'is_change_waybill'])
                    set_multistep_parcel_track_task.delay(customer_order.order_num, 'GL-000', request.user.id, '')
                else:
                    if tracking_label_url or tracking_num:
                        raise ParamError(_('不允许客户自行上传面单和派送单号'), ErrorCode.PARAM_ERROR)

        if settings.SYSTEM_VERSION == 'V2':
            data = {
                "customer_order_num": customer_order.customer_order_num,
                "service_code": customer_order.product.code,
                "weight": customer_order.weight,
                "order_remark": customer_order.remark or '',
                "order_time": customer_order.order_time,
                "order_num": customer_order.order_num,
                "tracking_num": customer_order.tracking_num or '',
                "code": 200,
                "msg": "success"
            }
            return Response(data=data, status=status.HTTP_200_OK)

        return success_response(request, "success")

    @extend_schema(description='创建小包订单API')
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_pccc(self, request):
        """开放接口,让客户查询pccc是否正确"""
        pccc = request.data.get("pccc")
        name = request.data.get("name")
        phone = request.data.get("phone")

        data = call_special_api('check_pccc', pccc, name, phone)
        return Response(data=data, status=200)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def api_create_order(self, request):
        '''
        通过api下单
        :param request:
        :return:
        '''
        user = request.user
        print(request.data['ref_no'])
        # return success_response(request, "success")

        # parcel_item_data = request.data.get('parcelItem', None)
        customer_order_num = request.data.get('ref_no', None)

        if not user.company:
            raise ParamError(_('客户不存在,请联系客服'), ErrorCode.PARAM_ERROR)

        old_order = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'), customer_order_num=customer_order_num,
                                                       customer=user.company,
                                                       del_flag=False)
        if old_order.count() > 0:
            f_msg = _("客户订单号不能重复")
            raise ParamError(f'{f_msg}:{str(customer_order_num)}', ErrorCode.PARAM_ERROR)

        create_order_data = {
            'customer_order_num': customer_order_num,
            'weight': request.data.get('weight', 0),
        }
        customer_order = ParcelCustomerOrder.objects.create(**create_order_data)
        customer_order.customer = user.company

        sender = request.data.get('sender', None)
        if not sender:
            raise ParamError(_('发货地址必须填写'), ErrorCode.PARAM_ERROR)

        customer_order.contact_name = sender['name']
        customer_order.contact_phone = sender.get('telephone', '')
        customer_order.company_name = sender.get('company', '')
        customer_order.address_one = sender['street_1']

        address_two = sender.get('street_2', None)
        street3 = sender.get('street_3', None)
        if address_two and street3:
            address_two += ' ' + street3
        elif not address_two and street3:
            address_two = street3
        customer_order.address_two = address_two
        customer_order.city_code = sender['city']
        customer_order.postcode = sender['postcode']
        customer_order.country_code = sender['countrycode']
        customer_order.vat_num = sender.get('vat', '')
        customer_order.state_code = sender['province']

        consignee = request.data.get('consignee', '')
        if not consignee:
            raise ParamError(_('收货地址必须填写'), ErrorCode.PARAM_ERROR)

        customer_order.buyer_name = consignee['name']
        customer_order.buyer_mail = consignee.get('email', '')
        customer_order.buyer_phone = consignee.get('telephone', '')
        customer_order.buyer_country_code = consignee['countrycode']
        customer_order.buyer_country = consignee['countrycode']
        customer_order.buyer_state = consignee['province']
        customer_order.buyer_city_code = consignee['city']
        customer_order.buyer_city = consignee['city']
        customer_order.buyer_postcode = consignee['postcode']
        # customer_order.buyer_house_num = consignee['']
        customer_order.buyer_address_one = consignee['street_1']
        consignee_address_two = consignee.get('street_2', None)
        consignee_street3 = consignee.get('street_3', None)
        if consignee_address_two and consignee_street3:
            consignee_address_two += ' ' + consignee_street3
        elif not consignee_address_two and consignee_street3:
            consignee_address_two = consignee_street3

        customer_order.buyer_address_two = consignee_address_two

        customer_order.buyer_tax = consignee.get('tax', '')

        # # 兼容warehouse_code
        # address_num = request.data.get('address_num', None)
        # if address_num:
        #     address_list = Address.objects.filter(address_num=address_num, del_flag=False)
        #     if address_list.count() > 0:
        #         address = address_list[0]
        #         customer_order.warehouse_code = address

        service_code = request.data.get('channel_code', '')
        if service_code:
            product_list = Product.objects.filter(code=service_code, del_flag=False)
            if product_list.count() == 0:
                raise ParamError(_('无此产品服务') + str(service_code), ErrorCode.PARAM_ERROR)
            product = product_list.first()
            if product.status == 'OFF':
                raise ParamError(_('此产品服务已被停用') + str(service_code), ErrorCode.PARAM_ERROR)
            customer_order.product = product
            service_list = Service.objects.filter(product=product, del_flag=False, is_default=True)
            customer_order.service = service_list.first()
        else:
            raise ParamError(_('未选择产品服务'), ErrorCode.PARAM_ERROR)

        customer_order.order_status = 'WO'

        itemList = request.data.get('items', None)

        parcel_item_data = []
        for item in itemList:
            parcel_item = {
                'parcel_num': '1',
                'parcel_length': request.data.get('length', 10),
                'parcel_width': request.data.get('width', 10),
                'parcel_height': request.data.get('height', 10),
                'parcel_weight': request.data.get('weight', 0.01),
                'is_electronic': request.data.get('is_electronic', 0),
                'classification': request.data.get('classification', 0),

                'item_code': item['sku'],
                'declared_nameCN': item['cn_name'],
                'declared_nameEN': item['en_name'],
                'declared_price': item['value'],
                'item_qty': 1,
                'customs_code': item['hs_code'],
                'item_weight': item['weight'],
            }
            parcel_item_data.append(parcel_item)
        output_json = {
            'buyer_tax': customer_order.buyer_tax,
            'order_num': customer_order.order_num,
            'buyer_country_code': customer_order.buyer_country_code,
            'buyer_city_code': customer_order.buyer_city_code,
            'buyer_name': customer_order.buyer_name,
            'buyer_mail': customer_order.buyer_mail,
            'contact_phone': customer_order.contact_phone,
            'buyer_city': customer_order.buyer_city,
            'buyer_state': customer_order.buyer_state,
            'buyer_postcode': customer_order.buyer_postcode,
            'third_orderNo': customer_order_num
        }
        handler_create_order(customer_order, [], [], parcel_item_data, user, output_json, product.id)

        request.data['order_num'] = customer_order.order_num
        request.data['order_status'] = customer_order.order_status
        request.data['tail_service_name'] = service_code

        # if customer_order.order_status == 'DR':
        #     return success_response(request, "success")
        #
        # # 从客户账号扣钱
        # if customer_order.product and customer_order.product.is_valuation:
        #     deduction_account(customer_order, user, ParcelOrderChargeIn)
        #
        if product and product.label_type != 'ZW':
            create_order_label_task(customer_order, user, ParcelOrderLabelTask, ParcelOrderParcel)
        #
        # # 返回费用给客户
        # charge_in_list = ParcelOrderChargeIn.objects.filter(customer_order_num=customer_order, del_flag=False)
        # if charge_in_list.count() > 0:
        #     orderFee = 0
        #     currency = ''
        #     for charge_in in charge_in_list:
        #         orderFee += charge_in.charge_total
        #         if currency == '':
        #             currency = charge_in.currency_type
        #     request.data['orderFee'] = orderFee
        #     request.data['currency'] = currency
        #
        return success_response(request, "success")

    # 获取面单
    @action(methods=['POST'], detail=False)
    def api_get_label(self, request):
        '''
        下面面单接口
        :param request:
        :return:
        '''
        order_num = request.data['order_num']
        label_size = request.data.get('label_size', 140)
        if not order_num:
            raise ParamError(_('订单号必填'), ErrorCode.PARAM_ERROR)

        customer_orders = ParcelCustomerOrder.objects.filter(order_num=order_num, del_flag=False)
        if customer_orders.count() == 0:
            raise ParamError(_('查无此订单') + str(order_num), ErrorCode.PARAM_ERROR)

        parcel_customer_order = customer_orders.first()
        if parcel_customer_order.order_status == 'VO':
            raise ParamError(_("订单已作废 %s") % order_num, ErrorCode.PARAM_ERROR)

        product = parcel_customer_order.product

        if product.is_virtual and product.strategy_type in ['MULTI_CHANNEL', 'MC_COST_LOW']:

            sub_product_list = get_sub_product_list(parcel_customer_order, product)

            order_label_task_list = ParcelOrderLabelTask.objects.filter(order_num=parcel_customer_order,
                                                                        del_flag=False,
                                                                        product__in=sub_product_list).order_by('-id')

            if order_label_task_list.count() == 0:
                raise ParamError(_('无面单任务，请联系客服'), ErrorCode.PARAM_ERROR)

            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                if order_label_task.label_desc:
                    raise ParamError(order_label_task.label_desc, ErrorCode.PARAM_ERROR)
                else:
                    raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)
            elif order_label_task.status == 'VO':
                raise ParamError(_('面单已作废'), ErrorCode.PARAM_ERROR)

            order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_customer_order,
                                                               product__in=sub_product_list, del_flag=False)
            if len(order_label_list) == 0:
                raise ParamError('面单还未生成，请稍后再试', ErrorCode.PARAM_ERROR)

            label_path = settings.STATIC_MEDIA_DIR
            file_url = 'http://' + settings.DOMAIN_URL + '/media/'

            label_list = []
            for order_label in order_label_list:
                file_url_str = file_url + order_label.label_url
                label_path_str = label_path + order_label.label_url

                with open(label_path_str, 'rb') as f:
                    base64_data = base64.b64encode(f.read())
                    base64_str = base64_data.decode()

                label_dict = {
                    "tracking_no": order_label.tracking_no,
                    "label": file_url_str,
                    "label_base64": base64_str,
                    'label_billid': parcel_customer_order.label_billid or ''
                }
                label_list.append(label_dict)

            request.data['label_infos'] = label_list

        elif product.label_type in ['WC', 'WA'] or parcel_customer_order.is_weighing:
            order_label_task_list = ParcelOrderLabelTask.objects.filter(~Q(product__code='ZX_LABEL'),
                                                                        order_num=parcel_customer_order,
                                                                        del_flag=False).order_by('-id')
            if order_label_task_list.count() == 0:
                raise ParamError(_('无面单任务，请联系客服'), ErrorCode.PARAM_ERROR)

            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                if order_label_task.label_desc:
                    raise ParamError(order_label_task.label_desc, ErrorCode.PARAM_ERROR)
                else:
                    raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)
            elif order_label_task.status == 'VO':
                raise ParamError(_('面单已作废'), ErrorCode.PARAM_ERROR)

            order_label_list = ParcelOrderLabel.objects.filter(~Q(product__code='ZX_LABEL'),
                                                               order_num=parcel_customer_order, del_flag=False)
            if len(order_label_list) == 0:
                raise ParamError('面单还未生成，请稍后再试', ErrorCode.PARAM_ERROR)

            label_path = settings.STATIC_MEDIA_DIR
            file_url = 'http://' + settings.DOMAIN_URL + '/media/'

            label_list = []
            for order_label in order_label_list:
                file_url_str = file_url + order_label.label_url
                label_path_str = label_path + order_label.label_url

                with open(label_path_str, 'rb') as f:
                    base64_data = base64.b64encode(f.read())
                    base64_str = base64_data.decode()

                label_dict = {
                    "tracking_no": order_label.tracking_no,
                    "label": file_url_str,
                    "label_base64": base64_str,
                    'label_billid': parcel_customer_order.label_billid or ''
                }
                label_list.append(label_dict)

            request.data['label_infos'] = label_list
            if parcel_customer_order.tracking_num:
                request.data['main_tracking_no'] = parcel_customer_order.tracking_num
            else:
                main_tracking_no = order_label_list.first().tracking_no
                request.data['main_tracking_no'] = main_tracking_no
                parcel_customer_order.tracking_num = main_tracking_no

        elif product.label_type == 'ZW' and not parcel_customer_order.is_weighing:

            product = Product.objects.filter(code='ZX_LABEL', del_flag=False).first()
            order_label_task_list = ParcelOrderLabelTask.objects.filter(product=product,
                                                                        order_num=parcel_customer_order,
                                                                        del_flag=False).order_by('-id')
            if order_label_task_list.exists():

                order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_customer_order,
                                                                   product=product,
                                                                   del_flag=False)
                if len(order_label_list) == 0:
                    raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)

                label_path = settings.STATIC_MEDIA_DIR
                file_url = 'http://' + settings.DOMAIN_URL + '/media/'

                label_list = []
                for order_label in order_label_list:
                    file_url_str = file_url + order_label.label_url
                    label_path_str = label_path + order_label.label_url

                    with open(label_path_str, 'rb') as f:
                        base64_data = base64.b64encode(f.read())
                        base64_str = base64_data.decode()

                    label_dict = {
                        "tracking_no": order_label.tracking_no,
                        "label": file_url_str,
                        "label_base64": base64_str,
                        'label_billid': parcel_customer_order.label_billid or ''
                    }
                    label_list.append(label_dict)

                request.data['label_infos'] = label_list

            else:
                # 兼容旧的无面单任务的
                label_list = []
                orders = []
                orders.append(assemble_barcode_params(parcel_customer_order))
                base64_str = create_barcodes_for_order(orders, label_size)
                label_dict = {
                    "tracking_no": parcel_customer_order.order_num,
                    "label": '',
                    "label_base64": base64_str,
                    'label_billid': parcel_customer_order.label_billid or ''
                }
                label_list.append(label_dict)
                request.data['label_infos'] = label_list

        elif product.label_type == 'HW' and not parcel_customer_order.is_weighing:

            hlyz_product = Product.objects.filter(code='HLYZ', del_flag=False).first()
            order_label_task_list = ParcelOrderLabelTask.objects.filter(product=hlyz_product,
                                                                        order_num=parcel_customer_order,
                                                                        del_flag=False).order_by('-id')
            if order_label_task_list.count() == 0:
                raise ParamError(_('无面单任务，请联系客服'), ErrorCode.PARAM_ERROR)

            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                if order_label_task.label_desc:
                    raise ParamError(order_label_task.label_desc, ErrorCode.PARAM_ERROR)
                else:
                    raise ParamError(_('面单还未生成，请稍后再试'), ErrorCode.PARAM_ERROR)
            if order_label_task.status == 'VO':
                raise ParamError(_('面单已作废'), ErrorCode.PARAM_ERROR)

            order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_customer_order, del_flag=False)
            order_label_list = order_label_list.filter(product=hlyz_product)
            if len(order_label_list) == 0:
                raise ParamError('面单还未生成，请稍后再试', ErrorCode.PARAM_ERROR)

            label_list = []
            orders = []
            orders.append(assemble_barcode_params(parcel_customer_order))
            # base64_str = create_barcodes_for_order_v1(orders)
            base64_str = create_barcodes_for_order(orders)
            label_dict = {
                "tracking_no": parcel_customer_order.tracking_num or order_label_list.first().tracking_no,
                "label": '',
                "label_base64": base64_str,
                'label_billid': parcel_customer_order.label_billid or ''
            }
            label_list.append(label_dict)
            request.data['label_infos'] = label_list

        # 下载面单次数加1
        download_num = parcel_customer_order.download_num or 0
        ParcelCustomerOrder.objects.filter(id=parcel_customer_order.id).update(download_num=download_num + 1,
                                                                               update_date=datetime.now())

        # logger.info(str(request.data))
        return success_response(request, '')

    @action(methods=['POST'], detail=False)
    def api_cancel_label(self, request):
        '''
        取消面单接口
        :param request:
        :return:
        '''

        order_num = request.data.get('order_num', None)
        customer_order_num = request.data.get('customer_order_num', None)
        # if order_num is None:
        #     raise ParamError('缺少必要参数order_num', ErrorCode.PARAM_ERROR)
        if not any([order_num, customer_order_num]):
            raise ParamError(_('缺少必要参数order_num或customer_order_num'), ErrorCode.PARAM_ERROR)
        user = request.user
        if order_num:
            customer_orders = ParcelCustomerOrder.objects.filter(order_num=order_num, customer=user.company,
                                                                 del_flag=False)
        else:
            customer_orders = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                                 customer_order_num=customer_order_num,
                                                                 customer=user.company, del_flag=False)
            if customer_orders.first():
                order_num = customer_orders.first().order_num
            else:
                raise ParamError(_('客户订单不存在'), ErrorCode.PARAM_ERROR)
        create_cancel_order_task_with_orders(customer_orders, source='API_CANCEL_LABEL')
        lock_key = f'api_cancel_label_{order_num}'
        if cache.get(lock_key):
            return fail_response(request, _("%s已经接受到取消请求，重试请等待3s") % order_num)
        cache.set(lock_key, "1", 3)
        if customer_orders.count() == 0:
            raise ParamError(_('查无此订单%s') + str(order_num), ErrorCode.PARAM_ERROR)
        if settings.SYSTEM_MARK in ['MD']:
            one_minute_ago = timezone.now() - timezone.timedelta(seconds=10)
            customer_orders = customer_orders.filter(create_date__lt=one_minute_ago)
            if customer_orders.count() == 0:
                raise ParamError(_('订单创建时间小于1分钟无法取消') + str(order_num), ErrorCode.PARAM_ERROR)
        customer_order = customer_orders.first()

        if customer_order.is_cost_lock or customer_order.is_revenue_lock:
            return fail_response(request, '收入或成本已确认，不允许作废!')

        success_message = []
        fail_message = []

        if settings.SYSTEM_VERSION == "V2":
            customer_order = customer_orders.first()
            # 未确认和未生成面单的直接作废，无需审核
            if customer_order.order_status in ['DR', 'WO', 'GL', 'VO']:
                customer_order.is_change_waybill = False
                handler_cancel_label(customer_order, order_num, success_message, fail_message, user)
            else:
                review_order()
        else:
            # v1逻辑
            handler_cancel_label(customer_order, order_num, success_message, fail_message, user)
        if cache.get(lock_key):
            cache.delete(lock_key)

        if settings.SYSTEM_VERSION == "V2":
            data = {
                'msg': _("作废失败%s" % order_num),
                'code': 200,
                'order_num': order_num,
                'customer_order_num': customer_order.customer_order_num
            }
            if fail_message and not success_message:
                return do_response_common(msg='Failure', code=200, data=data)
            if not success_message and not fail_message:
                return do_response_common(msg='Failure', code=200, data=data)

            data['msg'] = order_num + _('作废成功')
            return do_response_common(msg='Success', code=200, data=data)
        else:
            if success_message and not fail_message:
                return success_response(request, arr_to_str(success_message) + _('作废成功'))
            if fail_message and not success_message:
                return fail_response(request, arr_to_str(fail_message))

            if not success_message and not fail_message:
                return fail_response(request, _("订单作废失败%s") % order_num)
            return success_response(request, arr_to_str(success_message) + '作废成功,' + str(fail_message))

    @action(methods=['POST'], detail=False)
    def api_get_order(self, request):
        # order_num = request.data['order_num']
        order_num = request.data.get('order_num', None)
        customer_order_num = request.data.get('customer_order_num', None)
        user = request.user
        if order_num:
            customer_orders = ParcelCustomerOrder.objects.filter(order_num=order_num, customer=user.company,
                                                                 del_flag=False)
        elif customer_order_num:
            customer_orders = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                                 customer_order_num=customer_order_num,
                                                                 customer=user.company, del_flag=False)
        if customer_orders.count() == 0:
            return fail_response(request, _('查无此订单') + str(order_num))
        customer_order = customer_orders.first()
        if settings.SYSTEM_VERSION == "V2":
            sp_obj = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='SP').last()
            rc_obj = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='RC').last()
            ov_obj = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='OV').last()

            parcel_item = []
            parcel_objs = ParcelOrderParcel.objects.filter(customer_order=customer_order, del_flag=False).all()
            for parcel_obj in parcel_objs:
                item_objs = ParcelOrderItem.objects.filter(parcel_num=parcel_obj, del_flag=False).all()
                for item_obj in item_objs:
                    parcel_item.append({
                        "parcel_type": parcel_obj.parcel_type,
                        "classification": parcel_obj.classification,
                        "in_HScode": item_obj.in_HScode or '',
                        "out_HScode": item_obj.out_HScode or '',
                        "out_declared_price":item_obj.out_declared_price or '',
                        "out_declared_currency":item_obj.out_declared_currency or '',
                        "parcel_length": float(parcel_obj.parcel_length or 0),
                        "parcel_width": float(parcel_obj.parcel_width or 0),
                        "parcel_height": float(parcel_obj.parcel_height or 0),
                        "parcel_weight": float(parcel_obj.parcel_height or 0),
                        "parcel_qty": float(parcel_obj.parcel_qty or 0),
                        "declared_nameCN": item_obj.declared_nameCN,
                        "declared_nameEN": item_obj.declared_nameEN,
                        "declared_price": float(item_obj.declared_price or 0),
                        "declared_currency": item_obj.declared_currency,
                        "distribution_remark": item_obj.distribution_remark,
                        "sku_url": item_obj.sku_url,
                        "origin_country": item_obj.origin_country,
                        "texture": item_obj.texture,
                        "use": item_obj.use,
                        "brand": item_obj.brand,
                        "model": item_obj.model,
                        "item_qty": item_obj.item_qty,
                        "item_weight": float(item_obj.item_weight or 0)
                    })
            track_code_obj = TrackCode.objects.filter(affiliated_track='T', del_flag=False, is_show=True,
                                                      system_code=customer_order.order_status).last()
            ParcelOrder = ParcelOrderExtend.objects.filter(customer_order=customer_order, del_flag=False).first()

            data = {
                'msg': '获取成功',
                'code': 200,
                'data': {
                    'order_num': customer_order.order_num,
                    'tracking_num': customer_order.tracking_num or '',
                    'customer_order_num': customer_order.customer_order_num or '',
                    'order_code': track_code_obj.code if track_code_obj else '',
                    'label_billid': customer_order.label_billid or '',
                    # 派送资源派送商
                    "delivery_carrier": customer_order.service.courier_code if customer_order.service else '',
                    # 第三方配送单号
                    'third_party_tracking_number': ParcelOrder.third_party_tracking_number if ParcelOrder else '',
                    # 第三方配送承运商
                    'third_party_carrier': ParcelOrder.third_party_carrier if ParcelOrder else '',

                },
                'orderinfo': {
                    'parcelItem': parcel_item,
                    'recipientInfo': APIParcelOrderAddressSerializer(instance=rc_obj).data,
                    'returninfo': APIParcelOrderAddressSerializer(instance=ov_obj).data,
                    'shipperInfo': APIParcelOrderAddressSerializer(instance=sp_obj).data,
                },
            }
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {
                'order_num': customer_order.order_num,
                'tracking_num': customer_order.tracking_num or '',
                'customer_order_num': customer_order.customer_order_num or '',
                'order_status': customer_order.order_status or '',
                'label_billid': customer_order.label_billid or ''
            }
            request.data['data'] = data

        return success_response(request, '获取成功')

    # 修改处理次数
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def update_handler_times(self, request):
        ids = request.data['ids']
        user = request.user
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)
        if parcel_customer_order_list.count() == 0:
            return fail_response(request, _("请选择订单操作"))

        ParcelOrderLabelTask.objects.filter(order_num__in=parcel_customer_order_list, del_flag=False) \
            .update(handle_times=0, update_date=datetime.now(), update_by=user)

        return success_response(request, _("修改成功"))

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_get_label_task(self, request):
        """
        修改抓单任务的状态，重新拉取
        :param request:
        :return:
        """
        ids = request.data['ids']
        user = request.user
        for order_id in ids:
            parcel_order = ParcelCustomerOrder.objects.get(id=order_id)

            ParcelOrderLabel.objects.filter(order_num=parcel_order, del_flag=False).update(del_flag=True,
                                                                                           remark='删除重新拉取',
                                                                                           update_date=datetime.now(),
                                                                                           update_by=user)

            ParcelOrderLabelTask.objects.filter(order_num=parcel_order).update(handle_times=0, status='UnHandled',
                                                                               update_date=datetime.now(),
                                                                               update_by=user)
        return success_response(request, _("修改成功！"))

    @action(methods=['POST'], detail=False)
    def update_product(self, request):
        id = request.data['id']
        product_code = request.data['product_code']
        user = request.user
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(id=id, del_flag=False)
        if parcel_customer_order_list.count() == 0:
            return fail_response(request, _("请选择订单操作"))

        parcel_customer_order = parcel_customer_order_list.first()

        if parcel_customer_order.order_status != 'DR':
            return fail_response(request, _("非草稿状态订单不能更改产品"))

        product_queryset = Product.objects.filter(code=product_code, del_flag=False)
        if not product_queryset.exists():
            return fail_response(request, _("%s不存在") % product_code)

        product = product_queryset.first()
        service = Service.objects.filter(product=product, del_flag=False).first()
        parcel_customer_order_list.update(product=product, service=service, update_date=datetime.now(), update_by=user)

        return success_response(request, _("修改成功"))

    # 运费试算(试算费用)
    @action(methods=['POST'], detail=False)
    def trial_fee(self, request):
        ids = request.data['ids']
        user = request.user
        if len(ids) > 1:
            return fail_response(request, _("请选择一个订单操作"))

        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)
        if not parcel_customer_order_list.exists():
            return fail_response(request, _("请选择订单操作"))

        parcel_customer_order = parcel_customer_order_list.first()

        parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=parcel_customer_order, del_flag=False)

        parcelList = []
        for parcel in parcel_queryset:
            parcelList.append({
                'weight': parcel.parcel_weight,
                'length': parcel.parcel_length,
                'width': parcel.parcel_width,
                'height': parcel.parcel_height,
                'volume': (parcel.parcel_length * parcel.parcel_width * parcel.parcel_height) / 1000000,
                'parcel_qty': parcel.parcel_qty
            })

        order_calc_vo = OrderCalcVo()

        warehouse = parcel_customer_order.warehouse_code

        start_dict_vo = DistCalcVo()
        start_dict_vo.countryCode = warehouse.country_code
        start_dict_vo.postCode = warehouse.postcode
        order_calc_vo.startDistVo = start_dict_vo
        end_dict_vo = DistCalcVo()
        buyer_country_code = parcel_customer_order.buyer_country_code
        end_dict_vo.countryCode = buyer_country_code
        end_dict_vo.postCode = parcel_customer_order.buyer_postcode
        order_calc_vo.endDistVo = end_dict_vo
        order_calc_vo.parcels = parcelList
        order_calc_vo.customer = parcel_customer_order.customer
        order_calc_vo.calcDate = datetime.now()
        order_calc_vo.customer_order = parcel_customer_order

        product_zone_queryset = ProductZone.objects.filter(country_code=buyer_country_code, del_flag=False,
                                                           type='Buyer')
        if not product_zone_queryset.exists():
            return fail_response(request, _("%s此国家未配置产品分区") % buyer_country_code)

        product_route_queryset = ProductRoute.objects.filter(end_zone__in=product_zone_queryset, del_flag=False)
        products = [x.product.id for x in product_route_queryset if x.product]

        product_queryset = Product.objects.filter(id__in=products, type='PC', is_open=True, is_virtual=False,
                                                  del_flag=False)

        success_data = []
        fail_data = []
        customer = user.company
        logger.info(f'{parcel_customer_order}_trial_fee, 用户信息-customer = {customer}, user= {user}')
        for product in product_queryset:
            # 产品客户限制
            product_limit_user_queryset = ProductLimitUser.objects.filter(product=product, del_flag=False)
            if product_limit_user_queryset:
                logger.info(f'{parcel_customer_order}_trial_fee,存在产品客户限制,product = {product}')
                product_limit_user_list = product_limit_user_queryset.filter(customer=customer)
                if not product_limit_user_list.exists():
                    logger.info(f'不计费, customer = {customer} 不在 product_limit_user_list = {product_limit_user_list}')
                    continue

            order_calc_vo.product = product
            try:
                revenue_results = revenue_calc(order_calc_vo, product)
            except Exception as e:
                logger.error(f'product = {product}, order_calc_vo = {order_calc_vo.__dict__} 计费失败: {traceback.format_exc()}')
                fail_data.append({'product_name': product.name,
                                  'product_code': product.code,
                                  'calc_result': str(e)})
                continue

            calc_result = {
                'product_name': product.name,
                'product_code': product.code,
                'total_fee': 0,
                'yf_fee': 0,
                'ryf_fee': 0,
                'pyf_fee': 0,
                'zz_fee': 0,
                'other_fee': 0,
            }
            for revenue_result in revenue_results:
                calc_result['currency'] = revenue_result.currency
                calc_result['total_fee'] = calc_result['total_fee'] + revenue_result.result_fee
                if revenue_result.charge_code in ['YF', 'PSF']:
                    calc_result['yf_fee'] = calc_result['yf_fee'] + revenue_result.result_fee
                elif revenue_result.charge_code == 'RYF':
                    calc_result['ryf_fee'] = calc_result['ryf_fee'] + revenue_result.result_fee
                elif revenue_result.charge_code == 'PYF':
                    calc_result['pyf_fee'] = calc_result['pyf_fee'] + revenue_result.result_fee
                elif revenue_result.charge_code == 'SRZZF':
                    calc_result['zz_fee'] = calc_result['zz_fee'] + revenue_result.result_fee
                else:
                    calc_result['other_fee'] = calc_result['other_fee'] + revenue_result.result_fee

            success_data.append(calc_result)

        data = {}
        data['data'] = {'success_data': success_data, 'fail_data': fail_data}
        data['msg'] = ''
        data['code'] = 200

        return Response(data=data, status=status.HTTP_200_OK)

    # 提交订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def submit_order(self, request):
        ids = request.data['ids']
        user = request.user
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(id__in=ids, order_status='DR', del_flag=False)
        if parcel_customer_order_list.count() == 0:
            msg = _('请选择草稿状态的订单操作')
            return fail_response(request, f'{msg},{ids}')

        for parcel_customer_order in parcel_customer_order_list:
            logger.info("order_num =" + parcel_customer_order.order_num)

            parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=parcel_customer_order, del_flag=False)
            if not parcel_queryset.exists():
                f_msg = _("订单未有包裹信息")
                return fail_response(request, f'{parcel_customer_order.order_num}{f_msg}')

            # 是否收入计价
            product = parcel_customer_order.product
            if settings.SYSTEM_VERSION == 'V1':
                if product.label_type == 'HW':

                    add_label_task_by_product_type(parcel_customer_order, request.user)
                elif product.label_type == 'WA':
                    create_order_label_task(parcel_customer_order, user, ParcelOrderLabelTask, ParcelOrderParcel)

                elif product.label_type != 'ZW':

                    if not parcel_customer_order.warehouse_code and product and (
                            product.is_valuation or product.is_cost_valuation):
                        raise ParamError(_('计价异常:未配置发货地址'), ErrorCode.PARAM_ERROR)

                    if product and product.is_valuation:
                        ParcelOrderChargeIn.objects.filter(customer_order_num=parcel_customer_order, del_flag=False,
                                                           is_system=True).update(del_flag=True)
                        add_revenue(parcel_customer_order, user, ParcelOrderChargeIn)
                    # 是否成本计价
                    if product and product.is_cost_valuation:
                        ParcelOrderChargeOut.objects.filter(customer_order_num=parcel_customer_order, del_flag=False,
                                                            is_system=True).update(del_flag=True)
                        add_cost(parcel_customer_order, user, ParcelOrderChargeOut)

                    # 从客户账号扣钱
                    if product and product.is_valuation:
                        deduction_account(parcel_customer_order, user, ParcelOrderChargeIn)

                    # 创建面单定时任务
                    create_order_label_task(parcel_customer_order, user, ParcelOrderLabelTask, ParcelOrderParcel)

                elif product.label_type == 'ZW':
                    parcel_customer_order.tracking_num = parcel_customer_order.order_num

            elif settings.SYSTEM_VERSION == 'V2':
                pass

            parcel_customer_order.order_status = 'WO'
            parcel_customer_order.update_date = datetime.now()
            parcel_customer_order.update_by = user
            parcel_customer_order.save()

        return success_response(request, _("提交成功，订单状态变更为等待作业！"))

    # 配置大包单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_big_parcel(self, request):
        ids = request.data['ids']
        orders = request.data.get('selectData', [])
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids, product__isnull=False, del_flag=False)
        product_num = queryset.values('product').distinct().count()
        if len(orders) == 0:
            for item in queryset:
                if item.big_parcel is not None:
                    big_parcel = BigParcel.objects.get(id=item.big_parcel.id)
                    parcel_order_num = ParcelCustomerOrder.objects.filter(big_parcel=big_parcel,
                                                                          del_flag=False).count() - 1
                    big_parcel.parcel_qty = parcel_order_num
                    if parcel_order_num == 0:
                        big_parcel.product = None
                        big_parcel.is_link_parcel_order = False
                    big_parcel.update_by = get_update_params(request)['update_by']
                    big_parcel.update_date = get_update_params(request)['update_date']
                    big_parcel.save()
                    item.big_parcel = None
                    item.save()
            return success_response(request, _("取消与大包单关联成功"))
        elif queryset.filter(big_parcel=None).count() != len(ids):
            return fail_response(request, _("请选择所选小包订单都未关联大包单！"))
        elif queryset.count() != len(ids) or product_num != 1:
            return fail_response(request, _("请确保所有小包单都有产品并且所选小包单都是相同的产品！"))
        else:
            big_parcel = BigParcel.objects.get(id=orders[0]['id'])
            if big_parcel.product is not None:
                if big_parcel.product == queryset.first().product:
                    queryset.update(big_parcel=big_parcel)
                    parcel_order_num = ParcelCustomerOrder.objects.filter(big_parcel=big_parcel, del_flag=False).count()
                    big_parcel.parcel_qty = parcel_order_num
                    big_parcel.is_link_parcel_order = True
                    big_parcel.update_by = get_update_params(request)['update_by']
                    big_parcel.update_date = get_update_params(request)['update_date']
                    big_parcel.save()
                    return success_response(request, _("配置成功"))
                else:
                    return fail_response(request, _("请确保所选小包订单的产品和大包单一致！"))
            else:
                queryset.update(big_parcel=big_parcel)
                parcel_order_num = ParcelCustomerOrder.objects.filter(big_parcel=big_parcel, del_flag=False).count()
                big_parcel.product = queryset.first().product
                big_parcel.parcel_qty = parcel_order_num
                big_parcel.is_link_parcel_order = True
                big_parcel.update_by = get_update_params(request)['update_by']
                big_parcel.update_date = get_update_params(request)['update_date']
                big_parcel.save()
                return success_response(request, _("配置成功"))

    # 小包重新计价
    @lock_request
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def revaluation(self, request):
        ids = request.data['ids']
        if not ids:
            ids = [obj.id for obj in self.filter_queryset(self.get_queryset())]
        queryset = ParcelCustomerOrder.objects.filter(~Q(order_status__in=['DR', 'VO', 'FC']), id__in=ids,
                                                      del_flag=False)
        if queryset.count() != len(ids):
            return fail_response(request, _("请选择状态为等待作业/入库/出库/转运的小包订单重新计价！"))
        is_revenue_lock_orders = ParcelCustomerOrder.objects.filter(id__in=ids, is_revenue_lock=True, del_flag=False)
        if is_revenue_lock_orders.exists():
            order_nums = [x.order_num for x in is_revenue_lock_orders]
            f_msg = _("已收入确认, 无法重新计价！")
            return fail_response(request, f'{order_nums}{f_msg}')
        is_cost_lock_orders = ParcelCustomerOrder.objects.filter(id__in=ids, is_cost_lock=True, del_flag=False)

        if is_cost_lock_orders.exists():
            order_nums = [x.order_num for x in is_cost_lock_orders]
            f_msg = _("已成本确认, 无法重新计价！")
            return fail_response(request, f'{order_nums}{f_msg}')

        if settings.SYSTEM_ORDER_MARK not in ['ZHS']:
            is_not_inboud_times = ParcelCustomerOrder.objects.filter(id__in=ids, inbound_time__isnull=True,
                                                                     del_flag=False)
            if is_not_inboud_times.exists():
                order_nums = [x.order_num for x in is_not_inboud_times]
                f_msg = _("未有入仓时间, 无法重新计价！")
                return fail_response(request, f'{order_nums}{f_msg}')

        for customer_order in queryset:
            # 更新计费状态
            parcel_order_extends = customer_order.parcelOrderExtends.first()
            parcel_order_extends.billing_status = True
            parcel_order_extends.save()

            product = customer_order.product
            user = request.user
            # 客户账号退款
            if product.is_valuation:
                refund_account(customer_order, user, ParcelOrderChargeIn)

            ParcelOrderChargeIn.objects.filter(customer_order_num=customer_order,
                                               del_flag=False, is_system=True).update(del_flag=True)
            ParcelOrderChargeOut.objects.filter(customer_order_num=customer_order,
                                                del_flag=False, is_system=True).update(del_flag=True)
            if product.is_valuation:
                add_revenue(customer_order, user, ParcelOrderChargeIn, True)
            if product.is_cost_valuation:
                add_cost(customer_order, user, ParcelOrderChargeOut, True)
            customer_order.save()

            # 从客户账号扣钱
            if product.is_valuation:
                deduction_account(customer_order, user, ParcelOrderChargeIn)

        return success_response(request, _("重新计价成功！"))

    # 配置清关大包单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def set_customs_clearance_big_parcel_order(self, request):
        ids = request.data['ids']
        orders = request.data.get('selectData', [])
        queryset = ParcelCustomerOrder.objects.filter(id__in=ids, product__isnull=False, del_flag=False)
        if len(orders) == 0:
            for item in queryset:
                if item.customs_clearance_big_parcel_order:
                    item.customs_clearance_big_parcel_order = None
                    item.save()
            return success_response(request, _("取消与清关大包单关联成功"))
        elif queryset.filter(customs_clearance_big_parcel_order=None).count() != len(ids):
            return fail_response(request, _("请选择所选清关包裹订单都未关联清关大包单！"))
        else:
            big_parcel = CustomsClearanceBigParcelOrder.objects.get(id=orders[0]['id'])
            queryset.update(customs_clearance_big_parcel_order=big_parcel)
            # 是否记录数量

            return success_response(request, _("配置成功"))

    # 批量搜索
    @action(methods=['POST'], detail=False)
    def multi_search(self, request):

        return get_multi_search(self, request, serializer=ParcelCustomerOrderListSerializer, foreign_key=None,
                                select_related_fields=True)

    # 轨迹更改
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_track(self, request):
        ids = request.data['ids']
        # orders = ParcelCustomerOrder.objects.filter(id__in=ids)
        code = request.data['selectActionVal']
        date = request.data['date']
        qty = request.data['amount']
        for order_id in ids:
            set_parcel_track(order_id, code, date, request.user, 'CN')
            order = ParcelCustomerOrder.objects.get(id=order_id)
            change_parcel_order_status(order, code, user=request.user)
            # set_customer_track('PC', date, order.id, code, qty, request)
        return success_response(request, _("轨迹更新成功！"))

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_track_v2(self, request):
        """新增小包单轨迹"""
        data = request.data
        id = data.pop("id", None)
        if id is not None:
            """更新数据"""
            try:
                ParcelTrack.objects.filter(id=id).update(**data)
                return success_response(request, _("轨迹编辑成功！"))
            except Exception as e:
                return fail_response(request, _("轨迹编辑失败：%s") % str(e))
        else:
            """新增数据"""
            serializer = ParcelTrackSerializer(data=request.data)
            try:
                if serializer.is_valid(raise_exception=True):
                    serializer.save()
                    return success_response(request, _("轨迹新增成功！"))
            except ValidationError as e:
                return fail_response(request, _("轨迹新增失败：%s") % str(e))
                # return fail_response(request, f'{_("轨迹新增失败！")}-> {str(e)}')

    # 获取当前订单的产品下的轨迹code
    @action(methods=['GET'], detail=False)
    def get_product_track_code(self, request):
        id = request.query_params['id']
        order = ParcelCustomerOrder.objects.get(id=id)
        if order.product is None:
            raise ParamError(_('该订单暂未配置产品'), ErrorCode.PARAM_ERROR)
        else:
            product_track_codes = ProductTrackCode.objects.filter(product=order.product, del_flag=False)
            if product_track_codes.count() == 0:
                raise ParamError(_('该产品暂未配置任何轨迹代码！'), ErrorCode.PARAM_ERROR)
            json_data = ProductTrackCodeSerializer(product_track_codes, many=True).data
            request.data['data'] = json_data
        return success_response(request, 'success')

    @action(methods=['POST'], detail=False)
    def print_label_general(self, request):
        """
        # 打印中性面单页签通用
        :param request:
        :return:
        """
        ids = request.data['ids']

        # 获取订单数据，预取相关对象
        parcel_orders = ParcelCustomerOrder.objects.filter(
            id__in=ids,
            del_flag=False
        ).select_related(
            'product',  # 获取Product对象
            'customer'  # 获取Company对象（客户信息）
        ).prefetch_related(
            Prefetch(  # 订单 -> 包裹 -> 商品
                'parcel',
                queryset=ParcelOrderParcel.objects.prefetch_related(
                    Prefetch(
                        'parcelItem',
                        queryset=ParcelOrderItem.objects.only('declared_nameCN', 'distribution_remark')
                    )
                ).only("classification")
            )
        )

        service_leave = {1: 'P', 2: 'S', 3: 'E', 4: 'A'}
        result = []
        for order in parcel_orders:
            # 基础信息
            order_info = {
                # 产品信息
                "产品名称": order.product.name if order.product else 'None',

                # 发件人信息
                "发件人姓名": order.contact_name if order.contact_name else 'None',
                "发件人地址": order.address_one if order.address_one else 'None',

                # 收件人信息
                "收件人姓名": order.buyer_name if order.buyer_name else 'None',
                "收件人地址": order.buyer_address_one if order.buyer_address_one else 'None',
                "收件人电话": order.buyer_phone if order.buyer_phone else 'None',
                "收件人国家代码": order.buyer_country_code if order.buyer_country_code else 'None',

                # 产品属性
                "服务等级": service_leave.get(order.product.service_level if order.product else None, 'P'),
                "货物类型": None,

                # 单号信息
                "单号": order.order_num if order.order_num else '001',

                # 客户信息
                "客户代码": order.customer.short_name if order.customer else 'None',
                "销售人员代码": order.customer.saler_name if order.customer else 'None',
                "客服人员代码": order.customer.customer_service_name if order.customer else 'None',

                "创建时间": order.order_time,
                "客户订单号": order.customer_order_num
            }

            classifications = set()
            for parcel in order.parcel.all():
                if parcel.classification:  # 确保字段存在且有效
                    classifications.add(str(parcel.classification))

            order_info["货物类型"] = str(next(iter(classifications), 4))

            #  处理申报品名和配货信息（可能多个）
            declared_items = []
            distribution_remarks = []

            # 遍历所有包裹
            for parcel in order.parcel.all():
                # 遍历包裹内所有商品
                for item in parcel.parcelItem.all():
                    if item.declared_nameCN:
                        declared_items.append(item.declared_nameCN)
                    if item.distribution_remark:
                        distribution_remarks.append(item.distribution_remark)

            # 合并多个值
            order_info["申报品"] = ", ".join(filter(None, declared_items)) or 'None'
            order_info["配货信息"] = ", ".join(filter(None, distribution_remarks)) or 'None'

            result.append(order_info)
        # 最终结果存储在result列表中
        # orders = []
        # for parcel_order in parcel_customer_order_list:
        #     orders.append(assemble_barcode_params(parcel_order))
        request.data['base64'] = create_label_general_view(result, 140)

        request.data['msg'] = _('打印成功！')
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def print_label(self, request):
        """
        # 打印中性面单页签
        :param request:
        :return:
        """
        ids = request.data['ids']
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)

        orders = []
        for parcel_order in parcel_customer_order_list:
            orders.append(assemble_barcode_params(parcel_order))

        request.data['base64'] = create_barcodes_for_order(orders, 140)
        request.data['msg'] = '打印成功！'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def print_label_100(self, request):
        """
        # 打印中性面单页签
        :param request:
        :return:
        """
        ids = request.data['ids']
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)

        orders = []
        for parcel_order in parcel_customer_order_list:
            orders.append(assemble_barcode_params(parcel_order))

        request.data['base64'] = create_barcodes_for_order(orders, label_size=100)
        request.data['msg'] = _('打印成功！')
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def print_tail_journey_waybill(self, request):
        """
        打印尾程条码
        """
        ids = request.data['ids']
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)

        tracking_nums = []
        for parcel_order in parcel_customer_order_list:
            if parcel_order.tracking_num:
                tracking_nums.append(parcel_order.tracking_num)
        if tracking_nums:
            request.data['base64'] = create_tail_journey_waybill(tracking_nums)
            request.data['msg'] = '打印成功！'
            request.data['code'] = 200
            return Response(data=request.data, status=status.HTTP_200_OK)
        else:
            request.data['msg'] = _('打印失败！没有派送单号')
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def print_hanjin_shipping_label(self, request):
        """
        打印韩进发货证明
        """
        ids = request.data['ids']
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(
            id__in=ids,
            order_status__in=['OUTBOUND', 'SF'],
            del_flag=False
        )
        if not parcel_customer_order_list:
            raise ParamError(_('请选择已出库的订单'), ErrorCode.PARAM_ERROR)

        orders = []
        for parcel_order in parcel_customer_order_list:
            # 兼容无发货时间的
            # if not parcel_order.outbound_time:
            #     parcle_track = ParcelTrack.objects.filter(order_num=parcel_order.order_num, track_code='outbound',
            #                                               del_flag=False).first()
            #     if parcle_track:
            #         parcel_order.outbound_time = parcle_track.actual_time
            #         ParcelCustomerOrder.objects.filter(id=parcel_order.id).update(
            #             outbound_time=parcle_track.actual_time)
            orders.append(assemble_delivery_certificate_params(parcel_order))

        request.data['base64'] = create_shipment_proof(orders)
        request.data['msg'] = _('打印成功！')
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    # create_proof_of_delivery
    @action(methods=['POST'], detail=False)
    def print_delivery_label(self, request):
        """
        打印韩进签收证明
        """
        ids = request.data['ids']
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(
            id__in=ids,
            order_status__in=['SF'],
            del_flag=False
        )
        if not parcel_customer_order_list:
            raise ParamError(_('请选择已出库的订单'), ErrorCode.PARAM_ERROR)

        orders = []
        for parcel_order in parcel_customer_order_list:
            # 兼容无发货时间的
            # if not parcel_order.outbound_time:
            #     parcle_track = ParcelTrack.objects.filter(order_num=parcel_order.order_num, track_code='outbound',
            #                                               del_flag=False).first()
            #     if parcle_track:
            #         parcel_order.outbound_time = parcle_track.actual_time
            #         ParcelCustomerOrder.objects.filter(id=parcel_order.id).update(
            #             outbound_time=parcle_track.actual_time)
            orders.append(assemble_delivery_certificate_params(parcel_order))

        request.data['base64'] = create_proof_of_delivery(orders)
        request.data['msg'] = '打印成功！'
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def print_shipping_label(self, request):
        """
        打印发货证明
        :param request:
        :return:
        """
        ids = request.data['ids']
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(
            id__in=ids,
            order_status__in=['OUTBOUND', 'SF'],
            del_flag=False
        )
        if not parcel_customer_order_list:
            raise ParamError(_('请选择已出库的订单'), ErrorCode.PARAM_ERROR)

        orders = []
        for parcel_order in parcel_customer_order_list:
            # 兼容无发货时间的
            if not parcel_order.outbound_time:
                parcle_track = ParcelTrack.objects.filter(order_num=parcel_order.order_num, track_code='outbound',
                                                          del_flag=False).first()
                if parcle_track:
                    parcel_order.outbound_time = parcle_track.actual_time
                    ParcelCustomerOrder.objects.filter(id=parcel_order.id).update(
                        outbound_time=parcle_track.actual_time)
            orders.append(assemble_delivery_certificate_params(parcel_order))

        request.data['base64'] = create_shipping_label(orders)
        request.data['msg'] = _('打印成功！')
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_handle_times(self, request):
        """
        修改抓单任务的处理次数
        :param request:
        :return:
        """
        ParcelOrderLabelTask.objects.filter(id=request.data['id']).update(handle_times=request.data['num'])
        return success_response(request, _("修改成功！"))

    # api预估费用 给客户调，不能出现成本
    @action(methods=['POST'], detail=False)
    def api_pre_fee(self, request):
        product_code = request.data['service_code']
        if not product_code:
            return fail_response(request, _('查无此产品') + product_code + ',请输入正常产品')
        product_queryset = Product.objects.filter(code=product_code, del_flag=False)
        if product_queryset.count() == 0:
            return fail_response(request, _('查无此产品') + product_code + ',请输入正常产品')

        product = product_queryset.first()

        sender_country = request.data['sender_country']
        sender_postcode = request.data['sender_postcode']

        receive_country = request.data['receive_country']
        receive_postcode = request.data['receive_postcode']

        parcels = request.data['parcels']

        parcelList = []
        for parcel in parcels:
            parcelList.append({
                'weight': Decimal(parcel['parcel_weight']),
                'length': Decimal(parcel['parcel_length']),
                'width': Decimal(parcel['parcel_width']),
                'height': Decimal(parcel['parcel_height']),
                'parcel_weight': Decimal(parcel['parcel_weight']),
                'volume': (Decimal(parcel['parcel_length']) * Decimal(parcel['parcel_width']) * Decimal(
                    parcel['parcel_height'])) / 1000000,
                'parcel_qty': 1
            })

        order_calc_vo = OrderCalcVo()
        order_calc_vo.product = product

        start_dict_vo = DistCalcVo()
        start_dict_vo.countryCode = sender_country
        start_dict_vo.postCode = sender_postcode
        order_calc_vo.startDistVo = start_dict_vo
        end_dict_vo = DistCalcVo()
        end_dict_vo.countryCode = receive_country
        end_dict_vo.postCode = receive_postcode
        order_calc_vo.endDistVo = end_dict_vo
        order_calc_vo.parcels = parcelList
        # order_calc_vo.customer = 'test'
        order_calc_vo.calcDate = datetime.now()
        # order_calc_vo.purpose_code = ''

        results = []
        if settings.SYSTEM_VERSION == 'V2':
            calc_zone = get_product_calc_zone(None, product, receive_country, receive_postcode)
            revenue_results = revenue_calc_v2(order_calc_vo, product, calc_zone)
            results = revenue_results
        else:
            if product.is_valuation:
                revenue_results = revenue_calc(order_calc_vo, product, False)
                results = revenue_results

        result = []
        for revenue_result in results:
            result.append({
                'result_fee': revenue_result.result_fee,
                'currency': revenue_result.currency,
                'charge_name': revenue_result.charge_name,
                # 'base_msg': ",".join(revenue_result.base_msgs),
            })

        data = {}
        data['data'] = result
        data['msg'] = ''
        data['code'] = 200

        return Response(data=data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def pre_fee(self, request):

        product_code = request.data['service_code']
        customer_code = request.data['customer_code']
        if not product_code:
            return fail_response(request, _('查无此产品') + product_code + ',请输入正常产品')
        product_queryset = Product.objects.filter(code=product_code, del_flag=False)
        if product_queryset.count() == 0:
            return fail_response(request, _('查无此产品') + product_code + ',请输入正常产品')

        product = product_queryset.first()

        sender_country = request.data['sender_country']
        sender_postcode = request.data['sender_postcode']

        receive_country = request.data['receive_country']
        receive_postcode = request.data['receive_postcode']

        confirm_charge_weight = request.data.get('confirm_charge_weight')
        shipper = request.data.get('shipper')
        confirm_volume = request.data.get('confirm_volume')

        parcels = request.data['parcels']

        parcelList = []
        for parcel in parcels:
            parcelList.append({
                'weight': Decimal(parcel['parcel_weight']),
                'length': Decimal(parcel['parcel_length']),
                'width': Decimal(parcel['parcel_width']),
                'height': Decimal(parcel['parcel_height']),
                'volume': (Decimal(parcel['parcel_length']) * Decimal(parcel['parcel_width']) * Decimal(
                    parcel['parcel_height'])) / 1000000,
                'parcel_qty': 1
            })

        if customer_code:
            customer = Company.objects.filter(short_name=customer_code, del_flag=False).first()

        order_calc_vo = OrderCalcVo()
        order_calc_vo.product = product

        start_dict_vo = DistCalcVo()
        start_dict_vo.countryCode = sender_country
        start_dict_vo.postCode = sender_postcode
        order_calc_vo.startDistVo = start_dict_vo
        end_dict_vo = DistCalcVo()
        end_dict_vo.countryCode = receive_country
        end_dict_vo.postCode = receive_postcode
        order_calc_vo.endDistVo = end_dict_vo
        order_calc_vo.parcels = parcelList
        order_calc_vo.customer = customer
        order_calc_vo.calcDate = datetime.now()
        # order_calc_vo.purpose_code = ''
        order_calc_vo.confirm_charge_weight = confirm_charge_weight
        order_calc_vo.shipper = shipper
        order_calc_vo.confirm_volume = confirm_volume

        results = []
        if product.is_valuation:
            revenue_results = revenue_calc(order_calc_vo, product, True)
            results = revenue_results

        default_product = None
        base_msgs = []
        if product.is_cost_valuation:
            base_msgs.append(f'<br>成本计算: <br> 产品名称: {product.name} ,  产品编码: {product.code}')
            # 如果是虚拟产品，成本最低策略
            if product.is_virtual and product.strategy_type == 'COST_LOW':
                base_msgs.append(
                    f'<br>产品名称: {product.name} 是虚拟产品，并按成本最低策略计算, 收件人国家:{receive_country}')
                cost_results, default_product = handler_virtual_calc(order_calc_vo, product, None, base_msgs, True)
                results = results + cost_results
            else:
                cost_results = cost_calc(order_calc_vo, product)
                results = results + cost_results

        if not product.is_cost_valuation and product.is_virtual and product.strategy_type in ['MULTI_CHANNEL']:
            base_msgs.append(
                f'<br>产品名称: {product.name} 是虚拟产品，并按多渠道策略计算, 收件人国家:{receive_country}')
            cost_results, default_product = handler_virtual_calc_multi_channel(order_calc_vo, product, 'REPLACE_ORDER',
                                                                               base_msgs, True)
            results = results + cost_results

        result = []
        for revenue_result in results:
            result.append({
                'result_fee': revenue_result.result_fee,
                'currency': revenue_result.currency,
                'charge_name': revenue_result.charge_name,
                'base_msg': ",".join(revenue_result.base_msgs),
            })

        if default_product:
            result.append({
                # 'result_fee': '',
                # 'currency': '',
                # 'charge_name': '',
                'base_msg': f'最终成本最低的产品是：{default_product.name}',
            })

        data = {}
        data['data'] = result
        data['msg'] = ''
        data['code'] = 200

        return Response(data=data, status=status.HTTP_200_OK)

    # 获取parcelCalcPriceInfo
    @action(methods=['POST'], detail=False)
    def api_get_parcel_calc_price_info(self, request):
        order_num = request.data.get('order_num', None)
        order, parcel_li = None, []
        if not order_num:
            return fail_response(request, _('订单号不能为空'))
        if is_parcel_customer_order(order_num):
            order = ParcelCustomerOrder.objects.filter(order_num=order_num, del_flag=False).first()
            parcel_li = ParcelOrderParcel.objects.filter(customer_order=order, del_flag=False)
        elif order_num.startswith(settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK):
            # else:
            order = CustomerOrder.objects.filter(order_num=order_num, del_flag=False).first()
            parcel_li = Parcel.objects.filter(customer_order=order, del_flag=False)
        if not order:
            return fail_response(request, _('请输入正确的订单号'))

        parcels = []
        for i in parcel_li:
            parcel = {
                'parcel_weight': i.parcel_weight,
                'parcel_length': i.parcel_length,
                'parcel_width': i.parcel_width,
                'parcel_height': i.parcel_height
            }
            parcels.append(parcel)
        confirm_charge_weight = shipper = confirm_volume = None
        if isinstance(order, CustomerOrder):
            confirm_charge_weight = order.confirm_charge_weight
            confirm_volume = order.confirm_volume
            shipper = order.shipper
        data = {
            'data': {'receive_country': order.buyer_country_code,
                     'receive_postcode': order.buyer_postcode,
                     'sender_postcode': order.warehouse_code.postcode
                     if hasattr(order, 'warehouse_code') and order.warehouse_code and order.warehouse_code.postcode
                     else (order.shipper.postcode if hasattr(order, 'shipper')
                                                     and order.shipper and order.shipper.postcode else order.postcode),
                     'sender_country': order.warehouse_code.country_code
                     if hasattr(order, 'warehouse_code') and order.warehouse_code and
                        order.warehouse_code.country_code else
                     (order.shipper.country_code if hasattr(order, 'shipper') and order.shipper
                                                    and order.shipper.country_code else order.country_code),
                     'parcels': parcels, 'service_code': order.product.code, 'order_num': order_num,
                     'confirm_charge_weight': confirm_charge_weight,
                     'confirm_volume': confirm_volume,
                     'shipper': shipper,
                     'customer_code': order.customer.short_name
                     },
            'msg': '', 'code': 200}

        return Response(data=data, status=status.HTTP_200_OK)

    # 获取sendinfo
    @action(methods=['POST'], detail=False)
    def api_get_address(self, request):
        service_code = request.data.get('service_code', None)
        address_num = request.data.get('address_num', None)
        if service_code:
            product_obj = Product.objects.filter(code=service_code, del_flag=False).first()
            if not product_obj:
                return fail_response(request, _('查无此产品') + service_code + ',请输入正常产品')
            address_num = product_obj.address_num

        address_obj = Address.objects.filter(address_num=address_num, del_flag=False).first()
        if not address_obj:
            return fail_response(request, _('查无此地址,请输入正常产品或者地址'))
        data = {}
        data['data'] = {'country_code': address_obj.country_code, 'postcode': address_obj.postcode}
        data['msg'] = ''
        data['code'] = 200

        return Response(data=data, status=status.HTTP_200_OK)

    # 预估费用, 不传产品
    @action(methods=['POST'], detail=False)
    def api_pre_fee_all_product(self, request):
        # ids = request.data['ids']
        user = request.user
        # if len(ids) > 1:
        #     return fail_response(request, "请选择一个订单操作")

        # parcel_customer_order_list = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)
        # if not parcel_customer_order_list.exists():
        #     return fail_response(request, "请选择订单操作")

        # parcel_customer_order = parcel_customer_order_list.first()

        # parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=parcel_customer_order, del_flag=False)
        parcels = request.data['parcels']

        parcel_list = []
        for parcel in parcels:
            parcel_list.append({
                'weight': Decimal(parcel['parcel_weight']),
                'length': Decimal(parcel['parcel_length']),
                'width': Decimal(parcel['parcel_width']),
                'height': Decimal(parcel['parcel_height']),
                'volume': (Decimal(parcel['parcel_length']) * Decimal(parcel['parcel_width']) * Decimal(
                    parcel['parcel_height'])) / 1000000,
            })

        order_calc_vo = OrderCalcVo()

        # warehouse = parcel_customer_order.warehouse_code

        start_dict_vo = DistCalcVo()
        start_dict_vo.countryCode = request.data['sender_country']
        start_dict_vo.postCode = request.data['sender_postcode']
        order_calc_vo.startDistVo = start_dict_vo
        end_dict_vo = DistCalcVo()
        buyer_country_code = request.data['receive_country']
        end_dict_vo.countryCode = buyer_country_code
        end_dict_vo.postCode = request.data['receive_postcode']
        order_calc_vo.endDistVo = end_dict_vo
        order_calc_vo.parcels = parcel_list
        order_calc_vo.customer = user.company
        order_calc_vo.calcDate = datetime.now()

        product_zone_queryset = ProductZone.objects.filter(country_code=buyer_country_code, del_flag=False,
                                                           type='Buyer')
        if not product_zone_queryset.exists():
            return fail_response(request, _("%s此国家未配置产品分区") % buyer_country_code)

        product_route_queryset = ProductRoute.objects.filter(end_zone__in=product_zone_queryset, del_flag=False)
        products = [x.product.id for x in product_route_queryset if x.product]

        product_queryset = Product.objects.filter(id__in=products, type='PC', is_open=True, is_virtual=False,
                                                  del_flag=False)

        success_data = []
        fail_data = []
        for product in product_queryset:
            order_calc_vo.product = product
            try:
                revenue_results = revenue_calc(order_calc_vo, product)
            except Exception as e:
                fail_data.append({'product_name': product.name,
                                  'calc_result': str(e)})
                continue

            calc_result = {
                'product_name': product.name,
                # 'product_code': product.code,
                'total_fee': 0,
                'yf_fee': 0,
                'ryf_fee': 0,
                'pyf_fee': 0,
                'zz_fee': 0,
                'other_fee': 0,
            }
            for revenue_result in revenue_results:
                calc_result['currency'] = revenue_result.currency
                calc_result['total_fee'] = calc_result['total_fee'] + revenue_result.result_fee
                if revenue_result.charge_code in ['YF', 'PSF']:
                    calc_result['yf_fee'] = calc_result['yf_fee'] + revenue_result.result_fee
                elif revenue_result.charge_code == 'RYF':
                    calc_result['ryf_fee'] = calc_result['ryf_fee'] + revenue_result.result_fee
                elif revenue_result.charge_code == 'PYF':
                    calc_result['pyf_fee'] = calc_result['pyf_fee'] + revenue_result.result_fee
                elif revenue_result.charge_code == 'SRZZF':
                    calc_result['zz_fee'] = calc_result['zz_fee'] + revenue_result.result_fee
                else:
                    calc_result['other_fee'] = calc_result['other_fee'] + revenue_result.result_fee

            success_data.append(calc_result)

        res_data = {
            'success_data': success_data,
            'fail_data': fail_data,
            'msg': '',
            'code': 200
        }
        return Response(data=res_data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    @transaction.atomic
    def api_create_container(self, request):
        """创建容器接口"""
        is_repack = request.data.get('is_repack', True)
        order_nums = request.data.get('order_nums')
        customer_order_nums = request.data.get('customer_order_nums')
        # transfer_info = request.data.get('transferInfo', {})  # 交接地址
        container_info = request.data.get('containerInfo', {})  # 容器信息
        user = request.user
        # if not transfer_info:
        #     return fail_response(request, '请填写交接地址！')
        if not container_info:
            return fail_response(request, _('请填写容器信息！'))
        if order_nums:
            queryset = ParcelCustomerOrder.objects.filter(order_num__in=order_nums, customer=user.company,
                                                          product__isnull=False,
                                                          del_flag=False)
        elif customer_order_nums:
            queryset = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                          customer_order_num__in=customer_order_nums,
                                                          customer=user.company,
                                                          product__isnull=False,
                                                          del_flag=False)
        product_num = queryset.values('product').distinct().count()
        if order_nums:
            if queryset.count() != len(order_nums) or product_num != 1:
                return fail_response(request, _('请确保所有小包单都有产品并且所选小包单都是相同的产品！'))
        elif customer_order_nums:
            if queryset.count() != len(customer_order_nums) or product_num != 1:
                return fail_response(request, _('请确保所有小包单都有产品并且所选小包单都是相同的产品！'))
        # 已绑定容器的不解绑重新绑定
        if is_repack is False:
            queryset = queryset.filter(big_parcel__isnull=True).all()
            if not queryset.exists():
                return fail_response(request, _('除已绑定不解绑的小包单外，没有待绑定的小包单！'))

        product = queryset.first().product

        big_parcel = BigParcel.objects.create(
            parcel_weight=container_info['weight'],
            parcel_length=container_info['length'],
            parcel_width=container_info['width'],
            parcel_height=container_info['height'],
            # zone=transfer_info['country_code'],
        )

        # if transfer_info:
        #     defaults = transfer_info
        #     defaults['address_type'] = 'TD'
        #     ParcelOrderAddress.objects.update_or_create(big_parcel=big_parcel, defaults=defaults)

        queryset.update(big_parcel=big_parcel)

        parcel_order_num = ParcelCustomerOrder.objects.filter(big_parcel=big_parcel, del_flag=False).count()
        big_parcel.parcel_num = 'BP' + create_order_num(big_parcel.id) + (big_parcel.zone or '')
        big_parcel.product = product
        big_parcel.parcel_qty = parcel_order_num
        big_parcel.is_link_parcel_order = True
        big_parcel.update_by = get_update_params(request)['update_by']
        big_parcel.update_date = get_update_params(request)['update_date']
        big_parcel.save()
        request.data['container_id'] = big_parcel.parcel_num
        return success_response(request, _('配置成功'))

    @action(methods=['POST'], detail=False)
    @transaction.atomic
    def api_cancel_container(self, request):
        """取消容器接口"""
        user = request.user
        try:
            relieve_type = request.data['relieve_type']
            container_id = str(request.data['container_id']).strip()
            order_num = str(request.data.get('order_num')).strip() if request.data.get('order_num') else None
            customer_order_num = str(request.data.get('customer_order_num')).strip() if request.data.get(
                'customer_order_num') else None

            # 参数检查
            big_parcel = BigParcel.objects.get(parcel_num=container_id, del_flag=False)
            if not big_parcel:
                raise ObjectDoesNotExist(_("大包单号不存在"))
            if relieve_type == 1:
                # 更新小包单
                ParcelCustomerOrder.objects.filter(
                    del_flag=False, big_parcel=big_parcel, customer=user.company
                ).update(
                    big_parcel=None, update_date=datetime.now()
                )

                # 移除大包单
                BigParcel.objects.filter(
                    parcel_num=container_id, del_flag=False
                ).update(
                    audit_weight=0, parcel_qty=0, del_flag=True
                )
            else:
                user = request.user
                if order_num:
                    pco = ParcelCustomerOrder.objects.get(order_num=order_num, customer=user.company, del_flag=False,
                                                          big_parcel=big_parcel)
                elif customer_order_num:
                    pco = ParcelCustomerOrder.objects.get(~Q(order_status='VO'), customer_order_num=customer_order_num,
                                                          customer=user.company, del_flag=False,
                                                          big_parcel=big_parcel)
                if not pco:
                    raise ObjectDoesNotExist(_("小包订单号不存在"))

                # 修改数据库
                lose_qty = big_parcel.parcel_qty - 1
                lose_weight = big_parcel.audit_weight - pco.weight
                if order_num:
                    # 使用 update 方法同时更新多个对象的相同字段
                    ParcelCustomerOrder.objects.filter(
                        order_num=order_num, del_flag=False, big_parcel=big_parcel
                    ).update(
                        big_parcel=None, update_date=datetime.now()
                    )
                elif customer_order_num:
                    ParcelCustomerOrder.objects.filter(
                        customer_order_num=customer_order_num, del_flag=False, big_parcel=big_parcel
                    ).update(
                        big_parcel=None, update_date=datetime.now()
                    )

                BigParcel.objects.filter(
                    parcel_num=container_id, del_flag=False
                ).update(
                    audit_weight=lose_weight, parcel_qty=lose_qty
                )

            request.data['msg'] = _('取消成功')
            request.data['code'] = 200
            return Response(data=request.data, status=status.HTTP_200_OK)

        except ObjectDoesNotExist as e:
            logger.error(e)
            request.data['msg'] = str(e)
            request.data['code'] = 404
            return Response(data=request.data, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:  # 捕获其他异常
            # 记录日志或其他错误处理逻辑
            logger.error(e)
            request.data['msg'] = str(e)
            request.data['code'] = 500
            return Response(data=request.data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['POST'], detail=False)
    def api_print_container(self, request):
        """打印文件接口"""
        container_ids = request.data['container_ids']

        big_parcel_list = BigParcel.objects.filter(parcel_num__in=container_ids, del_flag=False)
        if big_parcel_list.count() == 0:
            request.data['msg'] = _('未找到大包订单,请确认')
            request.data['code'] = 400
            return Response(data=request.data, status=status.HTTP_200_OK)

        orders = []
        for big_parcel in big_parcel_list:

            customer_order_num = ''
            if big_parcel.customer_order:
                customer_order_num = big_parcel.customer_order.order_num
            product = ''
            if big_parcel.product:
                product = big_parcel.product.code

            order = {
                "outboundorder_num": big_parcel.parcel_num,
                "weight": big_parcel.parcel_weight,
                "country_code": big_parcel.zone,
                "product_code": product,
                "pices": big_parcel.parcel_qty,
                "order_no": customer_order_num,
                "big_order_no": big_parcel.outbound_num or '',
            }
            orders.append(order)

        request.data['base64'] = batch_create_barcodes_for_big_parcelorder(orders)
        request.data['msg'] = _('打印成功！')
        request.data['code'] = 200
        return Response(data=request.data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    @transaction.atomic
    def api_create_gxjh(self, request):
        """创建干线交航接口"""
        # is_repack = request.data.get('is_repack', True)
        # order_nums = request.data.get('order_nums')
        # transfer_info = request.data.get('transferInfo', {})  # 交接地址
        # container_info = request.data.get('containerInfo', {})  # 容器信息
        #
        # if not transfer_info:
        #     return fail_response(request, '请填写交接地址！')
        # if not container_info:
        #     return fail_response(request, '请填写容器信息！')
        #
        # queryset = ParcelCustomerOrder.objects.filter(order_num__in=order_nums, product__isnull=False, del_flag=False)
        # product_num = queryset.values('product').distinct().count()
        # if queryset.count() != len(order_nums) or product_num != 1:
        #     return fail_response(request, '请确保所有小包单都有产品并且所选小包单都是相同的产品！')
        # # 已绑定容器的不解绑重新绑定
        # if is_repack is False:
        #     queryset = queryset.filter(big_parcel__isnull=True).all()
        #     if not queryset.exists():
        #         return fail_response(request, '除已绑定不解绑的小包单外，没有待绑定的小包单！')
        #
        # product = queryset.first().product
        #
        # big_parcel = BigParcel.objects.create(
        #     parcel_weight=container_info['weight'],
        #     parcel_length=container_info['length'],
        #     parcel_width=container_info['width'],
        #     parcel_height=container_info['height'],
        #     zone=transfer_info['country_code'],
        # )
        #
        # if transfer_info:
        #     defaults = transfer_info
        #     defaults['address_type'] = 'TD'
        #     ParcelOrderAddress.objects.update_or_create(big_parcel=big_parcel, defaults=defaults)
        #
        # queryset.update(big_parcel=big_parcel)
        #
        # parcel_order_num = ParcelCustomerOrder.objects.filter(big_parcel=big_parcel, del_flag=False).count()
        # big_parcel.parcel_num = 'BP' + create_order_num(big_parcel.id) + (big_parcel.zone or '')
        # big_parcel.product = product
        # big_parcel.parcel_qty = parcel_order_num
        # big_parcel.is_link_parcel_order = True
        # big_parcel.update_by = get_update_params(request)['update_by']
        # big_parcel.update_date = get_update_params(request)['update_date']
        # big_parcel.save()
        # request.data['container_id'] = big_parcel.parcel_num
        return success_response(request, _('暂未开放'))

    # 导出小包订单
    @action(methods=['POST'], detail=False)
    def download_excel(self, request):
        '''
        导出订单数据
        :param request:
        :return:
        '''
        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError(_('订单必选'), ErrorCode.PARAM_ERROR)

        wb = Workbook()
        # 创建一个sheet
        sheet_name = '订单数据'
        w = wb.create_sheet(sheet_name, 0)

        title_dict = [
            "客户", "运输单", "出货单", "订单号", "订单状态", "客户订单号",
            "入库时间（称重时间）", "出库时间", "产品名称", "产品编码", "邮政单号", "尾程派送单号", "供应商单号",
            "大包号", "核重（仓库称重）", "中文品名", "英文品名", "海关编码", "商品重量",
            "商品数量", "商品申报单价", "币种", "下单时间", "大包重量", "大包长", "大包宽",
            "大包高", "包裹长", "包裹宽", "包裹高", "VAT No.", "收货人",
            "收件地址1", "收件地址2", "收件城市", "收件州", "收件电话", "收件邮编", "收件国家", "门牌号", "操作员",
            "计费重转换率",
            '计费重量', '转单状态', '转单信息', '是否拦截', '拦截原因'
        ]
        # 填充标题
        for i, title in enumerate(title_dict):
            w.cell(row=1, column=i + 1).value = title
            # 设置单元格格式
            cell = w.cell(row=1, column=i + 1)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.font = Font(size=15, bold=True)
            w.column_dimensions[w.cell(row=1, column=i + 1).column_letter].width = 30

        excel_row = 2
        for id in ids:
            customerOrder = ParcelCustomerOrder.objects.get(id=id)
            pick_record_queryset = PickRecord.objects.filter(del_flag=False,
                                                             order_num=customerOrder.order_num).order_by('-update_date')
            label_task = ParcelOrderLabelTask.objects.filter(order_num_id=id, del_flag=False).first()
            label_status = dict(ParcelOrderLabelTask.STATUS).get(label_task.status) if label_task else '未查到相关面单任务'
            label_desc = label_task.label_desc if label_task else '未查到相关面单任务'
            operator = None
            if pick_record_queryset.count() > 0:
                operator = pick_record_queryset.first().user

            ci = 1
            # 客户
            # 根据配置决定导出的是客户代码还是客户全称
            has_simplified_display = Dict.objects.filter(value='simplified_display', del_flag=False).exists()
            if get(customerOrder, 'customer.name'):
                if has_simplified_display:
                    self.setData(ci, customerOrder.customer.short_name, excel_row, w)
                else:
                    self.setData(ci, customerOrder.customer.name, excel_row, w)
                # self.setData(ci, customerOrder.customer.name, excel_row, w)
            ci += 1
            # 运输单
            if get(customerOrder, 'big_parcel.parcel_outbound_order.customer_order.order_num'):
                self.setData(ci, customerOrder.big_parcel.parcel_outbound_order.customer_order.order_num, excel_row, w)
            ci += 1

            # 出货单
            if get(customerOrder, 'big_parcel.parcel_outbound_order.outbound_num'):
                self.setData(ci, customerOrder.big_parcel.parcel_outbound_order.outbound_num, excel_row, w)
            ci += 1

            # 订单号
            self.setData(ci, customerOrder.order_num, excel_row, w)
            ci += 1

            # 订单状态
            order_status_map = dict(ParcelCustomerOrder.STATUS)
            self.setData(ci, order_status_map.get(customerOrder.order_status), excel_row, w)
            ci += 1

            # 客户订单号
            self.setData(ci, customerOrder.customer_order_num, excel_row, w)
            ci += 1

            # 入库时间（称重时间）
            self.setData(ci, customerOrder.inbound_time, excel_row, w)
            ci += 1

            # # 出库时间
            # if get(customerOrder, 'big_parcel.ship_time'):
            #     self.setData(ci, customerOrder.big_parcel.ship_time, excel_row, w)
            self.setData(ci, customerOrder.outbound_time, excel_row, w)
            ci += 1

            # 产品名称
            if customerOrder.product:
                self.setData(ci, customerOrder.product.name, excel_row, w)
            ci += 1
            # 产品编码
            if customerOrder.product:
                self.setData(ci, customerOrder.product.code, excel_row, w)
            ci += 1

            # 邮政单号
            self.setData(ci, customerOrder.label_billid, excel_row, w)
            ci += 1

            # 尾程派送单号
            self.setData(ci, get(customerOrder, 'tracking_num'), excel_row, w)
            ci += 1

            # 供应商单号
            self.setData(ci, get(customerOrder, 'third_orderNo'), excel_row, w)
            print(get(customerOrder, 'third_orderNo'))
            ci += 1

            # 大包号
            try:
                if customerOrder.big_parcel:
                    self.setData(ci, customerOrder.big_parcel.parcel_num, excel_row, w)
            except Exception as e:
                logger.info(traceback.format_exc())

            ci += 1

            #  核重（仓库称重）
            self.setData(ci, customerOrder.weighing_weight, excel_row, w)
            ci += 1

            parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=customerOrder, del_flag=False)
            item_queryset = ParcelOrderItem.objects.filter(parcel_num__in=parcel_queryset, del_flag=False)
            if item_queryset:
                item = item_queryset.first()
                # 中文品名
                self.setData(ci, item.declared_nameCN, excel_row, w)
                ci += 1

                # 英文品名
                self.setData(ci, item.declared_nameEN, excel_row, w)
                ci += 1

                # 海关编码
                self.setData(ci, item.customs_code, excel_row, w)
                ci += 1

                # 商品重量
                self.setData(ci, item.item_weight, excel_row, w)
                ci += 1

                # 商品数量
                self.setData(ci, item.item_qty, excel_row, w)
                ci += 1

                # 商品申报单价
                self.setData(ci, item.declared_price, excel_row, w)
                ci += 1

                # 币种
                self.setData(ci, item.declared_currency, excel_row, w)
                ci += 1
            else:
                ci += 7
            # 下单时间
            self.setData(ci, customerOrder.order_time, excel_row, w)
            ci += 1
            # 大包重量
            self.setData(ci, get(customerOrder, 'big_parcel.parcel_weight'), excel_row, w)
            ci += 1
            # 大包长
            self.setData(ci, get(customerOrder, 'big_parcel.parcel_length'), excel_row, w)
            ci += 1
            # 大包宽
            self.setData(ci, get(customerOrder, 'big_parcel.parcel_width'), excel_row, w)
            ci += 1
            # 大包高
            self.setData(ci, get(customerOrder, 'big_parcel.parcel_height'), excel_row, w)
            ci += 1
            if parcel_queryset:
                parcelq = parcel_queryset.first()
                # 包裹长
                self.setData(ci, parcelq.parcel_length, excel_row, w)
                ci += 1
                # 包裹宽
                self.setData(ci, parcelq.parcel_width, excel_row, w)
                ci += 1
                # 包裹高
                self.setData(ci, parcelq.parcel_height, excel_row, w)
                ci += 1
            else:
                ci += 3

            # VAT No.
            if customerOrder.warehouse_code:
                self.setData(ci, customerOrder.vat_num, excel_row, w)
            ci += 1
            # 收货人
            self.setData(ci, customerOrder.buyer_name, excel_row, w)
            ci += 1
            # 收件地址1
            self.setData(ci, customerOrder.buyer_address_one, excel_row, w)
            ci += 1
            # 收件地址2
            self.setData(ci, customerOrder.buyer_address_two, excel_row, w)
            ci += 1
            # 收件城市
            self.setData(ci, customerOrder.buyer_city_code, excel_row, w)
            ci += 1
            # 收件州
            self.setData(ci, customerOrder.buyer_state, excel_row, w)
            ci += 1
            # 收件电话
            self.setData(ci, customerOrder.buyer_phone, excel_row, w)
            ci += 1
            # 收件邮编
            self.setData(ci, customerOrder.buyer_postcode, excel_row, w)
            ci += 1
            # 收件国家
            self.setData(ci, customerOrder.buyer_country_code, excel_row, w)
            ci += 1
            # 门牌号
            self.setData(ci, customerOrder.buyer_house_num or '', excel_row, w)
            ci += 1
            # 操作员
            self.setData(ci, get(operator, 'username'), excel_row, w)
            ci += 1
            # 计费重转换率
            self.setData(ci, customerOrder.charge_trans, excel_row, w)
            ci += 1
            # 计费重量
            self.setData(ci, customerOrder.charge_weight, excel_row, w)
            ci += 1
            # 转单状态
            self.setData(ci, label_status, excel_row, w)
            ci += 1
            # 转单信息
            self.setData(ci, label_desc, excel_row, w)
            ci += 1
            # 是否拦截
            self.setData(ci, '是' if customerOrder.intercept_mark else '否', excel_row, w)
            ci += 1
            # 拦截原因
            self.setData(ci, customerOrder.remark, excel_row, w)
            ci += 1
            excel_row += 1

        response = HttpResponse(content_type='application/msexcel')
        response['Content-Disposition'] = 'attachment;filename=%s.xlsx' % sheet_name
        wb.save(response)
        return response

    @action(methods=['POST'], detail=False)
    def download_excel_by_parcel(self, request):
        '''
        导出订单数据 --- 多包裹（跟踪号取包裹）
        :param request:
        :return:
        '''
        ids = request.data['ids']
        if len(ids) == 0:
            raise ParamError(_('订单必选'), ErrorCode.PARAM_ERROR)

        wb = Workbook()
        # 创建一个sheet
        sheet_name = '订单数据'
        w = wb.create_sheet(sheet_name, 0)

        title_dict = [
            "客户", "运输单", "出货单", "订单号", "订单状态", "客户订单号",
            "入库时间（称重时间）", "出库时间", "产品名称", "产品编码", "邮政单号", "尾程派送单号",
            "大包号", "核重（仓库称重）", "中文品名", "英文品名", "海关编码", "商品重量",
            "商品数量", "商品申报单价", "币种", "下单时间", "大包重量", "大包长", "大包宽",
            "大包高", "包裹长", "包裹宽", "包裹高", "VAT No.", "收货人",
            "收件地址1", "收件地址2", "收件城市", "收件州", "收件电话", "收件邮编", "收件国家", "门牌号", "操作员",
            "计费重转换率",
            '计费重量', '转单状态', '转单信息', '是否拦截', '拦截原因'
        ]
        # 填充标题
        for i, title in enumerate(title_dict):
            w.cell(row=1, column=i + 1).value = title
            # 设置单元格格式
            cell = w.cell(row=1, column=i + 1)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.font = Font(size=15, bold=True)
            w.column_dimensions[w.cell(row=1, column=i + 1).column_letter].width = 30

        excel_row = 2
        for id in ids:
            customerOrder = ParcelCustomerOrder.objects.get(id=id)
            pick_record_queryset = PickRecord.objects.filter(del_flag=False,
                                                             order_num=customerOrder.order_num).order_by('-update_date')
            label_task = ParcelOrderLabelTask.objects.filter(order_num_id=id, del_flag=False).first()
            label_status = dict(ParcelOrderLabelTask.STATUS).get(label_task.status) if label_task else '未查到相关面单任务'
            label_desc = label_task.label_desc if label_task else '未查到相关面单任务'
            operator = None
            if pick_record_queryset.count() > 0:
                operator = pick_record_queryset.first().user

            ci = 1
            # 客户
            if get(customerOrder, 'customer.name'):
                self.setData(ci, customerOrder.customer.name, excel_row, w)
            ci += 1
            # 运输单
            if get(customerOrder, 'big_parcel.parcel_outbound_order.customer_order.order_num'):
                self.setData(ci, customerOrder.big_parcel.parcel_outbound_order.customer_order.order_num, excel_row, w)
            ci += 1

            # 出货单
            if get(customerOrder, 'big_parcel.parcel_outbound_order.outbound_num'):
                self.setData(ci, customerOrder.big_parcel.parcel_outbound_order.outbound_num, excel_row, w)
            ci += 1

            # 订单号
            self.setData(ci, customerOrder.order_num, excel_row, w)
            ci += 1

            # 订单状态
            order_status_map = dict(ParcelCustomerOrder.STATUS)
            self.setData(ci, order_status_map.get(customerOrder.order_status), excel_row, w)
            ci += 1

            # 客户订单号
            self.setData(ci, customerOrder.customer_order_num, excel_row, w)
            ci += 1

            # 入库时间（称重时间）
            self.setData(ci, customerOrder.inbound_time, excel_row, w)
            ci += 1

            # # 出库时间
            # if get(customerOrder, 'big_parcel.ship_time'):
            #     self.setData(ci, customerOrder.big_parcel.ship_time, excel_row, w)
            self.setData(ci, customerOrder.outbound_time, excel_row, w)
            ci += 1

            # 产品名称
            if customerOrder.product:
                self.setData(ci, customerOrder.product.name, excel_row, w)
            ci += 1
            # 产品编码
            if customerOrder.product:
                self.setData(ci, customerOrder.product.code, excel_row, w)
            ci += 1

            # 邮政单号
            self.setData(ci, customerOrder.label_billid, excel_row, w)
            ci += 1

            # 尾程派送单号
            parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=customerOrder, del_flag=False)
            tracking_nums = [p.tracking_num for p in parcel_queryset if p.tracking_num]
            tracking_num_str = ', '.join(tracking_nums) if tracking_nums else ''
            if tracking_num_str:
                self.setData(ci, tracking_num_str, excel_row, w)
            else:
                self.setData(ci, get(customerOrder, 'tracking_num'), excel_row, w)
            ci += 1

            # 大包号
            try:
                if customerOrder.big_parcel:
                    self.setData(ci, customerOrder.big_parcel.parcel_num, excel_row, w)
            except Exception as e:
                logger.info(traceback.format_exc())

            ci += 1

            #  核重（仓库称重）
            self.setData(ci, customerOrder.weighing_weight, excel_row, w)
            ci += 1

            parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=customerOrder, del_flag=False)
            item_queryset = ParcelOrderItem.objects.filter(parcel_num__in=parcel_queryset, del_flag=False)
            if item_queryset:
                item = item_queryset.first()
                # 中文品名
                self.setData(ci, item.declared_nameCN, excel_row, w)
                ci += 1

                # 英文品名
                self.setData(ci, item.declared_nameEN, excel_row, w)
                ci += 1

                # 海关编码
                self.setData(ci, item.customs_code, excel_row, w)
                ci += 1

                # 商品重量
                self.setData(ci, item.item_weight, excel_row, w)
                ci += 1

                # 商品数量
                self.setData(ci, item.item_qty, excel_row, w)
                ci += 1

                # 商品申报单价
                self.setData(ci, item.declared_price, excel_row, w)
                ci += 1

                # 币种
                self.setData(ci, item.declared_currency, excel_row, w)
                ci += 1
            else:
                ci += 7
            # 下单时间
            self.setData(ci, customerOrder.order_time, excel_row, w)
            ci += 1
            # 大包重量
            self.setData(ci, get(customerOrder, 'big_parcel.parcel_weight'), excel_row, w)
            ci += 1
            # 大包长
            self.setData(ci, get(customerOrder, 'big_parcel.parcel_length'), excel_row, w)
            ci += 1
            # 大包宽
            self.setData(ci, get(customerOrder, 'big_parcel.parcel_width'), excel_row, w)
            ci += 1
            # 大包高
            self.setData(ci, get(customerOrder, 'big_parcel.parcel_height'), excel_row, w)
            ci += 1
            if parcel_queryset:
                parcelq = parcel_queryset.first()
                # 包裹长
                self.setData(ci, parcelq.parcel_length, excel_row, w)
                ci += 1
                # 包裹宽
                self.setData(ci, parcelq.parcel_width, excel_row, w)
                ci += 1
                # 包裹高
                self.setData(ci, parcelq.parcel_height, excel_row, w)
                ci += 1
            else:
                ci += 3

            # VAT No.
            if customerOrder.warehouse_code:
                self.setData(ci, customerOrder.vat_num, excel_row, w)
            ci += 1
            # 收货人
            self.setData(ci, customerOrder.buyer_name, excel_row, w)
            ci += 1
            # 收件地址1
            self.setData(ci, customerOrder.buyer_address_one, excel_row, w)
            ci += 1
            # 收件地址2
            self.setData(ci, customerOrder.buyer_address_two, excel_row, w)
            ci += 1
            # 收件城市
            self.setData(ci, customerOrder.buyer_city_code, excel_row, w)
            ci += 1
            # 收件州
            self.setData(ci, customerOrder.buyer_state, excel_row, w)
            ci += 1
            # 收件电话
            self.setData(ci, customerOrder.buyer_phone, excel_row, w)
            ci += 1
            # 收件邮编
            self.setData(ci, customerOrder.buyer_postcode, excel_row, w)
            ci += 1
            # 收件国家
            self.setData(ci, customerOrder.buyer_country_code, excel_row, w)
            ci += 1
            # 门牌号
            self.setData(ci, customerOrder.buyer_house_num or '', excel_row, w)
            ci += 1
            # 操作员
            self.setData(ci, get(operator, 'username'), excel_row, w)
            ci += 1
            # 计费重转换率
            self.setData(ci, customerOrder.charge_trans, excel_row, w)
            ci += 1
            # 计费重量
            self.setData(ci, customerOrder.charge_weight, excel_row, w)
            ci += 1
            # 转单状态
            self.setData(ci, label_status, excel_row, w)
            ci += 1
            # 转单信息
            self.setData(ci, label_desc, excel_row, w)
            ci += 1
            # 是否拦截
            self.setData(ci, '是' if customerOrder.intercept_mark else '否', excel_row, w)
            ci += 1
            # 拦截原因
            self.setData(ci, customerOrder.remark, excel_row, w)
            ci += 1
            excel_row += 1

        response = HttpResponse(content_type='application/msexcel')
        response['Content-Disposition'] = 'attachment;filename=%s.xlsx' % sheet_name
        wb.save(response)
        return response

    @action(methods=['POST'], detail=False)
    def export_parcel_customer_order(self, request):
        ids = request.data.get('ids')
        create_date = request.data.get('create_date')
        inbound_time = request.data.get('inbound_time')
        account_time = request.data.get('account_time')
        # if len(ids) == 0:
        #     raise ParamError('订单必选', ErrorCode.PARAM_ERROR)
        try:
            wb = async_generate_statement(ids, create_date, inbound_time, account_time)
        except Exception as e:
            logger.error(f'导出小包单数据报错: {traceback.format_exc()}')
            return Response(data={'code': 400, 'msg': str(e)}, status=status.HTTP_200_OK)

        sheet_name = '订单数据'
        response = HttpResponse(content_type='application/msexcel')
        response['Content-Disposition'] = 'attachment;filename=%s.xlsx' % sheet_name
        wb.save(response)
        return response

    # 批量回退订单
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def bulk_rollback_order(self, request):
        user = request.user
        excel = request.FILES.get('file')
        # 打开excel
        wb = xlrd.open_workbook(filename=None, file_contents=excel.read())

        # 获取第一张表
        table = wb.sheets()[0]
        nrows = table.nrows  # 行数
        data_list = []
        for row_index in range(1, nrows):
            row_values = table.row_values(row_index)
            # 打印每一行的值
            bulk_order_num = row_values[0]

            try:
                parcel_customer_order = ParcelCustomerOrder.objects.get(order_num=bulk_order_num, del_flag=False)
            except ParcelCustomerOrder.DoesNotExist:
                parcel_customer_order = None
            handler_data = {'bulk_order_num': bulk_order_num}

            if not parcel_customer_order:
                handler_data['status'] = '未找到大包订单,请确认'
                data_list.append(handler_data)
                continue

            if parcel_customer_order.big_parcel:
                f_msg = _('检测到有关联大包单的订单,请选择强制导入')
                raise ParamError(f"{f_msg}{parcel_customer_order.order_num}" ,
                                 ErrorCode.PARAM_ERROR)

            # 处理回退逻辑
            hander_rollback_order(bulk_order_num, parcel_customer_order, user)

            handler_data['status'] = '处理成功'
            data_list.append(handler_data)

        print(data_list, 'data_list')
        # 打开现有的 Excel 文件
        sheet_name = '小包批量回退数据'
        # 写入标题行
        titles = ['订单号', '回退状态']
        save_bulk_model_obj(data_list, excel, sheet_name, titles, 'RollBack')
        return Response(data={"msg": _('批量回退成功！'), 'code': 200}, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def force_bulk_rollback_order(self, request):
        user = request.user
        excel = request.FILES.get('file')
        # 打开excel
        wb = xlrd.open_workbook(filename=None, file_contents=excel.read())

        # 获取第一张表
        table = wb.sheets()[0]
        nrows = table.nrows  # 行数
        data_list = []
        for row_index in range(1, nrows):
            row_values = table.row_values(row_index)
            # 打印每一行的值
            bulk_order_num = row_values[0]

            try:
                parcel_customer_order = ParcelCustomerOrder.objects.get(order_num=bulk_order_num, del_flag=False)
            except ParcelCustomerOrder.DoesNotExist:
                parcel_customer_order = None
            handler_data = {'bulk_order_num': bulk_order_num}

            if not parcel_customer_order:
                handler_data['status'] = '未找到大包订单,请确认'
                data_list.append(handler_data)
                continue

            # 处理回退逻辑
            hander_rollback_order(bulk_order_num, parcel_customer_order, user)

            handler_data['status'] = '处理成功'
            data_list.append(handler_data)

        print(data_list, 'data_list')
        # 打开现有的 Excel 文件
        sheet_name = '小包批量回退数据'
        # 写入标题行
        titles = ['订单号', '回退状态']
        save_bulk_model_obj(data_list, excel, sheet_name, titles, 'RollBack')
        return Response(data=[{"msg": _('批量回退成功！'), 'code': 200}], status=status.HTTP_200_OK)

    # 获取订单分拣分区
    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def get_order_sorting_zone(self, request):

        order_num = request.data.get('order_num', '')
        if not order_num:
            return fail_response(request, _('订单号必填'))

        parcel_customer_order = ParcelCustomerOrder.objects.filter(order_num=order_num, del_flag=False).first()
        if not parcel_customer_order:
            return fail_response(request, _("%s订单不存在") % order_num)

        buyer_address = ParcelOrderAddress.objects.filter(customer_order=parcel_customer_order, del_flag=False,
                                                          address_type='RC').first()
        if not buyer_address:
            raise ParamError(_('未配置收件地址'), ErrorCode.PARAM_ERROR)

        product_line = parcel_customer_order.product_line

        line_sorting_setting_queryset = LineSortingSetting.objects.filter(line=product_line, del_flag=False)

        sorting_zone = None
        for line_sorting_setting in line_sorting_setting_queryset:
            end_dist_zone = get_zone_code_v2(parcel_customer_order, buyer_address.country_code, buyer_address.postcode,
                                             line_sorting_setting.post_code_name)
            if end_dist_zone:
                sorting_zone = line_sorting_setting.sorting_zone
                break

        if not sorting_zone:
            raise ParamError(_('未找到分拣分区'), ErrorCode.PARAM_ERROR)

        res_data = SortingZoneSerializer(sorting_zone).data

        res_data['line_name'] = product_line.name
        res_data['line_code'] = product_line.code

        request.data['data'] = res_data
        return success_response(request, 'success')

    def setData(self, ci, value, excel_row, w):
        w.cell(row=excel_row, column=ci).value = value
        self.setStyle(excel_row, w, ci)

    # 设置excel字体
    def setStyle(self, excel_row, w, excel_col):
        cell = w.cell(row=excel_row, column=excel_col)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.font = Font(size=12)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def update_remark(self, request):
        ids = request.data.get('ids')
        remark = request.data.get('remark')
        if not ids:
            raise ParamError(_('请先选择对应的订单'), ErrorCode.PARAM_ERROR)
        ParcelCustomerOrder.objects.filter(id__in=ids).update(remark=remark)
        return Response(data={"msg": 'success', 'code': 200}, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def create_parcel_order_extend(self, request):
        # 临时使用方法，用于修复数据
        ids = request.data.get('ids')
        if settings.SYSTEM_MARK not in ('ZJ', 'TEST'):
            raise ParamError(_('此平台不支持操作'), ErrorCode.PARAM_ERROR)
        
        if not ids:
            raise ParamError(_('请先选择对应的订单'), ErrorCode.PARAM_ERROR)
        
        # 查询所有指定的订单
        orders = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)
        if not orders.exists():
            raise ParamError(_('未找到对应的订单'), ErrorCode.PARAM_ERROR)
        
        created_count = 0
        for order in orders:
            # 检查是否已经存在扩展表关联
            if not hasattr(order, 'parcelOrderExtends') or not order.parcelOrderExtends.filter(del_flag=False).exists():
                # 如果没有关联扩展表，则新建一条关联数据
                ParcelOrderExtend.objects.create(
                    customer_order=order,
                    create_by=request.user,
                    update_by=request.user
                )
                created_count += 1
        
        return Response(data={
            "msg": f'成功创建{created_count}条扩展表记录', 
            "code": 200,
            "created_count": created_count
        }, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def update_status(self, request):
        ids = request.data.get('ids')
        new_order_status = request.data.get('new_order_status')
        if not ids:
            raise ParamError(_('请先选择对应的订单'), ErrorCode.PARAM_ERROR)
        ParcelCustomerOrder.objects.filter(id__in=ids).update(order_status=new_order_status)
        return Response(data={"msg": 'success', 'code': 200}, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def update_label_data(self, request):
        """
        更新下单数据
        :param request:
        :return:
        """
        label_task_id = request.data['id']
        user = request.user
        order_label_task = ParcelOrderLabelTask.objects.get(id=label_task_id)
        customer_order = order_label_task.order_num

        if order_label_task.status == 'Success':  # 已经抓单成功，不允许更新
            raise ParamError(_("已经抓单成功，不允许操作！") + str(customer_order.order_num), ErrorCode.PARAM_ERROR)

        if not user.roles.filter(name__in=('超级管理员', '系统管理员')).exists():
            raise ParamError(_("您没有权限操作！") + str(customer_order.order_num), ErrorCode.PARAM_ERROR)

        if customer_order.order_status not in ('DR', 'WO', 'INBOUND'):
            raise ParamError(_("不允许 草稿, 等待作业, 入库 状态的单据更新订单！") + str(customer_order.order_num),
                             ErrorCode.PARAM_ERROR)

        # 已经有了跟踪单号并且单号不是ZH开头，禁止更新
        if customer_order.tracking_num and not customer_order.tracking_num.startswith('ZH'):
            raise ParamError(_("已经有跟踪单号，禁止更新！") + str(customer_order.order_num), ErrorCode.PARAM_ERROR)

        if not order_label_task.third_order_no:  # 没有identifier号，不允许操作
            raise ParamError(_("未生成 唯一确定单号，不允许更新！") + str(customer_order.order_num), ErrorCode.PARAM_ERROR)

        if not customer_order.service:
            raise ParamError(_("此订单未配置服务！") + str(customer_order.order_num), ErrorCode.PARAM_ERROR)

        service = customer_order.service
        product = customer_order.product

        # 如果是拆包
        if order_label_task.parcel:
            parcel_list = [order_label_task.parcel]
            is_unpack = True
        else:
            parcel_list = ParcelOrderParcel.objects.filter(customer_order=customer_order.id, del_flag=False)
            is_unpack = False

        parcel_id_list = [parcel.id for parcel in parcel_list]
        parcel_item_list = ParcelOrderItem.objects.filter(parcel_num__in=parcel_id_list, del_flag=False)

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)

        parcel_customer_order_charge_out_obj = customer_order.parcel_customer_order_charge_out.all()
        if not parcel_customer_order_charge_out_obj.exists():
            raise ParamError(_("成本明细未配置尾程供应商") + str(customer_order.order_num), ErrorCode.PARAM_ERROR)
        parcel_customer_order_charge_out_first = parcel_customer_order_charge_out_obj.first()  # 取任意一条，因为尾程供应商是同一个
        if not parcel_customer_order_charge_out_first:
            raise ParamError(_("无法找到尾程供应商！") + str(customer_order.order_num), ErrorCode.PARAM_ERROR)
        supplier_name = parcel_customer_order_charge_out_first.supplier.short_name
        if supplier_name != 'fastway':  # 暂时只针对 fastway 供应商
            raise ParamError(_("此操作目前仅针对 Fastway！") + str(customer_order.order_num), ErrorCode.PARAM_ERROR)

        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id,
                                                                   del_flag=False)
        supplier_account = supplier_account_list[0]
        class_name = supplier_butt.class_name
        label_order_vo = LabelOrderVo()
        label_order_vo.orderLabelTask = order_label_task
        label_order_vo.supplierAccount = supplier_account
        label_order_vo.customerOrder = customer_order
        label_order_vo.parcelList = parcel_list
        label_order_vo.parcelItemList = parcel_item_list
        label_order_vo.product = product
        label_order_vo.service = service
        label_order_vo.supplier_butt = supplier_butt
        label_order_vo.is_unpack = is_unpack

        # 通过反射实例化对象
        obj = globals()[class_name]()
        result = update_order(obj, label_order_vo)

        if result['code'] == 200:
            return success_response(request, _("更新成功！"))
        else:
            return fail_response(request, _('订单') + customer_order.order_num + _('更新失败！'))

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def change_product_line(self, request):
        """变更路线"""
        product_line_id = request.data.get('product_line_id')
        ids = request.data.get('ids')
        if ids and product_line_id:
            if not ParcelCustomerOrder.objects.filter(id__in=ids).exists():
                data = {'msg': '小包单不存在！', 'code': 400}
                return Response(data=data, status=status.HTTP_200_OK)
            if not ProductLine.objects.filter(id=product_line_id).exists():
                data = {'msg': '路线不存在！', 'code': 400}
                return Response(data=data, status=status.HTTP_200_OK)
            ParcelCustomerOrder.objects.filter(id__in=ids).update(product_line_id=product_line_id)
            data = {'msg': _('变更路线成功！'), 'code': 200}
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {'msg': _('请先勾选小包单和路线数据！'), 'code': 400}
            return Response(data=data, status=status.HTTP_200_OK)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def update_ship_time(self, request):
        """更新发运时间"""
        ids = request.data.get('ids')
        ship_time = request.data.get('ship_time')
        if ids and ship_time:
            parcel_customer_orders = ParcelCustomerOrder.objects.filter(id__in=ids).all()
            ParcelOutboundOrder.objects.filter(
                parcel_outbound_order_big_parcel__big_parcel__in=parcel_customer_orders).update(ship_time=ship_time)
            data = {'msg': _('小包单发运时间更新成功！'), 'code': 200}
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {'msg': _('请先勾选小包单和发运时间！'), 'code': 400}
            return Response(data=data, status=status.HTTP_200_OK)

    @action(detail=False, methods=['post'])
    def batch_delete_drafts(self, request):
        """
        批量删除草稿接口（支持部分删除）
        请求参数格式：{"ids": [1,2,3]}
        """
        ids = request.data.get('ids', [])

        # 参数校验
        if not isinstance(ids, list) or not ids:
            return Response({'msg': _('参数格式错误或ID列表为空'), 'code': 400}, status=status.HTTP_400_BAD_REQUEST)

        # 查询符合删除条件的记录
        valid_objs = self.get_queryset().filter(id__in=ids, order_status='DR')
        count = valid_objs.count()
        valid_ids = set(valid_objs.values_list('id', flat=True))
        invalid_ids = list(set(ids) - valid_ids)  # 计算无效ID

        # 根据是否存在有效记录返回不同响应
        if not valid_objs.exists():
            return Response({'msg': _('没有符合条件的草稿记录'), 'code': 400}, status=status.HTTP_400_BAD_REQUEST)

        # 执行删除操作
        valid_objs.update(del_flag=True)
        f_msg = _("删除成功,删除数量")
        response_data = {
            'msg': f'{f_msg}:{count}',
            'invalid_ids': invalid_ids,
            'deleted_count': valid_objs.count(),
            'code': 200
        }

        return Response(response_data, status=status.HTTP_200_OK)

    @action(detail=False, methods=['post'])
    def api_sync_order_status(self, request):
        ids = request.data.get('ids', [])
        if not isinstance(ids, list) or not any(ids):
            return Response({'msg': _('参数格式错误或ID列表为空'), 'code': 400}, status=status.HTTP_400_BAD_REQUEST)
        label_tasks = ParcelOrderLabelTask.objects.filter(
            order_num_id__in=ids,
            del_flag=False,
            status__in=["Success", "ConfirmLabel", "HandledBy3rdNo"],
            shipment_digest__isnull=False
        ).select_related('order_num')
        fail_order_notices = []
        success_order_nums = []
        if not label_tasks:
            return fail_response(request, msg=_("相关订单未获取到轨迹,请稍后重试"))
        for label_task in label_tasks:
            customer_order = label_task.order_num

            if customer_order.order_status in ["TRANSIT", "SF"]:
                success_order_nums.append(customer_order.order_num)
                continue
            label_order_vo = LabelOrderVo()
            label_order_vo.customerOrder = customer_order
            label_order_vo.orderLabelTask = label_task
            service = label_task.service
            api_setting_queryset = ServiceApiSetting.objects.filter(service=service, del_flag=False)
            if not api_setting_queryset:
                f_msg = _('未配置单号规则 api')
                fail_order_notices.append(f"{customer_order.order_num}{f_msg}")
                continue
            settings_dict = {item.field_name: item.field_value for item in api_setting_queryset}

            supplier_account = SupplierButtAccount(
                url=settings_dict.get('url'),
                auth_id=settings_dict.get('auth_id'),
                auth_pwd=settings_dict.get('auth_pwd')
            )
            label_order_vo.supplierAccount = supplier_account
            label_order_vo.service_dict = settings_dict
            res = AmazonShippingOneClickService().get_trackings(label_order_vo)
            if res.get("msg"):
                f_msg1 = _('亚马逊获取轨迹失败, 订单编号:')
                f_msg2 = _('错误信息:')
                fail_order_notices.append(f"{f_msg1}{customer_order.order_num},{f_msg2}{res.get('msg')}")
            else:
                success_order_nums.append(customer_order.order_num)
        msg = _('同步轨迹')
        if fail_order_notices:
            f_msg1 = _('失败, 订单编号')
            msg += f"{f_msg1}:{arr_to_str(fail_order_notices)}\n"
        if success_order_nums:
            f_msg2 = _('成功, 订单编号')
            msg += f"{f_msg2}:{arr_to_str(success_order_nums)}\n"
        response_data = {
            "msg": msg,
            'code': 200
        }
        return Response(response_data, status=status.HTTP_200_OK)


class ParcelOrderAddressViewSet(CustomViewBase):
    """小包单收发件人信息"""
    queryset = ParcelOrderAddress.objects.all()
    serializer_class = ParcelOrderAddressSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ()
    filterset_fields = ()
    ordering_fields = ('id',)
    authentication_classes = (AlitaJSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)


class ParcelOrderItemViewSet(CustomViewBase):
    """商品信息"""
    queryset = ParcelOrderItem.objects.all()
    serializer_class = ParcelOrderItemSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ()
    filterset_fields = ()
    ordering_fields = ('id',)
    authentication_classes = (AlitaJSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)


def hander_rollback_order(bulk_order_num, parcel_customer_order, user):
    ParcelOrderLabel.objects.filter(order_num=parcel_customer_order, del_flag=False).update(del_flag=True,
                                                                                            update_by=user,
                                                                                            update_date=datetime.now(),
                                                                                            remark='订单回退')
    ParcelOrderLabelTask.objects.filter(order_num=parcel_customer_order, del_flag=False).update(del_flag=True,
                                                                                                update_by=user,
                                                                                                update_date=datetime.now(),
                                                                                                remark='订单回退')
    ParcelOrderParcel.objects.filter(customer_order=parcel_customer_order, del_flag=False).update(tracking_num='',
                                                                                                  update_by=user,
                                                                                                  update_date=datetime.now())
    ParcelCustomerOrder.objects.filter(order_num=bulk_order_num, del_flag=False).update(tracking_num='',
                                                                                        big_parcel=None,
                                                                                        is_weighing=False,
                                                                                        weighing_weight=None,
                                                                                        order_status='WO',
                                                                                        update_by=user,
                                                                                        third_orderNo='',
                                                                                        update_date=datetime.now())
    charge_in_queryset = ParcelOrderChargeIn.objects.filter(customer_order_num=parcel_customer_order, del_flag=False,
                                                            is_system=True)

    ParcelTrack.objects.filter(order_num=bulk_order_num, del_flag=False).update(del_flag=True, remark='订单回退',
                                                                                update_date=datetime.now())

    if charge_in_queryset:
        # 退款给客户
        refund_account(parcel_customer_order, user, ParcelOrderChargeIn)
        charge_in_queryset.update(del_flag=True, remark='回退订单', update_by=user, update_date=datetime.now())
    ParcelOrderChargeOut.objects.filter(customer_order_num=parcel_customer_order, del_flag=False,
                                        is_system=True).update(
        del_flag=True, remark='回退订单', update_by=user, update_date=datetime.now())


# 导出报表数据excel
def async_generate_statement(ids, create_date, inbound_time, account_time):
    logger.info('异步导出报表数据excel start: ')
    if ids:
        order_queryset = ParcelCustomerOrder.objects.filter(id__in=ids, del_flag=False)
    else:
        if not any([create_date, inbound_time, account_time]):
            logger.error('请选择要导出的订单或者选择日期范围')
            raise ParamError(_('请选择要导出的订单或者选择日期范围'), ErrorCode.PARAM_ERROR)
        order_queryset = ParcelCustomerOrder.objects.all()
        if create_date:
            print('create_date-->', create_date, type(create_date))
            # 筛选以周为单位的日期范围
            dt = datetime.strptime(create_date, "%Y-%m-%dT%H:%M:%S.%fZ")
            dt_7_later = dt + timedelta(days=7)
            order_queryset = order_queryset.filter(create_date__gte=dt,
                                                   create_date__lt=dt_7_later,
                                                   del_flag=False)
        if inbound_time:
            dt = datetime.strptime(inbound_time, "%Y-%m-%dT%H:%M:%S.%fZ")
            dt_7_later = dt + timedelta(days=7)
            order_queryset = order_queryset.filter(inbound_time__gte=dt,
                                                   inbound_time__lt=dt_7_later,
                                                   del_flag=False)
        if account_time:
            dt = datetime.strptime(account_time, "%Y-%m-%dT%H:%M:%S.%fZ")
            dt_7_later = dt + timedelta(days=7)
            order_queryset = order_queryset.filter(account_time__gte=dt,
                                                   account_time__lt=dt_7_later,
                                                   del_flag=False)
        if not order_queryset.exists():
            raise ParamError(_('输入的日期范围未查询到小包单'), ErrorCode.PARAM_ERROR)
    wb = Workbook()
    # 创建一个sheet
    sheet_name = '订单数据'
    ws = wb.create_sheet(sheet_name, 0)
    # {'空运主单', '收件人邮箱', 'ioss编码', '商品申报单件'}
    title_dict = [
        '客户', '运输单', '出货单', '订单号', '订单状态', '客户订单号', '空运主单', '入库时间（称重时间）',
        '出库时间', '产品名称', '产品编码', '邮政单号', '尾程派送单号', '大包号', '核重（仓库称重）',
        '中文品名', '英文品名', '海关编码', '商品重量', '商品数量', '商品申报单价', '币种', '下单时间',
        '大包重量', '大包长', '大包宽', '大包高', '包裹长', '包裹宽', '包裹高', '计费重量', '计费重转换率',
        'ioss编码', '收货人', '收件地址1', '收件地址2', '收件城市', '收件州', '收件电话', '收件邮编',
        '收件国家', '是否拦截', '收件人邮箱'
    ]
    # 填充标题
    for i, title in enumerate(title_dict):
        ws.cell(row=1, column=i + 1).value = title
        # 设置单元格格式
        cell = ws.cell(row=1, column=i + 1)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.font = Font(size=12, bold=True)
        ws.column_dimensions[ws.cell(row=1, column=i + 1).column_letter].width = 30

    query_sql = f"""
    -- 订单信息
    SELECT
    cc.NAME 客户,
    c.order_num 运输单,
    p.outbound_num 出货单,
    o.order_num 订单号,
    -- dr 草稿 wo 等待作业 fc 已完成 vo 已作废
    case when o.order_status = 'dr' then '草稿'
    when o.order_status = 'wo' then '等待作业'
    when o.order_status = 'fc' then '已完成'
    when o.order_status = 'vo' then '已作废'
    when o.order_status = 'OUTBOUND' then '已出库'
    else '未知'
    end
    订单状态,
    o.customer_order_num 客户订单号,
    c.master_number "空运主单",
    o.inbound_time "入库时间（称重时间）",
    t1.actual_time "出库时间",
    --  null "状态（已下单或已入库或已出库或已退件）",
    --  null "备注（退件原因、拦截原因）",
    pms_product.`name` 产品名称,
    pms_product.`code` 产品编码,
    o.label_billid 邮政单号,
    o.tracking_num 尾程派送单号,
    b.parcel_num 大包号,
    o.weighing_weight "核重（仓库称重）",
    i.declared_nameCN 中文品名,
    i.declared_nameEN 英文品名,
    i.customs_code 海关编码,
    i.item_weight 商品重量,
    i.item_qty 商品数量,
    i.declared_price 商品申报单价,
    i.declared_currency 币种,
    o.order_time 下单时间,
    b.parcel_weight 大包重量,
    b.parcel_length 大包长,
    b.parcel_width  大包宽,
    b.parcel_height   大包高,
    pa.parcel_length 包裹长,
    pa.parcel_width 包裹宽,
    pa.parcel_height 包裹高,
    o.charge_weight 计费重量,
    o.charge_trans 计费重转换率,
    --  计泡系数 材重 收费重（核重与材重取大值）
    o.ioss_num "ioss编码",
    o.buyer_name 收货人,
    o.buyer_address_one "收件地址1",
    o.buyer_address_two "收件地址2",
    o.buyer_city 收件城市,
    o.buyer_state 收件州,
    o.buyer_phone 收件电话,
    o.buyer_postcode 收件邮编,
    o.buyer_country 收件国家,
    case when o.intercept_mark = '0' then '没有拦截'
    when o.intercept_mark = '1' then '已拦截'
    else '未知'
    end
    是否拦截,
    o.buyer_mail 收件人邮箱

    FROM
    order_parcelorderitem i
    LEFT JOIN order_parcelorderparcel pa ON i.parcel_num_id = pa.id
    LEFT JOIN order_parcelcustomerorder o ON pa.customer_order_id = o.id
    LEFT JOIN order_bigparcel b ON o.big_parcel_id = b.id
    LEFT JOIN order_parceloutboundorder p ON b.parcel_outbound_order_id = p.id
    LEFT JOIN order_customerorder c ON p.customer_order_id = c.id
    LEFT JOIN company_company cc ON o.customer_id = cc.id
    left join pms_product on o.product_id = pms_product.id
    left join order_parceltrack  on order_parceltrack.order_num = o.order_num  and order_parceltrack.track_code = 'inbound'
    left join order_parceltrack  t1 on t1.order_num = o.order_num  and t1.track_code = 'outbound'
    where t1.actual_time >= '2024-06-01' and t1.actual_time < '2024-06-07'
    --  limit 12000
    """

    start_row = 2
    for parcel_customer_order in order_queryset:
        pick_record_queryset = PickRecord.objects.filter(
            del_flag=False,
            order_num=parcel_customer_order.order_num).order_by('-update_date')
        # label_task = ParcelOrderLabelTask.objects.filter(order_num_id=id, del_flag=False).first()
        # label_status = dict(ParcelOrderLabelTask.STATUS).get(label_task.status) if label_task else '未查到相关面单任务'
        # label_desc = label_task.label_desc if label_task else '未查到相关面单任务'
        operator = None
        if pick_record_queryset.count() > 0:
            operator = pick_record_queryset.first().user

        ci = 1
        # 客户
        if get(parcel_customer_order, 'customer.name'):
            set_data(ci, parcel_customer_order.customer.name, start_row, ws)
        ci += 1
        # 运输单
        if get(parcel_customer_order, 'big_parcel.parcel_outbound_order.customer_order.order_num'):
            set_data(ci, parcel_customer_order.big_parcel.parcel_outbound_order.customer_order.order_num, start_row, ws)
        ci += 1

        # 出货单
        if get(parcel_customer_order, 'big_parcel.parcel_outbound_order.outbound_num'):
            set_data(ci, parcel_customer_order.big_parcel.parcel_outbound_order.outbound_num, start_row, ws)
        ci += 1

        # 订单号
        set_data(ci, parcel_customer_order.order_num, start_row, ws)
        ci += 1

        # 订单状态
        order_status_map = dict(ParcelCustomerOrder.STATUS)
        set_data(ci, order_status_map.get(parcel_customer_order.order_status), start_row, ws)
        ci += 1

        # 客户订单号
        set_data(ci, parcel_customer_order.customer_order_num, start_row, ws)
        ci += 1

        # 空运主单
        if get(parcel_customer_order, 'big_parcel.parcel_outbound_order.customer_order.master_num.order_num'):
            set_data(ci, parcel_customer_order.big_parcel.parcel_outbound_order.customer_order.master_num.order_num,
                     start_row, ws)
        ci += 1

        # 入库时间（称重时间）
        set_data(ci, parcel_customer_order.inbound_time, start_row, ws)
        ci += 1

        # 出库时间
        if get(parcel_customer_order, 'big_parcel.ship_time'):
            set_data(ci, parcel_customer_order.big_parcel.ship_time, start_row, ws)
        ci += 1

        # 产品名称
        if parcel_customer_order.product:
            set_data(ci, parcel_customer_order.product.name, start_row, ws)
        ci += 1
        # 产品编码
        if parcel_customer_order.product:
            set_data(ci, parcel_customer_order.product.code, start_row, ws)
        ci += 1

        # 邮政单号
        set_data(ci, parcel_customer_order.label_billid, start_row, ws)
        ci += 1

        # 尾程派送单号
        set_data(ci, get(parcel_customer_order, 'tracking_num'), start_row, ws)
        ci += 1

        # 大包号
        if parcel_customer_order.big_parcel:
            set_data(ci, parcel_customer_order.big_parcel.parcel_num, start_row, ws)
        ci += 1

        #  核重（仓库称重）
        set_data(ci, parcel_customer_order.weighing_weight, start_row, ws)
        ci += 1

        parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=parcel_customer_order, del_flag=False)
        item_queryset = ParcelOrderItem.objects.filter(parcel_num__in=parcel_queryset, del_flag=False)
        if item_queryset:
            item = item_queryset.first()
            # 中文品名
            set_data(ci, item.declared_nameCN, start_row, ws)
            ci += 1

            # 英文品名
            set_data(ci, item.declared_nameEN, start_row, ws)
            ci += 1

            # 海关编码
            set_data(ci, item.customs_code, start_row, ws)
            ci += 1

            # 商品重量
            set_data(ci, item.item_weight, start_row, ws)
            ci += 1

            # 商品数量
            set_data(ci, item.item_qty, start_row, ws)
            ci += 1

            # 商品申报单价
            set_data(ci, item.declared_price, start_row, ws)
            ci += 1

            # 币种
            set_data(ci, item.declared_currency, start_row, ws)
            ci += 1
        else:
            ci += 7
        # 下单时间
        set_data(ci, parcel_customer_order.order_time, start_row, ws)
        ci += 1
        # 大包重量
        set_data(ci, get(parcel_customer_order, 'big_parcel.parcel_weight'), start_row, ws)
        ci += 1
        # 大包长
        set_data(ci, get(parcel_customer_order, 'big_parcel.parcel_length'), start_row, ws)
        ci += 1
        # 大包宽
        set_data(ci, get(parcel_customer_order, 'big_parcel.parcel_width'), start_row, ws)
        ci += 1
        # 大包高
        set_data(ci, get(parcel_customer_order, 'big_parcel.parcel_height'), start_row, ws)
        ci += 1
        if parcel_queryset:
            parcelq = parcel_queryset.first()
            # 包裹长
            set_data(ci, parcelq.parcel_length, start_row, ws)
            ci += 1
            # 包裹宽
            set_data(ci, parcelq.parcel_width, start_row, ws)
            ci += 1
            # 包裹高
            set_data(ci, parcelq.parcel_height, start_row, ws)
            ci += 1
        else:
            ci += 3
        # 计费重量
        set_data(ci, parcel_customer_order.charge_weight, start_row, ws)
        ci += 1
        # 计费重转换率
        set_data(ci, parcel_customer_order.charge_trans, start_row, ws)
        ci += 1
        # ioss编码
        set_data(ci, parcel_customer_order.ioss_num, start_row, ws)
        ci += 1
        # 收货人
        set_data(ci, parcel_customer_order.buyer_name, start_row, ws)
        ci += 1
        # 收件地址1
        set_data(ci, parcel_customer_order.buyer_address_one, start_row, ws)
        ci += 1
        # 收件地址2
        set_data(ci, parcel_customer_order.buyer_address_two, start_row, ws)
        ci += 1
        # 收件城市
        set_data(ci, parcel_customer_order.buyer_city_code, start_row, ws)
        ci += 1
        # 收件州
        set_data(ci, parcel_customer_order.buyer_state, start_row, ws)
        ci += 1
        # 收件电话
        set_data(ci, parcel_customer_order.buyer_phone, start_row, ws)
        ci += 1
        # 收件邮编
        set_data(ci, parcel_customer_order.buyer_postcode, start_row, ws)
        ci += 1
        # 收件国家
        set_data(ci, parcel_customer_order.buyer_country_code, start_row, ws)
        ci += 1
        # 操作员
        set_data(ci, get(operator, 'username'), start_row, ws)
        ci += 1
        # # 转单状态
        # set_data(ci, label_status, start_row, ws)
        # ci += 1
        # # 转单信息
        # set_data(ci, label_desc, start_row, ws)
        # ci += 1
        # 是否拦截
        set_data(ci, '是' if parcel_customer_order.intercept_mark else '否', start_row, ws)
        ci += 1
        # 收件人邮箱
        set_data(ci, parcel_customer_order.buyer_mail, start_row, ws)
        ci += 1
        # 拦截原因
        set_data(ci, parcel_customer_order.remark, start_row, ws)
        ci += 1
        start_row += 1

    logger.info(f'异步导出报表数据excel end')
    return wb


class ParcelOrderLabelTaskViewSet(CustomViewBase):
    """
    请求小面单日志
    """
    # 针对外键的表进行预加载
    queryset = ParcelOrderLabelTask.objects.all()

    # 针对1对多和多对多的预加载
    # queryset = queryset.prefetch_related('orderLabelTasks', 'truck_order_id',)

    serializer_class = ParcelOrderLabelTaskListSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = {
        'order_num__order_num': ['exact'],
        'create_date': ['gte', 'lte']
    }
    ordering_fields = ('id',)
    authentication_classes = (AlitaJSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)


class SyncWMSTasksViewSet(CustomViewBase):
    """
    OMP同步WMS日志，WMS同步OMP日志
    """
    queryset = SyncWMSTasks.objects.all()

    # 针对1对多和多对多的预加载
    # queryset = queryset.prefetch_related('orderLabelTasks', 'truck_order_id',)

    serializer_class = SyncWMSTasksSerializer
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = {
        'unique_id': ['exact'],
        'create_date': ['gte', 'lte']
    }
    ordering_fields = ('id',)
    authentication_classes = (AlitaJSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)


class LastMileAPILogViewSet(CustomViewBase):
    """
    渠道API日志
    """
    permission_classes = (RbacPermission,)

    @extend_schema(
        request=LastMileAPILogSerializer,
        summary="渠道API日志",
        tags=["日志"],
    )
    @action(methods=['POST'], detail=False)
    def get_list(self, request):
        """渠道API日志"""
        page = request.data.get('page', 1)
        size = request.data.get('size', 10)
        req_num = request.data.get('req_num')
        customer = request.data.get('customer')
        func_name = request.data.get('func_name')
        order_num = request.data.get('order_num')
        start_time = request.data.get('start_time')
        end_time = request.data.get('end_time')
        air_waybill_number = request.data.get('air_waybill_number')
        customer_order_num = request.data.get('customer_order_num')
        tracking_num = request.data.get('tracking_num')
        service = request.data.get('service')
        str_status = request.data.get('status')
        supplier = request.data.get('supplier')
        request_data = request.data.get('request_data')
        response_data = request.data.get('response_data')
        start_round_trip_time = request.data.get('start_round_trip_time')
        end_round_trip_time = request.data.get('end_round_trip_time')

        url = settings.OPENOBSERVE_HOST
        if not url:
            data = {
                'code': 200,
                'msg': 'success'
            }
            return Response(data=data, status=status.HTTP_200_OK)

        # WHERE条件构建
        conditions = []
        
        # 精确匹配字段
        exact_fields = {
            'order_num': order_num,
            'customer': customer,
            'func_name': func_name,
            'service': service,
            'status': str_status,  # 注意字段名映射
            'supplier': supplier,
            'air_waybill_number': air_waybill_number,
            'customer_order_num': customer_order_num,
            'tracking_num': tracking_num
        }
        
        # 构建精确匹配条件
        for field, value in exact_fields.items():
            if value:
                conditions.append(f"{field} = '{value}'")

        # 构建请求单据查询
        if req_num:
            conditions_or_where = []
            for field, value in exact_fields.items():
                conditions_or_where.append(f"{field} = '{req_num}'")
            if conditions_or_where:
                conditions.append(' OR '.join(conditions_or_where))
                conditions.append(f" OR request_data LIKE '%{req_num}%' ")
                conditions.append(f" OR response_data LIKE '%{req_num}%'")
        
        # 构建模糊匹配条件
        if request_data:
            conditions.append(f"request_data LIKE '%{request_data}%'")
        if response_data:
            conditions.append(f"response_data LIKE '%{response_data}%'")

        # 构建范围匹配
        if start_round_trip_time is not None:
            conditions.append(f"round_trip_time >= {start_round_trip_time}")
        if end_round_trip_time:
            conditions.append(f"round_trip_time <= {end_round_trip_time}")
        
        # 构建SQL
        sql = f"SELECT * "
        where_sql = f"FROM '{StreamEnum.LASTMILE.value}'"
        if conditions:
            where_sql += f" WHERE {' AND '.join(conditions)}"
        sql += where_sql
        sql += " ORDER BY _timestamp DESC"

        if start_time:
            start_time = int(datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S").timestamp() * 1000000)

        if end_time:
            end_time = int(datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S").timestamp() * 1000000)

        data = openobserve_client.search_list(sql, start_time, end_time, int(page), int(size), where_sql)

        data = dict(
            code=200,
            msg='success',
            data=data
        )
        return Response(data=data, status=status.HTTP_200_OK)


class ClientApiLogViewSet(CustomViewBase):
    """
    客户端API日志
    """
    permission_classes = (RbacPermission,)

    @extend_schema(
        request=ClientApiLogSerializer,
        summary="客户端API日志",
        tags=["日志"],
    )
    @action(methods=['POST'], detail=False)
    def get_list(self, request):
        """客户端API日志"""
        page = request.data.get('page', 1)
        size = request.data.get('size', 10)
        req_num = request.data.get('req_num')
        customer = request.data.get('customer')
        func_name = request.data.get('func_name')
        order_num = request.data.get('order_num')
        start_time = request.data.get('start_time')
        end_time = request.data.get('end_time')
        air_waybill_number = request.data.get('air_waybill_number')
        customer_order_num = request.data.get('customer_order_num')
        tracking_num = request.data.get('tracking_num')
        service = request.data.get('service')
        str_status = request.data.get('status')
        product = request.data.get('product')
        request_data = request.data.get('request_data')
        response_data = request.data.get('response_data')
        start_round_trip_time = request.data.get('start_round_trip_time')
        end_round_trip_time = request.data.get('end_round_trip_time')

        url = settings.OPENOBSERVE_HOST
        if not url:
            data = {
                'code': 200,
                'msg': 'success'
            }
            return Response(data=data, status=status.HTTP_200_OK)

        # WHERE条件构建
        conditions = []

        # 精确匹配字段
        exact_fields = {
            'order_num': order_num,
            'customer': customer,
            'func_name': func_name,
            'service': service,
            'status': str_status,  # 注意字段名映射
            'product': product,
            'air_waybill_number': air_waybill_number,
            'customer_order_num': customer_order_num,
            'tracking_num': tracking_num
        }

        # 构建精确匹配条件
        for field, value in exact_fields.items():
            if value:
                conditions.append(f"{field} = '{value}'")

        # 构建请求单据查询
        if req_num:
            conditions_or_where = []
            for field, value in exact_fields.items():
                conditions_or_where.append(f"{field} = '{req_num}'")
            if conditions_or_where:
                conditions.append(' OR '.join(conditions_or_where))
                conditions.append(f" OR request_data LIKE '%{req_num}%'")
                conditions.append(f" OR response_data LIKE '%{req_num}%'")

        # 构建模糊匹配条件
        if request_data:
            conditions.append(f"request_data LIKE '%{request_data}%'")
        if response_data:
            conditions.append(f"response_data LIKE '%{response_data}%'")

        # 构建范围匹配
        if start_round_trip_time is not None:
            conditions.append(f"round_trip_time >= {start_round_trip_time}")
        if end_round_trip_time:
            conditions.append(f"round_trip_time <= {end_round_trip_time}")

        # 构建SQL
        sql = f"SELECT * "
        where_sql = f"FROM '{StreamEnum.CUSTOMER_API.value}'"
        if conditions:
            where_sql += f" WHERE {' AND '.join(conditions)}"
        sql += where_sql
        sql += " ORDER BY _timestamp DESC"

        if start_time:
            start_time = int(datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S").timestamp() * 1000000)

        if end_time:
            end_time = int(datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S").timestamp() * 1000000)

        data = openobserve_client.search_list(sql, start_time, end_time, int(page), int(size), where_sql)

        data = dict(
            code=200,
            msg='success',
            data=data
        )
        return Response(data=data, status=status.HTTP_200_OK)
