from __future__ import absolute_import, unicode_literals

import base64
import hashlib
import math
import os
import random
import time
import traceback
from datetime import datetime, timedelta
from decimal import Decimal
import io

import json
import numpy as np
import matplotlib.pyplot as plt
import openpyxl
import pytz
from PyPDF2 import PdfFileMerger
from celery.signals import after_setup_task_logger
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist, MultipleObjectsReturned
from django.db.models import Q, Sum, F, DecimalField
from django.forms import model_to_dict
from django.core.cache import cache
from django.conf import settings
from django.db import transaction, DatabaseError
from django.db import connection
from matplotlib import patheffects
from scipy.interpolate import make_interp_spline
from celery.exceptions import Retry

import common
from account.views.account import deduction_account as deduction_account_v
from alita.logger import logger
from celery_once import QueueOnce
from alita.celery_config import app
from common import custom
from common.async_tasks import common_order_async_task
from common.common_parameter import TRACK17_STATUS_CODES, TRACK17_MASK_WORD
from common.exception_handler import exception_handler
from common.order_statement.order_statement_dmas import fba_order_not_revenue_confirm, statistics_revenue_and_cost, \
    place_order_count, pick_up_aging, arrive_overseas_warehouse_aging, combine_multiple_plots_vertically, \
    return_order_confirm, account_balance_alert, arrive_overseas_warehouse_aging_new
from common.service.clearance_out_service import push_clearance_out_order, pull_clearance_out_order
from common.service.label_service import save_label_file_by_request
from common.service.master_service import handler_master_order_push_task
from common.service.parcel_order import assemble_barcode_params, add_label_task_by_product_type, \
    handler_product_limit_user, handler_scan_form_v2, handler_cancel_label
from common.service.product_attribute_limit import check_order_data, check_prop_limit, get_productBasicRestriction, \
    process_gb_postcode
from common.order_num_gen_rule import create_order_num
from common.service.track_service import add_track_task
from common.tools import gen_parcel_order_num, get_service_from_product, set_17track_task, \
    get_17track_courier_code, update_order_status, get_service_class_url, set_multistep_parcel_track, deduction_account
from common.utils.barcode_gen import create_barcodes_for_order
from common.utils.base_util import to_utc_iso_format
from common.utils.decorators import distributed_lock_ship
from common.utils.dmas_util import send_dmas_message, send_dmas_base64_image, save_image_file
from common.utils.object import upper_first, is_valid_email
from common.utils.tracking51 import create_51tracking
from common.utils.tracking17 import register_17tracking_number, get_17tracking
from common.utils.amazon_ship_track_util import send_request
import requests
from company.models import SupplierButt, SupplierButtAccount, Address, Company, NodeInsertRule, NodeAutomaticPush
from crm.models import OrderCommissionDiscounts
from info.models import ServiceClass, Charge, TrackCode
from order.clearanceIntegrantion.customsClearanceIntegrationInterface import ClearanceOrderVo, create_clearance_order
from order.insurance.insuranceIntegrationInterface import InsuranceOrderVo, create_insurance_order
from order.integration.call_service.service import call_last_mile_service
from order.integration.integrationInterface import create_order, LabelOrderVo, get_label, add_fail_label_task, \
    check_parcel_list, check_item, get_secondary_label, add_fail_sync_task, confirm_ship, set_parcel_item_default_value, \
    set_parcel_default_value, set_parcel_customer_order_default_value, TaxInfo
from order.integration.link.constants import WaybillType
from order.integration.util.abstract_func import create_order_label_task
from order.integration.util.alitaUtil import get_orders, get_order_detail
from order.integration.util.cainiaoUtil import update_order, create_big_bag, get_big_label
from order.integration.util.commonUtil import DecimalEncoder, convert_dict
from order.integration.util.emsCnUtil import get_order, ems_push_track
from order.integration.util.emsZjUtil import handler_push_ems_order
from order.integration.util.hzhUtil import push_label, push_track, push_error_label
from order.integration.util.mabangUtil import get_mabang_order, accept_tracking_num, exception_order
from order.integration.util.wishpost_util import msg_send_carrier
from order.integration.util.yicangUtil import get_yicang_order, push_tracking_yicang, push_order_status_yicang
from order.integration.util.omniShipUtil import omni_ship_client
from order.integration.util.tongtoolUtil import TongToolUtil
from order.integration.util.asendiaUtil import create_big_order_for_asendia, generate_big_parcel_num, \
    get_big_order_label_for_asendia
from order.integration.util.rdUtil import rd_receptacel_upload

from info.models import Dict
from order.models import (OrderLabelTask, CustomerOrder, Parcel, ParcelItem, ParcelOrderLabelTask, ParcelCustomerOrder, \
                          ParcelOrderParcel, ParcelOrderItem, OrderLabel, ParcelOrderLabel,
                          ParcelOrderChargeIn, ParcelOrderChargeOut, ParcelTrack, Track, CustomerParcelTrack,
                          OrderSyncTask, ParcelSize, \
                          CustomsClearanceOrder, CustomsClearanceOrderSupplierTask, CustomsClearanceBigParcelOrder,
                          MasterOrderPushTask, BigParcel, PushEmsOrder, BigParcelLabelTask,
                          InsuranceOrderTask, BigParcelLabel, CustomerOrderChargeIn, CustomerOrderChargeOut,
                          OceanOrderTrack, CustomerOrderRelateOcean, CustomsClearanceAddress, ClearanceOutSyncTask,
                          InsuranceOrderPerson, InsuranceObject, Shop, OrderScanFormTask, ParcelOrderExtend, Webhook,
                          TruckOrderTrack, TruckOrder, AmazonShipTrackTask, CancelParcelCustomerOrderLabelTask,
                          CustomerOrderRelateTruck, ParcelCustomerOrderBatchExportTask, PickRecord,
                          PushWishPickUpInfoTask)

from common.service.pms_service import add_revenue, add_cost
from order.integration.util.customerOrderUtil import request_server, CreateCustomerOrder, CreateParcel, \
    request_get_server
from order.sync_wms_tasks import request_wms_intercept
from order.integration.util.shopifyUtil import sync_orders, get_shopify_shop_info

from pms.models import Product, Service, ProductBasicRestriction, ServiceApiSetting

from alita.settings.base import MEDIA_URL

from order.integration.postkeeperIntegrationService import PostkeeperIntegrationService
from order.integration.customerOrderIntegrationService import CustomerOrderIntegrationService, CustomerOrderSyncService
from order.integration.parcelCustomerOrderIntegrationService import ParcelCustomerOrderIntegrationService
from order.integration.parcelCustomerOrderIntegrationService import ParcelCustomerOrderIntegrationService
from order.integration.idealIntegrationService import IdealIntegrationService
from order.integration.deUpsIntegrationService import DeUpsIntegrationService
from order.integration.oblIntegrationService import OblIntegrationService
from order.integration.ideal2IntegrationService import Ideal2IntegrationService
from order.integration.ideal3IntegrationService import Ideal3IntegrationService
from order.integration.hsIntegrationService import HsIntegrationService
from order.integration.megaIntegrationService import MegaIntegrationService
from order.integration.kwtIntegrationService import KwtIntegrationService
from order.integration.px4IntegrationService import Px4IntegrationService
from order.integration.px4LmaIntegrationService import Px4LmaIntegrationService
from order.integration.hltIntegrationService import HltIntegrationService
from order.integration.anNengIntegrationService import AnNengIntegrationService
from order.integration.anNengSyncCreateIntegrationService import AnNengSyncCreateIntegrationService
from order.integration.hf2IntegrationService import Hf2IntegrationService
from order.integration.zsdIntegrationService import ZsdIntegrationService
from order.integration.jnIntegrationService import JnIntegrationService
from order.integration.yhIntegrationService import YhIntegrationService
from order.integration.ninjaVanIntegrationService import NinjaVanIntegrationService
from order.integration.gsIntegrationService import GsIntegrationService
from order.integration.lcsIntegrationService import LcsIntegrationService
from order.integration.thzIntegrationService import ThzIntegrationService
from order.integration.mapleIntegrationService import MapleIntegrationService
from order.integration.dhlIntegrationService import DhlIntegrationService
from order.integration.pospeipIntegrationService import PospeipIntegrationService
from order.integration.k5IntegrationService import K5IntegrationService
from order.integration.zrIntegrationService import ZrIntegrationService
from order.integration.hallIntegrationService import HallIntegrationService
from order.integration.shopLineIntegrationService import ShopLineIntegrationService
from order.integration.dhlShippingIntegrationService import DhlShippingIntegrationService
from order.integration.dhlIntlShippingIntegrationService import DhlIntlShippingIntegrationService
from order.integration.dhlIntlEconomyIntegrationService import DhlIntlEconomyIntegrationService
from order.integration.shipHuBxIntegrationService import ShipHuBxIntegrationService
from order.integration.jaIntegrationService import JaIntegrationService
from order.integration.jdIntegrationService import JdIntegrationService
from order.integration.shipNitroIntegrationService import ShipNitroIntegrationService
from order.integration.hlIntegrationService import HlIntegrationService
from order.integration.emsZjIntegrationService import EmsZjIntegrationService
from order.integration.didaIntegrationService import DidaIntegrationService
from order.integration.olwIntegrationService import OlwIntegrationService
from order.integration.shaoKeIntegrationService import ShaoKeIntegrationService
from order.integration.dpexIntegrationService import DpexIntegrationService
from order.integration.spxIntegrationService import SpxIntegrationService
from order.integration.htyIntegrationService import HtyIntegrationService
from order.integration.ycIntegrationService import YcIntegrationService
from order.integration.cainiaoCdPacketIntegrationService import CainiaoCdPacketIntegrationService
from order.integration.cainiaoFullPacketIntegrationService import CainiaoFullPacketIntegrationService
from order.integration.paisongyiIntegrationService import PaisongyiIntegrationService
from order.integration.gogoshipIntegrationService import GogoShipIntegrationService
from order.integration.iMileIntegrationService import IMileIntegrationService
from order.integration.gweIntegrationService import GweIntegrationService
from order.integration.gwe2IntegrationServeice import Gwe2IntegrationService
from order.integration.ruiDianIntegrationService import RuiDianIntegrationService
from order.integration.saichengIntegrationService import SaichengIntegrationService
from order.integration.maegmantIntegrationService import MaegmantIntegrationService
from order.integration.multicourierIntegrationService import MulticourierIntegrationService
from order.integration.jerryIntegrationService import JerryIntegrationService
from order.integration.jyIntegrationService import JyIntegrationService
from order.integration.gpsIntegrationService import GPSIntegrationService
from order.integration.maerskIntegrationService import MaerskIntegrationService
from order.integration.haiTunIntegrationService import HaiTunIntegrationService
from order.integration.yzIntegrationService import YzIntegrationService
from order.integration.rlabelIntegrationService import RLabelIntegrationService
from order.integration.colisIntegrationService import ColisIntegrationService
from order.integration.cainiaoOverseaIntegrationService import CainiaoOverseaIntegrationService
from order.integration.winitLmaIntegrationService import WinitLmaIntegrationService
from order.integration.yicIntegrationService import YicIntegrationService
from order.integration.zxLabelIntegrationService import ZxLabelIntegrationService
from order.integration.worldTechIntegrationService import WorldTechIntegrationService
from order.integration.hermesIntegrationService import HermesIntegrationService
from order.integration.sengiIntegrationService import SengiIntegrationService
from order.integration.skyeShipIntegrationService import SkyeShipIntegrationService
from order.integration.feikeIntegrationService import FeiKeIntegrationService
from order.integration.earlybirdIntegrationService import EarlyBirdIntegrationService
from order.integration.pacticIntegrationService import PacticIntegrationService
from order.integration.yodelIntegrationService import YodelIntegrationService
from order.integration.scurriIntegrationService import ScurriIntegrationService
from order.integration.hanJinIntegrationService import HanJinIntegrationService
from order.integration.asendiaIntegrationService import AsendiaIntegrationService
from order.integration.tongDaDpdIntegrationService import TongDaDpdIntegrationService
from order.integration.dhlNlIntegrationService import DhlNlIntegrationService
from order.integration.boFengService import BoFengIntegrationService
from order.integration.amazonShippingOneClickService import AmazonShippingOneClickService
from order.integration.royalMailIntegrationService import RoyalMailIntegrationService
from order.integration.yidaService import YiDaIntegrationService
from order.integration.eboIntegrationService import EboIntegrationService
from order.integration.shaoKeDHLIntegrationService import ShaokeDHLIntegrationService
from order.integration.jingDongSmallBagIntegrationService import JingDongSmallBagIntegrationService
from order.integration.evriIntegrationService import EvriIntegrationService
from order.integration.meiShangAnXinIntegrationService import MeiShangAnXinIntegrationService
from order.integration.sfService import SFIntegrationService
from order.integration.UralIntegrationService import UralIntegrationService
from order.integration.sfExportEcommerceService import SFExportEcommerceIntegrationService
from order.integration.anPostIntegrationService import AnPostIntegrationService
from order.integration.omsClientIntegrationService import OmsClientIntegrationService

# 清关服务类
from order.clearanceIntegrantion.clevyIntegrationService import ClevyIntegrationService

# 保险
from order.insurance.antInsuranceIntegrationService import AntInsuranceIntegrationService

from rbac.models import UserProfile
from salesorder.models import SalesOrder, SalesOrderDetail
from track.models import TrackTask

from django.db.models import OuterRef, Exists
from django.utils import timezone
from order.integration.integrationInterface import LabelOrderVo, cancel_label
from celery.schedules import crontab
from info.models import Dict
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font
from dateutil import parser
from pydash import get

User = get_user_model()


# QueueOnce是利用redis分布式锁，防止同一重复执行
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def createLabelTask(self, modeKey):
    '''
    创建面单定时任务
    :param modeKey:
    :return:
    '''
    logger.info(f"-------------createLabelTask---->{modeKey}")
    orderLabelTaskList = OrderLabelTask.objects.filter(status='UnHandled', del_flag='0', mode_key=modeKey,
                                                       handle_times__lt=121).reverse()[:20]
    if len(orderLabelTaskList) == 0:
        return

    for order_label_task in orderLabelTaskList:

        customer_order = CustomerOrder.objects.get(id=order_label_task.order_num.id)
        logger.info("--createLabelTask--order_num-->" + str(customer_order.order_num))

        if customer_order.order_status == 'VO':
            order_label_task.del_flag = True
            order_label_task.label_desc = '订单已作废'
            order_label_task.update_date = datetime.now()
            order_label_task.save()
            continue

        key = 'handler_label_' + customer_order.order_num
        cache.set(key, "1", 60)

        # 如果是单包
        if order_label_task.parcel:
            parcel_list = [order_label_task.parcel]
            is_unpack = True
        else:
            parcel_list = Parcel.objects.filter(customer_order=customer_order.id, del_flag=0)
            is_unpack = False

        parcel_id_list = [parcel.id for parcel in parcel_list]

        parcel_item_list = ParcelItem.objects.filter(parcel_num__in=parcel_id_list, del_flag=False)

        check_result = check_parcel_list(parcel_list)
        if len(check_result) > 0:
            add_fail_label_task(order_label_task, str(check_result))
            continue

        check_result = check_item(parcel_item_list)
        if len(check_result) > 0:
            add_fail_label_task(order_label_task, str(check_result))
            continue

        if not customer_order.product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            continue

        logger.info("--productId-->" + str(customer_order.product.id))
        product = customer_order.product
        if not customer_order.service:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
            continue
        service = customer_order.service

        # 如果是虚拟产品，以最小成本的产品打单
        if product.is_virtual:
            product = customer_order.real_product
            service = Service.objects.filter(product=product, del_flag=False).first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        supplier_account = supplier_account_list.first()

        class_name = supplier_butt.class_name
        if not class_name:
            add_fail_label_task(order_label_task, '未对接实现类')
            continue
        if not supplier_account:
            add_fail_label_task(order_label_task, '未对接供应商账户')
            continue

        label_order_vo = LabelOrderVo()
        label_order_vo.orderLabelTask = order_label_task
        label_order_vo.supplierAccount = supplier_account
        label_order_vo.customerOrder = customer_order
        label_order_vo.parcelList = parcel_list
        label_order_vo.parcelItemList = parcel_item_list
        label_order_vo.product = product
        label_order_vo.service = service
        label_order_vo.is_unpack = is_unpack
        label_order_vo.supplier_butt = supplier_butt

        logger.info("className =" + str(class_name))
        # 通过反射实例化对象
        obj = globals()[class_name]()
        create_order(obj, label_order_vo)

        cache.delete(key)


@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def getLabelTask(self, modeKey):
    '''
    创建面单定时任务
    :param modeKey:
    :return:
    '''
    logger.info(f"-------------getLabelTask---->{modeKey}")
    order_label_task_list = OrderLabelTask.objects.filter(status='HandledBy3rdNo', del_flag='0', handle_times__lt=122,
                                                          mode_key=modeKey).reverse()[:20]

    for order_label_task in order_label_task_list:

        customer_order = CustomerOrder.objects.get(id=order_label_task.order_num.id)
        logger.info('--getLabelTask-->>' + customer_order.order_num)
        key = 'handler_label_' + customer_order.order_num
        cache.set(key, "1", 60)

        if not customer_order.product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            continue
        product = Product.objects.get(id=customer_order.product.id)

        # 是否拆包
        if order_label_task.parcel:
            parcel_list = [order_label_task.parcel]
            is_unpack = True
        else:
            parcel_list = Parcel.objects.filter(customer_order=customer_order.id, del_flag=0)
            is_unpack = False

        parcel_id_list = [parcel.id for parcel in parcel_list]

        parcelItemList = ParcelItem.objects.filter(parcel_num__in=parcel_id_list)

        if not customer_order.service:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
            continue
        service = customer_order.service

        # 如果是虚拟产品，以最小成本的产品打单
        if product.is_virtual:
            product = customer_order.real_product
            service = Service.objects.filter(product=product, del_flag=False).first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        supplier_account = supplier_account_list[0]

        class_name = supplier_butt.class_name

        label_order_vo = LabelOrderVo()
        label_order_vo.orderLabelTask = order_label_task
        label_order_vo.supplierAccount = supplier_account
        label_order_vo.customerOrder = customer_order
        label_order_vo.product = product
        label_order_vo.service = service
        label_order_vo.parcelList = parcel_list
        label_order_vo.parcelItemList = parcelItemList
        label_order_vo.is_unpack = is_unpack
        label_order_vo.supplier_butt = supplier_butt

        # 通过反射实例化对象
        obj = globals()[class_name]()
        get_label(obj, label_order_vo)

        order_label_queryset = OrderLabel.objects.filter(order_num=customer_order, del_flag=False)
        if order_label_queryset.count() == len(parcel_list):
            i = 0
            for order_label in order_label_queryset:
                parcel = parcel_list[i]
                parcel.tracking_num = order_label.tracking_no
                parcel.save()
                i += 1


def split_string(s):
    # 使用 rpartition 方法从右边开始查找最后一个 '_'
    left, sep, right = s.rpartition('_')

    # 检查是否找到了分隔符
    if sep:
        return left, right
    else:
        return None, None


@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def sync_label_task(self, modeKey):
    logger.info(f"-----sync_label_task start---->{modeKey}")

    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'YQF']:
        return

    order_label_task_queryset = OrderLabelTask.objects.filter(status='Success', sync_status='WS', mode_key=modeKey,
                                                              del_flag=False)
    for order_label_task in order_label_task_queryset:
        is_sync_finish = handler_sync_order_label(order_label_task)

        if is_sync_finish:
            order_label_task.update_date = datetime.now()
            order_label_task.sync_status = 'FC'
            order_label_task.save()

    logger.info(f"-----sync_label_task start---->{modeKey}")


@transaction.atomic
def handler_sync_order_label(order_label_task):
    customer_order = order_label_task.customer_order
    product = order_label_task.product
    service = Service.objects.filter(product=product, is_default=True, del_flag=False)
    parcel_gl_queryset = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
    is_sync_finish = False
    for parcel_gl in parcel_gl_queryset:
        parcel_num, order_type = split_string(parcel_gl.parcel_num)
        order_label_queryset = OrderLabel.objects.filter(tracking_no=parcel_gl.tracking_num, del_flag=False)
        if not order_label_queryset.exists():
            is_sync_finish = False
            break

        parcel_queryset = Parcel.objects.filter(parcel_num=parcel_num, order_type=order_type, del_flag=False)
        if not parcel_queryset.exists():
            is_sync_finish = False
            break
        order_label = order_label_queryset.first()

        parcel_queryset.update(tracking_num=order_label.tracking_no,
                               voucher=order_label.label_url,
                               courier_code=service.courier_code,
                               update_date=datetime.now())
        is_sync_finish = True

    return is_sync_finish


@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True}, priority=9)  # 添加优先级设置
def create_parcel_label_task(self, mode_key):
    '''
    创建小包面单定时任务
    :param mode_key:
    :return:
    '''
    logger.info(f'-----create_parcel_label_task---->{mode_key}')
    order_label_task_list = ParcelOrderLabelTask.objects.filter(status='UnHandled', del_flag=False, mode_key=mode_key,
                                                                handle_times__lt=121)[:200]

    if not order_label_task_list.exists():
        return

    # 先锁单，筛选出可以处理的任务
    valid_tasks = []
    for order_label_task in order_label_task_list:
        parcel_customer_order = order_label_task.order_num
        key = 'handler_label_' + parcel_customer_order.order_num
        # 如果缓存存在，则跳过，防止重复执行
        if cache.get(key):
            logger.info(f'-----create_parcel_label_task--防止重复执行-->{parcel_customer_order.order_num}')
            continue
        cache.set(key, "1", 60)
        valid_tasks.append(order_label_task)

    # 再处理筛选后的任务
    for order_label_task in valid_tasks:
        if settings.SYSTEM_MARK not in ['TANGUS','ZHS', 'ZJ', 'HJ', 'MD']:
            handler_create_parcel_label_task.delay(mode_key, order_label_task.id)
        else:
            handler_create_parcel_label_task.apply_async(
                args=[mode_key, order_label_task.id],
                queue=f'label_queue_{mode_key}',
                routing_key=f'label_queue_{mode_key}'
            )


@app.task(bind=True, base=QueueOnce, max_retries=0, once={'graceful': True}, priority=9)
def get_parcel_label_task(self, mode_key):
    '''
    获取小包面单定时任务
    :param mode_key:
    :return:
    '''
    logger.info("-------------get_parcel_label_task---->" + mode_key)
    order_label_task_list = ParcelOrderLabelTask.objects.filter(status='HandledBy3rdNo', del_flag=False,
                                                                handle_times__lt=121,
                                                                mode_key=mode_key)[:200]

    # 先锁单，筛选出可以处理的任务
    valid_tasks = []
    for order_label_task in order_label_task_list:
        parcel_customer_order = order_label_task.order_num
        key = 'handler_get_label_' + parcel_customer_order.order_num
        # 如果缓存存在，则跳过，防止重复执行
        if cache.get(key):
            logger.info(f'-----get_parcel_label_task--防止重复执行-->{parcel_customer_order.order_num}')
            continue
        cache.set(key, "1", 60)
        valid_tasks.append(order_label_task)

    # 再处理筛选后的任务
    for order_label_task in valid_tasks:
        if settings.SYSTEM_MARK not in ['TANGUS', 'ZHS', 'ZJ', 'HJ', 'MD']:
            handler_get_parcel_label_task.delay(mode_key, order_label_task.id)
        else:
            handler_get_parcel_label_task.apply_async(
                args=[mode_key, order_label_task.id],
                queue=f'label_queue_{(int(mode_key) + 20)}',
                routing_key=f'label_queue_{(int(mode_key) + 20)}'
            )


@app.task(bind=True, base=QueueOnce, max_retries=0, once={'graceful': True, 'timeout': 60})
def confirm_parcel_label_task(self, mode_key):
    '''
    创建小包确认定时任务
    :param mode_key:
    :return:
    '''
    # 查询更新时间12小时后的
    current_date = datetime.now()
    time_ago = current_date - timedelta(hours=12)
    logger.info("-------------confirm_parcel_label_task---->" + mode_key)
    order_label_task_list = ParcelOrderLabelTask.objects.filter(status='Success',
                                                                del_flag=False,
                                                                handle_times__lt=121,
                                                                update_date__lt=time_ago,
                                                                is_need_confirm=True)[:200]

    logger.info(f'confirm_label : {order_label_task_list.query}')
    for order_label_task in order_label_task_list:
        handler_confirm_parcel_label_task.delay(mode_key, order_label_task.id)


@app.task(bind=True, max_retries=0, base=QueueOnce,
          once={'graceful': True, 'keys': ['mode_key', 'label_task_id'], 'timeout': 60})
def handler_create_parcel_label_task(self, mode_key, label_task_id):
    """
    处理创建小包抓单任务
    :param self:
    :return:
    """
    logger.info(f'---start--handler_create_parcel_label_task--{mode_key}-->{label_task_id}')
    order_label_task = ParcelOrderLabelTask.objects.get(id=label_task_id)
    if not order_label_task or order_label_task.status != 'UnHandled' or order_label_task.handle_times > 120:
        return

    parcel_customer_order = order_label_task.order_num
    if parcel_customer_order.order_status == 'VO':
        order_label_task.del_flag = True
        order_label_task.label_desc = '订单已作废'
        order_label_task.update_date = datetime.now()
        order_label_task.save()
        return

    order_num = parcel_customer_order.order_num

    # 如果是拆包
    if order_label_task.parcel:
        parcel_list = [order_label_task.parcel]
        is_unpack = True
    else:
        parcel_list = ParcelOrderParcel.objects.filter(customer_order=parcel_customer_order.id, del_flag=False)
        is_unpack = False

    parcel_id_list = [parcel.id for parcel in parcel_list]

    parcel_item_list = ParcelOrderItem.objects.filter(parcel_num__in=parcel_id_list, del_flag=False)

    if settings.SYSTEM_VERSION in ['V1']:

        task_product = order_label_task.product
        if task_product and task_product.code == 'ZX_LABEL':
            class_name = 'ZxLabelIntegrationService'
            label_order_vo = LabelOrderVo()
            label_order_vo.orderLabelTask = order_label_task
            label_order_vo.customerOrder = parcel_customer_order
            # 通过反射实例化对象
            obj = globals()[class_name]()
            create_order(obj, label_order_vo)
            return

        if not parcel_customer_order.product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            return
        product = parcel_customer_order.product
        if not parcel_customer_order.service:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
            return
        service = parcel_customer_order.service

        # 如果是组合产品 多渠道,或者YW
        if (product.is_virtual and product.strategy_type in ['MULTI_CHANNEL', 'MC_COST_LOW',
                                                             'COST_LOW']) or product.label_type == 'HW':
            product = order_label_task.product
            service = Service.objects.filter(product=product, is_default=True, del_flag=False).first()

        logger.info(f'--handler_create_parcel_label_task order_num-->>{order_num}')
        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        if supplier_account_list.count() == 0:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置完成供应商信息')
            return
        supplier_account = supplier_account_list[0]

        # 设置包裹默认值
        set_parcel_default_value(parcel_list, product)

        # 设置商品默认值
        set_parcel_item_default_value(parcel_item_list, product)

        # 根据产品类型判断将 label_ 的值 替换掉 不带label 的字段
        if product.label_type == 'WWWC':
            # 1. ParcelOrderItem 字段赋值（只在内存中赋值，不保存到数据库，类型保持QuerySet）
            for parcel_item in parcel_item_list:
                if hasattr(parcel_item, 'label_declared_nameCN'):
                    parcel_item.declared_nameCN = parcel_item.label_declared_nameCN
                if hasattr(parcel_item, 'label_declared_nameEN'):
                    parcel_item.declared_nameEN = parcel_item.label_declared_nameEN
                if hasattr(parcel_item, 'label_customs_code'):
                    parcel_item.customs_code = parcel_item.label_customs_code
                if hasattr(parcel_item, 'label_item_qty'):
                    parcel_item.item_qty = parcel_item.label_item_qty
                if hasattr(parcel_item, 'label_declared_price'):
                    parcel_item.declared_price = parcel_item.label_declared_price
                if hasattr(parcel_item, 'label_declared_currency'):
                    parcel_item.declared_currency = parcel_item.label_declared_currency
                if hasattr(parcel_item, 'label_item_weight'):
                    parcel_item.item_weight = parcel_item.label_item_weight
            # 后续如需用到parcel_item_list，依然是QuerySet类型

            # 2. ParcelCustomerOrder 关联 ParcelOrderExtend 字段赋值（只在内存中赋值，不保存到数据库）
            parcel_order_extend_obj = ParcelOrderExtend.objects.filter(
                customer_order=parcel_customer_order, del_flag=False
            ).first()
            # ioss_num_wwwc = parcel_customer_order.ioss_num
            if parcel_order_extend_obj and hasattr(parcel_order_extend_obj, 'label_ioss_num'):
                parcel_customer_order.ioss_num = parcel_order_extend_obj.label_ioss_num

        # 设置小包包裹默认值
        set_parcel_customer_order_default_value(parcel_customer_order, product)

        check_result = check_parcel_list(parcel_list)
        if len(check_result) > 0:
            add_fail_label_task(order_label_task, check_result)
            return

        check_result = check_item(parcel_item_list)
        if len(check_result) > 0:
            add_fail_label_task(order_label_task, check_result)
            return

        class_name = upper_first(supplier_butt.class_name)

        label_order_vo = LabelOrderVo()
        label_order_vo.orderLabelTask = order_label_task
        label_order_vo.supplierAccount = supplier_account
        label_order_vo.customerOrder = parcel_customer_order
        label_order_vo.parcelList = parcel_list
        label_order_vo.parcelItemList = parcel_item_list
        label_order_vo.product = product
        label_order_vo.service = service
        label_order_vo.supplier_butt = supplier_butt
        label_order_vo.is_unpack = is_unpack

        logger.info("className =" + str(class_name))
        # 通过反射实例化对象
        obj = globals()[class_name]()
        create_order(obj, label_order_vo)

        # 加入到拉轨迹任务中, 因是称重后拉轨迹所以排除了'ZJ', 'ZHS'
        order_label_task = ParcelOrderLabelTask.objects.get(id=label_task_id)
        if service.trackSupplier and order_label_task.status == 'Success' and settings.SYSTEM_MARK not in ['ZJ', 'ZHS']:
            add_track_task(parcel_customer_order, service.trackSupplier, service.courier_code)
            track_supplier = service.trackSupplier
            if track_supplier and track_supplier.class_name and track_supplier.class_name == 'Track51TrackService':
                create_51tracking(parcel_customer_order.tracking_num, service.courier_code, track_supplier.auth_pwd,
                                  parcel_customer_order.buyer_postcode)

    elif settings.SYSTEM_VERSION == 'V2':
        logger.info(f'---new---parcel_customer_order--->{parcel_customer_order}-->')
        try:
            deduction_account_v(parcel_customer_order, parcel_customer_order.create_by, ParcelOrderChargeIn)
        except Exception as e:
            order_label_task.handle_times = 121
            order_label_task.label_desc = str(e)
            order_label_task.update_date = datetime.now()
            order_label_task.save()
            logger.info(f'---new---create_parcel_label_task--->{order_num}-->{str(e)}')
            return

        service = order_label_task.service
        api_setting_queryset = ServiceApiSetting.objects.filter(service=service, del_flag=False)
        logger.info(f'api_setting_queryset: {api_setting_queryset.values()}')
        settings_dict = {item.field_name: item.field_value for item in api_setting_queryset}

        class_name = service.butt_code.class_name
        class_name = upper_first(class_name)

        parcel_order_extend_obj = ParcelOrderExtend.objects.filter(
            customer_order=parcel_customer_order, del_flag=False
        ).first()

        # 税信息
        tax_info = TaxInfo(
            tax_pay_mode=parcel_order_extend_obj.tax_pay_mode,
            tax_type=parcel_order_extend_obj.tax_type,
            tax_no=parcel_order_extend_obj.tax_no,
            tax_company=parcel_order_extend_obj.tax_company,
            tax_country=parcel_order_extend_obj.tax_country,
            tax_address=parcel_order_extend_obj.tax_address,
        )

        label_order_vo = LabelOrderVo()
        label_order_vo.orderLabelTask = order_label_task
        label_order_vo.customerOrder = parcel_customer_order
        label_order_vo.parcelList = parcel_list
        label_order_vo.parcelItemList = parcel_item_list
        label_order_vo.product = parcel_customer_order.product
        label_order_vo.service = parcel_customer_order.service
        label_order_vo.supplier_butt = ''
        label_order_vo.is_unpack = False
        label_order_vo.tax_info = tax_info

        label_order_vo.service_dict = settings_dict

        logger.info("className =" + str(class_name))
        # 通过反射实例化对象
        # obj = globals()[class_name]()
        obj = call_last_mile_service(class_name)

        try:
            create_order(obj, label_order_vo)
        except Exception as e:
            logger.info(traceback.format_exc())

    key = 'handler_label_' + parcel_customer_order.order_num
    if cache.get(key):
        cache.delete(key)

    logger.info(f'---end--handler_create_parcel_label_task--{mode_key}-->{label_task_id}')


@app.task(bind=True, base=QueueOnce, once={'graceful': True, 'keys': ['mode_key', 'label_task_id']})
def handler_get_parcel_label_task(self, mode_key, label_task_id):
    logger.info(f'---start--handler_get_parcel_label_task--{mode_key}-->{label_task_id}')

    order_label_task = ParcelOrderLabelTask.objects.get(id=label_task_id)

    if not order_label_task or order_label_task.status != 'HandledBy3rdNo' or order_label_task.handle_times > 120:
        return

    parcel_customer_order = order_label_task.order_num

    logger.info(f'--handler_get_parcel_label_task--order_num-->{parcel_customer_order.order_num}')

    if not parcel_customer_order.product:
        add_fail_label_task(order_label_task, '未选择产品，请选择产品')
        return

    product = parcel_customer_order.product

    parcelList = ParcelOrderParcel.objects.filter(customer_order=parcel_customer_order.id, del_flag=0)

    parcel_id_list = [parcel.id for parcel in parcelList]

    parcelItemList = ParcelOrderItem.objects.filter(parcel_num__in=parcel_id_list)

    if not parcel_customer_order.service:
        add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
        return

    service = parcel_customer_order.service

    if settings.SYSTEM_VERSION == 'V2':
        api_setting_queryset = ServiceApiSetting.objects.filter(service=service, del_flag=False)
        logger.info(f'api_setting_queryset: {api_setting_queryset.values()}')
        settings_dict = {item.field_name: item.field_value for item in api_setting_queryset}
        class_name = service.butt_code.class_name
        class_name = upper_first(class_name)

        label_order_vo = LabelOrderVo()
        label_order_vo.orderLabelTask = order_label_task
        label_order_vo.customerOrder = parcel_customer_order
        label_order_vo.parcelList = parcelList
        label_order_vo.parcelItemList = parcelItemList
        label_order_vo.product = product
        label_order_vo.service = service
        label_order_vo.supplier_butt = ''
        label_order_vo.is_unpack = False

        label_order_vo.service_dict = settings_dict

        # 通过反射实例化对象
        obj = globals()[class_name]()
        get_label(obj, label_order_vo)
    else:
        # 如果是组合产品 多渠道
        if (product.is_virtual and product.strategy_type in ['MULTI_CHANNEL', 'MC_COST_LOW',
                                                             'COST_LOW']) or product.label_type == 'HW':
            product = order_label_task.product
            service = Service.objects.filter(product=product, is_default=True, del_flag=False).first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        supplier_account = supplier_account_list[0]

        class_name = upper_first(supplier_butt.class_name)

        label_order_vo = LabelOrderVo()
        label_order_vo.orderLabelTask = order_label_task
        label_order_vo.supplierAccount = supplier_account
        label_order_vo.customerOrder = parcel_customer_order
        label_order_vo.service = service
        label_order_vo.parcelList = parcelList
        label_order_vo.parcelItemList = parcelItemList
        label_order_vo.supplier_butt = supplier_butt
        label_order_vo.product = product

        # 通过反射实例化对象
        obj = globals()[class_name]()
        get_label(obj, label_order_vo)

    # 加入到拉轨迹任务中, 因是称重后拉轨迹所以排除了'ZJ', 'ZHS'
    order_label_task = ParcelOrderLabelTask.objects.get(id=label_task_id)
    if service.trackSupplier and order_label_task.status == 'Success' and settings.SYSTEM_MARK not in ['ZJ', 'ZHS']:
        add_track_task(parcel_customer_order, service.trackSupplier, service.courier_code)

    order_label_queryset = ParcelOrderLabel.objects.filter(order_num=parcel_customer_order, del_flag=False)
    if order_label_queryset.count() == len(parcelList):
        i = 0
        for order_label in order_label_queryset:
            parcel = parcelList[i]
            parcel.tracking_num = order_label.tracking_no
            parcel.save()
            i += 1

    # 处理完成清除 key
    key = 'handler_get_label_' + parcel_customer_order.order_num
    if cache.get(key):
        cache.delete(key)
    logger.info(f'---end--handler_get_parcel_label_task--{mode_key}-->{label_task_id}')


# 小包确认
@app.task(bind=True, max_retries=0, base=QueueOnce,
          once={'graceful': True, 'keys': ['mode_key', 'label_task_id'], 'timeout': 600})
def handler_confirm_parcel_label_task(self, mode_key, label_task_id):
    """
    处理创建小包抓单任务
    :param self:
    :return:
    """
    logger.info(f' handler_confirm_parcel_label_task start--{mode_key}-->{label_task_id}')

    order_label_task = ParcelOrderLabelTask.objects.get(id=label_task_id)

    if not order_label_task or order_label_task.status != 'Success' or order_label_task.handle_times > 120:
        return

    parcel_customer_order = order_label_task.order_num

    if parcel_customer_order.order_status == 'VO':
        order_label_task.del_flag = True
        order_label_task.label_desc = '订单已作废'
        order_label_task.update_date = datetime.now()
        order_label_task.save()
        return

    if not parcel_customer_order.product:
        add_fail_label_task(order_label_task, '未选择产品，请选择产品')
        return

    product = parcel_customer_order.product
    if not parcel_customer_order.service:
        add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
        return
    service = parcel_customer_order.service

    # 如果是组合产品 多渠道,或者YW
    if (product.is_virtual and product.strategy_type in ['MULTI_CHANNEL', 'MC_COST_LOW',
                                                         'COST_LOW']) or product.label_type == 'HW':
        product = order_label_task.product
        service = Service.objects.filter(product=product, is_default=True, del_flag=False).first()

    supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
    supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
    if supplier_account_list.count() == 0:
        add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置完成供应商信息')
        return
    supplier_account = supplier_account_list[0]

    class_name = upper_first(supplier_butt.class_name)

    label_order_vo = LabelOrderVo()
    label_order_vo.orderLabelTask = order_label_task
    label_order_vo.supplierAccount = supplier_account
    label_order_vo.customerOrder = parcel_customer_order
    label_order_vo.product = product
    label_order_vo.service = service
    label_order_vo.supplier_butt = supplier_butt

    logger.info("className =" + str(class_name))
    # 通过反射实例化对象
    obj = globals()[class_name]()
    confirm_ship(obj, label_order_vo)

    logger.info(f'handler_confirm_parcel_label_task end--{mode_key}---{label_task_id}')


# 更新小包重量
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def update_parcel_task(self, mode_key):
    logger.info("-------------update_parcel_task--start-->" + mode_key)
    order_label_task_list = BigParcelLabelTask.objects.filter(status='UnHandled', del_flag=False, mode_key=mode_key,
                                                              handle_times__lt=121).reverse()[:20]
    if len(order_label_task_list) == 0:
        return

    for order_label_task in order_label_task_list:

        big_parcel_order = BigParcel.objects.get(id=order_label_task.order_num.id, del_flag=False)
        if not big_parcel_order:
            order_label_task.del_flag = True
            order_label_task.label_desc = '订单已删除'
            order_label_task.update_date = datetime.now()
            order_label_task.save()
            continue

        key = 'handler_update_parcel_' + big_parcel_order.parcel_num
        cache.set(key, "1", 60)

        logger.info("-------------update_parcel_task---->" + mode_key + '，---->' + big_parcel_order.parcel_num)

        if not big_parcel_order.product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            continue

        product = big_parcel_order.product
        if not product:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
            continue

        # 针对 asendia大包则直接返回 菜鸟大包需要更新重量，非菜鸟直接返回
        if product.code in ['ASENDIA-DB', 'DLDK-DB']:
            continue

        service = Service.objects.filter(product=product, is_default=True, del_flag=False).first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        if supplier_account_list.count() == 0:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置完成供应商信息')
            continue
        supplier_account = supplier_account_list[0]

        parcel_customer_order_queryset = ParcelCustomerOrder.objects.filter(big_parcel=big_parcel_order, del_flag=False)

        is_success = True
        for parcel_customer_order in parcel_customer_order_queryset:
            parcel = ParcelOrderParcel.objects.filter(customer_order=parcel_customer_order, del_flag=False).first()
            res = update_order(parcel_customer_order.third_orderNo,
                               str(int(parcel.label_weight * Decimal(1000))))

            if not res:
                is_success = False
                order_label_task.label_desc = '未知异常'
                break
            elif not res['success']:
                is_success = False
                order_label_task.label_desc = res['errorMsg']
                order_label_task.handle_times = 121
                break
            else:
                logger.info(res)

        if is_success:
            order_label_task.status = 'UPDATED'
            order_label_task.label_desc = ''

        order_label_task.update_date = datetime.now()
        order_label_task.save()
        cache.delete(key)

    logger.info("-------------update_parcel_task--end-->" + mode_key)


# 创建大包重量
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def create_big_parcel_label_task(self, mode_key):
    logger.info("-------------create_big_parcel_label_task--start-->" + mode_key)
    order_label_task_list = BigParcelLabelTask.objects.filter(status='UPDATED', del_flag=False, mode_key=mode_key,
                                                              handle_times__lt=121).reverse()[:20]
    if len(order_label_task_list) == 0:
        return

    for order_label_task in order_label_task_list:

        big_parcel_order = BigParcel.objects.get(id=order_label_task.order_num.id, del_flag=False)
        if not big_parcel_order:
            order_label_task.del_flag = True
            order_label_task.label_desc = '订单已删除'
            order_label_task.update_date = datetime.now()
            order_label_task.save()
            continue

        key = 'handler_create_parcel_' + big_parcel_order.parcel_num
        cache.set(key, "1", 60)

        logger.info(
            "-------------create_big_parcel_label_task---->" + mode_key + '，---->' + big_parcel_order.parcel_num)

        if not big_parcel_order.product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            continue

        product = big_parcel_order.product
        if not product:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
            continue

        if product.code in ['ASENDIA-DB', 'DLDK-DB']:  # 针对 非菜鸟的直接返回
            continue
        service = Service.objects.filter(product=product, is_default=True, del_flag=False).first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        if supplier_account_list.count() == 0:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置完成供应商信息')
            continue
        supplier_account = supplier_account_list[0]

        order_code_list = list(
            ParcelCustomerOrder.objects.filter(big_parcel=big_parcel_order, del_flag=False).values_list(
                'third_orderNo', flat=True))
        logger.info('create_big_parcel_label_task-->' + str(order_code_list))
        res = create_big_bag(order_code_list, str(float(big_parcel_order.audit_weight)))
        if res and res['success'] == 'true':
            order_label_task.status = 'HandledBy3rdNo'
            order_label_task.third_order_no = res['data']['bigBagCode']  # 大包单
            order_label_task.shipment_digest = res['data']['bigBagTrackingNumber']  # 大包单跟踪号
            order_label_task.update_date = datetime.now()
            order_label_task.save()
        else:
            order_label_task.label_desc = str(res)
            order_label_task.handle_times = 121
            order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            order_label_task.save()

        cache.delete(key)

    logger.info("-------------create_big_parcel_label_task--end-->" + mode_key)


# 创建大包 -- asendia
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def create_big_parcel_label_task_for_asendia(self, mode_key):
    logger.info("-------------create_big_parcel_label_task asendia--start-->" + mode_key)
    order_label_task_list = BigParcelLabelTask.objects.filter(status='UnHandled', del_flag=False, mode_key=mode_key,
                                                              handle_times__lt=121).reverse()[:20]
    if len(order_label_task_list) == 0:
        return

    for order_label_task in order_label_task_list:

        big_parcel_order = BigParcel.objects.get(id=order_label_task.order_num.id, del_flag=False)
        if not big_parcel_order:
            order_label_task.del_flag = True
            order_label_task.label_desc = '订单已删除'
            order_label_task.update_date = datetime.now()
            order_label_task.save()
            continue

        key = 'handler_create_parcel_' + big_parcel_order.parcel_num
        cache.set(key, "1", 60)

        logger.info(
            "-------------create_big_parcel_label_task asendia---->" + mode_key + '，---->' + big_parcel_order.parcel_num)

        if not big_parcel_order.product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            continue

        product = big_parcel_order.product
        if not product:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
            continue
        logger.info(f'debug xxxxxxxxxxxx 产品编码：{product.code}')
        if product.code != 'ASENDIA-DB':  # 针对 asendia大包， 非 asendia 直接跳过。
            continue

        service = Service.objects.filter(product=product, is_default=True, del_flag=False).first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        if supplier_account_list.count() == 0:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置完成供应商信息')
            continue
        supplier_account = supplier_account_list[0]

        # order_code_list = list(
        #     ParcelCustomerOrder.objects.filter(big_parcel=big_parcel_order, del_flag=False).values_list(
        #         'tracking_num', 'weighing_weight'))

        # 偷重的概念 传递的重量都要乘0.7（70%）
        order_code_list = list(
            ParcelCustomerOrder.objects.filter(big_parcel=big_parcel_order, del_flag=False).annotate(
                weighed_weight_90_percent=F('weighing_weight') * Decimal('0.7')
            ).values_list('tracking_num', 'weighed_weight_90_percent')
        )
        logger.info('create_big_parcel_label_task tracking_no list asendia-->' + str(order_code_list))
        dest_code = big_parcel_order.zone
        big_order_num = generate_big_parcel_num(dest_code)
        try:
            res = create_big_order_for_asendia(supplier_account, order_code_list, big_parcel_order, service.code,
                                               big_order_num)
        except Exception as e:
            logger.info(f'创建asendia大包单接口异常：{e}')
            order_label_task.label_desc = f'创建大包单失败：{str(e)}'
            order_label_task.handle_times = 121
            order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            order_label_task.save()
            return
        logger.info(f'创建asendia大包单接口返回结果：{res}')
        if res and res['Status'] == 'success':
            order_label_task.status = 'HandledBy3rdNo'
            # order_label_task.third_order_no = res['data']['bigBagCode']  # 大包单
            order_label_task.third_order_no = big_order_num  # 大包单号 对于 asendia 跟踪号等于大包单号
            order_label_task.update_date = datetime.now()
            order_label_task.save()
        else:
            error_msgs = res['ErrorMessage']
            if isinstance(error_msgs, list):
                for item in error_msgs:
                    err = item['Error']
                    if 'already exists' in err:
                        # 大包单号碰撞
                        order_label_task.label_desc = f'创建大包单失败：{str(res)}'
                        order_label_task.handle_times += 1  # 单号重复，则重新下一次请求
                        order_label_task.save()
                        break
                else:
                    order_label_task.label_desc = f'创建大包单失败：{str(res)}'
                    order_label_task.handle_times = 121
                    order_label_task.save()

            else:
                order_label_task.label_desc = f'创建大包单失败：{str(res)}'
                order_label_task.handle_times = 121
                order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                order_label_task.save()

        cache.delete(key)

    logger.info("-------------create_big_parcel_label_task asendia--end-->" + mode_key)


@app.task(bind=True, max_retries=3, base=QueueOnce, once={'graceful': True})
def create_big_parcel_label_task_for_rd_post(self, mode_key):
    # 瑞典邮政-- 创建大包
    logger.info("-------------create_big_parcel_label_task rd_post --start-->" + mode_key)
    order_label_task_list = BigParcelLabelTask.objects.filter(status='UnHandled', del_flag=False, mode_key=mode_key,
                                                              handle_times__lt=121).reverse()[:20]
    if len(order_label_task_list) == 0:
        return

    for order_label_task in order_label_task_list:

        big_parcel_order = BigParcel.objects.get(id=order_label_task.order_num.id, del_flag=False)
        if not big_parcel_order:
            order_label_task.del_flag = True
            order_label_task.label_desc = '订单已删除'
            order_label_task.update_date = datetime.now()
            order_label_task.save()
            continue

        key = 'handler_create_parcel_' + big_parcel_order.parcel_num
        cache.set(key, "1", 60)

        logger.info(
            "-------------create_big_parcel_label_task rd_post ---->" + mode_key + '，---->' + big_parcel_order.parcel_num)

        if not big_parcel_order.product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            continue

        product = big_parcel_order.product
        if not product:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
            continue
        logger.info(f'debug rd_post 产品编码：{product.code}')
        if product.code != 'DLDK-DB':  # 针对 瑞典邮政大包， 非 瑞典邮政大包 直接跳过。
            continue

        # 接口写死，不要这些信息
        # service = Service.objects.filter(product=product, is_default=True, del_flag=False).first()
        # supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        # supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        # if supplier_account_list.count() == 0:
        #     add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置完成供应商信息')
        #     continue
        # supplier_account = supplier_account_list[0]

        order_code_list = list(
            ParcelCustomerOrder.objects.filter(big_parcel=big_parcel_order, del_flag=False).values_list(
                'tracking_num', 'weighing_weight'))

        # 偷重的概念 传递的重量都要乘0.9（90%）
        # order_code_list = list(
        #     ParcelCustomerOrder.objects.filter(big_parcel=big_parcel_order, del_flag=False).annotate(
        #         weighed_weight_90_percent=F('weighing_weight') * Decimal('0.8')
        #     ).values_list('tracking_num', 'weighed_weight_90_percent')
        # )
        logger.info('create_big_parcel_label_task tracking_no list rd_post -->' + str(order_code_list))
        # dest_code = big_parcel_order.zone
        # big_order_num = generate_big_parcel_num(dest_code)
        try:
            res = rd_receptacel_upload(big_parcel_order.parcel_num, order_code_list, big_parcel_order)
            if 'responses' in res and res.get('responses')[0].get('status') == 0:
                label_data = res['responses'][0]['receptacle']['pdfReceptacleLable']
                big_tracking_num = res['responses'][0]['receptacle']['receptacleNo']
            else:
                logger.info(f'创建 rd_post 大包单接口失败：{res}')
                order_label_task.label_desc = f'创建大包单失败：{res}'
                order_label_task.handle_times = 121
                order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                order_label_task.save()
                return

        except Exception as e:
            logger.info(f'创建 rd_post 大包单接口异常：{e}')
            logger.info(traceback.format_exc())
            order_label_task.label_desc = f'创建大包单失败：{str(e)}'
            order_label_task.handle_times = 121
            order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            order_label_task.save()
            return

        logger.info(f'创建 rd_post 大包单接口返回结果：{res}')

        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + big_parcel_order.parcel_num + ".pdf"
        decoded_file_name = MEDIA_URL + label_url

        base_dir = os.path.dirname(decoded_file_name)
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)

        label_data = label_data  # base64
        with open(decoded_file_name, "wb") as code:
            code.write(base64.b64decode(label_data))

        order_label = BigParcelLabel()
        order_label.order_num = big_parcel_order
        order_label.tracking_no = big_tracking_num
        order_label.third_order_no = big_tracking_num
        order_label.label_url = label_url
        order_label.create_by = order_label_task.create_by
        order_label.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label.product = order_label_task.product
        order_label.save()

        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'
        order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label_task.save()

        cache.delete(key)

    logger.info("-------------create_big_parcel_label_task rd_post --end-->" + mode_key)


# 获取大包单面单
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def get_big_parcel_label_task(self, mode_key):
    '''
    创建小包面单定时任务
    :param modeKey:
    :return:
    '''
    logger.info("-------------get_big_parcel_label_task--start-->" + mode_key)
    order_label_task_list = BigParcelLabelTask.objects.filter(status='HandledBy3rdNo', del_flag=False,
                                                            mode_key=mode_key,
                                                            handle_times__lt=121).reverse()[:20]
    if len(order_label_task_list) == 0:
        return

    for order_label_task in order_label_task_list:

        big_parcel_order = BigParcel.objects.get(id=order_label_task.order_num.id, del_flag=False)
        if not big_parcel_order:
            order_label_task.del_flag = True
            order_label_task.label_desc = '订单已删除'
            order_label_task.update_date = datetime.now()
            order_label_task.save()
            continue

        key = 'handler_update_parcel_' + big_parcel_order.parcel_num
        cache.set(key, "1", 60)

        logger.info("-------------get_big_parcel_label_task---->" + mode_key + '，---->' + big_parcel_order.parcel_num)

        if not big_parcel_order.product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            continue

        product = big_parcel_order.product
        if not product:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
            continue

        if product.code in ['ASENDIA-DB', 'DLDK-DB']:  # 非菜鸟的直接跳过
            continue

        service = Service.objects.filter(product=product, is_default=True, del_flag=False).first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        if supplier_account_list.count() == 0:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置完成供应商信息')
            continue
        supplier_account = supplier_account_list[0]

        result = get_big_label(order_label_task.third_order_no, waybill_type=WaybillType.BIG_BAG_LABEL.value)

        response_data = result['data']

        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + big_parcel_order.parcel_num + ".pdf"
        decoded_file_name = MEDIA_URL + label_url

        base_dir = os.path.dirname(decoded_file_name)
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)

        label_data = response_data['waybillPdfData']
        with open(decoded_file_name, "wb") as code:
            code.write(base64.b64decode(label_data))

        order_label = BigParcelLabel()
        order_label.order_num = big_parcel_order
        order_label.tracking_no = response_data['trackingNumber']
        order_label.third_order_no = order_label_task.third_order_no
        order_label.label_url = label_url
        order_label.create_by = order_label_task.create_by
        order_label.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label.product = order_label_task.product
        order_label.save()

        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'
        order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label_task.save()

        cache.delete(key)

    logger.info("-------------get_big_parcel_label_task--end-->" + mode_key)


# 获取大包单面单 -- asendia
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def get_big_parcel_label_task_for_asendia(self, mode_key):
    '''
    创建小包面单定时任务
    :param mode_key:
    :return:
    '''
    logger.info("-------------get_big_parcel_label_task asendia--start-->" + mode_key)
    order_label_task_list = BigParcelLabelTask.objects.filter(status='HandledBy3rdNo', del_flag=False,
                                                             mode_key=mode_key,
                                                             handle_times__lt=121).reverse()[:20]
    if len(order_label_task_list) == 0:
        return

    for order_label_task in order_label_task_list:

        big_parcel_order = BigParcel.objects.get(id=order_label_task.order_num.id, del_flag=False)
        if not big_parcel_order:
            order_label_task.del_flag = True
            order_label_task.label_desc = '订单已删除'
            order_label_task.update_date = datetime.now()
            order_label_task.save()
            continue

        key = 'handler_update_parcel_' + big_parcel_order.parcel_num
        cache.set(key, "1", 60)

        logger.info(
            "-------------get_big_parcel_label_task asendia---->" + mode_key + '，---->' + big_parcel_order.parcel_num)

        if not big_parcel_order.product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            continue

        product = big_parcel_order.product
        if not product:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
            continue

        if product.code != 'ASENDIA-DB':  # 针对 asendia大包， 非 asendia 直接跳过
            continue

        service = Service.objects.filter(product=product, is_default=True, del_flag=False).first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        if supplier_account_list.count() == 0:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置完成供应商信息')
            continue
        supplier_account = supplier_account_list[0]
        try:
            result = get_big_order_label_for_asendia(supplier_account, order_label_task.third_order_no, service.code,
                                                     big_parcel_order.parcel_num)
        except Exception as e:
            logger.info(f'asendia 获取大包面单异常：{str(e)}')
            order_label_task.label_desc = f'获取大包单面单失败：{str(e)}'
            order_label_task.handle_times = 121
            order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            order_label_task.save()
            return
        label_data = result.content  # 二进制数据
        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + big_parcel_order.parcel_num + ".pdf"
        decoded_file_name = MEDIA_URL + label_url

        base_dir = os.path.dirname(decoded_file_name)
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        with open(decoded_file_name, "wb") as code:
            code.write(label_data)

        order_label = BigParcelLabel()
        order_label.order_num = big_parcel_order
        order_label.tracking_no = order_label_task.third_order_no
        order_label.third_order_no = order_label_task.third_order_no
        order_label.label_url = label_url
        order_label.create_by = order_label_task.create_by
        order_label.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label.product = order_label_task.product
        order_label.save()

        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'
        order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label_task.save()

        cache.delete(key)

    logger.info("-------------get_big_parcel_label_task asendia--end-->" + mode_key)


@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_push_label(self):
    logger.info('handler_push_label start -->' + settings.SYSTEM_ORDER_MARK)

    if settings.SYSTEM_ORDER_MARK != 'CQJYD':
        return

    order_label_task_list = ParcelOrderLabelTask.objects.filter(status='Success', del_flag=False,
                                                                is_push=False).reverse()[:20]

    for order_label_task in order_label_task_list:

        parcel_order_label_list = ParcelOrderLabel.objects.filter(order_num=order_label_task.order_num)
        parcel_order_label = parcel_order_label_list.first()
        file_url = 'http://' + settings.DOMAIN_URL + '/media/'
        label_url = file_url + parcel_order_label.label_url
        parcel_customer_order = order_label_task.order_num
        if parcel_customer_order.buyer_country_code == 'MX':
            service_code = 'ESTAFETA'
        else:
            service_code = 'USPS'

        result = push_label(parcel_customer_order.customer_order_num, parcel_order_label.tracking_no, label_url,
                            service_code)

        if result['msg'] == 'success':
            order_label_task.is_push = True
            order_label_task.save()
        elif str(result['code']) == '410':
            order_label_task.is_push = True
            order_label_task.remark = '订单已取消'
            order_label_task.save()

            parcel_customer_order.save_fields(order_status='VO', remark='订单已取消')

        else:
            logger.info(parcel_customer_order.customer_order_num + '--推送失败-->>>' + str(result))

    logger.info('handler_push_label end')


@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_push_error_label_info(self):
    logger.info('handler_push_error_label_info start -->' + settings.SYSTEM_ORDER_MARK)

    if settings.SYSTEM_ORDER_MARK != 'CQJYD':
        return

    order_label_task_list = ParcelOrderLabelTask.objects.filter(status='UnHandled', handle_times__gt=121,
                                                                del_flag=False, is_push=False).reverse()[:20]
    for order_label_task in order_label_task_list:
        parcel_customer_order = order_label_task.order_num
        result = push_error_label(parcel_customer_order.customer_order_num, '', '400', order_label_task.label_desc)
        order_label_task.is_push = True
        order_label_task.save()

    logger.info('handler_push_error_label_info end')


# Todo: 按投保申请接口新增属性进行修改
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
@distributed_lock_ship
def create_insurance_order_task(self, mode_key):
    '''
    创建保险单定时任务
    :param mode_key:
    :return:
    '''
    logger.info(f"-------------create_insurance_order_task---->{mode_key}")

    if settings.SYSTEM_ORDER_MARK not in ['MM', 'FX']:
        return

    order_task_list = InsuranceOrderTask.objects.filter(status='UnHandled', del_flag=False, mode_key=mode_key,
                                                        handle_times__lt=121).order_by('mode_key')[:20]
    if len(order_task_list) == 0:
        return

    for order_label_task in order_task_list:

        # InsuranceOrder
        insurance_order_order = order_label_task.order_num

        if insurance_order_order.insurance_status == 'VO':
            order_label_task.del_flag = True
            order_label_task.label_desc = '订单已作废'
            order_label_task.update_date = datetime.now()
            order_label_task.save()
            continue

        key = 'handler_label_' + insurance_order_order.trade_no
        cache.set(key, "1", 60)

        logger.info(f"-------------create_insurance_order_task-{mode_key}--->{insurance_order_order.trade_no}")

        product = insurance_order_order.product
        if not product:
            add_fail_label_task(order_label_task, '未选择产品，请选择产品')
            continue

        supplier_butt = SupplierButt.objects.get(id=product.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        if supplier_account_list.count() == 0:
            add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置完成供应商信息')
            continue
        supplier_account = supplier_account_list[0]

        person_queryset = InsuranceOrderPerson.objects.filter(customer_order_num=insurance_order_order, del_flag=False)
        insurance_object_queryset = InsuranceObject.objects.filter(customer_order_num=insurance_order_order,
                                                                   del_flag=False)

        class_name = upper_first(supplier_butt.class_name)

        insurance_order_vo = InsuranceOrderVo()
        insurance_order_vo.orderLabelTask = order_label_task
        insurance_order_vo.supplierAccount = supplier_account
        insurance_order_vo.product = product
        # insurance_order_vo.service = service
        insurance_order_vo.supplier_butt = supplier_butt
        insurance_order_vo.customerOrder = insurance_order_order
        insurance_order_vo.applicant_person = person_queryset.filter(person_type='APPLICANT').first()
        insurance_order_vo.insurance_object_list = insurance_object_queryset

        logger.info(f"insurance className ={class_name}")

        # 通过反射实例化对象
        obj = globals()[class_name]()
        create_insurance_order(obj, insurance_order_vo)


# 获取二次面单
# @transaction.atomic
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_get_secondary_label(self):
    print("---start--handler_get_secondary_label--")
    order_label_task_list = ParcelOrderLabelTask.objects.filter(pull_status='NOT_PULL',
                                                                is_secondary=True,
                                                                status='Success',
                                                                del_flag=False)[:100]

    for order_label_task in order_label_task_list:
        with transaction.atomic():
            customer_order = ParcelCustomerOrder.objects.get(id=order_label_task.order_num.id)
            logger.info("--get_parcel_label_task--order_num-->" + str(customer_order.order_num))
            key = 'handler_label_' + customer_order.order_num
            cache.set(key, "1", 60)

            if not customer_order.product:
                add_fail_label_task(order_label_task, '未选择产品，请选择产品')
                continue
            logger.info("--productId-->" + str(customer_order.product.id))
            product = Product.objects.get(id=customer_order.product.id)

            parcelList = ParcelOrderParcel.objects.filter(customer_order=customer_order.id, del_flag=0)

            parcel_id_list = [parcel.id for parcel in parcelList]

            parcelItemList = ParcelOrderItem.objects.filter(parcel_num__in=parcel_id_list)

            if not customer_order.service:
                add_fail_label_task(order_label_task, '产品[' + product.code + ']未配置服务')
                continue
            service = customer_order.service

            # 如果是虚拟产品，以最小成本的产品打单
            if product.is_virtual:
                product = customer_order.real_product
                service = Service.objects.filter(product=product, del_flag=False).first()

            supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
            supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id,
                                                                       del_flag=False)
            supplier_account = supplier_account_list[0]

            class_name = supplier_butt.class_name

            label_order_vo = LabelOrderVo()
            label_order_vo.orderLabelTask = order_label_task
            label_order_vo.supplierAccount = supplier_account
            label_order_vo.customerOrder = customer_order
            label_order_vo.service = service
            label_order_vo.product = product
            label_order_vo.parcelList = parcelList
            label_order_vo.parcelItemList = parcelItemList
            label_order_vo.supplier_butt = supplier_butt

            # 通过反射实例化对象
            obj = globals()[class_name]()
            get_secondary_label(obj, label_order_vo)

            order_label_queryset = ParcelOrderLabel.objects.filter(order_num=customer_order, del_flag=False)
            if order_label_queryset.count() == len(parcelList):
                i = 0
                for order_label in order_label_queryset:
                    parcel = parcelList[i]
                    parcel.tracking_num = order_label.tracking_no
                    parcel.save()
                    i += 1

    print("---end--handler_get_secondary_label--")


# 获取邮政订单
@transaction.atomic
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_ems_order(self):
    if settings.SYSTEM_ORDER_MARK + settings.PARCEL_ORDER_MARK not in ['ZHPHD']:
        return

    current_date = datetime.now().strftime("%Y%m%d")
    # current_date = (datetime.now() - timedelta(days=2)).strftime('%Y%m%d')  # todo 邮政拉18号的单
    logger.info("----handler_ems_order----start---->" + current_date)

    supplier_queryset = SupplierButtAccount.objects.filter(type='G', del_flag=False)
    for supplier in supplier_queryset:
        # product_codes = ['TKUSQP001', 'TKUSQE001', 'BKZHQP001', 'THUSQP001', 'THUSCAQE001']
        product_codes = ['ZHUSTHE001', 'ZHWTKP001', 'ZHUSTHP001']
        for product_code in product_codes:
            logger.info("----handler_ems_order----productcode---->" + product_code)
            handler_ems_get_order(current_date, supplier, product_code)
            # handler_ems_get_order('********', supplier, product_code)

    logger.info("----handler_ems_order----end---->" + current_date)


@distributed_lock_ship
def handler_ems_get_order(current_date, supplier, product_code):
    logger.info(f'handler_ems_get_order-start-->{current_date}')
    user = UserProfile()
    user.id = 1
    for page in range(1, 20000):
        result = get_order(current_date, supplier, product_code, page)
        if 'success' in result.keys() and result['success'] == 'T':
            if not result['orders']:
                logger.info(f'handler_ems_get_order-end-->{current_date}')
                return
            for order in result['orders']:

                orderid = order['orderid']
                label_billid = order['mailNum']
                order_queryset = ParcelCustomerOrder.objects.filter(
                    ~Q(order_status='VO') &
                    (Q(customer_order_num=orderid) | Q(order_num=orderid)) &
                    Q(del_flag=False)
                )
                if order_queryset.count() > 0:
                    logger.info(f'ems_get_order 客户单号已存在------>>{orderid}--->> {label_billid}')
                    parcel_customer_order = order_queryset.first()
                    if not parcel_customer_order.subsidiary_organ_code:
                        parcel_customer_order.save_fields(subsidiary_organ_code=order.get('subsidiaryOrganCode', ''),
                                                          subsidiary_organ_name=order.get('subsidiaryOrganName', ''))
                    continue

                ems_order_querset = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'), label_billid=label_billid,
                                                                       del_flag=False)
                if ems_order_querset.count() > 0:
                    logger.info(f'ems_get_order AG单号已存在------>>{orderid}--->> {label_billid}')
                    continue

                parcel_customer_order = ParcelCustomerOrder()
                parcel_customer_order.customer = supplier.office_id
                parcel_customer_order.create_by = user
                parcel_customer_order.update_by = user
                parcel_customer_order.order_source = 'EMS_ZJ'
                parcel_customer_order.create_date = datetime.now()
                parcel_customer_order.update_date = datetime.now()
                parcel_customer_order.order_time = order['createTime']
                parcel_customer_order.subsidiary_organ_code = order.get('subsidiaryOrganCode', '')
                parcel_customer_order.subsidiary_organ_name = order.get('subsidiaryOrganName', '')
                parcel_customer_order.customer_order_num = orderid
                parcel_customer_order.label_billid = label_billid
                parcel_customer_order.tracking_num = label_billid
                product_code = order['productcode']
                product_queryset = Product.objects.filter(code=product_code, del_flag=False)
                if product_queryset.count() > 0:
                    product = product_queryset.first()
                    parcel_customer_order.product = product
                    service_queryset = Service.objects.filter(product=product, del_flag=False, is_default=True)
                    parcel_customer_order.service = service_queryset.first()
                    if product.address_num:
                        address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
                        if address_queryset.count() > 0:
                            parcel_customer_order.warehouse_code = address_queryset.first()
                else:
                    parcel_customer_order.remark = '无此产品编码:' + product_code
                # 揽收类型，0：用户自送（默认），1：上门揽收
                # parcel_customer_order.product = order['clcttype']
                # 退回类型，01：退回（默认），02：丢弃，03：保证支付退回的邮件
                # parcel_customer_order.label_billid = order['untread']

                parcel_customer_order.weight = Decimal(str(order['totalweight']))
                # 内件性质，00：一般商品（默认），01：带电
                # parcel_customer_order.weight = order['goodnature']
                # 报关种类，00 - 一般报关（默认）， 01: 9610 报关
                # parcel_customer_order.weight = order['declarationType']

                sender = order['sender']
                parcel_customer_order.contact_name = sender['name']
                parcel_customer_order.contact_email = sender.get('email', '')
                parcel_customer_order.contact_phone = sender['phone']
                parcel_customer_order.country_code = sender['country']
                parcel_customer_order.state_code = sender['province']
                parcel_customer_order.city_code = sender['city']
                parcel_customer_order.postcode = sender['postcode']
                # parcel_customer_order.house_no = ''
                # 判断街道地址(对应我们address_one)长度,超过100，去除空格

                address_one_len = len(sender['street'])
                if address_one_len > 100:
                    logger.info(f'orderid->>{orderid} 的street 字符太长,超过100个字符 {sender["street"]}')
                    street_res = sender['street'].replace(' ', '')
                else:
                    street_res = sender['street']
                parcel_customer_order.address_one = street_res
                parcel_customer_order.address_two = sender.get('county', '')
                parcel_customer_order.company_name = sender['company']

                receiver = order['receiver']
                parcel_customer_order.buyer_name = receiver['name']
                parcel_customer_order.buyer_mail = receiver['name']
                parcel_customer_order.buyer_phone = receiver.get('phone', '')
                parcel_customer_order.buyer_country_code = receiver['country']
                parcel_customer_order.buyer_country = receiver['country']
                buyer_state = receiver['province']
                if len(buyer_state) > 50:
                    logger.info(f'orderid->>{orderid} 的province 字符太长,超过50个字符 {buyer_state}')
                    continue
                parcel_customer_order.buyer_state = buyer_state
                parcel_customer_order.buyer_city_code = receiver['city']
                parcel_customer_order.buyer_city = receiver['city']
                parcel_customer_order.buyer_postcode = receiver['postcode']
                parcel_customer_order.buyer_house_num = ''
                parcel_customer_order.buyer_address_one = receiver['street']
                parcel_customer_order.buyer_address_two = ''
                parcel_customer_order.buyer_company_name = receiver['company']
                parcel_customer_order.buyer_tax = receiver.get('taxcode', '')
                # parcel_customer_order.pull_status = 'NOT_PUSH'
                parcel_customer_order.order_status = 'WO'
                parcel_customer_order.save()
                gen_parcel_order_num(parcel_customer_order)
                parcel_customer_order.save()

                # 这里添加 将数据存入扩展表中
                ParcelOrderExtend.objects.create(customer_order=parcel_customer_order)

                parcel = ParcelOrderParcel()
                if 'length' in order.keys():
                    parcel.parcel_length = order['length']
                else:
                    parcel.parcel_length = 10
                if 'width' in order.keys():
                    parcel.parcel_width = order['width']
                else:
                    parcel.parcel_width = 10
                if 'height' in order.keys():
                    parcel.parcel_height = order['height']
                else:
                    parcel.parcel_height = 10

                parcel.parcel_num = '1'
                parcel.parcel_qty = 1
                parcel.parcel_weight = Decimal(order['totalweight'])
                parcel.create_by = user
                parcel.update_by = user
                parcel.create_date = datetime.now()
                parcel.update_date = datetime.now()
                parcel.customer_order = parcel_customer_order
                if order['goodnature'] == '01':
                    parcel.is_electronic = True
                else:
                    parcel.is_electronic = False
                parcel.save()

                for item in order['items']:
                    parcel_item = ParcelOrderItem()
                    item_code = item['enname']
                    if len(item_code) > 49:
                        item_code = item_code[:49]
                    parcel_item.item_code = item_code
                    parcel_item.item_name = item_code
                    parcel_item.sale_currency = 'USD'
                    parcel_item.sale_price = item['delcarevalue']
                    parcel_item.declared_currency = 'USD'
                    parcel_item.declared_price = item['delcarevalue']
                    parcel_item.declared_nameCN = item['cnname']
                    parcel_item.declared_nameEN = item['enname']
                    # parcel_item.item_length = item['enname']
                    # parcel_item.item_width = item['enname']
                    # parcel_item.item_height = item['enname']
                    # parcel_item.item_volume = item['enname']
                    parcel_item.item_weight = item['weight']
                    parcel_item.item_qty = item['count']
                    parcel_item.customs_code = item.get('hscode', '')

                    # parcel_item.texture = item['enname']
                    # parcel_item.item_size = item['enname']
                    # parcel_item.use = item['enname']
                    # parcel_item.brand = item['enname']
                    # parcel_item.model = item['enname']
                    parcel_item.parcel_num = parcel
                    parcel_item.create_by = user
                    parcel_item.update_by = user
                    parcel_item.create_date = datetime.now()
                    parcel_item.update_date = datetime.now()
                    parcel_item.save()

                # 计算成本收入
                if parcel_customer_order.product and parcel_customer_order.product.is_valuation:
                    add_revenue(parcel_customer_order, user, ParcelOrderChargeIn)
                if parcel_customer_order.product and parcel_customer_order.product.is_cost_valuation:
                    add_cost(parcel_customer_order, user, ParcelOrderChargeOut)

                # 从客户账号扣钱
                if parcel_customer_order.product and parcel_customer_order.product.is_valuation:
                    deduction_account(parcel_customer_order, user, ParcelOrderChargeIn)

                # 创建下单任务
                create_order_label_task(parcel_customer_order, user, ParcelOrderLabelTask, ParcelOrderParcel)


# ems 推送
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_push_ems_receiver_lading(self):
    logger.info('handler_push_ems_receiver_lading start')

    master_order_push_task_queryset = MasterOrderPushTask.objects.filter(status='UnHandled',
                                                                         handle_times__lt=121,
                                                                         del_flag=False)

    for master_order_push_task in master_order_push_task_queryset:
        print(master_order_push_task.master_order_no)
        handler_master_order_push_task(master_order_push_task)

    logger.info('handler_push_ems_receiver_lading end')


# 获取马帮订单
@exception_handler
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_mabang_order(self):
    logger.info(" handler_mabang_order start---->")

    if not settings.MABANG_API_KEY or not settings.MABANG_ACCOUNT_ID:
        return

    result_data = get_mabang_order(settings.MABANG_API_KEY, settings.MABANG_ACCOUNT_ID, 1)
    if 'Data' in result_data.keys():
        total_page = math.ceil(int(result_data['Data']['pagination']['totalCount']) / int(
            result_data['Data']['pagination']['rowsPerPage']))
        if total_page > 0:
            for page in range(1, total_page + 1):
                logger.info(f" handler_mabang_order start---->{page}")
                results = get_mabang_order(settings.MABANG_API_KEY, settings.MABANG_ACCOUNT_ID, page)
                if 'Data' in results.keys() and 'orders' in results['Data'].keys():
                    i = 0
                    for order in results['Data']['orders']:
                        try:
                            orderid = order['code']
                            logger.info(str(i) + "-----orderid--->>" + str(orderid))
                            i += 1

                            order_querset = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                                               customer_order_num=orderid,
                                                                               del_flag=False)
                            if order_querset.count() > 0:
                                continue

                            if 'customer' not in order.keys() or 'logisticsKeys' not in order['customer'].keys():
                                logger.info(str(orderid) + '--mabang not customer ')
                                continue
                            logisticsKeys = order['customer']['logisticsKeys']
                            mabang_account_name = settings.MABANG_ACCOUNT_NAME
                            if mabang_account_name not in logisticsKeys.keys() or 'api_key' not in logisticsKeys[
                                mabang_account_name].keys():
                                logger.info(f'{str(orderid)}--mabang not {mabang_account_name} ')
                                continue
                            api_key = logisticsKeys[mabang_account_name]['api_key']
                            if not api_key:
                                logger.info(str(orderid) + '--mabang not api_key ')
                                continue

                            user_queryset = UserProfile.objects.filter(username=api_key)
                            if user_queryset.count() == 0:
                                logger.info(str(orderid) + f'--mabang not user: {api_key}')
                                continue

                            product_code = order['myExpressChannel']['customerCode']
                            sender = order['addressPickup']
                            if len(sender['address']) > 100:
                                processMessage = str(orderid) + ' 发货人地址1太长: ' + str(sender['address'])
                                logger.info(processMessage)
                                exception_order(orderid, processMessage, settings.MABANG_API_KEY,
                                                settings.MABANG_ACCOUNT_ID)
                                continue

                            receiver = order['addressReceive']
                            if len(receiver['street1']) > 100:
                                processMessage = str(orderid) + ' 收件人地址1太长: ' + str(receiver['street1'])
                                logger.info(processMessage)
                                exception_order(orderid, processMessage, settings.MABANG_API_KEY,
                                                settings.MABANG_ACCOUNT_ID)
                                continue

                            if len(receiver['city']) > 50:
                                processMessage = str(orderid) + ' 收件人城市太长: ' + str(receiver['city'])
                                logger.info(processMessage)
                                exception_order(orderid, processMessage, settings.MABANG_API_KEY,
                                                settings.MABANG_ACCOUNT_ID)
                                continue

                            set_parcel_customer_order(order, orderid, product_code, receiver, sender, user_queryset)

                        except Exception as e:
                            logger.error(traceback.format_exc())
                            logger.info(' handler_mabang_order --error->>' + str(e))
                            continue

                logger.info(f" handler_mabang_order end---->{page}")

    logger.info("----handler_mabang_order----end---->")


# 封装并保存马帮订单
@transaction.atomic
def set_parcel_customer_order(order, orderid, product_code, receiver, sender, user_queryset):
    logger.info('set_parcel_customer_order start')
    user = user_queryset.first()
    parcel_customer_order = ParcelCustomerOrder()
    parcel_customer_order.customer = user.company
    parcel_customer_order.create_by = user
    parcel_customer_order.update_by = user
    parcel_customer_order.create_date = datetime.now()
    parcel_customer_order.update_date = datetime.now()
    parcel_customer_order.order_time = order['timeCreated']
    parcel_customer_order.customer_order_num = orderid
    # 未确认
    parcel_customer_order.pull_status = 'NO_ACCEPT'
    product_queryset = Product.objects.filter(code=product_code, del_flag=False)
    if product_queryset.count() > 0:
        product = product_queryset.first()
        parcel_customer_order.product = product
        service_queryset = Service.objects.filter(product=product, del_flag=False, is_default=True)
        parcel_customer_order.service = service_queryset.first()
        if product.address_num:
            address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
            if address_queryset.count() > 0:
                parcel_customer_order.warehouse_code = address_queryset.first()
    else:

        exception_order(orderid, f'无此产品编码:{product_code}', settings.MABANG_API_KEY, settings.MABANG_ACCOUNT_ID)
        raise ValueError({"code": 400, "detail": f'无此产品编码:{product_code}'})

    parcel_customer_order.weight = Decimal(str(order['weightForcast'])) / Decimal('1000')
    if order['vatType'] == 1:
        parcel_customer_order.ioss_num = order['vatNo']
    else:
        parcel_customer_order.buyer_tax = order['vatNo']
    parcel_customer_order.contact_name = sender['contact']
    if is_valid_email(str(sender['email'])):
        parcel_customer_order.contact_email = sender['email']
    parcel_customer_order.contact_phone = sender['telephone']
    parcel_customer_order.country_code = sender['countryCode']
    parcel_customer_order.state_code = sender['province']
    parcel_customer_order.city_code = sender['city']
    parcel_customer_order.postcode = sender['zipcode']
    # parcel_customer_order.house_no = ''
    parcel_customer_order.address_one = sender['address']
    # parcel_customer_order.address_two = sender['county']
    parcel_customer_order.company_name = sender.get('companyName', '')

    # 如果长度超过100 则截取
    rec_buyer_name = receiver['receiver']
    if len(rec_buyer_name) > 100:
        buyer_name = rec_buyer_name[:100]
        logger.info(f'set_parcel_customer_order --rec_buyer_name--{rec_buyer_name}')
    else:
        buyer_name = rec_buyer_name

    parcel_customer_order.buyer_name = buyer_name

    parcel_customer_order.buyer_mail = receiver.get('email', '')
    parcel_customer_order.buyer_phone = receiver.get('telephone', '')
    parcel_customer_order.buyer_country_code = receiver['countryCode']
    parcel_customer_order.buyer_country = receiver['countryCode']
    parcel_customer_order.buyer_state = receiver['province']
    parcel_customer_order.buyer_city_code = receiver['city']
    parcel_customer_order.buyer_city = receiver['city']
    parcel_customer_order.buyer_postcode = receiver['zipcode']
    parcel_customer_order.buyer_house_num = receiver.get('doorcode', '')
    parcel_customer_order.buyer_address_one = receiver['street1']
    parcel_customer_order.buyer_address_two = receiver.get('street2', '')
    parcel_customer_order.buyer_company_name = receiver.get('companyStreet', '')
    parcel_customer_order.buyer_tax = ''
    parcel_customer_order.order_status = 'WO'
    parcel_customer_order.order_source = 'MaBang'
    parcel_customer_order.save()
    gen_parcel_order_num(parcel_customer_order)
    parcel_customer_order.save()

    # 这里添加 将数据存入扩展表中
    ParcelOrderExtend.objects.create(customer_order=parcel_customer_order)

    try:

        if get_productBasicRestriction(product, 'process_gb_postcode', parcel_customer_order.buyer_country_code):
            parcel_customer_order.buyer_postcode = process_gb_postcode(parcel_customer_order.buyer_postcode)

        # 是否合并地址1和地址2
        if get_productBasicRestriction(product, 'is_merge_address_one_two', parcel_customer_order.buyer_country_code) \
                and parcel_customer_order.buyer_address_two:
            parcel_customer_order.buyer_address_one += ' ' + parcel_customer_order.buyer_address_two
            parcel_customer_order.buyer_address_two = ''

        # 限制客户
        handler_product_limit_user(parcel_customer_order.customer, product)

        check_prop_limit(parcel_customer_order, parcel_customer_order.product)
        # 验证数据
        data = model_to_dict(parcel_customer_order)
        logger.info('验证马帮数据： start' + str(data))
        check_order_data(data, parcel_customer_order.product)

    except Exception as e:
        exception_order(orderid, str(e), settings.MABANG_API_KEY, settings.MABANG_ACCOUNT_ID)
        raise ValueError({"code": 400, "detail": str(e)})

    # 创建包裹
    parcel = ParcelOrderParcel()
    if 'length' in order.keys() and order['length'] > 0:
        parcel.parcel_length = Decimal(order.get('length', '1')) / 10
    else:
        parcel.parcel_length = 5
    if 'width' in order.keys() and order['width'] > 0:
        parcel.parcel_width = Decimal(order.get('width', '1')) / 10
    else:
        parcel.parcel_width = 5
    if 'height' in order.keys() and order['height'] > 0:
        parcel.parcel_height = Decimal(order.get('height', '1')) / 10
    else:
        parcel.parcel_height = 5
    parcel.parcel_num = order['packageId']
    parcel.parcel_qty = 1
    parcel.parcel_weight = Decimal(order['weightForcast']) / 1000
    parcel.create_by = user
    parcel.update_by = user
    parcel.create_date = datetime.now()
    parcel.update_date = datetime.now()
    parcel.customer_order = parcel_customer_order
    if order['hasBattery'] == '1':
        parcel.is_electronic = True
    else:
        parcel.is_electronic = False
    parcel.save()
    for item in order['itemList']:
        parcel_item = ParcelOrderItem()
        parcel_item.item_code = item['sku']
        parcel_item.item_name = item.get('productName', '')
        parcel_item.sale_currency = 'USD'
        parcel_item.sale_price = item['declareValue']
        parcel_item.declared_currency = 'USD'
        parcel_item.declared_price = item['declareValue']
        parcel_item.declared_nameCN = item['declareNameCn']
        parcel_item.declared_nameEN = item['declareNameEn']
        parcel_item.item_length = Decimal(item.get('length', '1')) / 10
        parcel_item.item_width = Decimal(item.get('width', '1')) / 10
        parcel_item.item_height = Decimal(item.get('height', '1')) / 10
        # parcel_item.item_volume = item['enname']
        parcel_item.item_weight = Decimal(item.get('weight', '0')) / 1000
        parcel_item.item_qty = item['quantity']

        parcel_item.texture = item.get('commodityMaterial', '')
        # parcel_item.item_size = item['enname']
        parcel_item.use = item.get('commodityUse', '')
        # parcel_item.brand = item['enname']
        # parcel_item.model = item['enname']
        parcel_item.customs_code = item.get('hsCode', '')
        parcel_item.parcel_num = parcel
        parcel_item.create_by = user
        parcel_item.update_by = user
        parcel_item.create_date = datetime.now()
        parcel_item.update_date = datetime.now()
        parcel_item.save()

    label_type_handle(parcel_customer_order, user)
    logger.info('set_parcel_customer_order end')


# 回传跟踪号给马帮
@transaction.atomic
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_accept_mabang_order(self):
    if not settings.MABANG_API_KEY or not settings.MABANG_ACCOUNT_ID:
        return

    logger.info("----handler_accept_mabang_order----start---->")
    parcel_order_queryset = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'), order_source='MaBang',
                                                               pull_status='NO_ACCEPT', del_flag=False)

    for parcel_order in parcel_order_queryset:
        logger.info(f'handler_accept_mabang_order --->{parcel_order.order_num}')
        if not parcel_order.tracking_num:
            logger.info("查无跟踪号-->>>" + parcel_order.order_num)
            continue

        finePath = settings.STATIC_MEDIA_DIR
        fileUrl = 'http://' + settings.DOMAIN_URL + '/media/'
        product = parcel_order.product

        if product.label_type == 'HW' and not parcel_order.is_weighing:
            hlyz_product = Product.objects.filter(code='HLYZ', del_flag=False).first()
            order_label_task_list = ParcelOrderLabelTask.objects.filter(product=hlyz_product,
                                                                        order_num=parcel_order,
                                                                        del_flag=False).order_by('-id')
            if order_label_task_list.count() == 0:
                continue
            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                continue
            if order_label_task.status == 'VO':
                continue
            order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_order, del_flag=False)
            order_label_list = order_label_list.filter(product=hlyz_product)
            if len(order_label_list) == 0:
                continue

            orders = []
            orders.append(assemble_barcode_params(parcel_order))
            base64_str = create_barcodes_for_order(orders)
            fineName = parcel_order.order_num + '_c.pdf'
            finePath += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            fileUrl += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            dir = os.path.dirname(finePath)
            if not os.path.exists(dir):
                os.makedirs(dir)
            with open(finePath, 'wb') as f:
                f.write(base64.b64decode(base64_str))

        # elif product.label_type == 'ZW' and not parcel_order.is_weighing:
        elif product.label_type in ('ZW', 'WWWC') and not parcel_order.is_weighing:
            orders = []
            orders.append(assemble_barcode_params(parcel_order))
            base64_str = create_barcodes_for_order(orders)
            fineName = parcel_order.order_num + '_c.pdf'
            finePath += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            fileUrl += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            dir = os.path.dirname(finePath)
            if not os.path.exists(dir):
                os.makedirs(dir)
            with open(finePath, 'wb') as f:
                f.write(base64.b64decode(base64_str))

        else:
            orderLabelList = ParcelOrderLabel.objects.filter(order_num=parcel_order, del_flag=False)
            # if parcel_order.tracking_num and parcel_order.tracking_num != parcel_order.label_billid:
            #     orderLabelList = orderLabelList.filter(is_secondary=True)

            if len(orderLabelList) == 0:
                logger.info("查无面单-->>>" + parcel_order.order_num)
                continue

            elif len(orderLabelList) == 1:
                orderLabel = orderLabelList[0]
                finePath += orderLabel.label_url
                fileUrl += orderLabel.label_url
            else:
                pdf_merger = PdfFileMerger()

                fineName = parcel_order.order_num + '_m.pdf'
                finePath += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
                fileUrl += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
                dir = os.path.dirname(finePath)
                if not os.path.exists(dir):
                    os.makedirs(dir)
                i = 0
                for orderLabel in orderLabelList:
                    pdf_merger.merge(i, settings.STATIC_MEDIA_DIR + orderLabel.label_url)

                    i += 1
                pdf_merger.write(finePath)

        result = accept_tracking_num(parcel_order.customer_order_num, parcel_order.order_num, parcel_order.tracking_num,
                                     fileUrl, settings.MABANG_API_KEY, settings.MABANG_ACCOUNT_ID)
        if 'Message' in result.keys() and result['Message'] == 'SUCCESS':
            ParcelCustomerOrder.objects.filter(id=parcel_order.id).update(pull_status='ACCEPT')

    logger.info("----handler_accept_mabang_order----end---->")


# 获取通途订单
@exception_handler
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_tongtool_order(self):
    logger.info(" handler_tongtool_order start---->")

    app_key = Dict.objects.filter(label='tong_tu_app_key', del_flag=False).first()
    secret_access_key = Dict.objects.filter(label='tong_tu_secret_access_key', del_flag=False).first()
    if not app_key or not secret_access_key:
        return
    app_key = app_key.value
    secret_access_key = secret_access_key.value
    tong_tool_obj = TongToolUtil(app_key, secret_access_key)
    next_token, i = None, None

    while next_token or i is None:
        result_data = tong_tool_obj.get_tong_tool_order(next_token=next_token)
        orders = result_data.get('datas', dict()).get('orderArray', [])
        next_token = result_data.get('datas', dict()).get('nextToken', None)
        i = 0
        if result_data.get('code', -1) != 200 or not orders:
            return

        for order in orders:
            try:
                orderid = order['ttPacketId']
                i += 1
                logger.info(str(i) + "-----orderid--->>" + str(orderid))

                order_querset = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                                   customer_order_num=orderid,
                                                                   del_flag=False)
                if order_querset.count() > 0:
                    continue

                username = None
                for apiInfo in order['apiParamArray']:
                    if apiInfo['apiKey'] == 'username':
                        username = apiInfo['apiValue']
                if not username:
                    logger.info(str(orderid) + '--tongtool not username ')
                    continue

                user_queryset = UserProfile.objects.filter(username=username)
                if user_queryset.count() == 0:
                    logger.info(str(orderid) + '--tongtool not user_queryset ')
                    continue

                product_code = order['shippingMethodCode']
                if len(order['senderAddress1']) > 100:
                    processMessage = str(orderid) + ' 发货人地址1太长: ' + str(order['senderAddress1'])
                    logger.info(processMessage)
                    tong_tool_obj.exception_order(orderid, processMessage)
                    continue
                # if len(order['senderAddress2']) > 100:
                #     processMessage = str(orderid) + ' 发货人地址2太长: ' + str(order['senderAddress2'])
                #     logger.info(processMessage)
                #     tong_tool_obj.exception_order(orderid, processMessage)
                #     continue

                if len(order['recipientAddress1']) > 100:
                    processMessage = str(orderid) + ' 收件人地址1太长: ' + str(order['recipientAddress1'])
                    logger.info(processMessage)
                    tong_tool_obj.exception_order(orderid, processMessage)
                    continue

                if len(order['recipientAddress2']) > 100:
                    processMessage = str(orderid) + ' 收件人地址2太长: ' + str(order['recipientAddress2'])
                    logger.info(processMessage)
                    tong_tool_obj.exception_order(orderid, processMessage)
                    continue

                if len(order['recipientCity']) > 50:
                    processMessage = str(orderid) + ' 收件人城市太长: ' + str(order['recipientCity'])
                    logger.info(processMessage)
                    tong_tool_obj.exception_order(orderid, processMessage)
                    continue

                set_tong_tool_parcel_customer_order(order, orderid, product_code, tong_tool_obj, user_queryset)

            except Exception as e:
                logger.error(traceback.format_exc())
                logger.info(' handler_tongtool_order --error->>' + str(e))
                continue

            logger.info(f" handler_tongtool_order end---->{i}")

    logger.info("----handler_tongtool_order----end---->")


# 封装并保存通途订单
@transaction.atomic
def set_tong_tool_parcel_customer_order(order, orderid, product_code, tong_tool_obj, user_queryset):
    user = user_queryset.first()
    parcel_customer_order = ParcelCustomerOrder()
    parcel_customer_order.customer = user.company
    parcel_customer_order.create_by = user
    parcel_customer_order.update_by = user
    parcel_customer_order.create_date = datetime.now()
    parcel_customer_order.update_date = datetime.now()
    parcel_customer_order.order_time = datetime.now()
    parcel_customer_order.customer_order_num = orderid
    # 未确认
    parcel_customer_order.pull_status = 'NO_ACCEPT'
    product_queryset = Product.objects.filter(code=product_code, del_flag=False)
    if product_queryset.count() > 0:
        product = product_queryset.first()
        parcel_customer_order.product = product
        service_queryset = Service.objects.filter(product=product, del_flag=False, is_default=True)
        parcel_customer_order.service = service_queryset.first()
        if product.address_num:
            address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
            if address_queryset.count() > 0:
                parcel_customer_order.warehouse_code = address_queryset.first()
    else:
        tong_tool_obj.exception_order(orderid, f'无此产品编码:{product_code}')
        raise ValueError({"code": 400, "detail": f'无此产品编码:{product_code}'})

    declaration_array = order.get('declarationArray', [])
    parcel_customer_order.weight = Decimal(
        sum([i['declareNumber'] * i['declareWeight'] for i in declaration_array])) / Decimal('1000')
    # if order['vatType'] == 1:
    #     parcel_customer_order.ioss_num = order['vatNo']
    # else:
    #     parcel_customer_order.buyer_tax = order['vatNo']
    parcel_customer_order.ioss_num = order.get('iossNo', '')
    parcel_customer_order.buyer_tax = order.get('vatNo', '')
    parcel_customer_order.contact_name = order['senderName']
    if is_valid_email(str(order['senderEmail'])):
        parcel_customer_order.contact_email = order['senderEmail']
    parcel_customer_order.contact_phone = order['senderTelephone'] or order['senderMobile']
    parcel_customer_order.country_code = order['senderCountry']
    parcel_customer_order.state_code = order['senderState']
    parcel_customer_order.city_code = order['senderCity']
    parcel_customer_order.postcode = order['senderPostalCode']
    # parcel_customer_order.house_no = ''
    parcel_customer_order.address_one = order['senderAddress1']
    parcel_customer_order.address_two = order['senderAddress2']
    parcel_customer_order.company_name = order.get('senderCompany', '')
    parcel_customer_order.buyer_name = order['recipientName']
    parcel_customer_order.buyer_mail = order.get('recipientEmail', '')
    parcel_customer_order.buyer_phone = order.get('recipientTelephone', '') or order.get('recipientMobile', '')
    parcel_customer_order.buyer_country_code = order['recipientCountry']
    parcel_customer_order.buyer_country = order['recipientCountryEnName']
    parcel_customer_order.buyer_state = order['recipientState']
    parcel_customer_order.buyer_city_code = order['recipientCity']
    parcel_customer_order.buyer_city = order['recipientCity']
    parcel_customer_order.buyer_postcode = order['recipientPostalCode']
    # parcel_customer_order.buyer_house_num = receiver.get('doorcode', '')
    parcel_customer_order.buyer_address_one = order['recipientAddress1']
    parcel_customer_order.buyer_address_two = order.get('recipientAddress2', '')
    parcel_customer_order.buyer_company_name = order.get('recipientCompany', '')
    # parcel_customer_order.buyer_tax = ''
    parcel_customer_order.order_status = 'WO'
    parcel_customer_order.order_source = 'TongTool'
    parcel_customer_order.save()
    gen_parcel_order_num(parcel_customer_order)
    parcel_customer_order.save()

    try:

        if get_productBasicRestriction(product, 'process_gb_postcode', parcel_customer_order.buyer_country_code):
            parcel_customer_order.buyer_postcode = process_gb_postcode(parcel_customer_order.buyer_postcode)

        # 是否合并地址1和地址2
        if get_productBasicRestriction(product, 'is_merge_address_one_two', parcel_customer_order.buyer_country_code) \
                and parcel_customer_order.buyer_address_two:
            parcel_customer_order.buyer_address_one += ' ' + parcel_customer_order.buyer_address_two
            parcel_customer_order.buyer_address_two = ''

        # 限制客户
        handler_product_limit_user(parcel_customer_order.customer, product)

        check_prop_limit(parcel_customer_order, parcel_customer_order.product)
        # 验证数据
        data = model_to_dict(parcel_customer_order)
        logger.info('验证通途数据： start' + str(data))
        check_order_data(data, parcel_customer_order.product)

    except Exception as e:
        tong_tool_obj.exception_order(orderid, str(e))
        raise ValueError({"code": 400, "detail": str(e)})

    # 创建包裹
    parcel = ParcelOrderParcel()
    if 'length' in order.keys() and order['length'] > 0:
        parcel.parcel_length = Decimal(order.get('length', '1'))
    else:
        parcel.parcel_length = 5
    if 'width' in order.keys() and order['width'] > 0:
        parcel.parcel_width = Decimal(order.get('width', '1'))
    else:
        parcel.parcel_width = 5
    if 'height' in order.keys() and order['height'] > 0:
        parcel.parcel_height = Decimal(order.get('height', '1'))
    else:
        parcel.parcel_height = 5
    parcel.parcel_num = order['ttPacketId']
    parcel.parcel_qty = 1
    parcel.parcel_weight = Decimal(sum([i['declareNumber'] * i['declareWeight'] for i in declaration_array])) / Decimal(
        '1000')
    parcel.create_by = user
    parcel.update_by = user
    parcel.create_date = datetime.now()
    parcel.update_date = datetime.now()
    parcel.customer_order = parcel_customer_order
    # if order['hasBattery'] == '1':
    #     parcel.is_electronic = True
    # else:
    #     parcel.is_electronic = False
    parcel.save()
    for item in order['declarationArray']:
        parcel_item = ParcelOrderItem()
        parcel_item.item_code = item['goodsSku']
        parcel_item.item_name = item.get('declareEnName', '')
        parcel_item.sale_currency = item.get('declareCurrency', 'USD')
        parcel_item.sale_price = item['declareValue']
        parcel_item.declared_currency = item.get('declareCurrency', 'USD')
        parcel_item.declared_price = item['declareValue']
        parcel_item.declared_nameCN = item['declareCnName']
        parcel_item.declared_nameEN = item['declareEnName']
        # parcel_item.item_length = Decimal(item.get('length', '1')) / 10
        # parcel_item.item_width = Decimal(item.get('width', '1')) / 10
        # parcel_item.item_height = Decimal(item.get('height', '1')) / 10
        # parcel_item.item_volume = item['enname']
        parcel_item.item_weight = Decimal(item.get('declareWeight', '0')) / 1000
        parcel_item.item_qty = item['declareNumber']

        parcel_item.texture = item.get('material', '')
        # parcel_item.item_size = item['enname']
        parcel_item.use = item.get('purpose', '')
        # parcel_item.brand = item['enname']
        # parcel_item.model = item['enname']
        parcel_item.customs_code = item.get('hsCode', '')
        parcel_item.parcel_num = parcel
        parcel_item.create_by = user
        parcel_item.update_by = user
        parcel_item.create_date = datetime.now()
        parcel_item.update_date = datetime.now()
        parcel_item.save()

    label_type_handle(parcel_customer_order, user)


# 回传跟踪号给通途
@transaction.atomic
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_accept_tong_tool_order(self):
    app_key = Dict.objects.filter(label='AppKey', del_flag=False).first()
    secret_access_key = Dict.objects.filter(label='SecretAccessKey', del_flag=False).first()
    if not app_key or not secret_access_key:
        return
    app_key = app_key.value
    secret_access_key = secret_access_key.value
    tong_tool_obj = TongToolUtil(app_key, secret_access_key)

    logger.info("----handler_accept_tong_tool_order----start---->")
    parcel_order_queryset = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'), order_source='TongTool',
                                                               pull_status='NO_ACCEPT', del_flag=False)

    for parcel_order in parcel_order_queryset:
        logger.info(f'handler_accept_tong_tool_order --->{parcel_order.order_num}')
        if not parcel_order.tracking_num:
            logger.info("查无跟踪号-->>>" + parcel_order.order_num)
            continue

        finePath = settings.STATIC_MEDIA_DIR
        fileUrl = 'http://' + settings.DOMAIN_URL + '/media/'
        product = parcel_order.product

        if product.label_type == 'HW' and not parcel_order.is_weighing:
            hlyz_product = Product.objects.filter(code='HLYZ', del_flag=False).first()
            order_label_task_list = ParcelOrderLabelTask.objects.filter(product=hlyz_product,
                                                                        order_num=parcel_order,
                                                                        del_flag=False).order_by('-id')
            if order_label_task_list.count() == 0:
                continue
            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                continue
            if order_label_task.status == 'VO':
                continue
            order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_order, del_flag=False)
            order_label_list = order_label_list.filter(product=hlyz_product)
            if len(order_label_list) == 0:
                continue

            orders = []
            orders.append(assemble_barcode_params(parcel_order))
            base64_str = create_barcodes_for_order(orders)
            fineName = parcel_order.order_num + '_c.pdf'
            finePath += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            fileUrl += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            dir = os.path.dirname(finePath)
            if not os.path.exists(dir):
                os.makedirs(dir)
            with open(finePath, 'wb') as f:
                f.write(base64.b64decode(base64_str))

        # elif product.label_type == 'ZW' and not parcel_order.is_weighing:
        elif product.label_type in ('ZW', 'WWWC') and not parcel_order.is_weighing:
            orders = []
            orders.append(assemble_barcode_params(parcel_order))
            base64_str = create_barcodes_for_order(orders)
            fineName = parcel_order.order_num + '_c.pdf'
            finePath += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            fileUrl += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
            dir = os.path.dirname(finePath)
            if not os.path.exists(dir):
                os.makedirs(dir)
            with open(finePath, 'wb') as f:
                f.write(base64.b64decode(base64_str))

        else:
            orderLabelList = ParcelOrderLabel.objects.filter(order_num=parcel_order, del_flag=False)
            # if parcel_order.tracking_num and parcel_order.tracking_num != parcel_order.label_billid:
            #     orderLabelList = orderLabelList.filter(is_secondary=True)

            if len(orderLabelList) == 0:
                logger.info("查无面单-->>>" + parcel_order.order_num)
                continue

            elif len(orderLabelList) == 1:
                orderLabel = orderLabelList[0]
                finePath += orderLabel.label_url
                fileUrl += orderLabel.label_url
            else:
                pdf_merger = PdfFileMerger()

                fineName = parcel_order.order_num + '_m.pdf'
                finePath += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
                fileUrl += 'label/' + (datetime.now().strftime("%Y/%m/%d/")) + fineName
                dir = os.path.dirname(finePath)
                if not os.path.exists(dir):
                    os.makedirs(dir)
                i = 0
                for orderLabel in orderLabelList:
                    pdf_merger.merge(i, settings.STATIC_MEDIA_DIR + orderLabel.label_url)

                    i += 1
                pdf_merger.write(finePath)

        result = tong_tool_obj.accept_tracking_num(parcel_order.customer_order_num, parcel_order.tracking_num, fileUrl)
        if int(result.get('code', -1)) == 200:
            ParcelCustomerOrder.objects.filter(id=parcel_order.id).update(pull_status='ACCEPT')

    logger.info("----handler_accept_tong_tool_order----end---->")


# 根据产品的面单类型填写转单号或直接计算成本
def label_type_handle(parcel_customer_order, user):
    product = parcel_customer_order.product

    if product.label_type == 'CPW':
        # 客户推送面单类型，不生成抓单任务
        parcel_customer_order.save()
        # 计算成本收入
        if product and product.is_valuation:
            add_revenue(parcel_customer_order, user, ParcelOrderChargeIn)
        if product and product.is_cost_valuation:
            add_cost(parcel_customer_order, user, ParcelOrderChargeOut)

    # elif product.label_type == 'ZW':
    elif product.label_type in ('ZW', 'WWWC'):
        parcel_customer_order.tracking_num = parcel_customer_order.order_num
        parcel_customer_order.save()
    elif product.label_type == 'HW':
        parcel_customer_order.tracking_num = parcel_customer_order.order_num
        parcel_customer_order.save()
        add_label_task_by_product_type(parcel_customer_order, user)
    else:
        # 计算成本收入
        if product and product.is_valuation:
            add_revenue(parcel_customer_order, user, ParcelOrderChargeIn)
        if product and product.is_cost_valuation:
            add_cost(parcel_customer_order, user, ParcelOrderChargeOut)

        # 从客户账号扣钱
        if product and product.is_valuation:
            deduction_account(parcel_customer_order, user, ParcelOrderChargeIn)

        # 创建下单任务
        create_order_label_task(parcel_customer_order, user, ParcelOrderLabelTask, ParcelOrderParcel)


# 获取易仓订单
# @exception_handler
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_yicang_order(self):
    # 生产环境改成 MZ
    if settings.SYSTEM_ORDER_MARK not in ['MZ']:
        return

    logger.info("----handler_yicang_order----start---->")

    results = get_yicang_order(4)
    logger.info(f'获取易仓订单数据: {results}')
    if str(results.get('code')) != '200':
        logger.error(f"易仓拉取订单失败: {results.get('message')}")
    biz_content = json.loads(results.get('biz_content')) if results.get('biz_content') else None
    # print('biz_content-->', biz_content)
    # 如果没有获取到"已提交"订单则获取"缺货"订单
    if not biz_content or 'data' not in biz_content.keys():
        results = get_yicang_order(3)
        logger.info(f'获取易仓订单数据: {results}')
        if str(results.get('code')) != '200':
            logger.error(f"易仓拉取订单失败: {results.get('message')}")
        biz_content = json.loads(results.get('biz_content')) if results.get('biz_content') else None
    if biz_content and 'data' in biz_content.keys():
        i = 0
        for order in biz_content.get('data', []):
            try:
                orderid = order['reference_no']
                logger.info(str(i) + "-----yicang orderid-->>>" + str(orderid))
                i += 1
                order_queryset = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'), customer_order_num=orderid,
                                                                    po_no=order['warehouse_order_code'],
                                                                    del_flag=False)
                if order_queryset.count() > 0:
                    logger.info(f'易仓订单{orderid}已同步过了')
                    continue

                if 'service_provider_code' not in order.keys():
                    logger.info(f'{orderid}--yicang not customer ')
                    continue

                # sender = order['addressPickup']
                # if len(sender['address']) > 100:
                #     processMessage = str(orderid) + ' 发货人地址1太长: ' + str(sender['address'])
                #     logger.info(processMessage)
                #     # exception_order(orderid, processMessage)
                #     continue

                receiver = order['receipt_address']
                if len(receiver['street_address1']) > 100:
                    processMessage = str(orderid) + ' 收件人地址1太长: ' + str(receiver['street_address1'])
                    logger.info(processMessage)
                    # exception_order(orderid, processMessage)
                    push_order_status_yicang(order['warehouse_order_code'], 2, reason=processMessage)
                    continue

                if len(receiver['city']) > 50:
                    processMessage = str(orderid) + ' 收件人城市太长: ' + str(receiver['city'])
                    logger.info(processMessage)
                    push_order_status_yicang(order['warehouse_order_code'], 2, reason=processMessage)
                    continue

                service_id = 'ERP2011209ZN'
                user_queryset = UserProfile.objects.filter(username=service_id)

                set_parcel_customer_order_yicang(order, orderid, receiver, user_queryset)

            except Exception as e:
                logger.error(f'--handler_yicang_order--error->>{str(traceback.format_exc())}')
                continue
    else:
        logger.info('没有获取到易仓订单数据')

    print("----handler_yicang_order----end---->")


# 把转单号(跟踪号)推送给易仓
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_push_tracking_num_yicang(self):
    # 查询存在跟踪号的包裹就推送包裹跟踪号
    logger.info('---handler_push_tracking_num_yicang start-->')
    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'YQF']:
        return
    # 查询30天前的和30分钟后的轨迹
    # current_date = datetime.now()
    # one_month_ago = current_date - timedelta(days=30)
    # one_minutes_ago = current_date - timedelta(minutes=30)
    # TODO  待优化慢查询，建议加上时间条件
    order_list = ParcelCustomerOrder.objects.filter(order_source='YiCang',
                                                    push_status__in=['WAIT_PUSH', 'PUSH_FAIL'],
                                                    tracking_num__isnull=False,
                                                    del_flag=False)[:1000]

    for parcel_order in order_list:
        product = parcel_order.product
        if product and product.label_type == 'HW' and not parcel_order.is_weighing:
            hlyz_product = Product.objects.filter(code='HLYZ', del_flag=False).first()
            order_label_task_list = ParcelOrderLabelTask.objects.filter(product=hlyz_product,
                                                                        order_num=parcel_order,
                                                                        del_flag=False).order_by('-id')
            if order_label_task_list.count() == 0:
                continue
            order_label_task = order_label_task_list.first()
            if order_label_task.status in ['HandledBy3rdNo', 'UnHandled']:
                continue
            if order_label_task.status == 'VO':
                continue
            order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_order, del_flag=False)
            order_label_list = order_label_list.filter(product=hlyz_product)
            if len(order_label_list) == 0:
                continue
        logger.info(f'易仓小包单: {parcel_order.order_num}推送转单号: {parcel_order.tracking_num}给易仓,')

        # order_labels = ParcelOrderLabel.objects.filter(order_num=parcel_order, del_flag=False)
        # if not order_labels.exists():
        #     logger.error(f"订单{parcel_order.order_num}推送转单号给易仓没有面单")
        #     return
        # label_url = 'http://' + settings.DOMAIN_URL + '/media/'
        # label_url += order_labels.first().label_url

        # 文件转base64
        # order_label_list = ParcelOrderLabel.objects.filter(order_num=parcel_order, del_flag=False)
        # if not order_label_list.exists():
        #     logger.error(f"订单{parcel_order.order_num}推送转单号给易仓没有面单")
        #     return
        #
        # label_path = settings.STATIC_MEDIA_DIR
        # # file_url = 'http://' + settings.DOMAIN_URL + '/media/'
        #
        # for order_label in order_label_list:
        #     label_path_str = label_path + order_label.label_url
        #
        #     with open(label_path_str, 'rb') as f:
        #         base64_data = base64.b64encode(f.read())
        #         base64_str = base64_data.decode()

        base64_str = create_barcodes_for_order([assemble_barcode_params(parcel_order)])

        push_tracking_result = push_tracking_yicang(parcel_order.po_no, parcel_order.tracking_num, base64_str)
        logger.info(f'{parcel_order.order_num}推送转单号给易仓: {push_tracking_result}')
        if str(push_tracking_result.get('code')) != '200':
            logger.error(f"推送转单号给易仓失败: {push_tracking_result.get('message')}")
            parcel_order.push_status = 'PUSH_FAIL'
            return

        # service = get_service_from_product(parcel_order.product)
        # # 推送订单状态给易仓
        # push_status_result = push_order_status_yicang(parcel_order.order_num, 1,
        #                                               tracking_num=parcel_order.tracking_num,
        #                                               service_code=service.courier_code)
        # logger.info(f'推送订单状态给易仓: {push_status_result}')
        # if str(push_status_result.get('code')) != '200':
        #     logger.error(f"推送订单状态给易仓失败: {push_status_result.get('message')}")
        #     return
        parcel_order.push_status = 'PUSHED'
        parcel_order.update_date = datetime.now()
        parcel_order.save()

    logger.info('---handler_push_tracking_num_yicang end-->')


# 封装并保存易仓订单
@transaction.atomic
def set_parcel_customer_order_yicang(order, orderid, receiver, user_queryset):
    # 对重复客户单号进行逻辑删除
    old_parcel_order = ParcelCustomerOrder.objects.filter(~Q(order_status='VO'),
                                                          customer_order_num=orderid,
                                                          del_flag=False)
    if old_parcel_order.exists():
        old_parcel_order.update(del_flag=True)
    # 小包订单
    user = user_queryset.first()
    parcel_customer_order = ParcelCustomerOrder()
    parcel_customer_order.customer = user.company
    parcel_customer_order.create_by = user
    parcel_customer_order.update_by = user
    parcel_customer_order.create_date = datetime.now()
    parcel_customer_order.update_date = datetime.now()
    parcel_customer_order.order_time = order['add_time']
    parcel_customer_order.customer_order_num = orderid
    # address_queryset = Address.objects.filter(address_num=order['warehouse_code'], del_flag=False)
    # if address_queryset.exists():
    #     parcel_customer_order.warehouse_code = address_queryset.first()
    # 未确认
    parcel_customer_order.pull_status = 'NO_ACCEPT'
    product = order['channel_code']
    product_queryset = Product.objects.filter(code=product, del_flag=False)
    if product_queryset.exists():
        product = product_queryset.first()
        parcel_customer_order.product = product
        service_queryset = Service.objects.filter(product=product, del_flag=False, is_default=True)
        parcel_customer_order.service = service_queryset.first()
        if product.address_num:
            address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
            if address_queryset.count() > 0:
                parcel_customer_order.warehouse_code = address_queryset.first()
    else:
        parcel_customer_order.remark = '无此产品名称:' + product
    parcel_item_list = order['product_list']
    # parcel_customer_order.weight = \
    #     Decimal(str(product_list['product_weight'])) * Decimal(str(product_list['product_sku_qty'])) / Decimal('1000')
    # if order['vatType'] == 1:
    #     parcel_customer_order.ioss_num = order['vatNo']
    # else:
    #     parcel_customer_order.buyer_tax = order['vatNo']
    # 订单发件人
    # parcel_customer_order.contact_name = sender['contact']
    # parcel_customer_order.contact_email = sender['email']
    # parcel_customer_order.contact_phone = sender['telephone']
    # parcel_customer_order.country_code = sender['countryCode']
    # parcel_customer_order.state_code = sender['province']
    # parcel_customer_order.city_code = sender['city']
    # parcel_customer_order.postcode = sender['zipcode']
    # parcel_customer_order.address_one = sender['address']
    # parcel_customer_order.company_name = sender.get('companyName', '')

    # 订单收件人
    parcel_customer_order.buyer_name = receiver['first_name']
    parcel_customer_order.buyer_mail = receiver.get('email', '')
    parcel_customer_order.buyer_phone = receiver.get('phone', '')
    parcel_customer_order.buyer_country_code = receiver['country']
    parcel_customer_order.buyer_country = receiver['country']
    parcel_customer_order.buyer_state = receiver['state']
    parcel_customer_order.buyer_city_code = receiver['city']
    parcel_customer_order.buyer_city = receiver['city']
    parcel_customer_order.buyer_postcode = receiver['post_code']
    parcel_customer_order.buyer_house_num = receiver.get('door_plate', '')
    parcel_customer_order.buyer_address_one = receiver['street_address1']
    parcel_customer_order.buyer_address_two = receiver.get('street_address2', '')
    parcel_customer_order.buyer_company_name = receiver.get('company', '')
    parcel_customer_order.buyer_tax = ''

    parcel_customer_order.order_status = 'WO'
    parcel_customer_order.order_source = 'YiCang'
    parcel_customer_order.push_status = 'WAIT_PUSH'
    # 保存易仓唯一号
    parcel_customer_order.po_no = order['warehouse_order_code']
    # 先给订单重量赋初值
    parcel_customer_order.weight = 0
    parcel_customer_order.save()
    gen_parcel_order_num(parcel_customer_order)
    parcel_customer_order.save()

    # 对订单进行校验
    if parcel_customer_order.product:
        from order.serializers.parcel_customer_order import ParcelCustomerOrderSerializer
        parcel_customer_order_data = ParcelCustomerOrderSerializer(parcel_customer_order).data
        check_order_data(parcel_customer_order_data, parcel_customer_order.product)

    # 小包包裹
    parcel = ParcelOrderParcel()
    parcel.parcel_length = 5
    parcel.parcel_width = 5
    parcel.parcel_height = 5
    # parcel.parcel_num = product_list['ref_item_id']
    parcel.parcel_num = 'YC' + create_order_num(parcel_customer_order.id)
    parcel.parcel_qty = 1
    # parcel.parcel_weight = \
    #     Decimal(str(product_list['product_weight'])) * Decimal(str(product_list['product_sku_qty'])) / Decimal('1000')
    parcel.create_by = user
    parcel.update_by = user
    parcel.create_date = datetime.now()
    parcel.update_date = datetime.now()
    parcel.customer_order = parcel_customer_order
    # if product_list['hasBattery'] == '1':
    #     parcel.is_electronic = True
    # else:
    #     parcel.is_electronic = False
    parcel.save()

    # 小包商品
    for item in parcel_item_list:
        parcel_item = ParcelOrderItem()
        parcel_item.item_code = item['product_sku']
        parcel_item.item_name = item.get('product_title', '')
        parcel_item.sale_currency = 'USD'
        parcel_item.sale_price = item['declared_value']
        parcel_item.declared_currency = 'USD'
        parcel_item.declared_price = item['declared_value']
        parcel_item.declared_nameCN = item['product_title']
        parcel_item.declared_nameEN = item['product_title']
        parcel_item.item_length = 5
        parcel_item.item_width = 5
        parcel_item.item_height = 5
        parcel_item.item_weight = Decimal(item.get('product_weight', '0')) / 1000
        # parcel_item.item_qty = item['product_sku_qty']

        # parcel_item.texture = item.get('commodityMaterial', '')
        # parcel_item.use = item.get('commodityUse', '')
        parcel_item.customs_code = item.get('warehouse_product_barcode', '')
        parcel_item.parcel_num = parcel
        parcel_item.create_by = user
        parcel_item.update_by = user
        parcel_item.create_date = datetime.now()
        parcel_item.update_date = datetime.now()
        parcel_item.save()

    parcel_items = ParcelOrderItem.objects.filter(parcel_num=parcel, del_flag=False)
    parcel_customer_order.weight = parcel.parcel_weight = parcel_items.aggregate(total=Sum('item_weight'))['total'] or 0
    # parcel_customer_order.weight = parcel.parcel_weight = parcel_items.aggregate(
    #     total_weight_qty=Sum(F('item_weight') * F('item_qty'), output_field=DecimalField())
    # ).get('total_weight_qty') or 0
    print('parcel_customer_order.weight-->', parcel_customer_order.weight)
    parcel_customer_order.save()
    parcel.save()

    label_type_handle(parcel_customer_order, user)


# 小包推送51/17转单号
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True, 'keys': ['mode_key']})
def handler_push_parcel_track_info(self, mode_key):
    logger.info(f'---handler_push_parcel_track_info start-->{mode_key}')

    # 查询30天前的和30分钟后的轨迹
    current_date = datetime.now()
    one_month_ago = current_date - timedelta(days=30)
    one_minutes_ago = current_date - timedelta(minutes=10)

    parcel_track_list = ParcelTrack.objects.filter(push_status='WAIT_PUSH', del_flag=False,
                                                   create_date__gte=one_month_ago,
                                                   create_date__lt=one_minutes_ago,
                                                   mode_key=mode_key)[:1000]
    # 铭志的转单号需要批量推送, 接口有频率限制
    mz_parcel_tracks = []
    for parcel_track in parcel_track_list:
        logger.info('---handler_push_parcel_track_info start-->' + str(parcel_track.order_num))
        parcel_customer_order_list = ParcelCustomerOrder.objects.filter(order_num=parcel_track.order_num,
                                                                        del_flag=False)
        parcel_customer_order = parcel_customer_order_list.first()
        if not parcel_customer_order:
            logger.error(f'小包单未找到: {parcel_track.order_num}')
            continue
        order_num = parcel_customer_order.order_num
        parcel_track.push_status = 'NO_PUSH'
        if order_num.startswith('CQJYD'):
            push_track(parcel_customer_order.customer_order_num,
                       parcel_customer_order.tracking_num,
                       parcel_track, '')
            parcel_track.push_status = 'PUSHED'

        elif order_num.startswith('ZJ') or order_num.startswith('ZHS') or \
                settings.SYSTEM_ORDER_MARK + settings.PARCEL_ORDER_MARK in ['ZHPHD']:
            if parcel_customer_order.label_billid:
                token = '5C8FD11438DB11EF93D8B4055D1DA780'
                mail_num = parcel_customer_order.label_billid
                event_time = parcel_track.actual_time
                event_status = parcel_track.system_code
                event_country = ''
                event_city = parcel_track.location
                result = ems_push_track(token, mail_num, event_country, event_city, event_time, event_status)
                if result['message'] == "success" or result['errorMsg'] == '不允许上传重复数据!':
                    parcel_track.push_status = 'PUSHED'
                else:
                    parcel_track.push_status = 'PUSH_FAIL'
                    # parcel_track.remark = result['message']
                parcel_track.push_time = datetime.now()

            # 出库推送给51
            if parcel_track.system_code == 'outbound' and parcel_customer_order.tracking_num:

                parcel_order_label_queryset = ParcelOrderLabel.objects.filter(
                    tracking_no=parcel_customer_order.tracking_num,
                    del_flag=False)
                if parcel_order_label_queryset.count() > 0:
                    product = parcel_order_label_queryset.first().product or parcel_customer_order.product
                    if product:
                        logger.info('---handler_push_parcel_track_info-product-->>>' + product.name)
                        service_queryset = Service.objects.filter(product=product, del_flag=False)
                        if service_queryset.exists():
                            service = service_queryset.first()
                            track_supplier = service.trackSupplier
                            # if service.courier_code and settings.TRACKING51_API_KEY:
                            #     create_51tracking(parcel_customer_order.tracking_num, service.courier_code,
                            #                       settings.TRACKING51_API_KEY, parcel_customer_order.buyer_postcode)
                            #     parcel_track.push_status = 'PUSHED'
                            if track_supplier and track_supplier.class_name and track_supplier.class_name == 'Track51TrackService':
                                create_51tracking(parcel_customer_order.tracking_num, service.courier_code,
                                                  track_supplier.auth_pwd, parcel_customer_order.buyer_postcode)
                                parcel_track.push_status = 'PUSHED'
                            if track_supplier and track_supplier.class_name and track_supplier.class_name == 'Track17TrackService':
                                register_17tracking_number(
                                    [{'number': parcel_customer_order.tracking_num, 'carrier': service.courier_code}],
                                    track_supplier.auth_pwd)
                                parcel_track.push_status = 'PUSHED'

        elif order_num.startswith('MZ') and parcel_track.system_code == 'outbound' and \
                parcel_customer_order.tracking_num:
            mz_parcel_tracks.append(parcel_track)

        parcel_track.update_date = datetime.now()
        parcel_track.save()

    if mz_parcel_tracks:
        for parcel_track_index in range(0, len(mz_parcel_tracks), 40):
            mz_tracking_num = []
            # 接口单次最多只允许传40个转单号
            for parcel_track in mz_parcel_tracks[parcel_track_index: parcel_track_index + 40]:
                parcel_customer_order = ParcelCustomerOrder.objects.filter(order_num=parcel_track.order_num,
                                                                           del_flag=False).first()
                if not parcel_customer_order:
                    logger.error(f'17track小包: {parcel_track.order_num}找不到')
                    continue
                logger.info(f'17track推送小包订单号: {parcel_customer_order.order_num}, '
                            f'转单号: {parcel_customer_order.tracking_num}')
                # FBA订单的包裹对接17track需要对每个包裹选对运输商代码, 所以这里需要用户自己填写物流商编码, 小包可以根据产品上的默认服务的尾程物流商编码来
                service = get_service_from_product(parcel_customer_order.product)
                courier_code = get_17track_courier_code(service.courier_code)
                if not courier_code:
                    logger.error(f'17track开始推送小包订单号: {parcel_customer_order.order_num}, 找不到供应商编码')
                    continue
                mz_tracking_num.append({
                    'number': parcel_customer_order.tracking_num, 'carrier': courier_code
                })
            result = register_17tracking_number(mz_tracking_num)
            # print('result-->', result)

            accepted = result.get('data', {}).get('accepted', [])
            for item in accepted:
                tracking_number = item['number']
                parcel_order = ParcelCustomerOrder.objects.filter(tracking_num=tracking_number, del_flag=False).first()
                if not parcel_order:
                    logger.error(f'17track找不到转单号为 {tracking_number} 的小包单')
                    continue
                parcel_track = ParcelTrack.objects.filter(order_num=parcel_order.order_num, system_code='outbound',
                                                          del_flag=False).first()
                if not parcel_track:
                    logger.error(f'17track找不到订单号为 {parcel_order.order_num} 的出货包裹轨迹')
                    continue
                parcel_track.push_status = 'PUSHED'
                parcel_track.update_date = datetime.now()
                parcel_track.save()
                logger.info(f'17track小包: {parcel_track.order_num}上传转单号成功: {parcel_order.tracking_num}')
            rejected = result.get('data', {}).get('rejected', [])
            for item in rejected:
                error = item.get('error', {})
                tracking_number = item['number']
                parcel_order = ParcelCustomerOrder.objects.filter(tracking_num=tracking_number, del_flag=False).first()
                if not parcel_order:
                    logger.error(f'17track找不到转单号为 {tracking_number} 的小包单')
                    continue
                parcel_track = ParcelTrack.objects.filter(order_num=parcel_order.order_num, system_code='outbound',
                                                          del_flag=False).first()
                if not parcel_track:
                    logger.error(f'17track找不到订单号为 {parcel_order.order_num} 的出货包裹轨迹')
                    continue
                if error.get('code') == -18019901:
                    # 已经注册过了, 代表已推送
                    parcel_track.push_status = 'PUSHED'
                    parcel_track.update_date = datetime.now()
                    logger.info(f'17track小包: {parcel_track.order_num}已上传转单号: {parcel_order.tracking_num}')
                else:
                    parcel_track.push_status = 'PUSH_FAIL'
                    parcel_track.update_date = datetime.now()
                    parcel_track.remark = error_msg = error.get('message')
                    logger.error(f'17track小包: {parcel_track.order_num}上传转单号失败: {error_msg}')
                parcel_track.save()
            if not accepted and not rejected:
                errors = result.get('data', {}).get('errors')
                logger.error(f'17track小包异常: {errors}')
            time.sleep(3)

    logger.info('---handler_push_parcel_track_info end-->')


# 小包获取51轨迹
@app.task(bind=True, base=QueueOnce, max_retries=0, once={'graceful': True, 'keys': ['mode_key']})
def handler_get_51tracking_info(self, mode_key):
    logger.info('handler_get_51tracking_info start' + mode_key)

    if settings.SYSTEM_ORDER_MARK in ['MZ']:

        parcel_track_queryset = ParcelTrack.objects.filter(pull_status='WAIT_PULL',
                                                           system_code='outbound',
                                                           del_flag=False,
                                                           mode_key=mode_key,
                                                           pull_times=0)[:40]

        if parcel_track_queryset.count() == 0:
            return

        parcel_track_ids = [x.id for x in parcel_track_queryset]
        track_num_list = []
        for parcel_track in parcel_track_queryset:
            parcel_order = ParcelCustomerOrder.objects.filter(order_num=parcel_track.order_num, del_flag=False).first()
            if not parcel_order:
                ParcelTrack.objects.filter(id=parcel_track.id).update(pull_times=1, update_date=datetime.now())
                logger.error(f'17track未找到{parcel_track.parcel_num}小包包裹, 请检查')
            service = get_service_from_product(parcel_order.product)
            courier_code = get_17track_courier_code(service.courier_code)
            if not courier_code:
                logger.error(f'17track获取小包轨迹: {parcel_order.order_num}, 找不到供应商编码')
                continue
            track_num_list.append({
                "number": parcel_order.tracking_num,
                'carrier': courier_code,
            })
        logger.info(f'17track: 本次拉取转单号{track_num_list}的轨迹')
        # track_num_list = [{
        #     "number": '1ZC600A30318392000',
        #     'carrier': '100002',
        # }, {
        #     "number": '776098637441',
        #     'carrier': '100003',
        # }]
        res_data = get_17tracking(track_num_list)
        print('res_data22-->\n', res_data)
        if res_data.get('code') != 0 and not res_data.get('data'):
            ParcelTrack.objects.filter(id__in=parcel_track_ids).update(pull_times=1,
                                                                       update_date=datetime.now())
            return
        json_data = res_data['data']
        if not json_data.get('accepted'):
            ParcelTrack.objects.filter(id__in=parcel_track_ids).update(pull_times=1,
                                                                       update_date=datetime.now())
            return
        # 每一个tracking_number
        for data in json_data['accepted']:
            tracking_number = data['number']
            parcel_order = ParcelCustomerOrder.objects.filter(tracking_num=tracking_number, del_flag=False).first()
            if not parcel_order:
                logger.error(f'17track找不到转单号为 {tracking_number} 的小包单')
                continue
            parcel_track = ParcelTrack.objects.filter(order_num=parcel_order.order_num, system_code='outbound',
                                                      del_flag=False).first()
            track_info = data.get('track_info', {})
            if not track_info:
                ParcelTrack.objects.filter(order_num=parcel_order.order_num,
                                           del_flag=False).update(pull_times=1, update_date=datetime.now())
                continue

            providers = track_info.get('tracking', {}).get('providers', [])
            for provider in providers:
                for event in provider.get('events', []):
                    checkpoint_date = event['time_iso']
                    if len(checkpoint_date.split('-')) == 4 or '+' in str(checkpoint_date):
                        datetime_object = datetime.strptime(checkpoint_date, '%Y-%m-%dT%H:%M:%S%z')
                        checkpoint_date = datetime_object.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        datetime_object = datetime.strptime(checkpoint_date, '%Y-%m-%dT%H:%M:%S')
                        checkpoint_date = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                    # print('17track time_iso-->', checkpoint_date)
                    # print('17track location-->', event['location'])
                    # print('17track status-->', event['stage'])
                    delivery_status = event['stage']
                    # 有一部分轨迹没有轨迹代码, 但也需要保存
                    # if not delivery_status:
                    #     continue
                    # 如果是小包已签收, 则将小包单状态改为已签收
                    if delivery_status == 'Delivered':
                        parcel_track.pull_status = 'PULL_FINISH'
                        parcel_track.save()
                        update_order_status(parcel_order, checkpoint_date, 'SF')

                    # if TRACK17_MASK_WORD in event['description']:
                    track_old_queryset = ParcelTrack.objects.filter(order_num=parcel_order.order_num,
                                                                    system_code=delivery_status,
                                                                    actual_time=checkpoint_date,
                                                                    remark=event['description'])

                    if track_old_queryset.count() > 0:
                        # track_old_queryset.update(remark=event['description'])
                        logger.info(
                            f'17track小包单{parcel_order.order_num}最后的轨迹为: {delivery_status}, 还没有新的轨迹')
                        continue

                    parcel_track_new = ParcelTrack()
                    parcel_track_new.order_num = parcel_order.order_num
                    parcel_track_new.system_code = delivery_status
                    parcel_track_new.track_code = delivery_status
                    parcel_track_new.track_name = TRACK17_STATUS_CODES.get(delivery_status)
                    parcel_track_new.location = event['location']
                    parcel_track_new.actual_time = checkpoint_date
                    parcel_track_new.remark = event['description']
                    if TRACK17_MASK_WORD in event['description']:
                        parcel_track_new.del_flag = True
                    parcel_track_new.save()
                    logger.info(f'17track小包单{parcel_order.order_num}更新轨迹: {delivery_status}')

        ParcelTrack.objects.filter(id__in=parcel_track_ids).update(pull_times=1, update_date=datetime.now())
    logger.info('handler_get_51tracking_info end -->' + mode_key)


# @app.task(bind=True, base=QueueOnce)
# def handler_51tracking_pull_times(self):
#     ParcelTrack.objects.filter(pull_times=1, del_flag=False, pull_status='WAIT_PULL').update(pull_times=0)
#     TrackTask.objects.filter(pull_times=1, del_flag=False, pull_status='WAIT_PULL').update(pull_times=0)

# 查询60天前
# current_date = datetime.now()
# two_month_ago = current_date - timedelta(days=60)
# TrackTask.objects.filter(del_flag=False, pull_status='WAIT_PULL', create_date__lte=two_month_ago).update(pull_status='NOT_PULL', err_msg='超60天不拉取')


# FBA订单包裹推送17转单号
# @transaction.atomic
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_push_parcel_17_tracking_num(self, mode_key):
    # 查询存在跟踪号的包裹就推送包裹跟踪号
    logger.info(f'---handler_push_parcel_17_tracking_num start-->{mode_key}')

    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'YQF']:
        return

    # 查询30天前的和30分钟后的轨迹
    # current_date = datetime.now()
    # one_month_ago = current_date - timedelta(days=30)
    # one_minutes_ago = current_date - timedelta(minutes=30)

    # 包裹存在转单号, 则推送
    # parcel_list = Parcel.objects.filter(~Q(push_status_17='PUSHED'), tracking_num__isnull=False,
    #                                     courier_code__isnull=False, del_flag=False)[:2]
    # todo_x: CustomerParcelTrack 需要新增一个push_times字段, 用于记录推送次数和重试推送
    parcel_track_queryset = CustomerParcelTrack.objects.filter(push_status='WAIT_PUSH', track_code='PL', del_flag=False)
    if not parcel_track_queryset.exists():
        # 目前有很多需要推送的包裹没有创建初始轨迹, 需要做兼容
        parcel_list = Parcel.objects.filter(~Q(push_status_17='PUSHED'), tracking_num__isnull=False,
                                            courier_code__isnull=False, del_flag=False)[:10]
        for parcel in parcel_list:
            set_17track_task(parcel)
            parcel.push_status_17 = 'PUSHED'
            parcel.save()
        return

    mz_tracking_num = []
    # 接口单次最多只允许传40个转单号
    for parcel_track in parcel_track_queryset[:40]:
        parcel = Parcel.objects.filter(parcel_num=parcel_track.parcel_num, del_flag=False).first()
        if not parcel:
            logger.error(f'17trackFBA包裹号: {parcel_track.parcel_num}找不到')
            continue
        logger.info(f'17track开始推送FBA包裹号: {parcel.parcel_num}, 转单号: {parcel.tracking_num}')
        if not parcel.tracking_num or not parcel.courier_code:
            logger.info(
                f'17track推送FBA包裹号没有转单号或物流商编码, 包裹: {parcel.parcel_num}, 转单号: {parcel.tracking_num}, '
                f'物流商编码: {parcel.courier_code}')
            # parcel_track.push_status = 'PUSH_FAIL'
            # parcel_track.save()
            continue
        mz_tracking_num.append({
            'number': parcel.tracking_num, 'carrier': parcel.courier_code
        })
    result = register_17tracking_number(mz_tracking_num)
    # print('result-->', result)

    accepted = result.get('data', {}).get('accepted', [])
    for item in accepted:
        tracking_number = item['number']
        try:
            parcel = Parcel.objects.get(tracking_num=tracking_number, del_flag=False)
        except ObjectDoesNotExist:
            logger.error(f'17track找不到FBA包裹的转单号 {tracking_number}')
            continue
        except MultipleObjectsReturned:
            error_msg = f'17track找到多个FBA包裹的转单号 {tracking_number}'
            logger.error(error_msg)
            parcel_ids = Parcel.objects.filter(tracking_num=tracking_number,
                                               del_flag=False).values_list('id', flat=True)
            CustomerParcelTrack.objects.filter(parcel_num__in=parcel_ids, track_code='PL', del_flag=False).update(
                push_status='PUSH_FAIL',
                remark=error_msg,
                update_date=datetime.now()
            )
            continue
        parcel_track = CustomerParcelTrack.objects.filter(parcel_num=parcel.parcel_num, track_code='PL',
                                                          del_flag=False).first()
        if not parcel_track:
            logger.error(f'17track找不到FBA包裹号为 {parcel.parcel_num} 的已提柜包裹轨迹')
            continue
        parcel_track.push_status = 'PUSHED'
        parcel_track.update_date = datetime.now()
        parcel_track.save()
        logger.info(f'17trackFBA单号: {parcel_track.parcel_num}上传转单号成功: {parcel.tracking_num}')
    rejected = result.get('data', {}).get('rejected', [])
    for item in rejected:
        error = item.get('error', {})
        tracking_number = item['number']
        try:
            parcel = Parcel.objects.get(tracking_num=tracking_number, del_flag=False)
        except ObjectDoesNotExist:
            logger.error(f'17track找不到FBA包裹的转单号 {tracking_number}')
            continue
        except MultipleObjectsReturned:
            error_msg = f'17track找到多个FBA包裹的转单号 {tracking_number}'
            logger.error(error_msg)
            parcel_ids = Parcel.objects.filter(tracking_num=tracking_number,
                                               del_flag=False).values_list('id', flat=True)
            CustomerParcelTrack.objects.filter(parcel_num__in=parcel_ids, track_code='PL', del_flag=False).update(
                push_status='PUSH_FAIL',
                remark=error_msg,
                update_date=datetime.now()
            )
            continue
        parcel_track = CustomerParcelTrack.objects.filter(parcel_num=parcel.parcel_num, track_code='PL',
                                                          del_flag=False).first()
        if not parcel_track:
            logger.error(f'17track找不到FBA包裹号为 {parcel.parcel_num} 的已提柜包裹轨迹')
            continue
        if error.get('code') == -18019901:
            # 已经注册过了, 代表已推送
            parcel_track.push_status = 'PUSHED'
            parcel_track.update_date = datetime.now()
            logger.info(f'17trackFBA包裹: {parcel_track.parcel_num}已上传转单号: {parcel.tracking_num}')
        else:
            parcel_track.push_status = 'PUSH_FAIL'
            parcel_track.update_date = datetime.now()
            parcel_track.remark = error_msg = error.get('message')
            logger.error(f'17trackFBA上传转单号失败: {error_msg}')
        parcel_track.save()

    logger.info('---handler_push_parcel_17_track end-->')


# FBA订单包裹获取17轨迹
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_get_17track_info(self):
    logger.info('handler_get_17track_info start')

    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'YQF']:
        return
    # 查询所有存在转单号但是未完全获取轨迹的包裹
    # parcel_queryset_init = Parcel.objects.filter(~Q(pull_status_17='PULL_FINISH'),
    #                                              push_status_17='PUSHED',
    #                                              tracking_num__isnull=False,
    #                                              courier_code__isnull=False,
    #                                              del_flag=False,
    #                                              # mode_key=mode_key
    #                                              )
    parcel_track_queryset_init = CustomerParcelTrack.objects.filter(pull_status='WAIT_PULL',
                                                                    track_code='PL',
                                                                    del_flag=False,
                                                                    pull_times=0)
    if not parcel_track_queryset_init.exists():
        return
    parcel_track_queryset = parcel_track_queryset_init[:40]
    parcel_track_ids = [x.id for x in parcel_track_queryset]
    track_num_list = []
    with transaction.atomic():
        for parcel_track in parcel_track_queryset:
            parcel = Parcel.objects.filter(parcel_num=parcel_track.parcel_num, del_flag=False).first()
            if not parcel:
                CustomerParcelTrack.objects.filter(id=parcel_track.id).update(pull_times=1, update_date=datetime.now())
                logger.error(f'17track未找到{parcel_track.parcel_num}包裹, 请检查')
                continue
            track_num_list.append({
                "number": parcel.tracking_num,
                'carrier': parcel.courier_code,
            })
        logger.info(f'17track本次拉取转单号{track_num_list}的轨迹')
        # track_num_list = [{
        #     "number": '1ZC600A30318392000',
        #     'carrier': '100002',
        # }, {
        #     "number": '776098637441',
        #     'carrier': '100003',
        # }]
        res_data = get_17tracking(track_num_list)
        print('res_data22-->\n', res_data)
        if res_data.get('code') != 0 and not res_data.get('data'):
            CustomerParcelTrack.objects.filter(id__in=parcel_track_ids).update(pull_times=1, update_date=datetime.now())
            return
        json_data = res_data['data']
        if not json_data.get('accepted'):
            # CustomerParcelTrack.objects.filter(id__in=parcel_track_ids).update(
            #     pull_times=1, update_date=datetime.now())
            CustomerParcelTrack.objects.filter(id__in=parcel_track_ids).update(pull_times=1, update_date=datetime.now())
            return
        # 每一个tracking_number
        for data in json_data['accepted']:
            tracking_number = data['number']
            parcel = Parcel.objects.filter(tracking_num=tracking_number, del_flag=False).first()
            # parcel = parcel_queryset[0]
            if not parcel:
                logger.error(f'17track找不到转单号为 {tracking_number} 的包裹')
                continue
            parcel_track = CustomerParcelTrack.objects.filter(parcel_num=parcel.parcel_num, track_code='PL',
                                                              del_flag=False).first()
            track_info = data.get('track_info', {})
            if not track_info:
                CustomerParcelTrack.objects.filter(parcel_num=parcel.parcel_num,
                                                   del_flag=False).update(pull_times=1, update_date=datetime.now())
                continue

            providers = track_info.get('tracking', {}).get('providers', [])
            for provider in providers:
                for event in provider.get('events', []):
                    checkpoint_date = event['time_iso']
                    if len(checkpoint_date.split('-')) == 4 or '+' in str(checkpoint_date):
                        datetime_object = datetime.strptime(checkpoint_date, '%Y-%m-%dT%H:%M:%S%z')
                        checkpoint_date = datetime_object.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        datetime_object = datetime.strptime(checkpoint_date, '%Y-%m-%dT%H:%M:%S')
                        checkpoint_date = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                    # print('17track time_iso-->', checkpoint_date)
                    # print('17track location-->', event['location'])
                    # print('17track status-->', event['stage'])
                    delivery_status = event['stage']
                    # 有一部分轨迹没有轨迹代码, 但也需要保存
                    # if not delivery_status:
                    #     continue

                    # 包裹保存ascan time
                    if event['description'] == 'Processing at UPS Facility' or event['description'] == 'Picked up':
                        parcel.ascan_time = checkpoint_date
                        parcel.save()

                    if delivery_status == 'Delivered':
                        parcel_track.pull_status = 'PULL_FINISH'
                        parcel_track.save()
                        # 包裹保存dscan time
                        parcel.dscan_time = checkpoint_date
                        parcel.save()

                    # if TRACK17_MASK_WORD in event['description']:
                    track_old_queryset = CustomerParcelTrack.objects.filter(parcel_num=parcel.parcel_num,
                                                                            track_code=delivery_status,
                                                                            actual_time=checkpoint_date,
                                                                            remark=event['description'])

                    if track_old_queryset.exists():
                        # track_old_queryset.update(remark=event['description'])
                        logger.info(f'17track包裹{parcel.parcel_num}最后的轨迹为: {delivery_status}, 还没有新的轨迹')
                        continue

                    # 如果是FBA包裹, 则将第一个包裹的轨迹更新到订单轨迹中
                    customer_order = parcel.customer_order
                    order_first_parcel = Parcel.objects.filter(customer_order=customer_order,
                                                               del_flag=False).order_by('id').first()
                    if parcel == order_first_parcel:
                        params = {
                            'order_num': customer_order.order_num,
                            'order_id': customer_order.id,
                            'track_code': delivery_status,
                            'track_name': TRACK17_STATUS_CODES.get(delivery_status),
                            'actual_time': checkpoint_date,
                            'product': customer_order.product,
                            'remark': event['description']
                        }
                        Track.objects.create(**params)
                        # 如果包裹轨迹是已签收, 则将订单状态改为已签收
                        if delivery_status == 'Delivered':
                            update_order_status(customer_order, checkpoint_date, 'SF')

                    parcel_track_new = CustomerParcelTrack()
                    parcel_track_new.parcel_num = parcel.parcel_num
                    parcel_track_new.track_code = delivery_status
                    parcel_track_new.track_name = TRACK17_STATUS_CODES.get(delivery_status)
                    parcel_track_new.location = event['location']
                    parcel_track_new.actual_time = checkpoint_date
                    # parcel_track_new.push_status = 'WAIT_PUSH'
                    parcel_track_new.remark = event['description']
                    if TRACK17_MASK_WORD in event['description']:
                        parcel_track_new.del_flag = True
                    parcel_track_new.save()
                    logger.info(f'17track包裹{parcel.parcel_num}更新轨迹: {delivery_status}')

        CustomerParcelTrack.objects.filter(id__in=parcel_track_ids,
                                           del_flag=False).update(pull_times=1, update_date=datetime.now())
        # parcel_queryset.update(pull_status_17='PULL_FINISH', update_date=datetime.now())
    logger.info('handler_get_17track_info end')


# 定时修改拉取次数, 已拉取的就按时拉, 没有拉取的立即拉
@app.task(bind=True, max_retries=0, base=QueueOnce, once={'graceful': True})
def handler_17track_pull_times(self):
    logger.info(f'开始执行handler_17track_pull_times')
    CustomerParcelTrack.objects.filter(pull_times=1, del_flag=False, pull_status='WAIT_PULL').update(pull_times=0)


# @app.task(bind=True, base=QueueOnce)
# def handler_get_usps_info(self, mode_key):
#     logger.info(f'handler_get_usps_info start {mode_key}')
#     if settings.SYSTEM_ORDER_MARK not in ['MZ'] or True:
#         return
#
#     parcel_track_queryset = ParcelTrack.objects.filter(pull_status='WAIT_PULL',
#                                                        track_code='outbound',
#                                                        courier_code='USPS',
#                                                        del_flag=False,
#                                                        mode_key=mode_key,
#                                                        pull_times=0)[:100]
#
#     if parcel_track_queryset.count() == 0:
#         return
#
#     for parcel_track in parcel_track_queryset:
#         parcel_order_queryset = ParcelCustomerOrder.objects.filter(order_num=parcel_track.order_num, del_flag=False)
#         if not parcel_order_queryset:
#             continue
#
#         parcel_order = parcel_order_queryset.first()
#         json_data = get_usps_tracking(parcel_order.tracking_num)
#
#         if 'trackingEvents' not in json_data.keys() or not json_data['trackingEvents']:
#             return
#
#         for trackingEvent in json_data['trackingEvents']:
#
#             eventCode = trackingEvent['eventCode']
#             eventType = str(trackingEvent['eventType']).strip()
#             eventTimestamp = trackingEvent['eventTimestamp']
#             eventCity = trackingEvent['eventCity']
#
#             TrackCodes = {
#                 'PSR_GX': 'Shipping Label Created, USPS Awaiting Item',
#                 'SPH_OA': 'Accepted at USPS Regional Facility',
#                 'TRD_8': 'Arrived at USPS Regional Origin Facility',
#                 'TRD_DE': 'Departed USPS Regional Facility',
#                 'TRD_15': 'Arrived at USPS Regional Facility',
#                 'TRD_34': 'Arrived at USPS Facility',
#                 'TRD_EF': 'Departed USPS Facility',
#                 'TRD_6': 'Forwarded',
#                 'TRD_82': 'Processing at USPS Facility',
#                 'TRD_NT': 'In Transit to Next Facility',
#                 'TRD_AR': 'Arrived at Hub',
#                 'TRD_7': 'Arrived at Post Office',
#                 'OFD': 'Out for Delivery',
#                 'TRD_AS': 'Arrived at USPS Destination Facility',
#                 'DAT_73': 'Available for Pickup',
#                 'DLD': 'Delivered',
#                 'DAT_52': 'Held at Post Office, At Customer Request',
#                 'DAT_VR': 'Redelivery Scheduled for Next Business Day',
#                 'UDL_24': 'Forward Expired',
#                 'DAT_57': 'Delivery Exception, Local Weather Delay',
#                 'UDL_28': 'Return to Sender',
#                 'RSP': 'Return to Sender Processed',
#                 'UDL_22': 'Insufficient Address',
#                 'UDL_21': 'No Such Number',
#                 'RPD': 'Returned Parcel Delivered',
#                 'UDL_26': 'Vacant',
#             }
#
#             track_code = next((code for code, desc in TrackCodes.items() if desc == eventType), None) or eventCode
#
#             actual_time = datetime.strptime(eventTimestamp, '%Y-%m-%dT%H:%M:%SZ')
#
#             parcel_track_old_queryset = ParcelTrack.objects.filter(order_num=parcel_order.order_num,
#                                                                    track_code=track_code,
#                                                                    actual_time=actual_time,
#                                                                    del_flag=False)
#
#             if parcel_track_old_queryset.count() > 0:
#                 parcel_track_old_queryset.update(remark=eventType)
#                 continue
#
#             parcel_track_new = ParcelTrack()
#             parcel_track_new.order_num = parcel_order.order_num
#             parcel_track_new.track_code = track_code
#             parcel_track_new.track_name = eventType
#             parcel_track_new.location = eventCity or parcel_order.buyer_country_code
#             parcel_track_new.actual_time = actual_time
#             parcel_track_new.push_status = 'WAIT_PUSH'
#             parcel_track_new.remark = eventType
#             parcel_track_new.save()
#             if track_code in ['DLD', '01']:
#                 parcel_track.pull_status = 'PULL_FINISH'
#                 parcel_track.save()
#
#             ParcelTrack.objects.filter(id=parcel_track.id).update(pull_times=1, update_date=datetime.now())
#
#     logger.info('handler_get_usps_info end -->' + mode_key)


# 获取alita订单
@app.task(bind=True, base=QueueOnce)
def handler_get_alita_order(self):
    if not settings.IS_SYNC_ALITA_ORDER:
        return

    orders = get_orders('ZJUSRL-M,US-70465-M,US-70464-M')

    if not orders or not orders['order_nums']:
        return

    for order_num in orders['order_nums']:

        order_detail = get_order_detail(order_num)

        parcel_list = order_detail.pop('parcel', [])
        parcel_order_labels = order_detail.pop('parcelOrderLabels', [])
        order_num = order_detail.pop('order_num', '')
        create_by = order_detail.pop('create_by', '')
        update_by = order_detail.pop('update_by', '')
        warehouse_code = order_detail.pop('warehouse_code', None)
        customer = order_detail.pop('customer', None)
        product = order_detail.pop('product', None)
        service = order_detail.pop('service', None)
        real_product = order_detail.pop('real_product', None)
        id = order_detail.pop('id', None)

        old_order_queryset = ParcelCustomerOrder.objects.filter(order_num='US' + order_num, del_flag=False)
        if old_order_queryset:
            print('已存在' + order_num)
            continue

        if not parcel_list:
            print('无包裹' + order_num)
            continue

        if not parcel_order_labels:
            print('无面单' + order_num)
            continue

        handler_save_alita_order(order_detail, order_num, parcel_list, parcel_order_labels)


@transaction.atomic
@exception_handler
def handler_save_alita_order(order_detail, order_num, parcel_list, parcel_order_labels):
    user = UserProfile()
    user.id = 1
    product = Product.objects.filter(code='SYNC_PRODUCT', del_flag=False).first()
    service = Service.objects.filter(product=product, del_flag=False).first()
    params = {
        'create_by': user,
        'update_by': user,
    }
    order_detail['product'] = product
    order_detail['service'] = service
    customer_order = ParcelCustomerOrder.objects.create(**order_detail, **params)
    customer_order.order_num = 'US' + order_num
    customer_order.save()
    for parcel in parcel_list:
        parcel.pop('id', None)
        parcel.pop('create_by', '')
        parcel.pop('update_by', '')
        parcel_item_list = parcel.pop('parcelItem', [])
        parcel['customer_order'] = customer_order
        parcle_order_parcel = ParcelOrderParcel.objects.create(**parcel, **params)

        for parcel_item in parcel_item_list:
            parcel_item.pop('id', None)
            parcel_item.pop('create_by', '')
            parcel_item.pop('update_by', '')
            parcel_item['parcel_num'] = parcle_order_parcel
            parcel_item = ParcelOrderItem.objects.create(**parcel_item, **params)
    i = 1
    for parcel_order_label in parcel_order_labels:
        parcel_order_label.pop('id', None)
        parcel_order_label.pop('create_by', '')
        parcel_order_label.pop('update_by', '')
        label_all_url = parcel_order_label.pop('label_all_url', '')
        label_url = save_label_file_by_request(customer_order, i, label_all_url)
        parcel_order_label['label_url'] = label_url
        parcel_order_label['order_num'] = customer_order
        parcel_order_label['product'] = product
        parcel_order_label = ParcelOrderLabel.objects.create(**parcel_order_label, **params)


# 推送订单到ems
@app.task(bind=True, base=QueueOnce)
def handler_push_to_ems_order(self):
    if settings.SYSTEM_ORDER_MARK not in ['ZJ'] and settings.SYSTEM_ORDER_MARK + settings.PARCEL_ORDER_MARK not in [
        'ZHPHD']:
        return

    handler_push_to_ems_order_by_type('is_sync_ems', 'ZJYZXQC')
    handler_push_to_ems_order_by_type('is_sync_ems_electric', 'ZJYZXQC-E')


def handler_push_to_ems_order_by_type(encoding, product_code):
    product_queryset = ProductBasicRestriction.objects.filter(encoding=encoding,
                                                              insert_trick=True,
                                                              del_flag=False).values_list('product',
                                                                                          flat=True).distinct()

    if not product_queryset.exists():
        return

    # 查询5天前的和30分钟后的订单
    current_date = datetime.now()
    three_day_ago = current_date - timedelta(days=5)
    one_minutes_ago = current_date - timedelta(minutes=30)

    pracel_customer_order_queryset = ParcelCustomerOrder.objects.filter(~Q(order_status__in=['VO', 'DR']),
                                                                        product__in=product_queryset, del_flag=False,
                                                                        label_billid__isnull=True,
                                                                        create_date__gte=three_day_ago,
                                                                        create_date__lt=one_minutes_ago)

    if not pracel_customer_order_queryset.exists():
        return

    product = Product.objects.filter(code=product_code, del_flag=False).first()
    service = Service.objects.filter(product=product, del_flag=False).first()
    supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
    supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
    supplier_account = supplier_account_list.first()

    for parcel_customer_order in pracel_customer_order_queryset:

        push_ems_order_queryset = PushEmsOrder.objects.filter(customer_order_num=parcel_customer_order, del_flag=False)
        if push_ems_order_queryset.exists():
            continue

        parcel_list = ParcelOrderParcel.objects.filter(customer_order=parcel_customer_order, del_flag=False)

        parcel_id_list = [parcel.id for parcel in parcel_list]

        parcel_item_list = ParcelOrderItem.objects.filter(parcel_num__in=parcel_id_list, del_flag=False)

        try:

            result = handler_push_ems_order(parcel_customer_order, product, service, supplier_account, parcel_list,
                                            parcel_item_list)

            push_ems_order = PushEmsOrder()
            push_ems_order.customer_order_num = parcel_customer_order

            if 'ret' in result.keys() and result['ret'] == 0:
                push_ems_order.status = 'Success'
                push_ems_order.label_desc = 'Success'
                am_num = result['data']
                push_ems_order.am_num = am_num
                push_ems_order.save()

                ParcelCustomerOrder.objects.filter(id=parcel_customer_order.id).update(label_billid=am_num)

            else:
                if 'msg' in result.keys() and result['msg']:
                    push_ems_order.status = 'Failure'
                    push_ems_order.label_desc = result['msg']
                    push_ems_order.save()
                else:
                    logger.info(f'未知异常 {parcel_customer_order.order_num}')

        except Exception as e:
            logger.error(traceback.format_exc())


# 同步订单到供应商(例如壹起飞同步到铭志)(同步fba订单)
@app.task(bind=True, base=QueueOnce)
def sync_customer_order_task(self, mode_key, customer_order_id=None, is_sync_parcel=False, is_update_order=False):
    """

    :param self:
    :param mode_key:
    :param customer_order_id:
    :param is_sync_parcel:
    :param is_update_order: 增加更新订单操作
    :return:
    """
    logger.info(f"--sync_customer_order_task start---->{mode_key}")

    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'YQF', 'FX']:
        return

    if customer_order_id is None:
        order_label_task_list = OrderSyncTask.objects.filter(Q(status='UnHandled') | Q(status='Failure'),
                                                             mode_key=mode_key,
                                                             task_type='PUSH_ORDER',
                                                             handle_times__lt=121, del_flag=0).reverse()[:100]
    else:
        order_label_task_list = OrderSyncTask.objects.filter(order_num_id=customer_order_id,
                                                             task_type='PUSH_ORDER', del_flag=False)

    if len(order_label_task_list) == 0:
        return

    for order_label_task in order_label_task_list:
        # 开始处理时, 把状态改为已提交
        # order_label_task.status = 'HandledBy3rdNo'
        # order_label_task.save()
        customer_order = CustomerOrder.objects.get(id=order_label_task.order_num.id)
        if customer_order.order_status == 'VO':
            add_fail_sync_task(order_label_task, f'订单: {customer_order.order_num} 已作废, 无法下发数据')
            continue
        logger.info("--sync_customer_order_task--order_num-->" + str(customer_order.order_num))

        key = 'handler_sync_customer_order_task_' + customer_order.order_num
        cache.set(key, "1", 60)

        # 如果是单包
        if order_label_task.parcel:
            parcel_list = [order_label_task.parcel]
        else:
            parcel_list = Parcel.objects.filter(customer_order=customer_order, del_flag=False)

        parcel_id_list = [parcel.id for parcel in parcel_list]
        parcel_item_list = ParcelItem.objects.filter(parcel_num__in=parcel_id_list, del_flag=False)

        logger.info("--productId-->" + str(customer_order.product.id))

        product = customer_order.product
        service = get_service_from_product(customer_order.product)

        # TODO 目前只有一个，后面需要改成选供应商推送，那就要用
        service_class_code = 'CustomerOrderSyncService'
        service_class = ServiceClass.objects.filter(code=service_class_code, del_flag=False).first()
        if not service_class:
            add_fail_sync_task(order_label_task, f'没有创建 {service_class_code} 服务类')
            continue

        supplier_butt = SupplierButt.objects.filter(class_name=service_class.code, del_flag=False).first()
        if not supplier_butt:
            add_fail_sync_task(order_label_task, f'供应商服务对接没有关联 {service_class_code} 服务类')
            continue

        supplier_account = SupplierButtAccount.objects.filter(vendor_id__code=supplier_butt.vendor_id.code,
                                                              type='SYNC',
                                                              del_flag=False).first()
        if not supplier_account:
            add_fail_sync_task(order_label_task, f'供应商对接账户信息没有关联供应商ID {supplier_butt.vendor_id.code}')
            continue

        class_name = supplier_butt.class_name
        if not class_name:
            add_fail_sync_task(order_label_task, '未实现对接类')
            continue

        logger.info("className =" + str(class_name))

        label_order_vo = LabelOrderVo()
        label_order_vo.orderLabelTask = order_label_task
        label_order_vo.supplierAccount = supplier_account
        label_order_vo.customerOrder = customer_order
        label_order_vo.parcelList = parcel_list
        label_order_vo.parcelItemList = parcel_item_list
        label_order_vo.product = product
        label_order_vo.service = service

        # 通过反射实例化对象
        obj = globals()[class_name]()
        # create_order(obj, label_order_vo)
        obj.create_order(label_order_vo, is_sync_parcel=is_sync_parcel, is_update_order=is_update_order)

        cache.delete(key)
        logger.info(f"--sync_customer_order_task end---->{mode_key}")


def sync_order_error_record(wait_pull_task, task_desc):
    logger.error(task_desc)
    wait_pull_task.task_desc = task_desc
    wait_pull_task.status = 'Failure'
    wait_pull_task.save()


# # (同步件重体)(同步fba订单件重体)
# # 上游供应商下单后定时拉取订单的件重体和状态: 例如一起飞创建订单, 同步到铭志, 铭志对订单进行后续称重入库操作, 一起飞定时从铭志读取称重件重体, 状态和轨迹
# @app.task(bind=True, base=QueueOnce)
# def sync_order_data_to_supplier(self, mode_key):
#     logger.info(f'sync_order_data_to_supplier start: {mode_key}')
#
#     if settings.SYSTEM_ORDER_MARK not in ['MZ', 'YQF']:
#         return
#
#     # 查询成功同步到供应商的订单, 定时获取订单件重体和状态
#     # 查询待处理订单
#     wait_pull_tasks = OrderSyncTask.objects.filter(~Q(task_type='PUSH_ORDER'),
#                                                    Q(status='UnHandled') | Q(status='Failure'),
#                                                    handle_times=0,
#                                                    mode_key=mode_key, del_flag=False)
#
#     if not wait_pull_tasks.exists():
#         return
#
#     wait_pull_tasks = wait_pull_tasks[:100]
#
#     wait_pull_task_ids = [i.id for i in wait_pull_tasks]
#     for wait_pull_task in wait_pull_tasks:
#
#         if wait_pull_task.order_num:
#             customer_order = wait_pull_task.order_num
#         else:
#             sync_order_error_record(wait_pull_task, '拉取订单件重体和轨迹: 不存在关联的订单')
#             continue
#
#         logger.info(f'开始拉取订单件重体:{customer_order.order_num}的下游件重体和状态, {mode_key}')
#
#         # 获取供营商配置信息
#         code, res = get_service_class_url('CustomerOrderSyncService')
#         if code == 1:
#             sync_order_error_record(wait_pull_task, res)
#             continue
#         else:
#             supplier_account = res
#
#         # 过滤掉已经作废的订单, 不允许再同步数据
#         if customer_order.order_status == 'VO':
#             sync_order_error_record(wait_pull_task, f'订单: {customer_order.order_num} 已作废, 无法同步数据')
#             continue
#
#         try:
#             # 拉取订单件重体和状态等数据
#             if wait_pull_task.task_type == 'PULL_ORDER_STATUS':
#                 pull_supplier_order_data(supplier_account, wait_pull_task, customer_order)
#
#             # 拉取订单轨迹
#             elif wait_pull_task.task_type == 'PULL_TRACK':
#                 pull_supplier_order_track(supplier_account, wait_pull_task, customer_order)
#
#             # 拉取订单的pod文件
#             elif wait_pull_task.task_type == 'PULL_POD_FILE':
#                 pull_supplier_order_pod_file(supplier_account, wait_pull_task, customer_order)
#
#             # 拉取订单的包裹轨迹
#             elif wait_pull_task.task_type == 'PULL_PARCEL_TRACK':
#                 pull_supplier_order_parcel_track(supplier_account, wait_pull_task, customer_order)
#
#             # 推送订单收入(PUSH_REVENUE)改为手动推送, 目前这个任务不执行
#             elif wait_pull_task.task_type == 'PUSH_REVENUE':
#                 push_supplier_order_revenue(supplier_account, wait_pull_task, customer_order)
#
#             # 拉取订单成本
#             elif wait_pull_task.task_type == 'PULL_COST':
#                 pull_supplier_order_cost(supplier_account, wait_pull_task, customer_order)
#
#         except Exception as e:
#             logger.info(traceback.format_exc())
#             logger.info(f'---->{e}')
#
#         logger.info(f'结束拉取订单件重体:{customer_order.order_num}的下游件重体和状态, {mode_key}')
#
#     OrderSyncTask.objects.filter(id__in=wait_pull_task_ids).update(handle_times=1)
#     logger.info(f'sync_order_data_to_supplier end: {mode_key}')


# (同步件重体)(同步fba订单件重体)(main)
# 上游供应商下单后定时拉取订单的件重体和状态: 例如一起飞创建订单, 同步到铭志, 铭志对订单进行后续称重入库操作, 一起飞定时从铭志读取称重件重体, 状态和轨迹
@app.task(bind=True, base=QueueOnce)
def sync_order_data_to_supplier(self, mode_key):
    logger.info(f'sync_order_data_to_supplier start: {mode_key}')

    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'YQF']:
        return

    # 获取供营商配置信息
    code, supplier_account = get_service_class_url('CustomerOrderSyncService')
    if code == 1:
        logger.error(f'sync_order_data_to_supplier定时任务获取供营商配置信息失败: code: 1, res: {supplier_account}')
        return

    # 批量处理的任务分组
    bulk_tasks = {
        'PULL_ORDER_STATUS': [],  # 拉取订单件重体和状态等数据, 每批不超过10单
        'PULL_TRACK': [],  # 拉取订单轨迹
        'PULL_PARCEL_TRACK': [],  # 拉取订单的包裹轨迹
        'PULL_COST': [],  # 拉取订单成本
        'PULL_POD_FILE': [],  # 拉取订单的pod文件
        # 'PUSH_REVENUE': [], # 推送订单收入(PUSH_REVENUE)改为手动推送, 目前这个任务不执行
    }

    wait_pull_task_ids = []
    for bulk_task in bulk_tasks.keys():
        # 查询成功同步到供应商的订单, 定时获取订单件重体和状态
        # 查询待处理订单
        # wait_pull_tasks = OrderSyncTask.objects.filter(~Q(task_type='PUSH_ORDER'),
        wait_pull_tasks = OrderSyncTask.objects.filter(Q(status='UnHandled') | Q(status='Failure'),
                                                       task_type=bulk_task,
                                                       handle_times=0,
                                                       mode_key=mode_key, del_flag=False)

        if not wait_pull_tasks.exists():
            continue

        for wait_pull_task in wait_pull_tasks[:120]:

            if wait_pull_task.order_num:
                customer_order = wait_pull_task.order_num
            else:
                sync_order_error_record(wait_pull_task, '拉取订单件重体和轨迹: 不存在关联的订单')
                continue

            logger.info(f'开始拉取订单件重体:{customer_order.order_num}的下游件重体和状态, {mode_key}')

            # 过滤掉已经作废的订单, 不允许再同步数据
            if customer_order.order_status == 'VO':
                sync_order_error_record(wait_pull_task, f'订单: {customer_order.order_num} 已作废, 无法同步数据')
                continue

            bulk_tasks[bulk_task].append({
                'wait_pull_task': wait_pull_task,
                'customer_order': wait_pull_task.order_num,
            })

            wait_pull_task_ids.append(wait_pull_task.id)

    # 批量处理任务
    for task_type, task_list in bulk_tasks.items():
        if not task_list:
            continue

        logger.info(f'批量处理 {task_type} 任务, 数量: {len(task_list)}')

        try:
            if task_type == 'PULL_ORDER_STATUS':
                # pull_supplier_order_data_batch(task_list, supplier_account)
                # 批量改为单个处理
                for task_info in task_list:
                    wait_pull_task = task_info['wait_pull_task']
                    customer_order = task_info['customer_order']
                    try:
                        pull_supplier_order_data(supplier_account, wait_pull_task, customer_order)
                    except Exception as e:
                        error_msg = f'拉取订单件重体和状态等数据: {customer_order.order_num} 失败, e: { e }, ' \
                                    f'错误信息: {traceback.format_exc()}'
                        logger.info(error_msg)
            elif task_type == 'PULL_TRACK':
                pull_supplier_order_track_batch(task_list, supplier_account)
            elif task_type == 'PULL_PARCEL_TRACK':
                pull_supplier_order_parcel_track_batch(task_list, supplier_account)
            elif task_type == 'PULL_COST':
                pull_supplier_order_cost_batch(task_list, supplier_account)
            elif task_type == 'PULL_POD_FILE':
                pull_supplier_order_pod_file_batch(task_list, supplier_account)
            # elif task_type == 'PUSH_REVENUE':
            #     push_supplier_order_revenue(task_list)
        except Exception as e:
            logger.error(f'批量处理 {task_type} 任务失败: {str(e)}')
            logger.error(traceback.format_exc())

    # 更新任务处理状态
    OrderSyncTask.objects.filter(id__in=wait_pull_task_ids).update(handle_times=1)
    logger.info(f'sync_order_data_to_supplier end: {mode_key}')


# 定时修改拉取次数, 已拉取的就按时拉, 没有拉取的立即拉(重置同步订单任务)
@app.task(bind=True, base=QueueOnce)
def handler_sync_order_data_handle_times(self):
    hour = datetime.now().hour
    logger.info(f'handler_sync_order_data_handle_times start')
    immediate_task = ['PULL_ORDER_STATUS', 'PULL_TRACK', 'PULL_POD_FILE', ]
    not_immediate_task = ['PULL_PARCEL_TRACK']
    # todo_x
    # timeout_close = ['PULL_PARCEL_TRACK', 'PULL_POD_FILE', 'PULL_COST']
    timeout_close = ['PULL_PARCEL_TRACK', 'PULL_COST']
    two_month_ago = datetime.now() - timedelta(days=60)
    one_month_ago = datetime.now() - timedelta(days=30)
    # 超过两个月的 timeout_close 同步任务主动关闭掉
    completed_order_tasks = OrderSyncTask.objects.filter(Q(status='UnHandled') | Q(status='Failure'),
                                                         task_type__in=timeout_close,
                                                         order_num__create_date__lt=two_month_ago,
                                                         order_num__order_status__in=['FC', 'SF'],
                                                         del_flag=False)
    if completed_order_tasks.exists():
        completed_order_tasks.update(status='Success', remark='历史订单已完成, 不再继续同步')

    # 未超过两个月的 immediate_task 同步任务每小时同步一次
    OrderSyncTask.objects.filter(Q(status='UnHandled') | Q(status='Failure'),
                                 task_type__in=immediate_task,
                                 order_num__create_date__gte=two_month_ago,
                                 handle_times=1, del_flag=False).update(handle_times=0)

    ClearanceOutSyncTask.objects.filter(Q(status='UnHandled'), del_flag=False).update(handle_times=0)

    if hour % 2 == 0:
        # 超过两个月的 immediate_task 同步任务每2小时同步一次
        OrderSyncTask.objects.filter(Q(status='UnHandled') | Q(status='Failure'),
                                     task_type__in=immediate_task,
                                     order_num__create_date__lt=two_month_ago,
                                     handle_times=1, del_flag=False).update(handle_times=0)
        # 未超过一个月的 not_immediate_task 同步任务每2小时同步一次
        OrderSyncTask.objects.filter(Q(status='UnHandled') | Q(status='Failure'),
                                     task_type__in=not_immediate_task,
                                     order_num__create_date__gte=one_month_ago,
                                     handle_times=1, del_flag=False).update(handle_times=0)
    if hour % 4 == 0:
        # 超过一个月的 not_immediate_task 同步任务每4小时同步一次
        OrderSyncTask.objects.filter(Q(status='UnHandled') | Q(status='Failure'),
                                     task_type__in=not_immediate_task,
                                     order_num__create_date__lt=one_month_ago,
                                     handle_times=1, del_flag=False).update(handle_times=0)

    if hour % 6 == 0:
        # 拉取成本的同步任务每6小时同步一次
        OrderSyncTask.objects.filter(Q(status='UnHandled') | Q(status='Failure'), task_type='PULL_COST',
                                     handle_times=1, del_flag=False).update(handle_times=0)

    logger.info(f'handler_sync_order_data_handle_times end')


@transaction.atomic
def pull_supplier_order_data(supplier_account, wait_pull_task, customer_order):
    # 同步订单和包裹件重体, 订单状态
    url = supplier_account.url + "/api/customerOrders/api_sync_order_data/"
    result = request_server({'order_num': customer_order.order_num}, url,
                            {'Content-Type': 'application/json'}, print_log=False)

    data = result.get('data')
    if not data:
        sync_order_error_record(wait_pull_task, f'拉取订单件重体和轨迹: 未获取到任何订单数据')
        return

    parcel_item_data = data.get('parcelItem')
    if not parcel_item_data:
        sync_order_error_record(wait_pull_task, f'拉取订单件重体和轨迹: 包裹信息不存在')
        return

    customer_order_dict = CreateCustomerOrder().__dict__
    customer_order_dict = convert_dict(customer_order_dict, data)
    order_status = customer_order_dict.get('order_status')

    customer_orders = CustomerOrder.objects.filter(id=customer_order.id, del_flag=False)
    # 如果订单已确认数据, 则不更新确认计费重和确认体积
    first_customer_order = customer_orders.first()
    if first_customer_order.confirm_charge_weight:
        customer_order_dict.pop('confirm_charge_weight', None)
    if first_customer_order.confirm_volume:
        customer_order_dict.pop('confirm_volume', None)
    if first_customer_order.check_in_time:
        customer_order_dict.pop('check_in_time', None)
    customer_order_dict.pop('remark', None)
    customer_orders.update(**customer_order_dict, update_date=datetime.now())

    parce_type = data.get('parceType') or True

    if parce_type:
        # 完整商品
        if parcel_item_data:
            for item in parcel_item_data:
                print('item??-->', item)
                exists_parcel = Parcel.objects.filter(customer_order=customer_order,
                                                      parcel_num=item['parcel_num'], del_flag=False).first()
                if not exists_parcel:
                    sync_order_error_record(
                        wait_pull_task, f'拉取订单件重体和轨迹: 本地不存在订单号: {customer_order.order_num}, '
                                        f'包裹号: {item["parcel_num"]}的包裹')
                    return
                order_parcel_dict = CreateParcel().__dict__

                order_parcel_dict = convert_dict(order_parcel_dict, item)
                print('order_parcel_dict6-->', order_parcel_dict)
                Parcel.objects.filter(id=exists_parcel.id).update(**order_parcel_dict,
                                                                  update_date=datetime.now())

    else:
        # 简易
        if parcel_item_data:
            for item in parcel_item_data:
                exists_parcel = ParcelSize.objects.filter(customer_order=customer_order,
                                                          del_flag=False).first()
                order_parcel_dict = CreateParcel().__dict__
                order_parcel_dict = convert_dict(order_parcel_dict, item)
                ParcelSize.objects.filter(id=exists_parcel.id).update(**order_parcel_dict,
                                                                      update_date=datetime.now())
    # if order_status in ['DEP', 'TF', 'SF', 'FC']:
    if order_status in ['ARR', 'TF', 'SF', 'FC']:
        wait_pull_task.task_desc = 'PULL_FINISH'
        wait_pull_task.status = 'Success'
        wait_pull_task.save()
    else:
        wait_pull_task.status = 'UnHandled'
        wait_pull_task.save()
    if order_status == 'AW':
        future_time = datetime.now() + timedelta(minutes=1)
        common_order_async_task(customer_order.order_num, 'TR', 'BL', customer_order.update_by,
                                customer_order_num=customer_order.ref_num, start_time=future_time)
    logger.info(f'拉取订单件重体和轨迹: {customer_order.order_num}已更新订单数据, 订单状态: {order_status}')
    return order_status


# 批量处理函数示例（其他批量函数结构类似）
def pull_supplier_order_data_batch(task_list, supplier_account):
    """
    批量拉取订单件重体和状态
    :param task_list: 任务列表
    [
        {
                'wait_pull_task': wait_pull_task,
                'customer_order': customer_order,
        }
    ]
        :param supplier_account: 供营商配置信息
    """
    if not task_list:
        return

    url = supplier_account.url + "/api/customerOrders/api_sync_order_data_batch/"
    # url = 'http://*************:8000/' + '/api/customerOrders/api_sync_order_data_batch/'

    # 提取所有订单号
    order_nums = [t['customer_order'].order_num for t in task_list]
    logger.info(f'拉取订单件重体和状态等数据: 订单数量 {len(order_nums)}')

    # 分批次处理（每40个订单一批）
    for i in range(0, len(order_nums), 10):
        batch_orders = order_nums[i:i + 10]
        batch_size = len(batch_orders)
        logger.info(f'拉取订单件重体和状态等数据: 处理批次 {i // 10 + 1}: {batch_size}个订单')

        # 请求服务器
        result = request_server({'order_nums': batch_orders}, url,
                                {'Content-Type': 'application/json'}, print_log=False)

        # 处理错误响应
        if not result or not result.get('data') or result.get('code') != 200:
            # 每个任务写入错误数据
            for order_num in batch_orders:
                task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
                if task_info:
                    sync_order_error_record(task_info['wait_pull_task'],
                                            f'拉取订单件重体和状态等数据: 未获取到任何订单数据')
            continue

        # 处理返回数据
        orders_data = result.get('data', {})
        for order_num, data in orders_data.items():
            task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
            if task_info and data:
                # 接口定义的正常data为字典结构, 如果返回的是错误信息, 则data为字符串
                if isinstance(data, str):
                    sync_order_error_record(task_info['wait_pull_task'], f'拉取订单件重体和状态等数据: {data}')
                else:
                    process_single_order_data(task_info, data)


@transaction.atomic
def process_single_order_data(task_info, data):
    """处理单个订单的件重体数据（从原pull_supplier_order_data提取）"""
    wait_pull_task = task_info['wait_pull_task']
    customer_order = task_info['customer_order']

    parcel_item_data = data.get('parcelItem')
    if not parcel_item_data:
        sync_order_error_record(wait_pull_task, f'拉取订单件重体和状态等数据: 包裹信息不存在')
        return

    customer_order_dict = CreateCustomerOrder().__dict__
    customer_order_dict = convert_dict(customer_order_dict, data)
    order_status = customer_order_dict.get('order_status')

    customer_orders = CustomerOrder.objects.filter(id=customer_order.id, del_flag=False)
    # 如果订单已确认数据, 则不更新确认计费重和确认体积
    first_customer_order = customer_orders.first()
    if first_customer_order.confirm_charge_weight:
        customer_order_dict.pop('confirm_charge_weight', None)
    if first_customer_order.confirm_volume:
        customer_order_dict.pop('confirm_volume', None)
    if first_customer_order.check_in_time:
        customer_order_dict.pop('check_in_time', None)
    customer_order_dict.pop('remark', None)
    customer_orders.update(**customer_order_dict, update_date=datetime.now())

    parce_type = data.get('parceType') or True

    if parce_type:
        # 完整商品
        if parcel_item_data:
            for item in parcel_item_data:
                print('item??-->', item)
                exists_parcel = Parcel.objects.filter(customer_order=customer_order,
                                                      parcel_num=item['parcel_num'], del_flag=False).first()
                if not exists_parcel:
                    sync_order_error_record(
                        wait_pull_task, f'拉取订单件重体和状态等数据: 本地不存在订单号: {customer_order.order_num}, '
                                        f'包裹号: {item["parcel_num"]}的包裹')
                    return
                order_parcel_dict = CreateParcel().__dict__

                order_parcel_dict = convert_dict(order_parcel_dict, item)
                print('order_parcel_dict6-->', order_parcel_dict)
                Parcel.objects.filter(id=exists_parcel.id).update(**order_parcel_dict,
                                                                  update_date=datetime.now())

    else:
        # 简易
        if parcel_item_data:
            for item in parcel_item_data:
                exists_parcel = ParcelSize.objects.filter(customer_order=customer_order,
                                                          del_flag=False).first()
                order_parcel_dict = CreateParcel().__dict__
                order_parcel_dict = convert_dict(order_parcel_dict, item)
                ParcelSize.objects.filter(id=exists_parcel.id).update(**order_parcel_dict,
                                                                      update_date=datetime.now())
    # if order_status in ['DEP', 'TF', 'SF', 'FC']:
    if order_status in ['ARR', 'TF', 'SF', 'FC']:
        wait_pull_task.task_desc = 'PULL_FINISH'
        wait_pull_task.status = 'Success'
        wait_pull_task.save()
    else:
        wait_pull_task.status = 'UnHandled'
        wait_pull_task.save()
    if order_status == 'AW':
        future_time = datetime.now() + timedelta(minutes=1)
        common_order_async_task(customer_order.order_num, 'TR', 'BL', customer_order.update_by,
                                customer_order_num=customer_order.ref_num, start_time=future_time)
    logger.info(f'拉取订单件重体和状态等数据: {customer_order.order_num}已更新订单数据, 订单状态: {order_status}')
    return order_status


@transaction.atomic
def pull_supplier_order_track(supplier_account, wait_pull_task, customer_order):
    # 1. 同步订单轨迹
    url = supplier_account.url + '/api/trackinfoFba/'
    # url = 'http://127.0.0.1:8000' + '/api/trackinfoFba/'
    # url = 'http://cshm.mz56.com' + '/api/trackinfoFba/'
    params = {'order_num': customer_order.order_num}
    result = request_get_server(params, url, {'Content-Type': 'application/json'})

    # 处理异常结果
    if result and result.get('code') != 200:
        error_msg = result.get('msg')
        if error_msg and '查询不到订单' in error_msg:
            wait_pull_task.status = 'Failure'
        wait_pull_task.task_desc = error_msg[:2000]
        wait_pull_task.save()
        return

    data = result.get('data', {})
    new_tracks = []
    for track in data:
        track_old_queryset = Track.objects.filter(order_id=customer_order.id,
                                                  order_num=customer_order.order_num,
                                                  track_code=track.get('track_code'),
                                                  track_name=track.get('track_name'),
                                                  actual_time=track.get('actual_time'),
                                                  del_flag=False)
        if track_old_queryset.exists():
            logger.info(f'拉取订单件重体和轨迹: {customer_order.order_num}已存在轨迹为: {track.get("track_name")}, '
                        f'还没有新的轨迹')
            new_tracks.append(track_old_queryset.last())
            continue

        track_new = Track()
        track_new.order_id = customer_order.id
        track_new.order_num = customer_order.order_num
        track_new.track_code = track.get('track_code')
        track_new.track_name = track.get('track_name')
        track_new.actual_time = track.get('actual_time')
        track_new.remark = track.get('description')
        track_new.location = track.get('location')
        track_new.save()

        new_tracks.append(track_new)

        if track.get('track_code') in ['SF', 'DEL']:
            wait_pull_task.remark = 'PULL_FINISH'
            wait_pull_task.status = 'Success'
            wait_pull_task.save()
            customer_order.save_fields(order_status='SF')
        else:
            wait_pull_task.status = 'UnHandled'
            wait_pull_task.save()

        logger.info(f'拉取订单件重体和轨迹: {customer_order.order_num}更新轨迹: {track.get("track_name")}')
    return new_tracks


def pull_supplier_order_track_batch(task_list, supplier_account):
    if not task_list:
        return

    # 1. 同步订单轨迹
    url = supplier_account.url + '/api/trackinfoFba/'
    # url = 'http://*************:8000/' + '/api/trackinfoFba/'
    # 提取所有订单号
    order_nums = [t['customer_order'].order_num for t in task_list]
    logger.info(f'拉取订单轨迹: 订单数量 {len(order_nums)}')

    # 分批次处理（每40个订单一批）
    for i in range(0, len(order_nums), 40):
        batch_orders = order_nums[i:i + 40]
        batch_size = len(batch_orders)
        logger.info(f'拉取订单轨迹: 处理批次 {i // 40 + 1}: {batch_size}个订单')

        # 请求服务器
        result = request_server({'order_nums': batch_orders}, url,
                                {'Content-Type': 'application/json'}, print_log=False)

        # 处理错误响应
        if not result or not result.get('data') or result.get('code') != 200:
            # 每个任务写入错误数据
            for order_num in batch_orders:
                task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
                if task_info:
                    sync_order_error_record(task_info['wait_pull_task'],
                                            f'拉取订单轨迹: 未获取到任何订单数据')
            continue

        # 处理返回数据
        orders_data = result.get('data', {})
        # logger.info(f'orders_data数据: {orders_data}')
        for order_num, data in orders_data.items():
            # logger.info(f'1data里面有假数据: {data}')
            task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
            if task_info and data:
                # 接口定义的正常data为列表结构, 如果返回的是错误信息, 则data为字符串
                if isinstance(data, str):
                    sync_order_error_record(task_info['wait_pull_task'], f'拉取订单轨迹: {data}')
                else:
                    process_single_order_track(task_info, data)


@transaction.atomic
def process_single_order_track(task_info, data):
    """处理单个订单的轨迹数据（从原pull_supplier_order_track提取）"""
    wait_pull_task = task_info['wait_pull_task']
    customer_order = task_info['customer_order']

    new_tracks = []
    for track in data:
        track_old_queryset = Track.objects.filter(order_id=customer_order.id,
                                                  order_num=customer_order.order_num,
                                                  track_code=track.get('track_code'),
                                                  track_name=track.get('track_name'),
                                                  actual_time=track.get('actual_time'),
                                                  del_flag=False)
        if track_old_queryset.exists():
            logger.info(f'拉取订单轨迹: {customer_order.order_num}已存在轨迹为: {track.get("track_name")}, '
                        f'还没有新的轨迹')
            new_tracks.append(track_old_queryset.last())
            continue

        if not track.get('actual_time'):
            logger.info(f'拉取订单轨迹存在异常为空: {customer_order.order_num}, track: {track}, data: {data}')
            continue

        track_new = Track()
        track_new.order_id = customer_order.id
        track_new.order_num = customer_order.order_num
        track_new.track_code = track.get('track_code')
        track_new.track_name = track.get('track_name')
        track_new.actual_time = track.get('actual_time')
        track_new.remark = track.get('description')
        track_new.location = track.get('location')
        track_new.save()

        new_tracks.append(track_new)

        if track.get('track_code') in ['SF', 'DEL']:
            wait_pull_task.remark = 'PULL_FINISH'
            wait_pull_task.status = 'Success'
            wait_pull_task.save()
            customer_order.save_fields(order_status='SF')
        else:
            wait_pull_task.status = 'UnHandled'
            wait_pull_task.save()

        logger.info(f'拉取订单轨迹: {customer_order.order_num}更新轨迹: {track.get("track_name")}')
    return new_tracks


@transaction.atomic
def pull_supplier_order_pod_file(supplier_account, wait_pull_task, customer_order):
    if customer_order.pod_file:
        wait_pull_task.status = 'Success'
        wait_pull_task.task_desc = f'已存在Pod文件: {customer_order.pod_file}'
        wait_pull_task.save()
        return

    url = supplier_account.url + '/api/customerOrders/api_sync_order_pod_file/'
    params = {'order_nums': [customer_order.order_num]}
    result = request_server(params, url, {'Content-Type': 'application/json'})

    # 处理异常结果
    if result and result.get('code') != 200:
        error_msg = result.get('msg')
        if error_msg and '查询不到订单' in error_msg:
            wait_pull_task.status = 'Failure'
        wait_pull_task.task_desc = error_msg[:2000]
        wait_pull_task.save()
        return

    data = result.get('data')
    for pod_data in data:
        order_num = pod_data.get('order_num')
        if not order_num:
            wait_pull_task.task_desc = '无订单信息'
            wait_pull_task.save()
            continue

        customer_orders = CustomerOrder.objects.filter(~Q(order_status='VO'), order_num=order_num, del_flag=False)
        if not customer_orders.exists():
            wait_pull_task.task_desc = f'查询不到订单{order_num}'
            wait_pull_task.save()
            continue

        # 保存卡派pod文件
        pod_file = pod_data.get('pod_file')
        if pod_file:
            pod_file_url = "files/" + (datetime.now().strftime("%Y/%m/%d/")) + pod_file
            # logger.info(f'拉取订单pod文件成功: {customer_order.order_num}接收pod文件pod_file_url: {pod_file_url}')
            decoded_file_name = MEDIA_URL + pod_file_url
            file_dir = os.path.dirname(decoded_file_name)
            if not os.path.exists(file_dir):
                os.makedirs(file_dir)

            pod_file_base64 = pod_data.get('pod_file_base64')
            with open(decoded_file_name, "wb") as code:
                code.write(base64.b64decode(pod_file_base64))
            customer_orders.update(pod_file=pod_file_url)
            logger.info(f'拉取订单pod文件成功: {customer_order.order_num}接收pod文件: {pod_file}')
            wait_pull_task.status = 'Success'
            wait_pull_task.task_desc = f'已存在Pod文件: {customer_order.pod_file}'
            wait_pull_task.save()
        else:
            wait_pull_task.status = 'UnHandled'
            wait_pull_task.save()


def pull_supplier_order_pod_file_batch(task_list, supplier_account):
    if not task_list:
        return

    url = supplier_account.url + '/api/customerOrders/api_sync_order_pod_file_batch/'
    # url = 'http://*************:8000/' + '/api/customerOrders/api_sync_order_pod_file_batch/'

    order_nums = []
    for i in task_list:
        customer_order = i['customer_order']
        wait_pull_task = i['wait_pull_task']
        if customer_order.pod_file:
            wait_pull_task.status = 'Success'
            wait_pull_task.task_desc = f'已存在Pod文件: {customer_order.pod_file}'
            wait_pull_task.save()
            continue

        order_nums.append(customer_order.order_num)
    # 提取所有订单号
    logger.info(f'# 拉取订单的pod文件: 订单数量 {len(order_nums)}')

    # 分批次处理（每40个订单一批）
    for i in range(0, len(order_nums), 40):
        batch_orders = order_nums[i:i + 40]
        batch_size = len(batch_orders)
        logger.info(f'# 拉取订单的pod文件: 处理批次 {i // 40 + 1}: {batch_size}个订单')

        # 请求服务器
        result = request_server({'order_nums': batch_orders}, url,
                                {'Content-Type': 'application/json'}, print_log=False)

        # 处理错误响应
        if not result or not result.get('data') or result.get('code') != 200:
            # 每个任务写入错误数据
            for order_num in batch_orders:
                task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
                if task_info:
                    sync_order_error_record(task_info['wait_pull_task'],
                                            f'# 拉取订单的pod文件: 未获取到任何订单数据')
            continue

        # 处理返回数据
        orders_data = result.get('data', {})
        for order_num, data in orders_data.items():
            task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
            if task_info and data:
                process_single_order_order_pod_file(task_info, data)
            elif task_info:
                sync_order_error_record(task_info['wait_pull_task'], f'拉取订单的pod文件: 未获取到任何订单数据')


@transaction.atomic
def process_single_order_order_pod_file(task_info, data):
    """处理单个订单的pod文件（从原pull_supplier_order_pod_file提取）"""
    wait_pull_task = task_info['wait_pull_task']
    customer_order = task_info['customer_order']

    order_num = customer_order.order_num
    if not order_num:
        wait_pull_task.task_desc = '无订单信息'
        wait_pull_task.save()
        return

    customer_orders = CustomerOrder.objects.filter(~Q(order_status='VO'), order_num=order_num, del_flag=False)
    if not customer_orders.exists():
        wait_pull_task.task_desc = f'查询不到订单{order_num}'
        wait_pull_task.save()
        return

    # 保存卡派pod文件
    pod_file = data.get('pod_file')
    if pod_file:
        pod_file_url = "files/" + (datetime.now().strftime("%Y/%m/%d/")) + pod_file
        # logger.info(f'拉取订单pod文件成功: {customer_order.order_num}接收pod文件pod_file_url: {pod_file_url}')
        decoded_file_name = MEDIA_URL + pod_file_url
        file_dir = os.path.dirname(decoded_file_name)
        if not os.path.exists(file_dir):
            os.makedirs(file_dir)

        pod_file_base64 = data.get('pod_file_base64')
        with open(decoded_file_name, "wb") as code:
            code.write(base64.b64decode(pod_file_base64))
        customer_orders.update(pod_file=pod_file_url)
        logger.info(f'拉取订单pod文件成功: {customer_order.order_num}接收pod文件: {pod_file}')
        wait_pull_task.status = 'Success'
        wait_pull_task.task_desc = f'已存在Pod文件: {customer_order.pod_file}'
        wait_pull_task.save()
    else:
        logger.info(f'没有卡派pod数据: {data}')
        wait_pull_task.status = 'UnHandled'
        wait_pull_task.save()


@transaction.atomic
def pull_supplier_order_parcel_track(supplier_account, wait_pull_task, customer_order):
    # 同步订单的包裹轨迹
    url = supplier_account.url + '/api/customerParcelTrack/query_parcel_track_by_order/'
    # url = 'http://127.0.0.1:8000' + '/api/customerParcelTrack/query_parcel_track_by_order/'
    # url = 'http://cshm.mz56.com' + '/api/customerParcelTrack/query_parcel_track_by_order/'

    params = {'order_nums': [customer_order.order_num]}
    result = request_server(params, url, {'Content-Type': 'application/json'}, print_log=False)

    # 处理异常结果
    if result and result.get('code') != 200:
        error_msg = result.get('msg')
        if error_msg and '查询不到订单' in error_msg:
            wait_pull_task.status = 'Failure'
        wait_pull_task.task_desc = error_msg[:2000]
        wait_pull_task.save()
        return

    order_data = result.get('data', [])
    if not order_data:
        wait_pull_task.task_desc = f'未获取到任何包裹轨迹数据'
        wait_pull_task.save()
        return

    judge_finish_flag = True
    for order_item in order_data:
        order_num = order_item.get('order_num')
        order_tracks = order_item.get('order_tracks', [])
        for order_track in order_tracks:
            parcel_num = order_track.get('parcel_num')
            parcel_tracks = order_track.get('parcel_tracks', [])
            for parcel_track in parcel_tracks:
                track_old_queryset = CustomerParcelTrack.objects.filter(
                    parcel_num=parcel_num,
                    track_code=parcel_track.get('track_code'),
                    track_name=parcel_track.get('track_name'),
                    actual_time=parcel_track.get('actual_time'),
                    del_flag=False)
                if track_old_queryset.exists():
                    logger.info(f'拉取订单件重体和轨迹: {customer_order.order_num}已存在包裹轨迹为: '
                                f'{parcel_track.get("track_name")}, 还没有新的轨迹')
                    continue
                track_params = {
                    'parcel_num': parcel_num,
                    'track_code': parcel_track.get('track_code'),
                    'track_name': parcel_track.get('track_name'),
                    'actual_time': parcel_track.get('actual_time'),
                    'remark': parcel_track.get('description'),
                }
                CustomerParcelTrack.objects.create(**track_params)
                logger.info(f'拉取订单件重体和轨迹: {customer_order.order_num}更新包裹轨迹: '
                            f'{parcel_track.get("track_name")}')
            # 如果当前包裹没有已完成, 则设置包裹轨迹为待拉取
            finished_parcel = CustomerParcelTrack.objects.filter(
                parcel_num=parcel_num,
                track_code='Delivered',
                del_flag=False
            )
            if not finished_parcel.exists():
                judge_finish_flag = False

    if judge_finish_flag:
        wait_pull_task.status = 'Success'
        wait_pull_task.task_desc = '已完成轨迹拉取'
        wait_pull_task.save()
    else:
        wait_pull_task.status = 'UnHandled'
        wait_pull_task.save()


def pull_supplier_order_parcel_track_batch(task_list, supplier_account):
    """
    批量拉取订单包裹轨迹
    :param task_list: 任务列表
    [
        {
                'wait_pull_task': wait_pull_task,
                'customer_order': customer_order,
        }
    ]
        :param supplier_account: 供营商配置信息
    """

    if not task_list:
        return

    # 同步订单的包裹轨迹
    url = supplier_account.url + '/api/customerParcelTrack/query_parcel_track_by_order_batch/'
    # url = 'http://*************:8000/' + '/api/customerParcelTrack/query_parcel_track_by_order_batch/'
    # url = 'http://cshm.mz56.com' + '/api/customerParcelTrack/query_parcel_track_by_order/'

    # 提取所有订单号
    order_nums = [t['customer_order'].order_num for t in task_list]
    logger.info(f'拉取订单的包裹轨迹: 订单数量 {len(order_nums)}')

    # 分批次处理（每40个订单一批）
    for i in range(0, len(order_nums), 40):
        batch_orders = order_nums[i:i + 40]
        batch_size = len(batch_orders)
        logger.info(f'拉取订单的包裹轨迹: 处理批次 {i // 40 + 1}: {batch_size}个订单')

        # 请求服务器
        result = request_server({'order_nums': batch_orders}, url,
                                {'Content-Type': 'application/json'}, print_log=False)

        # 处理错误响应
        if not result or not result.get('data') or result.get('code') != 200:
            # 每个任务写入错误数据
            for order_num in batch_orders:
                task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
                if task_info:
                    sync_order_error_record(task_info['wait_pull_task'],
                                            f'拉取订单的包裹轨迹: 未获取到任何订单数据')
            continue

        # 处理返回数据
        orders_data = result.get('data', {})
        for order_num, data in orders_data.items():
            task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
            if task_info and data:
                # 接口定义的正常data为列表结构, 如果返回的是错误信息, 则data为字符串
                if isinstance(data, str):
                    sync_order_error_record(task_info['wait_pull_task'], f'拉取订单轨迹: {data}')
                else:
                    process_single_order_parcel_track(task_info, data)


@transaction.atomic
def process_single_order_parcel_track(task_info, data):
    """处理单个订单的包裹轨迹数据（从原pull_supplier_order_parcel_track提取）"""

    wait_pull_task = task_info['wait_pull_task']
    customer_order = task_info['customer_order']

    judge_finish_flag = True
    for order_item in data:
        parcel_num = order_item.get('parcel_num')
        parcel_tracks = order_item.get('parcel_tracks', [])
        for parcel_track in parcel_tracks:
            track_old_queryset = CustomerParcelTrack.objects.filter(
                parcel_num=parcel_num,
                track_code=parcel_track.get('track_code'),
                track_name=parcel_track.get('track_name'),
                actual_time=parcel_track.get('actual_time'),
                del_flag=False)
            if track_old_queryset.exists():
                logger.info(f'拉取订单件重体和轨迹: {customer_order.order_num}已存在包裹轨迹为: '
                            f'{parcel_track.get("track_name")}, 还没有新的轨迹')
                continue
            track_params = {
                'parcel_num': parcel_num,
                'track_code': parcel_track.get('track_code'),
                'track_name': parcel_track.get('track_name'),
                'actual_time': parcel_track.get('actual_time'),
                'remark': parcel_track.get('description'),
            }
            CustomerParcelTrack.objects.create(**track_params)
            logger.info(f'拉取订单件重体和轨迹: {customer_order.order_num}更新包裹轨迹: '
                        f'{parcel_track.get("track_name")}')
        # 如果当前包裹没有已完成, 则设置包裹轨迹为待拉取
        finished_parcel = CustomerParcelTrack.objects.filter(
            parcel_num=parcel_num,
            track_code='Delivered',
            del_flag=False
        )
        if not finished_parcel.exists():
            judge_finish_flag = False

    if judge_finish_flag:
        wait_pull_task.status = 'Success'
        wait_pull_task.task_desc = '已完成轨迹拉取'
        wait_pull_task.save()
    else:
        wait_pull_task.status = 'UnHandled'
        wait_pull_task.save()


# 推送收入
@transaction.atomic
def push_supplier_order_revenue(supplier_account, wait_pull_task, customer_order):
    # 检查是否已生成账单汇总
    # exists_debits = Debit.objects.filter(customer_order=customer_order.order_num, del_flag=False)
    if not customer_order.is_revenue_lock:
        # todo_c:
        # if not any(exists_debits.values_list('invoice', flat=True)):
        #     wait_pull_task.task_desc = '还未生成账单汇总'
        wait_pull_task.task_desc = '还未收入确认'
        wait_pull_task.save()
        return

    charge_in_queryset = CustomerOrderChargeIn.objects.filter(customer_order_num=customer_order, del_flag=False)

    data = []
    for charge_in in charge_in_queryset:
        data.append({
            'charge_code': charge_in.charge.code,
            'charge_name': charge_in.charge.name,
            'charge_rate': float(charge_in.charge_rate),
            'charge_count': float(charge_in.charge_count),
            'charge_total': float(charge_in.charge_total),
            'currency_type': charge_in.currency_type,
            'current_exchange': float(charge_in.current_exchange),
            'account_charge': float(charge_in.account_charge),
        })

    url = supplier_account.url + '/api/customerOrders/api_set_order_revenue/'
    # url = 'http://127.0.0.1:8000' + '/api/customerOrders/api_set_order_revenue/'
    # url = 'http://cshm.mz56.com' + '/api/customerOrders/api_set_order_revenue/'

    params = {'order_num': customer_order.order_num, 'revenue_data': data}
    result = request_server(params, url, {'Content-Type': 'application/json'})

    if not result:
        return

    if result.get('code') == 200:
        wait_pull_task.status = 'Success'
        # wait_pull_task.task_desc = '收入推送完成'
        wait_pull_task.task_desc = result.get('msg') or result.get('detail')
        wait_pull_task.save()
    else:
        error_msg = result.get('msg') or result.get('detail')
        if error_msg and '查询不到订单' in error_msg:
            wait_pull_task.status = 'Failure'
        else:
            wait_pull_task.status = 'UnHandled'
        wait_pull_task.task_desc = error_msg[:2000]
        wait_pull_task.save()
        return


# 拉取订单成本
@transaction.atomic
def pull_supplier_order_cost(supplier_account, wait_pull_task, customer_order):
    url = supplier_account.url + '/api/customerOrders/api_get_order_cost/'
    # url = 'http://127.0.0.1:8000' + '/api/customerOrders/api_get_order_cost/'
    # url = 'http://cshm.mz56.com' + '/api/customerOrders/api_get_order_cost/'

    params = {'order_num': customer_order.order_num}
    result = request_server(params, url, {'Content-Type': 'application/json'})

    if not result:
        wait_pull_task.task_desc = '查无数据'
        wait_pull_task.save()
        return

    # 处理异常结果
    if result.get('code') != 200:
        error_msg = result.get('msg')
        if error_msg and '查询不到订单' in error_msg:
            wait_pull_task.status = 'Failure'
        wait_pull_task.task_desc = error_msg[:2000]
        wait_pull_task.save()
        return

    cost_data = result.get('data')
    if not cost_data:
        wait_pull_task.task_desc = '查无数据'
        wait_pull_task.save()
        return

    cost_arr = []
    for cost in cost_data:
        charge_code = cost.pop('charge_code')
        charge_name = cost.pop('charge_name')
        charge_rate = cost['charge_rate']
        charge_total = cost['charge_total']
        currency_type = cost['currency_type']
        charge_queryset = Charge.objects.filter(code=charge_code, del_flag=False)
        if not charge_queryset.exists():
            wait_pull_task.task_desc = f'费用项不存在{charge_name}'
            wait_pull_task.save()
            return

        charge = charge_queryset.first()

        charge_out_queryset = CustomerOrderChargeOut.objects.filter(customer_order_num=customer_order, charge=charge,
                                                                    charge_rate=charge_rate,
                                                                    charge_total=charge_total,
                                                                    currency_type=currency_type, data_source='L',
                                                                    del_flag=False)
        if charge_out_queryset.exists():
            continue

        cost['charge'] = charge
        cost_arr.append(cost)

    for cost in cost_arr:
        CustomerOrderChargeOut.objects.create(**cost,
                                              supplier=supplier_account.office_id,
                                              data_source='L',
                                              create_by=customer_order.create_by,
                                              create_date=datetime.now(),
                                              customer_order_num=customer_order)

    wait_pull_task.status = 'Success'
    wait_pull_task.task_desc = '成本拉取完成'
    wait_pull_task.save()


# 拉取订单成本
def pull_supplier_order_cost_batch(task_list, supplier_account):
    if not task_list:
        return

    url = supplier_account.url + '/api/customerOrders/api_get_order_cost_batch/'
    # url = 'http://*************:8000/' + '/api/customerOrders/api_get_order_cost_batch/'

    # 提取所有订单号
    order_nums = [t['customer_order'].order_num for t in task_list]
    logger.info(f'# 拉取订单成本: 订单数量 {len(order_nums)}')

    # 分批次处理（每40个订单一批）
    for i in range(0, len(order_nums), 40):
        batch_orders = order_nums[i:i + 40]
        batch_size = len(batch_orders)
        logger.info(f'# 拉取订单成本: 处理批次 {i // 40 + 1}: {batch_size}个订单')

        # 请求服务器
        result = request_server({'order_nums': batch_orders}, url,
                                {'Content-Type': 'application/json'}, print_log=False)

        # 处理错误响应
        if not result or not result.get('data') or result.get('code') != 200:
            # 每个任务写入错误数据
            for order_num in batch_orders:
                task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
                if task_info:
                    sync_order_error_record(task_info['wait_pull_task'],
                                            f'# 拉取订单成本: 未获取到任何订单数据')
            continue

        # 处理返回数据
        orders_data = result.get('data', {})
        for order_num, data in orders_data.items():
            task_info = next((t for t in task_list if t['customer_order'].order_num == order_num), None)
            if task_info and data:
                # 接口定义的正常data为列表结构, 如果返回的是错误信息, 则data为字符串
                if isinstance(data, str):
                    sync_order_error_record(task_info['wait_pull_task'], f'拉取订单成本: {data}')
                else:
                    process_single_order_cost(task_info, supplier_account, data)


@transaction.atomic
def process_single_order_cost(task_info, supplier_account, data):
    """处理单个订单的轨迹数据（从原pull_supplier_order_cost提取）"""
    wait_pull_task = task_info['wait_pull_task']
    customer_order = task_info['customer_order']

    cost_arr = []
    for cost in data:
        charge_code = cost.pop('charge_code')
        charge_name = cost.pop('charge_name')
        charge_rate = cost['charge_rate']
        charge_total = cost['charge_total']
        currency_type = cost['currency_type']
        charge_queryset = Charge.objects.filter(code=charge_code, del_flag=False)
        if not charge_queryset.exists():
            wait_pull_task.task_desc = f'费用项不存在{charge_name}'
            wait_pull_task.save()
            return

        charge = charge_queryset.first()

        charge_out_queryset = CustomerOrderChargeOut.objects.filter(customer_order_num=customer_order, charge=charge,
                                                                    charge_rate=charge_rate,
                                                                    charge_total=charge_total,
                                                                    currency_type=currency_type, data_source='L',
                                                                    del_flag=False)
        if charge_out_queryset.exists():
            continue

        cost['charge'] = charge
        cost_arr.append(cost)

    for cost in cost_arr:
        CustomerOrderChargeOut.objects.create(**cost,
                                              supplier=supplier_account.office_id,
                                              data_source='L',
                                              create_by=customer_order.create_by,
                                              create_date=datetime.now(),
                                              customer_order_num=customer_order)

    wait_pull_task.status = 'Success'
    wait_pull_task.task_desc = '成本拉取完成'
    wait_pull_task.save()


# 创建清关订单
@app.task(bind=True, max_retries=3, base=QueueOnce, once={'graceful': True})
def create_customs_clearance_order(self, mode_key):
    logger.info(f'create_customs_clearance_order start {mode_key}')
    create_clearance_order_task_queryset = CustomsClearanceOrderSupplierTask.objects.filter(status='UnHandled',
                                                                                            del_flag=False,
                                                                                            mode_key=mode_key,
                                                                                            handle_times__lt=121)[:200]

    if not create_clearance_order_task_queryset.exists():
        return

    for create_task in create_clearance_order_task_queryset:

        customs_clearance_order = create_task.order_num
        if customs_clearance_order.clear_status == 'VO':
            create_task.del_flag = True
            create_task.label_desc = '订单已作废'
            create_task.update_date = datetime.now()
            create_task.save()
            return

        order_num = customs_clearance_order.order_num

        logger.info(f'---create_customs_clearance_order---->{mode_key}---->{order_num}')

        if not customs_clearance_order.product:
            add_fail_clearance_task(create_task, '未选择产品，请选择产品', 121)
            return

        product = customs_clearance_order.product

        service_queryset = Service.objects.filter(product=product, del_flag=False)
        if not service_queryset.exists():
            add_fail_clearance_task(create_task, f'产品[{product.code}]未配置服务', 121)
            return
        service = service_queryset.first()

        supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
        supplier_account_list = SupplierButtAccount.objects.filter(vendor_id=supplier_butt.vendor_id.id, del_flag=False)
        if supplier_account_list.count() == 0:
            add_fail_clearance_task(create_task, f'产品{product.code}未配置完成供应商信息', 121)
            return
        supplier_account = supplier_account_list[0]

        # 提单收件人
        bill_lading_consignee_queryset = CustomsClearanceAddress.objects.filter(
            customer_order_num=customs_clearance_order, address_type='SHIPPER', del_flag=False)
        if not bill_lading_consignee_queryset.exists():
            add_fail_clearance_task(create_task, f'未设置提单收件人', 121)
            return

        # 发货人
        # if not product.address_num:
        #     add_fail_clearance_task(create_task, f'未设置发件人',121)
        #     return
        # address_queryset = Address.objects.filter(address_num=product.address_num, address_type='SP', del_flag=False)
        # if not address_queryset.exists():
        #     add_fail_clearance_task(create_task, f'未设置发件人',121)
        #     return

        # 包裹
        big_parcel_queryset = CustomsClearanceBigParcelOrder.objects.filter(
            customs_clearance_order=customs_clearance_order, del_flag=False)
        big_parcel_ids = [x.id for x in big_parcel_queryset]
        parcel_order_queryset = ParcelCustomerOrder.objects.filter(
            customs_clearance_big_parcel_order__in=big_parcel_ids, del_flag=False, push_status='NO_PUSH')[:20]

        class_name = upper_first(supplier_butt.class_name)

        clearance_order_vo = ClearanceOrderVo()
        clearance_order_vo.order_task = create_task
        clearance_order_vo.supplier_account = supplier_account
        clearance_order_vo.customer_order = customs_clearance_order
        clearance_order_vo.product = product
        clearance_order_vo.service = service
        clearance_order_vo.supplier_butt = supplier_butt
        clearance_order_vo.bill_lading_consignee = bill_lading_consignee_queryset.first()
        clearance_order_vo.parcel_order_list = parcel_order_queryset
        # clearance_order_vo.sender = address_queryset.first()

        logger.info("create_customs_clearance_order className =" + str(class_name))
        # 通过反射实例化对象
        obj = globals()[class_name]()
        create_clearance_order(obj, clearance_order_vo)

    logger.info(f'create_customs_clearance_order end {mode_key}')


def add_fail_clearance_task(create_task, msg_desc, handle_times=1):
    create_task.task_desc = msg_desc
    create_task.update_date = datetime.now()
    create_task.handle_times += handle_times
    create_task.save()


@app.task(bind=True, base=QueueOnce)
def sync_create_waybills_parcels(self):
    logger.info('---sync_create_waybills_parcels start-->')

    clearance_order = CustomsClearanceOrder.objects.filter(is_pushed_to_supplier=True, del_flag=False)
    supplier_task = CustomsClearanceOrderSupplierTask.objects.filter(customer_order_num__in=clearance_order,
                                                                     parcels_status="UnHandled")
    big_parcel_order = CustomsClearanceBigParcelOrder.objects.filter(
        customs_clearance_order__in=[i.customer_order_num_id for i in supplier_task])
    parcel_customer_order = ParcelCustomerOrder.objects.filter(
        customs_clearance_big_parcel_order__in=[i.id for i in big_parcel_order])

    for parcel_order in parcel_customer_order:

        name = parcel_order.warehouse_code.contact_name if parcel_order.warehouse_code else parcel_order.contact_name
        street = parcel_order.warehouse_code.address_one if parcel_order.warehouse_code else parcel_order.address_one
        zipcode = parcel_order.warehouse_code.postcode if parcel_order.warehouse_code else parcel_order.postcode
        city = parcel_order.warehouse_code.city_code if parcel_order.warehouse_code else parcel_order.city_code
        countryCode = parcel_order.warehouse_code.country_code if parcel_order.warehouse_code else parcel_order.country_code

        if not parcel_order.ioss_num:
            continue
        parcels_data = {}

        parcel_items = ParcelOrderItem.objects.filter(parcel_num__customer_order=parcel_order)
        if not parcel_items:
            continue

        clearance_order_id = parcel_order.customs_clearance_big_parcel_order.customs_clearance_order.id
        supplier_task = CustomsClearanceOrderSupplierTask.objects.filter(customer_order_num__id=clearance_order_id,
                                                                         parcels_status="UnHandled").first()

        parcels_data['waybillId'] = supplier_task.order_num
        parcels_data['transactionType'] = "B2C"
        parcels_data['finalMileTrackingNumber'] = parcel_order.tracking_num
        parcels_data['bigBagBarcode'] = parcel_order.customs_clearance_big_parcel_order.third_order_no
        parcels_data['sellerDetails'] = {}
        parcels_data['sellerDetails']['name'] = name
        parcels_data['sellerDetails']['street'] = street
        parcels_data['sellerDetails']['zipcode'] = zipcode
        parcels_data['sellerDetails']['city'] = city
        parcels_data['sellerDetails']['countryCode'] = countryCode
        parcels_data['sellerDetails']['iossNumber'] = parcel_order.ioss_num
        parcels_data['buyerDetails'] = {}
        parcels_data['buyerDetails']['name'] = parcel_order.buyer_name
        parcels_data['buyerDetails']['street'] = parcel_order.buyer_address_one
        parcels_data['buyerDetails']['zipcode'] = parcel_order.buyer_postcode
        parcels_data['buyerDetails']['city'] = parcel_order.buyer_city
        parcels_data['buyerDetails']['countryCode'] = parcel_order.buyer_country_code

        parcels_data['items'] = [
            {
                'hsCode': item['customs_code'],
                'description': item['declared_nameEN'],
                'quantity': item['item_qty'],
                'weight': item['weight_unit'],
                'invoiceCurrency': item['declared_currency'],
                'invoiceAmount': item['total_price'],
                'sku': item['item_code']
            }
            for item in parcel_items.values('item_code', 'item_name', 'item_qty', 'customs_code', 'declared_nameEN',
                                            'weight_unit', 'declared_currency', 'total_price')
        ]

        services = Service.objects.filter(
            product__id=parcel_order.customs_clearance_big_parcel_order.customs_clearance_order.product.id).first()
        parcels_data['shippingMethod'] = services.code

        create_waybills_response = omni_ship_client.create_parcels_waybills(supplier_task.order_num, parcels_data)
        if create_waybills_response.get('status') == "success":
            supplier_task.status = "Success"
            supplier_task.save()
        else:
            supplier_task.task_desc = create_waybills_response.get('content')
            supplier_task.save()


# @after_setup_task_logger.connect
# def setup_celery(**kwargs):
#     print('-----setup_celery----->>')
#     setup_periodic_tasks(sender=app)
#

# 自动注册，后面禁用自动注册，改使用数据库管理定时任务
# @app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """
    设置定时任务 - 已禁用自动注册
    现在使用 django-celery-beat 数据库调度器管理定时任务
    如需首次初始化任务，请手动调用此函数
    :param sender:
    :param kwargs:
    """
    customer_order_times = settings.CUSTOMER_ORDER_TIMES
    parcel_customer_order_times = settings.PARCEL_CUSTOMER_ORDER_TIMES

    # 执行createLabelTask一次
    for i in range(settings.MODE_KEY_DEFAULT_NUM):
        i = i + 1
        sender.add_periodic_task(customer_order_times, createLabelTask.s(str(i)), name='创建面单线程' + str(i))
        sender.add_periodic_task(customer_order_times, getLabelTask.s(str(i)), name='获取面单线程' + str(i))
        sender.add_periodic_task(customer_order_times, sync_label_task.s(str(i)), name='同步面单线程' + str(i))

    for i in range(settings.PARCEL_MODE_KEY_DEFAULT_NUM):
        i = i + 1
        sender.add_periodic_task(parcel_customer_order_times, create_parcel_label_task.s(str(i)),
                                 name='创建小包面单线程' + str(i))
        sender.add_periodic_task(parcel_customer_order_times, get_parcel_label_task.s(str(i)),
                                 name='获取小包面单线程' + str(i))

    sender.add_periodic_task(customer_order_times, confirm_parcel_label_task.s(str(1)),
                             name='创建小包确认定时任务' + str(1))

    sender.add_periodic_task(customer_order_times, update_parcel_task.s(str(1)), name='更新小包重量线程' + str(1))
    sender.add_periodic_task(customer_order_times, create_big_parcel_label_task.s(str(1)),
                             name='创建大包面单线程' + str(1))
    sender.add_periodic_task(customer_order_times, get_big_parcel_label_task.s(str(1)),
                             name='获取大包面单线程' + str(1))

    sender.add_periodic_task(customer_order_times, create_big_parcel_label_task_for_asendia.s(str(1)),
                             name='创建asendia大包面单线程' + str(1))
    sender.add_periodic_task(customer_order_times, get_big_parcel_label_task_for_asendia.s(str(1)),
                             name='获取asendia大包面单线程' + str(1))

    for i in range(2):
        i = i + 1
        sender.add_periodic_task(parcel_customer_order_times, handler_get_51tracking_info.s(str(i)),
                                 name='获取51轨迹线程' + str(i))

    sender.add_periodic_task(5.0, handler_push_parcel_17_tracking_num.s(str(1)), name='推送17转单号线程')
    sender.add_periodic_task(5.0, handler_get_17track_info, name='获取17轨迹线程')

    if settings.SYSTEM_ORDER_MARK in ['MZ', 'YQF']:
        for i in range(settings.MODE_KEY_SYNC_ORDER_NUM):
            i = i + 1
            sender.add_periodic_task(settings.CUSTOMER_ORDER_SYNC_TIMES, sync_customer_order_task.s(str(i)),
                                     name='创建同步订单线程' + str(i))
            sender.add_periodic_task(settings.CUSTOMER_ORDER_SYNC_TIMES, sync_order_data_to_supplier.s(str(i)),
                                     name='创建同步供应商订单件重体和轨迹线程' + str(i))

            sender.add_periodic_task(settings.CUSTOMER_ORDER_SYNC_TIMES, sync_clearance_out_data_to_supplier.s(str(i)),
                                     name='创建同步出口报关单线程' + str(i))

    # 麦点
    if settings.SYSTEM_ORDER_MARK in ['MM']:
        for i in range(settings.MODE_KEY_DEFAULT_NUM):
            i = i + 1
            sender.add_periodic_task(customer_order_times, create_insurance_order_task.s(str(i)),
                                     name='创建保险抓单线程' + str(i))

    if settings.SYSTEM_ORDER_MARK in ['ZH']:
        sender.add_periodic_task(5.0, create_customs_clearance_order.s('1'), name='创建清关订单1')

    # 设置scanfrom任务
    for i in range(5):
        i = i + 1
        sender.add_periodic_task(parcel_customer_order_times, create_beat_scan_form_task.s(str(i)),
                                 name='创建ScanForm_beat任务' + str(i))

    sender.add_periodic_task(
        crontab(hour='6,12,0', minute=0),
        generate_cancel_parcel_order_label_tasks,
        name='生成二次取消小包单面单任务'
    )
    # 小量高频执行二次取消任务；确保在间隔期间能跑完调度出来的任务
    sender.add_periodic_task(
        300,
        cancel_label_with_cancelled_order,
        name='执行二次取消小包单面单任务'
    )
    sender.add_periodic_task(
        crontab(hour='12,3', minute=20),
        auto_cancel_parcel_customer_order,
        name='自动作废小包单'
    )
    sender.add_periodic_task(
        crontab(minute=20),
        push_parcel_customer_order_export_task,
        name='发布小包单导出任务'
    )


@app.task(
    bind=True,
    max_retries=1,
    base=QueueOnce,
    once={'graceful': True, 'timeout': 860},
    time_limit=800
)
def push_parcel_customer_order_export_task(self):
    '''
    定时发布小包单导出任务
    1. 查询小包单导出任务是否已经存在正在执行的任务
    2. 如果正在执行的任务超过限制，则不在发布新的任务
    3. 如果没有正在执行的任务或者正在执行的任务数量小于限制，则发布1个新任务
    '''
    logger.info('开始发布小包单导出任务')
    start_time = datetime.now()
    run_limit = 4
    try:
        with transaction.atomic():
            processing_tasks = ParcelCustomerOrderBatchExportTask.objects.filter(status='processing', del_flag=0)
            if processing_tasks.count() >= run_limit:
                logger.info(
                    f'当前有{processing_tasks.count()}个小包单导出任务正在执行，超过限制({run_limit})，不再发布新的任务')
            else:
                pending_task = ParcelCustomerOrderBatchExportTask.objects.filter(status='pending', del_flag=0).first()
                if pending_task:
                    pending_task.status = 'processing'
                    pending_task.save(update_fields=['status'])
                    handle_parcel_customer_order_export_task.delay(pending_task.id)
                    logger.info(f'发布导出任务，任务ID: {pending_task.id}')
    except Exception as e:
        logger.error(f'发布小包单导出任务失败: {traceback.format_exc()}')
    end_time = datetime.now()
    time_diff = end_time - start_time
    logger.info(f'发布小包单导出任务执行完毕, 花费时间: {time_diff}')


@app.task(
    bind=True, max_retries=1, base=QueueOnce,
    once={'graceful': True, 'keys': ['export_task_id'], 'timeout': 43260},
    time_limit=43200)
def handle_parcel_customer_order_export_task(self, export_task_id):
    logger.info(f'开始处理小包单导出任务，任务ID: {export_task_id}')
    task = ParcelCustomerOrderBatchExportTask.objects.filter(id=export_task_id).first()
    task.run_timestamp = int(datetime.now().timestamp())
    task_start_time = datetime.now()
    if task.status == 'success':
        logger.info(f'小包单导出任务已完成，任务ID: {export_task_id}，无需重复处理')
    else:
        try:
            # 查询所有需要导出的小包单
            query = task.query
            order_time = query.get('order_time', None)
            product = query.get('product', None)
            customer = query.get('customer', None)
            is_weighing = query.get('is_weighing', None)
            account_time = query.get('account_time', None)
            has_scanform = query.get('has_scanform', None)
            inbound_time = query.get('inbound_time', None)
            is_cost_lock = query.get('is_cost_lock', None)
            order_status = query.get('order_status', None)
            third_orderNo = query.get('third_orderNo', None)
            has_trackingno = query.get('has_trackingno', None)
            intercept_mark = query.get('intercept_mark', None)
            is_confirm_ship = query.get('is_confirm_ship', None)
            is_revenue_lock = query.get('is_revenue_lock', None)
            buyer_country_code = query.get('buyer_country_code', None)
            big_parcel__parcel_num = query.get('big_parcel__parcel_num', None)

            if not order_time:
                start_date = datetime.now() - timedelta(days=183)
                end_date = datetime.now()
            else:
                # 分割并解析时间
                start_str, end_str = order_time.split(',')
                start_date = parser.parse(start_str)
                end_date = parser.parse(end_str)
            end_date += timedelta(days=1)
            current_date = start_date
            list_parcel_customer_order = []
            max_run_count = 13
            run_count = 0
            # 按30天跨度遍历
            while current_date <= end_date:
                run_count += 1
                if run_count > max_run_count:
                    break
                # 计算当前30天周期的结束日期（不超过总结束日期）
                period_end = min(current_date + timedelta(days=30), end_date)
                min_date_str = current_date.strftime('%Y-%m-%d')
                max_date_str = period_end.strftime('%Y-%m-%d')
                logger.info(f'task({export_task_id})查询小包单数据: 当前周期 {min_date_str} 到 {max_date_str}')

                orders = ParcelCustomerOrder.objects.filter(
                    del_flag=False,
                    create_date__gte=min_date_str,
                    create_date__lt=max_date_str
                )
                if big_parcel__parcel_num:
                    orders = orders.filter(big_parcel__parcel_num=big_parcel__parcel_num)
                if buyer_country_code:
                    orders = orders.filter(buyer_country_code=buyer_country_code)
                if is_revenue_lock:
                    if is_revenue_lock == 'true':
                        orders = orders.filter(is_revenue_lock=True)
                    else:
                        orders = orders.filter(is_revenue_lock=False)
                if is_confirm_ship:
                    if is_confirm_ship == 'true':
                        orders = orders.filter(is_confirm_ship=True)
                    else:
                        orders = orders.filter(is_confirm_ship=False)
                if intercept_mark:
                    if intercept_mark == 'true':
                        orders = orders.filter(is_intercept_record=True)
                    else:
                        orders = orders.filter(is_intercept_record=False)
                if has_trackingno:
                    if has_trackingno == 'Y':
                        orders = orders.filter(tracking_num__isnull=False)
                    else:
                        orders = orders.filter(tracking_num__isnull=True)

                if third_orderNo:
                    orders = orders.filter(third_orderNo=third_orderNo)
                if order_status:
                    orders = orders.filter(order_status=order_status)
                if is_cost_lock:
                    if is_cost_lock == 'false':
                        orders = orders.filter(is_cost_lock=False)
                    else:
                        orders = orders.filter(is_cost_lock=True)
                if account_time:
                    account_time = account_time.split(',')
                    account_start = account_time[0]
                    account_end = account_time[1]
                    account_end = parser.parse(account_end)
                    account_end = account_end + timedelta(days=1)
                    account_end = account_end.strftime('%Y-%m-%d')
                    logger.info(f'account_start: {account_start}, account_end: {account_end}')
                    orders = orders.filter(account_time__gte=account_start, account_time__lt=account_end)
                if inbound_time:
                    inbound_time = inbound_time.split(',')
                    inbound_start = inbound_time[0]
                    inbound_end = inbound_time[1]
                    orders = orders.filter(inbound_time__gte=inbound_start, inbound_time__lte=inbound_end)
                if has_scanform:
                    if has_scanform == 'Y':
                        orders = orders.filter(parcelOrderExtends__scanform_task__isnull=False)
                    else:
                        orders = orders.filter(parcelOrderExtends__scanform_task__isnull=True)

                if is_weighing:
                    if is_weighing == 'false':
                        orders = orders.filter(is_weighing=False)
                    else:
                        orders = orders.filter(is_weighing=True)
                if product:
                    product = product.split(',')
                    orders = orders.filter(product__id__in=product)
                if customer:
                    orders = orders.filter(customer__id=customer)
                if orders.count() > 0:
                    list_parcel_customer_order.extend(orders)

                # 移动到下一个周期的开始
                current_date = period_end + timedelta(days=1)

            if not list_parcel_customer_order:
                logger.info(f'导出task({export_task_id})没有查询到匹配条件的小包单数据')
                task.status = 'failure'
                task.result = '没有查询到匹配条件的小包单数据'
                task.save()
                return
            wb = Workbook()
            # 创建一个sheet
            sheet_name = '订单数据'
            w = wb.create_sheet(sheet_name, 0)

            title_dict = [
                "客户", "运输单", "出货单", "订单号", "订单状态", "客户订单号",
                "入库时间（称重时间）", "出库时间", "产品名称", "产品编码", "邮政单号", "尾程派送单号",
                "大包号", "核重（仓库称重）", "中文品名", "英文品名", "海关编码", "商品重量",
                "商品数量", "商品申报单价", "币种", "下单时间", "大包重量", "大包长", "大包宽",
                "大包高", "包裹长", "包裹宽", "包裹高", "VAT No.", "收货人",
                "收件地址1", "收件地址2", "收件城市", "收件州", "收件电话", "收件邮编", "收件国家", "门牌号", "操作员",
                "计费重转换率",
                '计费重量', '转单状态', '转单信息', '是否拦截', '拦截原因'
            ]
            logger.info(f'导出task({export_task_id})开始填充标题')

            # 填充标题
            for i, title in enumerate(title_dict):
                w.cell(row=1, column=i + 1).value = title
                # 设置单元格格式
                cell = w.cell(row=1, column=i + 1)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.font = Font(size=15, bold=True)
                w.column_dimensions[w.cell(row=1, column=i + 1).column_letter].width = 30

            excel_row = 2

            def setStyle(excel_row, w, excel_col):
                cell = w.cell(row=excel_row, column=excel_col)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.font = Font(size=12)

            def setData(ci, value, excel_row, w):
                w.cell(row=excel_row, column=ci).value = value
                setStyle(excel_row, w, ci)

            logger.info(f'导出task({export_task_id})查询到小包单数据数量: {len(list_parcel_customer_order)}')
            for customerOrder in list_parcel_customer_order:
                pick_record_queryset = PickRecord.objects.filter(
                    del_flag=False,
                    order_num=customerOrder.order_num
                )
                label_task = ParcelOrderLabelTask.objects.filter(order_num=customerOrder, del_flag=False).first()
                label_status = dict(ParcelOrderLabelTask.STATUS).get(label_task.status) if label_task else '未查到相关面单任务'
                label_desc = label_task.label_desc if label_task else '未查到相关面单任务'
                operator = None
                if pick_record_queryset.count() > 0:
                    operator = pick_record_queryset.first().user

                ci = 1
                # 客户
                if get(customerOrder, 'customer.name'):
                    setData(ci, customerOrder.customer.name, excel_row, w)
                ci += 1
                # 运输单
                if get(customerOrder, 'big_parcel.parcel_outbound_order.customer_order.order_num'):
                    setData(ci, customerOrder.big_parcel.parcel_outbound_order.customer_order.order_num, excel_row, w)
                ci += 1

                # 出货单
                if get(customerOrder, 'big_parcel.parcel_outbound_order.outbound_num'):
                    setData(ci, customerOrder.big_parcel.parcel_outbound_order.outbound_num, excel_row, w)
                ci += 1

                # 订单号
                setData(ci, customerOrder.order_num, excel_row, w)
                ci += 1

                # 订单状态
                order_status_map = dict(ParcelCustomerOrder.STATUS)
                setData(ci, order_status_map.get(customerOrder.order_status), excel_row, w)
                ci += 1

                # 客户订单号
                setData(ci, customerOrder.customer_order_num, excel_row, w)
                ci += 1

                # 入库时间（称重时间）
                setData(ci, customerOrder.inbound_time, excel_row, w)
                ci += 1
                setData(ci, customerOrder.outbound_time, excel_row, w)
                ci += 1

                # 产品名称
                if customerOrder.product:
                    setData(ci, customerOrder.product.name, excel_row, w)
                ci += 1
                # 产品编码
                if customerOrder.product:
                    setData(ci, customerOrder.product.code, excel_row, w)
                ci += 1

                # 邮政单号
                setData(ci, customerOrder.label_billid, excel_row, w)
                ci += 1

                # 尾程派送单号
                setData(ci, get(customerOrder, 'tracking_num'), excel_row, w)
                ci += 1

                # 大包号
                try:
                    if customerOrder.big_parcel:
                        setData(ci, customerOrder.big_parcel.parcel_num, excel_row, w)
                except Exception as e:
                    logger.info(traceback.format_exc())

                ci += 1

                #  核重（仓库称重）
                setData(ci, customerOrder.weighing_weight, excel_row, w)
                ci += 1

                parcel_queryset = ParcelOrderParcel.objects.filter(customer_order=customerOrder, del_flag=False)
                item_queryset = ParcelOrderItem.objects.filter(parcel_num__in=parcel_queryset, del_flag=False)
                if item_queryset:
                    item = item_queryset.first()
                    # 中文品名
                    setData(ci, item.declared_nameCN, excel_row, w)
                    ci += 1

                    # 英文品名
                    setData(ci, item.declared_nameEN, excel_row, w)
                    ci += 1

                    # 海关编码
                    setData(ci, item.customs_code, excel_row, w)
                    ci += 1

                    # 商品重量
                    setData(ci, item.item_weight, excel_row, w)
                    ci += 1

                    # 商品数量
                    setData(ci, item.item_qty, excel_row, w)
                    ci += 1

                    # 商品申报单价
                    setData(ci, item.declared_price, excel_row, w)
                    ci += 1

                    # 币种
                    setData(ci, item.declared_currency, excel_row, w)
                    ci += 1
                else:
                    ci += 7
                # 下单时间
                setData(ci, customerOrder.order_time, excel_row, w)
                ci += 1
                # 大包重量
                setData(ci, get(customerOrder, 'big_parcel.parcel_weight'), excel_row, w)
                ci += 1
                # 大包长
                setData(ci, get(customerOrder, 'big_parcel.parcel_length'), excel_row, w)
                ci += 1
                # 大包宽
                setData(ci, get(customerOrder, 'big_parcel.parcel_width'), excel_row, w)
                ci += 1
                # 大包高
                setData(ci, get(customerOrder, 'big_parcel.parcel_height'), excel_row, w)
                ci += 1
                if parcel_queryset:
                    parcelq = parcel_queryset.first()
                    # 包裹长
                    setData(ci, parcelq.parcel_length, excel_row, w)
                    ci += 1
                    # 包裹宽
                    setData(ci, parcelq.parcel_width, excel_row, w)
                    ci += 1
                    # 包裹高
                    setData(ci, parcelq.parcel_height, excel_row, w)
                    ci += 1
                else:
                    ci += 3

                # VAT No.
                if customerOrder.warehouse_code:
                    setData(ci, customerOrder.vat_num, excel_row, w)
                ci += 1
                # 收货人
                setData(ci, customerOrder.buyer_name, excel_row, w)
                ci += 1
                # 收件地址1
                setData(ci, customerOrder.buyer_address_one, excel_row, w)
                ci += 1
                # 收件地址2
                setData(ci, customerOrder.buyer_address_two, excel_row, w)
                ci += 1
                # 收件城市
                setData(ci, customerOrder.buyer_city_code, excel_row, w)
                ci += 1
                # 收件州
                setData(ci, customerOrder.buyer_state, excel_row, w)
                ci += 1
                # 收件电话
                setData(ci, customerOrder.buyer_phone, excel_row, w)
                ci += 1
                # 收件邮编
                setData(ci, customerOrder.buyer_postcode, excel_row, w)
                ci += 1
                # 收件国家
                setData(ci, customerOrder.buyer_country_code, excel_row, w)
                ci += 1
                # 门牌号
                setData(ci, customerOrder.buyer_house_num or '', excel_row, w)
                ci += 1
                # 操作员
                setData(ci, get(operator, 'username'), excel_row, w)
                ci += 1
                # 计费重转换率
                setData(ci, customerOrder.charge_trans, excel_row, w)
                ci += 1
                # 计费重量
                setData(ci, customerOrder.charge_weight, excel_row, w)
                ci += 1
                # 转单状态
                setData(ci, label_status, excel_row, w)
                ci += 1
                # 转单信息
                setData(ci, label_desc, excel_row, w)
                ci += 1
                # 是否拦截
                setData(ci, '是' if customerOrder.intercept_mark else '否', excel_row, w)
                ci += 1
                # 拦截原因
                setData(ci, customerOrder.remark, excel_row, w)
                ci += 1
                excel_row += 1
            logger.info(f'task({export_task_id})小包单数据导出完成，开始保存本地')

            file_name = 'parcel_customer_orders' + f"{export_task_id}" + datetime.now().strftime(
                "%Y%m%d%H%M%S") + '.xlsx'
            file_path = settings.STATIC_MEDIA_DIR + 'customerOrder/' + file_name
            wb.save(file_path)

            task.file_url = settings.DOMAIN_URL + '/media/customerOrder/' + file_name
            task.status = 'success'
            task.result = '任务执行完毕'
            task.save()
            # 更新任务状态
            # invoice.debit_detail_url = settings.DOMAIN_URL + '/media/bill/' + file_name
            # invoice.save()
            logger.info(f'小包单导出任务处理成功, 任务id = {export_task_id}，导出文件路径: {file_path}')
        except Exception as e:
            logger.error(f'小包单导出任务处理失败: {traceback.format_exc()}')
            task.status = 'failure'
            task.result = f'{str(e)}'
            task.save()

    end_time = datetime.now()
    task_time_diff = end_time - task_start_time
    logger.info(f'小包单导出任务处理完毕，任务ID: {export_task_id}, 耗时: {task_time_diff}')


@app.task(
    bind=True,
    max_retries=1,
    base=QueueOnce,
    once={'graceful': True, 'timeout': 3660},
    time_limit=3600
)
def auto_cancel_parcel_customer_order(self):
    '''
    自动作废小包单
    条件: 等待作业+成本确认+未成功获取到面单+开启全局变量(是否自动作废小包单，value=1)
    '''
    logger.info('自动作废小包单开始执行')
    # 查询有没有开启自动作废
    obj = Dict.objects.filter(label='是否自动作废小包单', value='1', type='auto_cancel_order', del_flag=False).last()
    if not obj:
        logger.info('没有开启自动作废')
        return
    start_time = datetime.now()

    # 查询符合条件的小包单
    after_date = timezone.now() - timezone.timedelta(days=30)
    # 成本确认在48小时后
    before_date = timezone.now() - timezone.timedelta(hours=48)
    cancelled_orders = ParcelCustomerOrder.objects.filter(
        create_date__gte=after_date,
        create_date__lte=before_date,
        order_status='WO',
        is_cost_lock=True,
        parcelOrderLabelTasks__status__in=['UnHandled', 'HandledBy3rdNo', 'Failure', 'VO']
    )
    order_count = len(cancelled_orders)
    run_count = 0
    success_count = 0
    for customer_order in cancelled_orders:
        success_message = []
        fail_message = []

        try:
            label = ParcelOrderLabel.objects.filter(order_num=customer_order, del_flag=False).first()
            if label:
                logger.info(f'{customer_order}存在面单，不自动作废')
                continue
            run_count += 1
            handler_cancel_label(customer_order, customer_order.order_num, success_message, fail_message, None)
            customer_order.remark = '自动作废-未获取到面单'
            customer_order.save(update_fields=['remark'])
            logger.info(
                f'{customer_order}自动作废执行完毕,success_message={success_message},fail_message={fail_message}')
            success_count += 1
        except Exception as err:
            logger.error(f'order = {customer_order}, 自动作废失败: {traceback.format_exc()}')

    end_time = datetime.now()
    time_diff = end_time - start_time
    logger.info(
        f'auto_cancel_parcel_customer_order执行完毕, 花费时间: {time_diff}, 订单总数:{order_count}, 执行取消总数:{run_count},成功取消总数:{success_count}')


@app.task(bind=True, base=QueueOnce)
def generate_cancel_parcel_order_label_tasks(self):
    """
    生成二次取消小包单面单任务
    """
    logger.info('开始生成二次取消小包单任务')
    start_time = datetime.now()
    create_count = 0
    exit_count = 0
    order_count = 0
    try:
        # 获取30天内的作废小包单
        thirty_days_ago = timezone.now() - timezone.timedelta(days=30)
        one_hour_ago = timezone.now() - timezone.timedelta(hours=1)
        cancelled_orders = ParcelCustomerOrder.objects.filter(
            create_date__gte=thirty_days_ago,
            create_date__lte=one_hour_ago,
            order_status__in=['VO', 'BC', 'CS', 'CF']
        )
        order_count = len(cancelled_orders)
        for order in cancelled_orders:
            try:
                cancel_task = CancelParcelCustomerOrderLabelTask.objects.filter(order=order).first()
                # 检查是否已存在任务
                if not cancel_task:
                    CancelParcelCustomerOrderLabelTask.objects.create(
                        order=order, 
                        order_num=order.order_num,
                        order_create_date=order.create_date,
                        customer_order_num=order.customer_order_num,
                        tracking_num=order.tracking_num,
                    )
                    logger.info(f'order = {order}, 生成取消小包单任务成功')
                    create_count += 1
                else:
                    logger.info(f'order = {order}, 取消小包单任务已存在-无需创建')
                    if order.tracking_num and not cancel_task.tracking_num:
                        cancel_task.tracking_num = order.tracking_num
                        cancel_task.save(update_fields=['tracking_num'])
                        logger.info(f'order = {order}, 更新取消小包单任务的tracking_num成功')
                    exit_count += 1
            except Exception as e:
                logger.error(f'order = {order}, 生成取消小包单任务失败: {traceback.format_exc()}')

    except Exception as e:
        logger.error(f'generate_cancel_parcel_order_label_tasks执行失败: {traceback.format_exc()}')
    end_time = datetime.now()
    time_diff = end_time - start_time
    logger.info(
        f'generate_cancel_parcel_order_label_tasks执行完毕, 花费时间: {time_diff}, 创建任务数量: {create_count}, 已存在任务数量: {exit_count}, 执行总数: {order_count}')


@app.task(bind=True, base=QueueOnce)
def cancel_label_with_cancelled_order(self):
    """取消异常小包单的面单"""
    logger.info('开始进行二次取消小包单')
    start_time = datetime.now()
    try:
        current_timestamp = int(time.time())
        # 获取30天前的时间戳
        thirty_days_ago = current_timestamp - (30 * 24 * 60 * 60)

        # 查询待处理的任务，限制调度量，确保在两个任务的间隔期间能跑完调度出来的任务;130个8S左右
        tasks = CancelParcelCustomerOrderLabelTask.objects.filter(
            status='NORMAL',
            next_run_time__lt=current_timestamp
        )[:300]

        for task in tasks:
            try:
                # 检查订单创建时间是否在30天内
                order_create_timestamp = int(task.order_create_date.timestamp())
                if order_create_timestamp < thirty_days_ago:
                    # 更新超时任务状态
                    task.status = 'EXPIRED'
                    task.next_run_time = 1999999999
                    task.remark = '小包单创建已经超过30天'
                    task.save()
                    continue
                # 获取需要取消的面单任务
                label_tasks = ParcelOrderLabelTask.objects.filter(~(Q(product__code='ZX_LABEL')), order_num=task.order)
                cancel_results = []
                if not label_tasks:
                    task.status = 'COMPLETED'
                    task.next_run_time = 1999999999
                    task.remark = '不存在面单任务'
                    task.save()
                    continue
                cancel_status = 'UNKONW'
                for order_label_task in label_tasks:
                    product = order_label_task.product
                    service = Service.objects.filter(product=product.id, del_flag=False).first()
                    if not service:
                        logger.info('二次取消小包单-订单号' + task.order_num + '产品[' + product.code + ']未配置服务')
                        task.remark = '产品[' + product.code + ']未配置服务,如需取消请联系供应商处理'
                        cancel_status = 'FAILED'
                        continue

                    # 如果是虚拟产品和成本最低的，用真实产品
                    if product.is_virtual:
                        product = task.order.real_product
                        service = Service.objects.filter(product=product, del_flag=False).first()

                    if not service or not service.butt_code:
                        logger.info(
                            f'二次取消小包单-订单号{task.order_num}的产品[{product.code}]没有供应商服务，无法取消')
                        task.remark = '产品[' + product.code + ']没有供应商服务,如需取消请联系供应商处理'
                        cancel_status = 'FAILED'
                        continue

                    supplier_butt = SupplierButt.objects.get(id=service.butt_code.id)
                    

                    supplier_account_list = SupplierButtAccount.objects.filter(
                        vendor_id=supplier_butt.vendor_id.id,
                        del_flag=False
                    )
                    supplier_account = supplier_account_list.first()
                    class_name = supplier_butt.class_name
                    if not order_label_task.third_order_no:
                        order_label_task.third_order_no = task.order_num
                    label_order_vo = LabelOrderVo()
                    label_order_vo.orderLabelTask = order_label_task
                    label_order_vo.supplierAccount = supplier_account
                    label_order_vo.customerOrder = task.order
                    label_order_vo.service = service
                    if not supplier_butt.is_support_cancel:
                        logger.info(f'二次取消小包单-订单号{task.order_num}的产品[{product.code}]不支持取消')
                        task.remark = '产品[' + product.code + ']供应商不支持取消,如需取消请联系供应商处理'
                        cancel_status = 'FAILED'
                        # try:
                        #     obj = globals()[class_name]()
                        #     result = cancel_label(obj, label_order_vo)
                        #     logger.info(f'二次取消小包单{task.order_num} - 面单任务id={order_label_task.pk}- 结果: {result}')
                        # except Exception as e:
                        #     pass
                        continue
                    obj = globals()[class_name]()
                    result = cancel_label(obj, label_order_vo)
                    cancel_results.append(f"{result}")
                    logger.info(f'二次取消小包单{task.order_num} - 面单任务id={order_label_task.pk}- 结果: {result}')
                task.next_run_time = current_timestamp + (24 * 60 * 60)  # 1天后
                task.result = ';'.join(cancel_results)
                list_success_str = [
                    "{'code': '0'}",
                    "状态有变动",
                    "Canceling",
                    "CancelSucceeded",
                    "{'code': '400', 'msg': '错误状态：Retry'}",
                    "{'code': '400', 'msg': '错误状态：Error'}",
                    "{'code': '400', 'msg': '错误状态：Created'}"
                ]
                if task.cancel_status != 'Success':
                    if task.result:
                        for succcess_str in list_success_str:
                            if succcess_str in task.result:
                                cancel_status = 'Success'
                                break
                    task.cancel_status = cancel_status
                task.run_count += 1
                task.save()

            except Exception as e:
                logger.error(f'二次取消小包面单失败任务ID={task.id}时发生错误: {traceback.format_exc()}')
                task.next_run_time = current_timestamp + (24 * 60 * 60)
                task.result = f"{e}"
                task.save()

    except Exception as e:
        logger.error(f'cancel_label_with_cancelled_order执行失败: {traceback.format_exc()}')
    end_time = datetime.now()
    time_diff = end_time - start_time
    logger.info(f'二次取消面单处理完毕, 花费时间:{time_diff}')




# 每天给客户推送订单消息(每日dmas)
@app.task(bind=True, base=QueueOnce)
def fba_order_statistics_dmas(self, write_excel=False):
    logger.info('fba_order_statistics_dmas start')
    if settings.SYSTEM_ORDER_MARK not in ['MZ']:
        return
    else:
        time.sleep(10)

    # base64_code = get_orders_txt_base64(['aaabbb', 'cccddd'])
    # send_dmas_base64_txt(base64_code, message_group=['铭志每日财务报表群'])
    # return

    time.sleep(5)
    fba_order_not_revenue_confirm(write_excel=write_excel)

    logger.info('fba_order_statistics_dmas end')


# 上周7天每天的收入和成本
@app.task(bind=True, base=QueueOnce)
def settle_statistics_dmas(self):
    logger.info('开始执行settle_statistics_dmas')
    if settings.SYSTEM_ORDER_MARK not in ['MZ']:
        return
    else:
        time.sleep(10)
    now = datetime.now()
    start_day = now - timedelta(days=now.weekday() + 7)
    statistics_revenue_and_cost(start_day, now - timedelta(days=now.weekday()))


# 上周7天每天的下单量
@app.task(bind=True, base=QueueOnce)
def place_order_count_dmas(self):
    logger.info('开始执行place_order_count_dmas')
    if settings.SYSTEM_ORDER_MARK not in ['MZ']:
        return
    else:
        time.sleep(10)
    now = datetime.now()
    start_day = now - timedelta(days=now.weekday() + 7)
    place_order_count(start_day, now - timedelta(days=now.weekday()))


# 海运时效报表(main)
@app.task(bind=True, base=QueueOnce)
def ocean_order_aging_dmas(self, start_day=None, end_day=None):
    is_notification = Dict.objects.filter(label='OceanDmasNotification', del_flag=False).last()
    if not is_notification:
        return
    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'FX']:
        return
    else:
        time.sleep(10)
    logger.info('ocean_order_aging_dmas start')
    current_date = datetime.today()
    two_weeks = timedelta(days=60)
    delta = timedelta(days=6)
    # 计算两周前的日期
    two_weeks_ago = current_date - two_weeks
    previous_date = two_weeks_ago - delta

    # 格式化日期输出
    # start_day = previous_date.strftime("%Y-%m-%d")
    # end_day = two_weeks_ago.strftime("%Y-%m-%d")
    # 如果未传入参数，则赋默认值
    if start_day is None:
        start_day = previous_date
    if end_day is None:
        end_day = two_weeks_ago
    # ocean_order_aging(start_day, now - timedelta(days=now.weekday()))
    query_products = ['PK0001', 'PK0003', 'PK0004', 'PK0005', 'PK0007', 'PK0008', 'PK0012']

    pick_up_aging_img = pick_up_aging(query_products, start_day, end_day)
    warehouse_codes_map = {
        '洛杉矶渠道': [
            'IUSJ', 'IUSP', 'IUSQ', 'LAX9', 'SBD1', 'PSC2', 'MQJ1', 'ONT8', 'GYR2', 'GYR3', 'IАН3',
            'IND9', 'LAS1', 'VGT2', 'SMF3', 'MEM1', 'MDW2', 'LGB8', 'RFD2', 'FTW1', 'SCK4', 'MIT2', 'GEU2'
        ],
        '纽约渠道': [
            'ABE8', 'AVPI', 'CLT2', 'ORF2', 'RDU2', 'RMN3', 'SWF2', 'TEB9', 'CHO1', 'BOS7', 'ILG1',
            'SWF1', 'TEB4'
        ],
        '萨凡纳渠道': ['CHA2', 'CLT2', 'CLT3', 'JAX3', 'MCO2', 'MGE3', 'PBI3', 'RDU2', 'RDU4', 'SAV3', 'TMB8'],
        '芝加哥渠道': [
            'DET1', 'DET2', 'IND2', 'MDW2', 'MDW9', 'ORD2', 'STL3', 'STL4', 'CMH3', 'IGQ2', 'MDW6',
            'MDW8', 'MKC4'
        ],
    }
    if pick_up_aging_img:
        all_img_base64 = [pick_up_aging_img]
    else:
        all_img_base64 = []
    for channel, warehouse_codes in warehouse_codes_map.items():
        if channel == '洛杉矶渠道':
            arrive_overseas_warehouse_aging_img = arrive_overseas_warehouse_aging(channel, warehouse_codes,
                                                                                  start_day, end_day,
                                                                                  is_draw_big_title='提柜到入仓平均时效')
        else:
            arrive_overseas_warehouse_aging_img = arrive_overseas_warehouse_aging(channel, warehouse_codes,
                                                                                  start_day, end_day)
            # arrive_overseas_warehouse_aging_img = None
        if arrive_overseas_warehouse_aging_img:
            all_img_base64.append(arrive_overseas_warehouse_aging_img)

    # for channel, aaa in warehouse_codes_map2.items():
    #     if channel == '洛杉矶渠道':
    #         arrive_overseas_warehouse_aging_img = arrive_overseas_warehouse_aging(channel, aaa[1], aaa[0],
    #                                                                               is_draw_big_title='提柜到入仓平均时效')
    #     else:
    #         arrive_overseas_warehouse_aging_img = arrive_overseas_warehouse_aging(channel, aaa[1], aaa[0])
    #     if arrive_overseas_warehouse_aging_img:
    #         all_img_base64.append(arrive_overseas_warehouse_aging_img)

    img_base64 = combine_multiple_plots_vertically(all_img_base64)

    img_url = save_image_file(img_base64)

    send_ocean_order_aging_img(img_url, start_day, end_day, is_notification)

    logger.info('ocean_order_aging_dmas end')


# 海运时效报表-开船到提柜时效(main)
@app.task(bind=True, base=QueueOnce)
def pick_up_aging_dmas(self, start_day=None, end_day=None):
    is_notification = Dict.objects.filter(label='OceanDmasNotification', del_flag=False).last()
    if not is_notification:
        return
    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'FX']:
        return
    else:
        time.sleep(5)
    logger.info('ocean_order_aging_dmas start')
    current_date = datetime.today()
    two_weeks = timedelta(days=60)
    delta = timedelta(days=6)
    # 计算两周前的日期
    two_weeks_ago = current_date - two_weeks
    previous_date = two_weeks_ago - delta

    # 如果未传入参数，则赋默认值
    if start_day is None:
        start_day = previous_date
    if end_day is None:
        end_day = two_weeks_ago

    query_products = [
        'PK0005', 'PK0006',
        'PK0047', 'PK0048',
        'PK0049', 'PK0050',
        'PK0007', 'PK0008',
        'PK0034', 'PK0035',
        'PK0026', 'PK0027',
        'PK0012', 'PK0013',
        'PK0023', 'PK0024',
        'PK0054', 'PK0055',
        'PK0052', 'PK0053',
        'PK0043', 'PK0044',
        'PK0001',
        'PK0003',
        'PK0056',
        'PK0061',
        'PK0004',
        'PK0046',
    ]
    for i in range(0, len(query_products), 10):
        query_products_10 = query_products[i: i + 10]
        pick_up_aging_img = pick_up_aging(query_products_10, start_day, end_day)

        if not pick_up_aging_img:
            logger.info(f'pick_up_aging_dmas has no pick_up_aging_img: {i}')
            continue

        img_url = save_image_file(pick_up_aging_img)
        send_ocean_order_aging_img(img_url, start_day, end_day, is_notification)

    logger.info('ocean_order_aging_dmas end')


# 海运时效报表-提柜到入仓时效(main)
@app.task(bind=True, base=QueueOnce)
def arrive_overseas_warehouse_aging_dmas(self, start_day=None, end_day=None):
    is_notification = Dict.objects.filter(label='OceanDmasNotification', del_flag=False).last()
    if not is_notification:
        return
    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'FX']:
        return
    else:
        time.sleep(5)
    logger.info('ocean_order_aging_dmas start')
    current_date = datetime.today()
    two_weeks = timedelta(days=60)
    delta = timedelta(days=6)
    # 计算两周前的日期
    two_weeks_ago = current_date - two_weeks
    previous_date = two_weeks_ago - delta

    # 如果未传入参数，则赋默认值
    if start_day is None:
        start_day = previous_date
    if end_day is None:
        end_day = two_weeks_ago

    warehouse_codes_map = [
        {
            'title': '志速达卡派',
            'product': 'PK0005',
            'end_zone': [
                'ABQ2', 'FTW1', 'FWA4', 'GEU2', 'GYR2', 'GYR3', 'IAH3', 'IND9', 'LAS1', 'LGB8', 'MDW2', 'MIT2', 'ONT8',
                'POC1', 'POC2', 'POC3', 'PSC2', 'SBD1', 'SCK4', 'SCK8', 'SMF3', 'SMF6', 'TCY1', 'TCY2', 'VGT2',
            ]
        },
        {
            'title': '志速达拼箱',
            'product': 'PK0006',
            'end_zone': [
                'ABQ2', 'FTW1', 'FWA4', 'GEU2', 'GYR2', 'GYR3', 'IAH3', 'IND9', 'LAS1', 'LGB8', 'MDW2', 'MIT2', 'ONT8',
                'POC1', 'POC2', 'POC3', 'PSC2', 'SBD1', 'SCK4', 'SCK8', 'SMF3', 'SMF6', 'TCY1', 'TCY2', 'VGT2',
            ]
        },
        {
            'title': '合德定提卡派',
            'product': 'PK0047',
            'end_zone': [
                'ABQ2', 'PSC2', 'IND9', 'GYR3', 'FTW1', 'MIT2', 'SMF3', 'SCK8', 'GEU2', 'GYR2', 'SBD1', 'TCY2', 'LGB8',
                'TCY1', 'LAS1', 'POC1', 'GEU3', 'IAH3', 'SMF6', 'LAX9', 'FWA4', 'VGT2', 'ONT8', 'SCK4', 'FTW5'
            ]
        },
        {
            'title': '合德定提拼箱',
            'product': 'PK0048',
            'end_zone': [
                'ABQ2', 'PSC2', 'IND9', 'GYR3', 'FTW1', 'MIT2', 'SMF3', 'SCK8', 'GEU2', 'GYR2', 'SBD1', 'TCY2', 'LGB8',
                'TCY1', 'LAS1', 'POC1', 'GEU3', 'IAH3', 'SMF6', 'LAX9', 'FWA4', 'VGT2', 'ONT8', 'SCK4', 'FTW5'
            ]
        },
        {
            'title': '普船卡派（洛杉矶）',
            'product': 'PK0007',
            'end_zone': [
                'ABQ2', 'MIT2', 'LGB8', 'PSC2', 'IUSP', 'LFT1', 'ONT8', 'PHX7', 'HLI2', 'SCK8', 'GEU3', 'IND9', 'GYR3',
                'GYR2', 'IUSJ', 'DEN8', 'SBD1', 'FTW5', 'LAS1', 'SMF3', 'XLX7', 'FTW1', 'FWA4', 'SCK4', 'HSV1',
            ]
        },
        {
            'title': '普船拼箱（洛杉矶）',
            'product': 'PK0008',
            'end_zone': [
                'ABQ2', 'MIT2', 'LGB8', 'PSC2', 'IUSP', 'LFT1', 'ONT8', 'PHX7', 'HLI2', 'SCK8', 'GEU3', 'IND9', 'GYR3',
                'GYR2', 'IUSJ', 'DEN8', 'SBD1', 'FTW5', 'LAS1', 'SMF3', 'XLX7', 'FTW1', 'FWA4', 'SCK4', 'HSV1',
            ]
        },
        {
            'title': '美西卡派特惠专线',
            'product': 'PK0034',
            'end_zone': [
                'ABQ2', 'MIT2', 'LGB8', 'PSC2', 'IUSP', 'LFT1', 'ONT8', 'PHX7', 'HLI2', 'SCK8', 'GEU3', 'IND9', 'GYR3',
                'GYR2', 'IUSJ', 'DEN8', 'SBD1', 'FTW5', 'LAS1', 'SMF3', 'XLX7', 'FTW1', 'FWA4', 'SCK4',
            ]
        },
        {
            'title': '美西拼箱特惠专线',
            'product': 'PK0035',
            'end_zone': [
                'ABQ2', 'MIT2', 'LGB8', 'PSC2', 'IUSP', 'LFT1', 'ONT8', 'PHX7', 'HLI2', 'SCK8', 'GEU3', 'IND9', 'GYR3',
                'GYR2', 'IUSJ', 'DEN8', 'SBD1', 'FTW5', 'LAS1', 'SMF3', 'XLX7', 'FTW1', 'FWA4', 'SCK4',
            ]
        },
        {
            'title': '芝加哥专线-整柜直送',
            'product': 'PK0026',
            'end_zone': [
                'FWA4', 'IND9', 'MDW2',
            ]
        },
        {
            'title': '芝加哥拼箱-整柜直送',
            'product': 'PK0027',
            'end_zone': [
                'FWA4', 'IND9', 'MDW2',
            ]
        },
        {
            'title': '芝加哥专线',
            'product': 'PK0012',
            'end_zone': [
                'CMH2', 'CMH3', 'DET1', 'DET2', 'FOE1', 'FWA4', 'IGQ2', 'IND5', 'IND9', 'JVL1', 'LAN2', 'MCI3', 'MDW2',
                'MDW6', 'MEM1', 'MEM6', 'MKC4', 'MQJ1', 'MQJ2', 'ORD2', 'PPO4', 'RFD2', 'STL4',
            ]
        },
        {
            'title': '芝加哥拼箱',
            'product': 'PK0013',
            'end_zone': [
                'CMH2', 'CMH3', 'DET1', 'DET2', 'FOE1', 'FWA4', 'IGQ2', 'IND5', 'IND9', 'JVL1', 'LAN2', 'MCI3', 'MDW2',
                'MDW6', 'MEM1', 'MEM6', 'MKC4', 'MQJ1', 'MQJ2', 'ORD2', 'PPO4', 'RFD2', 'STL4',
            ]
        },
        {
            'title': '纽约快线卡派',
            'product': 'PK0023',
            'end_zone': [
                'ABE8', 'SWF2', 'AVP1', 'RMN3', 'TEB9', 'ORF2', 'ACY2', 'BOS7', 'ABE4', 'ILG1', 'TEB4', 'XRI3', 'AKR1',
                'ALB1', 'DCA6', 'CHO1', 'TEB3', 'BWI4', 'TEB6', 'HGR6', 'MDT1', 'LBE1', 'MDT4', 'CMH3', 'PIT2'
            ]
        },
        {
            'title': '纽约快线拼箱卡派',
            'product': 'PK0024',
            'end_zone': [
                'ABE8', 'SWF2', 'AVP1', 'RMN3', 'TEB9', 'ORF2', 'ACY2', 'BOS7', 'ABE4', 'ILG1', 'TEB4', 'XRI3', 'AKR1',
                'ALB1', 'DCA6', 'CHO1', 'TEB3', 'BWI4', 'TEB6', 'HGR6', 'MDT1', 'LBE1', 'MDT4', 'CMH3', 'PIT2'
            ]
        },
        {
            'title': '纽约经济卡派',
            'product': 'PK0054',
            'end_zone': [
                'ABE8', 'AVP1', 'SWF2', 'RMN3', 'ORF2', 'TEB9', 'LBE1', 'HGR6', 'ABE4', 'MDT4', 'ACY2', 'BOS7', 'PIT2',
                'AKR1', 'MDT1', 'ALB1', 'TEB6', 'TEB4', 'DCA6', 'CMH3', 'PHL6', 'XRI3', 'BWI4', 'CMH2', 'SWF1',
            ]
        },
        {
            'title': '纽约经济拼箱卡派',
            'product': 'PK0055',
            'end_zone': [
                'ABE8', 'AVP1', 'SWF2', 'RMN3', 'ORF2', 'TEB9', 'LBE1', 'HGR6', 'ABE4', 'MDT4', 'ACY2', 'BOS7', 'PIT2',
                'AKR1', 'MDT1', 'ALB1', 'TEB6', 'TEB4', 'DCA6', 'CMH3', 'PHL6', 'XRI3', 'BWI4', 'CMH2', 'SWF1',
            ]
        },
        {
            'title': '萨凡纳快线卡派',
            'product': 'PK0052',
            'end_zone': [
                'BNA2', 'BNA6', 'CHA2', 'CLT2', 'CLT3', 'GSO1', 'HSV1', 'IUSR', 'JAX3', 'MCO2', 'MEM1', 'MGE3', 'PBI2',
                'PBI3', 'RDU2', 'RDU4', 'RYY2', 'SAV3', 'TMB8', 'TPA2', 'TPA3', 'TPA6', 'XAV3', 'XLX6',
            ]
        },
        {
            'title': '萨凡纳快线拼箱卡派',
            'product': 'PK0053',
            'end_zone': [
                'BNA2', 'BNA6', 'CHA2', 'CLT2', 'CLT3', 'GSO1', 'HSV1', 'IUSR', 'JAX3', 'MCO2', 'MEM1', 'MGE3', 'PBI2',
                'PBI3', 'RDU2', 'RDU4', 'RYY2', 'SAV3', 'TMB8', 'TPA2', 'TPA3', 'TPA6', 'XAV3', 'XLX6',
            ]
        },
        {
            'title': '萨凡纳经济卡派',
            'product': 'PK0043',
            'end_zone': [
                'BNA2', 'BNA6', 'CHA2', 'CLT2', 'CLT3', 'GSO1', 'HSV1', 'IUSR', 'JAX3', 'MCO2', 'MEM1', 'MEM8', 'MGE3',
                'PBI3', 'RDU2', 'RDU4', 'RYY2', 'SAV3', 'TMB8', 'TPA2', 'TPA3', 'TPA6', 'XAV3', 'XLX6',
            ]
        },
        {
            'title': '萨凡纳经济拼箱卡派',
            'product': 'PK0044',
            'end_zone': [
                'BNA2', 'BNA6', 'CHA2', 'CLT2', 'CLT3', 'GSO1', 'HSV1', 'IUSR', 'JAX3', 'MCO2', 'MEM1', 'MEM8', 'MGE3',
                'PBI3', 'RDU2', 'RDU4', 'RYY2', 'SAV3', 'TMB8', 'TPA2', 'TPA3', 'TPA6', 'XAV3', 'XLX6',
            ]
        },
        {
            'title': '志尊达卡派',
            'product': 'PK0046',
            'end_zone': [
                'ABQ2', 'GEU2', 'GEU3', 'GEU5', 'GYR2', 'GYR3', 'LAS1', 'LAX9', 'LGB8',
                'MIT2', 'ONT8', 'POC1', 'POC3', 'PSC2', 'SBD1', 'SCK4', 'SCK8', 'SMF3', 'VGT2',
            ]
        },
    ]
    # for index, channel in enumerate(warehouse_codes_map):
    for i in range(0, len(warehouse_codes_map), 4):
        warehouse_codes_map_4 = warehouse_codes_map[i: i + 4]
        all_img_base64 = []
        for index, channel in enumerate(warehouse_codes_map_4):
            if index == 0:
                arrive_overseas_warehouse_aging_img = arrive_overseas_warehouse_aging_new(
                    channel,
                    start_day,
                    end_day,
                    is_draw_big_title='提柜到入仓平均时效'
                )
            else:
                arrive_overseas_warehouse_aging_img = arrive_overseas_warehouse_aging_new(
                    channel,
                    start_day,
                    end_day
                )

            if arrive_overseas_warehouse_aging_img:
                all_img_base64.append(arrive_overseas_warehouse_aging_img)

        img_base64 = combine_multiple_plots_vertically(all_img_base64)
        img_url = save_image_file(img_base64)
        send_ocean_order_aging_img(img_url, start_day, end_day, is_notification, is_pick_up=False)

    logger.info('ocean_order_aging_dmas end')


def send_ocean_order_aging_img(img_url, start_day, end_day, is_notification, is_pick_up=True):
    if is_pick_up:
        msg_format = '开船到提柜时效'
    else:
        msg_format = '提柜到入仓时效'
    if isinstance(start_day, str):
        start_day_format = start_day
        end_day_format = end_day
    else:
        start_day_format = start_day.strftime("%m%d")
        end_day_format = end_day.strftime("%m%d")
    if is_notification.value == '1':
        send_dmas_base64_image(
            file_url=img_url,
            # send_msg=f'美国海运时效报表 {start_day.strftime("%m%d")}-{end_day.strftime("%m%d")}',
            send_msg=f'',
            message_group=['普信IT支持群'],
            # at_list=['王子斌Eli', '玄冬'],
            at_msg=f'美国海运时效报表-{msg_format} {start_day_format}-{end_day_format}'
        )
    elif is_notification.value == '2':
        send_dmas_base64_image(
            file_url=img_url,
            # send_msg=f'美国海运时效报表 {start_day.strftime("%m%d")}-{end_day.strftime("%m%d")}',
            send_msg=f'',
            message_group=['铭志经营分析'],
            # at_list=['闫 Leo', '王子斌Eli', '玄冬'],
            at_msg=f'美国海运时效报表-{msg_format} {start_day_format}-{end_day_format}'
        )


def get_orders_txt_base64(orders):
    orders_string = '\n'.join(orders)
    base64_string = base64.b64encode(orders_string.encode("utf-8")).decode("utf-8")
    return base64_string


def base64_to_file(base64_code, file_name):
    with open(file_name + '.png', "wb") as code:
        code.write(base64.b64decode(base64_code))


# 同步出口报关单
@app.task(bind=True, base=QueueOnce)
def sync_clearance_out_data_to_supplier(self, mode_key):
    logger.info(f'sync_clearance_out_data_to_supplier start-->{mode_key}')

    if settings.SYSTEM_ORDER_MARK not in ['MZ', 'YQF', 'FX']:
        return

    # 查询待处理订单
    wait_pull_tasks = ClearanceOutSyncTask.objects.filter(Q(status='UnHandled') | Q(status='Failure'),
                                                          handle_times=0,
                                                          mode_key=mode_key, del_flag=False)

    if not wait_pull_tasks.exists():
        return

    wait_pull_tasks = wait_pull_tasks[:100]

    wait_pull_task_ids = [i.id for i in wait_pull_tasks]
    for wait_pull_task in wait_pull_tasks:

        clearance_out = wait_pull_task.order_num

        # 获取供营商配置信息
        code, res = get_service_class_url('CustomerOrderSyncService')
        if code == 1:
            sync_order_error_record(wait_pull_task, res)
            continue
        else:
            supplier_account = res

        # 过滤掉已经作废的订单, 不允许再同步数据
        if clearance_out.clear_status == 'VO':
            sync_order_error_record(wait_pull_task, f'订单: {clearance_out.clearance_num} 已作废, 无法同步数据')
            continue

        try:
            # 推送订单件重体和状态等数据(下单)
            if wait_pull_task.task_type == 'PUSH_ORDER':
                push_clearance_out_order(supplier_account, wait_pull_task, clearance_out)

            # 拉取订单数据
            elif wait_pull_task.task_type == 'PULL_ORDER_STATUS':
                pull_clearance_out_order(supplier_account, wait_pull_task, clearance_out)

            logger.info(f'同步出口报关单成功, 报关单号: {clearance_out.clearance_num}')
        except Exception as e:
            logger.info(f'同步出口报关单失败, 失败原因: {traceback.format_exc()}')

    ClearanceOutSyncTask.objects.filter(id__in=wait_pull_task_ids).update(handle_times=1)
    logger.info(f'sync_clearance_out_data_to_supplier end-->{mode_key}')


# 小包单拦截
@app.task(bind=True, base=QueueOnce)
def intercept_parcel_order_task(self, order_id):
    logger.info(f'intercept_parcel_order_task start-->{order_id}')

    parcel_order = ParcelCustomerOrder.objects.filter(id=order_id, del_flag=False).first()
    if not parcel_order:
        logger.info(f'intercept_parcel_order_task end-->{order_id} 订单不存在')
        return False

    order_num = parcel_order.order_num
    order_status = parcel_order.order_status
    if order_status in ['DR', 'VO', 'BC']:
        parcel_order.intercept_mark = True
        # 已拦截
        parcel_order.intercept_status = 'C'
        parcel_order.save()
        logger.info(f'intercept_parcel_order_task end-->{order_num} 订单状态: {order_status} 已拦截')
        return True

    # 请求WMS拦截接口，看是否能拦截
    intercept_result_data = request_wms_intercept(order_num)
    if not intercept_result_data:
        logger.info(f'intercept_parcel_order_task end-->{order_num} 未能拦截')
        parcel_order.intercept_mark = False
        parcel_order.intercept_status = 'F'
        parcel_order.save()
        return False

    # 拦截成功
    parcel_order.intercept_mark = True
    parcel_order.intercept_status = 'C'
    parcel_order.save()

    return True


@app.task(
    bind=True,
    base=QueueOnce,
    once={'graceful': True}
)
def set_multistep_parcel_track_task(self, order_num, track_code, user_id, location, timezone_offset=None):
    """添加轨迹编码"""
    if settings.SYSTEM_MARK not in ['HJ']:
        return True

    try:
        logger.info(f'set_multistep_parcel_track_task start-->{order_num}')
        # 可能抛出ObjectDoesNotExist
        user = UserProfile.objects.get(pk=user_id)
        set_multistep_parcel_track(order_num, track_code, datetime.now(), user, location, timezone_offset=timezone_offset)
        logger.info(f'set_multistep_parcel_track_task end-->{order_num}')
        return True

    except ObjectDoesNotExist as exc:
        logger.error(f"{exc} not found, retrying...")
        raise self.retry(exc=exc, countdown=5)  # 表数据不存在等待2秒重试

    except DatabaseError as exc:
        logger.error("Database error, retrying...")
        raise self.retry(exc=exc)  # 使用默认退避时间

    except Exception as exc:
        logger.exception("Unexpected error")
        raise  # 其他异常也会被autoretry_for捕获


@app.task(bind=True, max_retries=3, base=QueueOnce, once={'graceful': True})
def delete_multistep_parcel_track_task(self, order_num, track_code, user_id):
    """删除轨迹编码"""
    try:
        logger.info(f'delete_multistep_parcel_track_task start-->{order_num}, {track_code}')

        from rbac.models import UserProfile
        from common.tools import delete_multistep_parcel_track

        # 获取用户对象
        user = UserProfile.objects.get(pk=user_id)

        # 调用删除轨迹方法
        deleted_count = delete_multistep_parcel_track(order_num, track_code, user)

        logger.info(f'delete_multistep_parcel_track_task end-->{order_num}, 删除数量: {deleted_count}')
        return deleted_count

    except ObjectDoesNotExist as exc:
        logger.error(f"用户 {user_id} 不存在, 重试中...")
        raise self.retry(exc=exc, countdown=5)

    except DatabaseError as exc:
        logger.error("数据库错误, 重试中...")
        raise self.retry(exc=exc)

    except Exception as exc:
        logger.error(f"删除轨迹时发生未知错误: 订单={order_num}, 代码={track_code}, 错误={str(exc)}")
        raise self.retry(exc=exc, countdown=60)


@app.task(bind=True, max_retries=1, base=QueueOnce, once={'graceful': True})
def create_beat_scan_form_task(self, mode_key):
    """创建scan_form任务"""
    logger.info(f'-----create_beat_scan_form_task---->{mode_key}')
    scan_form_task_list = OrderScanFormTask.objects.filter(status='UnHandled', del_flag=False, mode_key=mode_key)[:200]
    if not scan_form_task_list.exists():
        return

    # 再处理
    for scan_form_task in scan_form_task_list:
        handle_scan_form_task.delay(mode_key, scan_form_task.id)


@app.task(bind=True, max_retries=1, base=QueueOnce,
          once={'graceful': True, 'keys': ['mode_key', 'scan_form_task_id'], 'timeout': 600})
def handle_scan_form_task(self, mode_key, scan_form_task_id):
    """处理 scan form"""
    logger.info(f'开始处理scanform scan_form_task_id:{scan_form_task_id}, mode_key:{mode_key}')
    task = OrderScanFormTask.objects.get(pk=scan_form_task_id, status='UnHandled', mode_key=mode_key, del_flag=False)
    lock_key = f'scanform_task_{task.id}'
    try:
        if cache.get(lock_key):
            logger.info(f'scanform_task({task.id}) is running')
            return
        cache.set(lock_key, "1", 60)
        task.status = 'HandledBy3rdNo'
        task.save(update_fields=['status'])
        request_data = task.request_data
        order_nums = request_data.get('order_nums')
        orders = ParcelCustomerOrder.objects.filter(
            ~Q(order_status__in=['VO', 'BC']),
            Q(order_num__in=order_nums) |
            Q(customer_order_num__in=order_nums) |
            Q(tracking_num__in=order_nums),
            del_flag=False,
            customer=task.create_by.company
        )
        result = handler_scan_form_v2(orders, request_data, task.scan_form_type)
    except Exception as e:
        task.remark = f'scanform 失败 {e}'
        task.status = 'Failure'
        task.save(update_fields=['status', 'remark'])
        logger.info(f'OrderScanFormTask:{task.id} 异常： {traceback.format_exc()}')
        if cache.get(lock_key):
            cache.delete(lock_key)
        return

    if not result or result.get('code', None) is None:
        task.remark = '不支持scanform'
        task.status = 'Failure'
        task.save(update_fields=['status', 'remark'])
        logger.info(f'OrderScanFormTask:{task.id} 不支持scanform')
        return
    if settings.SYSTEM_VERSION == 'V2':
        if int(result['code']) == 200:
            task.status = 'Success'
            task.remark = ''
            task.response_data = result
            task.save()

            result_order_nums = result.get('order_nums')
            if result_order_nums:
                # 添加轨迹
                success_order_nums = []
                for i in result_order_nums:
                    order_num = i.get('order_num')
                    if i.get('code') == 200:
                        success_order_nums.append(order_num)
                        set_multistep_parcel_track_task.delay(order_num, 'MP-000', task.create_by.id, '')
                    else:
                        set_multistep_parcel_track_task.delay(order_num, 'MP-999', task.create_by.id, '')
                if success_order_nums:
                    ParcelCustomerOrder.objects.filter(order_num__in=success_order_nums).update(order_status='MP')
                    # 订单变更已确认
                    ParcelOrderExtend.objects.filter(customer_order__order_num__in=success_order_nums,
                                                     del_flag=False).update(confirm_types=2, confirm_progress=2)
        logger.info(f'api_scanform 返回: {result}')
    else:
        if int(result['code']) == 0:
            task.status = 'Success'
            task.remark = ''
            task.response_data = result
            task.save(update_fields=['status', 'response_data', 'remark'])
            logger.info(f'OrderScanFormTask:{task.id},order_nums = {order_nums}, api_scanform 返回: {result}')
        else:
            msg = result['msg']
            # msg有可能超长
            # task.remark = msg
            task.status = 'Failure'
            task.response_data = result
            task.save(update_fields=['status', 'remark', 'response_data'])
            logger.info(f'OrderScanFormTask:{task.id},order_nums = {order_nums},请求供应商失败: {msg}')
    if cache.get(lock_key):
        cache.delete(lock_key)


# 拉取shopify订单
@app.task(bind=True, base=QueueOnce)
def handle_shopify_order(self):
    logger.info(f'def handle_shopify_order: start')

    # 获取授权成功的店铺
    shop_queryset = Shop.objects.filter(del_flag=False, status=Shop.ShopStatusChoices.AUTH_SUCCESS.value)
    if not shop_queryset.exists():
        logger.info(f'def handle_shopify_order: end - 店铺不存在')
        return False

    for shop in shop_queryset:
        access_token = shop.access_token
        since_id = None  # 初始化 since_id 为 None

        # 获取店铺信息
        shop_data = get_shopify_shop_info(shop.shop_name, access_token)
        if not shop_data:
            logger.info(f'def handle_shopify_order: {shop.shop_name} end - 店铺信息获取失败')
            continue

        while True:
            # 同步已经授权成功的店铺对应的 Shopify 订单
            shopify_orders = sync_orders(shop.shop_name, access_token, since_id)
            if not shopify_orders:
                logger.info(f'def handle_shopify_order: {shop.shop_name} end - 没有更多订单')
                break  # 没有更多订单时跳出循环

            for order in shopify_orders:
                # 处理每个订单
                # user_queryset = UserProfile.objects.filter(company=)

                # 封装并保存单个订单
                set_parcel_customer_order_for_shopify_order(order, shop_data, shop)

            # 更新 since_id 为当前批次订单中的最后一个订单 ID
            if shopify_orders:
                since_id = shopify_orders[-1]['id']
            else:
                logger.info(f'def handle_shopify_order: 店铺:{shop.shop_name} 的订单已经全部同步完成, 没有更多订单')
                break  # 如果没有订单返回，跳出循环

    logger.info(f'def handle_shopify_order: end')


@app.task(bind=True, base=QueueOnce)
def get_fail_customer_order(self, wb_name=None):
    logger.info(f'def get_fail_customer_order: start')
    # url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=eb4effa9-34c8-4119-97f5-4f1fc366fbeb"
    if wb_name:
        first = Webhook.objects.filter(wb_name=wb_name).first()
        url = first.wb_url
    else:
        raise Exception("get_fail_customer_order need wb_name")
    sql = """
            SELECT
                a.customer_order_num
            FROM
                order_orderapilog a
                LEFT JOIN company_company b ON a.customer_id = b.id
                LEFT JOIN pms_product c ON a.product_id = c.id
            WHERE
                a.STATUS = 'Failure'
                AND a.task_name = 'HanJinIntegrationService'
                AND a.remark = '邮编校验不通过'
                AND customer_id = 5
                AND a.create_date>=DATE_SUB(CURDATE(), INTERVAL 1 DAY);
    """
    # 测试sql
    # sql = """
    #             SELECT
    #                 a.customer_order_num
    #             FROM
    #                 order_orderapilog a
    #                 LEFT JOIN company_company b ON a.customer_id = b.id
    #                 LEFT JOIN pms_product c ON a.product_id = c.id
    #             WHERE
    #                 a.STATUS = 'Failure'
    #                 AND a.task_name = 'HanJinIntegrationService'
    #                 AND a.remark = '邮编校验不通过';
    #     """
    with connection.cursor() as cursor:
        cursor.execute(sql)
        # 获取所有结果
        rows = cursor.fetchall()
        logger.info("get_fail_customer_order rows =========== ", rows)
        if rows:
            """推送企微消息"""
            rows = [row[0] for row in rows]
            order_len = len(rows)
            # now_dt = datetime.now().strftime("%Y-%m-%d")
            # 获取当前日期
            today = datetime.now()
            # 计算昨天的日期
            yesterday = today - timedelta(days=1)
            # 格式化为 "年-月-日" 的字符串
            yesterday_str = yesterday.strftime("%Y-%m-%d")
            content = r'通达收件人邮编错误出单<font color="warning">' + str(
                order_len) + r'</font>票，请留意。' + '\n' + '>' + '**创建日期**' + '\n' + '>' + '\n' + '>' + yesterday_str + '\n' + '>' + '\n' + '>' + '**单号列表**' + '\n' + '>' + '\n' + '>' + "\n>\n>".join(
                rows)
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            }
            try:
                requests.post(url=url, json=data)
            except Exception as e:
                logger.info(f'def get_fail_customer_order: error:{str(e)}')

        # 测试
        # data = {
        #     "msgtype": "text",
        #     "text": {
        #         "content": "广州今日天气：29度，大部分多云，降雨概率：60%",
        #         "mentioned_list": ["wangqing", "@all"],
        #         "mentioned_mobile_list": ["13800001111", "@all"]
        #     }
        # }
        # try:
        #     requests.post(url=url, json=data)
        # except Exception as e:
        #     logger.info(f'def get_fail_customer_order: error2:{str(e)}')


# 封装并保存shopify订单
@transaction.atomic
def set_parcel_customer_order_for_shopify_order(order, shop_data, shop):
    seller_order_id = order.get('id')

    # 判断是否已存在
    sales_order_queryset = SalesOrder.objects.filter(seller_order_id=seller_order_id, shop=shop, del_flag=False)
    if sales_order_queryset.exists():
        return

    # 创建一个 SalesOrder 实例, 将shopify订单同步到电商平台销售订单表
    user = shop.create_by
    sales_order = SalesOrder()
    sales_order.shop = shop
    sales_order.customer = shop.customer
    sales_order.create_by = user
    sales_order.update_by = user
    sales_order.create_date = datetime.now()
    sales_order.update_date = datetime.now()
    created_at = order.get('created_at')
    created_at = convert_to_utc_datetime(created_at)
    sales_order.order_time = created_at

    sales_order.customer_order_num = order.get('order_number')  # 客户订单号
    sales_order.order_status = 'PAID'  # 订单状态:已支付
    sales_order.order_source = 'Shopify'

    # fulfillments 对象
    # fulfillments = order.get('fulfillments', [])
    # if fulfillments:
    #     sales_order.tracking_num = fulfillments[0].get('tracking_number')

    # 获取收件人地址信息
    receiver = order['shipping_address']
    if not receiver:
        logger.info(f'订单{seller_order_id} 还未设置买家地址')
        return

    sales_order.buyer_first_name = receiver.get('first_name')  # 买家姓氏
    sales_order.buyer_last_name = receiver.get('last_name')  # 买家名字
    sales_order.payer = receiver.get('name')  # 付款人信息
    # 买家信息
    sales_order.buyer_name = receiver.get('name', '')
    sales_order.buyer_mail = receiver.get('email', '')
    sales_order.buyer_phone = receiver.get('phone', '')
    sales_order.buyer_country_code = receiver.get('country_code', '')
    sales_order.buyer_country = receiver.get('country', '')
    sales_order.buyer_state = receiver.get('province_code', '')
    sales_order.buyer_city_code = receiver.get('city', '')
    sales_order.buyer_city = receiver.get('city', '')
    sales_order.buyer_postcode = receiver.get('zip', '')
    # parcel_customer_order.buyer_house_num = receiver.get('doorcode', '')
    sales_order.buyer_address_one = receiver.get('address1', '')
    sales_order.buyer_address_two = receiver.get('address2', '')
    sales_order.buyer_company_name = receiver.get('company', '')

    # 封装fx_oc_sales_order相关字段信息
    sales_order.seller_account_name = order.get('contact_email')  # 卖家账户名称

    sales_order.seller_order_id = seller_order_id  # 卖家平台订单ID
    sales_order.seller_order_code = order.get('order_number')  # 卖家订单号
    sales_order.site_code = shop_data.get('primary_location_id')  # 电商站点编码
    sales_order.pay_status = 'Paid'  # 支付状态(Paid/UnPaid)
    # sales_order.buyer_user_id = order.get('order_number') # 买家用户ID

    # sales_order.buyer_shipping_selected = fulfillments.get('tracking_company') # 买家选择的配送方式
    sales_order.order_create_time = created_at  # 订单在平台的创建时间
    sales_order.checkout_time = convert_to_utc_datetime(order.get('processed_at'))  # 结账时间

    sales_order.paid_time = convert_to_utc_datetime(order.get('processed_at'))  # 付款时间
    # sales_order.shipped_time = order.get('order_number') # 发货时间
    sales_order.guarantee = 'N'  # 是否担保订单(Y/N)
    # if fulfillments:
    #     sales_order.guarantee_date = fulfillments.get('estimated_delivery_at')  # 承诺送达时间
    #     sales_order.carrier_name = fulfillments.get('tracking_company')  # 物流承运商名称

    sales_order.payment_method = order.get('payment_gateway_names')  # 付款方式

    sales_order.payee = shop_data.get('shop_owner')  # 收款人信息

    # refunds = order.get('refunds')
    # sales_order.pay_transcation_no = refunds['transactions']['id']  # 支付交易号

    sales_order.total_price = order.get('current_total_price')  # 订单总金额
    sales_order.total_price_currency = order.get('currency')  # 订单金额币种

    # 获取运费
    shipping_presentment_money = order.get('total_shipping_price_set')['presentment_money']
    if not shipping_presentment_money:
        sales_order.shipping_price = shipping_presentment_money['amount']  # 运费金额
        sales_order.shipping_price_currency = shipping_presentment_money['currency_code']  # 运费币种

    # 获取礼物包装费
    # gift_wrap_presentment_money = refunds['total_duties_set']['presentment_money']
    # if not gift_wrap_presentment_money :
    #     sales_order.gift_wrap_price = gift_wrap_presentment_money['amount']  # 礼物包装费
    #     sales_order.gift_wrap_price_currency = gift_wrap_presentment_money['currency_code']  # 礼物包装费币种

    # 获取优惠金额
    discount_presentment_money = order.get('total_discounts_set')['presentment_money']
    if not discount_presentment_money:
        sales_order.discount_price = discount_presentment_money['amount']  # 优惠金额
        sales_order.discount_price_currency = discount_presentment_money['currency_code']  # 优惠金额币种

    # 手续费 = 支付金额 (amount) - 入账金额 (processed_amount)
    # transactions = refunds['transactions']
    # if not transactions:
    #     if not transactions.get('amount') and not transactions.get('processed_amount'):
    #         sales_order.paypal_charge = Decimal(transactions.get('amount')) - Decimal(transactions.get('processed_amount'))
    #         sales_order.paypal_charge_currency = order.get('currency')

    sales_order.outbound_order_num = sales_order.tracking_num  # 供应商出库单号
    sales_order.warehouse_code = ''  # 供应商仓库编码
    sales_order.buyer_message = order.get('note_attributes')  # 买家留言信息

    # 获取发货渠道
    # fulfillment_service = fulfillments.get('service')
    # if not fulfillment_service:
    #     # 发货渠道(MFN/AFN)
    #     if fulfillment_service == 'manual':
    #         # AFN（亚马逊物流）
    #         sales_order.fulfillment_channel = 'AFN'
    #     else:
    #         # MFN（自营物流/商家自配送）
    #         sales_order.fulfillment_channel = 'MFN'

    sales_order.sync_status = '0'  # 同步状态
    sales_order.order_ship_status = ''  # 发货状态
    sales_order.document_code = order.get('order_number')  # 文档编码
    sales_order.is_non_amazon = '0'  # 是否非亚马逊(0/1)
    sales_order.sub_status = 0  # 子状态(0-3)
    sales_order.is_one_key = 0  # 是否一件代发(0/1)
    sales_order.seller_id = shop_data.get('id')  # 店铺ID

    # 卖家信息
    sales_order.contact_name = shop_data.get('shop_owner', '')
    sales_order.contact_email = shop_data.get('email', '')
    sales_order.contact_phone = shop_data.get('phone', '')
    sales_order.country_code = shop_data.get('country_code', '')
    # sales_order.company_name = shop_data.get('company','')
    sales_order.state_code = shop_data.get('province_code', '')
    sales_order.city_code = shop_data.get('city', '')
    sales_order.postcode = shop_data.get('zip', '')
    # parcel_customer_order.house_no = ''
    sales_order.address_one = shop_data.get('address1')
    sales_order.address_two = shop_data.get('address2')
    sales_order.vat_num = ''

    sales_order.save()

    for item in order['line_items']:
        sales_order_detail = SalesOrderDetail()
        sales_order_detail.order = sales_order
        sales_order_detail.item_id = item.get('product_id')  # 平台商品ID
        sales_order_detail.asin = ''  # Amazon商品ASIN
        sales_order_detail.sku = item.get('sku')  # 商家SKU
        sales_order_detail.sys_sku = item.get('sku')  # 系统SKU
        sales_order_detail.tracking_code = item.get('sku')  # 商品追踪号
        sales_order_detail.qty_ordered = item.get('quantity')  # 订购数量
        # sales_order_detail.qty_shipped = item.get('sku')  # 已发运数量
        sales_order_detail.item_name = item.get('name')  # 商品名称
        sales_order_detail.item_en_name = item.get('name')  # 商品英文名称
        sales_order_detail.item_desc = item.get('title')  # 商品描述
        # sales_order_detail.item_url = item.get('sku')  # 商品链接URL
        # sales_order_detail.item_category_name = item.get('sku')  # 商品分类名称
        # sales_order_detail.item_category_id = item.get('sku')  # 商品分类ID
        # sales_order_detail.item_location = item.get('sku')  # 商品产地
        # sales_order_detail.site_code = item.get('sku')  # 站点编码
        sales_order_detail.item_price = item.get('price')  # 商品单价
        # sales_order_detail.item_price_currency = item.get('sku')  # 商品价格币种
        # sales_order_detail.shipping_price = item.get('sku')  # 商品运费
        # sales_order_detail.shipping_price_currency = item.get('sku')  # 运费币种
        # sales_order_detail.gift_wrap_price = item.get('sku')  # 礼物包装费
        # sales_order_detail.gift_wrap_price_currency = item.get('sku')  # 礼物包装费币种
        # sales_order_detail.discount_price = item.get('sku')  # 优惠金额
        # sales_order_detail.discount_price_currency = item.get('sku')  # 优惠金额币种
        # sales_order_detail.final_value_fee = item.get('sku')  # eBay交易费
        # sales_order_detail.final_value_fee_urrency = item.get('sku')  # eBay交易费币种
        # sales_order_detail.tax_amount = item.get('sku')  # 税费金额
        # sales_order_detail.tax_currency = item.get('sku')  # 税费币种
        # sales_order_detail.item_length = item.get('sku')  # 商品长度(cm)
        # sales_order_detail.item_width = item.get('sku') # 商品宽度(cm)
        # sales_order_detail.item_height = item.get('sku') # 商品高度(cm)
        # sales_order_detail.item_unit = item.get('sku')  # 商品尺寸单位
        sales_order_detail.item_weight = Decimal(item.get('grams', '0')) / 1000  # 商品重量
        sales_order_detail.item_weight_unit = 'KG'  # 商品重量单位
        # sales_order_detail.package_length = item.get('sku')  # 包裹长度(cm)
        # sales_order_detail.package_width = item.get('sku')  # 包裹宽度(cm)
        # sales_order_detail.package_height = item.get('sku')  # 包裹高度(cm)
        # sales_order_detail.package_unit = item.get('sku')  # 包裹尺寸单位
        # sales_order_detail.package_weight = item.get('sku')  # 包裹重量
        # sales_order_detail.package_weight_unit = item.get('sku')  # 包裹重量单位
        # sales_order_detail.item_attr = item.get('sku')  # 商品属性
        sales_order_detail.create_by = user
        sales_order_detail.update_by = user
        sales_order_detail.create_date = datetime.now()
        sales_order_detail.update_date = datetime.now()
        sales_order_detail.save()


def convert_to_utc_datetime(timestamp):
    """
    Convert ISO 8601 timestamp to UTC datetime string in format 'YYYY-MM-DD HH:MM:SS'

    Args:
        timestamp (str): ISO 8601 timestamp string (e.g. "2025-03-26T03:30:53-04:00")

    Returns:
        str: UTC datetime string in format 'YYYY-MM-DD HH:MM:SS' or None if input is None/empty
    """
    if not timestamp:
        return None
    dt = datetime.fromisoformat(timestamp)
    utc_dt = dt.astimezone(pytz.UTC)
    return utc_dt.strftime('%Y-%m-%d %H:%M:%S')


@app.task(bind=True, base=QueueOnce)
def amazon_ship_track_task(self):
    '''
    执行 amazon ship track 任务
    :param modeKey:
    :return:
    '''
    logger.info(f"-------------AmazonShipTrackTask---->")
    if settings.SYSTEM_MARK not in ['xxxx']:
        return

    amazon_ship_track_tasks = AmazonShipTrackTask.objects.filter(status='UnHandled', del_flag=False,
                                                                 handle_times__lt=3).reverse()[:20]

    # todo 重试次数满足指数退避算法（重试次数不超过三次）
    if len(amazon_ship_track_tasks) == 0:
        return

    # todo 配置信息放哪里？
    url = ''
    region = ''
    aws_account_id = ''
    aws_access_key_id = ''
    aws_secret_access_key = ''
    role_name = ''
    api_key = ''
    carrier_scac = ''
    transport_mode = ''
    ship_from_country_code = ''

    for amazon_ship_track_task in amazon_ship_track_tasks:
        if amazon_ship_track_task.status == 'VO':  # todo 预留
            continue
        utc_zone = pytz.UTC
        utc_time = datetime.now(utc_zone)
        # 调用接口
        payload = {
            "fbaShipmentId": amazon_ship_track_task.shipment_id,
            "trackingId": amazon_ship_track_task.tracking_id,
            'carrierScac': carrier_scac,
            'transportMode': transport_mode,
            'shipFromCountryCode': ship_from_country_code,
            "timestamp": utc_time.strftime('%Y-%m-%dT%H:%M:%S.') + f"{utc_time.microsecond // 1000:03d}Z",
        }
        logger.info(f'ama ship track task payload: {payload}')
        try:
            res = send_request(url, region, payload, aws_account_id, aws_access_key_id, aws_secret_access_key,
                               role_name, api_key)
        except requests.exceptions.Timeout:
            logger.info(f"ama ship track task 请求超时")
            amazon_ship_track_task.handle_times += 1
            amazon_ship_track_task.status = 'Failure'
            amazon_ship_track_task.execute_time = datetime.now()
            amazon_ship_track_task.result_desc = '执行超时'
            amazon_ship_track_task.save()
            continue
        except Exception as e:
            logger.info(f"ama ship track task 请求异常: {e}")
            amazon_ship_track_task.handle_times += 1
            amazon_ship_track_task.status = 'Failure'
            amazon_ship_track_task.execute_time = datetime.now()
            amazon_ship_track_task.result_desc = str(e)
            amazon_ship_track_task.save()
            continue
        logger.info(f'ama ship track task result: {res.text}')
        if res.status_code == 200:
            res_json = res.json()
            if res_json['success']:
                amazon_ship_track_task.status = 'Success'
                amazon_ship_track_task.handle_times += 1
                amazon_ship_track_task.execute_time = datetime.now()
                amazon_ship_track_task.result_desc = res.text
                amazon_ship_track_task.save()
            else:
                amazon_ship_track_task.status = 'Failure'
                amazon_ship_track_task.handle_times += 1
                amazon_ship_track_task.execute_time = datetime.now()
                amazon_ship_track_task.result_desc = res.text
                amazon_ship_track_task.save()
        else:
            amazon_ship_track_task.status = 'Failure'
            amazon_ship_track_task.handle_times += 1
            amazon_ship_track_task.execute_time = datetime.now()
            amazon_ship_track_task.result_desc = res.text
            amazon_ship_track_task.save()


@transaction.atomic
@app.task(bind=True, base=QueueOnce)
def wishpost_msg_send_carrier(self):
    logger.info(f'wishpost_msg_send_carrier start--->')

    # 查询是否有推送任务
    push_wish_pickup_info_task_objs = PushWishPickUpInfoTask.objects.filter(status='UnHandled', handle_times__lt=2,
                                                                            del_flag=False).reverse()[:20]
    if len(push_wish_pickup_info_task_objs) == 0:
        return

    for push_pickup_info in push_wish_pickup_info_task_objs:
        customer_orders = ParcelCustomerOrder.objects.filter(order_num=push_pickup_info.order_num,
                                                             order_source='wishpost', del_flag=False
                                                             ).only('customer_order_num', 'country_code', 'weight',
                                                                    "order_time")
        if not customer_orders.exists():
            continue
        customer_order = customer_orders.first()
        order_parcels = ParcelOrderParcel.objects.filter(
            customer_order_id=customer_order.id, del_flag=False
        ).only("customer_order_id", "is_electronic")

        is_electronic = order_parcels.first().is_electronic or False
        # is_electronic_map = {i.customer_order_id: i.is_electronic for i in order_parcels}
        # to_update_objs = []
        # for parcel_order in customer_orders:
        # 业务要求,有订单号或者订单号以 ZHPHD 开头就是已经下单了
        # if bool(parcel_order.tracking_num) or parcel_order.tracking_num.startswith("ZHPHD"):
        #     status = 1
        # else:
        #     status = 1001
        data = {
            "carrier_code": 160,  # 暂写死，wish向我们创建订单的时候就是写死
            "tracking_id": customer_order.customer_order_num,
            "carry_type": 2,
            "pickup_info": {
                "status": 0,  # 揽收成功
                "timestamp": to_utc_iso_format(customer_order.inbound_time),
                "logistics_order_code": customer_order.order_num,
                "operation_center_code": 'CN',  # 固定值，wish放此字段已经不需要，但是不传会报错
                "has_battery": is_electronic,
                "weight": float(customer_order.weighing_weight)
            }
        }

        logger.info(f'wishpost msg_send_carrier data--->{data}')
        try:
            res_data = msg_send_carrier(data)
        except Exception as e:
            logger.info(f'wishpost msg_send_carrier error--->{str(e)}')
            logger.info(traceback.format_exc())
            push_pickup_info.status = 'Failure'
            push_pickup_info.result_desc = '推送失败'
            push_pickup_info.execute_time = datetime.now()
            push_pickup_info.save()
            continue

        logger.info(f'wishpost msg_send_carrier res_data--->{res_data}')
        if res_data.get("code") == 0:
            push_pickup_info.status = 'Success'
            push_pickup_info.result_desc = '推送成功'
            push_pickup_info.execute_time = datetime.now()
            push_pickup_info.save()
        else:
            push_pickup_info.status = 'Failure'
            push_pickup_info.result_desc = '推送失败'
            push_pickup_info.execute_time = datetime.now()
            push_pickup_info.save()

    # ParcelCustomerOrder.objects.bulk_update(to_update_objs, ["is_pushed_wish"])

    logger.info(f'wishpost_msg_send_carrier end--->')


# tangus客户退件单提醒
@app.task(bind=True, base=QueueOnce)
def return_order_statistics_dmas(self, write_excel=False):
    logger.info('退件单截止日期前提醒开始')
    if settings.SYSTEM_ORDER_MARK not in ['ZH']:
        return
    else:
        time.sleep(10)

    time.sleep(5)
    return_order_confirm()

    logger.info('退件单截止日期前提醒结束')


def get_order_data_by_order_num(customer_order: CustomerOrder):
    """
    根据订单号获取订单数据（件重体信息）

    参数:
        order_num: 订单号

    返回:
        dict: 包含订单和包裹信息的字典
    """

    # # 查询订单（排除已作废订单）
    # customer_order = CustomerOrder.objects.filter(
    #     ~Q(order_status='VO'),
    #     order_num=order_num,
    #     del_flag=False
    # ).first()
    #
    # if not customer_order:
    #     logger.error(f'未查询到订单号: {order_num}')

    # 获取订单关联的包裹和包裹项
    parcels = customer_order.parcel.filter(del_flag=False)
    parcel_items = ParcelItem.objects.filter(parcel_num__in=parcels, del_flag=False)

    # 构建订单字典
    order_dict = model_to_dict(customer_order)
    customer_order_dict = CreateCustomerOrder().__dict__
    customer_order_dict = convert_dict(customer_order_dict, order_dict)

    # 构建包裹信息
    regroup_parcels = []
    for parcel_item in parcel_items:
        for parcel in parcels:
            if parcel == parcel_item.parcel_num:
                order_parcel_dict = CreateParcel().__dict__
                parcel_dict = model_to_dict(parcel)
                order_parcel_dict = convert_dict(order_parcel_dict, parcel_dict)

                # 添加包裹号
                order_parcel_dict['parcel_num'] = parcel.parcel_num
                regroup_parcels.append(order_parcel_dict)

    # 添加包裹信息到订单字典
    customer_order_dict['parcelItem'] = regroup_parcels

    return customer_order_dict


@app.task(bind=True, base=QueueOnce)
def sync_order_data_to_yqf(self, order_num):
    """
    件重体数据同步给壹起飞
    :param order_num: 单号(铭志)
    """
    logger.info('进入 sync_order_data_to_yqf')

    # 由mz推送给yqf
    if settings.SYSTEM_ORDER_MARK not in ['MZ']:
        return

    # 入参校验
    if not order_num:
        logger.error('订单号不能为空')

    # 查询订单（排除已作废订单）
    customer_order = CustomerOrder.objects.filter(
        ~Q(order_status='VO'),
        order_num=order_num,
        del_flag=False
    ).first()

    if not customer_order:
        logger.error(f'未查询到订单号: {order_num}')
        return

    if not customer_order.ref_num.startswith('YQF'):
        logger.info(f'订单号: {order_num} 已经推送过了')
        return

    # 获取件重体数据
    res_dic = get_order_data_by_order_num(customer_order)

    json_str = json.dumps(
        res_dic,
        sort_keys=True,
        ensure_ascii=False, separators=(',', ':'),
        cls=DecimalEncoder)

    json_data = json.loads(json_str)
    json_data['ref_num'] = customer_order.ref_num

    header = {
        'Content-Type': 'application/json',
    }

    url = 'http://cshm.yiqifei56.com' + '/api/customerOrders/api_accept_order_data/'
    # url = 'http://*************:8000/' + '/api/customerOrders/api_accept_order_data/'

    result = request_server({'data': json_data, }, url, headers=header)

    if not result:
        logger.error('件重体和状态等信息推送失败')

    if result.get('code') == 200:
        logger.info(f'sync_order_data_to_yqf 推送数据成功！')

    else:
        error_msg = result.get('msg', '') or result.get('detail', '')
        logger.error(f'件重体和状态等信息推送失败: {error_msg}')


@app.task(bind=True, base=QueueOnce)
def sync_pod_file_to_yqf(self, truck_order_id):
    """
    铭志主动推送pod文件给壹起飞(同步pod文件)
    :param truck_order:
    """

    logger.info('进入 sync_pod_file_to_yqf')

    # 由mz推送给yqf
    if settings.SYSTEM_ORDER_MARK not in ['MZ']:
        return

    # 入参校验
    if not truck_order_id:
        logger.info(f'卡派单id不能为空')
        return

    truck_order = TruckOrder.objects.filter(id=truck_order_id, del_flag=False).first()

    if not truck_order:
        logger.info(f'卡派单不能为空')
        return

    if not truck_order.transport_file:
        logger.info(f'卡派单 {truck_order.truck_order_num} 没有pod文件')
        return

    # 通过中间表查询当前卡派单关联的订单
    relate_orders = CustomerOrderRelateTruck.objects.filter(truck_order=truck_order, del_flag=False)

    if not relate_orders.exists():
        logger.info(f'当前卡派单 {truck_order.truck_order_num} 没有与任何订单关联')
        return

    # 通过关联关系查询客户订单
    customer_orders = CustomerOrder.objects.filter(id__in=relate_orders.values_list('customer_order_num_id', flat=True),
                                                   del_flag=False)

    if not customer_orders.exists():
        logger.info(f'未查询到客户订单{relate_orders.values_list("customer_order_num", flat=True)}')
        return

    pod_file = truck_order.transport_file

    pod_file_content = base64.b64encode(pod_file.file.read()).decode('ascii')

    ref_nums = list(customer_orders.values_list('ref_num', flat=True))
    ref_nums = [i for i in ref_nums if i.startswith('YQF')]

    if not ref_nums:
        logger.info(f'当前卡派单 {truck_order.truck_order_num}pod文件 不需要推送壹起飞')
        return

    req_data = {
        'ref_nums': list(customer_orders.values_list('ref_num', flat=True)),
        'pod_file': os.path.basename(pod_file.name),
        'pod_file_base64': pod_file_content,
    }

    json_str = json.dumps(
        req_data,
        sort_keys=True,
        ensure_ascii=False, separators=(',', ':'),
        cls=DecimalEncoder)

    json_data = json.loads(json_str)

    header = {
        'Content-Type': 'application/json',
    }

    url = 'http://cshm.yiqifei56.com' + '/api/customerOrders/api_accept_order_pod_file/'
    # url = 'http://*************:8000/' + '/api/customerOrders/api_accept_order_pod_file/'

    result = request_server({'data': json_data, }, url, headers=header, print_log=False)

    if not result:
        logger.error('pod文件信息推送失败')

    if result.get('code') == 200:
        logger.info(f'sync_pod_file_to_yqf 推送数据成功！')

    else:
        error_msg = result.get('msg', '') or result.get('detail', '')
        logger.error(f'pod文件信息推送失败: {error_msg}')


@app.task(bind=True, base=QueueOnce)
def sync_modify_remark_to_mz(self, order_num, remark):
    """
    壹起飞主动推送备注修改给铭志
    :param order_num: 壹起飞订单号
    :param remark: 修改后的备注
    """

    logger.info('进入 sync_modify_remark_to_mz')

    # 由yqf推送给mz
    if settings.SYSTEM_ORDER_MARK not in ['YQF']:
        return

    # 入参校验
    if not order_num:
        logger.info(f'传入的壹起飞单号不能为空')
        return

    # 只同步备注修改给已推送供应商的订单
    order_sync_tasks = OrderSyncTask.objects.filter(order_num__order_num=order_num,
                                                    task_type='PUSH_ORDER', status='Success',
                                                    del_flag=False)
    # 还未同步给铭志得订单不推送备注修改
    if not order_sync_tasks.exists():
        logger.info(f'当前订单还未同步给铭志')
        return

    req_data = {
        'ref_num': order_num,
        'remark': remark,
    }

    json_str = json.dumps(
        req_data,
        sort_keys=True,
        ensure_ascii=False, separators=(',', ':'),
        cls=DecimalEncoder)

    json_data = json.loads(json_str)

    header = {
        'Content-Type': 'application/json',
    }

    url = 'http://cshm.mz56.com' + '/api/customerOrders/api_accept_order_remark/'
    # url = 'http://172.16.27.3:8000/' + '/api/customerOrders/api_accept_order_remark/'

    result = request_server({'data': json_data, }, url, headers=header)

    if not result:
        logger.error('备注信息推送失败')

    if result.get('code') == 200:
        logger.info(f'sync_modify_remark_to_mz 推送数据成功！')

    else:
        error_msg = result.get('msg', '') or result.get('detail', '')
        logger.error(f'备注信息推送失败: {error_msg}')


# tangus客户账户余额提醒提醒
@app.task(bind=True, base=QueueOnce)
def return_account_balance_alert_dmas(self, write_excel=False):
    logger.info('账户余额提醒提醒开始')
    if settings.SYSTEM_ORDER_MARK not in ['ZH']:
        return
    else:
        time.sleep(10)

    time.sleep(5)
    account_balance_alert()

    logger.info('账户余额提醒提醒结束')


# 韩进轨迹插入
@app.task(bind=True, base=QueueOnce)
def trace_insert(self):
    logger.info('韩进轨迹插入任务开始')

    # 查询符合条件的轨迹代码，构建system_code到code的映射字典
    track_codes = TrackCode.objects.filter(affiliated_track='T', del_flag=False).values('system_code', 'code', 'name')
    system_code_to_code_dict = {item['system_code']: item['code'] for item in track_codes}
    name_dict = {item['code']: item['name'] for item in track_codes}

    logger.info(f'获取到轨迹代码映射关系: {len(system_code_to_code_dict)} 条记录')

    # 构建code到system_code的反向映射字典
    code_to_system_code_dict = {v: k for k, v in system_code_to_code_dict.items()}

    # 重新查询NodeInsertRule表，获取time_interval和insert_method字段
    node_insert_rules = NodeInsertRule.objects.filter(del_flag=False).values(
        'route_code', 'basis_node', 'time_interval', 'insert_method', 'insert_node', 'location',
        'supplier_track_code', 'track_description', 'track_en_description', 'supplier_track_sub_code',
        'track_sub_description', 'track_sub_en_description'
    )

    # 遍历NodeInsertRule表
    for rule in node_insert_rules:
        route_code = rule['route_code']
        basis_node = rule['basis_node']
        time_interval = rule['time_interval']
        insert_method = rule['insert_method']
        insert_node_o = rule['insert_node']
        location = rule['location']
        supplier_track_code = rule['supplier_track_code']
        track_description = rule['track_description']
        track_en_description = rule['track_en_description']
        supplier_track_sub_code = rule['supplier_track_sub_code']
        track_sub_description = rule['track_sub_description']
        track_sub_en_description = rule['track_sub_en_description']

        # 通过basis_node(code)获取对应的system_code
        if basis_node not in code_to_system_code_dict:
            logger.info(f'basis_node {basis_node} 在轨迹代码映射中未找到对应的system_code')
            continue

        system_code = code_to_system_code_dict[basis_node]
        insert_node_n = code_to_system_code_dict[insert_node_o]

        # 查询符合条件的ParcelCustomerOrder记录
        matching_orders = ParcelCustomerOrder.objects.filter(
            product_line__code=route_code,
            order_status__contains=system_code,
            del_flag=False
        )

        logger.info(
            f'route_code: {route_code}, insert_node: {insert_node_n}, basis_node: {basis_node}, system_code: {system_code}, 匹配订单数: {matching_orders.count()}')

        # 处理匹配的订单
        for order in matching_orders:
            order_num = order.order_num

            # 查询ParcelTrack表获取基准时间记录
            base_track = ParcelTrack.objects.filter(
                order_num=order_num,
                system_code=system_code,
                del_flag=False
            ).first()

            if not base_track or not base_track.actual_time:
                logger.info(f'订单 {order_num} 未找到基准轨迹记录或actual_time为空')
                continue

            # 解析时间间隔 100101 格式
            if time_interval:
                try:
                    hours = int(time_interval[0:2])
                    minutes = int(time_interval[2:4])
                    seconds = int(time_interval[4:6])

                    # 创建timedelta对象
                    time_delta = timedelta(hours=hours, minutes=minutes, seconds=seconds)

                    # 根据insert_method计算新的时间
                    if insert_method == 'before':
                        new_actual_time = base_track.actual_time - time_delta
                    elif insert_method == 'after':
                        new_actual_time = base_track.actual_time + time_delta
                    else:
                        logger.warning(f'未知的insert_method: {insert_method}')
                        continue

                    # 检查是否已存在相同的轨迹记录，避免重复插入
                    existing_track = ParcelTrack.objects.filter(
                        order_num=order_num,
                        system_code=insert_node_n,
                        track_code=insert_node_o,
                        del_flag=False
                    ).first()
                    
                    if existing_track:
                        logger.info(f'订单 {order_num} 已存在轨迹记录: system_code={insert_node_n}, code={insert_node_o}，跳过插入')
                        continue
                    
                    # 插入新的轨迹记录
                    ParcelTrack.objects.create(
                        order_num=order_num,
                        system_code=insert_node_n,
                        track_code=insert_node_o,
                        actual_time=new_actual_time,
                        track_name=name_dict[insert_node_o],
                        location=location,
                        supplier_track_code=supplier_track_code,
                        track_description=track_description,
                        track_en_description=track_en_description,
                        supplier_track_sub_code=supplier_track_sub_code,
                        track_sub_description=track_sub_description,
                        track_sub_en_description=track_sub_en_description,
                        del_flag=False
                    )
                    logger.info(
                        f'为订单 {order_num} 插入新轨迹记录: system_code={insert_node_n}, code={insert_node_o}, actual_time={new_actual_time}')

                except Exception as e:
                    logger.error(f'出错: {time_interval}, 错误: {str(e)}')
            else:
                logger.info(f'time_interval为空，跳过处理')

    logger.info('韩进轨迹插入任务结束')


# 韩进轨迹自动推送任务
@app.task(bind=True, base=QueueOnce)
def trace_push(self):
    logger.info('韩进轨迹推送任务开始')
    # 查询符合条件的轨迹代码，构建system_code到code的映射字典
    track_codes = TrackCode.objects.filter(affiliated_track='T', del_flag=False).values('system_code', 'code', 'name')
    system_code_to_code_dict = {item['code']: item['system_code'] for item in track_codes}
    name_dict = {item['code']: item['name'] for item in track_codes}

    node_insert_rules = NodeAutomaticPush.objects.filter(del_flag=False)
    # 遍历NodeAutomaticPush表
    for rule in node_insert_rules:
        push_target = rule.push_target
        push_route = rule.push_route   # 线路名称
        push_shipment_node = rule.push_shipment_node   # 推送运单节点
        push_basis_node = rule.push_basis_node   # 推送依据节点
        push_method = rule.push_method   # 推送方式
        push_time_interval = rule.push_time_interval   # 推送时间间隔
        push_track_timezone = rule.push_track_timezone   # 时区
        push_track_location = rule.push_track_location   # 位置
        push_customer = rule.push_customer   # 客户
        # 小包单中的状态编码
        system_code = system_code_to_code_dict[push_basis_node]
        # 如果是推送供应商,此时有线路
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer token12345ABCDE.!",  # 待获取
        }
        url = 'https://callback.hanjinexp.com/order/update'
        if push_target == 'supplier':
            # 查询符合条件的ParcelCustomerOrder记录
            matching_orders = ParcelCustomerOrder.objects.filter(
                product_line__name=push_route,
                order_status__contains=system_code,
                del_flag=False
            )
        elif push_target == 'customer':
            # 查询符合条件的ParcelCustomerOrder记录
            matching_orders = ParcelCustomerOrder.objects.filter(
                customer__name=push_customer,
                order_status__contains=system_code,
                del_flag=False
            )
        for order in matching_orders:

            logger.info(f'system_code: {system_code}, 匹配订单数: {matching_orders.count()}')

            # 查询ParcelTrack表获取基准时间记录
            base_track = ParcelTrack.objects.filter(
                order_num=order.order_num,
                system_code=system_code,
                del_flag=False
            ).first()

            if not base_track:
                logger.info(f'订单 {order.order_num} 未找到基准轨迹记录')
                continue

            # 解析时间间隔 100101 格式
            if push_time_interval:
                try:
                    hours = int(push_time_interval[0:2])
                    minutes = int(push_time_interval[2:4])
                    seconds = int(push_time_interval[4:6])

                    # 创建timedelta对象
                    time_delta = timedelta(hours=hours, minutes=minutes, seconds=seconds)

                    # 根据insert_method计算新的时间
                    if push_method == 'before_node':
                        new_actual_time = base_track.actual_time - time_delta
                    elif push_method == 'after_node':
                        new_actual_time = base_track.actual_time + time_delta
                    else:
                        logger.warning(f'未知的push_method: {push_method}')
                        continue

                    data = {
                        'order': order.order_num,
                        'code': push_shipment_node,
                        'name': name_dict[push_shipment_node],
                        'timezone': push_track_timezone,
                        'location': push_track_location,
                        'time': new_actual_time,
                    }

                    response = requests.post(url, data=data, headers=headers)


                except Exception as e:
                    logger.error(f'出错: {push_time_interval}, 错误: {str(e)}')
            else:
                logger.info(f'time_interval为空，跳过处理')

    logger.info('韩进轨迹推送任务结束')
