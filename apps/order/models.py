from datetime import datetime

from django.conf import settings
from django.db import models, transaction
from django.urls import reverse
from django.utils import timezone
from django_celery_beat.models import PeriodicTask

from common.models import BaseEntity, BaseContactAddress
from company.models import Address, Company, Recorder, OceanPort
from info.models import Charge
from pms.models import Product, Service, SupplierService, ProductRevenueVersion, ProductCostVersion, InsuranceProduct, \
    InsuranceProudctVersion, ProductLine
from rbac.models import UserProfile, Organization
from wms.models import StorePosition
from alita.logger import logger

SOURCE_TYPE = [
    ('S', '系统添加'),
    ('A', '手动录入'),
    ('P', '推送添加'),
    ('L', '拉取添加'),
]


# 海运提单
class OceanOrder(BaseEntity):
    ORDER_STATUS = [
        ('DR', '草稿'),
        ('SM', '已提交'),
        ('WO', '等待作业'),
        # ('ML', '跨国运输'),  # 海运提单没有跨国运输
        ('FC', '完成'),
        ('VO', '作废'),
        # 2023-11-21 新增
        ('DEC', '已报关'),
        ('DEP', '已离港'),
        ('ARR', '已到港'),
        ('CC', '已清关'),
        ('VER', '已查验'),
        ('REL', '已放行'),
        ('AOW', '已到海外仓'),
        ('BDW', '已预约送仓'),
        ('SIG', '已签收'),
        # 2024-01-19 新增
        ('LOA', '配载中'),
        ('LOC', '配载完成'),
        ('OUS', '出库中'),
        ('DCO', '出库完成'),

        ('PL', '已提柜'),
        ('RTC', '已还柜'),
    ]
    order_num = models.CharField(max_length=30, verbose_name='提单号', null=True, unique=True)
    container_no = models.CharField(max_length=50, verbose_name='柜号', null=True, blank=True)
    container_type = models.CharField(max_length=32, verbose_name='柜型', null=True, blank=True)
    mbl_no = models.CharField(max_length=50, verbose_name='CARRIER MB/L NO', null=True, blank=True)
    clearance_num = models.CharField(max_length=30, verbose_name='关联进口报关单', null=True, blank=True)
    clearanceOut_num = models.CharField(max_length=30, verbose_name='关联出口报关单', null=True, blank=True)
    order_status = models.CharField(max_length=30, choices=ORDER_STATUS, null=True, default='SM',
                                    verbose_name='订单状态')
    carton = models.IntegerField(verbose_name='提单件数', null=True, blank=True)
    weight = models.FloatField(verbose_name='提单重量', null=True, blank=True)
    volume = models.FloatField(verbose_name='提单体积', null=True, blank=True)
    customer_carton = models.IntegerField(verbose_name='客户单件数', null=True, blank=True)
    customer_weight = models.FloatField(verbose_name='客户单重量', null=True, blank=True)
    customer_volume = models.FloatField(verbose_name='客户单体积', null=True, blank=True)
    booking_carton = models.IntegerField(verbose_name='订舱件数', null=True, blank=True)
    booking_weight = models.FloatField(verbose_name='订舱重量', null=True, blank=True)
    booking_volume = models.FloatField(verbose_name='订舱体积', null=True, blank=True)
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='总支出',
                                       default=0)
    charge_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='计费重量', null=True, blank=True)
    departure = models.CharField(max_length=50, verbose_name='启运港', null=True, blank=True)
    destination = models.CharField(max_length=50, verbose_name='目的港', null=True, blank=True)
    discharge = models.CharField(max_length=50, verbose_name='卸货港', null=True, blank=True)
    vessel = models.CharField(max_length=50, verbose_name='船名', null=True, blank=True)
    voyage_num = models.CharField(max_length=20, verbose_name='航次', null=True, blank=True)
    airline_short_name = models.CharField(max_length=20, verbose_name='航线简称', null=True, blank=True)
    carrier_code = models.CharField(max_length=30, verbose_name='CARRIER SCAC CODE', null=True, blank=True)
    ams_num = models.CharField(max_length=100, verbose_name='AMS NO.', null=True, blank=True)
    ams_code = models.CharField(max_length=30, verbose_name='AMS SCAC CODE', null=True, blank=True)
    estimated_time_departure = models.DateField(verbose_name='预计离港日期', null=True, blank=True)
    estimated_time_arrival = models.DateField(verbose_name='预计到港日期', null=True, blank=True)
    actual_leave_date = models.DateField(verbose_name='实际离港日期', null=True, blank=True)
    actual_arrivals_date = models.DateField(verbose_name='实际到港日期', null=True, blank=True)
    supplier = models.ForeignKey(Company, verbose_name='提单所属', null=True, on_delete=models.DO_NOTHING)
    charge_trans = models.FloatField(verbose_name='计费转换乘率', null=True, default=6000)
    main_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='提单')
    cabin_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='舱单')
    is_first = models.BooleanField(verbose_name='是否优先', default=False)
    shipper = models.ForeignKey(Address, verbose_name='发件人', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='OceanOrderShipper')
    receiver = models.ForeignKey(Address, verbose_name='收件人', null=True, blank=True, on_delete=models.SET_NULL,
                                 related_name='OceanOrderReceiver')
    # 跟踪海运提单号 ==> 这里使用自关联会增加表的复杂程度，后期不易拆解
    bind_ocean = models.CharField(max_length=30, verbose_name='跟踪海运提单', null=True, blank=True)
    pre_loading_time = models.DateTimeField(verbose_name='预计装柜时间', null=True, blank=True)
    # PDA出仓最后一箱的装柜时间
    actual_loading_time = models.DateTimeField(verbose_name='实际装柜时间', null=True, blank=True)
    loading_period = models.CharField(max_length=32, verbose_name='装柜周期', null=True, blank=True)
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)
    service = models.ForeignKey(SupplierService, verbose_name='供应商服务', on_delete=models.DO_NOTHING, null=True,
                                blank=True)
    booking_time = models.DateTimeField(verbose_name='订舱时间', null=True, blank=True)
    is_share_cost = models.BooleanField(verbose_name='成本分摊', null=True, blank=True, default=False)
    is_share_revenue = models.BooleanField(verbose_name='收入分摊', null=True, blank=True, default=False)
    logistics_planning = models.ForeignKey('LogisticsPlanning', related_name='ocean_order_logistics_planning',
                                           verbose_name='物流计划', null=True, blank=True, on_delete=models.DO_NOTHING)
    is_bco = models.BooleanField(verbose_name='是否BCO', default=False, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['order_num', 'del_flag']),
        ]
        verbose_name_plural = '提单'
        verbose_name = '提单'

    def __str__(self):
        return f'{self.order_num}'


class OceanOrderChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    customer_order_num = models.ForeignKey(OceanOrder, related_name='oceanOrderChargeIns', on_delete=models.DO_NOTHING,
                                           null=True, verbose_name='海运单')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    class Meta:
        verbose_name = '海运单收入'
        verbose_name_plural = '海运单收入'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class OceanOrderChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(OceanOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='ocean_order_charge_out')
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    class Meta:
        verbose_name = '海运单费用'
        verbose_name_plural = '海运单费用'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


# 海运提单轨迹
class OceanOrderTrack(BaseEntity):
    # TRACK_CODE = [
    #     ('PU', '卡车揽收'),
    #     ('AW', '集货仓收货'),
    #     ('AA', '运输路线确认'),
    #     ('EI', '出境清关查验'),
    #     ('EC', '出境清关完成'),
    #     ('TS', '国际运输出港'),
    #     ('CI', '入境清关查验'),
    #     ('CF', '入境清关完成'),
    #     ('TR', '卡车转运'),
    #     ('HS', '派送中'),
    #     ('DC', '派送完成'),
    #     ('CL', '订单取消'),
    # ]
    track_code = models.CharField(max_length=20, verbose_name='轨迹代码', blank=True, null=True)
    track_name = models.CharField(max_length=20, verbose_name='轨迹名称', blank=True, null=True)
    location = models.CharField(max_length=200, verbose_name='位置', blank=True, null=True)
    operation_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)
    description = models.CharField(max_length=100, verbose_name='描述', blank=True, null=True)
    ocean_order_num = models.ForeignKey(OceanOrder, related_name='oceanOrderTrack', on_delete=models.DO_NOTHING,
                                        null=True, verbose_name='提单号')


# 海运提单标准时效基础数据(提单标准时效表)
class OceanOrderStandardAging(BaseEntity):
    version_name = models.CharField(max_length=64, verbose_name='版本名称')
    departure = models.CharField(max_length=50, verbose_name='起运港', null=True, blank=True)
    destination = models.CharField(max_length=50, verbose_name='目的港', null=True, blank=True)
    cut_off_time = models.DateTimeField(verbose_name='截补料时间', null=True, blank=True)
    cut_off_suggest_loading = models.IntegerField(verbose_name='截补料-装柜时效', null=True, blank=True)
    suggest_loading_customs_clearance = models.IntegerField(verbose_name='装柜-海关放行时效', null=True, blank=True)
    customs_clearance_terminal_release = models.IntegerField(verbose_name='海关放行-码头放行时效', null=True,
                                                             blank=True)
    terminal_release_estimated_departure = models.IntegerField(verbose_name='码头放行-预计离港时效', null=True,
                                                               blank=True)
    estimated_departure_estimated_arrival = models.IntegerField(verbose_name='预计离港-预计到港时效', null=True,
                                                                blank=True)
    estimated_arrival_estimated_arrival_wh = models.IntegerField(verbose_name='预计到港-预计到仓时效', null=True,
                                                                 blank=True)
    estimated_arrival_wh_earliest_available = models.IntegerField(verbose_name='预计到仓-最早可约时效', null=True,
                                                                  blank=True)

    # suggest_loading_time = models.DateTimeField(verbose_name='建议装柜时间', null=True, blank=True)
    # customs_clearance_time = models.DateTimeField(verbose_name='海关放行时间', null=True, blank=True)
    # terminal_release_time = models.DateTimeField(verbose_name='码头放行时间', null=True, blank=True)
    # estimated_time_departure = models.DateField(verbose_name='预计离港日期', null=True, blank=True)
    # estimated_time_arrival = models.DateField(verbose_name='预计到港日期', null=True, blank=True)
    # estimated_arrival_wh_time = models.DateTimeField(verbose_name='预计到仓时间', null=True, blank=True)
    # earliest_available_time = models.DateTimeField(verbose_name='最早可约时间', null=True, blank=True)
    # interval_days = models.IntegerField(verbose_name='间隔天数', null=True, blank=True)

    class Meta:
        verbose_name_plural = '提单标准时效表'
        verbose_name = '提单标准时效表'

    def __str__(self):
        return f'{self.departure}-{self.destination}'


# 海运单图片
class OceanOrderImage(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='图片名称', )
    img_url = models.FileField(upload_to='oceanOrder/%Y/%m/%d', null=True, blank=True, verbose_name='海运提单图片')
    ocean_num = models.ForeignKey(OceanOrder, related_name='images', verbose_name='海运提单',
                                  on_delete=models.DO_NOTHING, null=True, blank=True)


# 空运主单
class MasterOrder(BaseEntity):
    ORDER_STATUS = [
        ('DR', '草稿'), #已创建
        ('SM', '已提交'),
        ('AP', '已打板'),
        ('WO', '等待作业'),  # 已装机
        ('ML', '跨国运输'),
        ('FC', '完成'),
        ('VO', '作废'),
        ('DEC', '已报关'),
        ('DEP', '已离港'),  # 已起飞
        ('ARR', '已到港'),  # 已落地
        ('CC', '已清关'),
        ('VER', '已查验'),
        ('REL', '已放行'),
        ('AOW', '已到海外仓'),
        ('BDW', '已预约送仓'),
        ('SIG', '已签收'),
        ('LOA', '配载中'), #配舱中 在已放舱之后，有大包关联关系以后
        ('LOC', '配载完成'), #已配舱
        ('OUS', '出库中'),
        ('DCO', '出库完成'),
        ('PL', '已提柜'),  # 已提货、已提取
        ('RTC', '已还柜'),
        ('AM', '已交邮'),
        ('HA', '已交航'),
        ('SCF', '配舱确认'),
        ('RCS', '已放舱'),
    ]
    TRANSPORT_ATTRIBUTE = [
        ('1', '普货'),
        ('2', '带电'),
        ('3', '敏感'),
    ]
    TRANSPORT_TYPE = [
        ('1', '整板'),
        ('2', '散舱'),
        ('3', '包机'),
    ]
    CARRIAGE_TYPE = [
        ('Airline', '航司'),
    ]
    SEAL_STATUS = [
        ('SEALED', '封单'),
        ('UNSEALED', '解封'),
    ]
    order_num = models.CharField(max_length=30, verbose_name='主单号', null=True, unique=True, db_index=True)
    clearance_num = models.CharField(max_length=30, verbose_name='关联进口报关单', null=True, blank=True)
    clearanceOut_num = models.CharField(max_length=30, verbose_name='关联出口报关单', null=True, blank=True)
    order_status = models.CharField(max_length=30, choices=ORDER_STATUS, null=True, default='SM',
                                    verbose_name='订单状态')
    carton = models.IntegerField(verbose_name='主单件数', null=True, blank=True)
    weight = models.FloatField(verbose_name='主单重量', null=True, blank=True)
    volume = models.FloatField(verbose_name='主单体积', null=True, blank=True)
    customer_carton = models.IntegerField(verbose_name='客户单件数', null=True, blank=True)
    customer_weight = models.FloatField(verbose_name='客户单重量', null=True, blank=True)
    customer_volume = models.FloatField(verbose_name='客户单体积', null=True, blank=True)
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='总支出',
                                       default=0)
    charge_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='计费重量', null=True, blank=True)
    destination = models.CharField(max_length=50, verbose_name='目的港', null=True, blank=True) #落地机场
    departure = models.CharField(max_length=50, verbose_name='启运港', null=True, blank=True) #起飞机场
    transit_airport = models.CharField(max_length=300, verbose_name='中转港', null=True, blank=True)
    airline_num = models.CharField(max_length=300, verbose_name='航班号', null=True, blank=True)
    plan_leave_date = models.DateField(verbose_name='预计离港日期', null=True, blank=True)
    actual_leave_date = models.DateField(verbose_name='实际离港日期', null=True, blank=True)
    plan_arrivals_date = models.DateField(verbose_name='预计到港日期', null=True, blank=True)
    actual_arrivals_date = models.DateField(verbose_name='实际到港日期', null=True, blank=True)
    supplier = models.ForeignKey(Company, verbose_name='主单所属', null=True, on_delete=models.DO_NOTHING)
    bubble = models.CharField(max_length=20, verbose_name='客户分泡(%)', null=True, blank=True, default=0)
    bubble_weight = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True,
                                        verbose_name='分泡后计费重')
    charge_trans = models.FloatField(verbose_name='计费转换乘率', null=True, default=6000)
    main_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='主单')
    cabin_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='舱库')
    is_push = models.BooleanField(verbose_name='是否推送', default=False)
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)
    service = models.ForeignKey(SupplierService, verbose_name='供应商服务', on_delete=models.DO_NOTHING,
                                null=True, blank=True)
    booking_time = models.DateTimeField(verbose_name='订舱时间', null=True, blank=True)
    logistics_planning = models.ForeignKey('LogisticsPlanning', related_name='master_order_logistics_planning',
                                           verbose_name='物流计划', null=True, blank=True, on_delete=models.DO_NOTHING)
    transport_attribute = models.CharField(max_length=10, verbose_name='运输属性', choices=TRANSPORT_ATTRIBUTE, default='1')
    transport_type = models.CharField(max_length=10, verbose_name='运输类型', choices=TRANSPORT_TYPE, default='2')
    carriage_type = models.CharField(verbose_name='承运类型', max_length=30, choices=CARRIAGE_TYPE, default='Airline')
    ref_num = models.CharField(max_length=50, verbose_name='运输单号', null=True, blank=True)
    customer = models.ForeignKey(Company, related_name='master_order_customer', verbose_name='客户', on_delete=models.DO_NOTHING, null=True, blank=True)
    product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True, blank=True)
    is_customs_declaration = models.BooleanField(verbose_name='是否报关', default=False, null=True, blank=True)
    plan_arrivals_warehouse_date = models.DateTimeField(verbose_name='计划到仓时间', null=True, blank=True)
    carrier_code = models.CharField(max_length=30, verbose_name='航空公司编码', null=True, blank=True)
    warehouse_receipt_number = models.CharField(max_length=50, verbose_name='入仓单号', null=True, blank=True)
    remark = models.TextField(verbose_name='运输单备注', null=True, blank=True)
    seal_status = models.CharField(max_length=50, verbose_name='封单状态', choices=SEAL_STATUS, default='UNSEALED')
    ETD = models.DateTimeField(verbose_name='预计出发时间', null=True, blank=True)
    ETA = models.DateTimeField(verbose_name='预计到达时间', null=True, blank=True)
    ATD = models.DateTimeField(verbose_name='实际离港时间', null=True, blank=True)
    ATA = models.DateTimeField(verbose_name='实际到达时间', null=True, blank=True)
    stowage_plan_time = models.DateTimeField(verbose_name='配舱时间', null=True, blank=True)
    cargo_release_time = models.DateTimeField(verbose_name='放舱时间', null=True, blank=True)

    class Meta:
        verbose_name_plural = '主单'
        verbose_name = '主单'

    def __str__(self):
        return self.order_num

    def get_absolute_url(self):
        return reverse('master_order')
        # return reverse('mastser_order_detail', kwargs={'pk': self.pk})


class MasterOrderAddress(BaseContactAddress):
    """空运单地址快照表"""
    ADDRESS_TYPE = [
        ('SP', '发件人'),
        ('RC', '收件人'),
        ('TD', '交接地址'),
        ('ZW', '退件地址'),
        ('CA', '揽收地址'),
        ('NP', 'Notify Party')
    ]

    customer_order = models.ForeignKey(MasterOrder, related_name='MasterOrderAddress',
                                       on_delete=models.DO_NOTHING,
                                       verbose_name='空运单号', null=True, blank=True)
    address_type = models.CharField(max_length=10, choices=ADDRESS_TYPE, verbose_name='地址类型', default='RC')

    class Meta:
        verbose_name = '空运单地址信息'
        verbose_name_plural = '空运单地址信息'

    def __str__(self):
        return '%s: %s %s ' % (self.address_num, self.postcode, self.country_code,)


class MasterOrderChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    UNIT = [
        ('weight', '重量'),
        ('volume', '体积'),
        ('chargeWeight', '计费重'),
        ('confirmChargeWeight', '确认计费重'),
        ('carton', '件数'),
        ('SKUCarton', 'SKU预报件数'),
        ('SKUShelfCarton', 'SKU上架件数'),
        ('SKUCategories', 'SKU 种类数量'),
        ('newSkuQuantity', 'SKU 新品数量'),
        ('oneSKUWeight', '单件sku重量'),
        ('oneParcelSKUCarton', '单件包裹sku件数'),
        ('mix', '混合'),
        ('combine', '组合收费'),
        ('boxWeight', '箱数重量'),
        ('confirmChargeVolume', '确认计费体积'),
        ('halfDisposal', '半抛'),
        ('numberOfStrokes', '打板数'),
        ('numberOfTrucks', '用车数'),
        ('bubble', '泡比'),
    ]
    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    customer_order_num = models.ForeignKey(MasterOrder, related_name='masterOrderChargeIns',
                                           on_delete=models.DO_NOTHING, null=True, verbose_name='空运单')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')
    charge_unit = models.CharField(max_length=64, choices=UNIT, verbose_name='计算条件', default='weight',
                                   null=True, blank=True)
    remark = models.TextField(verbose_name='备注', null=True, blank=True)

    class Meta:
        verbose_name = '空运单收入'
        verbose_name_plural = '空运单收入'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class MasterOrderChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    UNIT = [
        ('weight', '重量'),
        ('volume', '体积'),
        ('chargeWeight', '计费重'),
        ('confirmChargeWeight', '确认计费重'),
        ('carton', '件数'),
        ('SKUCarton', 'SKU预报件数'),
        ('SKUShelfCarton', 'SKU上架件数'),
        ('SKUCategories', 'SKU 种类数量'),
        ('oneSKUWeight', '单件sku重量'),
        ('newSkuQuantity', 'SKU 新品数量'),
        ('oneParcelSKUCarton', '单件包裹sku件数'),
        ('mix', '混合'),
        ('combine', '组合收费'),
        ('boxWeight', '箱数重量'),
        ('confirmChargeVolume', '确认计费体积'),
        ('halfDisposal', '半抛'),
        ('numberOfStrokes', '打板数'),
        ('numberOfTrucks', '用车数'),
        ('bubble', '泡比'),
    ]
    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(MasterOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='masterOrderChargeOuts')
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')
    charge_unit = models.CharField(max_length=64, choices=UNIT, verbose_name='计算条件', default='weight',
                                   null=True, blank=True)
    remark = models.TextField(verbose_name='备注', null=True, blank=True)

    class Meta:
        verbose_name = '空运单费用'
        verbose_name_plural = '空运单费用'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


# 航班追踪
class MasterOrderFlightTrack(BaseEntity):
    TIME_TYPE = [
        ('A', '一程航班'),
        ('B', '二程航班'),
        ('C', '三程航班'),
        ('D', '四程航班'),
        ('E', '五程航班'),
        ('C', '六程航班'),
    ]
    time_type = models.CharField(max_length=50, verbose_name='时间类型', choices=TIME_TYPE, default='A')
    route = models.CharField(max_length=50, verbose_name='路径', null=True, blank=True)
    airline_num = models.CharField(max_length=20, verbose_name='航班号', null=True, blank=True)
    plan_departure_time = models.CharField(max_length=50, verbose_name='计划起飞时间', null=True, blank=True)
    atd = models.CharField(max_length=50, verbose_name='实际起飞时间', null=True, blank=True)
    plan_arrival_time = models.CharField(max_length=50, verbose_name='计划到达时间', null=True, blank=True)
    actual_arrival_time = models.CharField(max_length=50, verbose_name='实际到达时间', null=True, blank=True)

    masterOrder_num = models.ForeignKey(MasterOrder, on_delete=models.DO_NOTHING, null=True,
                                        related_name='masterOrderFlightTracks')

    class Meta:
        verbose_name = '主单货物追踪'
        verbose_name_plural = '主单货物追踪'

    def __str__(self):
        return self.route


# 推送ems 大包数据
class MasterOrderToEms(BaseEntity):
    mail_no = models.CharField(max_length=30, verbose_name='邮政单号', null=True, blank=True)
    weight = models.CharField(max_length=30, verbose_name='重量', null=True, blank=True)
    dest_country = models.CharField(max_length=50, verbose_name='目的国家', null=True, blank=True)
    envelop_time = models.DateTimeField(verbose_name='离港时间', null=True, blank=True)
    bag_id = models.CharField(max_length=50, verbose_name='大包单号', null=True, blank=True)
    master_order = models.CharField(max_length=50, verbose_name='提单号', null=True, blank=True)
    airline_num = models.CharField(max_length=50, verbose_name='航班号', null=True, blank=True)
    departure = models.CharField(max_length=50, verbose_name='起运港', null=True, blank=True)
    destination = models.CharField(max_length=50, verbose_name='目的港', null=True, blank=True)
    org_code = models.CharField(max_length=50, verbose_name='组织编码', null=True, blank=True)
    org_name = models.CharField(max_length=50, verbose_name='组织', null=True, blank=True)
    source_type = models.CharField(max_length=50, verbose_name='来源', null=True, blank=True)
    is_push_finish = models.BooleanField(verbose_name='是否推送完成', default=False)
    request_date = models.DateTimeField(verbose_name='推送时间', null=True, blank=True)

    class Meta:
        verbose_name = 'ems大包数据'
        verbose_name_plural = 'ems大包数据'

    def __str__(self):
        return self.mail_no


class MasterOrderPushTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('Success', '处理成功'),
        ('NOHandled', '不处理'),
        ('VO', '已作废'),
    ]

    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='任务状态')
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True, default=1)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True, default=0)
    master_order_num = models.ForeignKey(MasterOrder, on_delete=models.DO_NOTHING, null=True,
                                         related_name='masterOrderPushTasks')
    master_order_no = models.CharField(max_length=100, verbose_name='提单号', null=True, blank=True)

    class Meta:
        verbose_name = '主单推送任务'
        verbose_name_plural = '主单推送任务'

    def __str__(self):
        return self.master_order_num


class HouseOrder(BaseEntity):
    ORDER_STATUS = [
        ('DR', '草稿'),
        ('SM', '已提交'),
        ('WO', '等待作业'),
        ('FC', '完成'),
        ('VO', '作废'),
    ]
    order_num = models.CharField(max_length=30, verbose_name='分单号', null=True, db_index=True)
    clearance_num = models.CharField(max_length=30, verbose_name='关联进口报关单', null=True, blank=True)
    clearanceOut_num = models.CharField(max_length=30, verbose_name='关联出口报关单', null=True, blank=True)
    order_status = models.CharField(max_length=30, choices=ORDER_STATUS, null=True, default='SM',
                                    verbose_name='订单状态')
    carton = models.IntegerField(verbose_name='件数', null=True, blank=True)
    weight = models.FloatField(verbose_name='重量', null=True, blank=True)
    volume = models.FloatField(verbose_name='体积', null=True, blank=True)
    destination = models.CharField(max_length=50, verbose_name='目的港', null=True, blank=True)
    departure = models.CharField(max_length=50, verbose_name='启运港', null=True, blank=True)
    goods_name = models.CharField(max_length=50, verbose_name='英文品名', null=True, blank=True)
    charge_trans = models.FloatField(verbose_name='计费转换', null=True, default=6000)
    charge_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='计费重量', null=True, blank=True)
    airline_num = models.CharField(max_length=20, verbose_name='航班号', null=True, blank=True)
    actual_leave_date = models.DateField(verbose_name='实际离港日期', null=True, blank=True)
    actual_arrivals_date = models.DateField(verbose_name='实际到港日期', null=True, blank=True)

    class Meta:
        verbose_name_plural = '分单'
        verbose_name = '分单'

    def __str__(self):
        return self.order_num


# 进口报关单
class Clearance(BaseEntity):
    CLEAR_STATUS = [
        ('DR', '草稿'),
        ('WO', '已提交'),
        ('HI', '处理中'),
        ('VO', '作废'),
        ('FC', '订单完成'),
    ]
    TRANS_STATUS = [
        ('TI', '运输中'),
        ('AD', '已到目的港'),
        ('WC', '入库完成'),
        ('HO', '移交派送'),
        ('DF', 'ASCAN'),
    ]
    CLEAR_TYPE = [
        ('MS', '空运主单'),
        ('OC', '海运提单'),
    ]
    AGING_STATUS = [
        ('0', '统计中'),
        ('1', '统计完毕'),
    ]
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    clearance_num = models.CharField(max_length=20, verbose_name='报关单号', null=True, blank=True)
    clear_status = models.CharField(max_length=10, choices=CLEAR_STATUS, verbose_name='状态', default='DR')
    trans_status = models.CharField(max_length=10, choices=TRANS_STATUS, verbose_name='运输状态', null=True, blank=True,
                                    default='TI')
    clear_type = models.CharField(max_length=10, choices=CLEAR_TYPE, verbose_name='进口报关单类型', default='MS')
    delivery_address = models.CharField(max_length=300, verbose_name='送货地址', null=True, blank=True)
    delivery_theme = models.CharField(max_length=100, verbose_name='送货主题', null=True, blank=True)
    carton = models.IntegerField(verbose_name='件数', default=0)
    internal_carton = models.IntegerField(verbose_name='内件数', default=0)
    weight = models.DecimalField(verbose_name='重量', max_digits=16, decimal_places=6, default=0)
    volume = models.DecimalField(verbose_name='体积', max_digits=16, decimal_places=6, default=0)
    export_recorder = models.ForeignKey(Recorder, related_name='export_recorder', verbose_name='出口商',
                                        on_delete=models.CASCADE, null=True)
    import_recorder = models.ForeignKey(Recorder, related_name='import_recorder', verbose_name='进口商',
                                        on_delete=models.CASCADE, null=True)
    master_order_id = models.ForeignKey(MasterOrder, verbose_name='主单号', null=True, blank=True,
                                        on_delete=models.CASCADE)
    master_order_num = models.CharField(max_length=30, verbose_name='空运主单号', null=True, blank=True, db_index=True)
    house_order_id = models.ForeignKey(HouseOrder, verbose_name='分单号', null=True, blank=True,
                                       on_delete=models.CASCADE)
    house_order_num = models.CharField(max_length=30, verbose_name='分单号码', null=True, blank=True, db_index=True)
    ocean_order_id = models.ForeignKey(OceanOrder, verbose_name='提单号', null=True, blank=True,
                                       on_delete=models.CASCADE)
    ocean_order_num = models.CharField(max_length=30, verbose_name='海运提单号', null=True, blank=True, db_index=True)

    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商',
                                 related_name='supplier_id')
    total_charge_in = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='总收入', default=0)
    total_charge_out = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='总支出', default=0)
    is_check = models.BooleanField(verbose_name='是否查验', default=False)

    customer = models.ForeignKey(Company, verbose_name='客户', on_delete=models.DO_NOTHING, null=True,
                                 related_name='customer_id')
    product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True, blank=True)
    service = models.ForeignKey(Service, verbose_name='产品服务', on_delete=models.DO_NOTHING, null=True, blank=True)
    master_ocean_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='主单/提单')
    dispatch_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='派送清单')
    main_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='发票和箱单')
    cabin_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='报关清单')
    check_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='ISF')
    bill_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='电放提单')
    tax_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='税单')
    is_send_debit = models.BooleanField(verbose_name='账单发送', default=False)
    is_send_forecast = models.BooleanField(verbose_name='预报发送', default=False)
    is_revenue_lock = models.BooleanField(verbose_name='收入确认', default=False)
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    opera_date = models.DateField(verbose_name='操作时间', null=True, blank=True)
    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)
    is_isf = models.BooleanField(verbose_name='是否ISF匹配', default=False)
    is_cc = models.BooleanField(verbose_name='是否报关完成', default=False)

    income = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='收入')
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    gross_profit = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='毛利')
    gross_currency = models.CharField(max_length=10, verbose_name='毛利币种', null=True, blank=True)
    email_content = models.CharField(max_length=500, verbose_name='邮件正文', null=True, blank=True)
    release_date = models.DateField(verbose_name='通知放行时间', null=True, blank=True)
    delivery_date = models.DateField(verbose_name='交货时间', null=True, blank=True)
    delivery_finish_date = models.DateField(verbose_name='派送完成时间', null=True, blank=True, default=None)
    delivery_warehouse_date = models.DateField(verbose_name='入库完成时间', null=True, blank=True)
    assign_msg = models.CharField(verbose_name='交派信息', max_length=500, null=True, blank=True)

    # 海运提单内容
    vessel = models.CharField(max_length=50, verbose_name='船名', null=True, blank=True)
    carrier_code = models.CharField(max_length=30, verbose_name='CARRIER SCAC CODE', null=True, blank=True)
    ams_num = models.CharField(max_length=100, verbose_name='AMS NO.', null=True, blank=True)
    ams_code = models.CharField(max_length=30, verbose_name='AMS SCAC CODE', null=True, blank=True)
    estimated_time_departure = models.DateField(verbose_name='预计离港时间', null=True, blank=True)
    estimated_time_arrival = models.DateField(verbose_name='预计到达时间', null=True, blank=True)
    voyage_num = models.CharField(max_length=20, verbose_name='航次', null=True, blank=True)
    mbl_no = models.CharField(max_length=50, verbose_name='CARRIER MB/L NO', null=True, blank=True)
    container_no = models.CharField(max_length=50, verbose_name='柜号', null=True, blank=True)

    # 公共部分
    destination = models.CharField(max_length=50, verbose_name='目的港', null=True, blank=True)
    departure = models.CharField(max_length=50, verbose_name='启运港', null=True, blank=True)
    actual_arrivals_date = models.DateField(verbose_name='实际到港日期', null=True, blank=True)

    # 空运主单内容
    airline_num = models.CharField(max_length=20, verbose_name='航班号', null=True, blank=True)
    actual_leave_date = models.DateField(verbose_name='实际离港日期', null=True, blank=True)
    aging = models.IntegerField(verbose_name='时效', null=True, blank=True, default=0)
    aging_status = models.CharField(max_length=10, choices=AGING_STATUS, verbose_name='时效统计状态', default='0')
    book_truck_date = models.DateField(verbose_name='预约卡车时间', null=True, blank=True)
    is_share_cost = models.BooleanField(verbose_name='成本分摊', null=True, blank=True, default=False)
    is_share_revenue = models.BooleanField(verbose_name='收入分摊', null=True, blank=True, default=False)

    class Meta:
        indexes = [
            models.Index(fields=['clearance_num', 'del_flag']),
        ]
        verbose_name_plural = '进口报关单'
        verbose_name = '进口报关单'

    def __str__(self):
        return self.clearance_num


class ClearanceParcel(BaseEntity):
    customer_order = models.CharField(max_length=20, verbose_name='客户订单号', null=True, blank=True)
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True)
    parcel_desc = models.CharField(max_length=100, verbose_name='包裹描述', null=True, blank=True, default='0')
    parcel_length = models.FloatField(verbose_name='长', null=True, blank=True, default=1)
    parcel_width = models.FloatField(verbose_name='宽', null=True, blank=True, default=1)
    parcel_height = models.FloatField(verbose_name='高', null=True, blank=True, default=1)
    parcel_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True,
                                        default=0.0001)
    parcel_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
                                        default=0.000001)
    parcel_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True)
    tracking_num = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True)
    clearance_order = models.ForeignKey(Clearance, related_name='parcel', on_delete=models.DO_NOTHING,
                                        verbose_name='进口报关单号', null=True,
                                        blank=True)

    class Meta:
        verbose_name = '包裹'
        verbose_name_plural = '包裹'

    def __str__(self):
        return self.parcel_num


class ClearanceParcelItem(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    item_code = models.CharField(max_length=256, verbose_name='物品号', null=True, blank=True, default='0')
    item_name = models.CharField(max_length=256, verbose_name='物品名称', null=True, blank=True, default='0')
    sale_currency = models.CharField(max_length=10, verbose_name='报价币种', default='CNY', null=True,
                                     blank=True)
    sale_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='销售价格', null=True, blank=True,
                                     default=1)
    declared_currency = models.CharField(max_length=10, verbose_name='申报币种', default='USD',
                                         null=True,
                                         blank=True)
    declared_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='申报价格', null=True,
                                         blank=True,
                                         default=1)
    declared_nameCN = models.CharField(max_length=256, verbose_name='中文申报品名', null=True, blank=True)
    declared_nameEN = models.CharField(max_length=256, verbose_name='英文申报品名', null=True, blank=True)
    item_length = models.FloatField(verbose_name='长', null=True, blank=True, default=1)
    item_width = models.FloatField(verbose_name='宽', null=True, blank=True, default=1)
    item_height = models.FloatField(verbose_name='高', null=True, blank=True, default=1)
    item_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
                                      default=0.000001)
    item_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True,
                                      default=0.0001)
    item_qty = models.IntegerField(verbose_name='数量', null=True, blank=True, default=1)
    texture = models.CharField(max_length=50, verbose_name='材质', null=True, blank=True)
    item_size = models.CharField(max_length=50, verbose_name='尺寸', null=True, blank=True)
    use = models.CharField(max_length=50, verbose_name='用途', null=True, blank=True)
    brand = models.CharField(max_length=50, verbose_name='品牌', null=True, blank=True)
    model = models.CharField(max_length=50, verbose_name='型号', null=True, blank=True)
    customs_code = models.CharField(max_length=50, verbose_name='海关编码', null=True, blank=True)
    parcel_num = models.ForeignKey(ClearanceParcel, related_name='parcelItem', verbose_name='包裹号',
                                   on_delete=models.DO_NOTHING, null=True, blank=True)

    class Meta:
        verbose_name = '物品'
        verbose_name_plural = '物品'

    def __str__(self):
        return 'ITEM保存成功。'


class ClearanceDetail(BaseEntity):
    line_num = models.CharField(verbose_name='行号', max_length=50, null=True, blank=True)
    clearance_id = models.ForeignKey(Clearance, verbose_name='报关单', on_delete=models.CASCADE)
    quantity = models.IntegerField(verbose_name='PCS', default=0)

    def __str__(self):
        return self.id


# 进口报关单成本
class ClearanceChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(Clearance, related_name='clearanceChargeOuts', on_delete=models.DO_NOTHING,
                                           null=True, verbose_name='报关单')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


# 进口报关单备注修改记录
class ClearanceRemark(BaseEntity):
    recording = models.CharField(max_length=500, verbose_name='备注记录', null=True, blank=True)
    clearance = models.ForeignKey(Clearance, related_name='clearance_remarks', on_delete=models.DO_NOTHING,
                                  verbose_name='报关单')

    def __str__(self):
        return '备注记录'


class ClearanceChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    customer_order_num = models.ForeignKey(Clearance, related_name='clearanceChargeIns', on_delete=models.DO_NOTHING,
                                           null=True, verbose_name='报关单')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


# 出口报关单
class ClearanceOut(BaseEntity):
    CLEAR_STATUS = [
        ('DR', '草稿'),
        ('WO', '待审核'),  # mz使用
        ('WA', '等待报关'),  # mz使用
        ('FC', '报关完成'),  # mz使用
        ('ITP', '报关失败'),  # mz使用
        ('WD', '待报关'),
        ('WC', '待确认'),
        ('AC', '已确认'),
        ('FD', '已报关'),
        ('VO', '作废'),  # mz使用
        ('WFCI', '等待客户资料'),  # mz使用
        ('IFP', '资料已提供'),  # mz使用
        ('PWNC', '预录单未确认'),  # mz使用
        ('PWAC', '预录单已确认'),  # mz使用
    ]
    CLEAR_TYPE = [
        ('AC', '单独报关'),
        ('MC', '合并报关'),
    ]
    TRADE_MODE = [
        ('GTD', '一般贸易'),
        ('9710', '9710'),
        ('1039', '1039'),
        ('9810', '9810'),
    ]
    TRANSACTION_MODE = [
        ('FOB', 'FOB'),
        ('C&F', 'C&F'),
        ('EXW', 'EXW'),
        ('CIF', 'CIF'),
    ]
    clearance_num = models.CharField(max_length=20, verbose_name='报关单号', null=True, blank=True, db_index=True)
    customer_clearance_num = models.CharField(max_length=50, verbose_name='客户报关单号', null=True, blank=True,
                                              db_index=True)
    customer = models.ForeignKey(Company, verbose_name='客户', related_name='clearance_out_customer',
                                 on_delete=models.DO_NOTHING, null=True)
    clear_status = models.CharField(max_length=10, choices=CLEAR_STATUS, verbose_name='状态', default='WO')
    carton = models.IntegerField(verbose_name='件数', default=0)
    weight = models.DecimalField(verbose_name='重量', max_digits=16, decimal_places=6, default=0)
    volume = models.DecimalField(verbose_name='体积', max_digits=16, decimal_places=6, default=0)
    gross_weight = models.DecimalField(verbose_name='毛重', max_digits=16, decimal_places=6, default=0)
    net_weight = models.DecimalField(verbose_name='净重', max_digits=16, decimal_places=6, default=0)
    export_recorder = models.ForeignKey(Recorder, related_name='out_export_recorder', verbose_name='出口商',
                                        on_delete=models.CASCADE, null=True)
    import_recorder = models.ForeignKey(Recorder, related_name='out_import_recorder', verbose_name='进口商',
                                        on_delete=models.CASCADE, null=True)
    master_order_id = models.ForeignKey(MasterOrder, verbose_name='主单号', null=True, blank=True,
                                        related_name='master_order', on_delete=models.CASCADE)
    ocean_order_id = models.ForeignKey(OceanOrder, verbose_name='提单号', null=True, blank=True,
                                       related_name='ocean_order', on_delete=models.CASCADE)
    house_order_id = models.ForeignKey(HouseOrder, verbose_name='分单号', null=True, blank=True,
                                       on_delete=models.CASCADE)
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    total_charge_in = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='总收入', default=0)
    total_charge_out = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='总支出', default=0)
    is_check = models.BooleanField(verbose_name='是否查验商品', default=False)
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)
    is_share_cost = models.BooleanField(verbose_name='成本分摊', null=True, blank=True, default=False)
    is_share_revenue = models.BooleanField(verbose_name='收入分摊', null=True, blank=True, default=False)
    clear_type = models.CharField(max_length=10, choices=CLEAR_TYPE, verbose_name='报关方式', default='AC')
    trade_mode = models.CharField(max_length=24, choices=TRADE_MODE, verbose_name='贸易方式', null=True, blank=True)
    transaction_mode = models.CharField(max_length=24, choices=TRANSACTION_MODE, verbose_name='成交方式',
                                        null=True, blank=True)
    registration_num = models.CharField(max_length=48, verbose_name='拼单号', null=True, blank=True)
    hs_code = models.CharField(max_length=100, verbose_name='海关代码', null=True, blank=True)
    consignee_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="收货人名称")
    shipper_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="发货人名称")

    container_no = models.CharField(max_length=50, verbose_name='柜号', null=True, blank=True)
    departure = models.CharField(max_length=50, verbose_name='启运港', null=True, blank=True)
    destination = models.CharField(max_length=50, verbose_name='目的港', null=True, blank=True)
    discharge = models.CharField(max_length=50, verbose_name='卸货港', null=True, blank=True)
    vessel = models.CharField(max_length=50, verbose_name='船名', null=True, blank=True)
    voyage_num = models.CharField(max_length=20, verbose_name='航次', null=True, blank=True)

    def __str__(self):
        return self.clearance_num


class ClearanceOutChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(ClearanceOut, related_name='clearanceOutChargeOuts',
                                           on_delete=models.DO_NOTHING, null=True, verbose_name='报关单')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class ClearanceOutChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    customer_order_num = models.ForeignKey(ClearanceOut, related_name='clearanceOutChargeIns',
                                           on_delete=models.DO_NOTHING, null=True, verbose_name='报关单')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class ClearanceOutParcel(BaseEntity):
    customer_order = models.CharField(max_length=20, verbose_name='客户订单号', null=True, blank=True)
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True)
    parcel_desc = models.CharField(max_length=100, verbose_name='包裹描述', null=True, blank=True, default='0')
    parcel_length = models.FloatField(verbose_name='长', null=True, blank=True, default=1)
    parcel_width = models.FloatField(verbose_name='宽', null=True, blank=True, default=1)
    parcel_height = models.FloatField(verbose_name='高', null=True, blank=True, default=1)
    parcel_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True)
    parcel_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True,
                                        default=0.0001)
    parcel_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
                                        default=0.000001)
    tracking_num = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True)
    customer_order_num = models.ForeignKey(ClearanceOut, related_name='parcel', on_delete=models.DO_NOTHING,
                                           verbose_name='出口报关单号', null=True, blank=True)

    class Meta:
        verbose_name = '包裹'
        verbose_name_plural = '包裹'

    def __str__(self):
        return self.parcel_num


class ClearanceOutParcelItem(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    item_code = models.CharField(max_length=256, verbose_name='物品号', null=True, blank=True, default='0')
    item_name = models.CharField(max_length=256, verbose_name='物品名称', null=True, blank=True, default='0')
    sale_currency = models.CharField(max_length=10, verbose_name='报价币种', default='CNY', null=True,
                                     blank=True)
    sale_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='销售价格', null=True, blank=True,
                                     default=1)
    declared_currency = models.CharField(max_length=10, verbose_name='申报币种', default='USD',
                                         null=True,
                                         blank=True)
    declared_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='申报价格', null=True,
                                         blank=True,
                                         default=1)
    declared_nameCN = models.CharField(max_length=256, verbose_name='中文申报品名', null=True, blank=True)
    declared_nameEN = models.CharField(max_length=256, verbose_name='英文申报品名', null=True, blank=True)
    item_length = models.FloatField(verbose_name='长', null=True, blank=True, default=1)
    item_width = models.FloatField(verbose_name='宽', null=True, blank=True, default=1)
    item_height = models.FloatField(verbose_name='高', null=True, blank=True, default=1)
    item_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
                                      default=0.000001)
    item_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True,
                                      default=0.0001)
    item_qty = models.IntegerField(verbose_name='数量', null=True, blank=True, default=1)
    texture = models.CharField(max_length=50, verbose_name='材质', null=True, blank=True)
    item_size = models.CharField(max_length=50, verbose_name='尺寸', null=True, blank=True)
    use = models.CharField(max_length=50, verbose_name='用途', null=True, blank=True)
    brand = models.CharField(max_length=50, verbose_name='品牌', null=True, blank=True)
    model = models.CharField(max_length=50, verbose_name='型号', null=True, blank=True)
    customs_code = models.CharField(max_length=50, verbose_name='海关编码', null=True, blank=True)
    fba_no = models.CharField(max_length=50, verbose_name='FBA号', null=True, blank=True)
    parcel_num = models.ForeignKey(ClearanceOutParcel, related_name='parcelItem', verbose_name='包裹号',
                                   on_delete=models.DO_NOTHING, null=True, blank=True)

    class Meta:
        verbose_name = '物品'
        verbose_name_plural = '物品'

    def __str__(self):
        return 'ITEM保存成功。'


# 出口报关单轨迹
class ClearanceOutTrack(BaseEntity):
    track_code = models.CharField(max_length=20, verbose_name='轨迹代码', blank=True, null=True)
    track_name = models.CharField(max_length=20, verbose_name='轨迹名称', blank=True, null=True)
    location = models.CharField(max_length=200, verbose_name='位置', blank=True, null=True)
    operation_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)
    description = models.CharField(max_length=100, verbose_name='描述', blank=True, null=True)
    clearance_out = models.ForeignKey(ClearanceOut, related_name='clearanceOutTrack', on_delete=models.DO_NOTHING,
                                      null=True, verbose_name='出口报关单')


class ClearanceOutSyncTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('HandledBy3rdNo', '已提交'),  # 目前未使用这个状态
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]

    TASK_TYPE = [
        ('PUSH_ORDER', '同步订单'),
        ('PULL_ORDER_STATUS', '拉取订单数据'),
    ]

    order_num = models.ForeignKey(ClearanceOut, on_delete=models.DO_NOTHING, null=True,
                                  related_name='clearanceOutSyncTasks')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='任务状态')
    task_type = models.CharField(max_length=30, choices=TASK_TYPE, null=True, default='PUSH_ORDER',
                                 verbose_name='任务类型')
    task_desc = models.CharField(max_length=2048, verbose_name='任务描述', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='处理次数', null=True, blank=True, default=0)
    third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)

    class Meta:
        verbose_name_plural = '出口报关单同步任务表'
        verbose_name = '出口报关单同步任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


class ClearanceOutDetail(BaseEntity):
    line_num = models.CharField(verbose_name='行号', max_length=50, null=True, blank=True)
    clearance_out_id = models.ForeignKey(ClearanceOut, verbose_name='出口报关单', on_delete=models.CASCADE,
                                         related_name='clearance_out_details', null=True, blank=True)
    clearance_id = models.ForeignKey(Clearance, verbose_name='报关单', on_delete=models.CASCADE, null=True, blank=True)
    sku_name = models.CharField(verbose_name='商品名称', max_length=100, null=True, blank=True)
    material = models.CharField(verbose_name='材质', max_length=50, null=True, blank=True)
    specification = models.CharField(verbose_name='规格', max_length=50, null=True, blank=True)
    use = models.CharField(verbose_name='用途', max_length=50, null=True, blank=True)
    model = models.CharField(verbose_name='型号', max_length=50, null=True, blank=True)
    brand = models.CharField(verbose_name='品牌', max_length=50, null=True, blank=True)
    sku_code = models.CharField(verbose_name='商品编码', max_length=50, null=True, blank=True)
    uom = models.CharField(verbose_name='计量单位', max_length=50, null=True, blank=True)
    quantity = models.IntegerField(verbose_name='PCS', default=0)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='单价', null=True, blank=True)
    weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='重量', null=True, blank=True)
    currency = models.CharField(verbose_name='币种', max_length=50, null=True, blank=True)
    origin_country = models.CharField(verbose_name='原产国', max_length=20, null=True, blank=True)
    destination_country = models.CharField(verbose_name='目的国', max_length=20, null=True, blank=True)


# 卡派单(卡车单)
class TruckOrder(BaseEntity):
    TRANS_TYPE = [
        ('PU', '提货'),
        ('TR', '转运'),
    ]
    ORDER_STATUS = [
        ('DRA', '草稿'),
        ('BOO', '已预约'),
        ('CBE', '已延约'),
        ('DEL', '已送达'),
        ('VO', '作废'),
    ]
    truck_order_num = models.CharField(verbose_name='卡车订单号', max_length=50, null=True, blank=True)
    appointment_num = models.CharField(verbose_name='预约单号', max_length=50, blank=True, null=True)
    start_destination = models.CharField(verbose_name='始发站', max_length=50, null=True, blank=True)
    arrive_destination = models.CharField(verbose_name='目的站', max_length=100, null=True, blank=True)
    carton = models.IntegerField(verbose_name='总件数', default=0)
    weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量')
    volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积')
    charge_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='计费重量', null=True, blank=True)
    trans_type = models.CharField(max_length=20, choices=TRANS_TYPE, verbose_name='类型', null=True, blank=True)
    trans_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='转换率', null=True, blank=True)
    total_charge_in = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='总收入', default=0, null=True,
                                          blank=True)
    total_charge_out = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='总支出', default=0, null=True,
                                           blank=True)
    # POD file
    transport_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='运输单')
    order_status = models.CharField(max_length=30, choices=ORDER_STATUS, null=True, default='DRA',
                                    verbose_name='订单状态')
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)
    ocean_order_num = models.CharField(max_length=50, verbose_name='海运提单号', null=True, blank=True)
    book_warehouse_date = models.DateField(verbose_name='预约送仓时间', null=True, blank=True)
    actual_warehouse_date = models.DateField(verbose_name='实际送仓时间', null=True, blank=True)
    bind_truck_order_id = models.IntegerField(verbose_name='关联的父单', null=True, blank=True)
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    is_share_cost = models.BooleanField(verbose_name='成本分摊', null=True, blank=True, default=False)
    is_share_revenue = models.BooleanField(verbose_name='收入分摊', null=True, blank=True, default=False)

    class Meta:
        indexes = [
            models.Index(fields=['truck_order_num', 'del_flag']),
        ]
        verbose_name_plural = '卡派单'
        verbose_name = '卡派单'

    def __str__(self):
        return self.truck_order_num


# 卡车单轨迹
class TruckOrderTrack(BaseEntity):
    track_code = models.CharField(max_length=20, verbose_name='轨迹代码', blank=True, null=True)
    track_name = models.CharField(max_length=20, verbose_name='轨迹名称', blank=True, null=True)
    location = models.CharField(max_length=200, verbose_name='位置', blank=True, null=True)
    operation_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)
    description = models.CharField(max_length=100, verbose_name='描述', blank=True, null=True)
    # customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True, verbose_name='卡车单')
    truckOrder = models.ForeignKey(
        TruckOrder, related_name='truckOrderTrack', verbose_name='卡车单',
        on_delete=models.DO_NOTHING, null=True
    )


# class TruckOrderChargeIn(BaseEntity):
#     CURRENCY = [
#         ('CNY', 'CNY'),
#         ('USD', 'USD'),
#         ('GBP', 'GBP'),
#         ('EUR', 'EUR'),
#         ('HKD', 'HKD'),
#         ('CAD', 'CAD'),
#         ('CHF', 'CHF'),
#         ('AUD', 'AUD'),
#     ]
#
#     charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
#     charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
#     charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
#     charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
#     currency_type = models.CharField(max_length=10,  verbose_name='币种', default='CNY')
#     current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
#     account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
#                                          verbose_name='记账金额')
#     customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
#     customer_order_num = models.ForeignKey(TruckOrder, related_name='truckOrderChargeIns',
#                                            on_delete=models.DO_NOTHING, null=True, verbose_name='卡派单收入')
#     charge_price = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='计费价')
#     is_system = models.BooleanField(verbose_name='系统添加', default=False)
#
#     def __str__(self):
#         return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class TruckOrderChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(TruckOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='truck_order_charge_out')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')
    is_system = models.BooleanField(verbose_name='系统添加', default=False)

    class Meta:
        verbose_name = '卡派单费用'
        verbose_name_plural = '卡派单费用'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


# 揽收单
class CollectOrder(BaseEntity):
    ORDER_STATUS = [
        ('DR', '草稿'),
        ('SM', '已提交'),
        ('WO', '等待作业'),
        ('FC', '完成'),
        ('VO', '作废'),
    ]
    order_num = models.CharField(max_length=30, verbose_name='揽收单号', null=True, unique=True, db_index=True)
    order_status = models.CharField(max_length=30, choices=ORDER_STATUS, null=True, default='SM',
                                    verbose_name='揽收状态')
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    state_code = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True)
    city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True)
    postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)
    address = models.CharField(max_length=100, verbose_name='揽收地址', null=True, blank=True)
    address_num = models.ForeignKey(Address, verbose_name='揽收地址编码', null=True, blank=True,
                                    on_delete=models.DO_NOTHING, related_name='collect_address')
    destination_address = models.ForeignKey(Address, verbose_name='目的地地址编码', null=True, blank=True,
                                            on_delete=models.DO_NOTHING, related_name='destination_address')
    warehouse_code = models.CharField(max_length=100, verbose_name='仓库编码', null=True, blank=True)
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商',
                                 related_name='supplier_collect_order')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户',
                                 related_name='customer_collect_order')
    service_code = models.ForeignKey(Service, verbose_name='供应商服务', null=True, blank=True,
                                     on_delete=models.DO_NOTHING)
    pre_carton = models.IntegerField(verbose_name='预计件数', null=True, blank=True)
    pre_weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='预计重量', null=True, blank=True)
    pre_volume = models.DecimalField(decimal_places=2, max_digits=20, verbose_name='预计体积', null=True, blank=True)
    carton = models.IntegerField(verbose_name='件数', null=True, blank=True)
    weight = models.DecimalField(decimal_places=4, max_digits=15, verbose_name='重量', null=True, blank=True)
    volume = models.DecimalField(decimal_places=6, max_digits=15, verbose_name='体积', null=True, blank=True)
    income = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='收入')
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    is_revenue_lock = models.BooleanField(verbose_name='收入确认', default=False)
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    # product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True, blank=True)
    plan_arrival_wh_time = models.DateTimeField(verbose_name='预计到仓时间', null=True, blank=True)
    plan_visit_time = models.DateTimeField(verbose_name='预计上门时间', null=True, blank=True)
    car_number = models.CharField(max_length=50, verbose_name='车牌号', null=True, blank=True)
    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)
    driver_name = models.CharField(verbose_name="司机姓名", max_length=32, null=True, blank=True)
    driver_mobile = models.CharField(max_length=11, verbose_name="司机联系方式", null=True, blank=True)
    license_plate = models.CharField(verbose_name="车牌号", max_length=20, null=True, blank=True)
    is_carry = models.BooleanField(verbose_name='是否搬运', default=False)
    is_pay_for_another = models.BooleanField(verbose_name='是否代付', default=False)
    is_share_cost = models.BooleanField(verbose_name='成本分摊', null=True, blank=True, default=False)
    is_share_revenue = models.BooleanField(verbose_name='收入分摊', null=True, blank=True, default=False)

    class Meta:
        verbose_name = '揽收单'
        verbose_name_plural = '揽收单'

    def __str__(self):
        return self.order_num


class CollectOrderTrack(BaseEntity):
    track_code = models.CharField(max_length=20, verbose_name='轨迹代码', blank=True, null=True)
    track_name = models.CharField(max_length=20, verbose_name='轨迹名称', blank=True, null=True)
    operation_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)
    description = models.CharField(max_length=100, verbose_name='描述', blank=True, null=True)
    collect_order = models.ForeignKey(CollectOrder, related_name='CollectOrder', on_delete=models.DO_NOTHING,
                                      null=True, verbose_name='关联的揽收单号')


class CollectOrderChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    customer_order_num = models.ForeignKey(CollectOrder, related_name='collectOrderChargeIns',
                                           on_delete=models.DO_NOTHING, null=True, verbose_name='揽收单')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    class Meta:
        verbose_name = '揽收单收入'
        verbose_name_plural = '揽收单收入'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class CollectOrderChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(CollectOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='collectOrderChargeOuts')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    class Meta:
        verbose_name = '揽收单费用'
        verbose_name_plural = '揽收单费用'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


# 客户订单-----------------------------------------------------------------------------------------------------------
class CustomerOrder(BaseEntity):
    ORDER_TYPE = [
        ('TR', '运输单'),
        ('FBA', 'FBA订单'),
        ('GL', '快递派送单'),
        ('FBM', 'FBM专线订单')
    ]

    ORDER_STATUS = [
        ('DR', '草稿'),
        ('WO', '等待作业'),
        # ========FBA========
        ('PDC', '已预报'),
        ('PW', '已部分入仓'),
        ('AW', '已全部入仓'),
        ('CWED', '已确认入仓数据'),
        ('OW', '已出国内仓'),
        ('DEP', '已离港'),
        ('TF', '转运'),
        ('SF', '已签收'),
        ('ITP', '已拦截'),
        ('VO', '作废'),
        # ========FBM========
        ('VC', '已审核'),
        ('IW', '已到货'),
        ('IWC', '确认入仓'),
        ('OWH', '已出仓'),
        ('DE', '已报关'),
        ('SO', '已离港/已起飞'),
        ('AR', '已到港/已降落'),
        ('CC', '已清关'),
        ('IWW', '已到达海外仓'),
        ('OOD', '派送中'),
        ('PSF', '部分签收'),
        ('FC', '完成'),
    ]
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    # CUSTOM_CLEARANCE = [
    #     ('CND', '渠道默认'),
    #     ('PR', '部分退税'),
    #     ('TRD', '退税报关'),
    #     ('CDWP', '买单报关'),
    # ]

    TRANSPORT_TYPE = [
        ('AIR', '空运'),
        ('SEA', '海运'),
        ('MHK', '中港运输'),
        ('ALLOCATE', '调拨'),
    ]

    CARRIAGE_TYPE = [
        ('Airline', '航司'),
        ('ShippingLine', '船司'),
        ('Trucking', '汽运'),
    ]

    TRANSPORT_STATUS = [
        # ('PL', '待装车'),
        # ('LIP', '装车中'),
        # ('AL', '已装车'),
        ('AD', '已发车'),
        ('AA', '已到达'),
        ('UIP', '卸车中'),
        ('AU', '已卸车'),
        ('AHO', '已交接'),
        ('HC', '交接完成'),
    ]

    order_num = models.CharField(max_length=30, verbose_name='订单号', null=True, blank=True)
    ref_num = models.CharField(max_length=50, verbose_name='客户订单号', null=True, blank=True)
    warehouse_receipt_number = models.CharField(max_length=50, verbose_name='入仓单号', null=True, blank=True)
    order_status = models.CharField(max_length=30, choices=ORDER_STATUS, null=True, default='WO',
                                    verbose_name='订单状态')
    transport_status = models.CharField(max_length=10, choices=TRANSPORT_STATUS, verbose_name='运输状态', default='AD')

    transport_type = models.CharField(verbose_name='运输类型', max_length=30, choices=TRANSPORT_TYPE, null=True,
                                      blank=True)
    carriage_type = models.CharField(verbose_name='承运类型', max_length=30, choices=CARRIAGE_TYPE, null=True,
                                     blank=True)
    arrival_date = models.DateField(verbose_name='预计到货日期', null=True, blank=True)
    actual_arrival_date = models.DateTimeField(verbose_name='实际到货时间', null=True, blank=True)
    customer = models.ForeignKey(Company, related_name='order_customer',
                                 verbose_name='客户', on_delete=models.DO_NOTHING, null=True)
    contact = models.CharField(max_length=20, verbose_name='联系人', null=True, blank=True)
    pre_carton = models.IntegerField(verbose_name='预计件数', null=True, blank=True)
    pre_weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='预计重量', null=True, blank=True)
    pre_volume = models.DecimalField(decimal_places=2, max_digits=20, verbose_name='预计体积', null=True, blank=True)
    pre_size = models.CharField(max_length=500, verbose_name='预报尺寸', null=True, blank=True)
    carton = models.IntegerField(verbose_name='件数', null=True, blank=True)
    weight = models.DecimalField(decimal_places=4, max_digits=12, verbose_name='重量', null=True, blank=True)
    # 包裹体积保留6位小数, 计算包裹分摊成本时需要 *包裹体积/订单体积, 所以精度需要保持一致
    volume = models.DecimalField(decimal_places=6, max_digits=15, verbose_name='体积', null=True, blank=True)
    supplier_carton = models.IntegerField(verbose_name='供应商总件数', null=True, blank=True)
    supplier_weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='供应商总重量', null=True,
                                          blank=True)
    supplier_volume = models.DecimalField(decimal_places=2, max_digits=20, verbose_name='供应商总体积', null=True,
                                          blank=True)
    charge_trans = models.DecimalField(decimal_places=2, max_digits=10, verbose_name='计费转换', null=True,
                                       default=6000)
    charge_weight = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='计费重量', null=True, blank=True)
    confirm_charge_weight = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='确认计费重量', null=True,
                                                blank=True)
    confirm_volume = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='确认体积', null=True,
                                         blank=True)
    # 无用, 可以去掉
    charge_volume = models.DecimalField(decimal_places=2, max_digits=10, verbose_name='计费体积', null=True, blank=True)
    size = models.CharField(max_length=500, verbose_name='尺寸', null=True, blank=True)
    airline_num = models.CharField(max_length=20, verbose_name='航班号', null=True, blank=True)
    expected_leave_date = models.DateField(verbose_name='预计离港日期', null=True, blank=True)
    expected_arrivals_date = models.DateField(verbose_name='预计到港日期', null=True, blank=True)
    actual_leave_date = models.DateField(verbose_name='实际离港日期', null=True, blank=True)
    actual_arrivals_date = models.DateField(verbose_name='实际到港日期', null=True, blank=True)
    departure = models.CharField(max_length=50, verbose_name='启运港', null=True, blank=True)
    destination = models.CharField(max_length=50, verbose_name='目的港', null=True, blank=True)
    expect_arrived_wh_date = models.DateField(verbose_name='预计到仓日期', null=True, blank=True)
    actual_arrived_wh_date = models.DateField(verbose_name='实际到海外仓日期', null=True, blank=True)
    master_num = models.ForeignKey(MasterOrder, related_name='master_customerOrders', verbose_name='主单号', null=True,
                                   blank=True, on_delete=models.SET_NULL)
    master_number = models.CharField(verbose_name='主单号', max_length=50, null=True, blank=True)
    # 海运优先轨迹外键, 注意: 海运单和订单是多对多关系, 关系表是: CustomerOrderRelateOcean
    ocean_num = models.ForeignKey(OceanOrder, related_name='ocean_customer_order', verbose_name='提单号', null=True,
                                  blank=True, on_delete=models.DO_NOTHING)
    ocean_number = models.CharField(verbose_name='提单号', max_length=50, null=True, blank=True)
    vessel = models.CharField(max_length=50, verbose_name='船名', null=True, blank=True)
    voyage_num = models.CharField(max_length=20, verbose_name='航次', null=True, blank=True)
    house_num = models.ForeignKey(HouseOrder, related_name='house_customerOrders', verbose_name='分单号', null=True,
                                  blank=True, on_delete=models.SET_NULL)
    collect_num = models.ForeignKey(CollectOrder, related_name='collect_customerOrders', verbose_name='揽收单号',
                                    null=True, blank=True, on_delete=models.SET_NULL)
    third_orderNo = models.CharField(max_length=50, verbose_name='第三方服务单号', null=True, blank=True)
    external_order_num = models.CharField(max_length=50, verbose_name='外部订单号', null=True, blank=True)
    external_order_status = models.CharField(max_length=50, verbose_name='外部订单状态', null=True, blank=True)
    tracking_num = models.CharField(max_length=100, verbose_name='转单号', null=True, blank=True)
    is_send_debit = models.BooleanField(verbose_name='账单发送', default=False)
    volume_weight = models.DecimalField(decimal_places=0, max_digits=10, verbose_name='重泡比', default=0)
    product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True, blank=True)
    service = models.ForeignKey(Service, verbose_name='服务', on_delete=models.DO_NOTHING, null=True, blank=True)
    # 如果产品是虚拟产品，则需要添加真实产品
    real_product = models.ForeignKey(Product, verbose_name='真实产品', on_delete=models.DO_NOTHING, null=True,
                                     blank=True, related_name='real_product')
    saler = models.CharField(max_length=20, verbose_name='销售负责人', null=True, blank=True)
    is_revenue_lock = models.BooleanField(verbose_name='收入确认', default=False)
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    # truck_order_id = models.ManyToManyField(TruckOrder, verbose_name='卡车单', through='TruckRelation')
    truck_num = models.CharField(max_length=20, verbose_name='车牌', null=True, blank=True)
    truck_order_id = models.ForeignKey(TruckOrder, related_name='truck_customerOrders', verbose_name='卡车单',
                                       null=True,
                                       blank=True, on_delete=models.SET_NULL)
    clearance_out = models.ForeignKey(ClearanceOut, related_name='orderClearanceOut', verbose_name='出口报关单',
                                      null=True, blank=True, on_delete=models.SET_NULL)
    truck_inquiry = models.ForeignKey('TruckInquiryPriceOrder', related_name='truck_inquiry_customerOrders',
                                      verbose_name='询价单', null=True, blank=True, on_delete=models.SET_NULL)
    bubble = models.CharField(max_length=20, verbose_name='客户分泡(%)', null=True, blank=True, default=0)
    bubble_weight = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                        verbose_name='分泡后计费重')
    income = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='收入')
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    gross_profit = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='毛利')
    gross_currency = models.CharField(max_length=10, verbose_name='毛利币种', null=True, blank=True)
    download_num = models.IntegerField(null=True, blank=True, default=0, verbose_name='下载面单次数')
    parceType = models.BooleanField(verbose_name='是否完整商品', default=True, null=True, blank=True)
    is_arrival_notice = models.BooleanField(verbose_name='到货通知', default=False, null=True, blank=True)
    is_pickup = models.BooleanField(verbose_name='是否提货', default=False, null=True, blank=True)
    pickup_contact = models.CharField(max_length=30, verbose_name='提货联系人', null=True, blank=True)
    pickup_address = models.CharField(max_length=255, verbose_name='提货地址', null=True, blank=True)
    pickup_phone = models.CharField(max_length=30, verbose_name='提货电话', null=True, blank=True)
    pickup_date = models.DateTimeField(verbose_name='提货时间', null=True, blank=True)
    pickup_remark = models.TextField(verbose_name='提货备注', null=True, blank=True)
    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)
    product_features = models.CharField(max_length=100, verbose_name='产品特征', null=True, blank=True)
    custom_clearance = models.CharField(max_length=100, verbose_name='报关方式', null=True, blank=True)

    # 收件人
    receiver = models.ForeignKey(Address, verbose_name='收件人', null=True, blank=True, on_delete=models.SET_NULL,
                                 related_name='customerorder_receiver')
    buyer_address_num = models.CharField(max_length=100, verbose_name='地址编码', null=True, blank=True)
    buyer_name = models.CharField(max_length=100, verbose_name='联系人', null=True, blank=True)
    buyer_mail = models.EmailField(max_length=300, verbose_name='邮箱', null=True, blank=True)
    buyer_phone = models.CharField(max_length=21, verbose_name='电话', null=True, blank=True)
    buyer_country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    buyer_state = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True)
    buyer_city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True)
    buyer_postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)
    buyer_house_num = models.CharField(max_length=120, verbose_name='门牌号', null=True, blank=True)
    buyer_address_one = models.CharField(max_length=256, verbose_name='地址行1', null=True, blank=True)
    buyer_address_two = models.CharField(max_length=256, verbose_name='地址行2', null=True, blank=True)
    buyer_company_name = models.CharField(max_length=150, verbose_name='公司名', null=True, blank=True)
    is_b_async = models.BooleanField(verbose_name='是否同步', default=False, null=True, blank=True)
    buyer_tax = models.CharField(max_length=256, verbose_name='tax', null=True, blank=True)

    # 发件人
    shipper = models.ForeignKey(Address, verbose_name='发件人', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='customerorder_shipper')
    address_num = models.CharField(max_length=100, verbose_name='地址编码', null=True, blank=True)
    contact_name = models.CharField(max_length=100, verbose_name='联系人', null=True, blank=True)
    contact_email = models.EmailField(max_length=300, verbose_name='邮箱', null=True, blank=True)
    contact_phone = models.CharField(max_length=50, verbose_name='电话', null=True, blank=True)
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    state_code = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True)
    city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True)
    postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)
    house_no = models.CharField(max_length=20, verbose_name='门牌号', null=True, blank=True)
    address_one = models.CharField(max_length=100, verbose_name='地址行1', null=True, blank=True)
    address_two = models.CharField(max_length=100, verbose_name='地址行2', null=True, blank=True)
    company_name = models.CharField(max_length=100, verbose_name='公司名', null=True, blank=True)
    is_r_async = models.BooleanField(verbose_name='是否同步', default=False, null=True, blank=True)

    is_insurance = models.BooleanField(verbose_name='是否下保险单', default=False, null=True, blank=True)
    insurance_num = models.CharField(max_length=20, verbose_name='保险单号', null=True, blank=True)
    zone_value = models.CharField(max_length=48, verbose_name='分区值', null=True, blank=True)
    customer_remark = models.CharField(max_length=500, verbose_name='客户通知', null=True, blank=True)
    is_customs_declaration = models.BooleanField(verbose_name='是否报关单', default=False, null=True, blank=True)
    order_remark = models.CharField(max_length=100, verbose_name='订单备注', null=True, blank=True)
    ioss_num = models.CharField(max_length=120, verbose_name='ioss号', null=True, blank=True)
    vat_num = models.CharField(max_length=120, verbose_name='VAT号', null=True, blank=True)
    eori_num = models.CharField(max_length=120, verbose_name='EORI号', null=True, blank=True)
    # 增加是否作废和是否拦截, 订单状态不变, 对于已作废/已拦截的订单, 显示已作废/已拦截, 恢复后订单状态正常显示
    # is_cancel = models.BooleanField(verbose_name='是否作废', default=False, null=True, blank=True)
    is_intercept = models.BooleanField(verbose_name='是否拦截', default=False, null=True, blank=True)
    first_track = models.BooleanField(verbose_name='优先轨迹', default=False, null=True, blank=True)
    is_remote = models.BooleanField(verbose_name='是否偏远地区', default=False, null=True, blank=True)
    is_overlength = models.BooleanField(verbose_name='是否超长', default=False, null=True, blank=True)
    is_overweight = models.BooleanField(verbose_name='是否超重', default=False, null=True, blank=True)
    is_private_address = models.BooleanField(verbose_name='是否私人地址', default=False, null=True, blank=True)
    reference_id = models.CharField(max_length=256, verbose_name='Reference Id', null=True, blank=True)
    shipment_id = models.CharField(max_length=256, verbose_name='Shipment Id', null=True, blank=True)
    # 大包签入第一件的时间
    check_in_time = models.DateTimeField(verbose_name='签入时间', null=True, blank=True)
    sales_price_card_time = models.DateTimeField(verbose_name='销售价卡时间', default=None, null=True, blank=True)
    # feedback_url = models.CharField(max_length=200, verbose_name='同步订单数据url', null=True, blank=True)
    pod_file = models.FileField(null=True, blank=True, upload_to='files/%Y/%m/%d', verbose_name='卡派pod')
    order_type = models.CharField(max_length=100, verbose_name='订单类型', choices=ORDER_TYPE, default='FBA')
    warehouse_type = models.CharField(max_length=48, verbose_name='仓库类型', blank=True, null=True)
    # todo_x: 预计送达时间
    estimated_delivery_date = models.DateField(verbose_name='签收时间', null=True, blank=True)
    sign_time = models.DateTimeField(verbose_name='签收时间', null=True, blank=True)
    logistics_planning = models.ForeignKey('LogisticsPlanning', related_name='customer_order_logistics_planning',
                                           verbose_name='物流计划', null=True, blank=True, on_delete=models.DO_NOTHING)
    ocean_warehouse = models.ForeignKey(Address, verbose_name='海外仓', null=True, blank=True,
                                        on_delete=models.DO_NOTHING,
                                        related_name='customer_order_ocean_warehouse')
    is_importer_clearance = models.BooleanField(verbose_name='是否使用进口商清关', default=False, null=True, blank=True)

    supplier = models.ForeignKey(Company, verbose_name='供应商', related_name='supplier_customerOrders',
                                 on_delete=models.DO_NOTHING, null=True, blank=True)
    combine_billing_order = models.ForeignKey('CombineBillingOrder', verbose_name='供应商',
                                              related_name='order_combine_billing_order',
                                              on_delete=models.DO_NOTHING, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['order_num', 'del_flag']),
            models.Index(fields=['ref_num', 'del_flag']),
            models.Index(fields=['tracking_num', 'del_flag']),
            models.Index(fields=['third_orderNo', 'del_flag']),
        ]
        verbose_name_plural = '客户订单'
        verbose_name = '客户订单'

    def __str__(self):
        if self.order_num:
            return self.order_num
        else:
            return ''


class CustomerOrderAddress(BaseContactAddress):
    """运输计划单地址快照表"""
    ADDRESS_TYPE = [
        ('SP', '发件人'),
        ('RC', '收件人'),
        ('TD', '交接地址'),
        ('ZW', '退件地址'),
        ('CA', '揽收地址'),
        ('NP', 'Notify Party')
    ]

    customer_order = models.ForeignKey(CustomerOrder, related_name='CustomerOrderAddress',
                                       on_delete=models.DO_NOTHING,
                                       verbose_name='运输作业单号', null=True, blank=True)
    address_type = models.CharField(max_length=10, choices=ADDRESS_TYPE, verbose_name='地址类型', default='RC')

    class Meta:
        verbose_name = '运输作业单地址信息'
        verbose_name_plural = '运输作业单地址信息'

    def __str__(self):
        return '%s: %s %s ' % (self.address_num, self.postcode, self.country_code,)


class CustomerOrderExtension(models.Model):
    """
    订单扩展表
    """
    customer_order = models.OneToOneField(
        'CustomerOrder',
        on_delete=models.CASCADE,
        related_name='extension',
        verbose_name='关联订单',
        null=True,
        blank=True
    )

    deliverable_date = models.DateTimeField(verbose_name='预计可派送时间', null=True, blank=True)
    clearance_dest = models.CharField(max_length=50, verbose_name='目的港清关', null=True, blank=True)
    declare_attachments = models.TextField(verbose_name='报关资料', null=True, blank=True)

    class Meta:
        verbose_name = '订单扩展信息'
        verbose_name_plural = '订单扩展信息'


class OrderFieldChangeLog(BaseEntity):
    ORDER_TYPE = [
        ('TR', '运输单'),
        ('FBA', 'FBA订单'),
        ('GL', '快递派送单'),
        ('FBM', 'FBM专线订单'),
        ('OutboundInstruct', '出库指令单'),
        ('OcShipment', '货件号')
    ]

    order_type = models.CharField(max_length=100, verbose_name='订单类型', choices=ORDER_TYPE, default='FBA')
    model_instance_id = models.IntegerField(verbose_name='对象id', db_index=True)
    field_name = models.CharField(max_length=100, verbose_name='字段名称')
    field_verbose_name = models.CharField(max_length=100, verbose_name='字段中文名称', null=True, blank=True)
    old_value = models.CharField(max_length=500, verbose_name='修改前字段值', null=True, blank=True)
    new_value = models.CharField(max_length=500, verbose_name='修改后字段值', null=True, blank=True)

    class Meta:
        verbose_name = '订单字段修改记录'
        verbose_name_plural = '订单字段修改记录'

    def __str__(self):
        return '%s: %s %s' % (self.order_type, self.model_instance_id, self.field_name)

    @classmethod
    def record(cls, instance, validated_data, order_type, user, instance_id=None, do_not_record=None):
        # data_id = validated_data.get('id', None)
        # if not data_id:
        #     return
        # if not instance:
        #     instance = class_obj.objects.filter(id=data_id).first()
        if not instance_id:
            instance_id = instance.id
        if not instance:
            return

        now = datetime.now()
        if not do_not_record:
            do_not_record = ['update_by', 'update_date']
        else:
            do_not_record.extend(['update_by', 'update_date'])

        for field in validated_data:
            if field in do_not_record:
                continue
            if hasattr(instance, field):
                origin_value = getattr(instance, field)
                # print('-----------------------------------------------', origin_value, validated_data[field])

                if (validated_data[field] or origin_value) and validated_data[field] != origin_value:
                    # verbose_name = None
                    # if class_model:
                    #     verbose_name = class_model._meta.get_field(field).verbose_name
                    verbose_name_field = instance.__class__._meta.get_field(field)
                    # print('verbose_name-->', verbose_name)
                    if hasattr(verbose_name_field, 'verbose_name'):
                        log_obj = cls(order_type=order_type, model_instance_id=instance_id,
                                      field_name=field,
                                      field_verbose_name=verbose_name_field.verbose_name,
                                      old_value=origin_value,
                                      new_value=validated_data[field],
                                      update_date=now, create_date=now,
                                      create_by=user, update_by=user)
                        log_obj.save()

    @classmethod
    def record_field(cls, instance, validated_data, field, order_type, user, instance_id=None, do_not_record=None):
        if not instance_id:
            instance_id = instance.id
        if not instance:
            return

        now = datetime.now()
        if not do_not_record:
            do_not_record = ['update_by', 'update_date']
        else:
            do_not_record.extend(['update_by', 'update_date'])

        if field in do_not_record:
            return
        if hasattr(instance, field):
            origin_value = getattr(instance, field)

            if (validated_data[field] or origin_value) and validated_data[field] != origin_value:
                verbose_name_field = instance.__class__._meta.get_field(field)
                if hasattr(verbose_name_field, 'verbose_name'):
                    log_obj = cls(order_type=order_type, model_instance_id=instance_id,
                                  field_name=field,
                                  field_verbose_name=verbose_name_field.verbose_name,
                                  old_value=origin_value,
                                  new_value=validated_data[field],
                                  update_date=now, create_date=now,
                                  create_by=user, update_by=user)
                    log_obj.save()

    @classmethod
    def get_ignore_record_field(cls, do_not_record: list = None):
        """
        获取不需要记录的字段列表
        :param do_not_record: 不需要记录的字段列表
        :return: 不需要记录的字段列表
        """
        ignore_field_list = ['update_by', 'update_date']

        if isinstance(do_not_record, list):
            ignore_field_list.extend(do_not_record)

        return ignore_field_list

    @classmethod
    def get_field_diff(cls, old_instance, new_instance, fields):
        """
        比较同一个模型修改前后模型实例之间的字段差异
        :param old_instance: 原始对象
        :param new_instance: 当前对象
        :param fields: 需要比较的字段列表
        :return: {field: {'old_val': old_val, 'new_val': new_val}, ...}
        """
        diff = {}
        for field in fields:
            old_val = getattr(old_instance, field)
            new_val = getattr(new_instance, field)
            if old_val != new_val:
                diff[field] = {'old_val': old_val, 'new_val': new_val}
        return diff

    @classmethod
    def prepare_log_entries(cls, changes_dic: dict, instance, order_type: str, user: str,
                            do_not_record: list = None) -> list:
        """
        准备操作记录条目
        :param changes_dic: 变化的字段信息 {field: {'old_val': old_val, 'new_val': new_val}, ...}
        :param instance: 实例对象
        :param order_type: 订单类型
        :param user: 修改人
        :return: 日志条目列表
        """

        now = datetime.now()
        common_data = {
            'order_type': order_type,
            'model_instance_id': instance.id,
            'update_date': now,
            'create_date': now,
            'create_by': user,
            'update_by': user,
        }

        # 获取不需要记录的字段
        ignore_field_list = cls.get_ignore_record_field(do_not_record)

        log_entries = []
        for field, change in changes_dic.items():
            if field in ignore_field_list:
                continue
            verbose_name_field = instance.__class__._meta.get_field(field)
            if hasattr(verbose_name_field, 'verbose_name'):
                log_entries.append(cls(
                    field_name=field,
                    field_verbose_name=verbose_name_field.verbose_name,
                    old_value=change['old_val'],
                    new_value=change['new_val'],
                    **common_data
                ))

        return log_entries

    @classmethod
    def record_instance_changes(cls, old_instance, new_instance, fields_list: list,
                                order_type: str, user: str, do_not_record: list = None):
        """
        记录实例字段的修改
        :param old_instance: 原始对象
        :param new_instance: 当前对象
        :param fields_list: 需要比较的字段列表
        :param order_type: 订单类型
        :param user: 修改人
        :param do_not_record: 不需要记录的字段列表
        """

        # 确保传入的是同一种对象
        if (not old_instance) or (not new_instance) or old_instance.__class__ != new_instance.__class__:
            return

        # 获取变化的字段信息
        changes_dic = cls.get_field_diff(old_instance, new_instance, fields_list)
        if not changes_dic:
            return

        # 准备并保存日志条目
        log_entries = cls.prepare_log_entries(
            changes_dic=changes_dic,
            instance=new_instance,
            order_type=order_type,
            user=user,
            do_not_record=do_not_record
        )

        # 批量写入操作记录
        if log_entries:
            cls.objects.bulk_create(log_entries)


# 运输单关联海运提单多对多关系(海运提单和订单的中间表)(以前的运输单中的外键关系已经不用了)
class CustomerOrderRelateOcean(BaseEntity):
    oceanOrder = models.ForeignKey(OceanOrder, related_name='oceanOrderRelateList', verbose_name='海运',
                                   on_delete=models.DO_NOTHING, null=True)
    customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='customerOrderRelateList')
    allocate_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='配载重量', default=0)
    allocate_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='配载体积', default=0)
    freight_num = models.IntegerField(verbose_name='配载件数', default=0)
    out_warehouse_num = models.IntegerField(verbose_name='出仓件数', default=0)
    container_head = models.CharField(max_length=50, verbose_name='柜头', null=True, blank=True)
    container_tail = models.CharField(max_length=50, verbose_name='柜尾', null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['customer_order_num', 'del_flag']),
            models.Index(fields=['oceanOrder', 'del_flag']),
        ]
        verbose_name = '运输关联海运提单'
        verbose_name_plural = '运输关联海运提单'

    def __str__(self):
        return '%s: %s' % (self.customer_order_num, self.oceanOrder)


# 订单绑定卡派单中间表
class CustomerOrderRelateTruck(BaseEntity):
    truck_order = models.ForeignKey(TruckOrder, related_name='truckOrderRelateList', verbose_name='拖车',
                                    on_delete=models.DO_NOTHING, null=True)
    customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='truck_customer_order_ref')
    allocate_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='配载重量', default=0)
    allocate_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='配载体积', default=0)
    freight_num = models.IntegerField(verbose_name='配载件数', default=0)
    is_sync_track = models.BooleanField(verbose_name='是否同步轨迹', null=True, blank=True, default=True)

    class Meta:
        indexes = [
            models.Index(fields=['customer_order_num', 'del_flag']),
            models.Index(fields=['truck_order', 'del_flag']),
        ]
        verbose_name = '运输关联卡派单'
        verbose_name_plural = verbose_name


# 合并计费单
class CombineBillingOrder(BaseEntity):
    ORDER_STATUS = [
        ('DR', '草稿'),
        ('SM', '已提交'),
        ('CH', '已审核'),
        ('FC', '完成'),
        ('VO', '作废'),
    ]
    order_num = models.CharField(max_length=30, verbose_name='计费单号', null=True, unique=True, db_index=True)
    order_status = models.CharField(max_length=30, choices=ORDER_STATUS, null=True, default='DR',
                                    verbose_name='单据状态')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商',
                                 related_name='combine_billing_order_supplier')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户',
                                 related_name='combine_billing_order_customer')
    product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True, blank=True)
    real_product = models.ForeignKey(Product, verbose_name='真实产品', on_delete=models.DO_NOTHING, null=True,
                                     blank=True, related_name='combine_billing_order_real_product')

    # 收件人
    receiver = models.ForeignKey(Address, verbose_name='收件人', null=True, blank=True, on_delete=models.DO_NOTHING,
                                 related_name='combine_billing_order_receiver')
    buyer_address_num = models.CharField(max_length=100, verbose_name='地址编码', null=True, blank=True)
    buyer_name = models.CharField(max_length=100, verbose_name='联系人', null=True, blank=True)
    buyer_mail = models.EmailField(max_length=300, verbose_name='邮箱', null=True, blank=True)
    buyer_phone = models.CharField(max_length=21, verbose_name='电话', null=True, blank=True)
    buyer_country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    buyer_state = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True)
    buyer_city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True)
    buyer_postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)
    buyer_house_num = models.CharField(max_length=120, verbose_name='门牌号', null=True, blank=True)
    buyer_address_one = models.CharField(max_length=256, verbose_name='地址行1', null=True, blank=True)
    buyer_address_two = models.CharField(max_length=256, verbose_name='地址行2', null=True, blank=True)
    buyer_company_name = models.CharField(max_length=150, verbose_name='公司名', null=True, blank=True)

    # 发件人
    shipper = models.ForeignKey(Address, verbose_name='发件人', null=True, blank=True, on_delete=models.DO_NOTHING,
                                related_name='combine_billing_order_shipper')
    address_num = models.CharField(max_length=100, verbose_name='地址编码', null=True, blank=True)
    contact_name = models.CharField(max_length=100, verbose_name='联系人', null=True, blank=True)
    contact_email = models.EmailField(max_length=300, verbose_name='邮箱', null=True, blank=True)
    contact_phone = models.CharField(max_length=50, verbose_name='电话', null=True, blank=True)
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    state_code = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True)
    city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True)
    postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)
    house_no = models.CharField(max_length=20, verbose_name='门牌号', null=True, blank=True)
    address_one = models.CharField(max_length=100, verbose_name='地址行1', null=True, blank=True)
    address_two = models.CharField(max_length=100, verbose_name='地址行2', null=True, blank=True)
    company_name = models.CharField(max_length=100, verbose_name='公司名', null=True, blank=True)

    pre_carton = models.IntegerField(verbose_name='预计件数', null=True, blank=True)
    pre_weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='预计重量', null=True, blank=True)
    pre_volume = models.DecimalField(decimal_places=2, max_digits=20, verbose_name='预计体积', null=True, blank=True)
    carton = models.IntegerField(verbose_name='件数', null=True, blank=True)
    weight = models.DecimalField(decimal_places=4, max_digits=15, verbose_name='重量', null=True, blank=True)
    volume = models.DecimalField(decimal_places=6, max_digits=15, verbose_name='体积', null=True, blank=True)
    charge_trans = models.DecimalField(decimal_places=2, max_digits=10, verbose_name='计费重转换率', null=True,
                                       default=6000)
    charge_weight = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='计费重', null=True, blank=True)
    confirm_charge_weight = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='确认计费重量', null=True,
                                                blank=True)
    confirm_volume = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='确认计费体积', null=True,
                                         blank=True)
    check_in_time = models.DateTimeField(verbose_name='签入时间', null=True, blank=True)

    income = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='合并收入')
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='合并成本')
    split_income = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='拆分收入')
    split_cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='拆分成本')
    is_revenue_lock = models.BooleanField(verbose_name='收入确认', default=False)
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    is_share_revenue = models.BooleanField(verbose_name='收入分摊', null=True, blank=True, default=False)
    is_share_cost = models.BooleanField(verbose_name='成本分摊', null=True, blank=True, default=False)

    class Meta:
        verbose_name = '合并计费单'
        verbose_name_plural = '合并计费单'

    def __str__(self):
        return self.order_num


class CombineBillingOrderChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    split_charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                             verbose_name='拆分合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    customer_order_num = models.ForeignKey(CombineBillingOrder, related_name='combineBillingOrderChargeIns',
                                           on_delete=models.DO_NOTHING, null=True, verbose_name='合并计费单')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    class Meta:
        verbose_name = '合并计费单收入'
        verbose_name_plural = '合并计费单收入'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class CombineBillingOrderChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    split_charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                             verbose_name='拆分合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(CombineBillingOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='combineBillingOrderChargeOuts')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')

    class Meta:
        verbose_name = '合并计费单费用'
        verbose_name_plural = '合并计费单费用'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


# 多模型公用币种
# CURRENCY = [
#     ('CNY', 'CNY'),
#     ('USD', 'USD'),
#     ('GBP', 'GBP'),
#     ('EUR', 'EUR'),
#     ('HKD', 'HKD'),
#     ('CAD', 'CAD'),
#     ('CHF', 'CHF'),
#     ('AUD', 'AUD'),
# ]
CURRENCY = [
    ('AUD', 'AUD'),
    ('BGN', 'BGN'),
    ('CAD', 'CAD'),
    ('CHF', 'CHF'),
    ('CZK', 'CZK'),
    ('DKK', 'DKK'),
    ('EUR', 'EUR'),
    ('GBP', 'GBP'),
    ('HKD', 'HKD'),
    ('HRK', 'HRK'),
    ('HUF', 'HUF'),
    ('IDR', 'IDR'),
    ('ILS', 'ILS'),
    ('ISK', 'ISK'),
    ('JPY', 'JPY'),
    ('MKD', 'MKD'),
    ('MXN', 'MXN'),
    ('MYR', 'MYR'),
    ('NOK', 'NOK'),
    ('NZD', 'NZD'),
    ('PHP', 'PHP'),
    ('PLN', 'PLN'),
    ('RON', 'RON'),
    ('RUB', 'RUB'),
    ('SEK', 'SEK'),
    ('SGD', 'SGD'),
    ('THB', 'THB'),
    ('TRY', 'TRY'),
    ('TWD', 'TWD'),
    ('USD', 'USD'),
    ('VND', 'VND'),
    ('MOP', 'MOP'),
    ('CNY', 'CNY')
]

COUNTRY_CODE = [
    ('FR', '法国'),
    ('US', '美国'),
    ('ES', '⻄班⽛'),
    ('CZ', '捷克'),
    ('EE', '爱沙尼亚'),
    ('PT', '葡萄⽛'),
    ('IT', '意⼤利'),
    ('DE', '德国'),
    ('PL', '波兰'),
    ('NL', '荷兰'),
    ('RO', '罗⻢尼亚'),
    ('HR', '克罗地亚'),
    ('SI', '斯洛⽂尼亚'),
    ('BE', '⽐利时'),
    ('DK', '丹⻨'),
    ('LV', '拉脱维亚'),
    ('HU', '匈⽛利'),
    ('SK', '斯洛伐克'),
    ('IE', '爱尔兰'),
    ('BG', '保加利亚'),
    ('LT', '⽴陶宛'),
    ('FI', '芬兰'),
    ('SE', '瑞典'),
    ('AT', '奥地利'),
    ('GR', '希腊'),
    ('GB', '英国'),
    ('LU', '卢森堡'),
    ('MT', '⻢⽿他'),
    ('MX', '墨⻄哥'),
    ('CY', '塞浦路斯'),
    ('AU', '澳⼤利亚'),
    ('CA', '加拿⼤'),
    ('CH', '瑞⼠'),
    ('NO', '挪威'),
    ('MC', '摩纳哥'),
    ('IS', '冰岛'),
    ('IL', '以⾊列'),
    ('TR', '⼟⽿其'),
    ('LI', '列⽀敦⼠登'),
    ('MK', '前南⻢其顿'),
    ('SM', '圣⻢⼒诺'),
]


# 新保险单(旧版本)
class InsuranceOrder(BaseEntity):
    # 状态选项
    INSURANCE_STATUS_CHOICES = (
        ('DR', '草稿'),
        ('WO', '已提交'),
        ('audit_completed', '审核完成'),
        ('audit_fail', '审核失败'),
        ('processing', '投保成功'),
        ('soc_processing', '理赔中'),
        ('soc_completed', '理赔完成'),
        ('soc_fail', '理赔失败'),
        ('completed', '投保完成'),
        ('VO', '作废'),
    )
    # INSURANCE_COMPANY_CODE = [
    #     ("CPIC", "CPIC"),
    #     ("DIC", "DIC"),
    #     ("PICC", "PICC"),
    # ]
    PAYMENT_STATUS_CHOICES = [
        ('APPLIED', '已申请投保'),
        ('PAYING', '保费⽀付中'),
        ('SUCCESS', '⽀付成功'),
        ('FAIL', '⽀付失败'),
    ]

    # 保单关联信息
    product = models.ForeignKey(InsuranceProduct, on_delete=models.DO_NOTHING, verbose_name='产品', null=True,
                                blank=True)
    customer = models.ForeignKey(Company, verbose_name='客户', on_delete=models.DO_NOTHING, null=True)

    # 保单自定义计价信息
    is_revenue_lock = models.BooleanField(verbose_name='收入确认', default=False)
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    opera_date = models.DateField(verbose_name='操作时间', null=True, blank=True)
    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)

    income = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='收入')
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    gross_profit = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='毛利')
    gross_currency = models.CharField(max_length=10, verbose_name='毛利币种', null=True, blank=True)

    # 投保基础信息
    # 承保申请号
    trade_no = models.CharField(max_length=255, unique=True, verbose_name="交易号", null=True, blank=True,
                                db_index=True)

    insurance_status = models.CharField(max_length=50, choices=INSURANCE_STATUS_CHOICES, default='DR',
                                        verbose_name="投保状态")

    # insurance_company_no = models.CharField(max_length=100, verbose_name="保司编码", choices=INSURANCE_COMPANY_CODE, default='CPIC')  # new
    # insurance_options_code = models.CharField(max_length=100, verbose_name="保司方案代码", null=True, blank=True)
    # insurance_project_code = models.CharField(max_length=100, verbose_name="保司方案代码", null=True, blank=True)

    insure_start = models.DateTimeField(verbose_name="保险起期", null=True, blank=True)
    insured_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="保险金额", null=True,
                                         blank=True)
    insured_amount_currency = models.CharField(max_length=10, verbose_name='保额币种', null=True,
                                               blank=True)

    # 投保结果信息
    policy_inner_no = models.CharField(max_length=255, blank=True, null=True, verbose_name="交易流水号")
    notics_no = models.CharField(max_length=255, blank=True, null=True, verbose_name="保司通知单号")
    policy_no = models.CharField(max_length=255, blank=True, null=True, verbose_name="保单号")
    premium = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="保费")
    insure_end = models.DateTimeField(blank=True, null=True, verbose_name="保险终止日期")
    policy_url = models.CharField(max_length=255, blank=True, null=True, verbose_name="电子保单URL")

    # 元数据和字符串表示
    class Meta:
        verbose_name = "保险订单"
        verbose_name_plural = "保险订单"

    def __str__(self):
        return self.trade_no


# 保单人员信息
class InsuranceOrderPerson(BaseEntity):
    CERTTYPE = [
        ("RESIDENT", "身份证"),
        ("HOUSEHOLD", "户口薄"),
        ("PASSPORT", "护照"),
        ("MILITARY", "军官证"),
        ("SOLIDER", "⼠兵证"),
        ("DRIVING_LICENSE", "驾驶证"),
        ("HOME_VISIT_PERMIT_HK_MC", "港澳居⺠来往内地通⾏证"),
        ("HOME_VISIT_PERMIT_TAIWAN", "台湾居⺠来往⼤陆通⾏证"),
        ("FOREIGN_RESIDENT", "外国⼈居留证"),
        ("OTHER_INDIVIDUAL", "其他个⼈证件"),
        ("ORG_CERT", "全国组织机构代码证书"),
        ("UNIFIED_SOCIAL_CREDIT_CODE_CERTIFICATE", "统⼀社会信⽤代码"),
        ("TAX_REGIS_CERT", "税务登记证（⼆证合⼀）"),
        ("OTHER_LEGAL", "其他法⼈证件")
    ]
    PERSONTYPE = [
        ("APPLICANT", "投保人"),
        ("INSURED", "被保人"),
        ("BENEFICIARY", "受益人"),
    ]
    GENDER = [
        ("MAN", "男"),
        ("WOMAN", "女")
    ]

    customer_order_num = models.ForeignKey(InsuranceOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='insuranceOrderPersons')

    # 必传参数
    member_name = models.CharField(max_length=100, verbose_name="保单人员名称")
    cert_no = models.CharField(max_length=100, verbose_name="人员证件号码")
    cert_type = models.CharField(max_length=100, verbose_name="人员证件类型", choices=CERTTYPE, default="RESIDENT")
    person_type = models.CharField(max_length=50, verbose_name="保单人员类型", choices=PERSONTYPE, default="APPLICANT")

    # 非必传参数
    member_mobile_no = models.CharField(max_length=255, verbose_name="手机号", null=True, blank=True)
    gender = models.CharField(max_length=50, verbose_name="性别", choices=GENDER, null=True, blank=True)
    age = models.IntegerField(verbose_name="年龄", null=True, blank=True)
    birthday = models.DateField(verbose_name="出生日期", null=True, blank=True)

    class Meta:
        verbose_name = '保险订单-保单人员信息'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.member_name


class InsuranceObject(BaseEntity):
    customer_order_num = models.ForeignKey(InsuranceOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='insuranceObjects')

    trade_order_id = models.CharField(max_length=100, null=True, blank=True, verbose_name="主订单号")
    goods_type = models.CharField(max_length=100, null=True, blank=True, verbose_name="货物大类")
    goods_quantity = models.CharField(max_length=10, null=True, blank=True, verbose_name="货物数量")
    order_amount = models.CharField(max_length=10, null=True, blank=True, verbose_name="订单总金额")
    currency = models.CharField(max_length=10, null=True, blank=True, verbose_name="币种")
    consigner_name = models.CharField(max_length=100, null=True, blank=True, verbose_name="发货人/商家名称")
    seller_id = models.CharField(max_length=100, null=True, blank=True, verbose_name="商家ID")
    goods_start_address = models.CharField(max_length=255, null=True, blank=True, verbose_name="货物出发地地址")
    goods_start_country_code = models.CharField(max_length=5, null=True, blank=True, verbose_name="货物出发地国别")
    consignee_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="收货人名称")
    buyer_id = models.CharField(max_length=25, null=True, blank=True, verbose_name="买家ID")
    goods_end_address = models.CharField(max_length=255, null=True, blank=True, verbose_name="货物目的地地址")
    goods_end_country_code = models.CharField(max_length=25, null=True, blank=True, verbose_name="货物目的地国别")
    express_logistics_type = models.CharField(max_length=25, null=True, blank=True,
                                              verbose_name="承运人类型/快递物流类型")
    carrier_name = models.CharField(max_length=100, null=True, blank=True, verbose_name="承运人名称")
    waybill_no = models.CharField(max_length=100, null=True, blank=True, verbose_name="运单号")
    delivery_time = models.DateTimeField(null=True, blank=True, verbose_name="发货时间")

    warehouse_name = models.CharField(max_length=25, null=True, blank=True, verbose_name="海外仓名称")
    warehouse_id = models.CharField(max_length=25, null=True, blank=True, verbose_name="海外仓ID")

    class Meta:
        verbose_name = '保险标的内容'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.trade_order_id


class InsuranceOrderChargeOut(BaseEntity):
    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    is_share = models.BooleanField(verbose_name='分摊', default=False)
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    customer_order_num = models.ForeignKey(InsuranceOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='insuranceOrderChargeOuts')
    share_charge_id = models.CharField(max_length=200, verbose_name='分摊单号', null=True, blank=True)
    price_version = models.ForeignKey(InsuranceProudctVersion, verbose_name='成本价格版本', on_delete=models.DO_NOTHING,
                                      null=True)
    charge_value = models.CharField(max_length=100, verbose_name='计费值', null=True, blank=True)

    class Meta:
        verbose_name = "保险订单成本"
        verbose_name_plural = "保险订单成本"

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class InsuranceOrderChargeIn(BaseEntity):
    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    customer_order_num = models.ForeignKey(InsuranceOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='insuranceOrderChargeIns')

    price_version = models.ForeignKey(InsuranceProudctVersion, verbose_name='收入价格版本', on_delete=models.DO_NOTHING,
                                      null=True)
    charge_value = models.CharField(max_length=100, verbose_name='计费值', null=True, blank=True)
    is_deducted = models.BooleanField(verbose_name='是否已扣费', default=False)

    class Meta:
        verbose_name = "保险订单收入"
        verbose_name_plural = "保险订单收入"

    def __str__(self):
        return '记录收入 %s 金额 %s %s' % (self.charge, self.charge_total, self.currency_type)


class InsuranceOrderTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]
    order_num = models.ForeignKey(InsuranceOrder, on_delete=models.DO_NOTHING, null=True,
                                  related_name='insuranceOrderTasks')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='订单状态')
    label_desc = models.CharField(max_length=2048, verbose_name='下单描述', null=True, blank=True)
    third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True)
    product_code = models.CharField(max_length=100, verbose_name='产品编码', null=True, blank=True)
    warehouse_code = models.CharField(max_length=100, verbose_name='仓库编码', null=True, blank=True)
    shipment_digest = models.TextField(verbose_name='第三方digest', null=True, blank=True)
    product = models.ForeignKey(InsuranceProduct, on_delete=models.DO_NOTHING, verbose_name='产品', null=True,
                                blank=True)
    is_master_product = models.BooleanField(verbose_name='是否主产品', default=False)

    class Meta:
        verbose_name = "保险订单任务表"
        verbose_name_plural = "保险订单任务表"
        ordering = ['-id']

    def __str__(self):
        return self.order_num


# 旧保险单
class Insurance(BaseEntity):
    ORDER_STATUS = [
        ('DR', '草稿'),
        ('WO', '已提交'),
        ('FC', '下保成功'),
        ('VO', '作废'),
    ]
    AUTH_STATUS = [
        (0, '待审核'),
        (1, '审核通过'),
        (2, '审核拒绝'),
        (10, '已承保'),
    ]
    order_num = models.CharField(max_length=60, verbose_name='系统保单号', null=True, blank=True)
    status = models.CharField(max_length=5, verbose_name='订单状态', choices=ORDER_STATUS, default='DR')
    auth_status = models.CharField(max_length=5, verbose_name='保单状态', choices=AUTH_STATUS, default=None, null=True,
                                   blank=True)
    trackingNo = models.CharField(max_length=60, verbose_name='原单号')
    productCode = models.CharField(max_length=20, verbose_name='产品代码')
    insuredName = models.CharField(max_length=20, verbose_name='被保险人名称')
    cargoValue = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='货值')
    freight = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='运费', null=True, blank=True)
    freightCurrencyCode = models.CharField(max_length=5, verbose_name='运费币种', null=True, blank=True)
    baseAmountWay = models.CharField(max_length=6, verbose_name='保额确定方式')
    ratio = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='运费')
    currencyCode = models.CharField(max_length=10, verbose_name='保单币种')
    chargeableWeight = models.DecimalField(max_digits=12, decimal_places=4, verbose_name='实际重量(KG)')
    insuredRemark = models.CharField(max_length=1500, verbose_name='保单备注')
    transportModeCode = models.CharField(max_length=10, verbose_name='运输方式')
    transportTool = models.CharField(max_length=40, verbose_name='运输工具及航次')
    blNo = models.CharField(max_length=40, verbose_name='提单号/运单号')
    deliverywayCode = models.CharField(max_length=40, verbose_name='派送方式')
    expressCompanyCode = models.CharField(max_length=40, verbose_name='快递公司')
    expressNo = models.CharField(max_length=40, verbose_name='快递单号', null=True, blank=True)
    shipmentId = models.CharField(max_length=40, verbose_name='shipmentId', null=True, blank=True)
    packingCode = models.CharField(max_length=10, verbose_name='包装类型')
    packingQuantity = models.IntegerField(verbose_name='包装数量')
    cargoDesc = models.CharField(max_length=3000, verbose_name='货物描述')
    cargoCategoryCode = models.CharField(max_length=10, verbose_name='货物类别')
    departureDate = models.DateField(verbose_name='起运时间')
    departureCountryCode = models.CharField(max_length=50, verbose_name='起运国')
    departureAddress = models.CharField(max_length=50, verbose_name='起运地')
    destType = models.CharField(max_length=10, verbose_name='目的地类型（code）')
    destinationCountryCode = models.CharField(max_length=50, verbose_name='目的国')
    destinationAddress = models.CharField(max_length=50, verbose_name='目的地')
    shelf = models.BooleanField(verbose_name='是否上架保障', default=False)
    shelfName = models.CharField(max_length=100, verbose_name='上架地点')
    createUserName = models.CharField(max_length=30, verbose_name='操作人', null=True, blank=True)
    unrestRisk = models.BooleanField(verbose_name='暴动保障', default=None, null=True)
    amendmentReason = models.CharField(max_length=800, verbose_name='批改理由', default=None)
    # 被保人信息
    certificateType = models.CharField(max_length=10, verbose_name='被保人证件类型', default=None)
    certificateNo = models.CharField(max_length=24, verbose_name='被保人证件号码', default=None)
    mobile = models.CharField(max_length=20, verbose_name='被保险人联系电话', default=None)
    contactAddress = models.CharField(max_length=100, verbose_name='被保人联系地址', default=None)
    async_remark = models.CharField(max_length=1000, verbose_name='下单信息', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True, default=0)
    is_modify = models.BooleanField(verbose_name='是否需要修改', default=False)
    insurance_orders = models.CharField(verbose_name='投保订单', max_length=5000, default='', null=True, blank=True)
    # order = models.ForeignKey(CustomerOrder, related_name='insurance', verbose_name='客户订单',
    #                           on_delete=models.DO_NOTHING, null=True)


# 客户订单附件
class OrderAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='customerOrder/%Y/%m/%d', null=True, blank=True,
                           verbose_name='客户订单附件')
    customerOrder = models.ForeignKey(CustomerOrder, related_name='attachments', verbose_name='客户订单',
                                      on_delete=models.DO_NOTHING, null=True)


# 签收单附件
class SignForOrderAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='customerOrder/%Y/%m/%d', null=True, blank=True,
                           verbose_name='签收单附件')
    customerOrder = models.ForeignKey(CustomerOrder, related_name='signForAttachments', verbose_name='客户订单',
                                      on_delete=models.DO_NOTHING, null=True)


# 主单附件
class MasterAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='masterOrder/%Y/%m/%d', null=True, blank=True,
                           verbose_name='主单附件')
    masterOrder = models.ForeignKey(MasterOrder, related_name='attachments', verbose_name='主单',
                                    on_delete=models.DO_NOTHING, null=True)


# 进口报关单附件
class ClearanceAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='clearanceOrder/%Y/%m/%d', null=True, blank=True,
                           verbose_name='进口报关单附件')
    clearanceOrder = models.ForeignKey(Clearance, related_name='attachments', verbose_name='进口报关单',
                                       on_delete=models.DO_NOTHING, null=True)


# 提单附件
class OceanAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='oceanOrder/%Y/%m/%d', null=True, blank=True,
                           verbose_name='主单附件')
    oceanOrder = models.ForeignKey(OceanOrder, related_name='attachments', verbose_name='提单',
                                   on_delete=models.DO_NOTHING, null=True)


# 卡车单附件
class TruckAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(
        upload_to='TruckOrder/%Y/%m/%d', null=True, blank=True,
        verbose_name='主单附件'
    )
    truckOrder = models.ForeignKey(
        TruckOrder, related_name='attachments', verbose_name='卡车单',
        on_delete=models.DO_NOTHING, null=True
    )


# 分单附件
class HouseAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='houseOrder/%Y/%m/%d', null=True, blank=True,
                           verbose_name='分单附件')
    houseOrder = models.ForeignKey(HouseOrder, related_name='attachments', verbose_name='分单',
                                   on_delete=models.DO_NOTHING, null=True)


# 出口报关单附件
class ClearanceOutAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='ClearanceOut/%Y/%m/%d', null=True, blank=True,
                           verbose_name='出口报关单附件')
    clearance_out = models.ForeignKey(ClearanceOut, related_name='attachments', verbose_name='出口报关单',
                                      on_delete=models.DO_NOTHING, null=True)


# 出口报关单-预录单
class ClearanceOutPreRecordingAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='预录单名称')
    url = models.FileField(upload_to='ClearanceOutPreRecording/%Y/%m/%d', null=True, blank=True,
                           verbose_name='出口预录单附件')
    clearance_out = models.ForeignKey(ClearanceOut, related_name='pre_recording_attachments', verbose_name='出口预录单',
                                      on_delete=models.DO_NOTHING, null=True)


# 出口报关单-放行单
class ClearanceOutReleaseAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='放行单名称')
    url = models.FileField(upload_to='ClearanceOutRelease/%Y/%m/%d', null=True, blank=True,
                           verbose_name='出口放行单附件')
    clearance_out = models.ForeignKey(ClearanceOut, related_name='release_attachments', verbose_name='出口放行单',
                                      on_delete=models.DO_NOTHING, null=True)


# class TruckRelation(BaseEntity):
#     truck_id = models.ForeignKey(TruckOrder, on_delete=models.CASCADE)
#     customer_order_id = models.ForeignKey(CustomerOrder, on_delete=models.CASCADE)
#     carton = models.IntegerField(verbose_name='总件数', default=0)
#     weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='重量')
#     volume = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='体积')
#     charge_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='计费重量')
#     trans_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='转换率')


class CustomerOrderOperate(CustomerOrder):
    class Meta:
        proxy = True
        verbose_name = '作业订单'
        verbose_name_plural = verbose_name


class ExpressOrder(BaseEntity):
    EXPRESS_STATUS = [
        ('DR', '草稿'),
        ('SM', '已提交'),
        ('ML', '跨国运输'),
        ('OW', '派送中'),
        ('DF', '派送完成'),
        ('FC', '订单完成'),
        ('VO', '作废'),
    ]
    expressOrder_num = models.CharField(max_length=100, verbose_name='快递单号', null=True, blank=True)
    tracking_num = models.CharField(max_length=100, verbose_name='跟踪号', null=True, blank=True)
    express_status = models.CharField(max_length=10, verbose_name='状态', choices=EXPRESS_STATUS, null=True, blank=True,
                                      default='SM')
    service_code = models.ForeignKey(Service, verbose_name='供应商服务', null=True, blank=True,
                                     on_delete=models.DO_NOTHING)
    shipper_address = models.ForeignKey(Address, verbose_name='发件人地址', null=True, blank=True,
                                        on_delete=models.DO_NOTHING, related_name='shipper_address')
    receiver_address = models.ForeignKey(Address, verbose_name='收件人地址', null=True, blank=True,
                                         on_delete=models.DO_NOTHING, related_name='receiver_address')
    supplier = models.ForeignKey(Company, verbose_name='供应商', null=True, blank=True, on_delete=models.DO_NOTHING)
    charge_trans = models.FloatField(verbose_name='计费转换', null=True, default=6000)
    customer_order = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True, blank=True,
                                       verbose_name='客户订单号')

    class Meta:
        verbose_name_plural = '快递单'
        verbose_name = '快递单'

    def __str__(self):
        return self.expressOrder_num


# 完整商品清单(订单包裹)
class Parcel(BaseEntity):
    STATUS = [
        ('NO_PUSH', '不推送'),
        ('WAIT_PUSH', '待推送'),
        ('PUSHED', '推送完成'),
        ('PUSH_FAIL', '推送失败'),
    ]
    PULL_STATUS = [
        ('NO_PULL', '不拉取'),
        ('WAIT_PULL', '待拉取'),
        ('PULL_FINISH', '拉取完成'),
        ('PULL_FAIL', '拉取失败'),
    ]
    ORDER_TYPE = [
        ('TR', '运输单'),
        ('FBA', 'FBA订单'),
        ('GL', '快递派送单')
    ]

    # 子单号
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True)
    sys_parcel_num = models.CharField(max_length=50, verbose_name='系统包裹号', null=True, blank=True)
    customer_sys_parcel_num = models.CharField(max_length=50, verbose_name='客户系统包裹号', null=True, blank=True)
    reference_id = models.CharField(max_length=64, verbose_name='Reference Id', null=True, blank=True)
    shipment_id = models.CharField(max_length=64, verbose_name='Shipment Id', null=True, blank=True)
    parcel_desc = models.CharField(max_length=100, verbose_name='包裹描述', null=True, blank=True, default='0')
    parcel_length = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='长', null=True, blank=True)
    parcel_width = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='宽', null=True, blank=True)
    parcel_height = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='高', null=True, blank=True)
    parcel_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True,
                                        default=0)
    label_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='打单重量', null=True, blank=True,
                                       default=0)
    label_length = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='打单长', null=True, blank=True)
    label_width = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='打单宽', null=True, blank=True)
    label_height = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='打单高', null=True, blank=True)

    debit_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='账单重量', null=True, blank=True,
                                       default=0)
    parcel_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
                                        default=0)
    actual_length = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='实际长', null=True, blank=True)
    actual_width = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='实际宽', null=True, blank=True)
    actual_height = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='实际高', null=True, blank=True)
    actual_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='实际重量', null=True, blank=True,
                                        default=0)
    actual_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='实际体积', null=True, blank=True,
                                        default=0)
    parcel_confirm_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='确认计费重', null=True,
                                                blank=True)
    parcel_confirm_volume = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='确认计费体积', null=True,
                                                blank=True)
    # 转单号
    tracking_num = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True, db_index=True)
    customer_order = models.ForeignKey(CustomerOrder, related_name='parcel', on_delete=models.DO_NOTHING,
                                       verbose_name='客户订单号', null=True,
                                       blank=True)
    parcel_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True, default=1)
    check_date = models.CharField(max_length=15, verbose_name='测量日期', null=True, blank=True)
    check_num = models.CharField(max_length=64, verbose_name='测量编号', null=True, blank=True)
    check_times = models.IntegerField(verbose_name='测量次数', default=0)
    parcel_picture = models.FileField(upload_to='Parcel/%Y/%m/%d', null=True, blank=True, verbose_name='包裹图片')
    voucher = models.FileField(upload_to='ParcelVoucher/%Y/%m/%d', null=True, blank=True, verbose_name='导入的面单')
    is_weighing = models.BooleanField(verbose_name='是否核重', default=False)
    intercept_mark = models.BooleanField(verbose_name='拦截标记', default=False)
    # 增加墨家扫描上传的图片
    mojia_img = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='图片')
    # 增加当前包裹是否出入仓
    in_warehouse = models.BooleanField(verbose_name='是否入仓', default=0)
    out_warehouse = models.BooleanField(verbose_name='是否出仓', default=0)
    # 绑定海运提单
    ocean_order = models.ForeignKey(OceanOrder, related_name='parcel', verbose_name='海运提单',
                                    on_delete=models.DO_NOTHING, null=True, blank=True)
    # 转单给17track后的推送状态和拉取状态
    push_status_17 = models.CharField(max_length=30, choices=STATUS, verbose_name='17track推送状态', default='NO_PUSH',
                                      null=True, blank=True)
    pull_status_17 = models.CharField(max_length=30, choices=PULL_STATUS, verbose_name='17track拉取状态',
                                      default='NO_PULL', null=True, blank=True)
    is_overlength = models.BooleanField(verbose_name='是否超长', default=False, null=True, blank=True)
    is_overweight = models.BooleanField(verbose_name='是否超重', default=False, null=True, blank=True)
    is_ten_carry = models.BooleanField(verbose_name='是否10kg进位', default=False, null=True, blank=True)
    # 17track需要物流商编码
    courier_code = models.CharField(max_length=100, verbose_name='物流商编码', blank=True, null=True)
    order_type = models.CharField(max_length=100, verbose_name='订单类型', choices=ORDER_TYPE, default='FBA')
    shop_type = models.CharField(max_length=100, verbose_name='店铺类型', null=True, blank=True)
    parcel_unit = models.CharField(max_length=30, verbose_name='包裹单位', null=True, blank=True)
    ascan_time = models.DateTimeField(verbose_name='ascan时间', null=True, blank=True)
    dscan_time = models.DateTimeField(verbose_name='dscan时间', null=True, blank=True)
    charge_weight = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='计费重', null=True, blank=True)
    confirm_charge_weight = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='确认计费重', null=True,
                                                blank=True)
    income = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='包裹收入')
    tracking_id = models.CharField(max_length=64, verbose_name='亚马逊唯一码', null=True, blank=True, db_index=True)

    class Meta:
        indexes = [
            models.Index(fields=['parcel_num', 'del_flag']),
            models.Index(fields=['sys_parcel_num', 'del_flag']),
            models.Index(fields=['customer_sys_parcel_num', 'del_flag']),
            models.Index(fields=['tracking_num', 'del_flag']),
            models.Index(fields=['customer_order', 'del_flag']),
            models.Index(fields=['reference_id', 'del_flag']),
        ]
        verbose_name = '包裹'
        verbose_name_plural = '包裹'

    def __str__(self):
        return self.parcel_num


# 包裹附件
class ParcelAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='Parcel/%Y/%m/%d', null=True, blank=True, verbose_name='订单包裹附件')
    parcel = models.ForeignKey(Parcel, related_name='attachment_parcel', verbose_name='订单包裹',
                               on_delete=models.DO_NOTHING, null=True)


# 客户订单包裹商品表
class ParcelItem(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    item_code = models.CharField(max_length=256, verbose_name='物品号', null=True, blank=True, default='0')
    item_name = models.CharField(max_length=256, verbose_name='物品名称', null=True, blank=True, default='0')
    sale_currency = models.CharField(max_length=10, verbose_name='报价币种', default='CNY', null=True,
                                     blank=True)
    sale_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='销售价格', null=True, blank=True,
                                     default=1)
    declared_currency = models.CharField(max_length=10, verbose_name='申报币种', default='USD',
                                         null=True,
                                         blank=True)
    declared_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='申报价格', null=True,
                                         blank=True,
                                         default=1)
    declared_nameCN = models.CharField(max_length=256, verbose_name='中文申报品名', null=True, blank=True)
    declared_nameEN = models.CharField(max_length=256, verbose_name='英文申报品名', null=True, blank=True)
    item_length = models.FloatField(verbose_name='长', null=True, blank=True, default=1)
    item_width = models.FloatField(verbose_name='宽', null=True, blank=True, default=1)
    item_height = models.FloatField(verbose_name='高', null=True, blank=True, default=1)
    item_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
                                      default=0.000001)
    item_weight = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='重量', null=True, blank=True,
                                      default=0.001)
    # 数量PCS
    item_qty = models.IntegerField(verbose_name='数量', null=True, blank=True, default=1)
    texture = models.CharField(max_length=255, verbose_name='材质', null=True, blank=True)
    item_size = models.CharField(max_length=50, verbose_name='尺寸', null=True, blank=True)
    use = models.CharField(max_length=255, verbose_name='用途', null=True, blank=True)
    brand = models.CharField(max_length=50, verbose_name='品牌', null=True, blank=True)
    model = models.CharField(max_length=50, verbose_name='型号', null=True, blank=True)
    customs_code = models.CharField(max_length=50, verbose_name='海关编码', null=True, blank=True)
    tax_rate = models.DecimalField(decimal_places=4, max_digits=10, verbose_name='最新税率', null=True, blank=True)
    fba_no = models.CharField(max_length=50, verbose_name='FBA号', null=True, blank=True)
    fba_track_code = models.CharField(max_length=50, verbose_name='FBA货物追踪编号', null=True, blank=True)
    parcel_num = models.ForeignKey(Parcel, related_name='parcelItem', verbose_name='包裹号',
                                   on_delete=models.DO_NOTHING,
                                   null=True, blank=True)
    item_picture = models.FileField(upload_to='ParcelItem/%Y/%m/%d', null=True, blank=True, max_length=512,
                                    verbose_name='商品图片')
    is_electric = models.BooleanField(verbose_name='是否带电', default=False)
    is_magnetic = models.BooleanField(verbose_name='是否带磁', default=False)
    # 报关方式
    custom_clearance = models.CharField(max_length=50, verbose_name='报关方式', null=True, blank=True)

    # FBM 原始SKU对应的包裹信息
    shipment_id = models.CharField(max_length=100, verbose_name='货件号', null=True, blank=True)
    box_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True, default=0)
    combined_parcel_num = models.CharField(max_length=50, verbose_name='合箱箱号', null=True, blank=True)
    sku_url = models.CharField(max_length=512, verbose_name='商品链接', null=True, blank=True)

    class Meta:
        verbose_name = '物品'
        verbose_name_plural = '物品'

    def __str__(self):
        return self.item_code


# 简易商品清单
class ParcelSize(BaseEntity):
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True, db_index=True)
    parcel_length = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='长', null=True, blank=True)
    parcel_width = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='宽', null=True, blank=True)
    parcel_height = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='高', null=True, blank=True)
    parcel_weight = models.DecimalField(
        max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True,
        default=0.0001
    )
    actual_length = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='实际长', null=True, blank=True)
    actual_width = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='实际宽', null=True, blank=True)
    actual_height = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='实际高', null=True, blank=True)
    actual_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='实际重量', null=True, blank=True,
                                        default=0)
    actual_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='实际体积', null=True, blank=True,
                                        default=0)
    parcel_volume = models.DecimalField(
        max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
        default=0.000001
    )
    customer_order = models.ForeignKey(
        CustomerOrder, related_name='parcel_size', on_delete=models.DO_NOTHING,
        verbose_name='客户订单号', null=True,
        blank=True
    )
    parcel_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True)
    check_date = models.CharField(max_length=15, verbose_name='测量日期', null=True, blank=True)
    check_num = models.CharField(max_length=64, verbose_name='测量编号', null=True, blank=True)
    check_times = models.IntegerField(verbose_name='测量次数', default=0)

    class Meta:
        verbose_name = '包裹'
        verbose_name_plural = '包裹'

    def __str__(self):
        return '简易包裹'


# 大包拣货记录
class BigPickRecord(BaseEntity):
    TYPE = [
        ('WEIGHING', '称重'),
        ('UPDATE', '更新'),
        ('REPLACE_LABEL', '换标'),
        ('EX_WAREHOUSE', '出仓'),
        ('CANCEL_EX_WAREHOUSE', '取消出仓'),
    ]
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True, db_index=True)
    order_num = models.CharField(max_length=30, verbose_name='运单号', null=True, blank=True, db_index=True)
    user = models.ForeignKey(UserProfile, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='操作员')
    length = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='长', null=True, blank=True)
    width = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='宽', null=True, blank=True)
    height = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='高', null=True, blank=True)
    weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True)
    error_msg = models.CharField(max_length=100, verbose_name='错误信息')
    type = models.CharField(max_length=30, choices=TYPE, null=True, default='WEIGHING', verbose_name='记录类型')
    chute = models.CharField(max_length=50, verbose_name='物流猫出格口', null=True, blank=True, db_index=True)
    # 对于包裹出仓需要记录对应的海运提单
    ocean_order = models.ForeignKey(OceanOrder, verbose_name='提单号', null=True, blank=True,
                                    on_delete=models.DO_NOTHING)

    class Meta:
        indexes = [
            models.Index(fields=['parcel_num', 'del_flag']),
        ]
        verbose_name = '打包称重记录'
        verbose_name_plural = '打包称重记录'


class MachinePassingRecord(BaseEntity):
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True, db_index=True)
    order_num = models.CharField(max_length=30, verbose_name='运单号', null=True, blank=True, db_index=True)
    chute = models.CharField(max_length=50, verbose_name='物流猫出格口', null=True, blank=True, db_index=True)
    is_finish = models.BooleanField(verbose_name='是否完成', default=False)
    pass_times = models.IntegerField(verbose_name='过机次数', null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['order_num', 'parcel_num', 'del_flag']),
        ]
        verbose_name = '过机记录表'
        verbose_name_plural = '过机记录表'


class ExpressOrderChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge_name = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(ExpressOrder, on_delete=models.DO_NOTHING, null=True)

    class Meta:
        verbose_name = '快递单费用'
        verbose_name_plural = '快递单费用'

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge_name, self.charge_total, self.currency_type)


# 客户订单成本
class CustomerOrderChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    UNIT = [
        ('weight', '重量'),
        ('volume', '体积'),
        ('chargeWeight', '计费重'),
        ('confirmChargeWeight', '确认计费重'),
        ('carton', '件数'),
        ('SKUCarton', 'SKU预报件数'),
        ('SKUShelfCarton', 'SKU上架件数'),
        ('SKUCategories', 'SKU 种类数量'),
        ('oneSKUWeight', '单件sku重量'),
        ('newSkuQuantity', 'SKU 新品数量'),
        ('oneParcelSKUCarton', '单件包裹sku件数'),
        ('mix', '混合'),
        ('combine', '组合收费'),
        ('boxWeight', '箱数重量'),
        ('confirmChargeVolume', '确认计费体积'),
        ('halfDisposal', '半抛'),
        ('numberOfStrokes', '打板数'),
        ('numberOfTrucks', '用车数'),
        ('bubble', '泡比'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用')
    charge_unit = models.CharField(max_length=64, choices=UNIT, verbose_name='计费条件', default='weight',
                                   null=True, blank=True)
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=15, decimal_places=6, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='customerOrderChargeOuts')
    is_share = models.BooleanField(verbose_name='分摊', default=False)
    share_charge_id = models.CharField(max_length=200, verbose_name='分摊单号', null=True, blank=True)
    base_price = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='基础价')
    charge_price = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='计费价')

    # 后面废弃is_system 使用 data_source
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    data_source = models.CharField(max_length=2, choices=SOURCE_TYPE, verbose_name='数据来源', default="S")
    charging_time = models.DateTimeField(verbose_name='计费时间', null=True, blank=True)
    charging_node = models.CharField(max_length=10, verbose_name='计费节点', null=True, blank=True)

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


# 客户订单收入
class CustomerOrderChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    UNIT = [
        ('weight', '重量'),
        ('volume', '体积'),
        ('chargeWeight', '计费重'),
        ('confirmChargeWeight', '确认计费重'),
        ('carton', '件数'),
        ('SKUCarton', 'SKU预报件数'),
        ('SKUShelfCarton', 'SKU上架件数'),
        ('SKUCategories', 'SKU 种类数量'),
        ('newSkuQuantity', 'SKU 新品数量'),
        ('oneSKUWeight', '单件sku重量'),
        ('oneParcelSKUCarton', '单件包裹sku件数'),
        ('mix', '混合'),
        ('combine', '组合收费'),
        ('boxWeight', '箱数重量'),
        ('confirmChargeVolume', '确认计费体积'),
        ('halfDisposal', '半抛'),
        ('numberOfStrokes', '打板数'),
        ('numberOfTrucks', '用车数'),
        ('bubble', '泡比'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_unit = models.CharField(max_length=64, choices=UNIT, verbose_name='计费条件', default='weight',
                                   null=True, blank=True)
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    # 需要开放到6位小数, 因为涉及到分摊比例, 数量就是分摊比例, 除不尽
    charge_count = models.DecimalField(max_digits=15, decimal_places=6, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    is_share = models.BooleanField(verbose_name='分摊', default=False)
    share_charge_id = models.CharField(max_length=200, verbose_name='分摊单号', null=True, blank=True)
    customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='customerOrderChargeIns')
    # 递增价计算中, 用于保存基础价格
    base_price = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='基础价格')
    # 计费走产品销售定价时, 用于保存产品销售定价的公布价
    published_price = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='公布价')
    # 计费走收入价格明细时, 用于保存收入价格明细计算时的价格, 后面用户自己改了费用项的话这一项也不变
    charge_price = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='计费价')
    published_account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                                   verbose_name='公布价记账金额')

    price_version = models.ForeignKey(ProductRevenueVersion, verbose_name='收入价格版本', on_delete=models.DO_NOTHING,
                                      null=True)
    charge_weight = models.CharField(max_length=100, verbose_name='计费重', null=True, blank=True)
    charge_value = models.CharField(max_length=100, verbose_name='计费值', null=True, blank=True)
    # 后面废弃is_system 使用 data_source
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    data_source = models.CharField(max_length=2, choices=SOURCE_TYPE, verbose_name='数据来源', default="S")
    is_deducted = models.BooleanField(verbose_name='是否已扣费', default=False)
    charging_time = models.DateTimeField(verbose_name='计费时间', null=True, blank=True)
    charging_node = models.CharField(max_length=10, verbose_name='计费节点', null=True, blank=True)

    def __str__(self):
        return '记录收入 %s 金额 %s %s' % (self.charge, self.charge_total, self.currency_type)


class CustomerOrderChargeRecord(BaseEntity):
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True)
    order_num = models.CharField(max_length=30, verbose_name='订单号', null=True, blank=True, db_index=True)
    charge_in = models.ForeignKey(CustomerOrderChargeIn, on_delete=models.DO_NOTHING, null=True,
                                  verbose_name='费用记录')
    receive_goods_price = models.CharField(max_length=100, verbose_name='收货价', null=True, blank=True)

    def __str__(self):
        return '包裹收入记录 %s %s %s' % (self.charge_in, self.order_num, self.parcel_num)


class ParcelTrack(BaseEntity):
    STATUS = [
        ('NO_PUSH', '不推送'),
        ('WAIT_PUSH', '待推送'),
        ('PUSHED', '推送完成'),
        ('PUSH_FAIL', '推送失败'),
    ]
    PULL_STATUS = [
        ('WAIT_PULL', '待拉取'),
        ('NOT_PULL', '不拉取'),
        ('PULL_FINISH', '拉取完成'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    system_code = models.CharField(max_length=20, verbose_name='系统轨迹编码', null=True, blank=True)
    track_code = models.CharField(max_length=200, verbose_name='轨迹代码', blank=True, null=True)
    track_sub_code = models.CharField(max_length=100, verbose_name='子轨迹代码', blank=True, null=True)
    track_name = models.CharField(max_length=200, verbose_name='轨迹说明', blank=True, null=True)
    location = models.CharField(max_length=200, verbose_name='位置', blank=True, null=True)
    actual_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)
    timezone_offset = models.CharField(verbose_name='时区偏移量', max_length=6, default=settings.DEFAULT_TIMEZONE_OFFSET,
                                       choices=settings.TIMEZONE_OFFSET, null=True, blank=True)
    order_num = models.CharField(max_length=100, verbose_name='订单号')
    push_status = models.CharField(max_length=30, choices=STATUS, verbose_name='推送状态', default='NO_PUSH', null=True,
                                   blank=True)
    push_time = models.DateTimeField(verbose_name='推送时间', null=True, blank=True)

    pull_status = models.CharField(max_length=30, choices=PULL_STATUS, verbose_name='拉取状态', default='WAIT_PULL',
                                   null=True, blank=True)
    pull_times = models.IntegerField(verbose_name='拉单次数', default=0)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', default=1)
    courier_code = models.CharField(max_length=100, verbose_name='尾程物流商编码', blank=True, null=True)

    track_num = models.CharField(max_length=100, verbose_name='跟踪号', blank=True, null=True)
    push_result = models.CharField(max_length=100, verbose_name='推送结果', blank=True, null=True)

    is_show_supplier_track_code = models.BooleanField(verbose_name='是否显示供应商轨迹编码', default=True, null=True,
                                                      blank=True)
    supplier_track_code = models.CharField(verbose_name='供应商轨迹编码', max_length=100, blank=True, null=True)
    track_description = models.CharField(verbose_name='供应商轨迹描述', max_length=1000, blank=True, null=True)
    track_en_description = models.CharField(verbose_name='供应商轨迹英文描述', max_length=1000, blank=True, null=True)
    supplier_track_sub_code = models.CharField(verbose_name='供应商轨迹子代码', max_length=100, blank=True, null=True)
    track_sub_description = models.CharField(verbose_name='供应商轨迹子描述', max_length=1000, blank=True, null=True)
    track_sub_en_description = models.CharField(verbose_name='供应商轨迹子英文描述', max_length=1000, blank=True,
                                                null=True)
    status = models.CharField(max_length=10, choices=STATUS_TYPE, verbose_name='状态', default='DR', blank=True,
                              null=True)
    is_pushed_wish = models.BooleanField(verbose_name="是否已经推送给 wish", default=False)

    parcel_num = models.CharField(verbose_name='大包单包裹号', max_length=100, blank=True, null=True)
    outbound_num = models.CharField(verbose_name='交干批次号', max_length=100, blank=True, null=True)
    line_id = models.IntegerField(verbose_name='线路ID', blank=True, null=True)
    service_id = models.IntegerField(verbose_name='资源ID', blank=True, null=True)
    supplier_id = models.IntegerField(verbose_name='供应商ID', blank=True, null=True)
    customer_id = models.IntegerField(verbose_name='客户ID', blank=True, null=True)
    customer_num = models.CharField(verbose_name='客户单号', max_length=100, blank=True, null=True)
    buyer_postcode = models.CharField(verbose_name='收货邮编', max_length=100, blank=True, null=True)
    warehouse_code = models.CharField(verbose_name='仓库编码', max_length=100, blank=True, null=True)
    is_api = models.BooleanField(verbose_name="轨迹来源是否是API获取的", default=False)

    class Meta:
        indexes = [
            models.Index(fields=['track_code', 'actual_time', 'del_flag', 'pull_times']),
            models.Index(fields=['order_num', 'del_flag']),
            models.Index(fields=['create_date', 'del_flag', 'push_status']),
        ]
        verbose_name_plural = '小包轨迹'
        verbose_name = '小包轨迹'

    def __str__(self):
        if self.track_code:
            return self.track_code
        else:
            return ''


class ParcelTrackBI(BaseEntity):
    order_num = models.CharField(max_length=30, verbose_name='订单号')
    tracking_num = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True)
    customer_name = models.CharField(max_length=60, verbose_name='客户', null=True, blank=True)
    service_name = models.CharField(max_length=30, verbose_name='客服', null=True, blank=True)
    dest_country = models.CharField(max_length=10, verbose_name='目的国', null=True, blank=True)
    track_supplier = models.CharField(max_length=50, verbose_name='轨迹供应商', null=True, blank=True)

    track_code = models.CharField(max_length=200, verbose_name='最新轨迹代码', blank=True, null=True)
    track_sub_code = models.CharField(max_length=100, verbose_name='最新子轨迹代码', blank=True, null=True)
    track_name = models.CharField(max_length=200, verbose_name='最新轨迹说明', blank=True, null=True)
    location = models.CharField(max_length=200, verbose_name='最新位置', blank=True, null=True)
    actual_time = models.DateTimeField(verbose_name='最新轨迹时间', null=True, blank=True)
    latest_time = models.DateTimeField(verbose_name='最新更新时间', null=True, blank=True)
    inbound_time = models.DateTimeField(verbose_name='入库时间', null=True, blank=True)
    delivered_time = models.DateTimeField(verbose_name='签收时间', null=True, blank=True)
    request_date = models.DateTimeField(verbose_name='请求时间', null=True, blank=True)
    courier_code = models.CharField(max_length=100, verbose_name='尾程物流商编码', blank=True, null=True)
    pickup_040_time = models.DateTimeField(verbose_name='已上网时间', null=True, blank=True)
    pickup_010_time = models.DateTimeField(verbose_name='派送中时间', null=True, blank=True)
    arrival_port_time = models.DateTimeField(verbose_name='干线抵达时间', null=True, blank=True)
    departure_port_time = models.DateTimeField(verbose_name='干线离开时间', null=True, blank=True)
    export_declaration_time = models.DateTimeField(verbose_name='出口报关时间', null=True, blank=True)
    outbound_time = models.DateTimeField(verbose_name='出库时间', null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['order_num', 'del_flag']),
            models.Index(fields=['inbound_time', 'del_flag']),
            models.Index(fields=['delivered_time', 'del_flag']),
            models.Index(fields=['actual_time', 'del_flag']),
        ]
        verbose_name_plural = '轨迹BI统计'
        verbose_name = '轨迹BI统计'

    def __str__(self):
        if self.track_code:
            return self.track_code
        else:
            return ''


# FBA订单下的包裹的轨迹(目前仅包裹转单后才会有包裹轨迹, 转单前都是订单轨迹)(fba订单包裹轨迹)
class CustomerParcelTrack(BaseEntity):
    PUSH_STATUS = [
        ('NO_PUSH', '不推送'),
        ('WAIT_PUSH', '待推送'),
        ('PUSHED', '推送完成'),
        ('PUSH_FAIL', '推送失败'),
    ]
    PULL_STATUS = [
        ('WAIT_PULL', '待拉取'),
        ('NOT_PULL', '不拉取'),
        ('PULL_FINISH', '拉取完成'),
    ]
    track_code = models.CharField(max_length=100, verbose_name='轨迹代码', blank=True, null=True)
    track_name = models.CharField(max_length=100, verbose_name='轨迹说明', blank=True, null=True)
    location = models.CharField(max_length=200, verbose_name='位置', blank=True, null=True)
    actual_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)
    parcel_num = models.CharField(max_length=100, verbose_name='包裹号')
    push_status = models.CharField(max_length=30, choices=PUSH_STATUS, verbose_name='推送状态', default='NO_PUSH',
                                   null=True, blank=True)
    pull_status = models.CharField(max_length=30, choices=PULL_STATUS, verbose_name='拉取状态', default='WAIT_PULL',
                                   null=True, blank=True)
    pull_times = models.IntegerField(verbose_name='拉单次数', default=0)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', default=1)

    class Meta:
        indexes = [
            models.Index(fields=['track_code', 'actual_time', 'del_flag']),
            models.Index(fields=['parcel_num', 'del_flag']),
        ]
        verbose_name_plural = '订单下包裹的轨迹'
        verbose_name = '订单下包裹的轨迹'

    def __str__(self):
        return self.track_code or ''


# 订单轨迹表
class Track(BaseEntity):
    CLEAR_TYPE = [
        ('MS', '空运主单'),
        ('OC', '海运提单'),
        ('CO', '揽收单'),
        ('TO', '卡派单'),
    ]
    NODE_TYPE = [
        ('A', '添加节点'),
        ('D', '删除节点'),
    ]
    order_id = models.IntegerField(verbose_name='订单id', null=True, blank=True)
    qty = models.IntegerField(verbose_name='订单件数', null=True, blank=True)
    expected_qty = models.IntegerField(verbose_name='订单预计件数', null=True, blank=True)
    order_num = models.CharField(max_length=100, verbose_name='订单号')
    master_order_num = models.CharField(max_length=100, verbose_name='空运主单号', null=True, blank=True)
    airline_num = models.CharField(max_length=100, verbose_name='航班号', null=True, blank=True)
    ocean_order_num = models.CharField(max_length=100, verbose_name='海运提单号', null=True, blank=True)
    collect_order_num = models.CharField(max_length=100, verbose_name='揽收单号', null=True, blank=True)
    container_no = models.CharField(max_length=100, verbose_name='柜号', null=True, blank=True)
    track_code = models.CharField(max_length=100, verbose_name='轨迹代码', blank=True, null=True)  # 节点
    node_type = models.CharField(max_length=10, choices=NODE_TYPE, verbose_name='节点类型', null=True,
                                 blank=True)
    track_name = models.CharField(max_length=100, verbose_name='轨迹说明', blank=True, null=True)
    clear_type = models.CharField(max_length=10, choices=CLEAR_TYPE, verbose_name='进口报关单类型', null=True,
                                  blank=True)
    expected_time = models.DateTimeField(verbose_name='预计时间', null=True, blank=True)
    actual_time = models.DateTimeField(verbose_name='实际时间', null=True, blank=True)  # 时间
    product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True)
    location = models.CharField(max_length=100, verbose_name='位置', blank=True, null=True)
    location_en = models.CharField(max_length=100, verbose_name='位置英文', blank=True, null=True)
    # 亚马逊轨迹映射唯一码
    tracking_id = models.CharField(max_length=64, verbose_name='亚马逊唯一码', null=True, blank=True, db_index=True)

    class Meta:
        indexes = [
            models.Index(fields=['order_num', 'del_flag']),
        ]
        verbose_name = '订单轨迹表'
        verbose_name_plural = '订单轨迹表'


class MasterOrderTrack(BaseEntity):
    # TRACK_CODE = [
    #     ('PU', '卡车揽收'),
    #     ('AW', '集货仓收货'),
    #     ('AA', '运输路线确认'),
    #     ('EI', '出境清关查验'),
    #     ('EC', '出境清关完成'),
    #     ('TS', '国际运输出港'),
    #     ('CI', '入境清关查验'),
    #     ('CF', '入境清关完成'),
    #     ('TR', '卡车转运'),
    #     ('HS', '派送中'),
    #     ('DC', '派送完成'),
    #     ('CL', '订单取消'),
    # ]
    track_code = models.CharField(max_length=20, verbose_name='轨迹代码', blank=True, null=True)
    track_name = models.CharField(max_length=20, verbose_name='轨迹名称', blank=True, null=True)
    location = models.CharField(max_length=200, verbose_name='位置', blank=True, null=True)
    operation_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)
    master_order_num = models.ForeignKey(MasterOrder, related_name='masterOrderTrack', on_delete=models.DO_NOTHING,
                                         null=True, verbose_name='主单号')


class OrderLabelTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('HandledBy3rdNo', '已提交'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]

    SYNC_STATUS = [
        ('NS', '不同步'),
        ('WS', '待同步'),
        ('FC', '同步完成')
    ]

    order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True, related_name='orderLabelTasks')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='订单状态')
    label_desc = models.CharField(max_length=2048, verbose_name='抓取面单描述', null=True, blank=True)
    third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True)
    product_code = models.CharField(max_length=100, verbose_name='产品编码', null=True, blank=True)
    warehouse_code = models.CharField(max_length=100, verbose_name='仓库编码', null=True, blank=True)
    parcel = models.ForeignKey(Parcel, verbose_name='包裹号', on_delete=models.DO_NOTHING, null=True, blank=True)
    shipment_digest = models.TextField(verbose_name='第三方digest', null=True, blank=True)
    is_submit_order = models.BooleanField(verbose_name='是否已提交订单', default=False)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    is_master_product = models.BooleanField(verbose_name='是否主产品', default=False)
    is_need_confirm = models.BooleanField(verbose_name='是否需要确认', default=False)
    sync_status = models.CharField(max_length=3, verbose_name='是否同步完成', choices=SYNC_STATUS, default="NS")

    class Meta:
        verbose_name_plural = '订单面单任务表'
        verbose_name = '订单面单任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


class OrderScanFormTask(BaseEntity):
    """订单ScanForm任务"""
    STATUS = [
        ('UnHandled', '未处理'),
        ('HandledBy3rdNo', '处理中'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]

    TYPE = [
        ('api', 'api'),
        ('web', 'web'),
    ]
    reference_num = models.CharField(max_length=30, verbose_name='批次号', blank=True, null=True, unique=True)
    master_air_waybill_number = models.CharField(max_length=30, verbose_name='航空主单号', null=True, blank=True,
                                                 db_index=True)
    handover_num = models.CharField(max_length=30, verbose_name='交接批次号', null=True, blank=True, db_index=True)
    items = models.CharField(max_length=30, null=True, blank=True, verbose_name='运单数量')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='订单状态')
    request_data = models.JSONField(verbose_name='请求信息', null=True, blank=True)
    response_data = models.JSONField(verbose_name='响应信息', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True)
    scan_form_type = models.CharField(max_length=5, verbose_name='scan_form类型', choices=TYPE, null=True, blank=True)

    class Meta:
        verbose_name_plural = '订单ScanForm任务表'
        verbose_name = '订单ScanForm任务表'
        ordering = ['-id']

    def __str__(self):
        return f'订单ScanForm任务表:{self.status}'


class OrderSyncTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('HandledBy3rdNo', '已提交'),  # 目前未使用这个状态
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]

    TASK_TYPE = [
        ('PUSH_ORDER', '同步订单'),
        ('PULL_ORDER_STATUS', '拉取订单数据'),
        ('PULL_TRACK', '拉取订单轨迹'),
        ('PULL_PARCEL_TRACK', '拉取包裹轨迹'),
        ('PULL_POD_FILE', '拉取Pod文件'),
        ('PUSH_REVENUE', '推送收入'),
        ('PULL_COST', '拉取成本'),
    ]

    PULL_STATUS = [
        ('NO_PULL', '不拉取'),
        ('WAIT_PULL', '待拉取'),
        ('PULL_FINISH', '拉取完成'),
        ('PULL_FAIL', '拉取失败'),
    ]

    order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True, related_name='orderSyncTasks')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='任务状态')
    task_type = models.CharField(max_length=30, choices=TASK_TYPE, null=True, default='PUSH_ORDER',
                                 verbose_name='任务类型')
    task_desc = models.CharField(max_length=2048, verbose_name='任务描述', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='处理次数', null=True, blank=True, default=0)
    product_code = models.CharField(max_length=100, verbose_name='产品编码', null=True, blank=True)
    warehouse_code = models.CharField(max_length=100, verbose_name='仓库编码', null=True, blank=True)
    parcel = models.ForeignKey(Parcel, verbose_name='包裹号', on_delete=models.DO_NOTHING, null=True, blank=True)
    is_submit_order = models.BooleanField(verbose_name='是否已提交订单', default=False)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    is_master_product = models.BooleanField(verbose_name='是否主产品', default=False)
    # 删除pull_times, 先把tasks.py中的任务中的pull_times改了之后再删, 统一用handle_times
    # pull_times = models.IntegerField(verbose_name='拉单次数', default=0)

    # 下面三个字段将废弃
    pull_order_status = models.CharField(max_length=20, choices=PULL_STATUS, verbose_name='拉取订单状态',
                                         default='WAIT_PULL', null=True, blank=True)
    pull_track_status = models.CharField(max_length=20, choices=PULL_STATUS, verbose_name='拉取轨迹状态',
                                         default='WAIT_PULL', null=True, blank=True)
    pull_parcel_track_status = models.CharField(max_length=20, choices=PULL_STATUS,
                                                verbose_name='拉取包裹轨迹状态',
                                                default='WAIT_PULL', null=True, blank=True)

    class Meta:
        verbose_name_plural = '订单同步任务表'
        verbose_name = '订单同步任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


class OrderLabel(BaseEntity):
    order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                  related_name='orderLabels')
    tracking_no = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True)
    third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)
    label_billid = models.CharField(max_length=128, verbose_name='AG单号', null=True, blank=True)
    label_url = models.CharField(max_length=255, verbose_name='面单URL', null=True, blank=True)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    is_master_product = models.BooleanField(verbose_name='是否主产品', default=False)

    class Meta:
        verbose_name_plural = '订单面单表'
        verbose_name = '订单面单表'

    def __str__(self):
        return self.order_num


# 出货单
class ParcelOutboundOrder(BaseEntity):
    TYPE = [
        ('P', '小包'),
        ('M', '大货'),
    ]
    STATUS = [
        ('DR', '草稿'),
        ('WO', '待出货'),
        ('OUT', '已出货'),
        ('VO', '作废'),
        ('ON', '启用'),
        ('OFF', '失效'),
    ]

    SHIP_TYPE = [
        ('ZZ', '中转发运'),
        ('JG', '交干发运'),
    ]

    TRANSPORT_STATUS = [
        ('PL', '待装车'),
        ('LIP', '装车中'),
        ('AL', '已装车'),
        ('AD', '已发车'),
        ('AA', '已到达'),
        ('UIP', '卸车中'),
        ('AU', '已卸车'),
        ('AHO', '已交接'),
        ('HC', '交接完成'),
    ]
    outbound_num = models.CharField(max_length=50, verbose_name='出货单号', null=True, blank=True, db_index=True)
    ship_time = models.DateTimeField(verbose_name='发出时间', null=True, blank=True)
    unloading_completed_time = models.DateTimeField(verbose_name='卸车完成时间', null=True, blank=True)
    arrival_time = models.DateTimeField(verbose_name='到达时间', null=True, blank=True)
    references = models.CharField(max_length=255, verbose_name='参考号', null=True, blank=True)
    type = models.CharField(max_length=10, choices=TYPE, verbose_name='类型', default='P')
    status = models.CharField(max_length=10, choices=STATUS, verbose_name='状态', default='WO')
    transport_status = models.CharField(max_length=10, choices=TRANSPORT_STATUS, verbose_name='运输状态', default='PL')
    destination = models.CharField(max_length=50, verbose_name='发往站点', null=True, blank=True)
    departure = models.CharField(max_length=50, verbose_name='发出站点', null=True, blank=True)
    is_link_customer_order = models.BooleanField(verbose_name='是否关联运输单', default=False)
    customer_order = models.ForeignKey(CustomerOrder, related_name='customer_parcel_outbound',
                                       on_delete=models.DO_NOTHING,
                                       verbose_name='运输单', null=True,
                                       blank=True)

    ship_type = models.CharField(max_length=3, choices=SHIP_TYPE, verbose_name='发运类型', default='JG')
    ship_num = models.IntegerField(verbose_name='发运大包数量', null=True, blank=True)
    unload_num = models.IntegerField(verbose_name='卸车大包数量', null=True, blank=True)
    truck_num = models.CharField(max_length=50, verbose_name='运输车牌', null=True, blank=True)
    line_type = models.CharField(max_length=50, verbose_name='线路类型', null=True, blank=True)
    line_code = models.CharField(max_length=50, verbose_name='线路编码', null=True, blank=True)
    transport_fee = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='运输费用', null=True, blank=True)
    currency = models.CharField(max_length=10, verbose_name='运输费用币种', null=True, blank=True)

    class Meta:
        verbose_name = '出货单'
        verbose_name_plural = '出货单'


# 大包单
class BigParcel(BaseEntity):
    TYPE = [
        ('TP', '中转包'),
        ('FP', '成品包'),
    ]

    STATUS = [
        ('DR', '草稿'),
        ('WO', '暂存'),
        ('IW', '已称重'),
        ('FC', '完成'),
        ('VO', '作废'),
        ('ON', '启用'),
        ('OFF', '失效'),
    ]

    TRANSPORT_STATUS = [
        ('PL', '待装车'),
        ('LIP', '装车中'),
        ('AL', '已装车'),
        ('AD', '已发车'),
        ('AA', '已到达'),
        ('UIP', '卸车中'),
        ('AU', '已卸车'),
        ('AHO', '已交接'),
        ('HC', '交接完成'),
    ]

    type = models.CharField(max_length=10, choices=TYPE, verbose_name='类型', default='TP', null=True, blank=True)
    status = models.CharField(max_length=10, choices=STATUS, verbose_name='状态', default='DR', null=True, blank=True)
    transport_status = models.CharField(max_length=10, choices=TRANSPORT_STATUS, verbose_name='运输状态', default='PL')
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True, db_index=True)
    outbound_num = models.CharField(max_length=50, verbose_name='出库箱号', null=True, blank=True, db_index=True)
    zone = models.CharField(max_length=50, verbose_name='国家', null=True, blank=True, db_index=True)
    parcel_qty = models.IntegerField(verbose_name='小件数', null=True, blank=True, default=0)
    parcel_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True)

    parcel_length = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='长', null=True, blank=True)
    parcel_width = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='宽', null=True, blank=True)
    parcel_height = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='高', null=True, blank=True)

    product = models.ForeignKey(Product, verbose_name='产品代码', null=True, blank=True, on_delete=models.DO_NOTHING, )
    is_link_parcel_order = models.BooleanField(verbose_name='是否关联小包', default=False)
    is_link_customer_order = models.BooleanField(verbose_name='是否关联运输单', default=False)
    is_link_outbound_order = models.BooleanField(verbose_name='是否关联出货单', default=False)
    is_weighing = models.BooleanField(verbose_name='是否核重', default=False)
    weighing_time = models.DateTimeField(verbose_name='称重时间', null=True, blank=True)
    customer_order = models.ForeignKey(CustomerOrder, related_name='customer_parcel', on_delete=models.DO_NOTHING,
                                       verbose_name='客户订单号', null=True,
                                       blank=True)
    audit_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='汇总重量', default=0)
    parcel_outbound_order = models.ForeignKey(ParcelOutboundOrder, related_name='parcel_outbound_order_big_parcel',
                                              on_delete=models.DO_NOTHING,
                                              verbose_name='出货单', null=True,
                                              blank=True)
    master_order = models.ForeignKey(MasterOrder, related_name='master_order_big_parcel',
                                     on_delete=models.DO_NOTHING, verbose_name='空运主单', null=True, blank=True)
    master_order_time = models.DateTimeField(verbose_name='空运主单绑定时间', null=True, blank=True)

    is_inbound = models.BooleanField(verbose_name='是否入库', default=False, null=True, blank=True)
    inbound_time = models.DateTimeField(verbose_name='入库时间', null=True, blank=True)

    user_desc = models.CharField(max_length=60, verbose_name='创建人', null=True, blank=True)
    address = models.ForeignKey(Address, on_delete=models.DO_NOTHING, related_name='address_big_parcel',
                                verbose_name='创建仓库', null=True, blank=True)
    bag_print_num = models.IntegerField(verbose_name='袋标打印份数', blank=True, null=True)
    package_type = models.CharField(verbose_name='集包类型', max_length=10, null=True, blank=True)
    zone_name = models.CharField(verbose_name='分区名称', max_length=30, null=True, blank=True)
    zone_code = models.CharField(verbose_name='分区代码', max_length=30, null=True, blank=True)
    custom_code = models.CharField(verbose_name='分区业务代码', max_length=30, null=True, blank=True)
    material_requirement = models.CharField(verbose_name='包材要求', max_length=100, null=True, blank=True)
    weight_limit = models.CharField(verbose_name='集包重量', max_length=100, null=True, blank=True)
    other_requirement = models.CharField(verbose_name='其它要求', max_length=100, null=True, blank=True)
    inject_port = models.CharField(max_length=10, verbose_name='注入口岸', blank=True, null=True)
    wh_code = models.CharField(max_length=10, verbose_name='仓库编码', blank=True, null=True)

    class Meta:
        verbose_name = '大包'
        verbose_name_plural = '大包'
        indexes = [
            models.Index(fields=['parcel_num', 'del_flag']),
            models.Index(fields=['outbound_num', 'del_flag']),
        ]


class BigParcelLabelTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('UPDATED', '已更新'),
        ('HandledBy3rdNo', '已提交'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]
    order_num = models.ForeignKey(BigParcel, on_delete=models.DO_NOTHING, null=True,
                                  related_name='bigParcelLabelTasks')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='订单状态')
    label_desc = models.CharField(max_length=2048, verbose_name='抓取面单描述', null=True, blank=True)
    third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True)
    product_code = models.CharField(max_length=100, verbose_name='产品编码', null=True, blank=True)
    warehouse_code = models.CharField(max_length=100, verbose_name='仓库编码', null=True, blank=True)
    shipment_digest = models.TextField(verbose_name='第三方digest', null=True, blank=True)
    is_submit_order = models.BooleanField(verbose_name='是否已提交订单', default=False)
    is_push = models.BooleanField(verbose_name='是否推送', default=False)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    is_master_product = models.BooleanField(verbose_name='是否主产品', default=False)
    pull_status = models.CharField(max_length=100, verbose_name='拉单状态', null=True, blank=True)
    is_secondary = models.BooleanField(verbose_name='是否二次拉单', default=False)

    class Meta:
        indexes = [
            models.Index(fields=['mode_key', 'status', 'del_flag', 'handle_times']),
            models.Index(fields=['mode_key']),
            models.Index(fields=['status']),
            models.Index(fields=['del_flag']),
            models.Index(fields=['handle_times']),
        ]
        verbose_name_plural = '大包单任务表'
        verbose_name = '大包单任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


class BigParcelLabel(BaseEntity):
    order_num = models.ForeignKey(BigParcel, on_delete=models.DO_NOTHING, null=True,
                                  related_name='bigParcelLabels')
    tracking_no = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True)
    third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)
    label_billid = models.CharField(max_length=128, verbose_name='AG单号', null=True, blank=True)
    label_url = models.CharField(max_length=255, verbose_name='面单URL', null=True, blank=True)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    is_master_product = models.BooleanField(verbose_name='是否主产品', default=False)

    class Meta:
        verbose_name_plural = '大包单面单表'
        verbose_name = '大包单面单表'

    def __str__(self):
        return self.order_num


# 小包拣货记录(小包称重记录)
class PickRecord(BaseEntity):
    TYPE = [
        ('WEIGHING', '称重'),
        ('PICK', '装箱'),
        ('REPLACE_LABEL', '换标'),
    ]
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True, db_index=True)
    order_num = models.CharField(max_length=30, verbose_name='运单号', null=True, blank=True, db_index=True)
    user = models.ForeignKey(UserProfile, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='操作员')
    length = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='长', null=True, blank=True)
    width = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='宽', null=True, blank=True)
    height = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='高', null=True, blank=True)
    weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True)
    error_msg = models.CharField(max_length=100, verbose_name='错误信息')
    type = models.CharField(max_length=30, choices=TYPE, null=True, default='WEIGHING', verbose_name='记录类型')

    class Meta:
        verbose_name = '小包拣货记录'
        verbose_name_plural = '小包拣货记录'


class CustomsClearanceOrder(BaseEntity):
    CLEAR_STATUS = [
        ('DR', '草稿'),
        ('WO', '已下单'),
        ('CO', '已审核'),
        ('HI', '航班/船只已到达'),
        ('DH', 'GHA已发NOA时间'),
        ('NT', '仓库收到NOA时间'),
        ('TH', 'GHA显示DLV时间'),
        ('AW', '仓库实际收货时间'),
        ('WN', '仓库通知清关时间'),
        ('PA', '部分到仓'),
        ('PC', '仓库实际收货时间'),
        ('RP', '已收到POD/CRM'),
        ('RC', '已收到清关文件'),
        ('CC', '清关完成'),
        ('ZY', '转运车已发'),
        ('FC', '已到转运仓'),
        ('UF', '上传POD/CRM完成'),
        ('VO', '作废'),

    ]

    CLEAR_TYPE = [
        ('MS', '空运主单'),
        ('OC', '海运提单'),
    ]
    AGING_STATUS = [
        ('0', '统计中'),
        ('1', '统计完毕'),
    ]
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    WAYBILLS_CUSTOMER = [
        ('TLI', 'TLI'),  # TECHLINKINFORMATION
        ('ZHI', 'ZHI'),  # ZHIHE
    ]

    TRANSPORT_TYPE = [
        ('AWB', 'AWB'),
        ('T1', 'T1'),
    ]
    clear_status = models.CharField(max_length=10, choices=CLEAR_STATUS, verbose_name='状态', default='DR')
    clear_type = models.CharField(max_length=10, choices=CLEAR_TYPE, verbose_name='进口报关单类型', default='MS')
    order_num = models.CharField(max_length=20, verbose_name='清关单号', null=True, blank=True, db_index=True)
    mawb = models.CharField(max_length=20, verbose_name='mawb', null=True, blank=True, db_index=True)

    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商',
                                 related_name='cco_supplier_id')
    total_charge_in = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='总收入', default=0)
    total_charge_out = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='总支出', default=0)
    is_check = models.BooleanField(verbose_name='是否查验', default=False)

    customer = models.ForeignKey(Company, verbose_name='客户', on_delete=models.DO_NOTHING, null=True,
                                 related_name='cco_customer_id')
    product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True, blank=True)
    service = models.ForeignKey(Service, verbose_name='产品服务', on_delete=models.DO_NOTHING, null=True, blank=True)
    awb_file = models.CharField(max_length=2000, verbose_name='AWB', null=True, blank=True)
    sop_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='SOP')
    manifest_file = models.CharField(max_length=2000, verbose_name='Manifest', null=True, blank=True)
    pod_file = models.CharField(max_length=2000, blank=True, null=True, verbose_name='POD')
    crm_file = models.CharField(max_length=2000, blank=True, null=True, verbose_name='CRM')
    noa_file = models.CharField(max_length=2000, blank=True, null=True, verbose_name='NOA')

    is_send_debit = models.BooleanField(verbose_name='账单发送', default=False)
    is_send_forecast = models.BooleanField(verbose_name='预报发送', default=False, null=True)
    is_revenue_lock = models.BooleanField(verbose_name='收入确认', default=False)
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    opera_date = models.DateField(verbose_name='操作时间', null=True, blank=True)
    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)

    income = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='收入')
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    gross_profit = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='毛利')
    gross_currency = models.CharField(max_length=10, verbose_name='毛利币种', null=True, blank=True)

    # 海运提单内容
    vessel = models.CharField(max_length=50, verbose_name='船名', null=True, blank=True)
    carrier_code = models.CharField(max_length=30, verbose_name='CARRIER SCAC CODE', null=True, blank=True)
    ams_num = models.CharField(max_length=100, verbose_name='AMS NO.', null=True, blank=True)
    ams_code = models.CharField(max_length=30, verbose_name='AMS SCAC CODE', null=True, blank=True)
    voyage_num = models.CharField(max_length=20, verbose_name='航次', null=True, blank=True)
    mbl_no = models.CharField(max_length=50, verbose_name='CARRIER MB/L NO', null=True, blank=True)
    container_no = models.CharField(max_length=50, verbose_name='柜号', null=True, blank=True)

    # 公共部分
    destination = models.CharField(max_length=20, verbose_name='目的港', null=True, blank=True)
    departure = models.CharField(max_length=20, verbose_name='启运港', null=True, blank=True)

    estimated_time_departure = models.DateTimeField(verbose_name='预计离港日期', null=True, blank=True)
    estimated_time_arrival = models.DateTimeField(verbose_name='预计到港日期', null=True, blank=True)
    actual_leave_date = models.DateTimeField(verbose_name='实际离港日期', null=True, blank=True)
    actual_arrivals_date = models.DateTimeField(verbose_name='实际到港日期', null=True, blank=True)
    noa_date = models.DateTimeField(verbose_name='到货通知时间', null=True, blank=True)
    pickup_date = models.DateTimeField(verbose_name='机场提货时间', null=True, blank=True)
    clearance_date = models.DateTimeField(verbose_name='清关完成时间', null=True, blank=True)
    actual_departure_date = models.DateTimeField(verbose_name='实际发车时间', null=True, blank=True)
    ataw_date = models.DateTimeField(verbose_name='实际到仓时间', null=True, blank=True)
    receive_noa_date = models.DateTimeField(verbose_name='仓库收到NOA时间', null=True, blank=True)

    # 空运主单内容
    airline_num = models.CharField(max_length=20, verbose_name='航班号', null=True, blank=True)
    airline_dest_country = models.CharField(max_length=50, verbose_name='航班目的国', null=True, blank=True,
                                            db_index=True)

    aging = models.IntegerField(verbose_name='时效', null=True, blank=True, default=0)
    aging_status = models.CharField(max_length=10, choices=AGING_STATUS, verbose_name='时效统计状态', default='0')
    book_truck_date = models.DateTimeField(verbose_name='预约卡车时间', null=True, blank=True)

    pre_carton = models.IntegerField(verbose_name='预计箱数', null=True, blank=True)
    pre_package_num = models.IntegerField(verbose_name='预计包裹数', null=True, blank=True)
    pre_pallet_num = models.IntegerField(verbose_name='预计托盘数', null=True, blank=True)
    pre_weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='预计重量', null=True, blank=True)

    carton = models.IntegerField(verbose_name='箱数', null=True, blank=True)
    package_num = models.IntegerField(verbose_name='包裹数', null=True, blank=True)
    weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='重量', null=True, blank=True)
    pallet_num = models.IntegerField(verbose_name='托盘数', null=True, blank=True)
    is_split_goods = models.BooleanField(verbose_name='是否分货', default=False)
    is_pushed_to_supplier = models.BooleanField(verbose_name='是否推送给供应商', default=False)
    waybills_customer = models.CharField(max_length=30, choices=WAYBILLS_CUSTOMER, verbose_name='清关供应商客户代码',
                                         null=True, blank=True, default='ZHI')

    NOA_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='NOA重量', default=0)
    warehouse_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='仓库称重', default=0)

    transport_type = models.CharField(max_length=10, choices=TRANSPORT_TYPE, verbose_name='运输类型', default='AWB')
    T1_num = models.CharField(max_length=50, verbose_name='T1编号', null=True, blank=True)
    pre_gross_weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='预计毛重', null=True, blank=True)
    pre_charge_weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='预计计费重', null=True, blank=True)
    pre_volume = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='预计体积', null=True, blank=True)
    volume = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='实际体积', null=True, blank=True)
    ULD_PMC = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='ULD/PMC数量', null=True, blank=True)


class CustomsClearanceAddress(BaseEntity):
    TYPE = [
        ('SHIPPER', '提单收件人'),
        ('receiver', '转运目的仓'),
        ('notify_party', 'NotifyParty'),
    ]
    address_type = models.CharField(max_length=20, choices=TYPE, verbose_name='地址类型', default='SHIPPER')
    address_id = models.IntegerField(verbose_name='地址ID', null=True, blank=True)
    address_num = models.CharField(max_length=50, verbose_name='地址编码', null=True, blank=True)
    address_name = models.CharField(max_length=50, verbose_name='地址简称', null=True)
    contact_name = models.CharField(max_length=100, verbose_name='联系人', null=True)
    contact_email = models.EmailField(max_length=300, verbose_name='邮箱', null=True, blank=True)
    contact_phone = models.CharField(max_length=50, verbose_name='电话', null=True, blank=True)
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True)
    state_code = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True)
    city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True)
    postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)
    house_no = models.CharField(max_length=20, verbose_name='门牌号', null=True, blank=True)
    address_one = models.CharField(max_length=100, verbose_name='地址行1', null=True)
    address_two = models.CharField(max_length=100, verbose_name='地址行2', null=True, blank=True)
    company_name = models.CharField(max_length=100, verbose_name='公司名', null=True, blank=True)
    supplier_code = models.CharField(max_length=50, verbose_name='供应商编码', null=True, blank=True)
    vat = models.CharField(max_length=50, verbose_name='vat', null=True, blank=True)
    eori = models.CharField(max_length=50, verbose_name='eori', null=True, blank=True)
    customer_order_num = models.ForeignKey(CustomsClearanceOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='clearanceAddressList')
    weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='重量', default=0)  # 实际重量
    package_num = models.IntegerField(verbose_name='箱数', default=0)
    actual_package_num = models.IntegerField(verbose_name='实际箱数', default=0)
    pre_carton = models.IntegerField(verbose_name='预计包裹数', null=True, blank=True)
    carton = models.IntegerField(verbose_name='实际包裹数', null=True, blank=True)
    adhesive_tape_color = models.CharField(verbose_name='胶带颜色', max_length=50, null=True, blank=True)

    delivery_method = models.CharField(verbose_name='派送方式', max_length=50, null=True, blank=True)
    delivery_address = models.CharField(verbose_name='派送地址', max_length=100, null=True, blank=True)
    pre_weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='预计重量', null=True, blank=True)

    ware_receiving_time = models.CharField(verbose_name='仓库实际收货时间', max_length=100, null=True, blank=True)
    ware_receiving_num = models.CharField(verbose_name='仓库实际收货数量', max_length=100, null=True, blank=True)
    clearance_finish_time = models.CharField(verbose_name='清关完成时间', max_length=100, null=True, blank=True)
    clearance_finish_num = models.CharField(verbose_name='清关完成数量', max_length=100, null=True, blank=True)
    transfer_vehicle_time = models.CharField(verbose_name='转运车已发时间', max_length=100, null=True, blank=True)
    transfer_vehicle_num = models.CharField(verbose_name='转运车已发数量', max_length=100, null=True, blank=True)
    arrive_ware_time = models.CharField(verbose_name='到达转运仓时间', max_length=100, null=True, blank=True)
    arrive_ware_num = models.CharField(verbose_name='到达转运仓数量', max_length=100, null=True, blank=True)

    class Meta:
        verbose_name = '清关地址'
        verbose_name_plural = '清关地址'

    def __str__(self):
        return '%s: %s %s ' % (self.address_name, self.postcode, self.country_code,)


class CustomsClearanceOrderChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    is_share = models.BooleanField(verbose_name='分摊', default=False)
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    customer_order_num = models.ForeignKey(CustomsClearanceOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='clearanceChargeOuts')
    share_charge_id = models.CharField(max_length=200, verbose_name='分摊单号', null=True, blank=True)
    price_version = models.ForeignKey(ProductCostVersion, verbose_name='成本价格版本', on_delete=models.DO_NOTHING,
                                      null=True)
    charge_weight = models.CharField(max_length=100, verbose_name='计费重', null=True, blank=True)
    charge_value = models.CharField(max_length=100, verbose_name='计费值', null=True, blank=True)

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class CustomsClearanceOrderChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    customer_order_num = models.ForeignKey(CustomsClearanceOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='clearanceChargeIns')
    price_version = models.ForeignKey(ProductRevenueVersion, verbose_name='收入价格版本', on_delete=models.DO_NOTHING,
                                      null=True)
    charge_weight = models.CharField(max_length=100, verbose_name='计费重', null=True, blank=True)
    charge_value = models.CharField(max_length=100, verbose_name='计费值', null=True, blank=True)
    is_deducted = models.BooleanField(verbose_name='是否已扣费', default=False)

    def __str__(self):
        return '记录收入 %s 金额 %s %s' % (self.charge, self.charge_total, self.currency_type)


class CustomsClearanceOrderTrack(BaseEntity):
    track_code = models.CharField(max_length=20, verbose_name='轨迹代码')
    track_name = models.CharField(max_length=20, verbose_name='轨迹名称')
    operation_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)
    order_num = models.ForeignKey(CustomsClearanceOrder, on_delete=models.DO_NOTHING, null=True,
                                  verbose_name='清关单号', related_name="clearance_tracks")

    def __str__(self):
        return self.track_name


class CustomsClearanceTruckOrder(BaseEntity):
    STATUS = [
        ('DR', '草稿'),
        ('WO', '已提交'),
        ('HI', '处理中'),
        ('VO', '作废'),
        ('FC', '订单完成'),
    ]

    TRANS_TYPE = [
        ('PU', '提货'),
        ('TR', '转运'),
    ]
    truck_order_num = models.CharField(verbose_name='卡车单号', max_length=50, null=True, blank=True)
    status = models.CharField(max_length=10, choices=STATUS, verbose_name='状态', default='DR')
    car_number = models.CharField(max_length=50, verbose_name='车牌号', null=True, blank=True)
    driver_name = models.CharField(max_length=50, verbose_name='司机', null=True, blank=True)

    pod_file = models.CharField(max_length=2000, blank=True, null=True, verbose_name='POD')
    crm_file = models.CharField(max_length=2000, blank=True, null=True, verbose_name='CRM')

    booking_truck_date = models.DateTimeField(verbose_name='预约卡车时间', null=True, blank=True)
    operation_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)
    estimated_time_departure = models.DateTimeField(verbose_name='预计出发时间', null=True, blank=True)
    estimated_time_arrival = models.DateTimeField(verbose_name='预计到达时间', null=True, blank=True)
    actual_leave_date = models.DateTimeField(verbose_name='实际出发时间', null=True, blank=True)
    actual_arrivals_date = models.DateTimeField(verbose_name='实际到达日期', null=True, blank=True)

    start_destination = models.CharField(verbose_name='始发站', max_length=50, null=True, blank=True)
    arrive_destination = models.CharField(verbose_name='目的站', max_length=50, null=True, blank=True)
    carton = models.IntegerField(verbose_name='箱数', default=0)
    package_num = models.IntegerField(verbose_name='包裹数', default=0)
    weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='重量', default=0)

    charge_weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='计费重量', null=True, blank=True)
    trans_type = models.CharField(max_length=20, choices=TRANS_TYPE, verbose_name='类型', null=True, blank=True)

    total_charge_in = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='总收入', default=0, null=True,
                                          blank=True)
    total_charge_out = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='总支出', default=0, null=True,
                                           blank=True)

    def __str__(self):
        return self.truck_order_num


class CustomsClearanceTruckChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('HKD', 'HKD'),
        ('EUR', 'EUR'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    customer_order_num = models.ForeignKey(CustomsClearanceTruckOrder, related_name='truckChargeOuts',
                                           on_delete=models.DO_NOTHING, null=True, verbose_name='卡车单')

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


class CustomsClearanceRelateTruck(BaseEntity):
    truck_order = models.ForeignKey(CustomsClearanceTruckOrder, related_name='relate_trucks',
                                    on_delete=models.DO_NOTHING, null=True, verbose_name='卡车单')
    order_num = models.ForeignKey(CustomsClearanceOrder, on_delete=models.DO_NOTHING, null=True,
                                  related_name='relate_orders')
    pre_package_num = models.IntegerField(verbose_name='预计包裹数', null=True, blank=True)
    package_num = models.IntegerField(verbose_name='包裹数', null=True, blank=True)


# 清关大包单
class CustomsClearanceBigParcelOrder(BaseEntity):
    order_num = models.CharField(max_length=20, verbose_name='清关大包单号', null=True, blank=True, db_index=True)
    customs_clearance_order = models.ForeignKey(CustomsClearanceOrder, on_delete=models.DO_NOTHING, null=True,
                                                blank=True,
                                                related_name='customsClearanceBigParcelOrderList')

    third_order_no = models.CharField(max_length=50, verbose_name='第三方大包单号', null=True, blank=True,
                                      db_index=True)

    zone = models.CharField(max_length=50, verbose_name='国家', null=True, blank=True, db_index=True)
    parcel_qty = models.IntegerField(verbose_name='包裹数', null=True, blank=True, default=0)
    parcel_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True)

    class Meta:
        verbose_name_plural = '清关大包单'
        verbose_name = '清关大包单'

    def __str__(self):
        return self.order_num or ''


class OrderInstruct(BaseEntity):
    ORDER_TYPE = [
        ('IN', '入库指令'),
        ('OUT', '出库指令')
    ]

    STATUS = [
        ('DR', '草稿'),
        ('WO', '已提交'),
        ('TG', '审核通过'),
        ('JJ', '审核拒绝'),
        ('VO', '作废'),
    ]

    order_num = models.CharField(max_length=30, verbose_name='关联单号', null=True, blank=True)
    order_status = models.CharField(max_length=10, verbose_name='订单状态', choices=STATUS, default='DR', null=True)
    order_type = models.CharField(max_length=10, verbose_name='订单类型', choices=ORDER_TYPE, default='IN', null=True)
    warehouse_code = models.CharField(max_length=100, verbose_name='转运目的仓', null=True, blank=True)
    actual_package_num = models.IntegerField(verbose_name='箱数', default=0)
    carton = models.IntegerField(verbose_name='包裹数', null=True, blank=True)
    operation_time = models.DateTimeField(verbose_name='操作时间', null=True, blank=True)

    class Meta:
        verbose_name_plural = '订单指令'
        verbose_name = '订单指令'

    def __str__(self):
        return self.order_num or ''


class ParcelCustomerOrderBatchExportTask(BaseEntity):
    STATUS = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('success', '处理成功'),
        ('failure', '处理失败'),
        ('cancel', '已取消'),
    ]
    task_no = models.CharField(max_length=50, verbose_name='任务编号', null=True, blank=True)
    query = models.JSONField(verbose_name="查询条件", null=True, blank=True)
    status = models.CharField(max_length=10, verbose_name='任务状态', choices=STATUS, default='pending', null=True)
    result = models.TextField(verbose_name='处理结果', null=True, blank=True)
    file_url = models.CharField(max_length=200, verbose_name='文件下载路径', null=True, blank=True)
    run_timestamp = models.IntegerField(verbose_name='任务执行时间戳',default=0, null=True, blank=True)

    class Meta:
        verbose_name_plural = '小包批量导出任务'
        verbose_name = '小包批量导出任务'

    def __str__(self):
        if self.pk:
            return f"ParcelCustomerOrderBatchExportTask(pk={self.pk})"
        return ''


# 小包单
class ParcelCustomerOrder(BaseEntity):
    STATUS = [
        ('DR', '草稿'),
        ('VO', '已作废'),
        ('BC', '取消中'),
        ('CS', '取消成功'),
        ('CF', '取消失败'),
        ('WO', '等待作业'),
        ('GL', '生成面单'),
        ('CONFIRMED', '已确认'),
        ('AW', '到仓收货'),
        ('INBOUND', '称重入库'),
        ('RL', '面单打印'),
        ('SORTING', '分拣作业'),
        ('LC', '组包'),
        ('OUTBOUND', '出库'),  # 干线发运
        ('OTW', '中转仓出库'),  # 调拨发运
        ('AB', '干线打板'),  # 干线打板
        ('HOA', '干线交航'),  # 干线交航
        ('DPO', '干线起飞'),
        ('ADP', '干线抵达'),
        ('CCP', '清关行提取'),
        ('DPCC', '目的港清关'),
        ('HOLM', '注入交邮'),
        ('LMP', '末端提取'),
        ('LRD', '境内退件交接'),
        ('LRS', '境内退件入库'),
        ('LRDO', '境内退件出库'),
        ('LMR', '境内退件'),
        ('TRANSIT', '运输中'),
        ('MP', 'Manifest'),  # 预报推送
        ('TF', '转运'),
        ('FC', '完成'),
        ('SF', '已签收'),
        ('QX', '境外退件'),
        ('DEO', '投递异常'),
    ]
    PUSH_STATUS = [
        ('NO_PUSH', '不推送'),
        ('WAIT_PUSH', '待推送'),
        ('PUSHED', '推送完成'),
        ('PUSH_FAIL', '推送失败'),
    ]
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    ORDER_TYPE = [
        ('PC', '小包'),
        ('CC', '清关包裹')
    ]

    INTERCEPT_STATUS = [
        ('I', '拦截中'),
        ('C', '已拦截'),
        ('F', '拦截失败'),
        ('Q', '取消拦截'),
    ]

    order_num = models.CharField(max_length=30, verbose_name='订单号', null=True, blank=True)
    tracking_num = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True)  # 欠费订单中的尾程单号
    order_status = models.CharField(max_length=10, verbose_name='订单状态', choices=STATUS, default='DR', null=True, )
    intercept_mark = models.BooleanField(verbose_name='拦截标记', default=False)
    customer_order_num = models.CharField(max_length=50, verbose_name='客户订单号', null=True, blank=True,
                                          db_index=True)
    third_orderNo = models.CharField(max_length=50, verbose_name='第三方服务单号', null=True, blank=True, db_index=True)
    po_no = models.CharField(max_length=30, verbose_name='po_no', null=True, blank=True, db_index=True)
    customer = models.ForeignKey(Company, verbose_name='客户', on_delete=models.DO_NOTHING, null=True)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    product_line = models.ForeignKey(ProductLine, verbose_name='线路', on_delete=models.DO_NOTHING, null=True,
                                     blank=True)
    service = models.ForeignKey(Service, verbose_name='服务', on_delete=models.DO_NOTHING, null=True, blank=True)
    real_product = models.ForeignKey(Product, verbose_name='真实产品', on_delete=models.DO_NOTHING, null=True,
                                     blank=True, related_name='real_product_parcel')
    # 发件人
    warehouse_code = models.ForeignKey(Address, verbose_name='仓库编码', null=True, blank=True,
                                       on_delete=models.SET_NULL,
                                       related_name='customerorder_warehouse')
    contact_name = models.CharField(max_length=100, verbose_name='联系人', null=True, blank=True)
    contact_email = models.EmailField(max_length=300, verbose_name='邮箱', null=True, blank=True)
    contact_phone = models.CharField(max_length=50, verbose_name='电话', null=True, blank=True)
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    state_code = models.CharField(max_length=64, verbose_name='省份(州)编码', null=True, blank=True)
    city_code = models.CharField(max_length=64, verbose_name='城市编码', null=True, blank=True)
    postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)
    house_no = models.CharField(max_length=20, verbose_name='门牌号', null=True, blank=True)
    address_one = models.CharField(max_length=256, verbose_name='地址行1', null=True, blank=True)
    address_two = models.CharField(max_length=256, verbose_name='地址行2', null=True, blank=True)
    company_name = models.CharField(max_length=150, verbose_name='公司名', null=True, blank=True)
    vat_num = models.CharField(max_length=120, verbose_name='vat号', null=True, blank=True)
    transfer_warehouse_code = models.CharField(max_length=120, verbose_name='转运目的仓', null=True, blank=True)

    # 收件人
    buyer_name = models.CharField(max_length=100, verbose_name='收件人', null=True, blank=True)
    buyer_mail = models.CharField(max_length=100, verbose_name='收件人邮箱', null=True, blank=True)
    buyer_phone = models.CharField(max_length=50, verbose_name='收件人电话', null=True, blank=True)
    buyer_country_code = models.CharField(max_length=10, verbose_name='收件人国家代码', null=True, blank=True)
    buyer_country = models.CharField(max_length=50, verbose_name='收件人国家', null=True, blank=True)
    buyer_state = models.CharField(max_length=64, verbose_name='收件人州', null=True, blank=True)
    buyer_city_code = models.CharField(max_length=64, verbose_name='收件人城市编码', null=True, blank=True)
    buyer_city = models.CharField(max_length=64, verbose_name='收件人城市', null=True, blank=True)
    buyer_postcode = models.CharField(max_length=50, verbose_name='邮编', null=True, blank=True)
    buyer_house_num = models.CharField(max_length=120, verbose_name='门牌号', null=True, blank=True)
    buyer_address_one = models.CharField(max_length=256, verbose_name='地址1', null=True, blank=True)
    buyer_address_two = models.CharField(max_length=256, verbose_name='地址2', null=True, blank=True)
    buyer_company_name = models.CharField(max_length=150, verbose_name='公司名', null=True, blank=True)
    buyer_tax = models.CharField(max_length=256, verbose_name='tax', null=True, blank=True)
    ioss_num = models.CharField(max_length=120, verbose_name='ioss号', null=True, blank=True)

    order_time = models.DateTimeField(verbose_name='下单时间', null=True, blank=True, default=timezone.now,
                                      db_index=True)
    weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量')
    volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True, default=0)
    is_revenue_lock = models.BooleanField(verbose_name='收入确认', default=False)
    is_cost_lock = models.BooleanField(verbose_name='成本确认', default=False)
    income = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='收入')
    cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='成本')
    gross_profit = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name='毛利')
    gross_currency = models.CharField(max_length=10, verbose_name='毛利币种', null=True, blank=True)
    is_inner_order = models.BooleanField(verbose_name='推送订单', default=False)
    download_num = models.IntegerField(null=True, blank=True, default=0, verbose_name='下载面单次数')
    is_api = models.BooleanField(verbose_name='API创建', default=False)
    is_confirm_ship = models.BooleanField(verbose_name='是否确认发货', default=False)
    weighing_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='称重重量', null=True,
                                          blank=True)
    is_weighing = models.BooleanField(verbose_name='是否核重', default=False)
    charge_trans = models.DecimalField(decimal_places=2, max_digits=10, verbose_name='计费转换', null=True, default=0)
    charge_weight = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='计费重量', null=True, blank=True)
    big_parcel = models.ForeignKey(BigParcel, related_name='big_parcel', on_delete=models.DO_NOTHING,
                                   verbose_name='大包单', null=True, blank=True, db_index=True)
    customs_clearance_big_parcel_order = models.ForeignKey(CustomsClearanceBigParcelOrder, related_name='big_parcel',
                                                           on_delete=models.DO_NOTHING, verbose_name='清关大包单',
                                                           null=True, blank=True, db_index=True)
    confirm_charge_weight = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='确认计费重量', null=True,
                                                blank=True)

    account_time = models.DateField(verbose_name='记账日期', null=True, blank=True)
    pay_account_time = models.DateField(verbose_name='成本记账日期', null=True, blank=True)
    zone_value = models.CharField(max_length=48, verbose_name='分区值', null=True, blank=True)
    order_remark = models.CharField(max_length=100, verbose_name='订单备注', null=True, blank=True)
    label_billid = models.CharField(max_length=128, verbose_name='AG单号(邮政单号)', null=True, blank=True)
    pull_status = models.CharField(max_length=100, verbose_name='拉单状态', null=True, blank=True)
    inbound_time = models.DateTimeField(verbose_name='入库称重时间', null=True, blank=True)
    outbound_time = models.DateTimeField(verbose_name='出库时间', null=True, blank=True)
    ship_date = models.DateField(verbose_name='发货时间', null=True, blank=True)
    order_source = models.CharField(max_length=100, verbose_name='订单来源', null=True, blank=True)
    order_type = models.CharField(max_length=100, verbose_name='订单类型', choices=ORDER_TYPE, default='PC')
    # hanjin
    waybill_node = models.CharField(max_length=50, verbose_name='运单节点', null=True, blank=True)
    waybill_node_status = models.CharField(max_length=50, verbose_name='运单节点状态', null=True, blank=True,
                                           default='000')
    is_change_waybill = models.BooleanField(verbose_name='是否已换面单', default=False, blank=True, null=True)

    # ems 要求字段
    subsidiary_organ_code = models.CharField(max_length=50, verbose_name='客户所属机构编号', null=True, blank=True)
    subsidiary_organ_name = models.CharField(max_length=50, verbose_name='客户所属机构名称', null=True, blank=True)
    # 改成push_status, 然后用order_source区分来源推送给哪个供应商
    push_status = models.CharField(max_length=30, choices=PUSH_STATUS, verbose_name='推送状态',
                                   default='NO_PUSH', null=True, blank=True)

    is_intercept_record = models.BooleanField(verbose_name='是否有拦截记录', default=False)
    intercept_status = models.CharField(max_length=10, verbose_name='拦截状态', choices=INTERCEPT_STATUS, null=True,
                                        blank=True)
    bulking_date = models.DateTimeField(verbose_name='组包时间', null=True, blank=True)
    is_pushed_wish = models.BooleanField(verbose_name="是否已经推送给 wish", default=False)

    class Meta:
        indexes = [
            models.Index(fields=['order_num', 'del_flag']),
            models.Index(fields=['tracking_num', 'del_flag']),
            models.Index(fields=['label_billid', 'del_flag']),
            models.Index(fields=['customer_order_num', 'del_flag']),
            models.Index(fields=['order_time', 'del_flag']),
        ]
        verbose_name_plural = '小包订单'
        verbose_name = '小包订单'

    def __str__(self):
        if self.order_num:
            return self.order_num
        else:
            return ''

    # @property
    # def parcel_tracks(self):
    #     return ParcelTrack.objects.filter(order_num=self.order_num)


class CancelParcelCustomerOrderLabelTask(BaseEntity):
    """二次取消小包单面单任务表"""
    TaskStatus = [
        ('PENDING', '待处理'),
        ('NORMAL', '正常'),
        ('EXPIRED', '已过期'),
        ('FAILED', '处理失败'),
        ('COMPLETED', '已完成'),
    ]
    CancelStatus = [
        ('Success', '取消成功'),
        ('FAILED', '取消失败'),
        ('UNKONW', '未知'),
    ]
    SourceType = [
        ('CANCEL_LABEL', '取消面单'),
        ('API_CANCEL_LABEL', '小包取消面单接口'),
        ('AUTO', '自动'),
        ('UNKONW', '未知'),
        ('CUSTOMER_CANCEL_LABEL', '客户取消面单'),
        ('CUSTOMER_FAIL_ORDER', '客户作废订单'),
        ('FAIL_ORDER', '作废订单'),
        ('FORCE_FAIL_ORDER','强制作废订单'),
    ]

    order = models.ForeignKey(ParcelCustomerOrder, verbose_name='小包单', on_delete=models.DO_NOTHING, null=True)
    status = models.CharField(max_length=15, default='NORMAL', choices=TaskStatus, null=True, blank=True,
                              verbose_name='任务状态', db_index=True)
    cancel_status = models.CharField(max_length=15, default='UNKONW', choices=CancelStatus, null=True, blank=True,
                              verbose_name='取消情况')
    run_count = models.IntegerField(default=0, verbose_name='执行次数', null=True, blank=True)
    next_run_time = models.IntegerField(default=0, verbose_name='下次执行时间', null=True, blank=True)
    result = models.TextField(null=True, blank=True, verbose_name='执行结果')
    order_num = models.CharField(max_length=50, verbose_name='小包单号', null=True, blank=True, db_index=True)
    order_create_date = models.DateTimeField(verbose_name='小包单创建时间', null=True, blank=True, db_index=True)
    source = models.CharField(max_length=50, default='AUTO', choices=SourceType, verbose_name='任务来源', null=True, blank=True)
    tracking_num = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True,db_index=True)
    customer_order_num = models.CharField(max_length=50, verbose_name='客户订单号', null=True, blank=True,
                                          db_index=True)

    class Meta:
        verbose_name = '二次取消小包单面单任务表'
        verbose_name_plural = '二次取消小包单面单任务表'


class ParcelOrderLog(BaseEntity):
    order_num = models.CharField(max_length=50, verbose_name='订单号', null=True, blank=True, db_index=True)
    company_desc = models.CharField(max_length=150, verbose_name='所属组织/仓库', null=True, blank=True)
    user_desc = models.CharField(max_length=150, verbose_name='创建对象', null=True, blank=True)
    details = models.CharField(max_length=500, verbose_name='详情（异常详情）', null=True, blank=True)

    class Meta:
        verbose_name = '小包订单日志表'
        verbose_name_plural = '小包订单日志表'


class ParcelOrderExtend(BaseEntity):
    TAX_PAY_MODE = [
        (1, 'DDU'),
        (2, 'DDP')
    ]
    CONFIRM_TYPES = [
        (1, '订单确认'),
        (2, 'scan form'),  # Manifest
    ]
    CONFIRM_PROGRESS = [
        (1, '未确认'),
        (2, '已确认'),
        (3, '取消中'),
        (4, '已取消'),
        (5, '取消失败'),
    ]
    CENCEL_TYPES = [
        (0, '未确认取消'),
        (1, '已确认取消'),
    ]
    TYPES_API = [
        (1, 'API 默认收费通过'),
        (2, 'API 审核通过'),
    ]

    ADDRESS_TYPE = [
        ('B', '商业住宅'),
        ('P', '私人住宅'),
        ('U', '未知类型'),
    ]

    SIGN_TYPE = [
        ('I', 'Indirect'),  # 间接签名（非本人）
        ('D', 'Direct'),    # 直接签名
        ('A', 'Adult'),     # 成人
    ]
    customer_order = models.ForeignKey(ParcelCustomerOrder, related_name='parcelOrderExtends',
                                       on_delete=models.DO_NOTHING,
                                       verbose_name='订单号', null=True, blank=True)
    # IOSS , VAT ,EORI, CPF, BR
    tax_type = models.CharField(max_length=10, verbose_name='税号类型', null=True, blank=True)   # 清关类型
    third_party_tracking_number = models.CharField(max_length=100, verbose_name='第三方配送单号', null=True, blank=True)
    third_party_carrier = models.CharField(max_length=100, verbose_name='第三方配送承运商', null=True, blank=True)
    tax_pay_mode = models.SmallIntegerField(choices=TAX_PAY_MODE, verbose_name='税费支付类型', null=True, blank=True)
    tax_no = models.CharField(max_length=30, verbose_name='税号值', null=True, blank=True)
    tax_company = models.CharField(max_length=50, verbose_name='注册公司', null=True, blank=True)
    tax_country = models.CharField(max_length=50, verbose_name='注册国家', null=True, blank=True)
    tax_address = models.CharField(max_length=100, verbose_name='注册地址', null=True, blank=True)

    # 特殊字段
    is_confirm_label = models.BooleanField(verbose_name='是否下单即确认', default=False)
    is_overseas_return = models.BooleanField(verbose_name='是否需要海外退件', default=False, null=True, blank=True)
    is_signature = models.BooleanField(verbose_name='是否需要签名服务', default=False, null=True, blank=True)

    confirm_types = models.CharField(max_length=100, verbose_name='确认方式', choices=CONFIRM_TYPES, default=1,
                                     null=True, blank=True)
    confirm_progress = models.CharField(max_length=100, verbose_name='确认进度', choices=CONFIRM_PROGRESS, default=1,
                                        null=True, blank=True)
    cancel_types = models.CharField(max_length=100, verbose_name='取消方式', choices=CENCEL_TYPES, default=0,
                                    null=True, blank=True)
    service_approval_remark = models.CharField(max_length=500, verbose_name='客户备注', null=True, blank=True)
    cancel_type_api = models.CharField(max_length=50, verbose_name='取消类型', choices=TYPES_API, null=True, blank=True)
    scanform_task = models.ForeignKey(
        OrderScanFormTask,
        related_name='parcelOrderExtends',
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True
    )
    store_length = models.CharField(max_length=50, verbose_name='入库长', null=True, blank=True)
    store_width = models.CharField(max_length=50, verbose_name='入库宽', null=True, blank=True)
    store_height = models.CharField(max_length=50, verbose_name='入库高', null=True, blank=True)

    billing_status = models.BooleanField(verbose_name='应收计费状态', null=True, default=False, blank=True)
    deduction_status = models.BooleanField(verbose_name='扣费费状态', null=True, default=False, blank=True)

    waybill_node = models.CharField(max_length=50, verbose_name='运单节点', null=True, blank=True)
    waybill_node_status = models.CharField(max_length=50, verbose_name='运单节点状态', null=True, blank=True)

    # 用于存储修改后清关信息
    label_ioss_num = models.CharField(max_length=120, verbose_name='ioss号-改后', null=True, blank=True)
    is_changed_customs_clearance = models.BooleanField(verbose_name='是否已更改清关信息', null=True, default=False, blank=True)
    # 地址类型、签名类型，与收费有关
    address_type = models.CharField(max_length=50, verbose_name='地址类型', choices=ADDRESS_TYPE, null=True, blank=True)
    signature_type = models.CharField(max_length=50, verbose_name='签名类型', choices=SIGN_TYPE, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['customer_order', 'del_flag']),
        ]
        verbose_name = '订单扩展表'
        verbose_name_plural = '订单扩展表'

    def __str__(self):
        return f'{self.customer_order}'


class ParcelOrderAddress(BaseContactAddress):
    ADDRESS_TYPE = [
        ('SP', '发件人'),
        ('RC', '收件人'),
        ('TD', '交接地址'),
        ('ZW', '退件地址'),
        ('CA', '揽收地址'),
        ('OV', '境外退件地址'),
    ]

    customer_order = models.ForeignKey(ParcelCustomerOrder, related_name='parcelOrderAddresses',
                                       on_delete=models.DO_NOTHING,
                                       verbose_name='订单号', null=True, blank=True)
    big_parcel = models.ForeignKey(BigParcel, related_name='bigParcelAddresses',
                                   on_delete=models.DO_NOTHING,
                                   verbose_name='大包单订单号', null=True, blank=True)
    address_type = models.CharField(max_length=10, choices=ADDRESS_TYPE, verbose_name='地址类型', default='RC')

    class Meta:
        verbose_name = '订单地址信息'
        verbose_name_plural = '订单地址信息'

    def __str__(self):
        return '%s: %s %s ' % (self.address_num, self.postcode, self.country_code,)

    @classmethod
    def create_address(cls, **kwargs):
        """创建地址"""
        if not kwargs:
            raise ValueError('地址信息未填写')
        obj = cls.objects.filter(customer_order=kwargs['customer_order'], address_type=kwargs['address_type'],
                                 del_flag=False)
        if obj.exists():
            return obj.last()
        return cls.objects.create(**kwargs)


# 小包收入
class ParcelOrderChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=15, decimal_places=6, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    customer_order_num = models.ForeignKey(ParcelCustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='parcel_customer_order_charge_in')
    is_share = models.BooleanField(verbose_name='分摊', default=False)
    share_charge_id = models.CharField(max_length=200, verbose_name='分摊单号', null=True, blank=True)
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')
    price_version = models.ForeignKey(ProductRevenueVersion, verbose_name='收入价格版本', on_delete=models.DO_NOTHING,
                                      null=True)
    charge_weight = models.CharField(max_length=100, verbose_name='计费重', null=True, blank=True)
    published_account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                                   verbose_name='公布价记账金额')

    charge_value = models.CharField(max_length=100, verbose_name='计费值', null=True, blank=True)
    is_deducted = models.BooleanField(verbose_name='是否已扣费', default=False)
    owe_mark = models.BooleanField(verbose_name='是否欠费', default=False)
    owe_time = models.DateTimeField(verbose_name='欠费操作时间', null=True, blank=True)
    charging_time = models.DateTimeField(verbose_name='计费时间', null=True, blank=True)
    charging_node = models.CharField(max_length=10, verbose_name='计费节点', null=True, blank=True)


# 小包成本
class ParcelOrderChargeOut(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用')
    charge_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=15, decimal_places=6, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    supplier = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商')
    is_system = models.BooleanField(verbose_name='系统添加', default=False)
    is_share = models.BooleanField(verbose_name='分摊', default=False)
    share_charge_id = models.CharField(max_length=200, verbose_name='分摊单号', null=True, blank=True)
    customer_order_num = models.ForeignKey(ParcelCustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='parcel_customer_order_charge_out')
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='基础价')
    price_version = models.ForeignKey(ProductCostVersion, verbose_name='成本价格版本', on_delete=models.DO_NOTHING,
                                      null=True)
    charge_value = models.CharField(max_length=100, verbose_name='计费值', null=True, blank=True)
    charging_time = models.DateTimeField(verbose_name='计费时间', null=True, blank=True)
    charging_node = models.CharField(max_length=10, verbose_name='计费节点', null=True, blank=True)

    def __str__(self):
        return '记录费用 %s 金额 %s %s ' % (self.charge, self.charge_total, self.currency_type)


# 小包包裹
class ParcelOrderParcel(BaseEntity):
    parcel_num = models.CharField(max_length=50, verbose_name='包裹号', null=True, blank=True)
    parcel_desc = models.CharField(max_length=100, verbose_name='包裹描述', null=True, blank=True, default='0')
    parcel_length = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='长', null=True, blank=True)
    parcel_width = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='宽', null=True, blank=True)
    parcel_height = models.DecimalField(max_digits=10, decimal_places=1, verbose_name='高', null=True, blank=True)
    parcel_size_unit = models.CharField(max_length=50, verbose_name='尺寸单位', null=True, blank=True, default='cm')
    parcel_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True,
                                        default=0.0001)
    parcel_weight_unit = models.CharField(max_length=50, verbose_name='重量单位', null=True, blank=True, default='kg')
    parcel_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
                                        default=0.000001)
    tracking_num = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True)
    customer_order = models.ForeignKey(ParcelCustomerOrder, related_name='parcel', on_delete=models.DO_NOTHING,
                                       verbose_name='客户订单号', null=True,
                                       blank=True)
    parcel_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True, default=1)
    is_electronic = models.BooleanField(verbose_name='是否带电', default=False)
    # 内件性质, 0:普货 1:带电 2: 危险品 3:液体 4:粉末 5:带磁 6：敏感 7：特敏 EE:经济带电 SE:标准带电 SG:标准普货 EG:经济普货
    classification = models.CharField(verbose_name='内件性质', null=True, blank=True, default='0', max_length=10)
    label_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='打单重量', null=True, blank=True,
                                       default=0)
    is_open_box = models.BooleanField(verbose_name='是否开箱', default=False)

    # 韩进
    # Parcel：包裹， File：文件
    parcel_type = models.CharField(max_length=10, verbose_name='包裹类型', default='Parcel', null=True, blank=True)

    # 货物属性 同 内件性质 （用字典）

    parcel_photo = models.CharField(max_length=255, verbose_name='包裹图片JPG', null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['customer_order', 'del_flag']),
        ]
        verbose_name = '包裹'
        verbose_name_plural = '包裹'

    def __str__(self):
        return self.parcel_num


# 小包订单包裹商品表
class ParcelOrderItem(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    item_code = models.CharField(max_length=256, verbose_name='商品SKU编码', null=True, blank=True, default='0')
    item_name = models.CharField(max_length=256, verbose_name='商品SKU名称', null=True, blank=True, default='0')
    # 中文品名
    declared_nameCN = models.CharField(max_length=256, verbose_name='中文申报品名', null=True, blank=True)
    # 英文品名
    declared_nameEN = models.CharField(max_length=256, verbose_name='英文申报品名', null=True, blank=True)

    sale_currency = models.CharField(max_length=10, verbose_name='报价币种', default='CNY', null=True,
                                     blank=True)
    sale_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='销售价格', null=True, blank=True,
                                     default=1)
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='总价格', null=True, blank=True,
                                      default=1)
    declared_currency = models.CharField(max_length=10, verbose_name='申报币种', default='USD',
                                         null=True,
                                         blank=True)
    # 单价
    declared_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='申报价格', null=True,
                                         blank=True,
                                         default=1)

    item_length = models.FloatField(verbose_name='长', null=True, blank=True, default=1)
    item_width = models.FloatField(verbose_name='宽', null=True, blank=True, default=1)
    item_height = models.FloatField(verbose_name='高', null=True, blank=True, default=1)
    size_unit = models.CharField(max_length=50, verbose_name='尺寸单位', null=True, blank=True, default='cm')
    item_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
                                      default=0.000001)
    item_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True,
                                      default=0.0001)
    weight_unit = models.CharField(max_length=50, verbose_name='重量单位', null=True, blank=True, default='kg')
    item_qty = models.IntegerField(verbose_name='数量', null=True, blank=True, default=1)

    # 材质
    texture = models.CharField(max_length=50, verbose_name='材质', null=True, blank=True)
    item_size = models.CharField(max_length=50, verbose_name='尺寸', null=True, blank=True)
    use = models.CharField(max_length=50, verbose_name='用途', null=True, blank=True)
    brand = models.CharField(max_length=50, verbose_name='品牌', null=True, blank=True)
    model = models.CharField(max_length=50, verbose_name='型号', null=True, blank=True)

    # 海关编码 字段废弃，新版用in_HScode，没有在取customs_code
    customs_code = models.CharField(max_length=50, verbose_name='海关编码', null=True, blank=True)
    fba_no = models.CharField(max_length=50, verbose_name='FBA号', null=True, blank=True)
    fba_track_code = models.CharField(max_length=50, verbose_name='FBA货物追踪编号', null=True, blank=True)
    parcel_num = models.ForeignKey(ParcelOrderParcel, related_name='parcelItem', verbose_name='包裹号',
                                   on_delete=models.DO_NOTHING, null=True, blank=True)
    sku_url = models.CharField(max_length=250, verbose_name='商品链接', null=True, blank=True)
    origin_country = models.CharField(max_length=10, verbose_name='原产国', null=True, blank=True)
    trade_methods = models.CharField(max_length=10, verbose_name='贸易方式', null=True, blank=True)
    trade_type = models.CharField(max_length=10, verbose_name='交易类型', null=True, blank=True)

    item_en_desc = models.CharField(max_length=300, verbose_name='商品英文描述', null=True, blank=True)
    item_desc = models.CharField(max_length=300, verbose_name='商品描述', null=True, blank=True)
    distribution_remark = models.CharField(max_length=300, verbose_name='配货备注', null=True, blank=True)
    net_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='净重', null=True, blank=True,
                                     default=0.0001)
    in_HScode = models.CharField(max_length=50, verbose_name='目的地进口海关编码', null=True, blank=True)
    out_HScode = models.CharField(max_length=50, verbose_name='始发地出口海关编码', null=True, blank=True)
    out_declared_price = models.DecimalField(decimal_places=3, max_digits=10, verbose_name='出口申报单价', null=True,
                                             blank=True, default=1)
    out_declared_currency = models.CharField(max_length=50, verbose_name='出口申报币种', null=True, blank=True,
                                             default='USD')

    # 产品特性相关字段
    battery_type = models.CharField(max_length=50, verbose_name='电池类型', null=True, blank=True)
    liquid_type = models.CharField(max_length=50, verbose_name='液体', null=True, blank=True)
    product_type = models.CharField(max_length=50, verbose_name='包裹的产品类型', null=True, blank=True)
    parcel_type = models.CharField(max_length=50, verbose_name='类型', null=True, blank=True)
    customs_value_type = models.CharField(max_length=50, verbose_name='海关价值类型', null=True, blank=True)
    vat_rate = models.CharField(max_length=50, verbose_name='增值税率', null=True, blank=True)
    transport_cost = models.CharField(max_length=50, verbose_name='运输成本', null=True, blank=True)
    insurance_cost = models.CharField(max_length=50, verbose_name='保险费用', null=True, blank=True)

    # 用于存储修改后清关信息
    label_declared_nameCN = models.CharField(max_length=256, verbose_name='中文申报品名-改后', null=True, blank=True)
    label_declared_nameEN = models.CharField(max_length=256, verbose_name='英文申报品名-改后', null=True, blank=True)
    label_customs_code = models.CharField(max_length=50, verbose_name='海关编码-改后', null=True, blank=True)
    label_item_qty = models.IntegerField(verbose_name='数量-改后', null=True, blank=True, default=1)
    label_declared_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='申报价格-改后', null=True,
                                         blank=True,
                                         default=1)
    label_declared_currency = models.CharField(max_length=10, verbose_name='申报币种-改后', default='USD',
                                         null=True,
                                         blank=True)
    label_item_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量-改后', null=True, blank=True,
                                      default=0.0001)


    class Meta:
        indexes = [
            models.Index(fields=['parcel_num', 'del_flag']),
        ]
        verbose_name = '商品'
        verbose_name_plural = '商品'

    def __str__(self):
        return f'{self.declared_nameEN}'


class ParcelOrderLabelTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('HandledBy3rdNo', '已提交'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
        ('ConfirmLabel', '已确认面单'),
        ('WaitForConfirm', '等待确认')
    ]
    order_num = models.ForeignKey(ParcelCustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                  related_name='parcelOrderLabelTasks')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='订单状态')
    label_desc = models.CharField(max_length=2048, verbose_name='抓取面单描述', null=True, blank=True)
    third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True)
    product_code = models.CharField(max_length=100, verbose_name='产品编码', null=True, blank=True)
    warehouse_code = models.CharField(max_length=100, verbose_name='仓库编码', null=True, blank=True)
    parcel = models.ForeignKey(ParcelOrderParcel, verbose_name='包裹号', on_delete=models.DO_NOTHING, null=True,
                               blank=True)
    shipment_digest = models.TextField(verbose_name='第三方digest', null=True, blank=True)
    is_submit_order = models.BooleanField(verbose_name='是否已提交订单', default=False)
    is_push = models.BooleanField(verbose_name='是否推送', default=False)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    is_master_product = models.BooleanField(verbose_name='是否主产品', default=False)
    pull_status = models.CharField(max_length=100, verbose_name='拉单状态', null=True, blank=True)
    is_secondary = models.BooleanField(verbose_name='是否二次拉单', default=False)
    is_need_confirm = models.BooleanField(verbose_name='是否需要确认', default=False)
    service = models.ForeignKey(Service, on_delete=models.DO_NOTHING, verbose_name='服务资源', null=True, blank=True)
    is_cancelled = models.BooleanField(verbose_name='是否已取消', null=True, default=False)

    class Meta:
        indexes = [
            models.Index(fields=['mode_key', 'status', 'del_flag', 'handle_times', 'create_date']),
        ]
        verbose_name_plural = '小包订单面单任务表'
        verbose_name = '小包订单面单任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


class ParcelOrderLabel(BaseEntity):
    order_num = models.ForeignKey(ParcelCustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                  related_name='parcelOrderLabels')
    tracking_no = models.CharField(max_length=60, verbose_name='跟踪号', null=True, blank=True)
    third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)
    label_url = models.CharField(max_length=255, verbose_name='面单URL', null=True, blank=True)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    is_master_product = models.BooleanField(verbose_name='是否主产品', default=False)
    is_secondary = models.BooleanField(verbose_name='是否二次拉单', default=False)
    sort_code = models.BigIntegerField(verbose_name='排序值', default=0)
    is_sync_wms = models.BooleanField(verbose_name='是否同步wms', default=False, null=True, blank=True)
    is_change_label = models.BooleanField(verbose_name='是否更改面单', default=False, null=True, blank=True)  # 是否更改面单

    class Meta:
        indexes = [
            models.Index(fields=['order_num', 'del_flag']),
            models.Index(fields=['third_order_no', 'del_flag']),
            models.Index(fields=['tracking_no', 'del_flag']),
        ]

        verbose_name_plural = '小包订单面单表'
        verbose_name = '小包订单面单表'

    def __str__(self):
        return self.order_num


# 清关报关单
class ClearanceOrder(BaseEntity):
    STATUS = [
        ('WC', '待清关'),
        ('HC', '已清关'),
    ]
    warehouse_date = models.DateField(verbose_name='到库日期', null=True, blank=True)
    sys_code = models.CharField(max_length=50, verbose_name='系统代码', null=True, blank=True)
    master_no = models.CharField(max_length=50, verbose_name='主单号', null=True, blank=True, unique=True)
    customer_no = models.CharField(max_length=100, verbose_name='客户编码', null=True, blank=True)
    weight = models.DecimalField(verbose_name='重量', max_digits=16, decimal_places=4, null=True, blank=True)
    pick_boxes = models.IntegerField(verbose_name='从海关提货的箱数', null=True, blank=True)
    forecast_boxes = models.IntegerField(verbose_name='预报箱数', null=True, blank=True)
    status = models.CharField(max_length=10, choices=STATUS, verbose_name='清关状态', null=True, blank=True,
                              default='WC')
    parcel_qty = models.IntegerField(verbose_name='总包裹数量', null=True, blank=True)
    proper_qty = models.IntegerField(verbose_name='总包裹数量', null=True, blank=True)
    clear_date = models.DateField(verbose_name='清关日期', null=True, blank=True)
    delivery_date = models.DateField(verbose_name='送货日期', null=True, blank=True)
    destination = models.CharField(max_length=50, verbose_name='目的地', null=True, blank=True)
    eta_time = models.DateTimeField(verbose_name='估计到达时间', null=True, blank=True)
    cmr_no = models.CharField(max_length=50, verbose_name='CMR Number', null=True, blank=True)


# 进口报关单商品汇总表
class ClearanceInSku(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    item_code = models.CharField(max_length=256, verbose_name='物品号', null=True, blank=True)
    declared_nameCN = models.CharField(max_length=256, verbose_name='中文申报品名', null=True, blank=True)
    declared_nameEN = models.CharField(max_length=256, verbose_name='英文申报品名', null=True, blank=True)
    customs_code = models.CharField(max_length=50, verbose_name='海关编码', null=True, blank=True)
    item_qty = models.IntegerField(verbose_name='数量', null=True, blank=True)
    declared_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='申报价格', null=True,
                                         blank=True)
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='申报总价', null=True, blank=True)
    item_length = models.FloatField(verbose_name='长', null=True, blank=True)
    item_width = models.FloatField(verbose_name='宽', null=True, blank=True)
    item_height = models.FloatField(verbose_name='高', null=True, blank=True)
    total_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='总重量', null=True, blank=True)
    volume_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='体积重', null=True, blank=True)
    use = models.CharField(max_length=50, verbose_name='用途', null=True, blank=True)
    brand = models.CharField(max_length=50, verbose_name='品牌', null=True, blank=True)
    model = models.CharField(max_length=50, verbose_name='型号', null=True, blank=True)
    texture = models.CharField(max_length=50, verbose_name='材质', null=True, blank=True)
    clearance = models.ForeignKey(Clearance, verbose_name='进口报关单', on_delete=models.DO_NOTHING)
    parcel_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True)

    class Meta:
        verbose_name = '进口报关单商品汇总'
        verbose_name_plural = '进口报关单商品汇总'


# 出口报关单商品汇总表
class ClearanceOutSku(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]
    item_code = models.CharField(max_length=256, verbose_name='物品号', null=True, blank=True)
    declared_nameCN = models.CharField(max_length=256, verbose_name='中文申报品名', null=True, blank=True)
    declared_nameEN = models.CharField(max_length=256, verbose_name='英文申报品名', null=True, blank=True)
    customs_code = models.CharField(max_length=50, verbose_name='海关编码', null=True, blank=True)
    item_qty = models.IntegerField(verbose_name='数量', null=True, blank=True)
    declared_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='申报价格', null=True,
                                         blank=True)
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='申报总价', null=True, blank=True)
    item_length = models.FloatField(verbose_name='长', null=True, blank=True)
    item_width = models.FloatField(verbose_name='宽', null=True, blank=True)
    item_height = models.FloatField(verbose_name='高', null=True, blank=True)
    total_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='总重量', null=True, blank=True)
    volume_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='体积重', null=True, blank=True)
    use = models.CharField(max_length=50, verbose_name='用途', null=True, blank=True)
    brand = models.CharField(max_length=50, verbose_name='品牌', null=True, blank=True)
    model = models.CharField(max_length=50, verbose_name='型号', null=True, blank=True)
    texture = models.CharField(max_length=50, verbose_name='材质', null=True, blank=True)
    clearance = models.ForeignKey(ClearanceOut, verbose_name='出口报关单', on_delete=models.DO_NOTHING)
    parcel_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True)

    class Meta:
        verbose_name = '出口报关单商品汇总'
        verbose_name_plural = '出口报关单商品汇总'


# 跨境煲保险单
class KuaJingBao(BaseEntity):
    STATUS = [
        ('WO', '待处理'),
        ('FC', '处理完成'),
        ('VO', '处理失败'),
    ]
    AUTH_STATUS = [
        (0, '待审核'),
        (1, '审核通过'),
        (2, '审核拒绝'),
        (10, '已承保'),
    ]
    order_num = models.CharField(max_length=30, verbose_name='订单号')
    request_data = models.CharField(max_length=5000, verbose_name='出参', null=True, blank=True)
    response_data = models.CharField(max_length=5000, verbose_name='入参', null=True, blank=True)
    status = models.CharField(max_length=10, choices=STATUS, null=True, default='WO', verbose_name='订单状态')
    auth_status = models.CharField(max_length=10, choices=STATUS, null=True, default=0, verbose_name='审核状态')
    handle_msg = models.CharField(max_length=150, verbose_name='处理信息', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='处理次数', default=0, null=True, blank=0)

    class Meta:
        verbose_name = '跨境堡'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


# 批量称重记录
class BulkParcelWeightRecord(BaseEntity):
    FILE_STATUS = [
        ('PR', "处理中"),
        ('SU', '完成')
    ],
    EXCEL_TYPE = [
        ('SMALL', '小包'),
        ('LARGE', '大包'),
        ('FBA', 'FBA'),
    ]
    RECORD_TYPE = [
        ('WEIGHING', '称重'),
        ('PICK', '装箱'),
        ('REPLACE_LABEL', '换标'),
        ('INSERT', '轨迹插入'),
        ('TRACK', '修改跟踪号'),
    ]

    excel_file_name = models.CharField(max_length=100, verbose_name='文件名')
    excel_file = models.FileField(blank=True, null=True, upload_to='files/%Y/%m/%d', verbose_name='文件数据')
    create_at = models.DateTimeField(verbose_name='创建时间', null=True, blank=True)
    status = models.CharField(max_length=10, choices=FILE_STATUS, verbose_name='处理状态')
    types = models.CharField(max_length=10, blank=True, null=True, verbose_name='类别')
    record_type = models.CharField(max_length=30, choices=RECORD_TYPE, null=True, default='WEIGHING',
                                   verbose_name='记录类型')

    class Meta:
        verbose_name = '小包批量称重记录'
        verbose_name_plural = '小包批量称重记录'


# 预报表
class CustomerForecast(BaseEntity):
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, verbose_name='公司', null=True, blank=True)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    item_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='体积', null=True, blank=True,
                                      default=0.000001)
    item_weight = models.DecimalField(max_digits=15, decimal_places=4, verbose_name='重量', null=True, blank=True,
                                      default=0.0001)
    item_qty = models.IntegerField(verbose_name='数量', null=True, blank=True, default=1)
    warehouse_code = models.ForeignKey(Address, on_delete=models.DO_NOTHING, verbose_name='仓库编码', null=True,
                                       blank=True)
    is_remote = models.BooleanField(verbose_name='是否偏远地区', default=False, null=True, blank=True)


# 订单excel上传任务表
class OrderSyncUploadTask(BaseEntity):
    ORDER_TYPE = [
        ('TR', '运输'),
        ('PC', '小包'),
        ('CPC', '客户小包'),  # 保留每个小包订单的执行结果
        ('BP', '大包'),
        ('CL', '清关'),
        ('OW', '海外仓'),
    ]
    GOODS_TYPE = [
        ('COM', '普通'),
        ('SINGLE', '单一商品'),
        ('MULTI', '多订单'),
    ]
    TASK_TYPE = [
        ('UploadOrderExcel', '导入订单Excel'),
        ('UploadParcelVoucher', '上传包裹面单'),
        ('BatchWeighting', '批量称重'),
        ('BatchPick', '批量装箱'),
        ('ChangedUploadOrderExcel', '批量更改小包清关信息'),  # 用于批量更改小包的清关信息
    ]
    STATUS = [
        ('Waiting', '等待中'),
        ('Processed', '处理中'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]
    file_name = models.CharField(max_length=100, verbose_name='文件名称', null=True, blank=True)
    order_num = models.CharField(max_length=100, verbose_name='业务单据号', null=True, blank=True)
    file_md5 = models.CharField(max_length=64, verbose_name='文件md5', null=True, blank=True)
    order_type = models.CharField(max_length=10, choices=ORDER_TYPE, verbose_name='单据类型', null=True, blank=True)
    task_type = models.CharField(max_length=32, choices=TASK_TYPE, verbose_name='任务类型', null=True, blank=True)
    goods_type = models.CharField(max_length=32, choices=GOODS_TYPE, verbose_name='商品类型', default='COM',
                                  null=True, blank=True)
    url = models.FileField(upload_to='uploadFiles/%Y/%m/%d', null=True, blank=True, verbose_name='文件附件')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='Waiting', verbose_name='上传状态')
    task_desc = models.CharField(max_length=2048, verbose_name='任务描述', null=True, blank=True)
    # mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    execution_time = models.DurationField(verbose_name='处理用时', null=True, blank=True)
    # todo_x
    # execution_start_time = models.DateTimeField(verbose_name='执行开始时间', null=True, blank=True)

    class Meta:
        verbose_name_plural = '订单上传任务表'
        verbose_name = '订单上传任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


class OrderSyncUploadTaskResult(BaseEntity):
    """订单excel上传任务表结果表"""

    STATUS = [
        # ('Waiting', '等待中'),
        # ('Processed', '处理中'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
    ]
    task_id = models.ForeignKey(OrderSyncUploadTask, verbose_name='任务ID', on_delete=models.DO_NOTHING)
    customer_order_num = models.CharField(max_length=100, verbose_name='客户单号', null=True, blank=True)
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='Waiting', verbose_name='上传状态')

    class Meta:
        verbose_name_plural = '订单上传任务结果表'
        verbose_name = '订单上传任务结果表'
        ordering = ['-id']

    def __str__(self):
        return self.customer_order_num


class OrderAsyncTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('Waiting', '等待中'),
        ('Processed', '处理中'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]
    ORDER_TYPE = [
        ('TR', '运输'),
        ('CL', '清关'),
        ('PC', '小包'),
        ('CLO', '出口报关单'),
        ('MS', '空运主单'),
        ('OC', '海运提单'),
        ('CLI', '进口报关单'),
        ('TO', '卡派单'),
    ]
    TASK_TYPE = [
        ('BL', '计费'),
        ('CostFinish', '成本确认'),
        ('CostShare', '成本分摊'),
        # ('EP', '导出'),
        ('OrderStatus', '同步订单状态'),
    ]
    order_num = models.CharField(max_length=64, verbose_name='业务单据', null=True, db_index=True)
    customer_orderNum = models.CharField(max_length=64, verbose_name='客户订单号', null=True, blank=True)
    order_type = models.CharField(max_length=10, choices=ORDER_TYPE, verbose_name='订单类型', null=True, blank=True)
    task_type = models.CharField(max_length=24, choices=TASK_TYPE, verbose_name='任务类型', null=True, blank=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True, default=None)
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='任务状态')
    task_desc = models.CharField(max_length=2048, verbose_name='任务描述', null=True, blank=True)
    # third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True)
    execution_time = models.DurationField(verbose_name='处理用时', null=True, blank=True)

    class Meta:
        verbose_name_plural = '订单异步任务表'
        verbose_name = '订单异步任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


# 订单关联库位表
class RelateStorePosition(BaseEntity):
    scan_carton = models.IntegerField(verbose_name='件数', null=True, blank=True)
    customer_order = models.ForeignKey(CustomerOrder, verbose_name='客户订单', on_delete=models.DO_NOTHING, null=True,
                                       related_name='storePositionRelateParcelCustomerOrder')
    store_position = models.ForeignKey(StorePosition, verbose_name='库位', on_delete=models.DO_NOTHING, null=True,
                                       related_name='relateStorePosition')
    binding_time = models.DateTimeField(verbose_name='绑定时间', null=True, blank=True)
    unbind_time = models.DateTimeField(verbose_name='解绑时间', null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['customer_order', 'del_flag']),
            models.Index(fields=['store_position', 'del_flag']),
        ]
        verbose_name = '订单关联库位'
        verbose_name_plural = '订单关联库位'

    def __str__(self):
        return '%s: %s' % (self.customer_order, self.store_position)


class CustomsClearanceOrderSupplierTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('Waiting', '等待中'),
        ('Processed', '处理中'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]

    PARCELS_STATUS = [
        ('UnHandled', '未处理'),
        ('Success', '处理成功'),
    ]

    order_num = models.ForeignKey(CustomsClearanceOrder, on_delete=models.DO_NOTHING, null=True,
                                  related_name='customsClearanceOrderSupplierTasks')
    third_order_no = models.CharField(max_length=128, verbose_name='第三方服务商的订单号', null=True, blank=True)
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='任务状态')
    task_desc = models.TextField(verbose_name='任务描述', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True, default=0)
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True, default=1)

    transportType = models.CharField(max_length=10, verbose_name='运输类型', null=True, blank=True)
    customer = models.CharField(max_length=100, verbose_name='客户名称', null=True, blank=True)
    customer_code = models.CharField(max_length=100, verbose_name='客户编码', null=True, blank=True)
    statusChangeUtc = models.DateTimeField(verbose_name='UTC时间标识上次状态更改的日期和时间', null=True, blank=True)
    statusChangeLocalTime = models.DateTimeField(verbose_name='仓库当地时间标识上次状态改变的日期和时间', null=True,
                                                 blank=True)
    statusChangedHuman = models.CharField(max_length=50, verbose_name='时间的可读表示', null=True, blank=True)
    declarationType = models.CharField(max_length=30, verbose_name='海关申报类型', null=True, blank=True)
    parcels_status = models.CharField(max_length=30, choices=PARCELS_STATUS, null=True, default='UnHandled',
                                      verbose_name='包裹提交状态')

    class Meta:
        verbose_name_plural = '清关订单推送供应商任务表'
        verbose_name = '清关订单推送供应商任务表'
        ordering = ['-id']


class PushEmsOrder(BaseEntity):
    STATUS = [
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
    ]
    customer_order_num = models.ForeignKey(ParcelCustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='push_ems_order')
    am_num = models.CharField(max_length=100, verbose_name='AM单号')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='订单状态')
    label_desc = models.CharField(max_length=2048, verbose_name='抓取面单描述', null=True, blank=True)

    class Meta:
        verbose_name = '推送ems订单'
        verbose_name_plural = '推送ems订单'


class TruckInquiryPriceOrder(BaseEntity):
    STATUS = [
        ('DR', '草稿'),
        ('WO', '已提交'),
        ('Quoted', '已报价'),
        ('VO', '已作废'),
    ]
    UNIT = [
        ('weight', '重量'),
        ('volume', '体积'),
        ('chargeWeight', '计费重'),
        ('confirmChargeWeight', '确认计费重'),
        ('carton', '件数'),
        ('confirmChargeVolume', '确认计费体积'),
        ('bubble', '泡比')
    ]

    inquiry_num = models.CharField(max_length=64, verbose_name='询价单号', null=True, blank=True)
    inquiry_status = models.CharField(max_length=30, choices=STATUS, null=True, default='DR', verbose_name='询价单状态')
    customer = models.ForeignKey(Company, verbose_name='客户', on_delete=models.DO_NOTHING,
                                 related_name='inquiry_customer', null=True, blank=True)
    buyer_address = models.CharField(max_length=256, verbose_name='收件人地址', null=True, blank=True)
    carton_num = models.IntegerField(verbose_name='件数', null=True, blank=True)
    weight = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='重量', null=True, blank=True)
    volume = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='体积', null=True, blank=True)
    time_limit = models.CharField(max_length=100, verbose_name='时效要求', null=True, blank=True)
    inquiry_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='报价')
    revenue_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='收入价')
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)
    product = models.ForeignKey(Product, verbose_name='产品', on_delete=models.DO_NOTHING, null=True, blank=True)
    destination_port = models.ForeignKey(OceanPort, verbose_name='目的港', null=True, blank=True,
                                         on_delete=models.DO_NOTHING,
                                         related_name='truck_query_destination_port')
    buyer_country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True,
                                          default='US')
    buyer_postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)
    valuation_unit = models.CharField(max_length=30, choices=UNIT, verbose_name='计价单位', default='weight')
    unloading_platform = models.BooleanField(verbose_name='是否有卸货平台', default=False, null=True, blank=True)

    class Meta:
        verbose_name_plural = verbose_name = '卡派询价单'


class TruckInquiryPriceChargeIn(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
    ]

    customer_order_num = models.ForeignKey(TruckInquiryPriceOrder, related_name='chargeIns',
                                           on_delete=models.DO_NOTHING, null=True, verbose_name='卡派询价收入')
    # truck_inquiry = models.ForeignKey(TruckInquiryPriceOrder, on_delete=models.DO_NOTHING, null=True,
    #                                   verbose_name='模板名称', related_name='chargeIns')
    charge = models.ForeignKey(Charge, on_delete=models.DO_NOTHING, null=True, verbose_name='费用名称')
    charge_rate = models.DecimalField(max_digits=15, decimal_places=6, null=True, blank=True, verbose_name='单价')
    charge_count = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='数量')
    charge_total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='合计')
    currency_type = models.CharField(max_length=10, verbose_name='币种', default='CNY')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='付款方')
    current_exchange = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name='汇率')
    account_charge = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                         verbose_name='记账金额')
    charge_price = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True, verbose_name='计费价')
    is_system = models.BooleanField(verbose_name='系统添加', default=False)

    class Meta:
        verbose_name_plural = verbose_name = '卡派询价单收入明细'


class OcShipment(BaseEntity):
    STATUS = [
        ('WAITING_SENT', '待发货'),
        ('SHIPPED', '已发货'),
        ('IN_TRANSIT', '已在途'),
        ('RESERVED', '已预约'),
        ('PICKED', '已拣货'),
        ('ALREADY_DELIVERY', '已出库'),
        ('PRIORITY_SHIPMENT', '已送达'),
        ('VO', '已作废'),

        # 0226重新定义状态,下面为原状态
        ('ARR', '已到港'),
        ('ARR_CAN_BE_BOOKED', '已到港可约'),
        ('PENDING_DELIVERY', '待出库'),
        ('PART_DELIVERY', '部分出库'),

        # ('BIND_EXCEPTION', '绑定异常'),
        # ('SIN_AGENDAR', '可预约'),
        # ('LOCAL_PENDING_RESERVATION', '本土待约'),
        # ('AMAZON_PENDING_RESERVATION', '亚马逊待约'),
        # ('LOCAL_DELIVERY', '暂存海外仓'),
        # ('CANCELLED', '已作废'),
        # ('FIRST_RESERVE_THEN_CHANGE', '先约再改'),
    ]

    # SPECIAL_REQUIRE = [
    #     ('W', '等通知预约'),
    #     ('E', '尽快预约'),
    #     ('N', '预约月底'),
    # ]

    SHOP_TYPE = [
        ('local', '本土店'),
        ('cross_border', '跨境店'),
        ('amazon', '亚马逊仓'),
        ('PA', '私人地址'),
        ('SW', '暂存海外仓'),

        ('OTHER', 'OTHER'),
        ('no_shop', '无'),
    ]

    customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='shipment_customer_order_ref')

    shipment_num = models.CharField(max_length=100, verbose_name='系统货件单号', null=True, blank=True)
    shipment_id = models.CharField(max_length=100, verbose_name='货件号', null=True, blank=True)
    reference_id = models.CharField(max_length=100, verbose_name='店铺ID', null=True, blank=True)
    shop_type = models.CharField(max_length=100, verbose_name='美客多店铺类型', null=False, choices=SHOP_TYPE,
                                 default='no_shop')
    parcel_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True)
    status = models.CharField(max_length=30, verbose_name='货件状态', null=False, default='WAITING_SENT',
                              choices=STATUS)
    white_list_time = models.DateTimeField(verbose_name='通过白名单时间', null=True, blank=True)
    scheduled_time = models.DateTimeField(verbose_name='已预约时间', null=True, blank=True)
    expiration_time = models.DateTimeField(verbose_name='货件号过期时间', null=True, blank=True)
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户')
    sort_code = models.CharField(max_length=100, verbose_name='分拣号', null=True, blank=True)
    sort_no = models.IntegerField(verbose_name='序号', null=True, blank=True)
    mercado_status = models.CharField(max_length=100, verbose_name='美客多状态', null=True, blank=True)
    mercado_estimated_time = models.DateTimeField(verbose_name='美客多预约时间', null=True, blank=True)
    old_shipment_id = models.CharField(max_length=100, verbose_name='原货件号', null=True, blank=True)
    sort_time = models.DateTimeField(verbose_name='分拣码生成时间', null=True, blank=True)
    # 海外仓地址
    address_num = models.CharField(max_length=100, verbose_name='地址编码', null=True, blank=True)
    etaw = models.DateTimeField(verbose_name='预计到仓日期', null=True, blank=True)
    earliest_available_time = models.DateTimeField(verbose_name='最早可约时间', null=True, blank=True)
    special_require = models.CharField(max_length=100, verbose_name='特殊要求', null=True, blank=True)
    # 目前用作货件重量尺寸汇总展示
    pre_weight = models.DecimalField(decimal_places=2, max_digits=12, verbose_name='预计重量', null=True, blank=True)
    pre_volume = models.DecimalField(decimal_places=2, max_digits=20, verbose_name='预计体积', null=True, blank=True)
    weight = models.DecimalField(decimal_places=4, max_digits=12, verbose_name='重量', null=True, blank=True)
    volume = models.DecimalField(decimal_places=6, max_digits=15, verbose_name='体积', null=True, blank=True)

    class Meta:
        verbose_name = '货件管理'
        verbose_name_plural = '货件管理'


# 出仓指令(出库指令)
class OutboundInstruct(BaseEntity):
    STATUS = [
        ('DR', '草稿'),
        ('SM', '已提交'),
        ('CH', '已审核'),
        ('PO', '待出库'),
        ('OD', '已出库'),
        ('SI', '已送达'),
        ('VO', '已作废'),
    ]
    SYNC_STATUS = [
        ('waiting', '等待同步'),
        ('success', '同步成功'),
        ('failed', '同步失败'),
    ]
    outbound_num = models.CharField(max_length=60, verbose_name='出仓指令单号', null=True, blank=True)
    status = models.CharField(max_length=30, verbose_name='状态', null=False, default='DR', choices=STATUS)
    sync_status = models.CharField(max_length=10, verbose_name='订单同步状态', choices=SYNC_STATUS, default='waiting',
                                   null=True, blank=True)
    sync_desc = models.CharField(max_length=2048, verbose_name='任务描述', null=True, blank=True)
    customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='outbound_instruct_order_ref', verbose_name='客户订单号')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户')
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='产品')

    appointment_time = models.DateTimeField(verbose_name='预约时间', null=True, blank=True)
    appointment_letter = models.CharField(max_length=2000, verbose_name='预约信', null=True, blank=True)
    attachments = models.CharField(max_length=2000, verbose_name="附件", null=True, blank=True)
    # attachments = models.FileField(upload_to='outboundInstructs/%Y/%m/%d', verbose_name='附件', null=True, blank=True)

    is_change_label = models.BooleanField(verbose_name='是否换箱标', default=False)
    change_instruct = models.CharField(max_length=2000, verbose_name='换标指令', null=True, blank=True)
    new_shipment_id = models.CharField(max_length=60, verbose_name='新货件号', null=True, blank=True)
    change_box = models.IntegerField(verbose_name='换标箱数', null=True, blank=True)
    new_box_label = models.CharField(max_length=2000, verbose_name='新货件箱标', null=True, blank=True)
    is_change_product_label = models.BooleanField(verbose_name='是否换产品标', default=False)
    new_qty = models.IntegerField(verbose_name='换标产品数', null=True, blank=True)
    new_label = models.CharField(max_length=2000, verbose_name='新产品标', null=True, blank=True)
    outbound_box = models.IntegerField(verbose_name='出仓箱数', null=True, blank=True)
    # 海外仓地址
    address_num = models.CharField(max_length=50, verbose_name='地址编码', null=True, blank=True)
    dest_address_num = models.CharField(max_length=50, verbose_name='目的仓地址编码', null=True, blank=True)
    submit_time = models.DateTimeField(verbose_name='提交时间', null=True, blank=True)
    reason = models.TextField(verbose_name='原因', null=True, blank=True)
    vas_order_num = models.CharField(max_length=30, verbose_name='增值服务单号', null=True, blank=True, db_index=True)

    # 改变结构-一对多关系
    shipment_id = models.CharField(max_length=60, verbose_name='货件号', null=True, blank=True)

    oc_shipment = models.ForeignKey(OcShipment, on_delete=models.DO_NOTHING, null=True,
                                    related_name='outbound_instruct_shipment_ref')
    agent_contract = models.BooleanField(verbose_name='是否需代约', null=True, blank=True)

    class Meta:
        verbose_name = '出仓指令'
        verbose_name_plural = '出仓指令'


class OutboundInstructDetail(BaseEntity):
    outbound_instruct = models.ForeignKey(OutboundInstruct, on_delete=models.DO_NOTHING, null=True,
                                          related_name='outbound_instruct_detail_ref', verbose_name='出仓指令')
    oc_shipment = models.ForeignKey(OcShipment, on_delete=models.DO_NOTHING, null=True,
                                    related_name='outbound_instruct_detail_shipment_ref', verbose_name='货件管理')
    outbound_box = models.IntegerField(verbose_name='出仓箱数', null=True, blank=True)

    class Meta:
        verbose_name = '出仓指令详情'
        verbose_name_plural = '出仓指令详情'


class OutboundInstructParcel(BaseEntity):
    outbound_instruct = models.ForeignKey(OutboundInstruct, related_name='outbound_instruct_parcel_detail',
                                          verbose_name='出仓指令', on_delete=models.DO_NOTHING,
                                          null=True, blank=True)
    parcel = models.ForeignKey(Parcel, related_name='parcel_outbound_instruct_detail',
                               verbose_name='包裹号', on_delete=models.DO_NOTHING, null=True, blank=True)
    parcel_volume = models.DecimalField(max_digits=15, decimal_places=6, verbose_name='包裹体积', null=True, blank=True,
                                        default=0)

    class Meta:
        verbose_name = '出仓包裹详情'
        verbose_name_plural = '出仓包裹详情'


# 货件操作标签
class OcShipmentLabel(BaseEntity):
    LABEL_STATUS = [
        ('W', '未处理'),
        ('I', '进行中'),
        ('C', '已完成'),
    ]
    operate_tag = models.CharField(max_length=48, verbose_name='操作标签', null=True, blank=True)
    content = models.TextField(verbose_name='操作描述', null=True)
    status = models.CharField(max_length=10, verbose_name='状态', choices=LABEL_STATUS, default='W')
    oc_shipment = models.ForeignKey(OcShipment, on_delete=models.DO_NOTHING, null=True, blank=True,
                                    related_name='shipment_label_oc_shipment')
    customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True, blank=True,
                                           related_name='shipment_label_customer_order')
    complete_time = models.DateTimeField(verbose_name='完成时间', null=True, blank=True)


class SupplierOperationRecord(BaseEntity):
    customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='supplier_record_ref')
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户')
    supplier_type = models.CharField(max_length=50, verbose_name='供应商类型', null=True, blank=True)
    supplier_name = models.CharField(max_length=50, verbose_name='供应商名称', null=True, blank=True)
    operation_type = models.CharField(max_length=50, verbose_name='操作类型', null=True, blank=True)
    operation_name = models.CharField(max_length=50, verbose_name='操作名称', null=True, blank=True)
    result = models.BooleanField(verbose_name='结果', null=True, blank=True)
    request_content = models.TextField(verbose_name='请求报文', null=True, blank=True)
    response_content = models.TextField(verbose_name='响应报文', null=True, blank=True)

    class Meta:
        verbose_name = '供应商操作记录'
        verbose_name_plural = '供应商操作记录'


# 盘点单
class TakeStockOrder(BaseEntity):
    STATUS = [
        ('W', '待盘点'),
        ('I', '盘点中'),
        ('C', '已完结'),
    ]

    order_num = models.CharField(max_length=60, verbose_name='盘点单号', null=True, blank=True, db_index=True)
    status = models.CharField(max_length=30, verbose_name='状态', null=False, default='W', choices=STATUS,
                              db_index=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)
    parcel_qty = models.IntegerField(verbose_name='件数', null=True, blank=True, default=0)

    class Meta:
        verbose_name = '盘点单'
        verbose_name_plural = '盘点单'


# 盘点任务
class TakeStockJob(BaseEntity):
    STATUS = [
        ('W', '待盘点'),
        ('I', '盘点中'),
        ('C', '已完结'),
    ]

    job_num = models.CharField(max_length=60, verbose_name='盘点任务号', null=True, blank=True, db_index=True)
    take_stock_order = models.ForeignKey(TakeStockOrder, on_delete=models.DO_NOTHING, null=True,
                                         related_name='order_take_stock_job')
    status = models.CharField(max_length=30, verbose_name='状态', null=False, default='W', choices=STATUS,
                              db_index=True)
    # customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户')
    # 由于盘仓单不和订单关联, 一个盘仓单可以盘点多个订单下的包裹, 所以去掉订单字段
    # customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
    #                                        related_name='take_stock_job_ref')
    user = models.ForeignKey(UserProfile, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='操作员')
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)
    parcel_qty = models.IntegerField(verbose_name='件数', null=True, blank=True, default=0)

    class Meta:
        verbose_name = '盘点任务'
        verbose_name_plural = '盘点任务'


# 盘点任务详情
class TakeStockJobDetail(BaseEntity):
    take_stock_job = models.ForeignKey(TakeStockJob, on_delete=models.DO_NOTHING, null=True,
                                       related_name='take_stock_job_detail_ref')
    arrival_time = models.DateTimeField(verbose_name='到货时间', null=True, blank=True)
    parcel_qty = models.IntegerField(verbose_name='件数', null=True, blank=True, default=0)
    order_num = models.CharField(max_length=50, verbose_name='订单号', null=True, blank=True)

    class Meta:
        verbose_name = '盘点作业明细'
        verbose_name_plural = '盘点作业明细'


# 物流计划表
class LogisticsPlanning(BaseEntity):
    # SZX-LAX-LAXP-241020-001
    planning_code = models.CharField(max_length=64, verbose_name='计划名称', null=True, blank=True)
    # departure_warehouse = models.CharField(max_length=50, verbose_name='起运仓', null=True, blank=True)
    departure_warehouse = models.ForeignKey(Address, verbose_name='起运仓', null=True, blank=True,
                                            on_delete=models.DO_NOTHING,
                                            related_name='logistics_planning_departure_warehouse')
    destination_warehouse = models.ForeignKey(Address, verbose_name='目的仓', null=True, blank=True,
                                              on_delete=models.DO_NOTHING,
                                              related_name='logistics_planning_destination_warehouse')
    departure_port = models.ForeignKey(OceanPort, verbose_name='启运港', null=True, blank=True,
                                       on_delete=models.DO_NOTHING,
                                       related_name='logistics_planning_departure_port')
    destination_port = models.ForeignKey(OceanPort, verbose_name='目的港', null=True, blank=True,
                                         on_delete=models.DO_NOTHING,
                                         related_name='logistics_planning_destination_port')
    departure_warehouse_dt = models.DateTimeField(verbose_name='预计离仓时间', null=True, blank=True)
    destination_warehouse_dt = models.DateTimeField(verbose_name='预计到仓时间', null=True, blank=True)
    departure_port_dt = models.DateTimeField(verbose_name='预计离港时间', null=True, blank=True)
    destination_port_dt = models.DateTimeField(verbose_name='预计到港时间', null=True, blank=True)
    expected_sign_time = models.DateTimeField(verbose_name='预计签收时间', null=True, blank=True)

    class Meta:
        verbose_name = '物流计划表'
        verbose_name_plural = '物流计划表'

    def __str__(self):
        return self.planning_code


class OrderAPILog(BaseEntity):
    """渠道API日志"""
    TASK_TYPE = [
        ('scanForm', 'scanForm'),
        ('createOrder', 'createOrder'),
        ('getLabel', 'getLabel'),
    ]
    STATUS = [
        ('UnHandled', '未处理'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
    ]

    unique_id = models.CharField(max_length=100, verbose_name='唯一标识符/订单号', db_index=True)
    tracking_num = models.CharField(max_length=100, verbose_name='跟踪单号', null=True, blank=True)
    customer_order_num = models.CharField(max_length=100, verbose_name='客户单号', null=True, blank=True)
    service = models.ForeignKey(Service, verbose_name='资源', null=True, blank=True,
                                on_delete=models.DO_NOTHING, related_name='service_order_api_log')
    product = models.ForeignKey(Product, verbose_name='产品', null=True, blank=True,
                                on_delete=models.DO_NOTHING, related_name='product_order_api_log')
    supplier = models.ForeignKey(Company, verbose_name='供应商', null=True, blank=True,
                                 on_delete=models.DO_NOTHING, related_name='supplier_order_api_log')
    customer = models.ForeignKey(Company, verbose_name='客户', null=True, blank=True,
                                 on_delete=models.DO_NOTHING, related_name='customer_order_api_log')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='订单状态')
    task_name = models.CharField(max_length=90, verbose_name='服务类函数名', null=True, blank=True)
    url = models.CharField(max_length=150, verbose_name='请求url', null=True, blank=True)
    params = models.JSONField(verbose_name='载荷', null=True, blank=True)
    data = models.JSONField(verbose_name='响应', null=True, blank=True)
    headers = models.JSONField(verbose_name='请求头', null=True, blank=True)
    task_type = models.CharField(max_length=90, verbose_name='任务类型', choices=TASK_TYPE, null=True, blank=True)

    class Meta:
        verbose_name_plural = '订单API日志'
        verbose_name = '订单API日志'


class SyncWMSTasks(BaseEntity):
    """同步数据到WMS任务表"""
    TASK_TYPE = [
        (0, '合作伙伴'),
        (1, '产品'),
        (2, '小包单'),
        (3, '大包单'),
        (4, '大包单状态'),
        (5, '操作部用户'),
        (6, '组织架构'),
        (7, '小包单面单'),
        (8, '产品线路'),
        (9, '交干发运'),
    ]
    STATUS = [
        ('UnHandled', '未处理'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
    ]

    unique_id = models.CharField(max_length=100, verbose_name='唯一标识符', db_index=True)
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='订单状态')
    task_name = models.CharField(max_length=90, verbose_name='任务名', null=True, blank=True)
    params = models.JSONField(verbose_name='请求参数', null=True, blank=True)
    task_type = models.IntegerField(verbose_name='任务类型', choices=TASK_TYPE, default=0)

    class Meta:
        verbose_name_plural = '任务日志表'
        verbose_name = '任务日志表'

    def __str__(self):
        return f'{self.unique_id} | {self.task_name}'

    @staticmethod
    def create_company(unique_id: str, params: str, task_name='同步合作伙伴任务'):
        """合作伙伴"""
        if not settings.SYNC_WMS_HOST:
            return False
        return SyncWMSTasks.objects.create(unique_id=unique_id, params=params, task_name=task_name, task_type=0)

    @staticmethod
    def create_product(unique_id: str, params: str, task_name='同步产品任务'):
        """产品"""
        if not settings.SYNC_WMS_HOST:
            return False
        return SyncWMSTasks.objects.create(unique_id=unique_id, params=params, task_name=task_name, task_type=1)

    @staticmethod
    def create_small_order(unique_id: str, params: str, task_name='同步小包任务'):
        """小包"""
        if not settings.SYNC_WMS_HOST:
            return False
        return SyncWMSTasks.objects.create(unique_id=unique_id, params=params, task_name=task_name, task_type=2)

    @staticmethod
    def create_big_order(unique_id: str, params: str, task_name='同步大包任务'):
        """大包"""
        if not settings.SYNC_WMS_HOST:
            return False
        return SyncWMSTasks.objects.create(unique_id=unique_id, params=params, task_name=task_name, task_type=3)

    @staticmethod
    def big_order_change_status(unique_id: str, params: str, task_name='同步大包单调整袋子状态任务'):
        """调整大包状态"""
        if not settings.SYNC_WMS_HOST:
            return False
        return SyncWMSTasks.objects.create(unique_id=unique_id, params=params, task_name=task_name, task_type=4)

    @staticmethod
    def create_user(unique_id: str, params: str, task_name='同步操作部用户任务'):
        """操作部用户"""
        if not settings.SYNC_WMS_HOST:
            return False
        return SyncWMSTasks.objects.create(unique_id=unique_id, params=params, task_name=task_name, task_type=5)

    @staticmethod
    def sync_department(unique_id: str, params: str, task_name='同步组织架构'):
        """操作部用户"""
        if not settings.SYNC_WMS_HOST:
            return False
        return SyncWMSTasks.objects.create(unique_id=unique_id, params=params, task_name=task_name, task_type=6)

    @staticmethod
    def sync_product_line(unique_id: str, params: str, task_name='同步产品线路'):
        """产品线路"""
        if not settings.SYNC_WMS_HOST:
            return False
        return SyncWMSTasks.objects.create(unique_id=unique_id, params=params, task_name=task_name, task_type=8)

    @staticmethod
    def sync_outbound_status(unique_id: str, params: str, task_name='同步交干发运状态任务'):
        """调整交干发运状态"""
        if not settings.SYNC_WMS_HOST:
            return False
        return SyncWMSTasks.objects.create(unique_id=unique_id, params=params, task_name=task_name, task_type=9)

class PriceForecast(BaseEntity):
    """价格预测"""
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, verbose_name='产品', null=True, blank=True)
    receive_country = models.CharField(max_length=50, verbose_name='收件人国家', null=True, blank=True)
    receive_postcode = models.CharField(max_length=50, verbose_name='收件人邮编', null=True, blank=True)
    sender_country = models.CharField(max_length=50, verbose_name='发件人国家', null=True, blank=True)
    sender_postcode = models.CharField(max_length=50, verbose_name='发件人邮编', null=True, blank=True)
    parcel_height = models.CharField(max_length=30, verbose_name='包裹高', null=True, blank=True)
    parcel_length = models.CharField(max_length=30, verbose_name='包裹长', null=True, blank=True)
    parcel_weight = models.CharField(max_length=30, verbose_name='包裹重', null=True, blank=True)
    parcel_width = models.CharField(max_length=30, verbose_name='包裹宽', null=True, blank=True)
    result_fee = models.CharField(max_length=30, verbose_name='费用', null=True, blank=True)
    currency = models.CharField(max_length=30, verbose_name='币种', null=True, blank=True)
    charge_name = models.CharField(max_length=30, verbose_name='收费项目', null=True, blank=True)
    failure_explanation = models.CharField(max_length=300, verbose_name='失效说明', null=True, blank=True)
    # 内件性质, 0:普货 1:带电 2: 危险品 3:液体 4:粉末 5:带磁 EE:经济带电 SE:标准带电 SG:标准普货 EG:经济普货
    classification = models.CharField(verbose_name='货物属性', null=True, blank=True, default='0', max_length=10)
    # Parcel：包裹， File：文件
    parcel_type = models.CharField(max_length=10, verbose_name='包裹类型', default='Parcel', null=True, blank=True)
    shipper = models.ForeignKey(Address, verbose_name='发货地点', null=True, blank=True, on_delete=models.SET_NULL,
                                related_name='PriceForecastShipper')
    service = models.ForeignKey(Service, verbose_name='产品服务', on_delete=models.DO_NOTHING, null=True, blank=True)

    class Meta:
        verbose_name = '价格预测'
        verbose_name_plural = '价格预测'

    def __str__(self):
        return self.id


class HanJinWayBillNumber(BaseEntity):
    order = models.CharField(max_length=255, verbose_name='范围单号')
    mod_value = models.CharField(max_length=100, verbose_name='取模值')
    way_bill_num = models.CharField(max_length=255, verbose_name='运单号')

    @classmethod
    def insert_coding_data(cls, start_value, end_value):
        data_to_insert = []

        for n in range(0, end_value - start_value + 1):
            order = start_value + n
            mod_value = str(order % 7)
            way_bill_num = str(order) + mod_value
            data_to_insert.append(cls(order=order, mod_value=mod_value, way_bill_num=way_bill_num))

            if len(data_to_insert) >= 1000:
                with transaction.atomic():
                    cls.objects.bulk_create(data_to_insert)
                data_to_insert = []  # 清空列表

        if data_to_insert:
            with transaction.atomic():
                cls.objects.bulk_create(data_to_insert)

    class Meta:
        indexes = [
            models.Index(fields=['way_bill_num', 'del_flag'])
        ]
        verbose_name = '韩进运单号'
        verbose_name_plural = '韩进运单号'


# 退件单
class CustomerReturnOrder(BaseEntity):
    class ReturnOrderStatusChoices(models.TextChoices):
        DRAFT = 'DR', '草稿'
        SUBMITTED = 'SM', '已提交'
        APPROVED = 'AP', '已审核'
        ARRIVED = 'PD', '已到仓'
        RECEIVED = 'A', '已收货'
        INVALID = 'VO', '已作废'

    return_order_num = models.CharField(max_length=60, verbose_name='退件订单号', null=True, blank=True, db_index=True)
    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户')
    customer_order_num = models.ForeignKey(CustomerOrder, on_delete=models.DO_NOTHING, null=True,
                                           related_name='return_order_ref')
    return_status = models.CharField(max_length=10, verbose_name='退件订单状态', null=False,
                                     choices=ReturnOrderStatusChoices.choices,
                                     default=ReturnOrderStatusChoices.DRAFT.value, db_index=True)
    return_warehouse = models.ForeignKey(Address, verbose_name='海外仓', null=True, blank=True,
                                         on_delete=models.DO_NOTHING, related_name='customer_return_warehouse')
    return_type = models.CharField(max_length=50, verbose_name='退件类型', null=True, blank=True)
    return_service = models.CharField(max_length=50, verbose_name='退件服务', null=True, blank=True)
    return_reason = models.CharField(max_length=255, verbose_name='退件原因', null=True, blank=True)
    submit_time = models.DateTimeField(verbose_name='提交时间', null=True, blank=True)
    approve_time = models.DateTimeField(verbose_name='审核时间', null=True, blank=True)
    arrival_time = models.DateTimeField(verbose_name='到仓时间', null=True, blank=True)
    receive_time = models.DateTimeField(verbose_name='收货时间', null=True, blank=True)

    class Meta:
        verbose_name = '退件单'
        verbose_name_plural = verbose_name


class ReturnOrderShipmentDetail(BaseEntity):
    return_order = models.ForeignKey(CustomerReturnOrder, on_delete=models.DO_NOTHING, null=True,
                                     related_name='return_order_shipment_detail_ref')
    shipment_id = models.CharField(max_length=64, verbose_name='货件号', null=True, blank=True)
    shop_type = models.CharField(max_length=100, verbose_name='店铺类型', null=True, blank=True)
    reference_id = models.CharField(max_length=100, verbose_name='店铺ID', null=True, blank=True)
    box_qty = models.IntegerField(verbose_name='箱数', null=True, blank=True, default=0)

    class Meta:
        verbose_name = '退件单货件详情'
        verbose_name_plural = verbose_name


class ReturnOrderAttachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='returnOrders/%Y/%m/%d', null=True, blank=True, verbose_name='退件单附件')
    return_order = models.ForeignKey(CustomerReturnOrder, related_name='attachments', verbose_name='退件单',
                                     on_delete=models.DO_NOTHING, null=True)

    class Meta:
        verbose_name = '退件单附件'
        verbose_name_plural = verbose_name


class Shop(BaseEntity):
    class ShopStatusChoices(models.TextChoices):
        NO_AUTH = 'NO_AUTH', '未授权'
        AUTH_SUCCESS = 'AUTH_SUCCESS', '授权成功'
        AUTH_FAILED = 'AUTH_FAILED', '授权失败'

    PLATFORM = [
        ('shopify', 'shopify'),
    ]

    customer = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='客户')
    shop_name = models.CharField(max_length=100, default='', verbose_name='店铺名称', null=False, blank=False)
    shop_url = models.CharField(max_length=512, verbose_name='店铺地址', null=False, blank=False)
    platform = models.CharField(max_length=50, verbose_name='平台', null=False, blank=False)
    status = models.CharField(max_length=20, verbose_name='状态', choices=ShopStatusChoices.choices,
                              default=ShopStatusChoices.NO_AUTH.value, db_index=True)
    access_token = models.CharField(max_length=255, verbose_name='授权token', blank=True, null=True)

    class Meta:
        verbose_name = '店铺授权信息'
        verbose_name_plural = verbose_name


class ClientApiLog(BaseEntity):
    """客户端api日志"""
    REQ_SOURCE = [
        ('api', 'api'),
        ('web', 'web'),
    ]
    interface_name = models.CharField(max_length=100, verbose_name='接口名称')
    request_source = models.CharField(max_length=20, verbose_name='请求来源', choices=REQ_SOURCE)
    request_documents = models.CharField(max_length=100, verbose_name='请求单据', null=True, blank=True)
    waybill_number = models.CharField(max_length=100, verbose_name='运单号', null=True, blank=True)
    delivery_order_number = models.CharField(max_length=100, verbose_name='派送单号', null=True, blank=True)
    product = models.CharField(max_length=100, verbose_name='产品')
    customer = models.CharField(max_length=100, verbose_name='客户')
    status = models.CharField(max_length=100, verbose_name='状态')
    url = models.CharField(max_length=512, verbose_name='接口url')
    request_header = models.JSONField(verbose_name='请求头')
    request_content = models.JSONField(verbose_name='请求内容')
    response_content = models.JSONField(verbose_name='响应内容')

    class Meta:
        indexes = [
            models.Index(fields=['interface_name', 'del_flag']),
            models.Index(fields=['waybill_number', 'del_flag']),
            models.Index(fields=['delivery_order_number', 'del_flag']),
        ]
        verbose_name = '客户端api日志'
        verbose_name_plural = '客户端api日志'


class Currency(BaseEntity):
    """货币信息"""
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    currency_type = models.CharField(max_length=20, unique=True, verbose_name='货币类型')
    status = models.CharField(max_length=20, choices=STATUS_TYPE, default='DR', verbose_name='状态')

    class Meta:
        verbose_name = '货币'
        verbose_name_plural = '货币列表'
        ordering = ['currency_type']

    def __str__(self):
        return self.currency_type


class LabelChangeConfig(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='名称', null=True, blank=True)
    is_enable = models.BooleanField(verbose_name='是否启用', default=True)
    service_code = models.CharField(max_length=100, verbose_name='服务编码', null=True, blank=True)
    product_code = models.CharField(max_length=100, verbose_name='产品编码', null=True, blank=True)
    warehouse_code = models.CharField(max_length=100, verbose_name='仓库编码', null=True, blank=True)

    class Meta:
        verbose_name_plural = '面单更改配置'
        verbose_name = '面单更改配置'
        ordering = ['-id']

    def __str__(self):
        return self.name or ''


class LabelChangeConfigDetail(BaseEntity):
    CONTENT_TYPE = (
        ('order_num', '订单号'),
        ('customer_num', '客户单号'),
        ('sku_and_qty', 'SKU * 数量'),

    )
    label_change_config = models.ForeignKey(LabelChangeConfig, verbose_name='面单更改配置',
                                            on_delete=models.DO_NOTHING, null=True, related_name='label_config_detail')
    x_coordinate = models.FloatField(verbose_name='X坐标')
    y_coordinate = models.FloatField(verbose_name='Y坐标')
    content_height = models.FloatField(verbose_name='区域高')
    content_width = models.FloatField(verbose_name='区域宽')
    content_type = models.CharField(max_length=64, choices=CONTENT_TYPE, verbose_name='填充内容类型', null=True,
                                    blank=True)
    content = models.CharField(max_length=255, verbose_name='填充内容', null=True, blank=True)
    is_center = models.BooleanField(verbose_name='是否居中', default=False)
    font_size = models.FloatField(verbose_name='字体大小')

    class Meta:
        verbose_name = '面单更改配置明细'
        verbose_name_plural = '面单更改配置明细'

    def __str__(self):
        return self.label_change_config.name or ''


class Webhook(BaseEntity):
    wb_periodic_task = models.ForeignKey(PeriodicTask, verbose_name='定时任务',
                                         on_delete=models.DO_NOTHING, related_name='periodic_task')
    wb_name = models.CharField(max_length=64, verbose_name='Webhook机器人备注', unique=True)
    wb_url = models.CharField(max_length=255, verbose_name='Webhook机器人地址')
    wb_content = models.CharField(max_length=255, verbose_name='传输消息内容')
    wb_crontab = models.JSONField(verbose_name="定时任务时间参数")
    wb_enabled = models.BooleanField(verbose_name="是否启用定时任务", default=True, blank=True)

    class Meta:
        verbose_name = 'Webhook'
        verbose_name_plural = 'Webhook'

    def __str__(self):
        return self.wb_name


class RmManifestConfig(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='名称', null=True, blank=True)
    is_enable = models.BooleanField(verbose_name='是否启用', default=True)
    product_code = models.CharField(max_length=100, verbose_name='产品编码', null=True, blank=True)
    carrier_code = models.CharField(max_length=100, verbose_name='承运商编码', null=True, blank=True)
    client_id = models.CharField(max_length=255, verbose_name='ClientId', null=True, blank=True)
    client_secret = models.CharField(max_length=255, verbose_name='ClientSecret', null=True, blank=True)
    shipping_location_id = models.CharField(max_length=100, verbose_name='location_id', null=True, blank=True)
    shipping_account_id = models.CharField(max_length=100, verbose_name='account_id', null=True, blank=True)
    manifest_url = models.CharField(max_length=100, verbose_name='url', null=True, blank=True)
    execute_time = models.DateTimeField(verbose_name='执行时间', null=True, blank=True)
    execute_result = models.TextField(verbose_name='执行结果', null=True, blank=True)

    class Meta:
        verbose_name_plural = 'RM Manifest配置表'
        verbose_name = 'RM Manifest配置表'
        ordering = ['-id']

    def __str__(self):
        return self.name or ''


class RmManifestResFile(BaseEntity):
    rm_manifest_config = models.ForeignKey(RmManifestConfig, on_delete=models.DO_NOTHING, null=True,
                                           related_name='rm_manifest_file')
    file_url = models.CharField(max_length=255, verbose_name='文件URL', null=True, blank=True)

    class Meta:
        verbose_name_plural = 'RM Manifest文件表'
        verbose_name = 'RM Manifest文件表'

    def __str__(self):
        return self.rm_manifest_config


class AmazonShipTrackTask(BaseEntity):
    # 用于向amazon 同步映射关系
    STATUS = [
        ('UnHandled', '未处理'),
        ('Failure', '失败'),
        ('Success', '成功'),
        ('VO', '已作废'),
    ]

    order_num = models.CharField(max_length=64, null=True, blank=True, verbose_name='FBA订单号')
    shipment_id = models.CharField(max_length=64, null=True, blank=True, verbose_name='ShipmentId')
    tracking_id = models.CharField(max_length=64, null=True, blank=True, verbose_name='TrackingId')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='同步状态')
    result_desc = models.CharField(max_length=2048, verbose_name='同步结果', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='重试次数', null=True, blank=True)
    execute_time = models.DateTimeField(verbose_name='最近一次执行时间', null=True, blank=True)

    class Meta:
        verbose_name_plural = 'AmaShipTrack服务对接任务表'
        verbose_name = 'AmaShipTrack服务对接任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num

    class PushApiConfig(BaseEntity):
        """推送API配置表"""

        push_company = models.ForeignKey('company.Company', on_delete=models.SET_NULL, null=True, blank=True,
                                         verbose_name='推送对象')
        push_service = models.ForeignKey('pms.ProductLine', on_delete=models.SET_NULL, null=True, blank=True,
                                         verbose_name='推送线路')
        push_product = models.ForeignKey('pms.Product', on_delete=models.SET_NULL, null=True, blank=True,
                                         verbose_name='推送产品')
        supplier_api_setting = models.ForeignKey('company.SupplierApiSetting', on_delete=models.SET_NULL,
                                                 verbose_name='API接口', null=True, blank=True,
                                                 related_name='push_api_configs')
        push_type = models.CharField(max_length=64, null=True, blank=True, verbose_name='推送任务类型')
        push_api = models.CharField(max_length=64, null=True, blank=True, verbose_name='推送API')
        trigger_action = models.CharField(max_length=64, null=True, blank=True, verbose_name='触发动作')

        class Meta:
            verbose_name = 'API推送配置'
            verbose_name_plural = verbose_name
            ordering = ['-id']

        def __str__(self):
            return f'{self.push_company}-{self.push_type}'

    class PushResourcesSetting(BaseEntity):
        """推送API配置表关联"""

        service = models.ForeignKey('order.PushApiConfig', verbose_name='资源', null=True, blank=True,
                                    on_delete=models.SET_NULL,
                                    related_name='push_api_settings')

        field_name = models.CharField(max_length=50, verbose_name='字段名称', null=True, blank=True)
        field_value = models.CharField(max_length=500, verbose_name='字段值', null=True, blank=True)

        class Meta:
            ordering = ['-id']
            verbose_name = '单号规则api配置'
            verbose_name_plural = '单号规则api配置'

        def __str__(self):
            return f'{self.service}:{self.field_name}'


class WsComMaterials(BaseEntity):
    """仓库物料表模型"""
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    TYPE = [
        (1, '纸箱'),
        (2, '编织袋'),
        (3, '木质托盘'),
        (4, '塑料托盘'),
    ]

    code = models.CharField(max_length=36, null=True, blank=True, verbose_name='物料代码')
    name = models.CharField(max_length=36, null=True, blank=True, verbose_name='物料名称')
    type = models.SmallIntegerField(choices=TYPE, null=True, blank=True, verbose_name='物料类型')
    warehouse = models.ForeignKey(Organization, verbose_name='所属仓库', on_delete=models.DO_NOTHING, null=True)
    weight = models.DecimalField(verbose_name='重量(kg)', null=True, blank=True, decimal_places=2, max_digits=10)
    length = models.DecimalField(verbose_name='长(cm)', null=True, blank=True, decimal_places=2, max_digits=10)
    width = models.DecimalField(verbose_name='宽(cm)', null=True, blank=True, decimal_places=2, max_digits=10)
    height = models.DecimalField(verbose_name='高(cm)', null=True, blank=True, decimal_places=2, max_digits=10)
    volume = models.DecimalField(verbose_name='体积(立方厘米)', null=True, blank=True, decimal_places=2, max_digits=30)
    untitled = models.DecimalField(verbose_name='成本价格(CNY)', null=True, blank=True, decimal_places=2, max_digits=10)
    status = models.CharField(verbose_name='状态', max_length=3, default='DR')

    class Meta:
        ordering = ['-id']
        verbose_name = '仓库物料'
        verbose_name_plural = '仓库物料'
        indexes = [
            models.Index(fields=['code', 'status', 'del_flag']),
        ]

    def __str__(self):
        return f'<WsComMaterials {self.code}>'

    def save(self, *args, **kwargs):
        # 自动计算体积（创建/更新时均触发）
        if all([self.length, self.width, self.height]):
            self.volume = (self.length * self.width * self.height)
        else:
            self.volume = None
        super().save(*args, **kwargs)


class PushWishPickUpInfoTask(BaseEntity):
    # 用于向 wishpost推送 揽收信息
    STATUS = [
        ('UnHandled', '未处理'),
        ('Failure', '失败'),
        ('Success', '成功'),
        ('VO', '已作废'),
    ]

    order_num = models.CharField(max_length=64, null=True, blank=True, verbose_name='订单号')
    status = models.CharField(max_length=30, choices=STATUS, null=True, default='UnHandled', verbose_name='同步状态')
    result_desc = models.CharField(max_length=2048, verbose_name='推送结果', null=True, blank=True)
    mode_key = models.CharField(max_length=2, verbose_name='取模值', null=True, blank=True)
    handle_times = models.IntegerField(verbose_name='处理次数', null=True, blank=True)
    execute_time = models.DateTimeField(verbose_name='最近一次执行时间', null=True, blank=True)

    class Meta:
        verbose_name_plural = '推送wishpost 揽收信息任务表'
        verbose_name = '推送wishpost 揽收信息任务表'
        ordering = ['-id']

    def __str__(self):
        return self.order_num


class FbaProductDeliveryTimeLimit(BaseEntity):
    """产品运输时效配置表"""
    product_code = models.CharField(max_length=50, verbose_name='产品编码', null=True, blank=True)
    product_name = models.CharField(max_length=100, verbose_name='产品名称', null=True, blank=True)
    warehouse_to_delivery = models.IntegerField(verbose_name='入仓-运达(天)', null=True, blank=True)
    customs_to_delivery = models.IntegerField(verbose_name='报关-运达(天)', null=True, blank=True)
    departure_to_delivery = models.IntegerField(verbose_name='离港-运达(天)', null=True, blank=True)
    arrival_to_delivery = models.IntegerField(verbose_name='到港-运达(天)', null=True, blank=True)
    clearance_to_delivery = models.IntegerField(verbose_name='清关-运达(天)', null=True, blank=True)
    unpacking_to_delivery = models.IntegerField(verbose_name='拆柜-运达(天)', null=True, blank=True)
    
    class Meta:
        verbose_name = 'FBA产品运输时效配置'
        verbose_name_plural = 'FBA产品运输时效配置'
        ordering = ['-id']
        indexes = [
            models.Index(fields=['product_code', 'del_flag']),
            models.Index(fields=['product_name', 'del_flag']),
        ]
    
    def __str__(self):
        return self.product_name or ''


# dmas消息推送任务表
class DmasMsgSendTask(BaseEntity):
    STATUS = [
        ('UnHandled', '未处理'),
        ('Waiting', '等待中'),
        ('Processed', '处理中'),
        ('Success', '处理成功'),
        ('Failure', '处理失败'),
        ('VO', '已作废'),
    ]

    MSG_TYPE = [
        ('text', '文本消息'),
        ('image', '图片消息'),
    ]

    SEND_TYPE = [
        ('sync', '原始文本'),
        ('async', 'AI渲染'),
    ]

    PUSH_TYPE = [
        ('common', '普通推送'),
        ('customer_code', '客户编码推送'),
    ]

    BUSINESS_TYPE = [
        ('mz', '铭志'),
        ('clt', '凯乐通'),
    ]

    order_id = models.IntegerField(verbose_name='单据id', null=True, blank=True)
    status = models.CharField(verbose_name='任务状态', max_length=30, choices=STATUS, null=True, default='UnHandled')
    msg_type = models.CharField(verbose_name='消息类型', max_length=32, choices=MSG_TYPE, null=True, default='text')
    mode_key = models.CharField(verbose_name='取模值', max_length=3, null=True, blank=True)
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True, default=None)
    handle_times = models.IntegerField(verbose_name='处理次数', null=True, blank=True, default=0)
    business_type = models.CharField(verbose_name='业务类型', max_length=32, choices=BUSINESS_TYPE, default='mz')
    send_type = models.CharField(verbose_name='发送模式', max_length=32, choices=SEND_TYPE, null=True, default='sync')
    push_type = models.CharField(verbose_name='推送类型', max_length=32, choices=PUSH_TYPE, null=True, default='common')
    message = models.CharField(verbose_name='文本消息', max_length=500, null=True, blank=True)
    image_url = models.FileField(upload_to='dmas/%Y%m%d', verbose_name='图片链接', null=True, blank=True)
    image_base64 = models.TextField(verbose_name='图片base64字符串', null=True, blank=True)
    message_group = models.TextField(verbose_name='推送群组', null=True, blank=True)
    at_list = models.TextField(verbose_name='@成员', null=True, blank=True)
    customer_code = models.CharField(verbose_name='客户编码', max_length=100, null=True, blank=True)
    task_desc = models.CharField(verbose_name='任务描述', max_length=2048, null=True, blank=True)

    class Meta:
        verbose_name_plural = 'dmas消息推送任务表'
        verbose_name = 'dmas消息推送任务表'
        ordering = ['-id']

    def __str__(self):
        return str(self.id)


class LocalSortingCode(models.Model):
    """本地分拣码"""
    zip_cod = models.CharField(max_length=80, verbose_name='邮编', db_index=True)
    zip_cit = models.CharField(max_length=255, verbose_name='zip_cit', null=True, blank=True)
    zip_gu = models.CharField(max_length=255, verbose_name='zip_gu', null=True, blank=True)
    zip_eup = models.CharField(max_length=255, verbose_name='zip_eup', null=True, blank=True)
    zip_sub = models.CharField(max_length=255, verbose_name='zip_sub', null=True, blank=True)
    zip_eng_cit = models.CharField(max_length=255, verbose_name='zip_eng_cit', null=True, blank=True)
    zip_eng_gu = models.CharField(max_length=255, verbose_name='zip_eng_gu', null=True, blank=True)
    zip_eng_eup = models.CharField(max_length=255, verbose_name='zip_eng_eup', null=True, blank=True)
    zip_eng_sub = models.CharField(max_length=255, verbose_name='zip_eng_sub', null=True, blank=True)
    hub_code = models.CharField(max_length=255, verbose_name='区域配送区 1', null=True, blank=True)
    tml_code = models.CharField(max_length=255, verbose_name='目的地站点编码', null=True, blank=True)
    hub_tml_cod = models.CharField(max_length=255, verbose_name='hub_tml_cod', null=True, blank=True)
    tml_nam = models.CharField(max_length=255, verbose_name='目的地站点名称', null=True, blank=True)
    dom_mid = models.CharField(max_length=255, verbose_name='区域配送区 2', null=True, blank=True)
    cen_cod = models.CharField(max_length=255, verbose_name='目标机构代码', null=True, blank=True)
    dom_cen_cod = models.CharField(max_length=255, verbose_name='dom_cen_cod', null=True, blank=True)
    cen_nam = models.CharField(max_length=255, verbose_name='主要目的地的办事处名称', null=True, blank=True)
    dom_pdz = models.CharField(max_length=255, verbose_name='区域配送区 3', null=True, blank=True)
    pdz_nam = models.CharField(max_length=255, verbose_name='目的地区域的名称', null=True, blank=True)
    es_nam = models.CharField(max_length=255, verbose_name='配送员姓名', null=True, blank=True)
    apl_ymd = models.CharField(max_length=255, verbose_name='apl_ymd', null=True, blank=True)

    class Meta:
        verbose_name_plural = '本地分拣码'
        verbose_name = '本地分拣码'
        ordering = ['-id']
