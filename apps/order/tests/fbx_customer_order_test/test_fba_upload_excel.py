import io
import os
import time
from datetime import datetime

import openpyxl
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework.test import APIClient

from company.models import Company, Supplier, Address
from order.models import (
    Product, Service, OrderSyncUploadTask, CustomerOrder, Parcel, OcShipment, ParcelItem
)
from pms.models import ProductZone, ProductZonePostCode, ProductRoute


class FbaCustomerOrderUploadExcelTest:
    # def setUp(self):
    def __init__(self):
        """Set up test data"""
        # Create test user
        User = get_user_model()

        # Create test company
        self.company = Company.objects.filter(name='TestCompany', short_name='TEST', is_customer=True, del_flag=False).last()
        if not self.company:
            self.company = Company.objects.create(
                name='TestCompany',
                short_name='TEST',
                is_customer=True,
            )

        # Create test user
        self.user = User.objects.filter(username='TestFbaUser', del_flag=False).last()
        if not self.user:
            self.user = User.objects.create_user(
                username='TestFbaUser',
                password='testpass123',
                company=self.company
            )

        # Create test product - 根据模板中的产品信息
        self.product = Product.objects.filter(
            name='志尊达',  # 对应模板中的产品编码
            code='PK0001',
            del_flag=False
        ).last()

        if not self.product:
            self.product = Product.objects.create(
                name='志尊达',
                code='PK0001',
                label_type='WC',
                is_open=True,
                is_valuation=True,
                charge_weight_rate=1,
            )

        # # Create test service
        # self.service = Service.objects.filter(name='TestService', code='TEST-SERVICE', del_flag=False).first()
        # if not self.service:
        #     self.service = Service.objects.create(
        #         name='TestService',
        #         code='TEST-SERVICE',
        #         product=self.product,
        #         is_default=True,
        #     )

        # # Create supplier
        # self.supplier = Supplier.objects.filter(name='TestSupplier', code='TEST-SUPPLIER', del_flag=False).first()
        # if not self.supplier:
        #     self.supplier = Supplier.objects.create(
        #         name='TestSupplier',
        #         code='TEST-SUPPLIER',
        #     )

        # Create test shipper - 发件人地址信息
        self.shipper = Address.objects.filter(
            address_num='ShenZhen',
            address_name='深圳仓',
            address_type='SP',
            del_flag=False
        ).last()

        if not self.shipper:
            self.shipper = Address.objects.create(
                address_num='ShenZhen',
                address_name='深圳仓',
                address_type='SP',
                contact_name='京东物流业务员',
                company_name='京东物流',
                address_one='深圳测试地址',
                city_code='深圳',
                state_code='广东',
                postcode='518000',
                contact_phone='12345678910',
                country_code='CN',
                status='ON',
            )

        # Create test receiver - FBA仓库地址（对应模板中的收件人信息）
        self.receiver = Address.objects.filter(
            address_num='TCY1',  # 对应模板中的FBA仓库代码
            address_name='FBA仓库',
            address_type='RC',
            del_flag=False
        ).last()

        if not self.receiver:
            self.receiver = Address.objects.create(
                address_num='TCY1',
                address_name='FBA仓库',
                address_type='RC',
                contact_name='FBA仓库管理员',
                company_name='Amazon FBA Warehouse',
                address_one='fba仓库测试地址',
                city_code='Irwindale',
                state_code='CA',
                postcode='91702',
                contact_phone='10987654321',
                country_code='US',
                status='ON',
            )

        # 创建中国分区（发件人）
        cn_zone = ProductZone.objects.filter(
            name='CN',
            country_code='CN',
            type='Seller',
            del_flag=False
        ).first()

        if not cn_zone:
            cn_zone = ProductZone.objects.create(
                name='CN',
                country_code='CN',
                type='Seller',
                zone_type='SZ',
                status='ON',
            )

        # 创建美国分区（收件人）
        us_zone = ProductZone.objects.filter(
            name='US',
            country_code='US',
            type='Buyer',
            del_flag=False
        ).first()

        if not us_zone:
            us_zone = ProductZone.objects.create(
                name='US',
                country_code='US',
                type='Buyer',
                zone_type='SZ',
                status='ON',
            )

        # 为中国分区配置邮编（深圳518000）
        ProductZonePostCode.objects.get_or_create(
            product_zone=cn_zone,
            country_code='CN',
            post_code='518000',
            type='AC',  # 精准邮编
            del_flag=False
        )

        # 为美国分区配置邮编（91702）
        ProductZonePostCode.objects.get_or_create(
            product_zone=us_zone,
            country_code='US',
            post_code='91702',
            type='AC',  # 精准邮编
            del_flag=False
        )

        # 创建产品路线连接两个分区
        ProductRoute.objects.get_or_create(
            product=self.product,
            start_zone=cn_zone,
            end_zone=us_zone,
            zone_value='TEST_ZONE_VALUE',  # 设置一个测试分区值
            route_type='COMMON',
            start_time=datetime.strptime('2025-07-01 00:00:00', '%Y-%m-%d %H:%M:%S'),  # 添加开始时间，设置为当前时间
            del_flag=False
        )

        # Create API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        print(settings.SYSTEM_VERSION)

    def fill_in_fba_excel(self):
        """下单前填写测试excel"""
        template_path = os.path.join(os.path.dirname(__file__), '../template', 'template_customer_fba_order.xlsx')
        workbook = openpyxl.load_workbook(template_path, data_only= True)
        sheet = workbook.active

        # 根据FBA模板截图填写数据
        sheet['B3'] = f'D{int(time.time())}'  # 跟踪号
        # 发件人信息区域 (左上角) - 使用创建的shipper数据
        sheet['B4'] = self.shipper.address_name       # Shipper发件人

        sheet['B9'] = self.product.name               # 产品名称
        sheet['F9'] = self.company.short_name              # 客户公司简称

        # 收件人信息区域 (右上角) - 使用创建的receiver数据
        sheet['I4'] = self.receiver.address_num       # FBA仓库代码
        
        # 生成随机shipment_id
        current_date = datetime.now()
        year_last_two = str(current_date.year)[-2:]
        microsecond_one_digit = str(current_date.microsecond // 100000)
        shipment_id = f"{year_last_two}{current_date.strftime('%m%d%H%M%S')}{microsecond_one_digit}"
        
        # 商品明细数据（从第12行开始填写，对应截图中的红色区域）
        # 填写第一行商品数据
        sheet['A12'] = f"FBA{shipment_id}001"         # 包号/FBA标签号
        sheet['B12'] = shipment_id                   # ShipmentId
        # sheet['C12'] = f"REF{shipment_id}001"         # Reference Id
        # sheet['D12'] = '20*15*10'                     # 材积CM(长*宽*高)
        # sheet['E12'] = 1.5                            # 重量KGS
        # sheet['F12'] = '8708999990'                   # 亚马逊身HSCODE
        # sheet['G12'] = 'TEST-SKU-001'                 # SKU
        # sheet['H12'] = '浴室地毯'                      # 中文品名
        # sheet['I12'] = 'Bath Rug'                     # 英文品名
        # sheet['J12'] = 10                             # 数量
        # sheet['K12'] = 5.99                           # 单价(USD)
        # sheet['L12'] = 59.90                          # 总价
        # sheet['M12'] = 'plastic'                      # 规格型号
        # sheet['N12'] = '塑料'                         # 材质
        # sheet['O12'] = '日用'                         # 用途
        
        # 填写第二行商品数据
        sheet['A13'] = f"FBA{shipment_id}002"         # 包号/FBA标签号
        sheet['B13'] = shipment_id                    # Shipment Id
        # sheet['C13'] = f"REF{shipment_id}002"         # Reference Id
        # sheet['D13'] = '25*18*12'                     # 材积CM(长*宽*高)
        # sheet['E13'] = 2.0                            # 重量KGS
        # sheet['F13'] = '8708999990'                   # 亚马逊身HSCODE
        # sheet['G13'] = 'TEST-SKU-002'                 # SKU
        # sheet['H13'] = '浴室地毯'                      # 中文品名
        # sheet['I13'] = 'Bath Rug'                     # 英文品名
        # sheet['J13'] = 8                              # 数量
        # sheet['K13'] = 7.50                           # 单价(USD)
        # sheet['L13'] = 60.00                          # 总价
        # sheet['M13'] = 'plastic'                      # 规格型号
        # sheet['N13'] = '塑料'                         # 材质
        # sheet['O13'] = '日用'                         # 用途
        
        # 填写第三行商品数据
        sheet['A14'] = f"FBA{shipment_id}003"         # 包号/FBA标签号
        sheet['B14'] = shipment_id                    # Shipment Id
        # sheet['C14'] = f"REF{shipment_id}003"         # Reference Id
        # sheet['D14'] = '30*20*15'                     # 材积CM(长*宽*高)
        # sheet['E14'] = 2.5                            # 重量KGS
        # sheet['F14'] = '8708999990'                   # 亚马逊身HSCODE
        # sheet['G14'] = 'TEST-SKU-003'                 # SKU
        # sheet['H14'] = '浴室地毯'                      # 中文品名
        # sheet['I14'] = 'Bath Rug'                     # 英文品名
        # sheet['J14'] = 5                              # 数量
        # sheet['K14'] = 12.00                          # 单价(USD)
        # sheet['L14'] = 60.00                          # 总价
        # sheet['M14'] = 'plastic'                      # 规格型号
        # sheet['N14'] = '塑料'                         # 材质
        # sheet['O14'] = '日用'                         # 用途

        # 将 workbook 保存到内存中的字节流
        excel_io = io.BytesIO()
        workbook.save(excel_io)
        excel_io.seek(0)
        return excel_io

    def test_upload_fba_excel_success_v1(self):
        """测试上传导入fba订单"""
        # 1. 读取模板excel并填入数据
        excel_io = self.fill_in_fba_excel()

        # 2. 模拟上传
        file = SimpleUploadedFile(
            name="test_fba_orders_filled.xlsx",
            content=excel_io.read(),
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

        upload_url = '/api/customerOrders/upload_file_sync_fba/'

        # 3. 模拟API调用
        response = self.client.post(upload_url, {'file': file}, format='multipart')

        # 调试API响应
        print(f"API Response Status Code: {response.status_code}")
        print(f"API Response Content: {response.content.decode('utf-8')}")

        # 4. 断言API响应
        assert response.status_code == 200, f'{upload_url} 响应状态码不等于200'
        assert response.data['msg'] == '已上传, 处理中...', f'{upload_url} 响应msg不等于"已上传, 处理中..."'

        # 确认 OrderSyncUploadTask 已创建
        upload_task_id = response.data['data']['upload_task']
        upload_tasks = OrderSyncUploadTask.objects.filter(id=upload_task_id)
        assert upload_tasks.exists(), '上传订单的任务未创建'

        upload_task = upload_tasks.last()

        # 5. 轮询等待Celery Worker处理任务
        is_processed = False
        for i in range(30):  # 最多等待30秒
            upload_task = OrderSyncUploadTask.objects.get(id=upload_task_id)
            print('upload_task-->', upload_task.status)
            if upload_task.status in ['Success', 'Failure']:
                assert upload_task.status == 'Success', f'task id: {upload_task_id}, 上传任务的状态不为Success, ' \
                                                        f'任务描述: {upload_task.task_desc}'
                # 验证任务执行后的结果
                customer_orders = CustomerOrder.objects.filter(order_num=upload_task.order_num, del_flag=False)
                assert customer_orders.exists(), f'上传的订单不存在: {upload_task.order_num}'
                customer_order = customer_orders.last()
                assert customer_order.order_status == 'PDC', f'上传的订单状态不为已预报, 订单: {upload_task.order_num}'
                
                # # 校验是否有货件号
                # oc_shipments = OcShipment.objects.filter(customer_order_num=customer_order, del_flag=False)
                # assert oc_shipments.count() >= 1, f'生成的货件号数量不正确, 订单: {upload_task.order_num}'
                
                # 校验包裹数量
                parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
                assert parcels.count() == 3, f'生成的包裹数量不等于3, 订单: {upload_task.order_num}'
                
                # 校验商品数量 - FBA可能与FBM不同，先不限制具体数量
                parcel_items = ParcelItem.objects.filter(parcel_num__in=parcels, del_flag=False)
                assert parcel_items.count() == 3, f'生成的商品数量不等于3, 订单: {upload_task.order_num}'
                
                is_processed = True
                break
            time.sleep(1)

        assert is_processed, f"任务在超时时间内未被Celery Worker处理完成, 任务id: {upload_task_id}"

        return upload_task.order_num

    def test_another_case(self):
        """ another case """
        pass
