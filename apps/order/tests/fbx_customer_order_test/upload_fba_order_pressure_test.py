import io
import os
import time
import threading
from datetime import datetime
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed

import requests
import openpyxl
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import TestCase
from rest_framework.test import APIClient

from company.models import Company, Address
from order.models import Product, OrderSyncUploadTask, CustomerOrder



class FbaOrderPressureTest:
    """FBA订单上传压力测试 - 测试并发上传相同客户订单号的情况"""
    
    def __init__(self):
        """初始化测试环境"""
        self.setup_test_data()
        self.test_ref_num = f"PRESSURE_TEST_{int(time.time())}"  # 统一的客户订单号
        self.results = []  # 存储测试结果
        self.lock = threading.Lock()  # 线程锁
        
    def setup_test_data(self):
        """设置测试数据"""
        User = get_user_model()

        # Create test company
        self.company = Company.objects.filter(name='PressureTestCompany', short_name='PRESS', is_customer=True, del_flag=False).last()
        if not self.company:
            self.company = Company.objects.create(
                name='PressureTestCompany',
                short_name='PRESS',
                is_customer=True,
            )

        # Create test user
        self.user = User.objects.filter(username='PressureTestUser', del_flag=False).last()
        if not self.user:
            self.user = User.objects.create_user(
                username='PressureTestUser',
                password='testpass123',
                company=self.company
            )

        # Create test product
        self.product = Product.objects.filter(
            name='志尊达',
            code='PK0001',
            del_flag=False
        ).last()

        if not self.product:
            self.product = Product.objects.create(
                name='志尊达',
                code='PK0001',
                label_type='WC',
                is_open=True,
                is_valuation=True,
                charge_weight_rate=1,
            )

        # Create test shipper
        self.shipper = Address.objects.filter(
            address_num='ShenZhen',
            address_name='深圳仓',
            address_type='SP',
            del_flag=False
        ).last()

        if not self.shipper:
            self.shipper = Address.objects.create(
                address_num='ShenZhen',
                address_name='深圳仓',
                address_type='SP',
                contact_name='京东物流业务员',
                company_name='京东物流',
                address_one='深圳测试地址',
                city_code='深圳',
                state_code='广东',
                postcode='518000',
                contact_phone='12345678910',
                country_code='CN',
                status='ON',
            )

        # Create test receiver
        self.receiver = Address.objects.filter(
            address_num='TCY1',
            address_name='FBA仓库',
            address_type='RC',
            del_flag=False
        ).last()

        if not self.receiver:
            self.receiver = Address.objects.create(
                address_num='TCY1',
                address_name='FBA仓库',
                address_type='RC',
                contact_name='FBA仓库管理员',
                company_name='Amazon FBA Warehouse',
                address_one='fba仓库测试地址',
                city_code='Irwindale',
                state_code='CA',
                postcode='91702',
                contact_phone='10987654321',
                country_code='US',
                status='ON',
            )

        print(f"压力测试数据设置完成")
        print(f"   - 公司: {self.company.name}")
        print(f"   - 用户: {self.user.username}")
        print(f"   - 产品: {self.product.name}")
        print(f"   - 发件人: {self.shipper.address_name}")
        print(f"   - 收件人: {self.receiver.address_name}")

    def create_fba_excel_with_same_ref_num(self, thread_id):
        """创建包含相同客户订单号的FBA Excel文件"""
        template_path = os.path.join(os.path.dirname(__file__), '../template', 'template_customer_fba_order.xlsx')
        workbook = openpyxl.load_workbook(template_path, data_only=True)
        sheet = workbook.active

        # 使用相同的客户订单号但不同的跟踪号
        tracking_num = f'TRACK_{thread_id}_{int(time.time() * 1000000) % 1000000}'
        
        # 填写基本信息
        sheet['B3'] = tracking_num  # 跟踪号（不同）
        sheet['B4'] = self.shipper.address_name       # Shipper发件人
        sheet['B9'] = self.product.name               # 产品名称
        sheet['F9'] = self.company.short_name         # 客户公司简称
        sheet['I4'] = self.receiver.address_num       # FBA仓库代码
        
        # 生成shipment_id（每个线程不同）
        current_time = datetime.now()
        microsecond = current_time.microsecond + thread_id * 1000  # 确保不同线程有不同的时间戳
        year_last_two = str(current_time.year)[-2:]
        shipment_id = f"{year_last_two}{current_time.strftime('%m%d%H%M%S')}{microsecond % 1000000}"
        
        # 商品明细数据 - 使用相同的客户订单号
        sheet['A12'] = f"FBA{shipment_id}001"         # 包号/FBA标签号
        sheet['B12'] = shipment_id                    # ShipmentId
        sheet['C12'] = self.test_ref_num              # 客户订单号（相同）
        
        # 将workbook保存到内存
        excel_io = io.BytesIO()
        workbook.save(excel_io)
        excel_io.seek(0)
        return excel_io, tracking_num

    def upload_fba_excel_single_thread(self, thread_id):
        """单线程上传FBA Excel"""
        try:
            print(f"线程 {thread_id}: 开始创建Excel...")
            
            # 创建独立的API客户端
            client = APIClient()
            client.force_authenticate(user=self.user)
            
            # 创建Excel文件
            excel_io, tracking_num = self.create_fba_excel_with_same_ref_num(thread_id)
            
            # 模拟上传文件
            file = SimpleUploadedFile(
                name=f"pressure_test_{thread_id}.xlsx",
                content=excel_io.read(),
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )

            upload_url = '/api/customerOrders/upload_file_sync_fba/'
            
            print(f"线程 {thread_id}: 开始上传，客户订单号: {self.test_ref_num}")
            start_time = time.time()
            
            # 调用上传API
            response = client.post(upload_url, {'file': file}, format='multipart')
            
            upload_end_time = time.time()
            upload_duration = upload_end_time - start_time
            
            print(f"线程 {thread_id}: 上传请求完成，耗时: {upload_duration:.3f}s，状态码: {response.status_code}")
            
            result = {
                'thread_id': thread_id,
                'tracking_num': tracking_num,
                'ref_num': self.test_ref_num,
                'status_code': response.status_code,
                'upload_duration': upload_duration,
                'response_data': None,
                'upload_task_id': None,
                'task_status': None,
                'success': False,
                'error_message': None,
                'total_duration': 0
            }
            
            # 检查HTTP响应状态
            if response.status_code == 200:
                try:
                    response_data = response.json() if hasattr(response, 'json') else response.data
                    result['response_data'] = response_data
                    
                    if 'data' in response_data and 'upload_task' in response_data['data']:
                        result['upload_task_id'] = response_data['data']['upload_task']
                        print(f"线程 {thread_id}: 获得任务ID: {result['upload_task_id']}，开始等待异步处理...")
                        
                        # 等待异步任务完成并检查任务状态
                        task_success, task_status, wait_time = self.wait_for_task_completion(result['upload_task_id'], thread_id)
                        result['task_status'] = task_status
                        result['success'] = task_success
                        result['total_duration'] = upload_duration + wait_time
                        
                        if task_success:
                            print(f"线程 {thread_id}: 任务处理成功！状态: {task_status}，总耗时: {result['total_duration']:.3f}s")
                        else:
                            print(f"线程 {thread_id}: 任务处理失败，状态: {task_status}，总耗时: {result['total_duration']:.3f}s")
                    else:
                        result['error_message'] = "响应中缺少upload_task字段"
                        print(f"线程 {thread_id}: 响应格式错误: {response_data}")
                    
                except Exception as e:
                    result['error_message'] = f"解析响应数据失败: {str(e)}"
                    print(f"线程 {thread_id}: 解析响应失败: {str(e)}")
            else:
                # 获取错误信息
                try:
                    error_content = response.content.decode('utf-8')
                    result['error_message'] = error_content
                    print(f"线程 {thread_id}: HTTP请求失败，错误: {error_content}")
                except:
                    result['error_message'] = f"HTTP {response.status_code}"
                    print(f"线程 {thread_id}: HTTP请求失败，状态码: {response.status_code}")
            
            # 线程安全地添加结果
            with self.lock:
                self.results.append(result)
                
            return result
            
        except Exception as e:
            error_msg = f"线程 {thread_id} 执行异常: {str(e)}"
            print(error_msg)
            
            result = {
                'thread_id': thread_id,
                'tracking_num': None,
                'ref_num': self.test_ref_num,
                'status_code': 0,
                'upload_duration': 0,
                'response_data': None,
                'upload_task_id': None,
                'success': False,
                'error_message': error_msg
            }
            
            with self.lock:
                self.results.append(result)
                
            return result

    def wait_for_task_completion(self, upload_task_id, thread_id, max_wait_time=30):
        """等待异步任务完成并检查状态"""
        wait_start_time = time.time()
        check_interval = 1  # 每秒检查一次
        
        while time.time() - wait_start_time < max_wait_time:
            try:
                # 查询任务状态
                upload_task = OrderSyncUploadTask.objects.filter(
                    id=upload_task_id,
                    del_flag=False
                ).first()
                
                if upload_task:
                    status = upload_task.status
                    print(f"线程 {thread_id}: 任务 {upload_task_id} 当前状态: {status}")
                    
                    # 检查是否完成（无论成功还是失败）
                    if status in ['Success', 'Failure', 'VO']:
                        wait_time = time.time() - wait_start_time
                        is_success = (status == 'Success')
                        return is_success, status, wait_time
                    
                    # 如果状态是处理中，继续等待
                    if status in ['Waiting', 'Processed']:
                        time.sleep(check_interval)
                        continue
                else:
                    print(f"线程 {thread_id}: 任务 {upload_task_id} 未找到")
                    wait_time = time.time() - wait_start_time
                    return False, 'NotFound', wait_time
                    
            except Exception as e:
                print(f"线程 {thread_id}: 查询任务状态异常: {str(e)}")
                time.sleep(check_interval)
                continue
        
        # 超时
        wait_time = time.time() - wait_start_time
        print(f"线程 {thread_id}: 任务 {upload_task_id} 等待超时")
        return False, 'Timeout', wait_time

    def test_concurrent_upload_same_ref_num(self, concurrent_count=10):
        """测试并发上传相同客户订单号"""
        print(f"\n开始并发压力测试...")
        print(f"   - 并发线程数: {concurrent_count}")
        print(f"   - 客户订单号: {self.test_ref_num}")
        print(f"   - 预期结果: 只有一个上传成功，其余应该失败")
        
        # 清空之前的结果
        self.results = []
        
        start_time = time.time()
        
        # 使用线程池并发执行
        with ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            # 提交所有任务
            futures = [executor.submit(self.upload_fba_excel_single_thread, i) for i in range(concurrent_count)]
            
            # 等待所有任务完成
            for future in as_completed(futures):
                try:
                    future.result()  # 获取结果，如果有异常会在这里抛出
                except Exception as e:
                    print(f"任务执行异常: {e}")
        
        total_time = time.time() - start_time
        print(f"\n并发测试完成，总耗时: {total_time:.3f}s")
        
        # 分析结果
        self.analyze_pressure_test_results()
        
        return self.results

    def analyze_pressure_test_results(self):
        """分析压力测试结果"""
        print(f"\n=== 压力测试结果分析 ===")
        
        success_count = len([r for r in self.results if r['success']])
        fail_count = len([r for r in self.results if not r['success']])
        
        print(f"总请求数: {len(self.results)}")
        print(f"成功数量: {success_count}")
        print(f"失败数量: {fail_count}")
        
        # 详细结果
        print(f"\n--- 详细结果 ---")
        for result in sorted(self.results, key=lambda x: x['thread_id']):
            status = "成功" if result['success'] else "失败"
            total_duration = f"{result.get('total_duration', result['upload_duration']):.3f}s"
            task_status = result.get('task_status', '未知')
            error = result['error_message'] if result['error_message'] else "无"
            task_id = result['upload_task_id'] if result['upload_task_id'] else "无"
            
            print(f"线程{result['thread_id']:2d}: {status:2s} | 总耗时: {total_duration:8s} | 任务状态: {task_status:8s} | 任务ID: {task_id:8d} | 错误: {error}")

        # 检查是否出现了重复成功的情况
        if success_count > 1:
            print(f"\n  发现并发问题！！！")
            print(f"   - 相同客户订单号 '{self.test_ref_num}' 有 {success_count} 个请求成功")
            print(f"   - 正常情况下应该只有1个成功，其余应该失败")
            print(f"   - 这表明存在并发控制问题")
            
            # 获取成功的任务ID
            success_task_ids = [r['upload_task_id'] for r in self.results if r['success'] and r['upload_task_id']]
            print(f"   - 成功的任务ID: {success_task_ids}")
            
            # 检查数据库中的订单情况
            self.check_database_consistency()
            
        elif success_count == 1:
            print(f"\n 并发控制正常")
            print(f"   - 只有1个请求成功，其余失败")
            print(f"   - 客户订单号唯一性得到保证")
            
        elif success_count == 0:
            print(f"\n 全部请求失败")
            print(f"   - 可能存在其他问题，需要检查错误信息")

    def check_database_consistency(self):
        """检查数据库一致性"""
        print(f"\n--- 检查数据库一致性 ---")

        # 首先检查所有相关的上传任务
        success_task_ids = [r['upload_task_id'] for r in self.results if r['success'] and r['upload_task_id']]
        all_task_ids = [r['upload_task_id'] for r in self.results if r['upload_task_id']]
        
        print(f"本次测试涉及的任务ID: {all_task_ids}")
        print(f"成功的任务ID: {success_task_ids}")

        # 检查OrderSyncUploadTask
        upload_tasks = OrderSyncUploadTask.objects.filter(
            id__in=all_task_ids,
            del_flag=False
        )
        print(f"OrderSyncUploadTask总数量: {upload_tasks.count()}")
        
        success_tasks = upload_tasks.filter(status='Success')
        print(f"状态为Success的任务数量: {success_tasks.count()}")
        
        for task in upload_tasks:
            print(f"   任务ID: {task.id}, 状态: {task.status}, 订单号: {task.order_num}")
        
        # 检查CustomerOrder
        customer_orders = CustomerOrder.objects.filter(
            ref_num=self.test_ref_num,
            del_flag=False
        )
        print(f"CustomerOrder数量: {customer_orders.count()}")
        
        for order in customer_orders:
            print(f"   订单号: {order.order_num}, 状态: {order.order_status}, 客户订单号: {order.ref_num}")
        
        # 分析并发问题
        if success_tasks.count() > 1:
            print(f"  发现任务级别并发问题！")
            print(f"   - 有 {success_tasks.count()} 个任务状态为Success")
            print(f"   - 客户订单号 '{self.test_ref_num}' 重复处理")
        
        if customer_orders.count() > 1:
            print(f"  发现订单级别并发问题！")
            print(f"   - 数据库中存在 {customer_orders.count()} 个相同客户订单号的订单")
        
        return {
            'upload_tasks_count': upload_tasks.count(),
            'success_tasks_count': success_tasks.count(),
            'customer_orders_count': customer_orders.count(),
            'upload_tasks': list(upload_tasks.values('id', 'status', 'order_num')),
            'customer_orders': list(customer_orders.values('order_num', 'order_status', 'ref_num'))
        }

    def run_pressure_test(self, concurrent_count=10, repeat_times=1):
        """运行完整的压力测试"""
        print(f"开始FBA订单上传压力测试...")
        print(f"   - 并发数: {concurrent_count}")
        print(f"   - 重复次数: {repeat_times}")
        
        all_results = []
        
        for round_num in range(repeat_times):
            print(f"\n========== 第 {round_num + 1} 轮测试 ==========")
            
            # 更新测试的客户订单号
            self.test_ref_num = f"PRESSURE_TEST_{int(time.time())}_{round_num}"
            
            # 执行并发测试
            round_results = self.test_concurrent_upload_same_ref_num(concurrent_count)
            all_results.extend(round_results)
            
            # 轮次间隔
            if round_num < repeat_times - 1:
                print(f"等待2秒后进行下一轮测试...")
                time.sleep(2)
        
        print(f"\n========== 压力测试总结 ==========")
        total_requests = len(all_results)
        total_success = len([r for r in all_results if r['success']])
        total_fail = total_requests - total_success
        
        print(f"总轮次: {repeat_times}")
        print(f"总请求数: {total_requests}")
        print(f"总成功数: {total_success}")
        print(f"总失败数: {total_fail}")
        
        # 统计每轮的成功数量
        round_success_counts = []
        for round_num in range(repeat_times):
            round_results = [r for r in all_results if f"_{round_num}" in r['ref_num']]
            round_success_count = len([r for r in round_results if r['success']])
            round_success_counts.append(round_success_count)
            print(f"第{round_num + 1}轮成功数: {round_success_count}")
        
        # 检查是否发现并发问题
        problem_rounds = [i for i, count in enumerate(round_success_counts) if count > 1]
        if problem_rounds:
            print(f"\n 发现并发问题的轮次: {[i+1 for i in problem_rounds]}")
            print(f"   建议检查客户订单号唯一性约束和并发控制机制")
        else:
            print(f"\n 所有轮次的并发控制都正常")


# Django测试框架兼容版本
class FbaOrderPressureTestCase(TestCase):
    """Django测试框架兼容的压力测试"""
    
    def test_fba_order_pressure(self):
        """FBA订单并发上传压力测试"""
        pressure_test = FbaOrderPressureTest()
        
        # 运行压力测试（参数可调整）
        results = pressure_test.run_pressure_test(concurrent_count=5, repeat_times=1)
        
        # 检查测试结果
        success_count = len([r for r in pressure_test.results if r['success']])
        self.assertTrue(success_count <= 1, "相同客户订单号应该只有一个上传成功")


# 使用示例
if __name__ == '__main__':
    # 设置Django环境
    import django
    import sys
    
    # 添加项目根目录到Python路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    
    # 设置Django设置模块
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'alita.settings.develop')
    django.setup()
    
    # 运行压力测试
    pressure_test = FbaOrderPressureTest()
    
    # 参数说明:
    # concurrent_count: 并发线程数，建议5-20
    # repeat_times: 重复测试轮次，每轮使用不同的客户订单号
    pressure_test.run_pressure_test(concurrent_count=10, repeat_times=3)