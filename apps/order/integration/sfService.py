import base64
import json
import traceback
from datetime import datetime
from decimal import Decimal
import time
import requests

from alita.logger import logger
from common.utils.file_util import upload_file
from common.utils.logger_util import alita_logger_info
from order.integration.integrationInterface import IntegrationInterface, get_label_order_vo, get_order_label_obj
from order.integration.util.commonUtil import DecimalEncoder
from order.integration.util.sfUtil import SFTokenAPI, sf_token, BizMsgCrypt


def get_call(customer_order, label_order_vo, service, is_unpack):
    """
    下单
    :param customer_order:
    :param label_order_vo:
    :param service:
    :return:
    """

    is_customer_order, parcel_order_num, service_code, supplier_account, warehouse = \
        get_label_order_vo(customer_order, label_order_vo, service)

    parcel_info_list = label_order_vo.parcelList
    parcel_item_list = label_order_vo.parcelItemList

    url = supplier_account.url
    appKey = supplier_account.auth_id    # 授权id
    appSecret = supplier_account.auth_pwd   # 授权码
    customerCode = supplier_account.account_name   # 客户编码
    encodingAESKey = supplier_account.account_pwd  # 密钥
    interProductCode = supplier_account.apicall_id  # 此处来配置产品， 页面中的字段名为 apiCall ID
    account_desc = supplier_account.account_desc  # 账户描述

    if is_customer_order:
        sender_obj = customer_order
    else:
        sender_obj = warehouse

    order_data = {
        'customerCode': customerCode,    # 客户编码  ICRM-CNO1N79804
        'customerOrderNo': parcel_order_num,    # 客户订单号
        'interProductCode': interProductCode,    # 国际产品映射码  国际小包 写死
        'declaredCurrency': "CNY",    # 申报价值币种
    }

    ReceiverInfo = {
        # 收件人信息
        'company': customer_order.buyer_company_name or customer_order.buyer_name,
        'contact': customer_order.buyer_name,
        'address': customer_order.buyer_address_one,
        'regionSecond': customer_order.buyer_city_code,
        'country': customer_order.buyer_country_code,
        'postCode': customer_order.buyer_postcode,
        'regionFirst': customer_order.buyer_state,
        'phoneNo': customer_order.buyer_phone,
        'email':  customer_order.buyer_mail
    }

    SenderInfo = {
        # 发件人信息
        'company': sender_obj.company_name or sender_obj.contact_name,
        'contact': sender_obj.contact_name,
        'country': sender_obj.country_code,
        'regionFirst': sender_obj.state_code,
        'regionSecond': sender_obj.city_code,
        'postCode': sender_obj.postcode,
        'address': sender_obj.address_one,
        'phoneNo': sender_obj.contact_phone,
        'email': sender_obj.contact_email
    }

    PaymentInfo = {
        # 付款方式
        'payMethod': 1,  # "1-寄方付 2-收方付 3-第三方付"
        'payMonthCard': account_desc,
        'taxPayMethod': 2,
        'taxPayMonthCard': account_desc,
    }

    order_data["senderInfo"] = SenderInfo
    order_data["receiverInfo"] = ReceiverInfo
    order_data["paymentInfo"] = PaymentInfo

    # 包裹信息
    parcel_weight = 0
    for parcel_info in parcel_info_list:

        parcel_weight += parcel_info.parcel_weight

    order_data['parcelTotalWeight'] = float(round(parcel_weight, 2))
    order_data['parcelWeightUnit'] = "KG"

    declaredValue = 0  # 所有商品的总价值 需要保证单位一致
    parcelQuantity = 0 # 包裹总件数
    # 包裹物品信息
    ParcelInfo = []
    for parcel_item in parcel_item_list:
        item = {
            "originCountry": parcel_item.origin_country,
            "quantity": parcel_item.item_qty,
            "amount": float(parcel_item.declared_price or 0),
            "unit": "个",
            "name": parcel_item.declared_nameCN,
        }
        ParcelInfo.append(item)

        declaredValue += (float(parcel_item.declared_price or 0) * parcel_item.item_qty)
        parcelQuantity += 1

    order_data['parcelInfoList'] = ParcelInfo
    order_data['declaredValue'] = declaredValue  # 所有商品的总价值
    order_data['parcelQuantity'] = parcelQuantity  # 所有商品的总价值

    sf = sf_token(url, appKey, appSecret)
    token_sf = sf.get("apiResultData").get("accessToken")

    # 获取当前时间的秒级时间戳，转换为毫秒
    timestamp_ms = str(int(time.time() * 1000))

    logger.info(f'顺丰国际 下单数据：{order_data}')

    url = f'{url}/openapi/api/dispatch'
    to_xml = json.dumps(order_data, ensure_ascii=False)

    # 测试加密接口
    encryp_test = BizMsgCrypt(token_sf, encodingAESKey, appKey)
    ret, encrypt, signature = encryp_test.EncryptMsg(to_xml, timestamp_ms, timestamp_ms)

    headers = {
        "Content-Type": "application/json",
        "token": token_sf,
        "msgType": "IUOP_CREATE_ORDER",
        "timestamp": timestamp_ms,
        "nonce": timestamp_ms,
        "signature": signature,
        "lang": "zh-CN",
        "appKey": appKey,
        'User-Agent': 'Mozilla/5.0'
    }

    json_data = json.dumps(order_data, sort_keys=True, ensure_ascii=False, separators=(',', ':'),  cls=DecimalEncoder)
    operate_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    alita_logger_info(f'顺丰 请求url={url} 入参: ', str(json_data), operate_time, customer_order.order_num)
    response = requests.post(url, data=encrypt, headers=headers)
    alita_logger_info(f'顺丰 请求url={url} 出参: ', str(response.text), operate_time, customer_order.order_num)

    return response.json(), token_sf, encodingAESKey, appKey

class SFIntegrationService(IntegrationInterface):
    """
    顺丰国际  接口
    """

    def create_order(self, labelOrderVo):
        """
        创建订单
        :param labelOrderVo:
        """
        logger.info("顺丰  create_order")
        service = labelOrderVo.service
        customer_order = labelOrderVo.customerOrder
        order_label_task = labelOrderVo.orderLabelTask
        is_unpack = labelOrderVo.is_unpack
        parcel_info_list = labelOrderVo.parcelList

        try:
            result, token_sf, encodingAESKey, appKey = get_call(customer_order, labelOrderVo, service, is_unpack)
        except Exception as e:
            logger.error(f'顺丰 create_order get_call 抛出异常：{e}')
            logger.error(f'错误堆栈：{traceback.format_exc()}')
            save_fail_result(order_label_task, f'下单失败: {e}', 121)
            return

        decrypt_test = BizMsgCrypt(token_sf, encodingAESKey, appKey)
        ret, decryp_xml = decrypt_test.DecryptMsg(result.get("apiResultData"))
        decryp_xml = json.loads(decryp_xml)

        logger.info(f'返回值{decryp_xml}')

        try:
            if decryp_xml.get('data').get("labelUrl") and decryp_xml.get('data').get("sfWaybillNo"):
                sfWaybillNo = decryp_xml['data']["sfWaybillNo"]    # 运单号
                label_url_link = decryp_xml['data']["labelUrl"]  # 面单地址
            elif decryp_xml['success'] == False:
                err_msg = decryp_xml['msg']
                save_fail_result(order_label_task, f'下单失败: {err_msg}', 121)
            elif result['apiResultCode'] == 0 and decryp_xml['success'] == True:
                # customerOrderNo = decryp_xml['data']["customerOrderNo"]    # 订单号
                sfWaybillNo = decryp_xml['data']["sfWaybillNo"]    # 运单号
                label_url_link = decryp_xml['data']["labelUrl"]  # 面单地址
            else:
                err_msg = result['apiErrorMsg']
                save_fail_result(order_label_task, f'下单失败: {err_msg}', 121)
                return
        except Exception as e:
            logger.error(f'顺丰异常：{e}')
            logger.error(f'错误堆栈2：{traceback.format_exc()}')
            save_fail_result(order_label_task, f'下单失败: {e}', 121)
            return

        order_label_task.status = 'HandledBy3rdNo'
        order_label_task.label_desc = '等待抓取面单'
        order_label_task.third_order_no = sfWaybillNo
        order_label_task.update_date = datetime.now()
        order_label_task.save()

        child_list = decryp_xml["data"].get("childWaybillNoList", [])
        # 确保两个列表长度一致（避免索引越界）
        if len(parcel_info_list) == len(child_list):
            # 遍历索引，一一对应赋值
            for index in range(len(parcel_info_list)):
                parcel_info = parcel_info_list[index]
                # 从 child_list 中取对应索引的跟踪号
                parcel_info.tracking_num = child_list[index]
                parcel_info.save()
        else:
            logger.warning(f"包裹数量与跟踪号数量不匹配：包裹{len(parcel_info_list)}个，跟踪号{len(child_list)}")


    def get_label(self, label_order_vo):
        """
        获取面单
        :param label_order_vo:
        """
        logger.info("---顺丰 get_label")
        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount
        customer_order = label_order_vo.customerOrder

        url = supplier_account.url
        appKey = supplier_account.auth_id  # 客户端id
        appSecret = supplier_account.auth_pwd  # 秘钥
        customerCode = supplier_account.account_name  # 客户编码
        encodingAESKey = supplier_account.account_pwd  # 密钥


        sf = sf_token(url, appKey, appSecret)
        token_sf = sf.get("apiResultData").get("accessToken")

        # 获取当前时间的秒级时间戳，转换为毫秒
        timestamp_ms = str(int(time.time() * 1000))
        print(timestamp_ms)

        url = f'{url}/openapi/api/dispatch'

        payload = {
            "customerCode": customerCode,
            "printType": "1",  # 打印类型。1-运单 2-发票
            "fileFormat": "PDF",
            "printWaybillNoDtoList": [{"sfWaybillNo": order_label_task.third_order_no, "isPrintSubParent":1}],
            "resolution": "",
            "requestId": str(time.time() * 1000),      # 唯一id,这里用时间戳,可以用uuid
        }
        to_xml = json.dumps(payload, ensure_ascii=False)

        # 测试加密接口
        encryp_test = BizMsgCrypt(token_sf, encodingAESKey, appKey)
        ret, encrypt, signature = encryp_test.EncryptMsg(to_xml, timestamp_ms, timestamp_ms)

        headers = {
            "Content-Type": "application/json",
            "token": token_sf,
            "msgType": "IUOP_PRINT_ORDER",
            "timestamp": timestamp_ms,
            "nonce": timestamp_ms,
            "signature": signature,
            "lang": "zh-CN",
            "appKey": appKey,
        }
        operate_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        try:
            alita_logger_info(f'顺丰获取面单 请求url={url} 入参: ', headers, operate_time, customer_order.order_num)
            response = requests.post(url, data=encrypt, headers=headers)
            alita_logger_info(f'顺丰获取面单 请求url={url} 出参: ', str(response.text), operate_time, customer_order.order_num)
            response.raise_for_status()  # 如果响应状态码不是200，会抛出异常
        except Exception as e:
            logger.info(f'顺丰 get_label error: {e}')
            save_fail_result(order_label_task, f'获取面单失败: {e}', 121)
            return

        response = response.json()

        decrypt_test = BizMsgCrypt(token_sf, encodingAESKey, appKey)
        ret, decryp_xml = decrypt_test.DecryptMsg(response.get("apiResultData"))
        decryp_xml = json.loads(decryp_xml)

        logger.info(f'返回值23{decryp_xml}')

        if response['apiResultCode'] == 0 and decryp_xml['success'] == True:
            label_data = decryp_xml["data"]["url"]
            logger.info(f'顺丰 面单url: {label_data}')
        else:
            save_fail_result(order_label_task, f'获取面单失败, 状态码:{response.status_code}', 121)
            logger.info(f'顺丰 get_label fail: {response.status_code}')
            return

        # 判断面单是否有效
        la_res_tmp = requests.get(label_data)
        if la_res_tmp.status_code != 200:  # 面单无效
            logger.info(f'面单链接无法访问')
            save_fail_result(order_label_task, '下单失败: 暂无面单！')
            return
        content_type = la_res_tmp.headers.get('Content-Type')
        if content_type != 'application/pdf':
            logger.info(f'面单链接无效')
            save_fail_result(order_label_task, '下单失败: 暂无面单！')
            return

        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + customer_order.order_num + ".pdf"
        upload_file(label_url, label_data, content_type='url')

        order_num = customer_order.order_num
        order_label = get_order_label_obj(order_num)
        order_label.order_num = customer_order
        order_label.tracking_no = order_label_task.third_order_no
        order_label.third_order_no = order_label_task.third_order_no
        order_label.label_url = label_url
        order_label.create_by = order_label_task.create_by
        order_label.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label.product = order_label_task.product
        order_label.save()

        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'
        order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label_task.save()

        customer_order.tracking_num = order_label.tracking_no
        customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        customer_order.save()


        return response




    def cancel_label(self, label_order_vo):
        """
        取消面单
        :param label_order_vo:
        """
        logger.info("顺丰 cancel_label")
        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount

        url = supplier_account.url
        appKey = supplier_account.auth_id  # 客户端id
        appSecret = supplier_account.auth_pwd  # 秘钥
        customerCode = supplier_account.account_name  # 客户编码
        encodingAESKey = supplier_account.account_pwd  # 密钥

        sf = sf_token(url, appKey, appSecret)
        token_sf = sf.get("apiResultData").get("accessToken")

        # 获取当前时间的秒级时间戳，转换为毫秒
        timestamp_ms = str(int(time.time() * 1000))

        url = f'{url}/openapi/api/dispatch'

        payload = {
            "customerCode": customerCode,
            "sfWaybillNo": order_label_task.third_order_no,
        }

        to_xml = json.dumps(payload, ensure_ascii=False)

        # 测试加密接口
        encryp_test = BizMsgCrypt(token_sf, encodingAESKey, appKey)
        ret, encrypt, signature = encryp_test.EncryptMsg(to_xml, timestamp_ms, timestamp_ms)

        headers = {
            "Content-Type": "application/json",
            "token": token_sf,
            "msgType": "IUOP_CANCEL_ORDER",
            "timestamp": timestamp_ms,
            "nonce": timestamp_ms,
            "signature": signature,
            "lang": "zh-CN",
            "appKey": appKey,
            'User-Agent': 'Mozilla/5.0'
        }

        try:
            response = requests.post(url, data=encrypt, headers=headers)
            response.raise_for_status()  # 如果响应状态码不是200，会抛出异常
        except requests.exceptions.RequestException as e:
            # print(f"Error occurred: {e}")
            # print(f"Response content: {response.text if 'response' in locals() else 'No response'}")
            logger.info(f'sf 国际国际取消失败！{str(e)}')
            return None

        response = response.json()
        decrypt_test = BizMsgCrypt(token_sf, encodingAESKey, appKey)
        ret, decryp_xml = decrypt_test.DecryptMsg(response.get("apiResultData"))
        decryp_xml = json.loads(decryp_xml)
        logger.info(f'顺丰 取消面单返回值是: {decryp_xml}')

        result_data = {}
        res_data = decryp_xml
        if str(res_data['code']) == '0':
            result_data['code'] = '0'
            result_data['msg'] = 'success'
        else:
            result_data['code'] = '400'
            result_data['msg'] = '取消失败'
        return result_data

    def get_order(self, label_order_vo):
        pass

    def confirm_ship(self, label_order_vo):
        pass

    def get_secondary_label(self, label_order_vo):
        '''
        二次获取面单
        '''
        pass

    def scanform(self, label_order_vo):
        pass

    def update_order(self, label_order_vo):
        """
        更新订单信息
        :param label_order_vo:
        :return:
        """
        pass

def save_fail_result(order_label_task, result, handle_times=1):
    order_label_task.label_desc = result
    order_label_task.handle_times += handle_times
    order_label_task.update_date = datetime.now()
    order_label_task.save()
