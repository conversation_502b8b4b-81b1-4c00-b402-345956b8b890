import json
import requests
import base64
from datetime import datetime
# 暂时注释掉，先测试基本功能
# from common.utils.logger_util import alita_logger_info

# FedEx API配置信息
FEDEX_CONFIG = {
    "base_url": "https://apis-sandbox.fedex.com",
    "auth_url": "https://apis-sandbox.fedex.com/oauth/token",
    "timeout": 60,
    # "api_version": "v1",
    "client_id": "l780ff127451a741ea8262837323976be3",
    "client_secret": "212037c3fa3f4b20a18d2d0a32d0b14c",
    "account_number": "*********"
}

# 数据类定义
class Address:
    def __init__(self, postal_code, full_name, phone, address, city, state, country, company_name=""):
        self.postal_code = postal_code
        self.full_name = full_name
        self.phone = phone
        self.address = address
        self.city = city
        self.state = state
        self.country = country
        self.company_name = company_name

class Package:
    def __init__(self, weight, length, width, height, value):
        self.weight = weight
        self.length = length
        self.width = width
        self.height = height
        self.value = value

class Item:
    def __init__(self, sku, name, name_en, quantity, price, weight, harmonized_code=""):
        self.sku = sku
        self.name = name
        self.name_en = name_en
        self.quantity = quantity
        self.price = price
        self.weight = weight
        self.harmonized_code = harmonized_code

def get_access_token(client_id, client_secret):
    """获取FedEx OAuth访问令牌"""
    url = FEDEX_CONFIG["auth_url"]
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    data = {
        "grant_type": "client_credentials",
        "client_id": client_id,
        "client_secret": client_secret
    }
    
    try:
        response = requests.post(url, headers=headers, data=data, timeout=30)
        print(f"🔐 获取令牌状态: {response.status_code}")
        print(f"令牌响应: {response.text}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            if access_token:
                print("✅ 获取访问令牌成功")
                return access_token
            else:
                print("❌ 响应中未找到access_token")
        else:
            print(f"❌ 获取令牌失败: {response.text}")
    
    except Exception as e:
        print(f"❌ 获取令牌异常: {e}")
    
    return None

def get_auth():
    """测试FedEx API认证是否正常"""
    print(f"🔐 测试FedEx API认证")
    
    # 使用真实的配置参数
    client_id = FEDEX_CONFIG["client_id"]
    client_secret = FEDEX_CONFIG["client_secret"]
    
    access_token = get_access_token(client_id, client_secret)
    
    if access_token:
        print("✅ FedEx API认证成功")
        return True
    else:
        print("❌ FedEx API认证失败")
        return False

def create_order_test(access_token=None):
    """测试创建FedEx订单功能"""
    print(f"📦 测试创建FedEx订单")
    
    if not access_token:
        print("❌ 缺少访问令牌")
        return None
    
    # 测试地址数据
    sender = Address(
        postal_code="38017",
        full_name="Sender Company",
        phone="9015551234",
        address="1234 Sender Street",
        city="Collierville",
        state="TN",
        country="US",
        company_name="Sender Corp"
    )
    
    receiver = Address(
        postal_code="27577",
        full_name="John Doe",
        phone="9195551234",
        address="1234 Receiver Street",
        city="Raleigh",
        state="NC",
        country="US",
        company_name="Receiver Corp"
    )
    
    package = Package(
        weight=2.0,  # lbs
        length=12.0,  # inches
        width=8.0,
        height=6.0,
        value=100.0
    )
    
    item = Item(
        sku="TEST-SKU-001",
        name="测试商品",
        name_en="Test Product",
        quantity=1,
        price=100.0,
        weight=2.0,
        harmonized_code="123456"
    )
    
    # 地址处理函数（与IntegrationService保持一致）
    def build_address_data(name, address1, address2, city, state, country, postcode, phone, email, company_name="", supplier_supports_address2=True):
        """标准地址处理函数"""
        address1 = address1 or ""
        address2 = address2 or ""
        
        if supplier_supports_address2:
            return {
                "contact": {
                    "personName": name,
                    "companyName": company_name,
                    "phoneNumber": phone or "",
                    "emailAddress": email or ""
                },
                "address": {
                    "streetLines": [address1, address2] if address2.strip() else [address1],
                    "city": city,
                    "stateOrProvinceCode": state,
                    "postalCode": postcode,
                    "countryCode": country
                }
            }
        else:
            # 供应商不支持address2，合并到address1
            if address2.strip():
                combined_address = f"{address1} {address2}".strip()
            else:
                combined_address = address1
                
            return {
                "contact": {
                    "personName": name,
                    "companyName": company_name,
                    "phoneNumber": phone or "",
                    "emailAddress": email or ""
                },
                "address": {
                    "streetLines": [combined_address],
                    "city": city,
                    "stateOrProvinceCode": state,
                    "postalCode": postcode,
                    "countryCode": country
                }
            }

    # FedEx支持address2字段（通过streetLines数组）
    supplier_supports_address2 = True
    
    # 使用标准函数构建地址数据
    sender_data = build_address_data(
        name=sender.full_name,
        address1=sender.address,
        address2="Suite 100",  # 测试address2功能
        city=sender.city,
        state=sender.state,
        country=sender.country,
        postcode=sender.postal_code,
        phone=sender.phone,
        email="<EMAIL>",
        company_name=sender.company_name,
        supplier_supports_address2=supplier_supports_address2
    )
    
    receiver_data = build_address_data(
        name=receiver.full_name,
        address1=receiver.address,
        address2="Apt 4B",  # 测试address2功能
        city=receiver.city,
        state=receiver.state,
        country=receiver.country,
        postcode=receiver.postal_code,
        phone=receiver.phone,
        email="<EMAIL>",
        company_name=receiver.company_name,
        supplier_supports_address2=supplier_supports_address2
    )

    # 构建FedEx订单数据
    order_data = {
        "labelResponseOptions": "URL_ONLY",
        "requestedShipment": {
            "shipper": sender_data,
            "recipients": [receiver_data],
            "shipDatestamp": datetime.now().strftime("%Y-%m-%d"),
            "serviceType": "FEDEX_GROUND",
            "packagingType": "YOUR_PACKAGING",
            "pickupType": "USE_SCHEDULED_PICKUP",
            "blockInsightVisibility": False,
            "shippingChargesPayment": {
                "paymentType": "SENDER"
            },
            "labelSpecification": {
                "imageType": "PDF",
                # "labelStockType": "STOCK_4X675",
                "labelStockType": "STOCK_4X6",
            },
            "customsClearanceDetail": {
                "dutiesPayment": {
                    "paymentType": "SENDER"
                },
                "documentContent": "NON_DOCUMENTS",
                "customsValue": {
                    "amount": package.value,
                    "currency": "USD"
                },
                "commercialInvoice": {
                    "shipmentPurpose": "SOLD"
                },
                "commodities": [
                    {
                        "description": item.name_en,
                        "countryOfManufacture": "US",
                        "quantity": item.quantity,
                        "quantityUnits": "EA",
                        "unitPrice": {
                            "amount": item.price,
                            "currency": "USD"
                        },
                        "weight": {
                            "units": "LB",
                            "value": item.weight
                        },
                        "harmonizedCode": item.harmonized_code or ""
                    }
                ]
            },
            "requestedPackageLineItems": [
                {
                    "weight": {
                        "units": "LB",
                        "value": package.weight
                    },
                    "dimensions": {
                        "length": package.length,
                        "width": package.width,
                        "height": package.height,
                        "units": "IN"
                    },
                    "customerReferences": [
                        {
                            "customerReferenceType": "CUSTOMER_REFERENCE",
                            "value": f"TEST_FEDEX_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                        }
                    ]
                },
                {
                    "weight": {
                        "units": "LB",
                        "value": package.weight
                    },
                    "dimensions": {
                        "length": package.length,
                        "width": package.width,
                        "height": package.height,
                        "units": "IN"
                    },
                    "customerReferences": [
                        {
                            "customerReferenceType": "CUSTOMER_REFERENCE",
                            "value": f"TEST_FEDEX_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                        }
                    ]
                }
            ]
        },
        "accountNumber": {
            "value": FEDEX_CONFIG["account_number"]
        }
    }
    
    # API调用
    url = f"{FEDEX_CONFIG['base_url']}/ship/v1/shipments"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "X-locale": "en_US"
    }
    
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(order_data, indent=2, ensure_ascii=False)}")
    
    try:
        start_time = datetime.now()
        response = requests.post(url, json=order_data, headers=headers, 
                               timeout=FEDEX_CONFIG['timeout'])
        elapsed = (datetime.now() - start_time).total_seconds()
        
        print(f"响应状态: {response.status_code}")
        print(f"响应耗时: {elapsed:.2f}秒")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            output = result.get("output", {})
            transaction_shipments = output.get("transactionShipments", [])
            
            if transaction_shipments:
                shipment = transaction_shipments[0]
                tracking_number = shipment.get("masterTrackingNumber")
                shipment_id = shipment.get("shipmentId")
                
                print("✅ FedEx订单创建成功")
                print(f"追踪号: {tracking_number}")
                print(f"订单ID: {shipment_id}")
                
                # 检查是否有面单URL
                pieces = shipment.get("pieceResponses", [])
                if pieces:

                    for piece in pieces:

                        i = piece.get('packageSequenceNumber')
                        packageDocuments = piece.get("packageDocuments", [{}])[0]
                        masterTrackingNumber = piece.get("masterTrackingNumber")
                        tracking_number = piece.get("trackingNumber")
                        label_url = packageDocuments.get("url")
                        print(f"面单URL{i}: {masterTrackingNumber}")
                        print(f"面单URL{i}: {tracking_number}")
                        print(f"面单URL{i}: {label_url}")



                return {
                    "success": True,
                    "tracking_number": tracking_number,
                    "shipment_id": shipment_id,
                    "label_url": label_url if pieces else None
                }
            else:
                print("❌ 响应中未找到订单信息")
        else:
            print(f"❌ API调用失败: {response.text}")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def get_label_test(shipment_id, access_token=None):
    """测试获取FedEx面单功能"""
    print(f"🏷️ 测试获取FedEx面单: {shipment_id}")
    
    if not access_token:
        print("❌ 缺少访问令牌")
        return False
    
    # 如果create_order已经返回了面单URL，这里可以直接下载
    # 或者使用FedEx的获取面单API
    url = f"{FEDEX_CONFIG['base_url']}/ship/v1/shipments/{shipment_id}/label"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "X-locale": "en_US"
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=60)
        print(f"获取面单状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            # 根据实际响应结构调整
            label_data = result.get("labelData")
            
            if label_data:
                # 保存面单文件
                filename = f"test_fedex_label_{shipment_id}.pdf"
                with open(filename, 'wb') as f:
                    f.write(base64.b64decode(label_data))
                print(f"✅ 面单保存成功: {filename}")
                return True
            else:
                print("❌ 未获取到面单数据")
        else:
            print(f"❌ 获取面单失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取面单异常: {e}")
    
    return False

def cancel_order_test(tracking_number, access_token=None):
    """测试取消FedEx订单功能"""
    print(f"❌ 测试取消FedEx订单: {tracking_number}")
    
    if not access_token:
        print("❌ 缺少访问令牌")
        return False
    
    # FedEx取消订单API
    url = f"{FEDEX_CONFIG['base_url']}/ship/v1/shipments/cancel"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "X-locale": "en_US"
    }
    
    cancel_data = {
        "accountNumber": {
            "value": FEDEX_CONFIG["account_number"]
        },
        "trackingNumber": tracking_number
    }
    
    try:
        response = requests.put(url, json=cancel_data, headers=headers, timeout=30)
        print(f"取消订单状态: {response.status_code}")
        print(f"取消订单响应: {response.text}")
        
        if response.status_code == 200:
            print("✅ FedEx订单取消成功")
            return True
        else:
            print(f"❌ FedEx订单取消失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 取消订单异常: {e}")
    
    return False

def get_tracking_test(tracking_number, access_token=None):
    """测试FedEx物流追踪功能"""
    print(f"📍 测试FedEx物流追踪: {tracking_number}")
    
    if not access_token:
        print("❌ 缺少访问令牌")
        return None
    
    # FedEx追踪API
    url = f"{FEDEX_CONFIG['base_url']}/track/v1/trackingnumbers"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "X-locale": "en_US"
    }
    
    tracking_data = {
        "includeDetailedScans": True,
        "trackingInfo": [
            {
                "trackingNumberInfo": {
                    "trackingNumber": tracking_number
                }
            }
        ]
    }
    
    try:
        response = requests.post(url, json=tracking_data, headers=headers, timeout=30)
        print(f"追踪查询状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            output = result.get("output", {})
            complete_track_results = output.get("completeTrackResults", [])
            
            if complete_track_results:
                track_result = complete_track_results[0]
                track_results = track_result.get("trackResults", [])
                
                if track_results:
                    track_info = track_results[0]
                    status = track_info.get("latestStatusDetail", {}).get("description")
                    scan_events = track_info.get("scanEvents", [])
                    
                    print(f"✅ 当前状态: {status}")
                    print("📋 物流事件:")
                    for event in scan_events:
                        event_time = event.get("date")
                        event_desc = event.get("eventDescription")
                        location = event.get("scanLocation", {})
                        city = location.get("city", "")
                        state = location.get("stateOrProvinceCode", "")
                        print(f"  - {event_time}: {event_desc} ({city}, {state})")
                    
                    return {
                        "status": status,
                        "events": scan_events
                    }
                else:
                    print("❌ 未找到追踪结果")
            else:
                print("❌ 追踪查询无结果")
        else:
            print(f"❌ 追踪查询失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 追踪查询异常: {e}")
    
    return None

def run_full_test():
    """运行完整的FedEx API集成测试"""
    print("🚀 FedEx API 完整集成测试")
    print("=" * 60)
    
    # 1. 认证测试
    print("\n1️⃣ 测试API认证")
    if not get_auth():
        print("❌ 认证失败，终止测试")
        return False
    
    # 2. 获取访问令牌
    print("\n2️⃣ 获取访问令牌")
    client_id = FEDEX_CONFIG["client_id"]
    client_secret = FEDEX_CONFIG["client_secret"]
    access_token = get_access_token(client_id, client_secret)
    
    if not access_token:
        print("❌ 获取令牌失败，终止测试")
        return False
    
    # 3. 创建订单测试
    print("\n3️⃣ 测试创建订单")
    order_result = create_order_test(access_token)
    if not order_result:
        print("❌ 创建订单失败，终止测试")
        return False
    
    tracking_number = order_result.get('tracking_number')
    shipment_id = order_result.get('shipment_id')
    
    # 4. 获取面单测试
    print("\n4️⃣ 测试获取面单")
    if shipment_id:
        get_label_test(shipment_id, access_token)
    else:
        print("⚠️ 无订单ID，跳过面单测试")
    
    # 5. 追踪测试
    print("\n5️⃣ 测试物流追踪")
    if tracking_number:
        get_tracking_test(tracking_number, access_token)
    else:
        print("⚠️ 无追踪号，跳过追踪测试")
    
    # 6. 取消订单测试（可选）
    print("\n6️⃣ 测试取消订单")
    if tracking_number:
        cancel_order_test(tracking_number, access_token)
    else:
        print("⚠️ 无追踪号，跳过取消测试")
    
    print("\n✅ FedEx API 集成测试完成")
    print("=" * 60)
    return True

def debug_address_processing():
    """调试地址处理逻辑"""
    print("🔍 调试FedEx地址处理逻辑")
    print("-" * 40)
    
    # 测试数据
    test_cases = [
        {"address1": "123 Main St", "address2": "Suite 100"},
        {"address1": "456 Oak Ave", "address2": ""},
        {"address1": "789 Pine St", "address2": "Apt 4B"},
    ]
    
    def build_address_data(name, address1, address2, city, state, country, postcode, phone, email, company_name="", supplier_supports_address2=True):
        """标准地址处理函数"""
        address1 = address1 or ""
        address2 = address2 or ""
        
        if supplier_supports_address2:
            return {
                "contact": {
                    "personName": name,
                    "companyName": company_name,
                    "phoneNumber": phone or "",
                    "emailAddress": email or ""
                },
                "address": {
                    "streetLines": [address1, address2] if address2.strip() else [address1],
                    "city": city,
                    "stateOrProvinceCode": state,
                    "postalCode": postcode,
                    "countryCode": country
                }
            }
        else:
            # 供应商不支持address2，合并到address1
            if address2.strip():
                combined_address = f"{address1} {address2}".strip()
            else:
                combined_address = address1
                
            return {
                "contact": {
                    "personName": name,
                    "companyName": company_name,
                    "phoneNumber": phone or "",
                    "emailAddress": email or ""
                },
                "address": {
                    "streetLines": [combined_address],
                    "city": city,
                    "stateOrProvinceCode": state,
                    "postalCode": postcode,
                    "countryCode": country
                }
            }
    
    for case in test_cases:
        # 测试支持address2的情况（FedEx使用streetLines数组）
        result_with_address2 = build_address_data(
            "Test User", case["address1"], case["address2"], 
            "City", "State", "US", "12345", "*********", "<EMAIL>",
            supplier_supports_address2=True
        )
        
        # 测试不支持address2的情况
        result_without_address2 = build_address_data(
            "Test User", case["address1"], case["address2"], 
            "City", "State", "US", "12345", "*********", "<EMAIL>",
            supplier_supports_address2=False
        )
        
        print(f"输入: {case}")
        print(f"支持address2: {result_with_address2}")
        print(f"不支持address2: {result_without_address2}")
        print("-" * 40)

def alita_logger_info(message, data, timestamp, order_num):
    """临时替代函数，用于测试"""
    print(f"[{timestamp}] [{order_num}] {message}: {data}")

if __name__ == "__main__":
    # 运行完整测试
    # run_full_test()
    
    # 或者单独测试某个功能
    get_auth()
    # create_order_test()
    # debug_address_processing() 