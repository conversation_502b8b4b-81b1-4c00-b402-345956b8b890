import base64
import ssl
from datetime import datetime

import certifi
import requests
import asyncio
import aiohttp
import time

import urllib3
# import uvloop
from async_timeout import timeout
from collections import deque
import platform

# if platform.system() != 'Windows':  # Windows不支持uvloop
#     asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

from common.utils.logger_util import alita_logger_info

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class ParcelCustomerOrder:
    """
    小包订单
    """

    def __init__(self):
        self.order_num = ''

        self.order_status = ''

        self.address_num = ''

        self.customer_order_num = ''

        self.third_orderNo = ''

        self.po_no = ''

        self.tracking_num = ''

        self.service_code = ''

        self.buyer_name = ''

        self.buyer_mail = ''

        self.buyer_phone = ''

        self.buyer_country_code = ''

        self.buyer_country = ''

        self.buyer_state = ''

        self.buyer_city_code = ''

        self.buyer_city = ''

        self.buyer_postcode = ''

        self.buyer_house_num = ''

        self.buyer_address_one = ''

        self.buyer_address_two = ''

        self.order_time = ''

        self.weight = ''

        self.volume = ''

        self.is_inner_order = ''

        self.customer_code = ''

        self.ioss_num = ''

        self.buyer_company_name = ''


# 性能监控类
class PerformanceMonitor:
    def __init__(self, window_size=1000):
        self.response_times = deque(maxlen=window_size)
        self.start_time = time.time()
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_print_time = time.time()

    def add_result(self, response_time, success=True):
        self.response_times.append(response_time)
        self.request_count += 1
        if success:
            self.success_count += 1
        else:
            self.error_count += 1

        # 每秒打印一次统计信息
        current_time = time.time()
        if current_time - self.last_print_time >= 1:
            self.print_stats()
            self.last_print_time = current_time

    def print_stats(self):
        if not self.response_times:
            return

        duration = time.time() - self.start_time
        print(f"\rQPS: {self.request_count / duration:.2f} | "
              f"Success: {self.success_count}/{self.request_count} | "
              f"Avg RT: {sum(self.response_times) / len(self.response_times) * 1000:.2f}ms",
              end='')


# 优化的token管理
class TokenManager:
    def __init__(self):
        self._token = None
        self._token_time = 0
        self._lock = asyncio.Lock()

    async def get_token(self, session, url, username, password):
        async with self._lock:
            current_time = time.time()
            # Token有效期设为30分钟
            if self._token and current_time - self._token_time < 1800:
                return self._token

            try:
                async with timeout(10):
                    async with session.post(
                            f'{url}/auth/login/',
                            json={'username': username, 'password': password},
                            headers={'Content-Type': 'application/json'}
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            if result and 'token' in result:
                                self._token = result['token']
                                self._token_time = current_time
                                return self._token
            except Exception as e:
                print(f"Token refresh failed: {str(e)}")
            return None


def get_token(url, username, password):
    """
    获取token
    :return:
    """

    # key = 'handler_get_alita_token'
    # val = cache.get(key)
    # if val:
    #     return val

    url = url + '/auth/login/'
    post_data = {
        'username': username,
        'password': password
    }

    headers = {}

    result = request_server(post_data, url, headers)
    # print(result)
    token_ = result['token']

    # cache.set(key, token_, 60*60*10)

    return token_


def request_server(post_data, url, headers):
    """
    请求
    :param post_data:
    :param url:
    :param headers:
    :return:
    """

    operate_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    alita_logger_info(f'请求url={url} 入参: ', str(post_data), operate_time)
    r = requests.post(url, json=post_data, headers=headers)
    alita_logger_info(f'请求url={url} 出参: ', r.text, operate_time)

    return r.json()


def create_order(index=1):
    # FBA订单
    # data = {
    #     'shipper': 'YiWu',
    #     'service_code': 'PK0001',
    #     'address_num': '',
    #     'buyer_address_one': '9815 Los Lagos Circle North',
    #     'buyer_address_two': '',
    #     'buyer_city': 'GRANITE BAY',
    #     'buyer_country': 'UNITED STATES',
    #     'buyer_country_code': 'US',
    #     'buyer_house_num': '',
    #     'buyer_mail': '<EMAIL>',
    #     'buyer_name': 'Tara Moon',
    #     'buyer_phone': '',
    #     'buyer_postcode': '95746',
    #     'buyer_state': 'CA',
    #     'ref_num': 'OLS3060107983999_TEST',   # 改一下
    #     'order_time': '2024-11-15 15:42:10',
    #     'order_status': 'WO',
    #     'parcelItem': [{
    #         'item_code': 'OLS3060107983999-01',  # 改一下
    #         'parcel_length': '10',
    #         'parcel_num': 'OLS3060107983999',  # 改一下
    #         'declared_nameCN': '银耳钉',
    #         'declared_nameEN': 'silver earring',
    #         'parcel_volume': '500',
    #         'parcel_width': '10',
    #         'parcel_weight': '0.5',
    #         'item_weight': '',
    #         'declared_price': '',
    #         'parcel_height': '5',
    #         'parcel_qty': '1'
    #     },{
    #         'item_code': 'OLS3060107983999-02',  # 改一下
    #         'parcel_length': '10',
    #         'parcel_num': 'OLS3060107983999',  # 改一下
    #         'declared_nameCN': '银耳钉',
    #         'declared_nameEN': 'silver earring',
    #         'parcel_volume': '500',
    #         'parcel_width': '10',
    #         'parcel_weight': '0.5',
    #         'item_weight': '',
    #         'declared_price': '',
    #         'parcel_height': '5',
    #         'parcel_qty': '1'
    #     }
    #
    #     ],
    #     'volume': '500',
    #     'weight': '1'
    # }

    # 小包测试
    # data = {
    #     "address_num": "CN",
    #     "order_status": "",
    #     "buyer_name": "Ophlia Smith",
    #     "buyer_address_one": "2400 Delaware Avenue",
    #     "buyer_address_two": "",
    #     "buyer_phone": "2013325063",
    #     "buyer_country": "US",
    #     "buyer_country_code": "US",
    #     "buyer_house_num": "",
    #     "buyer_state": "CA",
    #     "buyer_city": "Oakland",
    #     "buyer_postcode": "94607",
    #     "customer_order_num": "LS202408110842164666",
    #     "service_code": "DHL_BD",
    #     "weight": 0.22,
    #     "parcelItem": [{
    #         "parcel_num": 1,
    #         "declared_price": 10,
    #         "item_qty": 1,
    #         "declared_nameEN": "Women\'s Cardigan",
    #         "item_weight": 0.22,
    #         "declared_nameCN": "\\u5973\\u58eb\\u5f00\\u886b",
    #         "parcel_qty": 1,
    #         "item_code": "",
    #         "parcel_weight": 0.22,
    #         "customs_code": "",
    #         "parcel_height": 7,
    #         "parcel_length": 40,
    #         "parcel_width": 30,
    #         "parcel_volume": 0.003
    #     }]
    # }

    # data = {
    #     "taxInfo": {
    #         "tax_type": "IOSS",
    #         "tax_no": "231232"
    #     },
    #     "customer_order_num": f"HG512{index}",
    #     "service_code": "P10008",
    #     "shipperInfo": {
    #         "contact_name": "경기도 부천시 원미구 중동로 204 1301동101호 (그린타운 삼성@)",
    #         "company_name": None,
    #         "contact_email": None,
    #         "contact_phone": None,
    #         "contact_mobile": "000000",
    #         "country_code": "US",
    #         "state_code": "GUANGDONG",
    #         "city_code": "DONGGUAN",
    #         "region_code": None,
    #         "postcode": None,
    #         "house_no": None,
    #         "street": None,
    #         "address_one": "경기도 부천시 원미구 중동로 204 1301동101호 (그린타운 삼성@)",
    #         "address_two": None,
    #         "id_type": None,
    #         "id_number": None
    #     },
    #     "returninfo": {
    #         "contact_name": "jky",
    #         "company_name": None,
    #         "contact_email": None,
    #         "contact_phone": None,
    #         "contact_mobile": "000000",
    #         "country_code": "US",
    #         "state_code": "GUANGDONG",
    #         "city_code": "DONGGUAN",
    #         "region_code": None,
    #         "postcode": None,
    #         "house_no": None,
    #         "street": None,
    #         "address_one": "경기도 부천시 원미구 중동로 204 1301동101호 (그린타운 삼성@)",
    #         "address_two": None,
    #         "id_type": None,
    #         "id_number": None
    #     },
    #     "recipientInfo": {
    #         "contact_name": "유수형",
    #         "company_name": None,
    #         "contact_email": None,
    #         "contact_phone": "01037235112",
    #         "contact_mobile": "01037235112",
    #         "country_code": "US",
    #         "state_code": "경기도",
    #         "city_code": "경기도",
    #         "region_code": None,
    #         "postcode": "1234534",
    #         "house_no": None,
    #         "street": None,
    #         "address_one": "경기도 부천시 원미구 중동로 204 1301동101호 (그린타운 삼성@)",
    #         "address_two": None,
    #         "id_type": "PCCC",
    #         "id_number": None
    #     },
    #     "weight": 0.2,
    #     "order_remark": "",
    #     "parcelItem": [
    #         {
    #             "parcel_type": "Parcel",
    #             "classification": "0",
    #             "parcel_length": 0.1,
    #             "parcel_width": 0.1,
    #             "parcel_height": 0.1,
    #             "parcel_weight": 0.2,
    #             "parcel_qty": 1,
    #             "declared_nameCN": "SL-休闲保暖套装",
    #             "declared_nameEN": "SL - Casual Warm Set",
    #             "declared_price": 30.05,
    #             "declared_currency": "USD",
    #             "customs_code": None,
    #             "distribution_remark": "",
    #             "sku_url": None,
    #             "origin_country": None,
    #             "texture": None,
    #             "use": None,
    #             "brand": None,
    #             "model": None,
    #             "item_qty": 1,
    #             "item_weight": 0.0
    #         },
    #         {
    #             "parcel_type": "Parcel",
    #             "classification": "0",
    #             "parcel_length": 0.1,
    #             "parcel_width": 0.1,
    #             "parcel_height": 0.1,
    #             "parcel_weight": 0.2,
    #             "parcel_qty": 1,
    #             "declared_nameCN": "SL-休闲保暖套装",
    #             "declared_nameEN": "SL - Casual Warm Set",
    #             "declared_price": 30.05,
    #             "declared_currency": "USD",
    #             "customs_code": None,
    #             "distribution_remark": "",
    #             "sku_url": None,
    #             "origin_country": None,
    #             "texture": None,
    #             "use": None,
    #             "brand": None,
    #             "model": None,
    #             "item_qty": 1,
    #             "item_weight": 0.0
    #         },
    #         {
    #             "parcel_type": "Parcel",
    #             "classification": "0",
    #             "parcel_length": 0.0,
    #             "parcel_width": 0.0,
    #             "parcel_height": 0.0,
    #             "parcel_weight": 0.2,
    #             "parcel_qty": 1,
    #             "declared_nameCN": "SL-休闲保暖套装",
    #             "declared_nameEN": "SL - Casual Warm Set",
    #             "declared_price": 30.05,
    #             "declared_currency": "USD",
    #             "customs_code": None,
    #             "distribution_remark": "",
    #             "sku_url": None,
    #             "origin_country": None,
    #             "texture": None,
    #             "use": None,
    #             "brand": None,
    #             "model": None,
    #             "item_qty": 1,
    #             "item_weight": 0.0
    #         }
    #     ]
    # }

    # data = {'address_num': 'ORD-PB', 'customer_order_num': f'TEST00000000{index}', 'service_code': 'WAWA', 'buyer_name': 'Kayla Travers', 'buyer_mail': '', 'buyer_phone': '**************', 'buyer_country': 'US', 'buyer_state': 'CA', 'buyer_city': 'LOS ANGELES', 'buyer_postcode': '90045-3606', 'buyer_house_num': '', 'buyer_address_one': '8930 S SEPULVEDA BLVD', 'buyer_address_two': '', 'order_time': '2023-08-29 10:36:18', 'weight': 0.1, 'volume': 0.0, 'parcelItem': [{'parcel_num': 'eb298070-53f5-4caa-8f4a-964d382f704a', 'parcel_length': 10.0, 'parcel_width': 10.0, 'parcel_height': 10.0, 'parcel_weight': 0.1, 'parcel_volume': 1000.0, 'parcel_qty': 1, 'item_code': '夹子*1;', 'declared_nameCN': '夹子*1;', 'declared_nameEN': 'clip', 'declared_price': 8.0, 'declared_currency': '', 'item_qty': 1, 'item_weight': 0.1}]}

    # fbm订单下单测试
    # data = {"ref_num": "TEST000101", "address_num": "dedhlwarehouse", "buyer_address_one": "Josefstrasse 28",
    #         "buyer_address_two": "", "buyer_city": "Billerbeck", "buyer_country": "DE", "buyer_country_code": "DE",
    #         "buyer_house_num": "", "buyer_mail": "", "buyer_name": "Thomas Cheermann", "buyer_phone": "+49 170 1681156",
    #         "buyer_postcode": "48727", "buyer_state": "Nordrhein-Westfalen", "customer_order_num": "********",
    #         "order_time": "2025-01-10 19:50:02", "order_status": "WO",
    #         "ioss_num": "", "buyer_address_num": "Private address",
    #         "parcelItem": [{"customs_code": "9405410000", "declared_nameCN": "\u7167\u660e\u706f",
    #                         "declared_currency": "USD", "declared_nameEN": "floodlight",
    #                         "declared_price": "4.000", "item_code": "", "item_qty": "10",
    #                         "item_name": "", "item_weight": "0.900", "parcel_height": 12,
    #                         "parcel_length": 17, "parcel_num": "********", "parcel_desc": "",
    #                         "parcel_qty": 1, "parcel_volume": "0.003", "parcel_weight": 0.9, "parcel_width": 13,
    #                         "remark": ""}], "service_code": "MXM1", "volume": 0.003, "weight": "0.9"}

    data = {
        "customer_order_num": "TEST20250730004",
        "service_code": "P10003",
        # "is_confirm_label": 1,
        "shipperInfo": {
            "contact_name": "xiao yi",
            "company_name": "EC LLC",
            "contact_email": "<EMAIL>",
            "contact_phone": "1234567890",
            "contact_mobile": "1234567890",
            "country_code": "US",
            "state_code": "IN",
            "city_code": "GREENWOOD",
            "postcode": "46143",
            "house_no": "1101",
            "address_one": "EC LLC 110 N"
        },
        "recipientInfo": {
            "contact_name": "wukong",
            "company_name": "wukong",
            "contact_phone": "0000000000",
            "contact_mobile": "0000000000",
            "country_code": "US",
            "state_code": "AZ",
            "city_code": "San Tan Valley",
            "postcode": "85140",
            "address_one": "1506 W Sonoqui Blvd"
        },
        "is_overseas_return": 0,
        "is_electronic": 0,
        "signature_type": "D",
        "taxInfo": {
            "tax_type": "IOSS",
            "tax_no": "IOSS0001"
        },
        "weight": 0.2,
        "parcelItem": [
            {
                "parcel_type": "Parcel",
                "parcel_num": "BOX001",
                "classification": "0",
                "parcel_length": 1.0,
                "parcel_width": 1.0,
                "parcel_height": 1.0,
                "parcel_weight": 0.1,
                "parcel_qty": 1,
                "parcel_size_unit": "INCH",
                "parcel_weight_unit": "LB",
                "item_name": "苹果12手机壳",
                "item_code": "P12001",
                "declared_nameCN": "苹果12手机壳",
                "declared_nameEN": "iphone 12 mobile phone shell",
                "declared_price": 2.0,
                "declared_currency": "USD",
                "customs_code": "39264000.00",
                "distribution_remark": "iphone 12 手机壳",
                "sku_url": "http://iphone12_goods.com",
                "origin_country": "CN",
                "texture": "硅胶",
                "use": "",
                "brand": "",
                "model": "",
                "remark": "",
                "item_qty": 1,
                "item_weight": 0.1
            },
            {
                "parcel_type": "Parcel",
                "parcel_num": "BOX002",
                "classification": "0",
                "parcel_length": 1.0,
                "parcel_width": 1.0,
                "parcel_height": 1.0,
                "parcel_weight": 0.1,
                "parcel_qty": 1,
                "parcel_size_unit": "INCH",
                "parcel_weight_unit": "LB",
                "item_name": "苹果12手机壳",
                "item_code": "P12001",
                "declared_nameCN": "苹果12手机壳",
                "declared_nameEN": "iphone 12 mobile phone shell",
                "declared_price": 2.0,
                "declared_currency": "USD",
                "customs_code": "39264000.00",
                "distribution_remark": "iphone 12 手机壳",
                "sku_url": "http://iphone12_goods.com",
                "origin_country": "CN",
                "texture": "硅胶",
                "use": "",
                "brand": "",
                "model": "",
                "remark": "",
                "item_qty": 1,
                "item_weight": 0.1
            }
        ]
    }

    # data = {
    #     "recipientInfo": {
    #         "country_code": "US",
    #         "contact_name": "John Doe",
    #         "state_code": "TN",
    #         "city_code": "Memphis",
    #         "postcode": "38104",
    #         "address_one": "1234 Oak Street",
    #         "address_two": "Apt 2B",
    #         "contact_mobile": "9195551234",
    #         "contact_phone": "+19195551234ext.5678",
    #         "contact_email": "<EMAIL>"
    #     },
    #     "shipperInfo": {
    #         "country_code": "US",
    #         "contact_name": "Sender Company",
    #         "state_code": "TN",
    #         "city_code": "Collierville",
    #         "postcode": "38017",
    #         "address_one": "1234 Sender Street",
    #         "address_two": "",
    #         "contact_mobile": "9015551234",
    #         "contact_phone": "+19015551234",
    #         "contact_email": "<EMAIL>"
    #     },
    #     "parcelItem": [{
    #
    #         "parcel_type": "Parcel",
    #         "parcel_num": 1,
    #         "parcel_length": 100.0,
    #         "parcel_width": 10.0,
    #         "parcel_weight": 100.0,
    #         "parcel_height": 10.0,
    #         "parcel_qty": 1,
    #         "parcel_size_unit": "INCH",
    #         "parcel_weight_unit": "LB",
    #
    #         "declared_nameEN": "iphone",
    #         "declared_nameCN": "iphone",
    #         "declared_price": 100.0,
    #         "declared_currency": "USD",
    #         "item_qty": 1,
    #         "item_weight": 12.0,
    #         "customs_code": "",
    #         "remark": "",
    #         "texture": "",
    #         "model": "",
    #         "use": "",
    #         "brand": ""
    #     }],
    #     "is_overseas_return": 0,
    #     "is_electronic": 0,
    #     "taxInfo": {
    #         "tax_type": "IOSS",
    #         "tax_no": "IOSS0001"
    #     },
    #     "weight": 13.0,
    #     "signature_type": "D",
    #     "service_code": "P10003",
    #     "customer_order_num": "TT000001"
    # }

    # server_url = 'http://manage.tangus.cn'
    # server_url = 'http://cshm.mz56.com'
    # server_url = 'http://127.0.0.1:8000'
    # server_url = 'http://test.puxinc.com:38080'
    server_url = 'https://ops.4amship.com/apibackend'

    # 小包单api下单测试
    # url = f'{server_url}/api/parcelCustomerOrders/api_create/'
    # url = f'{server_url}/api/parcelCustomerOrders/api_create/'
    url = f'{server_url}/api/parcelCustomerOrders/api_create/'

    # fba订单api下单测试
    # url = f'{server_url}/api/customerOrders/api_create/'

    # token = get_token(server_url, 'HWTBF2430-LM', 'HWTBF2430')
    # token = get_token(server_url, 'ZHGT2423-LM', '123456')
    # token = get_token(server_url, 'admin', 'zfx888')
    # token = get_token(server_url, 'test', 'px2024')
    token = get_token(server_url, 't1', '111111')
    print(token)

    header = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    }
    # while True:
    #     if datetime.datetime.now() > datetime.datetime(2024, 6, 1, 18, 39, 0):
    result = requests.post(url, json=data, headers=header)
    print(result.text)

    return result.json()


def get_label():
    # data = {'order_num': 'FXS2500001544'}
    data = {'customer_order_num': 'TEST20250730003'}

    # server_url ='http://**************:38080'
    server_url ='https://ops.4amship.com/apibackend'

    url = f'{server_url}/api/parcelCustomerOrders/api_label/'

    # token = get_token('http://*************:38080/','admin','px209888')
    # token = get_token('http://*************:38080/', 'yisi', 'yshd@888')
    token = get_token(server_url, 'c11', '111111')
    # token = get_token(server_url, 'parcelLog', '123456')

    header = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
        # 'Authorization': 'AlitaToken:EU7022599D264AD59BF5E8E7E45625EU'
    }

    result = requests.post(url, json=data, headers=header)
    print(result.text)
    return result.json()


def api_get_services():
    url = 'http://cshm.mz56.com/api/products/api_get_services/?type=TR'

    # token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************.wss3SFKtgea9Cqhe5X4vwWBfn4ox-UzDL8Zeq7QqGc4'

    header = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + get_token('http://cshm.mz56.com', 'ERPTEST', '123456')
    }

    result = requests.get(url, headers=header)
    print(result.text)
    return result.json()


def api_cancel():
    data = {'order_num': 'FXS2500000187'}

    # server_url = 'http://127.0.0.1:8000'
    # server_url = 'http://testnew.puxinc.com:38080'
    server_url = 'https://ops.4amship.com/apibackend'

    url = f'{server_url}/api/parcelCustomerOrders/api_cancel_label/'

    # token = get_token(server_url, 'admin', 'px2024')
    token = get_token(server_url, 't1', '111111')

    header = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    }

    result = requests.post(url, json=data, headers=header)
    print(result.text)
    return result.json()


def api_get_label():
    data = {'order_num': 'DFJSEUOUT2501660'}

    server_url = 'https://omsm-eu.easttopintl.com'
    # server_url = 'http://*************:38080'
    # server_url = 'http://test.puxinc.com:38080'

    url = f'{server_url}/api/parcelCustomerOrders/api_get_label/'

    # token = get_token(server_url, 'yisi', 'yshd@888')
    # token = get_token(server_url, 'admin', 'px2025888')

    header = {
        'Content-Type': 'application/json',
        # 'Authorization': 'Bearer ' + token
        'Authorization': 'AlitaToken:EU7022599D264AD59BF5E8E7E45625EU'
    }

    result = requests.post(url, json=data, headers=header)
    print(result.text)
    return result.json()


def api_update_order_weight():
    data = {'order_num': 'FXFRR2400000788', 'weight': '0.2'}

    url = 'http://127.0.0.1:8000/api/parcelCustomerOrders/api_update_order_weight/'

    token = get_token('http://127.0.0.1:8000', 'test2', '123456')

    print(token)

    header = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    }

    result = requests.post(url, json=data, headers=header)
    print(result.text)
    return result.json()


def api_pre_fee():
    data = {"receive_country": "US", "receive_postcode": "94107", "sender_postcode": "60611", "sender_country": "US",
            "parcels": [{"parcel_weight": 1, "parcel_length": 1, "parcel_width": 1, "parcel_height": 1}],
            "service_code": "P10001"}

    # url = 'http://manage.tangus.cn/api/parcelCustomerOrders/api_pre_fee/'
    url = 'https://www.4amship.com/apibackend/api/parcelCustomerOrders/api_pre_fee/'

    # token = get_token('http://manage.tangus.cn', 'HWTBF2430-LM', 'HWTBF2430')
    token = get_token('https://www.4amship.com/apibackend', 'admin', 'px2024')

    print(token)

    header = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    }

    result = requests.post(url, json=data, headers=header)
    print(result.text)
    return result.json()


# Load Testing Configuration
DEFAULT_QPS = 100  # Default queries per second
DEFAULT_DURATION = 10  # Default duration in seconds
BATCH_SIZE = 50  # 减小批量大小，提高并发效率


async def async_get_token(url, username, password):
    """
    异步获取token
    """
    return  'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************.xLDDbWfuVGKmPA5E2-bEebNVxSTb7kwJZ8xak1nHF6E'
    # try:
    #     async with aiohttp.ClientSession() as session:
    #         login_url = f'{url}/auth/login/'
    #         headers = {'Content-Type': 'application/json'}
    #         post_data = {
    #             'username': username,
    #             'password': password
    #         }
    #
    #         async with session.post(login_url, json=post_data, headers=headers, timeout=100) as response:
    #             if response.status != 200:
    #                 print(f"Token request failed with status {response.status}")
    #                 return None
    #
    #             result = await response.json()
    #             if not result or 'token' not in result:
    #                 print("Invalid token response")
    #                 return None
    #
    #             return result.get('token')
    #
    # except asyncio.TimeoutError:
    #     print("Token request timeout")
    #     return None
    # except Exception as e:
    #     print(f"Token request failed: {str(e)}")
    #     return None


async def load_test(api_func, qps=DEFAULT_QPS, duration=DEFAULT_DURATION, *args, **kwargs):
    """
    Load test a specific API function with improved concurrency
    """
    total_requests = qps * duration

    # 预先获取token并重试
    # server_url = 'http://120.76.53.7:8089'
    # server_url = 'https://oms.hanjinexp.com/hjBackend'
    server_url = 'https://test.oms.hanjinexp.com/hjBackend'
    max_retries = 3
    token = None

    for retry in range(max_retries):
        token = await async_get_token(server_url, 'admin', '123456')
        # token = await async_get_token(server_url, 'allentest', '123456')
        if token:
            break
        print(f"Retrying token request {retry + 1}/{max_retries}")
        await asyncio.sleep(1)

    if not token:
        print("Failed to get token after retries")
        return

    # 创建一个session供所有请求使用
    connector = aiohttp.TCPConnector(limit=0, ttl_dns_cache=300)
    timeout = aiohttp.ClientTimeout(total=30)
    ssl_context = ssl.create_default_context(cafile=certifi.where())

    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context), timeout=timeout) as session:
        start = time.time()
        results = []

        # 创建所有任务但分批执行
        for i in range(0, total_requests, BATCH_SIZE):
            batch_tasks = []
            for j in range(BATCH_SIZE):
                if i + j < total_requests:
                    task = asyncio.create_task(api_func(session, token, i + j))
                    batch_tasks.append(task)

            if batch_tasks:
                # 等待当前批次完成
                batch_start = time.time()
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                batch_time = time.time() - batch_start

                # 控制发送速率
                desired_batch_time = BATCH_SIZE / qps
                if batch_time < desired_batch_time:
                    await asyncio.sleep(desired_batch_time - batch_time)

                results.extend(batch_results)

                # 打印进度
                completed = len(results)
                print(f"Progress: {completed}/{total_requests} requests completed")

        end_time = time.time()
        total_time = end_time - start

        # 统计结果
        successful_requests = len([r for r in results if r is not None and not isinstance(r, Exception)])
        failed_requests = total_requests - successful_requests

        print(f"\nLoad Test Results:")
        print(f"Total Requests: {total_requests}")
        print(f"Successful Requests: {successful_requests}")
        print(f"Failed Requests: {failed_requests}")
        print(f"Total Time: {total_time:.2f} seconds")
        print(f"Actual QPS: {total_requests / total_time:.2f}")
        if successful_requests > 0:
            print(f"Average Response Time: {(total_time / successful_requests * 1000):.2f} ms")


async def async_api_create(session, token, index):
    """
    Optimized async create order function
    """
    try:
        data = {
    # "customer_order_num": f"T6-202406240000{index}",
    "customer_order_num": f"A5-202504250000{index}",
    "service_code": "P10001",
    "shipperInfo": {
        "contact_name": "allengned",
        "company_name": None,
        "contact_email": None,
        "contact_phone": None,
        "contact_mobile": "000000",
        "country_code": "CN",
        "state_code": "GUANGDONG",
        "city_code": "DONGGUAN",
        "region_code": None,
        "postcode": "87237",
        "house_no": None,
        "street": None,
        "address_one": "CHANGAN",
        "address_two": None,
        "id_type": None,
        "id_number": None
    },
      "returninfo": {
        "contact_name": "jky",
        "company_name": None,
        "contact_email": None,
        "contact_phone": None,
        "contact_mobile": "000000",
        "country_code": "CN",
        "state_code": "GUANGDONG",
        "city_code": "DONGGUAN",
        "region_code": None,
        "postcode": None,
        "house_no": None,
        "street": None,
        "address_one": "CHANGAN",
        "address_two": None,
        "id_type": None,
        "id_number": None
    },
    "recipientInfo": {
        "contact_name": "유수형",
        "company_name": None,
        "contact_email": None,
        "contact_phone": "01037235112",
        "contact_mobile": "01037235112",
        "country_code": "KR",
        "state_code": "경기도",
        "city_code": "부천시",
        "region_code": None,
        "postcode": "14581",
        "house_no": None,
        "street": None,
        "address_one": "경기도 부천시 원미구 중동로 204 1301동101호 (그린타운 삼성@)경기도경기도",
        "address_two": None
    },
    "weight": 0.2,
    "order_remark": "",
    "parcelItem": [
        {
            "parcel_type": "Parcel",
            "classification": "0",
            "parcel_length": 0.0,
            "parcel_width": 0.0,
            "parcel_height": 0.0,
            "parcel_weight": 0.2,
            "parcel_qty": 1,
            "declared_nameCN": "SL-休闲保暖套装",
            "declared_nameEN": "SL - Casual Warm Set",
            "declared_price": 30.05,
            "declared_currency": "USD",
            "customs_code": None,
            "distribution_remark": "",
            "sku_url": None,
            "origin_country": None,
            "texture": None,
            "use": None,
            "brand": None,
            "model": None,
            "item_qty": 1,
            "item_weight": 0.0
        },
        {
            "parcel_type": "Parcel",
            "classification": "0",
            "parcel_length": 0.0,
            "parcel_width": 0.0,
            "parcel_height": 0.0,
            "parcel_weight": 0.2,
            "parcel_qty": 1,
            "declared_nameCN": "SL-休闲保暖套装",
            "declared_nameEN": "SL - Casual Warm Set",
            "declared_price": 30.05,
            "declared_currency": "USD",
            "customs_code": None,
            "distribution_remark": "",
            "sku_url": None,
            "origin_country": None,
            "texture": None,
            "use": None,
            "brand": None,
            "model": None,
            "item_qty": 1,
            "item_weight": 0.0
        },
        {
            "parcel_type": "Parcel",
            "classification": "0",
            "parcel_length": 0.0,
            "parcel_width": 0.0,
            "parcel_height": 0.0,
            "parcel_weight": 0.2,
            "parcel_qty": 1,
            "declared_nameCN": "SL-休闲保暖套装",
            "declared_nameEN": "SL - Casual Warm Set",
            "declared_price": 30.05,
            "declared_currency": "USD",
            "customs_code": None,
            "distribution_remark": "",
            "sku_url": None,
            "origin_country": None,
            "texture": None,
            "use": None,
            "brand": None,
            "model": None,
            "item_qty": 1,
            "item_weight": 0.0
        }
    ]
}

        server_url = 'http://127.0.0.1:8000'
        # server_url = 'http://120.76.53.7:8089'
        # server_url = 'https://omp.hanjinexp.com/hjBackend'
        # server_url = 'https://test.oms.hanjinexp.com/hjBackend'
        url = f'{server_url}/api/parcelCustomerOrders/api_create/'

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }

        async with session.post(url, json=data, headers=headers, timeout=30) as response:
            if response.status == 429:  # Rate limit
                await asyncio.sleep(1)  # Wait if rate limited
                return None
            return await response.json()

    except asyncio.TimeoutError:
        print(f"Timeout for index {index}")
        return None
    except Exception as e:
        print(f"Create order failed for index {index}: {str(e)}")
        return None


if __name__ == '__main__':
    # 设置更大的并发限制
    # if platform.system() != 'Windows':
    #     import resource
    #
    #     resource.setrlimit(resource.RLIMIT_NOFILE, (65535, 65535))
    #
    # asyncio.run(load_test(
    #     async_api_create,
    #     qps=100,  # 每秒200请求
    #     duration=5,
    #     args=tuple()
    # ))

    # Original test code
    # asyncio.run(attack())

    # for i in range(1, 1000):
    #     create_order(i)

    # import concurrent.futures

    # def create_orders_multithreaded():
    #     with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    #         # 提交1000个任务到线程池
    #         futures = [executor.submit(create_order, i) for i in range(1, 5)]

    #         # 可选：等待所有任务完成并处理异常
    #         for future in concurrent.futures.as_completed(futures):
    #             try:
    #                 future.result()  # 获取结果，如果有异常会在这里抛出
    #             except Exception as e:
    #                 print(f"订单创建失败: {e}")

    # create_orders_multithreaded()

    # res = get_label()
    res = create_order()
    print(res)
    # print(res['data'][0]['label_base64'])
    # with open('a.pdf', "wb") as code:
    #     code.write(base64.b64decode(res['data'][0]['label_base64']))

    # res = api_get_label()
    # print(res['label_infos'][0]['label_base64'])
    # with open('a.pdf', "wb") as code:
    #     code.write(base64.b64decode(res['label_infos'][0]['label_base64']))
    # api_pre_fee()

    # api_cancel()
