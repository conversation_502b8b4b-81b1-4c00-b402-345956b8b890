import json
import traceback
from datetime import datetime
import requests
import base64
import re
from alita.logger import logger
from common.utils.logger_util import alita_logger_info
from common.utils.file_util import upload_file
from order.integration.integrationInterface import IntegrationInterface, get_label_order_vo, get_order_label_obj, \
    get_label_order_vo_v2


# 校验 单位
def get_unit(unit_value):
    if not unit_value:
        logger.error(f"--{unit_value}不能为空")
    unit_value_up = unit_value.upper()
    unit_dict = {
        'CM': 'CM',
        'IN': 'IN',
        'INCH': 'IN',
        'LB': 'LB',
        'POUND': 'LB',
        'KG': 'KG'
    }
    return unit_dict.get(unit_value_up) or ''

def get_phone(phone_input):
    """处理FedEx电话号码输入，分离主号码和分机号，并校验格式合法性。

    参数:
        phone_input (str): 用户输入的电话号码字符串（可能包含分机号，如 "13145976399 ext. 1234"）

    返回:
        dict: {
            "valid": bool,          # 是否合法
            "phone_number": str,    # 标准化后的主号码（带国家代码）
            "phone_extension": str, # 分机号（无ext.前缀）
            "errors": list[dict]    # 错误详情（若存在）
        }"""
    result = {
        "valid": True,
        "phone_number": phone_input,
        "phone_extension": "",
        "errors": []
    }

    if not phone_input:
        result["valid"] = False
        result["errors"].append({"code": "EMPTY_PHONE", "message": "电话号码不能为空"})
        return result

    # --------------------------
    # 第一步：分离主号码和分机号
    # --------------------------
    phone_parts = re.split(r'(?:ext\.|x)\s*(\d+)', phone_input, flags=re.IGNORECASE)
    if len(phone_parts) == 1:
        # 无分机号的情况
        raw_phone = phone_parts[0].strip()
        extension = ""
    else:
        # 含分机号的情况
        raw_phone = phone_parts[0] .strip()
        extension = phone_parts[1].strip()
        if not re.fullmatch(r'^\d{1,6}$', extension):
            result["valid"] = False
            result["errors"].append({
                "code": "EXTENSION_INVALID",
                "message": f"分机号必须是1-6位数字，当前为: {extension}"
            })

    # --------------------------
    # 第二步：校验主号码格式
    # --------------------------
    # 移除所有非数字字符（保留可能的+国家代码）
    cleaned_phone = re.sub(r'[^\d+]', '', raw_phone)

    # 检查国家代码（+1或1开头）和号码长度
    if re.match(r'^(\+1|1)?\d+$', cleaned_phone):
        # 美国/加拿大号码：必须恰好10位数字（+1或1可选）
        digits_only = re.sub(r'^\+?1', '', cleaned_phone)
        if len(digits_only) != 10:
            result["valid"] = False
            result["errors"].append({
                "code": "US_CA_PHONE_LENGTH",
                "message": "美国/加拿大号码必须为10位数字（不含国家代码）"
            })
        else:
            # 标准化：添加+1前缀（FedEx推荐格式）
            result["phone_number"] = f"+1{digits_only}" if not cleaned_phone.startswith(('+1', '1')) else cleaned_phone
    else:
        # 国际号码：1-15位数字（允许国家代码）
        if len(cleaned_phone) < 10 or len(cleaned_phone) > 15:
            result["valid"] = False
            result["errors"].append({
                "code": "INTERNATIONAL_PHONE_LENGTH",
                "message": "国际号码长度需在10-15位之间（含国家代码）"
            })
        else:
            result["phone_number"] = cleaned_phone

    # --------------------------
    # 第三步：处理分机号
    # --------------------------
    if extension:
        if result["valid"]:  # 仅当主号码合法时才保留分机号
            result["phone_extension"] = extension
        else:
            result["phone_extension"] = ""  # 主号码非法时分机号无效
    logger.info(f'电话校验，{phone_input}--结果:{result}')
    return result

def build_address_data(order_label_task, name, address1, address2, city, state, country, postcode, phone_cus, email, company_name="",
                       supplier_supports_address2=True):
    """
    标准地址处理函数
    ⚠️ 重要：FedEx支持address2字段（通过streetLines数组）
    FedEx要求姓名、城市至少2个字符
    state 州代码标准化
    根据州代码修正邮政编码
    """
    if not name or len(name) < 2:
        save_fail_result(order_label_task, f'下单失败: 姓名不能少于2个字符:{name}', 121)
    phone_check = get_phone(phone_cus)
    if not phone_check["valid"]:
        logger.error(f'下单失败，电话号码错误，{phone_check["errors"]}')
        # save_fail_result(order_label_task, f'下单失败: 电话号码格式错误:{phone}', 122)
    phone = phone_check["phone_number"]
    if not phone:
        phone = phone_cus
    phone_extension = phone_check["phone_extension"]
    if supplier_supports_address2:
        # FedEx支持地址2，通过streetLines数组
        street_lines = [address1]
        if address2 and address2.strip():
            street_lines.append(address2)

        return_info =  {
            "contact": {
                "personName": name,
                # "companyName": company_name or "",
                "phoneNumber": phone,
                "emailAddress": email or ""
            },
            "address": {
                "streetLines": street_lines,
                "city": city,
                "stateOrProvinceCode": state,
                "postalCode": postcode,
                "countryCode": country
            }
        }
        if phone_extension:
            return_info["contact"]["phoneExtension"] = phone_extension
        return return_info
    else:
        # 如果不支持address2，合并到address1
        if address2:
            combined_address = f"{address1} {address2}".strip()
        else:
            combined_address = address1

        return_info = {
            "contact": {
                "personName": name,
                # "companyName": company_name or "",
                "phoneNumber": phone or "",
                "emailAddress": email or ""
            },
            "address": {
                "streetLines": [combined_address],
                "city": city,
                "stateOrProvinceCode": state,
                "postalCode": postcode,
                "countryCode": country
            }
        }
        if phone_extension:
            return_info["contact"]["phoneExtension"] = phone_extension
        return return_info





def get_call(customer_order, label_order_vo, service, is_unpack):
    """
    FedEx API调用核心函数
    """
    # 1. 获取标准化数据
    # is_customer_order, parcel_order_num, service_code, supplier_account, warehouse = \
    #     get_label_order_vo(customer_order, label_order_vo, service)
    order_label_task = label_order_vo.orderLabelTask
    label_order_vo_data = get_label_order_vo_v2(customer_order, label_order_vo, service)
    service_dict = label_order_vo.service_dict
    #
    is_customer_order = label_order_vo_data.is_customer_order
    parcel_order_num = label_order_vo_data.parcel_order_num
    service_code = label_order_vo_data.service_code
    supplier_account = label_order_vo_data.supplier_account
    warehouse = label_order_vo_data.warehouse
    recipient_info = label_order_vo_data.recipient_info
    #
    product = label_order_vo.product

    # 3. 确认FedEx支持address2字段（通过streetLines数组）True
    supplier_supports_address2 = service_dict.get("supplier_supports_address2", True)
    logger.info(f'---new--FedEx是否支持address2字段：{supplier_supports_address2}')
    # 4. 构建收件人地址
    recipient_data = build_address_data(
        order_label_task=order_label_task,
        name=recipient_info.contact_name,  # 姓名校验，必填
        address1=recipient_info.address_one,
        address2=recipient_info.address_two,
        city=recipient_info.city_code,
        state=recipient_info.state_code,
        country=recipient_info.country_code,
        postcode=recipient_info.postcode,
        phone_cus=recipient_info.contact_phone,
        email=recipient_info.contact_email or "",
        # company_name=customer_order.buyer_company_name or "",
        supplier_supports_address2=supplier_supports_address2
    )

    # 5. 构建发件人地址
    # if is_customer_order:
    #     sender_obj = customer_order
    #     sender_company = sender_obj.customer.name or ""
    # else:
    #     sender_obj = warehouse
    #     sender_company = sender_obj.company_name or ""
    sender_data = build_address_data(
        order_label_task=order_label_task,
        name=warehouse.contact_name,
        address1=warehouse.address_one,
        address2=warehouse.address_two,
        city=warehouse.city_code,
        state=warehouse.state_code,
        country=warehouse.country_code,
        postcode=warehouse.postcode,
        phone_cus=warehouse.contact_phone or "",
        email=warehouse.contact_email or "",
        # company_name= sender_company,
        supplier_supports_address2=supplier_supports_address2
    )

    # 6. 获取OAuth令牌
    base_url = service_dict.get('url')
    token_url = f"{base_url}/oauth/token"
    token_headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    token_data = {
        "grant_type": "client_credentials",
        "client_id": service_dict.get('auth_id'),
        "client_secret": service_dict.get('auth_pwd')
    }
    # logger.info(f'---new---token:{token_data},--{supplier_account.service_type}')
    try:

        logger.info(f'fedex {token_url} 入参:{token_data}')
        token_response = requests.post(token_url, headers=token_headers, data=token_data, timeout=30)
        logger.info(f'fedex {token_url} 出参:{token_response.text}')

        if token_response.status_code != 200:
            raise Exception(f"获取令牌失败: {token_response.text}")
        
        token_data = token_response.json()
        access_token = token_data.get("access_token")
        if not access_token:
            raise Exception("响应中未找到access_token")
    except Exception as e:
        raise Exception(f"认证失败: {e}")

    # 7. 构建包裹信息
    parcel_info_list = label_order_vo.parcelList
    packages = []
    for parcel_info in parcel_info_list:
        # 重量处理
        weight = float(parcel_info.label_weight or parcel_info.parcel_weight)

        # FedEx对包裹尺寸有严格限制
        # 最小尺寸：1英寸 x 1英寸 x 1英寸
        # 最大尺寸：108英寸 x 108英寸 x 108英寸
        # 周长限制：165英寸（长+宽+高）

        # 最终验证包裹数据
        package = {
            "weight": {
                "units": get_unit(parcel_info.parcel_weight_unit),
                "value": weight
            },
            "dimensions": {
                "length": float(parcel_info.parcel_length),
                "width": float(parcel_info.parcel_width),
                "height": float(parcel_info.parcel_height),
                "units": get_unit(parcel_info.parcel_size_unit)
            },
            "customerReferences": [
                {
                    "customerReferenceType": "CUSTOMER_REFERENCE",
                    "value": f"{parcel_order_num}"
                }
            ]
        }
        packages.append(package)


    # 9. 构建商品信息
    parcel_item_list = label_order_vo.parcelItemList
    commodities = []
    if parcel_item_list:
        for item in parcel_item_list:
            commodity = {
                # "description": '',
                "countryOfManufacture": item.origin_country or "",  # 根据实际情况调整
                "quantity": item.item_qty,
                # "quantityUnits": "",  # "EA"商品数量的单位
                "unitPrice": {
                    "amount": float(item.declared_price),
                    "currency": item.declared_currency or ""
                },
                "weight": {
                    "units": get_unit(item.weight_unit),
                    "value": item.item_weight
                },
                "harmonizedCode": item.customs_code or ""
            }
            commodities.append(commodity)
    else:
        logger.info("---new--no_valid_commodities_found")

    # 10. 构建FedEx订单数据
    # 获取服务类型
    service_type = service_dict.get("service_type")
    if not service_type:
        # service_type = "FEDEX_GROUND"  # 默认使用FedEx Ground服务
        logger.error(f"请配置服务类型，{service_dict}")
    payment_type = service_dict.get('payment_type')  # 运费支付方式（如 "SENDER"）
    if not payment_type:
        logger.error(f"请配置运费支付方式，{payment_type}")
    image_type = service_dict.get('image_type')
    if not image_type:
        logger.error(f"请配置运费支付方式，{image_type}")

    packaging_type = service_dict.get('packaging_type')
    # logger.info(f'---new---packaging_type.--{packaging_type}，{service_dict}')

    # 设置pickupType，如果没有配置则使用默认值
    pickup_type = service_dict.get('pickup_type')
    
    # 验证和修正 pickupType 值
    # valid_pickup_types = ["DROPOFF_AT_FEDEX_LOCATION", "USE_SCHEDULED_PICKUP", "REGULAR_PICKUP"]
    # if not pickup_type or pickup_type not in valid_pickup_types:
    #     # 如果是 DROP_OFF 或其他无效值，映射为正确的值
    #     if pickup_type == "DROP_OFF":
    #         pickup_type = "DROPOFF_AT_FEDEX_LOCATION"
    #     else:
    #         pickup_type = "REGULAR_PICKUP"  # 默认值
    #     logger.info(f"修正 pickupType 为有效值: {pickup_type}")
    
    # logger.info(f'---new---pickup_type.--{pickup_type}')
    label_stock_type = service_dict.get('label_stock_type')

    order_data = {
        # "labelResponseOptions": "LABEL",  # 改为LABEL以获得纯净面单
        "labelResponseOptions": "URL_ONLY",  # 改链接
        "requestedShipment": {
            "shipper": sender_data,
            "recipients": [recipient_data],
            "shipDatestamp": datetime.now().strftime("%Y-%m-%d"),
            "serviceType": service_type,  # 使用处理后的服务类型
            "packagingType": packaging_type,
            "pickupType": pickup_type,
            "blockInsightVisibility": False,
            "shippingChargesPayment": {
                "paymentType": payment_type
            },
            "labelSpecification": {
                "imageType": image_type,  # 根据打印机类型选择合适的imageType
                "labelStockType": label_stock_type  # 使用支持的labelStockType
            },
            "requestedPackageLineItems": packages
        },
        "accountNumber": {
            "value": service_dict.get('account_name')
        }
    }

    # 13. API调用
    url = f"{base_url}/ship/v1/shipments"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "X-locale": "en_US"
    }

    # 14. 记录请求日志
    operate_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    alita_logger_info(f'FedEx请求url={url} 入参: ', json.dumps(order_data, ensure_ascii=False), operate_time, customer_order.order_num)

    # 15. 发起请求
    response = requests.post(url, json=order_data, headers=headers, timeout=60)

    # 16. 记录响应日志
    alita_logger_info(f'FedEx请求url={url} 出参: ', response.text, operate_time, customer_order.order_num)

    return response


class FedExIntegrationService(IntegrationInterface):
    """
    FedEx 物流供应商集成服务
    """

    def create_order(self, labelOrderVo):
        """创建FedEx订单"""
        logger.info("FedEx create_order")
        
        service = labelOrderVo.service
        customer_order = labelOrderVo.customerOrder
        order_label_task = labelOrderVo.orderLabelTask
        is_unpack = labelOrderVo.is_unpack
        # label_order_vo_data = get_label_order_vo_v2(customer_order, label_order_vo, service)

        try:
            # 调用FedEx API
            response = get_call(customer_order, labelOrderVo, service, is_unpack)
        except Exception as e:
            logger.error(f'FedEx create_order 抛出异常：{e}')
            logger.error(f'错误堆栈：{traceback.format_exc()}')
            save_fail_result(order_label_task, f'下单失败: {e}', 121)
            return

        # 检查HTTP状态码
        if response.status_code != 200:
            save_fail_result(order_label_task, f'API调用失败: {response.text}', 121)
            return

        try:
            result = response.json()
        except:
            save_fail_result(order_label_task, f'响应解析失败: {response.text}', 121)
            return

        # 检查FedEx API错误
        if result.get('errors'):
            error_msg = result['errors'][0].get('message', '未知错误')
            save_fail_result(order_label_task, f'下单失败: {error_msg}', 121)
            return

        # 解析成功响应
        output = result.get('output', {})
        transaction_shipments = output.get('transactionShipments', [])
        
        if not transaction_shipments:
            save_fail_result(order_label_task, '下单失败: 未获取到订单信息', 121)
            return

        shipment = transaction_shipments[0]
        masterTrackingNumber = shipment.get('masterTrackingNumber')
        if not masterTrackingNumber:
            save_fail_result(order_label_task, '下单失败: 未获取到追踪号', 121)
            return

        # 获取面单信息
        pieces = shipment.get('pieceResponses', [])
        for piece in pieces:
            i = piece.get('packageSequenceNumber', 1)
            packageDocuments = piece.get("packageDocuments", [{}])[0]
            masterTrackingNumber = piece.get("masterTrackingNumber")
            tracking_number = piece.get("trackingNumber")
            label_url = packageDocuments.get("url")
            label_file_path = f"label/{datetime.now().strftime('%Y/%m/%d/')}{customer_order.order_num}_{i}.pdf"
            upload_file(label_file_path, label_url, 'url')

            order_num = customer_order.order_num
            order_label = get_order_label_obj(order_num, customer_order, tracking_number)
            order_label.order_num = customer_order
            order_label.tracking_no = tracking_number
            order_label.third_order_no = tracking_number
            order_label.label_url = label_file_path
            order_label.create_by = order_label_task.create_by
            order_label.create_date = datetime.now()
            order_label.product = order_label_task.product
            order_label.save()

        # 更新任务状态
        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'
        order_label_task.third_order_no = masterTrackingNumber
        order_label_task.update_date = datetime.now()
        order_label_task.save()

        # 更新客户订单
        customer_order.tracking_num = masterTrackingNumber
        customer_order.update_date = datetime.now()
        # 更新小包单状态
        customer_order.order_status = 'GL'
        # 是否换单状态
        customer_order.is_change_waybill = True
        customer_order.save(update_fields=['order_status', 'update_date', 'tracking_num', 'is_change_waybill'])

        logger.info(f"FedEx 下单成功: {customer_order.order_num} -> {masterTrackingNumber}")

    def get_label(self, label_order_vo):
        """获取面单 - 如果create_order没有返回面单，在这里实现"""
        pass

    def cancel_label(self, label_order_vo):
        """取消FedEx面单"""
        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount
        service_dict = label_order_vo.service_dict

        # 检查是否有追踪号
        if not order_label_task.third_order_no:
            return {'code': '0'}

        base_url = service_dict.get('url')
        try:
            # 获取OAuth令牌
            token_url = f"{base_url}/oauth/token"
            token_headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            token_data = {
                "grant_type": "client_credentials",
                "client_id": supplier_account.auth_id,
                "client_secret": supplier_account.auth_pwd
            }

            token_response = requests.post(token_url, headers=token_headers, data=token_data, timeout=30)
            if token_response.status_code != 200:
                return {'code': '400', 'msg': f'获取令牌失败: {token_response.text}'}
            
            token_data = token_response.json()
            access_token = token_data.get("access_token")
            if not access_token:
                return {'code': '400', 'msg': '响应中未找到access_token'}

            # 调用取消API
            url = f"{base_url}/ship/v1/shipments/cancel"
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "X-locale": "en_US"
            }
            
            cancel_data = {
                "accountNumber": {
                    "value": service_dict.get('account_name')
                },
                "trackingNumber": order_label_task.third_order_no
            }
            
            response = requests.put(url, json=cancel_data, headers=headers, timeout=60)
            
            if response.status_code == 200:
                return {'code': '0'}
            else:
                return {'code': '400', 'msg': response.text}
                
        except Exception as e:
            return {'code': '400', 'msg': str(e)}

    def get_order(self, label_order_vo):
        """获取FedEx订单信息"""
        pass

    def confirm_ship(self, label_order_vo):
        """确认发货"""
        pass

    def update_order(self, label_order_vo):
        """更新订单"""
        pass

    def scanform(self, label_order_vo):
        """扫描表单"""
        pass

    def get_trackings(self, label_order_vo):
        """获取FedEx物流追踪信息"""
        try:
            customer_order = label_order_vo.customerOrder
            supplier_account = label_order_vo.supplierAccount
            service_dict = label_order_vo.service_dict
            
            if not customer_order.tracking_num:
                return {'msg': '无追踪号'}

            # 获取OAuth令牌
            token_url = f"{supplier_account.url}/oauth/token"
            token_headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            token_data = {
                "grant_type": "client_credentials",
                "client_id": service_dict.get('auth_id'),
                "client_secret": service_dict.get('auth_pwd')
            }

            token_response = requests.post(token_url, headers=token_headers, data=token_data, timeout=30)
            if token_response.status_code != 200:
                return {'msg': f'获取令牌失败: {token_response.text}'}
            
            token_data = token_response.json()
            access_token = token_data.get("access_token")
            if not access_token:
                return {'msg': '响应中未找到access_token'}

            # 调用追踪API
            url = f"{supplier_account.url}/track/v1/trackingnumbers"
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "X-locale": "en_US"
            }
            
            tracking_data = {
                "includeDetailedScans": True,
                "trackingInfo": [
                    {
                        "trackingNumberInfo": {
                            "trackingNumber": customer_order.tracking_num
                        }
                    }
                ]
            }
            
            response = requests.post(url, json=tracking_data, headers=headers, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                output = result.get("output", {})
                complete_track_results = output.get("completeTrackResults", [])
                
                if complete_track_results:
                    track_result = complete_track_results[0]
                    track_results = track_result.get("trackResults", [])
                    
                    if track_results:
                        track_info = track_results[0]
                        latest_status = track_info.get("latestStatusDetail", {})
                        status_code = latest_status.get("code")
                        
                        # FedEx状态映射
                        # status_map = {
                        #     "IT": "TRANSIT",      # In Transit
                        #     "DL": "SF",           # Delivered
                        #     "DE": "SF",           # Delivered
                        #     "EX": "EXCEPTION",    # Exception
                        #     "OC": "TRANSIT",      # On the way
                        #     "PU": "TRANSIT",      # Picked up
                        #     "CA": "EXCEPTION"     # Cancelled
                        # }
                        status_map = {
                            "IT": "TRANSIT",  # In Transit
                            "DL": "FC",  # Delivered
                            "DE": "FC",  # Delivered
                            "EX": "DEO",  # Exception
                            "OC": "TRANSIT",  # On the way
                            "PU": "TRANSIT",  # Picked up
                            "CA": "VO"  # Cancelled
                        }
                        
                        order_status = status_map.get(status_code)
                        if order_status and order_status != customer_order.order_status:
                            customer_order.order_status = order_status
                            customer_order.save(update_fields=['order_status', "update_date"])
                        
                        return {'code': '0'}
                    
                return {'msg': '未找到追踪信息'}
            else:
                return {'msg': response.text}
                
        except Exception as e:
            return {'msg': str(e)}


def save_fail_result(order_label_task, result, handle_times=1):
    """保存失败结果"""
    order_label_task.label_desc = result
    order_label_task.handle_times += handle_times
    order_label_task.update_date = datetime.now()
    order_label_task.save() 