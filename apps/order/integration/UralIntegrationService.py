import json
from datetime import datetime
import os

import requests
from django.forms import model_to_dict

from alita.logger import logger
from common.utils.file_util import upload_file
from order.integration.integrationInterface import IntegrationInterface, get_order_label_obj, \
    get_warehouse_code_v2, get_label_order_vo_v2
from order.integration.util.uralUtil import UralApiClient, LogData
from order.integration.schemas.ural_schema import (
    UralCreateOrderRequest, UralOrderBody, UralSender, UralSenderAddress, UralSenderUserInfo,
    UralReceiver, UralReceiverAddress, UralReceiverUserInfo, UralPassport, UralProduct,
    UralDeliveryInfo, UralGetLabelRequest
)

from alita.settings.base import MEDIA_URL


class UralIntegrationService(IntegrationInterface):
    """
    Ural 接口
    """

    def create_order(self, label_order_vo):
        """
        创建订单
        :param label_order_vo:
        """
        logger.info("Ural create_order")
        customer_order = label_order_vo.customerOrder
        order_label_task = label_order_vo.orderLabelTask
        service = label_order_vo.service
        service_dict = label_order_vo.service_dict
        tax_info = label_order_vo.tax_info

        if not order_label_task:
            logger.info('--无order_label_task--->>')
            return

        label_order_vo_data = get_label_order_vo_v2(customer_order, label_order_vo, service)
        product = label_order_vo_data.product
        supplier_account = label_order_vo_data.supplier_account
        address_num = label_order_vo_data.address_num
        is_customer_order = label_order_vo_data.is_customer_order
        warehouse = label_order_vo_data. warehouse
        return_info = label_order_vo_data.return_info
        recipient_info = label_order_vo_data.recipient_info
        service_code = label_order_vo_data.service_code
        parcel_order_num = label_order_vo_data.parcel_order_num

        try:
            # 创建URAL API客户端
            ural_client = UralApiClient(
                base_url=service_dict.get('url'),
                key=service_dict.get('auth_id'),
                secret=service_dict.get('auth_pwd'),
                timeout=60
            )

            # 构建订单请求
            order_request = self._build_create_order_request(
                customer_order, service, warehouse, recipient_info, parcel_order_num,
                label_order_vo, is_customer_order, service_dict, tax_info
            )

            logger.info(f"Ural req_params: {order_request.to_dict()}")

            log_data = LogData(
                func_name="create_order",
                order_num=customer_order.order_num,
                customer_order_num=customer_order.customer_order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
            )

            # 发起创建订单请求
            response = ural_client.create_order(order_request, log_data)

            logger.info(f"Ural create_order rsp: {response.to_dict()}")

            if response.code == 200 and response.data:
                # 订单创建成功
                order_label_task.status = 'HandledBy3rdNo'
                order_label_task.label_desc = '等待抓取面单'
                order_label_task.third_order_no = response.data.waybill_num
                order_label_task.shipment_digest = response.data.mail_no
                order_label_task.update_date = datetime.now()
                order_label_task.save()

                # 更新客户订单追踪号
                if response.data.waybill_num:
                    customer_order.tracking_num = response.data.waybill_num
                    customer_order.update_date = datetime.now()
                    customer_order.save()

            else:
                # 订单创建失败
                message = response.message or '创建订单失败'
                if len(message) > 1000:
                    save_fail_result(order_label_task, '下单失败:' + message[0:1000])
                else:
                    save_fail_result(order_label_task, '下单失败:' + message)

        except Exception as e:
            logger.error(f"Ural create_order error: {str(e)}")
            save_fail_result(order_label_task, f'创建订单异常: {str(e)}')

    def get_label(self, label_order_vo):
        """
        获取面单
        :param label_order_vo:
        """
        logger.info("Ural get_label")

        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount
        customer_order = label_order_vo.customerOrder
        service_dict = label_order_vo.service_dict

        if not order_label_task.third_order_no:
            save_fail_result(order_label_task, '获取面单失败: 缺少第三方订单号')
            return

        try:
            # 创建URAL API客户端
            ural_client = UralApiClient(
                base_url=service_dict.get('url'),
                key=service_dict.get('auth_id'),
                secret=service_dict.get('auth_pwd'),
                timeout=30
            )

            # 构建获取面单请求
            label_request = UralGetLabelRequest(
                code_type='TYP_WAYBILL',  # 使用URAL单号
                number=order_label_task.third_order_no
            )

            logger.info(f"Ural get_label req_params: {label_request.to_dict()}")

            log_data = LogData(
                func_name="get_label",
                order_num=customer_order.order_num,
                customer_order_num=customer_order.customer_order_num,
                service=customer_order.service.id,
                supplier=customer_order.service.supplier.id,
                customer=customer_order.customer.id,
            )

            # 发起获取面单请求
            response = ural_client.get_order_supplier_label(label_request, log_data)

            logger.info(f"Ural get_label rsp: {response.to_dict()}")

            if response.code == 200 and response.data and response.data.supplier_pdf_url:
                # 面单获取成功，下载并保存面单
                self._save_label(response.data, customer_order, order_label_task)

                order_label_task.status = 'Success'
                order_label_task.label_desc = 'Success'
                order_label_task.update_date = datetime.now()
                order_label_task.save()

                # 订单状态变更
                customer_order.order_status = 'GL'
                # 是否换单状态
                customer_order.is_change_waybill = True
                customer_order.save(update_fields=['order_status', 'is_change_waybill'])

                # 更新客户订单追踪号
                if response.data.mail_no:
                    customer_order.tracking_num = response.data.mail_no
                    customer_order.update_date = datetime.now()
                    customer_order.save()

            else:
                # 面单获取失败
                message = response.message or '获取面单失败'
                save_fail_result(order_label_task, '获取面单失败: ' + message)

        except Exception as e:
            logger.error(f"Ural get_label error: {str(e)}")
            save_fail_result(order_label_task, f'获取面单异常: {str(e)}')

    def _build_create_order_request(self, customer_order, service, warehouse, recipient_info, parcel_order_num,
                                    label_order_vo, is_customer_order, service_dict, tax_info):
        """构建创建订单请求"""

        # 构建发件人信息
        sender = self._build_sender_info(warehouse, tax_info)

        # 构建收件人信息
        receiver = self._build_receiver_info(recipient_info, tax_info)

        # 构建商品信息
        products = self._build_products_info(label_order_vo.parcelItemList)

        # 计算包裹尺寸和重量 - 修改逻辑
        parcel_list = label_order_vo.parcelList
        total_weight = 0
        # 取第一个包裹的尺寸
        first_parcel = parcel_list[0] if parcel_list else None
        parcel_length = float(first_parcel.parcel_length or 0) if first_parcel else 0
        parcel_width = float(first_parcel.parcel_width or 0) if first_parcel else 0
        parcel_height = float(first_parcel.parcel_height or 0) if first_parcel else 0

        # 计算总重量
        for parcel_info in parcel_list:
            if is_customer_order:
                total_weight = float(parcel_info.label_weight or 0)
            else:
                total_weight = float(parcel_info.label_weight or parcel_info.parcel_weight or 0)

        # 计算总申报价值
        total_declare_value = 0
        currency = 'RUB'   # 默认卢布
        for product in products:
            total_declare_value += product.declareValue * product.quantity
            if product.currency:
                currency = product.currency

        # 构建订单主体
        order_body = UralOrderBody(
            orderNum=parcel_order_num,

            length=parcel_length,  # 使用第一个包裹的长度
            width=parcel_width,  # 使用第一个包裹的宽度
            height=parcel_height,  # 使用第一个包裹的高度
            weight=total_weight * 1000,  # 转换为克

            currency=currency,
            declareValue=total_declare_value,

            deliveryInfo=UralDeliveryInfo(deliveryType='Courier'),
            products=products,
            receiver=receiver,
            sender=sender,
        )

        create_order_request = UralCreateOrderRequest(
            channelType=service_dict.get('channelType'),
            countryCode=receiver.address.countryCode,
            clearanceSupplierCode=service_dict.get('clearanceSupplierCode'),
            orderBody=order_body,
            supplierCode=service_dict.get('supplierCode')
        )

        return create_order_request

    def _build_sender_info(self, sender_obj, tax_info):
        """构建发件人信息"""
        sender_address = UralSenderAddress(
            address=sender_obj.address_one,
            city=sender_obj.city_code,
            countryCode=sender_obj.country_code,
            district=sender_obj.region_code,
            province=sender_obj.state_code,
            street=sender_obj.street,
            zipCode=sender_obj.postcode
        )

        sender_user_info = UralSenderUserInfo(
            mobile=sender_obj.contact_mobile or sender_obj.contact_phone,
            name=sender_obj.contact_name,
            email=sender_obj.contact_email
        )

        sender = UralSender(
            address=sender_address,
            userInfo=sender_user_info,
        )

        return sender

    def _build_receiver_info(self, recipient_info, tax_info):
        """构建收件人信息"""
        receiver_address = UralReceiverAddress(
            address=recipient_info.address_one,
            city=recipient_info.city_code,
            countryCode=recipient_info.country_code,
            district=recipient_info.region_code,
            province=recipient_info.state_code,
            street=recipient_info.street,
            zipCode=recipient_info.postcode
        )
        passport = UralPassport(
            birthday=recipient_info.birthday,
            firstName=recipient_info.first_name,
            lastName=recipient_info.last_name,
            passportIssuingAuthorityCode=recipient_info.passport_issuing_authority_code,
            passportIssuingAuthorityName=recipient_info.passport_issuing_authority_name,
            passportIssuingDate=recipient_info.passport_issuing_date,
            passportNo=recipient_info.id_number,
            middleName=recipient_info.middle_name,
            taxNumber=tax_info.tax_no,
        )

        receiver_user_info = UralReceiverUserInfo(
            mobile=recipient_info.contact_mobile or recipient_info.contact_phone,
            name=f'{recipient_info.first_name} {recipient_info.middle_name} {recipient_info.last_name}',
            passport=passport,
            email=recipient_info.contact_email
        )

        receiver = UralReceiver(
            address=receiver_address,
            userInfo=receiver_user_info
        )

        return receiver

    def _build_products_info(self, parcel_item_list):
        """构建商品信息"""
        products = []

        for parcel_item in parcel_item_list:
            product = UralProduct(
                brand=parcel_item.brand,
                currency=parcel_item.declared_currency,
                declareValue=float(parcel_item.declared_price or 0),
                desc=parcel_item.item_desc,
                hsCode=parcel_item.in_HScode or parcel_item.customs_code,
                name=parcel_item.declared_nameCN,
                nameRu=parcel_item.item_name,
                nameEn=parcel_item.declared_nameEN,
                productUrlLink=parcel_item.sku_url,
                quantity=int(parcel_item.item_qty or 1),
                skuId=parcel_item.item_code,
                weight=float(parcel_item.item_weight or 0)
            )
            products.append(product)

        return products

    def _save_label(self, label_data, customer_order, order_label_task):
        """保存面单文件"""
        try:
            # 生成面单文件路径
            label_url = f"label/{datetime.now().strftime('%Y/%m/%d/')}{customer_order.order_num}_1.pdf"

            # 上传面单文件
            upload_file(label_url, label_data.supplier_pdf_url, 'url')

            # 保存面单记录
            order_label = get_order_label_obj(customer_order.order_num, customer_order, label_data.waybill_num)
            order_label.order_num = customer_order
            order_label.tracking_no = label_data.mail_no
            order_label.third_order_no = label_data.waybill_num
            order_label.label_url = label_url
            order_label.create_by = order_label_task.create_by
            order_label.create_date = datetime.now()
            order_label.product = order_label_task.product
            order_label.save()

            logger.info(f"Ural 面单保存成功: {customer_order.order_num}")

        except Exception as e:
            logger.error(f"Ural 保存面单失败: {str(e)}")
            raise

    def cancel_label(self, label_order_vo):
        """
        取消面单
        :param label_order_vo:
        """
        pass

    def get_order(self, label_order_vo):
        pass

    def confirm_ship(self, label_order_vo):
        pass

    def update_order(self, label_order_vo):
        pass

    def scanform(self, label_order_vo):
        pass


def save_fail_result(order_label_task, result):
    """保存失败结果"""
    order_label_task.label_desc = result
    order_label_task.handle_times += 1
    order_label_task.update_date = datetime.now()
    order_label_task.save()
