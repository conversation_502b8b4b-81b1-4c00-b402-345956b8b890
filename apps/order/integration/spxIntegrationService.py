import base64
import json
from datetime import datetime
import os

from alita.logger import logger
from common.utils.file_util import upload_file
from common.tools import change_label_common_func

from order.integration.integrationInterface import IntegrationInterface, get_order_label_obj, \
    get_order_num, get_label_order_vo

from alita.settings.base import MEDIA_URL
from order.integration.util.spxUtil import basicInfo, request_server, Features, CustomizedInfoForm, Recipient


def get_call(customer_order, label_order_vo, service, is_unpack, parcel_list, parcel_item_list):
    """
    下单
    :param customer_order:
    :param label_order_vo:
    :param service:
    :return:
    """
    is_customer_order, parcel_order_num, service_code, supplier_account, warehouse = \
        get_label_order_vo(customer_order, label_order_vo, service)

    recipient = Recipient()
    recipient.recipientOrgName = customer_order.buyer_company_name
    recipient.recipientName = customer_order.buyer_name
    recipient.recipientAddress1 = customer_order.buyer_address_one
    recipient.recipientAddress2 = customer_order.buyer_address_two
    recipient.recipientCity = customer_order.buyer_city_code
    recipient.recipientState = customer_order.buyer_state
    recipient.recipientZip5 = customer_order.buyer_postcode
    recipient.recipientPhone = customer_order.buyer_phone
    recipient.recipientCountry = customer_order.buyer_country_code

    customizedInfoForm = CustomizedInfoForm()
    customizedInfoForm.remark1 = customer_order.customer_order_num
    customizedInfoForm.remark2 = customer_order.customer_order_num
    # customizedInfoForm.customizedBarcodeValue = 'LOCALUSPSAXF-1113-3253'
    # customizedInfoForm.customizedInfoType = 'BARCODE'

    # 不需要
    # features = Features()
    # features.pushPreEvent = 'True'
    # features.preEventIntervalInHours = '12'

    # 包裹物品信息
    items = []
    for parcel_item in parcel_item_list:
        item = {
            "name": parcel_item.declared_nameEN,
            "country": 'CN',
            "currency": parcel_item.declared_currency,
            "description": parcel_item.declared_nameEN,
            "hsCode": parcel_item.customs_code,
            "unitWeight": parcel_item.item_weight,
            "unitValue": parcel_item.declared_price,
            "units": parcel_item.item_qty,
        }
        items.append(item)

    for i_parcle in parcel_list:
        basic_info = basicInfo()
        basic_info.courierCode = 'IGD'
        basic_info.pickUpAccountNum = service.code
        basic_info.portOfEntry = 'AUTO'
        basic_info.weight = float(i_parcle.label_weight or i_parcle.parcel_weight)
        basic_info.length = float(i_parcle.parcel_length)
        basic_info.width = float(i_parcle.parcel_width)
        basic_info.height = float(i_parcle.parcel_height)
        basic_info.dimensionUom = 'CM'
        basic_info.weightUom = 'KG'
        # 订单号
        basic_info.customerRefId = parcel_order_num

        basic_info.validateAddress = False
        basic_info.customizedInfoForm = customizedInfoForm.__dict__
        basic_info.recipient = recipient.__dict__
        basic_info.commodityForms = items
        # 预先推送
        # basic_info.features = {"pushPreEvent": True, "preEventIntervalInHours": "12"}

        url = supplier_account.url + '/rps-api/label/create-label'
        headers = {
            'api-key': supplier_account.auth_pwd,
            'Content-Type': 'application/json',
        }
        result = request_server(basic_info.__dict__, url, headers, customer_order.order_num)
        return result


def get_sender_address(warehouse):
    pass


class SpxIntegrationService(IntegrationInterface):
    """
    SpxShipping接口
    """

    def create_order(self, labelOrderVo):
        """
        创建订单
        :param labelOrderVo:
        """
        logger.info("spx shpping create_order")
        service = labelOrderVo.service
        product = labelOrderVo.product
        customer_order = labelOrderVo.customerOrder
        order_label_task = labelOrderVo.orderLabelTask
        is_unpack = labelOrderVo.is_unpack
        parcel_list = labelOrderVo.parcelList
        parcel_item_list = labelOrderVo.parcelItemList
        result = get_call(customer_order, labelOrderVo, service, is_unpack, parcel_list, parcel_item_list)
        if result:
            if 'code' in result.keys() and 'message' in result.keys() and result['code'] != 200:
                items_info = result['message']
                if items_info:
                    errorMessages = str(items_info)
                else:
                    errorMessages = str(result['success'])
                order_label_task.label_desc = str(errorMessages)
                order_label_task.handle_times = 121
                order_label_task.update_date = datetime.now()
                order_label_task.save()
                return

            elif 'payload' not in result.keys():
                order_label_task.label_desc = '无面单信息返回'
                order_label_task.handle_times = 121
                order_label_task.update_date = datetime.now()
                order_label_task.save()
                return

            main_tracking_no = result['payload']['trackingNumber']

            label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + customer_order.order_num + ".pdf"

            label_data = result['payload']['labelBase64']
            upload_file(label_url, label_data, 'base64')

            # decoded_file_name = MEDIA_URL + labelUrl
            # dir = os.path.dirname(decoded_file_name)
            # if not os.path.exists(dir):
            #     os.makedirs(dir)
            # with open(decoded_file_name, "wb") as code:
            #     code.write(base64.b64decode(label_data))

            order_num = customer_order.order_num
            order_label = get_order_label_obj(order_num)
            order_label.order_num = customer_order
            order_label.tracking_no = main_tracking_no
            order_label.third_order_no = main_tracking_no
            order_label.label_url = label_url
            order_label.create_by = order_label_task.create_by
            order_label.create_date = datetime.now()
            order_label.product = order_label_task.product
            order_label.save()

            order_label_task.status = 'Success'
            order_label_task.label_desc = 'Success'
            order_label_task.third_order_no = main_tracking_no
            order_label_task.update_date = datetime.now()
            order_label_task.save()

            customer_order.tracking_num = main_tracking_no
            customer_order.update_date = datetime.now()
            customer_order.save()

            # 面单更改逻辑
            order_label_changed = get_order_label_obj(order_num)
            try:
                change_label_common_func(service, product, customer_order, order_label,
                                         customer_order.order_num, order_label_changed, order_label_task,
                                         log_flag='spx')
            except Exception as e:
                order_label_task.status = 'UnHandled'
                order_label_task.label_desc = '更改面单失败：' + str(e)
                order_label_task.handle_times = 121
                order_label_task.update_date = datetime.now()
                order_label_task.save()

        elif 'message' in result.keys():
            order_label_task.label_desc = result['message']
            order_label_task.handle_times = 121
            order_label_task.update_date = datetime.now()
            order_label_task.save()
        else:
            order_label_task.label_desc = '处理异常'
            order_label_task.handle_times = 121
            order_label_task.update_date = datetime.now()
            order_label_task.save()

    def get_label(self, label_order_vo):
        """
        获取面单
        :param label_order_vo:
        """
        pass

    def cancel_label(self, label_order_vo):
        """
        取消面单
        :param label_order_vo:
        """

        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount

        data = [order_label_task.third_order_no]

        payload = json.dumps(data)
        print(payload)
        url = f"{supplier_account.url}/rps-api/label/void-labels"
        headers = {
            'api-key': supplier_account.auth_pwd,
            'Content-Type': 'application/json',
        }

        result = request_server(data, url, headers)
        result_data = {}
        if int(result['code']) == 200:
            result_data['code'] = '0'
        else:
            result_data['code'] = '400'
            result_data['msg'] = result['message']

        return result_data


    def get_order(self, label_order_vo):
        pass

    def confirm_ship(self, label_order_vo):
        """
       确认发货
       :param label_order_vo:
       """
        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount

        password = supplier_account.auth_pwd

        params = {
            'description': order_label_task.label_desc,
            'parcelList': [{
                "trackingNumber": order_label_task.third_order_no,
                "receptacleId": order_label_task.parcel
            }]
        }
        url = supplier_account.url + '/rps-api/manifest/submit'
        headers = {
            'api-key': password,
            'Content-Type': 'application/json',
        }
        result = request_server(params, url, headers)

        result_data = {}
        if 'code' in result.keys() and str(result['code']) == '200' and len(result['payload']['successCount']) > 0:
            result_data['code'] = '200'
        else:
            result_data['code'] = '400'
            if 'msg' in result.keys() and result['msg']:
                result_data['msg'] = result['msg']
            else:
                result_data['msg'] = '确认发货失败'

        return result_data

    def update_order(self, label_order_vo):
        pass

    def get_secondary_label(self, label_order_vo):
        '''
        二次获取面单
        '''
        pass

    def scanform(self, label_order_vo):
        pass


def save_fail_result(order_label_task, result):
    order_label_task.label_desc = result
    order_label_task.handle_times += 1
    order_label_task.update_date = datetime.now()
    order_label_task.save()
