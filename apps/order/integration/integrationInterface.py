import abc
import json
import random
from dataclasses import dataclass
from typing import Any, Optional, List, Union
from decimal import Decimal

from django.conf import settings
from datetime import datetime

from common.error import ParamError, ErrorCode
from common.exception_handler import exception_handler
from common.order_num_gen_rule import is_parcel_customer_order
from company.models import Address
from alita.logger import logger
from oms.models import OutboundOrderLabel, OutboundOrderChargeOut,ReturnOrderLabelTask,ReturnOrderLabel
from order.models import ParcelOrderLabel, OrderLabel, CustomerOrderChargeOut, ParcelOrderChargeOut, ParcelOrderAddress
from pms.models import ProductBasicRestriction, ServiceAddressRule, AddressRule


@dataclass
class ParcelInfo:
    """包裹信息"""
    parcel_length: Optional[float] = None  # 包裹长(cm)
    parcel_width: Optional[float] = None  # 包裹宽(cm)
    parcel_height: Optional[float] = None  # 包裹高(cm)
    parcel_weight: Optional[float] = None  # 包裹重量(kg)
    operation_time: Optional[str] = None  # 包裹入库时间(毫秒级时间戳13位整数)


@dataclass
class ContainerInfo:
    """容器(大包)信息"""
    container_id: Optional[str] = None  # 容器号
    length: Optional[float] = None  # 容器长(cm)
    width: Optional[float] = None  # 容器宽(cm)
    height: Optional[float] = None  # 容器高(cm)
    weight: Optional[float] = None  # 容器重量(kg)
    operation_time: Optional[str] = None  # 组包时间(毫秒级时间戳13位整数)


@dataclass
class FlightInfo:
    """航班信息"""
    mawbNo: str  # "123-102991821", //航空主单号
    flightNo: str  # "CA1202", //航班号
    etd: str  # "2023-12-23 08:12:01", //起飞时间
    eta: str  # "2023-12-23 12:12:01", //落地时间
    fromPortCode: str  # "SZX", //起飞口岸
    transitPortCode: str  # "", //中转口岸
    toPortCode: str  # "LAX" //落地口岸
    operation_time: Optional[str] = None  # 交航时间(毫秒级时间戳13位整数)


@dataclass
class AirlineReceiveData:
    """航线接收数据"""
    flightinfo: Optional[FlightInfo] = None      # 航班信息 (注意这里字段名小写)
    netWeightD: Optional[float] = None           # 净重(kg)
    weightD: Optional[float] = None              # 总重(kg)
    container_nums: Optional[List[str]] = None   # 容器号列表


@dataclass
class TaxInfo:
    """税信息"""
    tax_pay_mode: Optional[str] = None      # 税费支付类型 'DDU', 'DDP'
    tax_type: Optional[str] = None      # 税号类型
    tax_no: Optional[float] = None           # 税号值
    tax_company: Optional[float] = None       # 注册公司
    tax_country: Optional[List[str]] = None   # 注册国家
    tax_address: Optional[List[str]] = None   # 注册地址


class LabelOrderVo():
    supplierAccount = ''
    supplier_butt =''
    customerOrder = ''
    scan_form_data = {}  # scan_form 数据集合
    customerOrders = ''  # 多订单 queryset
    orderLabelTask = ''
    parcelList: Union[Any, List[ParcelInfo]] = []
    parcel_related_name = ''  # 包裹的 related_name
    parcelItem_related_name = ''  # 包裹中商品的 related_name
    parcelItemList = []
    product = ''
    service = ''
    is_unpack = ''  # 是否拆单包
    track_task = ''
    weight = ''
    order_nums = []
    service_dict = {}  # 供应商对接字段
    containerInfo: ContainerInfo = None  # 容器信息
    tracking_nums: Optional[List[str]] = None  # 派送号列表
    customer_order_nums: Optional[List[str]] = None  # 客户单号列表
    airline_receive_data: AirlineReceiveData = None  # 航线接收数据
    is_single = False  # 是否单一订单
    tax_info: Optional[TaxInfo] = None  # 税信息


class IntegrationInterface(metaclass=abc.ABCMeta):
    # 定义接口Interface类来模仿接口的概念，python中没有interface关键字来定义一个接口。
    @abc.abstractmethod
    def create_order(self, label_order_vo):  # 定接口函数创建订单
        pass

    @abc.abstractmethod
    def get_label(self, label_order_vo):  # 定义接口函数获取面单
        pass

    @abc.abstractmethod
    def cancel_label(self, label_order_vo):  # 定义接口函数取消面单
        pass

    @abc.abstractmethod
    def get_order(self, label_order_vo):  # 定义接口函数获取订单信息
        pass

    @abc.abstractmethod
    def confirm_ship(self, label_order_vo):  # 定义接口函数确认发货
        pass

    @abc.abstractmethod
    def update_order(self, label_order_vo):  # 定义接口函数确认发货
        pass

    @abc.abstractmethod
    def scanform(self, label_order_vo):  # 定义接口函数确认发货
        """scanform 和 manifest"""
        pass

    def push_order_parcel(self, label_order_vo):
        """推送订单入库包裹信息"""
        pass

    def push_packet_assembly(self, label_order_vo):
        """推送组包信息"""
        pass

    def push_airline_receive(self, label_order_vo):
        """推送干线交航"""
        pass


@exception_handler
def create_order(object, label_order_vo):
    """
     归一化设计
    :param label_order_vo:
    :param object:
    :return:
    """
    object.create_order(label_order_vo)


def get_label(object, label_order_vo):
    """
    归一化设计
    :param label_order_vo:
    :param object:
    :return:
    """
    return object.get_label(label_order_vo)


# 二次获取面单
def get_secondary_label(object, label_order_vo):
    """
    归一化设计
    :param label_order_vo:
    :param object:
    :return:
    """
    object.get_secondary_label(label_order_vo)


def cancel_label(object, label_order_vo):
    """
    归一化设计
    :param label_order_vo:
    :param object:
    :return:
    """
    return object.cancel_label(label_order_vo)


def get_order(object, label_order_vo):
    """
    归一化设计
    :param label_order_vo:
    :param object:
    :return:
    """
    return object.get_order(label_order_vo)


def confirm_ship(object, label_order_vo):
    """
    归一化设计
    :param label_order_vo:
    :param object:
    :return:
    """
    return object.confirm_ship(label_order_vo)

def update_order(object, label_order_vo):
    """
    归一化设计
    :param label_order_vo:
    :param object:
    :return:
    """
    return object.update_order(label_order_vo)


def scanform(object, label_order_vo):
    """
    归一化设计
    :param label_order_vo:
    :param object:
    :return:
    """
    return object.scanform(label_order_vo)


def get_order_label_obj(order_num, customer_order=None, trackingNo=None):
    """
    获取面单对象
    :param order_num:
    :return:
    """
    if is_parcel_customer_order(order_num):
        if customer_order and trackingNo:
            # 判断是否存在
            label_queryset = ParcelOrderLabel.objects.filter(order_num=customer_order, tracking_no=trackingNo,
                                                             del_flag=False)
            if label_queryset.exists():
                order_label = label_queryset.first()
            else:
                order_label = ParcelOrderLabel()
        elif customer_order:
            # 一个订单对应一个面单，防止重复生成面单
            label_queryset = ParcelOrderLabel.objects.filter(order_num=customer_order, del_flag=False)
            if label_queryset.exists():
                order_label = label_queryset.first()
            else:
                order_label = ParcelOrderLabel()
        else:
            order_label = ParcelOrderLabel()

    elif str(order_num).startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_OUTBOUND_ORDER):
        order_label = OutboundOrderLabel()
    elif str(order_num).startswith(settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK):
        order_label = OrderLabel()
    elif str(order_num).startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_RETURN_ORDER):
        order_label = ReturnOrderLabel()
    else:
        order_label = OrderLabel()
    return order_label


def get_address_v2(customer_order, service=None):
    """获取地址V2版本"""
    # 境外退件地址
    return_info = None
    # 收件人地址信息
    recipient_info = None
    # 仓库地址（发件人地址）
    warehouse = None
    # 产品默认地址
    product_address = AddressRule.objects.filter(product=customer_order.product)

    # 资源默认地址
    service_address = ServiceAddressRule.objects.filter(service=service, del_flag=False)
    if service_address.exists():
        # 客户特定地址，默认取最后一个
        customer_address_objs = service_address.filter(customer=customer_order.customer, is_general=False,
                                                       del_flag=False)
        if customer_address_objs.exists():
            # 发件人
            customer_address_obj = customer_address_objs.filter(address_type='SD').first()
            if customer_address_obj:
                warehouse = customer_address_obj.address
            # 境外退件地址
            customer_address_obj = customer_address_objs.filter(address_type='OV').first()
            if customer_address_obj:
                return_info = customer_address_obj.address
        else:
            # 通用地址
            address_objs = service_address.filter(is_general=True, del_flag=False)
            # 发件人
            address_obj = address_objs.filter(address_type='SD').first()
            if address_obj:
                warehouse = address_obj.address
            # 境外退件地址
            address_obj = address_objs.filter(address_type='OV').first()
            if address_obj:
                return_info = address_obj.address

    elif product_address.exists():
        # 客户特定地址，默认取最后一个
        customer_address_objs = product_address.filter(customer=customer_order.customer, is_general=False,
                                                       del_flag=False)
        if customer_address_objs.exists():
            # 发件人
            customer_address_obj = customer_address_objs.filter(address_type='SD').first()
            if customer_address_obj:
                warehouse = customer_address_obj.address
            # 境外退件地址
            customer_address_obj = customer_address_objs.filter(address_type='OV').first()
            if customer_address_obj:
                return_info = customer_address_obj.address
        else:
            # 通用地址
            address_objs = product_address.filter(is_general=True, del_flag=False)
            # 发件人
            address_obj = address_objs.filter(address_type='SD').first()
            if address_obj:
                warehouse = address_obj.address
            # 境外退件地址
            address_obj = address_objs.filter(address_type='OV').first()
            if address_obj:
                return_info = address_obj.address

    else:
        warehouse = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='SP',
                                                      del_flag=False).first()
        return_info = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='OV',
                                                        del_flag=False).first()
        recipient_info = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='RC',
                                                           del_flag=False).first()
    # 没有设置默认值的取客户传参
    if not warehouse:
        warehouse = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='SP',
                                                      del_flag=False).first()
    if not return_info:
        return_info = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='OV',
                                                        del_flag=False).first()

    if not recipient_info:
        recipient_info = ParcelOrderAddress.objects.filter(customer_order=customer_order, address_type='RC',
                                                           del_flag=False).first()
    return warehouse, return_info, recipient_info


def get_warehouse_code(customer_order, service=None):
    """
    获取仓库地址
    :param customer_order:
    :return:
    """
    is_customer_order = True
    if is_parcel_customer_order(customer_order.order_num):
        if settings.SYSTEM_VERSION == 'V2':
            warehouse, return_info, recipient_info = get_address_v2(customer_order, service)
            address_num = warehouse.address_num
        elif customer_order.warehouse_code:
            warehouse = Address.objects.get(id=customer_order.warehouse_code.id, del_flag=False)
            address_num = warehouse.address_num
        else:
            warehouse = None
            address_num = None

        is_customer_order = False

    elif str(customer_order.order_num).startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_OUTBOUND_ORDER):
        warehouse = Address.objects.get(id=customer_order.warehouse_code.id, del_flag=False)
        address_num = warehouse.address_num
        is_customer_order = False

    elif str(customer_order.order_num).startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_RETURN_ORDER):
        warehouse = Address.objects.get(id=customer_order.warehouse_code.id, del_flag=False)
        address_num = warehouse.address_num
        is_customer_order = False
    else:
        address_num = customer_order.address_num
        warehouse = ''

    # 判断是否有仓库地址,如果有就用默认地址
    if service and service.extend_field:
        extend_field_json = json.loads(service.extend_field)
        if 'defaule_warehouse_address' in extend_field_json.keys() and extend_field_json['defaule_warehouse_address']:
            address_queryset = Address.objects.filter(address_num=extend_field_json['defaule_warehouse_address'],
                                                      del_flag=False, address_type='SP')
            if address_queryset.exists():
                warehouse = address_queryset.first()
                address_num = warehouse.address_num
    return address_num, is_customer_order, warehouse


@dataclass
class WarehouseCodeData:
    address_num: Any = None  # 地址编码
    is_customer_order: Any = None  # is_customer_order
    warehouse: Any = None  # 仓库（发件人）
    return_info: Any = None  # 境外退件地址
    recipient_info: Any = None  # 收件人地址


def get_warehouse_code_v2(customer_order, service=None):
    """
    获取仓库地址
    :param customer_order:
    :return:
    """
    is_customer_order = True
    # 境外退件地址
    return_info = None
    # 收件人地址信息
    recipient_info = None
    # 仓库地址（发件人地址）
    warehouse = None
    if is_parcel_customer_order(customer_order.order_num):
        if settings.SYSTEM_VERSION == 'V2':
            warehouse, return_info, recipient_info = get_address_v2(customer_order, service)
            address_num = warehouse.address_num
        elif customer_order.warehouse_code:
            warehouse = Address.objects.get(id=customer_order.warehouse_code.id, del_flag=False)
            address_num = warehouse.address_num
        else:
            warehouse = None
            address_num = None

        is_customer_order = False

    elif str(customer_order.order_num).startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_OUTBOUND_ORDER):
        warehouse = Address.objects.get(id=customer_order.warehouse_code.id, del_flag=False)
        address_num = warehouse.address_num
        is_customer_order = False

    elif str(customer_order.order_num).startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_RETURN_ORDER):
        warehouse = Address.objects.get(id=customer_order.warehouse_code.id, del_flag=False)
        address_num = warehouse.address_num
        is_customer_order = False
    else:
        address_num = customer_order.address_num
        warehouse = ''

    # 判断是否有仓库地址,如果有就用默认地址
    if service and service.extend_field:
        extend_field_json = json.loads(service.extend_field)
        if 'defaule_warehouse_address' in extend_field_json.keys() and extend_field_json['defaule_warehouse_address']:
            address_queryset = Address.objects.filter(address_num=extend_field_json['defaule_warehouse_address'],
                                                      del_flag=False, address_type='SP')
            if address_queryset.exists():
                warehouse = address_queryset.first()
                address_num = warehouse.address_num

    return WarehouseCodeData(
        address_num=address_num,
        is_customer_order=is_customer_order,
        warehouse=warehouse,
        return_info=return_info,
        recipient_info=recipient_info
    )


def get_charge_out_obj(order_num):
    """
    获取面单对象
    :param order_num:
    :return:
    """
    if is_parcel_customer_order(order_num):
        charge_out = ParcelOrderChargeOut
    elif str(order_num).startswith(settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK):
        charge_out = CustomerOrderChargeOut
    elif str(order_num).startswith(settings.SYSTEM_ORDER_MARK + settings.OMS_OUTBOUND_ORDER):
        charge_out = OutboundOrderChargeOut
    else:
        charge_out = ''
    return charge_out


def check_item(parcelItemList):
    check_result = []
    for parcelItem in parcelItemList:
        # if not parcelItem.declared_nameCN:
        #     check_result.append('商品中文名称不能为空')
        # if not parcelItem.declared_nameEN:
        #     check_result.append('商品英文名称不能为空')
        print(parcelItem.item_length)
        if not parcelItem.item_length:
            check_result.append('商品长不能为空')
        if not parcelItem.item_width:
            check_result.append('商品宽不能为空')
        print(parcelItem.item_weight)
        if not parcelItem.item_height:
            check_result.append('商品高不能为空')
        if not parcelItem.item_weight:
            check_result.append('商品重量不能为空')
        if not parcelItem.item_volume:
            check_result.append('商品体积不能为空')
        if not parcelItem.item_qty:
            check_result.append('商品数量不能为空')
        if len(check_result) > 0:
            break

    return check_result


def check_parcel_list(parcelList):
    check_result = []
    for parcel in parcelList:
        if not parcel.parcel_num:
            check_result.append('包裹号不能为空')
        if not parcel.parcel_length:
            check_result.append('包裹长不能为空')
        if not parcel.parcel_width:
            check_result.append('包裹宽不能为空')
        if not parcel.parcel_height:
            check_result.append('包裹高不能为空')
        if not parcel.parcel_volume:
            check_result.append('包裹体积不能为空')

        if len(check_result) > 0:
            break

    return check_result


# 商品默认值设置
def set_parcel_item_default_value(parcel_item_list, product):
    field_names = ['item_weight']
    set_default_value(field_names, parcel_item_list, product)


# 包裹默认值设置
def set_parcel_default_value(parcel_list, product):
    field_names = ['parcel_length', 'parcel_width', 'parcel_height', 'parcel_weight', 'declared_currency']
    set_default_value(field_names, parcel_list, product)

# 小包订单默认值设置
def set_parcel_customer_order_default_value(parcel_customer_order, product):
    field_names = ['ioss_num']
    # 复用 set_default_value 函数， 注意传值传入 小包单对象
    parcel_customer_order_li = [parcel_customer_order]  # 为了复用， 传入可迭代对象
    set_default_value(field_names, parcel_customer_order_li, product)


def set_default_value(field_names, parcel_list, product):
    for field_name in field_names:
        # 查询商品是否有默认值
        default_value = None
        parcel_default = ProductBasicRestriction.objects.filter(product=product, encoding=field_name,
                                                                del_flag=False).first()
        if parcel_default:
            default_value = parcel_default.default_value
            if default_value:
                for parcel_item in parcel_list:
                    setattr(parcel_item, field_name, default_value)


def add_fail_label_task(order_label_task, label_desc):
    logger.error(label_desc)
    order_label_task.status = 'Failure'  # 添加状态更新
    order_label_task.label_desc = label_desc
    order_label_task.handle_times += 1
    order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    order_label_task.save()


def add_fail_sync_task(order_label_task, task_desc):
    logger.error(task_desc)
    order_label_task.status = 'Failure'
    order_label_task.task_desc = task_desc
    order_label_task.handle_times += 1
    order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    order_label_task.save()


# 获取单号
def get_order_num(customer_order, is_customer_order, product):
    logger.info(f'get_order_num--->{customer_order.order_num}')
    if product.push_order_type == 'S':
        return customer_order.order_num
    elif is_customer_order:
        return customer_order.ref_num or customer_order.order_num
    else:
        return customer_order.customer_order_num or customer_order.order_num


def get_label_order_vo(customer_order, label_order_vo, service):
    product = label_order_vo.product
    # 供应商账户
    supplier_account = label_order_vo.supplierAccount
    # 仓库
    address_num, is_customer_order, warehouse = get_warehouse_code(customer_order, service)
    # 服务码
    service_code = service.code
    # 订单号
    parcel_order_num = get_order_num(customer_order, is_customer_order, product)
    return is_customer_order, parcel_order_num, service_code, supplier_account, warehouse


@dataclass
class LabelOrderVOData:
    product: Any = None  # 产品
    supplier_account: Any = None  # 供应商账户
    address_num: Any = None  # 地址编码
    is_customer_order: Any = None  # is_customer_order
    warehouse: Any = None  # 仓库（发件人）
    return_info: Any = None  # 境外退件地址
    recipient_info: Any = None  # 收件人地址
    service_code: Any = None  # 服务码
    parcel_order_num: Any = None  # 订单号


def get_label_order_vo_v2(customer_order, label_order_vo, service):
    product = label_order_vo.product
    # 供应商账户
    supplier_account = label_order_vo.supplierAccount
    # 仓库
    # address_num, is_customer_order, warehouse, return_info, recipient_info
    warehouse_code_obj = get_warehouse_code_v2(customer_order, service)
    # 服务码
    service_code = service.code
    # 订单号
    parcel_order_num = get_order_num(customer_order, warehouse_code_obj.is_customer_order, product)

    return LabelOrderVOData(
        product=product,
        supplier_account=supplier_account,
        address_num=warehouse_code_obj.address_num,
        is_customer_order=warehouse_code_obj.is_customer_order,
        warehouse=warehouse_code_obj.warehouse,
        return_info=warehouse_code_obj.return_info,
        recipient_info=warehouse_code_obj.recipient_info,
        service_code=service_code,
        parcel_order_num=parcel_order_num,
    )


@dataclass
class StandardAddress:
    """标准地址参数"""
    # last_name: Optional[str] = None # 姓
    # first_name: Optional[str] = None # 名
    contact_name: Optional[str] = None  # 联系人
    company_name: Optional[str] = None  # 公司名
    country_code: Optional[str] = None  # 国家编码
    state_code: Optional[str] = None  # 省/州编码
    city_code: Optional[str] = None  # 城市编码
    region_code: Optional[str] = None  # 区
    street: Optional[str] = None  # 街道
    address_one: Optional[str] = None  # 地址行1
    address_two: Optional[str] = None  # 地址行2
    house_no: Optional[str] = None  # 门牌号
    postcode: Optional[str] = None  # 邮编
    contact_email: Optional[str] = None  # 邮箱
    contact_phone: Optional[str] = None  # 电话
    contact_mobile: Optional[str] = None  # 手机号
    id_type: Optional[str] = None  # 证件类型
    id_number: Optional[str] = None  # 证件号码


def convert_address(address_obj):
    """转换地址"""
    if not address_obj:
        return address_obj
    # 旧版订单上的地址转换为标准地址
    if (hasattr(address_obj, 'buyer_name') or hasattr(address_obj, 'buyer_country_code') or
            hasattr(address_obj, 'buyer_postcode')):
        standard_address = StandardAddress()
        standard_address.contact_name = address_obj.buyer_name
        standard_address.country_code = address_obj.buyer_country_code
        standard_address.state_code = address_obj.buyer_state
        standard_address.city_code = address_obj.buyer_city_code
        standard_address.address_one = address_obj.buyer_address_one
        standard_address.address_two = address_obj.buyer_address_two
        standard_address.postcode = address_obj.buyer_postcode
        standard_address.house_no = address_obj.buyer_house_num
        standard_address.contact_phone = address_obj.buyer_phone
        standard_address.contact_email = address_obj.buyer_mail
    else:
        # 新版地址转换为标准地址
        standard_address = StandardAddress(
            contact_name=address_obj.contact_name,
            company_name=address_obj.company_name,
            country_code=address_obj.country_code,
            state_code=address_obj.state_code,
            city_code=address_obj.city_code,
            region_code=address_obj.region_code,
            street=address_obj.street,
            address_one=address_obj.address_one,
            address_two=address_obj.address_two,
            house_no=address_obj.house_no,
            postcode=address_obj.postcode,
            contact_email=address_obj.contact_email,
            contact_phone=address_obj.contact_phone,
            contact_mobile=address_obj.contact_mobile,
            id_type=address_obj.id_type,
            id_number=address_obj.id_number
        )

    return standard_address
