'''
中达对接
'''
import base64
from datetime import datetime
import os
from decimal import Decimal

import requests
from django.forms import model_to_dict

from alita.logger import logger
from common.service.product_attribute_limit import get_productBasicRestriction
from common.utils.file_util import upload_file
from common.tools import change_label_common_func
from order.integration.integrationInterface import IntegrationInterface, get_order_label_obj, \
    get_order_num, get_label_order_vo

from alita.settings.base import MEDIA_URL
from order.integration.util.an_neng_util import Order, do_post

from order.models import ParcelOrderExtend


def get_call(customer_order, label_order_vo, service, is_unpack):
    """
    下单
    :param customer_order:
    :param label_order_vo:
    :param service:
    :return:
    """

    is_customer_order, parcel_order_num, service_code, supplier_account, warehouse = \
        get_label_order_vo(customer_order, label_order_vo, service)

    product = label_order_vo.product

    order = Order()
    order.ApiKey = supplier_account.auth_pwd
    order.OrderId = parcel_order_num
    order.SelfConfirm = 'Y' if service.is_confirm else 'N'

    if customer_order.ship_date:
        order.ShipDate= customer_order.ship_date.strftime('%Y-%m-%d')

    # 面单是否去logo
    order.removeShowJdwin = 'Y' if get_productBasicRestriction(product, 'is_label_remove_logo') else 'N'

    # 是否运输单
    if is_customer_order:
        order.FromAddressId = customer_order.address_num
    # 没仓库地址，直接传地址的
    elif not warehouse:
        order.FromAddressId = customer_order.address_num
    else:
        # 有仓库地址
        order.FromAddressId = warehouse.address_num

    parcel_info_list = label_order_vo.parcelList
    parcel_item_list = label_order_vo.parcelItemList

    parcel_info = parcel_info_list[0]
    parcel_item = parcel_item_list[0]

    order.ProductName = parcel_item.declared_nameEN
    order.Sku = parcel_item.item_code or ''
    # 克
    if is_customer_order:
        order.Weight = int(Decimal(str(parcel_info.label_weight)) * Decimal(1000))
    else:
        order.Weight = int(Decimal(str(parcel_info.label_weight or parcel_info.parcel_weight)) * Decimal(1000))

    item_weight = Decimal(str(0)) 
    for item in parcel_item_list:
        if item.item_weight:
            # 我们系统是kg，供应商是g
            item_weight += Decimal(str(item.item_weight)) * Decimal(1000)
    # item_weight = int(item_weight)
    # if order.Weight < item_weight:
    #     order.Weight = item_weight

    # Usps的邮寄类型，可选值：First 、 Priority
    order.MailClass = service_code
    order.ToName = customer_order.buyer_name
    # order.ToCompany = 'ly'
    order.ToAddress1 = customer_order.buyer_address_one
    order.ToAddress2 = customer_order.buyer_address_two or ''
    order.ToCity = customer_order.buyer_city_code
    order.ToStateCode = customer_order.buyer_state or ''
    order.ToPostalCode = customer_order.buyer_postcode
    order.ToPhone = customer_order.buyer_phone or ''
    order.ToEmail = customer_order.buyer_mail or ''
    # cm 转inch
    order.Length = int(Decimal(str(parcel_info.parcel_length)) * Decimal('0.39'))
    order.Width = int(Decimal(str(parcel_info.parcel_width)) * Decimal('0.39'))
    order.Height = int(Decimal(str(parcel_info.parcel_height)) * Decimal('0.39'))
    # order.Length = int(parcel_info.parcel_length)
    # order.Width = int(parcel_info.parcel_width)
    # order.Height = int(parcel_info.parcel_height)

    order.MailpieceShape = ''
    order.DealStrategy = '1'
    order.ChannelCode = ''
    # order.PushManifest = 'Y' if service.is_manifest else 'N'
    order.PushManifest = 'G' if get_productBasicRestriction(product, 'blocking_manifest') else (
        'Y' if service.is_manifest else 'N')
    
    order_extend = ParcelOrderExtend.objects.filter(customer_order=customer_order).last()
    if order_extend:
        if order_extend.is_signature:
            order.Signature = 'N'
        else:
            order.Signature ='Y'
    else:
        order.Signature =''

    data = order.__dict__

    url = supplier_account.url + '/api/UspsDom/Create'
    result = do_post(data, url, customer_order.order_num)

    return result, order.OrderId


class AnNengIntegrationService(IntegrationInterface):
    """
    安能接口
    """

    def create_order(self, labelOrderVo):
        """
        创建订单
        :param labelOrderVo:
        """
        logger.info("安能 create_order")
        service = labelOrderVo.service
        product = labelOrderVo.product
        customer_order = labelOrderVo.customerOrder
        order_label_task = labelOrderVo.orderLabelTask
        is_unpack = labelOrderVo.is_unpack
        result, orderId = get_call(customer_order, labelOrderVo, service, is_unpack)
        if 'errors' in result.keys() and result['errors']:
            save_fail_result(order_label_task, '下单失败:' + str(result['errors']), 121)
        elif int(result['Code']) == 0:
            order_label_task.status = 'HandledBy3rdNo'
            order_label_task.label_desc = '等待抓取面单'
            order_label_task.third_order_no = orderId
            order_label_task.update_date = datetime.now()
            if get_productBasicRestriction(product, 'blocking_manifest_task'):
                order_label_task.is_need_confirm = True
            order_label_task.save()
        else:
            if result['Msg']:
                save_fail_result(order_label_task, '下单失败:' + result['Msg'], 121)
            else:
                save_fail_result(order_label_task, '下单失败:无返回结果')

    def get_label(self, label_order_vo):
        """
        获取面单
        :param label_order_vo:
        """
        logger.info("安能 get_label")

        order_label_task = label_order_vo.orderLabelTask
        service = label_order_vo.service
        product = label_order_vo.product

        supplier_account = label_order_vo.supplierAccount
        customer_order = label_order_vo.customerOrder

        data = {
            'OrderId': order_label_task.third_order_no,
            'ApiKey': supplier_account.auth_pwd,
        }

        url = supplier_account.url + '/api/UspsDom/GetLabel'
        result = do_post(data, url, customer_order.order_num)

        if int(result['Code']) != 0:
            save_fail_result(order_label_task, '获取面单失败:' + result['Msg'], 121)
            return

        result_data = result['Data']
        if not result_data:
            save_fail_result(order_label_task, '获取面单失败:无返回结果')
            return

        mainTrackingNo = result_data['TrackingNumber']
        if mainTrackingNo == '':
            save_fail_result(order_label_task, '获取面单失败:无跟踪号')
            return

        trackingNo = mainTrackingNo
        label_data = result_data['Label']

        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + customer_order.order_num + ".pdf"

        upload_file(label_url, label_data, 'base64')
        # decoded_file_name = MEDIA_URL + label_url
        # dir = os.path.dirname(decoded_file_name)
        # if not os.path.exists(dir):
        #     os.makedirs(dir)
        # with open(decoded_file_name, "wb") as code:
        #     code.write(base64.b64decode(label_data))

        order_num = customer_order.order_num
        order_label = get_order_label_obj(order_num)
        order_label.order_num = customer_order
        order_label.tracking_no = trackingNo
        order_label.third_order_no = order_label_task.third_order_no
        order_label.label_url = label_url
        order_label.create_by = order_label_task.create_by
        order_label.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label.product = order_label_task.product
        order_label.save()

        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'

        order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label_task.save()

        customer_order.tracking_num = mainTrackingNo
        customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        customer_order.save(update_fields=['tracking_num','update_date'])


        # 面单更改逻辑
        order_label_changed = get_order_label_obj(order_num)
        try:
            change_label_common_func(service, product, customer_order, order_label,
                                                        customer_order.order_num, order_label_changed, order_label_task,
                                                        log_flag='anneng')
        except Exception as e:
            order_label_task.status = 'UnHandled'
            order_label_task.label_desc = '更改面单失败：' + str(e)
            order_label_task.handle_times = 121
            order_label_task.update_date = datetime.now()
            order_label_task.save()


    def cancel_label(self, label_order_vo):
        """
        取消面单
        :param label_order_vo:
        """
        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount

        data = {
            'OrderId': order_label_task.third_order_no,
            'ApiKey': supplier_account.auth_pwd
        }

        url = supplier_account.url + '/api/UspsDom/CancelLabel'
        result = do_post(data, url)
        logger.info(f'安能取消面单,url={url},data={data},result={result}')
        result_data = {}
        code = result.get('Code',99)
        if int(code) == 0:
            result_data['code'] = '0'
        else:
            result_data['code'] = '400'
            result_data['msg'] = result.get('Msg','')

        return result_data

    def get_order(self, label_order_vo):
        pass

    def confirm_ship(self, label_order_vo):

        customer_order = label_order_vo.customerOrder
        logger.info(f"安能 confirm_ship start order_num:{customer_order.order_num}")
        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount

        data = {
            'OrderId': order_label_task.third_order_no,
            'ApiKey': supplier_account.auth_pwd
        }

        url = supplier_account.url + '/api/UspsDom/ConfirmLabel'
        result = do_post(data, url)
        result_data = {}
        if int(result['Code']) == 0:
            order_label_task.status = 'ConfirmLabel'
            order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            order_label_task.save()

            customer_order.is_confirm_ship=True
            customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            customer_order.save()
            customer_order.save(update_fields=['is_confirm_ship','update_date'])

            result_data['code'] = '0'
            result_data['msg'] = 'success'
        else:
            save_fail_result(order_label_task, str(result['Msg']))
            result_data['code'] = '400'
            result_data['msg'] = str(result['Msg'])

        logger.info(f"安能 confirm_ship end order_num: {customer_order.order_num}")
        return result_data


    def get_secondary_label(self, label_order_vo):
        '''
        二次获取面单
        '''
        pass

    def scanform(self, label_order_vo):

        supplier_account = label_order_vo.supplierAccount
        customer_order = label_order_vo.customerOrder

        data = {
            'apiKey': supplier_account.auth_pwd,
            'trackingNumbers': label_order_vo.order_nums,
            # 'fromName': '',
            # 'fromCompany': '',
            # 'fromAddress': '',
            # 'fromCity': '',
            # 'fromState': '',
            # 'fromZip': '',
            'fromAddressId': customer_order.warehouse_code.address_num,
            # 'submissionID': ''
        }

        url = f'{supplier_account.url}/api/UspsDom/ScanForm'
        result = do_post(data, url)
        logger.info(f'中达（安能AnNeng） scanform　- order {customer_order.order_num} - 入参：{data}, 供应商返回结果:{result}')
        result_data = {}
        if int(result['Code']) == 0:
            result_data['code'] = 0
            data = {
                'file_base64': result['Data']['FileBase64'],
                'scanform_num': result['Data']['ScanFormNumber'],
            }
            result_data['data'] = data
        elif int(result['Code']) == 100:
            result_data['code'] = 0
            result_data['msg'] = result['Msg']
            data = {
                'file_base64': result['Data']['FileBase64'],
                'scanform_num': result['Data']['ScanFormNumber'],
                'FailedTrackNumbers': result['Data'].get('FailedTrackNumbers', []),
                'Message': result['Data'].get('Message', ''),
            }
            result_data['data'] = data
        else:
            result_data['code'] = '400'
            result_data['msg'] = result['Msg']

        return result_data


    def update_order(self, label_order_vo):
        pass


def save_fail_result(order_label_task, result, handle_times=1):
    order_label_task.label_desc = result
    order_label_task.handle_times += handle_times
    order_label_task.update_date = datetime.now()
    order_label_task.save()
