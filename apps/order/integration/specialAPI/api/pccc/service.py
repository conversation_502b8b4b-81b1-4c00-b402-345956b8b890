from alita.logger import logger

from order.integration.util.hanJinUtil import checkPccc

from pms.models import ServiceApiSetting, Service
from company.models import SupplierButt


def check_pccc(pccc, name, phone):
    """获取pccc校验结果"""

    butt_code = SupplierButt.objects.filter(class_name='hanJinIntegrationService', del_flag=False).first()

    service = Service.objects.filter(butt_code=butt_code, del_flag=False).first()

    api_setting_queryset = ServiceApiSetting.objects.filter(service=service, del_flag=False)

    settings_dict = {item.field_name: item.field_value for item in api_setting_queryset}

    # 身份验证
    auth_id = settings_dict.get('auth_id')  # 授权id
    auth_pwd = settings_dict.get('auth_pwd')  # 授权码
    url = settings_dict.get('url')
    # auth_id = 'SHENZHEN'
    # auth_pwd = '25A0BAC68D3F4247A73EFDEA35599BD8A0D7145B'
    # url = 'https://api-gateway.hanjin.co.kr:4443'

    req_data = {
        "clientId": auth_id,
        "persEcm": pccc,
        "pltxNm": name,
        "cralTelno": phone
    }
    logger.info(f'hanjin get_check_pccc data: {req_data}')
    res = checkPccc(auth_id, url, 'POST', auth_pwd, req_data)
    logger.info(f'hanjin get_check_pccc result: {res}')

    if res.get("tCnt") == "1":
        data = {
            'code': 200,
            'msg': 'success',
        }
    else:
        data = {
            'code': 400,
            'msg': res.get('ntceInfo')
        }
    return data