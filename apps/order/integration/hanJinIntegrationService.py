import json
import math
import traceback
from datetime import datetime
from django.conf import settings
from django.db import transaction

from common.error import ParamError, ErrorCode
from alita.logger import logger
from common.service.track_service import add_track_task
from common.tools import set_multistep_parcel_track, customer_order_restrict
from common.utils.file_util import upload_file
from common.utils.barcode_gen import create_barcodes_for_order_hanjin
from common.utils.logger_util import LogFormat, RequestStatusEnum, alita_logger_info, StreamEnum
from company.models import TrackSupplier
from order.integration.integrationInterface import IntegrationInterface, get_order_label_obj, get_label_order_vo, \
    get_label_order_vo_v2
from order.models import HanJinWayBillNumber, LocalSortingCode

from order.integration.util.hanJinUtil import getAddressRequestDto, hub_terminal_print, request_manifest, checkPccc, \
    request_confirm_ship

if hasattr(settings, 'TEST_ENV') and settings.TEST_ENV:
    ENV = 'dev'
else:
    ENV = 'prod'


def build_address(address_info):
    address_parts = [
        address_info.house_no,
        address_info.address_two,
        address_info.address_one,
        address_info.street,
        address_info.region_code,
        address_info.city_code,
        address_info.state_code,
        address_info.postcode,
        address_info.country_code
    ]
    return ', '.join(part for part in address_parts if part)


def combination_create_label_data(customer_order, label_order_vo, service, is_unpack, order_label_task):
    """
    组合面单信息
    :param customer_order:
    :param label_order_vo:
    :param service:
    :param is_unpack:
    :return:
    """
    create_data = combination_create_data(customer_order, label_order_vo, service, is_unpack, order_label_task)
    logger.info(f'combination_create_label_data: {create_data}')

    if not create_data:
        raise ParamError('没有运单号！', ErrorCode.PARAM_ERROR)

    origin_dest = create_data['fromcountry'] + ' ' + create_data['originCode'] + ' - ' + 'KR ' + create_data[
        'destinationCode']

    recv_name = create_data['hblList'][0]['receiverInfo']['receiverName']
    recv_tel = create_data['hblList'][0]['receiverInfo']['receiverCelNumber']
    recv_address = create_data['hblList'][0]['receiverInfo']['receiverAddress1']
    sender_name = create_data['hblList'][0]['shipperName']
    sender_tel = create_data['hblList'][0]['shipperTelNumber']
    sender_address = create_data['hblList'][0]['shipperAddress1']
    parcel_order_num = create_data['hblList'][0]['orderNumber']
    hsLi = create_data['hblList'][0]['itemList']
    parcel_info = ''
    for item in hsLi:
        item_info = str(item['goodsName1']) + '*' + str(item['goodsCount']) + ' '
        parcel_info += item_info
    item_info = parcel_info
    current_date = datetime.now().strftime("%Y-%m-%d")
    item_weight = create_data['hblList'][0]['grossWeight']
    weight_unit = create_data['hblList'][0]['weightUnit']
    weight_info = '1, ' + str(round(float(item_weight), 2)) + weight_unit
    way_bill_num = create_data['hblList'][0]['waybillNumber']
    format_num = "-".join([way_bill_num[i:i + 4] for i in range(0, len(way_bill_num), 4)])
    label_data = {
        'origin_dest': origin_dest,
        'recv_name': recv_name,
        'recv_tel': recv_tel,
        'recv_address': recv_address,
        'sender_name': sender_name,
        'sender_tel': sender_tel,
        'sender_address': sender_address,
        'parcel_order_num': parcel_order_num,
        'item_info': item_info,
        'current_date': current_date,
        'weight_info': weight_info,
        'way_bill_num': format_num,
        'way_bill_num_barcode': way_bill_num
    }
    return label_data


def combination_create_data(customer_order, label_order_vo, service, is_unpack, order_label_task):
    """
    组合下单信息
    :param customer_order:
    :param label_order_vo:
    :param service:
    :param is_unpack:
    :return:
    """

    label_order_vo_data = get_label_order_vo_v2(customer_order, label_order_vo, service)

    is_customer_order = label_order_vo_data.is_customer_order
    parcel_order_num = label_order_vo_data.parcel_order_num
    service_code = label_order_vo_data.service_code
    supplier_account = label_order_vo_data.supplier_account
    warehouse = label_order_vo_data.warehouse
    return_info = label_order_vo_data.return_info
    recipient_info = label_order_vo_data.recipient_info

    product = label_order_vo.product
    parcel_info_list = label_order_vo.parcelList
    parcel_item_list = label_order_vo.parcelItemList

    service_dict = label_order_vo.service_dict

    # 身份验证
    auth_id = service_dict.get('auth_id')  # 授权id
    auth_pwd = service_dict.get('auth_pwd')  # 授权码
    url = service_dict.get('url')
    dest_three_code = service_dict.get('toAirport_Code')
    origin_three_code = service_dict.get('fromAirport_Code')
    fromcountry = service_dict.get('fromcountry')

    logger.info(
        f'dest_three_code: {dest_three_code}; origin_three_code: {origin_three_code}, fromcountry: {fromcountry}')
    # auth_id = 'SHENZHEN'
    # auth_pwd = '25A0BAC68D3F4247A73EFDEA35599BD8A0D7145B'
    # url = 'https://api-gateway.hanjin.co.kr:4443'

    create_data = {
        'clientId': auth_id,
        'originCode': origin_three_code,
        'destinationCode': dest_three_code,
        'numberOfPackages': 1,
        "fromcountry": fromcountry
    }

    # 时间格式 YYYYMMDD
    current_date = datetime.now().strftime('%Y%m%d')

    # 时间格式 YYYYMMDDHH24MISS
    current_datetime = datetime.now().strftime('%Y%m%d%H%M%S')

    if not recipient_info:
        save_fail_result(order_label_task, f'生成面单失败: 收件人地址未填写,请核实')
        return

    if not return_info:
        save_fail_result(order_label_task, f'生成面单失败: 退件地址未填写,请核实')
        return

    with transaction.atomic():
        # 跟踪单号
        way_bill = HanJinWayBillNumber.objects.select_for_update(skip_locked=True).filter(del_flag=False).order_by('id').first()
        if way_bill:
            # 使用这个记录后，将其标记为逻辑删除
            tracking_num = way_bill.way_bill_num
            way_bill.del_flag = True
            way_bill.save()
        else:
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                func_name='create_order',
                status=RequestStatusEnum.FAILED.value,
                order_num=customer_order.order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                remark='hanjin waybillNumber 没有运单号！',
            ))
            logger.error('hanjin waybillNumber 没有运单号！')
            return

    # 发件人地址
    # 地址拼接顺序: 门牌号>地址2>地址1>街道>区>城市>省份>邮编>国家
    sender_address = build_address(return_info)

    # 收件人信息
    # 地址拼接顺序: 门牌号>地址2>地址1>街道>区>城市>省份>邮编>国家
    recv_address = build_address(recipient_info)

    shipper_data = {
        'orderNumber': parcel_order_num,
        'orderDate': current_date,
        'waybillNumber': tracking_num,
        'shipperZipcode': return_info.postcode,
        'shipperAddress1': sender_address,
        # shipperAddress1: Street xxx, Nanshan District, Shenzhen, Guangdong, China.
        'shipperAddress2': return_info.address_two,  # shipperAddress2: Room 501, Building xxx
        'shipperNationCode': 'CN',
        'ediCustomerCode': '0001768',
        'shipperName': return_info.contact_name,
        'shipperTelNumber': return_info.contact_phone,
        'shipperEmail': return_info.contact_email,
        'payCondition': 'CD',
        'packageYN': 'N',
        'boxQuantity': 1,
        'weightUnit': 'KG',
        'invoiceValuesUnit': 'USD',
        'clearanceType': '1',
        'transactionType': 'A',
        'usageType': '1',
    }

    receiver_info = {
        'receiverZipcode': recipient_info.postcode,
        'receiverAddress1': recv_address,
        'receiverAddress2': recipient_info.address_two,
        'receiverNationCode': 'KR',
        'receiverName': recipient_info.contact_name,
        'receiverName2': recipient_info.contact_name,
        'receiverCelNumber': recipient_info.contact_phone,
        # 'receiverCodeType': '1',
        # 'receiverCode': 'P' + str(buyer_tax)
    }
    if recipient_info.id_type == 'PCCC':
        receiver_info['receiverCodeType'] = '1'
        receiver_info['receiverCode'] = recipient_info.id_number
        # ret = get_check_pccc('check_pccc', recipient_info.id_number, recipient_info.contact_name,
        #                      recipient_info.contact_phone)
        # if ret.get("tCnt") != "1":
        #     """pccc校验失败"""
        #     logger.info('pccc校验失败！')
        #     raise ParamError('pccc校验失败', ErrorCode.PARAM_ERROR)

    parcel_info = parcel_info_list[0]  # 一条数据，一个包裹
    contents = []
    order_value_total = 0  # 总价值
    total_weight = 0  # 总重量
    item_goods_li = []
    item_num = 0
    # 总重从包裹拿，仓库可能会重新称重
    for parcel in parcel_info_list:
        total_weight += float(parcel.parcel_weight)

    for parcelItem in parcel_item_list:
        if parcelItem.parcel_num == parcel_info:
            item_value = float(parcelItem.declared_price * parcelItem.item_qty)
            # item_weight = float(parcelItem.item_weight) * parcelItem.item_qty
            item_goods_li.append((float(parcelItem.declared_price), parcelItem.declared_nameEN))
            item_num += 1
            item_dict = {
                'goodsSerialNumber': item_num,
                'goodsName1': parcelItem.declared_nameEN,
                'goodsCount': parcelItem.item_qty,
                'goodsWeight': round(float(parcelItem.item_weight), 2),
                'goodsWeightUnit': parcelItem.weight_unit,
                'goodsUnitPrice': float(parcelItem.declared_price),
                'unitPriceCurrency': 'USD',
                'hsCode': parcelItem.customs_code,  # 海关编码
                'sendDate': current_datetime,
                'goodsIMGURL': parcelItem.sku_url  # 商品SKU url
            }
            order_value_total += item_value
            # total_weight += item_weight
            contents.append(item_dict)

    max_tuple = max(item_goods_li, key=lambda x: x[0])
    extra_data = {
        'shoppingMallUrl': parcel_item_list[0].sku_url,  # 销售链接
        'goodsName': str(max_tuple[1]),
        'grossWeight': total_weight,
        # "weightUnit": "KG",  # 之前没传单位但是能成功
        'invoiceValues': round(order_value_total, 2),
    }

    shipper_data['receiverInfo'] = receiver_info
    shipper_data.update(extra_data)
    shipper_data['itemList'] = contents
    create_data['hblList'] = [shipper_data]
    return create_data


def manifest_receipt_dto(customer_orders, parcel_related_name, parcel_item_related_name,
                         service_dict, scan_form_data, label_order_vo, service):
    """拼接manifest参数"""

    hblList = []
    for customer_order in customer_orders:
        hblList.append(house_bill_of_landing_dto(
            customer_order, parcel_related_name, parcel_item_related_name, service_dict,
            label_order_vo, service
        ))

    flight_info = scan_form_data.get('flightInfo')
    if flight_info:
        create_data = {
            'clientId': service_dict.get('auth_id'),
            'mblNumber': flight_info.mawbNo,  # 主运单号。若没有主运单号，请使用序列号
            'originCode': flight_info.fromPortCode or service_dict.get('fromAirport_Code'),  # 始发地 3 位代码
            'destinationCode': flight_info.toPortCode or service_dict.get('toAirport_Code'),  # 目的地 3 位代码
            'flightNumber': flight_info.flightNo,  # 航班号
            'fltDepDate': flight_info.etd,  # 航班出发日期（YYYYMMDDHH24MI 格式）
            'fltArrDate': flight_info.eta,  # 航班到达日期（YYYYMMDDHH24MI 格式）
            'hblList': hblList,  # 分运单（HouseBillOfLandingDto）列表
        }
    else:
        create_data = {
            'clientId': service_dict.get('auth_id'),
            'originCode': service_dict.get('fromAirport_Code'),  # 始发地 3 位代码
            'destinationCode': service_dict.get('toAirport_Code'),  # 目的地 3 位代码
            'hblList': hblList,  # 分运单（HouseBillOfLandingDto）列表
        }

    return create_data


def house_bill_of_landing_dto(customer_order, parcel_related_name, parcel_item_related_name, service_dict,
                              label_order_vo, service):
    """
    拼接 manifest 接口 hblList
    """
    label_order_vo_data = get_label_order_vo_v2(customer_order, label_order_vo, service)
    is_customer_order = label_order_vo_data.is_customer_order
    parcel_order_num = label_order_vo_data.parcel_order_num
    service_code = label_order_vo_data.service_code
    supplier_account = label_order_vo_data.supplier_account
    warehouse = label_order_vo_data.warehouse
    return_info = label_order_vo_data.return_info
    recipient_info = label_order_vo_data.recipient_info

    parcel_objs = getattr(customer_order, parcel_related_name).filter(del_flag=False)
    parcel_item_list = getattr(parcel_objs.first(), parcel_item_related_name).filter(del_flag=False).all()

    # 时间格式 YYYYMMDD
    current_date = datetime.now().strftime('%Y%m%d')

    # 时间格式 YYYYMMDDHH24MISS
    current_datetime = datetime.now().strftime('%Y%m%d%H%M%S')

    country_str = warehouse.country_code
    state_str = warehouse.state_code
    city_str = warehouse.city_code
    addr1 = warehouse.address_one

    shipper_data = {
        'orderNumber': parcel_order_num,
        'orderDate': current_date,
        'waybillNumber': customer_order.tracking_num,
        'shipperZipcode': warehouse.postcode,
        # 'shipperAddress1': addr1 + ', ' + city_str + ', ' + state_str + ', ' + country_str,
        # shipperAddress1: Street xxx, Nanshan District, Shenzhen, Guangdong, China.
        # 'shipperAddress2': warehouse.address_two,  # shipperAddress2: Room 501, Building xxx
        'shipperAddress1': f'{addr1}{warehouse.address_two}',
        'shipperAddress2': addr1 + ', ' + city_str + ', ' + state_str + ', ' + country_str,
        # 'shipperAddress3': city_str,
        # 'shipperAddress4': state_str,
        # 'shipperAddress5': f'{addr1}{recipient_info.address_two}',
        # 'shipperAddress6': addr1 + ', ' + city_str + ', ' + state_str + ', ' + country_str,
        # 'shipperAddress7': city_str,
        # 'shipperAddress8': state_str,
        'shipperNationCode': 'CN',
        'ediCustomerCode': service_dict.get('ediCustomerCode', ''),
        'shipperName': warehouse.contact_name,
        'shipperTelNumber': warehouse.contact_phone,
        'shipperEmail': warehouse.contact_email,
        'payCondition': 'CD',
        'packageYN': 'N',
        'boxQuantity': 1,
        'weightUnit': 'KG',
        'invoiceValuesUnit': 'USD',
        'clearanceType': '1',
        'transactionType': 'A',
        'usageType': '1',
    }

    rec_country_str = recipient_info.country_code
    rec_state_str = recipient_info.state_code
    rec_city_str = recipient_info.city_code
    rec_addr1 = recipient_info.address_one
    receiver_info = {
        'receiverZipcode': recipient_info.postcode,
        'receiverAddress1': f'{rec_addr1}{recipient_info.address_two}',
        'receiverAddress2': rec_addr1 + ', ' + rec_city_str + ', ' + rec_state_str + ', ' + rec_country_str,
        # 'receiverAddress3': rec_city_str,
        # 'receiverAddress4': rec_state_str,
        'receiverAddress5': f'{rec_addr1}{recipient_info.address_two}',
        'receiverAddress6': rec_addr1 + ', ' + rec_city_str + ', ' + rec_state_str + ', ' + rec_country_str,
        # 'receiverAddress7': rec_city_str,
        # 'receiverAddress8': rec_state_str,
        'receiverNationCode': 'KR',
        'receiverName': recipient_info.contact_name,
        'receiverName2': recipient_info.contact_name,
        'receiverCelNumber': recipient_info.contact_phone,
        # 'receiverCodeType': '1',
        # 'receiverCode': 'P' + str(buyer_tax)
    }
    if recipient_info.id_type == 'PCCC':
        receiver_info['receiverCodeType'] = '1'
        receiver_info['receiverCode'] = recipient_info.id_number

    contents = []
    order_value_total = 0  # 总价值
    item_goods_li = []
    item_num = 0
    total_weight = 0

    for parcel in parcel_objs:
        total_weight += float(parcel.parcel_weight)

    for parcelItem in parcel_item_list:
        item_value = float(parcelItem.declared_price * parcelItem.item_qty)
        item_goods_li.append((float(parcelItem.declared_price), parcelItem.declared_nameEN))
        item_num += 1
        item_dict = {
            'goodsSerialNumber': item_num,
            'goodsName1': parcelItem.declared_nameEN,
            'goodsCount': parcelItem.item_qty,
            'goodsWeight': round(float(parcelItem.item_weight), 2),
            'goodsWeightUnit': parcelItem.weight_unit,
            'goodsUnitPrice': float(parcelItem.declared_price),
            'unitPriceCurrency': 'USD',
            'hsCode': parcelItem.in_HScode or parcelItem.customs_code,  # 海关编码
            'sendDate': current_datetime,
            'goodsIMGURL': parcelItem.sku_url  # 商品SKU url
        }
        order_value_total += item_value
        contents.append(item_dict)

    max_tuple = max(item_goods_li, key=lambda x: x[0])
    extra_data = {
        'shoppingMallUrl': parcel_item_list[0].sku_url,  # 销售链接
        'goodsName': str(max_tuple[1]),
        'grossWeight': total_weight,
        "weightUnit": "KG",
        'invoiceValues': round(order_value_total, 2),
    }

    shipper_data['receiverInfo'] = receiver_info
    shipper_data.update(extra_data)
    shipper_data['itemList'] = contents
    return shipper_data


def get_call(service_dict, create_data, service_id, supplier_id, customer_id, air_waybill_number):
    """
    下单
    """

    # 身份验证
    auth_id = service_dict.get('auth_id')  # 授权id
    auth_pwd = service_dict.get('auth_pwd')  # 授权码
    url = service_dict.get('url') + f'/svc/hjexpress/{ENV}/manifest'

    # auth_id = 'SHENZHEN'
    # auth_pwd = '25A0BAC68D3F4247A73EFDEA35599BD8A0D7145B'
    # url = 'https://api-gateway.hanjin.co.kr:4443'

    logger.info(f'xxxxx hanjin create_data: {create_data}')
    res, headers = request_manifest(url, auth_id, 'POST', auth_pwd, create_data, service_id,
                                    supplier_id, customer_id, air_waybill_number)
    return res, headers, url


def get_address_request_dto(customer_order, label_order_vo, service):
    label_order_vo_data = get_label_order_vo_v2(customer_order, label_order_vo, service)
    service_dict = label_order_vo.service_dict

    # 身份验证
    auth_id = service_dict.get('auth_id')  # 授权id
    auth_pwd = service_dict.get('auth_pwd')  # 授权码
    url = service_dict.get('url')
    req_data = {
        "clientId": auth_id,
        "orderId": label_order_vo_data.parcel_order_num,
        "zipCode": customer_order.buyer_postcode
    }
    logger.info(f'hanjin get_address data: {req_data}')
    res = getAddressRequestDto(auth_id, url, 'POST', auth_pwd, req_data, customer_order, service, ENV)
    logger.info(f'hanjin get_address result: {res}')
    return res


class HanJinIntegrationService(IntegrationInterface):
    """
    hanjin 接口
    """

    def create_order(self, label_order_vo):
        """
        创建订单
        :param label_order_vo:
        """
        service = label_order_vo.service
        parcel_info_list = label_order_vo.parcelList
        customer_order = label_order_vo.customerOrder
        order_label_task = label_order_vo.orderLabelTask
        is_unpack = label_order_vo.is_unpack

        order_num = customer_order.order_num

        # 防止重复生成面单
        if customer_order.tracking_num:
            logger.info(f'防止重复生成面单：{order_num}运单已获取派送单号{customer_order.tracking_num}')
            return
        # 面单文件已存在
        order_label = get_order_label_obj(order_num, customer_order)
        if order_label.id:
            logger.info(f'防止重复生成面单：{order_num}运单已生成面单{order_label.id}')
            return

        add_true = label_order_vo.service_dict.get('add_true', 'Y')  # Y校验分拣信息必填
        request_address = label_order_vo.service_dict.get('request_address', 'Y')  # Y走请求接口地址校验,N走本地分拣码校验

        is_verification_failed = False

        logger.info(f'hanjin start create_order {order_num}, 是否校验分拣信息{add_true}')
        if request_address == 'Y':
            # 请求address接口
            res_addr, url, request_data, request_headers = get_address_request_dto(customer_order, label_order_vo,
                                                                                   service)
            if res_addr.get('code') == 400:
                save_fail_result(order_label_task, res_addr.get('msg'))

                # 记录面单生成异常轨迹
                set_multistep_parcel_track(order_num, 'GL-999', datetime.now(), customer_order.create_by,
                                           '')

                return
        else:
            url = '本地分拣码'
            request_data = None
            request_headers = None
            res_addr = {'addrResult': 'F'}
            try:
                local_sorting_code = LocalSortingCode.objects.filter(zip_cod=customer_order.buyer_postcode).values(
                    'pdz_nam', 'tml_nam', 'cen_nam', 'es_nam', 'hub_code', 'tml_code', 'dom_mid', 'cen_cod', 'cen_nam'
                ).first()
                if local_sorting_code:
                    res_addr['addrResult'] = 'S'
                    res_addr['tmlNam'] = local_sorting_code['tml_nam'] or ''
                    res_addr['cenNam'] = local_sorting_code['cen_nam'] or ''
                    res_addr['esNam'] = local_sorting_code['es_nam'] or ''
                    res_addr['pdzNam'] = local_sorting_code['pdz_nam'] or ''
                    res_addr['hubCode'] = local_sorting_code['hub_code'] or ''
                    res_addr['tmlCode'] = local_sorting_code['tml_code'] or ''
                    res_addr['domMid'] = local_sorting_code['dom_mid'] or ''
                    res_addr['cenCode'] = local_sorting_code['cen_cod'] or ''
                    res_addr['cenNam'] = local_sorting_code['cen_nam'] or ''
            except Exception as e:
                alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                    url=url,
                    request_header=request_headers,
                    request_data=request_data,
                    response_data=None,
                    func_name='create_order',
                    status=RequestStatusEnum.FAILED.value,
                    order_num=customer_order.order_num,
                    service=service.id,
                    supplier=service.supplier.id,
                    customer=customer_order.customer.id,
                    remark=f'本地分拣码获取异常: {e}',
                ))

        json_response = json.dumps(res_addr, ensure_ascii=False)

        if res_addr['addrResult'] == 'S':
            addr_info = hub_terminal_print(res_addr)  # 用于写入面单顶部
            tml_name = res_addr['tmlNam']
            cen_name = res_addr['cenNam']
            es_name = res_addr['esNam']
            pdz_name = res_addr['pdzNam']
        elif res_addr['addrResult'] == 'F':
            is_verification_failed = True
            if add_true == 'N':
                addr_info = ''
                tml_name = ''
                cen_name = ''
                es_name = ''
                pdz_name = ''
            else:
                save_fail_result(order_label_task, f'生成面单失败: 收件人邮编不正确,请核实')
                return

        elif res_addr['addrResult'] == 'U':
            save_fail_result(order_label_task, '生成面单失败: 正在生成面单中,请等待并重新获取')
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=request_data,
                response_data=json_response,
                func_name='create_order',
                status=RequestStatusEnum.FAILED.value,
                order_num=customer_order.order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                remark='生成面单失败: 正在生成面单中,请等待并重新获取',
            ))
            return
        else:
            save_fail_result(order_label_task, f'生成面单失败: 正在生成面单中,请等待并重新获取 {res_addr}')
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=request_data,
                response_data=json_response,
                func_name='create_order',
                status=RequestStatusEnum.FAILED.value,
                order_num=customer_order.order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                remark='生成面单失败: 尾程供应商返回未知错误',
            ))
            return

        tmp_info = combination_create_label_data(customer_order, label_order_vo, service, is_unpack, order_label_task)
        logger.info(f'combination_create_label_data: {tmp_info}')
        tracking_num = tmp_info['way_bill_num_barcode']
        label_data = [{
            'head_title': addr_info,
            'tml_name': tml_name,
            'cen_name': cen_name,
            'es_name': es_name,
            'pdz_name': pdz_name,
            **tmp_info
        }]
        logger.info(f'系统生成面单数据: {label_data}')
        try:
            res_label_data = create_barcodes_for_order_hanjin(label_data)
        except Exception as e:
            logger.error(f'本地生成面单失败: {traceback.format_exc()}')
            save_fail_result(order_label_task, f'本地生成面单失败:{label_data}')
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=request_data,
                response_data=json_response,
                func_name='create_order',
                status=RequestStatusEnum.FAILED.value,
                order_num=customer_order.order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                remark=f'本地生成面单失败:{label_data}',
            ))
            return

        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + customer_order.order_num + ".pdf"
        upload_file(label_url, res_label_data, 'base64')
        order_num = customer_order.order_num
        order_label = get_order_label_obj(order_num, customer_order, tracking_num)
        order_label.order_num = customer_order
        order_label.tracking_no = tracking_num
        order_label.third_order_no = tracking_num
        order_label.label_url = label_url
        order_label.create_by = order_label_task.create_by
        order_label.create_date = datetime.now()
        order_label.product = order_label_task.product
        order_label.save()

        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'
        order_label_task.third_order_no = tracking_num
        order_label_task.update_date = datetime.now()
        order_label_task.save()

        customer_order.tracking_num = tracking_num
        customer_order.update_date = datetime.now()
        # 更新小包单状态
        customer_order.order_status = 'GL'
        # 是否换单状态
        customer_order.is_change_waybill = True
        customer_order.save(update_fields=['order_status', 'update_date', 'tracking_num', 'is_change_waybill'])

        # 插入面单生成成功轨迹
        set_multistep_parcel_track(customer_order.order_num, 'GL-000', datetime.now(), customer_order.create_by,
                                   '')

        track_supplier = TrackSupplier.objects.filter(class_name='HanJinTrackService', del_flag=False).first()
        if track_supplier:
            add_track_task(customer_order, track_supplier, '')

        if is_verification_failed:
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=request_data,
                response_data=json_response,
                func_name='create_order',
                status=RequestStatusEnum.FAILED.value,
                order_num=customer_order.order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                remark='邮编校验不通过',
            ))

        alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
            url=url,
            request_header=request_headers,
            request_data=request_data,
            response_data=json_response,
            func_name='create_order',
            status=RequestStatusEnum.SUCCESS.value,
            order_num=customer_order.order_num,
            service=service.id,
            supplier=service.supplier.id,
            customer=customer_order.customer.id,
            remark='成功',
        ))

    def get_label(self, label_order_vo):
        """
        获取面单
        :param label_order_vo:
        """
        order_label_task = label_order_vo.orderLabelTask
        customer_order = label_order_vo.customerOrder
        service = label_order_vo.service

        order_num = customer_order.order_num

        result = {
            'code': 400,
            'msg': '失败',
        }

        add_true = label_order_vo.service_dict.get('add_true', 'Y')  # Y校验分拣信息必填
        request_address = label_order_vo.service_dict.get('request_address', 'Y')  # Y走请求接口地址校验,N走本地分拣码校验

        is_verification_failed = False

        logger.info(f'hanjin start create_order {order_num}, 是否校验分拣信息{add_true}')
        if request_address == 'Y':
            # 请求address接口
            res_addr, url, request_data, request_headers = get_address_request_dto(customer_order, label_order_vo,
                                                                                   service)
            if res_addr.get('code') == 400:
                save_fail_result(order_label_task, res_addr.get('msg'))
                result['msg'] = res_addr.get('msg')
                return result
        else:
            url = '本地分拣码'
            request_data = None
            request_headers = None
            res_addr = {'addrResult': 'F'}
            try:
                local_sorting_code = LocalSortingCode.objects.filter(zip_cod=customer_order.buyer_postcode).values(
                    'pdz_nam', 'tml_nam', 'cen_nam', 'es_nam', 'hub_code', 'tml_code', 'dom_mid', 'cen_cod', 'cen_nam'
                ).first()
                if local_sorting_code:
                    res_addr['addrResult'] = 'S'
                    res_addr['tmlNam'] = local_sorting_code['tml_nam'] or ''
                    res_addr['cenNam'] = local_sorting_code['cen_nam'] or ''
                    res_addr['esNam'] = local_sorting_code['es_nam'] or ''
                    res_addr['pdzNam'] = local_sorting_code['pdz_nam'] or ''
                    res_addr['hubCode'] = local_sorting_code['hub_code'] or ''
                    res_addr['tmlCode'] = local_sorting_code['tml_code'] or ''
                    res_addr['domMid'] = local_sorting_code['dom_mid'] or ''
                    res_addr['cenCode'] = local_sorting_code['cen_cod'] or ''
                    res_addr['cenNam'] = local_sorting_code['cen_nam'] or ''
            except Exception as e:
                alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                    url=url,
                    request_header=request_headers,
                    request_data=request_data,
                    response_data=None,
                    func_name='create_order',
                    status=RequestStatusEnum.FAILED.value,
                    order_num=customer_order.order_num,
                    service=service.id,
                    supplier=service.supplier.id,
                    customer=customer_order.customer.id,
                    remark=f'本地分拣码获取异常: {e}',
                ))

        json_response = json.dumps(res_addr, ensure_ascii=False)

        if res_addr['addrResult'] == 'S':
            addr_info = hub_terminal_print(res_addr)  # 用于写入面单顶部
            tml_name = res_addr['tmlNam']
            cen_name = res_addr['cenNam']
            es_name = res_addr['esNam']
            pdz_name = res_addr['pdzNam']
        elif res_addr['addrResult'] == 'F':
            is_verification_failed = True
            if add_true == 'N':
                addr_info = ''
                tml_name = ''
                cen_name = ''
                es_name = ''
                pdz_name = ''
            else:
                save_fail_result(order_label_task, f'生成面单失败: 收件人邮编不正确,请核实')
                result['msg'] = '生成面单失败: 收件人邮编不正确,请核实'
                return result

        elif res_addr['addrResult'] == 'U':
            save_fail_result(order_label_task, '生成面单失败: 正在生成面单中,请等待并重新获取')
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=request_data,
                response_data=json_response,
                func_name='create_order',
                status=RequestStatusEnum.FAILED.value,
                order_num=customer_order.order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                remark='生成面单失败: 正在生成面单中,请等待并重新获取',
            ))
            result['msg'] = '生成面单失败: 正在生成面单中,请等待并重新获取'
            return result
        else:
            save_fail_result(order_label_task, f'生成面单失败: 正在生成面单中,请等待并重新获取 {res_addr}')
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=request_data,
                response_data=json_response,
                func_name='create_order',
                status=RequestStatusEnum.FAILED.value,
                order_num=customer_order.order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                remark='生成面单失败: 尾程供应商返回未知错误',
            ))
            result['msg'] = '生成面单失败: 正在生成面单中,请等待并重新获取'
            return result

        tmp_info = self.__get_label_data(customer_order, label_order_vo, service, order_label_task)
        logger.info(f'__get_label_data: {tmp_info}')
        tracking_num = tmp_info['way_bill_num_barcode']
        label_data = [{
            'head_title': addr_info,
            'tml_name': tml_name,
            'cen_name': cen_name,
            'es_name': es_name,
            'pdz_name': pdz_name,
            **tmp_info
        }]
        logger.info(f'系统生成面单数据: {label_data}')
        try:
            res_label_data = create_barcodes_for_order_hanjin(label_data)
        except Exception as e:
            logger.error(f'本地生成面单失败: {traceback.format_exc()}')
            save_fail_result(order_label_task, f'本地生成面单失败:{label_data}')
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=request_data,
                response_data=json_response,
                func_name='create_order',
                status=RequestStatusEnum.FAILED.value,
                order_num=customer_order.order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                remark=f'本地生成面单失败:{label_data}',
            ))
            result['msg'] = f'本地生成面单失败:{label_data}'
            return result

        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + customer_order.order_num + ".pdf"
        upload_file(label_url, res_label_data, 'base64')
        order_num = customer_order.order_num
        order_label = get_order_label_obj(order_num, customer_order, tracking_num)
        order_label.order_num = customer_order
        order_label.tracking_no = tracking_num
        order_label.third_order_no = tracking_num
        order_label.label_url = label_url
        order_label.create_by = order_label_task.create_by
        order_label.create_date = datetime.now()
        order_label.product = order_label_task.product
        order_label.save()

        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'
        order_label_task.third_order_no = tracking_num
        order_label_task.update_date = datetime.now()
        order_label_task.save()

        if is_verification_failed:
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=request_data,
                response_data=json_response,
                func_name='create_order',
                status=RequestStatusEnum.FAILED.value,
                order_num=customer_order.order_num,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                remark='邮编校验不通过',
            ))

        alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
            url=url,
            request_header=request_headers,
            request_data=request_data,
            response_data=json_response,
            func_name='create_order',
            status=RequestStatusEnum.SUCCESS.value,
            order_num=customer_order.order_num,
            service=service.id,
            supplier=service.supplier.id,
            customer=customer_order.customer.id,
            remark='成功',
        ))
        result['code'] = 200
        result['msg'] = '成功'
        return result

    def cancel_label(self, label_order_vo):
        """
        取消面单
        :param label_order_vo:
        """
        order_label_task = label_order_vo.orderLabelTask
        customer_order = label_order_vo.customerOrder
        product = label_order_vo.product
        service = label_order_vo.service

        # 回收运单号
        # HanJinWayBillNumber.objects.filter(
        #     del_flag=True, way_bill_num=customer_order.tracking_num
        # ).update(del_flag=False)

        return_data = {
            'msg': 'Success',
            'code': '0'
        }
        alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
            url='/api/parcelCustomerOrders/api_cancel_label/',
            request_header='',
            request_data='',
            response_data=json.dumps(return_data, ensure_ascii=False),
            func_name='cancel_label',
            status=RequestStatusEnum.SUCCESS.value,
            customer_order_num=customer_order.customer_order_num,
            order_num=customer_order.order_num,
            tracking_num=customer_order.tracking_num,
            service=service.id,
            supplier=service.supplier.id,
            customer=customer_order.customer.id,
        ))
        return return_data

    def get_order(self, label_order_vo):
        pass

    def confirm_ship(self, label_order_vo):
        service = label_order_vo.service
        supplier_account = label_order_vo.supplierAccount
        customer_order = label_order_vo.customerOrder
        parcel_related_name = label_order_vo.parcel_related_name
        parcel_item_related_name = label_order_vo.parcelItem_related_name
        service_dict = label_order_vo.service_dict
        scan_form_data = label_order_vo.scan_form_data

        order_num = customer_order.order_num

        result = {
            'code': 200,
            'message': 'OK',
            'order_num': order_num,
        }

        create_data = manifest_receipt_dto([customer_order], parcel_related_name,
                                           parcel_item_related_name, service_dict, scan_form_data,
                                           label_order_vo, service)

        # 身份验证
        auth_id = supplier_account.auth_id  # 授权id
        auth_pwd = supplier_account.auth_pwd  # 授权码
        url = supplier_account.url + f'/svc/hjexpress/{ENV}/manifest'

        logger.info(f'xxxxx hanjin confirm_ship: {create_data}')

        response, headers = request_confirm_ship(url, auth_id, 'POST', auth_pwd, create_data, service.id,
                                                 service.supplier.id, customer_order.customer.id, order_num)

        logger.info(f'confirm_ship 接口返回值 {response}')
        result['message'] = response.get('message')
        if response.get('code') == '200' and response['resultList']:
            logger.info(f'confirm_ship 成功: {response}')
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=headers,
                request_data=json.dumps(create_data, ensure_ascii=False),
                response_data=response,
                func_name='confirm_ship',
                status=RequestStatusEnum.SUCCESS.value,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                order_num=order_num,
            ))
            for result_list_info in response['resultList']:
                if result_list_info['detailCode'] == '200':
                    result['message'] = result_list_info.get('detailMessage')
                else:
                    result['code'] = 400
                    result['message'] = result_list_info.get('detailMessage')
        else:
            result['code'] = 400
            result['message'] = '供应商未返回结果'
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=headers,
                request_data=json.dumps(create_data, ensure_ascii=False),
                response_data=response,
                func_name='confirm_ship',
                status=RequestStatusEnum.SUCCESS.value,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                order_num=order_num,
            ))

        return result

    def get_secondary_label(self, label_order_vo):
        '''
        二次获取面单
        '''
        pass

    def scanform(self, label_order_vo):
        # 请求 Manifest 接口
        service = label_order_vo.service
        supplier_account = label_order_vo.supplierAccount
        customer_orders = label_order_vo.customerOrders
        parcel_related_name = label_order_vo.parcel_related_name
        parcel_item_related_name = label_order_vo.parcelItem_related_name
        service_dict = label_order_vo.service_dict
        scan_form_data = label_order_vo.scan_form_data
        customer_order = customer_orders.first()

        result = {
            'code': 200,
            'message': 'OK',
            'order_nums': [],
        }
        # 每500一批
        total_count = customer_orders.count()
        for i in range(math.ceil(total_count / 500)):
            bath_orders = customer_orders[i * 500: i * 500 + 500]
            self.handler_scanform_request(bath_orders, customer_order, customer_orders, label_order_vo,
                                          parcel_item_related_name, parcel_related_name, result, scan_form_data,
                                          service, service_dict)

        return result

    def handler_scanform_request(self, bath_orders, customer_order, customer_orders, label_order_vo,
                                 parcel_item_related_name, parcel_related_name, result, scan_form_data, service,
                                 service_dict):

        # 空运主单好
        air_waybill_number = scan_form_data.get('flightInfo', {}).mawbNo if scan_form_data.get('flightInfo', {}) else ''

        create_data = manifest_receipt_dto(bath_orders, parcel_related_name,
                                           parcel_item_related_name, service_dict, scan_form_data,
                                           label_order_vo, service)
        res_manifest, request_headers, url = get_call(service_dict, create_data, service.id,
                                                      service.supplier.id, customer_order.customer.id,
                                                      air_waybill_number)
        logger.info(f'Manifest 接口返回值 {res_manifest}')
        result['message'] = res_manifest.get('message')
        if res_manifest.get('code') == '200' and res_manifest['resultList']:
            logger.info(f'Manifest 成功: {res_manifest}')
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=json.dumps(create_data, ensure_ascii=False),
                response_data=res_manifest,
                func_name='scanform',
                status=RequestStatusEnum.SUCCESS.value,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                air_waybill_number=air_waybill_number,
            ))
            for result_list_info in res_manifest['resultList']:
                if result_list_info['detailCode'] == '200' or '13 : Already used order number' == result_list_info[
                    'detailMessage']:
                    result['order_nums'].append({
                        'order_num': result_list_info['orderNumber'],
                        'message': result_list_info.get('detailMessage'),
                        'code': 200,
                    })
                else:
                    result['order_nums'].append({
                        'order_num': result_list_info['orderNumber'],
                        'message': result_list_info.get('detailMessage'),
                        'code': 999,
                    })
            result['code'] = 200
        else:
            result['code'] = 400
            alita_logger_info(stream=StreamEnum.LASTMILE.value, logformat=LogFormat(
                url=url,
                request_header=request_headers,
                request_data=json.dumps(create_data, ensure_ascii=False),
                response_data=res_manifest,
                func_name='scanform',
                status=RequestStatusEnum.SUCCESS.value,
                service=service.id,
                supplier=service.supplier.id,
                customer=customer_order.customer.id,
                air_waybill_number=air_waybill_number,
            ))
            for customer_order in customer_orders:
                result['order_nums'].append({
                    'order_num': customer_order.order_num,
                    'message': '供应商未返回结果',
                    'code': 999,
                })

    def update_order(self, label_order_vo):
        """
        更新订单信息
        :param label_order_vo:
        :return:
        """
        pass

    def __get_label_data(self, customer_order, label_order_vo, service, order_label_task):
        """
        获取面单信息
        :param customer_order:
        :param label_order_vo:
        :param service:
        :return:
        """
        label_order_vo_data = get_label_order_vo_v2(customer_order, label_order_vo, service)

        is_customer_order = label_order_vo_data.is_customer_order
        parcel_order_num = label_order_vo_data.parcel_order_num
        service_code = label_order_vo_data.service_code
        supplier_account = label_order_vo_data.supplier_account
        warehouse = label_order_vo_data.warehouse
        return_info = label_order_vo_data.return_info
        recipient_info = label_order_vo_data.recipient_info

        product = label_order_vo.product
        parcel_info_list = label_order_vo.parcelList
        parcel_item_list = label_order_vo.parcelItemList

        dest_three_code = label_order_vo.service_dict.get('toAirport_Code')
        origin_three_code = label_order_vo.service_dict.get('fromAirport_Code')
        fromcountry = label_order_vo.service_dict.get('fromcountry')

        logger.info(
            f'dest_three_code: {dest_three_code}; origin_three_code: {origin_three_code}, fromcountry: {fromcountry}')

        if not recipient_info:
            save_fail_result(order_label_task, f'生成面单失败: 收件人地址未填写,请核实')
            return

        if not return_info:
            save_fail_result(order_label_task, f'生成面单失败: 退件地址未填写,请核实')
            return

        # 发件人地址
        # 地址拼接顺序: 门牌号>地址2>地址1>街道>区>城市>省份>邮编>国家
        sender_address = build_address(return_info)

        # 收件人信息
        # 地址拼接顺序: 门牌号>地址2>地址1>街道>区>城市>省份>邮编>国家
        recv_address = build_address(recipient_info)

        parcel_info = parcel_info_list[0]  # 一条数据，一个包裹
        total_weight = 0  # 总重量
        # 总重从包裹拿，仓库可能会重新称重
        for parcel in parcel_info_list:
            total_weight += float(parcel.parcel_weight)

        item_info = ''
        for parcelItem in parcel_item_list:
            if parcelItem.parcel_num == parcel_info:
                item_info = str(parcelItem.declared_nameEN) + '*' + str(parcelItem.item_qty) + ' '

        origin_dest = f'{fromcountry} {origin_three_code} - KR {dest_three_code}'

        current_date = datetime.now().strftime("%Y-%m-%d")
        # weight_unit = 'KG'
        weight_unit = ''
        weight_info = '1, ' + str(round(float(total_weight), 2)) + weight_unit
        way_bill_num = customer_order.tracking_num
        format_num = "-".join([way_bill_num[i:i + 4] for i in range(0, len(way_bill_num), 4)])
        label_data = {
            'origin_dest': origin_dest,
            'recv_name': recipient_info.contact_name,
            'recv_tel': recipient_info.contact_phone,
            'recv_address': recv_address,
            'sender_name': return_info.contact_name,
            'sender_tel': return_info.contact_phone,
            'sender_address': sender_address,
            'parcel_order_num': parcel_order_num,
            'item_info': item_info,
            'current_date': current_date,
            'weight_info': weight_info,
            'way_bill_num': format_num,
            'way_bill_num_barcode': way_bill_num
        }
        return label_data


def save_fail_result(order_label_task, result):
    order_label_task.label_desc = result
    order_label_task.handle_times += 1
    order_label_task.update_date = datetime.now()
    order_label_task.save()
