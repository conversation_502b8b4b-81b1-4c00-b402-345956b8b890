from datetime import datetime
import json
import os
from decimal import Decimal

import requests
from django.forms import model_to_dict

from alita.logger import logger
from common.utils.file_util import upload_file
from common.tools import change_label_common_func
from common.service.product_attribute_limit import get_attribute_limitation_default_value
from order.integration.integrationInterface import IntegrationInterface, get_order_label_obj, \
    get_label_order_vo

from common.service.product_attribute_limit import get_productBasicRestriction

from alita.settings.base import MEDIA_URL

from order.integration.util.px4_util import request_4px, OrderInfo, LogisticsServiceInfo, Parcel, Product, \
    DeclareProductInfo, Address, DeliverTypeInfo, ReturnInfo


def get_call(customer_order, label_order_vo, service, is_unpack):
    """
    下单
    :param customer_order:
    :param label_order_vo:
    :param service:
    :return:
    """
    is_customer_order, parcel_order_num, service_code, supplier_account, warehouse = \
        get_label_order_vo(customer_order, label_order_vo, service)

    order_info = OrderInfo()
    order_info.ref_no = parcel_order_num
    product = label_order_vo.product

    order_info.business_type = 'BDS'
    logistics_service_info = LogisticsServiceInfo()
    logistics_service_info.logistics_product_code = service.code
    order_info.logistics_service_info = logistics_service_info.__dict__
    order_info.is_insure = 'N'
    order_info.ioss_no = customer_order.ioss_num  # 增加ioss号

    parcel_info_list = label_order_vo.parcelList
    parcel_item_list = label_order_vo.parcelItemList

    default_value = get_attribute_limitation_default_value(product, 'declared_price', customer_order.buyer_country_code)
    logger.info(f'default_value: {default_value}')

    parcel_list = []
    total_qty = 0
    for parcel_info in parcel_info_list:
        parcel = Parcel()
        parcel.parcel_barcode = parcel_info.parcel_num
        if is_customer_order:
            parcel.weight = str(round(float(Decimal(str(parcel_info.label_weight))*Decimal(1000))))
        else:
            parcel.weight = str(round(float(Decimal(str(parcel_info.label_weight or parcel_info.parcel_weight))*Decimal(1000))))

        parcel.length = str(parcel_info.parcel_length)
        parcel.width = str(parcel_info.parcel_width)
        parcel.height = str(parcel_info.parcel_height)

        parcel.include_battery = 'N'

        product_list = []
        total_price = 0
        currency = ''
        for parcelItem in parcel_item_list:
            if parcelItem.parcel_num == parcel_info:
                product = Product()
                product.sku_code = parcelItem.item_code
                product.product_id = parcelItem.item_code
                product.product_name = parcelItem.declared_nameCN
                product.product_name_en = parcelItem.declared_nameEN
                product.product_description = parcelItem.item_code
                product.product_unit_price = str(parcelItem.declared_price)
                product.currency = parcelItem.declared_currency
                product.qty = parcelItem.item_qty
                product_list.append(product.__dict__)
                total_price += Decimal(parcelItem.declared_price)
                total_qty += parcelItem.item_qty
                if currency == '':
                    currency = parcelItem.declared_currency

                declare_product_info_list = []
                declare_product_info = DeclareProductInfo()
                declare_product_info.declare_product_code_qty = parcelItem.item_qty
                declare_product_info.declare_unit_price_export = str(parcelItem.declared_price)
                declare_product_info.currency_export = parcelItem.declared_currency
                declare_product_info.declare_unit_price_import = str(parcelItem.declared_price)
                declare_product_info.currency_import = parcelItem.declared_currency
                # declare_product_info.brand_export = ''
                # declare_product_info.brand_import = ''
                declare_product_info.declare_product_name_cn = parcelItem.declared_nameCN
                declare_product_info.declare_product_name_en = parcelItem.declared_nameEN
                declare_product_info.package_remarks = parcelItem.item_code
                declare_product_info_list.append(declare_product_info.__dict__)

        if default_value:
            logger.info(f'default_value 有值: {default_value}')
            parcel.parcel_value = str(default_value)
        else:
            parcel.parcel_value = str(total_price)
        parcel.currency = currency
        parcel.product_list = product_list
        parcel.declare_product_info = declare_product_info_list
        parcel_list.append(parcel.__dict__)

    order_info.parcel_list = parcel_list

    if is_customer_order:
        sender = get_sender_address(customer_order)
    else:
        sender = get_sender_address(warehouse)


    order_info.sender = sender.__dict__

    receiver = Address()
    receiver.first_name = customer_order.buyer_name
    receiver.company = customer_order.buyer_company_name or customer_order.buyer_name
    receiver.phone = customer_order.buyer_phone or ''
    if customer_order.buyer_mail:
       receiver.email = customer_order.buyer_mail
    receiver.post_code = customer_order.buyer_postcode
    receiver.country = customer_order.buyer_country_code
    if customer_order.buyer_state:
       receiver.state = customer_order.buyer_state
    receiver.city = customer_order.buyer_city_code
    # sender.district = ''
    receiver.street = customer_order.buyer_address_one
    if customer_order.buyer_address_two:
        receiver.street += ' ' + customer_order.buyer_address_two
    if customer_order.buyer_house_num:
       receiver.house_number = customer_order.buyer_house_num or ''

    order_info.recipient_info = receiver.__dict__

    deliver_type_info = DeliverTypeInfo()
    deliver_type_info.deliver_type = '3'
    order_info.deliver_type_info = deliver_type_info.__dict__

    # 根据扩展属性来判断是否给退回地址信息 if_not_return_info, 如果此属性打开则 return_info 返回空，默认原逻辑不变
    property_product = label_order_vo.product
    if_not_return_info = get_productBasicRestriction(property_product, 'if_not_return_info')
    if if_not_return_info:
        order_info.return_info = {
            "is_return_on_domestic": "N"
        }
    else:
        return_info = ReturnInfo()
        return_info.is_return_on_domestic='N'
        if is_customer_order:
            return_address = get_sender_address(customer_order)
        else:
            return_address = get_sender_address(warehouse)
        return_info.domestic_return_addr = return_address.__dict__

        order_info.return_info = return_info.__dict__

    app_key = supplier_account.auth_id
    auth_pwd = supplier_account.auth_pwd
    data = order_info.__dict__

    if default_value:  # 更改item中的申报价值
        item_price =  round(float(default_value) / total_qty, 2)
        parcel_lis = data.get('parcel_list')
        for parcel_li in parcel_lis:
            declare_product_infos = parcel_li.get('declare_product_info')
            for declare_product_info in declare_product_infos:
                declare_product_info['declare_unit_price_export'] = str(item_price)
                declare_product_info['declare_unit_price_import'] = str(item_price)

    url = supplier_account.url
    version = '1.0'
    return request_4px("ds.xms.order.create", app_key, auth_pwd, data, url, version, parcel_order_num)



def get_sender_address(warehouse):
    """
    封装发货地址
    :param sender_address:
    :param warehouse:
    """
    sender = Address()
    sender.first_name = warehouse.contact_name
    sender.phone = warehouse.contact_phone
    if warehouse.company_name:
        sender.company = warehouse.company_name
    else:
        sender.company = warehouse.contact_name
    if warehouse.contact_email:
       sender.email = warehouse.contact_email
    sender.post_code = warehouse.postcode
    sender.country = warehouse.country_code
    if warehouse.state_code:
       sender.state = warehouse.state_code
    sender.city = warehouse.city_code
    # sender.district = ''
    sender.street = warehouse.address_one
    if warehouse.address_two:
        sender.street += ' ' + warehouse.address_two

    if warehouse.house_no:
       sender.house_number = warehouse.house_no


    return sender


class Px4IntegrationService(IntegrationInterface):
    """
    4px接口
    """

    def create_order(self, labelOrderVo):
        """
        创建订单
        :param labelOrderVo:
        """
        logger.info("4px create_order")
        service = labelOrderVo.service
        customer_order = labelOrderVo.customerOrder
        order_label_task = labelOrderVo.orderLabelTask
        is_unpack = labelOrderVo.is_unpack
        result = get_call(customer_order, labelOrderVo, service, is_unpack)
        if int(result['result']) == 1:
            order_label_task.status = 'HandledBy3rdNo'
            order_label_task.label_desc = '等待抓取面单'
            label_barcode = result['data']['label_barcode']
            order_label_task.third_order_no = label_barcode
            order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            order_label_task.save()

        else:
            if result['errors']:
                order_label_task.label_desc = '下单失败:' + result['errors'][0]['error_msg']
                order_label_task.handle_times = 121
                order_label_task.update_date = datetime.now()
                order_label_task.save()
            else:
                save_fail_result(order_label_task, '下单失败:无返回结果')

    def get_label(self, label_order_vo):
        """
        获取面单
        :param label_order_vo:
        """
        logger.info("4px get_label")
        order_label_task = label_order_vo.orderLabelTask

        supplier_account = label_order_vo.supplierAccount
        customer_order = label_order_vo.customerOrder
        service = label_order_vo.service
        product = label_order_vo.product

        data = {
            "request_no": order_label_task.third_order_no,
            "is_print_merge": 'Y'
        }
        app_key = supplier_account.auth_id
        auth_pwd = supplier_account.auth_pwd
        url = supplier_account.url
        version = '1.0'
        result = request_4px("ds.xms.label.get", app_key, auth_pwd, data, url, version, customer_order.order_num)

        if int(result['result']) != 1:
            save_fail_result(order_label_task, '获取面单失败:' + result['errors'][0]['error_msg'])
            return

        result_data = result['data']
        if result_data == '':
            save_fail_result(order_label_task, '获取面单失败:无返回结果')
            return

        mainTrackingNo = result_data['label_barcode']
        if mainTrackingNo == '':
            save_fail_result(order_label_task, '获取面单失败:无跟踪号')
            return

        i = 1

        trackingNo = mainTrackingNo
        label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + customer_order.order_num + "_" + str(
            i) + ".pdf"
        i += 1
        label_data = result_data['label_url_info']['logistics_label']
        upload_file(label_url, label_data, 'url')

        # decoded_file_name = MEDIA_URL + label_url
        # dir = os.path.dirname(decoded_file_name)
        # if not os.path.exists(dir):
        #     os.makedirs(dir)
        # r = requests.get(result_data['label_url_info']['logistics_label'])
        # with open(decoded_file_name, "wb") as code:
        #     code.write(r.content)

        order_num = customer_order.order_num
        order_label = get_order_label_obj(order_num)
        order_label.order_num = customer_order
        order_label.tracking_no = trackingNo
        order_label.third_order_no = order_label_task.third_order_no
        order_label.label_url = label_url
        order_label.create_by = order_label_task.create_by
        order_label.create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label.product = order_label_task.product
        order_label.save()

        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'

        order_label_task.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        order_label_task.save()

        customer_order.tracking_num = mainTrackingNo
        customer_order.update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        customer_order.save()

        # 面单更改逻辑
        order_label_changed = get_order_label_obj(order_num)
        try:
            change_label_common_func(service, product, customer_order, order_label,
                                                        customer_order.order_num, order_label_changed, order_label_task,
                                                        log_flag='px4')
        except Exception as e:
            order_label_task.status = 'UnHandled'
            order_label_task.label_desc = '更改面单失败：' + str(e)
            order_label_task.handle_times = 121
            order_label_task.update_date = datetime.now()
            order_label_task.save()

    def cancel_label(self, label_order_vo):
        """
        取消面单
        :param label_order_vo:
        """
        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount
        customer_order = label_order_vo.customerOrder
        data = {
            "request_no": customer_order.tracking_num,
            "cancel_reason": '客户申请取消'
        }
        app_key = supplier_account.auth_id
        auth_pwd = supplier_account.auth_pwd
        url = supplier_account.url
        version = '1.0'
        result = request_4px("ds.xms.order.cancel", app_key, auth_pwd, data, url, version, customer_order.order_num)

        result_data = {}
        if int(result['result']) == 1:
            result_data['code'] = '0'
        else:
            result_data['code'] = '400'
            if result['errors'] and result['errors'][0]:
                result_data['msg'] = result['errors'][0]['error_msg']
            elif result['msg']:
                result_data['msg'] = result['msg']
            else:
                result_data['msg'] = '取消失败'

        return result_data

    def get_order(self, label_order_vo):
        pass

    def confirm_ship(self, label_order_vo):
        pass

    def update_order(self, label_order_vo):
        pass

    def get_secondary_label(self, label_order_vo):
        '''
        二次获取面单
        '''
        pass

    def scanform(self, label_order_vo):
        pass

def save_fail_result(order_label_task, result):
    order_label_task.label_desc = result
    order_label_task.handle_times += 1
    order_label_task.update_date = datetime.now()
    order_label_task.save()