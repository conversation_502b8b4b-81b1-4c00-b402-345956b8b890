import json
import traceback
from datetime import datetime
from decimal import Decimal
import requests

from alita.logger import logger
from common.utils.logger_util import alita_logger_info
from common.utils.file_util import upload_file
from order.integration.integrationInterface import IntegrationInterface, get_label_order_vo, get_order_label_obj
from order.integration.util.amazon_shipping_util import get_refresh_token, get_tracking
from order.integration.util.commonUtil import DecimalEncoder


def get_call(customer_order, label_order_vo, service, is_unpack):
    """
    下单
    :param customer_order:
    :param label_order_vo:
    :param service:
    :return:
    """

    is_customer_order, parcel_order_num, service_code, supplier_account, warehouse = \
        get_label_order_vo(customer_order, label_order_vo, service)

    parcel_info_list = label_order_vo.parcelList
    parcel_item_list = label_order_vo.parcelItemList
    service_dict = label_order_vo.service_dict

    url = service_dict.get('url')

    if is_customer_order:
        sender_obj = customer_order
    else:
        sender_obj = warehouse

    order_data = {
        'channelDetails': {"channelType": "EXTERNAL"},
    }


    receive_data = {
        # 收件人 shipTo
        'name': customer_order.buyer_name,
        'addressLine1': customer_order.buyer_address_one,
        'city': customer_order.buyer_city_code,
        'stateOrRegion': customer_order.buyer_state,
        'countryCode': customer_order.buyer_country_code,
        'postalCode': customer_order.buyer_postcode,
    }
    if customer_order.buyer_phone:
        receive_data['phoneNumber'] = customer_order.buyer_phone
    if customer_order.buyer_address_two:
        receive_data['addressLine2'] = customer_order.buyer_address_two

    shipper_data = {
        # 发件人 shipFrom
        'name': sender_obj.contact_name,
        'countryCode': sender_obj.country_code,
        'stateOrRegion': sender_obj.state_code,
        'city': sender_obj.city_code,
        'postalCode': sender_obj.postcode,
        'addressLine1': sender_obj.address_one,
    }
    if sender_obj.contact_phone:
        shipper_data['phoneNumber'] = sender_obj.contact_phone
    if customer_order.address_two:
        shipper_data['addressLine2'] = customer_order.address_two

    order_data["shipTo"] = receive_data
    order_data["shipFrom"] = shipper_data

    order_data["labelSpecifications"] = {
        "format": "PDF",
        "needFileJoining": False,
        "requestedLabelCustomization": {
            "requestAttributes": [
                "PACKAGE_CLIENT_REFERENCE_ID",
                "SELLER_DISPLAY_NAME"
            ]
        },
        "requestedDocumentTypes": [
            "LABEL"
        ],
        "pageLayout": "DEFAULT",
        "dpi": 300,
        "size": {
            "length": 6,
            "unit": "INCH",
            "width": 4
        }
    }

    packages = []
    i = 1
    for parcel_info in parcel_info_list:
        l_dict = {}

        if parcel_info.parcel_size_unit and str(parcel_info.parcel_size_unit).upper() == 'INCH':
            dimensions_dict = {
                'length': float(parcel_info.parcel_length),
                'width': float(parcel_info.parcel_width),
                'height': float(parcel_info.parcel_height),
                'unit': "INCH",
            }
        else:
            dimensions_dict = {
                'length': float(parcel_info.parcel_length) / 2.54,
                'width': float(parcel_info.parcel_width) / 2.54,
                'height': float(parcel_info.parcel_height) / 2.54,
                'unit': "INCH",
            }

        weight = float(parcel_info.label_weight or parcel_info.parcel_weight)

        if parcel_info.parcel_weight_unit and str(parcel_info.parcel_weight_unit).upper() == 'GRAM':
            weight_dict = {
                "unit": "GRAM",
                'value': float(weight),
            }
        elif parcel_info.parcel_weight_unit and str(parcel_info.parcel_weight_unit).upper() in ['KILOGRAM', 'KG']:
            weight_dict = {
                "unit": "KILOGRAM",
                'value': float(weight),
            }
        elif parcel_info.parcel_weight_unit and str(parcel_info.parcel_weight_unit).upper() == 'OUNCE':
            weight_dict = {
                "unit": "OUNCE",
                'value': float(weight),
            }
        elif parcel_info.parcel_weight_unit and str(parcel_info.parcel_weight_unit).upper() in ['POUND', 'LB']:
            weight_dict = {
                "unit": "POUND",
                'value': float(weight),
            }
        else:
            weight_dict = {
                "unit": "GRAM",
                'value': float(weight) * 1000,  # Convert KG to GRAM
            }

        declared_price = 1
        insuredValue_dict = {
            "unit": "USD",
            'value': float(declared_price),
        }

        l_dict["dimensions"] = dimensions_dict
        l_dict["weight"] = weight_dict
        l_dict["insuredValue"] = insuredValue_dict
        l_dict["packageClientReferenceId"] = f'{parcel_order_num}-{i}'
        i = i + 1
        items = []
        for parcel_item in parcel_item_list:
            item = {
                "itemIdentifier": parcel_item.item_code,
                "quantity": parcel_item.item_qty
            }
            item['itemValue'] = {
                "unit": "USD",
                "value": float(parcel_item.declared_price)
            }

            if parcel_item.weight_unit and str(parcel_item.weight_unit).upper() == 'GRAM':
                item['weight'] = {
                    "unit": "GRAM",
                    'value': float(parcel_item.item_weight),
                }
            elif parcel_item.weight_unit and str(parcel_item.weight_unit).upper() in ['KILOGRAM', 'KG']:
                item['weight'] = {
                    "unit": "KILOGRAM",
                    'value': float(parcel_item.item_weight),
                }
            elif parcel_item.weight_unit and str(parcel_item.weight_unit).upper() == 'OUNCE':
                item['weight'] = {
                    "unit": "OUNCE",
                    'value': float(parcel_item.item_weight),
                }
            elif parcel_item.weight_unit and str(parcel_item.weight_unit).upper() in ['POUND', 'LB']:
                item['weight'] = {
                    "unit": "POUND",
                    'value': float(parcel_item.item_weight),
                }
            else:
                item['weight'] = {
                    "unit": "GRAM",
                    "value": float(parcel_item.item_weight) * 1000
                }

            items.append(item)

        l_dict["items"] = items
        packages.append(l_dict)

    order_data['packages'] = packages

    order_data['serviceSelection'] = {
        "serviceId": [
            service_dict.get('serviceId')
        ]
    }

    initial_refresh_token = service_dict.get('initial_refresh_token')
    access_token = get_refresh_token(service_dict.get('auth_id'), service_dict.get('auth_pwd'), initial_refresh_token)

    headers = {
        "accept": "application/json",
        "x-amzn-shipping-business-id": service_dict.get('x-amzn-shipping-business-id'),
        "content-type": "application/json",
        "x-amz-access-token": access_token,
    }

    url = f'{url}/shipping/v2/oneClickShipment'

    json_data = json.dumps(order_data, sort_keys=True, ensure_ascii=False, separators=(',', ':'),  cls=DecimalEncoder)
    operate_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    alita_logger_info(f'amazon shipping 请求url={url} 入参: ', str(json_data), operate_time, customer_order.order_num)
    response = requests.post(url, json=order_data, headers=headers)
    alita_logger_info(f'amazon shipping 请求url={url} 出参: ', str(response.text), operate_time, customer_order.order_num)

    return response.json()

class AmazonShippingOneClickService(IntegrationInterface):
    """
    amazon  接口
    """

    def create_order(self, labelOrderVo):
        """
        创建订单
        :param labelOrderVo:
        """
        logger.info("amazon  create_order")
        service = labelOrderVo.service
        customer_order = labelOrderVo.customerOrder
        order_label_task = labelOrderVo.orderLabelTask
        is_unpack = labelOrderVo.is_unpack

        try:
            response = get_call(customer_order, labelOrderVo, service, is_unpack)
        except Exception as e:
            logger.error(f'amazon create_order get_call 抛出异常：{e}')
            logger.error(f'错误堆栈：{traceback.format_exc()}')
            save_fail_result(order_label_task, f'下单失败: {e}', 121)
            return

        errors = response.get('errors', None)
        if errors:
            message = errors[0]
            save_fail_result(order_label_task, f'下单失败: {str(message)}', 121)
            return

        packageDocumentDetails = response['payload']['packageDocumentDetails']
        main_tracking_id = None
        i = 1
        for packageDocumentDetail in packageDocumentDetails:
            trackingNo = packageDocumentDetail['trackingId']
            if not main_tracking_id:
               main_tracking_id = trackingNo

            label_data = packageDocumentDetail['packageDocuments'][0]['contents']
            label_url = "label/" + (datetime.now().strftime("%Y/%m/%d/")) + customer_order.order_num + "_" + str(
                i) + ".pdf"
            i += 1

            upload_file(label_url, label_data, 'base64')

            order_num = customer_order.order_num
            order_label = get_order_label_obj(order_num, customer_order, trackingNo)
            order_label.order_num = customer_order
            order_label.tracking_no = trackingNo
            order_label.third_order_no = trackingNo
            order_label.label_url = label_url
            order_label.create_by = order_label_task.create_by
            order_label.create_date = datetime.now()
            order_label.product = order_label_task.product
            order_label.save()

        if not main_tracking_id:
            save_fail_result(order_label_task, '获取面单失败:无跟踪号')
            return
        carrier = response['payload']['carrier']
        order_label_task.status = 'Success'
        order_label_task.label_desc = 'Success'
        order_label_task.third_order_no=response['payload']['shipmentId']
        order_label_task.shipment_digest = carrier["id"] # 保存carrier_id, 后续追踪 tracking 要用
        order_label_task.update_date = datetime.now()
        order_label_task.save()

        type(customer_order).objects.filter(id=customer_order.id, del_flag=False).update(
            tracking_num=main_tracking_id, update_date=datetime.now(), order_status='GL', is_change_waybill=True)

    def get_label(self, label_order_vo):
        """
        获取面单
        :param label_order_vo:
        """
        pass


    def cancel_label(self, label_order_vo):
        """
        取消面单
        :param label_order_vo:
        """

        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount
        service_dict = label_order_vo.service_dict
        customer_order = label_order_vo.customerOrder
        initial_refresh_token = service_dict.get('initial_refresh_token')
        access_token = get_refresh_token(service_dict.get('auth_id'), service_dict.get('auth_pwd'), initial_refresh_token)

        shipmentId = order_label_task.third_order_no
        url = f'{service_dict.get("url")}/shipping/v2/shipments/{shipmentId}/cancel'

        headers = {
            "accept": "application/json",
            "x-amzn-shipping-business-id": service_dict.get('x-amzn-shipping-business-id'),
            "content-type": "application/json",
            "x-amz-access-token": access_token
        }

        operate_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        alita_logger_info(f'amazon shipping cancel 请求url={url} 入参: ', '', operate_time,
                          customer_order.order_num)
        response = requests.put(url=url, headers=headers)
        alita_logger_info(f'amazon shipping cancel请求url={url} 出参: ', str(response.text), operate_time,
                          customer_order.order_num)
        result_data = {}
        if response.status_code == 200:
            result_data['code'] = '0'
        else:
            result = response.json()
            result_data['code'] = '400'
            result_data['msg'] = result['errors']

        return result_data



    def get_order(self, label_order_vo):
        pass

    def confirm_ship(self, label_order_vo):
        pass

    def get_secondary_label(self, label_order_vo):
        '''
        二次获取面单
        '''
        pass

    def scanform(self, label_order_vo):
        pass

    def update_order(self, label_order_vo):
        """
        更新订单信息
        :param label_order_vo:
        :return:
        """
        pass

    def get_trackings(self, label_order_vo):
        """获取 amazon 的轨迹, 修改 customerOrder 的 status 字段
        docs: https://developer-docs.amazon.com/amazon-shipping/reference/gettracking
        """
        order_label_task = label_order_vo.orderLabelTask
        supplier_account = label_order_vo.supplierAccount
        customer_order = label_order_vo.customerOrder
        service_dict = label_order_vo.service_dict

        initial_refresh_token = service_dict.get('initial_refresh_token')
        access_token = get_refresh_token(supplier_account.auth_id, supplier_account.auth_pwd, initial_refresh_token)
        params = {
            "trackingId": customer_order.tracking_num,
            "carrierId": order_label_task.shipment_digest,
            "order_num": customer_order.order_num,
        }
        response = get_tracking(access_token,params)
        result_data = {}

        if not response:
            return result_data
        result = response.json()

        if response.status_code == 200:
            track_status = result['payload'].get("summary", {}).get("status")
            result_data['code'] = '0'
            # 在途、已签收
            status_map = {"InTransit": "TRANSIT", "Delivered": "SF"}
            order_status = status_map.get(track_status)
            if order_status and order_status != customer_order.order_status:
                customer_order.order_status = order_status
                customer_order.save(update_fields=['order_status', "update_date"])
        else:
            result_data['msg'] = result.get("errors", [])[0].get("details") if result.get('errors') else None

        return result_data

def save_fail_result(order_label_task, result, handle_times=1):
    order_label_task.label_desc = result
    order_label_task.handle_times += handle_times
    order_label_task.update_date = datetime.now()
    order_label_task.save()
