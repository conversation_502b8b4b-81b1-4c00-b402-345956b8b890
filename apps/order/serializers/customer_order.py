import json
import math
import os
import time
from collections import OrderedDict
from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP

from django.conf import settings
from django.db import transaction
from django.db.models import Q
from django.forms import model_to_dict
from rest_framework import serializers
from rest_framework.relations import PrimaryKeyRelatedField
from rest_framework.serializers import ListSerializer

from alita.logger import logger
from common.aliyun_oss import get_sign_url_from_oss
from common.common_parameter import CUSTOMER_ORDER_EXCEPT_TRACK
from common.error import ErrorCode, ParamError
from common.serializer import DeletedFilterListSerializer, CustomerRolesRestrictSerializer, \
    BaseModelSerializer
from common.service.customer_order_service import save_shiper_address
from common.service.fbm_order_import_service import FbmOrderImportService
from common.service.product_attribute_limit import check_order_data
from common.tools import change_order_status, get_service_from_product, get_update_params, \
    summary_parcels_info, gen_order_num, judge_parcel_num_rule, create_sys_parcel_num, restrict_charge_item, \
    summary_predict_parcels_data, charge_weight_round, save_parcel, save_parcel_item, create_order_reference_id, \
    save_shipments, restrict_charge_item_and_supplier, get_service_class_url, check_product_route
from common.utils.customer_order_utils import sync_parcel_num_data
from common.utils.dingding import send_dingding
from common.utils.get_currency_rate import get_currency_rate
from company.models import Address, Company
from cs.models import CustomerOrderAbnormal
from info.models import Charge
from order.integration.schemas.customer_order_schema import CustomerOrderFbmExcelSchema
from order.integration.util.commonUtil import DecimalEncoder
from order.integration.util.customerOrderUtil import request_server
from order.models import ClearanceOut, CustomerOrder, CustomerOrderChargeIn, CustomerOrderChargeOut, Insurance, \
    MasterOrder, OceanOrder, OrderLabel, OrderLabelTask, Parcel, ParcelItem, ParcelSize, Track, \
    CustomerOrderRelateOcean, OrderSyncTask, CustomerParcelTrack, OrderAsyncTask, TruckInquiryPriceChargeIn, \
    TruckInquiryPriceOrder, OcShipment, OrderFieldChangeLog, ParcelAttachment, CustomerOrderRelateTruck, TruckOrder
from order.serializers.customer_img import OceanOrderImageSerializer
from order.serializers.oc_shipment_serializer import OcShipmentSerializer
from order.serializers.order_attachment import AttachmentSerializer
from order.serializers.order_signforattachment import SignForAttachmentSerializer
# from order.serializers.parcel_customer_order import add_cost, add_revenue
from order.serializers.parcel_outbound_order import ParcelOutboundOrderSerializer
from order.serializers.track import TrackSerializer
from order.serializers.truck_inquiry_price_order import TruckInquiryPriceChargeInSerializer
from order.tasks import sync_modify_remark_to_mz
from order.utils.fba_order_utils import fba_parcel_and_item_handle
from order.utils.outboundinstruct_utils import update_oc_shipment_outbound_instruct_data
from pms.models import Service, ProductCharge
from pms.util.calc import build_order_calc_vo, get_charge_conditions, filter_warehouse, common_judge_fba_and_remote, \
    check_customer_order_shipment_id
from common.service.pms_service import build_order_calc_vo
from pms.util.product_zone import get_zone
from pms.util.cost_calc import match_criteria
from rbac.models import UserProfile
from django.db.models import Subquery, OuterRef

from settle.models import DebitAdjustDetail
from settle.serializers.debitAdjust import DebitAdjustDetailSerializer


class OceanOrderSerializer(serializers.ModelSerializer):
    """
    角色序列化
    """
    supplier_name = serializers.CharField(source='supplier.short_name', required=False)

    class Meta:
        model = OceanOrder
        fields = '__all__'


class OrderLabelTaskSerializer(serializers.ModelSerializer):
    """
    面单任务序列化
    """

    class Meta:
        model = OrderLabelTask

        fields = '__all__'


class OrderSyncTaskSerializer(serializers.ModelSerializer):
    """
    订单同步任务序列化
    """

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = OrderSyncTask
        fields = '__all__'


class OrderAsyncTaskSerializer(serializers.ModelSerializer):
    """
    订单异步任务序列化
    """

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = OrderAsyncTask
        fields = '__all__'


class OrderFieldsChangeLogsSerializer(serializers.ModelSerializer):
    """
    订单字段修改日志
    """
    username = serializers.CharField(source='update_by.name', required=False,
                                     read_only=True)
    old_value_display = serializers.SerializerMethodField(required=False, read_only=True)
    new_value_display = serializers.SerializerMethodField(required=False, read_only=True)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = OrderFieldChangeLog
        fields = '__all__'

    def get_enum_display_value(self, field_name, field_value):
        """
        获取枚举字段的中文显示值
        """
        if not field_value:
            return field_value
            
        # CustomerOrder 模型的枚举字段映射
        enum_field_choices = {
            'order_status': CustomerOrder.ORDER_STATUS,
            'order_type': CustomerOrder.ORDER_TYPE,
            'transport_type': CustomerOrder.TRANSPORT_TYPE,
            'carriage_type': CustomerOrder.CARRIAGE_TYPE,
            'transport_status': CustomerOrder.TRANSPORT_STATUS,
        }
        
        # 检查是否为枚举字段
        if field_name in enum_field_choices:
            choices_dict = dict(enum_field_choices[field_name])
            return choices_dict.get(field_value, field_value)
        
        return field_value

    def get_old_value_display(self, obj):
        """
        获取修改前字段值的中文显示
        """
        return self.get_enum_display_value(obj.field_name, obj.old_value)

    def get_new_value_display(self, obj):
        """
        获取修改后字段值的中文显示
        """
        return self.get_enum_display_value(obj.field_name, obj.new_value)


class CustomerOderAbnormalInfoSerializer(serializers.ModelSerializer):
    """
    订单异常标签
    """
    username = serializers.CharField(source='update_by.name', required=False,
                                     read_only=True)
    abnormal_tag_name = serializers.SerializerMethodField()

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = CustomerOrderAbnormal
        fields = '__all__'
    def get_abnormal_tag_name(self, obj):
        return ','.join([tag.type_name for tag in obj.abnormal_tag.all()])


class ClearanceOutSerializer(serializers.ModelSerializer):
    master_number = serializers.CharField(source='master_order_id.order_num', required=False)
    ocean_number = serializers.CharField(source='ocean_order_id.order_num', required=False)

    class Meta:
        model = ClearanceOut
        fields = '__all__'


class CustomerOrderSerializer(serializers.ModelSerializer):
    """
    客户订单序列化
    """
    master_num_name = serializers.CharField(source='master_num.order_num', required=False)
    # 优先轨迹海运提单
    first_ocean_num = serializers.CharField(source='ocean_num.order_num', required=False)
    # 普通海运提单
    ocean_num_name = serializers.SerializerMethodField(required=False, allow_null=True)
    truck_order_num = serializers.SerializerMethodField(required=False, allow_null=True)
    container_no = serializers.SerializerMethodField(required=False, allow_null=True)
    collect_number = serializers.CharField(source='collect_num.order_num', required=False)
    house_num_name = serializers.CharField(source='house_num.order_num', required=False)
    truck_order_id_name = serializers.CharField(source='truck_order_id.truck_order_num', required=False)
    combine_billing_order_num = serializers.CharField(source='combine_billing_order.order_num', required=False)
    product_name = serializers.CharField(source='product.name', required=False)
    # product_code_code = serializers.CharField(source='product.code', required=False)
    clearance_num = serializers.CharField(source='clearance_out.clearance_num', required=False)
    create_by = serializers.CharField(source='create_by.name', required=False)
    saler_name = serializers.CharField(source='customer.saler_name', required=False, read_only=True)
    customer_name = serializers.CharField(source='customer.short_name', required=False)
    shipper_name = serializers.CharField(source='shipper.address_num', required=False)
    # todo_p: 页面上修改收件人选项, 收件人地址也会跟随变化, 为什么收件人地址没有跟随receiver_name一起变化, 导致两个数据不一致 ?
    # 仓库代码
    # receiver_name = serializers.CharField(source='receiver.address_num', required=False)
    receiver_name = serializers.SerializerMethodField()
    ocean_warehouse = serializers.CharField(source='ocean_warehouse.address_num', required=False)
    orderLabelTasks = OrderLabelTaskSerializer(many=True, required=False)
    is_sync_yqf = serializers.SerializerMethodField(required=False, read_only=True)
    # declared_name_cn = serializers.SerializerMethodField(required=False, allow_null=True)

    # vessel = serializers.SerializerMethodField(required=False, allow_null=True)
    # voyage_num = serializers.SerializerMethodField(required=False, allow_null=True)
    # container_type = serializers.SerializerMethodField(required=False, allow_null=True)
    # airline_short_name = serializers.SerializerMethodField(required=False, allow_null=True)
    # actual_loading_time = serializers.SerializerMethodField(required=False, allow_null=True)
    # estimated_time_departure = serializers.SerializerMethodField(required=False, allow_null=True)
    # estimated_time_arrival = serializers.SerializerMethodField(required=False, allow_null=True)
    logistics_planning_name = serializers.CharField(source='logistics_planning.planning_code', required=False,
                                                    read_only=True)
    parcel_item_value = serializers.SerializerMethodField(required=False, read_only=True)
    parcel_item_value_unit = serializers.SerializerMethodField(required=False, read_only=True)
    delivery_address_type = serializers.SerializerMethodField(required=False, read_only=True)

    class Meta:
        model = CustomerOrder
        fields = '__all__'

    def get_delivery_address_type(self, obj):
        if isinstance(obj, OrderedDict):
            return

        if obj.receiver:
            return obj.receiver.get_delivery_address_type_display()
        return None

    def get_parcel_item_value_old(self, obj):
        """
        判断订单下所有包裹的申报金额 * 数量总和是否 > 2300,大于为高货值
        :param obj: CustomerOrder 实例
        :return: '是' 或 '否'
        """
        if isinstance(obj, OrderedDict):
            return
        total_value = Decimal('0.00')  # 数量*价格累加和
        compare_value = Decimal('2300.00')

        # 获取订单下的所有包裹
        parcels = obj.parcel.filter(del_flag=False) if hasattr(obj, 'parcel') and obj.parcel.exists() else []

        for parcel in parcels:
            # 获取包裹下的所有物品
            items = parcel.parcelItem.filter(del_flag=False) if \
                hasattr(parcel, 'parcelItem') and parcel.parcelItem.exists() else []
            for item in items:
                declared_price = item.declared_price or Decimal('0.00')
                item_qty = item.item_qty or 0
                total_value += declared_price * item_qty

        # print('------------obj.pre_volume', obj.pre_volume)  # 总体积
        # print('------------obj.volume', obj.volume)  # 入仓总体积
        calculated_volume = obj.volume or obj.pre_volume

        # if calculated_volume and total_value / calculated_volume > compare_value:
        #     return '是'
        # else:
        #     return '否'
        return calculated_volume and (total_value / calculated_volume).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)

    def get_parcel_item_value(self, obj):
        if isinstance(obj, OrderedDict):
            return
        parcels = Parcel.objects.filter(customer_order=obj, del_flag=False)
        parcel_items = ParcelItem.objects.filter(parcel_num__in=parcels, del_flag=False)
        total_value = 0
        for parcel_item in parcel_items:
            total_value += (parcel_item.declared_price or 0) * (parcel_item.item_qty or 0)
        return total_value

    def get_parcel_item_value_unit(self, obj):
        if isinstance(obj, OrderedDict):
            return
        parcels = Parcel.objects.filter(customer_order=obj, del_flag=False)
        parcel_items = ParcelItem.objects.filter(parcel_num__in=parcels, del_flag=False)
        total_value = 0
        for parcel_item in parcel_items:
            total_value += (parcel_item.declared_price or 0) * (parcel_item.item_qty or 0)
        calculated_volume = obj.volume or obj.pre_volume
        return calculated_volume and (total_value / calculated_volume).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)

    def get_ocean_num_name(self, obj):
        relate_ocean_nums = CustomerOrderRelateOcean.objects.filter(
            customer_order_num=obj.id,
            del_flag=False
        ).values_list('oceanOrder__order_num', flat=True)
        return ','.join(filter(None, relate_ocean_nums))

    def get_truck_order_num(self, obj):
        truck_order_nums = CustomerOrderRelateTruck.objects.filter(
            customer_order_num=obj.id,
            del_flag=False
        ).values_list('truck_order__truck_order_num', flat=True)
        return ','.join(filter(None, truck_order_nums))

    def get_container_no(self, obj):
        relate_ocean_nums = CustomerOrderRelateOcean.objects.filter(
            customer_order_num=obj.id,
            del_flag=False
        ).values_list('oceanOrder__container_no', flat=True)
        return ','.join(filter(None, relate_ocean_nums))

    def get_is_sync_yqf(self, obj):
        if isinstance(obj, CustomerOrder):
            sync_status = OrderSyncTask.objects.filter(order_num=obj, task_type='PUSH_ORDER', del_flag=False).last()
            return sync_status.status if sync_status else None

    def get_receiver_name(self, obj: CustomerOrder):
        if obj.receiver:
            return obj.receiver.address_num
        else:
            return obj.buyer_address_num


class OrderLabelSerializer(serializers.ManyRelatedField):
    """
    面单序列化
    """

    class Meta:
        model = OrderLabel
        fields = '__all__'


class CustomerOrderRelateOceanSerializer(serializers.ModelSerializer):
    """
    关联海运单
    """

    oceanOrder_num = serializers.CharField(source='oceanOrder.order_num', required=False)

    class Meta:
        model = CustomerOrderRelateOcean
        fields = '__all__'


class CustomerOrderChargeInSerializer(serializers.ModelSerializer):
    """
    收入明细序列化
    """
    charge_name = serializers.CharField(source='charge.name', required=False)
    customer_name = serializers.CharField(source='customer.short_name', required=False, read_only=True)
    id = serializers.IntegerField(required=False, write_only=False)

    # 这个会导致莫名其妙的报错
    # company.models.Company.MultipleObjectsReturned: get() returned more than one Company -- it returned 2!
    # 当 客户管理中公司简称重复时, 会报错, 例如fbm业务 {'short_name': '济泰实业'} 就会报错
    # customer_code = serializers.SlugRelatedField(slug_field="short_name", queryset=Company.objects.all(),
    #                                              source="customer", required=False, allow_null=True)
    #
    # charge_code = serializers.SlugRelatedField(slug_field="code", queryset=Charge.objects.all(), source="charge",
    #                                            required=False, allow_null=True)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = CustomerOrderChargeIn
        fields = '__all__'


class CustomerOrderChargeOutSerializer(serializers.ModelSerializer):
    """
    成本明细序列化
    """
    charge_name = serializers.CharField(source='charge.name', required=False)
    supplier_name = serializers.CharField(source='supplier.name', required=False, read_only=True)
    id = serializers.IntegerField(required=False, write_only=False)

    # 如果 Company 的 short_name 重复, 加这个字段会在反序列化的时候报错:
    # company.models.Company.MultipleObjectsReturned: get() returned more than one Company -- it returned 2!
    # supplier_code = serializers.SlugRelatedField(slug_field="short_name", queryset=Company.objects.all(),
    #                                              source="supplier", required=False, allow_null=True)
    # charge_code = serializers.SlugRelatedField(slug_field="code", queryset=Charge.objects.all(),
    #                                            source="charge", required=False, allow_null=True)

    class Meta:
        list_serializer_class = CustomerRolesRestrictSerializer
        model = CustomerOrderChargeOut
        fields = '__all__'

    # def to_representation(self, data):
    #     print('value-->', data, type(data))
    #     return super(CustomerOrderChargeOutSerializer, self).to_representation(data)
    #     user_roles = self.context['request'].user.roles.values_list('id', flat=True)
    #     print('user_roles-->', user_roles, data.charge.roles)
    #     if data.charge.roles in user_roles and not data.del_flag:
    #         return CustomerOrderChargeOutSerializer(data, many=True).data
    #     else:
    #         return super(CustomerOrderChargeOutSerializer, self).to_representation(data)


class CustomerOrderChargeOutSerializerCommon(serializers.ModelSerializer):
    """
    成本明细序列化
    """
    charge_name = serializers.CharField(source='charge.name', required=False)
    supplier_name = serializers.CharField(source='supplier.name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)

    # supplier_code = serializers.SlugRelatedField(slug_field="short_name", queryset=Company.objects.all(),
    #                                              source="supplier", required=False, allow_null=True)
    # charge_code = serializers.SlugRelatedField(slug_field="code", queryset=Charge.objects.all(),
    #                                            source="charge", required=False, allow_null=True)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = CustomerOrderChargeOut
        fields = '__all__'


class ParcelItemSerializer(serializers.ModelSerializer):
    """
    包裹序列化
    """
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ParcelItem
        fields = '__all__'


class ParcelListSerializer(ListSerializer):

    def to_representation(self, data):
        if hasattr(data, 'filter'):
            data = data.filter(del_flag=False).order_by('-is_overlength', '-is_overweight', 'id')
        return super().to_representation(data)


class ParcelAttachmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ParcelAttachment
        fields = '__all__'


class ParcelSerializer(serializers.ModelSerializer):
    """
    包裹序列化
    """
    id = serializers.IntegerField(required=False, write_only=False)
    parcelItem = ParcelItemSerializer(many=True, required=False)
    img_url = serializers.SerializerMethodField(required=False, read_only=True)
    last_track = serializers.SerializerMethodField()
    attachment_parcel = ParcelAttachmentSerializer(many=True, required=False, read_only=True)
    ocean_order_num = serializers.CharField(source='ocean_order.order_num', required=False)

    class Meta:
        # list_serializer_class = DeletedFilterListSerializer
        list_serializer_class = ParcelListSerializer
        model = Parcel
        fields = '__all__'

    def get_img_url(self, obj):
        if 'check_num' in model_to_dict(obj).keys() and obj.check_num:
            file_name = obj.check_num
            if obj.check_times > 0:
                file_name = obj.check_num + ' (' + str(obj.check_times) + ')'
            file_url = obj.check_date + '/' + file_name + '.jpg'
            return get_sign_url_from_oss(file_url)

    def get_last_track(self, obj):
        if hasattr(obj, 'parcel_num'):
            track_queryset = CustomerParcelTrack.objects.filter(~Q(track_code='PL'), parcel_num=obj.parcel_num,
                                                                del_flag=False).order_by('-actual_time').first()
            # return CustomerParcelTrackSerializer(track_queryset).data
            if track_queryset:
                return track_queryset.track_name if track_queryset.track_name else track_queryset.remark


class ParcelSizeSerializer(serializers.ModelSerializer):
    """
    包裹序列化
    """
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = ParcelSize
        fields = '__all__'


class CustomerOrderAndDetatilSerializer(BaseModelSerializer):
    """
    客户订单与明细
    """
    master_num_name = serializers.CharField(source='master_num.order_num', required=False)
    first_ocean_num = serializers.CharField(source='ocean_num.order_num', required=False)
    ocean_num_name = serializers.SerializerMethodField(required=False, allow_null=True, read_only=True)
    truck_order_num = serializers.SerializerMethodField(required=False, allow_null=True, read_only=True)
    # vessel = serializers.CharField(source='ocean_num.vessel', required=False, read_only=True)
    # voyage_num = serializers.CharField(source='ocean_num.voyage_num', required=False, read_only=True)
    house_num_name = serializers.CharField(source='house_num.order_num', required=False)
    truck_order_id_name = serializers.CharField(source='truck_order_id.truck_order_num', required=False, read_only=True)
    # 卡派pod文件
    # truck_order_pod = serializers.CharField(source='truck_order_id.transport_file', required=False, read_only=True)
    truck_order_pod = serializers.SerializerMethodField(required=False, read_only=True)
    product_name = serializers.CharField(source='product.name', required=False)
    product_code_code = serializers.CharField(source='product.code', required=False)
    customer_name = serializers.CharField(source='customer.short_name', required=False, read_only=True)
    shipper_name = serializers.CharField(source='shipper.address_num', required=False)
    receiver_name = serializers.CharField(source='receiver.address_num', required=False)
    ocean_warehouse_name = serializers.CharField(source='ocean_warehouse.address_num', required=False)
    contact = serializers.CharField(source='customer.contact_name', required=False, read_only=True)
    saler = serializers.CharField(source='customer.saler_name', required=False, read_only=True)
    inquiry_num = serializers.CharField(source='truck_inquiry.inquiry_num', required=False, read_only=True)
    inquiry_num_id = serializers.CharField(source='truck_inquiry.id', required=False, read_only=True)
    # 简易订单中，客户订单号可为空
    # ref_num = serializers.CharField(allow_null=True, required=False)

    # 用户在使用的时，通过主键来关联
    create_by = PrimaryKeyRelatedField(queryset=UserProfile.objects.all(), required=False)
    update_by = PrimaryKeyRelatedField(queryset=UserProfile.objects.all(), required=False)
    # 继承类的属性需要特别定义
    del_flag = serializers.BooleanField(default=False)
    customerOrderChargeIns = CustomerOrderChargeInSerializer(many=True, required=False)
    # customerOrderChargeOuts = serializers.SerializerMethodField(required=False, allow_null=True)
    customerOrderChargeOuts = CustomerOrderChargeOutSerializer(many=True, required=False)
    # customer_parcel = BigParcelSerializer(many=True, required=False)
    customer_parcel_outbound = ParcelOutboundOrderSerializer(many=True, required=False)
    # 完整包裹商品
    # parcel = serializers.SerializerMethodField(required=False)
    parcel = ParcelSerializer(many=True, required=False)
    # 简易包裹
    parcel_size = ParcelSizeSerializer(many=True, required=False)
    id = serializers.IntegerField(required=False, write_only=False)
    # 附件
    attachments = AttachmentSerializer(many=True, required=False, read_only=True)
    signForAttachments = SignForAttachmentSerializer(many=True, required=False)
    orderLabelTasks = OrderLabelTaskSerializer(many=True, required=False)
    orderSyncTasks = OrderSyncTaskSerializer(many=True, required=False, read_only=True)
    orderAsyncTask = serializers.SerializerMethodField(required=False, read_only=True)
    service_name = serializers.CharField(source='service.name', required=False)
    # insurance = serializers.SerializerMethodField(required=False, read_only=True)
    tracks = serializers.SerializerMethodField(required=False, read_only=True)
    shipments = serializers.SerializerMethodField(required=False, read_only=True)

    # 出口报关单序列化: 空运主单/海运提单
    masterOrderOut = serializers.SerializerMethodField(required=False, allow_null=True)
    oceanOrderOut = serializers.SerializerMethodField(required=False, allow_null=True)

    customerOrderRelateList = serializers.SerializerMethodField(required=False, read_only=True)
    # 收入调整明细
    relative_debitAdjustDetails = serializers.SerializerMethodField(required=False, read_only=True)
    is_sync_yqf = serializers.SerializerMethodField(required=False, read_only=True)
    # pod_file = serializers.FileField(read_only=True)
    logistics_planning_name = serializers.CharField(source='logistics_planning.planning_code', required=False,
                                                    read_only=True)
    fields_change_logs = serializers.SerializerMethodField(required=False, read_only=True)
    parcel_item_value = serializers.SerializerMethodField(required=False, read_only=True)  # 高货值
    order_abnormal_tags = serializers.SerializerMethodField(required=False, read_only=True)

    class Meta:
        model = CustomerOrder
        fields = '__all__'

    def get_order_abnormal_tags(self, obj: CustomerOrder):
        if hasattr(obj, 'id'):
            customer_order_abnormal = CustomerOrderAbnormal.objects.filter(customer_order_num=obj,
                                                                           del_flag=False).order_by('-id')
            return CustomerOderAbnormalInfoSerializer(customer_order_abnormal, many=True).data

    def get_parcel_item_value(self, obj: CustomerOrder):
        """
        判断订单下所有包裹的申报金额 * 数量总和是否 > 2300, 大于为高货值
        :param obj: CustomerOrder 实例
        :return: '是' 或 '否'
        """
        if isinstance(obj, OrderedDict):
            return
        total_value = Decimal('0.00')  # 数量*价格累加和
        compare_value = Decimal('2300.00')

        # 获取订单下的所有包裹
        parcels = obj.parcel.filter(del_flag=False) if hasattr(obj, 'parcel') and obj.parcel.exists() else []

        for parcel in parcels:
            # 获取包裹下的所有物品
            items = parcel.parcelItem.filter(del_flag=False) \
                if hasattr(parcel, 'parcelItem') and parcel.parcelItem.exists() else []
            for item in items:
                declared_price = item.declared_price or Decimal('0.00')
                item_qty = item.item_qty or 0
                total_value += declared_price * item_qty

        # print('------------obj.pre_volume', obj.pre_volume)  # 总体积
        # print('------------obj.volume', obj.volume)  # 入仓总体积
        calculated_volume = obj.volume if obj.volume else obj.pre_volume

        # if calculated_volume and total_value / calculated_volume > compare_value:
        #     return '是'
        # else:
        #     return '否'
        return calculated_volume and (total_value / calculated_volume).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)

    def get_truck_order_pod(self, obj):
        # 兼容订单还未创建时 obj为collections.OrderedDict的情况
        if isinstance(obj, OrderedDict):
            return
        if hasattr(obj, 'pod_file') and obj.pod_file:
            request_url = self.context['request'].build_absolute_uri()
            return request_url + settings.STATIC_MEDIA_DIR.replace(settings.APP_DIR + '/', '', 1) + obj.pod_file.name
        # elif hasattr(obj, 'truck_order_id') and obj.truck_order_id:
        relate_trucks = CustomerOrderRelateTruck.objects.filter(customer_order_num=obj, del_flag=False)
        if relate_trucks.exists():
            truck_orders = TruckOrder.objects.filter(id__in=relate_trucks.values_list('truck_order', flat=True),
                                                     del_flag=False)
            from order.serializers.truck_order import TruckOrderSerializer
            for truck_order in truck_orders:
                # truck_order = obj.truck_order_id
                data = TruckOrderSerializer(truck_order, required=False).data
                # print('data-->', data['transport_file'], obj.truck_order_id.transport_file)
                if data and data['transport_file']:
                    file_path = data['transport_file']
                    # 原生字段返回时会自动拼接访问的路径, 所以这里也需要做拼接, 返回给前端保持一致性, 避免前端对路径做处理出现异常, 无法读取文件
                    request_url = self.context['request'].build_absolute_uri()
                    # print('url_path2-->', request_url + file_path)
                    return request_url + file_path

    # def get_parcel(self, obj):
    #     parcels = Parcel.objects.filter(customer_order=obj, del_flag=False).order_by('-is_overlength', '-is_overweight')
    #     serializer = ParcelSerializer(parcels, many=True)
    #     return serializer.data

    def get_customerOrderRelateList(self, obj):
        if hasattr(obj, 'id'):
            relate_ocean_queryset = CustomerOrderRelateOcean.objects.filter(
                del_flag=False, customer_order_num=obj.id
            ).values(
                'id', 'oceanOrder', 'oceanOrder__order_num', 'out_warehouse_num', 'freight_num',
                'allocate_weight', 'allocate_volume'
            )
            data = [
                {
                    'id': relate_ocean['id'],
                    'oceanOrder': relate_ocean['oceanOrder'],
                    'oceanOrder_num': relate_ocean['oceanOrder__order_num'],
                    'out_warehouse_num': relate_ocean['out_warehouse_num'],
                    'freight_num': relate_ocean['freight_num'],
                    'allocate_weight': relate_ocean['allocate_weight'],
                    'allocate_volume': relate_ocean['allocate_volume']
                } for relate_ocean in relate_ocean_queryset
            ]
            return data
        return []

    def get_tracks(self, obj):
        if hasattr(obj, 'order_num'):
            track_queryset = Track.objects.filter(~Q(track_code__in=CUSTOMER_ORDER_EXCEPT_TRACK), del_flag=False,
                                                  order_id=obj.id).order_by('-actual_time')
            return TrackSerializer(track_queryset, many=True).data

    def get_shipments(self, obj):
        if hasattr(obj, 'order_num'):
            shipment_queryset = OcShipment.objects.filter(del_flag=False, customer_order_num=obj)
            return OcShipmentSerializer(shipment_queryset, many=True).data

    def get_masterOrderOut(self, obj):
        if hasattr(obj, 'master_num') and hasattr(obj.master_num, 'master_order'):
            return ClearanceOutSerializer(obj.master_num.master_order.filter(del_flag=False).order_by('-id'),
                                          many=True).data
        # 若return None则会保留在返回字段中, 即'masterOrderOut': null, 前端添加元素时会把masterOrderOut作为数组往里面加值, 会引起报错
        return []

    def get_oceanOrderOut(self, obj):
        if hasattr(obj, 'ocean_num') and hasattr(obj.ocean_num, 'ocean_order'):
            return ClearanceOutSerializer(obj.ocean_num.ocean_order.filter(del_flag=False).order_by('-id'),
                                          many=True).data
        return []

    def get_is_sync_yqf(self, obj):
        if isinstance(obj, CustomerOrder):
            sync_status = OrderSyncTask.objects.filter(order_num=obj, task_type='PUSH_ORDER', del_flag=False).last()
            return sync_status.status if sync_status else None

    # def get_customerOrderChargeOuts(self, obj):
    #     # 这里写获取customerOrderChargeOuts相关数据的逻辑，并返回
    #     # 假设你是想要从obj中获取相关的数据，确保你的obj有这个属性或者相关的方法
    #     # TODO 所以必须用hasattr 判断
    #     if hasattr(obj, 'customerOrderChargeOuts'):
    #         # 对成本明细进行筛选, 只有属于成本配置的角色才能看到成本
    #         filtered_data = obj.customerOrderChargeOuts.filter(
    #             charge__roles__in=self.context['request'].user.roles.values_list('id', flat=True))
    #         return CustomerOrderChargeOutSerializer(filtered_data, many=True).data
    #     return []

    # def common_get_ocean_method(self, obj, field):
    #     relate_ocean_queryset = CustomerOrderRelateOcean.objects.filter(customer_order_num=obj.id, del_flag=False)
    #     if relate_ocean_queryset:
    #         relate_ocean_list = OceanOrderSerializer(OceanOrder.objects.filter(
    #             id__in=list(relate_ocean_queryset.values_list('oceanOrder', flat=True)),
    #             del_flag=False).order_by('-id'), many=True).data
    #         if relate_ocean_list:
    #             return relate_ocean_list[0].get(field)

    def get_ocean_num_name(self, obj):
        if hasattr(obj, 'id'):
            relate_ocean_nums = CustomerOrderRelateOcean.objects.filter(
                customer_order_num=obj.id,
                del_flag=False
            ).values_list('oceanOrder__order_num', flat=True)
            return ','.join(filter(None, relate_ocean_nums))
        else:
            return []

    def get_truck_order_num(self, obj):
        if hasattr(obj, 'id'):
            truck_order_nums = CustomerOrderRelateTruck.objects.filter(
                customer_order_num=obj.id,
                del_flag=False
            ).values_list('truck_order__truck_order_num', flat=True)
            return ','.join(filter(None, truck_order_nums))
        else:
            return []

    # def get_voyage_num(self, obj):
    #     # 有优先轨迹的数据就返回优先轨迹数据
    #     if obj.ocean_num and obj.ocean_num.voyage_num:
    #         return obj.ocean_num.voyage_num
    #     elif obj.voyage_num:
    #         return obj.voyage_num
    #     return self.common_get_ocean_method(obj, 'voyage_num')

    def get_relative_debitAdjustDetails(self, obj):
        if hasattr(obj, 'order_num'):
            debit_adjust_detail = DebitAdjustDetail.objects.filter(order_num=obj.order_num,
                                                                   del_flag=False).order_by('-id')
            return DebitAdjustDetailSerializer(debit_adjust_detail, many=True).data

    def get_orderAsyncTask(self, obj):
        if hasattr(obj, 'order_num'):
            tasks = OrderAsyncTask.objects.filter(order_num=obj.order_num,
                                                  del_flag=False).order_by('-id')
            return OrderAsyncTaskSerializer(tasks, many=True).data

    def get_fields_change_logs(self, obj):
        if hasattr(obj, 'id'):
            logs = OrderFieldChangeLog.objects.filter(order_type='FBA', model_instance_id=obj.id,
                                                      del_flag=False).order_by('-id')
            return OrderFieldsChangeLogsSerializer(logs, many=True).data

    @transaction.atomic
    def create(self, validated_data):
        validated_data.pop('charge_weight', None)
        customer_order_charge_in_list_data = validated_data.pop('customerOrderChargeIns', [])
        customer_order_charge_out_list_data = validated_data.pop('customerOrderChargeOuts', [])
        # customer_order_charge_out_list_data = self.context['request'].data.get('customerOrderChargeOuts', [])
        request = self.context['request']
        parcel_item_data = request.data.get('parcelItem', None)
        parce_type = validated_data.get('parceType', True)
        validated_data['service'] = get_service_from_product(validated_data.get('product', None))
        user = request.user
        parcel_size_list = validated_data.pop('parcel_size', None)

        # validated_data.pop('ref_num', None)
        ref_num = request.data.get('ref_num', None)
        old_order = CustomerOrder.objects.filter(~Q(order_status='VO'), ref_num=ref_num, del_flag=False)
        if old_order.count() > 0 and ref_num:
            raise ParamError('客户订单号' + ref_num + '不能重复', ErrorCode.PARAM_ERROR)

        # 卡派询价单
        truck_inquiry = request.data.get('truck_inquiry', None)
        customer = request.data.get('customer', None)
        customer = Company.objects.filter(id=customer).first()
        if not customer:
            raise ParamError('请选择正确的客户', ErrorCode.PARAM_ERROR)
        if truck_inquiry:
            if customer_order_charge_in_list_data:
                raise ParamError('已关联卡派询价单不能单独添加收入费用明细，以询价单为准', ErrorCode.PARAM_ERROR)
            customer_order_charge_in_list_data = process_truck_inquiry(truck_inquiry, customer)

        ocean_num = validated_data.pop('ocean_num', None)

        customer_order = CustomerOrder.objects.create(**validated_data)

        order_status = request.data.get('order_status', None)
        if not order_status or order_status != 'DR':
            change_order_status(customer_order, 'PDC', user)
        # 判断是否有空运主单号或者海运提单号，有则写入主单外键字段
        # master_number = customer_order.master_number.strip() if customer_order.master_number else None
        # if master_number:
        #     try:
        #         master_order = MasterOrder.objects.get(order_num=master_number, del_flag=False)
        #         customer_order.master_num = master_order
        #     except MasterOrder.DoesNotExist:
        #         pass
        # ocean_number = customer_order.ocean_number.strip() if customer_order.ocean_number else None
        # if ocean_number:
        #     try:
        #         master_order = OceanOrder.objects.get(order_num=ocean_number, del_flag=False)
        #         customer_order.ocean_num = master_order
        #     except OceanOrder.DoesNotExist:
        #         pass

        # for order_out in ['masterOrderOut', 'oceanOrderOut']:
        #     main_order = self.collect_out_order(customer_order, order_out)
        #     if order_out == 'oceanOrderOut' and main_order is not None:
        #         # 新增时添加船名和航次
        #         main_order.vessel = ocean_num.get('vessel')
        #         main_order.voyage_num = ocean_num.get('voyage_num')

        if not user.is_staff:
            # 该用户是客户 直接将客户字段设置为该客户
            customer_order.customer = user.company
            customer_order.save()

        # 兼容传code
        customer_code = request.data.get('customer_code', None)
        if customer_code is not (None or ''):
            customer_list = Company.objects.filter(short_name=customer_code, del_flag=False)
            if customer_list.count() > 0:
                customer = customer_list[0]
                customer_order.customer = customer
                customer_order.saler = customer.saler_name

        self.check_customer_status(customer)

        # 兼容不传productId
        service_code = request.data.get('service_code', None)
        if service_code:
            service_list = Service.objects.filter(code=service_code, del_flag=False)
            if service_list.count() > 0:
                # TODO 服务可能多个
                service = service_list.first()
                customer_order.product = service.product
                customer_order.service = service
            else:
                send_dingding(settings.SYSTEM_ORDER_MARK, 'order:' + str(service_code) +
                              ",not product service ,please check!")
                raise ParamError('订单' + str(service_code) + '无产品服务，请添加产品服务', ErrorCode.PARAM_ERROR)

        # 默认服务
        product = customer_order.product
        if product:
            check_order_data(validated_data, product)
        if product and not customer_order.service:
            service_list = Service.objects.filter(product=product, del_flag=False, is_default=True)
            if service_list.count() > 0:
                service = service_list.first()
                customer_order.service = service
            else:
                raise ParamError('产品' + str(product.name) + '无默认服务，请添加产品服务', ErrorCode.PARAM_ERROR)

        # 如果没有发件人 默认用产品
        if product and product.address_num and not customer_order.shipper:
            address_queryset = Address.objects.filter(address_num=product.address_num, del_flag=False)
            if address_queryset.count() > 0:
                address = address_queryset.first()
                save_shiper_address(address, customer_order)

        is_b_async = request.data.get('is_b_async', False)
        is_r_async = request.data.get('is_r_async', False)
        if is_r_async:
            # 需要把地址同步过地址库
            save_address(customer_order, True)
        if is_b_async:
            # 需要把地址同步过地址库
            save_address(customer_order, False)

        # result = check_purpose_code_is_remote(product, customer_order.receiver)
        # if result:
        #     customer_order.is_remote = True
        #     customer_order.is_intercept = True
        customer_order.save()

        create_or_update_parcel_sizes(customer_order, parcel_size_list)

        return handler_create_order(customer_order, customer_order_charge_in_list_data,
                                    customer_order_charge_out_list_data, parce_type, parcel_item_data, user)

    @transaction.atomic
    def update(self, instance: CustomerOrder, validated_data):
        # print('validated_data000-->', validated_data)
        # validated_data.pop('pod_file')
        # order_status = self.context['request'].data.get('order_status')
        # if order_status not in ['DR', 'WO', 'PDC', 'ITP', 'PW', 'AW', 'CWED']:
        # if order_status in ['FC', 'VO']:
        if instance.order_status in ['VO']:
            raise ParamError(f'作废订单不可编辑', ErrorCode.PARAM_ERROR)

        customer = validated_data.get('customer', None)
        if customer:
            self.check_customer_status(customer)

        validated_data['service'] = get_service_from_product(validated_data.get('product', None))

        # 有转单号 ==> 都不能改服务、产品、包裹、商品、收件人和发件人
        if instance.tracking_num:
            validated_data.pop('service', None)
            validated_data.pop('product', None)

        # 是否拦截不在详情页中编辑
        validated_data.pop('is_intercept', None)
        # 大包单
        validated_data.pop('customer_parcel', [])
        # parce_type = validated_data.get('parceType', True)
        validated_data.pop('orderLabelTasks', [])
        # validated_data.pop('orderSyncTasks', [])
        parcel_size_list = validated_data.pop('parcel_size', None)
        validated_data.pop('tracks', [])
        validated_data.pop('insurance', {})
        validated_data.pop('third_orderNo', None)
        validated_data.pop('customer_parcel_outbound', None)
        validated_data.pop('carton', None)
        validated_data.pop('charge_weight', None)

        user = self.context['request'].user
        # 修改订单费用
        # 因为编辑订单涉及到包裹和商品, 后端给前端的数据和前端提交到后端的数据需要做转换, 这里针对费用录入则只更新费用
        only_edit_charge = self.context['request'].data.get('onlyEditCharge')
        if only_edit_charge:
            modify_customer_order_charge(self.context['request'], instance, validated_data)
            return instance
        else:
            modify_customer_order_charge(self.context['request'], instance, validated_data)

        if settings.SYSTEM_ORDER_MARK == 'CLT' and instance.order_status in ['AR', 'CC', 'IWW', 'OOD', 'PSF', 'FC',
                                                                             'SF']:
            if validated_data.get('confirm_volume') != instance.confirm_volume:
                raise ParamError('订单已离港, 不允许修改确认计费体积', ErrorCode.PARAM_ERROR)

        parcel_item_data = self.context['request'].data.get('parcelItem', [])

        # if settings.SYSTEM_ORDER_MARK in ['CLT']:
        if instance.order_type == 'FBM':
            customer_order_fbm_schema = CustomerOrderFbmExcelSchema()
            order_data = FbmOrderImportService.build_fbm_parcel_info(parcel_item_data, customer_order_fbm_schema)
            FbmOrderImportService().update_fbm_order(instance, order_data, user, keep_shipment_id=True)
        else:
            fba_parcel_and_item_handle(instance, validated_data, parcel_item_data, user)

        # 有转单号，不允许修改地址信息(2024-12-16 转单号不是我们主动抓取的, 所以允许修改这些信息)

        OrderFieldChangeLog.record(instance, validated_data, 'FBA', user)

        print('海外仓是啥-->', validated_data.get('ocean_warehouse'), type(validated_data.get('ocean_warehouse')))
        print('收件人是啥-->', validated_data.get('shipper'), type(validated_data.get('shipper')))

        # 详情页修改备注(壹起飞同步铭志)
        origin_remark = instance.remark
        new_remark = validated_data.get('remark', instance.remark)
        if new_remark != origin_remark:
            # sync_modify_remark_to_mz.delay(instance.order_num, new_remark)
            sync_modify_remark_to_mz.apply_async(
                args=[instance.order_num, new_remark],
                queue=f'label_queue_1',
                routing_key=f'label_queue_1'
            )

        # 更新主记录的所有字段属性
        for field in validated_data:
            if field != 'id':
                # print('修改了哪些字段-->', field)
                # setattr(instance, field, validated_data.get(field, getattr(instance, field)))
                instance.save_fields(**{field: validated_data.get(field, getattr(instance, field))})
        # setattr之后还需要save进行保存
        # instance.save(update_fields=update_fields)

        # 校验修改后的产品
        product = instance.product
        if product:
            check_order_data(validated_data, product)

        # 如果修改了客户, 那么也修改客户对应的销售负责人
        customer = validated_data.pop('customer', None)
        if customer:
            instance.save_fields(saler=customer.saler_name)

        # 暂时不计价
        # # 是否收入计价
        # if instance.product and instance.product.is_valuation:
        #     CustomerOrderChargeIn.objects.filter(customer_order_num=instance, del_flag=False, is_system=True).update(del_flag=True)
        #     add_revenue(instance, user, CustomerOrderChargeIn)
        #
        # # 是否成本计价
        # if instance.product and instance.product.is_cost_valuation:
        #     CustomerOrderChargeOut.objects.filter(customer_order_num=instance, del_flag=False, is_system=True).update(del_flag=True)
        #     add_cost(instance, user, CustomerOrderChargeOut)

        # 判断是否有空运主单号或者海运提单号，有则写入主单外键字段
        # master_number = instance.master_number.strip() if instance.master_number else None
        # if master_number:
        #     try:
        #         master_order = MasterOrder.objects.get(order_num=master_number, del_flag=False)
        #         instance.master_num = master_order
        #     except MasterOrder.DoesNotExist:
        #         pass
        # ocean_number = instance.ocean_number.strip() if instance.ocean_number else None
        # if ocean_number:
        #     try:
        #         master_order = OceanOrder.objects.get(order_num=ocean_number, del_flag=False)
        #         instance.ocean_num = master_order
        #     except OceanOrder.DoesNotExist:
        #         pass

        check_customer_order_shipment_id(instance)

        # for order_out in ['masterOrderOut', 'oceanOrderOut']:
        #     self.collect_out_order(instance, order_out)

        if not user.is_staff and user.company:
            # 该用户是客户 直接将客户字段设置为该客户
            company = user.company
            instance.save_fields(customer=company, saler=company.saler_name)

        instance.save_fields(update_by=user, update_date=datetime.now())

        summary_predict_parcels_data(instance)
        summary_parcels_info(instance)

        # 计算体积重与分泡 ==> 修改了计费重、分泡重
        if instance.order_status == 'AW':
            calc_bubble_and_charge_weight(instance)

        is_b_async = self.context['request'].data.get('is_b_async', False)
        is_r_async = self.context['request'].data.get('is_r_async', False)
        if is_r_async:
            # 需要把地址同步过地址库
            save_address(instance, True)
        if is_b_async:
            # 需要把地址同步过地址库
            save_address(instance, False)

        create_or_update_parcel_sizes(instance, parcel_size_list)

        update_oc_shipment_outbound_instruct_data(instance)

        return instance

    def check_customer_status(self, customer):
        if customer and customer.status == 'OFF':
            raise ParamError(f'客户: {customer.name}已被停用', ErrorCode.PARAM_ERROR)

    # 创建空运/海运单外键, 创建空运/海运单绑定的出口报关单 (有bug暂时未使用)
    def collect_out_order(self, instance, out_order_type):
        # out_order_type = 'masterNumberOut'
        out_order_map = {'masterOrderOut': {'CustomerOrderOutNumber': 'master_num', 'OrderOutField': 'master_number',
                                            'ClearanceOutFK': 'master_order_id', 'OutOrderModel': MasterOrder, },
                         'oceanOrderOut': {'CustomerOrderOutNumber': 'ocean_num', 'OrderOutField': 'ocean_number',
                                           'ClearanceOutFK': 'ocean_order_id', 'OutOrderModel': OceanOrder, }, }
        order_out_number = out_order_map[out_order_type]['CustomerOrderOutNumber']
        order_out_field = out_order_map[out_order_type]['OrderOutField']
        clearance_out_fk = out_order_map[out_order_type]['ClearanceOutFK']
        out_order_model = out_order_map[out_order_type]['OutOrderModel']
        request = self.context['request']
        # 判断是否有出口报关单
        main_number_clearance_out = request.data.get(out_order_type, [])
        main_number_sli = list(set([i.get(order_out_field) for i in main_number_clearance_out]))
        if len(main_number_sli) > 1:
            raise ParamError(f'出口报关单中空运主单 / 海运提单的主单号必须唯一, 且要与运输信息中的单号一致',
                             ErrorCode.PARAM_ERROR)
        main_number_out = main_number_sli[0] if main_number_sli else None

        # 判断是否有空运主单号或者海运提单号，有则写入主单外键字段
        # todo: [chen]如果这里随便填一个空运单/海运单号, 未关联外键, 那么这个空运单/海运单号下的出口报关单无法在通过序列化器取到, 不能显示到前端页面
        main_order = None
        # main_number = instance.main_number.strip() if instance.main_number else None
        main_number = getattr(instance, order_out_field).strip() if getattr(instance, order_out_field) else None
        print('main_number_out-->', main_number_out, main_number)
        if not main_number and main_number_out:
            raise ParamError(f'未在运输信息中配置空运主单 / 海运提单号, 请先配置对应单号', ErrorCode.PARAM_ERROR)
        if main_number_out and main_number != main_number_out:
            raise ParamError(
                f'运输信息中的空运主单『{main_number}』与出口报关单中绑定的空运主单『{main_number_out}』不一致',
                ErrorCode.PARAM_ERROR)
        if main_number:
            try:
                main_order = out_order_model.objects.get(order_num=main_number, del_flag=False)
                setattr(instance, order_out_number, main_order)
            except out_order_model.DoesNotExist:
                pass

        # 创建出口报关单
        # 先把以前的出口报关单del_flag设置为True
        if main_order is not None:
            if clearance_out_fk == 'master_order_id':
                old_clearance_out = ClearanceOut.objects.filter(master_order_id=main_order, del_flag=False)
                if old_clearance_out.exists():
                    old_clearance_out.update(master_order_id=None,
                                             remark=f'订单详情创建出口报关单, 旧报关单取消关联, 空运单id: {main_order.id}')
            else:
                old_clearance_out = ClearanceOut.objects.filter(ocean_order_id=main_order, del_flag=False)
                if old_clearance_out.exists():
                    old_clearance_out.update(ocean_order_id=None,
                                             remark=f'订单详情创建出口报关单, 旧报关单取消关联, 海运单id: {main_order.id}')
        for clearance_out in main_number_clearance_out:
            clearance_out_params = {'clear_status': clearance_out.get('clear_status'),
                                    'clearance_num': clearance_out.get('clearance_num'), clearance_out_fk: main_order,
                                    'del_flag': False}
            if clearance_out.get('id'):
                # 存在id则修改
                clearance_out_by_id = None
                try:
                    clearance_out_by_id = ClearanceOut.objects.filter(id=clearance_out.get('id'))
                except ClearanceOut.DoesNotExist:
                    pass
                if clearance_out_by_id is not None:
                    clearance_out_by_id.update(**clearance_out_params)
            else:
                ClearanceOut.objects.create(**clearance_out_params, **get_update_params(request, True))
        return main_order


class InsuranceSerializer(serializers.ModelSerializer):
    """
    保险序列化
    """

    class Meta:
        model = Insurance
        fields = '__all__'


def handler_create_order(customer_order, customer_order_charge_in_list_data, customer_order_charge_out_list_data,
                         parce_type, parcel_item_data, user, is_summery_pre_data=True):
    # customer_order.order_num = settings.SYSTEM_ORDER_MARK + settings.CUSTOMER_ORDER_MARK + \
    #                            create_order_num(customer_order.id, 5)
    # 生成订单号
    gen_order_num(customer_order)

    # 计算毛利
    # customerOrder.gross_profit = (customerOrder.income or 0) - (customerOrder.cost or 0)
    if parce_type:
        # 完整商品
        # 创建包裹和包裹明细
        if parcel_item_data is not None:
            for item in parcel_item_data:
                # 同一个客户下的包裹号要求唯一
                # customer_parcels = Parcel.objects.filter(~Q(customer_order__order_status='VO'),
                #                                          customer_order__customer=customer_order.customer,
                #                                          parcel_num=item['parcel_num'], del_flag=False)
                # if customer_parcels.count() > 0:
                #     raise ParamError(f'客户『{customer_order.customer}』, 已存在包裹号『{item["parcel_num"]}』',
                #                      ErrorCode.PARAM_ERROR)
                # 判断是否已经存在了这个包裹, 有就修改, 没有就新增
                parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False,
                                                parcel_num=item['parcel_num'])
                if not parcels.exists():
                    if customer_order.order_type == 'FBA':
                        # FBA包裹号全局唯一
                        same_parcel_num = Parcel.objects.filter(~Q(customer_order__order_status='VO'),
                                                                parcel_num=item['parcel_num'],
                                                                order_type=customer_order.order_type,
                                                                del_flag=False)
                        if same_parcel_num.count() > 0:
                            raise ParamError(f'包裹号『{item["parcel_num"]}』已存在, 不能新增此包裹号',
                                             ErrorCode.PARAM_ERROR)
                    # 如果存在FBA仓库代码(收件人id), 则需要校验包裹号
                    # if customer_order.receiver:
                    #     judge_parcel_num_rule(item['parcel_num'], raise_error=True)
                    # 生成系统包裹号
                    item['customer_sys_parcel_num'] = item.get('sys_parcel_num')
                    item['sys_parcel_num'] = create_sys_parcel_num(customer_order)
                    # 生成新的包裹
                    parcel = save_parcel(customer_order, item, valuation_label=False)
                else:
                    parcel = Parcel.objects.get(customer_order=customer_order, del_flag=False,
                                                parcel_num=item['parcel_num'])

                # 生成包裹里面的商品
                save_parcel_item(item, parcel)
    else:
        # 简易
        # 创建包裹和包裹明细
        if parcel_item_data:
            for item in parcel_item_data:
                # 判断是否已经存在了这个包裹
                # 生成新的简易包裹
                save_parcel_size(customer_order, item)

    # 创建货件信息
    save_shipments(customer_order, parcel_item_data)

    # 汇总包裹中的reference id和shipment id到订单上
    parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
    check_customer_order_shipment_id(customer_order)

    reference_ids = list(set(parcels.values_list('reference_id', flat=True)))
    shipment_ids = list(set(parcels.values_list('shipment_id', flat=True)))
    customer_order.reference_id = create_order_reference_id(reference_ids)
    customer_order.shipment_id = create_order_reference_id(shipment_ids)
    customer_order.save()

    # 创建收入记录
    save_customer_order_charge_in(customer_order, customer_order_charge_in_list_data, user)
    # 创建成本记录
    save_customer_order_charge_out(customer_order, customer_order_charge_out_list_data, user)

    # # 是否收入计价
    # if customer_order.product and customer_order.product.is_valuation:
    #     add_revenue(customer_order, user, CustomerOrderChargeIn)
    # # 是否成本计价
    # if customer_order.product and customer_order.product.is_cost_valuation:
    #     add_cost(customer_order, user, CustomerOrderChargeOut)

    customer_order.create_by = user
    customer_order.update_by = user
    customer_order.create_date = customer_order.create_date or datetime.now()
    customer_order.update_date = datetime.now()

    if is_summery_pre_data:
        summary_predict_parcels_data(customer_order)
    summary_parcels_info(customer_order)
    # 计算体积重与分泡
    calc_bubble_and_charge_weight(customer_order)

    customer_order.save()
    # 判断非fba仓库/偏远地区
    common_judge_fba_and_remote(customer_order)

    # 校验产品路线
    check_product_route(customer_order)

    logger.info(f'运输订单创建成功: {customer_order.order_num}')
    return customer_order


def handler_update_order(customer_order, customer_order_charge_in_list_data, customer_order_charge_out_list_data,
                         parce_type, parcel_item_data, user, is_summery_pre_data=True):
    # 生成订单号
    gen_order_num(customer_order)

    if parce_type:
        # 完整商品
        # 创建包裹和包裹明细
        if parcel_item_data is not None:
            for item in parcel_item_data:
                # 判断是否已经存在了这个包裹, 有就修改, 没有就新增
                parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False,
                                                parcel_num=item['parcel_num'])
                if parcels.count() == 0:
                    if customer_order.order_type == 'FBA':
                        # FBA包裹号全局唯一
                        same_parcel_num = Parcel.objects.filter(~Q(customer_order__order_status='VO'),
                                                                parcel_num=item['parcel_num'],
                                                                order_type=customer_order.order_type,
                                                                del_flag=False)
                        if same_parcel_num.count() > 0:
                            raise ParamError(f'包裹号『{item["parcel_num"]}』已存在, 不能新增此包裹号',
                                             ErrorCode.PARAM_ERROR)

                    # 生成系统包裹号
                    item['sys_parcel_num'] = create_sys_parcel_num(customer_order)
                    # 生成新的包裹
                    parcel = save_parcel(customer_order, item, valuation_label=False)
                else:
                    parcel = Parcel.objects.get(customer_order=customer_order, del_flag=False,
                                                parcel_num=item['parcel_num'])

                # 生成包裹里面的商品
                save_parcel_item(item, parcel)
    else:
        # 简易
        # 创建包裹和包裹明细
        if parcel_item_data:
            for item in parcel_item_data:
                # 判断是否已经存在了这个包裹
                # 生成新的简易包裹
                save_parcel_size(customer_order, item)

    # 重建 货件信息
    OcShipment.objects.filter(
        customer_order_num=customer_order,
    ).update(del_flag=True)
    save_shipments(customer_order, parcel_item_data)

    # 汇总包裹中的reference id和shipment id到订单上
    parcels = Parcel.objects.filter(customer_order=customer_order, del_flag=False)
    check_customer_order_shipment_id(customer_order)

    reference_ids = list(set(parcels.values_list('reference_id', flat=True)))
    shipment_ids = list(set(parcels.values_list('shipment_id', flat=True)))
    customer_order.reference_id = create_order_reference_id(reference_ids)
    customer_order.shipment_id = create_order_reference_id(shipment_ids)
    customer_order.save()

    # 创建收入记录
    CustomerOrderChargeIn.objects.filter(customer_order_num=customer_order).update(del_flag=True)
    save_customer_order_charge_in(customer_order, customer_order_charge_in_list_data, user)
    # 创建成本记录
    CustomerOrderChargeOut.objects.filter(customer_order_num=customer_order).update(del_flag=True)
    save_customer_order_charge_out(customer_order, customer_order_charge_out_list_data, user)

    customer_order.update_by = user
    customer_order.update_date = datetime.now()

    if is_summery_pre_data:
        summary_predict_parcels_data(customer_order)
    summary_parcels_info(customer_order)
    # 计算体积重与分泡
    calc_bubble_and_charge_weight(customer_order)
    customer_order.save()
    # 判断非fba仓库/偏远地区
    common_judge_fba_and_remote(customer_order)
    logger.info(f'运输订单创建成功: {customer_order.order_num}')
    return customer_order


def save_customer_order_charge_out(customer_order, customer_order_charge_out_list_data, user):
    """
    封装保存成本记录
    :param customer_order:
    :param customer_order_charge_out_list_data:
    :param user:
    """
    restrict_charge_item_and_supplier(customer_order_charge_out_list_data, '成本')
    # 创建成本记录
    for customer_order_charge_out_data in customer_order_charge_out_list_data:
        customer_order_charge_out = CustomerOrderChargeOut.objects.create(customer_order_num=customer_order,
                                                                          **customer_order_charge_out_data)
        customer_order_charge_out.charge_total = (customer_order_charge_out.charge_rate or 0) * (
                customer_order_charge_out.charge_count or 0)

        # 通用获取汇率
        current_exchange = get_currency_rate(customer_order_charge_out.currency_type,
                                             [customer_order_charge_out.currency_type])
        # 通过汇率计算记账金额
        customer_order_charge_out.account_charge = customer_order_charge_out.charge_total * current_exchange
        customer_order_charge_out.current_exchange = current_exchange
        # 累计成本
        # customerOrder.cost = Decimal(customerOrder.cost) + (Decimal(customerOrderChargeOut.account_charge) or 0)
        customer_order_charge_out.create_by = user
        customer_order_charge_out.update_by = user
        customer_order_charge_out.create_date = datetime.now()
        customer_order_charge_out.update_date = datetime.now()
        customer_order_charge_out.save()


def save_customer_order_charge_in(customer_order, customer_order_charge_in_list_data, user):
    """
    封装保存收入
    :param customer_order:
    :param customer_order_charge_in_list_data:
    :param user:
    """
    restrict_charge_item(customer_order_charge_in_list_data, '收入')
    # 创建收入记录
    for customer_order_charge_in_data in customer_order_charge_in_list_data:
        customer_order_charge_in = CustomerOrderChargeIn.objects.create(customer_order_num=customer_order,
                                                                        **customer_order_charge_in_data)
        customer_order_charge_in.charge_total = (customer_order_charge_in.charge_rate or 0) * (
                customer_order_charge_in.charge_count or 0)

        # 通用获取汇率
        current_exchange = get_currency_rate(customer_order_charge_in.currency_type,
                                             [customer_order_charge_in.currency_type])
        # 通过汇率计算记账金额
        customer_order_charge_in.account_charge = customer_order_charge_in.charge_total * current_exchange
        customer_order_charge_in.current_exchange = current_exchange
        # 累计收入
        # customerOrder.income = Decimal(customerOrder.income) + (Decimal(customerOrderChargeIn.account_charge) or 0)

        # if not customer_order_charge_in.customer:
        # 对于订单修改客户的情况, 收入的付款方跟订单的客户保持一致
        customer_order_charge_in.customer = customer_order.customer or None

        customer_order_charge_in.create_by = user
        customer_order_charge_in.update_by = user
        customer_order_charge_in.create_date = datetime.now()
        customer_order_charge_in.update_date = datetime.now()
        customer_order_charge_in.save()


def save_parcel_size(customer_order, item):
    parcel = ParcelSize()
    parcel.parcel_length = item['parcel_length'] or 0
    parcel.parcel_width = item['parcel_width'] or 0
    parcel.parcel_height = item['parcel_height'] or 0
    parcel.parcel_weight = item['parcel_weight'] or 0
    parcel.actual_length = item['actual_length'] or 0
    parcel.actual_width = item['actual_width'] or 0
    parcel.actual_height = item['actual_height'] or 0
    parcel.actual_weight = item['actual_weight'] or 0
    parcel.parcel_qty = item['parcel_qty'] or 0
    if 'remark' in item.keys():
        parcel.remark = item['remark']
    if (item['parcel_width'] or 0) != 0 and (item['parcel_length'] or 0) != 0 and (item['parcel_height'] or 0) != 0:
        parcel.parcel_volume = Decimal(item['parcel_length']) * Decimal(item['parcel_width']) * Decimal(
            item['parcel_height']) / 1000000
    else:
        parcel.parcel_volume = 0
    if (item['actual_width'] or 0) != 0 and (item['actual_length'] or 0) != 0 and (item['actual_height'] or 0) != 0:
        parcel.actual_volume = Decimal(item['actual_length']) * Decimal(item['actual_width']) * Decimal(
            item['actual_height']) / 1000000
    else:
        parcel.actual_volume = 0
    parcel.customer_order = customer_order
    parcel.save()
    return parcel


@transaction.atomic
def create_or_update_parcel_sizes(customer_order: CustomerOrder, parcel_size_data):
    """
    根据ID更新或创建包裹测量数据
    :param customer_order: CustomerOrder实例
    :param parcel_size_data: 前端传入的包裹数据列表
    :return: 处理后的包裹列表
    """
    if not parcel_size_data:
        return []

    # 获取当前订单下所有已存在的包裹
    existing_parcels = ParcelSize.objects.filter(
        customer_order=customer_order,
        del_flag=False
    )

    # 将现有包裹按ID分组
    existing_parcels_dict = {str(p.id): p for p in existing_parcels}

    # 收集前端传入的所有ID（用于判断哪些记录需要删除）
    incoming_ids = {str(item.get('id')) for item in parcel_size_data if item.get('id')}

    # 标记删除不存在的包裹（在前端传入列表中不存在的ID）
    for parcel_id, parcel in existing_parcels_dict.items():
        if parcel_id not in incoming_ids:
            parcel.del_flag = True
            parcel.save()

    # 处理新增或更新
    result_parcels = []
    for item in parcel_size_data:
        parcel_id = str(item.get('id')) if item.get('id') else None

        # 如果存在ID则更新，否则创建
        if parcel_id and parcel_id in existing_parcels_dict:
            parcel = existing_parcels_dict[parcel_id]
            parcel.del_flag = False  # 确保不被标记为删除
        else:
            parcel = ParcelSize()
            parcel.customer_order = customer_order

        # 更新字段
        parcel.parcel_num = item.get('parcel_num', parcel.parcel_num if hasattr(parcel, 'parcel_num') else None)

        parcel.parcel_length = Decimal(item.get('parcel_length') or 0)
        parcel.parcel_width = Decimal(item.get('parcel_width') or 0)
        parcel.parcel_height = Decimal(item.get('parcel_height') or 0)
        parcel.parcel_weight = Decimal(item.get('parcel_weight') or 0)

        parcel.actual_length = Decimal(item.get('actual_length') or 0)
        parcel.actual_width = Decimal(item.get('actual_width') or 0)
        parcel.actual_height = Decimal(item.get('actual_height') or 0)
        parcel.actual_weight = Decimal(item.get('actual_weight') or 0)
        parcel.actual_volume = Decimal(item.get('actual_volume') or 0)
        parcel.parcel_qty = int(item.get('parcel_qty') or 0)

        # 计算体积
        if all([parcel.parcel_length, parcel.parcel_width, parcel.parcel_height]):
            parcel.parcel_volume = (parcel.parcel_length * parcel.parcel_width * parcel.parcel_height) / Decimal(
                '1000000')
        else:
            parcel.parcel_volume = Decimal('0')

        if all([parcel.actual_length, parcel.actual_width, parcel.actual_height]):
            parcel.actual_volume = (parcel.actual_length * parcel.actual_width * parcel.actual_height) / Decimal(
                '1000000')
        else:
            parcel.actual_volume = Decimal('0')

        # 保存并添加到结果列表
        parcel.save()
        result_parcels.append(parcel)

    return result_parcels


@transaction.atomic
def save_address(order, is_r_async=True):
    if is_r_async:
        # 收件人
        params = {'address_num': order.buyer_address_num, 'address_name': order.buyer_address_num,
                  'contact_name': order.buyer_name, 'contact_email': order.buyer_mail,
                  'contact_phone': order.buyer_phone, 'country_code': order.buyer_country_code,
                  'state_code': order.buyer_state, 'city_code': order.buyer_city_code, 'postcode': order.buyer_postcode,
                  'house_no': order.buyer_house_num, 'address_one': order.buyer_address_one,
                  'address_two': order.buyer_address_two, 'company_name': order.buyer_company_name,
                  'address_type': 'RC'}
    else:
        # 发件人
        params = {'address_num': order.address_num, 'address_name': order.address_num,
                  'contact_name': order.contact_name, 'contact_email': order.contact_email,
                  'contact_phone': order.contact_phone, 'country_code': order.country_code,
                  'state_code': order.state_code, 'city_code': order.city_code, 'postcode': order.postcode,
                  'house_no': order.house_no, 'address_one': order.address_one, 'address_two': order.address_two,
                  'company_name': order.company_name, 'address_type': 'SP'}
    address = Address.objects.filter(address_num=params['address_num'], del_flag=False)
    if address.count() == 0:
        Address.objects.create(**params)


def update_customer_order_charge_in(ChargeIn_items, customerOrderChargeIn_list_data, instance, user):
    if instance.is_revenue_lock:
        return
    restrict_charge_item(customerOrderChargeIn_list_data, '收入')
    print('customerOrderChargeIn_list_data-->', customerOrderChargeIn_list_data)

    # 根据ID来查找需要更新的记录
    for item in customerOrderChargeIn_list_data:
        item_id = item.get("id", None)
        # 没有ID的创建新的记录
        if item_id is None:
            chargeIn = instance.customerOrderChargeIns.create(**item)
            chargeIn.charge_total = (chargeIn.charge_rate or 0) * (chargeIn.charge_count or 0)
            # if not chargeIn.customer:
            chargeIn.customer = instance.customer or None
            # 通用获取汇率
            current_exchange = get_currency_rate(chargeIn.currency_type, [chargeIn.currency_type])
            # 通过汇率计算记账金额
            chargeIn.account_charge = chargeIn.charge_total * current_exchange
            chargeIn.current_exchange = current_exchange
            # 累计收入
            chargeIn.create_by = user
            chargeIn.create_date = datetime.now()
            chargeIn.save()
        # 有ID的，将记录内容更新掉
        elif ChargeIn_items.get(item_id, None) is not None:
            instance_item = ChargeIn_items.pop(item_id)

            old_charge_ins = CustomerOrderChargeIn.objects.filter(id=instance_item.id)
            if old_charge_ins.exists():
                charge_rate = old_charge_ins.first().charge_rate
                # 修改之后变成非系统添加
                if charge_rate != item.get("charge_rate", None):
                    item['is_system'] = False
                old_charge_ins.update(**item)
            chargeIn = CustomerOrderChargeIn.objects.get(id=instance_item.id)
            chargeIn.charge_total = (chargeIn.charge_rate or 0) * (chargeIn.charge_count or 0)
            # if not chargeIn.customer:
            # 对于订单修改客户的情况, 收入的付款方跟订单的客户保持一致
            chargeIn.customer = instance.customer or None
            # 通用获取汇率
            current_exchange = get_currency_rate(chargeIn.currency_type, [chargeIn.currency_type])
            # 通过汇率计算记账金额
            chargeIn.account_charge = chargeIn.charge_total * current_exchange
            chargeIn.current_exchange = current_exchange
            chargeIn.update_by = user
            chargeIn.update_date = datetime.now()
            chargeIn.save()


def update_customer_order_charge_out(ChargeOut_items, customerOrderChargeOut_list_data, instance, user):
    if instance.is_cost_lock:
        return
    restrict_charge_item_and_supplier(customerOrderChargeOut_list_data, '成本')
    for item in customerOrderChargeOut_list_data:
        item_id = item.get("id", None)
        # 没有ID的创建新的记录
        if item_id is None:
            chargeOut = instance.customerOrderChargeOuts.create(**item)
            chargeOut.charge_total = (chargeOut.charge_rate or 0) * (chargeOut.charge_count or 0)
            # 通用获取汇率
            current_exchange = get_currency_rate(chargeOut.currency_type, [chargeOut.currency_type])
            logger.info("--exchange1-->" + str(current_exchange) + ', type =' + str(chargeOut.currency_type))
            # 通过汇率计算记账金额
            chargeOut.account_charge = chargeOut.charge_total * current_exchange
            chargeOut.current_exchange = current_exchange
            chargeOut.create_by = user
            chargeOut.create_date = datetime.now()
            chargeOut.save()
        # 有ID的，将记录内容更新掉
        elif ChargeOut_items.get(item_id, None) is not None:
            instance_item = ChargeOut_items.pop(item_id)
            CustomerOrderChargeOut.objects.filter(id=instance_item.id).update(**item)
            chargeOut = CustomerOrderChargeOut.objects.get(id=instance_item.id)
            chargeOut.charge_total = (chargeOut.charge_rate or 0) * (chargeOut.charge_count or 0)
            # 通用获取汇率
            current_exchange = get_currency_rate(chargeOut.currency_type, [chargeOut.currency_type])
            logger.info("--exchange1-->" + str(current_exchange) + ', type =' + str(chargeOut.currency_type))
            # 通过汇率计算记账金额
            chargeOut.account_charge = chargeOut.charge_total * current_exchange
            chargeOut.current_exchange = current_exchange
            chargeOut.update_by = user
            chargeOut.update_date = datetime.now()
            chargeOut.save()


def calc_bubble_and_charge_weight(instance: CustomerOrder):
    """
    计算体积重与分泡
    """
    # 初始化计费重转换率
    logger.info(f'计算体积重与分泡1, 订单号: {instance.order_num}, 体积: {instance.volume}, 重量: {instance.weight}, '
                f'计费重: {instance.charge_weight}, 确认计费重: {instance.confirm_charge_weight}')
    product = instance.product
    if product and product.charge_weight_rate and not instance.charge_trans:
        instance.save_fields(charge_trans=product.charge_weight_rate)

    # 计费重量 = 体积 / 计费转换乘率 和 重量相比较, 谁大取谁
    is_calc_bubble_weight = False
    if instance.volume and instance.weight:
        num1 = (Decimal(instance.volume) * Decimal(1000000) / Decimal(instance.charge_trans or 6000))
        instance.save_fields(charge_volume=num1)
        if num1 > Decimal(instance.weight):
            instance.save_fields(charge_weight=num1)
            is_calc_bubble_weight = True
        else:
            instance.save_fields(charge_weight=Decimal(instance.weight))

        # 泡比和确认体积
        logger.info(f'volume到底是个啥: {instance.volume}, {type(instance.volume)}')
        if Decimal(instance.volume):
            volume_weight = Decimal(instance.weight) / Decimal(instance.volume)
            instance.save_fields(volume_weight=Decimal(volume_weight))
            if volume_weight > 363:
                confirm_volume = Decimal(instance.weight) / 363
            else:
                confirm_volume = Decimal(instance.volume)
            if instance.confirm_volume:
                # 判断是否有手动修改确认计费重的记录, 如果有, 则不覆盖, 如果没有, 则覆盖
                order_field_change_log = OrderFieldChangeLog.objects.filter(model_instance_id=instance.id,
                                                                            order_type='FBA',
                                                                            field_name='confirm_volume',
                                                                            del_flag=False)
                if not order_field_change_log.exists():
                    instance.save_fields(confirm_volume=confirm_volume)
            else:
                instance.save_fields(confirm_volume=confirm_volume)

    # 修改确认计费重量
    if instance.confirm_charge_weight:
        # 判断是否有手动修改确认计费重的记录, 如果有, 则不覆盖, 如果没有, 则覆盖
        order_field_change_log = OrderFieldChangeLog.objects.filter(model_instance_id=instance.id,
                                                                    order_type='FBA',
                                                                    field_name='confirm_charge_weight',
                                                                    del_flag=False)
        if not order_field_change_log.exists():
            instance.save_fields(confirm_charge_weight=instance.charge_weight)
    else:
        instance.save_fields(confirm_charge_weight=instance.charge_weight)
    logger.info(f'计算体积重与分泡2, 订单号: {instance.order_num}, 体积: {instance.volume}, 重量: {instance.weight}, '
                f'计费重: {instance.charge_weight}, 确认计费重: {instance.confirm_charge_weight}')

    # 设置分泡后计费重
    if Decimal(instance.bubble or 0) == 0:
        instance.save_fields(bubble_weight=instance.charge_weight)
    elif is_calc_bubble_weight:
        calc_bubble_weight(instance)
    else:
        instance.save_fields(bubble_weight=Decimal(instance.weight or 0))

    # 对计费重和确认计费重进行取整
    charge_weight_round(instance)


def calc_bubble_weight(customer_order):
    """
    分泡后计费重
    :param customer_order:
    """
    try:
        # 分泡后计费重 = （计费重-重量）* （1-客户分泡百分比）+重量
        # customer_order.bubble_weight = math.ceil(
        #     (float(customer_order.charge_weight) - float(customer_order.weight)) *
        #     (1 - float(customer_order.bubble) * 0.01) + float(customer_order.weight) *
        #     pow(10, settings.WEIGHT_PLACES)) / pow(10, settings.WEIGHT_PLACES)

        customer_order.bubble_weight = (Decimal(customer_order.charge_weight) - Decimal(
            customer_order.weight)) * Decimal(Decimal(1) - Decimal(customer_order.bubble) * Decimal(0.01)) + Decimal(
            customer_order.weight)
        customer_order.bubble_weight = customer_order.bubble_weight.quantize(Decimal('1.00'), ROUND_HALF_UP)
    except:
        customer_order.bubble_weight = 0
    customer_order.save(update_fields=['bubble_weight'])


def process_truck_inquiry(truck_inquiry, customer):
    truck_inquiry_price_order = TruckInquiryPriceOrder.objects.filter(
        id=truck_inquiry
    ).first()
    # if customer is not None and truck_inquiry_price_order and truck_inquiry_price_order.customer.id != customer:
    #     raise ParamError('卡派询价单客户不匹配', ErrorCode.PARAM_ERROR)

    truck_inquiry_price_charge_ins_queryset = TruckInquiryPriceChargeIn.objects.filter(
        customer_order_num=truck_inquiry, del_flag=False
    )

    if truck_inquiry_price_charge_ins_queryset.count() == 0:
        raise ParamError('卡派询价单没有收入项', ErrorCode.PARAM_ERROR)

    truck_inquiry_price_charge_ins = truck_inquiry_price_charge_ins_queryset.values(
        "charge", "charge_rate", "charge_count", "charge_total", "currency_type", )

    customer_order_charge_in_serializer = CustomerOrderChargeInSerializer(
        data=list(truck_inquiry_price_charge_ins), many=True)
    customer_order_charge_in_serializer.is_valid(raise_exception=True)

    data_list = customer_order_charge_in_serializer.data
    for item in data_list:
        item['charge'] = Charge.objects.get(id=item['charge'])
        item['customer'] = truck_inquiry_price_order.customer
        item['charge_rate'] = Decimal(item['charge_rate'])
        item['charge_count'] = Decimal(item['charge_count'])
        item['charge_total'] = Decimal(item['charge_total'])
        item.pop('charge_name')
        item.pop('customer_code')
        item.pop('charge_code')
        item.pop('customer_order_num')

    return data_list


# 修改FBA订单费用的公共方法
def modify_customer_order_charge(request, instance, validated_data):
    user = request.user
    action_flag = request.data.get('action_flag', None)
    customer_order = CustomerOrder.objects.get(id=instance.id, del_flag=False)
    if action_flag == 'cost_save':
        if customer_order.is_cost_lock:
            raise ParamError('成本已确认，请解锁再编辑成本', ErrorCode.PARAM_ERROR)
    if action_flag == 'revenue_save':
        if customer_order.is_revenue_lock:
            raise ParamError('收入已确认，请解锁再编辑收入', ErrorCode.PARAM_ERROR)
    customerOrderChargeIn_list_data = validated_data.pop('customerOrderChargeIns', [])
    customerOrderChargeOut_list_data = validated_data.pop('customerOrderChargeOuts', [])
    print('customerOrderChargeOut_list_data-->', customerOrderChargeOut_list_data)
    # 不能用这种方式获取customerOrderChargeOut_list_data,
    # 会引起下游报错: ValueError: Cannot assign "1": "CustomerOrderChargeOut.charge" must be a "Charge" instance.
    # customerOrderChargeOut_list_data = requestget('customerOrderChargeOuts', [])

    # 卡派询价单
    truck_inquiry = request.data.get('truck_inquiry', None)
    if truck_inquiry:
        if not customerOrderChargeIn_list_data:
            customerOrderChargeIn_list_data = process_truck_inquiry(truck_inquiry, instance.customer)
    # 获取当instance的所有子表记录
    ChargeIn_items = {item.id: item for item in instance.customerOrderChargeIns.all()}
    ChargeOut_items = {item.id: item for item in instance.customerOrderChargeOuts.all()}
    update_customer_order_charge_in(ChargeIn_items, customerOrderChargeIn_list_data, instance, user)
    # 因为customerOrderChargeOut对客户角色有限制, 只有费用的产品配置了客户对应的角色, 用户才能看到, 所以修改的时候要把客户不可见的成本明细加进来
    # print('ChargeOut_items-->', ChargeOut_items)
    # user_roles = list(user.roles.values_list('id', flat=True))
    # user_roles.append(None)
    # if 1 not in user.roles.values_list('id', flat=True):
    if not request.user.is_superuser:
        not_shown_charge_out = CustomerOrderChargeOut.objects.filter(
            ~Q(charge__roles__in=user.roles.values_list('id', flat=True)),
            charge__roles__isnull=False,
            customer_order_num=instance,
            del_flag=False).distinct()
        logger.info(f'什么成本: {not_shown_charge_out}')
        if not_shown_charge_out.exists():
            charge_out_queryset = CustomerOrderChargeOutSerializerCommon(not_shown_charge_out, many=True).data
            serializer_charge_out = CustomerOrderChargeOutSerializerCommon(data=charge_out_queryset, many=True)
            if serializer_charge_out.is_valid():
                validated_data_charge_out = serializer_charge_out.validated_data
                print('validated_data_charge_out-->', validated_data_charge_out)
                customerOrderChargeOut_list_data.extend(validated_data_charge_out)
    logger.info(f'customerOrderChargeOut_list_data?--> {len(customerOrderChargeOut_list_data)}')
    update_customer_order_charge_out(ChargeOut_items, customerOrderChargeOut_list_data, instance, user)

    # 针对还保留在列表里的记录，全部删除
    datetime_now = datetime.now()
    if not customer_order.is_revenue_lock:
        for item in ChargeIn_items.values():
            item.update_date = datetime_now
            item.del_flag = True
            item.save()
    if not customer_order.is_cost_lock:
        for item in ChargeOut_items.values():
            item.update_date = datetime_now
            item.del_flag = True
            item.save()
