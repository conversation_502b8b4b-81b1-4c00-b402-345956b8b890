from datetime import datetime
from decimal import Decimal

from django.conf import settings
from django.db import transaction
from rest_framework import serializers

from common.serializer import DeletedFilterListSerializer, DeletedFilterReverseListSerializer
from common.tools import create_order_cost, update_order_cost, update_order_revenue, get_update_params

from order.models import MasterOrder, MasterOrderChargeOut, CustomerOrder, MasterOrderFlightTrack, MasterOrderTrack, \
    MasterOrderPushTask, MasterOrderChargeIn, MasterOrderAddress, OceanPort, MasterAttachment
from rest_framework.relations import PrimaryKeyRelatedField

from order.serializers.customer_order import CustomerOrderSerializer
from order.serializers.master_attachment import AttachmentSerializer
from rbac.models import UserProfile
from alita.logger import logger
from company.models import Airline


class MasterOrderSerializer(serializers.ModelSerializer):
    '''
    主单序列化
    '''
    supplier_name = serializers.CharField(source='supplier.short_name', required=False)
    service_name = serializers.CharField(source='service.name', required=False)
    departure_name = serializers.SerializerMethodField()
    destination_name = serializers.SerializerMethodField()
    carrier_name = serializers.SerializerMethodField()
    supplierName = serializers.CharField(source='supplier.name', required=False)
    transport_type_name = serializers.SerializerMethodField()
    customer_name = serializers.CharField(source='customer.name', required=False)
    product_name = serializers.CharField(source='product.name', required=False)
    order_status_name = serializers.SerializerMethodField()
    transport_attribute_name = serializers.SerializerMethodField()
    carriage_type_name = serializers.SerializerMethodField()
    seal_status_name = serializers.SerializerMethodField()

    def get_departure_name(self, obj):

        if obj.departure:
            port = OceanPort.objects.filter(port_num=obj.departure).first()
            return port.port_name if port else ""
        return ""

    def get_destination_name(self, obj):

        if obj.destination:
            port = OceanPort.objects.filter(port_num=obj.destination).first()
            return port.port_name if port else ""
        return ""

    def get_carrier_name(self, obj):

        if obj.carrier_code:
            company = Airline.objects.filter(code=obj.carrier_code).first()
            return company.short_name if company else ""
        return ""

    def get_transport_type_name(self, obj):
        return obj.get_transport_type_display()

    def get_order_status_name(self, obj):
        return obj.get_order_status_display()

    def get_transport_attribute_name(self, obj):
        return obj.get_transport_attribute_display()

    def get_carriage_type_name(self, obj):
        return obj.get_carriage_type_display()

    def get_seal_status_name(self, obj):
        return obj.get_seal_status_display()
    class Meta:
        model = MasterOrder
        fields = '__all__'

class MasterOrderFlightTrackSerializer(serializers.ModelSerializer):
    '''
    航班追踪序列化
    '''
    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = MasterOrderFlightTrack
        fields = '__all__'


class MasterOrderChargeOutSerializer(serializers.ModelSerializer):
    charge_name = serializers.CharField(source='charge.name', required=False)
    supplier_name = serializers.CharField(source='supplier.short_name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = MasterOrderChargeOut
        fields = '__all__'


class MasterOrderChargeInSerializer(serializers.ModelSerializer):
    charge_name = serializers.CharField(source='charge.name', required=False)
    supplier_name = serializers.CharField(source='supplier.short_name', required=False)
    id = serializers.IntegerField(required=False, write_only=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = MasterOrderChargeOut
        fields = '__all__'


class MasterOrderAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasterOrderAddress
        fields = '__all__'


class MasterOrderTrackSerializer(serializers.ModelSerializer):
    """
    空运主单轨迹
    """

    class Meta:
        list_serializer_class = DeletedFilterReverseListSerializer
        model = MasterOrderTrack
        fields = '__all__'


class MasterOrderPushTaskSerializer(serializers.ModelSerializer):
    """
    推送任务
    """
    master_order_num_order_num = serializers.CharField(source='master_order_num.order_num', required=False)

    class Meta:
        list_serializer_class = DeletedFilterListSerializer
        model = MasterOrderPushTask
        fields = '__all__'



class MasterOrderAndDetatilSerializer(serializers.ModelSerializer):
    # 用户在使用的时，通过主键来关联
    create_by = PrimaryKeyRelatedField(queryset=UserProfile.objects.all(), required=False)
    update_by = PrimaryKeyRelatedField(queryset=UserProfile.objects.all(), required=False)
    # 继承类的属性需要特别定义
    del_flag = serializers.BooleanField(default=False)

    masterOrderChargeOuts = MasterOrderChargeOutSerializer(many=True, required=False)
    # master_customerOrders = CustomerOrderSerializer(many=True, required=False)

    # masterOrderFlightTracks = MasterOrderFlightTrackSerializer(many=True, required=False)
    # 空运主单轨迹
    masterOrderTrack = MasterOrderTrackSerializer(many=True, required=False)

    masterOrderPushTasks = MasterOrderPushTaskSerializer(many=True, required=False)

    supplier_name = serializers.CharField(source='supplier.short_name', required=False)
    service_name = serializers.CharField(source='service.name', required=False)

    # 附件
    attachments = AttachmentSerializer(many=True, required=False)
    # 自定义字段报错提示
    if settings.SYSTEM_VERSION == "V1":
        booking_time = serializers.DateTimeField(required=True, error_messages={'required': '[订舱时间]不能为空。',
                                                                                'null': '[订舱时间]不能为空。'})

    master_customerOrders = serializers.SerializerMethodField(required=False, allow_null=True)
    #新增序列化器
    senderForm = MasterOrderAddressSerializer(required=False)
    recipientForm = MasterOrderAddressSerializer(required=False)
    NotifyPartyForm = MasterOrderAddressSerializer(required=False)
    masterOrderChargeIns = MasterOrderChargeInSerializer(many=True, required=False)
    attachment_info = AttachmentSerializer(many=True, read_only=True, source='attachments')

    def get_master_customerOrders(self, obj):
        customer_order_queryset = CustomerOrder.objects.filter(master_num=obj, del_flag=False)
        return CustomerOrderSerializer(customer_order_queryset,many=True).data

    class Meta:
        model = MasterOrder
        fields = '__all__'

    @transaction.atomic
    def create(self, validated_data):
        masterOrderSenderForm_data = validated_data.pop('senderForm', {})  # 发件人信息
        masterOrderRecipientForm_data = validated_data.pop('recipientForm', {})  # 收件人信息
        masterOrderNotifyPartyForm_data = validated_data.pop('NotifyPartyForm', {}) # Notify Party
        masterOrderChargeOuts_list_data = validated_data.pop('masterOrderChargeOuts', []) # 成本录入
        masterOrderChargeIns_list_data = validated_data.pop('masterOrderChargeIns', []) # 收入录入
        masterOrderFlightTracks_list_data = validated_data.pop('masterOrderFlightTracks', [])
        # validated_data.pop('masterOrderPushTasks')
        validated_data.pop('masterOrderTrack', None)
        user = self.context['request'].user
        masterOrder = MasterOrder.objects.create(**validated_data)

        # 创建成本记录
        # for masterOrderChargeOut_data in masterOrderChargeOuts_list_data:
        #     masterOrderChargeOut = MasterOrderChargeOut.objects.create(masterOrder_num=masterOrder,
        #                                                                **masterOrderChargeOut_data)
        #     masterOrderChargeOut.charge_total = (masterOrderChargeOut.charge_rate or 0) * (
        #             masterOrderChargeOut.charge_count or 0)
        #     masterOrderChargeOut.account_charge = (masterOrderChargeOut.charge_rate or 0) * (
        #             masterOrderChargeOut.charge_count or 0)
        #
        #     # 通用获取汇率
        #     current_exchange = get_currency_rate(masterOrderChargeOut.currency_type,
        #                                          [masterOrderChargeOut.currency_type])
        #     masterOrderChargeOut.account_charge = masterOrderChargeOut.charge_total * current_exchange
        #     masterOrderChargeOut.create_by = user
        #     masterOrderChargeOut.create_date = datetime.now()
        #     masterOrderChargeOut.save()

        # 辅助函数：生成联系人姓名
        def generate_contact_name(data):
            """
            根据 last_name 和 first_name 生成 contact_name
            - 如果两个字段都有有效值：拼接生成
            - 如果只有一个字段有有效值：使用单个字段
            - 如果都无效：不生成 contact_name
            """
            last_name = data.get('last_name', '').strip() if data.get('last_name') else ''
            first_name = data.get('first_name', '').strip() if data.get('first_name') else ''
            
            if last_name and first_name:
                # 两个字段都有值，拼接
                return f"{last_name} {first_name}"
            elif last_name:
                # 只有姓氏
                return last_name
            elif first_name:
                # 只有名字
                return first_name
            else:
                # 都为空，不生成
                return None

        def filter_model_fields(model_class, data):
            model_fields = set(f.name for f in model_class._meta.get_fields())
            return {k: v for k, v in data.items() if k in model_fields}

        # 创建成本明细
        if masterOrderSenderForm_data:
            # 生成 contact_name
            contact_name = generate_contact_name(masterOrderSenderForm_data)
            if contact_name:
                masterOrderSenderForm_data['contact_name'] = contact_name
            filtered_sender = filter_model_fields(MasterOrderAddress, masterOrderSenderForm_data)
            MasterOrderAddress.objects.create(**{'customer_order': masterOrder, 'address_type': 'SP'}, **filtered_sender)

        if masterOrderRecipientForm_data:
            # 生成 contact_name
            contact_name = generate_contact_name(masterOrderRecipientForm_data)
            if contact_name:
                masterOrderRecipientForm_data['contact_name'] = contact_name
            filtered_recipient = filter_model_fields(MasterOrderAddress, masterOrderRecipientForm_data)
            MasterOrderAddress.objects.create(**{'customer_order': masterOrder, 'address_type': 'RC'}, **filtered_recipient)
        if masterOrderNotifyPartyForm_data:
            # 生成 contact_name
            contact_name = generate_contact_name(masterOrderNotifyPartyForm_data)
            if contact_name:
                masterOrderNotifyPartyForm_data['contact_name'] = contact_name
            filtered_notify_party = filter_model_fields(MasterOrderAddress, masterOrderNotifyPartyForm_data)
            MasterOrderAddress.objects.create(**{'customer_order': masterOrder, 'address_type': 'NP'}, **filtered_notify_party)
        if masterOrderChargeOuts_list_data:
            if settings.SYSTEM_VERSION == 'V2':
                for masterOrderChargeOut_data in masterOrderChargeOuts_list_data:
                    MasterOrderChargeOut.objects.create(
                        customer_order_num=masterOrder,
                        **masterOrderChargeOut_data
                    )
            else:
                create_order_cost(masterOrderChargeOuts_list_data, MasterOrderChargeOut, 'customer_order_num', masterOrder, user)
        if masterOrderChargeIns_list_data:
            if settings.SYSTEM_VERSION == 'V2':
                for masterOrderChargeIn_data in masterOrderChargeIns_list_data:
                    MasterOrderChargeIn.objects.create(
                        customer_order_num=masterOrder,
                        **masterOrderChargeIn_data
                    )

        # for masterOrderFlightTrack in masterOrderFlightTracks_list_data:
        #     masterOrderFlightTrack.create_by = user
        #     masterOrderFlightTrack.create_date = datetime.now()
        #     masterOrderFlightTrack.save()
        if settings.SYSTEM_VERSION == 'V2':
            masterOrder.order_status = 'DR'
        else:
            if masterOrder.carton is not None and masterOrder.weight is not None and masterOrder.volume is not None \
                    and masterOrder.order_status == 'SM':
                masterOrder.order_status = 'WO'
        if settings.SYSTEM_VERSION == 'V2':
            # 运输单号ref_num自动生成规则： ABK开头+6位日期+4位序列
            # 获取当前日期，格式为 YYMMDD
            current_date = datetime.now().strftime('%y%m%d')
            with transaction.atomic():
                # 使用 select_for_update() 防止并发问题
                # 查询当天已创建的运输单数量
                today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                today_end = today_start.replace(hour=23, minute=59, second=59, microsecond=999999)

                today_count = MasterOrder.objects.select_for_update().filter(
                    create_date__range=(today_start, today_end),
                    ref_num__startswith=f'ABK{current_date}'
                ).count()
                # 生成序列号，从0001开始
                sequence_number = str(today_count + 1).zfill(4)
                # 组合运输单号：ABK + 6位日期 + 4位序列号
                ref_num = f'ABK{current_date}{sequence_number}'
                masterOrder.ref_num = ref_num

            # 附件上传处理
            request = self.context['request']
            files = request.FILES.getlist('attachment')
            MAX_FILES = 10
            if len(files) > MAX_FILES:
                raise serializers.ValidationError(f'一次最多只能上传{MAX_FILES}个附件')

            for f in files:
                MasterAttachment.objects.create(
                    masterOrder=masterOrder,
                    url=f,
                    name=f.name,
                    **get_update_params(request, True)
                )
            #入仓单号如果没有写,保存的时候等于空运主单号
            order_num = validated_data.pop('order_num')  # 运输单据号码
            warehouse_receipt_number = validated_data.pop('warehouse_receipt_number', "")  # 入仓单号
            if not warehouse_receipt_number:
                masterOrder.warehouse_receipt_number = order_num

        masterOrder.create_by = user
        masterOrder.update_by = user
        masterOrder.create_date = datetime.now()
        masterOrder.update_date = datetime.now()

        # 设置计费重量
        try:
            # 计费重量 = 体积 / 计费转换乘率     和    重量相比较   谁大取谁
            num1 = (Decimal(masterOrder.volume) / Decimal(masterOrder.charge_trans))
            if num1 > Decimal(masterOrder.weight):
                masterOrder.charge_weight = num1
            else:
                masterOrder.charge_weight = Decimal(masterOrder.weight)
        except:
            masterOrder.charge_weight = 0
        # 计费重量小数位第一位小于5就为5，大于5就向上取整
        if masterOrder.charge_weight - int(masterOrder.charge_weight) >= 0.5:
            masterOrder.charge_weight = int(masterOrder.charge_weight) + 1
        else:
            masterOrder.charge_weight = int(masterOrder.charge_weight) + 0.5

        # 设置分泡后计费重
        try:
            # 分泡后计费重 = （计费重-重量）* （1-客户分泡百分比）+ 重量
            masterOrder.bubble_weight = (float(masterOrder.charge_weight) - float(masterOrder.weight)) * (
                    1 - float(masterOrder.bubble) * 0.01) + float(masterOrder.weight)
        except:
            masterOrder.bubble_weight = 0

        if settings.SYSTEM_VERSION != 'V2':
            # 如果主单有件数、重量、体积的话状态改为等待作业
            if masterOrder.carton is not None and masterOrder.weight is not None and masterOrder.volume is not None:
                masterOrder.order_status = 'WO'

        masterOrder.save()
        return masterOrder

    @transaction.atomic
    def update(self, instance, validated_data):
        # 获取传入的记录数据
        masterOrderSenderForm_data = validated_data.pop('senderForm', {})  # 发件人信息
        masterOrderRecipientForm_data = validated_data.pop('recipientForm', {})  # 收件人信息
        masterOrderNotifyPartyForm_data = validated_data.pop('NotifyPartyForm', {})  # Notify Party
        masterOrderChargeIns_list_data = validated_data.pop('masterOrderChargeIns', [])  # 收入录入
        masterOrderChargeOuts_list_data = validated_data.pop('masterOrderChargeOuts', []) # 成本录入
        masterOrderFlightTracks_list_data = validated_data.pop('masterOrderFlightTracks', [])
        validated_data.pop('masterOrderFlightTracks', [])
        validated_data.pop('masterOrderPushTasks', None)
        validated_data.pop('main_file', None)
        validated_data.pop('cabin_file', None)
        validated_data.pop('masterOrderTrack', None)
        user = self.context['request'].user
        # # 获取当instance的所有子表记录
        # ChargeOut_items = {item.id: item for item in instance.masterOrderChargeOuts.all()}
        # # # 根据ID来查找需要更新的记录
        # for item in masterOrderChargeOuts_list_data:
        #     item_id = item.get("id", None)
        #     # 没有ID的创建新的记录
        #     if item_id is None:
        #         chargeOut = instance.masterOrderChargeOuts.create(**item)
        #         chargeOut.charge_total = (chargeOut.charge_rate or 0) * (
        #                 chargeOut.charge_count or 0)
        #         # 通用获取汇率
        #         current_exchange = get_currency_rate(chargeOut.currency_type,
        #                                              [chargeOut.currency_type])
        #         chargeOut.account_charge = chargeOut.charge_total * current_exchange
        #         chargeOut.create_by = user
        #         chargeOut.create_date = datetime.now()
        #         chargeOut.save()
        #     # 有ID的，将记录内容更新掉
        #     elif ChargeOut_items.get(item_id, None) is not None:
        #         instance_item = ChargeOut_items.pop(item_id)
        #         MasterOrderChargeOut.objects.filter(id=instance_item.id).update(**item)
        #         chargeOut = MasterOrderChargeOut.objects.get(id=instance_item.id)
        #         chargeOut.charge_total = (chargeOut.charge_rate or 0) * (
        #                 chargeOut.charge_count or 0)
        #         # 通用获取汇率
        #         current_exchange = get_currency_rate(chargeOut.currency_type,
        #                                              [chargeOut.currency_type])
        #         chargeOut.account_charge = chargeOut.charge_total * current_exchange
        #         chargeOut.update_by = user
        #         chargeOut.update_date = datetime.now()
        #         chargeOut.save()
        #
        # # 针对还保留在列表里的记录，全部删除
        # for item in ChargeOut_items.values():
        #     item.del_flag = True
        #     item.save()

        # 更新地址信息
        if masterOrderSenderForm_data:
            MasterOrderAddress.objects.update_or_create(
                customer_order=instance, 
                address_type='SP',
                defaults=masterOrderSenderForm_data
            )
        if masterOrderRecipientForm_data:
            MasterOrderAddress.objects.update_or_create(
                customer_order=instance, 
                address_type='RC',
                defaults=masterOrderRecipientForm_data
            )
        if masterOrderNotifyPartyForm_data:
            MasterOrderAddress.objects.update_or_create(
                customer_order=instance, 
                address_type='NP',
                defaults=masterOrderNotifyPartyForm_data
            )
        if masterOrderChargeOuts_list_data:
            if settings.SYSTEM_VERSION == 'V2':
                # 获取当前instance的所有成本记录
                ChargeOut_items = {item.id: item for item in instance.masterOrderChargeOuts.all()}
                # 根据ID来查找需要更新的记录
                for item in masterOrderChargeOuts_list_data:
                    item_id = item.get("id", None)
                    # 没有ID的创建新的记录
                    if item_id is None:
                        MasterOrderChargeOut.objects.create(
                            customer_order_num=instance,
                            **item
                        )
                    # 有ID的，将记录内容更新掉
                    elif ChargeOut_items.get(item_id, None) is not None:
                        instance_item = ChargeOut_items.pop(item_id)
                        MasterOrderChargeOut.objects.filter(id=instance_item.id).update(**item)
                
                # 针对还保留在列表里的记录，全部删除
                for item in ChargeOut_items.values():
                    item.del_flag = True
                    item.save()
            else:
                update_order_cost(masterOrderChargeOuts_list_data, MasterOrderChargeOut, 'customer_order_num', instance, user)
        
        if masterOrderChargeIns_list_data:
            if settings.SYSTEM_VERSION == 'V2':
                # 获取当前instance的所有收入记录
                ChargeIn_items = {item.id: item for item in instance.masterOrderChargeIns.all()}
                # 根据ID来查找需要更新的记录
                for item in masterOrderChargeIns_list_data:
                    item_id = item.get("id", None)
                    # 没有ID的创建新的记录
                    if item_id is None:
                        MasterOrderChargeIn.objects.create(
                            customer_order_num=instance,
                            **item
                        )
                    # 有ID的，将记录内容更新掉
                    elif ChargeIn_items.get(item_id, None) is not None:
                        instance_item = ChargeIn_items.pop(item_id)
                        MasterOrderChargeIn.objects.filter(id=instance_item.id).update(**item)
                
                # 针对还保留在列表里的记录，全部删除
                for item in ChargeIn_items.values():
                    item.del_flag = True
                    item.save()
            else:
                update_order_revenue(masterOrderChargeIns_list_data, MasterOrderChargeIn, 'customer_order_num', instance, user)

        # # 获取当instance的所有子表记录
        master_order_flight_track_items = {item.id: item for item in instance.masterOrderFlightTracks.all()}
        # # 根据ID来查找需要更新的记录
        for item in masterOrderFlightTracks_list_data:
            item_id = item.get("id", None)
            # 没有ID的创建新的记录
            if item_id is None:
                master_order_flight_track = instance.masterOrderFlightTracks.create(**item)
                master_order_flight_track.create_by = user
                master_order_flight_track.create_date = datetime.now()
                master_order_flight_track.save()
            # 有ID的，将记录内容更新掉
            elif master_order_flight_track_items.get(item_id, None) is not None:
                instance_item = master_order_flight_track_items.pop(item_id)
                MasterOrderFlightTrack.objects.filter(id=instance_item.id).update(**item)
                master_order_flight_track = MasterOrderFlightTrack.objects.get(id=instance_item.id)
                master_order_flight_track.update_by = user
                master_order_flight_track.update_date = datetime.now()
                master_order_flight_track.save()

        # 针对还保留在列表里的记录，全部删除
        for item in master_order_flight_track_items.values():
            item.del_flag = True
            item.save()

        # 更新主记录的所有字段属性
        for field in validated_data:
            setattr(instance, field, validated_data.get(field, getattr(instance, field)))
        # 此时所有记录都已经在内存中更新完成，可以完成后台的逻辑操作
        instance.update_by = user
        instance.update_date = datetime.now()
        # 设置计费重量
        try:
            # 计费重量 = 体积 / 计费转换乘率     和    重量相比较   谁大取谁
            num1 = (Decimal(instance.volume) / Decimal(instance.charge_trans))
            if num1 > Decimal(instance.weight):
                instance.charge_weight = num1
            else:
                instance.charge_weight = Decimal(instance.weight)
        except:
            instance.charge_weight = 0

        # 计费重量小数位第一位小于5就为5，大于5就向上取整
        if instance.charge_weight - int(instance.charge_weight) >= 0.5:
            instance.charge_weight = int(instance.charge_weight) + 1
        else:
            instance.charge_weight = int(instance.charge_weight) + 0.5

        try:
            # 分泡后计费重 = （计费重-重量）* （1-客户分泡百分比）+重量
            instance.bubble_weight = (float(instance.charge_weight) - float(
                instance.weight)) * (1 - float(instance.bubble) * 0.01) + instance.weight
        except:
            instance.bubble_weight = 0
        if settings.SYSTEM_VERSION == 'V2':
            instance.order_status = 'DR'
        else:
            if instance.carton is not None and instance.weight is not None and instance.volume is not None and instance.order_status == 'SM':
                instance.order_status = 'WO'

            # 如果主单有件数、重量、体积的话状态改为等待作业
            if instance.carton is not None and instance.weight is not None and instance.volume is not None:
                instance.order_status = 'WO'
        instance.save()

        # 返写客户单的表头信息
        departure = validated_data.get('departure', None)
        destination = validated_data.get('destination', None)
        actual_leave_date = validated_data.get('actual_leave_date', None)
        actual_arrivals_date = validated_data.get('actual_arrivals_date', None)
        airline_num = validated_data.get('airline_num', None)
        queryset = CustomerOrder.objects.filter(master_num=instance.id)
        for item in queryset:
            item.departure = departure
            item.destination = destination
            item.actual_leave_date = actual_leave_date
            item.actual_arrivals_date = actual_arrivals_date
            item.airline_num = airline_num
            item.save()

        # 当主单实际离港时间不为空时
        if actual_leave_date is not None:
            # 给客户单插入一条 '国际运输出港' 轨迹
            # for customer_order in queryset:
            #     set_customer_track([customer_order.id], 'TS')
            pass
        return instance


