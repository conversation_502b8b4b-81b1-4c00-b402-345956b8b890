from django.db import models

from common.models import BaseEntity, BaseContactAddress


# Create your models here.


# from rbac.models import UserProfile


class Company(BaseEntity):
    CLIENT_TYPE = [
        # ('customer', '客户'),
        # ('supplier', '供应商'),
        ('direct', '直客'),
        ('peers', '同行'),
        ('agent', '代理'),
        ('platform', '平台'),
        ('project_client', '项目客户'),
        ('other', '其他'),
    ]
    DEBIT = [
        ('0', '否'),
        ('1', '周结'),
        ('2', '月结'),
        ('3', '半月结'),
        ('4', '票结-到海外仓结'),
        ('5', '票结-目的港结'),
        ('6', '票结-入仓结'),
        ('7', '票结-出仓结'),
        ('8', '票结-签收结'),
        ('9', '月结15'),
        ('10', '月结20'),
        ('11', '月结25'),
        ('12', '月结30'),
        ('13', '双月结15'),
        ('14', '双月结30'),
        ('15', '现结'),
    ]

    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    SUPPLIER_TYPE = [
        ('WC', '尾程配送'),
        ('JK', '进口清关'),
    ]

    BRANCH_OFFICE = [
        ('yiwu', '义乌分公司'),
        ('ningbo', '宁波分公司'),
        ('shenzhen', '深圳分公司'),
    ]
    # STATUS = [
    #     ("active", "正常"),
    #     ("suspend", "锁定"),
    #     ("inactive", "停用"),
    #     ("close", "关闭"),
    # ]

    CUSTOMER_ACQUIRE_TYPE = [
        ('0', '自拓客'),
        ('1', '重新分配'),
        ('2', '公司分配'),
        ('3', '市场推广'),
    ]
    TYPE_WAYBILL_LIST = [
        ('url', 'url'),
        ('base64', 'base64')
    ]
    short_name = models.CharField(max_length=100, verbose_name='公司编码', null=True, blank=True)
    name = models.CharField(max_length=300, verbose_name='公司名称')
    contact_name = models.CharField(max_length=100, verbose_name='联系人', null=True, default=None, blank=True, )
    contact_phone = models.CharField(max_length=50, verbose_name='联系电话', null=True, default=None, blank=True, )
    saler_name = models.CharField(max_length=100, verbose_name='销售负责人', null=True, default=None, blank=True, )
    customer_service_name = models.CharField(max_length=100, verbose_name='所属客服专员', null=True, default=None,
                                             blank=True, )
    settlement_name = models.CharField(max_length=100, verbose_name='所属结算专员', null=True, default=None,
                                       blank=True, )
    custom_name = models.CharField(max_length=100, verbose_name='客户负责人', null=True, default=None, blank=True, )
    business_email = models.CharField(max_length=500, verbose_name='业务邮箱', null=True, default=None, blank=True, )
    debit_email = models.CharField(max_length=500, verbose_name='账单邮箱', null=True, blank=True)
    is_customer = models.BooleanField(verbose_name='客户', null=True)
    is_supplier = models.BooleanField(verbose_name='供应商', null=True)
    client_type = models.CharField(max_length=50, choices=CLIENT_TYPE, verbose_name='客户分类', null=True, default=None,
                                   blank=True, )
    # 对方公司开票信息
    fullName = models.CharField(max_length=200, verbose_name='全称', default='', blank=True, )
    tax = models.CharField(max_length=200, verbose_name='税号', default='', blank=True, )
    bank = models.CharField(max_length=200, verbose_name='开户行', default='', blank=True, )
    account = models.CharField(max_length=200, verbose_name='账号', default='', blank=True, )
    district = models.CharField(max_length=200, verbose_name='区', default='', blank=True, )
    Street = models.CharField(max_length=200, verbose_name='街道', default='', blank=True, )
    address = models.CharField(max_length=200, verbose_name='详细地址', default='', blank=True, )
    # is_week_debit = models.NullBooleanField(verbose_name='周结', default=True)
    debit_date = models.CharField(max_length=10, choices=DEBIT, verbose_name='账单自动结算日', default='0')
    is_sign_contract = models.BooleanField(verbose_name='是否签合同', default=False)
    is_wallet_manage = models.BooleanField(verbose_name='是否钱包管理', default=True)

    # company_status = models.CharField(max_length=10, choices=STATUS, verbose_name='用户状态', default='active')
    create_time = models.DateField(verbose_name='开户时间', null=True, default=None, blank=True)
    first_send_time = models.DateField(verbose_name='首次发货时间', null=True, default=None, blank=True)
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    state_code = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True)
    city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True)

    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE)
    supplier_type = models.CharField(max_length=10, verbose_name='供应商类型', choices=SUPPLIER_TYPE,
                                     null=True, blank=True)
    belong_branch_office = models.CharField(max_length=50, choices=BRANCH_OFFICE, verbose_name='所属分公司',
                                            null=True, default=None, blank=True)
    customer_group = models.CharField(max_length=100, verbose_name='客户组', null=True, default=None, blank=True, )
    settlement = models.CharField(max_length=100, verbose_name='结算', null=True, default=None, blank=True, )
    customer_acquire_type = models.CharField(max_length=100, choices=CUSTOMER_ACQUIRE_TYPE, verbose_name='获客类型',
                                             null=True, default=None, blank=True)
    limit_low_deduct = models.BooleanField(verbose_name='额度不足是否扣件', default=True, null=True, blank=True)
    return_waybill = models.BooleanField(verbose_name='查询运单接口是否返回面单', default=False, null=True, blank=True)
    type_waybill = models.CharField(max_length=50, verbose_name='API接口返回面单类型', choices=TYPE_WAYBILL_LIST,
                                    default='url',
                                    null=True, blank=True)
    company_name = models.CharField(max_length=50, verbose_name='公司名', null=True, blank=True)
    house_number = models.CharField(max_length=50, verbose_name='门牌号', null=True, blank=True)
    postal_code = models.CharField(max_length=50, verbose_name='邮编', null=True, blank=True)
    id_type = models.CharField(max_length=50, verbose_name='证件类型', null=True, blank=True)
    id_number = models.CharField(max_length=50, verbose_name='证件号', null=True, blank=True)
    is_sleep = models.BooleanField(verbose_name='是否休眠', default=False, null=True)

    class Meta:
        verbose_name = '业务伙伴'
        verbose_name_plural = '业务伙伴管理'
        ordering = ['-id']

    def __str__(self):
        return self.short_name


# 开票信息表
class BillingInfo(BaseEntity):
    DEBIT = [
        ('0', '否'),
        ('1', '周结'),
        ('2', '月结'),
        ('3', '半月结'),
    ]
    fullName = models.CharField(max_length=200, verbose_name='全称', default='', blank=True, )
    tax = models.CharField(max_length=200, verbose_name='税号', default='', blank=True, )
    bank = models.CharField(max_length=200, verbose_name='开户行', default='', blank=True, )
    account = models.CharField(max_length=200, verbose_name='账号', default='', blank=True, )
    address = models.CharField(max_length=200, verbose_name='地址', default='', blank=True, )
    # is_week_debit = models.NullBooleanField(verbose_name='周结', default=True)
    debit_date = models.CharField(max_length=10, choices=DEBIT, verbose_name='账单自动结算日', default='0')
    is_sign_contract = models.BooleanField(verbose_name='是否签合同', default=False)
    company = models.ForeignKey(Company, related_name='companyBilling', on_delete=models.DO_NOTHING, null=True,
                                blank=True, verbose_name='归属公司')


class Attachment(BaseEntity):
    name = models.CharField(max_length=100, verbose_name='附件名称')
    url = models.FileField(upload_to='files/%Y%m%d', null=True, blank=True, verbose_name='营业执照,合同等附件')
    company = models.ForeignKey(Company, related_name='relative_attachments', verbose_name='合作伙伴',
                                on_delete=models.DO_NOTHING, null=True)

    class Meta:
        verbose_name_plural = '上传附件'
        verbose_name = '上传附件'

    def __str__(self):
        return self.name


class Bank(BaseEntity):
    CURRENCY = [
        ('CNY', 'CNY'),
        ('USD', 'USD'),
        ('GBP', 'GBP'),
        ('EUR', 'EUR'),
        ('HKD', 'HKD'),
        ('CAD', 'CAD'),
        ('CHF', 'CHF'),
        ('AUD', 'AUD'),
        ('MXN', 'MXN'),
    ]
    account_type = models.BooleanField(verbose_name='是否是对公账户', null=True, blank=True)
    short_name = models.CharField(max_length=100, verbose_name='简称')
    name = models.CharField(max_length=200, verbose_name='银行名称')
    account = models.CharField(max_length=100, verbose_name='账号')
    account_name = models.CharField(max_length=100, verbose_name='账号名称', null=True, blank=True)
    address = models.CharField(max_length=200, verbose_name='银行地址')
    code = models.CharField(max_length=100, verbose_name='分行代码')
    swift_code = models.CharField(max_length=50, verbose_name='SWIFT')
    amount = models.DecimalField(max_digits=20, decimal_places=4, verbose_name='金额', default=0.0)
    currency = models.CharField(max_length=64, verbose_name='币种')
    company = models.ForeignKey(Company, null=True, blank=True, related_name='company_bank', on_delete=models.SET_NULL,
                                verbose_name="所属客户")

    class Meta:
        verbose_name = '银行账户'
        verbose_name_plural = '银行账户'

    def __str__(self):
        return self.short_name


class Airline(BaseEntity):
    name = models.CharField(max_length=300, verbose_name='公司名称')
    short_name = models.CharField(max_length=100, verbose_name='公司简称')
    code = models.CharField(max_length=10, verbose_name='二字代码')
    prefix = models.CharField(max_length=10, verbose_name='前缀', null=True, blank=True)
    web = models.URLField(max_length=300, verbose_name='网址', null=True, blank=True)
    en_name = models.CharField(max_length=300, verbose_name='英文名称', null=True, blank=True)

    class Meta:
        verbose_name = '航空公司'
        verbose_name_plural = '航空公司'

    def __str__(self):
        return self.name


class ReceivingAddress(BaseEntity):
    ADDRESS_TYPE = [
        ('SP', '发件人'),
        ('RC', '收件人'),
        ('CA', '揽收地址'),
        ('TJ', '国内退件地址'),
        ('JW', '境外退件地址'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
        ('WA', '待审核'),
    ]
    address_num = models.CharField(max_length=50, verbose_name='地址编码', null=True, blank=True, default='')
    address_name = models.CharField(max_length=50, verbose_name='地址简称', null=True, blank=True, default='')
    last_name = models.CharField(max_length=100, verbose_name='姓', null=True, blank=True, default='')
    first_name = models.CharField(max_length=100, verbose_name='名', null=True, blank=True, default='')
    contact_name = models.CharField(max_length=100, verbose_name='联系人', null=True, default='')
    contact_email = models.EmailField(max_length=300, verbose_name='邮箱', null=True, blank=True, default='')
    contact_phone = models.CharField(max_length=50, verbose_name='电话', null=True, blank=True, default='')
    contact_mobile = models.CharField(max_length=15, verbose_name='手机号', null=True, blank=True, default='')
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, default='')
    state_code = models.CharField(max_length=50, verbose_name='省份(州)编码', null=True, blank=True, default='')
    city_code = models.CharField(max_length=50, verbose_name='城市编码', null=True, blank=True, default='')
    postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True, default='')
    house_no = models.CharField(max_length=20, verbose_name='门牌号', null=True, blank=True, default='')
    address_one = models.CharField(max_length=150, verbose_name='地址行1', null=True, default='')
    address_two = models.CharField(max_length=150, verbose_name='地址行2', null=True, blank=True, default='')
    company_name = models.CharField(max_length=100, verbose_name='公司名', null=True, blank=True, default='')
    customer = models.ForeignKey(Company, verbose_name='归属客户', on_delete=models.DO_NOTHING, null=True)
    address_type = models.CharField(max_length=10, choices=ADDRESS_TYPE, verbose_name='地址类型', default='CA')
    is_open = models.BooleanField(verbose_name='是否对外开放', default=True)
    is_sync_supplier = models.BooleanField(verbose_name='是否同步供应商', default=False)
    supplier_warehouse_code = models.CharField(max_length=50, verbose_name='供应商仓库编码', null=True, blank=True)
    address_detail = models.CharField(max_length=200, verbose_name='中文地址详情', null=True, blank=True)
    receiving_time = models.CharField(max_length=100, verbose_name='收货时间', null=True, blank=True)
    region_code = models.CharField(max_length=50, verbose_name='区', null=True, blank=True)
    street = models.CharField(max_length=50, verbose_name='街道', null=True, blank=True)
    id_type = models.CharField(max_length=15, verbose_name='证件类型', null=True, blank=True)
    id_number = models.CharField(max_length=30, verbose_name='证件号码', null=True, blank=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE)

    class Meta:
        verbose_name = '揽收地址'
        verbose_name_plural = '揽收地址'

    def __str__(self):
        return self.address_one


class Address(BaseContactAddress):
    ADDRESS_TYPE = [
        ('SP', '发件人'),
        ('RC', '收件人'),
        ('BLS', '提单发件人'),
        ('TD', '提单收件人'),
        ('ZW', '转运目的仓'),
        ('NP', 'notify party'),
        ('CA', '揽收地址'),
        ('DWA', '国内仓地址'),
        ('WH', '海外仓'),
        ('OV', '境外退件地址'),
    ]

    DELIVERY_ADDRESS_TYPE = [
        ('shipper', '发货仓'),
        ('MKD', '美客多'),
        ('FBA', 'FBA'),
        ('WalMart', '沃尔玛'),
        ('BA', '其它商业地址'),
        ('PA', '私人地址'),
        ('FBASatellite', 'FBA卫星仓'),
        ('WYT', '万邑通'),
        ('Tiktok', 'Tiktok'),
        ('TEMU', 'TEMU'),
        ('SHEIN', 'SHEIN'),
    ]
    WAREHOUSE_TYPE = [
        ('NormalWarehouse', '普通仓'),
        ('AutoWarehouse', '自动仓'),
        ('CollectWarehouse', '揽收仓'),
        ('ConsolidationWarehouse', '集运仓'),
    ]
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    address_name = models.CharField(max_length=50, verbose_name='地址简称', null=True)
    address_type = models.CharField(max_length=10, choices=ADDRESS_TYPE, verbose_name='地址类型', default='RC')
    supplier_code = models.CharField(max_length=50, verbose_name='供应商编码', null=True, blank=True)
    vat = models.CharField(max_length=50, verbose_name='vat', null=True, blank=True)
    eori = models.CharField(max_length=50, verbose_name='eori', null=True, blank=True)
    is_open = models.BooleanField(verbose_name='是否对外开放', default=True)
    delivery_address_type = models.CharField(max_length=32, choices=DELIVERY_ADDRESS_TYPE, verbose_name='平台目的仓',
                                             default='BA')
    is_sync_supplier = models.BooleanField(verbose_name='是否同步供应商', default=False)
    supplier_warehouse_code = models.CharField(max_length=50, verbose_name='供应商仓库编码', null=True, blank=True)
    address_detail = models.CharField(max_length=200, verbose_name='中文地址详情', null=True, blank=True)
    receiving_time = models.CharField(max_length=100, verbose_name='收货时间', null=True, blank=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE)
    deadline_days = models.PositiveIntegerField(verbose_name='存放期限天数', default=30, null=True, blank=True)
    warehouse_type = models.CharField(max_length=48, choices=WAREHOUSE_TYPE, verbose_name='仓库类型',
                                      default='NormalWarehouse')

    class Meta:
        verbose_name = '地址'
        verbose_name_plural = '地址'
        # unique_together = (('address_num', 'address_type', 'del_flag'),)

    def __str__(self):
        return f'{self.id}, %s: %s %s ' % (self.address_name, self.postcode, self.country_code)

class AddressLimitUser(BaseEntity):
    address = models.ForeignKey(Address, verbose_name='地址', on_delete=models.DO_NOTHING, null=True,
                                related_name='address_limit_user')
    customer = models.ForeignKey(Company, verbose_name='归属客户', on_delete=models.DO_NOTHING, null=True)

    class Meta:
        verbose_name_plural = '地址客户限制'
        verbose_name = '地址客户限制'
        ordering = ['-id']

    def __str__(self):
        return self.address


# 港口
class OceanPort(BaseEntity):
    """机场/港口"""
    PORT_TYPE = [
        ('departure_port', '起运港'),  # 发运地
        ('destination_port', '目的港'),  # 目的地
        ('discharge_port', '卸货港'),
    ]
    LOCATION_TYPE = [
        ('1', '港口'),
        ('2', '机场'),
    ]
    port_num = models.CharField(verbose_name='港口编码', max_length=50)
    port_name = models.CharField(verbose_name='港口中文名', max_length=100, null=True, blank=True)
    port_name_en = models.CharField(verbose_name='港口英文名', max_length=100, null=True, blank=True)
    port_type = models.CharField(verbose_name='港口类型', max_length=48, choices=PORT_TYPE, default='departure_port')
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    province = models.CharField(max_length=100, verbose_name='州省', null=True, blank=True)
    city = models.CharField(max_length=100, verbose_name='城市', null=True, blank=True)
    postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)
    port_en_name = models.CharField(verbose_name='港口英文名称', max_length=100, null=True, blank=True)
    location_type = models.CharField(max_length=10, verbose_name='位置类型', choices=LOCATION_TYPE, default='1')

    def __str__(self):
        return self.port_name


# 进出口商
class Recorder(BaseEntity):
    name = models.CharField(verbose_name='中文公司名称', max_length=100, null=True)
    name_en = models.CharField(verbose_name='英文公司名称', max_length=100, null=True)
    address = models.CharField(verbose_name='中文公司地址', max_length=300, null=True)
    address_en = models.CharField(verbose_name='英文公司地址', max_length=300, null=True)
    tax_num = models.CharField(verbose_name='公司税号', max_length=100, null=True, blank=True)
    customs_code = models.CharField(verbose_name='海关编码', max_length=100, null=True, blank=True)
    legal_person = models.CharField(verbose_name='中文法人', max_length=100, null=True)
    legal_person_en = models.CharField(verbose_name='英文法人', max_length=100, null=True)
    is_export = models.BooleanField(verbose_name='出口商', default=False)
    is_import = models.BooleanField(verbose_name='进口商', default=False)
    customer = models.ForeignKey(Company, verbose_name='归属客户', on_delete=models.DO_NOTHING, null=True)

    def __str__(self):
        return self.name


# 供应商信息
class Supplier(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]
    # 供应商API
    code = models.CharField(verbose_name='供应商编码', max_length=100, null=True)
    name = models.CharField(verbose_name='供应商名称', max_length=100, null=True)
    # 归属供应商
    office_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='归属机构')
    file_path = models.CharField(verbose_name='文件存储目录', max_length=100, null=True, blank=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE, null=True,
                              blank=True)


# 供应商对接表
class SupplierButt(BaseEntity):
    ORDER_TYPE = [
        ('C', '推客户单号'),
        ('S', '推系统单号'),
    ]

    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '失效'),
        ('DR', '草稿'),
    ]

    code = models.CharField(verbose_name='对接编码', max_length=100, null=True)
    name = models.CharField(verbose_name='对接名称', max_length=100, null=True)
    office_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='归属机构')
    vendor_id = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商ID')
    desc = models.CharField(verbose_name='对接信息描述', max_length=100, null=True, blank=True)
    class_name = models.CharField(verbose_name='实现类', max_length=100, null=True, blank=True)
    lable_file_ext = models.CharField(verbose_name='面单文件扩展名', max_length=100, null=True, blank=True)
    manifest_file_ext = models.CharField(verbose_name='预报文件扩展名', max_length=100, null=True, blank=True)
    vendor_service_code = models.CharField(verbose_name='供应商服务编码', max_length=100, null=True, blank=True)
    is_support_cancel = models.BooleanField(verbose_name='是否支持取消', default=False)
    push_order_type = models.CharField(max_length=10, verbose_name='推送单号类型', choices=ORDER_TYPE, default='C')
    is_get_track = models.BooleanField(verbose_name='是否获取轨迹', default=False)
    status = models.CharField(max_length=10, verbose_name='状态', default='DR', choices=STATUS_TYPE, null=True,
                              blank=True)



class SupplierApiSetting(BaseEntity):
    supplier_butt = models.ForeignKey(SupplierButt, related_name='SupplierApiSetting', on_delete=models.DO_NOTHING, null=True,
                                 blank=True, verbose_name='归属供应商API')
    field_name = models.CharField(max_length=50, verbose_name='字段名称', null=True, blank=True)
    field_value = models.CharField(max_length=500, verbose_name='字段值', null=True, blank=True)

    class Meta:
        ordering = ['-id']
        verbose_name = '供应商api参数配置'
        verbose_name_plural = '供应商api参数配置'

    def __str__(self):
        return f'{self.supplier_butt.office_id.name}:{self.supplier_butt.name}'


# 供应商对接账户信息表(api信息)
class SupplierButtAccount(BaseEntity):
    TYPE = [
        ('L', '面单'),
        ('M', '发货单'),
        ('P', '预报'),
        # ('T', '轨迹'),
        ('G', '拉订单'),
        ('BI', '对接BI'),
        ('SYNC', '同步订单'),
        ('WMS', 'WMS'),
        ('OMS', '海外仓'),
        ('I', '保险'),
    ]

    office_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='归属机构')
    vendor_id = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商ID')
    apicall_id = models.CharField(verbose_name='apiCall ID', max_length=100, null=True)
    type = models.CharField(verbose_name='类型', choices=TYPE, default='L', max_length=100)
    account_name = models.CharField(verbose_name='账户名', max_length=100, null=True)
    account_pwd = models.CharField(verbose_name='账户密码', max_length=100, null=True)
    account_desc = models.CharField(verbose_name='账户描述', max_length=100, null=True)
    warehouse_code = models.CharField(verbose_name='仓库编码', max_length=100, null=True, default=None)
    extend_field = models.CharField(verbose_name='扩展字段', max_length=2000, null=True, default=None)

    url = models.CharField(verbose_name='url', max_length=100, null=True)
    auth_id = models.CharField(verbose_name='授权ID', max_length=100, default='', null=True)
    auth_pwd = models.CharField(verbose_name='授权码', max_length=2000, default='', null=True)
    class_name = models.CharField(verbose_name='实现类', max_length=100, null=True, blank=True)


class TrackSupplier(BaseEntity):
    name = models.CharField(verbose_name='供应商名称', max_length=100, null=True)
    account_name = models.CharField(verbose_name='账户名', max_length=100, null=True)
    account_pwd = models.CharField(verbose_name='账户密码', max_length=100, null=True)
    account_desc = models.CharField(verbose_name='账户描述', max_length=100, null=True)
    url = models.CharField(verbose_name='url', max_length=100, null=True)
    auth_id = models.CharField(verbose_name='授权ID', max_length=100, default='')
    auth_pwd = models.CharField(verbose_name='授权码', max_length=2000, default='')
    extend_field = models.CharField(verbose_name='扩展字段', max_length=2000, null=True, default=None)
    class_name = models.CharField(verbose_name='服务类', max_length=100, null=True, blank=True)
    is_multiple_order_num = models.BooleanField(verbose_name='是否支持多个单号', default=False)

    class Meta:
        verbose_name = '轨迹供应商'
        verbose_name_plural = '轨迹供应商'
        ordering = ['-id']

    def __str__(self):
        return self.name


class TrackCodeTransform(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '停用'),
    ]
    TRANSFORM_TYPE = [
        ('supplier_track_code', '供应商轨迹编码'),
        ('track_sub_code', '供应商子编码'),
        ('remark', '描述'),
    ]

    METHOD = [
        ('start', '开始匹配'),
        ('end', '结束匹配'),
        ('include', '包含'),
    ]

    sys_track_code = models.CharField(verbose_name='系统轨迹编码', max_length=100, null=True, blank=True)
    supplier_track_code = models.CharField(verbose_name='供应商轨迹编码', max_length=100, null=True, blank=True)
    track_sub_code = models.CharField(verbose_name='供应商子编码', max_length=200, null=True, blank=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='ON', choices=STATUS_TYPE)

    track_supplier = models.ForeignKey(TrackSupplier, related_name='track_suppliers', verbose_name='轨迹供应商',
                                       on_delete=models.DO_NOTHING, null=True)
    transform_type = models.CharField(verbose_name='匹配类型', default='supplier_track_code', choices=TRANSFORM_TYPE,
                                      max_length=20, null=True)
    transform_value = models.CharField(verbose_name='匹配值', max_length=100, null=True, blank=True)
    # 匹配方式(使用描述时才用)
    transform_method = models.CharField(verbose_name='匹配方式', choices=METHOD, max_length=20, null=True, blank=True)
    order_node = models.CharField(verbose_name='订单节点', max_length=100, null=True, blank=True)
    order_status = models.CharField(verbose_name='订单状态', max_length=100, null=True, blank=True)
    node_event = models.CharField(verbose_name='节点事件', max_length=100, null=True, blank=True)
    is_show_supplier_track_code = models.BooleanField(verbose_name='是否显示供应商轨迹code', default=True, null=True,
                                                      blank=True)

    class Meta:
        verbose_name = '轨迹匹配表'
        verbose_name_plural = '轨迹匹配表'
        ordering = ['-id']

    def __str__(self):
        return self.sys_track_code


class TrackCodeBlack(BaseEntity):
    STATUS_TYPE = [
        ('ON', '启用'),
        ('OFF', '停用'),
    ]

    track_desc = models.CharField(verbose_name='轨迹描述', max_length=500, null=True)
    track_supplier = models.ForeignKey(TrackSupplier, related_name='track_black_suppliers', verbose_name='轨迹供应商',
                                       on_delete=models.DO_NOTHING, null=True)
    status = models.CharField(max_length=10, verbose_name='状态', default='ON', choices=STATUS_TYPE)

    class Meta:
        verbose_name = '轨迹编码黑名单表'
        verbose_name_plural = '轨迹编码黑名单表'
        ordering = ['-id']

    def __str__(self):
        return self.track_desc


# 供应商对接BI数据导出表
class SupplierButtBI(BaseEntity):
    statement_code = models.CharField(verbose_name='报表编码', max_length=50, null=True, blank=True)
    statement_name = models.CharField(verbose_name='报表名称', max_length=50, null=True, blank=True)
    office_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='归属机构')
    vendor_id = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, null=True, blank=True, verbose_name='供应商ID')
    bi_url = models.CharField(verbose_name='BI url', max_length=2000, null=True)
    auth_id = models.CharField(verbose_name='授权ID', max_length=100, default='')
    auth_pwd = models.CharField(verbose_name='授权码', max_length=2000, default='')
    extend_field = models.CharField(verbose_name='扩展字段', max_length=1000, null=True, default=None)


class TrackNodeAddress(BaseEntity):
    address_num = models.CharField(verbose_name='地址编码', max_length=50)
    address_name = models.CharField(verbose_name='地址中文名', max_length=100, null=True)
    address_name_en = models.CharField(verbose_name='地址英文名', max_length=100, null=True, blank=True)
    track_code = models.CharField(max_length=100, verbose_name='轨迹代码', blank=True, null=True)  # 节点
    # port_type = models.CharField(verbose_name='地址类型', max_length=48, choices=PORT_TYPE, default='departure_port')
    country_code = models.CharField(max_length=50, verbose_name='国家编码', null=True, blank=True)
    province = models.CharField(max_length=100, verbose_name='州省', null=True, blank=True)
    city = models.CharField(max_length=100, verbose_name='城市', null=True, blank=True)
    postcode = models.CharField(max_length=20, verbose_name='邮编', null=True, blank=True)

    def __str__(self):
        return f'{self.address_num}: {self.address_name}'


class CustomerProductPolicy(BaseEntity):
    """
    客户入库规则管理表
    """
    CHECK_TYPE_CHOICES = [
        ('customer_warehouse', '客户实重与仓库实重比较'),
        ('customer_warehousing_settlement', '客户实重与入库结算重比较'),
        ('exceed_weight_range', '超过重量区间'),
    ]

    WEIGHT_STRATEGY_CHOICES = [
        ('warehouse_requirements', '以仓库为准'),
        ('customer_requirements', '以客户为准'),
        ('Light_requirements', '以轻的为准'),
        ('heavy_requirements', '以重的为准'),
    ]

    customer_code = models.CharField(max_length=100, verbose_name='客户编码', null=True, blank=True, )
    customer_name = models.CharField(max_length=50, verbose_name='客户简称', null=True, blank=True, )
    product_code = models.CharField(max_length=100, verbose_name='产品编码', null=True, blank=True, )
    product_name = models.CharField(max_length=200, verbose_name='产品名称', null=True, blank=True, )
    verification_type = models.CharField(max_length=100, choices=CHECK_TYPE_CHOICES, verbose_name='校验类型',
                                         default='customer_warehouse', null=True, blank=True, )
    weight_strategy = models.CharField(max_length=100, choices=WEIGHT_STRATEGY_CHOICES, verbose_name='重量策略',
                                       default='warehouse_requirements', null=True, blank=True, )
    min_weight_tolerance = models.DecimalField(verbose_name='最小重量误差', null=True, blank=True, max_digits=10,
                                               decimal_places=3)
    max_weight_tolerance = models.DecimalField(verbose_name='最大重量误差', null=True, blank=True, max_digits=10,
                                               decimal_places=3)

    class Meta:
        verbose_name = '客户入库规则'
        verbose_name_plural = '客户入库规则管理'
        ordering = ['-id']

    def __str__(self):
        return f"{self.min_weight_tolerance}-{self.max_weight_tolerance}"


class NodeAutomaticPush(BaseEntity):
    """
    节点自动推送
    """
    PUSH_TARGET_CHOICES = [
        ('supplier', '供应商'),
        ('customer', '客户'),
    ]

    PUSH_METHOD_CHOICES = [
        ('before_node', '在节点之前'),
        ('after_node', '在节点之后'),
    ]

    push_target = models.CharField(max_length=50,choices=PUSH_TARGET_CHOICES,verbose_name='推送对象',null=True,blank=True)
    push_route = models.CharField(max_length=200,verbose_name='推送线路',null=True,blank=True)
    push_customer = models.CharField(max_length=100,verbose_name='推送客户',null=True,blank=True)
    push_product = models.CharField(max_length=100,verbose_name='推送产品',null=True,blank=True)
    push_shipment_node = models.CharField(max_length=100,verbose_name='推送运单节点',null=True,blank=True)
    push_basis_node = models.CharField(max_length=100,verbose_name='推送依据节点',null=True,blank=True)
    push_method = models.CharField(max_length=50,choices=PUSH_METHOD_CHOICES,verbose_name='推送方式',null=True,blank=True)
    push_time_interval = models.CharField(max_length=50,verbose_name='推送时间间隔(hhmmss)',null=True,blank=True)
    push_track_code = models.CharField(max_length=100,verbose_name='推送自定义轨迹代码',null=True,blank=True)
    push_track_description = models.CharField(max_length=255,verbose_name='推送自定义轨迹描述',null=True,blank=True)
    push_track_subdescription = models.CharField(max_length=255,verbose_name='推送自定义轨迹子描述',null=True,blank=True)
    push_track_timezone = models.IntegerField(verbose_name='推送轨迹时区',null=True,blank=True,default=0)
    push_track_location = models.CharField(max_length=100,verbose_name='推送轨迹位置',null=True,blank=True)
    push_track_country = models.CharField(max_length=50,verbose_name='推送轨迹国家',null=True,blank=True)

    class Meta:
        verbose_name = '节点自动推送'
        verbose_name_plural = '节点自动推送管理'
        ordering = ['-id']

    def __str__(self):
        return f"{self.push_target}-{self.push_method}"


class NodeInsertRule(BaseEntity):
    """
    节点插入规则
    """
    INSERT_METHOD_CHOICES = [
        ('before', '在依据节点之前'),
        ('after', '在依据节点之后'),
    ]

    route_code = models.CharField(max_length=100, verbose_name='线路代码', null=True, blank=True)
    basis_node = models.CharField(max_length=100, verbose_name='依据节点', null=True, blank=True)
    insert_method = models.CharField(max_length=50, choices=INSERT_METHOD_CHOICES, verbose_name='插入方式', null=True, blank=True)
    insert_node = models.CharField(max_length=100, verbose_name='插入节点', null=True, blank=True)
    time_interval = models.CharField(max_length=50,verbose_name='间隔时间(hhmmss)', null=True, blank=True)
    location = models.CharField(max_length=200, verbose_name='位置', blank=True, null=True, default='')
    supplier_track_code = models.CharField(verbose_name='供应商轨迹编码', max_length=100, blank=True, null=True, default='')
    track_description = models.CharField(verbose_name='供应商轨迹描述', max_length=1000, blank=True, null=True, default='')
    track_en_description = models.CharField(verbose_name='供应商轨迹英文描述', max_length=1000, blank=True, null=True, default='')
    supplier_track_sub_code = models.CharField(verbose_name='供应商轨迹子代码', max_length=100, blank=True, null=True, default='')
    track_sub_description = models.CharField(verbose_name='供应商轨迹子描述', max_length=1000, blank=True, null=True, default='')
    track_sub_en_description = models.CharField(verbose_name='供应商轨迹子英文描述', max_length=1000, blank=True,
                                                null=True, default='')

    class Meta:
        verbose_name = '节点插入规则'
        verbose_name_plural = '节点插入规则管理'
        ordering = ['-id']

    def __str__(self):
        return f"{self.route_code}-{self.insert_node}"


class NodeInsertRuleCeleryTaskInfo(BaseEntity):
    TASK_STATUS_CHOICES = (
        ('PENDING', '等待中'),
        ('STARTED', '已开始'),
        ('SUCCESS', '成功'),
        ('FAILURE', '失败'),
        ('REVOKED', '已撤销'),
    )

    task_id = models.CharField(max_length=255, unique=True, verbose_name='Celery任务ID')
    task_name = models.CharField(max_length=255, verbose_name='任务名称',default='')
    node_insert_rule = models.ForeignKey(
        'NodeInsertRule',
        on_delete=models.CASCADE,
        related_name='celery_tasks',
        verbose_name='节点插入规则'
    )
    status = models.CharField(
        max_length=20,
        choices=TASK_STATUS_CHOICES,
        default='PENDING',
        verbose_name='任务状态'
    )
    result = models.TextField(null=True, blank=True, verbose_name='任务结果')
    error_message = models.TextField(null=True, blank=True, verbose_name='错误信息')

    class Meta:
        db_table = 'celery_task_info'
        verbose_name = '任务信息'
        verbose_name_plural = verbose_name


class ThirdPartyWarehouseMapping(BaseEntity):
    class PLATFORM_CHOICES(models.TextChoices):
        TEMU = 'Temu'

    platform = models.CharField(max_length=20, choices=PLATFORM_CHOICES.choices, verbose_name='平台', default=PLATFORM_CHOICES.TEMU.value, db_index=True)
    third_party_warehouse_code = models.CharField(max_length=50, verbose_name='平台仓库编码', null=True, blank=True, db_index=True)
    is_active = models.BooleanField(verbose_name='是否启用', default=False, db_index=True)
    address = models.ForeignKey(Address, verbose_name='仓库地址', on_delete=models.DO_NOTHING, null=True, blank=True, related_name='third_party_mappings',)
    json_extend = models.JSONField(verbose_name='扩展字段', null=True, blank=True, help_text='存储额外的配置信息')

    class Meta:
        verbose_name = '第三方仓库映射'
        verbose_name_plural = '第三方仓库映射'
        ordering = ['-id']

    def __str__(self):
        return f"{self.platform}-{self.third_party_warehouse_code}"

    def get_json_extend_value(self, key, default=None):
        """获取扩展配置中的值"""
        if not self.json_extend:
            return default
        return self.json_extend.get(key, default)

    def set_json_extend_value(self, key, value):
        """设置扩展配置中的值"""
        if not self.json_extend:
            self.json_extend = {}
        self.json_extend[key] = value
        self.save(update_fields=['json_extend'])