import datetime
import time

from django.db import transaction
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response

from alita.logger import logger
from common.order_statement.order_statement_dmas import arrive_overseas_warehouse_aging, statistics_charge_in_and_out, \
    statistics_revenue_and_cost, place_order_count
from common.repair_data import order_packet_track_data, delete_parcel_order_repetition_charge_in
from common.tools import get_track_location_remark, change_order_status, set_customer_track_fba, parse_custom_date, \
    save_crm_price, get_order_status_mapping
from common.utils.custom_viewset_base import CustomViewBase
from common.utils.dmas_util import send_dmas_message, send_dmas_msg_by_customer, get_dmas_token, send_fba_track_dmas
from common.utils.response import fail_response, success_response
from company.models import Supplier, SupplierButtBI, SupplierButtAccount, Address
from company.serializers.supplier import SupplierSerializer, SupplierButtBISerializer
from common.custom import RbacPermission
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from rest_framework_jwt.authentication import J<PERSON><PERSON>WebTokenAuthentication

from crm.tasks import sync_generate_royalty_derivation
from info.models import TrackCode
from oms.tasks import beat_oms_handle_rent_order_task, beat_oms_handle_inventory_age_task
from order.integration.util.customerOrderUtil import request_get_server
from order.models import Track, OceanOrderTrack, CustomerOrderRelateOcean, CustomerOrder, Parcel, OutboundInstruct, \
    OrderSyncUploadTask
from order.tasks import sync_order_data_to_supplier, fba_order_statistics_dmas, settle_statistics_dmas, \
    place_order_count_dmas, sync_clearance_out_data_to_supplier, sync_customer_order_task, ocean_order_aging_dmas, \
    handler_sync_order_data_handle_times
from order.utils.ocean_order import judge_zone_affiliation
from pms.tasks import handler_product_sales_price_strategy_task
from task.tasks import handler_reconciliation_task, handler_statement_compared_task, common_upload_order_handle, \
    common_order_async_task_handle, common_product_async_task_handle


class SupplierViewSet(CustomViewBase):
    '''
    航空公司管理：增删改查
    '''
    queryset = Supplier.objects.all()
    serializer_class = SupplierSerializer
    filter_backends = (SearchFilter, OrderingFilter)
    search_fields = ('name', 'code')
    ordering_fields = ('id',)
    authentication_classes = (JSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)


# 供应商BI报表对接
class SupplierButtBIViewSet(CustomViewBase):
    queryset = SupplierButtBI.objects.all()
    serializer_class = SupplierButtBISerializer
    filter_backends = (SearchFilter, OrderingFilter)
    search_fields = ('statement_name', 'statement_code', 'bi_url', 'vendor_id__name')
    ordering_fields = ('id',)
    authentication_classes = (JSONWebTokenAuthentication,)
    permission_classes = (RbacPermission,)

    @transaction.atomic
    @action(methods=['POST'], detail=False)
    def batch_delete(self, request):
        ids = request.data.get('ids')
        if ids:
            SupplierButtBI.objects.filter(id__in=ids, del_flag=0).update(del_flag=1)
        return Response(data={'code': 200, 'data': {}, 'msg': '删除成功'}, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=False)
    def get_statement_url(self, request):
        system_url = request.data.get('system_url')
        statement_code = request.data.get('statement_code')
        supplier_butt_account = SupplierButtAccount.objects.filter(url=system_url, type='BI', del_flag=False).first()
        if not supplier_butt_account:
            return fail_response(request, f'未找到url为 {system_url} 对应的供应商对接账户信息！')
        supplier_butt_bi = SupplierButtBI.objects.filter(vendor_id=supplier_butt_account.vendor_id,
                                                         statement_code=statement_code, del_flag=False).first()
        if not supplier_butt_bi:
            supplier_name = supplier_butt_account.vendor_id.name if supplier_butt_account.vendor_id \
                else supplier_butt_account.vendor_id
            return fail_response(request, f'未找到供应商ID: {supplier_name}, '
                                          f'报表编码: {statement_code} 对应的供应商对接BI信息！')
        else:
            return Response(data={"url": supplier_butt_bi.bi_url, "code": 200}, status=status.HTTP_200_OK)

    # 批量按时间导出下单统计图
    @action(methods=['POST'], detail=False)
    def select_order_export(self, request):
        select_time = request.data.get('select_time')
        print('select_order_export-->', select_time)
        start_date = datetime.datetime.strptime(select_time[0], '%Y-%m-%d')
        end_date = datetime.datetime.strptime(select_time[1], '%Y-%m-%d')
        place_order_count(start_date, end_date)
        return success_response(request, '成功!')

    # 批量按时间导出计费统计图
    @action(methods=['POST'], detail=False)
    def select_billing_export(self, request):
        select_time = request.data.get('select_time')
        print('select_billing_export-->', select_time)
        start_date = datetime.datetime.strptime(select_time[0], '%Y-%m-%d')
        end_date = datetime.datetime.strptime(select_time[1], '%Y-%m-%d')
        statistics_charge_in_and_out(start_date, end_date)
        return success_response(request, '成功!')

    # 批量按时间导出财务统计图
    @action(methods=['POST'], detail=False)
    def select_settle_export(self, request):
        select_time = request.data.get('select_time')
        print('select_settle_export-->', select_time)
        start_date = datetime.datetime.strptime(select_time[0], '%Y-%m-%d')
        end_date = datetime.datetime.strptime(select_time[1], '%Y-%m-%d')
        statistics_revenue_and_cost(start_date, end_date)
        return success_response(request, '成功!')

    # 测试接口: 用于调试各个接口的功能性, 前端有按钮可以触发此接口
    # @transaction.atomic
    @action(methods=['POST'], detail=False)
    def production_test(self, request):
        # 易仓
        # handler_yicang_order()
        # handler_push_tracking_num_yicang()
        # 小包推送51/17轨迹
        # handler_push_parcel_track_info()
        # time.sleep(3)
        # FBA推送51轨迹
        # handler_get_51tracking_info('2')
        # 供应商同步订单件重体
        # sync_order_data_to_supplier(1)
        # 对账
        # handler_reconciliation_task(3)
        # handler_statement_compared_task(3)
        # 订单异步任务(定时任务)测试
        # common_order_async_task_handle()
        # 获取轨迹测试
        # params = {'order_num': 'FXFR2400001383'}
        # url = 'http://127.0.0.1:8000' + '/api/trackinfoFba/'
        # result = request_get_server(params, url, {'Content-Type': 'application/json'})
        # print('result3-->', result)
        # 修复FBA订单包裹轨迹数据
        # order_packet_track_data()
        # 修复小包bug重复计费问题
        # delete_parcel_order_repetition_charge_in()
        # 每周给客户推送财务消息
        # fba_order_statistics_dmas(write_excel=True)
        # place_order_count_dmas()
        # settle_statistics_dmas()

        # language_preference = request.COOKIES.get('language', 'zh-hans')
        # language_preference = request.COOKIES.get('language')
        # language_preference = request.user.language
        # language_preference = request.user.is_staff
        # print('language_preference-->', request.COOKIES)

        # # 测试数据脏读1
        # order_id = 1529
        # order1 = CustomerOrder.objects.get(id=order_id)
        # order1.remark = '777'
        #
        # order2 = CustomerOrder.objects.get(id=order_id)
        # order2.order_remark = '订单备注2'
        # order2.save()
        #
        # order1.save()

        # # 测试数据脏读2
        # parcel_id = 7168
        # parcel = Parcel.objects.get(id=parcel_id)
        # # customer_order1 = parcel.customer_order
        # customer_order1 = CustomerOrder.objects.filter(order_num=parcel.customer_order, del_flag=False).first()
        # customer_order1.order_remark = '订单备注11'
        # customer_order1.save()
        #
        # parcel.remark = 'parcel1'
        # parcel.save()
        #
        # customer_order2 = parcel.customer_order
        # customer_order2.remark = '备注22'
        # customer_order2.save()

        # 测试计算泡比优惠和重量提成
        # customer_order = CustomerOrder.objects.filter(order_num='FXFR2400001541').first()
        # save_crm_price(customer_order)

        # 提成导出汇算接口
        # sync_generate_royalty_derivation()

        # 异步产品销售定价转协议价
        # common_product_async_task_handle()

        # 出口报关单同步测试
        # sync_clearance_out_data_to_supplier(1)
        # yqf同步fba订单到mz
        # sync_customer_order_task(1, 1625, is_sync_parcel=True)

        # aaa = get_order_status_mapping(['CWED', 'OW', 'DEP', 'TF', 'SF'])
        # print('aaa-->', aaa)
        # 计算库龄
        # beat_oms_handle_inventory_age_task()
        # 生成仓租单
        # beat_oms_handle_rent_order_task()

        # sync_order_data_to_supplier(mode_key=12)

        # arrive_overseas_warehouse_aging()

        # 重置同步件重体任务
        # handler_sync_order_data_handle_times()

        # send_dmas_message('测试磁盘空间消息: ZH磁盘空间70%', ['普信IT支持群'], 'sync')
        # send_dmas_msg_by_customer('这是一条客户测试消息2', customer_code='0600429')
        # get_dmas_token()

        # customer_order = CustomerOrder.objects.filter(order_num='MZFB25062865309', del_flag=False).last()
        # send_fba_track_dmas(customer_order, 'ARR', '2025-06-30 18:44:00', '已到港')

        # handler_product_sales_price_strategy_task()

        # common_upload_order_handle.delay(upload_task_id=139)

        # upload_task = OrderSyncUploadTask.objects.filter(id=140).last()
        # logger.info(f'upload_task执行时间: {upload_task.execution_time}')

        # fba/fbm订单导入测试
        common_upload_order_handle()

        # res = judge_zone_affiliation('xidilaite-1', '美中')
        # logger.info(f'res-->{res}')

        return success_response(request, '成功!')
