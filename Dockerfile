FROM registry.cn-shenzhen.aliyuncs.com/alita_px/alita_backend:base-1

# 访问 pipeline 变量
ARG deploy_version

# author
LABEL maintainer="px"

WORKDIR /data/alita

# 复制代码到目标路径
COPY . /data/alita

# Debug: 打印 deploy_version
RUN echo "Deploy Version: ${deploy_version}"

RUN export DJANGO_SETTINGS_MODULE=alita.settings.${deploy_version}

# 创建必要的目录和文件，并设置权限
RUN mkdir -p /data/log && \
    mkdir -p /data/pid && \
    touch /data/pid/alita.pid && \
    chmod +x /data/alita/deploy.sh /data/alita/deploy_celery.sh && \
    chmod +x /data/alita/deploy_celery_docker.sh


# 编译国际化
RUN /bin/bash -c "python3 manage.py compilemessages -l zh_Hans -l en"

# 安装
# RUN /bin/bash -c "pip install uwsgi"
RUN /bin/bash -c "pip install supervisor gunicorn uvicorn"

# 确保 uwsgi 安装在虚拟环境中
RUN /bin/bash -c "source /home/<USER>/alita/bin/activate && pip install supervisor gunicorn uvicorn"

# 启动
CMD ["/bin/bash", "-c", "tail -f /dev/null"]
