;[uwsgi]
;socket = 0.0.0.0:3038
;chdir = /data/hanjinTest/alita
;wsgi-file = alita/wsgi.py
;processes = 4
;threads = 2
;stats = 127.0.0.1:9198
;home = /home/<USER>/alita
;pidfile = /data/hanjinTest/pid/hanjinTest.pid
;daemonize = /data/hanjinTest/log/hanjinTest_uwsgi.log
;env = DJANGO_SETTINGS_MODULE=alita.settings.hanjinTest
;env LANG=en_US.utf8
;env LC_ALL=en_US.UTF-8
;env LC_LANG=en_US.UTF-8

;[uwsgi]
;# 基础配置
;socket = 0.0.0.0:3038
;chdir = /data/hanjinTest/alita
;wsgi-file = alita/wsgi.py
;home = /home/<USER>/alita
;
;# 进程和线程优化
;master = true                     # 启用主进程
;processes = 4                     # 调整为 CPU 核心数，避免过多进程竞争
;threads = 4                       # 增加线程数来处理并发
;enable-threads = true            # 启用线程支持
;thunder-lock = true              # 使用thunder lock，在多进程时提高性能
;max-worker-lifetime = 3600       # 工作进程最大生命周期（秒）
;worker-reload-mercy = 30         # 减少工作进程重启等待时间
;cheaper-algo = busyness          # 启用自适应进程数算法
;cheaper = 2                      # 最小进程数
;cheaper-initial = 4              # 初始进程数
;cheaper-step = 1                 # 每次增加/减少的进程数
;
;# 缓冲区设置
;buffer-size = 131072            # 增加缓冲区大小到 128KB
;post-buffering = 131072         # POST 请求缓冲增加到 128KB
;max-requests = 10000            # 增加单个进程处理的请求数
;harakiri = 30                   # 减少请求超时时间，快速释放卡死请求
;harakiri-verbose = true         # 记录超时日志
;
;# 连接优化
;listen = 4096                   # 增加监听队列大小
;socket-timeout = 30             # 减少 socket 超时时间
;so-keepalive = true            # 保持连接
;tcp-nodelay = true             # 禁用 Nagle 算法，减少延迟
;fastopen = 1000                # 启用 TCP Fast Open
;
;# 内存优化
;reload-on-rss = 1024           # 降低内存阈值，更早进行内存回收
;evil-reload-on-rss = 2048      # 降低强制重启阈值
;reload-on-as = 2048            # 降低虚拟内存阈值
;memory-report = true           # 启用内存报告
;no-orphans = true              # 防止孤立进程
;vacuum = true                  # 退出时清理环境
;
;# 缓存优化
;cache2 = name=mycache,items=10000,blocksize=4096,keysize=128
;cache-blocksize = 4096         # 缓存块大小
;cache-store = true             # 启用缓存存储
;
;# 日志设置
;pidfile = /data/hanjinTest/pid/hanjinTest.pid
;daemonize = /data/hanjinTest/log/hanjinTest_uwsgi.log
;log-maxsize = 20000000         # 降低单个日志文件大小
;log-reopen = true              # 支持日志文件重新打开
;log-slow = 1000                # 记录执行时间超过1秒的请求
;log-4xx = true                # 记录4xx错误
;log-5xx = true                # 记录5xx错误
;log-date = true               # 日志添加时间戳
;log-x-forwarded-for = true    # 记录真实客户端IP
;
;# 监控和统计
;stats = 127.0.0.1:9198
;stats-http = true             # 启用HTTP格式的统计信息
;memory-report = true          # 启用内存报告
;enable-metrics = true         # 启用指标收集
;
;# 环境变量设置
;env = DJANGO_SETTINGS_MODULE=alita.settings.hanjinTest
;env LANG=en_US.utf8
;env LC_ALL=en_US.UTF-8
;env LC_LANG=en_US.UTF-8
;
;# 系统资源优化
;max-fd = 65535               # 最大文件描述符数量
;single-interpreter = true    # 单解释器模式
;optimize-socket-backlog = true # 优化socket积压
;close-on-exec = true         # 执行时关闭描述符
;lazy-apps = true            # 延迟加载应用，减少启动时内存使用

[uwsgi]
socket = 127.0.0.1:3038
chdir = /data/hanjinTest/alita
wsgi-file = alita/wsgi.py
processes = 4
threads = 2
stats = 127.0.0.1:9198
home = /home/<USER>/alita
pidfile = /data/hanjinTest/pid/hanjinTest.pid
daemonize = /data/hanjinTest/log/hanjinTest_uwsgi.log
env = DJANGO_SETTINGS_MODULE=alita.settings.hanjinTest
env LANG=en_US.utf8
env LC_ALL=en_US.UTF-8
env LC_LANG=en_US.UTF-8

